<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="EN" xmlns="http://www.w3.org/1999/xhtml">
<head>
<title><spring:message code="am.lbl.title" /></title>


<script src="./static/js/validation/viewEscalation.js"
	type="text/javascript"></script>

<style>

.form-control {
	background-color: white !important;
}

.tableA th {
	border: 1px solid #c2d1e8;
	border-collapse: collapse;
	padding: 10px;
}
.tableA td {
	border: 1px solid #c2d1e8;
	border-collapse: collapse;
	padding: 10px;
}

.tableA {
	margin: 1%;
}

.tableA  th {
	width: 10%;
	text-align: center;
	background-color: #ecf4ff;
}

.width {
	width: 1%;
}

</style>

</head>


	<div class="container-fluid height-min">

		

		<div class="panel panel-default no_margin">
			<div class="panel-heading clearfix">
				<strong><span class="glyphicon glyphicon-th"></span> <span
					data-i18n="Data">Escalation Matrix</span></strong>
					<sec:authorize access="hasAuthority('Add Escalation Matrix')">
						<div class="icon_bar">
					<c:choose>
						<c:when test="${baseEscalationDTO.requestState  eq 'R'}">
				<sec:authorize access="hasAuthority('Add Escalation Matrix') ">
					<a data-toggle="tooltip" title="Edit"
						onclick="viewEscalationInfo('${baseEscalationDTO.memberId}','/addEscalation','Y')"
						href="#"><img src="./static/images/edit-grey.png" alt="edit"></a>

				</sec:authorize>
				</c:when>	
				</c:choose>				
				</div>
				</sec:authorize>
				
				
			</div>

			<div class="panel-body" id="printDiv" >
				<form:form onsubmit="removeSpace(this); encodeForm(this);"
					method="POST" id="viewEscalation" modelAttribute="baseEscalationDTO"
					enctype="multipart/form-data" action="viewEscalation" autocomplete="off">
					<br />
					<div class="alert alert-danger appRejMust" role="alert">Please
						Select Approve/Reject action.
						</div>
						<div class="alert alert-danger remarkMust" role="alert">Please
						Enter Remarks.
						</div>
						<br />
					
						<div class="row">
							<div class="col-md-3">
								<label>Participant Name</label>
								<form:input path="memberId" id="memberId" name="member"
									class="form-control" readonly="true" style="display:none;" />
									<form:input path="bankMemberName" id="bankMemberName" name="bankMemberName"
									class="form-control" readonly="true" />
								 
								<div id="errMember" class="error fail" style="color: #a94442"></div>
							</div>
						</div>
					
					
					<div class="bs-example">
						<c:forEach var="department" items="${departmentList}">
						<div class="table-responsive">
							<table id="tableA" class="tableA">
						
						<caption style="display:none;">Dept</caption>


							
							<thead style="display:none;"><th scope="col"></th></thead>
								<tr>
									<th scope="col">Department</th>
									<th scope="col" class="width">Levels</th>
									<th scope="col">Name</th>
									<th scope="col">Designation</th>
									<th scope="col">Email</th>
									<th scope="col">Landline Number</th>
									<th scope="col">Mobile </th>
									<th scope="col">State </th>
									<th scope="col">Zip Code </th>
								</tr>
							
								<tr>
									<td rowspan="6" style="text-align: center;">
										<label>${department.departmentName}</label>
									</td>
									
										<c:forEach var="user" items="${escalationList}">
										
											<c:if test="${user.departmentId eq department.departmentId}">
											
												<form:hidden path="escLevel" value="${user.escLevel}" />
												<form:hidden path="departmentId" value="${department.departmentId}" />
												<form:hidden path="userId" value="${user.userId}" />
												<tr>
												    
													<td>${user.escLevel}</td>
													<td>${user.name}</td>
													<td>${user.designation}</td>
													<td>${user.email}</td>
													<td>${user.landline}</td>
													<td>${user.mobile}</td>
													<td>${user.stateName}</td>
													<td>${user.pinCode}</td>
												</tr>
												
											</c:if>

										</c:forEach>
								</tr>
							</table>
							</div>
						</c:forEach>
					</div>
					
						<sec:authorize access="hasAuthority('Approve Escalation Matrix')">
					<table class="table table-striped" style="font-size: 12px">
					
<caption style="display:none;">ESCALATION</caption>
<thead style="display:none;"><th scope="col"></th></thead>


					
								<tbody>
									
										<c:if test="${baseEscalationDTO.requestState eq 'P'}">
											<tr>
												<td colspan="6"><div
														class="panel-heading-red  clearfix">
														<strong><span class="glyphicon glyphicon-info-sign"></span>
															<span data-i18n="Data"><spring:message
																	code="feeRate.requestInformation" /></span></strong>
													</div></td>
											</tr>

											<tr>
												<td><label><spring:message
															code="feeRate.approveReject" /><span style="color: red">*</span></label></td>
												<td><select name="select" id="apprej"
													onchange="display()">
														<option value="N"><spring:message
																code="feeRate.select" /></option>
														<option value="A" id="approve"><spring:message
																code="feeRate.approve" /></option>
														<option value="R" id="reject"><spring:message
																code="feeRate.reject" /></option>
												</select></td>
												<td><div style="text-align: center">
														<label><spring:message code="feeRate.remarks" /><span
															style="color: red">*</span></label>
													</div></td>

												<td colspan="2"><textarea rows="4" cols="50"
														maxlength="100" id="rejectReason"></textarea>
													<div id="errorrejectReason" class="error"></div></td>
											</tr>
										</c:if>
								
								</tbody>
							</table>
						</sec:authorize>
							<div class="row">
								<div class="col-sm-12 bottom_space">
									<hr />
									<div style="text-align: center">
										<sec:authorize access="hasAuthority('Approve Escalation Matrix')">
											<c:if test="${baseEscalationDTO.requestState eq 'P'}">
												<input name="button10" type="button" class="btn btn-success"
													id="approveFeeRateStatus" value="Submit"
													onclick="postAction('/approveOrRejectEscalation');" />
											</c:if>
										</sec:authorize>
										<c:choose>
											<c:when test="${baseEscalationDTO.requestState ne 'A' or show eq 'R' }">
												<button type="button" class="btn btn-danger"
													onclick="getEscalationList();">Back</button>
											</c:when>
											<c:otherwise>
												<button type="button" class="btn btn-danger"
													onclick="submitForm('/addEditEscalation');">Back</button>
											</c:otherwise>
										</c:choose>
									</div>
								</div>
							</div>
				</form:form>
				
				</div>
			</div>
		</div>
	

</body>
</html>