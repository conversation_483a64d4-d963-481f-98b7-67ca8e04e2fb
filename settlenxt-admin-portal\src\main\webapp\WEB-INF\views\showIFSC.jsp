<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>


<script type="text/javascript">
	var actionColumnIndex = 6;
	var firstColumnToBeSkippedInFilterAndSort = false;
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 8;
	firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 7;
	firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
</script>

<script>
	var referenceNoListPendings = [];

	<c:if test="${not empty ifsclist}">
	<c:forEach items="${ifsclist}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	referenceNoListPendings.push('${operator.ifscCode}');
	</c:if>
	</c:forEach>
	</c:if>
</script>



<script src="./static/js/validation/SearchIFSC.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>

<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />



<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>
<!-- Model -->
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalNews" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">IFSC
					Approval/Rejection</label>
				<p id="newsIds" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulkIFSC('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulkIFSC('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Model -->
<!-- Model -->
<input:hidden id="refNum" />

<div class="row">


	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Y'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/showIFSC');" role="tab"
			data-toggle="tab"><span class="glyphicon glyphicon-list-alt">
		</span> <spring:message code="ifsc.mainTab.title" /></a>


		<c:choose>
			<c:when test="${showApprovalTab eq 'Y'}">
				<li role="presentation" class="active">
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" role="tab" onclick="getPendingIFSCList();"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> </span> <spring:message
				code="ifsc.approvalTab.title" /></a>
	</ul>

	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
				<div class="col-sm-12">
					<sec:authorize access="hasAuthority('Add IFSC')">
						<c:if test="${showAddButton eq 'Y'}">
							<a class="btn btn-success pull-right btn_align" href="#"
								onclick="submitForm('/addIFSC?landing=main','P');"
								style="margin: -8px 1px 1px 0px;"><em class="glyphicon-plus"></em>
								<spring:message code="ifsc.addIFSCBtn" /> </a>
						</c:if>
					</sec:authorize>
					<div class="row">
						<div class="col-sm-12">
							<button class="btn  pull-right btn_align" id="clearFilters">
								<spring:message code="ifsc.clearFiltersBtn" />
							</button>
							&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
								id="csvExport"><spring:message code="ifsc.csvBtn" /> </a> <a
								class="btn btn-success pull-right btn_align" href="#"
								id="excelExport"><spring:message code="ifsc.exportBtn" /> </a>
						</div>
					</div>
					<div class="panel panel-default">
						<c:if test="${showMainTab eq 'Y'}">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="ifsc.listscreen.title" /></span></strong>
							</div>
						</c:if>
						<c:if test="${showApprovalTab eq 'Y'}">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="ifsc.listscreen.title" /></span></span></strong>
								<c:if test="${not empty ifsclist}">
									<sec:authorize access="hasAuthority('Approve IFSC')">
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkIFSC('A','No')"
											id="submitButtonA"
											value="<spring:message code="feeRate.Approve" />" />
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkIFSC('R','No')"
											id="submitButtonR"
											value="<spring:message code="feeRate.Reject" />" />
									</sec:authorize>
								</c:if>
							</div>

						</c:if>

						<div class="panel-body">
							<div>
								<div class="row">
									<div class="col-sm-12"></div>
								</div>

								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">IFSC</caption>
										<thead>
											<tr>
												<c:if test="${showApprovalTab eq 'Y'}">
													<sec:authorize access="hasAuthority('Approve IFSC')">
														<th scope = "col"><input type=checkbox name='selectAllCheck'
															id="selectAll" data-target="toggleModalNews" value="All"></input></th>
													</sec:authorize>
												</c:if>
												<th scope = "col"><spring:message code="ifsc.ifscCode" /></th>
												<th scope = "col"><spring:message code="ifsc.bankName" /></th>
												<th scope = "col"><spring:message code="ifsc.bankCode" /></th>
												<th scope = "col"><spring:message code="ifsc.nfsCode" /></th>
												<c:choose>
													<c:when test="${showApprovalTab eq 'Y'}">
														<th scope = "col"><spring:message code="ifsc.approvalStatus" /></th>
													</c:when>
													<c:otherwise>
														<th scope = "col"><spring:message code="ifsc.status" /></th>
													</c:otherwise>
												</c:choose>
												<c:if test="${showApprovalTab eq 'Y'}">
													<th scope = "col"><spring:message code="ifsc.checkerComments" /></th>
												</c:if>
												<th scope = "col"><spring:message code="ifsc.createdBy" /></th>
												<%-- <th scope = "col"><spring:message code="ifsc.actionTitle" /></th> --%>

											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty ifsclist}">
												<c:forEach var="ifsc" items="${ifsclist}">
													<tr>
														<c:if test="${showApprovalTab eq 'Y'}">
															<sec:authorize access="hasAuthority('Approve IFSC')">
																<c:if test="${ifsc.requestState =='P' }">
																	<td><input type=checkbox name='type'
																		id="selectSingle" onclick="mySelect();"
																		value="${ifsc.ifscCode}"></input></td>
																</c:if>
																<c:if test="${ifsc.requestState !='P' }">
																	<td></td>
																</c:if>
															</sec:authorize>
														</c:if>
														<c:if test="${showMainTab eq 'Y'}">
															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/getIFSC?landing=${landing}')">${ifsc.ifscCode}</td>
															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/getIFSC?landing=${landing}')">${ifsc.ifscDescription}</td>
															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/getIFSC?landing=${landing}')">${ifsc.bankCode}</td>
															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/getIFSC?landing=${landing}')">${ifsc.nfsCode}</td>
														</c:if>
														<c:if test="${showApprovalTab eq 'Y'}">

															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/viewApproveIFSC?landing=${landing}')">${ifsc.ifscCode}</td>
															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/viewApproveIFSC?landing=${landing}')">${ifsc.ifscDescription}</td>
															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/viewApproveIFSC?landing=${landing}')">${ifsc.bankCode}</td>
															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/viewApproveIFSC?landing=${landing}')">${ifsc.nfsCode}</td>
														</c:if>

														<c:choose>
															<c:when test="${showApprovalTab eq 'Y'}">
																<td
																	onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/viewApproveIFSC?landing=${landing}')">
																	<c:if test="${ifsc.requestState=='A' }">
																		<spring:message
																			code="ifsc.requestState.approved.description" />
																	</c:if> <c:if test="${ifsc.requestState=='P' }">
																		<spring:message
																			code="ifsc.requestState.pendingApproval.description" />
																	</c:if> <c:if test="${ifsc.requestState=='R' }">
																		<spring:message
																			code="ifsc.requestState.rejected.description" />
																	</c:if> <c:if test="${ifsc.requestState=='D' }">
																		<spring:message
																			code="ifsc.requestState.discarded.description" />
																	</c:if> &nbsp;
																</td>
															</c:when>
															<c:otherwise>
																<td
																	onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/getIFSC?landing=${landing}')">
																	<c:if test="${ifsc.status=='A' }">
																		<spring:message code="ifsc.activeStatus" />
																	</c:if> <c:if test="${ifsc.status=='I' }">
																		<spring:message code="ifsc.inactiveStatus" />
																	</c:if>
																</td>
															</c:otherwise>
														</c:choose>
														<c:if test="${showApprovalTab eq 'Y'}">
															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/viewApproveIFSC?landing=${landing}')">${ifsc.checkerComments}</td>

														</c:if>
														<c:if test="${showApprovalTab eq 'Y'}">
															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/viewApproveIFSC?landing=${landing}')">${ifsc.createdBy}</td>


														</c:if>
														<c:if test="${showMainTab eq 'Y'}">
															<td
																onclick="javascript:viewIFSCInfo('${ifsc.ifscCode}', '/getIFSC?landing=${landing}')">${ifsc.createdBy}</td>
														</c:if>


													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>



			</div>

		</div>



	</div>