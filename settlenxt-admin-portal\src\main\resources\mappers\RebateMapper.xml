<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.RebateRepository">

	<select id="getApprovedRebate" resultType="RebateDTO">
		SELECT R.REBATE_ID as rebateID, R.CARD_TYPE as cardType, R.FINANCIAL_YEAR as financialYear, R.FEATURE_OR_BASE_FEE as featureOrBaseFee
		, <PERSON><PERSON><PERSON>ER<PERSON>OR_INDI as operatorIndi,R.NEW_CARD_COUNT_1 as newCardCount1,R.NEW_CARD_COUNT_2 as newCardCount2,R.REBATE_PERCENTAGE as rebatePercentage
		,R.status as status,ct.description as cardTypeName  FROM mcpr_rebate_config R  
		left join lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' WHERE R.REBATE_ID =#{roleId}
	</select>
	<select id="getApprovedRebateMain" resultType="RebateDTO">
		SELECT R.REBATE_ID as rebateID, R.CARD_TYPE as cardType, R.FINANCIAL_YEAR as financialYear, R.FEATURE_OR_BASE_FEE as featureOrBaseFee
		, R.OPERATOR_INDI as operatorIndi,R.NEW_CARD_COUNT_1 as newCardCount1,R.NEW_CARD_COUNT_2 as newCardCount2,R.REBATE_PERCENTAGE as rebatePercentage
		,R.status as status,ct.description as cardTypeName  
		FROM mcpr_rebate_config R  
		inner join  mcpr_rebate_config_Stg stg  on R.REBATE_ID=stg.REBATE_ID
		left join lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' 
		WHERE R.REBATE_ID =#{roleId}
	</select>
	<select id="getRebateInfoByRebateId" resultType="RebateDTO">
		SELECT R.REBATE_ID as rebateID, R.CARD_TYPE as cardType, R.FINANCIAL_YEAR as financialYear, R.FEATURE_OR_BASE_FEE as featureOrBaseFee, R.OPERATOR_INDI as operatorIndi
		,R.NEW_CARD_COUNT_1 as newCardCount1,R.NEW_CARD_COUNT_2 as newCardCount2,R.REBATE_PERCENTAGE as rebatePercentage,R.status as status, R.LAST_UPDATED_BY,R.LAST_UPDATED_ON as lastUpdatedOn
		, R.CREATED_ON as createdOn, R.CREATED_BY as createdBy,ct.description as cardTypeName 
		FROM  mcpr_rebate_config R left join  lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' WHERE R.REBATE_ID =#{rebateId}	
	</select>
	<select id="getRebateStgInfoByRebateId" resultType="RebateDTO">
		SELECT R.REBATE_ID as rebateID, R.CARD_TYPE as cardType, R.FINANCIAL_YEAR as financialYear, R.FEATURE_OR_BASE_FEE as featureOrBaseFee, R.OPERATOR_INDI as operatorIndi,R.NEW_CARD_COUNT_1 as newCardCount1
		,R.NEW_CARD_COUNT_2 as newCardCount2,R.REBATE_PERCENTAGE as rebatePercentage,R.status as status,R.CHECKER_COMMENTS as checkerComments, R.LAST_UPDATED_BY as lastUpdatedBy,R.LAST_UPDATED_ON as lastUpdatedOn
		, R.CREATED_ON as createdOn, R.CREATED_BY as createdBy , R.LAST_OPERATION as lastOperation
		,case when R.REQUEST_STATE in ('P','S') then 'Pending For Approval' when R.REQUEST_STATE='R' then 'Rejected' when R.REQUEST_STATE='A' then 'Active'  end requestStateDiscription
		,R.REQUEST_STATE as requestState,ct.description as cardTypeName  
		FROM   mcpr_rebate_config_Stg R   left join  lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' WHERE R.REBATE_ID =#{rebateId}
	</select>
	<select id="getRolesPendingForApproval" resultType="RebateDTO">
		SELECT R.REBATE_ID as rebateID, R.CARD_TYPE as cardType, R.FINANCIAL_YEAR as financialYear, R.FEATURE_OR_BASE_FEE as featureOrBaseFee, R.OPERATOR_INDI as operatorIndi,R.NEW_CARD_COUNT_1 as newCardCount1
		,R.NEW_CARD_COUNT_2 as newCardCount2,R.REBATE_PERCENTAGE as rebatePercentage,case when R.REQUEST_STATE in ('P','S') then 'Pending For Approval' when R.REQUEST_STATE='R' then 'Rejected' when R.REQUEST_STATE='A' then 'Active'  end status
		, request_State as CURRWFSTATUSID,checker_comments as  REJECTREASON,'' as WFSTATUSDESC,COALESCE(R.CHECKER_COMMENTS,'') checkerComments,ct.description as cardTypeName
		,R.REQUEST_STATE as requestState   
		FROM  mcpr_rebate_config_Stg R  left join  lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' WHERE (R.REQUEST_STATE='S'  or R.REQUEST_STATE='P'  or R.REQUEST_STATE='R') ORDER BY REBATE_ID DESC
	</select>
	<select id="getRebateList" resultType="RebateDTO">
		SELECT R.REBATE_ID as rebateID, R.CARD_TYPE as cardType, R.FINANCIAL_YEAR as financialYear, R.FEATURE_OR_BASE_FEE as featureOrBaseFee, R.OPERATOR_INDI as operatorIndi,R.NEW_CARD_COUNT_1 as newCardCount1
		,R.NEW_CARD_COUNT_2 as newCardCount2,R.REBATE_PERCENTAGE as rebatePercentage,'Active' status,ct.description as cardTypeName  
		FROM  mcpr_rebate_config R 
		inner join  mcpr_rebate_config_Stg Rs  on r.REBATE_ID=rs.REBATE_ID  left join  lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' WHERE Rs.REQUEST_STATE = 'A' ORDER BY r.REBATE_ID DESC
	</select>
	<select id="getRebateListMain" resultType="RebateDTO">
		SELECT R.REBATE_ID as rebateID, R.CARD_TYPE as cardType, R.FINANCIAL_YEAR as financialYear, R.FEATURE_OR_BASE_FEE as featureOrBaseFee, R.OPERATOR_INDI as operatorIndi,R.NEW_CARD_COUNT_1 as newCardCount1
		,R.NEW_CARD_COUNT_2 as newCardCount2,R.REBATE_PERCENTAGE as rebatePercentage,'Active' status,ct.description as cardTypeName  
		FROM  mcpr_rebate_config R 
		inner join  mcpr_rebate_config_Stg stg  on R.REBATE_ID=stg.REBATE_ID  
		left join  lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' 
		ORDER BY R.REBATE_ID DESC
	</select>
	<update id="updateRebate" >
		UPDATE mcpr_rebate_config SET NEW_CARD_COUNT_1 = #{newCardCount1}, NEW_CARD_COUNT_2 = #{newCardCount2}, REBATE_PERCENTAGE = #{rebatePercentage},fromrenage=#{fromrange} , torenage=#{torange} WHERE REBATE_ID = #{rebateID}
	</update>
	<update id="updateRebateMaker" >
		UPDATE mcpr_rebate_config_Stg SET card_type=#{cardType},financial_year=#{financialYear} ,feature_or_base_fee=#{featureOrBaseFee},operator_indi=#{operatorIndi},NEW_CARD_COUNT_1 = #{newCardCount1}, NEW_CARD_COUNT_2 = #{newCardCount2}, REBATE_PERCENTAGE = #{rebatePercentage}, LAST_UPDATED_BY= #{lastUpdatedBy}, LAST_UPDATED_ON= #{lastUpdatedOn}, STATUS= #{status},REQUEST_STATE=#{requestState},fromrenage=#{fromrange} , torenage=#{torange},LAST_OPERATION=#{lastOperation}, CHECKER_COMMENTS=#{checkerComments} WHERE REBATE_ID = #{rebateID}
	</update>
	
	<select id="fetchIdFromRebateIdSequence" resultType="int">
		SELECT nextval('mcpr_rebateid_seq')
	</select>
	<select id="fetchIdFromRebateIdSequenceTemp" resultType="int">
		SELECT nextval('mcpr_rebateid_seq_temp')
	</select>
	<select id="fetchIdFromRebateCardVariantIdSequence" resultType="int">
		SELECT nextval('mcpr_rebate_card_varientid_seq')
	</select>
	<select id="getCardVariantList" resultType="CardVariantDTO">
		SELECT code as cardVariantId,description as cardVariantName from lookup where type='CARD_VARIANT'
	</select>
	<select id="getCardVariantListNotSelected" resultType="CardVariantDTO">
		SELECT code as cardVariantId,description as cardVariantName from lookup where type='CARD_VARIANT' and code not in (select CARD_VARIANT_ID from mcpr_rebate_config_card_variant_mapping_stg where  rebate_id  = #{rebateId})
	</select>
	<select id="getRebateCardVariantMappingList" resultType="CardVariantDTO">
		select R.CARD_VARIANT_ID as cardVariantId,C.CARD_VARIANT_NAME as cardVariantName from mcpr_rebate_config_card_variant_mapping R inner join (select code as CARD_VARIANT_ID,description as CARD_VARIANT_NAME from lookup where type='CARD_VARIANT') C on R.card_variant_id=c.card_variant_id  where rebate_id  = #{rebateId}
	</select>
	<select id="getRebateCardVariantMappingListStg" resultType="CardVariantDTO">
		select distinct R.CARD_VARIANT_ID as cardVariantId, CARD_VARIANT_NAME as cardVariantName from mcpr_rebate_config_card_variant_mapping_stg R inner join (select code as CARD_VARIANT_ID,description as CARD_VARIANT_NAME  from lookup where type='CARD_VARIANT') C on R.card_variant_id=c.card_variant_id where rebate_id  = #{rebateId}
	</select>
	<insert id="insertBatchRebateCardVariantStg" >
		insert into mcpr_rebate_config_card_variant_mapping_stg (rebate_id,card_variant_id)
		values <foreach collection='rebateCardVariateDTO.cardVariantDTOs' item='cardVariant' separator=','>
		 ( #{cardVariant.rebateId,jdbcType=INTEGER},#{cardVariant.cardVariantId,jdbcType=INTEGER}
		)
		</foreach>
	</insert>
	<insert id="insertBatchRebateCardVariant" >
		insert into mcpr_rebate_config_card_variant_mapping (rebate_id,card_variant_id) select rebate_id,card_variant_id from mcpr_rebate_config_card_variant_mapping_stg WHERE rebate_id =#{rebateId}
	</insert>

	<delete id="deleteRebateCardVariant" >
		DELETE FROM mcpr_rebate_config_card_variant_mapping WHERE rebate_id =#{rebateId}
	</delete>
	<update id="updateRebateStgState" >
		UPDATE mcpr_rebate_config_Stg SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},last_operation= #{lastOperation}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} WHERE rebate_id  = #{rebateID}
	</update>
	<delete id="deleteRebateCardVariantStg" >
		DELETE FROM mcpr_rebate_config_card_variant_mapping_stg WHERE rebate_id =#{rebateId} 
	</delete>
	<update id="updateRebateStgByDiscard" >
		UPDATE mcpr_rebate_config_Stg SET NEW_CARD_COUNT_1 = #{newCardCount1}, NEW_CARD_COUNT_2 = #{newCardCount2}, REBATE_PERCENTAGE = #{rebatePercentage},CREATED_BY= #{createdBy}, CREATED_ON= #{createdOn}, LAST_UPDATED_BY= #{lastUpdatedBy}, LAST_UPDATED_ON= #{lastUpdatedOn},LAST_OPERATION= #{lastOperation}, REQUEST_STATE= #{requestState},fromrenage=#{fromrange} , torenage=#{torange} WHERE REBATE_ID = #{rebateID}
	</update>
	<insert id="insertBatchRebateCardVariantDiscard" >
		insert into mcpr_rebate_config_card_variant_mapping_stg (rebate_id,card_variant_id) 
		select rebate_id,card_variant_id from mcpr_rebate_config_card_variant_mapping WHERE rebate_id =#{rebateId}
	</insert>
	<select id="getDuplicateCheckRebate" resultType="RebateDTO">
		select R.rebate_id from mcpr_rebate_config_Stg r 
		inner join mcpr_rebate_config_card_variant_mapping_stg rm on r.rebate_id=rm.rebate_id 
		where  r.rebate_id != #{workFlowId} and card_type=#{cardType} 
		and financial_year= #{financialYear}  and feature_or_base_fee=#{featureOrBaseFee} 
		and rm.card_variant_id in (select card_variant_id from mcpr_rebate_config_card_variant_mapping_temp where rebate_id= #{rebateID}) and ( (#{fromrange} between fromrenage and torenage  ) or (#{torange} between fromrenage and torenage  ))
	</select>
	<select id="getDuplicateCheckRebateUpdateRecord" resultType="RebateDTO">
		select R.rebate_id from mcpr_rebate_config_Stg r 
			inner join mcpr_rebate_config_card_variant_mapping_stg rm on r.rebate_id=rm.rebate_id 
			where r.rebate_id != #{rebateID} and card_type=#{cardType} 
		and financial_year= #{financialYear}   and feature_or_base_fee=#{featureOrBaseFee} 
		and rm.card_variant_id in (select card_variant_id from mcpr_rebate_config_card_variant_mapping_stg where rebate_id= #{rebateID}) and ( (#{fromrange} between fromrenage and torenage  ) or (#{torange} between fromrenage and torenage  )) 
	</select>
	<insert id="insertBatchRebateCardVariantStgTemp" >
		insert into mcpr_rebate_config_card_variant_mapping_temp (rebate_id,card_variant_id)
		values <foreach collection='cardVariantDTOs' item='cardVariant' separator=','>
		 ( #{cardVariant.rebateId,jdbcType=INTEGER},#{cardVariant.cardVariantId,jdbcType=INTEGER}
		)
		</foreach>
	</insert>

	<delete id="deleteRebateCardVariantStgTemp" >
		DELETE FROM insertBatchRebateCardVariantStg_Temp WHERE rebate_id =#{rebateId}
	</delete>
	<select id="getLookUpList" resultType="CodeValueDTO">
		SELECT type ,code, description from lookup where type=#{type}
	</select>
	
	<insert id="saveRebate" >
	insert into mcpr_rebate_config(rebate_id,card_type,financial_year,feature_or_base_fee,operator_indi,new_card_count_1,new_card_count_2,rebate_percentage,created_by,created_on,last_updated_by,status,fromrenage,torenage,financial_year_number,last_updated_on)  
	VALUES 
	(#{rebateID}, #{cardType}, #{financialYear},#{featureOrBaseFee}, #{operatorIndi}, #{newCardCount1},#{newCardCount2},#{rebatePercentage},#{createdBy},#{createdOn},#{lastUpdatedBy}, #{requestState},#{fromrange},#{torange},#{financialYearNumber},#{lastUpdatedOn})
	</insert>
	
	
	<insert id="insertRebateStg" >
	insert into mcpr_rebate_config_Stg(rebate_id,card_type,financial_year,feature_or_base_fee,operator_indi,new_card_count_1,new_card_count_2,rebate_percentage,created_by,created_on,last_updated_by,status,LAST_UPDATED_ON,REQUEST_STATE, LAST_OPERATION,fromrenage,torenage,financial_year_Number) 
	 VALUES 
	 (#{rebateID}, #{cardType}, #{financialYear},#{featureOrBaseFee}, #{operatorIndi}, #{newCardCount1},#{newCardCount2},#{rebatePercentage},#{createdBy},#{createdDate},#{lastUpdatedBy}, #{requestState},#{lastUpdatedOn}, 'P', #{lastOperation},#{fromrange},#{torange} ,#{financialYearNumber})
	</insert>
</mapper>