<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<script type="text/javascript"
	src="./static/js/bootstrap-multiselect.js"></script>
<link rel="stylesheet" href="./static/css/bootstrap-multiselect.css"
	type="text/css" />
<link rel="stylesheet" href="./static/css/selectize.bootstrap3.min.css" />
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />
<head>
<title></title>
<script type="text/javascript"
	src="./static/js/validation/commonValidation.js"></script>
<jsp:include page="addEditMemberValMsg.jsp" />

<script type="text/javascript" src="./static/js/validation/MemberAdd.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberSettlementBin.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberAcquirerBin.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberDocument.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberIssuerBin.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberValidation.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberDateFunctions.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberGateway.js"></script>
<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<script src="./static/js/selectize.min.js"></script>
<style>
.overlay {
	display: none;
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 999;
	background: rgba(255, 255, 255, 0.8) url("./static/images/spinner.gif")
		center no-repeat;
}

.loadingdata {
	overflow: hidden;
}

.loadingdata .overlay {
	display: block;
}

input.largerChcekbox {
	width: 20px;
	height: 20px
}

button.multiselect {
	margin-top: -5px;
}

.disableFlag{

}
</style>
</head>

<!-- .form-control {
	background-color: white !important;
}
 -->
<script>
 var bankIFSCMapping={};
 var currencyCodeMapping={};
 var productTypeMapping={};
 var domainUsageMapping={};
 var binCardTypeMapping={};
 var binCardProductTypeMapping={};
 var binCardVariantMapping={};
 var binCardBrandMapping={};
 var binTypeMapping={};
 var dataSubmittedApproval=false;
 var submemberBankType='<spring:eval expression="@environment.getProperty('submember.bank.type')" />';
 var sponsorBankType='<spring:eval expression="@environment.getProperty('sponsor.bank.type')" />' ;
 var unallocatedIssBins=[];

 <c:forEach var="codeValue" items="${currencyCodeList}" >
 currencyCodeMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${productTypeList}" >
 productTypeMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${domainUsageList}" >
 domainUsageMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${binCardTypeList}" >
 binCardTypeMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${binProdTypeList}" >
 binCardProductTypeMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${binCardVariantList}" >
 binCardVariantMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${binCardBrandList}" >
 binCardBrandMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${binTypeList}" >
 binTypeMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="issBinData" items="${unallocatedIssBinData}" >
 unallocatedIssBins.push("${issBinData.binNumber}"); </c:forEach>
 <c:forEach var="codeValue" items="${domainUsageList}" >
 domainUsageMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="sponsorBank" items="${sponsorBankList}" >
 bankIFSCMapping["${sponsorBank.participantId}"]="${sponsorBank.ifscCode}"; </c:forEach>
 
 var settlementBinData=[]; 
 <c:if test="${not empty memberOnBoardingDTO.settlementBinList}">
  <c:forEach var="settlementBin" items="${memberOnBoardingDTO.settlementBinList}" varStatus="status">
  	settlementBinData.push({settlementBinId:"${settlementBin.settlementBinNumber}".replace("${memberOnBoardingDTO.ifscCode}",""), status: "${settlementBin.status}", settlementBinNumber:"${settlementBin.settlementBinNumber}",isDefault :"${settlementBin.isDefault}",settlementCurrency:"${settlementBin.settlementCurrency}",clearingAgencyType:"${settlementBin.clearingAgencyType}",isNew:false,actionType:"NA"}); </c:forEach>
 </c:if>
 var acquirerBinData=[];
 <c:if test="${not empty memberOnBoardingDTO.acqBinList}">
 <c:forEach var="acqBin" items="${memberOnBoardingDTO.acqBinList}" varStatus="status">
 acquirerBinData.push({acquirerId:"${acqBin.acquirerId}", status: "${acqBin.status}", acqDomainUsage:"${acqBin.acqDomainUsage}",acqBankGroup :"${acqBin.acqBankGroup}",acqFrmDateStr:"${acqBin.acqFrmDateStr}",acqToDateStr:"${acqBin.acqToDateStr}",acqProductType:"${acqBin.acqProductType}",offlineAllowed:"${acqBin.offlineAllowed}",acqSettlementBin:"${acqBin.acqSettlementBin}",isNew:false,actionType:"NA"}); </c:forEach>
</c:if>
var issuerBinData=[];
<c:if test="${not empty memberOnBoardingDTO.issBinList}">
<c:forEach var="issBin" items="${memberOnBoardingDTO.issBinList}" varStatus="status">
var listOfNetwork=[]
var strNetwork="${issBin.networkSelection}"
	strNetwork=strNetwork.substring(1,strNetwork.length-1);
listOfNetwork=strNetwork.split(",").map(x=>x.trim());

issuerBinData.push({issBinType:"${issBin.issBinType}",issBankGroup:"${issBin.issBankGroup}",binNumber:"${issBin.binNumber}",binLength:"${issBin.binLength}",additionalParams:"${issBin.additionalParams}",lowBin:"${issBin.lowBin}",highBin:"${issBin.highBin}",issFrmDateStr:"${issBin.issFrmDateStr}",issToDateStr:"${issBin.issToDateStr}",panLength:"${issBin.panLength}",binCardType:"${issBin.binCardType}",binProductType:"${issBin.binProductType}",binCardVariant:"${issBin.binCardVariant}",binCardBrand:"${issBin.binCardBrand}",issDomainUsage:"${issBin.issDomainUsage}",messageType:"${issBin.messageType}",cardTechnology:"${issBin.cardTechnology}",authMechanism:"${issBin.authMechanism}",subScheme:"${issBin.subScheme}",cardSubVariant:"${issBin.cardSubVariant}",programDetails:"${issBin.programDetails}",formFactor:"${issBin.formFactor}",issProductType:"${issBin.issProductType}",issSettlementBin:"${issBin.issSettlementBin}",offlineAllowed:"${issBin.offlineAllowed}",featureIssBin:"${issBin.featureIssBin}", status:"${issBin.status}",isNew:false,actionType:"NA",issBinTypeName:"${issBin.binDetailsCodeDescription.issBinType}",networkLicenseId:"${issBin.networkLicenseId}",networkIssId:"${issBin.networkIssId}",markUp:"${issBin.markUp}",networkSelection:listOfNetwork}); </c:forEach>
</c:if>
var documentData=[];
<c:if test="${not empty memberOnBoardingDTO.documents}">
<c:forEach var="document" items="${memberOnBoardingDTO.documents}" varStatus="status">
documentData.push({documentId:"${document.documentId}", documentName:"${document.documentName}",documentPath:"${document.documentPath}", isNew:false, status:"${document.status}",actionType:"NA"});</c:forEach>
</c:if>
var featureMap=[];
<c:forEach var="featureEntry" items="${featureMap}" >
featureMap['${featureEntry.key}']= '${featureEntry.value}';</c:forEach>
validationMessages["networkSelection"]="Enter Network Selection"
validationMessages["markUp"]="MarkUp should be between 0-100 and upto two decimal"
validationMessages["networkIssId"]="Network Issuer Id  should be six digit numeric"
validationMessages["networkLicenseId"]="Network License Id  should be six digit numeric"
</script>

<div id="memberBody" class="container-fluid height-min">
	<div class="overlay"></div>
	<div class="row">
		<div role="alert" style="display: none" id="jqueryError2">
			<div id="errorStatus2" class="alert alert-danger" role="alert">${errorStatus}</div>
		</div>
		<div role="alert" style="display: none" id="jquerySuccess">
			<div id="successStatus" class="alert alert-success" role="alert">${successStatus}</div>
		</div>
		<div id="errLvType" class="alert alert-danger" role="alert"
			style="display: none"></div>
	</div>
	<div>
		<input type="hidden" id="binCardTypesForJS"
			value="${binCardTypesForJS}" /> 
			<input type="hidden" id="domesticFlagParticipant"
			value="${memberOnBoardingDTO.domesticFlag}" /> 
			<input type="hidden" id="networkUsed"
			value="${InternationalParticipant}" />
			<input type="hidden" id="codeForNetwork"
			value="${networkCode}" /><input type="hidden" id="recStatus"
			value="${recStatus}" /> <input type="hidden" id="submitdisableCheck" />
		<input type="hidden" id="featureList" /> <input type="hidden"
			id="newMemberRejectedFlag" value="${NEWMEMREJECTED}" />
		<input type="hidden"
			id="subNetDefaultValue" value="${subNetDefaultValue}" />
	</div>
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="am.lbl.memberReg" /></span></strong>
		</div>


							
							
		<div class="panel-body">
			<form:form onsubmit="removeSpace(this); encodeForm(this);"
				method="POST" id="addEditMember"
				modelAttribute="memberOnBoardingDTO" enctype="multipart/form-data"
				action="addMember" autocomplete="off">
				<br />
				<form:hidden path="reqType" value="${reqType}" />
				<form:hidden path="requestState" />
				<input type="hidden" id="ApproveMemberFlag"
					value="${memberOnBoardingDTO.isActive}" />
				<div class="bs-example">
					<div class="panel-group" id="accordion">
						<div class="panel panel-default">
							<div class="panel-heading">
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#accordion"
										href="#collapseOne" id="collapseOneLink"><spring:message
											code="member.bankRegistration" /> <span
										class="glyphicon glyphicon-plus areaShowHde  "></span> </a>
								</h4>
							</div>
							<div id="collapseOne"
								class="panel-collapse collapse in panelHideShow">
								<div class="panel-body">
									<div class="">
										<div class="row">
											<div class="col-md-12">
												<div class="card">
													<div class="card-body">
														<div class="row">
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.bankType" /><span class="red">*</span></label>
																	<form:select path="memberType" id="memberType"
																		class="form-control">
																		<form:option value="0">
																			<spring:message code="msg.lbl.select"></spring:message>
																		</form:option>
																		<form:options itemLabel="description" itemValue="code"
																			items="${memberTypeList}" />
																	</form:select>
																	<div id="errmemberType">
																		<span for="memberType" class="error"><form:errors
																				path="memberType" /></span>
																	</div>

																</div>
															</div>
															<div class="col-md-3" id="parentMemIdDiv"
																style="display: none;">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.spBank" /><span class="red">*</span></label>
																	<form:select path="parentParticipantId"
																		id="parentParticipantId" name="sponserBank"
																		cssClass="form-control input-square ">
																		<form:option value="0">
																			<spring:message code="member.select" />
																		</form:option>
																		<form:options itemLabel="participantName"
																			itemValue="participantId" items="${sponsorBankList}" />
																	</form:select>
																	<div id="errparentParticipantId">
																		<span for="parentParticipantId" class="error"><form:errors
																				path="parentParticipantId" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.bankName" /><span class="red">*</span></label>
																	<form:input path="memberName" id="memberName"
																		name="memberName" maxlength="100"
																		cssClass="form-control input-square " />
																	<div id="errmemberName">
																		<span for="memberName" class="error"><form:errors
																				path="memberName" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.IFSC" /><span class="red">*</span></label>
																	<form:select path="ifscCode" id="ifscCode"
																		cssClass="form-control medantory">
																		<form:option value="SELECT" >
																			<spring:message code="msg.lbl.select"></spring:message>
																		</form:option>
																		<form:options itemLabel="lkpDesc" itemValue="lkpValue"
																			items="${ifscCodesList}" />
																	</form:select>
																	<div id="errifscCode">
																		<span for="ifscCode" class="error"><form:errors
																				path="ifscCode" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2" id="bankSectorDiv">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.bankSector" /> <span class="red">*</span></label>
																	<form:select path="bankSector" id="bankSector"
																		class="form-control">
																		<form:option value="0">
																			<spring:message code="msg.lbl.select"></spring:message>
																		</form:option>
																		<form:options itemLabel="description"
																				itemValue="code" items="${bankSectorList}" />
																	</form:select>
																	<div id="errbankSector">
																		<span for="bankSector" class="error"><form:errors
																				path="bankSector" /></span>
																	</div>
																</div>
															</div>
														</div>
														<div class="row">
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.bnkmastrcode" /><span class="red">*</span></label>
																			<c:choose>
																			<c:when test="${InternationalParticipant eq 'Y'}">
																			<form:input path="bankMasterCode" id="bankMasterCode"
																		name="bankMasterCode"
																		cssClass="form-control input-square" />
																			</c:when>
																			<c:otherwise>
																			<form:input path="bankMasterCode" id="bankMasterCode"
																		name="bankMasterCode"
																		cssClass="form-control input-square" readonly="true" />
																			</c:otherwise>
																			</c:choose>
																	
																
																<div id="errbankMasterCode">
																	<span for="bankMasterCode" class="error"><form:errors
																			path="bankMasterCode" /></span>
																</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.RTGScode" /><span class="red">*</span></label>
																			
																			<c:choose>
																			<c:when test="${InternationalParticipant eq 'Y'}">
																		<form:input path="rtgsCode" id="rtgsCode"
																		name="rtgsCode" cssClass="form-control input-square"
																		/>
																			</c:when>
																			<c:otherwise>
																			<form:input path="rtgsCode" id="rtgsCode"
																		name="rtgsCode" cssClass="form-control input-square"
																		readonly="true" />
																			</c:otherwise>
																			</c:choose>
																	
																
																<div id="errrtgsCode">
																	<span for="rtgsCode" class="error"><form:errors
																			path="rtgsCode" /></span>
																</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.participantId" /><span class="red">*</span></label>
																			
																				<c:choose>
																			<c:when test="${InternationalParticipant eq 'Y'}">
																		<form:input path="participantId" id="participantId"
																		name="participantId"
																		cssClass="form-control input-square" />
																			</c:when>
																			<c:otherwise>
																		<form:input path="participantId" id="participantId"
																		name="participantId"
																		cssClass="form-control input-square" readonly="true" />
																			</c:otherwise>
																			</c:choose>
																	
																	
																
																<div id="errparticipantId">
																	<span for="participantId" class="error"><form:errors
																			path="participantId" /></span>
																</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.savingsAccNo" /> <span class="red">*</span>
																	</label>
																	
																	<c:choose>
																			<c:when test="${InternationalParticipant eq 'Y'}">
																		<form:input path="savingsAccNumber"
																		id="savingsAccNumber" name="savingsAccNumber"
																		cssClass="form-control input-square" />
																			</c:when>
																			<c:otherwise>
																		<form:input path="savingsAccNumber"
																		id="savingsAccNumber" name="savingsAccNumber"
																		cssClass="form-control input-square" readonly="true" />
																			</c:otherwise>
																			</c:choose>
																	
																	
																
																<div id="errsavingsAccNumber">
																	<span for="savingsAccNumber" class="error"><form:errors
																			path="savingsAccNumber" /></span>
																</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.currentAccNo" /> <span class="red">*</span>
																	</label>
																	
																		<c:choose>
																			<c:when test="${InternationalParticipant eq 'Y'}">
																			<form:input path="currentAccNumber"
																		id="currentAccNumber" name="currentAccNumber"
																		cssClass="form-control input-square"  />
																			</c:when>
																			<c:otherwise>
																			<form:input path="currentAccNumber"
																		id="currentAccNumber" name="currentAccNumber"
																		cssClass="form-control input-square" readonly="true" />
																			</c:otherwise>
																			</c:choose>
																
																
																<div id="errcurrentAccNumber">
																	<span for="currentAccNumber" class="error"><form:errors
																			path="currentAccNumber" /></span>
																</div>
																</div>
															</div>
														</div>
														<div class="row">
															<div class="col-md-2" id="uniqueBnkNameDiv">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.uniqBankName" /><span class="red">*</span></label>
																	<form:select path="uniqueBnkName" id="uniqueBnkName"
																		class="form-control">
																		<form:option value="SELECT">
																			<spring:message code="sm.lbl.others"></spring:message>
																		</form:option>
																	</form:select>
																
																<div id="erruniqueBnkName">
																	<span for="uniqueBnkName" class="error"><form:errors
																			path="uniqueBnkName" /></span>
																</div>
																</div>
															</div>
															<div id="uniqueBnkDiv" class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.uniqBank" /><span class="red">*</span></label>
																	<form:input path="uniqueBnk" id="uniqueBnk"
																		name="uniqueBnk" maxlength="100"
																		cssClass="form-control input-square" readonly="false" />
																
																<%-- 
																value="${uniqueBank}" --%>
																<div id="erruniqueBnk">
																	<span for="uniqueBnk" class="error"><form:errors
																			path="uniqueBnk" /></span>
																</div>
																</div>
															</div>
															<%-- <c:if test="${editFinalMember ne 'Yes'}"> --%>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.nfsParticipantId" /><span class="red">*</span></label>
																	<form:input path="participantIdNFS"
																		id="participantIdNFS" name="participantIdNFS"
																		maxlength="3" cssClass="form-control input-square disableFlag" />
																
																<div id="errparticipantIdNFS">
																	<span for="participantIdNFS" class="error"><form:errors
																			path="participantIdNFS" /></span>
																</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																code="member.subNet" /><span
																		class="red">*</span></label>
																	<form:select path="subNet" id="subNet"
																		class="form-control disableFlag">																		
																		<form:options itemLabel="description" itemValue="code"
																			items="${subNetList}" />
																	</form:select>
																
																<div id="errsubNet">
																	<span for="subNet" class="error"><form:errors
																			path="subNet" /></span>
																</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																code="member.maxUser" /><span
																		class="red">*</span></label>
																	<form:input path="maxUser"
																		id="maxUser" name="maxUser"
																		maxlength="3" cssClass="form-control input-square" />
																
																<div id="errmaxUser">
																	<span for="maxUser" class="error"><form:errors
																			path="maxUser" /></span>
																</div>
																</div>
															</div>

														</div>
														<div class="row">
															<div class="form-group">
																<label for="squareInput"> <strong> 
																		<spring:message code="member.contactInfo" />
																</strong>
																</label>
															</div>
														</div>
														<div class="row">
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.phoneNo1" /><span class="red">*</span></label>
																	<form:input path="bnkPhone" id="bnkPhone"
																		name="bnkPhone" value="" minlength="8" maxlength="12"
																		cssClass="form-control input-square" />
																	<div id="errbnkPhone">
																		<span for="bnkPhone" class="error"><form:errors
																				path="bnkPhone" /></span>
																	</div>

																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.phoneNo2" /></label>
																	<form:input path="bnkPhone2" id="bnkPhone2"
																		name="bnkPhone2" value="" maxlength="12"
																		cssClass="form-control input-square" />
																	<div id="errbnkPhone2">
																		<span for="bnkPhone2" class="error"></span>
																		<form:errors path="bnkPhone2" />
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.mobileNo1" /><span class="red">*</span></label>
																	<form:input path="bnkMobile" id="bnkMobile"
																		name="bnkMobile" minlength="10" maxlength="15"
																		cssClass="form-control  input-square" />
																	<div id="errbnkMobile">
																		<span for="bnkMobile" class="error"><form:errors
																				path="bnkMobile" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.mobileNo2" /></label>
																	<form:input path="bnkMobile2" id="bnkMobile2"
																		name="bnkMobile2" maxlength="15"
																		cssClass="form-control  input-square" />
																	<div id="errbnkMobile2">
																		<span for="errbnkMobile2" class="error"></span>
																		<form:errors path="bnkMobile2" />
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.emailadrs1" /><span class="red">*</span></label>
																	<form:input path="bnkEmail" id="bnkEmail"
																		name="bnkEmail" maxlength="100"
																		cssClass="form-control input-square" />
																	<div id="errbnkEmail">
																		<span for="bnkEmail" class="error"><form:errors
																				path="bnkEmail" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.emailadrs2" /></label>
																	<form:input path="bnkEmail2" id="bnkEmail2"
																		name="bnkEmail2" maxlength="100"
																		cssClass="form-control input-square" />
																
																<div id="errbnkEmail2">
																	<span for="bnkEmail2" class="error"><form:errors
																			path="bnkEmail2" /></span>
																</div>
																</div>
															</div>
														</div>
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.legaladrs" /><span class="red">*</span></label>
																	<form:input path="bnkAdd" id="bnkAdd" name="bnkAdd"
																		maxlength="200" cssClass="form-control input-square" />

																	<div id="errbnkAdd">
																		<span for="bnkAdd" class="error"><form:errors
																				path="bnkAdd" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.country" /><span class="red">*</span></label>
																	<form:select path="bnkCountry" class="form-control"
																		id="bnkCountry">
																		<form:option value="0">
																			<spring:message code="msg.lbl.select"></spring:message>
																		</form:option>
																		<form:options items="${countryList}"
																			itemValue="countryId" itemLabel="countryName" />
																	</form:select>
																	<div id="errbnkCountry">
																		<span for="bnkCountry" class="error"><form:errors
																				path="bnkCountry" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.state" /><span class="red">*</span></label>
																	<form:select path="bnkState" id="bnkState"
																		class="form-control">
																		<form:option value="0">
																			<spring:message code="msg.lbl.select"></spring:message>
																		</form:option>
																		<form:options itemLabel="lkpDesc" itemValue="lkpValue"
																			items="${stateList}" />
																	</form:select>
																	<div id="errbnkState">
																		<span for="bnkState" class="error"><form:errors
																				path="bnkState" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.city" /><span class="red">*</span></label>
																	<form:select path="bnkCity" id="bnkCity"
																		class="form-control">
																		<form:option value="0">
																			<spring:message code="msg.lbl.select"></spring:message>
																		</form:option>
																		<form:options itemLabel="lkpDesc" itemValue="lkpValue"
																			items="${cityList}" />
																	</form:select>
																	<div id="errbnkCity">
																		<span for="bnkCity" class="error"><form:errors
																				path="bnkCity" /></span>
																	</div>
																</div>
															</div>
														</div>
														<div class="row">
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.zipcode" /><span class="red">*</span></label>
																	<form:input path="bnkPincode" id="bnkPincode"
																		name="bnkPincode" maxlength="6"
																		cssClass="form-control input-square" />
																	<div id="errbnkPincode">
																		<span for="bnkPincode" class="error"><form:errors
																				path="bnkPincode" /></span>
																	</div>
																</div>
															</div>
														</div>
														<div class="row">
															<div class="col-md-3">
																&nbsp;
															</div>
														</div>
														<a id="gstDiv">
														<div class="row">
															<div class="form-group">
																<label for="squareInput"> <strong> 
																		<spring:message code="member.gstdetails" />
																</strong></label>
															</div>
														</div>
														<div class="row">
															<div class="col-md-3">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.gstin" /><span class="red">*</span></label>
																	<form:input path="gstIn" id="gstn" name="gstn" value=""
																		maxlength="15" cssClass="form-control input-square" />
																	<div id="errgstn">
																		<span for="gstn" class="error"><form:errors
																				path="gstn" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.gstaddrs" /><span class="red">*</span></label>
																	<form:input path="gstAdd" id="gstAdd" name="gstAdd"
																		maxlength="200" cssClass="form-control input-square" />

																	<div id="errgstAdd">
																		<span for="gstAdd" class="error"><form:errors
																				path="gstAdd" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.gstcountry" /><span class="red">*</span></label>
																	<form:select path="gstCountry" class="form-control"
																		id="gstCountry">
																		<form:option value="0">
																			<spring:message code="msg.lbl.select"></spring:message>
																		</form:option>
																		<form:options items="${countryList}"
																			itemValue="countryId" itemLabel="countryName" />
																	</form:select>
																	<div id="errgstCountry">
																		<span for="gstCountry" class="error"><form:errors
																				path="gstCountry" /></span>
																	</div>
																</div>
															</div>
														</div>
														<div class="row">
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareSelect"><spring:message
																			code="member.gststate" /><span class="red">*</span></label>
																	<form:select path="gstState" id="gstState"
																		class="form-control">
																		<form:option value="0">
																			<spring:message code="msg.lbl.select"></spring:message>
																		</form:option>
																		<form:options itemLabel="lkpDesc" itemValue="lkpValue"
																			items="${stateList}" />
																	</form:select>
																	<div id="errgstState">
																		<span for="gstState" class="error"><form:errors
																				path="gstState" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.gstcity" /><span class="red">*</span></label>
																	<form:select path="gstCity" id="gstCity"
																		class="form-control">
																		<form:option value="0">
																			<spring:message code="msg.lbl.select"></spring:message>
																		</form:option>
																		<form:options itemLabel="lkpDesc" itemValue="lkpValue"
																			items="${cityListGST}" />
																	</form:select>
																	<div id="errgstCity">
																		<span for="gstCity" class="error"><form:errors
																				path="gstCity" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.gstzipcode" /><span class="red">*</span></label>
																	<form:input path="gstPincode" id="gstPincode"
																		name="gstPincode" maxlength="6"
																		cssClass="form-control input-square" />
																	<div id="errgstPincode">
																		<span for="gstPincode" class="error"><form:errors
																				path="gstPincode" /></span>
																	</div>
																</div>
															</div>
															<div class="col-md-2">
																<div class="form-group">
																	<label for="squareInput"><spring:message
																			code="member.website" /></label>
																	<form:input path="webSite" id="webSite" name="webSite"
																		maxlength="100" cssClass="form-control input-square" />
																	<div id="errwebSite">
																		<span for="webSite" class="error"><form:errors
																				path="webSite" /></span>
																	</div>
																</div>
															</div>
														</div>
														</a>
													</div>
												</div>
											</div>
										</div>
										<c:if test="${reqType eq 'E'}">
											<a href="#" id="clearMemData" value="Clear"
												class="btn btn-success"><spring:message
													code="member.clrBtn" /></a>
										</c:if>
										<c:if test="${reqType eq 'ADD'}">
											<a href="#" id="clearAddMemData" value="Clear"
												class="btn btn-success"><spring:message
													code="member.clrBtn" /></a>
										</c:if>
									</div>
									<form:hidden path="saveMemberResult" id="saveMemberResult"
										value="${saveMemberResult}" />
								</div>
							</div>
						</div>
							<c:if test="${InternationalParticipant eq 'Y'}">
										<c:import url='settlementPanel.jsp' />
										</c:if>
						<div id="afterSave" style="display: none;">
							<div class="panel panel-default">
								<div class="panel-heading">
									<h4 class="panel-title">
										<a data-toggle="collapse" data-parent="#accordion"
											href="#collapseThree" id="collapseThreeLink"><spring:message
												code="member.contactinfo" /> <span
											class="glyphicon glyphicon-plus areaShowHde"></span> </a>
									</h4>
								</div>
								<div id="collapseThree"
									class="panel-collapse collapse panelHideShow">
									<div class="panel-body">
										<div class="">
											<div class="row">
												<div class="col-md-12">
													<div class="card">
														<div class="card-body">
															<div class="row">
																<div class="col-md-2">
																	<div class="form-group">
																		<label for="squareSelect"><spring:message
																				code="member.addrstype" /><span class="red">*</span></label>
																		<form:select path="addressType" id="addressType"
																			class="form-control">
																			<form:option value="0">
																				<spring:message code="msg.lbl.select"></spring:message>
																			</form:option>
																			<form:options itemLabel="description"
																				itemValue="code" items="${addressTypeList}" />
																		</form:select>
																		<div id="erraddressType">
																			<span for="addressType" class="error"><form:errors
																					path="addressType" /></span>
																		</div>
																	</div>
																</div>
																<div class="col-md-3">
																	<div class="form-group">
																		<label for="squareSelect"><spring:message
																				code="member.name" /><span class="red">*</span> </label>
																		<form:input path="cntChkrName" id="cntChkrName"
																			name="cntChkrName" maxlength="50"
																			cssClass="form-control input-square" />
																		<div id="errcntChkrName">
																			<span for="cntChkrName" class="error"><form:errors
																					path="cntChkrName" /></span>
																		</div>
																	</div>
																</div>
																<div class="col-md-3">
																	<div class="form-group">
																		<label for="squareInput"><spring:message
																				code="member.phno" /><span class="red">*</span></label>
																		<form:input path="cntPhone" id="cntPhone"
																			name="cntPhone" maxlength="12"
																			cssClass="form-control input-square" />
																		<div id="errcntPhone">
																			<span for="cntPhone" class="error"><form:errors
																					path="cntPhone" /></span>
																		</div>
																	</div>
																</div>
																<div class="col-md-3">
																	<div class="form-group">
																		<label for="squareSelect"><spring:message
																				code="member.moblno" /></label>
																		<form:input path="cntMobile" id="cntMobile"
																			name="cntMobile" maxlength="15"
																			cssClass="form-control input-square" />
																		<div id="errcntMobile">
																			<span for="cntMobile" class="error"><form:errors
																					path="cntMobile" /></span>
																		</div>
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-3">
																	<div class="form-group">
																		<label for="squareSelect"><spring:message
																				code="member.faxno" /></label>
																		<form:input path="cntFax" id="cntFax" name="cntFax"
																			maxlength="12" cssClass="form-control input-square" />
																		<div id="errcntFax">
																			<span for="cntFax" class="error"><form:errors
																					path="cntFax" /></span>
																		</div>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group">
																		<label for="squareInput"><spring:message
																				code="member.legaladdrs" /><span class="red">*</span></label>
																		<form:input path="cntAdd1" id="cntAdd1" name="cntAdd1"
																			maxlength="500" cssClass="form-control input-square" />

																		<div id="errcntAdd1">
																			<span for="cntAdd1" class="error"><form:errors
																					path="cntAdd1" /></span>
																		</div>

																	</div>
																</div>
																<div class="col-md-2">
																	<div class="form-group">
																		<label for="squareSelect"><spring:message
																				code="member.country" /><span class="red">*</span></label>
																		<form:select path="cntCountry" class="form-control"
																			id="cntCountry">
																			<form:option value="0">
																				<spring:message code="msg.lbl.select"></spring:message>
																			</form:option>
																			<form:options items="${countryList}"
																				itemValue="countryId" itemLabel="countryName" />
																		</form:select>
																		<div id="errcntCountry">
																			<span for="cntCountry" class="error"><form:errors
																					path="cntCountry" /></span>
																		</div>
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-3">
																	<div class="form-group">
																		<label for="squareSelect"><spring:message
																				code="member.state" /><span class="red">*</span></label>
																		<form:select path="cntState" id="cntState"
																			class="form-control">
																			<form:option value="0">
																				<spring:message code="msg.lbl.select"></spring:message>
																			</form:option>
																			<form:options itemLabel="lkpDesc"
																				itemValue="lkpValue" items="${stateList}" />
																		</form:select>
																		<div id="errcntState">
																			<span for="cntState" class="error"><form:errors
																					path="cntState" /></span>
																		</div>
																	</div>
																</div>
																<div class="col-md-3">
																	<div class="form-group">
																		<label for="squareInput"><spring:message
																				code="member.city" /><span class="red">*</span></label>
																		<form:select path="cntCity" id="cntCity"
																			class="form-control">
																			<form:option value="0">
																				<spring:message code="msg.lbl.select"></spring:message>
																			</form:option>
																			<form:options itemLabel="lkpDesc"
																				itemValue="lkpValue" items="${cityLists}" />
																		</form:select>
																		<div id="errcntCity">
																			<span for="cntCity" class="error"><form:errors
																					path="cntCity" /></span>
																		</div>
																	</div>
																</div>
																<div class="col-md-3">
																	<div class="form-group">
																		<label for="squareInput"><spring:message
																				code="member.zipcode" /><span class="red">*</span></label>
																		<form:input path="cntPincode" id="cntPincode"
																			name="cntPincode" maxlength="6"
																			cssClass="form-control input-square" />
																		<div id="errcntPincode">
																			<span for="cntPincode" class="error"><form:errors
																					path="cntPincode" /></span>
																		</div>
																	</div>
																</div>
																<div class="col-md-2">
																	<div class="form-group">
																		<label for="squareInput"><spring:message
																				code="member.emailad" /><span class="red">*</span></label>
																		<form:input path="cntEmail" id="cntEmail"
																			name="cntEmail" maxlength="100"
																			cssClass="form-control input-square" />
																		<div id="errcntEmail">
																			<span for="cntEmail" class="error"><form:errors
																					path="cntEmail" /></span>
																		</div>
																	</div>
																</div>
															</div>
															<div class="row">
																<div class="col-md-2">
																	<div class="form-group">
																		<label for="squareInput"><spring:message
																				code="member.authOffDesgn" /> </label>
																		<form:input path="cntDesignation" id="cntDesignation"
																			name="cntDesignation" maxlength="100"
																			cssClass="form-control input-square" />
																		<div id="errcntDesignation">
																			<span for="cntDesignation" class="error"><form:errors
																					path="cntDesignation" /></span>
																		</div>
																	</div>
																</div>
															</div>
															<a href="#" id="clearMemContactData" value="Clear"
																class="btn btn-success"><spring:message
																	code="member.clrBtn" /></a>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						
							<div class="panel panel-default">
								<div class="panel-heading">
									<h4 class="panel-title">
										<a data-toggle="collapse" data-parent="#accordion"
											href="#collapseTwo" id="collapseTwoLink"><spring:message
												code="member.bindetails" /><span
											class="glyphicon glyphicon-plus areaShowHde "></span></a>
									</h4>
								</div>
								<div id="collapseTwo"
									class="panel-collapse collapse panelHideShow">
									<div class="panel-body">
										<!-- Settlement Bin start -->
										<div class="row">
											<div class="col-md-12">
												<div class="card">
													<div class="card-header">
														<div class="card-title">
															<spring:message code="member.settlmntbindetails" />
														</div>
													</div>
													<div class="card-body">
														<div id="SettlementBinDetailsDiv">
															<table id="dataTable" border="0">
															<caption style="display:none;">Member add</caption> 
															<th scope="col"></th>
																<tr id="newSettlementBin">
																	<div class="row" id="newSettlementBinData">
																		<div class="card-body" id="newSettlementBin">
																			<div class="row">
																				<div id="settlementBinIdDiv" class="col-md-4">
																					<div class="form-group">
																						<label for="squareSelect"><spring:message
																								code="member.settlmntbinid" /> <span class="red">*</span>
																						</label> <br />
																						<div class="col-md-2">
																						<span id="SettlementIFSCCode">${memberOnBoardingDTO.ifscCode}</span>
																						</div>
																						<div class="col-md-2">
																						<form:input path="settlementBinId"
																							id="settlementBinId" name="settlementBinId"
																							cssStyle="width: 40px;" maxlength="2"
																							cssClass="form-control input-square" />
																						</div>
																						<div id="errsettlementBinId">
																							<span for="settlementBinId" class="error"><form:errors
																									path="settlementBinId" /></span>
																						</div>
																					</div>
																				</div>
																				<div class="col-md-2">
																					<div class="form-group">
																						<label for="squareSelect"><spring:message
																								code="member.currencyCode" /><span class="red">*</span></label>
																						<form:select path="currencyCode" id="currencyCode"
																							class="form-control">
																							<form:option value="0">
																								<spring:message code="msg.lbl.select"></spring:message>
																							</form:option>
																							<form:options itemLabel="description"
																								itemValue="code" items="${currencyCodeList}" />
																						</form:select>
																						<div id="errcurrencyCode">
																							<span for="currencyCode" class="error"><form:errors
																									path="currencyCode" /></span>
																						</div>
																					</div>
																				</div>
																				<div class="col-md-2">
																					<div class="form-group">
																						<label for="squareSelect"> &nbsp; &nbsp; &nbsp;
																							&nbsp; &nbsp;
																						</label>
																						<div>
																							<input id="isDefaultBin" name="isDefaultBin"
																								type="checkbox" class="largerChcekbox" /> <label
																								for="squareSelect"><spring:message
																									code="member.default" /></label>
																						</div>
																					</div>
																				</div>
																			<div class="col-md-2">
																			 <div class="form-group">
																									<label for="squareSelect"><spring:message
																											code="member.clearingAgencyType" /><span class="red">*</span></label>
																									<form:select path="clearingAgencyType"
																										id="clearingAgencyType" class="form-control">
																									
																										<form:options itemLabel="description"
																											itemValue="description" items="${clearingAgencyTypeList}" />
																									</form:select> 
																									
																									
																									
																									<div id="errclearingAgencyType">
																										<span for="clearingAgencyType" class="error"><form:errors
																												path="clearingAgencyType" /></span>
																									</div>
																			 </div>
																			</div>
																			</div>
																			<a href="#/" id="clearSettlementBin" value="Clear"
																				class="btn btn-success"><spring:message
																					code="member.clrBtn" /></a> <a href="#/"
																				id="saveSettlementBin" class="btn btn-success"><spring:message
																					code="member.saveBtn" /></a>
																		</div>
																	</div>
																</tr>
																<tr>
																	<td><input type='hidden'
																		id='settlementOldBinNumber' /></td>
																</tr>
																<tr>
																	<td><input type='hidden' id='oldOrNewBinFlag'
																		value='NewBin' /></td>
																</tr>
																<div class="panel-body">
																<div id="errsettlementBin" style="display: none" ><span class="error"></span></div>
																	<table class="table table-striped">
																	<caption style="display:none;">Member add</caption> 
																		<thead>
																			<tr>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.participantId" /> </label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.settlmntBin" /> </label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.settlmntcurrency" /> </label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.clearingAgencyType" /> </label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.isdefaultbin" /> </label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.status" /></label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.action" /></label></th>
																			</tr>
																		</thead>
																		<tbody id="settlementBinsList">
																			<c:if
																				test="${not empty memberOnBoardingDTO.settlementBinList}">
																				<c:forEach var="settlementBin"
																					items="${memberOnBoardingDTO.settlementBinList}"
																					varStatus="status">

																					<tr>
																						<td>${settlementBin.participantId}</td>
																						<td>${settlementBin.settlementBinNumber}</td>
																						<td>${settlementBin.settlementCurrencyDescription}</td>
																						<td>${settlementBin.clearingAgencyType}</td>
																						<td>${settlementBin.isDefault}</td>
																						<td><c:choose>
																								<c:when test="${settlementBin.status eq 'B'}">
																									<spring:message code="member.blckd" />
																								</c:when>
																								<c:otherwise>
																									<spring:message code="member.active" />
																								</c:otherwise>
																							</c:choose></td>
																						<td><a
																							href="javascript:editSettlementBin('${settlementBin.settlementBinNumber}')"><span
																								class="glyphicon glyphicon-pencil my-tooltip"
																								title="EDIT"></span></a> <c:choose>
																								<c:when test="${(settlementBin.status eq 'B')}">
																							&nbsp; &nbsp;		<a
																										href="javascript:unblockSettlementBin('${settlementBin.settlementBinNumber}')"><span
																										class="glyphicon glyphicon-ok-circle my-tooltip"
																										title="UNBLOCK"></span></a>
																								</c:when>
																								<c:otherwise>
																								&nbsp; &nbsp;	<a
																										href="javascript:blockSettlementBin('${settlementBin.settlementBinNumber}')"><span
																										class="glyphicon glyphicon-ban-circle my-tooltip"
																										title="BLOCK"></span></a>
																								</c:otherwise>
																							</c:choose></td>
																					</tr>
																				</c:forEach>
																			</c:if>
																		</tbody>
																	</table>
																</div>
															</table>
														</div>
													</div>
													<!-- Settlement Bin end -->
													<div class="row">
														<div class="col-md-12">
															<div class="card">
																<div class="card-header">
																	<div class="card-title">
																		<spring:message code="member.acqbindetails" />
																	</div>
																</div>
																<div class="card-body">
																	<div id="AcquirerBinDetailsDiv">
																		<table id="dataTable" border="0">
																		<caption style="display:none;">Member add</caption> 
																		<th scope="col"></th>
																			<tr id="newAcquirerBin" hidden>
																				<div class="row" id="newAcquirerBinData">
																					<div class="card-body">
																						<div class="row">
																							<div class="col-md-2">
																								<div class="form-group">
																									<label for="squareSelect"><spring:message
																											code="member.bankgroup" /><span class="red">*</span></label>
																									<form:select path="acqBankGroup" placeholder="Pick a Bank Group..."
																										id="acqBankGroup" class="form-control">
																										<form:option value="0">
																											<spring:message code="msg.lbl.select"></spring:message>
																										</form:option>
																										<form:options itemLabel="description"
																											itemValue="code" items="${bankGroupList}" />
																									</form:select>
																									<div id="erracqBankGroup">
																										<span for="acqBankGroup" class="error"><form:errors
																												path="acqBankGroup" /></span>
																									</div>
																								</div>
																							</div>
																							<div class="col-md-2">
																								<div class="form-group">
																									<label for="squareInput"><spring:message
																											code="member.acqid" /><span class="red">*</span>
																									</label>
																									<form:input path="acquirerId" id="acquirerId"
																										name="acquirerId" maxlength="6"
																										cssClass="form-control input-square" />
																									<div id="erracquirerId">
																										<span for="acquirerId" class="error"><form:errors
																												path="acquirerId" /></span>
																									</div>
																								</div>
																							</div>
																							<div class="col-md-2">
																								<div class="form-group">
																									<label for="squareSelect"><spring:message
																											code="member.domainusage" /><span class="red">*</span></label>
																									<form:select path="acqDomainUsage"
																										id="acqDomainUsage" class="form-control">
																										<form:option value="0">
																											<spring:message code="msg.lbl.select"></spring:message>
																										</form:option>
																										<form:options itemLabel="description"
																											itemValue="code" items="${domainUsageList}" />
																									</form:select>
																									<div id="erracqDomainUsage">
																										<span for="acqDomainUsage" class="error"><form:errors
																												path="acqDomainUsage" /></span>
																									</div>
																								</div>
																							</div>
																							<div class="col-md-2">
																								<div class="form-group">
																									<label for="squareSelect"><spring:message
																											code="member.activtndate" /> <span
																										class="red">*</span> </label> <input type="text"
																										path="acqFrmDate"
																										data-date-format="dd-mm-yyyy" id="acqFrmDate"
																										name="acqFrmDate"
																										class="form-control input-square"
																										readonly="true" />

																									<div id="erracqFrmDate">
																										<span for="acqFrmDate" class="error"><form:errors
																												path="acqFrmDate" /></span>
																									</div>
																								</div>
																							</div>
																							<div class="col-md-2">
																								<div class="form-group">
																									<label for="squareInput"><spring:message
																											code="member.deactdate" /> <span class="red">*</span>
																									</label> <input type="text" path="acqToDate"
																										data-date-format="dd-mm-yyyy" id="acqToDate"
																										name="acqToDate"
																										class="form-control input-square"
																										readonly="true" />

																									<div id="erracqToDate">
																										<span for="acqToDate" class="error"><form:errors
																												path="acqToDate" /></span>
																									</div>
																								</div>
																							</div>
																							<div class="col-md-2" id="acqProductTypeDiv">
																								<div class="form-group">
																									<label for="squareSelect"><spring:message
																											code="member.pdttype" /><span class="red">*</span></label>
																									<form:select path="acqProductType"
																										id="acqProductType" class="form-control">
																										<%-- <form:option value="0">
																											<spring:message code="msg.lbl.select"></spring:message>
																										</form:option> --%>
																										<form:options itemLabel="description"
																											itemValue="code" items="${productTypeList}" />
																									</form:select>
																									<div id="erracqProductType">
																										<span for="acqProductType" class="error"><form:errors
																												path="acqProductType" /></span>
																									</div>
																								</div>
																							</div>
																						</div>
																						<div class="row">
																							<div class="col-md-2">
																								<div class="form-group">
																									<label for="squareSelect"><spring:message
																											code="member.settmntBIN" /> <span class="red">*</span>
																									</label>
																									<form:select path="acqSettlementBin"
																										id="acqSettlementBin" class="form-control">
																										<form:option value="0">
																											<spring:message code="msg.lbl.select"></spring:message>
																										</form:option>
																										<c:if
																											test="${not empty memberOnBoardingDTO.settlementBinList}">
																											<form:options itemLabel="settlementBinNumber"
																												itemValue="settlementBinNumber"
																												items="${memberOnBoardingDTO.settlementBinList}" />
																										</c:if>
																									</form:select>
																									<div id="erracqSettlementBin">
																										<span for="acqSettlementBin" class="error"><form:errors
																												path="acqSettlementBin" /></span>
																									</div>
																								</div>
																							</div>
																							<div class="col-md-3">
																								<div class="form-group">
																									<label for="squareSelect"><spring:message
																											code="member.isOfflAllowd" /> <span
																										class="red">*</span> </label>
																									<div>
																										<select name="acqOfflineAllowed"
																											id="acqOfflineAllowed">
																											<option value="N"><spring:message
																													code="member.no" /></option>
																											<option value="Y"><spring:message
																													code="member.yes" /></option>
																										</select>
																									</div>
																									<span id="errsacqOfflineAllowed" class="error"></span>
																								</div>
																							</div>
																						</div>
																						<a type="button" id="clearAcquirerBin"
																							value="Clear" class="btn btn-success"><spring:message
																								code="member.clrBtn" /></a> <a href="#/"
																							id="saveAcquirerBin" class="btn btn-success"><spring:message
																								code="member.saveBtn" /></a>
																					</div>
																				</div>
																			</tr>
																			<tr>
																				<td><input type='hidden' id='oldAcquirerId' /></td>
																			</tr>
																			<div class="panel-body">
																				<table class="table table-striped">
																				<caption style="display:none;">Member add</caption> 
																					<thead>
																						<tr>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.acqid" /></label></th>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.domainusage" /> </label></th>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.settlmntBin" /></label></th>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.pdttype" /></label></th>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.status" /></label></th>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.action" /></label></th>
																						</tr>
																					</thead>
																					<tbody id="acquirerBinsList">
																						<c:if
																							test="${not empty memberOnBoardingDTO.acqBinList}">
																							<c:forEach var="acqBin"
																								items="${memberOnBoardingDTO.acqBinList}"
																								varStatus="status">

																								<tr>
																									<td>${acqBin.acquirerId}</td>
																									<td>${acqBin.binDetailsCodeDescription.domainUsage}</td>
																									<td>${acqBin.acqSettlementBin}</td>
																									<td>${acqBin.binDetailsCodeDescription.productType}</td>
																									<td><c:choose>
																											<c:when test="${acqBin.status eq 'B'}">
																												<spring:message code="member.blckd" />
																											</c:when>
																											<c:otherwise>Active</c:otherwise>
																										</c:choose></td>
																									<td><a
																										href="javascript:editAcquirerBin('${acqBin.acquirerId}')"><span
																											class="glyphicon glyphicon-pencil my-tooltip"
																											title="EDIT"></span></a> <c:choose>
																											<c:when test="${(acqBin.status eq 'B')}">
																										 &nbsp; &nbsp;  		<a
																													href="javascript:unblockAcquirerBin('${acqBin.acquirerId}')"><span
																													class="glyphicon glyphicon-ok-circle my-tooltip"
																													title="UNBLOCK"></span></a>
																											</c:when>
																											<c:otherwise>
																											 &nbsp; &nbsp; 	<a
																													href="javascript:blockAcquirerBin('${acqBin.acquirerId}')"><span
																													class="glyphicon glyphicon-ban-circle my-tooltip"
																													title="BLOCK"></span></a>
																											</c:otherwise>
																										</c:choose>  &nbsp; &nbsp; <a
																										href="javascript:deleteAcquirerBin('${acqBin.acquirerId}')"><span
																											class="glyphicon glyphicon-trash my-tooltip"
																											title="DELETE"></span></a></td>
																								</tr>
																							</c:forEach>
																						</c:if>
																					</tbody>
																				</table>
																			</div>
																		</table>
																	</div>
																</div>
																<div class="row" id="issrow">
																	<div class="col-md-13">
																		<div class="card">
																			<div class="card-header">
																				<div class="card-title">
																					<spring:message code="member.issbindetails" />
																				</div>
																			</div>
																			<div class="card-body">
																				<div id="IssuerBinDetailsDiv">
																					<table id="dataTable1">
																					<caption style="display:none;">Member add</caption> 
																					<th scope="col"></th>
																						<tr id="newIssuerBin" hidden>
																							<div class="row" id="newIssuerBinData">
																								<div class="card-body" id="newIssuerBin">
																									<div class="row">
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.bintype" /><span class="red">*</span></label>
																												<form:select path="issBinType"
																													id="issBinType" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:option lable="Issuer Bin" value="I">
																														<spring:message code="member.issbin" />
																													</form:option>
																													<form:option lable="Token Bin" value="T">
																														<spring:message code="member.tokenbin" />
																													</form:option>
																												</form:select>
																												<div id="errissBinType">
																													<span for="issBinType" class="error"><form:errors
																															path="issBinType" /></span>
																												</div>
																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.bankgroup" /> <span
																													class="red">*</span> </label>
																												<form:select path="issBankGroup"
																													id="issBankGroup" class="form-control" 
																													placeholder="Pick a Bank Group..."
												name="issBankGroup" 
												onkeydown="validateFromCommonVal('issBankGroup',false,'SelectionBox',100,false);">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code" items="${bankGroupList}" />
																												</form:select>
																												<div id="errissBankGroup">
																													<span for="issBankGroup" class="error"><form:errors
																															path="issBankGroup" /></span>
																												</div>
																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareInput"><spring:message
																														code="member.binLength" /> <span class="red">*</span>
																												</label>
																												<form:select path="binLength"
																													id="binLength" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													 <form:options itemLabel="code"
																														itemValue="code"
																														items="${binTypeLength}" /> 
																												</form:select>

																												<div id="errbinLength">
																													<span for="binLength" class="error"><form:errors
																															path="binLength" /></span>
																												</div>
																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareInput"><spring:message
																														code="member.binno" /> <span class="red">*</span>
																												</label>
																												<form:input path="binNumber" id="binNumber"
																													name="binNumber" maxlength="9"
																													cssClass="form-control input-square" />

																												<div id="errbinNumber">
																													<span for="binNumber" class="error"><form:errors
																															path="binNumber" /></span>
																												</div>
																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareInput"><spring:message
																														code="member.lowbin" /></label>
																												<form:input path="lowBin" id="lowBin"
																													value="000000000" name="lowBin"
																													maxlength="9"
																													cssClass="form-control input-square"  readonly="true" />

																												<div id="errlowBin">
																													<span for="lowBin" class="error"><form:errors
																															path="lowBin" /></span>
																												</div>

																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareInput"><spring:message
																														code="member.highbin" /></label>
																												<form:input path="highBin" id="highBin"
																													value="999999999" name="highBin"
																													maxlength="9"
																													cssClass="form-control input-square" readonly="true" />

																												<div id="errhighBin">
																													<span for="highBin" class="error"><form:errors
																															path="highBin" /></span>
																												</div>
																											</div>
																										</div>
																										</div>
																										<div class="row">
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.activtndate" /> <span
																													class="red">*</span> </label> <input type="text"
																													path="issFrmDate"
																													data-date-format="dd-mm-yyyy"
																													id="issFrmDate" name="issFrmDate"
																													class="form-control input-square"
																													readonly="true" />

																												<div id="errissFrmDate">
																													<span for="issFrmDate" class="error"><form:errors
																															path="issFrmDate" /></span>
																												</div>
																											</div>
																										</div>
																									
																									
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareInput"><spring:message
																														code="member.deactdate" /> <span
																													class="red">*</span> </label> <input type="text"
																													path="issToDate"
																													data-date-format="dd-mm-yyyy"
																													id="issToDate" name="issToDate"
																													class="form-control input-square"
																													readonly="true" />

																												<div id="errissToDate">
																													<span for="issToDate" class="error"><form:errors
																															path="issToDate" /></span>
																												</div>
																											</div>
																										</div>

																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareInput"><spring:message
																														code="member.panlength" /> <span
																													class="red">*</span> </label>
																												<form:input path="panLength" id="panLength"
																													name="panLength" maxlength="2"
																													cssClass="form-control input-square" />

																												<div id="errpanLength">
																													<span for="panLength" class="error"><form:errors
																															path="panLength" /></span>
																												</div>

																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.bincardtype" /> <span
																													class="red">*</span> </label>
																												<form:select path="binCardType"
																													id="binCardType" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code"
																														items="${binCardTypeList}" />
																												</form:select>
																												<div id="errbinCardType">
																													<span for="binCardType" class="error"><form:errors
																															path="binCardType" /></span>
																												</div>
																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.binpdttype" /> <span
																													class="red">*</span> </label>
																												<form:select path="binProductType"
																													id="binProductType" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code"
																														items="${binProdTypeList}" />
																												</form:select>
																												<div id="errbinProductType">
																													<span for="binProductType" class="error"><form:errors
																															path="binProductType" /></span>
																												</div>
																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.bincardvarnt" /> <span
																													class="red">*</span> </label>
																												<form:select path="binCardVariant"
																													id="binCardVariant" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code"
																														items="${binCardVariantList}" />
																												</form:select>
																												<div id="errbinCardVariant">
																													<span for="binCardVariant" class="error"><form:errors
																															path="binCardVariant" /></span>
																												</div>
																											</div>
																										</div>
																										</div>
																										<div class="row">
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.bincardbrand" /> <span
																													class="red">*</span> </label>
																												<form:select path="binCardBrand"
																													id="binCardBrand" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code"
																											items="${binCardBrandList}" />
																												</form:select>
																												<div id="errbinCardBrand">
																													<span for="binCardBrand" class="error"><form:errors
																															path="binCardBrand" /></span>
																												</div>
																											</div>
																										</div>
																									
																									
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.domainusage" /> <span
																													class="red">*</span> </label>
																												<form:select path="issDomainUsage"
																													id="issDomainUsage" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code"
																														items="${domainUsageList}" />
																												</form:select>
																												<div id="errissDomainUsage">
																													<span for="issDomainUsage" class="error"><form:errors
																															path="issDomainUsage" /></span>
																												</div>
																											</div>
																										</div>

																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.msgtype" /> <span class="red">*</span>
																												</label>
																												<form:select path="messageType"
																													id="messageType" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code"
																														items="${messageTypeList}" />
																												</form:select>
																												<div id="errmessageType">
																													<span for="messageType" class="error"><form:errors
																															path="messageType" /></span>
																												</div>
																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.cardtech" /> <span
																													class="red">*</span> </label>
																												<form:select path="cardTechnology"
																													id="cardTechnology" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select">
																														</spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code" items="${cardTechList}" />
																												</form:select>
																												<div id="errcardTechnology">
																													<span for="cardTechnology" class="error"><form:errors
																															path="cardTechnology" /></span>
																												</div>
																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.authmech" /> <span
																													class="red">*</span> </label>
																												<form:select path="authMechanism"
																													id="authMechanism" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code" items="${authMechList}" />
																												</form:select>
																												<div id="errauthMechanism">
																													<span for="authMechanism" class="error"><form:errors
																															path="authMechanism" /></span>
																												</div>
																											</div>
																										</div>
																										<div class="col-md-2" id="issProductTypeDiv" >
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.pdttype" /> <span class="red">*</span>
																												</label>
																												<form:select path="issProductType"
																													id="issProductType" class="form-control">
																													<%-- <form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option> --%>
																													<form:options itemLabel="description"
																														itemValue="code"
																														items="${productTypeList}" />
																												</form:select>
																												<div id="errissProductType">
																													<span for="issProductType" class="error"><form:errors
																															path="issProductType" /></span>
																												</div>
																											</div>
																										</div>
																										</div>
																										<div class="row">
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.subscheme" /><span
																													class="red">*</span></label>
																												<form:select path="subScheme" id="subScheme"
																													class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code" items="${subSchemeList}" />
																												</form:select>
																												<div id="errsubScheme">
																													<span for="subScheme" class="error"><form:errors
																															path="subScheme" /></span>
																												</div>
																											</div>
																										</div>
																									
																									
																										<div class="col-md-2" id="cardSubVariantDiv">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.cardsubvar" /> <span
																													class="red">*</span> </label>
																												<form:select path="cardSubVariant"
																													id="cardSubVariant" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code"
																														items="${cardSubVariantList}" />
																												</form:select>
																												<div id="errcardSubVariant">
																													<span for="cardSubVariant" class="error"><form:errors
																															path="cardSubVariant" /></span>
																												</div>
																											</div>
																										</div>

																										<div class="col-md-2" id="programDetailsDiv">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.pgdetails" /> <span
																													class="red">*</span> </label>
																												<form:select path="programDetails"
																													id="programDetails" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code"
																														items="${programDetailsList}" />
																												</form:select>
																												<div id="errprogramDetails">
																													<span for="programDetails" class="error"><form:errors
																															path="programDetails" /></span>
																												</div>
																											</div>
																										</div>
																										<div class="col-md-2" id="formFactorDiv">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.formfactor" /> <span
																													class="red">*</span> </label>
																												<form:select path="formFactor"
																													id="formFactor" class="form-control">
																													<form:option value="0">
																														<spring:message code="msg.lbl.select"></spring:message>
																													</form:option>
																													<form:options itemLabel="description"
																														itemValue="code" items="${formFactorList}" />
																												</form:select>
																												<div id="errformFactor">
																													<span for="formFactor" class="error"><form:errors
																															path="formFactor" /></span>
																												</div>
																											</div>
																										</div>
																										<div id="settlementBinDiv">
																											<div class="col-md-2">
																												<div class="form-group">
																													<label for="squareSelect"><spring:message
																															code="member.settmntBIN" /> <span
																														class="red">*</span> </label>
																													<form:select path="issSettlementBin"
																														id="issSettlementBin" class="form-control">
																														<form:option value="0">
																															<spring:message code="msg.lbl.select"></spring:message>
																														</form:option>
																														<c:if
																															test="${not empty memberOnBoardingDTO.settlementBinList}">
																															<form:options
																																itemLabel="settlementBinNumber"
																																itemValue="settlementBinNumber"
																																items="${memberOnBoardingDTO.settlementBinList}" />
																														</c:if>
																													</form:select>

																													<div id="errissSettlementBin">
																														<span for="issSettlementBin" class="error"><form:errors
																																path="issSettlementBin" /></span>
																													</div>

																												</div>
																											</div>
																										</div>
																										<div class="col-md-2">
																											<div class="form-group">
																												<label for="squareSelect"><spring:message
																														code="member.isOfflAllowd" /> <span
																													class="red">*</span> </label>
																												<div>
																													<select name="issOfflineAllowed"
																														id="issOfflineAllowed">
																														<option value="N"><spring:message
																																code="member.no" /></option>
																														<option value="Y"><spring:message
																																code="member.yes" /></option>
																													</select>
																												</div>
																												<span id="errsissOfflineAllowed"
																													class="error"></span>
																											</div>
																										</div>
																										<c:if test="${InternationalParticipant eq 'Y'}">
																													<div class="col-md-2">
																										<div class="form-group">
																											<label for="markUp">Markup<span class="red">*</span>
																											</label>
																												<form:input path="markUp" id="markUp"
																		name="markUp"
																		     cssClass="form-control input-square" />
																											<div id="errmarkUp">
																												<span for="markUp" class="error"><form:errors
																														path="markUp" /></span>
																											</div>
																										</div>
																									</div>
																									
																										<div class="col-md-2">
																										<div class="form-group">
																											<label for="squareSelect">Network Lincense Id </label>
																												<form:input path="networkLicenseId" id="networkLicenseId"
																		name="networkLicenseId"
																		     cssClass="form-control input-square" />
																											<div id="errnetworkLicenseId">
																												<span for="networkLicenseId" class="error"><form:errors
																														path="networkLicenseId" /></span>
																											</div>
																										</div>
																									</div>
																									
																									
																										<div class="col-md-2">
																										<div class="form-group">
																											<label for="squareSelect">Network Issuer Id </label>
																												<form:input path="networkIssId" id="networkIssId"
																		name="networkIssId"
																		     cssClass="form-control input-square" />
																											<div id="errnetworkIssId">
																												<span for="networkIssId" class="error"><form:errors
																														path="networkIssId" /></span>
																											</div>
																										</div>
																									</div>
																									</c:if>
																									</div>
																								</div>
																								<div id="featureMultiples">
																									<div class="col-md-2">
																										<div class="form-group">
																											<label for="squareSelect"><spring:message
																													code="member.featurefee" /> 
																											</label>
																											<form:select path="feature"
																												id="featureMultiple"
																												name="featureMultiple[]" multiple="multiple"
																												Class="form-control">
																											</form:select>
																											<div id="errfeatureMultiple">
																												<span for="featureMultiple" class="error"><form:errors
																														path="featureMultiple" /></span>
																											</div>
																										</div>
																									</div>
																									
																								</div>
																								<div class="col-md-2">
																										<div class="form-group">
																											<label for="additionalParams"><spring:message
																													code="member.additionalParams" /> <span class="red">*</span>
																											</label>
																											<form:select path="additionalParams"
																												id="additionalParams"
																												name="additionalParams[]" multiple="multiple"
																												Class="form-control">
																											<c:forEach items="${additionalParamsList}"
																												var="item">
																												<form:option value="${item.code}"
																													label="${item.code}-${item.description}" />
																											</c:forEach>

																										</form:select>
																											<div id="erradditionalParams">
																												<span for="additionalParams" class="error"><form:errors
																														path="additionalParams" /></span>
																											</div>
																										</div>
																									</div>
																									<c:if test="${InternationalParticipant eq 'Y'}">
																									<div class="col-md-2">
																										<div class="form-group">
																											<label for="networkSelection">Network Selection<span class="red">*</span>
																											</label>
																											<form:select path="networkSelection"
																												id="networkSelection"
																												name="networkSelection[]" multiple="multiple"
																												Class="form-control">
																											<c:forEach items="${networkSelectionList}"
																												var="item">
																												<form:option value="${item.code}"
																													label="${item.description}" />
																											</c:forEach>

																										</form:select>
																											<div id="errnetworkSelection">
																												<span for="networkSelection" class="error"><form:errors
																														path="networkSelection" /></span>
																											</div>
																										</div>
																									</div>
																						
																									</c:if>
																									
																							</div>
																							</div>
																							<a href="#/" id="clearIssuerBin" value="Clear"
																								class="btn btn-success"><spring:message
																									code="member.clrBtn" /></a>
																							<a id="saveIssuerBin" class="btn btn-success"><spring:message
																									code="member.saveBtn" /></a>

																						</tr>
																						<tr>
																							<td><input type='hidden' id='oldBinNumber' /></td>
																						</tr>
																						<div class="panel-body">
																							<table class="table table-striped">
																							<caption style="display:none;">Member add</caption> 
																								<thead>
																									<tr>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.binno" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.bincardtype" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.binpdttype" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.bincardvarnt" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.bincardbrand" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.bintype" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.status" /></label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.action" /></label></th>

																									</tr>
																								</thead>
																								<tbody id="issuerBinsList">
																									<c:if
																										test="${not empty memberOnBoardingDTO.issBinList}">
																										<c:forEach var="issBin"
																											items="${memberOnBoardingDTO.issBinList}"
																											varStatus="status">
																											<tr>
																												<td>${issBin.binNumber}</td>
																												<td>${issBin.binDetailsCodeDescription.binCardType}</td>
																												<td>${issBin.binDetailsCodeDescription.binProductType}</td>
																												<td>${issBin.binDetailsCodeDescription.binCardVariant}</td>
																												<td>${issBin.binDetailsCodeDescription.binCardBrand}</td>
																												<td>${issBin.binDetailsCodeDescription.issBinType}</td>
																												<td><c:choose>
																														<c:when
																															test="${issBin.status eq 'B'}">
																															<spring:message code="member.blckd" />
																														</c:when>
																														<c:otherwise>Active</c:otherwise>
																													</c:choose></td>

																												<td><a
																													href="javascript:editIssuerBin('${issBin.binNumber}','${issBin.domesticFlag}')"><span
																														class="glyphicon glyphicon-pencil my-tooltip"
																														title="EDIT"></span></a> <c:choose>
																														<c:when test="${(issBin.status eq 'B')}">
																															 &nbsp; &nbsp; <a
																																href="javascript:unblockIssuerBin('${issBin.binNumber}')"><span
																																class="glyphicon glyphicon-ok-circle my-tooltip"
																																title="UNBLOCK"></span></a>
																														</c:when>
																														<c:otherwise>
																															 &nbsp; &nbsp; <a
																																href="javascript:blockIssuerBin('${issBin.binNumber}')"><span
																																class="glyphicon glyphicon-ban-circle my-tooltip"
																																title="BLOCK"></span></a>
																														</c:otherwise>
																													</c:choose>  &nbsp; &nbsp; <a
																													href="javascript:deleteIssuerBin('${issBin.binNumber}')"><span
																														class="glyphicon glyphicon-trash my-tooltip"
																														title="DELETE"></span></a></td>
																											</tr>
																										</c:forEach>
																									</c:if>
																								</tbody>
																							</table>
																						</div>
																					</table>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="panel panel-default">
											<div class="panel-heading">
												<h4 class="panel-title">
													<a data-toggle="collapse" data-parent="#accordion"
														href="#collapseFive" id="collapseFiveLink"><spring:message
															code="member.docupld&downld" /> <span
														class="glyphicon glyphicon-plus areaShowHde "></span> </a>
												</h4>
											</div>
											<div id="collapseFive"
												class="panel-collapse collapse panelHideShow">
												<div class="panel-body">
													<div class="">
														<div class="row">
															<div class="col-md-12">
																<div class="card">
																	<div class="card-body">
																		<div class="row">
																			<div class="col-md-6">
																				<div class="form-group">

																					<c:if test="${empty pending && reqType ne 'V'}">
																						<input name="file" id="file" type="file"
																							multiple="multiple"
																							class="form-control input-square"
																							onchange="fileUpload();" />
																						<label id="title"><span class="red">*</span><spring:message
																								code="member.extnsn" /></label>
																					</c:if>
																					<div id="errfileupload">
																		<span for="file" class="error"></span>
																	</div>
																					<table class="table table-striped">
																					<caption style="display:none;">Member add</caption> 
																						<thead>
																							<tr>
																								<th scope="col"><input type="checkbox"
																									id="checkAllDocument" /></th>
																								<th scope="col"><label for="squareInput"><strong><spring:message
																											code="member.filename" /></b></label></th>
																								<th scope="col"><strong><label for="squareInput"><strong><spring:message
																											code="member.status" /></strong></label></th>
																								<th scope="col"><strong><label for="squareInput"><strong><spring:message
																											code="member.action" /></strong></label></th>

																							</tr>


																						</thead>
																						<tbody id="documentList">
																						
																							<c:if
																								test="${not empty memberOnBoardingDTO.documents}">
																								<c:forEach var="document"
																									items="${memberOnBoardingDTO.documents}"
																									varStatus="status">
																									<tr>
																										<td><input type="checkbox"
																											id="${document.documentName}"
																											name="selectMemberDocument"
																											onclick="toggleCheckAllDocuments()" /></td>
																										<td>${document.documentName}</td>
																										<td>Uploaded</td>
																										<td><a
																											href="javascript:deleteFile('${document.documentName}')"><span
																												class="glyphicon glyphicon-trash my-tooltip"
																												title="DELETE"></span></a>  &nbsp; &nbsp;  <a
																											href="javascript:downloadMemberDocument('${document.documentName}')"><span
																												class="glyphicon glyphicon-download my-tooltip"
																												title="DOWNLOAD"></span></a></td>
																									</tr>
																								</c:forEach>
																							</c:if>
																						 <c:if
																								test="${empty memberOnBoardingDTO.documents}">
																								<tr>
																										<td colspan="3" style="text-align:center">
																										  <label><spring:message
																											code="member.memberna"/></label>
																										</td>
																										</tr>
																								</c:if>
																						</tbody>
																					</table>
																					<div class="card-action">
																					<c:if
																						test="${not empty memberOnBoardingDTO.documents}">
																							<button type="button" class="btn btn-primary"
																								onclick="downloadMemberDocumentBatch()"
																								id="downloadFiles">
																								<spring:message code="st.lbl.dwnFiles" />
																							</button>  &nbsp; &nbsp; 
																					</c:if>
																						<%-- <button type="button" class="btn btn-primary"
																							onclick="deleteMemberDocumentBatch()"
																							id="deleteAllFiles">
																							<spring:message code="st.lbl.deleteAllFiles" />
																						</button>  &nbsp; &nbsp; --%> 
																						
																		 <button type="button" class="btn btn-primary"
																							onclick="deleteFiles()"
																							id="deleteAllFiles">
																							<spring:message code="st.lbl.deleteAllFiles" />
																						</button>  &nbsp; &nbsp; 
																						
																					</div>
																					<div></div>

																				</div>
																			</div>
																		</div>
																	</div>
																</div>
															</div>

														</div>
													</div>
												</div>
											</div>
										</div>


										
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="text-center">
					<div class="card" style="padding: 15px;">
						<div class="">
							<c:if
								test="${reqType eq 'ADD' or recStatus eq 'I' or recStatus eq 'R'or recStatus eq 'A'}">


									<input type="button" class="btn btn-success"  id="saveMemberData" name="save"
										value="<spring:message code="member.saveBtn" />" />										
							</c:if>
									<input type="button" class="btn btn-success"  id="submitMemberData" name="submit"
										value="<spring:message code="member.submitBtn" />" />										

							<c:if test="${recStatus eq 'R'}">
								<a href="#" id="discardData" 
									class="btn btn-success button"><spring:message
										code="member.discardBtn" /></a>
							</c:if>
							<c:if test="${recStatus eq 'A' or reqType eq 'ADD'}">
								<a href="#" onclick="userAction('N','/showFinalMembers');"
									class="btn btn-danger"><spring:message
										code="member.backBtn" /></a>
							</c:if>
							<c:if test="${recStatus eq 'I'}">
								<a href="#" onclick="userAction('N','/showSavedMembers');"
									class="btn btn-danger"><spring:message
										code="member.backBtn" /></a>
							</c:if>
							<c:if test="${recStatus eq 'P' or recStatus eq 'R'}">
								<a href="#" onclick="userAction('N','/pendingMembersFrAppr');"
									class="btn btn-danger"><spring:message
										code="member.backBtn" /></a>
							</c:if>
						</div>
					</div>
				</div>
			</form:form>
		</div>
	</div>
</div>

</html>