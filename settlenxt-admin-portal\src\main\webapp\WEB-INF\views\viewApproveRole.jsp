<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/viewApproveRole.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">
		<span data-i18n="Data"><spring:message
				code="am.lbl.appRejAction" /></span>
	</div>
	<div class="alert alert-danger remarkMust" role="alert">
		<span data-i18n="Data"><spring:message code="sm.lbl.remarkMust" /></span>
	</div>
	<c:url value="approveRoleStatus" var="approveRoleStatus" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveRole" modelAttribute="roleInfoDto"
		action="${approveRoleStatus}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="am.lbl.pendingRole" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="roleId" value="${roleInfoDto.roleId}" />

						<input type="hidden" id="crtuser"
							value="${roleInfoDto.lastUpdatedBy}" />

						<table class="table table-striped infobold"
							style="font-size: 12px">
							<caption style="display:none;">ROLE</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message
														code="sm.lbl.requestInformation" /></span></strong>
										</div></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>
								<tr>

									<td><label><span data-i18n="Data"><spring:message
													code="am.lbl.reqType" /></label></td>
									<td>${roleInfoDto.lastOperation}</td>
									<td><label><spring:message
													code="sm.lbl.requestDate" /></label></td>
																								<c:choose>
    <c:when test="${not empty roleInfoDto.lastUpdatedOn}">
    
    <td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
																		value="${roleInfoDto.lastUpdatedOn}" /></td>
    
    
										
        
												
    
               
    </c:when>
    <c:otherwise>
    
    
    
    
    <td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
																		value="${roleInfoDto.createdOn}" /></td>
    
	
    
   
    </c:otherwise>
</c:choose> 

													
									
									<td><label><spring:message
													code="sm.lbl.requestStatus" /></label></td>
									<c:if test="${roleInfoDto.requestState=='A' }">
											<td>Approved</td>
										</c:if>
									<c:if test="${roleInfoDto.requestState=='P' }">
											<td>Pending</td></c:if>
									<c:if test="${roleInfoDto.requestState=='R' }">
											<td>Rejected</td></c:if>
									<c:if test="${roleInfoDto.requestState=='D' }">
										<td>Discarded</td></c:if>
									<td></td>
									<td></td>
									<td></td>
									<td></td>

								</tr>
								<tr>
									<td><label><spring:message
													code="sm.lbl.requestBy" /></label></td>
													<c:choose>
    <c:when test="${not empty roleInfoDto.lastUpdatedBy}">
    <td>${roleInfoDto.lastUpdatedBy}</td>
    
               
    </c:when>
    <c:otherwise>
   <td>${roleInfoDto.createdBy}</td>
    
   
    </c:otherwise>
</c:choose> 
									

									<td><label><spring:message
													code="am.lbl.approverComments" />
													</label></td>
									<td colspan=2>${roleInfoDto.checkerComments}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>

								<tr>
															
									<td colspan="6">
                                        <div class="panel-heading-red clearfix">
                                            <strong><span class="glyphicon glyphicon-user"></span> <span
                                                data-i18n="Data"><spring:message
														code="AM_MSG_roleInformation" /></span></strong>
                                        </div>
                                    </td>

									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>


								<tr>
									<td><label><spring:message
													code="am.lbl.roleId" /></label></td>
									<td>${roleInfoDto.roleId }</td>
									<td><label><spring:message
													code="am.lbl.roleName" /></label></td>
									<td>${roleInfoDto.roleName }</td>
									<td><label><spring:message
													code="am.lbl.roleDescription" /></label></td>
									<td>${roleInfoDto.roleDesc }</td>
									<td><label><strong> <span
													data-i18n="Data"><spring:message
															code="am.lbl.roleType" /></span></strong><span style="color: red"></span></label></td>
															<td>${roleInfoDto.makChkFlag}</td>	
									<td><label><spring:message
															code="am.lbl.userType" /></label></td>
										<c:choose>
												<c:when test="${roleInfoDto.roleType eq '2' }">
													<td style="color: red"><spring:message
															code="am.lbl.ntwAdmin" /></td>
												</c:when>
												<c:when test="${roleInfoDto.roleType eq '3' }">
													<td style="color: red"><spring:message
															code="am.lbl.bankAdmin" /></td>
												</c:when>
												<c:when test="${roleInfoDto.roleType eq '4' }">
													<td style="color: red"><spring:message
															code="am.lbl.bankUsers" /></td>
												</c:when>
											</c:choose>
									
									<td></td>
								</tr>

								<c:if test="${fn:length(functionalityList)>0}">

									<tr>
										<td colspan="6"><div class="panel-heading-red  clearfix">
												<strong><spring:message
															code="am.lbl.funcInfo" /></strong>
											</div></td>
										<td></td>
										<td></td>
										<td></td>
										<td></td>
									</tr>

									<tr>
										<td colspan="6">
											<table id="tabnew" class="table table-striped table-bordered"
												style="width:100%"  >
												<caption style="display:none;">ROLE</caption>
												<thead>
													<tr>
														<th scope="col"><span data-i18n="Data"><spring:message
																	code="am.lbl.funcname" /></span></th>
														<th scope="col"><span data-i18n="Data"><spring:message
																	code="am.lbl.funcDesc" /></span></th>

													</tr>
												</thead>
												<tbody>
													<c:if test="${fn:length(functionalityList)>0}">
														<c:forEach var="func" items="${functionalityList}">
															<tr>
																<c:if test="${ func.status ne 'E'}">
																	<td style="color: green">${func.funcName}</td>
																	<td style="color: green">${func.funcDesc}</td>
																</c:if>
																<c:if test="${func.status eq 'E' }">
																	<td>${func.funcName}</td>
																	<td>${func.funcDesc}</td>
																</c:if>

															</tr>
														</c:forEach>
													</c:if>
												</tbody>
											</table>
										</td>
									</tr>


								</c:if>
								<sec:authorize access="hasAuthority('Approve Role')">
									<c:if test="${roleInfoDto.requestState eq 'P'}">
										<tr>
											<td colspan="6"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"><spring:message
																code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
										</tr>

										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="AM.lbl.reject" /></option>
											</select></td>
											<td>
												<div style="text-align: center">
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											<!-- //Added by deepak on 31-03-2016 -->
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										</tr>
									</c:if>
								</sec:authorize>

							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align: center">
									<sec:authorize access="hasAuthority('Approve Role')">
										<c:if test="${roleInfoDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success button"
												id="approveRole" value="Submit"
												onclick="postAction('/approveRoleStatus');" />
										</c:if>
									</sec:authorize>

									<button type="button" class="btn btn-danger"
										onclick="backAction('${roleInfoDto.roleType}','/rolePendingForApproval');">Back</button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

