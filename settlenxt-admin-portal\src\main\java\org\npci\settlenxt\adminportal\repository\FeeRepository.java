package org.npci.settlenxt.adminportal.repository;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.adminportal.dto.FeeDTO;
import org.npci.settlenxt.adminportal.dto.FeeMajorMinorPriorityDTO;
import org.npci.settlenxt.portal.common.dto.BaseFeeDTO;
import org.npci.settlenxt.portal.common.dto.BaseFeeMajorMinorPriorityDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.FeeRateConfigDTO;
import org.npci.settlenxt.portal.common.repository.BaseFeeRepository;

@Mapper
public interface FeeRepository extends BaseFeeRepository {

	 List<FeeDTO> getApprovedFeeRateList(@Param("type") String type, @Param("requestState") String requestState);

	 List<FeeDTO> getPendingForApprovalFeeRateList(@Param("type") String type,
			@Param("requestState") List<String> requestState);

	 FeeDTO getFeesDetails(@Param("feeId") int feeId, @Param("currencyCode") String currencyCode,
			@Param("feeType") String feeType);

	 FeeDTO getFeesDetailsMain(@Param("feeId") int feeId, @Param("currencyCode") String currencyCode,
			@Param("feeType") String feeType);

	 FeeDTO getFeeRateStgInfoByFeeId(@Param("feeId") int feeId, @Param("currencyCode") String currencyCode,
			@Param("feeType") String feeType);

	 void saveFee(FeeDTO feeRateDto);

	 void updateFee(FeeDTO feeRateDto);

	 void updateFeeStgState(@Param("lastUpdatedBy") String lastUpdatedBy,
			@Param("lastUpdatedOn") LocalDateTime lastUpdatedOn, @Param("requestState") String requestState,
			@Param("feeId") int feeId, @Param("remarks") String remarks,
			@Param("checkerComments") String checkerComments, @Param("lastOperation") String lastOperation);

	 FeeDTO getFeeRateInfoByFeeId(@Param("feeId") int feeId);

	 FeeDTO getFeeRate(@Param("type") String type, @Param("feeId") int feeId);

	 FeeDTO getFeeRateMain(@Param("feeId") int feeId);

	 int updateFeeRate(FeeDTO feeRateDto);

	 int deleteDiscardedEntry(@Param("feeId") int feeId);

	 List<FeeDTO> getFeeCodeList(@Param("type") String type);

	 List<FeeDTO> getGstCodeList(@Param("sysType") String sysType);

	 List<FeeDTO> getTxnCurrency(@Param("type") String type);

	 List<FeeDTO> getPartyTypeList(@Param("type") String type);

	 long addfee(FeeDTO feeRateDto);

	 int fetchIdFromFeeRateSequence();

	 int checkDistinctFeecodeStg(FeeDTO feeRateDto);

	 int checkDistinctFeecode(FeeDTO feeRateDto);

	 FeeDTO getEditFeeRate(@Param("feeId") int feeId);

	 int updateEditFeeRate(FeeDTO feeRateDto);

	 List<FeeDTO> getMinorList(@Param("requestState") String requestState,
			@Param("significance") String significance);

	 List<FeeDTO> getMajorList(@Param("requestState") List<String> requestState);

	 List<FeeDTO> getPendingMajorList(@Param("requestState") List<String> requestState);

	 List<FeeDTO> getPendingMinorList(@Param("requestState") List<String> requestState,
			@Param("significance") String significance);

	 List<FeeRateConfigDTO> getEditFeeMinor(FeeDTO feeDto);

	 void updateEditFeeMajorStg(FeeDTO feeDto);

	 int fetchIdFromFeeMasterSequence();

	 long addFeeMajorStg(FeeDTO feeDto);

	 long addFeeConfigStg(FeeRateConfigDTO feeDto);

	 long addFeeMasterStg(FeeDTO feeDto);

	 List<FeeMajorMinorPriorityDTO> getFeeIdList(@Param("feeType") String feeType);

	 FeeMajorMinorPriorityDTO getEditFeeConfig(@Param("majorId") int majorId, @Param("priority") int priority);

	 int checkDistinctFeeMajorMinorStg(FeeDTO feeDto);

	 int checkDistinctFeeMajorMinor(FeeDTO feeDto);

	 List<FeeMajorMinorPriorityDTO> getSavedMinorList(@Param("requestState") String requestState);

	 FeeDTO getFeeMajorStgInfoByMajorId(@Param("majorId") String majorId);

	 void updateFeeMasterStgMajor(@Param("lastUpdatedBy") String lastUpdatedBy,
			@Param("lastUpdatedOn") LocalDateTime lastUpdatedOn, @Param("requestState") String requestState,
			@Param("majorId") String majorId, @Param("remarks") String remarks,
			@Param("checkerComments") String checkerComments);

	 void updateFeeMasterStgMinor(@Param("lastUpdatedBy") String lastUpdatedBy,
			@Param("lastUpdatedOn") LocalDateTime lastUpdatedOn, @Param("requestState") String requestState,
			@Param("majorId") String majorId, @Param("remarks") String remarks,
			@Param("checkerComments") String checkerComments);

	 FeeDTO getFeeMajorInfoByMajorId(@Param("feeMajorId") String feeMajorId,
			@Param("significance") String significance);

	 void saveFeeMajor(FeeDTO feeRateDto);

	 void updateFeeMajor(FeeDTO feeRateDto);

	 void saveFeeConfig(FeeRateConfigDTO feeRateDto);

	 void saveFeeConfigMinor(FeeRateConfigDTO feeRateDto);

	 List<FeeRateConfigDTO> getFeeMinorStgInfoByminorId(@Param("minorId") String minorId,
			@Param("significance") String significance, @Param("lastOperation") String lastOperation);

	 List<FeeRateConfigDTO> getFeeMinorInfoByMinorId(@Param("feeMinorId") String feeMinorId);

	 List<FeeMajorMinorPriorityDTO> getMinorPriorityStgInfo(@Param("feeMajorId") String feeMajorId);

	 void saveMinorPriority(FeeMajorMinorPriorityDTO feeRateDto);

	 int updateFeeMinorPriorityStg(FeeMajorMinorPriorityDTO feeConfigDto);

	 int updateFeeMasterStg(FeeDTO feeMasterStgDto);

	 int deleteFeeMinorPriority(@Param("majorId") String majorId);

	 void deleteDiscardedMinorEntry(@Param("minorId") String minorId);

	 void updateFeeConfigStg(FeeDTO feeRateDtoMain);

	 int deleteDiscardedEntryMajor(@Param("feeMajorId") String feeMajorId);

	 int deleteDiscardedFeeConfigEntry(@Param("feeMajorId") String feeMajorId);

	 int deleteDiscardedMasterEntry(@Param("feeMajorId") String feeMajorId);

	 FeeMajorMinorPriorityDTO getRequestIdInFeeMasterStg(@Param("feeMajorId") String feeMajorId,
			@Param("requestState") String requestState, @Param("lastOperation") String lastOperation);

	 void deleteFeeMinorPriorityStg(@Param("feeMajorId") String feeMajorId);

	 int checkFeeMinorAvilable(@Param("feeConfigId") String feeConfigId, @Param("status") String status,
			@Param("significance") String significance);

	 void deleteMinorEntry(@Param("minorId") String minorId);

	 List<FeeRateConfigDTO> getFeeMajorConfigStgInfoByMajorId(@Param("majorId") String majorId,
			@Param("significance") String significance);

	 List<FeeRateConfigDTO> getFeeMajorConfigStgInfoByMinorId(@Param("majorId") String majorId,
			@Param("significance") String significance);

	 int checkFeeMajorAvilable(@Param("feeConfigId") String feeConfigId, @Param("status") String status);

	 long addFeeConfig(BaseFeeMajorMinorPriorityDTO feeMajorMinor);

	 List<FeeDTO> getMinorInfo(@Param("significance") String significance,
			@Param("requestState") String requestState, @Param("minorIdsArr") List<String> minorIdsArr);

	 List<FeeMajorMinorPriorityDTO> getSavedMinorListByMajorId(@Param("requestState") String requestState,
			@Param("feeMajorId") String feeMajorId);

	List<CodeValueDTO> getUnmappedFieldNameData(@Param("type") String type, @Param("feeConfigId") String feeConfigId);

	 void updateFeeMasterStgStateMajor(@Param("lastUpdatedBy") String lastUpdatedBy,
			@Param("lastUpdatedOn") LocalDateTime lastUpdatedOn, @Param("requestState") String requestState,
			@Param("majorId") String majorId, @Param("lastOperation") String lastOperation,
			@Param("oldFeeMajorId") String oldFeeMajorId);

	 void updateFeeMasterStgStateMajor2(@Param("lastUpdatedBy") String lastUpdatedBy,
			@Param("lastUpdatedOn") LocalDateTime lastUpdatedOn, @Param("requestState") String requestState,
			@Param("majorId") String majorId, @Param("lastOperation") String lastOperation);

	 void updateFeeMasterStgStateMinor(@Param("lastUpdatedBy") String lastUpdatedBy,
			@Param("lastUpdatedOn") LocalDateTime lastUpdatedOn, @Param("requestState") String requestState,
			@Param("majorId") String majorId, @Param("lastOperation") String lastOperation,
			@Param("oldFeeMinorId") String oldFeeMinorId);

	 int deleteDiscardedFeeMinorPriorityStg(@Param("feeMajorId") String feeMajorId);

	 List<FeeMajorMinorPriorityDTO> getFeeMinorPriorityStg(@Param("feeMajorId") String feeMajorId);

	 FeeDTO getFeeMasterStg(@Param("feeMajorId") String feeMajorId);

	 List<FeeMajorMinorPriorityDTO> getFeeMinorPriorityMain(@Param("feeMajorId") String feeMajorId);

	 FeeDTO getEditFeeMajor(BaseFeeDTO feeDto);

	 void removeMajorMinorMapping(FeeMajorMinorPriorityDTO feeDto);

	 List<FeeMajorMinorPriorityDTO> getMinorIdList(@Param("feeMajorId") String feeMajorId);

	 int checkDuplicatePriority(@Param("priority") int priority, @Param("majorId") String majorId,
			@Param("cardType") String cardType);

	 int checkDuplicatePriorityStg(@Param("priority") int priority,
			@Param("requestState") List<String> requestState, @Param("majorId") String majorId,
			@Param("cardType") String cardType);

	 List<String> getFeeMajorId(@Param("priority") int priority, @Param("requestState") List<String> requestState,
			@Param("majorId") String majorId);

	 void updatePriorityForMajorIdMain(@Param("feeMajorId") String feeMajorId, @Param("priority") int priority);

	 void updatePriorityForMajorIdStg(@Param("feeMajorId") String feeMajorId, @Param("priority") int priority);

	 long updateFeeConfig(BaseFeeMajorMinorPriorityDTO feeMajorMinor);

	 List<String> getFeeMajorMappedMinorIDS(@Param("feeMajorId") String feeMajorId);

	 String fetchFeeMajorJson(@Param("majorId") String majorId, @Param("status") String status);

	 int checkDuplicateMajorID(FeeDTO feeDto);

	 List<FeeRateConfigDTO> getFeeMinorByminorIdForView(@Param("minorId") String minorId,
			@Param("significance") String significance, @Param("lastOperation") String lastOperation);

	 FeeDTO getFeeMajorByMajorIdForView(@Param("majorId") String majorId);

	 List<FeeDTO> getFeeRateStgInfoList(@Param("feeIdList") List<Integer> feeIdList,
			@Param("currencyCode") String currencyCode, @Param("feeType") String feeType);

	 List<FeeDTO> getFeeMajorStgInfoByMajorIdList(@Param("list") List<String> list);

	 List<FeeRateConfigDTO> getFeeMinorStgInfoByminorIdList(@Param("list") List<String> list,
			@Param("significance") String significance, @Param("lastOperation") String lastOperation);

	 FeeDTO getFeeMajorStgByMajorIdForView(@Param("majorId") String majorId);

	 List<FeeMajorMinorPriorityDTO> getFeeDescriptionForMinor();

	 int checkFeeRateAvilable(@Param("feeId") int feeId);

	 List<CodeValueDTO> getDateActionList();

}
