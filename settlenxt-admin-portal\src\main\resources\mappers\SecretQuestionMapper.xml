<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.SecretQuestionRepository">


<select id="getSecretQuestion" resultType="SecretQuestionDTO">
SELECT SECRET_ID as secretId, DESCRIPTION as description FROM SECRET_QUESTION WHERE SECRET_ID= #{secretId}
</select>

<select id="getSecretQuestionList" resultType="SecretQuestionDTO">
SELECT   DISTINCT SECRET_ID as secretId ,DESCRIPTION as description FROM SECRET_QUESTION
</select>

</mapper>	
