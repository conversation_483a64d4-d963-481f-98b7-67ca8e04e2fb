<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<html  lang="EN" xmlns="http://www.w3.org/1999/xhtml">
<head>
<title><spring:message code="am.lbl.title" /></title>
<meta http-equiv="Cache-control" content="no-cache" />
<meta http-equiv="Cache-control" content="no-store" />
<meta http-equiv="Expires" content="0" />
<meta http-equiv="pragma" content="no-cache" />


<script src="./static/js/validation/viewEscalationMatrix.js"
	type="text/javascript"></script>
<script type="text/javascript" src="js/custom_js/jquery-ui.js"></script>
<link href="css/jquery-ui.css" rel="stylesheet" type="text/css" />
<style>

table,th,td {
	border: 1px solid #c2d1e8;
	border-collapse: collapse;
	padding: 10px;
}

table {
	margin: 1%;
}

th {
	width: 10%;
	text-align: center;
	background-color: #ecf4ff;
}

.width {
	width: 1%;
}

</style>

</head>
<body onload="noBack();">

	<div class="container-fluid height-min">

		<div class="row">
			<div class="alert alert-danger jqueryError" role="alert"
				style="display: none" id="jqueryError"></div>
			<c:if test="${not empty successStatus}">
				<div class="alert alert-success" role="alert">${successStatus}</div>
			</c:if>
			<c:if test="${not empty errorStatus}">
				<div class="alert alert-danger" role="alert">${errorStatus}</div>
			</c:if>
		</div>

		<div class="panel panel-default no_margin">
			<div class="panel-heading clearfix">
				<strong><span class="glyphicon glyphicon-th"></span> <span
					data-i18n="Data">Escalation Matrix</span></strong>
				<strong>
					<span class="float-right glyphicon glyphicon-print fa-lg" title="Print"></span>
				</strong>
			</div>

			<div class="panel-body" id="printDiv" >
				<form:form onsubmit="removeSpace(this); encodeForm(this);"
					method="POST" id="viewEscalation" modelAttribute="baseEscalationDTO"
					enctype="multipart/form-data" action="viewEscalation" autocomplete="off">
					<br />
					
					<c:if test="${not empty memberList}">
						<div class="row">
							<div class="col-md-3">
								<label>Select Bank</label>
								<form:select path="memberId" id="memberId" name="member"
									class="form-control">
									<form:option value="0">
										Select
									</form:option>
									<form:option value="999">
										NPCI
									</form:option>
									<form:options itemLabel="label" itemValue="value"
										items="${memberList}" />
								</form:select>
								<div id="errMember" class="error fail" style="color: #a94442"></div>
							</div>
						</div>
					</c:if>
					
					<div class="bs-example">
						<c:forEach var="department" items="${departmentList}">
						<div class="table-responsive">
							<table>
							<caption style="display:none;">Dept</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
								<tr>
									<th scope="col">Department</th>
									<th scope="col" class="width">Levels</th>
									<th scope="col">Name</th>
									<th scope="col">Designation</th>
									<th scope="col">Email</th>
									<th scope="col">Landline Number</th>
									<th scope="col">Mobile </th>
									<th scope="col">Legal Address </th>
									<th scope="col">State </th>
									<th scope="col">Zip Code </th>
								</tr>
							
								<tr>
									<td rowspan="6" style="text-align: center;">
										<label>${department.departmentName}</label>
									</td>
									
										<c:forEach var="user" items="${escalationList}">
										
											<c:if test="${user.departmentId eq department.departmentId}">
											
												<form:hidden path="escLevel" value="${user.escLevel}" />
												<form:hidden path="departmentId" value="${department.departmentId}" />
												<form:hidden path="userId" value="${user.userId}" />
												<tr>
												    
													<td>${user.escLevel}</td>
													<td>${user.name}</td>
													<td>${user.designation}</td>
													<td>${user.email}</td>
													<td>${user.landline}</td>
													<td>${user.mobile}</td>
													<td>${user.address}</td>
													<td>${user.stateName}</td>
													<td>${user.pinCode}</td>
												</tr>
												
											</c:if>

										</c:forEach>
								</tr>
							</table>
							</div>
						</c:forEach>
					</div>
				</form:form>
				</div>
			</div>
		</div>
	<%-- <%@include file="footer.jsp"%> --%>

</body>
</html>