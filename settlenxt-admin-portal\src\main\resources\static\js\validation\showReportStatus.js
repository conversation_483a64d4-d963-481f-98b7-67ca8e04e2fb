$(document).ready(function() {
		
		 $('#select-participantId').selectize({
	          sortField: 'text'
	      });

			 $('#productCode').selectize({
	          sortField: 'text'
	      });
 		$('#reportType').selectize({
	          sortField: 'text'
	      });

 		$('#status').selectize({
	          sortField: 'text'
	      });
		 $('#cycleNumber').selectize({
	          sortField: 'text'
	      });


if($('#flag').val()=="YES"){

getDataforcycle();

}



 $('#cycleDate').on('keyup keypress blur change',function () {
     	validateFromCommonVal('cycleDate', true, "DateFormat", 11 , false);
   });
            $("#cycleDate").datepicker({
				 changeMonth: true,
        		dateFormat: 'dd-mm-yy',
            });



		var $select1 = $('#productCode').selectize();
  var selectize = $select1[0].selectize;

                // attach blur event
                selectize.on('blur', function onBlur() {
                    // get the value from selectize instance.
				validateFromCommonVal('productCode', true, "SelectionBox", 11 , false);
					
                });


					selectize.on('change', function onChange() {
				validateFromCommonVal('productCode', true, "SelectionBox", 11 , false);
					getDataforcycle();
                });
                
                
                

var $select2 = $('#cycleNumber').selectize();
  var selectize1 = $select2[0].selectize;

                // attach blur event
                selectize1.on('blur', function onBlur() {
                    // get the value from selectize instance.
				validateFromCommonVal('cycleNumber', true, "SelectionBox", 11 , false);
					
                });


					selectize1.on('change', function onChange() {
                    // get the value from selectize instance.
				validateFromCommonVal('cycleNumber', true, "SelectionBox", 11 , false);
					
                });




  $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    searchBoxFunc(colIdx, cell, title, api);
                });
            $('#tabnew_filter').hide();
           
        },
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Report Status',
                header: 'false',
                title: null,
                sheetName: 'Report Status',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'Report Status' ,
				header:'false', 
				title: null,
				sheetName:'Report Status',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });

		
		$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	
     $("#excelExport").on("click", function () {
    	        $(".buttons-excel").trigger("click");
    	    });
    	    
    	     $("#csvExport").on("click", function () {
    	        $(".buttons-csv").trigger("click");
    	    });
    	 
    	     $("#clearFilters").on("click", function () {
    	       $(".search-box").each(function() {
    				   $(this).val("");
    				     $(this).trigger("change");
    				});
    	    });
		
		
		
});




function searchBoxFunc(colIdx, cell, title, api) {
    if (colIdx < actionColumnIndex) {
        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function(_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
}

function viewData()
{
	$('#afterSave').show();

	var isValid = true;

	if(!validateFromCommonVal('select-participantId',false,'SelectionBox',100,false)){		
		isValid = false;
		}
		
	if(!validateFromCommonVal('productCode',true,'SelectionBox',100,false)){		
		isValid = false;
		}
		
	if(!validateFromCommonVal('reportType',false,'SelectionBox',100,false)){		
		isValid = false;
		}
		
		if(!validateFromCommonVal('cycleDate',true,'DateFormat',100,false)){		
		isValid = false;
		}
		
							
		if(!validateFromCommonVal('cycleNumber',true,'SelectionBox',100,false)){		
		isValid = false;
		}
		
		if(!validateFromCommonVal('status',false,'SelectionBox',100,false)){		
		isValid = false;
		}
		
		
		if(isValid){
			
			
			
			var participantId = $("#select-participantId").val();
	var productCode = $("#productCode").val();
	var reportType= $("#reportType").val();
	var status = $("#status").val();
	var cycleNumber = $("#cycleNumber").val();
	var cycleDate = $("#cycleDate").val();	
	
	
	let url = "/reportStatusFetchData";
	
		var data = "participantId," + participantId
		 + ",productCode," + productCode  + ",reportType," + reportType + ",status," + status + ",cycleNumber," + cycleNumber + ",cycleDate," + cycleDate;
		

		postData(url, data);
	
		}
		
	
	
	
}

function resetAction() {

	
	 var $select = $('#select-participantId').selectize();
	 var control = $select[0].selectize;
	 control.clear();
	  $('#select-participantId').val("");


 var $select1 = $('#productCode').selectize();
	 var control1 = $select1[0].selectize;
	 control1.clear();
$('#productCode').val("");

 var $select2 = $('#reportType').selectize();
	 var control2 = $select2[0].selectize;
	 control2.clear();
$('#reportType').val("");


 var $select3 = $('#status').selectize();
	 var control3 = $select3[0].selectize;
	 control3.clear();
$('#status').val("");


 var $select4 = $('#cycleNumber').selectize();
	 var control4 = $select4[0].selectize;
	 control4.clear();
$('#cycleNumber').val("");
getDataforcycle();

$('#cycleDate').val("");
$("#tabnew").DataTable().clear().draw();

$('#errproductCode').hide();
$('#errcycleDate').hide();
	$('#errcycleNumber').hide();
 	
}



function getDataforcycle(){

var productCode = $("#productCode").val();
			
					var tokenValue = document.getElementsByName("_TransactToken")[0].value;
					var $select1 = $('#cycleNumber').selectize();
  				var selectize = $select1[0].selectize;
					
					$.ajax({
							url: "getCycleNum",
							type: "POST",
							data: {
								productCode: productCode,
								"_TransactToken": tokenValue
							},
							dataType: "json",
							success: function(result) {
							
							
							for(var res of result){
         
          var data = [
            {value:res,text:res},
          ];
    
         selectize.addOption(data);
        
							
        }
        
        
        let a = $('#cycleNumbers').val();
var select = $('#cycleNumber').selectize();
select[0].selectize.setValue(a);
								
							}
						});
						
	selectize.clearOptions();
						
						

}




