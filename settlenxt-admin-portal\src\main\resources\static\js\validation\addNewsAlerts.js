$(document).ready(function() {
	if( $('#lastOperation').val()=='SAVE' ||   $('#lastOperation').val()=='SUBMIT'
	 || $('#reqType').val()=='E')
	{
		 $('#afterSave').show();
		 $('#afterSave2').show();
		
		
	}
	
	
	
	
	$('form')
    .each(function(){
        $(this).data('serialized', $(this).serialize())
    })
    .on('change input', function(){
        $(this)             
            .find('input:submit, button:submit')
                .prop('disabled', $(this).serialize() == $(this).data('serialized'))
        ;
        $('#submitNewsData').attr('disabled', false);
       
     })
     
     
$('#submitNewsData').attr('disabled', true);
	
$('#title').on('keyup keypress blur change',function () {
        validateNewsAlertWithSpecialCase('title', true, "", 60, false);
    });	
$('#subTitle').on('keyup keypress blur change',function () {
        validateNewsAlertWithSpecialCase('subTitle', true, "", 60, false);
    });	
$('#summary').on('keyup keypress blur change',function () {
        validateNewsAlertWithSpecialCase('summary', true, "", 4000, false);
    });	
$('#details').on('keyup keypress blur change',function () {
        validateNewsAlertWithSpecialCase('details', true, "", 4000, false);
    });	
$('#footerData').on('keyup keypress blur change',function () {
        validateNewsAlertWithSpecialCase('footerData', true, "", 4000, false);
    });	
$('#public').on('keyup keypress blur change',function () {
        validateFromCommonVal('public', true, "", "", false);
    });	          
$('#specific').on('keyup keypress blur change',function () {
        validateFromCommonVal('specific', true, "", "", false);
    });	      
$('#common').on('keyup keypress blur change',function () {
        validateFromCommonVal('common', true, "", "", false);
    });	      
$('#periodType').on('keyup keypress blur change',function () {
        validateFromCommonVal('periodType', true, "SelectionBox", "", false);
    });	 
$('#weekFrom').on('keyup keypress blur change',function () {
        validateFromCommonVal('weekFrom', true, "SelectionBox", "", false);
    });	 
$('#weekTo').on('keyup keypress blur change',function () {
        validateFromCommonVal('weekTo', true, "SelectionBox", "", false);
    });	
$('#monthFrom').on('keyup keypress blur change',function () {
        validateFromCommonVal('monthFrom', true, "SelectionBox", "", false);
    });	 
$('#monthTo').on('keyup keypress blur change',function () {
        validateFromCommonVal('monthTo', true, "SelectionBox", "", false);
    });	
$('#comment').on('keyup keypress blur change',function () {
        validateNewsAlertWithSpecialCase('comment', true, "", "100", false);
    });	  


	
	
$("#sendMail").hide();
				
	
	$('#criticalDiv').hide();
	
	const fromDateStr = document.getElementById('fromDateStr');  
	fromDateStr.style.backgroundColor = 'white';

	const acqToDate = document.getElementById('toDateStr');  
	acqToDate.style.backgroundColor = 'white';
	
	const type = document.getElementById('types');  
	type.style.backgroundColor = 'white';
	const title1 = document.getElementById('title1');  
	title1.style.backgroundColor = 'white';
	const subTitle1 = document.getElementById('subTitle1');  
	subTitle1.style.backgroundColor = 'white';
	

	$('input[type=radio][name=publishType]').change(function() {
		  $('#submitNewsData').attr('disabled', false);
		  $("#publishTypeErr").find('.error').html('');
		  $("#errsendMail").find('.error').html('');
		  $("#errusers").find('.error').html('');
	$('#errusers').hide();
			
			$('#publishTypeErr').hide();
	    if (this.value == 'Specific') {
	        $("#Specific").show();
	        $("#SpecificMultipleGroups").multiselect('clearSelection');
	        $("#SpecificMultipleOrganization").multiselect('clearSelection');
	    	$('#SpecificMultipleOrganization').multiselect('refresh');
	    
	       
	        $('#SpecificMultipleGroups').multiselect('refresh');
	       	
	       
	        $("#SpecificMultipleUsers").empty();
		   	
	    }else{
	    	
	    	$("#Specific").hide();
	    	$('#sendMail input[type=radio]:checked').prop('checked',false);
	    
	    	$("#SpecificMultipleUsers").empty();
	    }
	});
	
	$('input[type=radio][name=sendMail]').change(function() {
		  $('#submitNewsData').attr('disabled', false);
		  $("#errsendMail").find('.error').html('');
	});
	

	
	
	var allSelected=true;
	  $('#SpecificMultipleOrganization').multiselect({
			buttonWidth : '300px',
			maxHeight:400,
			nonSelectedText:'Select Organizations',
			includeSelectAllOption: true,
			enableCaseInsensitiveFiltering: true,
			
			
			  onSelectAll: function(){
								  $("#errorgs").find('.error').html('');
					
					$('#errorgs').hide();
				  if(allSelected){
				     allSelected = false;
				  
				     if($('#SpecificMultipleOrganization').val()!=0){
						  if($('#SpecificMultipleGroups').val()!=0){
							  getUserList();
						  }
						  else{
							  
							  
							  $('#SpecificMultipleUsers').empty();
							  $("#SpecificMultipleUsers").multiselect("rebuild");
						    	$("#SpecificMultipleUsers").multiselect("refresh");
						  }
						  
					  }
				     
				     else{
				    	  $('#SpecificMultipleUsers').empty();
				    	  $("#SpecificMultipleUsers").multiselect("rebuild");
					    	$("#SpecificMultipleUsers").multiselect("refresh");
				     }
				  
				  }
				  
				  else{
					  
					  if($('#SpecificMultipleOrganization').val()!=0){
						  if($('#SpecificMultipleGroups').val()!=0){
							  getUserList();
						  }
						  else{
							  
							  
							  $('#SpecificMultipleUsers').empty();
							  $("#SpecificMultipleUsers").multiselect("rebuild");
						    	$("#SpecificMultipleUsers").multiselect("refresh");
						  }
						  
					  }
				     
				     else{
				    	  $('#SpecificMultipleUsers').empty();
				    	  $("#SpecificMultipleUsers").multiselect("rebuild");
					    	$("#SpecificMultipleUsers").multiselect("refresh");
				     }
					  
					  allSelected = true;
						
				  }
				  },
			
			onChange : function(_options, selected) {			
				if (selected) {
					 $("#errorgs").find('.error').html('');
						
						$('#errorgs').hide();
	  if($('#SpecificMultipleOrganization').val()!=0){
						  if($('#SpecificMultipleGroups').val()!=0){
							  getUserList();
						  }
					  }
					  else{
						  $('#SpecificMultipleUsers').empty(); 
						  $("#SpecificMultipleUsers").multiselect("rebuild");
					    	$("#SpecificMultipleUsers").multiselect("refresh");
					  }
								
				} else {
				
					  if($('#SpecificMultipleOrganization').val()!=0){
						  if($('#SpecificMultipleGroups').val()!=0){
							  getUserList();
						  }
					  }
					  else{
						  $('#SpecificMultipleUsers').empty(); 
						  $("#SpecificMultipleUsers").multiselect("rebuild");
					    	$("#SpecificMultipleUsers").multiselect("refresh");
					  }
				}

			},
		});
	  var allSelecteds=true;
	  
	  
	  $('#SpecificMultipleGroups').multiselect({
			buttonWidth : '300px',
			maxHeight:400,
			nonSelectedText:'Select Roles ',
			includeSelectAllOption: true,
			enableCaseInsensitiveFiltering: true,
			
			  onSelectAll: function(){
				  $("#errroles").find('.error').html('');
					
					$('#errroles').hide();
				  if(allSelecteds){
					     allSelecteds = false;
					  
					     if($('#SpecificMultipleOrganization').val()!=0){
							  if($('#SpecificMultipleGroups').val()!=0){
								  getUserList();
							  }
							  else{
								  
								  
								  $('#SpecificMultipleUsers').empty();
								  $("#SpecificMultipleUsers").multiselect("rebuild");
							    	$("#SpecificMultipleUsers").multiselect("refresh");
							  }
							  
						  }
					     
					     else{
					    	  $('#SpecificMultipleUsers').empty();
					    	  $("#SpecificMultipleUsers").multiselect("rebuild");
						    	$("#SpecificMultipleUsers").multiselect("refresh");
					     }
					  
					  }
					  
					  else{
						  
						  if($('#SpecificMultipleOrganization').val()!=0){
							  if($('#SpecificMultipleGroups').val()!=0){
								  getUserList();
							  }
							  else{
								  
								  
								  $('#SpecificMultipleUsers').empty();
								  $("#SpecificMultipleUsers").multiselect("rebuild");
							    	$("#SpecificMultipleUsers").multiselect("refresh");
							  }
							  
						  }
					     
					     else{
					    	  $('#SpecificMultipleUsers').empty();
					    	  $("#SpecificMultipleUsers").multiselect("rebuild");
						    	$("#SpecificMultipleUsers").multiselect("refresh");
					     }
						  
						  allSelecteds = true;
							
					  }
				  
				  },
			
			
			onChange : function(_options, selected) {
				
				if (selected) {
					
					 $("#errroles").find('.error').html('');
						
						$('#errroles').hide();
					 
					  if($('#SpecificMultipleOrganization').val()!=0){
						  if($('#SpecificMultipleGroups').val()!=0){
							  getUserList();
							
							  
							  
						  }
						  else{
							  $('#SpecificMultipleUsers').empty();
							  $("#SpecificMultipleUsers").multiselect("rebuild");
						    	$("#SpecificMultipleUsers").multiselect("refresh");
						  }
					  }
					  else{
						  $('#SpecificMultipleUsers').empty();
						  $("#SpecificMultipleUsers").multiselect("rebuild");
					    	$("#SpecificMultipleUsers").multiselect("refresh");
					  }
					  
				} else {
				
					
					 
					  if($('#SpecificMultipleOrganization').val()!=0){
						  if($('#SpecificMultipleGroups').val()!=0){
							  getUserList();
							
							  
							  
						  }
						  else{
							  $('#SpecificMultipleUsers').empty();
							  $("#SpecificMultipleUsers").multiselect("rebuild");
						    	$("#SpecificMultipleUsers").multiselect("refresh");
						  }
					  }
					  else{
						  $('#SpecificMultipleUsers').empty();
						  $("#SpecificMultipleUsers").multiselect("rebuild");
					    	$("#SpecificMultipleUsers").multiselect("refresh");
					  }
					
				}

			},
		});
	  var allSelectedSU=true;
	  $('#SpecificMultipleUsers').multiselect({
			buttonWidth : '300px',
			maxHeight:400,
			nonSelectedText:'Select Users',
			includeSelectAllOption: true,
			enableCaseInsensitiveFiltering: true,
			
			 onSelectAll: function(){
				 $("#errusers").find('.error').html('');
					
					$('#errusers').hide();
				  if(allSelectedSU){
					  allSelectedSU = false;
				  
				     $("#errusers").find('.error').html('');
						
						$('#errusers').hide();
				  
				  }
				  
				  else{
					
					  allSelectedSU = true;
						
				  }
				  },
			
			onChange : function(_options, selected) {
				
				 
				if (selected) {
								
					 $("#errusers").find('.error').html('');
						
						$('#errusers').hide();
				} 

			},
		});




	
	
	$("#fromDateStr").datepicker({
dateFormat : "dd-mm-yy",
changeMonth : true,
changeYear : true,
yearRange: "2022:2099",
        minDate : 0,
      
        onSelect : function() {
            this.focus();
        },
        onClose : function(_selectedDate) {
			valiDate();
       }
    });



   $("#toDateStr").datepicker({
 dateFormat : "dd-mm-yy",
        changeMonth : true,
        changeYear : true,
        yearRange: "2022:2099",
        minDate : 0,
     
        onSelect : function() {
            this.focus();
        },
        onClose : function(_selectedDate) {
        valiDate();
       }
    });	
		$("#fromDateStr")
	.on(
			'blur',
			function() {

				var fromDate = $("#fromDateStr").val();
				var toDate = $("#toDateStr").val();
				var fromDates = (document.getElementById('fromDateStr').value).replace("/^\s*|\s*$/g", '');

				var fromarr = fromDate.split('-');
				var toarr = toDate.split('-');
				var from = new Date(fromarr[2], fromarr[1], fromarr[0]);
				var to = new Date(toarr[2], toarr[1], toarr[0]);

				if (fromDates == "") {
					$("#fromDateStrErr").find('.error').html("Please enter From Date");
					
					$('#fromDateStrErr').show();
					
					
				} else if (!isEmpty(toDate) && (from.getTime() > to.getTime())) {
$("#fromDateStrErr").find('.error').html("From Date should be less than or equal to To date");
					
					$('#fromDateStrErr').show();
					
					
				} else {
$("#fromDateStrErr").find('.error').html('');
					
					$('#fromDateStrErr').hide();
					
					
				}
				
				
			});
	$("#toDateStr")
	.on(
			'blur',
			function() {

				var fromDate = $("#fromDateStr").val();
				var toDate = $("#toDateStr").val();
				var toDates = (document.getElementById('toDateStr').value).replace("/^\s*|\s*$/g", '');
				var fromarr = fromDate.split('-');
				var toarr = toDate.split('-');
				var from = new Date(fromarr[2], fromarr[1], fromarr[0]);
				var to = new Date(toarr[2], toarr[1], toarr[0]);

				if (toDates == "") {
	$("#toDateStrErr").find('.error').html("Please enter To Date");
					
					$('#toDateStrErr').show();
				
				
				} else if (!isEmpty(fromDate) && (from.getTime() > to.getTime())) {
	$("#toDateStrErr").find('.error').html("To Date should be greater than or equal to From date");
					
					$('#toDateStrErr').show();
				
					
					
				} else {
$("#toDateStrErr").find('.error').html('');
					
					$('#toDateStrErr').hide();
				
					
					
				}
			});

	
	$('input[type=radio][name=isType]').change(function() {
		$("#errisType").find('.error').html('');
		
		$('#errisType').hide();
	document.getElementById('types').value=this.value;
	
		if(this.value == 'Alerts'){
			$('#criticalDiv').show();
			
			
	
		}else{
			$('#criticalDiv').hide();
		
			
		}
	});
			
	
$('#clearSaveData').click(function()
		{
	
	$(".error").each(function() {
				if($(this).text().replace(/^\s+|\s+$/g, "").length > 0) {
			
		$(this).empty();
		
	}
});

$('input[type=radio]:checked').prop('checked',false);

	
if($('#reqType').val()=='ADD' )
	    {
	 
	$('#title').val("");
		$('#subTitle').val("");
		document.querySelector("#fromDateStr").value = "";
		document.querySelector("#toDateStr").value ="";
		$('#summary').val("");
		$('#details').val("");
		$('#footerData').val("");
		$('#title').val("");
		 $('#footerData').val("");
		 document.getElementById("newsId").checked = false;
		 document.getElementById("alertId").checked = false;
		  document.getElementById("critical").checked = false;
		 document.getElementById("nonCritical").checked = false;
		}
		if($('#clearId').val()=='E')
	    {
	     
		 $("#title1").val($('#title').val());	        
	 $("#subTitle1").val($('#subTitle').val());
		$("#Specific").hide();
	    	$("#SpecificMultipleOrganization").empty();
	    	$("#SpecificMultipleOrganization").multiselect("rebuild");
	    	$("#SpecificMultipleOrganization").multiselect("refresh");
	    	$("#SpecificMultipleGroups").empty();
	    	$("#SpecificMultipleGroups").multiselect("rebuild");
	    	$("#SpecificMultipleGroups").multiselect("refresh");
	    	$("#SpecificMultipleUsers").empty();
	    	$("#SpecificMultipleUsers").multiselect("rebuild");
	    	$("#SpecificMultipleUsers").multiselect("refresh");
	    		$('#publishTypes').val("");
	    			$('#sendMail').val("");
	    				$('#periodType').val("0");
	    				$('#weekFrom').val("0");
	    				$('#weekTo').val("0");
	    					$('#monthFrom').val("0");
	    				$('#monthTo').val("0");
$('#comment').val("");
		}
	

		});	
		

	 var elem="";
	 if($('#reqType').val()=='E')
	    {
	        
	        $("#title1").val($('#title').val());
	        
	        $("#subTitle1").val($('#subTitle').val());
	        
	    
	    
	    var value = document.getElementById('newsType').value;
	     document.getElementById('types').value = value;
	     
	    var value1 = document.getElementById('publishTypes').value;  
	    
	    var criticalFlag = document.getElementById('criticalFlag').value;  
	    
	    if(value!=""){
	    $("input[name=isType][value=" + value + "]").prop('checked', 'true');
	    
	    if(value=="Alerts"){
	    	$('#criticalDiv').show();
	    	if(criticalFlag=="Y"){
	    	$("input[name=critical][value=" + criticalFlag + "]").prop('checked', 'true');
	    	}
	    	else{
	    	criticalFlag="N";
	    		$("input[name=critical][value=" + criticalFlag + "]").prop('checked', 'true');	
	    		
	    	}
	    }
	    
	    }
	    if(value1!=""){
	    $("input[name=publishType][value=" + value1 + "]").prop('checked', 'true');
	    
	    var value3 = document.getElementById('sendMailFlag').value;
	    
	    
	    if(value1!='common'){
	    	
	    	if(value1!='Specific')
	    	{  $("#sendMail").show();}
	       
	        if(value3=='Y' || value3=='N'){
	        
	        $("input[name=sendMail][value=" + value3 + "]").prop('checked', 'true');}
	        
	        if(value1=='Specific'){
	        	   $("#Specific").show();
	    if(document.getElementById("editRoleList")!=null){
				var y = document.getElementById("editRoleList").value;
										let dataarray=[];
										if(y!=null){
							              dataarray=y.split(",");
							        	 
							        	  }
										
	     
										$('#SpecificMultipleGroups').multiselect('select', dataarray);}
	     
	     if(document.getElementById("editBankList")!=null){
				var y2 = document.getElementById("editBankList").value;
									let dataarray2=[];	
										if(y2!=null){
							              dataarray2=y2.split(",");
							        	 
							        	  }
										
										$('#SpecificMultipleOrganization').multiselect('select', dataarray2);  }
	   	 $('#SpecificMultipleGroups').multiselect('refresh');
	   	$('#SpecificMultipleOrganization').multiselect('refresh');
	   	 getUserList();
	   	 }
	   	 
	    }
	    else if(value1=='common'){
	    	
	    	 $("#sendMail").show();
	       
	        if(value3=='Y'){
	        
	        $("input[name=sendMail][value=" + value3 + "]").prop('checked', 'true');}
	    	
	    }
	    }
	    
	    



	       elem = document.getElementById("periodType").value;
	     
	        
	     if(elem == 'Weekly')
	    {
	    $("#weekDiv").show();
	     $("#monthDiv").hide();
	    }
	    else if (elem =='Monthly')
	    {
	   $("#monthDiv").show();
	    $("#weekDiv").hide();
	    }
	    else{
	          $("#monthDiv").hide();
	            $("#weekDiv").hide();
	        
	    }
	    }
				
$('#saveNewsData').click(function(){
var check=validateNewsAlerts(false);
 
 


 var crVal = $("input[name=critical]:checked").val();
 
 var newsType=$("input[name=isType]:checked").val();
 if(newsType=="Alerts"){
 if(crVal == "" || crVal==undefined){
						check=true;
						

						$("#errcritical").find('.error').html("Please Select Criticality Type");

						
						$('#errcritical').show();
					

					} else {
	$("#errcritical").find('.error').html('');
						
						$('#errcritical').hide();
					
					}
					
 
 }
 
var typeVal=$("input[name=isType]:checked").val();
let screenName="";
	var usersList ="";
					if(typeVal == "" || typeVal==undefined){
						check=true;
						
						$("#errisType").find('.error').html("Please Select Type");
						
						$('#errisType').show();
					

					} else {
	$("#errisType").find('.error').html('');
						
						$('#errisType').hide();
					
					}
					
					if($('#saveNewsResult').val()=='SAVE' || $('#saveNewsResult').val()=='EDIT' ){
						
				
				
					
					if($('#screenName').val()=='SAVED'){
						 screenName="SAVEDBACK"
					 }
					if($("input[name=publishType]:checked").val()=='Specific'){
					 var text3 = $('#SpecificMultipleUsers option:selected').toArray().map(item => item.text).join();
						
					 
					 
					 if( text3!=null){
					  
							$("#errusers").find('.error').html('');
							
							$('#errusers').hide();
						
							var nameArr3 = text3.split(',');
							
							 usersList = nameArr3[0];
							for ( var i = 1; i < nameArr3.length; i++) {
								usersList = usersList+ "," + nameArr3[i] 
										
							}
							}
					 else{
						 $("#errusers").find('.error').html('Please Select Users');
							
							$('#errusers').show();
						 
											 }}else{
												 var sendMailVal = $("input[name=sendMail]:checked").val();
												 if(sendMailVal == "" || sendMailVal==undefined){
														check=true;
														
														$("#errsendMail").find('.error').html("Please Select Send Mail Flag");
														
														$('#errsendMail').show();
													

													} else {
									$("#errsendMail").find('.error').html('');
														
														$('#errsendMail').hide();
													
													}	 
												 
											 }
					
					
					
					
					
					}

					if(!check){
						
					var titleVal = $('#title').val();
					
					var subTitleVal = $('#subTitle').val();
					
					$('#title1').val=titleVal;
					document.getElementById('title1').value=titleVal;
					
					$('#subTitle1').val = subTitleVal;
					document.getElementById('subTitle1').value=subTitleVal;
					
					var critical="N";				
					if($("input[name=isType]:checked").val()=='Alerts'){
					critical=$("input[name=critical]:checked").val();
					}
				
				var period= document.getElementById('periodType').value;
				
						var weekFrom = "";
						var weekTo = "";
						var monthFrom = "";
						var monthTo = "";
						
					if(period == 'Weekly')
					{
						weekFrom = document.getElementById('weekFrom').value;
						 weekTo = document.getElementById('weekTo').value;

					}
					else if(period == "Monthly")
					{
						monthFrom = document.getElementById('monthFrom').value;
					monthTo = document.getElementById('monthTo').value;
					}
					
				var tokenValue=	document.getElementsByName("_TransactToken")[0].value;
				
				var buttonType='';
	if($('#saveNewsData').attr('id')=='saveNewsData'){
		buttonType = 'save'
		}
					
					var model = {																		
						    	"newsId" : $('#newsId').val(),	
						    	"isType":$("input[name=isType]:checked").val(),
								"title" : encodeURIComponent($('#title').val()),
								"subTitle": encodeURIComponent($('#subTitle').val()),
								"fromDateStr": $('#fromDateStr').val(),											
								"toDateStr": $('#toDateStr').val(),
								"summary": encodeURIComponent($('#summary').val()),
								"details": encodeURIComponent($('#details').val()),
								"footerData":encodeURIComponent($('#footerData').val()),
								 "publishType":$("input[name=publishType]:checked").val(),                   
                                "sendMail":$("input[name=sendMail]:checked").val(),
								"periodType":$('#periodType').val(),
								"comment":encodeURIComponent($('#comment').val()),
								"reqType": $('#reqType').val(),
								"saveNewsResult": $('#saveNewsResult').val(),
								"buttonType":  buttonType,
								"lastOperation": $('#lastOperation').val(),
								 "critical":critical,
								"users":usersList,
								"weekFrom":weekFrom,
								"weekTo":weekTo,
								"monthFrom":monthFrom,
								"monthTo":monthTo,
								"screenName":screenName,
								
				
								
					}
						var formData = new FormData();
						formData.append("objectData", JSON.stringify(model));
											
					$.ajax({
						url : "addNewsAlerts",
						type : "POST",		   
						cache: false,
						processData: false,  // tell jQuery not to process the data
						contentType: false,  // tell jQuery not to set contentType
						dataType: "json",
						data: formData,
						"beforeSend": function(xhr) {
							xhr.setRequestHeader('_TransactToken', tokenValue);
						},
						headers: {
							'_TransactToken': tokenValue
						},		
					   success : function(data) {
							
						
							$('#isType').text("");
							$('#title').text("");
							$('#subTitle').text("");
							$('#fromDateStr').text("");
							$('#toDateStr').text("");
							$('#summary').text("");
							$('#details').text("");
							$('#footerData').text("");
							$('#saveNewsResult').val(data.saveNewsResult);
							$('#newsId').val(data.newsId);
							
							
							if(data.success && data.success.length > 0){
								$('#successStatus').html(data.success);
								$('#jqueryError').hide();
								$('#jquerySuccess').show();
								$('#requestState').val(data.requestState);
								 $('#afterSave').show();
								 $('#afterSave2').show();
								  $('#clearSaveData').hide();
								   $('#saveNewsData').show();
									$('#screenName').val(data.screenName);
							}
							

							if(data.error && data.error.length > 0){
								$('#errorStatus').html(data.error);
								$('#jqueryError').show();
								$('#jquerySuccess').hide();
							}
						
						},
						error : function() {
						console.log("error")
						urlPostAction("","/errorToken");
						}
					});
					} 
					});
				
				$('#submitNewsData').click(function(){	
					
					
					
					
					
					if(($('#submitNewsData').attr('disabled')!='disabled')){
				
					$('.jqueryError').text("");
					$('.jqueryError').hide();
					var check = false;
						var collapseOne = false;
						var collapseTwo = false;
						var collapseThree = false;
						check=validateNewsAlerts(true);
						
						
						
						 var newsType=$("input[name=isType]:checked").val();
						 if(newsType=="Alerts"){
				
						
						var crVal2 = $("input[name=critical]:checked").val();
 
 if(crVal2 == "" || crVal2==undefined){
						check=true;
						

						$("#errcritical").find('.error').html("Please Select Criticality Type");

						$('#errcritical').show();
					

					} else {
	$("#errcritical").find('.error').html('');
						
						$('#errcritical').hide();
					
					}}
						var typeVal2=$("input[name=isType]:checked").val();
						if(typeVal2 == "" || typeVal2==undefined){
							check=true;
							$("#errisType").find('.error').html("Please Select Type");
							
							$('#errisType').show();

							
						} else {
							$("#errisType").find('.error').html('');
							
							$('#errisType').hide();

						}
						var typeVal=$("input[name=publishType]:checked").val();
						if(typeVal == "" || typeVal==undefined){
							check=true;
	$("#publishTypeErr").find('.error').html("Please Select Type");
							
							$('#publishTypeErr').show();
							
						} else {
$("#publishTypeErr").find('.error').html('');
							
							$('#publishTypeErr').hide();
							
						}
						
						 var screenName="";
						if($('#screenName').val()=='SAVED'){
							 screenName="SAVEDBACK"
						 }
						if($("input[name=publishType]:checked").val()=='Specific'){
						 var text3 = $('#SpecificMultipleUsers option:selected').toArray().map(item => item.text).join();
						 var multipleOrg = $('#SpecificMultipleOrganization option:selected').toArray().map(item => item.text).join();
						 var multipleRole = $('#SpecificMultipleGroups option:selected').toArray().map(item => item.text).join();
						 
						 if(multipleOrg == "" || multipleOrg==null){
							 $("#errorgs").find('.error').html('Please Select Organizations');
								
								$('#errorgs').show();
								check=true;
								
								
							} else {
								 $("#errorgs").find('.error').html('');
									
									$('#errorgs').hide();
							}
						 if(multipleRole == "" || multipleRole==null){
							 $("#errroles").find('.error').html('Please Select Roles');
								
								$('#errroles').show();
								check=true;
								
								
							} else {
								 $("#errroles").find('.error').html('');
									
									$('#errroles').hide();
							}
						 
					
						 if(text3 == "" || text3==null){
							 $("#errusers").find('.error').html('Please Select Users');
								
								$('#errusers').show();
								check=true;
								
								
							} else {
								 $("#errusers").find('.error').html('');
									
									$('#errusers').hide();
							}
						}else{
							 var sendMailVal = $("input[name=sendMail]:checked").val();
							 if(sendMailVal == "" || sendMailVal==undefined){
									check=true;
									
									$("#errsendMail").find('.error').html("Please Select Send Mail Flag");
									
									$('#errsendMail').show();
								

								} else {
				$("#errsendMail").find('.error').html('');
									
									$('#errsendMail').hide();
								
								}	 
						}
						console.log("validation in submit "+check);
						
						if (!check) {
					
							addEdit();
						}
						else
							{
						
							if (collapseOne) {

								if ($("#collapseOne").attr(
										'class').indexOf(
										'collapse in') == -1) {
									$("#collapseOneLink")
											.trigger("click");
								}

							} else if (collapseTwo) {

								if ($("#collapseTwo").attr(
										'class').indexOf(
										'collapse in') == -1) {
									$("#collapseTwoLink")
											.trigger("click");
								}

							} else if (collapseThree) {

								if ($("#collapseThree").attr(
										'class').indexOf(
										'collapse in') == -1) {
									$("#collapseThreeLink")
											.trigger("click");
								}

							} 
							}
							
					}
					
				});
				
$('#public').on('click', function(){
  $("#sendMail").show();
});

$('#specific').on('click', function(){
  $("#sendMail").hide();
});


$('#common').on('click', function(){
  $("#sendMail").show();
});


 elem = document.getElementById("periodType");
elem.onchange = function(){
	$('#submitNewsData').attr('disabled', false);
	if(this.value == 'Weekly')
	{
    $("#weekDiv").show();
     $("#monthDiv").hide();
    }
    else if (this.value=='Monthly')
    {
   $("#monthDiv").show();
    $("#weekDiv").hide();
    }
    else{
    	  $("#monthDiv").hide();
    	    $("#weekDiv").hide();
    	
    }
    
};			
					
});


function validateNewsAlertWithSpecialCase(fieldId, isMandatory, fieldType, length,
	isExactLength) {
	let fieldValue = $("#" + fieldId).val();
	let isValid = true;

	if (isMandatory && fieldValue.trim() == "") {
		isValid = false;
	}

	var regExAll = /^[a-zA-Z0-9!@#$%^&*()_+<>?|\/;:'"{}[\]~\-\\= ,.]+$/;
	if (!regExAll.test(fieldValue)) {
		isValid = false;
	}
	
	if (isExactLength && fieldValue.length != length) {
		isValid = false;
		if (fieldValue.length == 0 && !isMandatory) {
			isValid = true;
		}
	}
	
	if(fieldValue.length > length){
		isValid = false;
	}
	
	if (isValid) {
		$("#err" + fieldId).hide();
	} else {
		if (validationMessages[fieldId]) {
			$("#err" + fieldId).find('.error').html(validationMessages[fieldId]);
		}
		$("#err" + fieldId).show();
	}
	return isValid;

}



function valiDate(){
 $('#submitNewsData').attr('disabled', false);
         vaildateActDate('fromDateStr');
        	vaildateDeactDate('toDateStr');

}

function validateNewsAlerts(submit){
	
	var check = validateFieldsFromCommonVal();


if(submit){
	check = validateAfterSubmit(check);	 			
	}

	
console.log("check is "+check);
	return check;
	
}
function validateAfterSubmit(check) {
	if (!validateFromCommonVal('periodType', true, "SelectionBox", "", false)) {
		check = true;
	}
	var period = document.getElementById('periodType').value;

	if (period == 'Weekly') {
		if (!validateFromCommonVal('weekFrom', true, "SelectionBox", "", false)) {
			check = true;
		}
		if (!validateFromCommonVal('weekTo', true, "SelectionBox", "", false)) {
			check = true;
		}
	}
	if (period == 'Monthly') {
		if (!validateFromCommonVal('monthFrom', true, "SelectionBox", "", false)) {
			check = true;
		}
		if (!validateFromCommonVal('monthTo', true, "SelectionBox", "", false)) {
			check = true;
		}
	}
	if (!validateNewsAlertWithSpecialCase('comment', true, "", "100", false)) {
		check = true;
	}
	return check;
}

function validateFieldsFromCommonVal() {
	var check = false;

	if (!validateNewsAlertWithSpecialCase('title', true, "", 60, false)) {
		check = true;
	}

	if (!validateNewsAlertWithSpecialCase('subTitle', true, "", 60, false)) {
		check = true;
	}

	if (!validateNewsAlertWithSpecialCase('summary', true, "", 4000, false)) {
		check = true;
	}

	if (!validateNewsAlertWithSpecialCase('details', true, "", 4000, false)) {
		check = true;
	}

	if (!validateNewsAlertWithSpecialCase('footerData', true, "", 4000, false)) {
		check = true;
	}

	if (!vaildateActDate('fromDateStrErr')) {
		check = true;
	}

	if (!vaildateDeactDate('toDateStrErr')) {
		check = true;
	}
	return check;
}

function showChange(){
	var typeVal=$("input[name=isType]:checked").val();
	
						if(typeVal == "" || typeVal==undefined){
							check=true;
							
							$("#errisType").find('.error').html("Please Select Type");
							
							$('#errisType').show();
						

						} else {
		$("#errisType").find('.error').html('');
							
							$('#errisType').hide();
						
						}
}

function showChange2(){
	var typeVal=$("input[name=critical]:checked").val();
	
	
						if(typeVal == "" || typeVal==undefined){
							check=true;
							
							$("#errcritical").find('.error').html("Please Select Criticality Type");
							
							$('#errcritical').show();
						

						} else {
		$("#errcritical").find('.error').html('');
							
							$('#errcritical').hide();
						
						}
}


function showChangePublish(){
	var typeVal=$("input[name=publishType]:checked").val();
	if(typeVal == "" || typeVal==undefined){
		check=true;
$("#publishTypeErr").find('.error').html("Please Select Publish Type");
		
		$('#publishTypeErr').show();
		
	} else {
$("#publishTypeErr").find('.error').html('');
		
		$('#publishTypeErr').hide();
		
	}
}
function getUserList() {
	$("#errusers").find('.error').html('');
	
	$('#errusers').hide();
	  if($('#SpecificMultipleOrganization').val()!=0){
		  if($('#SpecificMultipleGroups').val()!=0){
			var id1 = $('#SpecificMultipleOrganization option:selected').toArray().map(item => item.value).join();
			var id2 = $('#SpecificMultipleGroups option:selected').toArray().map(item => item.value).join();
			var bankList = id1.split(',');
			var roleList = id2.split(',');

	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var model = {	
			"bankList":bankList,
			"roleList":roleList,
			"_TransactToken" : tokenValue,
			}
	$
			.ajax({
				url : "./getUserList",
				type : "POST",
				 data : JSON.stringify(model),
				contentType: "application/json; charset=utf-8",
				dataType : "json",
				headers: {
					'_TransactToken': tokenValue
				},		
				success : function(data) {
					 
					$("#SpecificMultipleUsers").empty();
				
					    $("#SpecificUsers").show();
					  
					    if(document.getElementById("editUserList")==null){  
					    	
					    	
					$.each(data, function (_index, item) {
						
			            var opt = $('<option />', {
			                value: item.loginId,
			                text: item.loginId,
			                id:item.loginId,
			                
			            });
			            $('#SpecificMultipleUsers').append(opt);
			       	 $('#SpecificMultipleUsers').multiselect('rebuild');
			            });
					
					    }
					
					  
					if(document.getElementById("editUserList")!=null){
						var y = document.getElementById("editUserList").value;
						 let dataarray=[];
						 let opt="";
												dataarray = setDataArray(y, dataarray);
												
									        
												$.each(data, function (_index, item) {
													
													
													
													opt = setOpt(dataarray, item, opt);
														
														
													
									
										       	 $('#SpecificMultipleUsers').append(opt);
												 $('#SpecificMultipleUsers').multiselect('rebuild');
										            });}
						
				}
				
				
			});
				
	  
		  }
	  }
					
}

function setDataArray(y, dataarray) {
	if (y != null) {
		dataarray = y.split(",");

	}
	return dataarray;
}

function setOpt(dataarray, item, opt) {
	if (dataarray.includes(item.loginId)) {

		opt = $('<option />', {
			value: item.loginId,
			text: item.loginId,
			id: item.loginId,
			selected: true,
		});
	}

	else {

		opt = $('<option />', {
			value: item.loginId,
			text: item.loginId,
			id: item.loginId,
		});
	}
	return opt;
}

function vaildateActDate(_msgID) {

	var fromDate = $("#fromDateStr").val();
	var toDate = $("#toDateStr").val();
	var fromDates = (document.getElementById('fromDateStr').value).replace("/^\s*|\s*$/g", '');

	var fromarr = fromDate.split('-');
	var toarr = toDate.split('-');
	var from = new Date(fromarr[2], fromarr[1], fromarr[0]);
	var to = new Date(toarr[2], toarr[1], toarr[0]);

	if (fromDates == "") {
		$('#fromDateStrErr').find('.error').html('Please enter From Date');
		$('#toDateStrErr').show();

		return false;
	} else if (!isEmpty(toDate) && (from.getTime() > to.getTime())) {
		$('#fromDateStrErr').find('.error').html('From Date should be less than or Equal To date');
		$('#toDateStrErr').show();

		return false;
	} else {
		$('#fromDateStrErr').find('.error').html('');
		$('#toDateStrErr').hide();

	}
	
	return true;
}

function vaildateDeactDate(_msgID) {

	var fromDate = $("#fromDateStr").val();
	var toDate = $("#toDateStr").val();
	var toDates = (document.getElementById('toDateStr').value).replace("/^\s*|\s*$/g", '');
	var fromarr = fromDate.split('-');
	var toarr = toDate.split('-');
	var from = new Date(fromarr[2], fromarr[1], fromarr[0]);
	var to = new Date(toarr[2], toarr[1], toarr[0]);

	if (toDates == "") {
		$("#toDateStrErr").find('.error').html("Please enter To Date");
		
		$('#toDateStrErr').show();
		
		return false;

	} else if (!isEmpty(fromDate) && (from.getTime() > to.getTime())) {
		$('#toDateStrErr').find('.error').html('To Date should be greater than or Equal to From date');
		$('#toDateStrErr').show();
		return false;
	} else {
		$('#toDateStrErr').hide();
		$('#toDateStrErr').find('.error').html('');
		return true;
	}

}

function userAction(_type, action) {
	var data = "";
if($('#screenName').val()=='SAVEDBACK')
{
	action='/getSavedNewsList';
}	
postData(action, data);
}

function display() {
	  
	  var all_input = document.getElementsByClassName('check_in');
	  
	  for (var i of all_input) {
	    if (i.checked) {
	    	return true;
	    }
	  }
	  return false;
	}




function isEmpty(val){
	 return (val == undefined || val == null || val.length <= 0 || val =='' || val =='NaN' ||  val == 0) ? true : false;
}




function validateType(_msgId){
	
	
$("input[name=isType]:checked").val();
	
	
 
	
}





function saveNews(){
	var tokenVal=	document.getElementsByName("_TransactToken")[0].value;
	 var text3 = $('#SpecificMultipleUsers option:selected').toArray().map(item => item.text).join();
		
	 var usersList ="";
	
	 var screenName="";
	 if($('#screenName').val()=='SAVED'){
		 screenName="SAVEDBACK"
	 }
	 
	 if( text3!=null){
	  


			var nameArr3 = text3.split(',');
			
			 usersList = nameArr3[0];
			for ( var i = 1; i < nameArr3.length; i++) {
				usersList = usersList+ "," + nameArr3[i] 
						
			}}
	var buttonType='';
	if($('#saveNewsData').attr('id')=='saveNewsData'){
		buttonType = 'save'
		}
	var model = {
														
		    	"newsId" : $('#newsId').val(),
		    	"isType":$("input[name=isType]:checked").val(),
				"title" : encodeURIComponent($('#title').val()),
				"subTitle": encodeURIComponent($('#subTitle').val()),
				"fromDateStr": $('#fromDateStr').val(),											
				"toDateStr": $('#toDateStr').val(),
				"summary": encodeURIComponent($('#summary').val()),
				"details": encodeURIComponent($('#details').val()),
				"footerData":encodeURIComponent($('#footerData').val()),
				 "publishType":$("input[name=publishType]:checked").val(),                   
                 "sendMail":$("input[name=sendMail]:checked").val(),
				"periodType":$('#periodType').val(),
				"comment":encodeURIComponent($('#comment').val()),
				"screenName":screenName,
				"buttonType":  buttonType,
				"users":usersList
				
			
				
	}
	
	$.ajax({
		url : "saveNewsAlerts",
		type : "POST",
	    data : JSON.stringify(model),
		contentType: "application/json; charset=utf-8",
		headers: {
					'_TransactToken': tokenVal
				},		
		dataType : "json",
		success : function(data) {
			if(data.success && data.success.length > 0){
				$('#successStatus').html(data.success);
				$('#jqueryError').hide();
				$('#jquerySuccess').show();
				$('#screenName').val(data.screenName);
			}
			if(data.error && data.error.length > 0){
				$('#errorStatus').html(data.error);
				$('#jqueryError').show();
				$('#jquerySuccess').hide();
			}
		},
		error : function() {
		
		console.log("error");
		}
	
	});
}


function validate(id, msgID) {

	var state = (document.getElementById(id).value).replace("/^\s*|\s*$/g", '');
	var errState = document.getElementById(msgID);
	var regEx = /^[A-Z ]+$/i;

	if (state == "") {

		errState.className = 'error';
		errState.innerHTML = "";

		return false;

	} else if (!regEx.test(state)) {

		errState.className = 'error';
		errState.innerHTML = "State must contain alphabets only";
		return false;

	} else {

		errState.className = 'error';
		errState.innerHTML = "";
	}

	return true;
}

function postDiscardAction(action, id) {

	var url = action;
		var data =  "referenceNumber," + id ;
	postData(url, data);
}

function validateMandatory(id,msgId){
	
	
	

	var title = (document.getElementById(id).value);
	
var sample="Please Enter "+id;

	if (title == "") {

		 $("#"+msgId).find('.error').html(sample);
			
			$("#"+msgId).show();
		

		return false;

	}  else {
		

		 $("#"+msgId).find('.error').html('');
			
			$("#"+msgId).hide();
		
	}

	return true;
	
}

function addEdit()

{
	var tokenVal=	document.getElementsByName("_TransactToken")[0].value;
	var critical="N";
					
					if($("input[name=isType]:checked").val()=='Alerts'){
					critical=$("input[name=critical]:checked").val();
					}
	var text3 = $('#SpecificMultipleUsers option:selected').toArray().map(item => item.text).join();
	
	 var usersList ="";
	 var screenName="";
	 if($('#screenName').val()=='SAVED'){
		 screenName="SAVEDBACK"
	 }
	 if( text3!=null){
			var nameArr3 = text3.split(',');
			
			 usersList = nameArr3[0];
			for ( var i = 1; i < nameArr3.length; i++) {
				usersList = usersList+ "," + nameArr3[i] 
						
			}}
	
	
	var period= document.getElementById('periodType').value;
	var { weekFrom, weekTo, monthFrom, monthTo } = setWeekAndMon(period);
	
	
	var buttonType = '';
	if($('#submitNewsData').attr("id")=='submitNewsData'){
		buttonType = 'submit'
	}
		
	var model = {
														
		    	"newsId" : $('#newsId').val(),
		    	"isType": $("input[name=isType]:checked").val(),
				"title" : encodeURIComponent($('#title').val()),
				"subTitle":encodeURIComponent($('#subTitle').val()),
				"fromDateStr": $('#fromDateStr').val(),											
				"toDateStr": $('#toDateStr').val(),
				"summary": encodeURIComponent($('#summary').val()),
				"details": encodeURIComponent($('#details').val()),
				"footerData":encodeURIComponent($('#footerData').val()),
				"publishType":$("input[name=publishType]:checked").val(),                   
                "sendMail":$("input[name=sendMail]:checked").val(),
				"periodType":$('#periodType').val(),
				"comment":encodeURIComponent($('#comment').val()),
				"status":$('#status').val(),
				"saveNewsResult": $('#saveNewsResult').val(),
				"reqType": $('#reqType').val(),
				"buttonType": buttonType,		
				"lastOperation": $('#lastOperation').val(),
				 "critical":critical,
				"users":usersList,
				"weekFrom":weekFrom,
				"weekTo":weekTo,
				"monthFrom":monthFrom,
				"monthTo":monthTo,
				"screenName":screenName,
	}
	
	var formData = new FormData();
	formData.append("objectData", JSON.stringify(model));
	
	$.ajax({
		url: "addNewsAlerts",
		type: "POST",
		cache: false,
		processData: false,  // tell jQuery not to process the data
		contentType: false,  // tell jQuery not to set contentType
		dataType: "json",
		data: formData,
		"beforeSend": function(xhr) {
			xhr.setRequestHeader('_TransactToken', tokenVal);
		},


		headers: {
			'_TransactToken': tokenVal
		},
		success : function(data) {
			if(data.success && data.success.length > 0){
				$('#successStatus').html(data.success);
				
				$('#screenName').val(data.screenName);
				$('#jqueryError').hide();
				$('#jquerySuccess').show();
				$('#afterSubmit').hide();
				$('#afterSubmitDiscard').hide();

				$('#requestState').val(data.requestState);
				if ($('#requestState').val() == "P") {
					$("input").prop('disabled', true);
					$("select").prop('disabled', true);
					}
				
				 $('#afterSave2').hide();
			}
			if(data.error && data.error.length > 0){
				$('#errorStatus').html(data.error);
				$('#jqueryError').show();
				$('#jquerySuccess').hide();
			}
		},
		error : function() {
	console.log("error")
	urlPostAction("","/errorToken");
		}
	
	});
	
	
}

function setWeekAndMon(period) {
	var weekFrom = "";
	var weekTo = "";
	var monthFrom = "";
	var monthTo = "";

	if (period == 'Weekly') {
		weekFrom = document.getElementById('weekFrom').value;
		weekTo = document.getElementById('weekTo').value;

	}
	else if (period == "Monthly") {
		monthFrom = document.getElementById('monthFrom').value;
		monthTo = document.getElementById('monthTo').value;
	}
	return { weekFrom, weekTo, monthFrom, monthTo };
}
