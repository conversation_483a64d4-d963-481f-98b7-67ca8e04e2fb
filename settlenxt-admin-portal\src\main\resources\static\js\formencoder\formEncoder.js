/*This function remove space*/
function removeSpace(f) {
	var e, i = 1;
	while (e = f.elements[i++]) 
	{
		if(e.type == 'text')
		{
			e.value = (e.value).replace(/^\s*|\s*$/g,'');
		}
	}
}

function encodeForm(f) {
	removeSpace(f);
	
	var encva=document.getElementsByName("encodeval");
	
	if(encva.length !=0)
	{
		$("[name=encodeval]").remove();
		$("[name=fieldNames]").remove();
	}
	
	
	var e, i = 0;
	//var encodeval;
	var formval = "";
	var fieldNames = "";
	while (e = f.elements[i++]) {
		
		if(!(e.type == "submit" || e.type == "button" || e.type == "reset" || e.type == "radio" || e.type == "checkbox" || e.type == "textarea" ||  e.name =='encodeval'|| e.name =='fieldNames' || e.name.match("_isChkBox")))
		{
		
		if(isAlphaNumeric(e.value)){
			formval = formval + e.value;}
			if (fieldNames != "")
				fieldNames = fieldNames + "," + e.name;
			else
				fieldNames = e.name;
		}

	}

	if(formval != "" && fieldNames != "")
	{
	
	
		var encodeval = getmyVal(formval);
		window.setTimeout(encodeval, 10000);
		encodeval = $('<div>').text(encodeval).html();
		//Create hidden
		var input = document.createElement("input");
		input.setAttribute("type", "hidden");
		input.setAttribute("name", "encodeval");
		input.setAttribute("value", encodeval);
		//Append to form element that you want .
		f.appendChild(input);
		var inputFields = document.createElement("input");
		inputFields.setAttribute("type", "hidden");
		inputFields.setAttribute("name", "fieldNames");
		inputFields.setAttribute("value", fieldNames);
		//Append to form element that you want .
		f.appendChild(inputFields);
	}
	
}
/*This function post form*/
function postDataForm(linkurl, data)
{
	var parameters = data.split(",");
	for(var i = 0; i < parameters.length; ++i)
	{
		var dynInput = document.createElement("input");
		dynInput.setAttribute("type", "hidden");
		dynInput.setAttribute("id", parameters[i]);
		dynInput.setAttribute("name",parameters[i] );
		++i;
		dynInput.setAttribute("value", parameters[i]);

		document.forms[0].appendChild(dynInput);
	}
	document.forms[0].action =linkurl;
	document.forms[0].method="POST";
	encodeForm(document.forms[0]);
	document.forms[0].submit();

}


function postBinData(linkurl, data)
{
	var parameters = data.split(",");
	for(var i = 0; i < parameters.length; ++i)
	{
		var dynInput = document.createElement("input");
		dynInput.setAttribute("type", "hidden");
		dynInput.setAttribute("id", parameters[i]);
		dynInput.setAttribute("name",parameters[i] );
		++i;
		dynInput.setAttribute("value", parameters[i]);

		document.forms[0].appendChild(dynInput);
	}
	document.forms[0].action =linkurl;
	encFrm(document.forms[0]);
	document.forms[0].submit();

}

function isAlphaNumeric(input){
var alphaNumericRegex=/^[a-zA-Z0-9+=/]+$/;
return alphaNumericRegex.test(input);
}

