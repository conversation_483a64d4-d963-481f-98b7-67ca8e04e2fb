package org.npci.settlenxt.adminportal.service;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.dto.TipSurchargeDTO;
import org.npci.settlenxt.adminportal.repository.TipSurchargeRepository;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Throwable.class)
public class TipSurchargeServiceImpl implements TipSurchargeService {

	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	TipSurchargeRepository tipSurchargeRepository;

	// show main tab
	public List<TipSurchargeDTO> getTipSurchargeList() {
		return tipSurchargeRepository.getTipSurchargeListMain();
	}

	// show approval tab
	@Override
	@Transactional(readOnly = true)
	public List<TipSurchargeDTO> getPendingTipSurcharge() {
		return tipSurchargeRepository.getTipSurchargePendingForApproval();
	}

	// view main tab info
	@Override
	@Transactional(readOnly = true)
	public TipSurchargeDTO getTipSurchargeMainInfo(int tipSurchargeId) {
		TipSurchargeDTO tipSurcharge = tipSurchargeRepository.getTipSurchargeProfileMain(tipSurchargeId);
		if (tipSurcharge == null) {
			throw new SettleNxtException("No Data Exist", "");
		}
		return tipSurcharge;
	}

	// view approval tab info
	@Override
	public TipSurchargeDTO getTipSurchargeStgInfo(String tipSurchargeId) {
		TipSurchargeDTO tipSurchargeStg = tipSurchargeRepository
				.getTipSurchargeStgInfoById(Integer.parseInt(tipSurchargeId));
		if (tipSurchargeStg == null) {
			throw new SettleNxtException("No Data Exist", "");
		}
		return tipSurchargeStg;
	}

	// add edit
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public TipSurchargeDTO addEditTipSurcharge(TipSurchargeDTO tipSurchargeDto) {
		int funcId = CommonConstants.TRANSACT_FUCTIONALITY_ADD_TIP_SURCHARGE_CONFIG;
		if (CommonConstants.EDIT_TIP_SURCHARGE.equalsIgnoreCase(tipSurchargeDto.getAddEditFlag())) {
			funcId = CommonConstants.TRANSACT_FUCTIONALITY_EDIT_TIP_SURCHARGE_CONFIG;
		}
		Date lt = new Date();
		tipSurchargeDto.setStatus("I");
		tipSurchargeDto.setRequestState("P");
		tipSurchargeDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

		if (funcId == CommonConstants.TRANSACT_FUCTIONALITY_EDIT_TIP_SURCHARGE_CONFIG) {
			tipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
			tipSurchargeDto.setLastUpdatedOn(lt);
			tipSurchargeDto.setLastUpdatedBy(sessionDTO.getUserName());
			tipSurchargeRepository.updateTipSurcharge(tipSurchargeDto);
		}
		if (funcId == CommonConstants.TRANSACT_FUCTIONALITY_ADD_TIP_SURCHARGE_CONFIG) {

			tipSurchargeDto.setCreatedOn(lt);
			tipSurchargeDto.setCreatedBy(sessionDTO.getUserName());
			tipSurchargeDto.setTipSurchargeId(tipSurchargeRepository.fetchTipSurchargeIdSequence());
			tipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
			tipSurchargeRepository.insertTipSurchargeStg(tipSurchargeDto);
		}

		return tipSurchargeRepository.getTipSurchargeStg(tipSurchargeDto.getTipSurchargeId());

	}

	// edit
	@Override
	@Transactional(readOnly = true)
	public TipSurchargeDTO getTipSurchargeForEdit(int tipSurchargeId) {
		return tipSurchargeRepository.getTipSurchargeStgInfoById(tipSurchargeId);
	}

	// for checker
	@Override
	public TipSurchargeDTO approveOrRejectTipSurcharge(int tipSurchargeId, String status, String remarks) {
		TipSurchargeDTO tipSurchargeDto = getTipSurchargeStg(tipSurchargeId);
		tipSurchargeDto.setRequestState(status);
		tipSurchargeDto.setCheckerComments(remarks);
		tipSurchargeDto.setStatus("A");
		
		Date lt = new Date();
		tipSurchargeDto.setLastUpdatedOn(lt);
		tipSurchargeDto.setLastUpdatedBy(sessionDTO.getUserName());
		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			tipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			TipSurchargeDTO ifscdtoMain = tipSurchargeRepository.getTipSurchargeMain(tipSurchargeId);
			if (ifscdtoMain != null) {
				tipSurchargeRepository.updateTipSurchargeMain(tipSurchargeDto);
			} else {
				tipSurchargeRepository.insertTipSurchargeMain(tipSurchargeDto);
			}
		}
		if (CommonConstants.REQUEST_STATE_REJECTED.equals(status)) {
			tipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
			}
		tipSurchargeRepository.updateTipSurchargeRequestState(tipSurchargeDto);
		return tipSurchargeDto;
	}

	// for bulk checker
	@Override
	public String approveOrRejectTipSurchargeBulk(String bulkApprovalReferenceNoList, String status, String remarks) {
		String[] referenceNoArr = bulkApprovalReferenceNoList.split("\\|");
		int tipSurchargeId = 0;

		for (String refNum:referenceNoArr) {
			try {
				if (!StringUtils.isEmpty(refNum)) {
					tipSurchargeId = Integer.parseInt(refNum);
					TipSurchargeDTO tipSurchargeDto = getTipSurchargeStg(tipSurchargeId);
					if (tipSurchargeDto == null) {

						throw new SettleNxtException("Exception occurred with Ref No" + refNum, "");
					}
					approveOrRejectBulk(status, remarks, tipSurchargeId, tipSurchargeDto);
				}
			} catch (Exception ex) {
				throw new SettleNxtException("", "Exception for Ref no" + refNum, ex);
			}
		}
		return CommonConstants.YES_FLAG;
	}

	private void approveOrRejectBulk(String status, String remarks, int tipSurchargeId,
			TipSurchargeDTO tipSurchargeDto) {
		if (tipSurchargeDto != null) {
			tipSurchargeDto.setRequestState(status);
			tipSurchargeDto.setCheckerComments(remarks);
			if ("A".equals(status)) {
				tipSurchargeDto.setStatus("A");
			} else {
				tipSurchargeDto.setStatus("I");
			}

			
			Date lt = new Date();
			tipSurchargeDto.setLastUpdatedOn(lt);
			tipSurchargeDto.setLastUpdatedBy(sessionDTO.getUserName());
			if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
				tipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
				TipSurchargeDTO tipSurchargeDtoMain = tipSurchargeRepository.getTipSurchargeMain(tipSurchargeId);
				if (tipSurchargeDtoMain != null) {
					tipSurchargeRepository.updateTipSurchargeMain(tipSurchargeDto);
				} else {
					tipSurchargeRepository.insertTipSurchargeMain(tipSurchargeDto);
				}
			}
			if (CommonConstants.REQUEST_STATE_REJECTED.equals(status)) {
				tipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
				}
			tipSurchargeRepository.updateTipSurchargeRequestState(tipSurchargeDto);
		}
	}

	// for discard
	@Override
	public TipSurchargeDTO discardTipSurcharge(int tipSurchargeId) {
		TipSurchargeDTO tipSurchargeDto = getTipSurchargeStg(tipSurchargeId);
		TipSurchargeDTO tipSurchargeDtoMain = tipSurchargeRepository.getTipSurchargeMain(tipSurchargeId);
		if (tipSurchargeDtoMain != null) {
			tipSurchargeDtoMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			tipSurchargeDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			tipSurchargeRepository.updateTipSurchargeDiscard(tipSurchargeDtoMain);
		} else {
			tipSurchargeRepository.deleteDiscardedEntry(tipSurchargeDto);
		}
		return tipSurchargeDto;
	}

	@Override
	public TipSurchargeDTO getTipSurchargeStg(int tipSurchargeId) {
		return tipSurchargeRepository.getTipSurchargeStg(tipSurchargeId);
	}
	@Override
	public List<TipSurchargeDTO> checkDuplicateDataForTipSurcharge(TipSurchargeDTO tipSurchargeDto)
	{
		return tipSurchargeRepository.getDuplicateTipSurchargeData(tipSurchargeDto);
		
		
	}
}
