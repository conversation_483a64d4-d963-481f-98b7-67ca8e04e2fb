<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<div class="panel panel-default">
															<div class="panel-heading">
																<h4 class="panel-title">
																	<a data-toggle="collapse" data-parent="#childAccordion"
																		href="#collapseFees${disTxnList.mti}${disTxnList.funcCode}${disTxnList.txnId}" class="collapsed" > See Fees and Penalties
																		<span class="glyphicon glyphicon-plus accor-fees-icon" ></span>
																	</a>
																</h4>
															</div>
															<div id="collapseFees${disTxnList.mti}${disTxnList.funcCode}${disTxnList.txnId}" class="panel-collapse collapse">
																<div class="panel-body">
																	<div class="">
																		<div class="row">
																			<div class="col-md-12">
																				<div class="card">
																					<div class="card-body">
																						<table name="tabNew" class="table table-striped" style="font-size: 12px">
																							<caption style="display:none;">SEE FEES PENALTY</caption>  
																							<thead>
																							<th style="display:none;" scope="col"></th>
																								<tr>
																									<td><label><spring:message code="RP_LBL_SrNo" /></label></td>
																									<td><label><spring:message code="txn.detail.fees.lbl.feeName" /></label></td>
																									<td><label><spring:message code="txn.detail.fees.lbl.currency" /></label></td>
																									<td><label><spring:message code="txn.detail.fees.lbl.acqFee" /></label></td>
																									<td><label><spring:message code="txn.detail.fees.lbl.issFee" /></label></td>
																								</tr>
																							</thead>
																							<tbody>
																								<!-- 1 -->
																								<c:set var="intchgFeeDto" value="${disTxnList.intchgFeeDto}" scope="page" />
																								<c:set var="onusFeeDto" value="${disTxnList.onusFeeDto}" scope="page" />
																								<c:set var="feeSrNo" value="1" scope="page" />
																								<c:if test="${not empty intchgFeeDto && empty onusFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode"><c:if test="${empty intchgFeeDto.feeCode}">N/A</c:if>${intchgFeeDto.feeCode }-<c:if test="${empty intchgFeeDto.feeDesc}">N/A</c:if>${intchgFeeDto.feeDesc}</td>
																										<td id="txnCurrency"><c:if test="${empty intchgFeeDto.txnCurrency}">N/A</c:if>${intchgFeeDto.txnCurrency}-<c:if test="${empty intchgFeeDto.txnCurrencyDesc}">N/A</c:if>${intchgFeeDto.txnCurrencyDesc}</td>
																										<td class="disputeFee" id="intchgFeeAcq"><c:if test="${empty disTxnList.intchgFeeAcq}">N/A</c:if>${disTxnList.intchgFeeAcq }</td>
																										<td class="disputeFee" id="intchgFeeIss"><c:if test="${empty disTxnList.intchgFeeIss}">N/A</c:if>${disTxnList.intchgFeeIss }</td>
																									</tr>
																									<!-- 2 -->
																									<%-- <tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode"><c:if test="${empty intchgFeeDto.feeCode}">N/A</c:if>${intchgFeeDto.feeCode }-<c:if test="${empty intchgFeeDto.feeDesc}">N/A</c:if>${intchgFeeDto.feeDesc}</td>
																										<td id="txnCurrency"><c:if test="${empty intchgFeeDto.txnCurrency}">N/A</c:if>${intchgFeeDto.txnCurrency}-<c:if test="${empty intchgFeeDto.txnCurrencyDesc}">N/A</c:if>${intchgFeeDto.txnCurrencyDesc }</td>
																										<td id="intchgFeeAcq"><c:if test="${empty disTxnList.intchgFeeAcq}">N/A</c:if>${disTxnList.intchgFeeAcq }</td>
																										<td id="intchgFeeIss">0.00</td>
																									</tr> --%>
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode">Interchange GST Fee</td>
																										<td id="txnCurrency"><c:if test="${empty intchgFeeDto.txnCurrency}">N/A</c:if>${intchgFeeDto.txnCurrency}-<c:if test="${empty intchgFeeDto.txnCurrencyDesc}">N/A</c:if>${intchgFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeAcq">${disTxnList.intchgFeeGstAcq}</td>
																										<td class="disputeFee" id="intchgFeeIss">${disTxnList.intchgFeeGstIss}</td>
																									</tr>
																								</c:if>
																								<c:if test="${not empty onusFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode"><c:if test="${empty onusFeeDto.feeCode}">N/A</c:if>${onusFeeDto.feeCode }-<c:if test="${empty onusFeeDto.feeDesc}">N/A</c:if>${onusFeeDto.feeDesc}</td>
																										<td id="txnCurrency"><c:if test="${empty onusFeeDto.txnCurrency}">N/A</c:if>${onusFeeDto.txnCurrency}-<c:if test="${empty onusFeeDto.txnCurrencyDesc}">N/A</c:if>${onusFeeDto.txnCurrencyDesc}</td>
																										<td class="disputeFee" id="intchgFeeAcq"><c:if test="${empty disTxnList.intchgFeeAcq}">N/A</c:if>${disTxnList.intchgFeeAcq }</td>
																										<td class="disputeFee" id="intchgFeeIss"><c:if test="${empty disTxnList.intchgFeeIss}">N/A</c:if>${disTxnList.intchgFeeIss }</td>
																									</tr>
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode">Onus GST Fee</td>
																										<td id="txnCurrency"><c:if test="${empty intchgFeeDto.txnCurrency}">N/A</c:if>${intchgFeeDto.txnCurrency}-<c:if test="${empty intchgFeeDto.txnCurrencyDesc}">N/A</c:if>${intchgFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeAcq">${disTxnList.intchgFeeGstAcq}</td>
																										<td class="disputeFee" id="intchgFeeIss">${disTxnList.intchgFeeGstIss}</td>
																									</tr>
																								</c:if>
																								<!-- 3 -->
																								<c:set var="issProcessingFeeDto" value="${disTxnList.issProcessingFeeDto}" scope="page" />
																								<c:if test="${not empty issProcessingFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode"><c:if test="${empty issProcessingFeeDto.feeCode}">N/A</c:if>${issProcessingFeeDto.feeCode }-<c:if test="${empty issProcessingFeeDto.feeDesc}">N/A</c:if>${issProcessingFeeDto.feeDesc}</td>
																										<td id="txnCurrency"><c:if test="${empty issProcessingFeeDto.txnCurrency}">N/A</c:if>${issProcessingFeeDto.txnCurrency}-<c:if test="${empty issProcessingFeeDto.txnCurrencyDesc}">N/A</c:if>${issProcessingFeeDto.txnCurrencyDesc}</td>
																										<td class="disputeFee" id="intchgFeeAcq">0.00</td>
																										<td class="disputeFee" id="intchgFeeIss"><c:if test="${empty disTxnList.processingFeeIss}">N/A</c:if>${disTxnList.processingFeeIss}</td>
																									</tr>
																								</c:if>
																								<!-- 4 -->
																								<c:set var="acqProcessingFeeDto" value="${disTxnList.acqProcessingFeeDto}" scope="page" />
																								<c:if test="${not empty acqProcessingFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode"><c:if test="${empty acqProcessingFeeDto.feeCode }">N/A</c:if>${acqProcessingFeeDto.feeCode }-<c:if test="${empty acqProcessingFeeDto.feeDesc}">N/A</c:if>${acqProcessingFeeDto.feeDesc }</td>
																										<td id="txnCurrency"><c:if test="${empty acqProcessingFeeDto.txnCurrency }">N/A</c:if>${acqProcessingFeeDto.txnCurrency}-<c:if test="${empty acqProcessingFeeDto.txnCurrencyDesc }">N/A</c:if>${acqProcessingFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeAcq"><c:if test="${empty disTxnList.processingFeeAcq }">N/A</c:if>${disTxnList.processingFeeAcq}</td>
																										<td class="disputeFee" id="intchgFeeIss">0.00</td>
																									</tr>
																								</c:if>
																								<c:if test="${not empty issProcessingFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode">Issuer Processing GST Fee</td>
																										<td id="txnCurrency"><c:if test="${empty issProcessingFeeDto.txnCurrency}">N/A</c:if>${issProcessingFeeDto.txnCurrency}-<c:if test="${empty issProcessingFeeDto.txnCurrencyDesc}">N/A</c:if>${issProcessingFeeDto.txnCurrencyDesc}</td>
																										<td class="disputeFee" id="intchgFeeAcq">0.00</td>
																										<td class="disputeFee" id="intchgFeeIss">${disTxnList.processingFeeGstIss}</td>
																									</tr>
																								</c:if>
																								<c:if test="${not empty acqProcessingFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode">Acquirer Processing GST Fee</td>
																										<td id="txnCurrency"><c:if test="${empty acqProcessingFeeDto.txnCurrency }">N/A</c:if>${acqProcessingFeeDto.txnCurrency}-<c:if test="${empty acqProcessingFeeDto.txnCurrencyDesc }">N/A</c:if>${acqProcessingFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeAcq">${disTxnList.processingFeeGstAcq}</td>
																										<td class="disputeFee" id="intchgFeeIss">0.00</td>
																									</tr>
																								</c:if>
																								<!-- 5 -->
																								<c:set var="issAssessmentFeeDto" value="${disTxnList.issAssessmentFeeDto}" scope="page" />
																								<c:if test="${not empty issAssessmentFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode"><c:if test="${empty issAssessmentFeeDto.feeCode}">N/A</c:if>${issAssessmentFeeDto.feeCode }-<c:if test="${empty issAssessmentFeeDto.feeDesc}">N/A</c:if>${issAssessmentFeeDto.feeDesc }</td>
																										<td id="txnCurrency"><c:if test="${empty issAssessmentFeeDto.txnCurrency}">N/A</c:if>${issAssessmentFeeDto.txnCurrency}-<c:if test="${empty issAssessmentFeeDto.txnCurrencyDesc}">N/A</c:if>${issAssessmentFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeAcq">0.00</td>
																										<td class="disputeFee" id="intchgFeeIss"><c:if test="${empty disTxnList.assessmentFeeIss}">N/A</c:if>${disTxnList.assessmentFeeIss}</td>
																									</tr>
																								</c:if>
																								<!-- 6 -->
																								<c:set var="acqAssessmentFeeDto" value="${disTxnList.acqAssessmentFeeDto}" scope="page" />
																								<c:if test="${not empty acqAssessmentFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode"><c:if test="${empty acqAssessmentFeeDto.feeCode}">N/A</c:if>${acqAssessmentFeeDto.feeCode }-<c:if test="${empty acqAssessmentFeeDto.feeDesc}">N/A</c:if>${acqAssessmentFeeDto.feeDesc }</td>
																										<td id="txnCurrency"><c:if test="${empty acqAssessmentFeeDto.txnCurrency}">N/A</c:if>${acqAssessmentFeeDto.txnCurrency}-<c:if test="${empty acqAssessmentFeeDto.txnCurrencyDesc}">N/A</c:if>${acqAssessmentFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeAcq"><c:if test="${empty disTxnList.assessmentFeeAcq}">N/A</c:if>${disTxnList.assessmentFeeAcq }</td>
																										<td class="disputeFee" id="intchgFeeIss">0.00</td>
																									</tr>
																								</c:if>
																								<c:if test="${not empty issAssessmentFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode">Issuer Assessment GST Fee</td>
																										<td id="txnCurrency"><c:if test="${empty issAssessmentFeeDto.txnCurrency}">N/A</c:if>${issAssessmentFeeDto.txnCurrency}-<c:if test="${empty issAssessmentFeeDto.txnCurrencyDesc}">N/A</c:if>${issAssessmentFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeAcq">0.00</td>
																										<td class="disputeFee" id="intchgFeeIss">${disTxnList.assessmentFeeGstIss}</td>
																									</tr>
																								</c:if>
																								<!-- 6 -->
																								<c:if test="${not empty acqAssessmentFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode">Acquirer Assessment GST Fee</td>
																										<td id="txnCurrency"><c:if test="${empty acqAssessmentFeeDto.txnCurrency}">N/A</c:if>${acqAssessmentFeeDto.txnCurrency}-<c:if test="${empty acqAssessmentFeeDto.txnCurrencyDesc}">N/A</c:if>${acqAssessmentFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeAcq">${disTxnList.assessmentFeeGstAcq}</td>
																										<td class="disputeFee" id="intchgFeeIss">0.00</td>
																									</tr>
																								</c:if>
																								<!-- 7 -->
																								<c:set var="issAuthFeeDto" value="${disTxnList.issAuthFeeDto}" scope="page" />
																								<c:if test="${not empty issAuthFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode"><c:if test="${empty issAuthFeeDto.feeCode}">N/A</c:if>${issAuthFeeDto.feeCode }-<c:if test="${empty issAuthFeeDto.feeDesc}">N/A</c:if>${issAuthFeeDto.feeDesc }</td>
																										<td id="txnCurrency"><c:if test="${empty issAuthFeeDto.txnCurrency}">N/A</c:if>${issAuthFeeDto.txnCurrency}-<c:if test="${empty issAuthFeeDto.txnCurrencyDesc}">N/A</c:if>${issAuthFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeIss">0.00</td>
																										<td class="disputeFee" id="intchgFeeAcq"><c:if test="${empty disTxnList.authFeeIss}">N/A</c:if>${disTxnList.authFeeIss}</td>
																									</tr>
																								</c:if>
																								<!-- 8 -->
																								<c:set var="acqAuthFeeDto" value="${disTxnList.acqAuthFeeDto}" scope="page" />
																								<c:if test="${not empty acqAuthFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode"><c:if test="${empty acqAuthFeeDto.feeCode}">N/A</c:if>${acqAuthFeeDto.feeCode }-<c:if test="${empty acqAuthFeeDto.feeDesc}">N/A</c:if>${acqAuthFeeDto.feeDesc }</td>
																										<td id="txnCurrency"><c:if test="${empty acqAuthFeeDto.txnCurrency}">N/A</c:if>${acqAuthFeeDto.txnCurrency}-<c:if test="${empty acqAuthFeeDto.txnCurrencyDesc}">N/A</c:if>${acqAuthFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeIss"><c:if test="${empty disTxnList.authFeeAcq}">N/A</c:if>${disTxnList.authFeeAcq }</td>
																										<td class="disputeFee" id="intchgFeeAcq">0.00</td>
																									</tr>
																								</c:if>
																								<c:if test="${not empty issAuthFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode">Issuer Auth GST Fee</td>
																										<td id="txnCurrency"><c:if test="${empty issAuthFeeDto.txnCurrency}">N/A</c:if>${issAuthFeeDto.txnCurrency}-<c:if test="${empty issAuthFeeDto.txnCurrencyDesc}">N/A</c:if>${issAuthFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeIss">0.00</td>
																										<td class="disputeFee" id="intchgFeeAcq">${disTxnList.authFeeGstIss}</td>
																									</tr>
																								</c:if>
																								<!-- 8 -->
																								<c:if test="${not empty acqAuthFeeDto}">
																									<tr>
																										<td id="feeCode">${feeSrNo}</td>
																										<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																										<td id="feeCode">Acquirer Auth GST Fee</td>
																										<td id="txnCurrency"><c:if test="${empty acqAuthFeeDto.txnCurrency}">N/A</c:if>${acqAuthFeeDto.txnCurrency}-<c:if test="${empty acqAuthFeeDto.txnCurrencyDesc}">N/A</c:if>${acqAuthFeeDto.txnCurrencyDesc }</td>
																										<td class="disputeFee" id="intchgFeeIss">${disTxnList.authFeeGstAcq }</td>
																										<td class="disputeFee" id="intchgFeeAcq">0.00</td>
																									</tr>
																								</c:if>



														
																								<c:forEach var="feeAcq" items="${disTxnList.feeListAcq}">
														
																									<c:if test="${not empty feeAcq}">
																										<tr>
																											<td id="feeCode">${feeSrNo}</td>
																											<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																											<td id="feeCode"><c:if test="${empty feeAcq.feeCode}">N/A</c:if>${feeAcq.feeCode }-<c:if
																													test="${empty feeAcq.feeDesc}">N/A</c:if>${feeAcq.feeDesc}</td>
																											<td id="txnCurrency"><c:if
																													test="${empty feeAcq.txnCurrency}">N/A</c:if>${feeAcq.txnCurrency}-<c:if
																													test="${empty feeAcq.txnCurrencyDesc}">N/A</c:if>${feeAcq.txnCurrencyDesc}</td>
														
																											<td><c:if test="${empty feeAcq.feeAmtInJson}">N/A</c:if>${feeAcq.feeAmtInJson}</td>
																											<td>0.00</td>
																										</tr>
																									</c:if>
																								</c:forEach>
														
																								<c:forEach var="feeIss" items="${disTxnList.feeListIss}">
														
																									<c:if test="${not empty feeIss}">
																										<tr>
																											<td id="feeCode">${feeSrNo}</td>
																											<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																											<td id="feeCode"><c:if test="${empty feeIss.feeCode}">N/A</c:if>${feeIss.feeCode }-<c:if
																													test="${empty feeIss.feeDesc}">N/A</c:if>${feeIss.feeDesc}</td>
																											<td id="txnCurrency"><c:if
																													test="${empty feeIss.txnCurrency}">N/A</c:if>${feeIss.txnCurrency}-<c:if
																													test="${empty feeIss.txnCurrencyDesc}">N/A</c:if>${feeIss.txnCurrencyDesc}</td>
																											<td>0.00</td>
																											<td><c:if test="${empty feeIss.feeAmtInJson}">N/A</c:if>${feeIss.feeAmtInJson}</td>
																										</tr>
																									</c:if>
																								</c:forEach>




										<!-- Filing Fee -->
																								<c:if test="${not empty disTxnList.filingDisputeFeeCode }">
																								<tr>
																									<td id="filingFeeCode">${feeSrNo}</td>
																									<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																									<td id="filingFeeCode">
																										<c:choose>
																												<c:when test="${not empty disTxnList.filingDisputeFeeCode && not empty disTxnList.filingDisputefeeName }">${disTxnList.filingDisputeFeeCode} - ${disTxnList.filingDisputefeeName}</c:when>
																												<c:otherwise>
																													<c:choose>
																														<c:when test="${not empty disTxnList.filingDisputeFeeCode &&  empty disTxnList.filingDisputefeeName}">${disTxnList.filingDisputeFeeCode}</c:when>
																													</c:choose>
																													<c:choose>
																														<c:when test="${empty disTxnList.filingDisputeFeeCode}">N/A</c:when>
																													</c:choose>
																												</c:otherwise>
																										</c:choose>
																									</td>
																									<td id="txnCurrency"><c:if
																											test="${empty disTxnList.tranCur}">N/A</c:if>${disTxnList.tranCur}-<c:if
																											test="${empty disTxnList.txnCurrencyDesc}">N/A</c:if>${disTxnList.txnCurrencyDesc}
																									</td>
																									<td class="disputeFee" id="intchgFeeIss">${disTxnList.filingDisputeFeeAcq}</td>
																									<td class="disputeFee" id="intchgFeeAcq">${disTxnList.filingDisputeFeeIss}</td>
																								</tr>
																								<!-- 2nd Row -->
																								<tr>
																									<td id="filingFeeCode">${feeSrNo}</td>
																									<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																									<td id="filingFeeCode">Filing Dispute GST</td>
																									<td id="txnCurrency"><c:if
																											test="${empty disTxnList.tranCur}">N/A</c:if>${disTxnList.tranCur}-<c:if
																											test="${empty disTxnList.txnCurrencyDesc}">N/A</c:if>${disTxnList.txnCurrencyDesc}
																									</td>
																									<td class="disputeFee" id="intchgFeeIss">${disTxnList.filingDisputeFeeGSTAcq}</td>
																									<td class="disputeFee" id="intchgFeeAcq">${disTxnList.filingDisputeFeeGSTIss}</td>
																								</tr>	
																							</c:if>
																							<!-- Member Dispute Fee -->
																							<c:if test="${not empty disTxnList.memberDisputeFeeCode}">
																								<tr>
																									<td id="memberFeeCode">${feeSrNo}</td>
																									<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																									<td id="memberFeeCode">
																										<c:choose>
																												<c:when test="${not empty disTxnList.memberDisputeFeeCode && not empty disTxnList.memberDisputefeeName }">${disTxnList.memberDisputeFeeCode} - ${disTxnList.memberDisputefeeName}</c:when>
																												<c:otherwise>
																													<c:choose>
																														<c:when test="${not empty disTxnList.memberDisputeFeeCode &&  empty disTxnList.memberDisputefeeName}">${disTxnList.memberDisputeFeeCode}</c:when>
																													</c:choose>
																													<c:choose>
																														<c:when test="${empty disTxnList.memberDisputeFeeCode}">N/A</c:when>
																													</c:choose>
																												</c:otherwise>
																										</c:choose>
																									</td>
																									<td id="txnCurrency"><c:if
																											test="${empty disTxnList.tranCur}">N/A</c:if>${disTxnList.tranCur}-<c:if
																											test="${empty disTxnList.txnCurrencyDesc}">N/A</c:if>${disTxnList.txnCurrencyDesc}
																									</td>
																									<td class="disputeFee" id="memberFeeIss">${disTxnList.memberDisputeFeeAcq}</td>
																									<td class="disputeFee" id="memberFeeAcq">${disTxnList.memberDisputeFeeIss}</td>
																								</tr>
																								<!-- 2nd Row -->
																								<tr>
																									<td id="memberFeeCode">${feeSrNo}</td>
																									<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																									<td id="memberFeeCode">Member Dispute GST</td>
																									<td id="txnCurrency"><c:if
																											test="${empty disTxnList.tranCur}">N/A</c:if>${disTxnList.tranCur}-<c:if
																											test="${empty disTxnList.txnCurrencyDesc}">N/A</c:if>${disTxnList.txnCurrencyDesc}
																									</td>
																									<td class="disputeFee" id="memberFeeIss">${disTxnList.memberDisputeFeeGSTAcq}</td>
																									<td class="disputeFee" id="memberFeeAcq">${disTxnList.memberDisputeFeeGSTIss}</td>
																								</tr>
																								
																							</c:if>
																							<!-- Customer Compensation Fee -->
																							<c:if test="${not empty disTxnList.custCompDisputeFeeCode}">
																								<tr>
																									<td id="custCompFeeCode">${feeSrNo}</td>
																									<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																									<td id="custCompFeeCode">
																										<c:choose>
																												<c:when test="${not empty disTxnList.custCompDisputeFeeCode && not empty disTxnList.custCompDisputefeeName }">${disTxnList.custCompDisputeFeeCode} - ${disTxnList.custCompDisputefeeName}</c:when>
																												<c:otherwise>
																													<c:choose>
																														<c:when test="${not empty disTxnList.custCompDisputeFeeCode &&  empty disTxnList.custCompDisputefeeName}">${disTxnList.custCompDisputeFeeCode}</c:when>
																													</c:choose>
																													<c:choose>
																														<c:when test="${empty disTxnList.custCompDisputeFeeCode}">N/A</c:when>
																													</c:choose>
																												</c:otherwise>
																										</c:choose>
																									</td>
																									<td id="txnCurrency"><c:if
																											test="${empty disTxnList.tranCur}">N/A</c:if>${disTxnList.tranCur}-<c:if
																											test="${empty disTxnList.txnCurrencyDesc}">N/A</c:if>${disTxnList.txnCurrencyDesc}
																									</td>
																									<td class="disputeFee" id="custCompFeeIss">${disTxnList.custCompDisputeFeeAcq}</td>
																									<td class="disputeFee" id="custCompFeeAcq">${disTxnList.custCompDisputeFeeIss}</td>
																								</tr>
																								<!-- 2nd Row -->
																								<tr>
																									<td id="custCompFeeCode">${feeSrNo}</td>
																									<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																									<td id="custCompFeeCode">Customer compensation GST</td>
																									<td id="txnCurrency"><c:if
																											test="${empty disTxnList.tranCur}">N/A</c:if>${disTxnList.tranCur}-<c:if
																											test="${empty disTxnList.txnCurrencyDesc}">N/A</c:if>${disTxnList.txnCurrencyDesc}
																									</td>
																									<td class="disputeFee" id="custCompFeeGSTAcq">${disTxnList.custCompDisputeFeeGSTAcq}</td>
																									<td class="disputeFee" id="custCompFeeGSTIss">${disTxnList.custCompDisputeFeeGSTIss}</td>
																								</tr>
																								
																							</c:if>
																							<!-- Penalty Fee -->
																							<c:if test="${not empty disTxnList.penaltyDisputeFeeCode}">
																								<tr>
																									<td id="penaltyFeeCode">${feeSrNo}</td>
																									<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																									<td id="penaltyFeeCode">
																										<c:choose>
																												<c:when test="${not empty disTxnList.penaltyDisputeFeeCode && not empty disTxnList.penaltyDisputefeeName }">${disTxnList.penaltyDisputeFeeCode} - ${disTxnList.penaltyDisputefeeName}</c:when>
																												<c:otherwise>
																													<c:choose>
																														<c:when test="${not empty disTxnList.penaltyDisputeFeeCode &&  empty disTxnList.penaltyDisputefeeName}">${disTxnList.penaltyDisputeFeeCode}</c:when>
																													</c:choose>
																													<c:choose>
																														<c:when test="${empty disTxnList.penaltyDisputeFeeCode}">N/A</c:when>
																													</c:choose>
																												</c:otherwise>
																										</c:choose>
																									</td>
																									<td id="txnCurrency"><c:if
																											test="${empty disTxnList.tranCur}">N/A</c:if>${disTxnList.tranCur}-<c:if
																											test="${empty disTxnList.txnCurrencyDesc}">N/A</c:if>${disTxnList.txnCurrencyDesc}
																									</td>
																									<td class="disputeFee" id="penaltyFeeIss">${disTxnList.penaltyDisputeFeeAcq}</td>
																									<td class="disputeFee" id="penaltyFeeAcq">${disTxnList.penaltyDisputeFeeIss}</td>
																								</tr>
																								<!-- 2nd Row -->
																								<tr>
																									<td id="penaltyFeeCode">${feeSrNo}</td>
																									<c:set var="feeSrNo" value="${feeSrNo+1}" scope="page" />
																									<td id="penaltyFeeCode">Penalty Dispute GST</td>
																									<td id="txnCurrency"><c:if
																											test="${empty disTxnList.tranCur}">N/A</c:if>${disTxnList.tranCur}-<c:if
																											test="${empty disTxnList.txnCurrencyDesc}">N/A</c:if>${disTxnList.txnCurrencyDesc}
																									</td>
																									<td class="disputeFee" id="penaltyFeeGSTAcq">${disTxnList.penaltyDisputeFeeGSTAcq}</td>
																									<td class="disputeFee" id="penaltyFeeGSTIss">${disTxnList.penaltyDisputeFeeGSTIss}</td>
																								</tr>
																							</c:if>
																							</tbody>
																						</table>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>