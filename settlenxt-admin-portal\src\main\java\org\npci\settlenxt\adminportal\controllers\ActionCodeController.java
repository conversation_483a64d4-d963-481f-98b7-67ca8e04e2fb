package org.npci.settlenxt.adminportal.controllers;

import java.net.URLDecoder;
import java.util.List;
import java.util.Locale;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.ActionCodeDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.ActionCodeService;
import org.npci.settlenxt.adminportal.service.CappingAmountService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

@Controller
public class ActionCodeController extends BaseController {

	@Autowired
	private MessageSource messageSource;

	@Autowired
	ActionCodeService actionCodeService;

	@Autowired
	private BaseLookupService lookupservice;

	@Autowired
	CappingAmountService cappingAmountService;

	@Autowired
	private SessionDTO sessionDTO;

	private static final String SHOW_ACTION_CODE = "showActionCode";
	private static final String ADD_ACTION_CODE = "addEditActionCode";
	private static final String APPROVE_ACTION_CODE = "viewApproveActionCode";
	private static final String VIEW_ACTION_CODE = "viewActionCode";
	private static final String MTI_LIST = "mtiList";
	private static final String RAISED_BY = "raisedBy";
	private static final String SHOW_CHECK_BOX = "showCheckBox";
	private static final String ACTION_CODE_LIST = "actionCodeList";
	private static final String ACTION_CODE_LIST2 = "actionCodeList2";
	private static final String REASON_CODE_LIST = "reasonCodeList";
	private static final String STATUS = "status";
	private static final String TAT_PERIOD_DAY_TYPE = "tatPeriodDayType";
	private static final String MAIN_TAB = "mainTab";
	private static final String APPROVAL_TAB = "approvalTab";
	private static final String SHOW_BUTTON="showButton";

	@PostMapping("/showActionCode")
	@PreAuthorize("hasAuthority('View Action Code')")
	public String showActionCode(Model model) {

		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.YES_FLAG);
		List<ActionCodeDTO> actionCodeList = actionCodeService.getApprovedActionCodeFromMain();
		model.addAttribute(CommonConstants.ACTION_CODE_LIST, actionCodeList);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
		}

		return getView(model, SHOW_ACTION_CODE);
	}

	@PostMapping("/actionCodePendingForApproval")
	@PreAuthorize("hasAuthority('View Action Code')")
	public String getPendingActionCode(Model model) {

		return loadActionCodePendingForApproval(model);
	}

	private String loadActionCodePendingForApproval(Model model) {
		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);

		List<ActionCodeDTO> pendingActionCodeList = actionCodeService.getPendingForApprovalActionCode();
		model.addAttribute(CommonConstants.PENDING_ACTION_CODE_LIST, pendingActionCodeList);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
		}

		return getView(model, SHOW_ACTION_CODE);
	}

	@PostMapping("/addActionCode")
	@PreAuthorize("hasAuthority('Add Action Code')")
	public String addActionCode(Model model) {

		ActionCodeDTO actionCodeDTO = new ActionCodeDTO();
		List<CodeValueDTO> mtiList = actionCodeService.getMccFromFuncCode();
		List<CodeValueDTO> functionCodeList = actionCodeService.getFuncCodeList();
		List<CodeValueDTO> raisedBy = lookupservice.getLookupData(CommonConstants.RAISED_BY);
		List<CodeValueDTO> actionCodeList = actionCodeService.getActionCodeList();
		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.PARENT_PAGE, MAIN_TAB);
		model.addAttribute(CommonConstants.FUNCTION_CODE_LIST, functionCodeList);
		model.addAttribute(MTI_LIST, mtiList);
		model.addAttribute(RAISED_BY, raisedBy);
		model.addAttribute(ACTION_CODE_LIST, actionCodeList);
		model.addAttribute(ACTION_CODE_LIST2, actionCodeList);
		List<CodeValueDTO> reasonCodeList = actionCodeService.getReasonCodeList();
		model.addAttribute(REASON_CODE_LIST, reasonCodeList);
		List<CodeValueDTO> callType = lookupservice.getLookupData(CommonConstants.CALL_TYPE);
		model.addAttribute(TAT_PERIOD_DAY_TYPE, callType);
		actionCodeDTO.setAddEditFlag(CommonConstants.ADD_ACTION_CODE);
		model.addAttribute(CommonConstants.ACTION_CODE_DTO, actionCodeDTO);

		return getView(model, ADD_ACTION_CODE);
	}

	@PostMapping("/saveActionCode")
	@PreAuthorize("hasAuthority('Add Action Code')")
	public String saveActionCode(@ModelAttribute ActionCodeDTO actionCodeDTO, Model model) {

		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		try {
			actionCodeDTO
					.setAllowedActncdToRemove(URLDecoder.decode(actionCodeDTO.getAllowedActncdToRemove(), "UTF-8"));

			actionCodeDTO = actionCodeService.addEditActionCode(actionCodeDTO);
		} catch (Exception ex) {
			model.addAttribute(CommonConstants.ACTION_CODE_DTO, actionCodeDTO);
			model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_ACTION_CODE, ex);

		}
		model.addAttribute(CommonConstants.ACTION_CODE_DTO, actionCodeDTO);
		List<CodeValueDTO> mtiList = actionCodeService.getMccFromFuncCode();

		List<CodeValueDTO> functionCodeList = actionCodeService.getFuncCodeList();
		List<CodeValueDTO> raisedBy = lookupservice.getLookupData(CommonConstants.RAISED_BY);

		List<CodeValueDTO> actionCodeList = actionCodeService.getActionCodeList();
		List<CodeValueDTO> reasonCodeList = actionCodeService.getReasonCodeList();
		model.addAttribute(REASON_CODE_LIST, reasonCodeList);
		model.addAttribute(CommonConstants.PARENT_PAGE, MAIN_TAB);
		model.addAttribute(SHOW_BUTTON, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.FUNCTION_CODE_LIST, functionCodeList);
		model.addAttribute(MTI_LIST, mtiList);
		model.addAttribute(RAISED_BY, raisedBy);
		model.addAttribute(ACTION_CODE_LIST, actionCodeList);
		model.addAttribute(ACTION_CODE_LIST2, actionCodeList);
		List<CodeValueDTO> callType = lookupservice.getLookupData(CommonConstants.CALL_TYPE);
		model.addAttribute(TAT_PERIOD_DAY_TYPE, callType);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("action.addSuccess.msg"));
		return getView(model, ADD_ACTION_CODE);

	}

	@PostMapping("/checkDupActionCode")
	@PreAuthorize("hasAuthority('Add Action Code')")
	public ResponseEntity<Object> checkDupActionCode(Model model, @RequestParam("actionCode") String actionCode) {

		boolean result = actionCodeService.checkDistinctActioncode(actionCode);

		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/viewApproveActionCode")
	@PreAuthorize("hasAuthority('View Action Code')")
	public String viewApproveActionCode(@RequestParam("actionCodeId") int actionCodeId, Model model) {

		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);

		ActionCodeDTO actionCodeDTO = actionCodeService.getActionCode(actionCodeId);

		model.addAttribute(CommonConstants.ACTION_CODE_DTO, actionCodeDTO);
		return getView(model, APPROVE_ACTION_CODE);

	}

	private void checkActionCodeApproveStatus(ActionCodeDTO actionCodeDTO, Model model) {
		Locale locale=Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(actionCodeDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("action.approveSuccess.msg", null, locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("action.rejectSuccess.msg", null, locale));
		}
	}

	@PostMapping("/approveOrRejectBulkActionCode")
	@PreAuthorize("hasAuthority('Approve Action Code')")
	public String approveOrRejectActionCode(@RequestParam("bulkActionCodeIdList") String bulkActionCodeIdList,
			@RequestParam("status") String status, Model model) {

		String remarks = "";
		if (status.equals(CommonConstants.STATUS_APPROVE)) {
			remarks = CommonConstants.BULK_APPROVE;
		} else if (status.equals(CommonConstants.STATUS_REJECT)) {
			remarks = CommonConstants.BULK_REJECT;
		}

		ActionCodeDTO actionCodeDTO = actionCodeService.updateApproveOrRejectBulkActionCode(bulkActionCodeIdList,
				status, remarks);
		checkActionCodeApproveStatus(actionCodeDTO, model);

		return loadActionCodePendingForApproval(model);

	}

	@PostMapping("/approveActionCode")
	@PreAuthorize("hasAuthority('Approve Action Code')")
	public String approveActionCode(@RequestParam("actionCodeId") int actionCodeId,
			@RequestParam("status") String status, @RequestParam("remarks") String remarks, Model model) {

		ActionCodeDTO actionCodeDTO = actionCodeService.approveOrRejectActionCode(actionCodeId, status, remarks);

		model.addAttribute(CommonConstants.ACTION_CODE_DTO, actionCodeDTO);

		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("action.approveSuccess.msg"));
		} else if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("action.rejectSuccess.msg"));
		}
		return getView(model, APPROVE_ACTION_CODE);
	}

	@PostMapping("/getActionCode")
	@PreAuthorize("hasAuthority('View Action Code')")
	public String getActionCode(@RequestParam("actionCodeId") int actionCodeId, Model model) {

		ActionCodeDTO actionCodeDTO = actionCodeService.getActionCodeInfoFromMain(actionCodeId);

		model.addAttribute(CommonConstants.ACTION_CODE_DTO, actionCodeDTO);
		model.addAttribute(MAIN_TAB, CommonConstants.YES_FLAG);
		return getView(model, VIEW_ACTION_CODE);

	}

	@PostMapping("/getRejActionCode")
	@PreAuthorize("hasAuthority('View Action Code')")
	public String getRejActionCode(@RequestParam("actionCodeId") int actionCodeId, Model model) {

		ActionCodeDTO actionCodeDTO = actionCodeService.getActionCode(actionCodeId);
		model.addAttribute(APPROVAL_TAB, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.ACTION_CODE_DTO, actionCodeDTO);
		return getView(model, VIEW_ACTION_CODE);

	}

	@PostMapping("/editActionCode")
	@PreAuthorize("hasAuthority('Edit Action Code')")
	public String editActionCode(@RequestParam("actionCodeId") int actionCodeId,
			@RequestParam("parentPage") String parentPage, Model model) {

		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
		List<CodeValueDTO> mtiList = actionCodeService.getMccFromFuncCode();
		List<CodeValueDTO> functionCodeList = actionCodeService.getFuncCodeList();
		List<CodeValueDTO> raisedBy = lookupservice.getLookupData(CommonConstants.RAISED_BY);
		model.addAttribute(CommonConstants.FUNCTION_CODE_LIST, functionCodeList);
		List<CodeValueDTO> reasonCodeList = actionCodeService.getReasonCodeList();
		model.addAttribute(REASON_CODE_LIST, reasonCodeList);
		List<CodeValueDTO> actionCodeList = actionCodeService.getActionCodeList();
		model.addAttribute(CommonConstants.FUNCTION_CODE_LIST, functionCodeList);
		model.addAttribute(MTI_LIST, mtiList);
		model.addAttribute(CommonConstants.PARENT_PAGE, parentPage);
		model.addAttribute(RAISED_BY, raisedBy);
		model.addAttribute(ACTION_CODE_LIST, actionCodeList);
		model.addAttribute(ACTION_CODE_LIST2, actionCodeList);
		List<CodeValueDTO> callType = lookupservice.getLookupData(CommonConstants.CALL_TYPE);
		model.addAttribute(TAT_PERIOD_DAY_TYPE, callType);
		ActionCodeDTO actionCodeDTO = actionCodeService.getActionCodeFromMainEdit(actionCodeId);

		actionCodeDTO.setAddEditFlag(CommonConstants.EDIT_ACTION_CODE);
		model.addAttribute(CommonConstants.ACTION_CODE_DTO, actionCodeDTO);
		return getView(model, ADD_ACTION_CODE);
	}

	@PostMapping("/updateActionCode")
	@PreAuthorize("hasAuthority('Edit Action Code')")
	public String updateActionCode(@ModelAttribute ActionCodeDTO actionCodeDTO,
			@RequestParam("parentPage") String parentPage, Model model) {

		try {

			actionCodeDTO
					.setAllowedActncdToRemove(URLDecoder.decode(actionCodeDTO.getAllowedActncdToRemove(), "UTF-8"));

			model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
			actionCodeService.updateActionCode(actionCodeDTO);

		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, ADD_ACTION_CODE, ex);
		}

		List<CodeValueDTO> mtiList = actionCodeService.getMccFromFuncCode();
		List<CodeValueDTO> functionCodeList = actionCodeService.getFuncCodeList();
		List<CodeValueDTO> raisedBy = lookupservice.getLookupData(CommonConstants.RAISED_BY);
		model.addAttribute(CommonConstants.FUNCTION_CODE_LIST, functionCodeList);
		List<CodeValueDTO> reasonCodeList = actionCodeService.getReasonCodeList();
		model.addAttribute(REASON_CODE_LIST, reasonCodeList);
		List<CodeValueDTO> actionCodeList = actionCodeService.getActionCodeList();
		model.addAttribute(CommonConstants.FUNCTION_CODE_LIST, functionCodeList);
		model.addAttribute(MTI_LIST, mtiList);
		model.addAttribute(RAISED_BY, raisedBy);
		model.addAttribute(CommonConstants.PARENT_PAGE, parentPage);
		model.addAttribute(ACTION_CODE_LIST, actionCodeList);
		model.addAttribute(ACTION_CODE_LIST2, actionCodeList);
		List<CodeValueDTO> callType = lookupservice.getLookupData(CommonConstants.CALL_TYPE);
		model.addAttribute(TAT_PERIOD_DAY_TYPE, callType);
		model.addAttribute(SHOW_BUTTON, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.ACTION_CODE_DTO, actionCodeDTO);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("action.editSuccess.msg"));
		return getView(model, ADD_ACTION_CODE);
	}

	@PostMapping("/discardActionCode")
	@PreAuthorize("hasAuthority('Edit Action Code')")
	public String discardActionCode(@RequestParam("actionCodeId") int actionCodeId, Model model) {

		ActionCodeDTO actionCodeDTO = actionCodeService.discardActionCode(actionCodeId);

		model.addAttribute(CommonConstants.ACTION_CODE_DTO, actionCodeDTO);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("action.discardSuccess.msg"));
		return getView(model, APPROVE_ACTION_CODE);
	}

}