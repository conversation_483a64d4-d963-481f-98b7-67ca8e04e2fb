<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.LookupRepository">
	
	<select id="getLookupListMain" resultType="LookUpDTO">
		SELECT ls.type as lkpType,ls.code as lkpValue,ls.status as status,ls.created_by as createdBy
		,ls.created_on as createdOn,ls.last_updated_by as lastUpdatedBy,ls.last_updated_on as lastUpdatedOn
		,ls.lookup_id as lookupId,ls.description as lkpDesc
		FROM lookup_stg l inner join lookup ls on ls.lookup_id = l.lookup_id 
		where l.request_state in <foreach item='item' index='index' collection='requestStateList' open='(' separator=',' close=')'>#{item}</foreach>
	</select>
	
	<insert id="insertLookUpList" >
	insert into lookup_stg(type,code,status,
	created_by,
	created_on,
	description,
	last_operation,request_state,
	lookup_id)
	values
	(
	#{lkpType},
	#{lkpValue},#{status},
	#{createdBy},#{createdOn},
	#{lkpDesc},#{lastOperation},#{requestState},#{lookupId}
	)
	</insert>
	
	<insert id="insertLookUpListMain" >
	insert into lookup(type,code,status,
	created_by,
	created_on,
	description,
	lookup_id)
	values
	(
	#{lkpType},
	#{lkpValue},#{status},
	#{createdBy},#{createdOn},	
	#{lkpDesc},#{lookupId}
	)
	</insert>
	
	<select id="getPendingLookupListStg" resultType="LookUpDTO">
		SELECT ls.type as lkpType,ls.code as lkpValue,ls.status as status,ls.created_by as createdBy
		,ls.created_on as createdOn,ls.last_updated_by as lastUpdatedBy,ls.last_updated_on as lastUpdatedOn,ls.last_operation as lastOperation
		,ls.lookup_id as lookupId,ls.description as lkpDesc,ls.request_state as requestState
		FROM lookup_stg ls where ls.request_state in <foreach item='item' index='index' collection='requestStateList' open='(' separator=',' close=')'>#{item}</foreach>
	</select>
	
	<select id="getApprovedLookupListStg" resultType="LookUpDTO">
		SELECT ls.type as lkpType,ls.code as lkpValue,ls.status as status,ls.created_by as createdBy
		,ls.created_on as createdOn,ls.last_updated_by as lastUpdatedBy,ls.last_updated_on as lastUpdatedOn,ls.last_operation as lastOperation
		,ls.lookup_id as lookupId,ls.description as lkpDesc,ls.request_state as requestState
		FROM lookup_stg ls where ls.request_state= #{requestState}
	</select>
	
	<select id="getLookUpInfoFromMain" resultType="LookUpDTO">
		SELECT l.type as lkpType,l.code as lkpValue,l.status as status,l.created_by as createdBy
		,l.created_on as createdOn,l.last_updated_by as lastUpdatedBy,l.last_updated_on as lastUpdatedOn
		,l.lookup_id as lookupId,l.description as lkpDesc
	    FROM lookup l where l.lookup_id=#{lookupId}
	</select>
	
	<select id="getLookUpInfoFromStg" resultType="LookUpDTO">
		SELECT l.type as lkpType,l.code as lkpValue,l.status as status,l.created_by as createdBy
		,l.created_on as createdOn,l.last_updated_by as lastUpdatedBy,l.last_updated_on as lastUpdatedOn,l.last_operation as lastOperation
		,l.lookup_id as lookupId,l.description as lkpDesc,l.request_state as requestState
	    FROM lookup_stg l where l.lookup_id=#{lookupId}
	</select>
	
	<select id="getEditLookUpInfoFromMain" resultType="LookUpDTO">
		SELECT l.type as lkpType,l.code as lkpValue,l.status as status,l.created_by as createdBy
		,l.created_on as createdOn,l.last_updated_by as lastUpdatedBy,l.last_updated_on as lastUpdatedOn,l.last_operation as lastOperation
		,l.lookup_id as lookupId,l.description as lkpDesc,l.request_state as requestState
	    FROM lookup_stg l where l.lookup_id=#{lookupId}
	</select>
	
	<update id = "updateLookUpStg">
	 UPDATE lookup_stg set code=#{lkpValue}, description=#{lkpDesc},status=#{status}
	 ,last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn}
	 ,last_operation=#{lastOperation},request_state=#{requestState} WHERE lookup_id=#{lookupId}
	</update>
	
	<select id="approveLookUpFromMain" resultType="LookUpDTO">
		SELECT l.type as lkpType,l.code as lkpValue,l.status as status,l.created_by as createdBy
		,l.created_on as createdOn,l.last_updated_by as lastUpdatedBy,l.last_updated_on as lastUpdatedOn
		,l.lookup_id as lookupId,l.description as lkpDesc
	    FROM lookup l where l.lookup_id=#{lookupId}
	</select>
	
    <update id = "updateLookUpReqState">
	 UPDATE lookup_stg set REQUEST_STATE=#{requestState}, CHECKER_COMMENTS=#{checkerComments}
	 ,LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON=#{lastUpdatedOn}, last_operation=#{lastOperation} where lookup_id=#{lookupId}
	</update>
	
	<update id = "updateLookUpApproval">
	 UPDATE lookup SET code=#{lkpValue}, description=#{lkpDesc},status=#{status}, last_updated_by=#{lastUpdatedBy}
	 ,last_updated_on=#{lastUpdatedOn} where lookup_id=#{lookupId}
	</update>
	
	<select id="getNextSequenceId" resultType="int">
		SELECT NEXTVAL('LOOKUP_SEQ')
	</select>
	
	<select id="getLookUpFrmStg" resultType="LookUpDTO">
		SELECT l.type as lkpType,l.code as lkpValue,l.status as status,l.created_by as createdBy
		,l.created_on as createdOn,l.last_updated_by as lastUpdatedBy,l.last_updated_on as lastUpdatedOn,l.last_operation as lastOperation
		,l.lookup_id as lookupId,l.description as lkpDesc,l.request_state as requestState
	    FROM lookup_stg l where l.lookup_id=#{lookupId}
	</select>
	
	<update id = "updateStgLookUp">
	 UPDATE lookup_stg set code=#{lkpValue}, description=#{lkpDesc},status=#{status}
	 ,last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn}
	 ,request_state=#{requestState}, last_operation=#{lastOperation} WHERE lookup_id=#{lookupId}
	</update>
	
	<delete id="deleteDiscardedEntry">
	DELETE FROM lookup_stg WHERE lookup_id = #{lookupId}
	</delete>
	
	<select id="fetchLookUpStgList" resultType="LookUpDTO">
		SELECT ls.type as lkpType,ls.code as lkpValue,ls.status as status,ls.created_by as createdBy
		,ls.created_on as createdOn,ls.last_updated_by as lastUpdatedBy,ls.last_updated_on as lastUpdatedOn,ls.last_operation as lastOperation
		,ls.lookup_id as lookupId,ls.description as lkpDesc,ls.request_state as requestState
		FROM lookup_stg ls where ls.lookup_id in <foreach item='item' index='index' collection='lookUpList' open='(' separator=',' close=')'>#{item}</foreach>
	</select>
	<select id="getLookUpTypeListFrmMain" resultType="LookUpDTO">
		SELECT ls.type as lkpType from lookup ls group by ls.type
	</select>
	
	<select id="checkDupLookUpDetailsAdd" resultType="int">
SELECT count(*)  FROM lookup_stg WHERE type=#{lkpType} and code=#{lkpValue}
</select>
	<select id="checkDupLookUpDetails" resultType="int">
SELECT count(*)  FROM lookup_stg WHERE type=#{lkpType} and code=#{lkpValue} and description=#{lkpDesc}
</select>
<select id="checkDupLookUpDetailsMain" resultType="int">
SELECT count(*)  FROM lookup WHERE type=#{lkpType} and code=#{lkpValue}
</select>


<select id="getAllLookUpData" resultType="org.npci.settlenxt.portal.common.dto.CodeValueDTO">
SELECT code as code , type as type, description as description from LOOKUP where status = #{status};
</select>

<select id="getLookUpData" resultType="org.npci.settlenxt.portal.common.dto.CodeValueDTO">
SELECT code as code , type as type , description as description from LOOKUP where type =#{type} and status =#{status} 
</select>

<select id="getFuncCodeData" resultType="org.npci.settlenxt.portal.common.dto.CodeValueDTO">
select func_code as code ,transaction_cycle as description,transaction_type as type from func_code_master
	</select>
	
</mapper>	
