var flow;
$(document).ready(
	function() {



		$('form')
			.each(function() {
				$(this).data('serialized', $(this).serialize())
			})
			.on('change input', function() {
				$(this)
					.find('input:submit, button:submit')
					.prop('disabled', $(this).serialize() == $(this).data('serialized'))
					;
				$('#submitButton').prop('disabled', false);


			})
		$('#submitButton').prop('disabled', true);
		$("form :input").change(function() {
			$(this).closest('form').data('changed', true)
		});

		if (($('#showbutton').val() != "Yes") && ($('#editLookUp').val() != "Yes")) {
			$('#lkpType').selectize({
				sortField: 'text'
			});
		}
		$('#lkpType')
			.change(
				function() {


					if ($('#lkpType').val() != 'SELECT' && $('#lkpType').val() == 'Others') {
						$('#lookUpTypeDiv').show();
					} else {
						$('#lookUpTypeDiv').hide();
					}
				});

		if ($('#success').val() == 'successStatus') {
			$("input").prop('disabled', true);
			$("select").prop('disabled', true);

		}

		if (($('#showbutton').val() == "Yes")) {
			$("input").prop('disabled', true);
			$("select").prop('disabled', true);

		}


		$('.lookUpAction').click(
			function() {
				var a = $(this).data("value");
				var b = a.split(",");
				var action = b[1];
				var tokenValue = $("[name=_TransactToken]").get(0).value;
				var data = "_TransactToken," + tokenValue;
				postData(action, data);
			});


		$('#clearLookUp').click(function() {

			$('#lkpType').data('selectize').setValue("SELECT");
			$('#lookUpTypeDiv').hide();
			$('#lkpValue').val("")
			$('#lkpDesc').val("")
			$('#status').val("A")
			$("#errlkpType").hide();
			$("#errlkpType1").hide();
			$("#errlkpValue").hide();
			$("#errlkpDesc").hide();
			$("#errstatus").hide();

		});


		$('#lkpType').on('keyup keypress blur change', function() {
			validateFromCommonVal('lkpType', true, "SelectionBox", "", false);
			checkDupLookUp();
		});

		$('#lkpType1').on('keyup keypress blur change', function() {
			validateFromCommonVal('lkpType1', false, "AlphaNumericWithSpace", 30, false);
			checkDupLookUp();

			if ($('#lkpType').val() != 'SELECT' && $('#lkpType').val() == 'Others') {
				validateFromCommonVal('lkpType1', true, "AlphaNumericWithSpace", 30, false);
				checkDupLookUp();
			}

		});


		$('#lkpValue').on('keyup keypress blur change', function() {
			validateFromCommonVal('lkpValue', true, "AlphaNumericWithSpace", 30, false);
			checkDupLookUp();
		});




		$('#lkpDesc').on('keyup keypress blur change', function() {
			validateFromCommonVal('lkpDesc', true, "AlphaNumericWithSpace", 30, false);
			checkDupLookUp();
		});



	});


function validateAddEditLookup(id) {
	$('.jqueryError').text("");
	$('.jqueryError').hide();
	var check = false;



	if (!validateFromCommonVal('lkpType',
		true, "SelectionBox", 30, false)) {
		check = true;

	}

	if (!validateFromCommonVal('lkpValue',
		true, "AlphaNumericWithSpace", 30, false)) {
		check = true;

	}


	if (!validateFromCommonVal('lkpDesc',
		true, "AlphaNumericWithSpace", 30, false)) {
		check = true;

	}


	if ($('#lkpType').val() != 'SELECT' && $('#lkpType').val() == 'Others') {
		if (!validateFromCommonVal('lkpType1',
			true, "AlphaNumericWithSpace", 30, false)) {
			check = true;

		}
	}



	var y = "";
	if ($('#addFlow').val() == "Yes") {
		y = checkDupLookUp();
		console.log(y);

		if (ajaxValidLookUp) {

			check = true;
		}

	}
	check  = forEditFlow( check);
	if (!check) {


		if ($('#addEditLookUp').data('changed')) {
			addEdit(id);
		} else {
			$('#jqueryError').text('No data modified');
			$('#jqueryError').show();
			return false;
		}

	} else {
		return false;
	}

}


var ajaxValidLookUp;

function forEditFlow(check) {
	let y;
	if ($('#editFlow').val() == "Yes") {
		var lkpTypeEdit = $('#lkpTypeEdit').val();
		var lkpValueEdit = $('#lkpValueEdit').val();
		var lkpDescEdit = $('#lkpDescEdit').val();
		var lkpType = $('#lkpType').val();
		var lkpValue = $('#lkpValue').val();
		var lkpDesc = $('#lkpDesc').val();
		if (lkpTypeEdit != lkpType && lkpValueEdit != lkpValue && lkpDescEdit != lkpDesc) {
			y = checkDupLookUp();
			console.log(y);
			if (ajaxValidLookUp) {

				check = true;
			}
		}

	}
	return check ;
}

function callBack(flag) {
	ajaxValidLookUp = flag;
}


function checkDupLookUp() {

	var lktpType = "";
	var lktpVal = $('#lkpValue').val();
	var lktpDesc = $('#lkpDesc').val();
	if ($('#lkpType').val() != 'SELECT' && $('#lkpType').val() != 'Others') {
		lktpType = $('#lkpType').val();
	} else if ($('#lkpType').val() != 'SELECT' && $('#lkpType').val() == 'Others') {
		lktpType = $('#lkpType1').val();
	}


	if ($('#addFlow').val() == "Yes") {
		flow = "add";
	}


	if ($('#editFlow').val() == "Yes") {
		var lkpTypeEdit = $('#lkpTypeEdit').val();
		var lkpValueEdit = $('#lkpValueEdit').val();
		var lkpDescEdit = $('#lkpDescEdit').val();
		var lkpType = $('#lkpType').val();
		var lkpValue = $('#lkpValue').val();
		var lkpDesc = $('#lkpDesc').val();
		if (lkpTypeEdit != lkpType && lkpValueEdit != lkpValue && lkpDescEdit != lkpDesc) {
			flow = "edit";
		}
	}
	if (lktpType != "" && lktpDesc != "" && lktpVal != "" && lktpType != 'SELECT' && lktpType != 'Others') {
		var validLookup = false;

		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		if (flow != "") {

			$.ajax({
				url: "checkDupLookUpDetails",
				type: "POST",
				dataType: "json",
				data: {

					"lkpType": lktpType,
					"lkpVal": lktpVal,
					"lkpDesc": lktpDesc,
					"flow": flow,
					"_TransactToken": tokenValue
				},
				success: function(response) {

					validLookup = onSuccess(response, validLookup);
				},
				error: function(_request, _status, _error) {
					$('#errorStatus2').html('');
					callBack(false);
				}
			});

		} else {
			validLookup = false;
		}

		return validLookup;
	}
}

function onSuccess(response, validLookup) {
	if (response.status == "BSUC_0001") {
		validLookup = true;

		$('#errorStatus2').show();

		$('#errorStatus2').html('LookUp Details Already Exists');

		callBack(true);




	}
	else {
		validLookup = false;
		$('#errorStatus2').hide();
		$('#errorStatus2').html('');
		callBack(false);

	}
	return validLookup;
}

function addEdit(id) {
	var data = "";
	var url = "";
	if (id == 'A') {
		var lookType = $('#lkpType').val();
		if ($('#lkpType').val() != 'SELECT' && $('#lkpType').val() == 'Others') {
			lookType = $('#lkpType1').val()
		}
		url = '/submitLookUpDetails';
		data = "lkpType," + lookType + ",lkpValue," + $('#lkpValue').val()
			+ ",lkpDesc," + $('#lkpDesc').val() + ",status," + $('#status').val();


		postData(url, data);

	} else if (id == 'E') {

		url = '/updateLookUpDetails';
		data = "lkpType," + $('#lkpType').val() + ",lookupId,"
			+ $('#lookupId').val() + ",lkpValue," + $('#lkpValue').val()
			+ ",lkpDesc," + $('#lkpDesc').val() + ",status," + $('#status').val();

		postData(url, data);
	}

}



function submitForm(action) {

	var url = action;
	var lookupId = $("#lookupId").val();
	var data = "lookupId," + lookupId;
	postData(url, data);

}

function submitFormMain(action) {
	var url = action;

	urlPostAction('', url);

}

function userAction(action) {

	var lookupId = $("#lookupId").val();
	var data = "lookupId," + lookupId;
	postData(action, data);

}
