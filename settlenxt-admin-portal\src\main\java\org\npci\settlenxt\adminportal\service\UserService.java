package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.UserDTO;
import org.npci.settlenxt.portal.common.dto.UserInfoDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseUserService;

public interface UserService extends BaseUserService {

	 UserDTO discardRejectedUserEntry(String userId) throws SettleNxtException;

	 String resetPwd(UserInfoDTO userInfoDto) throws SettleNxtException;

	 List<UserDTO> getUserList(String[] bankList, String[] roleList);

	 String getUserHierarchyAccessLevel();

	 List<CodeValueDTO> getUserHierarchyAccessLevelList(List<String> userList);

	 UserDTO updateApproveOrRejectBulkUser(List<Integer> list, String status, String remarks) throws SettleNxtException;

	boolean checkIfDashBoardFuncExists(String string, int userId);
}
