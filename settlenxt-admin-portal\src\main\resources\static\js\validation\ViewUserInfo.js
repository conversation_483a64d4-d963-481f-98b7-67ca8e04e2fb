$(document).ready(function() {
	$("#errMsg").hide();
	var funcList = "";
	function assignedFuncList(roleId) {
		funcList = funcList + "|" + roleId;
	}

	function getFuncList() {
		while (funcList.charAt(0) === '|')
			funcList = funcList.slice(1);
		return funcList;
	}

	$('#addSingle').click(function() {
		return !$('#source option:selected').remove().appendTo('#destination');
	});

	$('#addAll').click(function() {
		return !$('#source option').remove().appendTo('#destination');
	});

	$('#removeSingle').click(function() {
		return !$('#destination option:selected').remove().appendTo('#source');
	});

	$('#removeAll').click(function() {
		return !$('#destination option').remove().appendTo('#source');
	});

	$("#SUBMIT").click(function() {
		$('select#destination').find('option').each(function() {
			assignedFuncList($(this).val() + "|" + $(this).text());
		});

		var list = getFuncList();
		if (list == "") {
			return false;
		} else {
			$("#assFuncList").val(getFuncList());
			funcList = "";
		}
	});
	
});

function userAction(_type, action) {

	var url = action;
	var uid = document.getElementById("userId").value;
	var data = "uid," + uid  + ",status,"
			+ status+ ",userType," + $('#userType').val();
	postData(url, data);
}

function userActivateDeactivate(_type, action) {

	var url = action;
	var uid = document.getElementById("userId").value;
	var data = "uid," + uid  +",userType," + $('#userType').val();
	postData(url, data);
	}
	
	
function setRefnumUserId(reqId){
	document.getElementById("refNum1").value = reqId;
}

function postUserAction(action) {
	var url = action;
	var uid = $("#userId").val();
	var data =  "uid," + uid  +",userType," + $('#userType').val();
	postData(url, data);
}



