<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<head>
<title><spring:message code="am.lbl.title" /></title>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ page import="java.time.format.DateTimeFormatter"%>
<script type="text/javascript"
	src="./static/js/validation/settlenxtReportDownload.js"></script>


<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" /> 
<script type="text/javascript">
var actionColumnIndex = 6;


var firstColumnToBeSkippedInFilterAndSort=true;

</script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>

<script>
var reportId=[];

<c:if test="${not empty settlementCycleList}">
<c:forEach items="${settlementCycleList}" var="operator">
reportId.push(${operator.logId});
</c:forEach>
</c:if>

</script>

<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />


</head>
<div id="errorStatus2" class="alert alert-danger" role="alert"
	style="display: none"></div>
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>
<style>
.form-control {
	background-color: white !important;
}
</style>
<body onload="noBack();">

<div id="errorStatus2" class="alert alert-danger" role="alert"
	style="display: none"></div>
	<div class="container-fluid height-min">
		<div class="row">

			<div class="body-content">

				<div class="card">
					<div class="card-header">
						<div class="card-title">
							File Search
							</div>
					</div>
					<div class="card-body">
						<form:form onsubmit="encodeForm(this);"
							enctype="multipart/form-data" modelAttribute="auditBatchLogDTO"
							method="POST" autocomplete="off">

							<div class="row">
								<div class="col-md-3">
									<div class="form-group">
										<label for=""><spring:message
												code="am.lbl.file.fromDate" /> <span class="red">*</span></label>

										<form:input path="fromDateStr" id="fromDateStr"
											name="fromDateStr" cssClass="form-control" readonly="true"
											
											 />

										<div id="errFromDate">
											<span for=fromDateStr class="error"><form:errors
													path="fromDateStr" /></span>
										</div>
									</div>
								</div>
								<div class="col-md-3">
									<div class="form-group">
										<label for=""><spring:message
												code="am.lbl.file.toDate" /> <span class="red">*</span></label>
										<form:input path="toDateStr" id="toDateStr" name="toDateStr"
											 cssClass="form-control"
											readonly="true"  />
										<div id="errtoDate" >
											<span for=toDateStr class="error"><form:errors
													path="toDateStr" /></span>
										</div>
									</div>
								</div>

<div class="col-md-3">
									<div class="form-group">
										<label for=""><spring:message
												code="am.lbl.file.fileType" /> </label>
										<form:select path="fileType" id="fileType"
											class="form-control">
											<form:option label="SELECT" value="0" />
											<form:options items="${fileTypeList}" />
										</form:select>
									</div>
								</div>

							 
								<div class="col-md-3">
									<div class="form-group">
										<label for=""><spring:message
												code="am.lbl.file.cycleNum" /> </label>
										<form:select path="cycleNum" id="cycleNum"
											class="form-control">
											<form:option label="SELECT" value="0" />
											<form:options items="${settlementCycleNumberList}" />
										</form:select>
									</div>
								</div>
								
							</div>
											<div class="row">
											
											<div class="col-md-3">
												<label>Bank Participant</label>
												<form:select path="memberName" id="memberName" placeholder="Pick a Participant Name..." name="memberName"
													
													class="form-control">
																				
													
												<form:option value="0">All
												</form:option>
													<form:options itemLabel="label" itemValue="value"
														items="${memberList}" />
												</form:select>
												<div id="errmemberName" class="error fail"
													style="color: #a94442"></div>
											</div>
											
											</div>

						</form:form>
					</div>

					<div class="card-action">
						<button type="button" class="btn btn-primary" id="searchBtn">
							<spring:message code="am.lbl.file.fileSearch" />
						</button>
						<button type="button" value="Submit" class="btn btn-success"
													onclick="resetAction();">
													<spring:message code="am.lbl.reset" />
												</button>

					</div>

				</div>
			</div>
		</div>


		<div class="col-sm-12">
			<div class="card-white">
			
				<div class="row">
				<div class="col-sm-12">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="ifsc.clearFiltersBtn" />
					</button>
				</div>
			</div>

				<div class="panel panel-default">
					<div class="table-responsive">
						<table id="tabnew"
							class="table table-striped dataTable table-bordered order"
							style="width:100%;">
							<caption style="display:none;">settlenxtreport</caption>
							<thead>
								<tr>
									<th scope="col"><input type=checkbox name='selectAllCheck'
										id="selectAll" value='Hi'></input></th>
									<th id=""><spring:message code="am.lbl.file.fileName" /></th>
									<th id=""><spring:message code="am.lbl.file.fileType" /></th>
									<th id=""><spring:message code="am.lbl.file.cycleNum" /></th>
									<th id=""><spring:message code="am.lbl.participantId"/></th>
									<th id=""><spring:message code="am.lbl.file.fileGenDate" /></th>
									<th id=""><spring:message code="am.lbl.file.action" /></th>

								</tr>
							</thead>
						
							<tbody id="afterReset">

								<c:forEach var="report" items="${settlementCycleList}">

									<tr>
									<%-- <td style="display: none;">${report.logId}</td> --%>
									<sec:authorize
										access="hasAuthority('Delete Document Download')">
										<td onclick=event.stopPropagation() style="width:5%;"><input
											type=checkbox name='type' value='${report.logId}' width="5%"></input></td>

									</sec:authorize>
									
										<td style="width:15%;">${report.reportName}</td>
										<c:choose>
											<c:when test="${empty report.reportType}">
												<td style="width:15%;">${report.fileType}</td>
											</c:when>
											<c:otherwise>
												<td style="width:15%;">${report.fileType}</td>
											</c:otherwise>
										</c:choose>
										<td style="width:15%;">${report.cycleNum}</td>
										<td style="width:15%;"><c:if test="${(report.participantId ne '0')}">${report.participantId}</c:if><c:if test="${(report.participantId eq '0')}">All</c:if></td>
										<td style="width:15%;">${report.createdOn.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>
										<td style="width:10%;"><a
											href="javascript:getRecordDetails('${report.filePath}')"
											><spring:message
													code="am.lbl.file.download" /></a></td>


									</tr>
								</c:forEach>
							</tbody>

						</table>



					</div>
				</div>
			</div>
		</div>
		
<div id="afterReset2">
<c:if test="${not empty settlementCycleList}">
		<input type="button" class="btn btn-success pull-right btn_align"
			onclick="allcheckedFileDownload();" id="submitAllFile"
			value="Download All Checked Files" />
	</c:if>
	
	</div>
	<div id="afterReset3">
<c:if test="${not empty settlementCycleList}">
		<input type="button" class="btn btn-success pull-right btn_align"
			onclick="allFileDownload();" id="submitAllFile1"
			value="Download All Files" />
	</c:if>
	
	</div>

	</div>
	</div>
	</div>







</body>
</html>
