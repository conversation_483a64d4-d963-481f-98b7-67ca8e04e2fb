<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/showJsonValidator.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	
	<c:url value="getJsonValidator" var="getJsonValidator" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveJsonValidator" modelAttribute="jsonValidatorDto"
		action="${getJsonValidator}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"></span><spring:message code="jsonValidator.mainTab.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="crtuser"
							value="${jsonValidatorDto.createdBy}" />

						<table class="table table-striped infobold"
							style="font-size: 12px">
								<caption style="display:none;">View Json Validator</caption> 
							<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"></span><spring:message code="jsonValidator.viewscreen.title" /></span></strong>
										</div></td>
										<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									
								</tr>
								<tr>

								</tr>
								
								<tr>
									<td><label><spring:message code="jsonValidator.api" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.api}</td>
									<td><label><spring:message code="jsonValidator.code" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.code}</td>
									<td><label><spring:message code="jsonValidator.fieldName" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.fieldName}</td>
									<td><label><spring:message code="jsonValidator.regexps" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.regexps}</td>
									</tr>
									<tr>
									<td><label><spring:message code="jsonValidator.reasonCode" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.reasonCode}</td>
									<td><label><spring:message code="jsonValidator.reasonCodeDesc" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.reasonCodeDesc}</td>
									<td><label><spring:message code="jsonValidator.pcode" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.pcode}</td>
									<td><label><spring:message code="jsonValidator.priority" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.priority}</td>
									</tr>
									<tr>
									<td><label><spring:message code="jsonValidator.isMandatory" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.isMandatory}</td>
									<td><label><spring:message code="ifsc.checkerComments" /></label></td>
									<td ><c:if test="${empty jsonValidatorDto.checkerComments}">N/A</c:if>${jsonValidatorDto.checkerComments }</td>
									<td><label><spring:message code="binexcl.status" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty jsonValidatorDto.status}">N/A</c:if>${jsonValidatorDto.status}</td>
								</tr>
				
							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									
								

									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/showJsonValidator');"><spring:message code="budget.backBtn" /></button>
									<sec:authorize access="hasAuthority('Edit Json Validator')">
								
								<button type="button" class="btn btn-success"
										onclick="viewMcc('${jsonValidatorDto.seqId}','V')">
										<spring:message code="sm.lbl.edit" /></button>
								
								</sec:authorize>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

