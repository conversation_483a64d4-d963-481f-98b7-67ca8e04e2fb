$(document).ready(function () {
	 userIds=[];
    
    $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                	//If first column to be skipped to include the filter for the reasons line check box 
                    if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    searchBoxFunc(colIdx, cell, title, api);
                }
                });
            $('#tabnew_filter').hide();
            
        },
     // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
        order: [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Bin Exclusion Configuration',
                header: 'false',
                title: null,
                sheetName: 'Bin Exclusion Configuration',
                className: 'defaultexport'
                /*,
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }*/
            }
 ,
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Bin Exclusion Configuration' ,
						header:'false', 
						title: null,
						sheetName:'Bin Exclusion Configuration',
						className:'defaultexport'
						/*,
						exportOptions: {
				            columns: 'th:not(:last-child)'
				         }*/
                    }
        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true
    });

    $("#excelExport").on("click", function () {
        $(".buttons-excel").trigger("click");
    });
 
 	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });
     
     
     
     $("#selectAll").click(function(){
	 		
		 $('#jqueryError4').hide();
		 $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
		 
		 var referenceNoList = document.getElementById("newsIds");
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
        
		 if(userIds.length>0){
	referenceNoList.innerHTML = userIds.length+"     "+"records are selected";
	
     		if( $('#selectAll').is(':checked') )
		   {
			  
			  $("#toggleModalNews").modal('show');	
		  }
		 else
	     {
			 $("#toggleModalNews").modal('hide');
			 
			 }}else{
                var i=0;
                var userId2=[];
                 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
                                            userId2.push(this.value);
                                            i++;
                                        });
                showAndHideModel(userId2, referenceNoList);}
            });
 
  // Disabling SelectAll option diabling
   	if(referenceNoListPendings.length==0)
   	{
  		if (typeof selectAll != "undefined") {
  			document.getElementById("selectAll").disabled = true;
  			document.getElementById("submitButtonA").disabled = true;
 			document.getElementById("submitButtonR").disabled = true;
  		}
   	}
  	
 
});

	
function showAndHideModel(userId2, referenceNoList) {
    if (userId2.length > 0) {


        if (referenceNoListPendings.length > 0) {
            referenceNoList.innerHTML = referenceNoListPendings.length + "     " + "records are selected";

            if ($('#selectAll').is(':checked')) {
                $("#toggleModalNews").modal('show');

            }
            else {
                $("#toggleModalNews").modal('hide');

            }
        }
    }
}

function searchBoxFunc(colIdx, cell, title, api) {
	var cursorPosition = null;
    if (colIdx < actionColumnIndex) {
        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function(_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
                userIds = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
                        userIds.push(this.value);
                        i++;
                    });
                }
                else {
                    userIds = [];
                }
            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
    
}

function viewBinExclConfig(excId, type) {
var url;
	if (type == 'V')
		url = '/editBinExclConfig';
	else if (type == 'P')
		url = '/getBinExclConfig';
	else if (type == 'G')
		url = '/getPendingBinExclConfig';
	
	var data = "excId," + excId + ",viewType," + type ;
	postData(url, data);
}



function submitForm(url) {
	var data = "userType," + $('#userType').val();
	postData(url, data);
}




function Edit(action, excId,parentPage) {
	var viewType='V';
	var data = "excId," + excId  + ",viewType," + viewType  +",parentPage," + parentPage;
	
	postData(action, data);
}


function mySelect(){
$('#jqueryError4').hide();
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	 
	  var referenceNoList = document.getElementById("newsIds");
  
	 if(array.length==referenceNoListPendings.length){
		 $('#selectAll').prop('checked', true);
		
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
			 $("#toggleModalNews").modal('show');
	 }
	 else{
		 $("#toggleModalNews").modal('hide');
		 
	 }
	
}

function ApproveorRejectBulkBinExcl(type,action){
	
	 var url = '/approveBinExclBulk';
	let data ="";
	 var array = [];
	 	var referenceIdIdList = "";
		
		var i=0;

	 if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(action=='All'){
		 array=referenceNoListPendings; 
	 }
	   if(userIds.length>0){
			  
		  $('#jqueryError4').hide();
	
 		for(i of userIds){
 		referenceIdIdList = referenceIdIdList + i + "|";
 		}
 		
	if(type=='A'){
			
		 data =  "status,"+"A"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	else if(type=='R'){
		
		 data = "status,"+"R"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	
	postData(url, data);
	}
	   else if(array.length>0){
			  $('#jqueryError4').hide();
		
			for(i of array){  
		        referenceIdIdList = referenceIdIdList + i + "|" ;  
		     } 
		
			if(type=='A'){
			 data =  "status,"+"A"+",bulkApprovalReferenceNoList,"+referenceIdIdList;
			}
			else if(type=='R'){
			 data = "status,"+"R"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
			}
		postData(url, data);
		}
	else{
			  
			  $('#errorStatus4').html('Please Select  Atleast One Record');
				$('#jqueryError4').show();
		  }
}


function deselectAll() {
$('#jqueryError4').hide();
	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  
	var i=0;
 		for(i of ele){
 		if(i.type=='checkbox')
 			i.checked=false;
 		} 

}
