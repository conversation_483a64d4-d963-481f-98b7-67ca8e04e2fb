<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/addEditMccTipSurcharge.js" type="text/javascript"></script>
<script type="text/javascript">
var mccTipSurchargeValidationMessages={};
mccTipSurchargeValidationMessages['mccId']="<spring:message code='mccTipSurcharge.mccId.validation.msg' javaScriptEscape='true' />";
mccTipSurchargeValidationMessages['tipSurchargeId']="<spring:message code='mccTipSurcharge.tipSurchargeId.validation.msg' javaScriptEscape='true' />";


</script>

<div class="row">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			 
				<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"> <c:if test="${not empty addMccTipSurcharge}">
						<spring:message code="mccTipSurcharge.addscreen.title" />
					</c:if> <c:if test="${not empty editMccTipSurcharge}">
						<spring:message code="mccTipSurcharge.editscreen.title" />
					</c:if>
			</span></strong>
			
		</div>
		
		<div class="panel-body">
			<form:form onsubmit="return encodeForm(this);" method="POST"
				id="addEditMccTipSurcharge" modelAttribute="mccTipSurchargeDto"
				action="${submitMccTipSurchargeDetails}" autocomplete="off">
				<br />
				<form:hidden path="mccTipSurchargeId" id="mccTipSurchargeId"
					name="mccTipSurchargeId" value="${mccTipSurchargeDto.mccTipSurchargeId}" />
				<input id="hparentPage" type="hidden"
				value="${parentPage}" />	
		<c:if test="${not empty showbutton}">			
		<div class="row">
			<div class="col-sm-12">
				<div class="col-sm-3">
					<div class="form-group">
						<label><spring:message code="mccTipSurcharge.tipSurchargeId" /><span style="color: red">*</span></label>
							<form:select path="tipSurchargeId" id="tipSurchargeId" name="tipSurchargeId"
								maxlength="10" value="${mccTipSurchargeDto.tipSurchargeId}"
								cssClass="form-control medantory" >
								<form:option value="" label="SELECT" />
								<form:options items="${tipSurchargeNameList}"  itemValue="code" itemLabel="description"/>
								</form:select>
								<div id="errtipSurchargeId">
							<span for="tipSurchargeId" class="error"><form:errors
							path="tipSurchargeId" /> </span>
						</div>
					</div>
				</div>	
				
				<div class="col-sm-3">
					<div class="form-group">
						<label><spring:message code="mccTipSurcharge.mccId" /><span style="color: red">*</span></label>
							<form:select path="mccId" id="mccId" name="mccId"
								maxlength="10" value="${mccTipSurchargeDto.mccId}"
								cssClass="form-control medantory" >
								<form:option value="" label="SELECT" />
								<form:options items="${mccList}"  itemValue="code" itemLabel="description"/>
								</form:select>
								<div id="errmccId">
							<span for="mccId" class="error"><form:errors
							path="mccId" /> </span>
						</div>
					</div>
				</div>
			
			
			</div>
		</div>	
		</c:if>
		<c:if test="${empty showbutton}">	
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">Mcc Tip Surcharge</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
									<td><label><spring:message code="mccTipSurcharge.tipSurchargeId" /></label></td>
									<td >${mccTipSurchargeDto.tipSurchargeLookup }</td>
									<td><label><spring:message code="mccTipSurcharge.mccId" /></label></td>
									<td >${mccTipSurchargeDto.mccNameLookup }</td>
									</tr>
									
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			</c:if>
				
				<div class="col-sm-12 bottom_space">
					
					<div style="text-align:center">
						<c:if test="${not empty addMccTipSurcharge}">
						<c:if test="${not empty showbutton}">
									<button type="button" value="Submit" class="btn btn-success" onclick="resetAction();">
										    <spring:message code="mccTipSurcharge.resetBtn" /> </button>
							</c:if>			    
						<c:if test="${not empty showbutton}">
							<input type="button" class="btn btn-success"
									onclick="viewTipSurchargeAdd('/addMccTipSurcharge','A')" id="submitButton"
									value="<spring:message code="mccTipSurcharge.submitBtn" />" />
						</c:if>
						</c:if>
						<sec:authorize access="hasAuthority('Edit MCC Tip Surcharge')">
							<c:if test="${not empty editMccTipSurcharge}">
							 <c:if test="${not empty showbutton}"> 
								<input type="button" class="btn btn-success" id="bEdit"
									onclick="viewTipSurchargeAdd('/updateMccTipSurcharge','E')" id="submitButton"
									value="<spring:message code="mccTipSurcharge.submitBtn" />" />
							</c:if>
							</c:if> 
							
							<c:if test="${mccTipSurchargeDto.requestState  eq 'R' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="discard('/discardMccTipSurcharge','${mccTipSurchargeDto.mccTipSurchargeId}');" />
							<button type="button" class="btn btn-danger"
										onclick="userAction('P','/mccTipSurchargeForApproval');">
										<spring:message code="mccTipSurcharge.backBtn" /></button>
							
							</c:if>
							
							
						</sec:authorize>
						
						<c:if test="${mccTipSurchargeDto.requestState  ne 'R'}">
						
						<c:if test="${parentPage  eq 'pendingApprove'}">
						<button type="button" class="btn btn-danger"
							onclick="userAction('N','/mccTipSurchargeForApproval');">
							<spring:message code="mccTipSurcharge.backBtn" />
						</button>
						</c:if>
						 <c:if test="${parentPage ne 'pendingApprove'}">
						<button type="button" class="btn btn-danger"
							onclick="userAction('N','/mccTipSurchargeMain');">
							<spring:message code="mccTipSurcharge.backBtn" />
						</button>
						</c:if>
						</c:if>
					</div>
				</div>
			</form:form>
		</div>
	</div>
</div>
