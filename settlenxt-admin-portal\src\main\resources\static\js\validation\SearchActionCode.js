 $(document).ready(function () {
 
	actionIds=[];
 
	/* Initialization of datatables */
	$(document).ready(function () {
    	
    $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                //If first column to be skipped to include the filter for the reasons line check box 
               dataTableFunc(colIdx,api)
                });
            $('#tabnew_filter').hide();
           
        },
        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Action Code',
                header: 'false',
                title: null,
                sheetName: 'Action Code',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'Action Code' ,
				header:'false', 
				title: null,
				sheetName:'Action Code',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });
});	
  
  function dataTableFunc(colIdx,api){
   if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   handleInput(colIdx, cell, title, api);
                   }
  } 
 $('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	
	
	 $("#excelExport").on("click", function () {
	        $(".buttons-excel").trigger("click");
	    });
	    
	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
	 
	     $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
 
 
 
 
$("#selectAll").click(function(){



$('#errorStatus4').hide();
	        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
	      
	        var actionCodedata = document.getElementById("actionCodeee");
	actionCodedata.innerHTML = actionCodeListPendings.length+"     "+"records are selected";

	if(actionIds.length>0){
		actionCodedata.innerHTML = actionIds.length+"     "+"records are selected";
		
		if( $('#selectAll').is(':checked') ){
	 $("#toggleModal").modal('show');
	        
	}
	else{
	   $("#toggleModal").modal('hide');
	        
	}}else{
		var i=0;
		var ActionId2=[];
		 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
			 ActionId2.push(this.value);
				                    i++;
				                });
		 
		 if(ActionId2.length>0){


		if(actionCodeListPendings.length>0){
		
			
			actionCodedata.innerHTML = actionCodeListPendings.length+"     "+"records are selected";
			
			if( $('#selectAll').is(':checked') ){
		 $("#toggleModal").modal('show');
		        
		}
		else{
		   $("#toggleModal").modal('hide');
		        
		}
		 }}}

});
 
 
 
 
 
 });


function handleInput(colIdx, cell, title, api) {
	var cursorPosition =null;
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function () {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();

                actionIds = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
                        actionIds.push(this.value);
                        i++;
                    });
                }
                else {
                    actionIds = [];
                }

            })
            .on('click', function (e) {
                e.stopPropagation();
            })
            .on('keyup', function (e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    } else {
        $(cell).html(title + '<br> &nbsp;');
    }
    
}

function ApproveOrRejectBulk(type,action){

	var url = '/approveOrRejectBulkActionCode';

	
	 var array = [];
		if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	   }
	   else if(action=='All'){
	 if(actionIds.length>0){
	   array= actionIds;
	   		}else{
	   		array= actionCodeListPendings;}
	   	
	   		
	   }
	 
	var bulkActionCodeIdList = "";

		let i=0;
		for(i of array)
		{
		bulkActionCodeIdList = bulkActionCodeIdList + i + "|";
		}
		
		
		var data="";
		if(array.length!=0){
		if(type=='A'){
		
		data =  "status,"+"A"+",bulkActionCodeIdList,"+bulkActionCodeIdList+",remarks,"+"Approved";
		}
	
	else if(type=='R'){
			
		data =  "status,"+"R"+",bulkActionCodeIdList,"+bulkActionCodeIdList+",remarks,"+"Rejected";
		}
		
	postData(url, data);
		$('#errorStatus2').hide();
	$('#errorStatus2').html("");
	
	}else{
	
	$('#errorStatus2').html("Please select one or more records to bulk approve/reject records");
	$('#errorStatus2').show();
	}
	
}


function submitForm(url) {
	var data = "";
	postData(url, data);
}

function viewActionCodeInfo(actionCodeId,action) {
    var data = "actionCodeId," + actionCodeId ;
    postData(action, data);
}

function viewRejActionCodeInfo(actionCodeId,action) {
   var data = "actionCodeId," + actionCodeId ;
    postData(action, data);
}

function deselectAll() {


	var ele="";
   $('#selectAll').prop('checked', false);
      ele=document.getElementsByName('type');  
      let i=0;
   	for(i of ele)
   	{
   	if(i.type=='checkbox')
   	{
   	i.checked=false;
   	}
   	}
   
   $('#selectAll1').prop('checked', false);
   ele=document.getElementsByName('type');  
   
   for(i of ele)
   {
   if(i.type=='checkbox')
   {
   i.checked=false;
   }
   }
   
}




