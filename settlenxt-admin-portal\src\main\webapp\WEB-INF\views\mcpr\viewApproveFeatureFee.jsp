<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/viewApproveFeatureFee.js" type="text/javascript"></script>

<div class="container-fluid height-min">
	<div class="alert alert-danger appRejMust" role="alert">
		<span data-i18n="Data"><spring:message code="am.lbl.appRejAction" /></span>
	</div>
	<div class="alert alert-danger remarkMust" role="alert">
		<span data-i18n="Data"><spring:message code="sm.lbl.remarkMust" /></span>
	</div>

	<c:url value="approveFeatureFee" var="approveFeatureFee" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveFeatureFee" modelAttribute="featureFeeDto"
		action="${approveFeatureFee}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="featureFee.viewscreen.title" /></span></strong>
					</div>
					<div class="panel-body">
						<input type="hidden" id="cardId" value="${featureFeeDto.cardConfigId}" />
						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">Feature Fee</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6">
									<div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> 
											<span data-i18n="Data"><spring:message code="featureFee.requestInformation" /></span></strong>
									</div></td>
								</tr>
								<tr>
									<td><label><spring:message code="featureFee.requestType" /></label></td>
									<td>${featureFeeDto.lastOperation}</td>
									<td><label><spring:message code="featureFee.requestDate" /></label></td>
									<td>${featureFeeDto.lastUpdatedOn}</td>
									<td><label><spring:message code="featureFee.requestStatus" /></label></td>
									<td><c:if test="${featureFeeDto.requestState =='A' }"><spring:message	code="featureFee.requestState.approved.description" /></c:if>
										<c:if test="${featureFeeDto.requestState =='P' }"><spring:message code="featureFee.requestState.pendingApproval.description" /></c:if>
										<c:if test="${featureFeeDto.requestState =='R' }"><spring:message code="featureFee.requestState.rejected.description" /></c:if>
										<c:if test="${featureFeeDto.requestState =='D' }"><spring:message code="featureFee.requestState.discared.description" /></c:if>
									</td>
								</tr>
								<tr>
									<td><label><spring:message code="featureFee.requestBy" /></label></td>
									<td>${featureFeeDto.lastUpdatedBy}</td>
									<td><label><spring:message code="featureFee.approverComments" /><span
										style="color: red"></span></label></td>
									<td colspan=2>${featureFeeDto.checkerComments}</td>
									<td></td>
									
								</tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-credit-card"></span> <span
										data-i18n="Data"><spring:message code="featureFee.viewscreen.title" /></span></strong></div>
									</td>
								<tr>
									<td><label><spring:message code="featureFee.cardConfigId" /></label></td>
									<td>${featureFeeDto.cardConfigId }</td>
									<td><label><spring:message code="featureFee.cardType" /></label></td>
									<td>${featureFeeDto.cardTypeName }</td>
									<td><label><spring:message code="featureFee.cardVariant" /></label></td>
									<td>${featureFeeDto.cardVariantName }</td>
								</tr>
								<tr>
									<td><label><spring:message code="featureFee.featureFee" /></label></td>
									<td>${featureFeeDto.featureFee }</td>
									<td><label><spring:message code="featureFee.details" /></label></td>
									<td>${featureFeeDto.details }</td>
									<td><label><spring:message code="featureFee.feature" /></label></td>
									<td>${featureFeeDto.feature }</td>
								</tr>
								<tr>
									<td><label><spring:message code="featureFee.fromDate" /></label></td>
									<td>${featureFeeDto.fromDate }</td>
									<td><label><spring:message code="featureFee.toDate" /></label></td>
									<td>${featureFeeDto.toDate }</td>
									<td></td>
									<td></td>
								</tr>
								<sec:authorize access="hasAuthority('Approve Feature Fee')">
									<c:if test="${featureFeeDto.requestState eq 'P'}">
									<tr>
										<td colspan="6"><div class="panel-heading-red  clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span>
											<span data-i18n="Data">
											<spring:message code="featureFee.approvalPanel.title" /></span></strong></div>
										</td>
									</tr>
									<tr>
										<td><label><spring:message
											code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td>
												<select name="select" id="apprej"
													onchange="display()">
													<option value="N"><spring:message code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message code="AM.lbl.reject" /></option>
												</select>
											</td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
													style="color: red">*</span></label>
												</div>
											</td>
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div>
											</td>
											<td></td>
										</tr>
									</c:if>
								</sec:authorize>
							</tbody>
						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Feature Fee')">
										<c:if test="${featureFeeDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approvecard" value="Submit"
												onclick="postAction('/approveFeatureFee');" />
										</c:if>
									</sec:authorize>
													
									<sec:authorize access="hasAuthority('Edit Feature Fee')">				
									<c:if test="${featureFeeDto.requestState  eq 'R' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="userAction('/discardFeatureFee','${featureFeeDto.cardConfigId}');" />
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="edit('/editFeatureFee','${featureFeeDto.cardConfigId}','approvalTab');"/>
									</c:if>
									</sec:authorize>
									
										<button type="button" class="btn btn-danger"
										onclick="backAction('P','/featureFeePendingForApproval');">
										<spring:message code="featureFee.backBtn" /></button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

