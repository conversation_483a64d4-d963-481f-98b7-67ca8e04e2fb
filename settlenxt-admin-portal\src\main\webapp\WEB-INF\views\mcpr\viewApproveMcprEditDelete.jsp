<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/viewApproveMcprEditDelete.js" type="text/javascript"></script>

<div class="container-fluid height-min">
	<div class="alert alert-danger appRejMust" role="alert"><spring:message code="mcprBinDetails.apprejecterrormsg" /></div>
	<div class="alert alert-danger remarkMust" role="alert"><spring:message code="mcprBinDetails.remarkserror" /></div>

	<c:url value="approveMcprBinDetails" var="approveMcprBinDetails" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveMcprBinDetails" modelAttribute="mcprViewEditHistoricDto"
		action="${approveMcprBinDetails}" autocomplete="off">

	<div class="row">
	<div class="col-sm-12">
		<div class="panel panel-default no_margin">
			<div class="panel-heading clearfix">
				<strong><span class="glyphicon glyphicon-th"></span> 
				<span data-i18n="Data"><spring:message code="mcprBinDetails.viewscreen.title" /></span></strong>
			</div>
			<div class="panel-body">
				<input type="hidden" id="mcprId" value="${mcprViewEditHistoricDto.mcprBinDataDetailsId}" />
				<input type="hidden" id="lastOperation" value="${mcprViewEditHistoricDto.lastOperation}" />
				<table class="table table-striped infobold" style="font-size: 12px">
				<caption style="display:none;">MCPR Bin Data</caption>
						<thead style="display:none;"><th scope = "col"></th></thead>
					<tbody>
						<tr>
							<td colspan="6">
							<div class="panel-heading-red clearfix">
								<strong><span class="glyphicon glyphicon-info-sign"></span> 
								 <span data-i18n="Data"><spring:message code="mcprBinDetails.requestInformation" /></span></strong>
							</div>
							</td>
						</tr>
						<tr>
							<td><label><spring:message code="mcprBinDetails.requestType" /></label></td>
							<td>${mcprViewEditHistoricDto.lastOperation}</td>
							<td><label><spring:message code="mcprBinDetails.requestDate" /></label></td>
							<td>${mcprViewEditHistoricDto.lastUpdatedOn}</td>
							<td><label><spring:message code="mcprBinDetails.requestStatus" /></label></td>
							<td><c:if test="${mcprViewEditHistoricDto.requestState =='A' }"><spring:message code="mcprBinDetails.requestState.approved.description" /></c:if>
								<c:if test="${mcprViewEditHistoricDto.requestState =='P' }"><spring:message code="mcprBinDetails.requestState.pendingApproval.description" /></c:if>
								<c:if test="${mcprViewEditHistoricDto.requestState =='R' }"><spring:message code="mcprBinDetails.requestState.rejected.description" /></c:if>
								<c:if test="${mcprViewEditHistoricDto.requestState =='D' }"><spring:message code="mcprBinDetails.requestState.deleted.description" /></c:if>
							</td>
						</tr>
						<tr>
							<td><label><spring:message code="mcprBinDetails.requestBy" /></label></td>
							<td>${mcprViewEditHistoricDto.lastUpdatedBy}</td>
							<td><label><spring:message code="mcprBinDetails.approverComments" /></label></td>
							<td colspan=2>${mcprViewEditHistoricDto.checkerComments}</td>
							<td></td>
						</tr>
						<td colspan="6">
							<div class="panel-heading-red clearfix">
								<strong><span class="glyphicon glyphicon-credit-card"></span> 
								<span data-i18n="Data"><spring:message code="mcprBinDetails.viewscreen.title" /></span></strong>
							</div>
							</td>
								<tr>
									<td><label><spring:message code="mcprBinDetails.mcprBinDetailsId" /></label></td>
									<td>${mcprViewEditHistoricDto.mcprBinDataDetailsId }</td>
									<td><label><spring:message code="mcprBinDetails.monthEnding" /></label></td>
									<td>${mcprViewEditHistoricDto.monthEnding }</td>
									<td><label><spring:message code="mcprBinDetails.binNumber" /></label></td>
									<td>${mcprViewEditHistoricDto.binNumber}</td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.physicalContactCardCumulativeRpay" /></label></td>
									<td>${mcprViewEditHistoricDto.phyContactCardCummRuPayCard }</td>
									<td><label><spring:message code="mcprBinDetails.physicalContactCardIncremental" /></label></td>
									<td>${mcprViewEditHistoricDto.phyContactCardIncrementalCard }</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.physicalContactlessCardCumulativeRpay" /></label></td>
									<td>${mcprViewEditHistoricDto.phyContactlessCummRuPayCard}</td>
									<td><label><spring:message code="mcprBinDetails.physicalContactlessCardIncremental" /></label></td>
									<td>${mcprViewEditHistoricDto.phyContactlessIncrementalCard }</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.virtualCardCumulativeRpay" /></label></td>
									<td>${mcprViewEditHistoricDto.virtualCardCummRuPayCard }</td>
									<td><label><spring:message code="mcprBinDetails.virtualCardIncremental" /></label></td>
									<td>${mcprViewEditHistoricDto.virtualCardIncrementalCard}</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.ecomTxnOnusCount" /></label></td>
									<td>${mcprViewEditHistoricDto.ecommTxnOnusCount }</td>
									<td><label><spring:message code="mcprBinDetails.ecomTxnOnusAmt" /></label></td>
									<td>${mcprViewEditHistoricDto.ecommTxnOnusAmt }</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.posContactDomesticTxnCount" /></label></td>
									<td>${mcprViewEditHistoricDto.posConcactCardPresentDomTxnOnusCount}</td>
									<td><label><spring:message code="mcprBinDetails.posContactDomesticTxnAmt" /></label></td>
									<td>${mcprViewEditHistoricDto.posConcactCardPresentDomTxnOnusAmt }</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.posContactlessOnlineRetailsDomesticTxnCount" /></label></td>
									<td>${mcprViewEditHistoricDto.posContactlessOnlRetailsDomTxnOnusCount }</td>
									<td><label><spring:message code="mcprBinDetails.posContactlessOnlineRetailsDomesticTxnAmt" /></label></td>
									<td>${mcprViewEditHistoricDto.posContactlessOnlRetailsDomTxnOnusAmt}</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.posContactlessOnlineTransitDomesticTxnCount" /></label></td>
									<td>${mcprViewEditHistoricDto.posContactlessOnlTransitDomTxnOnusCount }</td>
									<td><label><spring:message code="mcprBinDetails.posContactlessOnlineTransitDomesticTxnAmt" /></label></td>
									<td>${mcprViewEditHistoricDto.posContactlessOnlTransitDomTxnOnusAmt }</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.posContactlessOfflineRetailsDomesticTxnCount" /></label></td>
									<td>${mcprViewEditHistoricDto.posContactlessOffRetailsDomTxnOnusCount}</td>
									<td><label><spring:message code="mcprBinDetails.posContactlessOfflineRetailsDomesticTxnAmt" /></label></td>
									<td>${mcprViewEditHistoricDto.posContactlessOffRetailsDomTxnOnusCount }</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.posContactlessOfflineTransitDomesticTxnCount" /></label></td>
									<td>${mcprViewEditHistoricDto.posContactlessOffTransitDomTxnOnusCount }</td>
									<td><label><spring:message code="mcprBinDetails.posContactlessOfflineTransitDomesticTxnAmt" /></label></td>
									<td>${mcprViewEditHistoricDto.posContactlessOffTransitDomTxnOnusAmt}</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.atmTxnCount" /></label></td>
									<td>${mcprViewEditHistoricDto.atmCardPresentDomTxnOnusCount }</td>
									<td><label><spring:message code="mcprBinDetails.atmTxnAmt" /></label></td>
									<td>${mcprViewEditHistoricDto.atmCardPresentDomTxnOnusAmt }</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="mcprBinDetails.totalCumulativeCards" /></label></td>
									<td>${mcprViewEditHistoricDto.totalCumulativeCards }</td>
									<td><label><spring:message code="mcprBinDetails.totalIncrementalCards" /></label></td>
									<td>${mcprViewEditHistoricDto.totalIncrementalCards }</td>
									<td></td>
									<td></td>
								</tr>			
							
							
								<sec:authorize access="hasAuthority('Approve MCPR Data')">
									<c:if test="${mcprViewEditHistoricDto.requestState eq 'P'}">
									<tr>
										<td colspan="6"><div class="panel-heading-red  clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span>
											<span data-i18n="Data"><spring:message code="mcprBinDetails.approvalTab.title" /></span></strong></div>
										</td>
									</tr>
									<tr>
										<td><label><spring:message
											code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td>
												<select name="select" id="apprej"
													onchange="display()">
													<option value="N"><spring:message code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message code="AM.lbl.reject" /></option>
												</select>
											</td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
													style="color: red">*</span></label>
												</div>
											</td>
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div>
											</td>
											<td></td>
										</tr>
									</c:if>
								</sec:authorize>
							</tbody>
						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
								<c:if test="${EditMenu eq 'Yes'}">
									<sec:authorize access="hasAuthority('Approve MCPR Data')">
										<c:if test="${mcprViewEditHistoricDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approvecard" value="Submit"
												onclick="postAction('/approveMcprBinDetailsEdit','E');" />
										</c:if>
									</sec:authorize>
									<sec:authorize access="hasAuthority('Edit MCPR Data')">				
									<c:if test="${mcprViewEditHistoricDto.requestState  eq 'R' and  delete eq 'YES' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="userAction('/discardMcprBinDetails','${mcprViewEditHistoricDto.mcprBinDataDetailsId}');" />
									</c:if>
									<c:if test="${mcprViewEditHistoricDto.requestState  eq 'R' and edit eq 'YES' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="userAction('/discardMcprBinDetails','${mcprViewEditHistoricDto.mcprBinDataDetailsId}');" />
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="userAction('/editViewMcprData','${mcprViewEditHistoricDto.mcprBinDataDetailsId}');"/>
									</c:if>
									</sec:authorize>
									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/viewEditMcprDataPendingForApproval');"><spring:message code="mcprBinDetails.backBtn" /></button>
									</c:if>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

