package org.npci.settlenxt.adminportal.common.mapping;

public class MessageFormatNameMapping {
	
	private String messageFormat;
	private String messageType;
	private String direction;
	private String mti;
	private String funcCode;
	private FieldTypes fields;
	
	private NameMappingContext context;
	public void setMessageFormat(String messageFormat) {
		this.messageFormat = messageFormat;
	}
	public String getMessageType() {
		return messageType;
	}
	public String getMessageFormat() {
		return messageFormat;
	}
	
	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}
	public String getDirection() {
		return direction;
	}
	
	public void setFields(FieldTypes fields) {
		this.fields = fields;
	}
	public NameMappingContext getContext() {
		return context;
	}
	public void setContext(NameMappingContext context) {
		this.context = context;
	}
	public void setDirection(String direction) {
		this.direction = direction;
	}
	public FieldTypes getFields() {
		return fields;
	}
	public String getMti() {
		return mti;
	}
	public void setMti(String mti) {
		this.mti = mti;
	}
	public String getFuncCode() {
		return funcCode;
	}
	public void setFuncCode(String funcCode) {
		this.funcCode = funcCode;
	}
	
	
	
	public boolean inOptionalFieldsList(String fieldName){
		boolean inOptionalFieldsList = false;
		if(fields.getOptionalFieldsMap().containsKey(fieldName)){
			inOptionalFieldsList = true;
		}
		return inOptionalFieldsList;
	}
	public boolean inValidFieldsList(String fieldName){
		boolean inValidFieldsList = false;
		if (fields.getRequiredFieldsMap().containsKey(fieldName)
				|| fields.getOptionalFieldsMap().containsKey(fieldName)
				|| fields.getConditionalFieldsMap().containsKey(fieldName)) {
			inValidFieldsList = true;
		}
		return inValidFieldsList;
	}
	
	
	public boolean inConditionalFieldsList(String fieldName){
		boolean inConditionalFieldsList = false;
		if(fields.getConditionalFieldsMap().containsKey(fieldName)){
			inConditionalFieldsList = true;
		}
		return inConditionalFieldsList;
	}
	public boolean inRequiredFieldsList(String fieldName){
		boolean inRequiredFieldsList = false;
		if(fields.getRequiredFieldsMap().containsKey(fieldName)){
			inRequiredFieldsList = true;
		}
		return inRequiredFieldsList;
	}
	
}
