<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix = "fmt" uri = "http://java.sun.com/jsp/jstl/fmt" %>
<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />
<script type="text/javascript"> 
var actionColumnIndex=6;
<c:if test="${showApprovalTab eq 'Yes'}">
	actionColumnIndex=10;
</c:if>
</script>

<script type="text/javascript"> 
var actionColumnIndex=6;
var firstColumnToBeSkippedInFilterAndSort=false;
<c:if test="${showCheckBox eq 'Y'}">
actionColumnIndex = 10;
firstColumnToBeSkippedInFilterAndSort=true;
</c:if>	
<c:if test="${showCheckBox eq 'N'}">
actionColumnIndex = 9;
firstColumnToBeSkippedInFilterAndSort=false;
</c:if>
</script>
 <script>
	var referenceNoListPendings = [];
	

	<c:if test="${not empty pendingForexRateList}">
	<c:forEach items="${pendingForexRateList}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	referenceNoListPendings.push('${operator.forexRateId}');
	
	</c:if>
	</c:forEach>
	</c:if>
</script> 


<script src="./static/js/validation/showForexRate.js"
	type="text/javascript"></script>
<script type="text/javascript" src=	"./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src=	"./static/js/jszip.min.js">
</script>
 
<script type="text/javascript" src=	"./static/js/buttons.html5.min.js">
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />

<style>
	 
	.defaultexport {
  visibility: hidden;
}

table.dataTable thead  { vertical-align: top;}
table.dataTable thead .sorting { vertical-align: top; background: url('./static/images/sort_both.png') no-repeat center right; }
table.dataTable thead .sorting_asc { vertical-align: top;background: url('./static/images/sort_asc.png') no-repeat center right; }
table.dataTable thead .sorting_desc { vertical-align: top;background: url('./static/images/sort_desc.png') no-repeat center right; }
table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before{ vertical-align: top;content:""}
table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after{ vertical-align: top;content:""}
.search-box  {	
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;	
	background-color: transparent;
    width: 100%;
    border-width:1px;
	border-style:inset;
    }
</style>

<!-- Modal -->	
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog" aria-labelledby="toggleModalNews" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Are you sure you want to Approve/Reject these records?</h5>
        <button type="button" class="close" data-dismiss="modal"  aria-label="Close" onclick="deselectAll()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
     <div>
          <label style="color:blue;font-weight:bold;" > ForexRate Approval/Rejection </label>
          <p id="newsIds"/>
          </div>



     <div class="modal-footer">
        <button type="button" class="btn btn-danger" onclick="ApproveOrRejectBulkForexRate('R','All')">Reject</button>
        <button type="button" class="btn btn-success" onclick="ApproveOrRejectBulkForexRate('A','All')">Approve</button>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<input:hidden id="refNum" />

<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/forexRates');" role="tab" data-toggle="tab">
			<span class="glyphicon glyphicon-credit-card"></span>
			<spring:message code="forexRate.main.title" />
		</a>
		
		<c:choose>
			<c:when test="${pendingAppForexRate eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" role="tab" onclick="getPendingForexRate();"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> </span>
			<spring:message code="forexRate.approval.title" /></a>
	</ul>
	
	
	<div class="row">
		<div class="col-sm-12">
			<sec:authorize access="hasAuthority('Add Forex Rates')">
				<c:if test="${addForexRate eq 'Yes'}">
					<a class="btn btn-success pull-right btn_align" href="#"
						onclick="submitForm('/forexRateCreation','P');"
						style="margin-top: -30px;"><em class="glyphicon-plus"></em> 
						<spring:message code="forexRate.addBtn" /></a>
				</c:if>
			</sec:authorize>
		</div>
	</div>

	<div class="tab-content">
		<div role="tabpanel" class="tab-pane active" id="home">
			<c:if test="${showMainTab eq 'Yes'}">

				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="forexRate.listscreen.title" /></span></strong>
							</div>


							<div class="panel-body">
								<!-- for search -->
								<div class="row">
									<div class="col-sm-12">
										<div class="row">
											<form:form modelAttribute="forexRateDto" method="POST"
												autocomplete="off" id="abc">
												<div class="row">
													<div class="col-md-3">
														<div class="form-group">
															<label><spring:message code="forexRate.networkId" />
																<span style="color: red">*</span></label>
															<form:select path="networkId" id="networkId"
																name="networkId" maxlength="10"
																value="${forexRateDto.networkIdLookup}"
																cssClass="form-control medantory">
																<form:option value="" label="SELECT" />
																<form:options items="${networkIdList}" itemValue="code"
																	itemLabel="description" />
															</form:select>
															<div id="errnetworkId">
																<span for="networkId" class="error"> <form:errors
																		path="networkId" />
																</span>
															</div>
														</div>
													</div>

													<div class="col-md-3">
														<div class="form-group">
															<label for="">Settle Date<span class="red">*</span></label>
															<form:input path="settleDate" readonly="true" id="settleDate"
																name="settleDate" cssClass="form-control input-square" />
															<div id="errsettleDate">
																<span for="settleDate" class="error"> <form:errors
																		path="settleDate" />
																</span>
															</div>
														</div>
													</div>
													<div class="col-md-3">
														<div class="form-group">
															<div>
																<button type="button" value="Submit"
																	class="btn btn-success" onclick="clearValues();">
																	<spring:message code="forexRate.clearBtn" />
																</button>
																<button type="button" value="Submit"
																	class="btn btn-success" onclick="searchForexRate('Y');">
																	<spring:message code="forexRate.searchBtn" />
																</button>
															</div>
														</div>
													</div>
													</div>
											</form:form>
										</div>
									</div>
								</div>
							</div>
							<!-- close search -->

							<c:if test="${searchFlag eq 'Y'}">
								<div class="row">
									<div class="col-sm-12">
										<button class="btn  pull-right btn_align" id="clearFilters">
											<spring:message code="forexRate.clearBtn" />
										</button>
										&nbsp; <a class="btn btn-success pull-right btn_align"
											href="#" id="csvExport"> <spring:message
												code="forexRate.csvBtn" />
										</a> <a class="btn btn-success pull-right btn_align" href="#"
											id="excelExport"><spring:message
												code="forexRate.exportBtn" /> </a>

									</div>
								</div>


								<div class="table-responsive">
									
									<table id="tabnew" class="table table-striped table-bordered"
										style="width:100%;">
										<caption style="display:none;">FOREX RATE</caption>
										<thead>
											<tr>
												<th scope="col"><label><spring:message
															code="forexRate.networkId" /></label></th>
												<th scope="col"><label><spring:message
															code="forexRate.rateConversion" /></label></th>
												<th scope="col"><label><spring:message
															code="forexRate.dateSettle" /></label></th>
												<th scope="col"><label><spring:message
															code="forexRate.currencyFrom" /></label></th>
												<th scope="col"><label><spring:message
															code="forexRate.currencyTo" /></label></th>

											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty forexRateList}">
												<c:forEach var="forexRateList" items="${forexRateList}">

													<tr>
														<td
															onclick="javascript:viewForexRate('${forexRateList.forexRateId}','V')">${forexRateList.networkIdLookup}</td>
														<td
															onclick="javascript:viewForexRate('${forexRateList.forexRateId}','V')">${forexRateList.rateConversion}</td>
														<td
															onclick="javascript:viewForexRate('${forexRateList.forexRateId}','V')"><fmt:formatDate
																pattern="yyyy-MM-dd" value="${forexRateList.settleDate}" /></td>
														<td
															onclick="javascript:viewForexRate'${forexRateList.forexRateId}','V')">${forexRateList.currencyFromLookup}</td>
														<td
															onclick="javascript:viewForexRate('${forexRateList.forexRateId}','V')">${forexRateList.currencyToLookup}</td>

													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>
							</c:if>
						</div>
					</div>
				</div>
				</c:if>
		</div>
		<c:if test="${showApprovalTab eq 'Yes'}">
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default">
						<div class="panel-heading">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message
										code="forexRate.listscreen.title" /></span></strong>
							<c:if test="${not empty pendingForexRateList}">
								<sec:authorize access="hasAuthority('Approve Forex Rates')">


									<input type="button" id="submitButtonA"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveOrRejectBulkForexRate('A','No')"
										id="submitButton"
										value="<spring:message code="forexRate.Approve" />" />
									<input type="button" id="submitButtonR"
										class="btn btn-danger pull-right btn_align"
										onclick="ApproveOrRejectBulkForexRate('R','No')"
										id="submitButton"
										value="<spring:message code="forexRate.Reject" />" />
								</sec:authorize>
							</c:if>
						</div>
						<div class="panel-body">
							<div class="row">
								<div class="col-sm-12">
									<button class="btn  pull-right btn_align" id="clearFilters">
										<spring:message code="forexRate.clearBtn" />
									</button>
									&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
										id="csvExport"> <spring:message code="forexRate.csvBtn" />
									</a> <a class="btn btn-success pull-right btn_align" href="#"
										id="excelExport"><spring:message
											code="forexRate.exportBtn" /> </a>

								</div>
							</div>
							<div class="table-responsive">
								<table id="tabnew" class="table table-striped table-bordered"
									style="width:100%;">
									<caption style="display:none;">FOREX RATE</caption>
									<thead>
										<tr>
											<sec:authorize access="hasAuthority('Approve Forex Rates')">
												<th scope="col"><input type=checkbox name='selectAllCheck'
													id="selectAll" data-target="toggleModalNews" value="All"></input></th>
											</sec:authorize>
											<th scope="col"><label><spring:message
														code="forexRate.networkId" /></label></th>
											<th scope="col"><label><spring:message
														code="forexRate.rateConversion" /></label></th>
											<th scope="col"><label><spring:message
														code="forexRate.dateSettle" /></label></th>
											<th scope="col"><label><spring:message
														code="forexRate.currencyFrom" /></label></th>
											<th scope="col"><label><spring:message
														code="forexRate.currencyTo" /></label></th>
											<th scope="col"><label><spring:message
														code="forexRate.requestType" /></label></th>
											<th scope="col"><label><spring:message
														code="forexRate.status" /></label></th>
											<th scope="col"><label><spring:message
														code="forexRate.checkerComments" /></label></th>
										</tr>
									</thead>
									<tbody>
										<c:if test="${not empty pendingForexRateList}">

											<c:forEach var="pendingForexRateList"
												items="${pendingForexRateList}">
												<tr>
													<sec:authorize access="hasAuthority('Approve Forex Rates')">
														<c:if test="${pendingForexRateList.requestState =='P' }">
															<th scope="col"><input type=checkbox name='type'
																id="selectSingle" onclick="mySelect();"
																value="${pendingForexRateList.forexRateId}"></input></th>
														</c:if>
														<c:if test="${pendingForexRateList.requestState =='R' }">
															<th scope="col"></th>
														</c:if>

													</sec:authorize>

													<td
														onclick="javascript:viewForexRate('${pendingForexRateList.forexRateId}','P')">${pendingForexRateList.networkIdLookup}</td>
													<td
														onclick="javascript:viewForexRate('${pendingForexRateList.forexRateId}','P')">${pendingForexRateList.rateConversion}</td>
													<td
														onclick="javascript:viewForexRate('${pendingForexRateList.forexRateId}','P')"><fmt:formatDate
															pattern="yyyy-MM-dd"
															value="${pendingForexRateList.settleDate}" /></td>
													<td
														onclick="javascript:viewForexRate('${pendingForexRateList.forexRateId}','P')">${pendingForexRateList.currencyFromLookup}</td>
													<td
														onclick="javascript:viewForexRate('${pendingForexRateList.forexRateId}','P')">${pendingForexRateList.currencyToLookup}</td>
													<td
														onclick="javascript:viewForexRate('${pendingForexRateList.forexRateId}','P')">${pendingForexRateList.lastOperation}</td>



													<td
														onclick="javascript:viewForexRate('${pendingForexRateList.forexRateId}','P')">
														<c:if test="${pendingForexRateList.requestState =='A' }">
															<spring:message
																code="forexRate.requestState.approved.description" />
														</c:if> <c:if test="${pendingForexRateList.requestState =='P' }">
															<spring:message
																code="forexRate.requestState.pendingApproval.description" />
														</c:if> <c:if test="${pendingForexRateList.requestState =='R' }">
															<spring:message
																code="forexRate.requestState.rejected.description" />
														</c:if> <c:if test="${pendingForexRateList.requestState =='D' }">
															<spring:message
																code="forexRate.requestState.discared.description" />
														</c:if>
													</td>
													<td
														onclick="javascript:viewForexRate('${pendingForexRateList.forexRateId}','P')">${pendingForexRateList.checkerComments}
														</td>
												</tr>
											</c:forEach>
										</c:if>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</c:if>
	</div>
</div>


