<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script src="./static/js/validation/showDisputeFeeRules.js"
	type="text/javascript"></script>
	
<div class="space_block">
<div class="container-fluid height-min">

	<c:url value="getReasonCode" var="getReasonCode" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewDisputeFeeRule" modelAttribute="disputeFeeRuleInfo"
		action="${getReasonCode}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"></span><spring:message code="disputeFeeRule.mainTab.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="crtuser"
							value="${disputeFeeRuleInfo.createdBy}" />
							
						<input type="hidden" id="seqId"
							value="${disputeFeeRuleInfo.seqId}" />
	
						<table class="table table-striped "
							style="font-size: 12px">
							<caption style="display:none;">View Dispute Fee Rule</caption> 
							<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
										<td colspan="6">
											<div class="panel-heading-red clearfix">
												<strong><span class="glyphicon glyphicon-info-sign"></span> <span
													data-i18n="Data"></span>
												<spring:message code="disputeFeeRule.viewscreen.title" /></span></strong>
											</div>
										</td>
									</tr>
								<tr>

								</tr>

								<tr>
									<td><label><spring:message code="disputeFeeRules.actionCode" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.actionCode}">N/A</c:if>${disputeFeeRuleInfo.actionCode}</td>
									<td><label><spring:message code="disputeFeeRules.feeType" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.feeType}">N/A</c:if>${disputeFeeRuleInfo.feeType}</td>
									<td><label><spring:message code="disputeFeeRules.priority" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.priority}">N/A</c:if>${disputeFeeRuleInfo.priority}</td>		
									
								</tr>
								<tr>
									<td><label><spring:message code="disputeFeeRules.feeCode" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.feeCode}">N/A</c:if>${disputeFeeRuleInfo.feeCode}</td>
									<td><label><spring:message code="disputeFeeRules.logicalFeeCode" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.logicalFeeCode}">N/A</c:if>${disputeFeeRuleInfo.logicalFeeCode}</td>
									<td><label><spring:message code="disputeFeeRules.fieldName" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.fieldName}">N/A</c:if>${disputeFeeRuleInfo.fieldName}</td>
								</tr>
								<tr>
									<td><label><spring:message code="disputeFeeRules.relationalOperator" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.relationalOperator}">N/A</c:if>${disputeFeeRuleInfo.relationalOperator}</td>
									<td><label><spring:message code="disputeFeeRules.fieldValue" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.fieldValue}">N/A</c:if>${disputeFeeRuleInfo.fieldValue}</td>
									<td><label><spring:message code="disputeFeeRules.fieldOperator" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.fieldOperator}">N/A</c:if>${disputeFeeRuleInfo.fieldOperator}</td>
								</tr>
								<tr>
									<td><label><spring:message code="disputeFeeRules.createdBy" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.createdBy}">N/A</c:if>${disputeFeeRuleInfo.createdBy}</td>
									<td><label><spring:message code="disputeFeeRules.createdOn" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.createdOn}">N/A</c:if>${disputeFeeRuleInfo.createdOn}</td>
									<td><label><spring:message code="disputeFeeRules.checkerComments" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleInfo.checkerComments}">N/A</c:if>${disputeFeeRuleInfo.checkerComments}</td>
								</tr>
							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									
								

									<button type="button" class="btn btn-danger"
										onclick="userAction('P','/showDisputeFeeRule');"><spring:message code="budget.backBtn" /></button>
									<sec:authorize access="hasAuthority('Edit Reason Code')">
								
								<button type="button" class="btn btn-success"
										onclick="view('${disputeFeeRuleInfo.seqId}','V')">
										<spring:message code="sm.lbl.edit" /></button>
								
								</sec:authorize>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>
</div>
