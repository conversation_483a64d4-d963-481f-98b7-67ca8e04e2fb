package org.npci.settlenxt.adminportal.controllers;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.service.JsonValidatorService;
import org.npci.settlenxt.adminportal.service.ReasonCodeMasterService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.JsonValidatorDTO;
import org.npci.settlenxt.portal.common.dto.ReasonCodeDto;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

@Controller
public class JsonValidatorController extends BaseController{
	
	private static final String SHOW_JSON_VALIDATOR = "showJsonValidator";

	private static final String ADD_EDIT_JSON_VALIDATOR = "addEditJsonValidator";

	private static final String VIEW_JSON_VALIDATOR = "viewJsonValidator";

	private static final String VIEW_APPROVE_JSON_VALIDATOR = "viewApproveJsonValidator";
	
	@Autowired
	JsonValidatorService jsonValidatorService;
	
	@Autowired
	ReasonCodeMasterService reasonCodeMasterService;
	
	@Autowired
	private BaseLookupService lookupService;
	
	@Autowired
	MessageSource messageSource;
	
	@PostMapping("/showJsonValidator")
	public String showJsonValidator(Model model) throws SettleNxtException {
		try {
		model.addAttribute(SHOW_JSON_VALIDATOR, CommonConstants.YES);
		
		List<JsonValidatorDTO> jsonValidatorDtoList = jsonValidatorService.getJsonValidatorMainDetails();
		model.addAttribute(CommonConstants.JSON_VALIDATOR_LIST, jsonValidatorDtoList);
		
		
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, SHOW_JSON_VALIDATOR, ex);
		}
		
		model.addAttribute(CommonConstants.ADD_JSON_VALIDATOR, CommonConstants.TRANSACT_YES);
		return getView(model,SHOW_JSON_VALIDATOR);
	}
	
	
	@PostMapping("/createJsonValidator")
	@PreAuthorize("hasAuthority('Add Json Validator')")
	public String createJsonValidator(Model model) throws SettleNxtException {
		try {
			List<ReasonCodeDto> reasonCodeList = reasonCodeMasterService.getReasonCodewithReasonType();
			model.addAttribute(CommonConstants.ADD_JSON_VALIDATOR_DETAIL, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.ADD_JSON_VALIDATOR, CommonConstants.TRANSACT_YES);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_BUDGET);
			
			List<String> pCodeList = jsonValidatorService.getPcodeList();
			for(ReasonCodeDto reasonCode : reasonCodeList ) {
				reasonCode.setReasonCodeDesc(reasonCode.getReasonCode()+ "|"+  reasonCode.getReasonCodeDesc());
			}
			JsonValidatorDTO jsonValidatorDto = new JsonValidatorDTO();
			jsonValidatorDto.setSeqId(0);
			model.addAttribute(CommonConstants.JSON_VALIDATOR_DTO, jsonValidatorDto);
			model.addAttribute(CommonConstants.REASON_CODE_MASTER_LIST, reasonCodeList);
			model.addAttribute(CommonConstants.PROCESSING_CODE, pCodeList);
			} catch (Exception ex) {
				return handleErrorCodeAndForward(model, ADD_EDIT_JSON_VALIDATOR, ex);
		}
		return getView(model, ADD_EDIT_JSON_VALIDATOR);
	}
	
	
	@PostMapping("/addJsonValidator")
	@PreAuthorize("hasAuthority('Add Json Validator')")
	public String addJsonValidator(@ModelAttribute("jsonValidatorDto") JsonValidatorDTO jsonValidatorDto, Model model) throws SettleNxtException{
		
		try {
			
			model.addAttribute(CommonConstants.ADD_JSON_VALIDATOR, CommonConstants.ADD_JSON_VALIDATOR);
			
			model.addAttribute(CommonConstants.JSON_VALIDATOR_DTO,jsonValidatorDto);
			jsonValidatorService.addJsonValidatorStg(jsonValidatorDto);
			
			model.addAttribute(CommonConstants.SUCCESS_STATUS,getMessageFromBundle("jsonValidator.addSuccess.msg"));
			
			} catch (Exception ex) {
				return handleErrorCodeAndForward(model, ADD_EDIT_JSON_VALIDATOR, ex);
			}
			
		return getView(model, ADD_EDIT_JSON_VALIDATOR);
		
	}
	@PostMapping("/addAllJsonValidator")
	@PreAuthorize("hasAuthority('Add Json Validator')")
	public ResponseEntity<Object> addAllJsonValidator(Model model,
			@RequestBody List<JsonValidatorDTO> jsonValidatorList, HttpServletResponse response) {
		for(JsonValidatorDTO jsonValidatorDto:jsonValidatorList)
			{
			jsonValidatorService.addJsonValidatorStg(jsonValidatorDto);
			}
		String result = "Success";
		JsonObject jsonResponse = new JsonObject();
		if (StringUtils.equals(result, "Success")) {
			jsonResponse.addProperty("status", BaseCommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty("status", BaseCommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}
	
	@PostMapping("/getJsonValidator")
	@PreAuthorize("hasAuthority('view Json Validator')")
	public String getJsonValidator(@RequestParam("seqId") int seqId,Model model)
			throws SettleNxtException {
		
		JsonValidatorDTO jsonValidatorDto = jsonValidatorService.getJsonValidator(seqId);
		
		model.addAttribute(CommonConstants.JSON_VALIDATOR_DTO,jsonValidatorDto);
		return getView(model, VIEW_JSON_VALIDATOR);
		
	}
	
	
	@PostMapping("/jsonValidatorPendingForApproval")
	@PreAuthorize("hasAuthority('view Json Validator')")
	public String jsonValidatorPendingForApproval(Model model) throws SettleNxtException {

		try {
			List<JsonValidatorDTO> jsonValidatorPendingList = jsonValidatorService.getPendingReasonCode();
			Long tranSize = jsonValidatorPendingList.stream().filter(jsonValidatorDto->jsonValidatorDto.getRequestState().equalsIgnoreCase("P")).count();
			model.addAttribute("tranSize",tranSize);
			model.addAttribute(CommonConstants.JSON_VALIDATOR_PENDING_LIST, jsonValidatorPendingList);
			model.addAttribute(CommonConstants.JSON_VALIDATOR_APP_PENDING, CommonConstants.YES);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_JSON_VALIDATOR, ex);
		}
		return getView(model, SHOW_JSON_VALIDATOR);
	} 
	
	@PostMapping("/getPendingJsonValidator")
@PreAuthorize("hasAuthority('view Json Validator')")
public String getPendingJsonValidatorDetail(@RequestParam("seqId") int seqId, Model model)
		throws SettleNxtException {
		JsonValidatorDTO jsonValidatorDto = jsonValidatorService.getJsonValidatorStgDetail(seqId);
		model.addAttribute(CommonConstants.JSON_VALIDATOR_DTO,jsonValidatorDto);
	
	return getView(model, VIEW_APPROVE_JSON_VALIDATOR);
	
}
	
	
	@PostMapping("/editJsonValidator")
	@PreAuthorize("hasAuthority('Edit Json Validator')")
	public String editJsonValidator( @RequestParam("seqId") int seqId, Model model) {
		JsonValidatorDTO jsonValidatorDto = new JsonValidatorDTO();
		List<ReasonCodeDto> reasonCodeList = new ArrayList<>();
		
		List<String> pCodeList = new ArrayList<>();
		try {
			reasonCodeList = reasonCodeMasterService.getReasonCode();
			jsonValidatorDto = jsonValidatorService.getJsonValidator(seqId);
			jsonValidatorDto.setReasonCodeDesc(jsonValidatorDto.getReasonCode()+ "|"+  jsonValidatorDto.getReasonCodeDesc());
			pCodeList = jsonValidatorService.getPcodeList();
			
			for(ReasonCodeDto reasonCode : reasonCodeList ) {
				reasonCode.setReasonCodeDesc(reasonCode.getReasonCode()+ "|"+  reasonCode.getReasonCodeDesc());
			}
			} catch (Exception ex) {
			handleErrorCodeAndForward(model,ADD_EDIT_JSON_VALIDATOR, ex);
		}
		model.addAttribute(CommonConstants.EDIT_JSON_VALIDATOR, CommonConstants.TRANSACT_YES);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.JSON_VALIDATOR_DTO, jsonValidatorDto);
		model.addAttribute(CommonConstants.REASON_CODE_MASTER_LIST, reasonCodeList);
		model.addAttribute(CommonConstants.PROCESSING_CODE, pCodeList);
		return getView(model, ADD_EDIT_JSON_VALIDATOR);
	}
	
	@PostMapping("/updateJsonValidator")
@PreAuthorize("hasAuthority('Edit Json Validator')")
public String updateJsonValidator(@ModelAttribute("jsonValidatorDto") JsonValidatorDTO jsonValidatorDto, Model model) {
try {
	jsonValidatorDto = jsonValidatorService.updateJsonValidatorStg(jsonValidatorDto);
	model.addAttribute(CommonConstants.JSON_VALIDATOR_DTO,jsonValidatorDto);
} catch (Exception ex) {
	
	return handleErrorCodeAndForward(model, ADD_EDIT_JSON_VALIDATOR, ex);
}

model.addAttribute(CommonConstants.EDIT_JSON_VALIDATOR,CommonConstants.TRANSACT_YES );
model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("jsonValidator.updateSuccess.msg"));
return getView(model, VIEW_APPROVE_JSON_VALIDATOR);
}
	
	
	@PostMapping("/approveJsonValidator")
	@PreAuthorize("hasAuthority('Approve Json Validator')")
public String approveJsonValidator(@RequestParam("seqId") int seqId,@RequestParam("status") String status,
		@RequestParam("remarks") String remarks, Model model,HttpServletRequest request) throws SettleNxtException {
	try {
		JsonValidatorDTO jsonValidatorDto = jsonValidatorService.updateApproveOrRejectJsonValidator(seqId, status, remarks);
		checkJsonValidatorApproveStatus(jsonValidatorDto, model);
		model.addAttribute(CommonConstants.JSON_VALIDATOR_DTO,jsonValidatorDto);
		
		
	} catch (Exception ex) {
		handleErrorCodeAndForward(model, VIEW_APPROVE_JSON_VALIDATOR, ex);
	}
	return getView(model, VIEW_APPROVE_JSON_VALIDATOR);
}

private void checkJsonValidatorApproveStatus(JsonValidatorDTO jsonValidatorDto, Model model) {
	if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(jsonValidatorDto.getStatusCode())) {
		model.addAttribute(CommonConstants.SUCCESS_STATUS,getMessageFromBundle("jsonValidator.approvalSuccess.msg"));
	} else {
		model.addAttribute(CommonConstants.SUCCESS_STATUS,getMessageFromBundle("jsonValidator.rejectionSuccess.msg"));
	}
	
}
	

@PostMapping("/discardRejectedJsonValidator")
@PreAuthorize("hasAuthority('Edit Json Validator')")
public String discardJsonValidator(@RequestParam("seqId") int seqId, Model model) {
JsonValidatorDTO jsonValidatorDto = new JsonValidatorDTO();
try {
	jsonValidatorDto = jsonValidatorService.discardJsonValidator(seqId);
} catch (Exception ex) {
	return handleErrorCodeAndForward(model, VIEW_APPROVE_JSON_VALIDATOR, ex);
}
model.addAttribute(CommonConstants.JSON_VALIDATOR_DTO,jsonValidatorDto);
model.addAttribute(CommonConstants.SUCCESS_STATUS,getMessageFromBundle("jsonValidator.discardSuccess.msg"));
return getView(model, VIEW_APPROVE_JSON_VALIDATOR);
}

@PostMapping("/bulkApproveJsonValidator")
@PreAuthorize("hasAuthority('Approve Json Validator')")
public String bulkApproveJsonValidator(Model model,@RequestParam("status") String status, @RequestParam("jsonValidatorList") String jsonValidatorList,
		HttpServletResponse response, HttpServletRequest request){
	
	String result = jsonValidatorService.updateBulkStgJsonValidator(jsonValidatorList, status);

	if (StringUtils.equals(result, CommonConstants.RESULT_SUCCESS) && StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
		model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
				messageSource.getMessage("jsonValidator.approvalSuccess.msg", null, request.getLocale()));
	} else if (StringUtils.equals(result, CommonConstants.RESULT_SUCCESS)
			&& StringUtils.equals(status, CommonConstants.RECORD_REJECTED)) {
		model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
				messageSource.getMessage("jsonValidator.rejectionSuccess.msg", null, request.getLocale()));
	} else {
		model.addAttribute(BaseCommonConstants.ERROR_STATUS,
				messageSource.getMessage("E00001", null, request.getLocale()));
		model.addAttribute(CommonConstants.JSON_VALIDATOR_PENDING_LIST, null);
		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB,BaseCommonConstants.NO_FLAG);
		
		return getView(model, SHOW_JSON_VALIDATOR);
	}

	List<JsonValidatorDTO> jsonValidatorPendingList = jsonValidatorService.getPendingReasonCode();
	Long tranSize = jsonValidatorPendingList.stream().filter(jsonValidatorDto->jsonValidatorDto.getRequestState().equalsIgnoreCase("P")).count();
	model.addAttribute("tranSize",tranSize);
	model.addAttribute(CommonConstants.JSON_VALIDATOR_PENDING_LIST, jsonValidatorPendingList);
	model.addAttribute(CommonConstants.JSON_VALIDATOR_APP_PENDING, CommonConstants.YES);

		return getView(model, SHOW_JSON_VALIDATOR);

}


}
