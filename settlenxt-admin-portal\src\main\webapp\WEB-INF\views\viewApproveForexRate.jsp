<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix = "fmt" uri = "http://java.sun.com/jsp/jstl/fmt" %>
<script src="./static/js/validation/viewApproveForexRate.js" type="text/javascript"></script>

<div class="container-fluid height-min">
	<c:url value="approveForexRate" var="approveForexRate" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveForexRate" modelAttribute="forexRateDto"
		action="${approveDispute}" autocomplete="off">
		<div class="alert alert-danger appRejMust" role="alert">Please
		Select Approve/Reject action.</div>
		<div class="alert alert-danger remarkMust" role="alert">Please
		Enter Remarks.</div>
		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="forexRate.viewscreen.title" /></span></strong>
					</div>
					<div class="panel-body">
						<input type="hidden" id="forexRateId" value="${forexRateDto.forexRateId}" />
						<table class="table table-striped infobold"
							style="font-size: 12px">
							<caption style="display:none;">FOREX RATE</caption>
							<thead style="display:none;"><th scope="col"></th></thead><tbody>
								<tr>
									<td colspan="6">
									<div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> 
											<span data-i18n="Data"><spring:message code="forexRate.requestInformation" /></span></strong>
									</div></td>
								</tr>
								<tr>
									<td><label><spring:message code="forexRate.requestType" /><span style="color: red"></span></label></td>
									<td>${forexRateDto.lastOperation}</td>
									<td><label><spring:message code="forexRate.requestDate" /><span style="color: red"></span></label></td>
									<td>${forexRateDto.lastUpdatedOn}</td>
									<td><label><spring:message code="forexRate.requestStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${forexRateDto.requestState =='A' }"><spring:message	code="forexRate.requestState.approved.description" /></c:if>
										<c:if test="${forexRateDto.requestState =='P' }"><spring:message code="forexRate.requestState.pendingApproval.description" /></c:if>
										<c:if test="${forexRateDto.requestState =='R' }"><spring:message code="forexRate.requestState.rejected.description" /></c:if>
										<c:if test="${forexRateDto.requestState =='D' }"><spring:message code="forexRate.requestState.discared.description" /></c:if>
									</td>
								</tr>
								<tr>
									<td><label><spring:message code="forexRate.requestBy" /><span style="color: red"></span></label></td>
									<td>${forexRateDto.lastUpdatedBy}</td>
									<td><label><spring:message code="forexRate.approverComments" /><span style="color: red"></span></label></td>
									<td colspan=2>${forexRateDto.checkerComments}</td>
									<td></td>
									
								</tr>
									<td colspan="6">
									<div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-credit-card"></span> <span data-i18n="Data">
										<spring:message code="forexRate.viewscreen.title" /></span></strong>
										</div>
									</td>
								<tr><sec:authorize access="hasAuthority('Approve Forex Rates')">
									<td><label><spring:message code="forexRate.forexRateId" /> <span style="color: red"></span></label></td>
									<td>${forexRateDto.forexRateId}</td>
									</sec:authorize>
									<td><label><spring:message code="forexRate.networkId" /><span style="color: red"></span></label></td>
									<td >${forexRateDto.networkIdLookup}</td>
									<td><label><spring:message code="forexRate.rateConversion" /><span style="color: red"></span></label></td>
									<td >${forexRateDto.rateConversion}</td>
									<td></td>
									<td></td>
									
								</tr>
								 <tr>
									<td><label><spring:message code="forexRate.dateSettle" /><span style="color: red"></span></label></td>
									<td ><fmt:formatDate pattern="yyyy-MM-dd" value="${forexRateDto.settleDate}" /></td>
									<td><label><spring:message code="forexRate.currencyFrom" /><span style="color: red"></span></label></td>
									<td >${forexRateDto.currencyFromLookup}</td>
									<td><label><spring:message code="forexRate.currencyTo" /><span style="color: red"></span></label></td>
									<td >${forexRateDto.currencyToLookup}</td>
								</tr> 
								<sec:authorize access="hasAuthority('Approve Forex Rates')">
									<c:if test="${forexRateDto.requestState eq 'P'}">
									<tr>
										<td colspan="6">
										<div class="panel-heading-red  clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span>
											<span data-i18n="Data">
											<spring:message code="forexRate.approvalPanel.title" /></span></strong>
										</div>
										</td>
									</tr>
									<tr>
										<td><label><spring:message
											code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td>
												<select name="select" id="apprej" onchange="display()">
													<option value="N"><spring:message code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message code="AM.lbl.reject" /></option>
												</select>
											</td>
											<td>
												<div style="text-align:center">
												<label><spring:message code="AM.lbl.remarks" /><span style="color: red">*</span></label>
												</div>
											</td>
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div>
											</td>
										</tr>
									</c:if>
								</sec:authorize>
							</tbody>
						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Forex Rates')">
										<c:if test="${forexRateDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approvecard" value="Submit"
												onclick="postAction('/approveForexRate');" />
										</c:if>
									</sec:authorize>
													
									<sec:authorize access="hasAuthority('Edit Forex Rates')">				
									<c:if test="${forexRateDto.requestState  eq 'R' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="userAction('/discardForexRate','${forexRateDto.forexRateId}');" />
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="EditForexRate('/editForexRate','${forexRateDto.forexRateId}','approvalPage');"/>
									</c:if>
									</sec:authorize>
									
										<button type="button" class="btn btn-danger"
										onclick="backAction('/forexRateForApproval');">
										<spring:message code="forexRate.backBtn" /></button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

