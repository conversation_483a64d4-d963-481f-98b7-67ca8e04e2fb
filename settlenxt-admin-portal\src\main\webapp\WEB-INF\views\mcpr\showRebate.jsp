<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript"> 
var actionColumnIndex=9;
var firstColumnToBeSkippedInFilterAndSort=false;
<c:if test="${showCheckBox eq 'Y'}">
actionColumnIndex = 11;
firstColumnToBeSkippedInFilterAndSort=true;
</c:if>	
<c:if test="${showCheckBox eq 'N'}">
	<c:if test="${approvedlist eq 'N'}">
	actionColumnIndex = 10;
	</c:if>
</c:if>
</script>
 <script>
	var referenceNoListPendings = [];
	

	<c:if test="${not empty rebatelist}">
	<c:forEach items="${rebatelist}" var="rebate">
	<c:if test="${rebate.requestState eq 'P' }">
	referenceNoListPendings.push('${rebate.rebateID}');
	
	</c:if>
	</c:forEach>
	</c:if>
	

	</script> 

<script src="./static/js/validation/mcpr/SearchRebateRGCS.js"
	type="text/javascript"></script>
<script type="text/javascript" src=	"./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src=	"./static/js/jszip.min.js">
</script>
 
<script type="text/javascript" src=	"./static/js/buttons.html5.min.js">
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
	 
	.defaultexport {
  visibility: hidden;
}

table.dataTable thead  { vertical-align: top;}
table.dataTable thead .sorting { vertical-align: bottom; background: url('./static/images/sort_both.png') no-repeat center right; }
table.dataTable thead .sorting_asc { vertical-align: top;background: url('./static/images/sort_asc.png') no-repeat center right; }
table.dataTable thead .sorting_desc { vertical-align: top;background: url('./static/images/sort_desc.png') no-repeat center right; }
table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before{ vertical-align: top;content:""}
table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after{ vertical-align: top;content:""}
.search-box  {	
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;	
	background-color: transparent;
    width: 100%;
    border-width:1px;
	border-style:inset;
    }
</style>
<!-- Modal -->	
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog" aria-labelledby="toggleModalNews" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Are you sure you want to Approve/Reject these records?</h5>
        <button type="button" class="close" data-dismiss="modal"  aria-label="Close" onclick="deselectAll()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
     <div>
          <label style="color:blue;font-weight:bold;" >Rebate Approval/Rejection</label>
          <p id="newsIds"/>
          </div>



     <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="ApproveorRejectBulkRebate('R','All')">Reject</button>
        <button type="button" class="btn btn-primary" onclick="ApproveorRejectBulkRebate('A','All')">Approve</button>
      </div>
    </div>
  </div>
</div>
<!-- Modal -->
<!-- Modal -->
<input:hidden id="refNum" />
<div class="row">
	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${approvedlist eq 'Y' }">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" onclick="submitForm('/showRebateList');" role="tab"
			data-toggle="tab"> <span class="glyphicon glyphicon-credit-card">&nbsp;</span>Rebate
		</a>

		<c:choose>
			<c:when test="${approvedlist eq 'N'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#profile" role="tab"  onclick="submitForm('/rebatePendingForApproval');"
			data-toggle="tab"> <span class="glyphicon glyphicon-ok">&nbsp;</span>Approval
		</a>


	</ul>

			<div class="row">
				<div class="col-sm-12">
					
					
				</div>
			</div>
	<div class="tab-content">
	<sec:authorize access="hasAuthority('Add Rebate')">
						<c:if test="${approvedlist eq 'Y'}">
							<a class="btn btn-success pull-right btn_align" href="#"
								onclick="submitForm('/rebateCreation','P');"
								style="margin: -10px 15px 1px 0px;"><em class="glyphicon-plus"></em>
								<spring:message
											code="rebate.listscreen.title" /></a>
						</c:if>
					</sec:authorize>
					<div class="col-sm-12">
										<button class="btn  pull-right btn_align" 
										id="clearFilters"><spring:message code="ifsc.clearFiltersBtn" /></button>
&nbsp; <a class="btn btn-success pull-right btn_align" href="#" id="csvExport">
<spring:message code="ifsc.csvBtn" /> </a>
											<a class="btn btn-success pull-right btn_align" href="#"
												id="excelExport"  >Excel
											</a>

									</div>
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">

				<div class="row">
					<div class="col-sm-12">
						
						<div class="panel panel-default">
								<div class="panel-heading">
									<c:if test="${approvedlist eq 'Y'}">
										<strong><span class="glyphicon glyphicon-th"></span> <span
											data-i18n="Data"><spring:message
													code="rebate.rebateList" /></span></strong>
									</c:if>
									<c:if test="${approvedlist eq 'N'}">
		
										<strong><span class="glyphicon glyphicon-th"></span> <span
											data-i18n="Data"><spring:message
													code="rebate.rebateList" /></span></strong>
									</c:if>
									<c:if test="${approvedlist eq 'N'}">
									
									<sec:authorize access="hasAuthority('Approve Rebate')">
	
	
											<input type="button"
												class="btn btn-success pull-right btn_align"
												onclick="ApproveorRejectBulkRebate('A','No')" id="submitButtonA"
												value="<spring:message code="feeRate.Approve" />" />
											<input type="button"
												class="btn btn-success pull-right btn_align"
												onclick="ApproveorRejectBulkRebate('R','No')" id="submitButtonR"
												value="<spring:message code="feeRate.Reject" />" />
									</sec:authorize>
									</c:if>
								</div>

							<div class="panel-body">
						
								<div class="row">
									
								</div>
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
									<caption style="display:none;">Rebate</caption>
										<thead>
											<tr>
												<c:if test="${approvedlist eq 'N'}">
													<sec:authorize access="hasAuthority('Approve Rebate') ">
														<th scope="col"><input type=checkbox name='selectAllCheck'
															 id="selectAll" data-target="toggleModalNews" value="All"></input></th>
													</sec:authorize>
												</c:if>
												<th scope="col"><spring:message code="rebate.cardType" /></th>
												<th scope="col"><spring:message code="rebate.financialYear" /></th>
												<th scope="col"><spring:message code="rebate.featureOrBaseFee" /></th>
												<th scope="col"><spring:message code="rebate.operationalIndi" /></th>
												<th scope="col"><spring:message code="rebate.newCardCount1" /></th>
												<th scope="col"><spring:message code="rebate.newCardCount2" /></th>
												<th scope="col"><spring:message code="rebate.rebatePercentage" /></th>
												<th scope="col"><spring:message code="rebate.status" /></th>
												<c:if test="${approvedlist eq 'N'}">
												<th scope="col"><spring:message code="rebate.approverComments" /></th>
												</c:if>	
											</tr>
										</thead>
										<tbody>
											<c:forEach var="rebate" items="${rebatelist}">

												<tr>
													<c:if test="${approvedlist eq 'N'}">
														<sec:authorize access="hasAuthority('Approve Rebate') ">
															<c:if test="${rebate.requestState eq'P'}">
																	<th scope="col"><input type=checkbox name='type'
																		 id="selectSingle" onclick="mySelect();" value="${rebate.rebateID}"></input></th>
															</c:if>
															<c:if test="${rebate.requestState ne'P'}">
																	<th scope="col"> </th>
															</c:if>
														</sec:authorize>
													</c:if>
													<td  onclick="javascript:viewRebateGrid('${rebate.rebateID}','${rebate.requestState}','${approvedlist}','${originPage}')">${rebate.cardTypeName}</td>
													<td  onclick="javascript:viewRebateGrid('${rebate.rebateID}','${rebate.requestState}','${approvedlist}','${originPage}')">${rebate.financialYear}</td>
													<td  onclick="javascript:viewRebateGrid('${rebate.rebateID}','${rebate.requestState}','${approvedlist}','${originPage}')">${rebate.featureOrBaseFee}</td>
													<td  onclick="javascript:viewRebateGrid('${rebate.rebateID}','${rebate.requestState}','${approvedlist}','${originPage}')">${rebate.operatorIndi}</td>
													<td  onclick="javascript:viewRebateGrid('${rebate.rebateID}','${rebate.requestState}','${approvedlist}','${originPage}')">${rebate.newCardCount1}</td>
													<td  onclick="javascript:viewRebateGrid('${rebate.rebateID}','${rebate.requestState}','${approvedlist}','${originPage}')">${rebate.newCardCount2}</td>
													<td  onclick="javascript:viewRebateGrid('${rebate.rebateID}','${rebate.requestState}','${approvedlist}','${originPage}')">${rebate.rebatePercentage}</td>
													<td  onclick="javascript:viewRebateGrid('${rebate.rebateID}','${rebate.requestState}','${approvedlist}','${originPage}')">${rebate.status}</td>
													<c:if test="${approvedlist eq 'N'}">
													<th scope="col"  onclick="javascript:viewRebateGrid('${rebate.rebateID}','${rebate.requestState}','${approvedlist}','${originPage}')">${rebate.checkerComments}</th>
													</c:if>
													
												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>


		</div>

	</div>



</div>
