<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="org.npci.settlenxt.adminportal.repository.UserRepository">
	
	<select id="getUserList" resultType="UserDTO">
		SELECT   DISTINCT UD.LOGIN_ID as loginId,UD.FIRST_NAME as firstName,
		UD.MIDDLE_NAME as middleName,UD.LAST_NAME as lastName,UD.EMAIL as emailId,
		UD.MOBILE_NO as mobileNo,UD.created_on as createdOn,UD.created_on as createdDate,UD.USER_ID as userId,
		UD.EMP_ID as empId,UD.STATUS as status,UD.CREATED_BY as createdBy,
		UD.LAST_UPDATED_BY as lastUpdatedBy,UD.LAST_UPDATED_ON as lastUpdatedOn,
		UD.USER_TYPE as userType,UD.LAST_LOGIN_TS as lastLoginTs,UD.BANK_NAME as bankName,
		US.REQUEST_STATE as requestState,UD.CONTACT_NO as contactNo,UD.LOCK_STATUS as lockStatus
		FROM USER_STG US, USER_DETAIL UD  WHERE US.LOGIN_ID=UD.LOGIN_ID and US.USER_TYPE = #{userType} and  UD.LOGIN_ID!=#{userName}  and (US.REQUEST_STATE=#{status}) ORDER BY UD.last_updated_on DESC
	</select>
	
		
	<select id="getUsersPendingForApproval" resultType="UserDTO">
		SELECT R.STATUS as status,  R.LOCK_STATUS as lockStatus, R.USER_ID as userId,R.MOBILE_NO as mobileNo, R.LOGIN_ID as loginId, R.FIRST_NAME as firstName, R.LAST_NAME as lastName, R.CHECKER_COMMENTS as checkerComments,R.LAST_OPERATION as lastOperation, R.REQUEST_STATE as requestState,R.EMAIL as emailId, R.CREATED_ON as createdOn,R.CREATED_BY as createdBy,R.LAST_UPDATED_BY as lastUpdatedBy,R.LAST_UPDATED_ON as lastUpdatedOn FROM USER_STG R WHERE R.REQUEST_STATE in   <foreach item='item' index='index' collection='requestStateList' open='(' separator=',' close=')'>#{item}</foreach> and  R.USER_TYPE=#{userType} and R.LOGIN_ID!=#{userName}    ORDER BY last_updated_on DESC
	</select>
	
	<select id="getUserAccessHierarchy" resultType="String">
		Select access_level as accessLevel from user_hierarchy where hierarchy_id=#{userType}
	</select>
	
	<select id="getUserAccessHierarchyList" resultType="CodeValueDTO">
		Select user_hierarchy as description,hierarchy_id as code from user_hierarchy where hierarchy_id in   <foreach item='item' index='index' collection='userTypeList' open='(' separator=',' close=')'>#{item}</foreach>
	</select>
	
	<select id="getBulkUserInfoByUserIdList" resultType="UserDTO">
		select distinct ( rus.user_id) as userId, rus.login_id as loginId, rus.first_name as firstName, 
		rus.last_name as lastName,rus.MIDDLE_NAME as middleName, rus.street_address as streetAddress, 
		rus.city as cityId, rus.state as stateId, rc.city_name as cityName, rs.state_name as stateName, 
		rus.pincode as pincode,  rus.email as emailId, rus.mobile_no as mobileNo, rus.contact_no as contactNo, 
		rus.emp_id as empId, rus.status as status, rus.created_by as createdBy, rus.created_on as createdOn, 
		rus.last_updated_by as lastUpdatedBy, rus.last_updated_on as lastUpdatedOn, rus.user_type as userType,
		 rus.bank_participant_id as bankParticipantId,  rus.last_login_ts as lastLoginTs,
		  rus.checker_comments as checkerComments, rus.request_state as requestState,rus.maker_checker_flag as makChkFlag,
		  rus.lock_status as lockStatus,rus.ispwd_change_req as isPwdChangeReq, rus.salutation as salutation ,
		  rus.last_operation as lastOperation,rus.bank_name as bankName,rus.access_level as accessLevel 
		   FROM user_stg rus inner join user_role_mapping_stg rurms on rus.user_id =rurms.user_id inner join role rr on rr.role_id =rurms.role_id LEFT OUTER JOIN city RC ON (RC.CITY_ID = rus.CITY) LEFT OUTER JOIN state RS ON (RS.STATE_ID = rus.STATE) WHERE rus.user_id in  <foreach item='item' index='index' collection='list' open='(' separator=',' close=')'>#{item}</foreach>
	</select>
	
	<update id="updateUserPsswd">
		update user_detail set status=#{status} ,passwd=#{hashAsInfo},ISPWD_CHANGE_REQ=#{isPwdChangeReq},LAST_UPDATED_ON=now() where login_id=#{loginId}
	</update>
	
	
	
	
	<select id="getUser" resultType="UserDTO">
		SELECT RU.USER_ID as userId, RU.LOGIN_ID as userName, RU.FIRST_NAME as firstName, 
		RU.LAST_NAME as lastName, RU.MIDDLE_NAME as middleName, RU.STREET_ADDRESS as streetAddress, 
		RU.CITY as cityId, RU.STATE as stateId, RU.PINCODE as pincode, RU.EMAIL as emailId, 
		RU.MOBILE_NO as mobileNo, RU.CONTACT_NO as contactNo, RU.EMP_ID as empId,RU.STATUS as status,
		 RU.CREATED_BY as createdBy, RU.CREATED_ON as createdOn, RU.LAST_UPDATED_BY as lastUpdatedBy, 
		 RU.LAST_UPDATED_ON as lastUpdatedOn, RU.USER_TYPE as userType, RU.BANK_PARTICIPANT_ID as bankParticipantId,
		  RU.BANK_NAME as bankName , RU.maker_checker_flag as makChkFlag, RU.LOCK_STATUS as lockStatus, 
		  RU.LAST_LOGIN_TS as lastLoginTs, RU.PASSWD as passwd, RU.SECRET_ID as secretId,
		   RU.SECRET_ANSWER as secretAnswer, RU.INVALID_LOGIN_ATTEMPTS as invalidLoginAttempts, 
		   RU.ISPWD_CHANGE_REQ as isPwdChangeReq, RU.MIDDLE_NAME as middleName, RU.SALUTATION as salutation, 
		   RU.ACCESS_LEVEL as accessLevel, RC.CITY_NAME as cityName, RS.STATE_NAME as stateName 
		   FROM USER_DETAIL RU LEFT OUTER JOIN CITY RC ON (RC.CITY_ID = RU.CITY) LEFT OUTER JOIN STATE RS ON (RS.STATE_ID = RU.STATE) WHERE upper(LOGIN_ID)= #{loginId}
	</select>
	
	<update id="updateLoginAttemtps">
		UPDATE USER_DETAIL SET INVALID_LOGIN_ATTEMPTS=#{invalidLoginAttempts} WHERE USER_ID= #{userId}
	</update>
	
	<update id="lockUser">
		UPDATE USER_DETAIL SET INVALID_LOGIN_ATTEMPTS=#{invalidLoginAttempts},  LOCK_STATUS =#{lockStatus} WHERE USER_ID= #{userId}
	</update>
	
	<update id="updateLoginTimestamp">
		UPDATE USER_DETAIL SET LAST_LOGIN_TS=#{localDateTime} ,  INVALID_LOGIN_ATTEMPTS=0 WHERE USER_ID= #{userId}
	</update>
	
	<select id="getRoleAndFunctions" resultType="RoleFunctionalityDTO">
		SELECT role.role_id as roleID, role.role_name as roleName, role.role_desc as roleDesc,
		 func.func_name as funcName , func.func_desc as funcDesc FROM role role, functionality func,
		 role_func_mapping role_func,user_role_mapping user_role 
		 WHERE user_role.user_id= #{userId} AND role.role_id=user_role.role_id AND role.role_id=role_func.role_id AND func.func_id= role_func.func_id
	</select>
	
	<select id="getUserForEditById" resultType="UserInfoDTO">
		SELECT DISTINCT USER_ID as userId,LOGIN_ID as loginId,FIRST_NAME as firstName,LAST_NAME as lastName,MIDDLE_NAME as middleName,STREET_ADDRESS as streetAddress,CITY as cityId,STATE as stateId,PINCODE as pincode,EMAIL as emailId,MOBILE_NO as mobileNo,CONTACT_NO as contactNo,EMP_ID as empId,STATUS as status,CREATED_BY as createdBy,CREATED_ON as createdOn ,LAST_UPDATED_BY as lastUpdatedBy,LAST_UPDATED_ON as lastUpdatedOn , USER_TYPE as userType,BANK_PARTICIPANT_ID as bankParticipationId ,LOCK_STATUS as lockStatus,LAST_LOGIN_TS as lastLoginTs,PASSWD as passwd,SECRET_ID as secretId, SECRET_ANSWER as secretAnswer,INVALID_LOGIN_ATTEMPTS as InvalidLoginAttempts, BANK_NAME  as bankName,EMPLOYEE_ID as employeeId,MAKER_CHECKER_FLAG as makChkFlag, ISPWD_CHANGE_REQ as isPwdChangeReq,salutation as salutation ,access_level as accessLevel FROM USER_DETAIL WHERE USER_ID = #{userId}
	</select>
	
	<select id="getRejectedUserForEditById" resultType="UserInfoDTO">
		SELECT DISTINCT (USER_ID) as userId,LOGIN_ID as loginId,FIRST_NAME as firstName,LAST_NAME as lastName,MIDDLE_NAME as middleName,STREET_ADDRESS as streetAddress,CITY as cityId,STATE as stateId,PINCODE as pincode,EMAIL as emailId,MOBILE_NO as mobileNo,CONTACT_NO as contactNo,EMP_ID as empId,STATUS as status,CREATED_BY as createdBy,CREATED_ON as createdOn,LAST_UPDATED_BY as lastUpdatedBy,LAST_UPDATED_ON as lastUpdatedOn,USER_TYPE as userType,BANK_PARTICIPANT_ID as bankParticipationId,LOCK_STATUS as lockStatus,LAST_LOGIN_TS as lastLoginTs  ,BANK_NAME as bankName  ,MAKER_CHECKER_FLAG as makChkFlag, ISPWD_CHANGE_REQ as isPwdChangeReq,REQUEST_STATE as requestState,ACCESS_LEVEL as accessLevel,salutation as salutation  FROM USER_STG WHERE USER_ID = #{userId} and REQUEST_STATE=#{status}
	</select>
	
	<select id="fetchUserRoleList" resultType="UserToRoleDTO">
		select  RFM.USER_ID as userId, R.login_id as loginId, RFM.ROLE_ID as roleId,FU.role_name as roleName,RFM.STATUS as status, FU.MAKER_CHECKER_FLAG as makChkFlag FROM user_role_mapping  RFM INNER JOIN USER_DETAIL R ON (RFM.USER_ID = R.USER_ID) INNER JOIN role FU  ON (FU.ROLE_ID = RFM.ROLE_ID) WHERE RFM.USER_ID = #{userId}
	</select>
	
	<select id="getUserProfile" resultType="UserDTO">
		select R.USER_ID as userId, R.login_id as userName,R.FIRST_NAME as firstName, R.LAST_NAME as lastName,
		 R.MIDDLE_NAME as middleName, R.STREET_ADDRESS as streetAddress, RC.CITY_NAME as cityName, 
		 RS.STATE_NAME as stateName, R.PINCODE as pincode,  R.EMAIL as emailId, R.MOBILE_NO as mobileNo,
		  RFM.ROLE_ID as roleId,FU.role_name as roleName,FU.MAKER_CHECKER_FLAG as makChkFlag,
		  R.CONTACT_NO as contactNo, R.EMP_ID as empId, R.SALUTATION as salutation, R.STATUS as status,
		   R.CREATED_BY as createdBy, R.CREATED_ON as createdOn, R.LAST_UPDATED_BY as lastUpdatedBy, 
		   R.LAST_UPDATED_ON as lastUpdatedOn, R.USER_TYPE as userType,R.BANK_PARTICIPANT_ID as bankParticipantId,
		   ud.LOCK_STATUS as lockStatus, R.LAST_LOGIN_TS as lastLoginTs,R.REQUEST_STATE as requestState,
		   R.ISPWD_CHANGE_REQ as isPwdChangeReq,R.access_level as accessLevel,R.bank_name as bankName,
		   R.status as status FROM user_detail ud,user_role_mapping_stg RFM INNER JOIN user_stg R ON (RFM.USER_ID = R.USER_ID) LEFT OUTER JOIN city RC ON (RC.CITY_ID = R.CITY) LEFT OUTER JOIN state RS ON (RS.STATE_ID = R.STATE) INNER JOIN role_stg FU ON (FU.ROLE_ID = RFM.ROLE_ID) WHERE RFM.USER_ID = #{userId} and r.user_id= ud.user_id
	</select>
	
	<select id="getUnmappedRoleNotInWorkflowForUserEdit" resultType="RoleDTO">
		SELECT DISTINCT RR.ROLE_ID as roleId,RR.ROLE_NAME as roleName FROM ROLE RR WHERE RR.ROLE_ID NOT IN( SELECT RU.ROLE_ID as roleId FROM USER_ROLE_MAPPING_STG RU WHERE RU.USER_ID = #{userId}) AND RR.ROLE_TYPE =#{userType} AND RR.MAKER_CHECKER_FLAG =#{makerChecker}
	</select>
	
	<select id="getUnmappedRoleNotInWorkflowForUserAdd" resultType="RoleDTO">
		SELECT DISTINCT RR.ROLE_ID as roleId,RR.ROLE_NAME as roleName FROM ROLE RR LEFT OUTER JOIN USER_ROLE_MAPPING RU ON RR.ROLE_ID =RU.ROLE_ID WHERE RR.ROLE_TYPE =#{userType}  AND RR.MAKER_CHECKER_FLAG =#{makerChecker}
	</select>
	
	<select id="getMappedRolesByUserId" resultType="UserToRoleDTO">
		SELECT RU.USER_ID as userId,RU.LOGIN_ID as loginId,RU.MIDDLE_NAME as middleName,RU.LOCK_STATUS as lockStatus,RU.STATUS  as status,RR.ROLE_ID as roleId,RR.ROLE_NAME as roleName FROM USER_DETAIL RU LEFT OUTER JOIN USER_ROLE_MAPPING RURM ON RU.USER_ID =RURM.USER_ID LEFT OUTER JOIN ROLE RR ON RR.ROLE_ID =RURM.ROLE_ID WHERE RU.USER_ID =#{userId}
	</select>
	
	<select id="fetchIdFromUserIdSequence" resultType="int">
		SELECT nextval('user_user_id_seq')
	</select>
	
	<insert id="insertUserStg">
		INSERT INTO user_stg(user_id, login_id, first_name, last_name, middle_name, street_address, city, state,pincode,  email, mobile_no, contact_no, emp_id, status, created_by, created_on, last_updated_by, last_updated_on, user_type, bank_participant_id, last_login_ts, maker_comments, checker_comments, request_state,maker_checker_flag, lock_status, ispwd_change_req,bank_name,LAST_OPERATION,salutation, access_level) VALUES (#{userId}, #{loginId}, #{firstName} , #{lastName}, #{middleName}, #{streetAddress}, #{cityId},#{stateId},#{pincode}, #{emailId}, #{mobileNo} , #{contactNo}, #{empId} ,#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{userType},#{participantId},#{lastLoginTs},#{makerComments},#{checkerComments},#{requestState} ,#{makerChecker} ,#{lockStatus} ,#{isPwdChangeReq},#{bankName},#{lastOperation},#{salutation}, #{accessLevel})
	</insert>
	
	<insert id="saveUser">
		INSERT INTO USER_DETAIL(user_id, login_id, first_name, last_name, middle_name, street_address, city, state,pincode, email, mobile_no, contact_no, emp_id,  status,created_by, created_on, last_updated_by, last_updated_on, user_type, bank_participant_id, lock_status,last_login_ts, secret_answer, invalid_login_attempts, bank_name, employee_id,maker_checker_flag,ispwd_change_req,salutation,access_level,passwd) VALUES (#{userId}, #{loginId}, #{firstName} , #{lastName}, #{middleName}, #{streetAddress},#{cityId},#{stateId},#{pincode}, #{emailId}, #{mobileNo} , #{contactNo}, #{empId},#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{userType},#{bankParticipantId},#{lockStatus},#{lastLoginTs},#{secretAnswer},#{invalidLoginAttempts},#{bankName},#{employeeId},#{makChkFlag},#{isPwdChangeReq},#{salutation},#{accessLevel},#{hashAsInfo})
	</insert>
	
	<insert id="saveUserActivityLog">
		INSERT INTO user_activity(login_id,user_ip,request_start_time,request_end_time,request_url,portal_base_path,activity) VALUES (#{loginId}, #{userIp}, #{requestStartTime} , #{requestEndTime}, #{requestURL}, #{portalBasePath}, #{activity})
	</insert>
	
	<select id="validateLoginIDStg" resultType="int">
		SELECT count(DISTINCT USER_ID) as userId FROM USER_STG WHERE UPPER(LOGIN_ID) = UPPER(#{loginId}) AND request_state=#{status}
	</select>
	
	<insert id="insertBatchUserRoletoFunc">
		insert into user_role_mapping_stg (USER_ID,ROLE_ID,STATUS,CREATED_BY,CREATED_ON,LAST_UPDATED_BY,LAST_UPDATED_ON)
		values <foreach  collection='userInfoDto.roleList' item='roleDTO' separator=','>
		(#{userInfoDto.userId,jdbcType=INTEGER}, #{roleDTO.roleId,jdbcType=INTEGER}, #{userInfoDto.status,jdbcType=VARCHAR},
		#{userInfoDto.createdBy,jdbcType=VARCHAR}, #{roleDTO.createdOn,jdbcType=TIMESTAMP},
		#{roleDTO.lastUpdatedBy,jdbcType=VARCHAR}, #{roleDTO.lastUpdatedOn,jdbcType=TIMESTAMP})</foreach>
	</insert>
	
	<select id="getPendingRolesForApproving" resultType="RoleDTO">
		SELECT RR.ROLE_ID as roleId,RR.ROLE_NAME as roleName,RR.ROLE_DESC as roleDesc  FROM ROLE RR , USER_ROLE_MAPPING_STG RURMS WHERE RR.ROLE_ID =RURMS.ROLE_ID AND RURMS.USER_ID = #{userId}
	</select>
	
	<select id="validateLoginID" resultType="int">
		SELECT COUNT(USER_ID) as userId FROM USER_DETAIL WHERE UPPER(LOGIN_ID) = UPPER(#{loginId})
	</select>
	
	<update id="updateUserChangePassword">
		UPDATE USER_DETAIL SET PASSWD=#{newPasswd}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON=#{lastUpdatedOn} WHERE LOGIN_ID= #{userName}
	</update>
	
	<update id="updateUserSecretQuestion">
		UPDATE USER_DETAIL SET SECRET_ID=#{secretId}, SECRET_ANSWER=#{secretAnswer}, LAST_UPDATED_BY=#{lastUpdatedBy},LAST_UPDATED_ON=#{lastUpdatedOn} WHERE USER_ID= #{userId}
	</update>
	
	<update id="updateUserRoleStgStatus">
		UPDATE USER_ROLE_MAPPING_STG SET STATUS = #{status}, LAST_UPDATED_BY = #{lastUpdatedBy} WHERE user_id = #{userId}
	</update>
	
	<select id="getUserInfoByUserId" resultType="UserDTO">
		SELECT DISTINCT USER_ID as userId,LOGIN_ID as userId,FIRST_NAME as firstName,LAST_NAME as lastName,
		MIDDLE_NAME as middleName,STREET_ADDRESS as streetAddress,CITY as cityId,STATE as stateId,
		PINCODE as pincode,EMAIL as emailId,MOBILE_NO as mobileNo,CONTACT_NO as contactNo,EMP_ID as empId,
		STATUS as status,CREATED_BY as createdBy,CREATED_ON as createdOn,LAST_UPDATED_BY as lastUpdatedBy,
		LAST_UPDATED_ON as lastUpdatedOn,USER_TYPE as userType,BANK_PARTICIPANT_ID as bankParticipantId,
		LOCK_STATUS as lockStatus,LAST_LOGIN_TS as lastLoginTs,PASSWD as passwd,SECRET_ID as secretId, 
		SECRET_ANSWER as secretAnswer,INVALID_LOGIN_ATTEMPTS as invalidLoginAttempts,BANK_NAME as bankName,
		EMPLOYEE_ID employeeId,MAKER_CHECKER_FLAG as makChkFlag, ISPWD_CHANGE_REQ as isPwdChangeReq,
		salutation as salutation,access_level as accessLevel FROM USER_DETAIL WHERE USER_ID = #{userId}
	</select>
	
	
	<update id="updateUser">
		UPDATE USER_DETAIL SET  FIRST_NAME=#{firstName},salutation=#{salutation}, LAST_NAME=#{lastName}, MIDDLE_NAME=#{middleName}, user_type=#{userType}, mobile_no=#{mobileNo},contact_no=#{contactNo},  STATUS=#{status}, LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn} ,EMP_ID=#{empId}, EMAIL=#{emailId}, pincode=#{pincode},STREET_ADDRESS=#{streetAddress}, STATE=#{stateId}, CITY=#{cityId}, ACCESS_LEVEL = #{accessLevel} WHERE USER_ID = #{userId}
	</update>
	
	<select id="getAvailableUsertoRoleCountByUserId" resultType="long">
		SELECT count(*) FROM USER_ROLE_MAPPING RRFM WHERE RRFM.USER_ID = #{userId}
	</select>
	
	<delete id="deleteUserRoleMapping">
		DELETE FROM user_role_mapping WHERE USER_ID =#{userId}
	</delete>
	
	<select id="getUserRoleStgMappingByUserId" resultType="UserToRoleDTO">
		SELECT RRFM.USER_ID as userId, RRFM.ROLE_ID as roleId, RRFM.CREATED_BY as createdBy, RRFM.CREATED_ON as createdOn, RRFM.LAST_UPDATED_BY as lastUpdatedBy, RRFM.LAST_UPDATED_ON as lastUpdatedOn, RRFM.STATUS as status FROM USER_ROLE_MAPPING_STG RRFM WHERE RRFM.USER_ID = #{userId}
	</select>
	
	<insert id="saveUserRoleMapping">
		INSERT INTO USER_ROLE_MAPPING( ROLE_ID,USER_ID,STATUS, CREATED_BY, CREATED_ON, LAST_UPDATED_BY, LAST_UPDATED_ON) VALUES(#{roleId}, #{userId}, #{status}, #{createdBy}, #{createdOn}, #{lastUpdatedBy}, #{lastUpdatedOn})
	</insert>
	
	<select id="getUserRoleStgByUserId" resultType="RoleDTO">
		SELECT RRFM.user_id as userId,RRFM.ROLE_ID as roleId,RF.ROLE_NAME as roleName,RF.ROLE_DESC as roleDesc, RRFM.CREATED_BY as createdBy, RRFM.CREATED_ON as createdOn, RRFM.LAST_UPDATED_BY as lastUpdatedBy, RRFM.LAST_UPDATED_ON as lastUpdatedOn, RRFM.STATUS as status FROM user_role_mapping_stg RRFM , role RF WHERE RRFM.role_id  = RF.ROLE_ID AND RRFM.USER_ID =#{userId}
	</select>
	
	<select id="verifyUserSecretAnswer" resultType="UserDTO">
		SELECT distinct user_id as userId,email as emailId FROM USER_DETAIL WHERE LOGIN_ID=#{loginId} AND SECRET_ANSWER=#{secretAnswer}
	</select>
	
	<update id="updateUserResetPassword">
		UPDATE USER_DETAIL SET PASSWD=#{newPasswd}, LAST_UPDATED_BY=#{lastUpdatedBy},
		 LAST_UPDATED_ON=#{lastUpdatedOn}, ISPWD_CHANGE_REQ=#{isPwdChangeReq},status=#{status} 
		 WHERE LOGIN_ID= #{userName}
	</update>
	
	<update id="updateActivateDeactivateUser">
		Update USER_DETAIL SET status=#{status}, LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn} WHERE USER_ID = #{userId}
	</update>
	
	<update id="updateLockUnlockUser">
		Update USER_DETAIL SET LOCK_STATUS =#{lockStatus},STATUS =#{status}, LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn} WHERE USER_ID = #{userId}
	</update>
	
	<update id="resetPwd">
		UPDATE USER_DETAIL SET PASSWD=#{hashAsInfo},ISPWD_CHANGE_REQ = #{isPwdChangeReq},
		status=#{status},last_updated_on=now() WHERE USER_ID = #{userId}
	</update>
	
	<select id="getEmailContent" resultType="EmailGateWayDTO">
		SELECT EMAIL_CONTENT as emailContent, SUBJECT as subject,purpose_id as purposeId,TO_EMAIL as toEmail,email_temp_id as emailTempId from TEMP_EMAIL WHERE PURPOSE_CODE=#{purposeCode}
	</select>
	
	<select id="getNextSequenceId" resultType="int">
		select NEXTVAL('EMAILLOG_REQID_SEQ')
	</select>
	
	<insert id="saveEmailSmsLogEncrypted">
		INSERT INTO EMAIL_SMS_LOG(REQ_ID,REQ_DATE,TO_EMAIL,PURPOSE_CODE,SUBJECT,EMAIL_CONTENT,PWD_EMAIL,EMAIL_STATUS_CODE,EMAIL_STATUS_DESC,EMAIL_TEMP_ID,REF_NO) VALUES (#{reqID}, #{crtDate}, encode(PUBLIC.ENCRYPT(#{receiverEmailID}::bytea,'4D5390BEF3EF1EE3D4A7E77FD42238CB','AES'),'hex'), #{purpose}, #{subject}, #{emailContent}, #{isPwdEmail},#{status},#{statusDesc},#{purposeId},#{userId})
	</insert>
	
	<insert id="saveEmailSMSLOG">
		INSERT INTO EMAIL_SMS_LOG(REQ_ID,REQ_DATE,TO_EMAIL,PURPOSE_CODE,SUBJECT,EMAIL_CONTENT,PWD_EMAIL,EMAIL_STATUS_CODE,EMAIL_STATUS_DESC,EMAIL_TEMP_ID,REF_NO) VALUES (#{reqID}, #{crtDate}, #{receiverEmailID}, #{purpose}, #{subject}, #{emailContent}, #{isPwdEmail},#{status},#{statusDesc},#{purposeId},#{userId})
	</insert>
	
	<select id="getRejectedMappedRolesByUserId" resultType="UserToRoleDTO">
		SELECT RU.USER_ID as userId,RU.LOGIN_ID as loginId,RU.MIDDLE_NAME as middleName,RU.LOCK_STATUS as lockStatus ,RU.STATUS as status ,RR.ROLE_ID as roleId,RR.ROLE_NAME as roleName FROM USER_STG RU LEFT OUTER JOIN USER_ROLE_MAPPING_STG RURM ON RU.USER_ID =RURM.USER_ID LEFT OUTER JOIN ROLE_STG RR ON RR.ROLE_ID =RURM.ROLE_ID WHERE RU.USER_ID =#{userId}
	</select>
	
	<select id="getRoleUserMappingByUserId" resultType="UserToRoleDTO">
		SELECT RRFM.ROLE_ID as roleId,RRFM.USER_ID as userId, RRFM.CREATED_BY as createdBy, RRFM.CREATED_ON as createdOn, RRFM.LAST_UPDATED_BY as lastUpdatedBy, RRFM.LAST_UPDATED_ON as lastUpdatedOn, RRFM.STATUS as status FROM user_role_mapping RRFM WHERE RRFM.user_id = #{userId}
	</select>
	
	<update id="updateUserStgState">
		UPDATE USER_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments},LOCK_STATUS=#{lockStatus} WHERE USER_ID =  #{userId}
	</update>
	
	<update id="updateUserStgOperation">
		UPDATE USER_STG SET LAST_UPDATED_BY = #{lastUpdatedBy},LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} , LAST_OPERATION= #{lastOperation} WHERE user_id = #{userId}
	</update>
	
	<delete id="deleteUserRoleMappingStg">
		DELETE FROM user_role_mapping_stg WHERE USER_ID =#{userId}
	</delete>
	
	<delete id="deleteUserStg">
		DELETE FROM user_stg WHERE USER_ID =#{userId}
	</delete>
	
	<update id="updateUserStgOpr">
		UPDATE USER_STG SET LAST_UPDATED_BY = #{lastUpdatedBy},salutation=#{salutation}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} , LAST_OPERATION=#{lastOperation}, FIRST_NAME=#{firstName}, LAST_NAME=#{lastName}, user_type=#{userType}, STATUS=#{status},middle_name=#{middleName},city=#{cityId}, state=#{stateId},pincode=#{pincode},email=#{emailId},contact_no=#{contactNo},mobile_no=#{mobileNo}, emp_id= #{empId},street_address= #{streetAddress},ACCESS_LEVEL = #{accessLevel}  WHERE user_id =  #{userId}
	</update>
	
<select id="fetchBinDetailsByBinIds" resultType="BinDTO">
SELECT BIN_ID as binId,ACQUIRER_ID as binNumber, PARTICIPANT_ID as participantId,SETTLEMENT_BIN as settlementBin,
bin_card_type as binCardType,bin_type as binType,bin_product_type as binProductType,
product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin 
FROM membin_details WHERE status='A' and  BIN_ID IN <foreach item='item' index='index' collection='intList' open='(' separator=',' close=')'>#{item}</foreach>
</select>

<!-- <select id="getPswdHistory" resultType="UserDTO">
select created_on as createdOn, user_id as userId from (select user_id as userId, created_on as createdOn,row_number() over(partition by user_id order by created_on desc) as rn from password_history) as t where t.rn = 1 and user_id =#{userId}
</select> -->

<select id="getPswdHistory" resultType="UserDTO">
select created_on as createdOn, user_id as userId from (select user_id, created_on,row_number() over(partition by user_id order by created_on desc) as rn from password_history) as t where t.rn = 1 and user_id =#{userId}
</select>

<select id="getAcqListByUserIdForView" resultType="BinDTO">
SELECT RUSB.BIN_ID as binId, RUSB.PARTICIPANT_ID as participantId,RUSB.USER_ID as userId,RMD.ACQUIRER_ID as binNumber FROM USER_BIN RUSB, membin_details RMD 
WHERE RUSB.BIN_ID=RMD.BIN_ID and USER_ID = #{userId} AND  BIN_TYPE =#{binType} and RMD.status !='D'
</select>

<select id="getBinListByUserIdForView" resultType="BinDTO">
SELECT RUSB.BIN_ID as binId, RUSB.PARTICIPANT_ID as participantId ,RUSB.USER_ID as userId,RMD.BIN_NUMBER as binNumber FROM USER_BIN RUSB, membin_details RMD 
WHERE RUSB.BIN_ID=RMD.BIN_ID and USER_ID = #{userId} AND BIN_TYPE =#{binType} and RMD.status !='D'
</select>

<select id="getUserProfileForView" resultType="UserDTO">
select R.USER_ID as userId, R.login_id as userName,R.FIRST_NAME as firstName, R.LAST_NAME as lastName, R.MIDDLE_NAME as middleName, R.STREET_ADDRESS as streetAddress, RC.CITY_NAME as cityName, RS.STATE_NAME as stateName, R.PINCODE as pincode, R.EMAIL as emailId, R.MOBILE_NO as mobileNo, RFM.ROLE_ID as roleId,FU.role_name as roleName ,
			R.MAKER_CHECKER_FLAG as makChkFlag,R.CONTACT_NO as contactNo, R.EMP_ID as empId, R.SALUTATION as salutation, R.STATUS as status, R.CREATED_BY as createdBy, R.CREATED_ON as createdOn, R.LAST_UPDATED_BY as lastUpdatedBy, R.LAST_UPDATED_ON as lastUpdatedOn, R.USER_TYPE as userType,
			 R.BANK_PARTICIPANT_ID as bankParticipantId,R.LOCK_STATUS as lockStatus, R.LAST_LOGIN_TS as lastLoginTs,R.ISPWD_CHANGE_REQ as ispwdChangeReq,R.access_level as accessLevel,R.bank_name as bankName,R.status as status FROM user_detail R inner join user_role_mapping RFM on R.user_id=RFM.user_id  
			 LEFT OUTER JOIN city RC ON (RC.CITY_ID = R.CITY)
			 LEFT OUTER JOIN state RS ON (RS.STATE_ID = R.STATE) 
			INNER JOIN role FU ON (FU.ROLE_ID = RFM.ROLE_ID) WHERE RFM.USER_ID = #{userId}
</select>

<select id="bankNameList" resultType="UserDTO">
select u.login_id as loginId   from user_detail u ,user_stg us where
			   u.user_id=us.user_id  and u.status!='D' and  u.bank_participant_id in <foreach item='item' index='index' collection='bankNameList' open='(' separator=',' close=')'>#{item}</foreach>  AND u.maker_checker_flag in <foreach item='item' index='index' collection='roleTypeList' open='(' separator=',' close=')'>#{item}</foreach> 
</select>

<select id="getRoleTypes" resultType="RoleDTO">
Select distinct(maker_checker_flag) as makerCheckerflag from role
</select>

<select id="checkIfUserExists" resultType="long">
select count(*) from USER_DETAIL where user_id = #{userId}
</select>
<update id="updateUserStgStatus" >
UPDATE USER_STG SET status=#{status},LAST_UPDATED_BY=#{lastUpdatedBy}, 
LAST_UPDATED_ON=#{lastUpdatedOn} WHERE LOGIN_ID= #{userName}
</update>
<select id="getBankName" resultType="String">
Select bank_name as bankName from PARTICIPANT where participant_id=#{participantId}
</select>

<select id="getCountFromParticpant" resultType="int">
SELECT DISTINCT max_user_count as maxUserCount FROM PARTICIPANT where participant_id=#{participantId} 
</select>

<select id="userDetailCount" resultType="int">
SELECT COUNT(USER_ID) as userId FROM USER_DETAIL WHERE BANK_PARTICIPANT_ID=#{participantId}
</select>

<select id="getMaxUserStgCountList" resultType="int">
select count(user_id) as userId from user_stg where BANK_PARTICIPANT_ID=#{participantId} and request_state  in <foreach item='item' index='index' collection='requestState' open='(' separator=',' close=')'>#{item}</foreach>
</select>

<!-- <select id="getPasswordHistory" resultType="UserDTO">
select created_on as createdOn, user_id as userId from (select user_id as userId, created_on as createdOn ,row_number() over(partition by user_id order by created_on desc) as rn from password_history ) as t where t.rn = 1 and user_id =#{userId}
</select> -->

<select id="getPasswordHistory" resultType="UserDTO">
select t.created_on as createdOn,t.user_id as userId from password_history t where  t.user_id =#{userId} order by created_on desc limit 1 
</select>

<select id="getBinIssListByUserId" resultType="BinDTO">
SELECT RUSB.BIN_ID as binId, RUSB.PARTICIPANT_ID as participantId,RUSB.USER_ID as userId,RMD.BIN_NUMBER as binNumber,SETTLEMENT_BIN as settlementBin,rusb.created_on as createdOn,rusb.last_updated_on as lastUpdatedOn,bin_type as binType,bin_card_variant as binCardVariant,bin_card_type as binCardType,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin  
FROM USER_BIN_STG RUSB, membin_details RMD WHERE RUSB.BIN_ID=RMD.BIN_ID and USER_ID = #{userId} AND status='A' and  BIN_TYPE =#{binType} and RMD.status !='D'
</select>

<select id="getBinAcqListByUserId" resultType="BinDTO">
SELECT RUSB.BIN_ID as binId, RUSB.PARTICIPANT_ID as participantId,RUSB.USER_ID as userId,RMD.ACQUIRER_ID as binNumber,SETTLEMENT_BIN as settlementBin,rusb.created_on as createdOn,rusb.last_updated_on as lastUpdatedOn,bin_type as binType,bin_card_variant as binCardVariant,bin_card_type as binCardType,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin  
FROM USER_BIN_STG RUSB, membin_details RMD WHERE RUSB.BIN_ID=RMD.BIN_ID and USER_ID = #{userId} AND status='A' and  BIN_TYPE =#{binType} and  RMD.status !='D'
</select>

<update id="passwordResetFlagMain">
UPDATE user_detail set ispwd_change_req=#{isPwdChangeReq},status=#{status},last_updated_on=now() where user_id=#{userId}
</update>

<update id="passwordResetFlag">
UPDATE user_stg set status=#{status},last_updated_on=now() where user_id=#{userId}
</update>

<update id="resetUserStatusStg">
UPDATE USER_STG SET status=#{status},LAST_UPDATED_ON = now() WHERE USER_ID = #{userId}
</update>

<update id="updateUserStg">
UPDATE USER_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, 
REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments},LOCK_STATUS=#{lockStatus},salutation=#{salutation},LAST_OPERATION=#{lastOperation}, FIRST_NAME=#{firstName}, LAST_NAME=#{lastName}, user_type=#{userType}, CREATED_BY=#{createdBy}, CREATED_ON=#{createdOn}, STATUS=#{status},
middle_name=#{middleName},city=#{cityId}, state=#{stateId},pincode=#{pincode},email=#{emailId},
contact_no=#{contactNo},mobile_no=#{mobileNo},emp_id= #{empId},street_address= #{streetAddress},
ACCESS_LEVEL = #{accessLevel}  WHERE user_id =  #{userId}
</update>

<select id="getAcqListByUserId" resultType="BinDTO">
SELECT RUSB.BIN_ID as binId, RUSB.PARTICIPANT_ID as participantId,RUSB.USER_ID as userId,RMD.ACQUIRER_ID as binNumber,SETTLEMENT_BIN settlementBin,rusb.created_on as createdOn,rusb.last_updated_on as lastUpdatedOn,bin_type as binType,bin_card_variant as binCardVariant,bin_card_type as binCardType,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin  
FROM USER_BIN_STG RUSB, membin_details RMD WHERE RUSB.BIN_ID=RMD.BIN_ID and USER_ID = #{userId} AND status='A' and  BIN_TYPE =#{binType} and  RMD.status !='D'
</select>

<select id="getBinListByUserId" resultType="BinDTO">


	SELECT RUSB.BIN_ID as binId, RUSB.PARTICIPANT_ID as participantId,RUSB.USER_ID as userId,RMD.BIN_NUMBER as binNumber,SETTLEMENT_BIN as settlementBin,bin_type as binType,
	bin_card_variant as binCardVariant,bin_product_type as binProductType,bin_card_type as binCardType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin
 	FROM USER_BIN_STG RUSB, membin_details RMD WHERE RUSB.BIN_ID=RMD.BIN_ID and status='A' and  USER_ID = #{userId} AND BIN_TYPE =#{binType} and RMD.status !='D'

</select>

<select id="getBinDetailListByUserId" resultType="BinDTO"> 
SELECT DISTINCT BIN_ID as binId, PARTICIPANT_ID as participantId,USER_ID as userId, created_on as createdOn, created_by as createdBy,
last_updated_on as lastUpdatedOn , last_updated_by as lastUpdatedBy FROM USER_BIN WHERE USER_ID = #{userId}
</select>

<select id="getBinDetailListByUserIdApproval" resultType="BinDTO">
SELECT DISTINCT BIN_ID as binId, created_on as createdOn,PARTICIPANT_ID as participantId,LAST_UPDATED_ON as lastUpdatedOn,USER_ID as userId FROM USER_BIN_STG WHERE USER_ID = #{userId} 
</select>

<insert id="insertUserBin">
INSERT INTO USER_BIN (BIN_ID, USER_ID , PARTICIPANT_ID,CREATED_BY, CREATED_ON,last_updated_on) VALUES (#{binId}, #{userId} , #{participantId},#{createdBy},#{createdOn},#{lastUpdatedOn}) 
</insert>

<delete id="deleteUserBin">
DELETE FROM user_bin WHERE USER_ID =#{userId}
</delete>

<delete id="deleteUserBinStg">
DELETE FROM user_bin_stg WHERE USER_ID =#{userId}
</delete>

<delete id="insertBinStg">
INSERT INTO USER_BIN_STG (BIN_ID, USER_ID , PARTICIPANT_ID,CREATED_BY, CREATED_ON,last_updated_on) VALUES (#{binId}, #{userId} , #{participantId},#{createdBy},#{createdOn},#{lastUpdatedOn})
</delete>
<select id="getBinListByBinIds" resultType="BinDTO">
SELECT BIN_ID as binId,ACQUIRER_ID as binNumber , PARTICIPANT_ID as participantId, SETTLEMENT_BIN as settlementBin,bin_card_type as binCardType,bin_type as binType,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin  
FROM membin_details WHERE status='A' and  BIN_ID IN (${binId})
</select>

<select id="getBinListByUserIdWithoutType" resultType="BinDTO">
SELECT BIN_ID as binId ,ACQUIRER_ID as binNumber, PARTICIPANT_ID as participantId, SETTLEMENT_BIN,bin_card_type as binCardType,bin_type as binType,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin  
FROM membin_details WHERE status='A' and  PARTICIPANT_ID=#{participantId}
</select>

<select id="getAcqDetailListByParticipantId" resultType="BinDTO">
SELECT BIN_ID as binId,SETTLEMENT_BIN as settlementBin,ACQUIRER_ID as binNumber, acquirer_id as acquirerId,  PARTICIPANT_ID as participantId,bin_card_type as binCardType,bin_type as binType,bin_card_variant as binCardVariant,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin 
FROM membin_details WHERE PARTICIPANT_ID=#{participantId} AND status='A' and BIN_TYPE =#{binType} ORDER BY BIN_NUMBER
</select>

<select id="getBinDetailListByParticipantId" resultType="BinDTO">
SELECT BIN_ID as binId,SETTLEMENT_BIN settlementBin,BIN_NUMBER as binNumber, PARTICIPANT_ID as participantID,bin_card_type as binCardType,bin_type as binType,bin_card_variant as binCardVariant,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin
 FROM membin_details WHERE PARTICIPANT_ID=#{participantId} AND status='A' and BIN_TYPE =#{binType} ORDER BY BIN_NUMBER
</select>

<select id="getParticipantList" resultType="ParticipantDTO">
SELECT DISTINCT CONCAT (PARTICIPANT_ID,'-', bank_name) as bankName , bank_name as participantName, participant_id as participantId FROM PARTICIPANT where status=#{status} order by participant_id asc
</select>

<update id="updateUserResetPasswordStg" >
UPDATE USER_STG  SET LAST_UPDATED_BY=#{lastUpdatedBy},
		 LAST_UPDATED_ON=#{lastUpdatedOn}, ISPWD_CHANGE_REQ=#{isPwdChangeReq},status=#{status} WHERE LOGIN_ID= #{userName}
</update>

<insert id="insertUserRoleMappingStg">
insert into user_role_mapping_stg (USER_ID,ROLE_ID,STATUS,CREATED_BY,CREATED_ON,LAST_UPDATED_BY,LAST_UPDATED_ON)
			 values(#{userId},#{roleId},#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn})
</insert>

<update id="updateUserStgActivate">
UPDATE USER_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} , LAST_OPERATION=#{lastOperation}, 
			 status=#{status} WHERE user_id = #{userId}
</update>

<update id="updateUserStgLock">
UPDATE USER_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} , LAST_OPERATION=#{lastOperation},
			 status=#{status} WHERE user_id = #{userId}
</update>

<select id="getUserStgInfoByUserId" resultType="UserDTO">
select distinct ( rus.user_id) as userId, rus.login_id as loginId, rus.first_name as firstName, rus.last_name as lastName,rus.MIDDLE_NAME as middleName, rus.street_address as streetAddress, rus.city as cityId, rus.state as stateId, rc.city_name as cityName, rs.state_name as stateName,
			rus.pincode as pincode, rus.email as emailId, rus.mobile_no as mobileNo, rus.contact_no as contactNo, rus.emp_id as empId,  rus.status as status, rus.created_by as createdBy, rus.created_on as createdOn, rus.last_updated_by as lastUpdatedBy, rus.last_updated_on as lastUpdatedOn, rus.user_type as userType, rus.bank_participant_id as bankParticipantId,
			rus.last_login_ts as lastLoginTs, rus.maker_comments as makerComments, rus.checker_comments as checkerComments, rus.request_state as requestState,
			rus.maker_checker_flag as makChkFlag,rus.lock_status as lockStatus,rus.ispwd_change_req as ispwdChangeReq, rus.salutation as salutation ,rus.last_operation as lastOperation,rus.bank_name as bankName,rus.access_level as accessLevel FROM user_stg rus inner join user_role_mapping_stg rurms on rus.user_id =rurms.user_id inner join role rr on rr.role_id =rurms.role_id
			 LEFT OUTER JOIN city RC ON (RC.CITY_ID = rus.CITY) 
			 LEFT OUTER JOIN state RS ON (RS.STATE_ID = rus.STATE) WHERE rus.user_id =#{userId}
</select>


<select id="getUserListForNewsandAlerts" resultType="UserDTO">
select u.login_id as loginId  from user_detail u ,user_stg us 
where u.user_id=us.user_id  and u.status!='D' and  u.bank_participant_id in <foreach item='item' index='index' collection='bankNameList' open='(' separator=',' close=')'>#{item}</foreach>  AND u.maker_checker_flag in <foreach item='item' index='index' collection='roleTypeList' open='(' separator=',' close=')'>#{item}</foreach>
</select>


<select id="checkIfDashBoardFuncExists" resultType="int">
select count(*) from user_role_mapping urm join role_func_mapping rfm on urm.role_id=rfm.role_id join functionality f on f.func_id =rfm.func_id where urm.user_id=#{userId} and f.func_name=#{funcName}
</select>
	
	<select id="getIssNetworkBinDetailListByParticipantId" resultType="BinDTO">
SELECT BIN_ID as binId,SETTLEMENT_BIN settlementBin,BIN_NUMBER as binNumber, PARTICIPANT_ID as participantID,bin_card_type as binCardType,bin_type as binType,bin_card_variant as binCardVariant,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin
 FROM network_bin_details WHERE PARTICIPANT_ID=#{participantId} AND status='A' and BIN_TYPE =#{binType} ORDER BY BIN_NUMBER
</select>
<select id="getAcqNetworkBinDetailListByParticipantId" resultType="BinDTO">
SELECT BIN_ID as binId,SETTLEMENT_BIN as settlementBin,ACQUIRER_ID as binNumber, acquirer_id as acquirerId,  PARTICIPANT_ID as participantId,bin_card_type as binCardType,bin_type as binType,bin_card_variant as binCardVariant,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin 
FROM network_bin_details WHERE PARTICIPANT_ID=#{participantId} AND status='A' and BIN_TYPE =#{binType} ORDER BY BIN_NUMBER
</select>

<select id="getIssNetworkBinDetailListByUserId" resultType="BinDTO">
SELECT BIN_ID as binId,SETTLEMENT_BIN settlementBin,BIN_NUMBER as binNumber, PARTICIPANT_ID as participantID,bin_card_type as binCardType,bin_type as binType,bin_card_variant as binCardVariant,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin
 FROM network_bin_details where bin_id  in (select bin_id  from user_bin_stg where user_id =#{userId}) AND status='A' and BIN_TYPE =#{binType} ORDER BY BIN_NUMBER
 </select>
 
 <select id="getAcqNetworkBinDetailListByUserId" resultType="BinDTO">
SELECT BIN_ID as binId,SETTLEMENT_BIN settlementBin,ACQUIRER_ID as binNumber, PARTICIPANT_ID as participantID,bin_card_type as binCardType,bin_type as binType,bin_card_variant as binCardVariant,bin_product_type as binProductType,product_type as productType,bin_card_brand as binCardBrand,card_sub_variant as cardSubVariant,high_bin as highBin,low_bin as lowBin
 FROM network_bin_details where bin_id  in (select bin_id  from user_bin_stg where user_id =#{userId}) AND status='A' and BIN_TYPE =#{binType} ORDER BY BIN_NUMBER
 </select>

</mapper>