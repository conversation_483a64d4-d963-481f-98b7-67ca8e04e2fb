package org.npci.settlenxt.adminportal.validator.check;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.mapping.ReasonCodes;
import org.npci.settlenxt.adminportal.common.util.LoadRejectReasonCode;
import org.npci.settlenxt.adminportal.common.util.ValidationType;
import org.npci.settlenxt.adminportal.common.util.ValidationUtils;
import org.npci.settlenxt.adminportal.validator.service.dto.FileError;
import org.npci.settlenxt.adminportal.validator.service.dto.HeaderRecord;
import org.npci.settlenxt.adminportal.validator.service.dto.Record;
import org.npci.settlenxt.adminportal.validator.service.dto.RecordError;
import org.npci.settlenxt.adminportal.validator.service.dto.TransactionRecord;
import org.npci.settlenxt.adminportal.validator.service.dto.ValidationResult;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.DateUtils;
import org.npci.settlenxt.portal.common.util.Utils;
import org.springframework.core.env.Environment;

/**
 * Process files header tags validation
 * <AUTHOR>
 *
 */
public class HeaderValidationCheck implements IValidationCheck {
	
	private static final Logger logger = LogManager.getLogger(HeaderValidationCheck.class);
	private static final String NDTTMFLGEN = "nDtTmFlGen";
	private static final String NRECNUM = "nRecNum";
	
    private Environment environment;
	
    private HeaderRecord headerRecord;
	private List<String> participantIds;
	private List<String> processors;
	private String userParticipantId;
	private String fileNameForXml;
	private String memInstCd;
	
	public HeaderValidationCheck(HeaderRecord headerRecord, String fileNameForXml, List<String> participantIds, List<String> processors, String userParticipantId, Environment environment, String memInstCd) {
		this.headerRecord = headerRecord;
		this.participantIds = participantIds;
		this.processors = processors;
		this.userParticipantId = userParticipantId;
		this.fileNameForXml = fileNameForXml;
		this.environment = environment;
		this.memInstCd = memInstCd;
	}
	
	@Override
	public void validate(ValidationResult validationResult, LoadRejectReasonCode rejReasonCode) {
	
		List<Record> headerRecordsList = headerRecord.getRecords();
		computeValidationResultExtracted(validationResult, rejReasonCode, headerRecordsList);
		
		String year = "";
	    
	    String fileGenerationTime = "";
	  
		for (Record headRec:headerRecordsList) {
			boolean validPatternAndLength = true;
		    Record headerElement =headRec;
		    
		    ReasonCodes reasonCodes = headerElement.getMapping()
					.getReasonCodes();

			int invalidLength = -1;
			int invalidType = -1;
			if (reasonCodes != null) {
				invalidLength = reasonCodes.getInvalidLength();
				invalidType = reasonCodes.getInvalidType();
			}

			// 3404 is "Invalid Data Input" - so we will use that if there is
			// nothing specifically defined
			invalidLength = invalidDataInputExtracted(invalidLength);

			invalidType = invalidDataInputExtracted(invalidType);
			 String currentHeaderElementName = headerElement.getName();
			 boolean dateTimeFileGenChecked = false;
			boolean twoSameMessages = false;
			boolean currentElementTypeNumeric = false;
				//Record Number Must be 00000001
				recordNumberValidationExtracted(validationResult, headerElement, currentHeaderElementName);

			if (!NDTTMFLGEN.equalsIgnoreCase(currentHeaderElementName) &&  !headerElement.isLengthValid()) {

				twoSameMessages = sameMessageValidationExtracted(invalidLength, invalidType, twoSameMessages);
				if (NDTTMFLGEN.equalsIgnoreCase(currentHeaderElementName)) {
					invalidLength = 5011;
					dateTimeFileGenChecked = true;
				}
				validationResult.getFileErrors().add(new FileError(invalidLength,
						rejReasonCode.getRejectReasonDesc(String.valueOf(invalidLength)), currentHeaderElementName));
				validPatternAndLength = false;
			}
			if (!twoSameMessages && !dateTimeFileGenChecked && !NDTTMFLGEN.equalsIgnoreCase(currentHeaderElementName) && !headerElement.isPatternValid()) {
				validPatternAndLength = patternHeaderValidation(validationResult, rejReasonCode, validPatternAndLength,
						headerElement, invalidType, currentHeaderElementName, currentElementTypeNumeric);
			}


			if(validPatternAndLength){
				// 1. Settlement Bin Check
				settlementBinValidationExtracted(validationResult, headerElement, currentHeaderElementName);
	
				// 2. File Type & Category - Member generated and whether Production / Test - Check
				fileTypeCategoryValidationExtracted(validationResult, headerElement, currentHeaderElementName);
	
				// 3. Duplicate File Name and ID - Check - nUnFlNm should match the file name
				duplicateFileNameValidation(validationResult, headerElement, currentHeaderElementName);
				
				// 4. Validations of member institution id checked whether its a valid participant id of the member bank
				// and all the transactions present in the file pertains to the same participant id.
				memberInstitutionIdValidationExtracted(validationResult, headerElement, currentHeaderElementName);
	
				// 5. Product Code - make sure it is a valid product code
				productCodeValidationExtracted(validationResult, headerElement, currentHeaderElementName);
	
				// 6. Version Number Check
				
				versionNumberValidation(validationResult, headerElement, currentHeaderElementName);
	
			    // 7. Date Check
				year = fileDateValidationExtracted(validationResult, year, headerElement, currentHeaderElementName);
				
				fileGenerationTime = computeFileGenTimeExtracted(fileGenerationTime, headerElement,
						currentHeaderElementName);
				
			}
		}
		
		setFileGenTimeExtracted(validationResult, year, fileGenerationTime);
	}

	private boolean sameMessageValidationExtracted(int invalidLength, int invalidType, boolean twoSameMessages) {
		if (invalidLength == invalidType) {
			twoSameMessages = true;
		}
		return twoSameMessages;
	}

	private int invalidDataInputExtracted(int invalidLength) {
		if (invalidLength == -1) {
			invalidLength = 3404;
		}
		return invalidLength;
	}

	private boolean patternHeaderValidation(ValidationResult validationResult, LoadRejectReasonCode rejReasonCode,
			boolean validPatternAndLength, Record headerElement, int invalidType, String currentHeaderElementName,
			boolean currentElementTypeNumeric) {
		if (NDTTMFLGEN.equalsIgnoreCase(currentHeaderElementName)) {
			invalidType = 5011;
		}
		String typeOfCurrentElement  = headerElement.getMapping().getDataType();
		if ("N".equals(typeOfCurrentElement) && StringUtils.isNumeric(headerElement.getValue())) {
			currentElementTypeNumeric = true;
		}
		validPatternAndLength = currentElementTypeNumValidation(validationResult, rejReasonCode,
				validPatternAndLength, invalidType, currentHeaderElementName, currentElementTypeNumeric);
		return validPatternAndLength;
	}

	private boolean currentElementTypeNumValidation(ValidationResult validationResult,
			LoadRejectReasonCode rejReasonCode, boolean validPatternAndLength, int invalidType,
			String currentHeaderElementName, boolean currentElementTypeNumeric) {
		if(!currentElementTypeNumeric){
			validationResult.getFileErrors().add(new FileError(invalidType,
					rejReasonCode.getRejectReasonDesc(String.valueOf(invalidType)), currentHeaderElementName));
			validPatternAndLength = false;
		}
		return validPatternAndLength;
	}

	private void recordNumberValidationExtracted(ValidationResult validationResult, Record headerElement,
			String currentHeaderElementName) {
		if(NRECNUM.equalsIgnoreCase(currentHeaderElementName)){
			String recordNumberInHeader = headerElement.getValue();
			if(!"00000001".equals(recordNumberInHeader)){
				validationResult.getFileErrors().add(new FileError(5010, "Record Number Must be 00000001.", currentHeaderElementName));
			}
			
		}
	}

	private void memberInstitutionIdValidationExtracted(ValidationResult validationResult, Record headerElement,
			String currentHeaderElementName) {
		if ("nMemInstCd".equalsIgnoreCase(currentHeaderElementName)) {
			String inst = headerElement.getValue();
			boolean partValidationDone = false;
			if (!participantIds.contains(inst)) {
				validationResult.getFileErrors().add(
						new FileError(5013, "Invalid Member Institution Id (PID)", currentHeaderElementName));
				partValidationDone = true;
			}
			
			
			if (!inst.equals(userParticipantId) && !partValidationDone) {
				validationResult.getFileErrors().add(
						new FileError(5013, "Invalid Member Institution Id (PID)", currentHeaderElementName));
			}
			
				
		}
	}

	private void settlementBinValidationExtracted(ValidationResult validationResult, Record headerElement,
			String currentHeaderElementName) {
		if ("nSetBIN".equalsIgnoreCase(currentHeaderElementName)) {
			String headerSettlBin = headerElement.getValue();
			
			// Make sure this field exists in the user's processor list (future)
			// right now we are making sure the participant id is in the processor list
			// waiting on server to finish the data model
			if(StringUtils.isEmpty(headerSettlBin)){
				validationResult.setHeaderSettlementBin(" ");
				validationResult.getFileErrors().add(new FileError(5019, "Settlement BIN must be present in Header message", currentHeaderElementName));
			}else{
				validationResult.setHeaderSettlementBin(headerSettlBin);
				if(!processors.contains(headerSettlBin)){
					validationResult.getFileErrors().add(new FileError(5018, "Settlement BIN not Valid", currentHeaderElementName));
				}
			}
			
		}
	}

	private void fileTypeCategoryValidationExtracted(ValidationResult validationResult, Record headerElement,
			String currentHeaderElementName) {
		if ("nFlCatg".equalsIgnoreCase(currentHeaderElementName)) {
			String category = headerElement.getValue();
			
			// Make sure the file type (identified from file name) is valid for the member bank
			// still need more details on this
			
			String acceptedValue = environment.getProperty("FILE_ENV_TYPE");
			
			if (!category.equalsIgnoreCase(acceptedValue)) {
				validationResult.getFileErrors().add(new FileError(5020, "Invalid File Category.", currentHeaderElementName));
			}
		}
	}

	private void duplicateFileNameValidation(ValidationResult validationResult, Record headerElement,
			String currentHeaderElementName) {
		if ("nUnFlNm".equalsIgnoreCase(currentHeaderElementName)) {
			String fileName = headerElement.getValue()  + ".xml";
			if (!fileName.equalsIgnoreCase(fileNameForXml)) {
				logger.error(fileName ,"---" , fileNameForXml);
				validationResult.getFileErrors().add(new FileError(5026, "File name not matched with header message", currentHeaderElementName));
			}
		}
	}

	private void productCodeValidationExtracted(ValidationResult validationResult, Record headerElement,
			String currentHeaderElementName) {
		if ("nProdCd".equalsIgnoreCase(currentHeaderElementName)) {
			String productCode = headerElement.getValue();
			

				if (!environment.containsProperty(productCode)) {
					validationResult.getFileErrors().add(new FileError(5015, "Invalid product code in Header Message", currentHeaderElementName));
				}

		}
	}

	private String fileDateValidationExtracted(ValidationResult validationResult, String year, Record headerElement,
			String currentHeaderElementName) {
		if("nUnFlNm".equalsIgnoreCase(currentHeaderElementName)){
			//9th to 13th character in the file name: YYDDD Julian date format.
			String dateTimeFileGenerated = "";
			dateTimeFileGenerated = headerElement.getValue().substring(14,19);
			Date date = Utils.convertJulianDate(dateTimeFileGenerated,"yyDDD");
			LocalDateTime ldt = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
			DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy");
			year = df.format(ldt);
			
			boolean validTodayOrADayBefore = date.before(new Date()) || date.equals(new Date());
			
			if (!validTodayOrADayBefore) {
				validationResult.getFileErrors().add(new FileError(5017, "Julian date in file name is not valid", currentHeaderElementName));
			}
			
			if(StringUtils.isNotBlank(memInstCd) && Boolean.FALSE.equals(StringUtils.equals(memInstCd, headerElement.getValue().substring(3,14)))) {
				validationResult.getFileErrors().add(new FileError(5017, "Incorrect Unique File Name as not matched with memInstCd", currentHeaderElementName));
			}
		}
		return year;
	}

	private void versionNumberValidation(ValidationResult validationResult, Record headerElement,
			String currentHeaderElementName) {
		String versionNumber = setVerisonNumberIfEmptyExtracted();
		if ("nVerNum".equalsIgnoreCase(currentHeaderElementName)) {
			String version = headerElement.getValue();
			
			validationResult.setVersion(version);
			
			if (!version.equals(versionNumber)) {
				validationResult.setVersion(versionNumber);
				validationResult.getFileErrors().add(new FileError(5021, "Version Number not Valid", currentHeaderElementName));
			}

		}
	}

	private String computeFileGenTimeExtracted(String fileGenerationTime, Record headerElement,
			String currentHeaderElementName) {
		if(StringUtils.equalsIgnoreCase(currentHeaderElementName, NDTTMFLGEN)) {
			fileGenerationTime = headerElement.getValue();
		}
		return fileGenerationTime;
	}

	private void setFileGenTimeExtracted(ValidationResult validationResult, String year, String fileGenerationTime) {
		if (StringUtils.isNotBlank(year) && StringUtils.isNotBlank(fileGenerationTime)) {
			fileGenerationTime = year + fileGenerationTime;
			String convertedFormat = DateUtils.getChangeDateFormat(fileGenerationTime, "yyyyMMddhhmmss", "dd/MM/yyyy");
			validationResult.setFileGenDateTime(convertedFormat);
		}
	}

	private void computeValidationResultExtracted(ValidationResult validationResult, LoadRejectReasonCode rejReasonCode,
			List<Record> headerRecordsList) {
		List<RecordError> requiredErrors =  ValidationUtils.validateRequiredFields(ValidationType.HEADER, headerRecordsList, validationResult.getXmlHeader(), rejReasonCode);
		
		if (Boolean.FALSE.equals(requiredErrors.isEmpty())) {
			for (RecordError error : requiredErrors) {
				validationResult.getFileErrors().add(new FileError(error.getErrorNo(), error.getErrorDescription(), error.getName()));
			}
		}
	}

	private String setVerisonNumberIfEmptyExtracted() {
		String versionNumber = environment.getProperty("NPCIVersion");
		
 	 	if(StringUtils.isEmpty(versionNumber)){
 	 		versionNumber = "01.00";
 	 	}
		return versionNumber;
	}

	@Override
	public void validate(ValidationResult validationResult, List<TransactionRecord> txnRecords) throws SettleNxtException {
		// 
		
	}

	@Override
	public void validate(ValidationResult validationResult, List<TransactionRecord> txnRecords, int taskIdsSizeForSplit,
			LoadRejectReasonCode rejReasonCode) throws SettleNxtException {
		// 
		
	}
}
