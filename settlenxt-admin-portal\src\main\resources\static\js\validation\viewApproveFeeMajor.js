$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	}

function userAction(action) {
	let url = action;
	 var data = "status,"
			+ status;
	postData(url, data);
}

function postAction(_action) {


var remarks="";
var url="";
var data="";
var feeMajorId="";
	if(maxLengthTextArea('rejectReason')){
	if ($('#apprej option:selected').val() == "A") {
		if ($("#rejectReason").val() != "") {
			
			 feeMajorId = $("#feeMajorId").val();
			 remarks = $("#rejectReason").val();
			 url = '/approveFeeMajorStatus';
			 data = "status," + "A" + ",remarks,"
					+ remarks+ ",feeMajorId," + feeMajorId;
			postData(url, data);
		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else if ($('#apprej option:selected').val() == "R") {
		if ($("#rejectReason").val() != "") {
				
				 remarks = $("#rejectReason").val();
				 feeMajorId = $("#feeMajorId").val();
				 url = '/approveFeeMajorStatus';
				 data = "status," + "R" + ",remarks,"
						+ remarks + ",feeMajorId," + feeMajorId;
				postData(url, data);
			

		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else {
		$(".appRejMust").show();
		$('html, body').animate({ scrollTop: 0 }, 'slow');
		return false;
	}
	}
}

function viewMajorMinorInfo(feeMajorId, requestId,significance) {
	
	let url = '/editFeeMajorMinor';
	var data = "feeConfigId," + feeMajorId + ",significance," + significance + ",requestId," + requestId ;
	
	postData(url, data);
}
