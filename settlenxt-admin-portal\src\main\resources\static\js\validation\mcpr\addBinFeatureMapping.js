var isdisableflag=0;
var isdisableflag1=0;
var localBinFeatureId;
$(document).ready(function() {

	
		    var date = new Date();
		    var sys_year = date.getFullYear();
		    var sys_month = date.getMonth()+1;
		    var sysDate = "" + sys_year + (padTo2Digits(sys_month));

			localBinFeatureId=$("#binFeatureId").val();
							
				var toDate=parseInt($("#toYear").val()+""+$("#toMonth").val());
				var fromDate=parseInt($("#fromYear").val()+""+$("#fromMonth").val());

		    if(localBinFeatureId>0){
		    if(fromDate<=sysDate ){
				   $('#fromMonth').attr('disabled', true);
				   $('#fromYear').attr('disabled', true);  
					isdisableflag=1;
					
				}
		    if(toDate<sysDate ){
				   $('#toMonth').attr('disabled', true);
				   $('#toYear').attr('disabled', true);  
			    	isdisableflag1=1;
				}
				document.getElementById("featureId").disabled = true;
		    }
		    if(isdisableflag==1 && isdisableflag1==1){
				document.getElementById("BEditSubmit").disabled = false;
		    }

populateBinData();
populateFeatureData();
				

	disableSave();
	
	$("#fromMonth").on('keyup keypress blur change', function () {
		validateField('fromMonth', true,"SelectionBox",0,false,0,0,false);
        unableSave();
    });
 	$("#fromYear").on('keyup keypress blur change', function () {
 		validateField('fromYear', true,"SelectionBox",0,false,0,0,false);
        unableSave();
    });
	$("#toMonth").on('keyup keypress blur change', function () {
		validateField('toMonth', true,"SelectionBox",0,false,0,0,false);
        unableSave();
    });
 	$("#toYear").on('keyup keypress blur change', function () {
 		validateField('toYear', true,"SelectionBox",0,false,0,0,false);
        unableSave();
    });	$("#participantName").on('keyup keypress blur change', function () {
    	validateField('participantName', true,"SelectionBox",0,false,0,0,false);
        unableSave();
    });
 	$("#binNumber").on('keyup keypress blur change', function () {
 		validateField('binNumber', true,"SelectionBox",0,false,0,0,false);
        unableSave();
    });	
    $("#featureId").on('keyup keypress blur change', function () {
    	validateField('featureId', true,"SelectionBox",0,false,0,0,false);
        unableSave();
    });
			if($("#deleteBinFeatureMapping").val()=="Yes")
			{
				   $('#fromMonth').attr('disabled', true);
				   $('#fromYear').attr('disabled', true);  
				   $('#toMonth').attr('disabled', true);
				   $('#toYear').attr('disabled', true);  
				   $('#featureId').attr('disabled', true);  
				   $('#participantName').attr('disabled', true);
				   $('#binNumber').attr('disabled', true);  
					document.getElementById("BEditSubmit").disabled = false;
			
			}
 
});

function disableSave()
{
if (typeof BAddSubmit != "undefined") {
	document.getElementById("BAddSubmit").disabled = true;
}
if (typeof BEditSubmit != "undefined") {
	document.getElementById("BEditSubmit").disabled = true;
}
}

function unableSave()
{
	if (typeof BAddSubmit != "undefined") {
		document.getElementById("BAddSubmit").disabled = false;
	}
	if (typeof BEditSubmit != "undefined") {
		document.getElementById("BEditSubmit").disabled = false;
	}
	if(isdisableflag==1 && isdisableflag1==1){
		document.getElementById("BAddSubmit").disabled = true;
	}
}


function padTo2Digits(sys_month) {
return sys_month.toString().padStart(2, '0');
}
				
			




window.history.forward();
function noBack() {
	window.history.forward();
}
function selectBinData() {
	var binexclBin=document.getElementById("binFeatureMappingBin").value;

    var selectObj = document.getElementById("binNumber");
    var i = 0;
     for (i of selectObj) {
        if (i.value == binexclBin) {
            i.selected = true;
            return;
        }
    }
     
}

function selectFeatureData() {
	var binexclBin=document.getElementById("hFeatureId").value;

    var selectObj = document.getElementById("featureId");
    var i = 0;
     for (i of selectObj) {
        if (i.value == binexclBin) {
            i.selected = true;
            return;
        }
    }
}

function resetAction() {
document.getElementById("addEditBinFeatureMapping").reset();
$("#errfromYear").find('.error').html('');
$("#errparticipantId").find('.error').html('');
$("#errtoMonth").find('.error').html('');
$("#errtoYear").find('.error').html('');
$("#errfeatureId").find('.error').html('');
$("#errfromMonth").find('.error').html('');
$("#errbinId").find('.error').html('');
}

function populateBinData() {
				if ($('#participantName').val() != '0') {
					var participantId = $("#participantName").val();
					var tokenVal = document.getElementsByName("_TransactToken")[0].value;
					
					$.ajax({
								url : "getBinFeatureMappingList",
								type : "POST",
								data : {
									participantId : participantId,
									_TransactToken: tokenVal
								},
								dataType : "json",
								"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenVal);},
								success : function(data) {
									
									$("#binNumber")
											.empty();
									$("#binNumber")
											.append(
													'<option value="">SELECT</option>');
									$
											.each(
													data,
													function(
															_index,
															option) {
														$(
																"#binNumber")
																.append(
																		'<option value="'
																				+ option.binNumber
																				+ '">'
																				+ option.binDetail
																				+ '</option>');
													});
									
									
									selectBinData();
								}
							});

				} else {
					$("#binNumber").empty();
				
				}

} 

function populateFeatureData() {

	var toDateFlag = $("#toMonth").val();
	var fromDateFlag = $("#fromMonth").val();
	var toYearFlag = $("#toYear").val();
	var fromYearFlag = $("#fromYear").val();
	var binFlag = $("#binNumber").val();
	var flag=false;
	if (toDateFlag != "" && fromDateFlag != "" && toYearFlag !="" && fromYearFlag!="" && binFlag!="") {
		flag=true;
	}
	
	if (flag)
	{
		return false;
	}
	
				if ($('#binNumber').val() != '0') {
					var binNumber = $("#binNumber").val();
					var tokenVal = document.getElementsByName("_TransactToken")[0].value;
					$
							.ajax({
								url : "getFeatureList",
								type : "POST",
								data : {
									binNumber : binNumber,
									fromDate : fromYearFlag+fromDateFlag,
									toDate : toYearFlag+toDateFlag,
									_TransactToken: tokenVal
								},
								dataType : "json",
								"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenVal);},
								
								success : function(data) {
									
									$("#featureId")
											.empty();
									$("#featureId")
											.append(
													'<option value="">SELECT</option>');
									$
											.each(
													data,
													function(
															_index,
															option) {
														$(
																"#featureId")
																.append(
																		'<option value="'
																				+ option.cardConfigId
																				+ '">'
																				+ option.feature
																				+ '</option>');
													});
									
									
									selectFeatureData();
								}
							});

				} else {
					$("#binNumber").empty();
				
				}

} 


function userAction1(_type, action) {
	var url = action;
	var binFeatureId = document.getElementById("binFeatureId").value;
	var data = "binFeatureId," + binFeatureId + ",status,"
		+ status;
	postData(url, data);
} 



function validateBinDate(){
	var toDate=0;
    var fromDate=0;
    var currentYear = new Date().getFullYear();
    var currentMonth = new Date().getMonth()+1;
    var sysDate = "" + currentYear + (padTo2Digits(currentMonth));
    toDate=parseInt($("#toYear").val()+""+$("#toMonth").val());
    fromDate=parseInt($("#fromYear").val()+""+$("#fromMonth").val());
    if(isdisableflag==0)
    {
		if(localBinFeatureId>0)
		{	   
		    if(fromDate <= sysDate){
		    	$("#errfromMonth").find('.error').html(binFeatureMappingValidationMessages['pastMonthValidation']);
		    	$("#errfromMonth").show();
				return false;
		     }
		 }	
		else	
		{	   
			if(parseInt(sysDate) -parseInt(fromDate) > 2){
		    	$("#errfromMonth").find('.error').html(binFeatureMappingValidationMessages['pastMonthValidation']);
		    	$("#errfromMonth").show();
				return false;
		 	}
		}
	} 
 
    if(fromDate>toDate){
    	$("#errtoYear").find('.error').html(binFeatureMappingValidationMessages['dateValidation']);
    	$("#errtoYear").show();
		return false;
        
    }
    
  
	return true;

}






function deleteForm() {
	var binFeatureId = document.getElementById("binFeatureId").value;
					var url="/updatedeleteBinFeatureMapping";
					var data = "binFeatureId," + binFeatureId 
							+",parentPage," + $("#hparentPage").val();
	
					postData(url, data);					
}

function validateAddEditForm(id) {
	
	$('.jqueryError').text("");
	$('.jqueryError').hide();
	
	var isValid = true;

    if (!validateField('participantName', true,"SelectionBox",0,false,0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('binNumber', true,"SelectionBox",0,false,0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('fromMonth', true,"SelectionBox",0,false,0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('fromYear', true,"SelectionBox",0,false,0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('toMonth', true,"SelectionBox",0,false,0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('toYear', true,"SelectionBox",0,false,0,0,false) && isValid) {
        isValid = false;
    }
    if(isValid)
    {
		isValid = validateBinDate();
    }
    if (!validateField('featureId', true,"SelectionBox",0,false,0,0,false) && isValid) {
        isValid = false;
    }
    
    
	if (isValid) {
	 
			checkDuplicateData(id);

	} else {
		
		return false;
	}
    

}
function checkDuplicateData(id) {
    var url;
	var validvRoleName=false;
	var binFeatureId = document.getElementById("binFeatureId").value;
	var fromMonth = document.getElementById("fromMonth").value;
	var fromYear = document.getElementById("fromYear").value;
	var toMonth = document.getElementById("toMonth").value;
	var toYear = document.getElementById("toYear").value;
	var participantName = document.getElementById("participantName").value;
	var bin = document.getElementById("binNumber").value;
	var featureId = document.getElementById("featureId").value;
	var msUrl = "validationCheck";
	var tokenVal = document.getElementsByName("_TransactToken")[0].value;
		$.ajax({
			url: msUrl,
			type: "POST",
			dataType: "json",
			"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenVal);},
			
			data: {
				"binFeatureId": binFeatureId,
				"fromMonth": fromMonth,
				"fromYear": fromYear,
				"toMonth": toMonth,
				"toYear": toYear,
				"participantName": participantName,
				"bin": bin,
				"bankName": $("#participantName").val(),
				"featureId":featureId,
				_TransactToken: tokenVal
			},
			success: function(response) {
				if (response.Status == "Y") {
					validvRoleName = true;
					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "";
					if (id == 'A') {
						url = '/addBinFeatureMapping';
				
					} else if (id == 'E') {
						url = '/updateBinFeatureMapping';
					}
					var data = "binFeatureId," + binFeatureId + ",fromMonth," + fromMonth
							+ ",fromYear," + fromYear + ",toMonth," + toMonth
							+ ",toYear," + toYear
							+ ",participantName," + participantName + ",bin," + bin
							+ ",bankName," +  $("#participantName").val() /*+",bin," +  $("#binNumber").val()*/
							+ ",featureId," + featureId 
							+ ",userType," + $('#userType').val()
							+",parentPage," + $("#hparentPage").val();
	
					postData(url, data);					
				} else {
					validvRoleName = false;
 					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "Bin Feature Mapping Configuration Already Exists";
				}
			},
			error: function(_request, _status, _error) {
				errvErrorInfo.className = 'error';
				errvErrorInfo.innerHTML = "";
			}
		});

	return validvRoleName;
}


function addEdit(id) {
	var url;
	if (id == 'A') {
		url = '/addBinFeatureMapping';

	} else if (id == 'E') {
		url = '/updateBinFeatureMapping';
	}

	
	var binFeatureId = document.getElementById("binFeatureId").value;
	var fromMonth = document.getElementById("fromMonth").value;
	var fromYear = document.getElementById("fromYear").value;
	var toMonth = document.getElementById("toMonth").value;
	var toYear = document.getElementById("toYear").value;
	var participantName = document.getElementById("participantName").value;
	var bin = document.getElementById("binNumber").value;
	var featureId = document.getElementById("featureId").value;
		

		


	var data = "binFeatureId," + binFeatureId + ",fromMonth," + fromMonth
			+ ",fromYear," + fromYear + ",toMonth," + toMonth
			+ ",toYear," + toYear
			+ ",participantName," + participantName + ",bin," + bin
			+ ",bankName," +  $("#participantName").val() /*+",bin," +  $("#binNumber").val()*/
			+ ",featureId," + featureId 
			+ ",userType," + $('#userType').val()
			+",parentPage," + $("#hparentPage").val();
	

		postData(url, data);

	}



function postDiscardBinAction(action) {
	
	var url = action;
	var binFeatureId = document.getElementById("binFeatureId").value;
	var data = "binFeatureId," + binFeatureId  ;
	postData(url, data);
	
}


function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType!="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")|| (isMandatory && fieldValue.trim() == "0" && fieldType=="SelectionBox")|| (isMandatory && fieldValue.trim() == "" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    var regEx;
    if (fieldType == "Alphabet") {
         regEx = /^[a-zA-Z]+$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "AlphabetWithSpace") {
         regEx = /^[a-zA-Z ]+$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "AlphanumericNoSpace") {
         regEx = /^[A-Za-z0-9]+$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "Integer") {
         regEx =/^\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    
    if (fieldType == "Decimal") {
         regEx = /^\d*(\.\d{0,2})?$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
      if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
      

    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
        if(binFeatureMappingValidationMessages[fieldId]){
            $("#err" + fieldId).find('.error').html(binFeatureMappingValidationMessages[fieldId]);
        }
        $("#err" + fieldId).show();
    }
    return isValid;
}
