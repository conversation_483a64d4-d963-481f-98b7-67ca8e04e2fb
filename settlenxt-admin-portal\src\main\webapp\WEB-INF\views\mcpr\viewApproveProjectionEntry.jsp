<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

	<script src="./static/js/validation/mcpr/viewApproveProjectionEntry.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert"><spring:message code="budget.apprejecterrormsg" /></div>
	<div class="alert alert-danger remarkMust" role="alert"><spring:message code="budget.remarkserror" /></div>
	<c:url value="approveCardProjection" var="approveCardProjection" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewapproveCardProjection" modelAttribute="projectionEntryDTO"
		action="${approveCardProjection}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="projectionEntry.approvalPendingViewScreen.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="cardProjectionId" value="${projectionEntryDTO.cardProjectionId}">

						<input type="hidden" id="crtuser"
							value="${projectionEntryDTO.lastUpdatedBy}" />

						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope = "col"></th></thead>
							<tbody>
								<tr>
									<td colspan="8"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message code="projectionEntry.requestInfo" /></span></strong>
										</div></td>
									
								</tr>
								<tr>


									<td><label><spring:message code="projectionEntry.requestType" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.lastOperation}</td>
									<td><label><spring:message code="projectionEntry.requestDate" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.lastUpdatedOn}</td>
									<td><label><spring:message code="projectionEntry.requestStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${projectionEntryDTO.requestState=='A' }">Approved</c:if></td>
									<td><c:if test="${projectionEntryDTO.requestState=='P' }">Pending for Approval</c:if></td>
									<td><c:if test="${projectionEntryDTO.requestState=='R' }">Rejected</c:if></td>
									<td><c:if test="${projectionEntryDTO.requestState=='D' }">Discarded</c:if></td>
									<td colspan="2"></td>
								</tr>
								<tr>
									<td><label><spring:message code="projectionEntry.requestBy" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.lastUpdatedBy}</td>

									<td><label><spring:message code="projectionEntry.approverComments" /><span
											style="color: red"></span></label></td>
									<td>${projectionEntryDTO.checkerComments}</td>
									<td colspan="4"></td>
								</tr>

								<tr>
									<td colspan="8">
										<div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-user"></span> <span
												data-i18n="Data"><spring:message code="projectionEntry.viewscreen.title" /></span></strong>
										</div>
									</td>
								</tr>


								<tr>
									<td><label><spring:message code="projectionEntry.cardProjectionId" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.cardProjectionId}</td>
									<td><label><spring:message code="projectionEntry.cardType" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.cardTypeName}</td>
									
									<td><label><spring:message code="projectionEntry.cardVariant" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.cardVariantName}</td>
									
									<td colspan="2"></td>
								</tr>
								<tr>
									<td><label><spring:message code="projectionEntry.totalCards" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.totalCards}</td>
									<td><label><spring:message code="projectionEntry.month" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.month}</td>
									
									<td colspan="4"></td>
									
								</tr>

								
								
								<sec:authorize access="hasAuthority('Approve Card Projection')">
									<c:if test="${projectionEntryDTO.requestState eq 'P'}">
										<tr>
											<td colspan="8"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"><spring:message
																code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
										</tr>
										

										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="AM.lbl.reject" /></option>
											</select></td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											<!-- //Added by deepak on 31-03-2016 -->
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										</tr>
									</c:if>
							</sec:authorize>	

							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Card Projection')">
										<c:if test="${projectionEntryDTO.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approveRole" value="Submit"
												onclick="postAction('/approveCardProjection');" />
										</c:if>
								</sec:authorize>
								
								<sec:authorize access="hasAuthority('Edit Card Projection')">				
									<c:if test="${projectionEntryDTO.requestState  eq 'R' and not empty showbutton}">
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="javascript:edit('${projectionEntryDTO.cardProjectionId}','V','approvalTab');"/>						
									
									<button type="button" class="btn btn-danger"
							onclick="postDiscardAction('/discardRejectedEntry');">
							<spring:message code="projectionEntry.discardBtn" /></button>
									</c:if>
									</sec:authorize>
								
								
									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/projectionEntryPendingForApproval');"><spring:message code="budget.backBtn" /></button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

