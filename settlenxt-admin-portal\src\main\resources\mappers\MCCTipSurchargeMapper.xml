<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.MCCTipSurchargeRepository">
	
	<select id="getTipSurchargeListMain" resultType="MCCTipSurchargeDTO">
		select m.mcc_tip_surcharge_id as mccTipSurchargeId, m.tip_surcharge_id  as tipSurchargeId,m.mcc_id as mccId, 
		mf.mcc_id, tf.tip_surcharge_id,  CONCAT(cast(mf.mcc_code as varchar(10)), '-',mf.mcc_desc) as mccNameLookup,
		tf.tip_surcharge_name as tipSurchargeLookup,stg.request_state as requestState
		from  mcc_tip_surcharge m
		inner join mcc_tip_surcharge_stg stg on m.mcc_tip_surcharge_id=stg.mcc_tip_surcharge_id
		left join  mcc_config mf on mf.mcc_id = m.mcc_id 
		left join  tip_surcharge tf on tf.tip_surcharge_id = m.tip_surcharge_id 
		 ORDER BY m.last_updated_on desc
	</select>
	<select id="getTipSurchargePendingForApproval" resultType="MCCTipSurchargeDTO">
		select m.mcc_tip_surcharge_id as mccTipSurchargeId, m.tip_surcharge_id  as tipSurchargeId,m.mcc_id as mccId, mf.mcc_id, tf.tip_surcharge_id, 
		m.request_state as requestState,m.CHECKER_COMMENTS as checkerComments, m.status,m.last_operation as lastOperation,
		CONCAT(cast(mf.mcc_code as varchar(10)), '-',mf.mcc_desc) as mccNameLookup,tf.tip_surcharge_name as tipSurchargeLookup 
		from  mcc_tip_surcharge_stg m 
		left join  mcc_config mf on mf.mcc_id = m.mcc_id 
		left join  tip_surcharge tf on tf.tip_surcharge_id = m.tip_surcharge_id 
		WHERE m.request_state in('P','R') ORDER BY m.last_updated_on desc
	</select>
	<select id="getMccTipSurchargeProfileMain" resultType="MCCTipSurchargeDTO">
		select m.mcc_tip_surcharge_id as mccTipSurchargeId, m.tip_surcharge_id  as tipSurchargeId,m.mcc_id as mccId, mf.mcc_id, tf.tip_surcharge_id,
		CONCAT(cast(mf.mcc_code as varchar(10)), '-',mf.mcc_desc) as mccNameLookup,tf.tip_surcharge_name as tipSurchargeLookup,stg.request_state as requestState,
		m.created_by as createdBy, m.created_on as createdOn,m.last_updated_by as lastUpdatedBy, m.last_updated_on as lastUpdatedOn
		from  mcc_tip_surcharge m 
		inner join mcc_tip_surcharge_stg stg on m.mcc_tip_surcharge_id=stg.mcc_tip_surcharge_id
		left join  mcc_config mf on mf.mcc_id = m.mcc_id 
		left join  tip_surcharge tf on tf.tip_surcharge_id = m.tip_surcharge_id 
		WHERE m.mcc_tip_surcharge_id = #{mccTipSurchargeId}  
	</select>
	<select id="getMccTipSurchargeStgInfoById" resultType="MCCTipSurchargeDTO">
		select m.mcc_tip_surcharge_id as mccTipSurchargeId, m.tip_surcharge_id  as tipSurchargeId,m.mcc_id as mccId, mf.mcc_id, tf.tip_surcharge_id,
		m.created_by as createdBy, m.created_on as createdOn,m.last_updated_by as lastUpdatedBy, m.last_updated_on as lastUpdatedOn, m.request_state as requestState,m.CHECKER_COMMENTS as checkerComments, m.status,m.last_operation as lastOperation, 
		CONCAT(cast(mf.mcc_code as varchar(10)), '-',mf.mcc_desc) as mccNameLookup,tf.tip_surcharge_name as tipSurchargeLookup
		from  mcc_tip_surcharge_stg m 
		left join  mcc_config mf on mf.mcc_id = m.mcc_id 
		left join  tip_surcharge tf on tf.tip_surcharge_id = m.tip_surcharge_id 
		WHERE m.mcc_tip_surcharge_id = #{mccTipSurchargeId}
	</select>
	<select id="getMccTipSurchargeMain" resultType="MCCTipSurchargeDTO">
		select m.mcc_tip_surcharge_id as mccTipSurchargeId, m.tip_surcharge_id  as tipSurchargeId,m.mcc_id as mccId, mf.mcc_id, tf.tip_surcharge_id,
		CONCAT(cast(mf.mcc_code as varchar(10)), '-',mf.mcc_desc) as mccNameLookup,tf.tip_surcharge_name as tipSurchargeLookup,m.status
		,m.last_updated_by as lastUpdatedBy, m.last_updated_on as lastUpdatedOn
		from  mcc_tip_surcharge m 
		left join  mcc_config mf on mf.mcc_id = m.mcc_id 
		left join  tip_surcharge tf on tf.tip_surcharge_id = m.tip_surcharge_id 
		WHERE m.mcc_tip_surcharge_id = #{mccTipSurchargeId}
	</select>
	<select id="getMccTipSurchargeStg" resultType="MCCTipSurchargeDTO">
		select m.mcc_tip_surcharge_id as mccTipSurchargeId, m.tip_surcharge_id  as tipSurchargeId,m.mcc_id as mccId, mf.mcc_id, tf.tip_surcharge_id, 
		m.created_by as createdBy, m.created_on as createdOn,m.last_updated_by as lastUpdatedBy, m.last_updated_on as lastUpdatedOn, m.request_state as requestState,m.CHECKER_COMMENTS as checkerComments, m.status,m.last_operation as lastOperation,
		CONCAT(cast(mf.mcc_code as varchar(10)), '-',mf.mcc_desc) as mccNameLookup,tf.tip_surcharge_name as tipSurchargeLookup
		from  mcc_tip_surcharge_stg m
		left join  mcc_config mf on mf.mcc_id = m.mcc_id  
		left join  tip_surcharge tf on tf.tip_surcharge_id = m.tip_surcharge_id 
		WHERE m.mcc_tip_surcharge_id = #{mccTipSurchargeId}	
	</select>
	<select id="fetchMccTipSurchargeIdSequence" resultType="int">	
		SELECT nextval('mcc_tip_surcharge_id_seq')
	</select>
	<insert id="insertMccTipSurchargeStg" >
		INSERT INTO  mcc_tip_surcharge_stg (mcc_tip_surcharge_id, tip_surcharge_id,mcc_id,CREATED_BY,CREATED_ON, request_state, last_operation, status) VALUES 
		(#{mccTipSurchargeId},cast( #{tipSurchargeId} as int), cast (#{mccId} as int), #{createdBy},#{createdOn}, #{requestState}, #{lastOperation}, #{status})
	</insert>
	<insert id="insertMccTipSurchargeMain">
		INSERT INTO   mcc_tip_surcharge (mcc_tip_surcharge_id, tip_surcharge_id,mcc_id,CREATED_BY,CREATED_ON, LAST_UPDATED_BY,LAST_UPDATED_ON, status) VALUES 
		(#{mccTipSurchargeId}, cast(#{tipSurchargeId} as int), cast(#{mccId} as int), #{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{status})
	</insert>
	<update id="updateMccTipSurchargeMain">
		UPDATE  mcc_tip_surcharge SET mcc_tip_surcharge_id=#{mccTipSurchargeId}, tip_surcharge_id= cast(#{tipSurchargeId} as int),mcc_id= cast(#{mccId} as int), LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status}  WHERE mcc_tip_surcharge_id = #{mccTipSurchargeId}
	</update>
	<update id="updateMccTipSurcharge">
		UPDATE  mcc_tip_surcharge_stg SET mcc_tip_surcharge_id=#{mccTipSurchargeId}, tip_surcharge_id= cast(#{tipSurchargeId} as int),mcc_id= cast(#{mccId} as int), LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status},request_state =#{requestState}, last_operation= #{lastOperation}, CHECKER_COMMENTS=''  WHERE mcc_tip_surcharge_id = #{mccTipSurchargeId}
	</update>
	<update id="updateMccTipSurchargeDiscard">
		UPDATE  mcc_tip_surcharge_stg SET mcc_tip_surcharge_id=#{mccTipSurchargeId}, tip_surcharge_id= cast(#{tipSurchargeId} as int),mcc_id= cast(#{mccId} as int), LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status},request_state ='A',last_operation= #{lastOperation}, CHECKER_COMMENTS=''  WHERE mcc_tip_surcharge_id = #{mccTipSurchargeId}
	</update>
	<update id="updateMccTipSurchargeRequestState">
		UPDATE  mcc_tip_surcharge_stg SET  request_state= #{requestState},status= #{status}, CHECKER_COMMENTS= #{checkerComments}, LAST_UPDATED_BY= #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, last_operation= #{lastOperation} WHERE mcc_tip_surcharge_id = #{mccTipSurchargeId}
	</update>
	<delete id="deleteDiscardedEntry">
		DELETE FROM  mcc_tip_surcharge_stg WHERE mcc_tip_surcharge_id = #{mccTipSurchargeId}
	</delete>
	<select id="validateDuplicateCheck" resultType="int">
		SELECT count(*) from  mcc_tip_surcharge_stg m where m.tip_surcharge_id  =cast( #{tipSurchargeId} as int) and m.mcc_id =cast (#{mccId} as int)
	</select>
</mapper>

