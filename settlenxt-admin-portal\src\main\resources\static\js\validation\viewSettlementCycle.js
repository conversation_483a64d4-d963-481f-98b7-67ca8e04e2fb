function viewActionCodeInfo(srNo, action) {
	var data = "srNo" + srNo;
	postData(action, data);
}


function editSettlementCycleInfo(srNo, action,reqState) {
	var data = "srNo," + srNo+",reqState," + reqState;
	postData(action, data);
}

function submitForm(url) {
	var data = "";
	postData(url, data);
}



function approve(url) {

	var srNo = $("#srNo").val();
	var remarks = $("#rejectReason").val();
	if ($('#apprej option:selected').val() == "A") {
		if ($("#rejectReason").val() != "") {


			var data = "srNo," + srNo + ",status," + "A" + ",remarks," + remarks;
	
			postData(url, data);
		}
		else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	}


	else if ($('#apprej option:selected').val() == "R") {
		if ($("#rejectReason").val() != "") {

			var dataRej = "srNo," + srNo + ",status," + "R" + ",remarks," + remarks;
			postData(url, dataRej);
		}
		else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else {
		$(".appRejMust").show();
		$('html, body').animate({ scrollTop: 0 }, 'slow');
		return false;
	}

}