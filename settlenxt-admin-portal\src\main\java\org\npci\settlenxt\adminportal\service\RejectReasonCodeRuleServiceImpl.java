package org.npci.settlenxt.adminportal.service;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.RejectReasonCodeRuleRepository;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Service
public class RejectReasonCodeRuleServiceImpl implements RejectReasonCodeRuleService {
	
	@Autowired
	RejectReasonCodeRuleRepository rejectReasonCodeRuleRepository;
	
	@Autowired
	SessionDTO sessionDTO;

	@Override
	public List<RejectReasonCodeDTO> getrejectReasonCodeRuleList() {
		
		return rejectReasonCodeRuleRepository.getRejectReasonCodeRule();
	}

	@Override
	public void addRejectReasonCodeRule(RejectReasonCodeDTO rejectReasonCodeDTO) {
		
		RejectReasonCodeDTO rejectReasonCodeDTOSeqId = rejectReasonCodeRuleRepository.fetchSeqId();
		rejectReasonCodeDTO.setSeqId(rejectReasonCodeDTOSeqId.getSeqId());
		
		
		rejectReasonCodeDTO.setCreatedOn(new Date());
		rejectReasonCodeDTO.setCreatedBy(sessionDTO.getUserName());
		rejectReasonCodeDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		rejectReasonCodeDTO.setLastOperation(CommonConstants.ADD_REJECT_REASON_CODE_RULE_LAST_OPERATION);
		rejectReasonCodeDTO.setStatus(CommonConstants.USER_ACTIVE_STATUS);
		rejectReasonCodeRuleRepository.addRejectReasonCode(rejectReasonCodeDTO);
		
	}

	@Override
	public RejectReasonCodeDTO getRejectReasonCode(int seqId) {
		
		return rejectReasonCodeRuleRepository.getRejectReasonCode(seqId);
	}
	
	@Override
	public RejectReasonCodeDTO getRejectReasonCodeStg(int seqId) {
		
		return rejectReasonCodeRuleRepository.getRejectReasonCodeStg(seqId);
	}

	@Override
	public List<RejectReasonCodeDTO> getPendingRejectReasonCodeList() {
		
		return rejectReasonCodeRuleRepository.getPendingRejectReasonCodeList();
	}

	@Override
	public RejectReasonCodeDTO getPendingRejectReasonCode(int seqId) {
		
		return rejectReasonCodeRuleRepository.getPendingRejectReasonCode(seqId);
	}

	@Override
	public RejectReasonCodeDTO updateApprovalStatus(int seqId, String status, String remarks) {
		
		RejectReasonCodeDTO rejectReasonCodeDTO = getPendingRejectReasonCode(seqId);
		rejectReasonCodeDTO.setRequestState(status);
		rejectReasonCodeDTO.setCheckerComments(remarks);
		
		
		
		
		if ("Approved".equals(status)) {
			rejectReasonCodeDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			rejectReasonCodeDTO.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
		} else {
			
			rejectReasonCodeDTO.setRequestState(CommonConstants.REQUEST_STATE_REJECTED);
		}

		

		if (rejectReasonCodeDTO.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
			
			try {
				updateRejectReasoncode(rejectReasonCodeDTO);
				
			} catch (Exception e) {
				
				log.error("Error while Updating in main reject reason code table");
			}
		}
		rejectReasonCodeDTO.setLastUpdatedBy(sessionDTO.getUserName());
		rejectReasonCodeDTO.setLastUpdatedOn(new Date());
		rejectReasonCodeDTO.setCheckerComments(remarks);
		rejectReasonCodeRuleRepository.updateRejectReasoncodeStg(rejectReasonCodeDTO);
		
		
		return rejectReasonCodeDTO;
		
	}
	
	private void updateRejectReasoncode(RejectReasonCodeDTO rejectReasonCodeDTO) {
		RejectReasonCodeDTO rejectReasonCodeDTOMain = rejectReasonCodeRuleRepository.getRejectReasonCode(rejectReasonCodeDTO.getSeqId());
			if (ObjectUtils.isEmpty(rejectReasonCodeDTOMain)) {
				
				rejectReasonCodeRuleRepository.saveRejectReasonCode(rejectReasonCodeDTO);
			
			} else {
				rejectReasonCodeDTO.setLastUpdatedBy(sessionDTO.getUserName());
				rejectReasonCodeDTO.setLastUpdatedOn(new Date());
				rejectReasonCodeRuleRepository.updateRejectReasonCode(rejectReasonCodeDTO);
			}

			rejectReasonCodeDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			
		}

	@Override
	public RejectReasonCodeDTO updateRejectReasonCodeStg(RejectReasonCodeDTO rejectReasonCodeDTO) {
	
		RejectReasonCodeDTO rejectReasonCodeStgDetail = rejectReasonCodeRuleRepository.getRejectReasonCodeStg(rejectReasonCodeDTO.getSeqId());
		rejectReasonCodeDTO.setLastUpdatedBy(sessionDTO.getUserName());
		rejectReasonCodeDTO.setLastUpdatedOn(new Date());
		rejectReasonCodeDTO.setCreatedBy(rejectReasonCodeStgDetail.getCreatedBy());
		rejectReasonCodeDTO.setCreatedOn(rejectReasonCodeStgDetail.getCreatedOn());
		rejectReasonCodeDTO.setStatus(CommonConstants.USER_ACTIVE_STATUS);
		rejectReasonCodeDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		rejectReasonCodeDTO.setLastOperation(CommonConstants.EDIT_REJECT_REASON_CODE_RULE_LAST_OPERATION);
		rejectReasonCodeRuleRepository.updateRejectReasoncodeStg(rejectReasonCodeDTO);
		return rejectReasonCodeDTO;
	}

	@Override
	public RejectReasonCodeDTO discardRejectReasonCode(int seqId) {
		
		RejectReasonCodeDTO rejectReasonCodeDTO = rejectReasonCodeRuleRepository.getPendingRejectReasonCode(seqId);
		RejectReasonCodeDTO rejectReasonCodeDTOMain = rejectReasonCodeRuleRepository.getRejectReasonCode(seqId);

		if (rejectReasonCodeDTOMain != null) {
			rejectReasonCodeDTOMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			rejectReasonCodeDTOMain.setLastOperation("Discarded");
			rejectReasonCodeRuleRepository.updateRejectReasoncodeStg(rejectReasonCodeDTOMain);
		} else {
			rejectReasonCodeRuleRepository.deleteRejectReasonCode(seqId);
		}

		return rejectReasonCodeDTO;
	}

	@Override
	public List<CodeValueDTO> fetchFuncCodeList() {
	
		return rejectReasonCodeRuleRepository.fetchFuncCodeList();
	}

	@Override
	public String updateBulkStgDisputeFee(String rejectReasonCodeList, String status) {
		
		try {
			String checkerComments = CommonConstants.RECORD_DESC_REJECTED;
			String reqState = CommonConstants.RECORD_REJECTED;
			if (StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
				checkerComments = CommonConstants.RECORD_DESC_APPROVED;
				reqState = "Approved";
			}
			String[] tranArray = rejectReasonCodeList.split("\\|");
			int id;
			for(String seqId:tranArray) {
				id = Integer.parseInt(seqId);
				updateApprovalStatus(id,reqState,checkerComments);
			}
		} catch (Exception e) {
			log.error("Error while approving/rejecting transition rule");
			return CommonConstants.RESULT_ERROR;
		}
		return CommonConstants.RESULT_SUCCESS;
	}

	@Override
	public List<RejectReasonCodeDTO> getRejectReasonCodeRulemasterList() {
		
		return rejectReasonCodeRuleRepository.getRejectReasonCodeRulemasterList();
	}

}
