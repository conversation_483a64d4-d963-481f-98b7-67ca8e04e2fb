<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript"
	src="./static/js/validation/ApproveActionCode.js"></script>


<form:form onsubmit="removeSpace(this); encodeForm(this);" method="POST"
	id="viewApproveActionCode" modelAttribute="actionCodeDTO" action="/approveActionCode"
	autocomplete="off">
	<div class="alert alert-danger appRejMust" role="alert">Please
		Select Approve/Reject action.</div>
		<div class="alert alert-danger remarkMust" role="alert">Please
		Enter Remarks.</div>
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="action.viewscreen.title" /></span></strong>
		</div>
		<div class="panel-body">
		
		<form:hidden path="actionCodeId" value="${actionCodeDTO.actionCodeId}" />
			
			<table class="table table-striped infobold" style="font-size: 12px">
			<caption style="display:none;">viewapproveactioncode</caption>
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
						<td colspan="6"><div class="panel-heading-red clearfix">
								<strong><span class="glyphicon glyphicon-info-sign"></span> <span
									data-i18n="Data"><spring:message
											code="ifsc.requestInformation" /></span></strong>
							</div></td>
						<td></td>
					</tr>
					<tr>
						<td><label><spring:message code="am.lbl.mti" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.mti}</td>
					
						<td><label><spring:message code="am.lbl.funcCode" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.funcCode}</td>
						
						<td><label><spring:message code="am.lbl.actionCode" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.actionCode}</td>
						
						
						
						
						</tr>
						
				<tr>
						
						<td><label><spring:message code="am.lbl.actionCodeDesc" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.actionCodeDesc}</td>
						
						<td><label><spring:message code="am.lbl.funcCodeDesc" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.funcCodeDesc}</td>
						
						<td><label><spring:message code="am.lbl.raisedBy" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.raisedBy}</td>
						
						
					<td><label><spring:message code="sm.lbl.requestStatus" /><span
								style="color: red"></span></label></td>
						<td><c:if test="${actionCodeDTO.requestState=='A' }">
								<spring:message code="ifsc.requestState.approved.description" />
							</c:if> <c:if test="${actionCodeDTO.requestState=='P' }">
								<spring:message
									code="ifsc.requestState.pendingApproval.description" />
							</c:if> <c:if test="${actionCodeDTO.requestState=='R' }">
								<spring:message code="ifsc.requestState.rejected.description" />
							</c:if> <c:if test="${actionCodeDTO.requestState=='D' }">
								<spring:message code="ifsc.requestState.discarded.description" />
							</c:if> &nbsp;</td>
						
						</tr>

					<tr>
						<td><label><spring:message code="ifsc.requestBy" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.lastUpdatedBy}</td>

						<td><label><spring:message
									code="ifsc.approverComments" /><span style="color: red"></span></label></td>
						<td>${actionCodeDTO.checkerComments}</td>
						
						<td><label><spring:message code="msg.lbl.requestType" /><span
								style="color: red"></span></label> </td>
						<td>${actionCodeDTO.lastOperation}</td>

					</tr>
						<tr>

								<td><label><spring:message code="am.lbl.allowedActncdToRemove" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.allowedActncdToRemove}</td>

						<td><label><spring:message code="st.lbl.capAmtCalReq" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.capAmtCalReq}</td>

						

						<td><label><spring:message code="am.lbl.transitionActionCode" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.transitionActionCode}</td>



					</tr>
					
					<tr>

								<td><label><spring:message code="am.lbl.tatPeriodDayType" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.tatPeriodDayType}</td>

						<td><label><spring:message code="am.lbl.tatPeriod" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.tatPeriod}</td>

						

						<td><label><spring:message code="am.lbl.defaultReasonRejCode" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.defaultReasonRejCode}</td>

					
					</tr>
					
				
					
			
			<c:if test="${actionCodeDTO.requestState eq 'P'}">
				 <sec:authorize access="hasAuthority('Approve Action Code')">
				
					
							<tr>
								<td colspan="6"><div class="panel-heading-red  clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> <span
											data-i18n="Data"><spring:message
													code="cap.approvalPanel.title" /></span></strong>
									</div></td>
							</tr>
							<tr>
								<td><label><spring:message
											code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
								<td><select name="select" id="apprej">
										<option value="N"><spring:message
												code="AM.lbl.select" /></option>
										<option value="A" id="approve"><spring:message
												code="AM.lbl.approve" /></option>
										<option value="R" id="reject"><spring:message
												code="AM.lbl.reject" /></option>
								</select></td>
								<td>
									<div style="float:center;">
										<label><spring:message code="AM.lbl.remarks" /><span
											style="color: red">*</span></label>
									</div>
								</td>
								<td colspan="5"><textarea rows="4" cols="50"
										maxlength="100" id="rejectReason"></textarea>
									<div id="errorrejectReason" class="error"></div></td>
							</tr>
							</sec:authorize> 
							
							 
							
							
							</c:if>
			
						</tbody>
					</table>
				

			<div class="row">
				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center">
					
						<sec:authorize access="hasAuthority('Approve Action Code')">
							<c:if test="${actionCodeDTO.requestState eq 'P'}">
								<input name="button10" type="button" class="btn btn-success"
									id="approveRole"
									value="<spring:message
							code="ifsc.submitBtn" />"
									onclick="approve('/approveActionCode');" />
							</c:if>
						</sec:authorize>
						
						<button type="button" class="btn btn-danger"
							onclick="submitForm('/actionCodePendingForApproval');">
							<spring:message code="ifsc.backBtn" />
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</form:form>
