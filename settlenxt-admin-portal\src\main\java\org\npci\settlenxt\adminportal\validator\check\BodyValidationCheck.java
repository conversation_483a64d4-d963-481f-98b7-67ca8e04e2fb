package org.npci.settlenxt.adminportal.validator.check;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.mapping.DataElementNameMapping;
import org.npci.settlenxt.adminportal.common.mapping.FieldConstraints;
import org.npci.settlenxt.adminportal.common.mapping.MessageFormatNameMapping;
import org.npci.settlenxt.adminportal.common.mapping.NameMappingContext;
import org.npci.settlenxt.adminportal.common.mapping.ReasonCodes;
import org.npci.settlenxt.adminportal.common.util.LoadRejectReasonCode;
import org.npci.settlenxt.adminportal.common.util.ValidationType;
import org.npci.settlenxt.adminportal.common.util.ValidationUtils;
import org.npci.settlenxt.adminportal.validator.service.dto.CustomMappingUtils;
import org.npci.settlenxt.adminportal.validator.service.dto.Record;
import org.npci.settlenxt.adminportal.validator.service.dto.RecordError;
import org.npci.settlenxt.adminportal.validator.service.dto.TransactionRecord;
import org.npci.settlenxt.adminportal.validator.service.dto.ValidationResult;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.Utils;

import lombok.extern.slf4j.Slf4j;

/**
 * Process files transaction tags validation
 * <AUTHOR>
 *
 */
@Slf4j
public class BodyValidationCheck implements IValidationCheck {
	
	
	private int validRecordCount = 0;
	private int invalidRecordCount = 0;
	
	
	private Map<Integer, List<Record>> invalidTxnRecords;
	private Map<Integer, List<Record>> validTxnRecords;
	
	private String userParticipantId;
	private static final String NACQINSTCD = "nAcqInstCd";
	private static final String NCCYCDTXN = "nCcyCdTxn";
	private static final String NICCDATA = "nICCData";
	private static final String NPOSDATACD = "nPosDataCd";
	private static final String NSERVCD = "nServCd";
	private static final String NTXNORGINSTCD = "nTxnOrgInstCd";
	private static final String NFUNCD = "nFunCd";
	private static final String NAMTTXN = "nAmtTxn";
	private static final String NRECNUM = "nRecNum";
	private static final String CURRENT_RECORD_HAS_ERRORS ="currentRecordHasErrors";
	private static final String VALIDMSGTYPE= "validMessageType";
	private static final String ISLENVALID="isLengthValid";
	private static final String ISPATTERNVALID="isPatternValid";
	
	
	public BodyValidationCheck(String userParticipantId) {
		this.invalidTxnRecords = new HashMap<>();
		this.validTxnRecords = new HashMap<>();
		this.userParticipantId = userParticipantId;
		
	}
	
	@Override
	public void validate(ValidationResult validationResult, List<TransactionRecord> txnRecords, int taskIdsSizeForSplit, LoadRejectReasonCode rejReasonCode) {		

		String xml;
		String mti; 
		String funcCode; 
		String fileName;
		String nPosEntModeStr;
		String fileParticipantId;
		String typeOfCurrentElement;
		
		
		String nCrdAcpLocStr;
		String nPosDataCdStr;

		List<RecordError> fieldParsingErrorList;
		List<RecordError> requiredErrorsList;

		Map<Integer, List<RecordError>> conditionalErrors;
		Map<Integer, List<RecordError>> unexpectedFieldErrors;
		
		Map<Integer, List<RecordError>> fieldParsingErrors;

		boolean nARDPatternValidation;
		boolean validRecordNum;
		boolean recNumValid;
		boolean validMessageType;
		boolean isLengthValid;
		boolean isPatternValid;
		boolean nTxnOrgInstCdValidationAvailable;
		boolean nAmtTxnValidationAvailable;
		boolean twoSameMessages;
		boolean currentElementTypeNumeric;
		boolean nrecNumLengthOrPatternInvalid;
		boolean nAcqInstCdLengthOrPatternInvalid;

		Integer recordNumber;
		
		
		Integer nAmtTxnInt;
		Integer nCcyCd;


		StringBuilder xmlTransactionsWithErrorsWritten = new StringBuilder();

		List<Record> records = null;
		
		boolean currentRecordHasErrors;
		List<Integer> existingTransactions = new ArrayList<>();
		int totalRecordCount = 0;
	
		for (TransactionRecord txnRecord : txnRecords) {
		
			xml = validationResult.getXmlTransactions().get(totalRecordCount);
		
			nARDPatternValidation = true;
			
			fieldParsingErrorList = new ArrayList<>();
			currentRecordHasErrors = false;
			log.info("=======================================================================================================================================");
			log.info("  NON NPCIMAKER Validating Txn Record : " + txnRecord.getRecordNumber());
			log.info("=======================================================================================================================================");
			records = txnRecord.getRecords();
			
			Map<String, String> recordMap = ValidationUtils.listAsMap(records);
			
			recordNumber = 0;
			validRecordNum = true;
			try{
				recordNumber = Integer.valueOf(recordMap.get(NRECNUM));
			}
			catch(Exception dd){
				validRecordNum = false;
			}
			if(validRecordNum){		
			recNumValid = !existingTransactions.contains(recordNumber);
			
				if (!recNumValid) {
					currentRecordHasErrors = true;
					
					validationResult.addRecordError(recordNumber, new RecordError(5036, "Invalid Record Number", NRECNUM, recordMap.get(NRECNUM), xml));
				} else {
					existingTransactions.add(recordNumber);
				}
			}
			mti = recordMap.get("nMTI");
			funcCode = recordMap.get(NFUNCD);
			fileName = validationResult.getFileName().substring(0, 2);
			
			Map<String,Boolean> flags = handleForMTIAndFuncCode(validationResult, xml, fileName, recordNumber,
					recordMap,currentRecordHasErrors);
			validMessageType=flags.get("");
			currentRecordHasErrors=flags.get(CURRENT_RECORD_HAS_ERRORS);
			
			
			if (validMessageType) {
				
				requiredErrorsList =  ValidationUtils.validateRequiredFields(ValidationType.TRANSACTION, recordMap, xml, rejReasonCode);			
				conditionalErrors = validateConditionalFields(recordMap, xml);
				unexpectedFieldErrors = validateNoUnexpectedFields(txnRecord, recordMap, xml);
				
				currentRecordHasErrors = handleReErrorList(validationResult, requiredErrorsList, currentRecordHasErrors,
						recordMap);
				
				if (null != conditionalErrors && !conditionalErrors.isEmpty()) {
					validationResult.setFullFileReject(true);
					validationResult.addRecordErrors(conditionalErrors);
					
					currentRecordHasErrors = true;
				}
				
				if (null != unexpectedFieldErrors && !unexpectedFieldErrors.isEmpty()) {
					validationResult.addRecordErrors(unexpectedFieldErrors);
					
					currentRecordHasErrors = true;
				}
				
				Map<String,Boolean> flag = handleDigitsInCurrentNARD(validationResult, xml, nARDPatternValidation,currentRecordHasErrors,
						recordNumber, recordMap);
				nARDPatternValidation= flag.get("nARDPatternValidation");
				currentRecordHasErrors= flag.get(CURRENT_RECORD_HAS_ERRORS);
				isLengthValid = true;
				isPatternValid = true;			
				
				// Offline Presentment Validation - First two digit should start with 2 or 6
				currentRecordHasErrors = handleNservCd(validationResult, xml, mti, funcCode, recordNumber,
						currentRecordHasErrors, recordMap);
				
				// Offline Presentment Validation - nCrdAcpLoc should be equal
				// to Last 20 Char of nPosDataCd

				try {
					nCrdAcpLocStr = recordMap.get("nCrdAcpLoc");
					nPosDataCdStr = recordMap.get(NPOSDATACD);

					if (null != recordMap.get(NPOSDATACD)) {
						log.info("5167 - nPosDataCd NOT NULL");
						log.info("To validate nPosDataCd last 20 chars against total 20 chars of nCrdAcpLoc.");
						log.info(
								"nPOSDataCd Substring Last 20 " + nPosDataCdStr.substring(nPosDataCdStr.length() - 20));
						log.info("nCrdAcpLoc " + nCrdAcpLocStr);
						if (!nCrdAcpLocStr.equals(nPosDataCdStr.substring(nPosDataCdStr.length() - 20))) {
							validationResult.addRecordError(recordNumber,
									new RecordError(5167, "Invalid Card Acceptor Location/address", NPOSDATACD,
											recordMap.get(NPOSDATACD), xml));
							currentRecordHasErrors = true;
							log.info("5167 - 20-DIGIT VALIDATION FAILURE");
						}
					}
				} catch (Exception ex) {
					log.info("5167 - nPosDataCd EXCEPTION");
					validationResult.addRecordError(recordNumber, new RecordError(5167,
							"Invalid Card Acceptor Location/address", NPOSDATACD, recordMap.get(NPOSDATACD), xml));
					currentRecordHasErrors = true;
				}
				
				
				nPosEntModeStr = recordMap.get("nPosEntMode");
				if(StringUtils.isNotBlank(nPosEntModeStr) && nPosEntModeStr.length() >=2){
					nPosEntModeStr = nPosEntModeStr.substring(0,2);
				}
				// To check nICCData only for nPosEntMode starts with 05 - ICC or 95 - Chip card with unreliable CVD or iCVD or 07 -
				// Contactless Payment Using Chip Card. or 91 - Contactless using CVD, ICVD checking Possible
				if (recordMap.get(NICCDATA) != null && StringUtils.isNotBlank(nPosEntModeStr) && ("05".equals(nPosEntModeStr) || "07".equals(nPosEntModeStr) || "91".equals(nPosEntModeStr) || "95".equals(nPosEntModeStr))) {
					log.info("nICCDATA: ENTRY -- its a Offline Presentment and nICCData available.");
					
					Map<String,Boolean>map=new HashMap<>();
					map.put(ISLENVALID,isLengthValid);
					map.put(ISPATTERNVALID,isPatternValid);
					Map<String,Boolean> map1 = handleNiccData(map, recordMap);
					isLengthValid=map1.get(ISLENVALID);
					isPatternValid=map1.get(ISPATTERNVALID);
					log.info("nICCDATA: EXIT -- its a Offline Presentment and nICCData available.");
				}
				if (!isLengthValid || !isPatternValid) {
					log.info("nICCData: Lenght or Pattern is Invalid for one of the nICCData tags.");

					validationResult.addRecordError(recordNumber, new RecordError(5147,
							"Invalid Format ICC system related data", NICCDATA, recordMap.get(NICCDATA), xml));
					currentRecordHasErrors = true;
					log.info("nICCData: Record has errors");
				}
				//SIT_R12_D450: To check if the Must be present validation is already there. if available to restrict invalid error message to be added.
				nTxnOrgInstCdValidationAvailable = false;
				nAmtTxnValidationAvailable = false;
				if(CollectionUtils.isNotEmpty(requiredErrorsList)){
					log.info("SIT_R12_D450: requiredErrorsList is not empty");
					Iterator<RecordError> it = requiredErrorsList.iterator();
					while (it.hasNext()) {
						RecordError recordErrorCurrent =  it.next();
						if(ObjectUtils.isNotEmpty(recordErrorCurrent.getErrorNo()) && recordErrorCurrent.getErrorNo() == 5057){
							nTxnOrgInstCdValidationAvailable = true;
							log.info("SIT_R12_D450: nTxnOrgInstCdValidationAvailable is SET TO TRUE");
						}else if(ObjectUtils.isNotEmpty(recordErrorCurrent.getErrorNo()) && recordErrorCurrent.getErrorNo() == 5050){
							nAmtTxnValidationAvailable = true;
							log.info("SIT_R12_D450: nAmtTxnValidationAvailable is SET TO TRUE");
						}
					}
				}
				List<String> optionalFields = CustomMappingUtils.getMessageFormatNameMapping(NameMappingContext.MESSAGEFORMAT, mti+funcCode).getFields().getOptionalFields();
				if(Boolean.FALSE.equals(optionalFields.isEmpty()) && optionalFields.contains(NTXNORGINSTCD)) {
					nTxnOrgInstCdValidationAvailable = true;
				}
				fileParticipantId= null;
				try {
					fileParticipantId = recordMap.get(NTXNORGINSTCD);
					if (!nTxnOrgInstCdValidationAvailable && (StringUtils.isEmpty(fileParticipantId) || !fileParticipantId.equals(StringUtils.trim(userParticipantId)))) {
						validationResult.addRecordError(recordNumber, new RecordError(5058,"Invalid Transaction Originator Institution ID Code",
								NTXNORGINSTCD, recordMap
														.get(NTXNORGINSTCD), xml));
						currentRecordHasErrors = true;
					}
				} catch (Exception exxxx) {
					log.error(exxxx.getMessage());
				}
				
				nAmtTxnInt= 0;

				try{
					
					nAmtTxnInt = Integer.valueOf(recordMap.get(NAMTTXN));
		

				if(nAmtTxnInt<=0){
					validationResult.addRecordError(recordNumber, new RecordError(5218, "Amount cannot be less than or equal to Zero", NAMTTXN, recordMap.get(NAMTTXN), xml));
					currentRecordHasErrors = true;
				} 
				
				}
				catch(Exception exxxx){
					log.error(exxxx.getMessage());
				}
				
				try{
					nAmtTxnInt = Integer.valueOf(recordMap.get(NAMTTXN));
					nCcyCd = Integer.valueOf(recordMap.get(NCCYCDTXN));
					
					if ( !( nCcyCd.equals(356) || nCcyCd.equals(64) || nCcyCd.equals(524) ) && null != nCcyCd) {//Adding currency code 064 and comparing nCcycd==64
						validationResult.addRecordError(recordNumber, new RecordError(5052, "Invalid Currency Code",
								NCCYCDTXN, recordMap.get(NCCYCDTXN), xml));
						currentRecordHasErrors = true;
					}
					

					
					if (!nAmtTxnValidationAvailable && null != recordMap.get(NAMTTXN) && nAmtTxnInt == 0) {
						
							validationResult.addRecordError(recordNumber, new RecordError(5218,
									"Amount cannot be less than or equal to Zero", NAMTTXN, recordMap.get(NAMTTXN), xml));
							currentRecordHasErrors = true;
						
					}
					
				}
				catch(Exception ex){
					log.error(ex.getMessage());
				}

			}
			
			fieldParsingErrors = new HashMap<>();
			
			for(Record recod : records) {				
				if (recod.getMapping() == null) {
					continue;
				}
				
				ReasonCodes reasonCodes = recod.getMapping().getReasonCodes();
				
				int invalidLength = -1;
				int invalidType = -1;
				if (reasonCodes != null) {
					invalidLength = reasonCodes.getInvalidLength();
					invalidType = reasonCodes.getInvalidType();
				}
				
				if (invalidLength == -1) {
					invalidLength = 3404;
				}
				
				if (invalidType == -1) {
					invalidType = 3404;
				}
				twoSameMessages= false;
				currentElementTypeNumeric = false;
				nrecNumLengthOrPatternInvalid = false;
				nAcqInstCdLengthOrPatternInvalid = false;
				if(!recod.getName().equalsIgnoreCase("nARD") && !recod.isLengthValid() && !recod.getName().equalsIgnoreCase(NICCDATA)) {
					if (invalidLength == invalidType) {
						twoSameMessages = true;
					}
					fieldParsingErrorList.add(new RecordError(invalidLength, "Length is not valid", recod.getName(), recod.getValue(), xml));
					
					currentRecordHasErrors = true;
					if (recod.getName().equalsIgnoreCase(NRECNUM)) {
						nrecNumLengthOrPatternInvalid = true;
					}else if (recod.getName().equalsIgnoreCase(NACQINSTCD)) {
						log.info("nAcqInstCd - SET TRUE in ICCDATA-1");
						nAcqInstCdLengthOrPatternInvalid = true;
					}

				}
				if (!recod.getName().equalsIgnoreCase("nARD") && !twoSameMessages && !recod.isPatternValid() && !recod.getName().equalsIgnoreCase(NICCDATA)) {
					typeOfCurrentElement  = recod.getMapping().getDataType();
					if("N".equals(typeOfCurrentElement)){
						try{
							if(StringUtils.isNumeric(recod.getValue())){
							currentElementTypeNumeric = true;
							}
						}catch(Exception ex){
							log.error(ex.getMessage());
						}
					}
					if(!currentElementTypeNumeric){
						fieldParsingErrorList.add(new RecordError(invalidType, "Value does not match pattern", recod.getName(), recod.getValue(), xml));					
						currentRecordHasErrors = true;
					}

					if (recod.getName().equalsIgnoreCase(NRECNUM)) {
						nrecNumLengthOrPatternInvalid = true;
					} else if (recod.getName().equalsIgnoreCase(NACQINSTCD)) {
						log.info("nAcqInstCd - SET TRUE in ICCDATA-2");
						nAcqInstCdLengthOrPatternInvalid = true;
					}

				}
				boolean[] flagData = {nARDPatternValidation,nrecNumLengthOrPatternInvalid,nAcqInstCdLengthOrPatternInvalid,currentRecordHasErrors};
				currentRecordHasErrors = handleAcqInstdCd(txnRecords, xml,recordNumber,fieldParsingErrorList, 
						   flagData,recordMap, recod);
				
				
			}
			
			fieldParsingErrors.put(recordNumber, fieldParsingErrorList);
			
			validationResult.addRecordErrors(fieldParsingErrors);
			
			totalRecordCount = handleRecordCounter(validationResult, xml, recordNumber,
					xmlTransactionsWithErrorsWritten, records, currentRecordHasErrors, totalRecordCount);
			
			printMemoryUsageStats("BodyValidation After Big For loop.");
		}
		
		validationResult.setInvalidRecordCount(invalidRecordCount);
		validationResult.setValidRecordCount(validRecordCount);
		validationResult.setTotalRecordCount(totalRecordCount);
		validationResult.setValidTxnRecords(validTxnRecords);
		validationResult.setInValidTxnRecords(invalidTxnRecords);
		if(StringUtils.isNotBlank(xmlTransactionsWithErrorsWritten)){
			validationResult.setXmlTransactionsWithErrorsWritten(xmlTransactionsWithErrorsWritten.toString());
		}
		
		
	}

	private boolean handleReErrorList(ValidationResult validationResult, List<RecordError> requiredErrorsList,
			boolean currentRecordHasErrors, Map<String, String> recordMap) {
		Map<Integer, List<RecordError>> requiredErrors;
		Integer currentnrecNum;
		if(!requiredErrorsList.isEmpty()){
			requiredErrors = new HashMap<>();
			
			
			
				currentnrecNum = Integer.valueOf(recordMap.get(NRECNUM));
		
		
				if (!requiredErrorsList.isEmpty()) {
					requiredErrors.put(currentnrecNum, requiredErrorsList);
				}

			validationResult.setFullFileReject(true);
			validationResult.addRecordErrors(requiredErrors);
			
			currentRecordHasErrors = true;
		}
		return currentRecordHasErrors;
	}

	private boolean handleNservCd(ValidationResult validationResult, String xml, String mti, String funcCode,
			Integer recordNumber, boolean currentRecordHasErrors, Map<String, String> recordMap) {
		String nServCdStr;
		if ("1240".equals(mti) && "260".equals(funcCode) && recordMap.get(NSERVCD) != null) {					
			try{
				nServCdStr = recordMap.get(NSERVCD);
				log.info("Offline Presentment Entered with Value " + nServCdStr);
				if (StringUtils.isNotBlank(nServCdStr) && nServCdStr.length() >= 2) {
					nServCdStr = nServCdStr.substring(0, 1);
					log.info("Offline Presentment After Substring " + nServCdStr);
				}
				
				if (!"2".equals(nServCdStr) && !"6".equals(nServCdStr) && StringUtils.isNotBlank(nServCdStr)) {
					validationResult.addRecordError(recordNumber, new RecordError(7038,
							"Compliance Rejected due to ServiceCode1ID", NSERVCD, recordMap.get(NSERVCD), xml));
					currentRecordHasErrors = true;
					
					log.info("Offline Presentment Error 7038 thrown" + nServCdStr);
				}
			}
			catch(Exception ex){
				log.info("nServCd: exception");
			}					
		}
		return currentRecordHasErrors;
	}

	private Map<String, Boolean> handleNiccData(Map<String,Boolean> map, Map<String, String> recordMap) {
		Boolean isPatternValid=map.get(ISPATTERNVALID);
		Boolean isLengthValid=map.get(ISLENVALID);
		try {
			String nICCDataValue = recordMap.get(NICCDATA);
			if (StringUtils.isNotBlank(nICCDataValue) && nICCDataValue.length() >= 2) {
				log.info("nICCData: nICCDataValue as string BEFORE stripping prefix and suffix: "
						+ nICCDataValue);
				nICCDataValue = nICCDataValue.substring(1, nICCDataValue.length() - 1);
			}
			log.info("nICCData: nICCDataValue as string AFTER stripping prefix and suffix: "
					+ nICCDataValue);
			String[] nICCDataValueAsArray = nICCDataValue.split(",");
			log.info("nICCData: nICCDataValueAsArray is generated by splitting it with comma.");
			for (String keyValuePair : nICCDataValueAsArray) {
				log.info("nICCData: Iterating over nICCDataValueAsArray");
				if (isLengthValid && isPatternValid) {
					log.info("nICCData: Length and Pattern are valid before checking the element.");
					String currentIccDataSubKey = StringUtils.trim(keyValuePair.split("=")[0]);
					log.info("nICCData: current currentIccDataSubKey is " + currentIccDataSubKey);
					String currentIccDataSubValue = keyValuePair.split("=")[1];
					log.info("nICCData: current currentIccDataSubValue is " + currentIccDataSubValue);
					DataElementNameMapping dataElementMappingForIccSubKey = CustomMappingUtils
							.getDataElementNameMapping(NameMappingContext.DATAELEMENTS,
									currentIccDataSubKey);
					if (ObjectUtils.isNotEmpty(dataElementMappingForIccSubKey)) {
						log.info(
								"nICCData: dataElementMappingForIccSubKey is not empty and ready for length and Pattern check.");
						String minLen = null;
						String maxLen = null;
						minLen = getMinLenSubKey(currentIccDataSubKey, dataElementMappingForIccSubKey, minLen);
						maxLen = maxLenSubKey(currentIccDataSubKey, dataElementMappingForIccSubKey, maxLen);
						isLengthValid = setValueOfIsLengthValid(isLengthValid, currentIccDataSubKey,
								currentIccDataSubValue, minLen, maxLen);
						isPatternValid = checkPatternValidity(isPatternValid, currentIccDataSubKey,
								currentIccDataSubValue, dataElementMappingForIccSubKey);
					} else {
						log.info("nICCData: Data Mapping not found for " + currentIccDataSubKey);
					}
				} else {
					log.info("nICCData: Either length or pattern is invalid and breaking the loop.");
					break;
				}
			}
		} catch (Exception iccDataEx) {
			log.info("nICCData: ICC DATA EXCEPTION");

		} 
		map.put(ISLENVALID, isLengthValid);
		map.put(ISPATTERNVALID,isPatternValid );
		return map;
	}

	private Boolean setValueOfIsLengthValid(Boolean isLengthValid, String currentIccDataSubKey,
			String currentIccDataSubValue, String minLen, String maxLen) {
		if (StringUtils.isEmpty(currentIccDataSubValue)) {
			log.info(
					"nICCData: length check failed as empty value for currentIccDataSubValue: "
							+ currentIccDataSubKey);
			isLengthValid = false;
		}
		if (currentIccDataSubValue.length() < Integer.parseInt(minLen)
				|| currentIccDataSubValue.length() > Integer.parseInt(maxLen)) {
			log.info(
					"nICCData: length check failed maxLen/minLen for currentIccDataSubValue: "
							+ currentIccDataSubKey);
			isLengthValid = false;
		}
		return isLengthValid;
	}

	private Map<String, Boolean> handleForMTIAndFuncCode(ValidationResult validationResult, String xml,
			String fileName, Integer recordNumber, Map<String, String> recordMap,Boolean currentRecordHasErrors) {
		boolean validMessageType;
		String mti = recordMap.get("nMTI");
		String funcCode = recordMap.get(NFUNCD);
		Map<String,Boolean> flags=new HashMap<>();
		List<String> mtiList;
		List<String> functionCodeList;
		validMessageType = true;
		if (StringUtils.isNotBlank(mti) && StringUtils.isNotBlank(funcCode)) {
			MessageFormatNameMapping mapping = CustomMappingUtils.getMessageFormatNameMapping(NameMappingContext.MESSAGEFORMAT, mti + funcCode);
			//SIT_R12_D465: To check if MTI and Function code are part of allowed list.
			mtiList = CustomMappingUtils.getMtiList();
			functionCodeList = CustomMappingUtils.getFunctionCodeList();
			log.info("List is empty: {}", CollectionUtils.isEmpty(mtiList));
			log.info("MTI coming from file : {}", mti);
			log.info("MTI LIST: {}", mtiList.toString());
			if(CollectionUtils.isEmpty(mtiList) || !mtiList.contains(mti)){
				validationResult.addRecordError(recordNumber, new RecordError(5001, "Invalid MTI", "nMTI", recordMap.get("nMTI"), xml));
				currentRecordHasErrors = true;
				validMessageType = false;
			} else if(CollectionUtils.isEmpty(functionCodeList) || !functionCodeList.contains(funcCode)){
				validationResult.addRecordError(recordNumber, new RecordError(5005, "Invalid Function Code", NFUNCD, recordMap.get(NFUNCD), xml));
				currentRecordHasErrors = true;
				validMessageType = false;
			} 
			else if (mapping == null) {
				validationResult.addRecordError(recordNumber, new RecordError(5204, "Invalid function code for the mentioned MTI", "nMTI", recordMap.get("nMTI"), xml));
				currentRecordHasErrors = true;
				validMessageType = false;
			}
		}else{
				if (StringUtils.isEmpty(mti)) {
					validationResult.addRecordError(recordNumber, new RecordError(5034, "MTI must be present in Transaction Message.", "nMTI", recordMap.get("nMTI"), xml));
					currentRecordHasErrors = true;
				}
				if (StringUtils.isEmpty(funcCode)) {
					validationResult.addRecordError(recordNumber, new RecordError(5211, "Base function code is not present for current transaction.", "nMTI", recordMap.get("nMTI"), xml));
					currentRecordHasErrors = true;
				}
				validMessageType = false;
		}
		
		Map<String,Boolean>map =new HashMap<>();
		map.put(VALIDMSGTYPE,validMessageType);
		map.put(CURRENT_RECORD_HAS_ERRORS, currentRecordHasErrors);
		String[] arr=new String[]{xml,mti,funcCode,fileName};
		Map<String,Boolean> map1= handleFileName00Or80(validationResult, recordNumber, recordMap,map,arr);
		validMessageType=map1.get(VALIDMSGTYPE);
		currentRecordHasErrors=map1.get(CURRENT_RECORD_HAS_ERRORS);
		flags.put(VALIDMSGTYPE, validMessageType);
		flags.put(CURRENT_RECORD_HAS_ERRORS, currentRecordHasErrors);
		return flags;
	}

	private Map<String, Boolean> handleFileName00Or80(ValidationResult validationResult, Integer recordNumber, Map<String, String> recordMap,Map<String, Boolean> map,String[] arr) {
		Boolean validMessageType= map.get(VALIDMSGTYPE);
		Boolean currentRecordHasErrors= map.get(CURRENT_RECORD_HAS_ERRORS);
		if(Boolean.TRUE.equals(validMessageType)){
			
			if("00".equals(arr[3])){
				if("1240".equals(arr[1]) && "999".equals(arr[2])){
					validationResult.addRecordError(recordNumber, new RecordError(6023, "Onus Transaction not allowed", "nMTI", recordMap.get("nMTI"), arr[0]));
					currentRecordHasErrors = true;
					validMessageType= false;
				}
			}
			else if("80".equals(arr[3]) && (!"1240".equals(arr[1]) || !"999".equals(arr[2]))){
				
					validationResult.addRecordError(recordNumber, new RecordError(6028, "Not a Onus Transaction", "nMTI", recordMap.get("nMTI"), arr[0]));
					currentRecordHasErrors = true;
					validMessageType = false;
				}
			
		}
		map.put(VALIDMSGTYPE, validMessageType);
		map.put(CURRENT_RECORD_HAS_ERRORS, currentRecordHasErrors);
		return map;
	}

	private Map<String, Boolean> handleDigitsInCurrentNARD(ValidationResult validationResult, String xml,
			boolean nARDPatternValidation,boolean currentRecordHasErrors, Integer recordNumber, Map<String, String> recordMap) {
		Map<String,Boolean>flags=new HashMap<>();
		if (recordMap.get("nARD") != null){
			
			BigInteger currentNARD;
			
			try {
				currentNARD = new BigInteger(recordMap.get("nARD"));

				String digitsInCurrentNARD = currentNARD.toString();
				int nARDTotalWithoutCheckDigit = 0;
				if (digitsInCurrentNARD.length() == 23) {

					int counter = 1;

						for (int i = 0; i < digitsInCurrentNARD.length() - 1; i++) {
							int digitPickedFrom22 =  digitsInCurrentNARD
									.charAt(i) - '0';
							digitPickedFrom22 = handleForEvenCounter(counter, digitPickedFrom22);
							nARDTotalWithoutCheckDigit = nARDTotalWithoutCheckDigit
									+ digitPickedFrom22;
							counter = counter + 1;
						}

				}
				else if(digitsInCurrentNARD.length() > 23 || digitsInCurrentNARD.length() < 23){
					log.info("5042 - nARD-LENGTH-FAILURE");
					nARDPatternValidation = false;
					currentRecordHasErrors = true;
					validationResult.addRecordError(recordNumber, new RecordError(5042, "Invalid Acquirer Reference Data", "nARD", recordMap.get("nARD"), xml));							
				}

			} catch (Exception exx) {
				log.info("5042 - EXCEPTION");
				validationResult.addRecordError(recordNumber, new RecordError(5042, "Invalid Acquirer Reference Data", "nARD", recordMap.get("nARD"), xml));
				currentRecordHasErrors = true;
			}
		}
		flags.put("nARDPatternValidation", nARDPatternValidation);
		flags.put(CURRENT_RECORD_HAS_ERRORS, currentRecordHasErrors);
		return flags;
	}

	private int handleForEvenCounter(int counter, int digitPickedFrom22) {
		if (counter % 2 == 0) {
			digitPickedFrom22 = digitPickedFrom22 * 2;
			int isDoubleCharacterCheckLength = String.valueOf(digitPickedFrom22).length();
			if (isDoubleCharacterCheckLength > 1) {
				int lastDigitPickedDoubleSize = digitPickedFrom22;
				int sum = 0;
				while (lastDigitPickedDoubleSize > 0) {
					sum = sum + lastDigitPickedDoubleSize % 10;
					lastDigitPickedDoubleSize = lastDigitPickedDoubleSize / 10;
				}
				digitPickedFrom22 = sum;
			}
		}
		return digitPickedFrom22;
	}

	private int handleRecordCounter(ValidationResult validationResult, String xml, Integer recordNumber,
			StringBuilder xmlTransactionsWithErrorsWritten, List<Record> records, boolean currentRecordHasErrors,
			int totalRecordCount) {
		if (!currentRecordHasErrors) {
			
			validRecordCount++;
			validTxnRecords.put(recordNumber, records);
			xmlTransactionsWithErrorsWritten.append(xml + "\n");
		} else {
			
			invalidRecordCount++;
			invalidTxnRecords.put(recordNumber, records);
			log.info("===============================================================");
			log.info("recordNumber :- {}", recordNumber);


			StringBuilder xmlError = new StringBuilder("");
			Map<Integer, String> recordErrorMap = new HashMap<>();
			
			
				for (RecordError error : validationResult.getRecordErrors().get(recordNumber)) {
					if (!recordErrorMap.containsKey(error.getErrorNo())) {
						recordErrorMap.put(error.getErrorNo(), error.getErrorDescription());
						log.info("ErrorNo :- {} ---- ErrorDescription :- {}",error.getErrorNo() ,error.getErrorDescription());
					}
				}
			
				log.info("===============================================================");
			
			printMemoryUsageStats("BodyValidation AFTER ADDING recordErrorMap.put FOR recordNumber " + recordNumber);
			for (Map.Entry<Integer, String> entry : recordErrorMap.entrySet()) {
				xmlError.append("<nRejectCode>" + entry.getKey() + "</nRejectCode>\n");
				xmlError.append("<nRejectReason>" + entry.getValue() + "</nRejectReason>\n");
			}
			xml = xml.replace("</Txn>", xmlError.toString() + "</Txn>\n");
			xmlTransactionsWithErrorsWritten.append(xml);
			printMemoryUsageStats("BodyValidation AFTER APPENDING XML FOR recordNumber " + recordNumber);
		}
		
		totalRecordCount++;
		return totalRecordCount;
	}

	private boolean handleAcqInstdCd(List<TransactionRecord> txnRecords, String xml,Integer recordNumber,
			List<RecordError> fieldParsingErrorList, boolean[] flagData, Map<String, String> recordMap, Record recod) {
		String currentNARDValue;
		String currentNAcqInstCd;
		
		boolean nARDPatternValidation = flagData[0];
		boolean nrecNumLengthOrPatternInvalid = flagData[0];
		boolean nAcqInstCdLengthOrPatternInvalid = flagData[0];
		boolean currentRecordHasErrors = flagData[0];
		
		if(recod.getName().equalsIgnoreCase(NRECNUM) && !nrecNumLengthOrPatternInvalid){
			if(recordNumber<2){
				fieldParsingErrorList.add(new RecordError(5036, " Invalid Record Number", recod.getName(), recod.getValue(), xml));
				currentRecordHasErrors = true;
			} else if (txnRecords.size() + 1 < recordNumber) {
				log.info("SIT_R12_D445: Full file is getting rejected when mandatory tags are not Present:  To include nRecNum check for list of records.");
				fieldParsingErrorList.add(new RecordError(5036, "Invalid Record Number ", recod.getName(),
						recod.getValue(), xml));
				currentRecordHasErrors = true;
			}
		} 
		else if(recod.getName().equalsIgnoreCase("nARD") && nARDPatternValidation && !nAcqInstCdLengthOrPatternInvalid){
			log.info("6100 - VALIDATION ENTERED");
			try{
				currentNARDValue =recordMap.get("nARD");
				currentNAcqInstCd = recordMap.get(NACQINSTCD);
				if(null != recordMap.get(NACQINSTCD)){
					log.info("6100 - nAcqInstCd NOT NULL");
					log.info("SIT_R12_D445  / SIT_R12_D466: To validate nAcqInstId last 6 chars against 1st 6 chars of nARD instead of validating the first 6 chars of nAcqInstId.: To pull back the fixes done from  Changeset 21871.");
					if(!currentNARDValue.substring(0,6).equals(currentNAcqInstCd.substring(currentNAcqInstCd.length()-6))){
						fieldParsingErrorList.add(new RecordError(6100, "Invalid ARD / Acquirer Institution ID code", recod.getName(), recod.getValue(), xml));
						currentRecordHasErrors = true;
						log.info("6100 - 6-DIGIT VALIDATION FAILURE");
					}
				}
			}catch (Exception ex){
				log.info("6100 - nAcqInstCd EXCEPTION");
				fieldParsingErrorList.add(new RecordError(6100, "Invalid ARD / Acquirer Institution ID code", recod.getName(), recod.getValue(), xml));
				currentRecordHasErrors = true;
			}
		}
		return currentRecordHasErrors;
	}

	private boolean checkPatternValidity(boolean isPatternValid, String currentIccDataSubKey, String currentIccDataSubValue,
			DataElementNameMapping dataElementMappingForIccSubKey) {
		try {
			String regexPattern = dataElementMappingForIccSubKey.getRegexPattern();
			log.info("nICCData: regexPattern for " + currentIccDataSubKey + " is "
					+ regexPattern);
			String type = dataElementMappingForIccSubKey.getDataType();
			log.info("nICCData: type for " + currentIccDataSubKey + " is " + type);
			String dateFormat = dataElementMappingForIccSubKey.getDateFormat();
			log.info("nICCData: dateFormat for " + currentIccDataSubKey + " is "
					+ dateFormat);
			if (!"D".equals(type)) {
				log.info("nICCData: type is not D for " + currentIccDataSubKey
						+ " and pattern check starts.");
				if (StringUtils.isEmpty(regexPattern)) {
					log.info("nICCData: regexPattern is empty and pattern is valid for "
							+ currentIccDataSubKey);
					isPatternValid = true;
				} else {
					log.info("nICCData: regexPattern is not empty for "
							+ currentIccDataSubKey);
					isPatternValid = Utils.isValueMatchesPattern(regexPattern,
							currentIccDataSubValue);
					log.info("nICCData: regexPattern check for " + currentIccDataSubKey
							+ " is " + isPatternValid);
				}

			} else {
				log.info(" nICCData: type is D for " + currentIccDataSubKey
						+ " and DATE pattern check starts.");
				if (StringUtils.isEmpty(regexPattern) && StringUtils.isEmpty(dateFormat)) {
					log.info("nICCData: type is D for" + currentIccDataSubKey
							+ " and DATE pattern and regex pattern are empty.");
					isPatternValid = true;
				} else {
					if (StringUtils.isEmpty(dateFormat)) {
						isPatternValid = true;
						log.info(" nICCData: type is D for" + currentIccDataSubKey
								+ " and dateFormat is empty and pattern is true.");
					} else {
						log.info("nICCData: type is D for " + currentIccDataSubKey
								+ " and dateFormat is NOT empty.");
						isPatternValid = checkDateFormat(isPatternValid, currentIccDataSubKey, currentIccDataSubValue,
								dateFormat);
					}
				}

			}
		} catch (Exception patternEx) {
			log.info("nICCData: Regex Pattern exception for " + currentIccDataSubKey);
		}
		return isPatternValid;
	}

	private boolean checkDateFormat(boolean isPatternValid, String currentIccDataSubKey, String currentIccDataSubValue,
			String dateFormat) {
		try {
			if (StringUtils.isNotBlank(currentIccDataSubValue)) {
				SimpleDateFormat dateFormatter = new SimpleDateFormat(
						dateFormat);
				dateFormatter.setLenient(false);
				
				isPatternValid = true;
				log.info("nICCData : type is D for " + currentIccDataSubKey
						+ " and dateFormat is VALID.");
			}
		} catch (Exception ee) {
			log.info("nICCData: type is D for {}, {}" , currentIccDataSubKey
					, " and dateFormat exception.");
			isPatternValid = false;
		}
		return isPatternValid;
	}

	private String maxLenSubKey(String currentIccDataSubKey, DataElementNameMapping dataElementMappingForIccSubKey,
			String maxLen) {
		try {
			maxLen = dataElementMappingForIccSubKey.getMaxLength();
			log.info("nICCData: maxLen for currentIccDataSubKey " + currentIccDataSubKey
					+ " is " + maxLen);
		} catch (Exception ex) {
			log.info("nICCData: exception getting maxLen for currentIccDataSubKey "
					+ currentIccDataSubKey);
			log.error("Error while getting max length: ", ex);
		}
		return maxLen;
	}

	private String getMinLenSubKey(String currentIccDataSubKey, DataElementNameMapping dataElementMappingForIccSubKey,
			String minLen) {
		try {
			minLen = dataElementMappingForIccSubKey.getMinLength();
			log.info("nICCData: minLen for currentIccDataSubKey " + currentIccDataSubKey
					+ " is " + minLen);
		} catch (Exception ex) {
			log.info("nICCData: exception getting minLen for currentIccDataSubKey "
					+ currentIccDataSubKey);

			log.error("Error while getting min length: ", ex);
		}
		return minLen;
	}
	
	public Map<Integer, List<RecordError>> validateConditionalFields(Map<String, String> recordsMap, String xml) {
		Map<Integer, List<RecordError>> errors = new HashMap<>();
		List<RecordError> errorList = new ArrayList<>();
		MessageFormatNameMapping mapping;
		String mti = recordsMap.get("nMTI");
		String funcCode = recordsMap.get(NFUNCD);
		if(StringUtils.isNotBlank(mti) && StringUtils.isNotBlank(funcCode)){
			String name = mti + funcCode;
			mapping = CustomMappingUtils.getMessageFormatNameMapping(NameMappingContext.MESSAGEFORMAT, name);
			if(mapping != null){
				List<String> conditionalFieldList = mapping.getFields().getConditionalFields();
				if(conditionalFieldList != null && !conditionalFieldList.isEmpty()){
					dataNameMapping(recordsMap, xml, errorList, mapping, name, conditionalFieldList);
					
				}
				
			}
		}
		
		if (errorList.isEmpty()) {
			Integer currentnrecNum = 0;
			
			
				currentnrecNum = Integer.valueOf(recordsMap.get(NRECNUM));
			

			errors.put(currentnrecNum, errorList);
		}
		
		return errors;
	}

	private void dataNameMapping(Map<String, String> recordsMap, String xml, List<RecordError> errorList,
			MessageFormatNameMapping mapping, String name, List<String> conditionalFieldList) {
		String fieldName = "";
		for(String condtField:conditionalFieldList){
		fieldName = condtField;
		DataElementNameMapping dataElementMapping = CustomMappingUtils.getDataElementNameMapping(NameMappingContext.DATAELEMENTS, fieldName);
		
		boolean fieldPresent = recordsMap.containsKey(fieldName);
		
		if(CollectionUtils.isNotEmpty(dataElementMapping.getConstraints())){
		String[] names = {name, fieldName};
		for (FieldConstraints constraint : dataElementMapping.getConstraints()) {
			validateFieldAndOperator(recordsMap, xml, errorList, mapping, names, fieldPresent, constraint);
		}
}
	}
	}

	private void validateFieldAndOperator(Map<String, String> recordsMap, String xml, List<RecordError> errorList,
			MessageFormatNameMapping mapping, String[] names, boolean fieldPresent,
			FieldConstraints constraint) {
		String name = names[0];
		String fieldName = names[1];
		String constraintName = constraint.getName();
		String constraintTransform = constraint.getTransform();
		String constraintOperator = constraint.getOperator();
		String constraintValue = constraint.getValue();
		
		String fieldValue = recordsMap.get(constraintName);
		
		boolean expected = false; 
		boolean	ignore = false;
		// the condition it relies on isn't there, so can't validate this. the field will likely fail
		// on a required field check
		if (fieldValue == null) {
			ignore = true;
		}

		if (!ignore) {
			if (StringUtils.isNotBlank(constraintTransform) && constraintTransform.startsWith("substring")) {
				
					int startIndex = Integer.parseInt(constraintTransform.substring(10, 11));
					int endIndex = Integer.parseInt(constraintTransform.substring(12, 13));
					
					
					fieldValue = fieldValue.substring(startIndex, endIndex);
					
				
			}
			
			expected = selectOperator(constraint, constraintOperator, constraintValue, fieldValue, expected);

			DataElementNameMapping dataMapping = CustomMappingUtils.getDataElementNameMapping(NameMappingContext.DATAELEMENTS, fieldName);
			
			int missingCode = -1; 
			int presentCode = -1;
			if (dataMapping != null && dataMapping.getReasonCodes() != null) {
				missingCode = dataMapping.getReasonCodes().getMissing();
				presentCode = dataMapping.getReasonCodes().getPresent();
			}
			
			if (expected && !fieldPresent) {
				errorList.add(new RecordError(missingCode, "Conditional field missing when condition matches", fieldName, null, xml));
			} else if (!expected && fieldPresent) {
				errorList.add(new RecordError(presentCode, "Conditional field exists when condition does not match", fieldName, recordsMap.get(fieldName), xml));
			}
			

			validateFieldPresent(recordsMap, xml, errorList, mapping, name, fieldPresent, constraintName);
		}
	}

	private boolean selectOperator(FieldConstraints constraint, String constraintOperator, String constraintValue,
			String fieldValue, boolean expected) {
		if ("=".equalsIgnoreCase(constraintOperator)) {
			if (fieldValue.equalsIgnoreCase(constraintValue)) {
				expected = true;
			}
		} else if ("IN".equalsIgnoreCase(constraintOperator)) {
			List<String> constraintValues = constraint.getValues();
			if (constraintValues.contains(fieldValue)) {
				expected = true;
			}
		}
		return expected;
	}

	private void validateFieldPresent(Map<String, String> recordsMap, String xml, List<RecordError> errorList,
			MessageFormatNameMapping mapping, String name, boolean fieldPresent, String constraintName) {
		if(fieldPresent){
			DataElementNameMapping dataElementMappingMissing = CustomMappingUtils.getDataElementNameMapping(NameMappingContext.DATAELEMENTS, constraintName);
			int missingCodeNew = 0;
			boolean constraintAlreadyCheckedInRequiredFields = false;
			try{
				MessageFormatNameMapping mappingNew = CustomMappingUtils.getMessageFormatNameMapping(NameMappingContext.MESSAGEFORMAT, name);
					if(mapping != null){
						List<String> requiredFieldList = mappingNew.getFields().getRequiredFields();	
						if(CollectionUtils.isNotEmpty(requiredFieldList) && requiredFieldList.contains(constraintName)){
							constraintAlreadyCheckedInRequiredFields = true;
						}
						if(!constraintAlreadyCheckedInRequiredFields && !recordsMap.containsKey(constraintName)){
							missingCodeNew = dataElementMappingMissing.getReasonCodes().getMissing();
							errorList.add(new RecordError(missingCodeNew, "Conditional field constraint field missing when condition matches", constraintName, null, xml));
						}
					}
				}
			catch(Exception missing){
				missingCodeNew = 5163;
				errorList.add(new RecordError(missingCodeNew, "Conditional field constraint field missing when condition matches", constraintName, null, xml));
			}
		}
	}
	
	public Map<Integer, List<RecordError>> validateNoUnexpectedFields(TransactionRecord recod, Map<String, String> recordsMap, String xml) {
		Map<Integer, List<RecordError>> errors = new HashMap<>();
		List<RecordError> errorList = new ArrayList<>();
		
		for (Entry<String, String> data : recordsMap.entrySet()) {
			String key = data.getKey();
			if (!recod.getValidFields().contains(key)) {
				boolean unexpectedIsConstraintField = false;
				MessageFormatNameMapping mapping;
				String mti = recordsMap.get("nMTI");
				String funcCode = recordsMap.get(NFUNCD);
				if(StringUtils.isNotBlank(mti) && StringUtils.isNotBlank(funcCode)){
					String name = mti + funcCode;
					mapping = CustomMappingUtils.getMessageFormatNameMapping(NameMappingContext.MESSAGEFORMAT, name);
				List<String> conditionalFieldList = mapping.getFields().getConditionalFields();
				if(conditionalFieldList != null && !conditionalFieldList.isEmpty()){
				
				unexpectedIsConstraintField = matchConstraintField(key, unexpectedIsConstraintField, conditionalFieldList);
				}
				}
				
				setCodes(recordsMap, xml, errorList, key, unexpectedIsConstraintField);
			}
		}
		
		if (!errorList.isEmpty()) {
			Integer currentnrecNum = 0;
			
			
				currentnrecNum = Integer.valueOf(recordsMap.get(NRECNUM));
			

			errors.put(currentnrecNum, errorList);
		}
		
		return errors;
	}

	private void setCodes(Map<String, String> recordsMap, String xml, List<RecordError> errorList, String key,
			boolean unexpectedIsConstraintField) {
		if(!unexpectedIsConstraintField){
		
		DataElementNameMapping dataMapping = CustomMappingUtils.getDataElementNameMapping(NameMappingContext.DATAELEMENTS, key);
		
		int presentCode = -1;
		if (dataMapping != null && dataMapping.getReasonCodes() != null) {
			presentCode = dataMapping.getReasonCodes().getPresent();
		}
		
		// 6025 is unexpected tag found
		if (dataMapping != null) {
			presentCode = 6025;
		}
		
		// 5163 is Invalid XML Tag Name - so if it can't find a real present code it will fall back to this
		if (presentCode == -1) {
			presentCode = 5163;
		}
		
		errorList.add(new RecordError(presentCode, "Unexpected field", key, recordsMap.get(key), xml));
		}
	}

	private boolean matchConstraintField(String key, boolean unexpectedIsConstraintField, List<String> conditionalFieldList) {
		String fieldName = "";
		for(String condtFiled:conditionalFieldList){
								fieldName = condtFiled;
		DataElementNameMapping dataElementMapping = CustomMappingUtils.getDataElementNameMapping(NameMappingContext.DATAELEMENTS, fieldName);
								
								
								
								if(CollectionUtils.isNotEmpty(dataElementMapping.getConstraints())){
								
								for (FieldConstraints constraint : dataElementMapping.getConstraints()) {
									String constraintName = constraint.getName();
									if(StringUtils.isNotBlank(constraintName) && constraintName.equals(key)){
										unexpectedIsConstraintField = true;
									}
		}
		}
		}
		return unexpectedIsConstraintField;
	}

	@Override
	public void validate(ValidationResult validationResult, LoadRejectReasonCode rejReasonCode) throws SettleNxtException {
//		Not in use for now
		
	}
	
	@Override
	public void validate(ValidationResult validationResult, List<TransactionRecord> txnRecords) throws SettleNxtException {
//		Not in use for now
		
	}
	
	public static void printMemoryUsageStats(String whatLocation){
		int mb = 1024*1024;
		//Getting the runtime reference from system
		Runtime runtime = Runtime.getRuntime();
		log.info("whatLocation is :"+ whatLocation);
		//Print used memory
		log.info("Used Memory:"+ (runtime.totalMemory() - runtime.freeMemory()) / mb);
		//Print free memory
		log.info("Free Memory:"+ runtime.freeMemory() / mb + " out of "+runtime.maxMemory() / mb);
		
	}
	

	
}
