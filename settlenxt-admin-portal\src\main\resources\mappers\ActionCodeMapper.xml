<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
namespace="org.npci.settlenxt.adminportal.repository.ActionCodeRepository">
<select id="getApprovedActionCodeFromMain" resultType="ActionCodeDTO">
select ac.actioncodeid as actionCodeId,ac.mti as mti,ac.function_code as funcCode,ac.action_code as actionCode,ac.action_code_description as actionCodeDesc,ac.function_code_description as funcCodeDesc,ac.raised_by as raisedBy,
ac.default_reason_rej_code as defaultReasonRejCode, ac.tat_period as tatPeriod , ac.tat_period_day_type as tatPeriodDayType , ac.transition_action_code as transitionActionCode,
ac.allowed_actncd_to_remove as allowedActncdToRemove, ac.cap_amt_cal_req as capAmtCalReq from action_code ac inner join action_code_stg acs on ac.actioncodeid =acs.actioncodeid where acs.request_state='A'
</select>
<select id="getPendingForApprovalActionCodeFromStg" resultType="ActionCodeDTO">
select actioncodeid as actionCodeId,mti, function_code as funcCode,
action_code as actionCode,action_code_description as actionCodeDesc,
function_code_description as funcCodeDesc,raised_by as raisedBy,last_updated_on as lastUpdatedOn,
last_updated_by as lastUpdatedBy,created_on as createdOn,created_by as createdBy,
request_state as requestState,last_operation as lastOperation,
checker_comments as checkerComments,
default_reason_rej_code as defaultReasonRejCode, tat_period as tatPeriod , tat_period_day_type as tatPeriodDayType , transition_action_code as transitionActionCode , 
allowed_actncd_to_remove as allowedActncdToRemove, cap_amt_cal_req as capAmtCalReq
from action_code_stg 
where request_state in <foreach item='item' index='index' collection='requestStateList' open='(' separator=',' close=')'>#{item}</foreach>
</select>
<insert id="insertActionCodeIntoStg" >
insert into action_code_stg(actioncodeid,mti,function_code,
action_code,
action_code_description,
function_code_description,
raised_by,created_on,
created_by,last_updated_on,
last_updated_by,last_operation,
request_state,
default_reason_rej_code, tat_period, tat_period_day_type, transition_action_code, allowed_actncd_to_remove, cap_amt_cal_req)
values
(
#{actionCodeId},
#{mti},#{funcCode},
#{actionCode},#{actionCodeDesc},
#{funcCodeDesc},#{raisedBy},
#{createdOn},#{createdBy},#{lastUpdatedOn},#{lastUpdatedBy},#{lastOperation},#{requestState},
#{defaultReasonRejCode}, #{tatPeriod}, #{tatPeriodDayType}, #{transitionActionCode}, #{allowedActncdToRemove}, #{capAmtCalReq}
)
</insert>
<select id="getFuncCodeList" resultType="CodeValueDTO">
select CONCAT(fc.func_code, '-',fc.func_code_desc) as description,fc.func_code as code from  func_code fc group by description,code
</select>
<select id="getActionCodeSeqId" resultType="int">
SELECT nextval('action_code_id_seq')
</select>
<select id="getActionCodeInfoFromMain" resultType="ActionCodeDTO">
select ac.actioncodeid as actionCodeId ,ac.mti,ac.function_code as funcCode,
ac.action_code as actionCode,ac.action_code_description as actionCodeDesc,
ac.function_code_description as funcCodeDesc,
ac.raised_by as raisedBy,ac.last_updated_by as lastUpdatedBy,
ac.default_reason_rej_code as defaultReasonRejCode, tat_period as tatPeriod , ac.tat_period_day_type as tatPeriodDayType , ac.transition_action_code as transitionActionCode , 
ac.allowed_actncd_to_remove as allowedActncdToRemove, ac.cap_amt_cal_req as capAmtCalReq from action_code ac
where ac.actioncodeid = #{actionCodeId}
</select>
<select id="getActionCodeInfoFromStg" resultType="ActionCodeDTO">
select ac.actioncodeid as actionCodeId ,ac.mti,ac.function_code as funcCode,
ac.action_code as actionCode,ac.action_code_description as actionCodeDesc,
ac.function_code_description as funcCodeDesc,
ac.raised_by as raisedBy,ac.last_updated_by as lastUpdatedBy,
ac.last_updated_on as lastUpdatedOn, ac.created_on as createdOn, 
ac.created_by as createdBy,ac.request_state as requestState,
default_reason_rej_code as defaultReasonRejCode, tat_period as tatPeriod , tat_period_day_type as tatPeriodDayType , transition_action_code as transitionActionCode , 
allowed_actncd_to_remove as allowedActncdToRemove, cap_amt_cal_req as capAmtCalReq from action_code_stg ac
where ac.actioncodeid = #{actionCodeId}
</select>
<select id="getActionCodeFromMainEdit" resultType="ActionCodeDTO">
select ac.actioncodeid as actionCodeId ,ac.mti as mti,ac.function_code as funcCode,
ac.action_code as actionCode,ac.action_code_description as actionCodeDesc,
ac.function_code_description as funcCodeDesc,ac.request_state as requestState,
ac.raised_by as raisedBy,
default_reason_rej_code as defaultReasonRejCode, tat_period as tatPeriod , tat_period_day_type as tatPeriodDayType , transition_action_code as transitionActionCode , 
allowed_actncd_to_remove as allowedActncdToRemove, cap_amt_cal_req as capAmtCalReq from action_code_stg ac
where ac.actioncodeid = #{actionCodeId}
</select>
<update id = "updateActionCode">
UPDATE action_code set mti=#{mti}, action_code=#{actionCode},
function_code=#{funcCode},action_code_description=#{actionCodeDesc},
function_code_description=#{funcCodeDesc},raised_by=#{raisedBy},
last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},
default_reason_rej_code=#{defaultReasonRejCode}, tat_period=#{tatPeriod}, tat_period_day_type=#{tatPeriodDayType}, transition_action_code=#{transitionActionCode}, allowed_actncd_to_remove=#{allowedActncdToRemove}, cap_amt_cal_req=#{capAmtCalReq}
where actioncodeid=#{actionCodeId}
</update>
<insert id="saveActionCodeMain" >
insert into action_code(actioncodeid,mti,function_code,
action_code,
action_code_description,
function_code_description,
raised_by,created_on,
created_by,last_updated_on,
last_updated_by,default_reason_rej_code, tat_period, tat_period_day_type, transition_action_code, allowed_actncd_to_remove, cap_amt_cal_req)
values
(
#{actionCodeId},
#{mti},#{funcCode},
#{actionCode},#{actionCodeDesc},
#{funcCodeDesc},#{raisedBy},
#{createdOn},#{createdBy},#{lastUpdatedOn},#{lastUpdatedBy},
#{defaultReasonRejCode}, #{tatPeriod}, #{tatPeriodDayType}, #{transitionActionCode}, #{allowedActncdToRemove}, #{capAmtCalReq}
)
</insert>
<update id = "updateActionCodeRequestState">
UPDATE action_code_stg SET  REQUEST_STATE=#{requestState}, 
CHECKER_COMMENTS=#{checkerComments}, LAST_UPDATED_BY=#{lastUpdatedBy}, 
LAST_UPDATED_ON=#{lastUpdatedOn}, last_operation=#{lastOperation}	
WHERE actioncodeid= #{actionCodeId}
</update>
<update id = "updateStgActionCode">
UPDATE action_code_stg set mti=#{mti}, action_code=#{actionCode},
function_code=#{funcCode},action_code_description=#{actionCodeDesc},
function_code_description=#{funcCodeDesc},raised_by=#{raisedBy},
last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},request_state=#{requestState},last_operation=#{lastOperation},
default_reason_rej_code=#{defaultReasonRejCode}, tat_period=#{tatPeriod}, tat_period_day_type=#{tatPeriodDayType}, transition_action_code=#{transitionActionCode}, allowed_actncd_to_remove=#{allowedActncdToRemove}, cap_amt_cal_req=#{capAmtCalReq}
where actioncodeid=#{actionCodeId}
</update>
<update id = "updateActionCodeStg">
UPDATE action_code_stg set mti=#{mti}, action_code=#{actionCode},
function_code=#{funcCode},action_code_description=#{actionCodeDesc},
function_code_description=#{funcCodeDesc},raised_by=#{raisedBy},
last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},request_state=#{requestState},last_operation=#{lastOperation},
default_reason_rej_code=#{defaultReasonRejCode}, tat_period=#{tatPeriod}, tat_period_day_type=#{tatPeriodDayType}, transition_action_code=#{transitionActionCode}, allowed_actncd_to_remove=#{allowedActncdToRemove}, cap_amt_cal_req=#{capAmtCalReq}
where actioncodeid=#{actionCodeId}
</update>
<delete id="deleteDiscardedEntry">
DELETE FROM action_code_stg WHERE actioncodeid = #{actionCodeId}
</delete>
<select id="fetchCappingAmountStgAppList" resultType="ActionCodeDTO">
select actioncodeid as actionCodeId,mti as mti, function_code as funcCode,
action_code as actionCode,action_code_description as actionCodeDesc,
function_code_description as funcCodeDesc,raised_by as raisedBy,last_updated_on as lastUpdatedOn,
last_updated_by as lastUpdatedBy,created_on as createdOn,created_by as createdBy,
request_state as requestState,last_operation as lastOperation,
checker_comments as checkerComments,
default_reason_rej_code as defaultReasonRejCode, tat_period as tatPeriod , tat_period_day_type as tatPeriodDayType , transition_action_code as transitionActionCode , 
allowed_actncd_to_remove as allowedActncdToRemove, cap_amt_cal_req as capAmtCalReq
from action_code_stg 
where actioncodeid in <foreach item='item' index='index' collection='actionCodeIdList' open='(' separator=',' close=')'>#{item}</foreach>
</select>
<select id="getMCCList" resultType="CodeValueDTO">
select distinct mti as code, mti as description from func_code;
</select>
<select id="getActionCodeList" resultType="CodeValueDTO">
select action_code as code, CONCAT(action_code,'-',action_code_description) as description from action_code
</select>
<select id="checkDistinctActionCodeStg" resultType="int">
SELECT count(action_code) FROM action_code_stg  WHERE action_code = #{actionCode} 
</select>
<select id="checkDistinctActionCode" resultType="int">
SELECT COUNT(action_code) FROM action_code WHERE action_code = #{actionCode} 
</select>

<select id="getReasonCodeList" resultType="CodeValueDTO">
select reason_code as code, CONCAT(reason_code,'-',reason_code_desc) as description from reason_code_master
</select>

<select id="getAllActionCodeData" resultType="org.npci.settlenxt.portal.common.dto.ActionCodeDTO">
SELECT MTI as mti,FUNCTION_CODE as functionCode,ACTION_CODE as actionCode,ACTION_CODE_DESCRIPTION as actionCodeDesc,review_option as reviewerOpt FROM ACTION_CODE ORDER BY FUNCTION_CODE;
</select>
</mapper>