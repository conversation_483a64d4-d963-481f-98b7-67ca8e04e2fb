<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.FileRejectRepository">
	
	<select id="getAllByFileId" resultType="FileRejectDTO">
		Select FILE_REJECT_ID as fileRejectId,	FILE_ID as fileId, SEQ_NO as seqNo, TASK_FORMATTED as taskFormatted, RECORD_NO as recordNo,	FIELD_NAME as fieldName,	REJECTREASON as rejectReason, REJECT_CODE as rejectCode,	REJECT_XML as rejectXml, REJECT_DT_TYPE as rejectDataType 
		from FILE_REJECT WHERE INSERT_DAY = #{insertDay} and FILE_ID = #{fileId}
	</select>
	<insert id="insertFileReject" >
		INSERT INTO FILE_REJECT 
		(FILE_ID,SEQ_NO,RECORD_NO,FIELD_NAME,REJECTREASON,REJECT_CODE,REJECT_XML,TASK_FORMATTED,INSERT_DAY,REJECT_DT_TYPE) 
		VALUES(#{fileId}, #{seqNo}, #{recordNo}, #{fieldName}, #{rejectReason}, #{rejectCode}, #{rejectXml}, #{taskFormatted}, #{insertDay}, #{rejectDataType})	
	</insert>
	<select id="getAllByFileIds" resultType="FileRejectDTO">
		Select FILE_REJECT_ID as fileRejectId,	FILE_ID as fileId, SEQ_NO as seqNo, TASK_FORMATTED as taskFormatted, RECORD_NO as recordNo,	FIELD_NAME as fieldName,	REJECTREASON as rejectReason, REJECT_CODE as rejectCode,	REJECT_XML as rejectXml 
		from FILE_REJECT WHERE INSERT_DAY >= #{minDate} and INSERT_DAY <![CDATA[ <= ]]> #{maxDate} and FILE_ID in (${stageFileIds})
	</select>
	<update id="updateFileReject">
		UPDATE file_reject SET RECON_CYCLE=#{cycleNumber}, NET_RECON_DATE=#{cycleDate} WHERE FILE_REJECT_ID = #{fileRejectId}
	</update>
	<delete id="discardFileReject">
		DELETE FROM file_reject WHERE FILE_ID = #{fileId}
	</delete>
</mapper>	

	