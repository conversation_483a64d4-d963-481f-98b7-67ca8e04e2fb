$(document).ready(
		function() {
	var cursorPosition =null;
	/* Initialization of datatables */
	$(document).ready(function () {
    	
     $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                //If first column to be skipped to include the filter for the reasons line check box 
            dataTableFunc(colIdx,api);
   
                });
            $('#tabnew_filter').hide();
           
        },
        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'User',
                header: 'false',
                title: null,
                sheetName: 'User',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'User' ,
				header:'false', 
				title: null,
				sheetName:'User',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });
    
    
    
    

});
   
function dataTableFunc(colIdx,api)
	{
	             if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   cursorPosition = handleInput(colIdx, cell, title, api, cursorPosition);
                   }
	}
	

   $('#fromDateStr').on('keyup keypress blur change',function () {
     validateFromDate('errFromDate');
   });
   $('#toDateStr').on('keyup keypress blur change',function () {
    validateToDate('errtoDate');
   });
	$("#fromDateStr").datepicker({
      maxDate: '0',
        changeMonth: true,
          changeYear: true,
        dateFormat: 'yy-mm-dd',
        onSelect: function (selectedDate, instance) {
            if (selectedDate != '') { //added this to fix the issue  
                     
             $("#toDateStr").datepicker("option", "minDate", selectedDate);
                let date = $.datepicker.parseDate(instance.settings.dateFormat, selectedDate, instance.settings);
                date.setMonth(date.getMonth() + 1);
                
                
                  date = $('#fromDateStr').datepicker('getDate');
               
            date.setTime(date.getTime() + (1000*60*60*24*365));
            let todaysDate = new Date();
            if(date.getTime() > todaysDate.getTime()) 
            	date = todaysDate;
            $("#toDateStr").datepicker("option", "minDate", selectedDate);
            $("#toDateStr").datepicker("option", "maxDate", date);
            }
        },
        onClose : function() {
       validateFromDate('errFromDate');
       }
    });
    $("#toDateStr").datepicker({
    
        minDate: "dateToday",
        changeMonth: true,
        changeYear: true,
        dateFormat: 'yy-mm-dd',
        onSelect: function (selectedDate) {
             $("#fromDateStr").datepicker("option", "maxDate", selectedDate);
        },
       onClose :function() {
       validateToDate('errtoDate');
       }
    });
    
       if( $('#search').val()=='Y'){
     		let date = $('#fromDateStr').datepicker('getDate');
            date.setTime(date.getTime() + (1000*60*60*24*365));
             let todaysDate = new Date();
            var selectedDate=$('#fromDate').val();
            if(date.getTime() > todaysDate.getTime())
            	date = todaysDate;
            $("#toDateStr").datepicker("option", "minDate", selectedDate);
            $("#toDateStr").datepicker("option", "maxDate", date);
   }

					
			$("#searchBtn").click(
					function() {
								
					var check = validateDate();						

	if(!check){
	var fromDateStr = $('#fromDateStr').val() ;
						var toDateStr = $('#toDateStr').val() ;
						var fileType = $('#fileType').val();
						var memberName = $('#memberName').val();
						
						var url = "/fileSearchMembrStr";

						

						var data =  "fromDateStr,"
								+ fromDateStr + ",toDateStr," + toDateStr + ",fileType,"
								+ fileType + ",memberName," + memberName ;
						postData(url, data);
	
	}else{
	return false;
	}
	

						
					});

			$(".bulkDtls1 clickFun").hover(function() {
				$('.bulkDtls1 clickFun').css('cursor', 'hand');

			});
			
$("#selectAll").click(function(){
		
		 
		 $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
     		 handleModalShow(); 
		  
		
		   var idList = document.getElementById("filePaths");
		   idList.innerHTML = fileIdList.join(",");
		    
		 
		
	});						

		});


function handleModalShow() {
    if (document.forms[0].selectAllCheck.checked) {

        $("#toggleModalDocDownload").modal('show');
    }

    else {
        $("#toggleModalDocDownload").modal('hide');

        var ele = document.getElementsByName('type');

        let i = 0;
        for (i of ele) {
            if (i.type == 'checkbox')
                i.checked = false;
        }


    }
}

function validateDate() {
    var check = false;
    if (!validateFromDate('errFromDate')) {
        check = true;
    }
    if (!validateToDate('errtoDate')) {
        check = true;
    }
    if (!check) {
        if (!validateFromToDate('errFromDate')) {
            check = true;
        }
    }
    return check;
}

function handleInput(colIdx, cell, title, api, cursorPosition) {
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function () {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
            })
            .on('click', function (e) {
                e.stopPropagation();
            })
            .on('keyup', function (e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    } else {
        $(cell).html(title + '<br> &nbsp;');
    }
    return cursorPosition;
}

function getRecordDetails(filePath) {
$('#errorStatus2').hide();
  	var tokenValue = document.getElementsByName("_TransactToken")[0].value;


     $.ajax({
    	 url : "fileDownloadVerify",
        type: "POST",
		dataType : "json",
				data: {"filePath" : escape(filePath),
					    "_TransactToken" : tokenValue},
       success: function(response) {
				var data="";
				var url="";
					if (response.status == "BSUC_0001") {
				url = "/fileContentDownload";
				data = "filePath," + escape(filePath);
	postData(url, data);
			}else{
			url = "/fileDownloadError";
			var fromDateStr = $('#fromDateStr').val() ;
						var toDateStr = $('#toDateStr').val() ;
						var fileType = $('#fileType').val();
						var memberName = $('#memberName').val();
				 data =  "fromDateStr,"
								+ fromDateStr + ",toDateStr," + toDateStr + ",fileType,"
								+ fileType + ",memberName," + memberName ;
	postData(url, data);
			}
}
})
}


function submitForm(url)
{
  var data = "";
    postData(url, data);
}

function validateFromDate(msgID) {
var errFrom = document.getElementById(msgID);
var dateString = (document.getElementById("fromDateStr").value).replace("/^\s*|\s*$/g", '');
if (dateString == "") {
errFrom.className = 'error';
errFrom.innerHTML = "Please Enter From Date";
return false;
} else{
errFrom.className = 'error';
errFrom.innerHTML = "";
}
return true;
}
function validateToDate(msgID) {
var errTo = document.getElementById(msgID);
var dateString = (document.getElementById("toDateStr").value).replace("/^\s*|\s*$/g", '');
if (dateString == "") {
errTo.className = 'error';
errTo.innerHTML = "Please Enter To Date";
return false;
} else{
errTo.className = 'error';
errTo.innerHTML = "";
}
return true;
}
function validateFromToDate(msgID) {
var errFrom = document.getElementById(msgID);
var fromDateStr = $('#fromDateStr').val() ;
var toDateStr = $('#toDateStr').val() ;
if(fromDateStr!="" && toDateStr!=""){
if (Date.parse(fromDateStr) > Date.parse(toDateStr)) {
errFrom.className = 'error';
errFrom.innerHTML ="From Date cannot be greater than To Date";
return false;
}
return true;
}
}
function deleteDocumentDownload(type){
	
	 var url = '/deleteDocDownloadFiles';
	
	 var array = [];
 if(type=='One'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(type=='All'){
		 array=fileIdList; 
	 }
	
	  
		
	
var fileIds = "";
let i=0;
for (i of array) {
fileIds = fileIds + i + "|";
}

	
	if(array.length == 0) {
$('#errorStatus2').html("Please select one or more records to Delete");
$('#errorStatus2').show();
}
	else {
$('#errorStatus2').hide();
var fromDateStr = $('#fromDateStr').val() ;
						var toDateStr = $('#toDateStr').val() ;
						var fileType = $('#fileType').val();
				var data =  "fromDateStr,"
								+ fromDateStr + ",toDateStr," + toDateStr + ",fileType,"
								+ fileType +",bulkdownloadIdList,"+fileIds;	
	
	
	postData(url, data);
	
}
	
	
		
}

function allFileDownload(){
	
	 var url = '/allfilesContentDownload';
	
	 var array = [];
 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	
	 
		var fileIds = "";
		let i=0;
		
		for(i of array)
		{
			fileIds = fileIds + i + "|";
					
		}
		
		
if(array.length == 0) {
$('#errorStatus2').html("Please select one or more records to download");
$('#errorStatus2').show();
}else {
$('#errorStatus2').hide();
var data = "bulkdownloadIdList,"+fileIds;
postData(url, data);
}

}



function deselectAll() {

	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  
    let i=0;
   
    for (i of ele) {
if(i.type=='checkbox')  
   i.checked=false;  
}
   
   
   
   
   
   
}
