package org.npci.settlenxt.adminportal.common.mapping;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Locale;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class NameMappingLoader {
	
	private static final String MAPPINGS_FOLDER = "mappings";
	private static final String MAPPING_FILE_SUFFIX = ".json";

	NameMappingLoader() {
		super();
	}

	protected static File getCustomMappingFile(NameMappingContext context, String customValue) {
		URL mappingURL = getFilePathUsingCP(MAPPINGS_FOLDER + File.separator + customValue.toUpperCase(Locale.ROOT)
				+ File.separator + context.getContext() + MAPPING_FILE_SUFFIX);
		try {
			URI mappingURI = mappingURL != null ? mappingURL.toURI() : null;
			return new File(mappingURI);
		} catch (URISyntaxException e) {
			log.error("Error while getCustomMappingFile: ", e);
		}

		return null;
	}

	protected static File getCustomMappingFile(String mappingURL) {
		try {
			URL url = getFilePathUsingCP(mappingURL);
			URI mappingURI = url != null ? url.toURI() : null;
			return new File(mappingURI);
		} catch (Exception e) {
			log.error("Error while getCustomMappingFile: ", e);
		}

		return null;
	}

	public static URL getFilePathUsingCP(String fileName) {
		log.debug("Looking for {}", fileName);
		URL resourceURL = null;
		File file = new File(fileName);
		try {
			if (file.exists() && file.canRead()) {
				return file.toURI().toURL();
			}
		} catch (IOException e) {
			log.error("Cannot find the searched file: ", e);
		}
		return resourceURL;
	}

}
