$(document).ready(function() {
	
	   setInterval(timerIncrement, 60000); // 1 minute

       // Zero the idle timer on mouse movement.
       $(this).mousemove(function (_e) {

		if(document.getElementById('id01').style.display=='block'){
				idleTime =idleTime;
				}else if(document.getElementById('id02').style.display=='block'){
				idleTime =idleTime;
				}else{
				idleTime=0;
				}
		
			
       });
       $(this).keypress(function (_e) {
    	   
           idleTime = 0;
       });
       
       
	setTimeout(pingBackend, getPingInterval());
});

var idleTime = 0;
function timerIncrement() {
	 console.log("Idle Time "+idleTime);
    idleTime = idleTime + 1;
}
function pingBackend() {
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	
	var idleTimePeriod=idleTime;
	var mySpan;
	
	$.ajax({
		url: "ping",
		type: "GET",
		dataType: "json",
		data: {
			"idleTimePeriod": idleTimePeriod,
			"_TransactToken": tokenValue,
		},
		headers: {
					'_TransactToken': tokenValue
				},		
	    success: function (data) {
	    
	        if(data.status=="success"){
	        	
	        	 setTimeout(pingBackend, getPingInterval());
	        	document.getElementById('id01').style.display='none';
	        		document.getElementById('id02').style.display='none';
			  	 
			   }
	        else if(data.status=="idleTimePropmt")
	        	{
	        	
	        	document.getElementById('id01').style.display='block';

				mySpan = document.getElementById("idletimeinterval");
				mySpan.innerHTML = data.idleTimePropmt;	        	
	        	 setTimeout(pingBackend, getPingInterval());
	        	}
	        else if(data.status=="idleTimePasswordPropmt")
	        	{
	        	
	        	document.getElementById('id02').style.display='block';
	        	mySpan = document.getElementById("idletimepwdinterval");
				mySpan.innerHTML = data.idleTimePasswordPropmt;	  
				
				var sessionSpan = document.getElementById("sessionTimeout");
				sessionSpan.innerHTML = data.sessionTimeout;
	        	
	        	 setTimeout(pingBackend, getPingInterval());
	        	}
	        	else{
	        		
	        		
			    reloadApp();
			    
			   }
			   
			   
	    },
	    error: function (_request, _status, _error) {
	    	
	       reloadApp();
	    }
	});
}
	
function refreshSessionBackEnd() {
var tokenValue=	document.getElementsByName("_TransactToken")[0].value;
	$.ajax({
	    type: "get", url: "./refreshSession",headers: {
					'_TransactToken': tokenValue
				},		
	    success: function (data, _text) {
	        if(data=="resumeActiviySuccess"){
			  	 document.getElementById('id01').style.display='none';
	        }
	        	else{
			    reloadApp();
			   }
	    },
	    error: function (_request, _status, _error) {
	        reloadApp();
	    }
	});
}
	
function encode(data) {
        let encrypt = new JSEncrypt();
		let k = document.getElementById('publicKey').value;
		encrypt.setPublicKey(k);
        let encryptedData = encrypt.encrypt(data);
		return encryptedData;
}

function verifyPassword() {
	
var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	
	var pswd=$('#passwordForSessResume').val();
	
pswd=encode(pswd);
	
	if(pswd!='' || pswd!=null){
	
	$.ajax({
		url: "verifyPasswordForSessionResume",
		type: "POST",
		dataType: "json",
		data: {
			"pswd": pswd,
			"_TransactToken": tokenValue,
		},headers: {
					'_TransactToken': tokenValue
				},		
	    success: function (data) {
	    	
	    
	        if(data.status=="success"){
	        	 document.getElementById('id02').style.display='none';
	        	 document.getElementById('id01').style.display='none';
	        	 setTimeout(pingBackend, getPingInterval());
	        	 $("#errPassword").find('.error').html('');
	        		
			  	 
			   }
	        	else if(data.status=="failed"){
	        		$("#errPassword").find('.error').html('Invalid Password');
	        		setTimeout(pingBackend, getPingInterval());
			   
			    
			   }
	    },
	    error: function (_request, _status, _error) {
	    	
	       reloadApp();
	    }
	});}
	else{
		$("#errPassword").find('.error').html('Please Enter Password');
	}
}
	


function getPingInterval(){
 if(uiPingInterval && !isNaN(uiPingInterval)){
   return parseInt(uiPingInterval);  
 }
 return 10000;
}
function reloadApp(){
   window.location.href="./invalidSession";
   }
