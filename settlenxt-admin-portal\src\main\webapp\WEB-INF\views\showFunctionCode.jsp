<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript"> 
var actionColumnIndex=9;
var firstColumnToBeSkippedInFilterAndSort=false;
<c:if test="${showCheckBox eq 'Y'}">
actionColumnIndex=13;
firstColumnToBeSkippedInFilterAndSort=true;
</c:if>
<c:if test="${showCheckBox eq 'N'}">
actionColumnIndex=12;
firstColumnToBeSkippedInFilterAndSort=false;
</c:if>
</script>

<script>
	var referenceNoListPendings = [];
	<c:if test="${not empty pendingFunctionCodeList}">
	<c:forEach items="${pendingFunctionCodeList}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	referenceNoListPendings.push('${operator.funcCodeId}');
	</c:if>
	</c:forEach>
	</c:if>
</script>

<script src="./static/js/validation/showFunctionCode.js" type="text/javascript"></script>
<script type="text/javascript" src=	"./static/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src=	"./static/js/jszip.min.js"></script>
<script type="text/javascript" src=	"./static/js/buttons.html5.min.js"></script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
	 
	.defaultexport {
  visibility: hidden;
}

table.dataTable thead  { vertical-align: top;}
table.dataTable thead .sorting { vertical-align: top; background: url('./static/images/sort_both.png') no-repeat center right; }
table.dataTable thead .sorting_asc { vertical-align: top;background: url('./static/images/sort_asc.png') no-repeat center right; }
table.dataTable thead .sorting_desc { vertical-align: top;background: url('./static/images/sort_desc.png') no-repeat center right; }
table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before{ vertical-align: top;content:""}
table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after{ vertical-align: top;content:""}
.search-box  {	
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;	
	background-color: transparent;
    width: 100%;
    border-width:1px;
	border-style:inset;
    }
</style>
<!-- Model -->	
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog" aria-labelledby="toggleModalNews" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Are you sure you want to Approve/Reject these records?</h5>
        <button type="button" class="close" data-dismiss="modal"  aria-label="Close" onclick="deselectAll()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
     <div>
          <label style="color:blue;font-weight:bold;" >Function Code Approval/Rejection</label>
          <p id="newsIds"/>
          </div>
          
     <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="ApproveorRejectBulkFunctionCode('R','All')">Reject</button>
        <button type="button" class="btn btn-primary" onclick="ApproveorRejectBulkFunctionCode('A','All')">Approve</button>
      </div>
    </div>
  </div>
</div>
<!-- Model -->

<input:hidden id="refNum" />
<div class="row">
	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/functionCodeMain');" role="tab"
			data-toggle="tab"><span class="glyphicon glyphicon-credit-card">
		</span> <spring:message code="functionCode.mainTab.title" /></a>
		<c:choose>
			<c:when test="${pendingAppFunctionCode eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" role="tab" onclick="getPendingFunctionCodeList();"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> </span>
			<spring:message code="functionCode.approvalTab.title" /></a>
	</ul>
	<div class="row">
		<div class="col-sm-12">
			<sec:authorize access="hasAuthority('Add Function Code')">
				<c:if test="${addFunctionCode eq 'Yes'}">
					<a class="btn btn-success pull-right btn_align" href="#"
						onclick="submitForm('/functionCodeCreation','P');"
						style="margin-top: -30px;"><em class="glyphicon-plus"></em> 
						<spring:message code="functionCode.addFunctionCodeBtn" /></a>
				</c:if>
			</sec:authorize>
		</div>
	</div>

	<div class="tab-content">
		<div role="tabpanel" class="tab-pane active" id="home">
			<c:if test="${showMainTab eq 'Yes'}">
			
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message code="functionCode.listscreen.title" /></span></strong>
							</div>
							<div class="panel-body">
								<div class="row">
									<div class="col-sm-12">
											<button class="btn  pull-right btn_align" 
											id="clearFilters"><spring:message code="functionCode.clearBtn" /></button>
											&nbsp; <a class="btn btn-success pull-right btn_align" href="#" id="csvExport">
											<spring:message code="functionCode.csvBtn" /> </a>
											<a class="btn btn-success pull-right btn_align" href="#"
												id="excelExport"  ><spring:message code="functionCode.exportBtn" />
											</a>
									</div>
								</div>
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
										<caption style="display:none;">Function Code</caption>
										<thead>
											<tr>
												<th scope = "col"><label><spring:message code="functionCode.mti" /></label></th>
												<th scope = "col"><label><spring:message code="functionCode.procCode" /></label></th>
												<th scope = "col"><label><spring:message code="functionCode.funcCode" /></label></th>
												<th scope = "col"><label><spring:message code="functionCode.funcCodeDesc" /></label></th>
												<th scope = "col"><label><spring:message code="functionCode.feeType" /></label></th>
												<th scope = "col"><label><spring:message code="functionCode.fundMovement" /></label></th>
												<th scope = "col"><label><spring:message code="functionCode.fundMovementSide" /></label></th>
												<th scope = "col"><label><spring:message code="functionCode.recalculate" /></label></th>
												<th scope = "col"><label><spring:message code="functionCode.transactionType" /></label></th>
											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty functionCodeList}">
												<c:forEach var="functionCode" items="${functionCodeList}">
													<tr>
														<td onclick="javascript:viewFunctionCode('${functionCode.funcCodeId}','V')">${functionCode.mti}</td>
														<td onclick="javascript:viewFunctionCode('${functionCode.funcCodeId}','V')">${functionCode.procCode}</td>
														<td onclick="javascript:viewFunctionCode('${functionCode.funcCodeId}','V')">${functionCode.funcCode}</td>
														<td onclick="javascript:viewFunctionCode('${functionCode.funcCodeId}','V')">${functionCode.funcCodeDesc}</td>
														<td onclick="javascript:viewFunctionCode('${functionCode.funcCodeId}','V')">${functionCode.feeType}</td>
														<td onclick="javascript:viewFunctionCode('${functionCode.funcCodeId}','V')">${functionCode.fundMovement}</td>
														<td onclick="javascript:viewFunctionCode('${functionCode.funcCodeId}','V')">${functionCode.fundMovementSide}</td>
														<td onclick="javascript:viewFunctionCode('${functionCode.funcCodeId}','V')">${functionCode.recalculate}</td>
														<td onclick="javascript:viewFunctionCode('${functionCode.funcCodeId}','V')">${functionCode.transactionType}</td>
													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>
			<c:if test="${showApprovalTab eq 'Yes'}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message code="functionCode.listscreen.title" /></span></span></strong>
									<c:if test="${not empty pendingFunctionCodeList}">
									<sec:authorize access="hasAuthority('Approve Function Code')">
										<input type="button" class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkFunctionCode('A','No')" id="submitButtonA"
											value="<spring:message code="feeRate.Approve" />" />
										<input type="button" class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkFunctionCode('R','No')" id="submitButtonR"
											value="<spring:message code="feeRate.Reject" />" />
									</sec:authorize>
									</c:if>
							</div>
							<div class="panel-body">
									<div class="row">
									<div class="col-sm-12">
											<button class="btn  pull-right btn_align" 
											id="clearFilters"><spring:message code="functionCode.clearBtn" /></button>
											&nbsp; <a class="btn btn-success pull-right btn_align" href="#" id="csvExport">
											<spring:message code="functionCode.csvBtn" /> </a>
											<a class="btn btn-success pull-right btn_align" href="#"
												id="excelExport"  ><spring:message code="functionCode.exportBtn" />
											</a>

									</div>
								</div>
									<div class="table-responsive">
											<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
												<caption style="display:none;">Function Code</caption>
												<thead>
													<tr>
													<sec:authorize access="hasAuthority('Approve Function Code')">
														<th scope = "col"><input type=checkbox name='selectAllCheck'
												 		id="selectAll" data-target="toggleModalNews" value="All"></input></th>
													</sec:authorize>
													<th scope = "col"><label><spring:message code="functionCode.mti" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.procCode" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.funcCode" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.funcCodeDesc" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.feeType" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.fundMovement" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.fundMovementSide" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.recalculate" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.transactionType" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.requestType" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.status" /></label></th>
													<th scope = "col"><label><spring:message code="functionCode.checkerComents" /></label></th>
													</tr>
												</thead>
												<tbody>
												<c:if test="${not empty pendingFunctionCodeList}">
												
													<c:forEach var="functionCodes" items="${pendingFunctionCodeList}">
														<tr>
															<sec:authorize access="hasAuthority('Approve Function Code')">
															<c:if test="${functionCodes.requestState =='P' }">
																<td><input type=checkbox name='type'
																 id="selectSingle" onclick="mySelect();" value="${functionCodes.funcCodeId}"></input></td>
															</c:if>
															<c:if test="${functionCodes.requestState !='P' }">
																<td></td>
															</c:if>
															</sec:authorize>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.mti}</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.procCode}</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.funcCode}</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.funcCodeDesc}</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.feeType}</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.fundMovement}</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.fundMovementSide}</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.recalculate}</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.transactionType}</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.lastOperation}</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">
																<c:if test="${functionCodes.requestState =='A' }"><spring:message code="functionCode.requestState.approved.description" /></c:if>
																<c:if test="${functionCodes.requestState =='P' }"><spring:message code="functionCode.requestState.pendingApproval.description" /></c:if>
																<c:if test="${functionCodes.requestState =='R' }"><spring:message code="functionCode.requestState.rejected.description" /></c:if>
																<c:if test="${functionCodes.requestState =='D' }"><spring:message code="functionCode.requestState.discared.description" /></c:if>
															</td>
															<td onclick="javascript:viewFunctionCode('${functionCodes.funcCodeId}','P')">${functionCodes.checkerComments}</td>
														</tr>
													</c:forEach>
													</c:if>
												</tbody>
											</table>
										</div>
								</div>
						</div>
					</div>
				</div>
			</c:if>
		</div>
	</div>
</div>
