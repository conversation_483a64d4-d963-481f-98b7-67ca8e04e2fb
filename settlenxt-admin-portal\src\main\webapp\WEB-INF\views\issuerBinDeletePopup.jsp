<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>

<div class="modal fade" id="toggleIssBinDelete" tabindex="-1" role="dialog" aria-labelledby="toggleIssBinDelete" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Are you sure you want to Delete the Issuer Bin?</h5>
        <button type="button" class="close" data-dismiss="modal"  aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
     <div>
          <label style="color:blue;font-weight:bold;" >Issuer Bin Number</label>
          <p id="binNumberIssBinDelete"/>
          </div>


<input type="hidden" name="modalbinNumberDelete" id="modalbinNumberDelete" value="">
<input type="hidden" name="modalparticipantIdDelete" id="modalparticipantIdDelete" value="">
<input type="hidden" name="modalbinTypeDelete" id="modalbinTypeDelete" value="">

     <div class="modal-footer">
        
        <button type="button" class="btn btn-primary" onclick="deleteBin(document.getElementById('modalbinNumberDelete').value,document.getElementById('modalparticipantIdDelete').value,document.getElementById('modalbinTypeDelete').value);">Approve</button>
     <button type="button" class="btn btn-primary" data-dismiss="modal"  aria-label="Close" >Reject</button>
      </div>
    </div>
  </div>
</div>

