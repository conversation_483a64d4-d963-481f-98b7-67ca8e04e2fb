<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.BinDetailsRepository">
	
<select id="getBinDetailsMain" resultType="binExclDTO">
            select R.EXC_ID as excId,R.BIN as bin,R.BASE_OR_FEATURE as feeSelectionType,R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,S.unique_bank_name  as bankName
			,R.from_Date_full as fromDateComplate ,R.to_Date_full as toDateComplate
			from  mcpr_bin_exclusion_config R 
			inner join mcpr_bin_exclusion_config_stg stg on R.EXC_ID=stg.EXC_ID
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID 
			where mb.status!='D'
			ORDER BY R.last_updated_on desc
</select>



<select id="getPendingBinExclusionConfigs" resultType="binExclDTO">
	       select   R.EXC_ID as excId,R.BIN as bin,R.BASE_OR_FEATURE as feeSelectionType,R.participant_id,R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate
		  ,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,S.unique_bank_name  as bankName,R.checker_comments as checkerComments,R.last_operation as lastOperation,R.request_state as requestState 
		  ,from_Date_full as fromDateComplate ,to_Date_full as toDateComplate
		  from  mcpr_bin_exclusion_config_stg R
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
		   INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID
		   where R.REQUEST_STATE in ('P', 'R') and  mb.status!='D'
</select>


<insert id="addBinExclusionConfig">
INSERT INTO  mcpr_bin_exclusion_config_stg 
(exc_id,PARTICIPANT_ID,BIN, BASE_OR_FEATURE,FROM_DATE,TO_DATE,CREATED_BY,CREATED_ON,LAST_UPDATED_BY,
LAST_UPDATED_ON, request_state, last_operation, status,from_Date_full,to_Date_full) VALUES (#{excId},#{participantName},#{bin}, 
#{feeSelectionType}, #{fromDate}, #{toDate},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{requestState}, #{lastOperation}, 
#{status} ,to_date(concat('01',#{fromMonth},#{fromYear}),'ddmmyyyy') ,to_date(concat(case  when #{toMonth} in ('03','12') then '31' else '30' end,
#{toMonth},#{toYear}),'ddmmyyyy'))
</insert>



<select id="getBinExclusionConfigById" resultType="binExclDTO">
			SELECT R.EXC_ID as excId,R.BIN as bin,R.BASE_OR_FEATURE as feeSelectionType,R.participant_id as participantId, R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate,R.status as status
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,R.checker_comments as checkerComments,R.last_operation as 
			 lastOperation,R.request_state as requestState,S.unique_bank_name  as bankName
			 ,from_Date_full as fromDateComplate ,to_Date_full as toDateComplate
			  FROM  mcpr_bin_exclusion_config_stg R
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			 INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID where exc_id = #{excId} and mb.status!='D'
	
</select>
<select id="getBinExclusionConfigByIdMain" resultType="binExclDTO">
			SELECT R.EXC_ID as excId,R.BIN as bin,R.BASE_OR_FEATURE as feeSelectionType,R.participant_id as participantId, R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate,R.status as status
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn , S.unique_bank_name  as bankName
			 ,R.from_Date_full as fromDateComplate ,R.to_Date_full as toDateComplate,stg.request_state as requestState
			  FROM  mcpr_bin_exclusion_config R
			  inner join mcpr_bin_exclusion_config_stg stg on R.EXC_ID=stg.EXC_ID
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			 INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID where R.exc_id = #{excId} and mb.status!='D'
	
</select>


<select id="getPendingBinExclusionConfigById" resultType="binExclDTO">
			 SELECT R.EXC_ID as excId,R.BIN as bin,R.BASE_OR_FEATURE as feeSelectionType,R.participant_id as participantId, R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate,R.status as status
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,R.checker_comments as checkerComments,R.last_operation as 
			 lastOperation,R.request_state as requestState,S.unique_bank_name  as bankName
			 ,from_Date_full as fromDateComplate ,to_Date_full as toDateComplate
			  FROM  mcpr_bin_exclusion_config_stg R
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			 INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID where exc_id = #{excId} and mb.status!='D'
			
</select>


<insert id="saveBinExclConfig">
			INSERT INTO  mcpr_bin_exclusion_config(EXC_ID,PARTICIPANT_ID,BIN,BASE_OR_FEATURE,FROM_DATE,TO_DATE,LAST_UPDATED_BY,last_updated_on,created_on,created_by,status,from_Date_full,to_Date_full) 
			values( #{excId},#{participantId},
			#{bin},#{feeSelectionType},#{fromDate},#{toDate},#{lastUpdatedBy},#{lastUpdatedOn},#{createdOn},#{createdBy}, #{status},#{fromDateComplate},#{toDateComplate})
	
</insert>

<update id="updateBinExclConfig">
			UPDATE  mcpr_bin_exclusion_config_stg SET BIN =#{bin},BASE_OR_FEATURE = #{feeSelectionType},FROM_DATE = #{fromDate},TO_DATE =#{toDate} 
		   ,LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},REQUEST_STATE =#{requestState} ,
			last_operation = #{lastOperation},checker_comments = '' 
			,from_Date_full=to_date(concat('01',#{fromMonth},#{fromYear}),'ddmmyyyy'),to_Date_full=to_date(concat(case  when #{toMonth} in ('03','12') then '31' else '30' end,#{toMonth},#{toYear}),'ddmmyyyy')
			where EXC_ID = #{excId}
</update>

<update id="updateBinExclConfigMain">
			UPDATE  mcpr_bin_exclusion_config SET BIN =#{bin},BASE_OR_FEATURE = #{feeSelectionType},FROM_DATE = #{fromDate},TO_DATE =#{toDate} 
		   ,LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},status =#{status} 
			,from_Date_full=#{fromDateComplate},to_Date_full=#{toDateComplate}
			 where EXC_ID = #{excId}
</update>

<update id="updateBinExclConfigDiscard">
			UPDATE  mcpr_bin_exclusion_config_stg SET BIN =#{bin},BASE_OR_FEATURE = #{feeSelectionType},FROM_DATE = #{fromDate},TO_DATE =#{toDate} 
		   ,LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},REQUEST_STATE =#{requestState} ,
			last_operation =  #{lastOperation},checker_comments = #{checkerComments}
			,from_Date_full=#{fromDateComplate},to_Date_full=#{toDateComplate}
			 where EXC_ID = #{excId}
</update>

<update id="updateBinExclStgState">
			 UPDATE  mcpr_bin_exclusion_config_stg SET LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},REQUEST_STATE =#{requestState} ,
			last_operation =  #{lastOperation},checker_comments = #{checkerComments} where EXC_ID = #{excId}
</update>



<select id="getBinExclStgData" resultType="binExclDTO">
		    SELECT R.EXC_ID as excId,R.BIN as bin,R.BASE_OR_FEATURE as feeSelectionType,R.participant_id as participantId, R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate,R.status as status
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,R.checker_comments as checkerComments,R.last_operation as 
			 lastOperation,R.request_state as requestState,S.unique_bank_name  as bankName
			 ,from_Date_full as fromDateComplate ,to_Date_full as toDateComplate
			  FROM  mcpr_bin_exclusion_config_stg R
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			 INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID where exc_id = #{excId} and mb.status!='D'
	
</select>

<select id="getBinExclMainData" resultType="binExclDTO">
		    SELECT R.EXC_ID as excId,R.BIN as bin,R.BASE_OR_FEATURE as feeSelectionType,R.participant_id as participantId, R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate,R.status as status
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,S.unique_bank_name  as bankName
			 ,from_Date_full as fromDateComplate ,to_Date_full as toDateComplate
			  FROM  mcpr_bin_exclusion_config R
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			 INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID where exc_id = #{excId} and mb.status!='D'
</select>	


<delete id="deleteDiscardedEntry">
			DELETE FROM  mcpr_bin_exclusion_config_stg WHERE EXC_ID = #{excId}
</delete>


<select id="getBegMonths" resultType="binExclDTO">
		SELECT TYPE as type ,CODE as code ,DESCRIPTION as description FROM  LOOKUP where type = 'BeginingQtrMonths'
</select>

<select id="getEndMonths" resultType="binExclDTO">
		 SELECT TYPE as type ,CODE as code ,DESCRIPTION as description FROM  LOOKUP where type = 'EndingQtrMonths'	
</select>

<select id="getYearsList" resultType="String">
			SELECT DESCRIPTION as description FROM  LOOKUP where type = 'BaseFeeYears'
</select>

<select id="getParticipantList" resultType="binExclDTO">
		SELECT distinct UNIQUE_BANK_NAME as participantId, UNIQUE_BANK_NAME as participantName FROM  PARTICIPANT where UNIQUE_BANK_NAME is not null	
</select>



<select id="getBinListForBanks" resultType="binExclDTO">
		SELECT BIN_ID as binId , BIN_NUMBER as binNumber FROM  MEMBIN_DETAILS 
		WHERE BIN_NUMBER IS NOT NULL AND PARTICIPANT_ID  in (SELECT PARTICIPANT_ID FROM  PARTICIPANT where UNIQUE_BANK_NAME =#{participantId})	and status!='D'
</select>



<select id="fetchBinFromDb" resultType="binExclDTO">
	SELECT * FROM  mcpr_bin_exclusion_config WHERE BIN = #{binNumber}		
</select>



<select id="fetchIdFromExcIdSequence" resultType="int">
		SELECT nextval('excid_seq')	
</select>



<select id="validateCombination" resultType="binExclDTO">
		SELECT EXC_ID as excId,bin as bin  FROM  mcpr_bin_exclusion_config_stg WHERE EXC_ID != #{excId} and BIN=#{bin} and BASE_OR_FEATURE=#{feeSelectionType}
		
</select>


<select id="validateDuplicateCheckList" resultType="binExclDTO">
		SELECT EXC_ID, BIN FROM  mcpr_bin_exclusion_config_stg WHERE EXC_ID != #{excId} and BIN=#{bin}
		and ((from_date_full=to_date(#{fromYearMonth},'ddmmyyyy') and to_date_full=to_date(#{toYearMonth},'ddmmyyyy')) 
		or (to_date(#{fromYearMonth},'ddmmyyyy') between from_date_full and to_Date_full )
		or (to_date(#{toYearMonth},'ddmmyyyy') between from_date_full and to_Date_full ) 
		or (from_date_full between to_date(#{fromYearMonth},'ddmmyyyy') and to_date(#{toYearMonth},'ddmmyyyy') ) 
		or (to_Date_full between to_date(#{fromYearMonth},'ddmmyyyy') and to_date(#{toYearMonth},'ddmmyyyy')  ) )
		
</select>



<select id="validateFromDateList" resultType="binExclDTO">
		SELECT EXC_ID as excId, BIN as bin FROM  mcpr_bin_exclusion_config_stg  WHERE EXC_ID != #{excId} and BIN=#{bin}
	    and to_Date_full= (to_date(#{fromYearMonth},'ddmmyyyy')) -interval '1 day'	
</select>

<select id="getBinExclusionConfigMainById" resultType="binExclDTO">
		     SELECT  R.EXC_ID as excId,R.participant_id as participantId,R.BIN as bin,R.BASE_OR_FEATURE as feeSelectionType,R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,S.unique_bank_name  as bankName
			,from_Date_full as fromDateComplate ,to_Date_full as toDateComplate
			from  mcpr_bin_exclusion_config R 
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID WHERE EXC_ID = #{excId} and mb.status!='D'
		     
		  
			
</select>

<select id="getParticipantNameById" resultType="String">
		SELECT BANK_NAME as bankName, participant_id as participantId FROM  PARTICIPANT WHERE PARTICIPANT_ID = #{participantId}	
</select>








</mapper>