package org.npci.settlenxt.adminportal.common.util;

import org.npci.settlenxt.portal.common.util.BaseFieldConstants;

public final class FieldConstants extends BaseFieldConstants {
	private FieldConstants() {
	}

	// Lookup Table
	public static final String LKP_TYPE = "lkpType";
	public static final String LKP_VALUE = "lkpValue";
	public static final String LKP_DESC = "lkpDesc";
	public static final String STATE_CODE = "stateCode";
	public static final String STATE_ID = "stateId";
	public static final String STATE_NAME = "stateName";
	public static final String CITY_ID = "cityId";
	public static final String CITY_NAME = "cityName";
	public static final String COUNTRY_ID = "countryId";
	public static final String COUNTRY_NAME = "countryName";

	public static final String BANK_MASTER_CODE = "bankMasterCode";
	public static final String RTGS_ACCT_CODE = "rtgscode";

	// MC<PERSON> fields
	public static final String SUB_SCHEME = "subScheme";
	public static final String CARD_SUB_VAR = "cardSubVariant";
	public static final String PRGRM_DETAILS = "programDetails";
	public static final String FORM_FACTOR = "formFactor";

	// IFSC_Fields
	public static final String MAKER_COMMENTS = "makerComments";
	public static final String RTGS_ACC_ID = "rtgsAccId";
	public static final String CURR_ACC_ID = "currAccId";
	public static final String SAVINGS_ACC_ID = "savingsAccId";
	public static final String NFS_CODE = "nfsCode";
	public static final String IFSC_DESCRIPTION = "ifscDescription";

	public static final String DOMAIN_USAGE = "domainUsage";
	public static final String SAVINGS_ACC_NO = "savingsAccNumber";
	public static final String CURRENT_ACC_NO = "currentAccNumber";
	public static final String ADDR_TYPE = "addressType";
	public static final String CNT_NAME = "cntChkrName";
	public static final String CNT_ADR = "cntAdd1";
	public static final String CNT_CNTY = "cntCountry";
	public static final String CARD_VARIANT = "cardSubVariant";
	public static final String PROG_DTLS = "programDetails";
	public static final String CNT_PIN_CODE = "cntPincode";
	public static final String ACQ_ACTIV_DATE_STR = "acqFrmDateStr";
	public static final String ACQ_DEACTIV_DATE_STR = "acqToDateStr";
	public static final String ISS_ACTIV_DATE_STR = "issFrmDateStr";
	public static final String ISS_DEACTIV_DATE_STR = "issToDateStr";

	public static final String ACQ_ACTIV_DATE = "acqFrmDate";
	public static final String ACQ_DEACTIV_DATE = "acqToDate";
	public static final String ISS_ACTIV_DATE = "issFrmDate";
	public static final String ISS_DEACTIV_DATE = "issToDate";

	public static final String REC_COUNT = "REC_COUNT";
	public static final String FEATURES_BIN = "feturesIssuerbin";
	public static final String VARIANT_BIN = "binCardVariant";
//	Fee Rate 
	public static final String FEE_ID = "feeId";

	public static final String MULTIPLIER = "multiplier";
	public static final String SETTLEMENT_BIN_ID = "settlementBinId";
	public static final String IS_COMPLETE = "isComplete";
	public static final String IS_ACTIVE = "isActive";
	public static final String TXN_CODE = "txnCode";
	public static final String UNIQUE_BANK_NAME = "uniqueBnk";
	public static final String FIELD_NAME = "fieldName";
	public static final String RELATIONAL_OPERATOR = "relationalOperator";
	public static final String FIELD_VALUE = "fieldValue";
	public static final String SIGNIFICANCE = "significance";
	public static final String SUB_FIELD_NAME = "subFieldName";
	public static final String MAJOR_ID = "feeMajorId";
	public static final String CARD_BRAND = "cardBrand";
	public static final String FUNCTION_CODE = "funCd";
	public static final String PRIORITY = "priority";
	public static final String FEE_CONFIGS = "feeConfigs";
	public static final String FEE_CONFIG_ID = "feeConfigId";
	public static final String MINOR_ID = "feeMinorId";
	public static final String REQUEST_ID = "requestId";
	public static final String REMARKS = "rejectReason";

	// FEE MAJOR MINOR
	
	public static final String ACQ_AUTH_FEE_ID = "acqAuthFeeID";
	public static final String ISS_AUTH_FEE_ID = "issAuthFeeID";
	public static final String OFFLINE_ALLOWED = "offlineAllowed";
	public static final String ACQ_PROC_FEE_ID = "acqProcessingFeeID";
	public static final String ACQ_ASS_FEE_ID = "acqAssessmentFeeID";
	public static final String ISS_PROC_FEE_ID = "issProcessingFeeID";
	public static final String ISS_ASS_FEE_ID = "issAssessmentFeeID";

	public static final String WEB_SITE = "webSite";
	public static final String CNT_EMAIL = "cntEmail";
	public static final String AUTH_OFFICER_DESG = "cntDesignation";

	public static final String ISS_OFFLINE_ALLOWED = "isIssOfflineAllowed";

	// card config
	public static final String CARD_CONFIG_ID = "cardConfigId";
	public static final String CARD_TYPE = "cardType";
	public static final String CARD_CONFIG_VARIANT = "cardVariant";
	public static final String BASE_FEE = "baseFee";
	public static final String FROM_DATE = "fromDate";
	public static final String TO_DATE = "toDate";
	public static final String CHECKER_COMMENTS = "checkerComments";
	public static final String CARD_TYPE_NAME = "cardTypeName";
	public static final String CARD_VARIANT_NAME = "cardVariantName";

	// feature fee
	public static final String DETAILS = "details";
	public static final String FEATURE_FEE = "featureFee";
	public static final String FEATURE = "feature";
	// MCPR BIN DETAILS
	public static final String MCPR_BIN_DATA_DETAILS_ID = "mcprBinDataDetailsId";
	public static final String MONTH_ENDING = "monthEnding";
	public static final String YEAR = "year";
	public static final String MONTH = "month";

	public static final String TRANSACTION_CYCLE = "transactionCycle";
	// News & Alerts
	public static final String NEWS_TYPE = "isType";
	public static final String NEWS_ID = "newsId";
	public static final String SUB_TITLE = "subTitle";
	public static final String TITLE = "title";
	public static final String SUMMARY = "summary";
	public static final String FOOTER_DATA = "footerData";
	public static final String REFERENCE_NUMBER = "referenceNumber";
	public static final String PUBLISH_TYPE = "publishType";
	public static final String PERIOD_TYPE = "periodType";
	public static final String SEND_MAIL = "sendMail";
	public static final String COMMENT = "comment";
	public static final String SCHEDULE_ID = "scheduleId";
	public static final String CREATED_ON = "createdOn";
	public static final String FREQ_TO = "freqTo";
	public static final String FREQ_FROM = "freqFrom";
	public static final String SCHEDULE_NAME = "scheduleName";
	public static final String SCHEDULE_DESC = "scheduleDesc";
	public static final String FEATURE_ISS_BIN = "featureIssBin";
	public static final String NEWS_PARTICIPANT_ID = "bankName";
	public static final String NEWS_ROLE = "roles";
	public static final String FROM_DATE_STR = "fromDateStr";
	public static final String TO_DATE_STR = "toDateStr";
	public static final String CRITICAL = "critical";
	public static final String GST_CODES = "gstCode";
	public static final String NET_MIN = "netMin";
	public static final String NET_MAX = "netMax";
	public static final String PARTICIPANT_ID_NFS = "participantIdNFS";
	public static final String FEE_TYPE_CODE = "feeTypCode";
	public static final String FEE_TYPES = "feeTypes";
	public static final String MODIFIED_DATE = "modifiedDate";
	public static final String INTERCHANGE_FEE_ID = "interchangeFeeID";

}
