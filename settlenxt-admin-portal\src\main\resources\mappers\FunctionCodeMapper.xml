<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.FunctionCodeRepository">
	
	<select id="getFunctionCodeListMain" resultType="FunctionCodeDTO">
		SELECT f.func_code_id as funcCodeId, f.mti ,f.proc_code as procCode, f.func_code as funcCode, 
		f.func_code_desc as funcCodeDesc, f.fee_type as feeType, f.fund_movement as fundMovement,f.fund_movement_side as fundMovementSide,
		f.recalculate, f.transaction_type as transactionType, f.network_txn_type as networkTxnType,stg.request_state as requestState
		from func_code f
		inner join func_code_stg stg on f.func_code_id=stg.func_code_id
		 ORDER BY stg.last_updated_on desc
	</select>
	
	<select id="getFunctionCodePendingForApproval" resultType="FunctionCodeDTO">
		SELECT f.func_code_id as funcCodeId,f.mti ,f.proc_code as procCode, f.func_code as funcCode, 
		f.func_code_desc as funcCodeDesc, f.fee_type as feeType, f.fund_movement as fundMovement, f.fund_movement_side as fundMovementSide, 
		f.recalculate, f.transaction_type as transactionType, f.network_txn_type as networkTxnType,
		f.request_state as requestState, f.CHECKER_COMMENTS as checkerComments, f.status,f.last_operation as lastOperation
		from func_code_stg f
		WHERE f.request_state in ('P','R')
	</select>
	<select id="getFunctionCodeProfileMain" resultType="FunctionCodeDTO">
		SELECT f.func_code_id as funcCodeId,f.mti ,f.proc_code as procCode, f.func_code as funcCode, 
		f.func_code_desc as funcCodeDesc, f.fee_type as feeType, f.fund_movement as fundMovement, stg.request_state as requestState,
		f.fund_movement_side as fundMovementSide,f.recalculate, f.transaction_type as transactionType, f.network_txn_type as networkTxnType
		from func_code f
		inner join func_code_stg stg on f.func_code_id=stg.func_code_id
		WHERE f.func_code_id = #{funcCodeId}
	</select>
	<select id="getFunctionCodeStgInfoById" resultType="FunctionCodeDTO">
		SELECT f.func_code_id as funcCodeId,f.mti ,f.proc_code as procCode, f.func_code as funcCode, 
		f.func_code_desc as funcCodeDesc, f.fee_type as feeType, f.fund_movement as fundMovement, 
		f.fund_movement_side as fundMovementSide, f.recalculate, f.transaction_type as transactionType, f.network_txn_type as networkTxnType, 
		f.request_state as requestState,f.created_by as createdBy, f.created_on as createdOn,f.last_operation as lastOperation,
		f.last_updated_by as lastUpdatedBy, f.last_updated_on as lastUpdatedOn, f.status,checker_comments as checkerComments
		from func_code_stg f
		WHERE f.func_code_id = #{funcCodeId}
	</select>
	<select id="getFunctionCodeMain" resultType="FunctionCodeDTO">
		SELECT f.func_code_id as funcCodeId,f.mti ,f.proc_code as procCode, f.func_code as funcCode, f.func_code_desc as funcCodeDesc, 
		f.fee_type as feeType, f.fund_movement as fundMovement, f.fund_movement_side as fundMovementSide, 
		f.recalculate, f.transaction_type as transactionType, f.network_txn_type as networkTxnType,
		f.last_updated_by as lastUpdatedBy, f.last_updated_on as lastUpdatedOn, f.status,f.created_by as createdBy, f.created_on as createdOn
		from func_code f
		WHERE f.func_code_id = #{funcCodeId}
	</select>
	<select id="getFunctionCodeStg" resultType="FunctionCodeDTO">
		select f.func_code_id as funcCodeId,f.mti ,f.proc_code as procCode, f.func_code as funcCode, f.func_code_desc as funcCodeDesc, 
		f.fee_type as feeType, f.fund_movement as fundMovement, f.fund_movement_side as fundMovementSide, 
		f.recalculate, f.transaction_type as transactionType, f.network_txn_type as networkTxnType, 
		f.request_state as requestState,f.created_by  as createdBy, f.created_on as createdOn,f.last_operation as lastOperation,
		f.last_updated_by as lastUpdatedBy, f.last_updated_on as lastUpdatedOn, f.status,f.checker_comments as checkerComments
		from func_code_stg f
		WHERE f.func_code_id = #{funcCodeId}
	</select>
	<select id="fetchFunctionCodeIdSequence" resultType="int">	
		SELECT nextval('func_code_id_seq')
	</select>
	
	<insert id="insertFunctionCodeStg" >
		INSERT INTO func_code_stg 
		(func_code_id, mti, proc_code, func_code, func_code_desc, fee_type ,fund_movement,fund_movement_side, recalculate, 
		transaction_type, network_txn_type,CREATED_BY,CREATED_ON, LAST_UPDATED_BY,LAST_UPDATED_ON, request_state, last_operation, status) VALUES
		(#{funcCodeId}, #{mti}, #{procCode}, #{funcCode}, #{funcCodeDesc},#{feeType}, #{fundMovement},#{fundMovementSide},#{recalculate}, 
		#{transactionType},#{networkTxnType}, #{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{requestState}, #{lastOperation}, #{status})
	</insert>
	<insert id="insertFunctionCodeMain">
	INSERT INTO func_code 
	(func_code_id, mti, proc_code, func_code, func_code_desc, fee_type ,fund_movement, 
	fund_movement_side, recalculate, transaction_type, network_txn_type,CREATED_BY,CREATED_ON, LAST_UPDATED_BY,LAST_UPDATED_ON, status) VALUES
	(#{funcCodeId}, #{mti}, #{procCode}, #{funcCode}, #{funcCodeDesc},#{feeType}, #{fundMovement},#{fundMovementSide},
	#{recalculate}, #{transactionType},#{networkTxnType}, #{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},  #{status})
	</insert>
	<update id="updateFunctionCodeMain">
		UPDATE  func_code SET func_code_id=#{funcCodeId},mti=#{mti}, proc_code=#{procCode}, func_code=#{funcCode}, 
		func_code_desc=#{funcCodeDesc},fee_type=#{feeType},fund_movement=#{fundMovement},fund_movement_side=#{fundMovementSide}, 
		recalculate=#{recalculate}, transaction_type=#{transactionType}, network_txn_type=#{networkTxnType}, 
		LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status}
		WHERE func_code_id = #{funcCodeId}
	</update>
	<update id="updateFunctionCode">
	UPDATE  func_code_stg SET func_code_id=#{funcCodeId},mti=#{mti}, proc_code=#{procCode}, func_code=#{funcCode}, 
	func_code_desc=#{funcCodeDesc}, fee_type=#{feeType},fund_movement=#{fundMovement},fund_movement_side=#{fundMovementSide}, 
	recalculate=#{recalculate}, transaction_type=#{transactionType}, network_txn_type=#{networkTxnType}, 
	LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status},CHECKER_COMMENTS='',
	request_state =#{requestState},last_operation= #{lastOperation}
	WHERE func_code_id = #{funcCodeId}
	</update>
	<update id="updateFunctionCodeDiscard">
		UPDATE  func_code_stg SET func_code_id=#{funcCodeId}, mti=#{mti}, proc_code=#{procCode}, func_code=#{funcCode}, 
		func_code_desc=#{funcCodeDesc},fee_type=#{feeType},fund_movement=#{fundMovement},fund_movement_side=#{fundMovementSide}, 
		recalculate=#{recalculate}, transaction_type=#{transactionType}, network_txn_type=#{networkTxnType}, 
		LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},request_state =#{requestState},last_operation= #{lastOperation}, CHECKER_COMMENTS=''
		 WHERE func_code_id = #{funcCodeId}
	</update>
	<update id="updateFunctionCodeRequestState">
		UPDATE  func_code_stg SET  request_state= #{requestState},status= #{status}, CHECKER_COMMENTS= #{checkerComments}, 
		LAST_UPDATED_BY= #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, last_operation= #{lastOperation} 
		WHERE  func_code_id = #{funcCodeId}
	</update>
	<delete id="deleteDiscardedEntry">
		DELETE FROM  func_code_stg WHERE func_code_id = #{funcCodeId}
	</delete>
	<select id="validateDuplicateCheck" resultType="int">
		SELECT count(*) from  func_code_stg f where f.mti  =#{mti} and f.proc_code  =#{procCode} and f.func_code  =#{funcCode}
	</select>
	
	<select id="getAll" resultType="org.npci.settlenxt.portal.common.dto.FunctionCode">
		SELECT
		mti as mti ,
		proc_code as procCode ,
		func_code as funcCode ,
		fee_type as feeType ,
		fund_movement as fundMovement ,
		fund_movement_side as fundMovementSide ,
		func_code_desc as functionCodeDescription,
		recalculate as recalculate,
		transaction_type as transactionType,
		network_txn_type as networkTransactionType,
		default_reason_rej_code as defaultReasonRejCode,
		action_code as actionCode
	FROM
		func_code;
	</select>
</mapper>