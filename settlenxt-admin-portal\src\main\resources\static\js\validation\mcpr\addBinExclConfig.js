var isdisableflag=0;
var isdisableflag1=0;
var localExcId;
$(document).ready(function() {

	
		    var date = new Date();
		    var sys_year = date.getFullYear();
		    var sys_month = date.getMonth()+1;
		    var sysDate = "" + sys_year + (padTo2Digits(sys_month));

			localExcId=$("#excId").val();
							
				var toDate=parseInt($("#toYear").val()+""+$("#toMonth").val());
				var fromDate=parseInt($("#fromYear").val()+""+$("#fromMonth").val());

		    if(localExcId>0){
		    if(fromDate<=sysDate ){
				   $('#fromMonth').attr('disabled', true);
				   $('#fromYear').attr('disabled', true); 
				   $('#feeSelectionType').attr('disabled', true); 
					isdisableflag=1;
					
				}
		    if(toDate<sysDate ){
				   $('#toMonth').attr('disabled', true);
				   $('#toYear').attr('disabled', true);  
			    	isdisableflag1=1;
				}
		    }
		    if(isdisableflag==1 && isdisableflag1==1){
				document.getElementById("BEditSubmit").disabled = false;
		    }


populateBinData();
				

	disableSave();
	
	$("#fromMonth").on('keyup keypress blur change', function () {
	validateFromMonth();
        unableSave();
    });
 	$("#fromYear").on('keyup keypress blur change', function () {
 	validateFromYear();
        unableSave();
    });
	$("#toMonth").on('keyup keypress blur change', function () {
	validateToMonth();
        unableSave();
    });
 	$("#toYear").on('keyup keypress blur change', function () {
 	validateToYear();
        unableSave();
    });	
    $("#participantName").on('keyup keypress blur change', function () {
    validateBank();
        unableSave();
    });
 	$("#binNumber").on('keyup keypress blur change', function () {
 	validateBin();
        unableSave();
    });	
    $("#feeSelectionType").on('keyup keypress blur change', function () {
    validateFeeType();
        unableSave();
    });
 
});

function disableSave()
{
if (typeof BAddSubmit != "undefined") {
	document.getElementById("BAddSubmit").disabled = true;
}
if (typeof BEditSubmit != "undefined") {
	document.getElementById("BEditSubmit").disabled = true;
}
}

function unableSave()
{
	if (typeof BAddSubmit != "undefined") {
		document.getElementById("BAddSubmit").disabled = false;
	}
	if (typeof BEditSubmit != "undefined") {
		document.getElementById("BEditSubmit").disabled = false;
	}
	if(isdisableflag==1 && isdisableflag1==1){
		document.getElementById("BAddSubmit").disabled = true;
	}
}


function padTo2Digits(sys_month) {
return sys_month.toString().padStart(2, '0');
}
				
			




window.history.forward();
function noBack() {
	window.history.forward();
}
function selectBinData() {
	var binexclBin=document.getElementById("binexclBin").value;

    var selectObj = document.getElementById("binNumber");
    var i = 0;
     for (i of selectObj) {
        if (i.text== binexclBin) {
            i.selected = true;
            return;
        }
    }
}
function resetAction() {


document.getElementById("addEditBinExclConfig").reset();
$("#errfromYear").find('.error').html('');
$("#errparticipantId").find('.error').html('');
$("#errtoMonth").find('.error').html('');
$("#errtoYear").find('.error').html('');
$("#errfeeSelectionType").find('.error').html('');
$("#errfromMonth").find('.error').html('');
$("#errbinId").find('.error').html('');
}

function populateBinData() {
				if ($('#participantName').val() != '0') {
					var participantId = $("#participantName").val();
					
					var tokenVal = document.getElementsByName("_TransactToken")[0].value;
					
					$.ajax({
								url : "getBinList",
								type : "POST",
								data : {
									participantId : participantId,
                					_TransactToken: tokenVal
								},
								dataType : "json",
								"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenVal);},
								success : function(data) {
									
									$("#binNumber")
											.empty();
									$("#binNumber")
											.append(
													'<option value="">SELECT</option>');
									$
											.each(
													data,
													function(
															_index,
															option) {
														$(
																"#binNumber")
																.append(
																		'<option value="'
																				+ option.binNumber
																				+ '">'
																				+ option.binNumber
																				+ '</option>');
													});
									
									
									selectBinData();
								}
							});

				} else {
					$("#binNumber").empty();
				
				}

} 


function userAction1(_type, action) {
	var url = action;
	var excId = document.getElementById("excId").value;
	var data = "excId," + excId + ",status,"
		+ status;
	postData(url, data);
} 


function validateBank() {
	var bankFlag = $("#participantName").val().replace(/(^\s*)|(\s*$)/g, '');
	if (bankFlag == "") {
		$("#errparticipantId").find('.error').html(binExclConfigValidationMessages['participantName']);
		return false;
	}
	else {
		$("#errparticipantId").find('.error').html('');
	}
	return true;
}



function validateBinDate(){
	var toDate=0;
    var fromDate=0;
    var currentYear = new Date().getFullYear();
    var currentMonth = new Date().getMonth()+1;
    var sysDate = "" + currentYear + (padTo2Digits(currentMonth));
    toDate=parseInt($("#toYear").val()+""+$("#toMonth").val());
    fromDate=parseInt($("#fromYear").val()+""+$("#fromMonth").val());
    if(isdisableflag==0)
    {
		if(localExcId>0)
		{	   
		    if(fromDate <= sysDate){
		    	$("#errfromMonth").find('.error').html(binExclConfigValidationMessages['pastMonthValidation']);
				return false;
		     }
		 }	
		else	
		{	   
			if(parseInt(sysDate) -parseInt(fromDate) > 2){
		    	$("#errfromMonth").find('.error').html(binExclConfigValidationMessages['pastMonthValidation']);
				return false;
		 	}
		}
	} 
 
    if(fromDate>toDate){
    	$("#errtoYear").find('.error').html(binExclConfigValidationMessages['dateValidation']);
		return false;
        
    }
    
  
	return true;

}

function validateBin() {
	var binFlag = $("#binNumber").val().replace(/(^\s*)|(\s*$)/g, '');
	if (binFlag == "") {
		$("#errbinId").find('.error').html(binExclConfigValidationMessages['binNumber']);
		return false;
	}
	else {
		$("#errbinId").find('.error').html('');
	}
	return true;
}
function validateFeeType() {
	var feeFlag = $("#feeSelectionType").val().replace(/(^\s*)|(\s*$)/g, '');
	if (feeFlag == "") {
		$("#errfeeSelectionType").find('.error').html(binExclConfigValidationMessages['feeSelectionType']);
		return false;
	}
	else {
		$("#errfeeSelectionType").find('.error').html('');
	}
	return true;
} 

function validateFromMonth() {
	var fromDateFlag = $("#fromMonth").val();
	if (fromDateFlag == "") {
		$("#errfromMonth").find('.error').html(binExclConfigValidationMessages['fromMonth']);
		return false;
	
	} else {
		$("#errfromMonth").find('.error').html('');
	}
	return true;
}
function validateToMonth() {
	var toDateFlag = $("#toMonth").val();
	if (toDateFlag == "") {
		$("#errtoMonth").find('.error').html(binExclConfigValidationMessages['toMonth']);
		return false;
	}
	else {
		$("#errtoMonth").find('.error').html('');
	}
	return true;
}
function validateToYear() {
	var toYearFlag = $("#toYear").val().replace(/(^\s*)|(\s*$)/g, '');
	if (toYearFlag == "") {
		$("#errtoYear").find('.error').html(binExclConfigValidationMessages['toYear']);
		return false;
	}
	else {
		$("#errtoYear").find('.error').html('');
	}
	return true;
	
}

function validateFromYear() {
	var fromYearFlag = $("#fromYear").val().replace(/(^\s*)|(\s*$)/g, '');
	if (fromYearFlag == "") {
		$("#errfromYear").find('.error').html(binExclConfigValidationMessages['fromYear']);
		return false;
	}
	else {
		$("#errfromYear").find('.error').html('');
	}
	return true;
}





function validateAddEditForm(id) {
	
	$('.jqueryError').text("");
	$('.jqueryError').hide();
	var check = false;
	

	if (!validateBank()) {

		check = true;
	}
	
	
	if (!validateBin()) {

		check = true;
	}
	
	if (!validateFeeType()) {

		check = true;
	}
	
	if (!validateFromMonth()) {

		check = true;
	}
	

	if (!validateToMonth()) {

		check = true;
	}
	

	if (!validateFromYear()) {

		check = true;
	}
	

	if (!validateToYear()) {

		check = true;
	}
	
	if (!validateBinDate()) {

		check = true;
	}
	
	
	if (!check) {
	 
			addEdit(id);

	} else {
		
		return false;
	}

}


function addEdit(id) {
	var url;
	if (id == 'A') {
		url = '/addBinExclConfig';

	} else if (id == 'E') {
		url = '/updateBinExclConfig';
	}

	
	var excId = document.getElementById("excId").value;
	var fromMonth = document.getElementById("fromMonth").value;
	var fromYear = document.getElementById("fromYear").value;
	var toMonth = document.getElementById("toMonth").value;
	var toYear = document.getElementById("toYear").value;
	var participantName = document.getElementById("participantName").value;
	var bin = document.getElementById("binNumber").value;
	var feeSelectionType = document.getElementById("feeSelectionType").value;
		

		


	var data = "excId," + excId + ",fromMonth," + fromMonth
			+ ",fromYear," + fromYear + ",toMonth," + toMonth
			+ ",toYear," + toYear
			+ ",participantName," + participantName + ",bin," + bin
			+ ",bankName," +  $("#participantName").val() /*+",bin," +  $("#binNumber").val()*/
			+ ",feeSelectionType," + feeSelectionType 
			+ ",userType," + $('#userType').val()
			+",parentPage," + $("#hparentPage").val();
	

		postData(url, data);

	}



function postDiscardBinAction(action) {
	
	var url = action;
	var excId = document.getElementById("excId").value;
	var data = "excId," + excId  ;
	postData(url, data);
	
}


