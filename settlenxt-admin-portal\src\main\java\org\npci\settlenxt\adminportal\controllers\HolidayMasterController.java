package org.npci.settlenxt.adminportal.controllers;

import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.HolidayMasterDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.HolidayMasterService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.service.BaseLookupServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class HolidayMasterController extends BaseController {

	@Autowired
	HolidayMasterService holidayMasterSvc;

	@Autowired
	private SessionDTO sessionDto;

	@Autowired
	BaseLookupServiceImpl lookUpService;

	private String showMainTab = "Y";

	private static final String STATUS = "status";

	@Autowired
	private MessageSource messageSource;
	private static final String SHOW_HOLIDAY_MASTER = "showHolidayMaster";
	private static final String SHOW_HOLIDAY = "showHoliday";
	private static final String SHOW_APPROVAL_TAB = "showApprovalTab";
	private static final String SHOW_CHECKBOX = "showCheckBox";
	private static final String VIEW_APPROVE_HOLIDAY = "viewApproveHoliday";
	private static final String ADD_HOLIDAY_MASTER = "addEditHolidayMaster";
	private static final String SHOW_MAIN_TAB = "showMainTab";
	private static final String HOLIDAY_TYPE = "HOLIDAY_TYPE";
	private static final String WEEK_TYPE = "WEEK_TYPE";
	private static final String WEEK_LIST = "WEEK_LIST";
	private static final String PRODUCT_LIST = "productList";
	private static final String WEEK_TYPE_LIST = "weekTypeList";
	private static final String DAY_OF_WEEK = "dayOfWeekList";
	private static final String PERIOD_TYPE_LIST = "periodTypeList";
	private static final String HOLIDAY_MASTER_DTO = "holiodayMasterDTO";
	private static final String SHOW_TO_DATE = "showToDate";
	private static final String SHOW_DAY_OF_WEEK = "showdayOfWeek";
	private static final String HOLIDAY_APPROVE_MSG = "Holiday Added successfully and sent for approval!!!";
	private static final String SHOW = "show";
	private static final String E_CONST = "E";
	private static final String HOLIDAY_DISCARD_MSG = "Holiday discarded successfully!!!";
	private static final String HOLIDAY_DELETE_MSG = "Holiday Deleted successfully and sent for Approval!!!";
	

	public static final String SHOW_BUTTON = "showButton";
	private static final String AFTERSAVE = "afterSave";

	private static final String SHOW_DISCARD = "showDiscard";
	private static final String HOLIDAY_UPDATE_MSG = "Holiday details updated successfully and sent for approval!!!";

	public void inputValidation(String input) {
		String regex = "[a-zA-Z0-9]+";
		Pattern p = Pattern.compile(regex);
		if (input == null) {
			throw new SettleNxtApplicationException("Input is Null", "");
		}
		Matcher m = p.matcher(input);

		if (!m.matches()) {
			throw new SettleNxtApplicationException("Invalid Input Fields", "");
		}
	}

	@PostMapping("/getHolidayMasterList")
	@PreAuthorize("hasAuthority('View Holiday Master')")
	public String getHolidayMasterList(Model model) {
		showMainTab = CommonConstants.YES_FLAG;
		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, CommonConstants.YES_FLAG);
		model.addAttribute(SHOW_MAIN_TAB, showMainTab);
		List<HolidayMasterDTO> approvedHolidayMasterList = holidayMasterSvc.getApprovedHolidayMasterList();
		model.addAttribute(CommonConstants.HOLIDAY_MASTER_LIST, approvedHolidayMasterList);
		return getView(model, SHOW_HOLIDAY_MASTER);
	}

	@PostMapping("/getHolidayMasterPendingForApproval")
	@PreAuthorize("hasAuthority('View Holiday Master')")
	public String getPendingHolidayMasterList(Model model) {
		showMainTab = BaseCommonConstants.NO_FLAG;
		model.addAttribute(SHOW_MAIN_TAB, showMainTab);
		model.addAttribute(SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		List<HolidayMasterDTO> pendingHolidayMasterList = holidayMasterSvc.getPendingHolidayMasterList();
		model.addAttribute(CommonConstants.HOLIDAY_MASTER_LIST, pendingHolidayMasterList);

		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}

		return getView(model, SHOW_HOLIDAY_MASTER);
	}

	@PostMapping("/viewApproveHolidayMaster")
	@PreAuthorize("hasAuthority('View Holiday Master')")
	public String viewApproveHoliday(@RequestParam("holidaySeqId") String holidaySeqId, Model model) {

		holidaySeqId = new String(java.util.Base64.getDecoder().decode(holidaySeqId));
		HolidayMasterDTO holidayMasterDTO;
		if (showMainTab.equals(CommonConstants.YES_FLAG)) {
			holidayMasterDTO = holidayMasterSvc.getHolidayFromMain(holidaySeqId);
		} else {
			holidayMasterDTO = holidayMasterSvc.getHoliday(holidaySeqId);
		}
		model.addAttribute(CommonConstants.HOLIDAY_MASTER_DTO, holidayMasterDTO);
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, showMainTab);

		return getView(model, VIEW_APPROVE_HOLIDAY);

	}

	@PostMapping("/editHoliday")
	@PreAuthorize("hasAuthority('Edit Holiday Master')")
	public String editHolidayMaster(@RequestParam("holidaySeqId") String holidaySeqId, Model model) {
		holidaySeqId = new String(java.util.Base64.getDecoder().decode(holidaySeqId));

		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
		List<CodeValueDTO> productList = holidayMasterSvc.getProductList();
		List<CodeValueDTO> periodTypeList = lookUpService.getLookupData(HOLIDAY_TYPE);
		List<CodeValueDTO> weekTypeList = lookUpService.getLookupData(WEEK_TYPE);
		List<CodeValueDTO> dayOfWeekList = lookUpService.getLookupData(WEEK_LIST);
		model.addAttribute(PRODUCT_LIST, productList);
		model.addAttribute(WEEK_TYPE_LIST, weekTypeList);
		model.addAttribute(DAY_OF_WEEK, dayOfWeekList);
		model.addAttribute(PERIOD_TYPE_LIST, periodTypeList);
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, showMainTab);

		HolidayMasterDTO holidayMasterDTO = new HolidayMasterDTO();
		try {
			holidayMasterDTO = holidayMasterSvc.getHoliday(holidaySeqId);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_HOLIDAY_MASTER, ex);
		}

		model.addAttribute(CommonConstants.HOLIDAY_MASTER_DTO, holidayMasterDTO);
		return getView(model, ADD_HOLIDAY_MASTER);
	}

	@PostMapping("/updateHoliday")
	@PreAuthorize("hasAuthority('Edit Holiday Master')")
	public String updateHolidayMaster(@ModelAttribute("HolidayMasterDTO") HolidayMasterDTO holidayMasterDTO,
			Model model) {

		try {

			inputValidation(holidayMasterDTO.getHolidayDesc());
			holidayMasterSvc.updateHoliday(holidayMasterDTO);

		} catch (Exception ex) {
			model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_HOLIDAY_MASTER, ex);
		}
		model.addAttribute(AFTERSAVE, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, showMainTab);

		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);

		List<CodeValueDTO> productList = holidayMasterSvc.getProductList();
		List<CodeValueDTO> periodTypeList = lookUpService.getLookupData(HOLIDAY_TYPE);
		List<CodeValueDTO> weekTypeList = lookUpService.getLookupData(WEEK_TYPE);
		List<CodeValueDTO> dayOfWeekList = lookUpService.getLookupData(WEEK_LIST);
		model.addAttribute(PRODUCT_LIST, productList);
		model.addAttribute(WEEK_TYPE_LIST, weekTypeList);
		model.addAttribute(DAY_OF_WEEK, dayOfWeekList);
		model.addAttribute(PERIOD_TYPE_LIST, periodTypeList);

		model.addAttribute(CommonConstants.HOLIDAY_MASTER_DTO, holidayMasterDTO);
		model.addAttribute(SHOW_BUTTON, CommonConstants.YES_FLAG);

		model.addAttribute(CommonConstants.SUCCESS_STATUS, HOLIDAY_UPDATE_MSG);

		return getView(model, ADD_HOLIDAY_MASTER);
	}

	@PostMapping("/addHoliday")

	@PreAuthorize("hasAuthority('Add Holiday Master')")
	public String addHolidayMaster(Model model) {
		HolidayMasterDTO holiodayMasterDTO = new HolidayMasterDTO();
		List<CodeValueDTO> productList = holidayMasterSvc.getProductList();
		List<CodeValueDTO> periodTypeList = lookUpService.getLookupData(HOLIDAY_TYPE);
		List<CodeValueDTO> weekTypeList = lookUpService.getLookupData(WEEK_TYPE);
		List<CodeValueDTO> dayOfWeekList = lookUpService.getLookupData(WEEK_LIST);
		model.addAttribute(HOLIDAY_MASTER_DTO, holiodayMasterDTO);
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, showMainTab);

		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(PRODUCT_LIST, productList);
		model.addAttribute(WEEK_TYPE_LIST, weekTypeList);
		model.addAttribute(DAY_OF_WEEK, dayOfWeekList);
		model.addAttribute(PERIOD_TYPE_LIST, periodTypeList);
		return getView(model, ADD_HOLIDAY_MASTER);
	}

	@PostMapping("/saveHolidayMaster")

	@PreAuthorize("hasAuthority('Add Holiday Master')")
	public String saveHolidayMaster(@ModelAttribute HolidayMasterDTO holidayMasterDto, Model model) {

		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		try {

			inputValidation(holidayMasterDto.getHolidayDesc());
			holidayMasterDto = holidayMasterSvc.addEditHolidayMaster(holidayMasterDto);

		} catch (Exception ex) {
			List<CodeValueDTO> productList = holidayMasterSvc.getProductList();
			List<CodeValueDTO> periodTypeList = lookUpService.getLookupData(HOLIDAY_TYPE);
			List<CodeValueDTO> weekTypeList = lookUpService.getLookupData(WEEK_TYPE);
			List<CodeValueDTO> dayOfWeekList = lookUpService.getLookupData(WEEK_LIST);
			model.addAttribute(CommonConstants.SHOW_MAIN_TAB, showMainTab);
			model.addAttribute(PRODUCT_LIST, productList);
			model.addAttribute(WEEK_TYPE_LIST, weekTypeList);
			model.addAttribute(DAY_OF_WEEK, dayOfWeekList);
			model.addAttribute(PERIOD_TYPE_LIST, periodTypeList);
			model.addAttribute(CommonConstants.HOLIDAY_MASTER_DTO, holidayMasterDto);
			model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_HOLIDAY_MASTER, ex);

		}
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, showMainTab);
		model.addAttribute(AFTERSAVE, CommonConstants.YES_FLAG);

		List<CodeValueDTO> productList = holidayMasterSvc.getProductList();

		List<CodeValueDTO> periodTypeList = lookUpService.getLookupData(HOLIDAY_TYPE);

		List<CodeValueDTO> weekTypeList = lookUpService.getLookupData(WEEK_TYPE);

		List<CodeValueDTO> dayOfWeekList = lookUpService.getLookupData(WEEK_LIST);

		model.addAttribute(PRODUCT_LIST, productList);
		model.addAttribute(WEEK_TYPE_LIST, weekTypeList);
		model.addAttribute(DAY_OF_WEEK, dayOfWeekList);
		model.addAttribute(PERIOD_TYPE_LIST, periodTypeList);

		model.addAttribute(CommonConstants.HOLIDAY_MASTER_DTO, holidayMasterDto);
		model.addAttribute(SHOW_BUTTON, CommonConstants.YES_FLAG);
		model.addAttribute(SHOW_HOLIDAY, CommonConstants.YES_FLAG);

		if (holidayMasterDto.getToDate() == null) {
			model.addAttribute(SHOW_TO_DATE, CommonConstants.YES_FLAG);
			model.addAttribute(SHOW_DAY_OF_WEEK, CommonConstants.YES_FLAG);

		}

		model.addAttribute(CommonConstants.SUCCESS_STATUS, HOLIDAY_APPROVE_MSG);

		return getView(model, ADD_HOLIDAY_MASTER);

	}

	@PostMapping("/approveOrRejectBulkHolidayMaster")
	@PreAuthorize("hasAuthority('Approve Holiday Master')")
	public String approveUserStatus(@RequestParam("bulkApprovalholidayMasterList") String bulkApprovalholidayMasterList,
			@RequestParam(STATUS) String status, Model model) {
		try {

			bulkApprovalholidayMasterList = new String(
					java.util.Base64.getDecoder().decode(bulkApprovalholidayMasterList));
			String remarks = "";
			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}

			HolidayMasterDTO holidayMasterDto = holidayMasterSvc
					.updateApproveOrRejectHolidayMasterBulk(bulkApprovalholidayMasterList, status, remarks);
			checkHolidayMasterApproveStatus(holidayMasterDto, model);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_HOLIDAY_MASTER, ex);
		}

		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		List<HolidayMasterDTO> pendingHolidayMasterDTOList = holidayMasterSvc.getPendingForAppovalHolidayMasterList();
		model.addAttribute(CommonConstants.HOLIDAY_MASTER_LIST, pendingHolidayMasterDTOList);

		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}

		return getView(model, SHOW_HOLIDAY_MASTER);

	}

	private void checkHolidayMasterApproveStatus(HolidayMasterDTO holidayMasterDto, Model model) {
		Locale locale=Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(holidayMasterDto.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_HolidayMasterApproved", null, locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_HolidayMasterRejected", null, locale));
		}
	}

	@PostMapping("/approveOrRejectHoliday")
	@PreAuthorize("hasAuthority('Approve Holiday Master')")
	public String approveUserStatus(@RequestParam("holidaySeqId") String holidayMasterSeqId,
			@RequestParam(STATUS) String status, @RequestParam("remarks") String remarks, Model model) {
		try {

			holidayMasterSeqId = new String(java.util.Base64.getDecoder().decode(holidayMasterSeqId));
			int holidaySeqId = Integer.parseInt(holidayMasterSeqId);
			HolidayMasterDTO holidayMasterDto = holidayMasterSvc.updateApproveOrRejectHolidayMaster(holidaySeqId,
					status, remarks);
			checkHolidayMasterApproveStatus(holidayMasterDto, model);
			model.addAttribute(CommonConstants.HOLIDAY_MASTER_APP_REJ_DTO, holidayMasterDto);
			model.addAttribute(SHOW, E_CONST);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_HOLIDAY, ex);
		}
		return getView(model, VIEW_APPROVE_HOLIDAY);
	}

	@PostMapping("/discardHoliday")
	@PreAuthorize("hasAuthority('Edit Holiday Master')")
	public String discardHoliday(@RequestParam("holidaySeqId") String holidaySeqId, Model model) {
		try {
			holidaySeqId = new String(java.util.Base64.getDecoder().decode(holidaySeqId));
			HolidayMasterDTO holidayMasterDTO = holidayMasterSvc.discardHoliday(holidaySeqId);
			model.addAttribute(CommonConstants.HOLIDAY_MASTER_DTO, holidayMasterDTO);
			model.addAttribute(SHOW_DISCARD, BaseCommonConstants.NO_FLAG);

			model.addAttribute(CommonConstants.SUCCESS_STATUS, HOLIDAY_DISCARD_MSG);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_HOLIDAY, ex);

		}
		return getView(model, VIEW_APPROVE_HOLIDAY);
	}

	@PostMapping("/deleteHolidayMaster")
	@PreAuthorize("hasAuthority('Edit Holiday Master')")
	public String deactivateHoliday(@RequestParam("hid") String hid, @RequestParam("reqst") String reqst, Model model,
			HttpServletRequest request) {
		try {
			hid = new String(java.util.Base64.getDecoder().decode(hid));

			HolidayMasterDTO holidayMasterDTO;

			holidayMasterSvc.deleteReq(hid, reqst);
			holidayMasterDTO = holidayMasterSvc.getHoliday(hid);
			model.addAttribute(CommonConstants.HOLIDAY_MASTER_DTO, holidayMasterDTO);
			model.addAttribute(CommonConstants.SHOW_MAIN_TAB, showMainTab);
			model.addAttribute(CommonConstants.SUCCESS_STATUS, HOLIDAY_DELETE_MSG);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_HOLIDAY, ex);

		}
		return getView(model, VIEW_APPROVE_HOLIDAY);
	}
}