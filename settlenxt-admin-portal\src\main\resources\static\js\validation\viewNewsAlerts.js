$(document).ready(function() {
	$('.appRejMust').hide();
	$('.remarkMust').hide();

	$('.viewOnly').attr('readonly', 'true');

	$('#apprej').change(function() {
		if ($("#apprej").val() != "N") {
			$(".appRejMust").hide();
		} else {
			$(".appRejMust").show();
			$(".remarkMust").hide();
		}
	});

	$('#rejectReason').change(function() {
		if ($('#rejectReason').val() == "")
			$(".remarkMust").show();
		else
			$(".remarkMust").hide();
	});

	$('select').attr("disabled", "disabled");
	$('#apprej').removeAttr("disabled");
	
	
	$("#title1").val($('#title').val());
	
	$("#subTitle1").val($('#subTitle').val());
	
	var value3 = document.getElementById('publishType').value;
	
	if(value3!="Daily"){
$('#freq').show();
}

	
});

function backAction(_type, action) {
	let url = action;
	var data = "";
	postData(url, data);
}

function enadisReject() {
	$(".appRejMust").hide();
}

function postAction(action) {
	
	
	if ($("#apprej").val() != "N") {
		$(".appRejMust").hide();
	} else {
		$(".appRejMust").show();
		return false;
	}

	if ($('#rejectReason').val() == "") {
		$(".remarkMust").show();
		return false;
	} else {
		$(".remarkMust").hide();
		if (maxLengthTextArea('rejectReason')) {
			$('button').prop('disabled', true);
			var url = action;
			var data = "newsId," + $('#newsId').val() +",referenceNumber,"+ $('#referenceNumber').val() + ",status,"
					+ $('#apprej option:selected').val() + ",remarks," + $('#rejectReason').val();
						
			postData(url, data);
		}
	}

	
}

function editNews(reqId) {
	
	let url = '/getNewsAlerts';
	var data = "reqType,"+"E"+",editFlag,"+"E"+",refNumber,"+reqId+",screenName,"+$('#screenName').val(); 
	postData(url, data);
	clickAndDisable(this); 
}

function setRefnum(reqId){
	document.getElementById("refNum").value = reqId;

}
function deleteNewsAlerts() {

	
	
	var reqId = document.getElementById("refNum").value;
var screenName=$('#screenName').val();
	let url = '/deleteNewsAlerts';
	var data = "refNumber,"+reqId+",screenName,"+screenName; 
	postData(url, data);
	clickAndDisable(this);
}