package org.npci.settlenxt.adminportal.controllers;

import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.cache.DataSecurityUtility;
import org.npci.settlenxt.adminportal.common.cache.FunctionCodeCache;
import org.npci.settlenxt.adminportal.common.cache.LookupDTOCache;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.ActionCodeService;
import org.npci.settlenxt.adminportal.service.IncomingTransactionDetailService;
import org.npci.settlenxt.common.cache.BaseActionCodeCache;
import org.npci.settlenxt.portal.common.controllers.BaseTransactionDetailController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.DisputeTxnModel;
import org.npci.settlenxt.portal.common.dto.IncomingTransactionDetailDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class IncomingTransactionDetailController extends BaseTransactionDetailController {
	@Autowired
	LookupDTOCache lookupDTOCache;

	@Autowired
	FunctionCodeCache functionCodeCache;

	@Autowired
	BaseActionCodeCache actionCodeCache;

	@Autowired
	SessionDTO sessionDTO;

	@Autowired
	IncomingTransactionDetailService incomingTransactionDetailService;

	@Autowired
	private DataSecurityUtility dataSecurityUtil;

	@Autowired
	ActionCodeService actionCodeService;

	// Common constants
	public static final String SUBNET_LIST = "subnetList";
	public static final String FUNC_CODE_LIST = "funcCodeList";

	@PostMapping("/incomingTransactionDetails")
	@PreAuthorize("hasAuthority('View Incoming Transaction Search')")
	public String incomingTransactionSearch(Model model,
			@ModelAttribute("incomingTransactionDetailDTO") IncomingTransactionDetailDTO incomingTransactionDetailDTO) {
		List<CodeValueDTO> subnetList = incomingTransactionDetailService.fetchNetworkBankTypeSubnet();
		List<CodeValueDTO> funcCodeList = getFormattedFuncCodeList();
		List<CodeValueDTO> rejectReasonList = actionCodeService.getReasonCodeList();

		addAttributesToModel(model, incomingTransactionDetailDTO, rejectReasonList, subnetList, funcCodeList);
		if (incomingTransactionDetailDTO.getPan() != null) {
			incomingTransactionDetailDTO
					.setDecryptedPan(dataSecurityUtil.encrypt(incomingTransactionDetailDTO.getPan()));
		}
		List<DisputeTxnModel> finalList = getDisputesDetailsIfNeeded(incomingTransactionDetailDTO);
		if (finalList != null && !finalList.isEmpty()) {
			for (DisputeTxnModel disputeTxnModel : finalList) {
				setSearchTranSearchFields(disputeTxnModel);
			}

			model.addAttribute("disputeTxnModel", finalList);
		}
		configureModelForPageType(model, incomingTransactionDetailDTO);
		if (incomingTransactionDetailDTO.isSearchBtnPressed() || (finalList != null && !finalList.isEmpty())) {

			model.addAttribute("show", "Y");

		}

		return getView(model, "incomingTransactionDetailSearch");
	}

	@PostMapping("/approveMakerData")
	@PreAuthorize("hasAuthority('Approve Disputes as Admin Maker')")
	public String approveRecord(@RequestParam(value = "data", required = false) String data, Model model) {
		List<IncomingTransactionDetailDTO> disputeList = decodeAndParseTransactionList(data, true);
		updateModelBasedOnStatus(model, disputeList);
		disputeList.get(0).setSearchBtnPressed(true);
		return incomingTransactionSearch(model, disputeList.get(0));
	}

	@PostMapping("/approveCheckerData")
	@PreAuthorize("hasAuthority('Approve Disputes as Admin Checker')")
	public String approveRecordChecker(@RequestParam(value = "data", required = false) String data, Model model) {
		List<IncomingTransactionDetailDTO> disputeList = decodeAndParseTransactionList(data, false);
		updateModelBasedOnStatus(model, disputeList);
		disputeList.get(0).setSearchBtnPressed(true);
		return incomingTransactionSearch(model, disputeList.get(0));
	}

	private void addAttributesToModel(Model model, IncomingTransactionDetailDTO incomingTransactionDetailDTO,
			List<CodeValueDTO> rejectReasonList, List<CodeValueDTO> subnetList, List<CodeValueDTO> funcCodeList) {
		model.addAttribute("incomingTransactionDetailDTO", incomingTransactionDetailDTO);
		model.addAttribute("reasonCodeList", rejectReasonList);
		model.addAttribute(SUBNET_LIST, subnetList);
		model.addAttribute(FUNC_CODE_LIST, funcCodeList);
	}

	private List<CodeValueDTO> getFormattedFuncCodeList() {
		List<CodeValueDTO> funcCodeList = lookupDTOCache.getLookupListOfType("UNIQ_FUNC_CODE_INC_TXN_SEARCH");
		return funcCodeList.stream().map(codeValueDto -> {
			CodeValueDTO codeValDto = new CodeValueDTO();
			codeValDto.setCode(codeValueDto.getCode());
			codeValDto.setDescription(codeValueDto.getCode().split("-")[0] + "-" + codeValueDto.getDescription());
			return codeValDto;
		}).toList();
	}

	private List<DisputeTxnModel> getDisputesDetailsIfNeeded(
			IncomingTransactionDetailDTO incomingTransactionDetailDTO) {
		if (incomingTransactionDetailDTO.getOutgoingCreatedDate() != null)
			return incomingTransactionDetailService.getDisputesDetails(incomingTransactionDetailDTO);
		return new ArrayList<>();

	}

	private void configureModelForPageType(Model model, IncomingTransactionDetailDTO incomingTransactionDetailDTO) {
		if (incomingTransactionDetailDTO.getPage() != null) {
			model.addAttribute("showCheckerTab", incomingTransactionDetailDTO.getPage().equals("C") ? "Y" : "N");
			model.addAttribute("showMakerTab", incomingTransactionDetailDTO.getPage().equals("M") ? "Y" : "N");
		} else {
			model.addAttribute("showMakerTab", "Y");
		}
	}

	private void updateModelBasedOnStatus(Model model, List<IncomingTransactionDetailDTO> disputeList) {
		String status = disputeList.get(0).getStatus();
		if ("RPA".equals(status) || "IP".equals(status)) {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS, "Selected disputes are approved sucessfully");
		} else if ("FAILED".equals(status)) {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS, "Selected disputes are rejected sucessfully");
		}
	}

	private List<IncomingTransactionDetailDTO> decodeAndParseTransactionList(String data, boolean isMaker) {
		byte[] decodedBytes = Base64.getDecoder().decode(data);
		String decodedString = new String(decodedBytes, StandardCharsets.UTF_8);
		String jsonString = decodedString.replace('|', ',');

		List<IncomingTransactionDetailDTO> disputeList = new ArrayList<>();
		if (isMaker) {
			incomingTransactionDetailService.parseJsonToTransactionList(disputeList, jsonString);
		} else {
			incomingTransactionDetailService.parseJsonToTransactionListChecker(disputeList, jsonString);
		}
		return disputeList;
	}

	private void setSearchTranSearchFields(DisputeTxnModel disputeTxnModel) {
		disputeTxnModel.setOrgFuncCode(disputeTxnModel.getOrgFuncCode() + " - "
				+ functionCodeCache
						.getFunctionCodeDetails(disputeTxnModel.getMti(), disputeTxnModel.getPcode().substring(0, 2),
								disputeTxnModel.getOrgFuncCode(), actionCodeCache
										.getActionCode(disputeTxnModel.getMti(), disputeTxnModel.getOrgFuncCode()))
						.getFunctionCodeDescription());
		disputeTxnModel.setRrn(disputeTxnModel.getAcqRefData() + " - " + disputeTxnModel.getRrn());
		if (disputeTxnModel.getOrgDateCapture() != null) {
			disputeTxnModel.setDateTimeLocal(disputeTxnModel.getOrgDateCapture().atStartOfDay()
					.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
		}
		String maskedPanPrefix = StringUtils.rightPad(disputeTxnModel.getPanPrefix(), 12, "*");
		String panSuf = StringUtils.isNotBlank(disputeTxnModel.getPanSuffix()) ? disputeTxnModel.getPanSuffix() : "N/A";
		disputeTxnModel.setEncryptedPan(maskedPanPrefix + panSuf);

	}

}
