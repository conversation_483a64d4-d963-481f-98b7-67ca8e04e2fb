package org.npci.settlenxt.adminportal.service;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.FieldConstants;
import org.npci.settlenxt.adminportal.dto.FeeDTO;
import org.npci.settlenxt.adminportal.dto.FeeMajorMinorPriorityDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.FeeRepository;
import org.npci.settlenxt.portal.common.dto.BaseFeeMajorMinorPriorityDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.FeeRateConfigDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.npci.settlenxt.portal.common.util.BaseFunctionalityEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Transactional(rollbackFor = Throwable.class)
@Service
public class FeeServiceImpl implements FeeService {

	@Autowired
	FeeRepository feeRateRepository;

	@Autowired
	SessionDTO sessionDTO;

	private static final String ACTIVE = "Active";
	private static final String MINOR = "MINOR";

	@Override
	public List<FeeDTO> getApprovedFeeRateList() {
		return feeRateRepository.getApprovedFeeRateList(CommonConstants.FEE_TYPE,
				CommonConstants.REQUEST_STATE_APPROVED);
	}

	@Override
	public List<FeeDTO> getPendingForAppovalFeeRateList() {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(CommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(CommonConstants.REQUEST_STATE_REJECTED);
		return feeRateRepository.getPendingForApprovalFeeRateList(CommonConstants.FEE_TYPE, requestStateList);

	}

	@Override
	public FeeDTO getFee(int feeId) {

		FeeDTO feeRateDto = feeRateRepository.getFeesDetails(feeId, CommonConstants.TXN_CURR_CODE,
				CommonConstants.FEE_TYPE);

		if (feeRateDto != null) {
			return feeRateDto;
		}
		return null;
	}

	@Override
	public FeeDTO getFeeMain(int feeId) {

		FeeDTO feeRateDto = feeRateRepository.getFeesDetailsMain(feeId, CommonConstants.TXN_CURR_CODE,
				CommonConstants.FEE_TYPE);

		if (feeRateDto != null) {
			return feeRateDto;
		}
		return null;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public FeeDTO updateApproveOrRejectFeeRate(int feeId, String status, String remarks) throws SettleNxtException {

		if (feeId == 0) {
			throw new SettleNxtException("Fees id should not be empty", "");
		}

		FeeDTO feeRateDto = getFeeRateStgInfo(feeId);
		prepareFeeRateApproval(status, remarks, feeRateDto);

		return feeRateDto;
	}

	public FeeDTO getFeeRateStgInfo(int feeId) {

		FeeDTO feeRateDto = feeRateRepository.getFeeRateStgInfoByFeeId(feeId, CommonConstants.TXN_CURR_CODE,
				CommonConstants.FEE_TYPE);
		if (feeRateDto == null) {
			throw new SettleNxtException("No Data Exist", "");
		}

		return feeRateDto;

	}

	private void updateApprovedFee(FeeDTO feeRateDto) {
		FeeDTO feeRateDtoDb = feeRateRepository.getFeeRateInfoByFeeId(feeRateDto.getFeeId());
		if (ObjectUtils.isEmpty(feeRateDtoDb)) {
			LocalDateTime lt = LocalDateTime.now();
			feeRateDto.setCreatedOn(lt);
			feeRateRepository.saveFee(feeRateDto);
		} else {
			feeRateRepository.updateFee(feeRateDto);
		}

		feeRateDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
	}

	@Override
	public FeeDTO discardFeeRate(int feeId) throws SettleNxtException {
		FeeDTO feeRateDto = feeRateRepository.getFeeRate(CommonConstants.FEE_TYPE, feeId);
		FeeDTO feeRateDtoMain = feeRateRepository.getFeeRateMain(feeId);
		if (feeRateDtoMain != null) {
			feeRateDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			feeRateDtoMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			feeRateRepository.updateFeeRate(feeRateDtoMain);
			return feeRateRepository.getFeeRate(CommonConstants.FEE_TYPE, feeId);
		} else {
			feeRateRepository.deleteDiscardedEntry(feeId);
			return feeRateDto;
		}

	}

	@Override
	public List<FeeDTO> getFeeCodeList() {
		return feeRateRepository.getFeeCodeList(CommonConstants.FEE_TYPE);
	}

	@Override
	public List<FeeDTO> getGstCodeList() {
		return feeRateRepository.getGstCodeList(CommonConstants.GST_SYS_TYPE);
	}

	@Override
	public List<FeeDTO> getTxnCurrencyList() {
		return feeRateRepository.getTxnCurrency(CommonConstants.TXN_CURR_CODE);
	}

	@Override
	public List<FeeDTO> getPartyTypeList() {
		return feeRateRepository.getPartyTypeList(CommonConstants.PARTY_TYPE_CODE);
	}

	@Override
	public long addFeeRate(FeeDTO feeRateDto) {

		if (feeRateDto.getMultiplier() == null) {
			feeRateDto.setMultiplier(BigDecimal.ZERO);
		}
		feeRateDto.setFeeId(feeRateRepository.fetchIdFromFeeRateSequence());
		feeRateDto.setCreatedBy(sessionDTO.getUserName());
		feeRateDto.setCreatedOn(LocalDateTime.now());
		feeRateDto.setLastUpdatedBy(null);
		feeRateDto.setLastUpdatedOn(null);
		feeRateDto.setStatus(feeRateDto.getStatus());
		feeRateDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		feeRateDto.setLastOperation(CommonConstants.LAST_OPERATION_ADD);

		long count = feeRateRepository.addfee(feeRateDto);
		feeRateDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
		return count;
	}

	@Override
	public boolean checkDistinctFeecode(int feeCode) {
		FeeDTO feeRateDto = new FeeDTO();
		feeRateDto.setFeeCode(String.valueOf(feeCode));
		feeRateDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		int i = feeRateRepository.checkDistinctFeecode(feeRateDto);
		int j = feeRateRepository.checkDistinctFeecodeStg(feeRateDto);

		return i > 0 || j > 0;

	}

	@Override
	public FeeDTO getEditFeeRate(int feeId) {
		return feeRateRepository.getEditFeeRate(feeId);
	}

	@Override
	public long updateEditFeeRate(FeeDTO feeRateDto) {
		feeRateDto.setLastUpdatedBy(sessionDTO.getUserName());
		feeRateDto.setLastUpdatedOn(LocalDateTime.now());
		feeRateDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		feeRateDto.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
		return feeRateRepository.updateEditFeeRate(feeRateDto);

	}

	@Override
	public List<FeeDTO> getMajorMinorList(String significance) {
		String requestState = CommonConstants.REQUEST_STATE_APPROVED;
		if (CommonConstants.MAJOR_TYPE.equalsIgnoreCase(significance)) {
			List<String> requestStateList = new ArrayList<>();
			requestStateList.add(CommonConstants.REQUEST_STATE_APPROVED);
			requestStateList.add(CommonConstants.REQUEST_STATE_SAVED);
			return feeRateRepository.getMajorList(requestStateList);
		} else
			{
			return feeRateRepository.getMinorList(requestState, significance);
			}
	}

	@Override
	public List<FeeDTO> getPendingFeeConfig(String significance) {

		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(CommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(CommonConstants.REQUEST_STATE_REJECTED);

		if (CommonConstants.MAJOR_TYPE.equalsIgnoreCase(significance)) {
			
				return feeRateRepository.getPendingMajorList(requestStateList);
			
		} else
			{
			return feeRateRepository.getPendingMinorList(requestStateList, significance);
			}
	}

	@Override
	public void addFeeMajorMinor(FeeDTO feeDto) throws SettleNxtException {

		feeDto.setCreatedBy(sessionDTO.getUserName());
		feeDto.setCreatedOn(LocalDateTime.now());
		feeDto.setLastUpdatedBy("");
		feeDto.setLastUpdatedOn(null);
		feeDto.setStatus(CommonConstants.USER_STATUS_ACTIVE);
		feeDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

		if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MAJOR_TYPE)) {

			feeDto.setLastOperation(BaseFunctionalityEnum.ADD_FEE_MAJOR.name());
			feeDto.setRequestId(feeRateRepository.fetchIdFromFeeMasterSequence());
			feeDto.setFeeConfigId(feeDto.getFeeMajorId());
			List<FeeRateConfigDTO> feeConfigList = prepareFeeConfigList(feeDto);
			feeDto.setFeeConfigDto(feeConfigList);
			feeRateRepository.addFeeMasterStg(feeDto);
			if (CollectionUtils.isNotEmpty(feeConfigList)) {
				JSONArray jsonFeeConfig = prepareFeeConfigJson(feeConfigList);

				feeDto.setFeeConfigs(jsonFeeConfig.toString());

				for (FeeRateConfigDTO feeConfig : feeConfigList) {
					feeRateRepository.addFeeConfigStg(feeConfig);
				}
			}
			feeRateRepository.addFeeMajorStg(feeDto);
		} else if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MINOR_TYPE)) {
			feeDto.setLastOperation(BaseFunctionalityEnum.ADD_FEE_MINOR.name());
			feeDto.setRequestId(feeRateRepository.fetchIdFromFeeMasterSequence());
			feeDto.setFeeConfigId(feeDto.getFeeMinorId());
			List<FeeRateConfigDTO> feeConfigList = prepareFeeConfigList(feeDto);
			feeRateRepository.addFeeMasterStg(feeDto);
			feeDto.setFeeConfigDto(feeConfigList);
			if (CollectionUtils.isNotEmpty(feeConfigList)) {
				for (FeeRateConfigDTO feeConfig : feeConfigList) {
					feeRateRepository.addFeeConfigStg(feeConfig);
				}
			}
		}
		feeDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

	}

	@Override
	public FeeDTO getEditFeeMajoRConfigInfo(FeeDTO feeDto) {

		if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MAJOR_TYPE)) {
			List<FeeRateConfigDTO> feeMajorConfigList = feeRateRepository
					.getFeeMajorConfigStgInfoByMajorId(feeDto.getFeeMajorId(), CommonConstants.MAJOR_TYPE);

			feeDto.setFeeConfigDto(feeMajorConfigList);
		} else if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MINOR_TYPE)) {

			List<FeeRateConfigDTO> feeMinorConfigList = feeRateRepository
					.getFeeMajorConfigStgInfoByMinorId(feeDto.getFeeMinorId(), CommonConstants.MINOR_TYPE);
			feeDto.setFeeConfigDto(feeMinorConfigList);

		}

		return feeDto;
	}

	@Override
	public FeeDTO getEditFeeMajorMinor(FeeDTO feeDto) {
		FeeDTO feeDTO = new FeeDTO();
		if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MAJOR_TYPE)) {
			feeDto.setFeeMajorId(String.valueOf(feeDto.getFeeConfigId()));
			feeDTO = feeRateRepository.getEditFeeMajor(feeDto);
			List<FeeRateConfigDTO> feeMajorConfigList = feeRateRepository
					.getFeeMajorConfigStgInfoByMajorId(feeDTO.getFeeMajorId(), CommonConstants.MAJOR_TYPE);
			feeDTO.setFeeConfigDto(feeMajorConfigList);

		} else if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MINOR_TYPE)) {
			feeDto.setFeeMinorId(String.valueOf(feeDto.getFeeConfigId()));
			List<FeeRateConfigDTO> feeMinorConfigList = feeRateRepository.getEditFeeMinor(feeDto);
			feeDTO.setFeeMinorId(String.valueOf(feeDto.getFeeConfigId()));
			feeDTO.setFeeConfigId(String.valueOf(feeDto.getFeeConfigId()));
			feeDTO.setRequestState(feeMinorConfigList.get(0).getRequestState());
			feeDTO.setSignificance(feeMinorConfigList.get(0).getSignificance());
			feeDTO.setFeeConfigDto(feeMinorConfigList);
		}
		return feeDTO;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public FeeDTO updateApproveOrRejectFeeRateBulk(String feeIdList, String status, String remarks) throws SettleNxtException {
		String[] feeIdArray = feeIdList.split("\\|");
		FeeDTO feerate = new FeeDTO();

		List<Integer> feeIdlist = Arrays.stream(feeIdArray).map(Integer::valueOf).toList();
		List<FeeDTO> feeRateDtoArr = feeRateRepository.getFeeRateStgInfoList(feeIdlist, CommonConstants.TXN_CURR_CODE,
				CommonConstants.FEE_TYPE);
		Map<Integer, List<FeeDTO>> feeMap = feeRateDtoArr.stream().collect(Collectors.groupingBy(FeeDTO::getFeeId));

		for (String feeId:feeIdArray) {

			try {
				List<FeeDTO> feeDto = feeMap.get(Integer.parseInt(feeId));
				FeeDTO feeRateDto = feeDto.get(0);
				if (feeRateDto == null) {
					throw new SettleNxtException("Exception occurred with FeeId" + feeId, "");
				} else {
					prepareFeeRateApproval(status, remarks, feeRateDto);
					if (feeRateDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
						feerate.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
					}
				}

			} catch (Exception ex) {
				throw new SettleNxtException("Exception for FeeId" + feeId, "", ex);

			}
		}

		return feerate;
	}

	private void prepareFeeRateApproval(String status, String remarks, FeeDTO feeRateDto) {
		feeRateDto.setRequestState(status);
		feeRateDto.setCheckerComments(remarks);
		LocalDateTime lt = LocalDateTime.now();
		feeRateDto.setLastUpdatedOn(lt);
		feeRateDto.setLastUpdatedBy(sessionDTO.getUserName());

		if (feeRateDto.getRequestState().equals(CommonConstants.REQUEST_STATE_REJECTED)) {
			feeRateDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
		}
		if (feeRateDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
			feeRateDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			updateApprovedFee(feeRateDto);
			feeRateDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
		}

		feeRateRepository.updateFeeStgState(feeRateDto.getLastUpdatedBy(), feeRateDto.getLastUpdatedOn(),
				feeRateDto.getRequestState(), feeRateDto.getFeeId(), remarks,
				feeRateDto.getCheckerComments(), feeRateDto.getLastOperation());
	}

	@Override
	public void updateFeeMajorMinor(FeeDTO feeDto) throws SettleNxtException {

		feeDto.setCreatedBy(sessionDTO.getUserName());
		feeDto.setCreatedOn(LocalDateTime.now());
		feeDto.setLastUpdatedBy(sessionDTO.getUserName());
		feeDto.setLastUpdatedOn(LocalDateTime.now());
		feeDto.setStatus(CommonConstants.USER_STATUS_ACTIVE);
		feeDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MINOR_TYPE)) {
			feeDto.setLastOperation(BaseFunctionalityEnum.EDIT_FEE_MINOR.name());
			feeDto.setFeeConfigId(feeDto.getFeeMinorId());
			List<FeeRateConfigDTO> feeConfigList = prepareFeeConfigList(feeDto);
			feeDto.setFeeConfigDto(feeConfigList);
			feeRateRepository.updateFeeMasterStgStateMinor(feeDto.getLastUpdatedBy(), feeDto.getLastUpdatedOn(),
					feeDto.getRequestState(), feeDto.getFeeMinorId(), BaseFunctionalityEnum.EDIT_FEE_MINOR.name(),
					feeDto.getOldFeeMinorId());

			if (CollectionUtils.isNotEmpty(feeConfigList)) {
				if (StringUtils.isBlank(feeDto.getOldFeeMinorId())) {
					feeRateRepository.deleteDiscardedMinorEntry(feeDto.getFeeMinorId());

				} else {
					feeRateRepository.deleteDiscardedMinorEntry(feeDto.getOldFeeMinorId());

				}

				for (FeeRateConfigDTO feeConfig : feeConfigList) {
					feeRateRepository.addFeeConfigStg(feeConfig);
				}
			}
		} else if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MAJOR_TYPE)) {
			updateFeeMajor(feeDto);

		}

	}

	private void updateFeeMajor(FeeDTO feeDto) {

		List<FeeRateConfigDTO> feeConfigList = prepareFeeConfigList(feeDto);
		feeDto.setFeeConfigDto(feeConfigList);
		if (CollectionUtils.isNotEmpty(feeConfigList)) {
			feeRateRepository.updateFeeMasterStgStateMajor(feeDto.getLastUpdatedBy(), feeDto.getLastUpdatedOn(),
					feeDto.getRequestState(), feeDto.getFeeMajorId(), BaseFunctionalityEnum.MAP_FEE_MAJOR_CONFIG.name(),
					feeDto.getOldFeeMajorId());
			if (StringUtils.isBlank(feeDto.getOldFeeMajorId())) {
				feeRateRepository.deleteDiscardedFeeConfigEntry(feeDto.getFeeMajorId());

			} else {
				feeRateRepository.deleteDiscardedFeeConfigEntry(feeDto.getOldFeeMajorId());

			}
			JSONArray jsonFeeConfig = prepareFeeConfigJson(feeConfigList);
			feeDto.setFeeConfigs(jsonFeeConfig.toString());
			feeRateRepository.updateEditFeeMajorStg(feeDto);

			for (FeeRateConfigDTO feeConfig : feeConfigList) {
				feeRateRepository.addFeeConfigStg(feeConfig);
			}
		}
	}

	@Override
	public List<FeeMajorMinorPriorityDTO> getFeeIdList(String feeTypeCode) {
		return feeRateRepository.getFeeIdList(feeTypeCode);
	}

	@Override
	public List<FeeMajorMinorPriorityDTO> getFeeMinorIdList(String feeMajorId) {
		return feeRateRepository.getMinorIdList(feeMajorId);
	}

	@Override
	public void addFeeConfig(BaseFeeMajorMinorPriorityDTO feeMajorMinor, String reqType) throws SettleNxtException {
		FeeMajorMinorPriorityDTO requestinfo;

		feeMajorMinor.setStatus("A");
		feeMajorMinor.setFeeConfigId(feeMajorMinor.getFeeMajorId());
		feeMajorMinor.setCreatedBy(sessionDTO.getUserName());
		feeMajorMinor.setCreatedOn(LocalDateTime.now());
		feeMajorMinor.setLastUpdatedBy(sessionDTO.getUserName());
		feeMajorMinor.setLastUpdatedOn(LocalDateTime.now());
		feeMajorMinor.setLastOperation(BaseFunctionalityEnum.MAP_FEE_MAJOR_CONFIG.name());
		if (CommonConstants.REQUEST_STATE_SUBMITTED.equalsIgnoreCase(reqType)) {
			feeMajorMinor.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

		} else {
			feeMajorMinor.setRequestState(CommonConstants.REQUEST_STATE_SAVED);
		}

		Map<Integer, Long> priortiyMap = new HashMap<>();
		if (!StringUtils.isBlank(feeMajorMinor.getSortPriorArr())
				|| !StringUtils.isEmpty(feeMajorMinor.getSortPriorArr())) {
			feeMajorMinor.setAddFeeConfigList(prioritySettingFeeMinorMapping(feeMajorMinor, priortiyMap));
		}

		else {
			feeMajorMinor.setAddFeeConfigList(prioritySettingFeeMinorMappingWithoutPriorSorting(feeMajorMinor));
		}
		if (feeMajorMinor.getAddFeeConfigList() != null) {
			feeMajorMinor.setAddFeeConfigList(prepareFeeConfigList(feeMajorMinor, reqType));
		}

		feeRateRepository.updateFeeMasterStgStateMajor2(feeMajorMinor.getLastUpdatedBy(),
				feeMajorMinor.getLastUpdatedOn(), feeMajorMinor.getRequestState(), feeMajorMinor.getFeeMajorId(),
				BaseFunctionalityEnum.MAP_FEE_MAJOR_CONFIG.name());

		if (CommonConstants.REQUEST_STATE_SUBMITTED.equalsIgnoreCase(reqType)) {
			feeMajorMinor.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
			requestinfo = feeRateRepository.getRequestIdInFeeMasterStg(feeMajorMinor.getFeeMajorId(),
					CommonConstants.REQUEST_STATE_SUBMITTED, BaseFunctionalityEnum.MAP_FEE_MAJOR_CONFIG.name());
		} else {
			feeMajorMinor.setRequestState(CommonConstants.REQUEST_STATE_SAVED);
			requestinfo = feeRateRepository.getRequestIdInFeeMasterStg(feeMajorMinor.getFeeMajorId(),
					CommonConstants.REQUEST_STATE_SAVED, BaseFunctionalityEnum.MAP_FEE_MAJOR_CONFIG.name());
		}

		feeRateRepository.deleteFeeMinorPriorityStg(feeMajorMinor.getFeeMajorId());
		if (feeMajorMinor.getAddFeeConfigList() != null) {
			feeMajorMinor.getAddFeeConfigList().forEach(feeConfigDto -> {
				if (feeConfigDto.getFeeMinorId() != null) {
					feeConfigDto.setRequestId(requestinfo.getRequestId());

					feeRateRepository.addFeeConfig(feeConfigDto);

				}
			});
		}
		feeMajorMinor.setStatus(CommonConstants.TRANSACT_SUCCESS);
	}

	private List<BaseFeeMajorMinorPriorityDTO> prioritySettingFeeMinorMapping(
			BaseFeeMajorMinorPriorityDTO feeMajorMinor, Map<Integer, Long> priortiyMap) {
		String[] tableIdArray = feeMajorMinor.getSortPriorArr().split("\\,");

		long priority = 10;

		int[] valueArray = new int[tableIdArray.length];
		if (0 != tableIdArray.length) {

			for (int i = 0; i < tableIdArray.length; i++) {
				
				String regex=".*[a-z].*";
				Pattern pattern=Pattern.compile(regex);
				Matcher matcher=pattern.matcher(tableIdArray[i]);

				if (matcher.matches()) {

					int value = Integer.parseInt(tableIdArray[i].replaceAll("\\D", ""));
					valueArray[i] = value;
					priortiyMap.put(value, priority);
					priority += 10;
				}
			}

			feeMajorMinor.setAddFeeConfigList(priorityFeeMajorMinorMapping(priortiyMap, valueArray, feeMajorMinor));

		}

		return feeMajorMinor.getAddFeeConfigList();
	}

	private List<BaseFeeMajorMinorPriorityDTO> priorityFeeMajorMinorMapping(Map<Integer, Long> priortiyMap,
			int[] valueArray, BaseFeeMajorMinorPriorityDTO feeMajorMinor) {

		for (int val:valueArray) {

			if (feeMajorMinor.getAddFeeConfigList().get(val).getFeeMinorId() != null) {
				if (null != priortiyMap && !priortiyMap.isEmpty()) {

					feeMajorMinor.getAddFeeConfigList().get(val).setPriority(priortiyMap.get(val));

				} else {
					feeMajorMinor.setAddFeeConfigList(prioritySettingFeeMinorMappingWithPriorSorting(feeMajorMinor));
					break;
				}
			}
		}
		return feeMajorMinor.getAddFeeConfigList();
	}

	private List<BaseFeeMajorMinorPriorityDTO> prioritySettingFeeMinorMappingWithPriorSorting(
			BaseFeeMajorMinorPriorityDTO feeMajorMinor) {

		int k = 10;
		int size = feeMajorMinor.getAddFeeConfigList().size();
		if (size > 0) {
			for (int j = 0; j < size; j++) {
				if (feeMajorMinor.getAddFeeConfigList().get(j).getFeeMinorId() != null) {
					feeMajorMinor.getAddFeeConfigList().get(j).setPriority(k);
					k = k + 10;
				}

			}
		}
		return feeMajorMinor.getAddFeeConfigList();
	}

	private List<BaseFeeMajorMinorPriorityDTO> prepareFeeConfigList(BaseFeeMajorMinorPriorityDTO feeMajorMinor,
			String reqType) {
		feeMajorMinor.getAddFeeConfigList().forEach(feeConfigDtoLists -> {

			if (feeConfigDtoLists.getFeeMinorId() != null) {
				feeConfigDtoLists.setCreatedBy(sessionDTO.getUserName());
				feeConfigDtoLists.setFeeMajorId(feeMajorMinor.getFeeMajorId());
				feeConfigDtoLists.setCreatedOn(LocalDateTime.now());
				feeConfigDtoLists.setLastOperation(BaseFunctionalityEnum.MAP_FEE_MAJOR_CONFIG.name());
				if (feeConfigDtoLists.getStatus().equalsIgnoreCase(ACTIVE)
						|| feeConfigDtoLists.getStatus().equalsIgnoreCase("A")) {
					feeConfigDtoLists.setStatus("A");
				} else if (feeConfigDtoLists.getStatus().equalsIgnoreCase("InActive")
						|| feeConfigDtoLists.getStatus().equalsIgnoreCase("I")) {
					feeConfigDtoLists.setStatus("I");
				}
				feeConfigDtoLists.setLastUpdatedBy(sessionDTO.getUserName());
				feeConfigDtoLists.setLastUpdatedOn(LocalDateTime.now());
				feeConfigDtoLists.setCreatedOn(LocalDateTime.now());
				if (CommonConstants.REQUEST_STATE_SUBMITTED.equalsIgnoreCase(reqType)) {
					feeConfigDtoLists.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
				} else {
					feeConfigDtoLists.setRequestState(CommonConstants.REQUEST_STATE_SAVED);
				}
			}

		});

		return feeMajorMinor.getAddFeeConfigList();
	}

	private List<BaseFeeMajorMinorPriorityDTO> prioritySettingFeeMinorMappingWithoutPriorSorting(
			BaseFeeMajorMinorPriorityDTO feeMajorMinor) {

		if (feeMajorMinor.getAddFeeConfigList() != null) {
			int k = 10;

			for (int i = 0; i < feeMajorMinor.getAddFeeConfigList().size(); i++) {
				if (feeMajorMinor.getAddFeeConfigList().get(i).getFeeMinorId() != null) {
					feeMajorMinor.getAddFeeConfigList().get(i).setPriority(k);
					k = k + 10;
				}

			}
		}
		return feeMajorMinor.getAddFeeConfigList();
	}

	@Override
	public List<FeeMajorMinorPriorityDTO> getSavedFeeConfigByMajorId(String feeMajorId, String reqType) {
		return feeRateRepository.getSavedMinorListByMajorId(reqType, feeMajorId);
	}

	@Override
	public FeeMajorMinorPriorityDTO getEditFeeConfig(int majorId, int priority) {
		return feeRateRepository.getEditFeeConfig(majorId, priority);
	}

	@Override
	public boolean checkDistinctFeeMajorMinorId(String feeConfigId, String significance) {
		FeeDTO feeDto = new FeeDTO();
		feeDto.setFeeConfigId(feeConfigId);
		feeDto.setSignificance(significance);
		feeDto.setStatus(CommonConstants.USER_STATUS_ACTIVE);
		feeDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

		int i = 0;
		if (CommonConstants.MINOR_TYPE.equalsIgnoreCase(significance)) {
			i = feeRateRepository.checkDistinctFeeMajorMinor(feeDto);
		} else {
			i = feeRateRepository.checkDuplicateMajorID(feeDto);
		}
		int j = feeRateRepository.checkDistinctFeeMajorMinorStg(feeDto);

		return i > 0 || j > 0;

	}

	@Override
	public List<FeeMajorMinorPriorityDTO> getSavedFeeConfig() {
		return feeRateRepository.getSavedMinorList(CommonConstants.REQUEST_STATE_SAVED);
	}

	@Override
	public FeeDTO updateApproveOrRejectFeeMajor(String majorId, String status, String remarks) throws SettleNxtException {

		if (majorId == null) {
			throw new SettleNxtException("Fees major id should not be empty", "");
		}

		FeeDTO feeRateDto = getFeeMajorStgInfo(majorId);
		feeRateDto.setRequestState(status);
		feeRateDto.setCheckerComments(remarks);
		LocalDateTime lt = LocalDateTime.now();
		feeRateDto.setLastUpdatedOn(lt);
		feeRateDto.setLastUpdatedBy(sessionDTO.getUserName());

		if (feeRateDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
			// To update final table
			updateApprovedFeeMajor(feeRateDto);
		}
		feeRateRepository.updateFeeMasterStgMajor(feeRateDto.getLastUpdatedBy(), feeRateDto.getLastUpdatedOn(),
				feeRateDto.getRequestState(), feeRateDto.getFeeMajorId(), remarks, feeRateDto.getCheckerComments());

		return feeRateDto;

	}

	private FeeDTO getFeeMajorStgInfo(String majorId) {
		FeeDTO feeRateDto = feeRateRepository.getFeeMajorStgInfoByMajorId(majorId);
		if (feeRateDto == null) {
			throw new SettleNxtException("No Data Exist", "");
		}

		return feeRateDto;
	}

	private void updateApprovedFeeMajor(FeeDTO feeRateDto) {

		int count = feeRateRepository.checkFeeMajorAvilable(feeRateDto.getFeeMajorId(), "A");
		if (count == 0) {
			feeRateRepository.saveFeeMajor(feeRateDto);

			List<FeeMajorMinorPriorityDTO> feeMajorMappingList = feeRateRepository
					.getMinorPriorityStgInfo(feeRateDto.getFeeMajorId());
			if (CollectionUtils.isNotEmpty(feeMajorMappingList)) {
				for (FeeMajorMinorPriorityDTO feeDTO : feeMajorMappingList) {
					feeRateRepository.saveMinorPriority(feeDTO);
				}
			}
		} else {
			feeRateRepository.updateFeeMajor(feeRateDto);
			feeRateRepository.deleteFeeMinorPriority(feeRateDto.getFeeMajorId());

			List<FeeMajorMinorPriorityDTO> feeMajorMappingList = feeRateRepository
					.getMinorPriorityStgInfo(feeRateDto.getFeeMajorId());
			if (CollectionUtils.isNotEmpty(feeMajorMappingList)) {
				for (FeeMajorMinorPriorityDTO feeDTO : feeMajorMappingList) {
					feeRateRepository.saveMinorPriority(feeDTO);
				}
			}
		}

		feeRateDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

	}

	@Override
	public FeeDTO updateApproveOrRejectFeeMinor(String minorId, String status, String remarks) throws SettleNxtException {
		if (minorId == null) {
			throw new SettleNxtException("Fees minor id should not be empty", "");
		}

		List<FeeRateConfigDTO> feeConfigList = getFeeMinorStgInfo(minorId);
		FeeDTO feeRateDto = new FeeDTO();
		feeRateDto.setFeeMinorId(minorId);
		feeRateDto.setRequestState(status);
		feeRateDto.setCheckerComments(remarks);
		LocalDateTime lt = LocalDateTime.now();
		feeRateDto.setLastUpdatedOn(lt);
		feeRateDto.setLastUpdatedBy(sessionDTO.getUserName());

		if (feeRateDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
			updateApprovedFeeMinor(feeRateDto, feeConfigList);
		}
		feeRateRepository.updateFeeMasterStgMinor(feeRateDto.getLastUpdatedBy(), feeRateDto.getLastUpdatedOn(),
				feeRateDto.getRequestState(), feeRateDto.getFeeMinorId(), remarks, feeRateDto.getCheckerComments());
		return feeRateDto;
	}

	private void updateApprovedFeeMinor(FeeDTO feeRateDto, List<FeeRateConfigDTO> feeConfigList) {
		int count = feeRateRepository.checkFeeMinorAvilable(feeRateDto.getFeeMinorId(), "A", "N");
		if (count == 0) {
			for (FeeRateConfigDTO feeDto : feeConfigList) {
				feeRateRepository.saveFeeConfigMinor(feeDto);
			}
		} else {
			feeRateRepository.deleteMinorEntry(feeRateDto.getFeeMinorId());
			for (FeeRateConfigDTO feeDto : feeConfigList) {
				feeRateRepository.saveFeeConfigMinor(feeDto);
			}
		}

		feeRateDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

	}

	private List<FeeRateConfigDTO> getFeeMinorStgInfo(String minorId) {

		return feeRateRepository.getFeeMinorStgInfoByminorId(minorId, CommonConstants.MINOR_TYPE, MINOR);

	}

	@Override
	public FeeDTO discardFeeMinor(String minorId) throws SettleNxtException {

		FeeDTO feeRateDtoMain = new FeeDTO();
		List<FeeRateConfigDTO> feeRateConfigMainList = feeRateRepository.getFeeMinorInfoByMinorId(minorId);

		LocalDateTime lt = LocalDateTime.now();

		if (CollectionUtils.isNotEmpty(feeRateConfigMainList)) {
			feeRateDtoMain.setLastUpdatedOn(lt);
			feeRateDtoMain.setLastUpdatedBy(sessionDTO.getUserName());
			feeRateDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);

			feeRateRepository.updateFeeMasterStgMinor(feeRateDtoMain.getLastUpdatedBy(),
					feeRateDtoMain.getLastUpdatedOn(), feeRateDtoMain.getRequestState(), minorId, "", "");

			feeRateRepository.deleteDiscardedMinorEntry(minorId);

			for (FeeRateConfigDTO feeDto : feeRateConfigMainList) {
				feeRateRepository.addFeeConfigStg(feeDto);
			}
			return feeRateDtoMain;

		} else {
			feeRateDtoMain.setRequestState(CommonConstants.REQUEST_STATE_DISCARDED);

			feeRateRepository.updateFeeMasterStgMinor(feeRateDtoMain.getLastUpdatedBy(),
					feeRateDtoMain.getLastUpdatedOn(), feeRateDtoMain.getRequestState(), minorId, "", "");
			feeRateRepository.deleteDiscardedMinorEntry(minorId);
			return feeRateDtoMain;
		}

	}

	@Override
	public FeeDTO discardFeeMajor(String feeMajorId) throws SettleNxtException, ParseException {
		FeeDTO feeMajorDto = feeRateRepository.getFeeMajorStgInfoByMajorId(feeMajorId);
		FeeDTO feeMajorDtoMain = feeRateRepository.getFeeMajorInfoByMajorId(feeMajorId, CommonConstants.MAJOR_TYPE);
		LocalDateTime lt = LocalDateTime.now();
		List<FeeMajorMinorPriorityDTO> feeMinorPriorityStgDto = feeRateRepository.getFeeMinorPriorityStg(feeMajorId);

		FeeDTO feeMasterStgDto = feeRateRepository.getFeeMasterStg(feeMajorId);

		List<FeeMajorMinorPriorityDTO> feeMinorPriorityMain = feeRateRepository.getFeeMinorPriorityMain(feeMajorId);

		if (feeMinorPriorityMain != null) {

			feeMasterStgDto.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			feeMasterStgDto.setCreatedBy(sessionDTO.getUserName());
			feeMasterStgDto.setCreatedOn(LocalDateTime.now());
			feeMasterStgDto.setLastUpdatedOn(LocalDateTime.now());
			feeMasterStgDto.setStatus(CommonConstants.USER_STATUS_ACTIVE);
			feeMasterStgDto.setLastUpdatedBy(sessionDTO.getUserName());

			feeMasterStgDto.setLastOperation(BaseFunctionalityEnum.ADD_FEE_MAJOR.name());
			feeRateRepository.updateFeeMasterStg(feeMasterStgDto);

			feeRateRepository.deleteDiscardedFeeMinorPriorityStg(feeMajorId);

			feeMinorPriorityMain.forEach(feeConfigDto -> {

				feeConfigDto.setRequestId(feeMinorPriorityStgDto.get(0).getRequestId());

				feeRateRepository.updateFeeMinorPriorityStg(feeConfigDto);
			});

		} else {
			feeRateRepository.deleteDiscardedFeeMinorPriorityStg(feeMajorId);

		}
		if (feeMajorDtoMain != null) {
			feeMajorDtoMain.setLastUpdatedOn(lt);
			feeMajorDtoMain.setLastUpdatedBy(sessionDTO.getUserName());
			feeMajorDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			List<FeeRateConfigDTO> feeConfigList = prepareFeeConfigMajorFromJson(feeMajorDtoMain.getFeeMajorId());
			deleteFeeconfigMajorDiscard(feeMajorId, feeConfigList);

			feeMajorDtoMain.setFeeConfigDto(feeConfigList);

			if (CollectionUtils.isNotEmpty(feeConfigList)) {
				JSONArray jsonFeeConfig = prepareFeeConfigJson(feeConfigList);

				feeMajorDtoMain.setFeeConfigs(jsonFeeConfig.toString());

			}
			feeMajorDtoMain.setOldFeeMajorId(feeMajorDtoMain.getFeeMajorId());
			feeRateRepository.updateEditFeeMajorStg(feeMajorDtoMain);
			feeRateRepository.updateFeeMasterStgMajor(feeMajorDtoMain.getLastUpdatedBy(),
					feeMajorDtoMain.getLastUpdatedOn(), feeMajorDtoMain.getRequestState(),
					feeMajorDtoMain.getFeeMajorId(), "", "");

		} else {
			feeMajorDto.setRequestState(CommonConstants.REQUEST_STATE_DISCARDED);
			feeRateRepository.deleteDiscardedFeeConfigEntry(feeMajorId);
			feeRateRepository.deleteDiscardedMasterEntry(feeMajorId);
			feeRateRepository.deleteDiscardedEntryMajor(feeMajorId);
		}

		return feeMajorDto;
	}

	@Override
	public FeeDTO getFeeMajor(String majorId) {

		FeeDTO feeRateDto = feeRateRepository.getFeeMajorByMajorIdForView(majorId);
		if (feeRateDto != null) {
			return feeRateDto;
		}
		return null;
	}

	@Override
	public List<FeeRateConfigDTO> getFeeMinor(String minorId) {
		return feeRateRepository.getFeeMinorStgInfoByminorId(minorId, CommonConstants.MINOR_TYPE, MINOR);

	}

	private List<FeeRateConfigDTO> prepareFeeConfigList(FeeDTO feeDto) {
		FeeRateConfigDTO feeConfig;
		List<FeeRateConfigDTO> feeConfigList = new ArrayList<>();

		try {
			if (!ObjectUtils.isEmpty(feeDto.getFeeConfigs())) {

				String[] fuctionality = URLDecoder.decode(feeDto.getFeeConfigs(), "UTF-8").split("\\#");

				for (String funct:fuctionality) {
					feeConfig = new FeeRateConfigDTO();
					String[] splittedString = funct.split("\\|");

					feeConfig.setFieldName(splittedString[0]);
					feeConfig.setRelationalOperator(splittedString[1]);
					feeConfig.setStatus(splittedString[2]);
					feeConfig.setFieldValue(splittedString[3]);
					feeConfig.setCreatedBy(sessionDTO.getUserName());
					feeConfig.setCreatedOn(LocalDateTime.now());
					feeConfig.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
					feeConfig.setSignificance(feeDto.getSignificance());
					feeConfig.setFeeConfigId(feeDto.getFeeConfigId());
					if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MAJOR_TYPE)) {
						feeConfig.setFeeConfigId(feeDto.getFeeMajorId());
					} else if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MINOR_TYPE)) {
						feeConfig.setFeeConfigId(feeDto.getFeeMinorId());
						feeDto.setFeeConfigId(feeDto.getFeeMinorId());
					}

					feeConfigList.add(feeConfig);
				}
			}
		} catch (Exception ex) {
			throw new SettleNxtException("Please enter Fee config details", "Please enter Fee config details", ex);
		}
		return feeConfigList;
	}

	@Override
	public List<FeeRateConfigDTO> getFeeMajorConfig(String majorId) {
		return feeRateRepository.getFeeMajorConfigStgInfoByMajorId(majorId, CommonConstants.MAJOR_TYPE);
	}

	@Override
	public List<FeeDTO> getMinorInfo(String[] minorIdsArr) {
		String significance = "N";

		List<String> minorIdList = Arrays.asList(minorIdsArr);

		return feeRateRepository.getMinorInfo(significance, CommonConstants.REQUEST_STATE_APPROVED, minorIdList);
	}

	@Override
	public List<CodeValueDTO> getUnmappedFieldNameData(String feeConfigId) {
		return feeRateRepository.getUnmappedFieldNameData(CommonConstants.FIELD_EX, feeConfigId);
	}

	@Override
	public boolean removeMajorMinorMapping(String feeMinorId, String feeMajorId) {

		FeeMajorMinorPriorityDTO feeDto = new FeeMajorMinorPriorityDTO();
		feeDto.setFeeMajorId(feeMajorId);
		feeDto.setFeeMinorId(feeMinorId);
		feeDto.setStatus(CommonConstants.REMOVE_MINOR_MAPPING);

		feeRateRepository.removeMajorMinorMapping(feeDto);
		return true;
	}

	@SuppressWarnings("unchecked")
	public JSONArray prepareFeeConfigJson(List<FeeRateConfigDTO> feeConfigList) {
		JSONArray jArray = new JSONArray();
		for (FeeRateConfigDTO feeConfig : feeConfigList) {
			JSONObject jObj = new JSONObject();

			jObj.put(FieldConstants.FIELD_NAME, feeConfig.getFieldName());
			jObj.put(FieldConstants.RELATIONAL_OPERATOR, feeConfig.getRelationalOperator());
			jObj.put(FieldConstants.FIELD_VALUE, feeConfig.getFieldValue());
			jArray.add(jObj);
		}

		return jArray;
	}

	@Override
	public Pair<List<String>, String> checkDuplicatePriority(int priority, String majorId, String cardType) {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(CommonConstants.REQUEST_STATE_SUBMITTED);
		List<String> feeId = new ArrayList<>();
		int i = feeRateRepository.checkDuplicatePriority(priority, majorId, cardType);
		int j = feeRateRepository.checkDuplicatePriorityStg(priority, requestStateList, majorId, cardType);
		if (i > 0 || j > 0) {
			requestStateList.add(BaseCommonConstants.REQUEST_STATE_APPROVED);
			feeId = feeRateRepository.getFeeMajorId(priority, requestStateList, majorId);
			return Pair.of(feeId, "true");
		} else {
			return Pair.of(feeId, "false");
		}

	}

	@Override
	public List<FeeRateConfigDTO> prepareFeeConfigMajorFromJson(String feeMajorId) throws SettleNxtException, ParseException {
		List<FeeRateConfigDTO> feeConfigList = new ArrayList<>();
		String str = feeRateRepository.fetchFeeMajorJson(feeMajorId, CommonConstants.USER_STATUS_ACTIVE);
		
		JSONArray json = (JSONArray) new JSONParser().parse(str);

		int size = json.size();
		if (size > 0) {
			for (int i = 0; i < size; i++) {
				JSONObject explrObject = (JSONObject) json.get(i);
				// get field value from JSONObject using get() method
				FeeRateConfigDTO fees = new FeeRateConfigDTO();
				fees.setSignificance(CommonConstants.MAJOR_TYPE);
				fees.setStatus(CommonConstants.USER_STATUS_ACTIVE);
				fees.setFeeConfigId(feeMajorId);
				fees.setFieldName(explrObject.get(FieldConstants.FIELD_NAME).toString());
				fees.setRelationalOperator(explrObject.get(FieldConstants.RELATIONAL_OPERATOR).toString());
				fees.setFieldValue(explrObject.get(FieldConstants.FIELD_VALUE).toString());
				feeConfigList.add(fees);
			}
		}
		return feeConfigList;

	}

	public void deleteFeeconfigMajorDiscard(String feeMajorId, List<FeeRateConfigDTO> feeConfigList) {
		feeRateRepository.deleteDiscardedFeeConfigEntry(feeMajorId);
		for (FeeRateConfigDTO config : feeConfigList) {
			feeRateRepository.addFeeConfigStg(config);
		}
	}

	@Override
	public boolean checkFeeMajorAvailable(String feeMajorId) {
		int count = feeRateRepository.checkFeeMajorAvilable(feeMajorId, "A");
		return count == 0;
	}

	@Override
	public boolean checkFeeMinorAvailable(String feeMinorId) {
		int count = feeRateRepository.checkFeeMinorAvilable(feeMinorId, "A", "N");

		return count == 0;
	}

	@Override
	public void updateStgStatus(String feeMajorId) {
		LocalDateTime lt = LocalDateTime.now();

		feeRateRepository.updateFeeMasterStgStateMajor2(sessionDTO.getUserName(), lt, "I", feeMajorId,
				BaseFunctionalityEnum.MAP_FEE_MAJOR_CONFIG.name());

	}

	@Override
	public List<FeeRateConfigDTO> getFeeMinorForView(String minorId) {
		return feeRateRepository.getFeeMinorByminorIdForView(minorId, CommonConstants.MINOR_TYPE, MINOR);

	}

	@Override
	public FeeDTO getFeeMajorForView(String majorId) {

		FeeDTO feeRateDto = feeRateRepository.getFeeMajorStgByMajorIdForView(majorId);
		if (feeRateDto != null) {
			return feeRateDto;
		}
		return null;
	}

	@Override
	public FeeDTO updateApproveOrRejectFeeMajorBulk(String feeMajorIdList, String status, String remarks)
			throws SettleNxtException {

		String[] feeMajorIdArray = feeMajorIdList.split("\\|");
		FeeDTO feeMajorDto = new FeeDTO();

		List<String> list = Arrays.asList(feeMajorIdArray);

		List<FeeDTO> feeConfigMajorarr = getFeeMajorStgBulkInfo(list);

		Map<String, List<FeeDTO>> userMap = feeConfigMajorarr.stream()
				.collect(Collectors.groupingBy(FeeDTO::getFeeMajorId));

		for (String feeMajorId:feeMajorIdArray) {

			try {

				List<FeeDTO> userdto = userMap.get(feeMajorId);
				FeeDTO feeRateDto = userdto.get(0);

				if (feeRateDto == null) {
					throw new SettleNxtException("Fees major id should not be empty", "");
				} else {
					feeRateDto.setRequestState(status);
					feeRateDto.setCheckerComments(remarks);
					LocalDateTime lt = LocalDateTime.now();
					feeRateDto.setLastUpdatedOn(lt);
					feeRateDto.setLastUpdatedBy(sessionDTO.getUserName());

					if (feeRateDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
						// To update final table
						updateApprovedFeeMajor(feeRateDto);
						feeRateDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
						feeMajorDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
					}
					feeRateRepository.updateFeeMasterStgMajor(feeRateDto.getLastUpdatedBy(),
							feeRateDto.getLastUpdatedOn(), feeRateDto.getRequestState(), feeRateDto.getFeeMajorId(),
							remarks, feeRateDto.getCheckerComments());
				}
			} catch (Exception ex) {
				throw new SettleNxtException("Exception for feeId" + feeMajorId, "", ex);
			}
		}

		return feeMajorDto;

	}

	private List<FeeDTO> getFeeMajorStgBulkInfo(List<String> list) {
		return feeRateRepository.getFeeMajorStgInfoByMajorIdList(list);
	}

	@Override
	public FeeDTO updateApproveOrRejectFeeMinorBulk(String minorIdList, String status, String remarks)
			throws SettleNxtException {

		String[] feeMinorIdArray = minorIdList.split("\\|");
		FeeDTO feeMinorDto = new FeeDTO();

		List<String> list = Arrays.asList(feeMinorIdArray);

		for (int i = 0; i < feeMinorIdArray.length; i++) {

			try {
				FeeDTO feeRateDto = new FeeDTO();

				List<FeeRateConfigDTO> feeConfigList = getFeeMinorStgInfo(list.get(i));
				feeRateDto.setFeeMinorId(feeMinorIdArray[i]);
				feeRateDto.setRequestState(status);
				feeRateDto.setCheckerComments(remarks);
				LocalDateTime lt = LocalDateTime.now();
				feeRateDto.setLastUpdatedOn(lt);
				feeRateDto.setLastUpdatedBy(sessionDTO.getUserName());

				if (feeRateDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
					updateApprovedFeeMinor(feeRateDto, feeConfigList);
					feeRateDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
					feeMinorDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
				}
				feeRateRepository.updateFeeMasterStgMinor(feeRateDto.getLastUpdatedBy(), feeRateDto.getLastUpdatedOn(),
						feeRateDto.getRequestState(), feeRateDto.getFeeMinorId(), remarks,
						feeRateDto.getCheckerComments());
			}

			catch (Exception ex) {
				throw new SettleNxtException("Exception for feeId" + feeMinorIdArray[i], "", ex);
			}
		}
		return feeMinorDto;
	}

	@Override
	public List<FeeMajorMinorPriorityDTO> getFeeMinorPriorityMain(String feeMajorId) {
		return feeRateRepository.getFeeMinorPriorityMain(feeMajorId);
	}

	@Override
	public List<FeeMajorMinorPriorityDTO> getFeeDescriptionForMinor() {

		return feeRateRepository.getFeeDescriptionForMinor();
	}

	public List<FeeMajorMinorPriorityDTO> prepareFeeDescriptionForMinorPrior(
			List<FeeMajorMinorPriorityDTO> savedFeeConfigList) {

		List<FeeMajorMinorPriorityDTO> getFeeDescriptionForMinor = getFeeDescriptionForMinor();
		Map<String, String> minorDescMap = new HashMap<>();
		for (FeeMajorMinorPriorityDTO i : getFeeDescriptionForMinor)
			{
			minorDescMap.put(String.valueOf(i.getFeeId()), i.getFeeDesc());
			}
		for (FeeMajorMinorPriorityDTO x : savedFeeConfigList) {
			if (x.getStatus().equalsIgnoreCase("A") || x.getStatus().equalsIgnoreCase(ACTIVE)) {
				x.setStatus(ACTIVE);
			} else {
				x.setStatus("InActive");
			}
			setFeeDescForMinorPrior(minorDescMap, x);

		}

		return savedFeeConfigList;
	}

	private void setFeeDescForMinorPrior(Map<String, String> minorDescMap, FeeMajorMinorPriorityDTO x) {
		if (x.getInterchangeFeeID() != 999) {
			x.setInterchangeFeeIDDesc(minorDescMap.get(String.valueOf(x.getInterchangeFeeID())));
		} else {
			x.setInterchangeFeeIDDesc("999");
		}

		if (!x.getAcqAssessmentFeeID().equalsIgnoreCase("999")) {
			x.setAcqAssessmentFeeID(minorDescMap.get(x.getAcqAssessmentFeeID()));
		} else {
			x.setAcqAssessmentFeeID("999");
		}
		if (!x.getAcqAuthFeeID().equalsIgnoreCase("999")) {
			x.setAcqAuthFeeID(minorDescMap.get(x.getAcqAuthFeeID()));
		} else {
			x.setAcqAuthFeeID("999");
		}
		if (!x.getAcqProcessingFeeID().equalsIgnoreCase("999")) {
			x.setAcqProcessingFeeID(minorDescMap.get(x.getAcqProcessingFeeID()));
		} else {
			x.setAcqProcessingFeeID("999");
		}
		if (!x.getIssAssessmentFeeID().equalsIgnoreCase("999")) {
			x.setIssAssessmentFeeID(minorDescMap.get(x.getIssAssessmentFeeID()));
		} else {
			x.setIssAssessmentFeeID("999");
		}
		if (!x.getIssAuthFeeID().equalsIgnoreCase("999")) {
			x.setIssAuthFeeID(minorDescMap.get(x.getIssAuthFeeID()));
		} else {
			x.setIssAuthFeeID("999");
		}
		if (!x.getIssProcessingFeeID().equalsIgnoreCase("999")) {
			x.setIssProcessingFeeID(minorDescMap.get(x.getIssProcessingFeeID()));
		} else {
			x.setIssProcessingFeeID("999");
		}
	}

	public List<CodeValueDTO> getDateActionList() {
		return feeRateRepository.getDateActionList();
	}

	@Override
	public boolean checkFeeRateAvailable(int feeId) {
		int count = feeRateRepository.checkFeeRateAvilable(feeId);

		return count == 0;
	}
}
