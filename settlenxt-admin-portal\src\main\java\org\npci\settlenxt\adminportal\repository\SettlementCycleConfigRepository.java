package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.SettlementCycleConfigDTO;
import org.npci.settlenxt.portal.common.repository.BaseSettlementCycleRepository;

@Mapper
public interface SettlementCycleConfigRepository extends BaseSettlementCycleRepository{

	List<SettlementCycleConfigDTO> getApprovedDataFromMain();

	List<SettlementCycleConfigDTO> getPendingSettlementFromStg();

	List<SettlementCycleConfigDTO> fetchSrNoList(List<Integer> srNoList);

	SettlementCycleConfigDTO getSettlementDetails(int srNo);

	void updateRequestStateInStg(SettlementCycleConfigDTO settlementCycleConfigDTO);

	SettlementCycleConfigDTO searchSrNoInMain(int parseInt);

	void updateSrNoInMain(SettlementCycleConfigDTO settlementCycleConfigDTO);

	void insertSrNoInMain(SettlementCycleConfigDTO settlementCycleConfigDTO);

	void editSettlement(SettlementCycleConfigDTO settlementCycleConfigDTO);

	void deleteSettlement(int parseInt);

	 SettlementCycleConfigDTO getSrNoInfoFromMain(int srNo);

	 List<CodeValueDTO> getLookupDataSorted(String data);

	 SettlementCycleConfigDTO getSrNoInfoFromStg(int i);

	 void insertSettlementCycleIntoStg(SettlementCycleConfigDTO settlementCycleConfigDTO);

	 int getSettlementCycleConfigSeqId();

	 int updateSettlementCycleStg(SettlementCycleConfigDTO settlementCycleConfigDTO);

	 int validateSettlementTimeEdit(SettlementCycleConfigDTO settlementCycleConfigDTO);

	 int validateSettlementTime(SettlementCycleConfigDTO settlementCycleConfigDTO);

	 List<SettlementCycleConfigDTO> getAllTimeIntervals(@Param("prodId") String prodId,
			@Param("cycleNum") String cycleNum);

}
