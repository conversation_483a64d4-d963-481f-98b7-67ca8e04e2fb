package org.npci.settlenxt.adminportal.repository;

import java.util.List;
import java.util.Optional;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.portal.common.dto.MccDTO;

@Mapper
public interface MccRepository {

	List<MccDTO> getMccList();
	List<MccDTO> getMccListMain();
	List<MccDTO> getPendingMccConfigs();
	void addMccConfig(MccDTO mccDTO);
	Integer fetchIdFromMccIdSequence();
	MccDTO getMccConfigById(Integer mccId);
	MccDTO getMccConfigByIdMain(Integer mccId);
	void updateMccConfig(MccDTO mccDTO);
	void updateMccConfigEdit(MccDTO mccDTO);
	void updateMccMain(MccDTO mccDTO);
	MccDTO getPendingMccConfigById(Integer mccId);

	void updateMccStgState(MccDTO mccDTO);

	MccDTO getApprovedMccConfigById(Integer mccId);
	long saveMccConfig(MccDTO mccDTO);
	MccDTO getMccStgData(Integer mccId);
	MccDTO getMccMainData(Integer mccId);
	void deleteDiscardedMccEntry(Integer mccId);
	 int validateDuplicateCheck(MccDTO mccDTO);
	void updateMccConfigDiscard(MccDTO mccDTO);
	Optional<MccDTO> getByMccCode(String mccCode);
	void updateMccConfigForChargeType(MccDTO mccDto);
}
