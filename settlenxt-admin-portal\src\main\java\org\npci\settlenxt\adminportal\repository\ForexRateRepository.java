package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.adminportal.dto.ForexRateDTO;

@Mapper
public interface ForexRateRepository {
	List<ForexRateDTO> getForexRateList(ForexRateDTO forexRateDto);

	List<ForexRateDTO> getPendingForexRateList();

	ForexRateDTO getForexRateViewPage(int forexRateId);

	ForexRateDTO getForexRateStgInfoById(int forexRateId);

	void insertForexRateStg(ForexRateDTO forexRateDto);

	int fetchForexRateIdSequence();

	ForexRateDTO getForexRateStg(int forexRateId);

	void updateForexRate(ForexRateDTO forexRateDto);

	ForexRateDTO getForexRateMain(int forexRateId);

	void updateForexRateMain(ForexRateDTO forexRateDto);

	void insertForexRateMain(ForexRateDTO forexRateDto);

	void updateForexRateRequestState(ForexRateDTO forexRateDto);

	void updateForexRateDiscard(ForexRateDTO forexRateDto);

	void deleteForexRateDiscardedEntry(ForexRateDTO forexRateDto);

	List<ForexRateDTO> validateDuplicate(ForexRateDTO forexRateDto);

	void insertForexRateListStg(@Param("forexRates") List<ForexRateDTO> forexRates);

	String getLatestConversionRate(@Param("networkId") String networkId, @Param("fromCurrency") String fromCurrency,
			@Param("toCurrency") String toCurrency);

}