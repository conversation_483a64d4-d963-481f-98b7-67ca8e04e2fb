package org.npci.settlenxt.adminportal.controllers;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.ReportStatusDTO;
import org.npci.settlenxt.adminportal.service.ReportService;
import org.npci.settlenxt.adminportal.service.UserService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.BaseSessionDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.UserDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.security.BaseSecurityUtil;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.service.BaseUserService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.npci.settlenxt.portal.common.util.BaseErrorConstants;
import org.npci.settlenxt.portal.common.util.CredentialGenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.common.base.Strings;
import com.google.gson.JsonObject;

@Controller
public class HomeController extends BaseController {

	@Autowired
	private BaseUserService userService;
	@Autowired
	private UserService userSvc;
	@Autowired
	private ReportService reportService;
	@Autowired
	BaseLookupService lookupService;
	@Autowired
	private BaseSessionDTO sessionDTO;

	@Autowired
	private MessageSource messageSource;


	private static final String PROC_CODE_LIST = "prodCd";
	private static final String STATUS_DATA = "statusData";
	private static final String SHOW_REPORT_STATUS = "showReportStatus";
	private static final String STATUS = "status";
	private static final String PROCCODE = "prodcode";
	private static final String FLAG = "flag";
	private static final String CHECK = "check";
	private static final String CYCLE_NUMBERS = "cycleNumbers";
	private static final String HOME_FLAG = "home";
	private static final String REPORT_TYPE = "reportType";
	
	private static final String MAP = "map";
	private static final String SETTLENXT_HOME = "SettleNxtHome";
	private static final String FAILED = "failed";
	private static final String SUCCESS = "success";
	private static final String SETTLENXT_INTER_LOGIN = "interSettlenxtLoginPage";
	private static final String SETTLENXTUIIDLETIME = "settlenxt.ui.idleTime.passwordPrompt.interval";

	private static final String SEPARATOR = "-";
	private static final String APPEND_YEAR = "-20";
	private static final String RPY = "RPY";
	private static final String CYCLE_DATES = "cycleDates";
	private static final String SESSIONTIMEOUT="Session timed out";

	@GetMapping("/settleNxtHome")	
	public String viewHome(Model model, String viewName) {

		UserDTO historyDto = userService.getPswdHistory(sessionDTO.getUserId());

		if (null != historyDto && (!StringUtils.isBlank(historyDto.getStatusCode()))
				&& historyDto.getPwdAlertDays() <= BaseCommonConstants.ALERT_PERIOD && (historyDto.getPwdAlertDays() != -1)) {
			
				Locale locale=Locale.ROOT;
				String x = "Password will expire in " + historyDto.getPwdAlertDays() + " days";
				if (0 == historyDto.getPwdAlertDays()) {
					x = "Password will expire tommorrow";
				}

				x = x.replace("-", "");
				model.addAttribute(BaseCommonConstants.ERROR_STATUS,messageSource.getMessage("yyyy", null, x, locale));
			

		}

		model.addAttribute(BaseCommonConstants.IDLE_TIME_INTERVAL, env.getProperty(SETTLENXTUIIDLETIME));
		model.addAttribute(BaseCommonConstants.IDLE_TIME_PWD_INTERVAL,
				env.getProperty(SETTLENXTUIIDLETIME));
		long sessionTimePeriod = NumberUtils.toLong(env.getProperty("server.servlet.session.timeout"));
		sessionTimePeriod = sessionTimePeriod == 0L ? 1500 : sessionTimePeriod;
		int sessionTime = (int) (sessionTimePeriod / 60);
		model.addAttribute(BaseCommonConstants.SESSION_TIMEOUT_INTERVAL, sessionTime);

		List<String> prodCdList = reportService.getProductId();
		model.addAttribute(PROC_CODE_LIST, prodCdList);
		ReportStatusDTO reportStatusDTO = reportService.getCycleInfo(RPY);
		StringBuilder stringBuilder = new StringBuilder(reportStatusDTO.getCycleDate());
		stringBuilder.insert(2, SEPARATOR);
		stringBuilder.insert(5, APPEND_YEAR);
		reportStatusDTO.setCycleDate(stringBuilder.toString());
		
		if (userSvc.checkIfDashBoardFuncExists("View Dashboard", sessionDTO.getUserId())) {
			getDataBasedDataOnProdCode(RPY,reportStatusDTO.getCycleDate(),reportStatusDTO.getCycleNumber(), model);
			model.addAttribute(HOME_FLAG,"");
		} else {
			model.addAttribute(HOME_FLAG, CommonConstants.YES);
		}
		return getView(model, SETTLENXT_HOME);
	}

	@PostMapping("/getDataBasedOnProdCode")
	@PreAuthorize("hasAuthority('View Dashboard')")
	public String getDataBasedDataOnProdCode(
			@RequestParam(name = "productCode", defaultValue = "RPY") String productCode,@RequestParam(value= "cycleDate", required = false) String cycleDate,@RequestParam(value= "cycleNumber", required = false) String cycleNumber, Model model) {
		List<String> prodCdList = reportService.getProductId();
		
		ReportStatusDTO reportStatusDTO = new ReportStatusDTO();
	model.addAttribute(CYCLE_NUMBERS, cycleNumber);
	model.addAttribute(CYCLE_DATES,cycleDate);
	if(cycleNumber == null || cycleDate == null) {
		reportStatusDTO = reportService.getCycleInfo(productCode);
		model.addAttribute(CYCLE_NUMBERS, reportStatusDTO.getCycleNumber());
		
		StringBuilder stringBuilder = new StringBuilder(reportStatusDTO.getCycleDate());
		stringBuilder.insert(2, SEPARATOR);
		stringBuilder.insert(5, APPEND_YEAR);
		reportStatusDTO.setCycleDate(stringBuilder.toString());
		model.addAttribute(CYCLE_DATES,reportStatusDTO.getCycleDate());
		String[] splitDate = reportStatusDTO.getCycleDate().split(SEPARATOR);
		 String year = splitDate[2];
			year = year.substring(2);
			String date = splitDate[0] + splitDate[1] + year;
			reportStatusDTO.setCycleDate(date);
			reportStatusDTO.setCycleNumber(reportStatusDTO.getCycleNumber());
	}
	else {
		String[]  splitDate = cycleDate.split(SEPARATOR);
	 String year = splitDate[2];
		year = year.substring(2);
		String date = splitDate[0] + splitDate[1] + year;
		reportStatusDTO.setCycleDate(date);
		reportStatusDTO.setCycleNumber(cycleNumber);
	}
	
	model.addAttribute(CommonConstants.SETTLEMENT_CYCLE_LIST_NO, reportService.getSettlementCycleList());
		model.addAttribute(PROC_CODE_LIST, prodCdList);
		model.addAttribute(PROCCODE, productCode);
		model.addAttribute(FLAG, CommonConstants.YES);
	List<ReportStatusDTO> pair = reportService.getStatusCount(productCode, reportStatusDTO.getCycleDate(),reportStatusDTO.getCycleNumber());
		
		if (pair.isEmpty()) {
			Locale locale = Locale.ROOT;
			model.addAttribute(CommonConstants.ERROR_STATUS, messageSource.getMessage("report.noData", null, locale));
			model.addAttribute(CHECK, CommonConstants.YES);
		} else {
			Map<String, String> statusMap = pair.stream()
					.collect(Collectors.toMap(ReportStatusDTO::getStatus, ReportStatusDTO::getCount));
			model.addAttribute(MAP, statusMap);
			model.addAttribute(CHECK, CommonConstants.NO);
		}
		return getView(model, SETTLENXT_HOME);
	}

	@PostMapping("/settlenxtHome")
	public String getSettlenxtHome(Model model) {
		model.addAttribute(HOME_FLAG, CommonConstants.YES);
		return viewHome(model, "");
	}

	@GetMapping("/interSettlenxtLogin")
	public String getSettlenxtLogin(Model model) {
		return getView(model, SETTLENXT_INTER_LOGIN);
	}

	@PostMapping("/getDataBasedOnStatusAndProcCode")
	public String getDataBasedOnStatusAndProdCode(@RequestParam("productCode") String productCode,
			@RequestParam("label") String label,
			@RequestParam(value= "cycleDate", required = false) String cycleDate,
			@RequestParam(value= "cycleNumber", required = false) String cycleNumber,Model model) {

		ReportStatusDTO reportStatusDTO = new ReportStatusDTO();
		reportStatusDTO.setProductCode(productCode);
		reportStatusDTO.setStatus(label);
		List<CodeValueDTO> status1 = lookupService.getLookupData(CommonConstants.REPORTSTATUS);
		List<String> prodCdList = reportService.getProductId();
		model.addAttribute(STATUS, status1);
		model.addAttribute(PROC_CODE_LIST, prodCdList);
		String[]  splitDate = cycleDate.split(SEPARATOR);
		 String year = splitDate[2];
			year = year.substring(2);
			String date = splitDate[0] + splitDate[1] + year;
			reportStatusDTO.setCycleDate(date);
			reportStatusDTO.setCycleNumber(cycleNumber);
		List<ReportStatusDTO> statusData = reportService.getStatusDataForProd(reportStatusDTO);
		if (!CollectionUtils.isEmpty(statusData)) {
			if (StringUtils.isNotEmpty(statusData.get(0).getCycleDate())
					&& StringUtils.isNotEmpty(statusData.get(0).getCycleNumber())) {

				StringBuilder stringBuilder = new StringBuilder(statusData.get(0).getCycleDate());
				stringBuilder.insert(2, SEPARATOR);
				stringBuilder.insert(5, APPEND_YEAR);
				reportStatusDTO.setCycleDate(stringBuilder.toString());
				reportStatusDTO.setCycleNumber(statusData.get(0).getCycleNumber());
				model.addAttribute(CYCLE_NUMBERS, statusData.get(0).getCycleNumber());
			}
			model.addAttribute(STATUS_DATA, statusData);
		}
		model.addAttribute(FLAG, CommonConstants.YES);
		model.addAttribute(CommonConstants.SETTLEMENT_CYCLE_LIST_NO, reportService.getSettlementCycleList());
		List<String> reportType1 = reportService.getSettlementFileType();
		model.addAttribute(REPORT_TYPE, reportType1);
		model.addAttribute(CommonConstants.REPORT_STATUS_DTO, reportStatusDTO);
		return getView(model, SHOW_REPORT_STATUS);
	}

	@GetMapping("/ping")
	public ResponseEntity<Object> ping(HttpSession session, Model model,
			@RequestParam("idleTimePeriod") String idleTimePeriod, HttpServletResponse response) { // idleTimePeriod
																									// in
																									// min

		JsonObject jsonResponse = new JsonObject();
		long sessionTimePeriod = NumberUtils.toLong(env.getProperty("server.servlet.session.timeout"));
		sessionTimePeriod = sessionTimePeriod == 0L ? 1500 : sessionTimePeriod;
		int sessionTime = (int) (sessionTimePeriod / 60);
		
		
		

		if (idleTimePeriod != null) {

			int idletime = Integer.parseInt(idleTimePeriod);
			int idleTimePeriod1 = NumberUtils.toInt(env.getProperty("settlenxt.ui.idleTime.interval"));
			int idleTimePeriod2 = NumberUtils.toInt(env.getProperty(SETTLENXTUIIDLETIME));
			if (idletime >= idleTimePeriod1 && idletime < idleTimePeriod2) {
				jsonResponse.addProperty("idleTimePropmt", env.getProperty("settlenxt.ui.idleTime.interval"));
				jsonResponse.addProperty(STATUS, "idleTimePropmt");
				return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
			} else if (idletime >= idleTimePeriod2 && idletime < sessionTime) {
				jsonResponse.addProperty("sessionTimeout",sessionTime);
				jsonResponse.addProperty("idleTimePasswordPropmt", env.getProperty(SETTLENXTUIIDLETIME));
				jsonResponse.addProperty(STATUS, "idleTimePasswordPropmt");
				return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
			} else {

				if (sessionDTO.getLastRefershedTime().plusSeconds(sessionTimePeriod).isBefore(LocalDateTime.now())) {

					session.invalidate();
					jsonResponse.addProperty(SESSIONTIMEOUT, SESSIONTIMEOUT);
					return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.UNAUTHORIZED);
				}
			}

		}
		if ("0".equals(idleTimePeriod)
				&& sessionDTO.getLastRefershedTime().plusSeconds(sessionTimePeriod).isBefore(LocalDateTime.now())) {

			session.invalidate();
			jsonResponse.addProperty(SESSIONTIMEOUT, SESSIONTIMEOUT);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.UNAUTHORIZED);

		}
		jsonResponse.addProperty(STATUS, SUCCESS);
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}
	
	 //Decoding for password
	  private static String decode(String data) {
		  StringBuilder bld=new StringBuilder();
	        String decrytpedPd = "";
	        for (int i = 0; i < data.length(); i = i + 3) {
	            if (data.charAt(i) == '0') {
	            	bld.append(Character.toString((char) Integer.parseInt(data.substring(i + 1, i + 3))));
	            } else {
	            	bld.append(Character.toString((char) Integer.parseInt(data.substring(i, i + 3))));
	            }
	        }
	        decrytpedPd=bld.toString();
	        return decrytpedPd;
	    }


	@PostMapping("/verifyPasswordForSessionResume")
	public ResponseEntity<Object> verifyPassword(HttpSession session, Model model, @RequestParam("pswd") String pswd,
			HttpServletResponse response) {

		JsonObject jsonResponse = new JsonObject();

		if (!Strings.isNullOrEmpty(pswd)) {
			String decodedCred = CredentialGenUtil.decode(pswd);
			UserDTO user = userService.getLoggedInUser();
			String saltKey = env.getProperty(BaseCommonConstants.SALT_KEY_FOR_HASHING);
			String hash1 = null;
			String hash2 = null;
			hash1 = user.getPasswd();
			try {

				BaseSecurityUtil encoder2 = BaseSecurityUtil.getInstance();
				hash2 = encoder2.encode(decodedCred, saltKey);

			} catch (Exception ex) {
				throw new SettleNxtException("Error verifying Password For Session Resume", "", ex);
			}

			if (hash1.equalsIgnoreCase(hash2)) {

				jsonResponse.addProperty(STATUS, SUCCESS);
				return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
			} else {

				jsonResponse.addProperty(STATUS, FAILED);
				return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
			}
		} else {

			jsonResponse.addProperty(STATUS, FAILED);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

		}
	}

	@GetMapping("/refreshSession")
	public ResponseEntity<String> refreshSession(HttpSession session) {
		refreshSession();
		return new ResponseEntity<>("resumeActiviySuccess", HttpStatus.OK);

	}

	@GetMapping("/errorMapping")
	public String mappingMethodCheck(Model model, HttpServletRequest request, HttpSession session) {
		model.addAttribute(ERROR_STAT, getMessageFromBundle(BaseErrorConstants.UNAUTHORIZED_ERROR_CODE));
		return viewHome(model, "");
	}

}
