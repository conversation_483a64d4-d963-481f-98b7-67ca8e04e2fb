{"disputeConfList": [{"name": "arbitration", "title": "arbitration", "actionCode": null, "funcCode": "479", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "arbitrationAcceptance", "title": "arbitrationAcceptance", "actionCode": null, "funcCode": "480", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "arbitrationContinuation", "title": "arbitrationContinuation", "actionCode": null, "funcCode": "481", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": [], "label": "Document Indicator"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actionCode": null, "funcCode": "482", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "arbitrationVerdict", "title": "arbitrationVerdict", "actionCode": null, "funcCode": "483", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["transactionCycle"], "label": "Verdict Decision"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Verdict Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "retrievalRequest", "title": "retrievalRequest", "actionCode": null, "funcCode": "603", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code & Description"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "retrievalRequestFulfillment", "title": "retrievalRequestFulfillment", "actionCode": null, "funcCode": "605", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "retrievalRequestNonFullfillment", "title": "retrievalRequestNonFullfillment", "actionCode": null, "funcCode": "630", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code & Description"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "preComplianceRaise", "title": "preComplianceRaise", "actionCode": null, "funcCode": "672", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["cardAcptAddress", "cardAcptCity", "cardAcptRegion", "cardAcptCountry"], "label": "Card Acceptor Location/Address", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["cardAcptTermId"], "label": "Card Acceptor Terminal ID"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code & Description"}, {"defaultVal": "N/A", "attributeName": ["cardAcptCountry"], "label": "Card Acceptor Country Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptRegion"], "label": "Card Acceptor State Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptCity"], "label": "Card Acceptor City"}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["pcodeDesc"], "label": "Processing Code"}, {"defaultVal": "N/A", "attributeName": ["instIdAcq"], "label": "Acquirer Institution ID Code"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "preComplianceAcceptance", "title": "preComplianceAcceptance", "actionCode": null, "funcCode": "673|684", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}]}, {"name": "preComplianceDecline", "title": "preComplianceDecline", "actionCode": null, "funcCode": "674", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "complianceRaise", "title": "complianceRaise", "actionCode": null, "funcCode": "675", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["cardAcptAddress", "cardAcptCity", "cardAcptRegion", "cardAcptCountry"], "label": "Card Acceptor Location/Address", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["cardAcptTermId"], "label": "Card Acceptor Terminal ID"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code & Description"}, {"defaultVal": "N/A", "attributeName": ["cardAcptCountry"], "label": "Card Acceptor Country Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptRegion"], "label": "Card Acceptor State Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptCity"], "label": "Card Acceptor City"}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["pcodeDesc"], "label": "Processing Code"}, {"defaultVal": "N/A", "attributeName": ["instIdAcq"], "label": "Acquirer Institution ID Code"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "complianceAcceptance", "title": "complianceAcceptance", "actionCode": null, "funcCode": "676", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "complianceContinuation", "title": "complianceContinuation", "actionCode": null, "funcCode": "677", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "complianceWithdrawn", "title": "complianceWithdrawn", "actionCode": null, "funcCode": "678", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "complianceVerdict", "title": "complianceVerdict", "actionCode": null, "funcCode": "679", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["transactionCycle"], "label": "Verdict Decision"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Verdict Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}]}, {"name": "goodfaithRaise", "title": "goodfaithRaise", "actionCode": null, "funcCode": "680", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["pcodeDesc"], "label": "Processing Code"}, {"defaultVal": "N/A", "attributeName": ["acqRefData"], "label": "Acquirer Reference Data"}, {"defaultVal": "N/A", "attributeName": ["instIdAcq"], "label": "Acquirer Institution ID Code"}, {"defaultVal": "N/A", "attributeName": ["approvalCode"], "label": "Approval Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptTermId"], "label": "Card Acceptor Terminal ID"}, {"defaultVal": "N/A", "attributeName": ["txnOrgInstId", "txnOrgInstIdDesc"], "label": "Transaction Originator Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["txnDestInstId", "txnDestInstIdDesc"], "label": "Transaction Destination Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["cardAcptAddress", "cardAcptCity", "cardAcptRegion", "cardAcptCountry"], "label": "Card Acceptor Location/Address", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["cardAcptCountry"], "label": "Card Acceptor Country Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptRegion"], "label": "Card Acceptor State Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptCity"], "label": "Card Acceptor City"}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "goodfaithAcceptance", "title": "goodfaithAcceptance", "actionCode": null, "funcCode": "681|683", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "goodfaithDecline", "title": "goodfaithDecline", "actionCode": null, "funcCode": "682", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "memberFundCollect", "title": "memberFundCollect", "actionCode": null, "funcCode": "700|702", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["<PERSON><PERSON><PERSON>"], "label": "PAN/Token PAN"}, {"defaultVal": "N/A", "attributeName": ["acqRefData", "rrn"], "label": "Acquirer Reference Data/RRN", "separator": "/"}, {"defaultVal": "N/A", "attributeName": ["instIdAcq"], "label": "Acquirer Institution ID Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptTermId"], "label": "Card Acceptor Terminal ID"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCur", "tranCurDesc"], "label": "Currency Code, Transaction", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["txnOrgInstId", "txnOrgInstIdDesc"], "label": "Transaction Originator Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["txnDestInstId", "txnDestInstIdDesc"], "label": "Transaction Destination Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Amount, Settlement", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Raise User ID"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["pcodeDesc"], "label": "Processing Code"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code and Description"}]}, {"name": "memberFundDisburse", "title": "memberFundDisburse", "actionCode": null, "funcCode": "701|703", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["<PERSON><PERSON><PERSON>"], "label": "PAN/Token PAN"}, {"defaultVal": "N/A", "attributeName": ["acqRefData", "rrn"], "label": "Acquirer Reference Data/RRN", "separator": "/"}, {"defaultVal": "N/A", "attributeName": ["instIdAcq"], "label": "Acquirer Institution ID Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptTermId"], "label": "Card Acceptor Terminal ID"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCur", "tranCurDesc"], "label": "Currency Code, Transaction", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["txnOrgInstId", "txnOrgInstIdDesc"], "label": "Transaction Originator Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["txnDestInstId", "txnDestInstIdDesc"], "label": "Transaction Destination Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Amount, Settlement", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Raise User ID"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["pcodeDesc"], "label": "Processing Code"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code and Description"}]}, {"name": "npciFundCollect", "title": "npciFundCollect", "actionCode": null, "funcCode": "760", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["filingDispFee"], "label": "<PERSON><PERSON> Amount"}, {"defaultVal": "N/A", "attributeName": ["txnDestInstId", "txnDestInstIdDesc"], "label": "Transaction Destination Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["feeTypCode", "feeName"], "label": "Fee Name", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Raise User ID"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "npciFundDisburse", "title": "npciFundDisburse", "actionCode": null, "funcCode": "761", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["filingDispFee"], "label": "<PERSON><PERSON> Amount"}, {"defaultVal": "N/A", "attributeName": ["txnDestInstId", "txnDestInstIdDesc"], "label": "Transaction Destination Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Raise User ID"}, {"defaultVal": "N/A", "attributeName": ["feeTypCode", "feeName"], "label": "Fee Name", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}]}, {"name": "creditAdjustment", "title": "creditAdjustment", "actionCode": null, "funcCode": "762|401", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code & Description"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "debitAdjustment", "title": "debitAdjustment", "actionCode": null, "funcCode": "763", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code & Description"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "presentmentWithAuth", "title": "presentmentWithAuth", "actionCode": null, "funcCode": "200", "mti": "1240", "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["cardAcptPstCode"], "label": "Card Acceptor Zip Code"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Amount, Settlement", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["merchTelNo"], "label": "Merchant Telephone Number"}, {"defaultVal": "N/A", "attributeName": ["cardHldrUID"], "label": "Card Holder <PERSON>"}, {"defaultVal": "N/A", "attributeName": ["cardHldrInTaxPan"], "label": "Card Holder Income Tax PAN"}, {"defaultVal": "N/A", "attributeName": ["cardAcptAdnlAddress"], "label": "Card Acceptor Additional Address"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["latePresentmentIndicator"], "label": "Late Presentment Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["merchantCatInd"], "label": "Merchant Indicator", "valueMap": {"S": "Small Merchant", "L": "LIC Merchant", "D": "<PERSON><PERSON><PERSON>"}}]}, {"name": "void", "title": "void", "actionCode": null, "funcCode": "266", "mti": "8144", "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "representmentRaise", "title": "representmentRaise", "actionCode": null, "funcCode": "205", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "representmentAcceptance", "title": "representmentAcceptance", "actionCode": null, "funcCode": "261", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "refund", "title": "refund", "actionCode": null, "funcCode": "262", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code & Description"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount,Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["ctrlNo"], "label": "Control Number"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}]}, {"name": "refundChargeback", "title": "refundChargeback", "actionCode": null, "funcCode": "264", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code and Description"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["ctrlNo"], "label": "Control Number"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}]}, {"name": "presentmentReversal", "title": "presentmentReversal", "actionCode": null, "funcCode": "400|420", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}, {"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code & Description"}]}, {"name": "DmsSms", "title": "DmsSms", "actionCode": null, "funcCode": "260|430", "mti": "0210|0110", "attributes": [{"defaultVal": "N/A", "attributeName": ["<PERSON><PERSON><PERSON>"], "label": "PAN/Token PAN"}, {"defaultVal": "N/A", "attributeName": ["tstampLocalTransaction"], "label": "Date and Time, Local Transaction"}, {"defaultVal": "N/A", "attributeName": ["rrn"], "label": "RRN"}, {"defaultVal": "N/A", "attributeName": ["pcodeDesc"], "label": "Processing Code"}, {"defaultVal": "N/A", "attributeName": ["tranCur", "tranCurDesc"], "label": "Currency Code, Transaction", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["approvalCode"], "label": "Approval Code"}, {"defaultVal": "N/A", "attributeName": ["posEntryMode", "posEntryModeDesc"], "label": "POS Entry Mode", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["posCondCode", "posCondCodeDesc"], "label": "POS Condition Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["instIdAcq"], "label": "Acquirer Institution ID Code"}, {"defaultVal": "N/A", "attributeName": ["mcc", "cardAcptBsnsCode"], "label": "Card Acceptor Business Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["cardAcptTermId"], "label": "Card Acceptor Terminal ID"}, {"defaultVal": "N/A", "attributeName": ["cardAcptId"], "label": "Card Acceptor ID Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptAddress"], "label": "Card Acceptor Location/Address"}, {"defaultVal": "N/A", "attributeName": ["cardAcptRegion"], "label": "Card Acceptor State Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptPstCode"], "label": "Card Acceptor Zip Code"}, {"defaultVal": "N/A", "attributeName": ["cardAcptName"], "label": "Card Acceptor Name"}, {"defaultVal": "N/A", "attributeName": ["actionCodeDesc"], "label": "Action Code"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["acqInstCountryDesc"], "label": "Acquirer Country Code"}, {"defaultVal": "N/A", "attributeName": ["arqcAuthInd"], "label": "ARQC Authorization Indicator"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Amount, Settlement", "separator": " "}, {"defaultVal": "N/A", "attributeName": [], "label": "International Auth Network ID"}, {"defaultVal": "N/A", "attributeName": ["cardAcptCity"], "label": "Card Acceptor City"}, {"defaultVal": "N/A", "attributeName": ["fraudScore"], "label": "<PERSON><PERSON> Score"}, {"defaultVal": "N/A", "attributeName": ["cardHldrUID"], "label": "Card Holder <PERSON>"}, {"defaultVal": "N/A", "attributeName": [], "label": "Loyalty Point for Debit"}, {"defaultVal": "N/A", "attributeName": ["recPymtInd"], "label": "Recurring Payment Indicator"}, {"defaultVal": "N/A", "attributeName": ["icsResCode2"], "label": "ICS Result code 2"}, {"defaultVal": "N/A", "attributeName": ["persPhrsCode"], "label": "Personal Phrase Code"}, {"defaultVal": "N/A", "attributeName": ["serviceCode2"], "label": "Service Code Position 2"}, {"defaultVal": "N/A", "attributeName": [], "label": "ICS 2 Data from Authorization"}, {"defaultVal": "N/A", "attributeName": [], "label": "Transaction ID"}, {"defaultVal": "N/A", "attributeName": ["txnOrgInstId", "txnOrgInstIdDesc"], "label": "Transaction Originator Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["cardAcptAdnlAddress"], "label": "Card Acceptor Additional Address"}, {"defaultVal": "N/A", "attributeName": ["pinEntryCap"], "label": "PIN Entry Capability"}, {"defaultVal": "N/A", "attributeName": ["eciInd"], "label": "ECI Indicator"}, {"defaultVal": "N/A", "attributeName": ["cvvCvcResult"], "label": "CVD ICVD Match Result"}, {"defaultVal": "N/A", "attributeName": ["cardHldrInTaxPan"], "label": "Card Holder Income Tax PAN"}, {"defaultVal": "N/A", "attributeName": ["icsResCode1"], "label": "ICS Result Code 1"}, {"defaultVal": "N/A", "attributeName": ["productCodeDesc"], "label": "Product Code"}, {"defaultVal": "N/A", "attributeName": ["emiAmt"], "label": "EMI Amount"}, {"defaultVal": "N/A", "attributeName": ["stipInd"], "label": "STIP Indicator"}, {"defaultVal": "N/A", "attributeName": ["imgCode"], "label": "Image Code"}, {"defaultVal": "N/A", "attributeName": ["serviceCode1"], "label": "Service Code Position 1"}, {"defaultVal": "N/A", "attributeName": ["serviceCode3"], "label": "Service Code Position 3"}, {"defaultVal": "N/A", "attributeName": [], "label": "ICS 1 Data from Authorization"}, {"defaultVal": "N/A", "attributeName": [], "label": "Cross Border Flag"}, {"defaultVal": "N/A", "attributeName": ["loyaltyBal"], "label": "Loyalty Balance"}, {"defaultVal": "N/A", "attributeName": ["merchTelNo"], "label": "Merchant Telephone Number"}, {"defaultVal": "N/A", "attributeName": [], "label": "Customer Mobile / Tel. Number"}, {"defaultVal": "N/A", "attributeName": ["cvd2MtchRslt"], "label": "CVD2 Match Result"}, {"defaultVal": "N/A", "attributeName": ["txnDestInstId", "txnDestInstIdDesc"], "label": "Transaction Destination Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["merchantCatInd"], "label": "Small Merchant", "valueMap": {"S": "Small Merchant", "L": "LIC Merchant", "D": "<PERSON><PERSON><PERSON>"}}]}, {"name": "chargeBack", "title": "chargeBack", "actionCode": null, "funcCode": "450", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code & Description"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "chargeBackdeemedAcceptance", "title": "chargeBackdeemedAcceptance", "actionCode": null, "funcCode": "465", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}]}, {"name": "smsTipSurcharge", "title": "smsTipSurcharge", "actionCode": null, "funcCode": "265", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "authCancellationRequest", "title": "authCancellationRequest", "actionCode": null, "funcCode": "267", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "authCancellationAcceptance", "title": "authCancellationAcceptance", "actionCode": null, "funcCode": "268", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "cashBack", "title": "cashBack", "actionCode": null, "funcCode": "276", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Adjustment", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["txnDestInstId", "txnDestInstIdDesc"], "label": "Transaction Destination Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code and Description"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Raise User ID"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}]}, {"name": "cashBackRev", "title": "cashBackRev", "actionCode": null, "funcCode": "277", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Adjustment", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["txnDestInstId", "txnDestInstIdDesc"], "label": "Transaction Destination Institution ID Code", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code and Description"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Raise User ID"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}]}, {"name": "chargebackAcceptance", "title": "chargebackAcceptance", "actionCode": null, "funcCode": "470", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "preArbitration", "title": "preArbitration", "actionCode": null, "funcCode": "471", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["reasonCodeDesc"], "label": "Reason Code & Description"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["deadlineDate"], "label": "DeadLine Date"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "P": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "preArbitrationDecline", "title": "preArbitrationDecline", "actionCode": null, "funcCode": "473", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}, {"name": "preArbitrationAcceptance", "title": "preArbitrationAcceptance", "actionCode": null, "funcCode": "474|475", "mti": null, "attributes": [{"defaultVal": "N/A", "attributeName": ["memMsgTxt"], "label": "Member Message Text"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountTran"], "label": "Amount, Transaction", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "amountAdd"], "label": "Amounts, Additional", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["docInd"], "label": "Document Indicator", "valueMap": {"Y": "Yes", "N": "No"}}, {"defaultVal": "N/A", "attributeName": ["tstampLocal"], "label": "Raise Date and Time"}, {"defaultVal": "N/A", "attributeName": ["internalTrackNo"], "label": "Internal Tracking Number"}, {"defaultVal": "N/A", "attributeName": ["cardBillCur", "tranCurDesc"], "label": "Currency Code, Settlement", "separator": "-"}, {"defaultVal": "N/A", "attributeName": ["tranCurDesc", "settlementAmount"], "label": "Settlement Amount", "separator": " "}, {"defaultVal": "N/A", "attributeName": ["netReconDate"], "label": "RGCS Settlement Date"}, {"defaultVal": "N/A", "attributeName": ["partialInd"], "label": "Full / Partial Indicator", "valueMap": {"F": "Full", "N": "Partial"}}, {"defaultVal": "N/A", "attributeName": ["makerId"], "label": "Maker User Name"}, {"defaultVal": "N/A", "attributeName": ["checkerId"], "label": "Checker User Name"}]}]}