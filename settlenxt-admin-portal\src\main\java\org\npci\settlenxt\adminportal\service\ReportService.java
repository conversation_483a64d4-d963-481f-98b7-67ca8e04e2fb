package org.npci.settlenxt.adminportal.service;


import java.util.List;

import org.npci.settlenxt.adminportal.dto.ReportStatusDTO;
import org.npci.settlenxt.portal.common.dto.AuditBatchLogDTO;
import org.npci.settlenxt.portal.common.service.BaseReportService;

public interface ReportService extends BaseReportService {
	// Reports

	 List<AuditBatchLogDTO> reportGeneratedList(AuditBatchLogDTO auditBatchLogDTO);

	 List<AuditBatchLogDTO> settleNxtreportGeneratedList(AuditBatchLogDTO auditBatchLogDTO);

	 List<String> getSettlementFileType();

	 List<String> getProductId();

	 List<ReportStatusDTO> getReportStatusData(ReportStatusDTO reportStatusDto);

	 List<ReportStatusDTO> udpatestatus(List<ReportStatusDTO> statusData, int configureTime);

	 List<ReportStatusDTO> getStatusCount(String productCode, String cycleDate, String cycleNumber);

	 List<ReportStatusDTO> getStatusDataForProd(ReportStatusDTO reportStatusDTO);

	 List<String> getSettlementCycleListBasedOnProdCode(String productCode);

	 ReportStatusDTO getLatestCycleDN(ReportStatusDTO reportStatusDTO);
	 ReportStatusDTO getCycleInfo(String productCode);

}
