$(document).ready(function() {
	
	$('#uploadErrorMsg').hide();
	$('#uploadSuccessMsg').hide();
	$('#rejectFiles').hide();
	$("#errnetwork").hide();
	$("#errforexId").hide();
	
	$("#fromDate").datepicker({
		dateFormat: "yyyy-mm-dd",
		changeMonth: true,
		changeYear: true,
		maxDate: 0,
		//	minDate : 0,
		onSelect: function(_selectedDate) {
			
			var dt = new Date(selected);
			dt.setDate(dt.getDate() + 1);
			$("#toDate").datepicker("option", "minDate", dt);
		}
	}).datepicker("setDate", 'now');

	$("#toDate").datepicker({
		dateFormat: "yyyy-mm-dd",
		changeMonth: true,
		changeYear: true,
		maxDate: 0,
		onSelect: function(_selectedDate) {
			var dt = new Date(selected);
			dt.setDate(dt.getDate() - 1);
			$("#fromDate").datepicker("option", "maxDate", dt);
		}
	}).datepicker("setDate", 'now');

	var myTable = $("#tabnew").DataTable({
		initComplete: function() {
			var api = this.api();

			// For each column
			api
				.columns()
				.eq(0)
				.each(function(colIdx) {
					//If first column to be skipped to include the filter for the reasons line check box 
					if (!(colIdx == 0  && firstColumnToBeSkippedInFilterAndSort)) {
						// Set the header cell to contain the input element
						var cell = $('#tabnew thead tr th').eq(
							$(api.column(colIdx).header()).index()
						);
						var title = $(cell).text();
						handleInput(colIdx, cell, title, api);
					}
				});
			$('#tabnew_filter').hide();
			
		},
		columnDefs: [
			{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0}
		],
		"order": [],
		dom: 'lBfrtip',
		buttons: [

			{
				extend: 'excelHtml5',
				text: 'Export',
				filename: 'CurrencyFileUpload',
				header: 'false',
				title: null,
				sheetName: 'FileUpload',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			},
			{
				extend: 'csvHtml5',
				text: 'Export',
				filename: 'CurrencyFileUpload',
				header: 'false',
				title: null,
				sheetName: 'FileUpload',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			}

		],
		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true
	});

	$('#network').blur(function () {
        validateField('network', true, "SelectionBox",0,false);
    });	
    $('#forexId').blur(function () {
        validateField('forexId', true,"SelectionBox", 0, false);
    });
    

   $("#uploadId").click(function() {
    $("#uploadId").prop('disabled', true);
    var check = validatepage();
    var specialCharsCSV = /^[a-zA-z0-9\-_ ]*$/;
    if (check) {
        if ($("#fileArr").val() != '') {
            var net = null;
            var flag = false;
            var errorName = null;
            var totalNoRecord=null;
             var filePromises = [];

            for (var i of $("#fileArr").get(0).files) {
					var uplodedFile = i.name;
					var fileNameSplit = uplodedFile.split('.');
					//jcb net and cup
				
				if($("#network").val() =='JCB'){
					if(fileNameSplit[1].toUpperCase() != "TXT"){
					flag = true;
					errorName='networkError';	
					}
					else{
						filePromises.push(readFile(i)); 
					}
				}
				
				if($("#network").val() =='UPI'){
					if(!fileNameSplit[0].toUpperCase().startsWith("IFO")){
					flag = true;
					errorName='networkError';
					}
					else{
					filePromises.push(readFile(i)); 
					}
				}
				
					if(fileNameSplit.some(part => part.toUpperCase() === 'CSV') && !flag && !['JCB','UPI'].includes($("#network").val())){
						if(!specialCharsCSV.test(fileNameSplit[0])){
							flag = true;
							errorName='specialChar';
						} else {
                        
						if(!uplodedFile.toUpperCase().includes('DCI') && $("#network").val() =='DFS'){
							flag = true;
							errorName='networkError';
						}
						else{
							filePromises.push(readFile(i));
						}
                    }
                }
                
            }

            Promise.all(filePromises).then(results => {
                results.forEach(result => {
                    if (result.error) {
                        flag = true;
                        errorName = result.errorName;
                    } else {
                        net = result.net;
                        totalNoRecord=result.totalNoRecord;
                        if ($("#network").val() != net && !['JCB', 'DFS', 'UPI'].includes($("#network").val())) {
                            flag = true;
                            errorName = 'networkError';
                        }
                    }
                });
                validateAndHandleAjaxCall(flag, errorName,totalNoRecord);
            });
          }else {
            $("#errFile").text("Please select file");
            $("#uploadId").prop('disabled', false);
            $("#errnetwork").show();
            $("#errforexId").show();
        }
    } else {
        $("#uploadId").prop('disabled', false);
    }
});

function readFile(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const contents = e.target.result;
            const result = fileReader.parse(contents, {
                header: false,
                skipEmptyLines: true
            });

            if (result.data.length >= 2) {
                 const net = result.data[1][0];
                 const totalNoRecord = result.data.length; // Count the number of records
                resolve({ net: net, totalNoRecord: totalNoRecord });
            } else {
                resolve({ error: true, errorName: 'invalidFile' });
            }
        };
        reader.onerror = function() {
            reject(new Error('File reading failed'));
        };
        reader.readAsText(file);
    });
}
	
	$("#resetBtn").click(function() {
		$('#rejectFiles').text('');
		$('#rejectFiles').hide();
		$('#uploadErrorMsg').hide();
		$('#uploadSuccessMsg').hide();
		document.getElementById("fileArr").value = "";
		$("#uploadId").prop('disabled', false);
		$("#errFile").hide();
		$("#errnetwork").find('.error').html('');
    $("#errforexId").find('.error').html('');
	});

	$("#fileUploadResetBtn").click(function() {
		$("#fromDate").val("");
		$("#toDate").val("");
		document.getElementById("fileName").value = "";
		document.getElementById("status").value = "";
	});

	$("#fileUploadSearchBtn").click(function() {
		var fromDate = $('#fromDate').val();
		var toDate = $('#toDate').val();
		var fileName = $('#fileName').val();
		var statusSelect = $('#status').val();

		var url = "/searchCurrencyFileUploadList";

		if ($('#fromDate').val() === "") {
			$('#errFromDate').text('Please select From Date ');
			return false;
		} else {
			$('#errFromDate').text('');
		}
		if ($('#toDate').val() === "") {
			$('#errToDate').text('Please select To Date ');
			return false;
		} else {
			$('#errToDate').text('');
		}
		if (Date.parse(fromDate) > Date.parse(toDate)) {
			$('#errToDate').text('To date cannot be less than From date');
			return false;
		} else {
			$('#errToDate').text('');
		}
		fromDate = fromDate + ' 00:00:00';
		toDate = toDate + ' 23:59:59';
		var fromDateMoment = moment(fromDate, 'MM/DD/YYYY HH:mm:SS').format('YYYY-MM-DDTHH:mm:SS');
		var toDateMoment = moment(toDate, 'MM/DD/YYYY HH:mm:SS').format('YYYY-MM-DDTHH:mm:SS');
		
		var data = "fromDateStr,"
			+ fromDateMoment + ",toDateStr," + toDateMoment
			+ ",fileName," + fileName
			+ ",status," + statusSelect;
		postData(url, data);
	});

	var allPages = myTable.rows().nodes();

	$('#selectall').click(function() {
		
		if ($(this).hasClass('allChecked')) {
			$('.selectedId', allPages).prop('checked', false);
		} else {
			$('.selectedId', allPages).prop('checked', true);
		}
		$(this).toggleClass('allChecked');
	});

	$('.selectedId').change(function() {
		var check = ($('.selectedId').filter(":checked").length == $('.selectedId').length);
		$('#selectall').prop("checked", check);
	});


	$('#fileUploadStagehBtn').click(function() {
		let fileIds = "";
		if ($(".selectedId:checkbox:checked").length < 1) {
			$("#stageErr").text("Atleast select one file..!!")
			return;
		}

		$('.selectedId', allPages).filter(":checked").each(function(_i, _obj) {
			fileIds = fileIds + $(this).attr("documentId") + "-";
		});
		$('#stageFileIds').val(fileIds.slice(0, -1));
		
		let data = "stageFileIds," + $("#stageFileIds").val();
		
		// Post data 
		postData("/stageUploadedFilesCurrency", data);
		
	});

	$("#excelExport").on("click", function() {
		$(".buttons-excel").trigger("click");
	});

	$("#csvExport").on("click", function() {
		$(".buttons-csv").trigger("click");
	});
	
	$("#refreshId").on("click", function() {
		let data="isDBCallRequired,"
			+ true ;
		postData("/currencyFilesUploadView",  data);
	});

});

function handleSpecialCharacters(specialChars, fileNameSplit, flag) {
	var f=flag;
	for (var j of specialChars) {
		if (fileNameSplit[0].indexOf(j) > -1) {
			f = true;
		}
	}
	return f;
}

function validateAndHandleAjaxCall(flag,errorName,totalNoRecord) {
	if (flag) {
		if(errorName=='specialChar'){
		$('#errFile').text('Special characters are not allowed in file name');
		}
		if(errorName=='networkError'){
		$('#errFile').text('Network type is not same for the uploaded file');
		}
		if(errorName=='invalidFile'){
		$('#errFile').text('Invalid file uploaded.Check the file data');
		}
		$('#uploadErrorMsg').hide();
		$('#rejectFiles').hide();
		$('#uploadSuccessMsg').hide();
		$("#uploadId").prop('disabled', false);
	} 
	else {
		$('#errFile').text('');
		var network= $("#network").val();
		var forexId= $("#forexId").val();
		var url = getURL("/bulkFilesUploadCurrency");
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		var formData = new FormData();
		var fileLength = document.getElementById('fileArr').files.length;
		for (var x = 0; x < fileLength; x++) {
			formData.append("document", document.getElementById("fileArr").files[x]);
		}
		formData.append("network", network);
		formData.append("forexId", forexId);
		formData.append("totalNoRecord",totalNoRecord);
		formData.append("_TransactToken", tokenValue);
		$("#overlay").addClass("loadingdata");
		$.ajax({
			method: 'POST',
			url: url,
			cache: false,
			processData: false,
			contentType: false,
			data: formData,
			success: async function (response) {
				if (response === "Success") {
					$('#uploadErrorMsg').hide();
					$('#rejectFiles').hide();
					

					await sleep(5000);
					$("#overlay").removeClass("loadingdata");
					$('#uploadSuccessMsg').text('File validation completed. Please view the file status for further processing');
					$('#uploadSuccessMsg').show();
					await sleep(3000);
					let tkn = "_TransactToken," + tokenValue+",isDBCallRequired,"+true;
					//added this function for forwarding request to new url as common func(vtransact) not working properly
					postDataNew("/currencyFilesUploadView", tkn);
				} else if (response.length > 0 && !response.startsWith("ERROR")) {
					$('#rejectFiles').text('File already uploaded');
					$('#uploadErrorMsg').hide();
					$('#rejectFiles').show();
					$("#uploadId").prop('disabled', false);
					$("#overlay").removeClass("loadingdata");
				} else if (response.startsWith("ERROR")) {
					$('#uploadErrorMsg').text('Something went wrong. Please try again!');
					$('#rejectFiles').hide();
					$('#uploadErrorMsg').show();
					$("#uploadId").prop('disabled', false);
					$("#overlay").removeClass("loadingdata");
				}
			},
			error: function (_jqXHR, _textStatus, _errorThrown) {
				$('#uploadErrorMsg').text('Something went wrong. Please try again!');
				$('#uploadErrorMsg').show();
				$("#uploadId").prop('disabled', false);
				$("#overlay").removeClass("loadingdata");
			}
		});
	}
}

function handleInput(colIdx, cell, title, api) {
	if (colIdx < actionColumnIndex) {

		$(cell).html(title + '<br><input class="search-box"   type="text" />');

		// On every keypress in this input
		$(
			'input',
			$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
		)
			.off('keyup change')
			.on('change', function (_e) {
				// Get the search value
				$(this).attr('title', $(this).val());
				var regexr = '({search})';
				// Search the column for that value
				api
					.column(colIdx)
					.search(
						this.value != ''
							? regexr.replace('{search}', '(((' + this.value + ')))')
							: '',
						this.value != '',
						this.value == ''
					)
					.draw();
			})
			.on('click', function (e) {
				e.stopPropagation();
			})
			.on('keyup', function (e) {
				e.stopPropagation();

				$(this).trigger('change');
				if (cursorPosition && cursorPosition != null) {
					$(this)
						.focus()[0]
						.setSelectionRange(cursorPosition, cursorPosition);
				}
			});
	} else {
		$(cell).html(title + '<br> &nbsp;');
	}
}



function getContextPath() {
	return "${pageContext.request.contextPath}";
}

function isChecked(checkboxId) {
	var id = '#' + checkboxId;
	return $(id).is(":checked");
}

function validatepage() {
	var flag = true;
	
    if (!validateField('network', true, "SelectionBox",0,false) && flag) {
        flag = false;
    }
    if (!validateField('forexId', true,"SelectionBox", 0, false) && flag) {
        flag = false;
    }
	if ($('#fileArr').val().trim() == '') {
		$('#errFile').text('Please attach file');
		$('#errFile').show();
		flag = false;
	} else {
		$('#errFile').text('');
	}
	return flag;
}


function noBack() {
	window.history.forward();
}


function postDataNew(action, data) {
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	var linkurl = pathName + action;

	var form = document.createElement("form");
	form.method = "POST";

	var parameters = data.split(",");

	for (var i = 0; i < parameters.length; ++i) {
		var dynInput = document.createElement("input");
		dynInput.setAttribute("type", "hidden");
		dynInput.setAttribute("id", parameters[i]);
		dynInput.setAttribute("name", parameters[i]);
		++i;
		dynInput.setAttribute("value", parameters[i]);

		form.appendChild(dynInput);
	}
	document.body.appendChild(form); // added this	for firefox Browser
	encodeForm(form);	//Added by piyush for form encode

	form.action = linkurl;
	form.submit();
}

async function sleep(msec) {
    return new Promise(resolve => setTimeout(resolve, msec));
}

function currencyFileRejectView(fileId){
	let data="fileId,"+fileId;
	postData("/currencyFileRejectView", data);
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType != "SelectionBox")) || (isMandatory && fieldValue.trim() == "" && fieldType == "SelectionBox")) {
		isValid = false;
	}
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
        if(currencyFileUploadValidationMessages[fieldId]){
            $("#err" + fieldId).find('.error').html(currencyFileUploadValidationMessages[fieldId]);
        }
        $("#err" + fieldId).show();
    }
    return isValid;
}

function backToUpload(){
	let data="isDBCallRequired,"
			+ true ;
	postData("/currencyFilesUploadView", data);
}

function currencyFileDiscard(fileId,fileName){
	let data="fileId,"+fileId + ",fileName," + fileName;
	postData("/currencyFileDiscard", data);
}