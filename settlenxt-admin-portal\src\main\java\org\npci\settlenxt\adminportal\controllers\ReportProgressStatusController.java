package org.npci.settlenxt.adminportal.controllers;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.DateUtils;
import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.service.IReportProgressStatusService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;

import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;


/**
 * Controller used for 
 * <li> View Report progress status</li>
 * <AUTHOR>
 *
 */
@Slf4j
@Controller
public class ReportProgressStatusController extends BaseController {
	
	private static final Logger logger = LogManager.getLogger(ReportProgressStatusController.class);

	@Autowired
	IReportProgressStatusService reportProgressStatusService;
	public static final String SHOW_REPORT_PROJ_STATUS="showReportProgressStatus";
	public static final String CYCLE_MAN_DTO="cycleManagementDTO";

	/**
	 * This method is used for get the report status of summary reports
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/reportProgressStatus")
	public String reportProgressStatus(@ModelAttribute("cycleManagementDTO") CycleManagementDTO cycleManagementDTO, Model model) throws SettleNxtException {
		logger.info("ReportProgressStatusController : in");
		cycleManagementDTO = reportProgressStatusService.getReportProgressStatusList(cycleManagementDTO);
		model.addAttribute(CYCLE_MAN_DTO, cycleManagementDTO);
		String response = reportProgressStatusService.fetchProductIdDetails(cycleManagementDTO);
		JsonObject data = new JsonObject(response);
		@SuppressWarnings("unchecked")
		Map<String, String> productData = data.mapTo(Map.class);
		cycleManagementDTO.setSettleProduct(productData);
		logger.info("ReportProgressStatusController : out");
		return getView(model, SHOW_REPORT_PROJ_STATUS);
	}
	
	/**
	 * This method is used to fetch product id with respective cycle number
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/fetchProductIdDetails")
	public String fetchProductIdDetails(@ModelAttribute("cycleManagementDTO") CycleManagementDTO cycleManagementDTO, Model model) throws SettleNxtException {
		String response = reportProgressStatusService.fetchProductIdDetails(cycleManagementDTO);
		JsonObject data = new JsonObject(response);
		@SuppressWarnings("unchecked")
		Map<String, String> productData = data.mapTo(Map.class);
		cycleManagementDTO.setSettleProduct(productData);
		if (StringUtils.isBlank(cycleManagementDTO.getCycleDate())) {
			String formattedRequestDate = DateUtils.getTodayLocalDate(DateUtils.YYYY_MM_DD);
			cycleManagementDTO.setCycleDate(formattedRequestDate);
		}
		model.addAttribute(CYCLE_MAN_DTO, cycleManagementDTO);
		return getView(model, SHOW_REPORT_PROJ_STATUS);
	}

}
