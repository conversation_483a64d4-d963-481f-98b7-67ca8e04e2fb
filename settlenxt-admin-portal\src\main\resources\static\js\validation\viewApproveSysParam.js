$(document).ready(function () {
	$('.appRejMust').hide();
	$('.remarkMust').hide();

	$('#apprej').change(function () {
		if ($("#apprej").val() != "N") {
			$(".appRejMust").hide();
		} else {
			$(".appRejMust").show();
			$(".remarkMust").hide();
		}
	});

});

function display() {
	$(".appRejMust").hide();
}

function userAction(action, sysType ,sysKey ) {
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "sysType," + sysType + ",sysKey," + sysKey + ",_vTransactToken,"
		+ tokenValue;
	postData(action, data);
}


function backAction(action) {
	var data = "status," + status;
	postData(action, data);
}

function postAction(action) {
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data;
	var sysType ,sysKey;
	var remarks;
	if (maxLengthTextArea('rejectReason')) {
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				sysType = $("#sysType").val();
				sysKey = $("#sysKey").val();
				remarks = $("#rejectReason").val();
				data = "sysType," + sysType + ",sysKey," + sysKey + ",status," + "A" + ",_vTransactToken," + tokenValue + ",remarks,"
					+ remarks;
				postData(action, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				sysType = $("#sysType").val();
				sysKey = $("#sysKey").val();
				remarks = $("#rejectReason").val();
				data = "sysType," + sysType + ",sysKey," + sysKey + ",status," + "R" + ",_vTransactToken," + tokenValue + ",remarks,"
					+ remarks;
				postData(action, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	}
}
