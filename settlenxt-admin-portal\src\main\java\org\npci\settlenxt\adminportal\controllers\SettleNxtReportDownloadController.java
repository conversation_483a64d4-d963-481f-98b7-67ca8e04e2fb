package org.npci.settlenxt.adminportal.controllers;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URLDecoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.zip.Deflater;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletResponse;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.IOUtils;
import org.npci.settlenxt.adminportal.common.cache.SysParams;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.service.NetworkFileService;
import org.npci.settlenxt.adminportal.service.ReportService;
import org.npci.settlenxt.common.cache.BaseLookupDTOCache;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.AuditBatchLogDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.npci.settlenxt.portal.common.util.Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

import lombok.extern.slf4j.Slf4j;

@Controller
@Slf4j
@RequiredArgsConstructor
public class SettleNxtReportDownloadController extends BaseController {

	@Autowired
	private ReportService reportService;

	private final NetworkFileService networkFileService;
	private final SysParams sysParams;
	private final BaseLookupDTOCache baseLookupDTOCache;

	private static final String SETTLENXT_NETWORK_REPORTS = "settleNxtNetworkReports";
	private static final String SETTLENXT_REPORT_DOWNLOAD = "settleNxtReportDownload";
	private static final String ALL_DISPUTES="All_Disputes-";
	private static final String ALL_DISPUTES_FILENAME="All_Disputes.csv";

	private static final String ZIP = ".zip";
	private static final String CSV_FILE_EXTENSION = ".csv";
	private static final String ALL_ZIP = "All";
	private static final String ZERO = "0";
	private static final String DELIMITER = "_";
	private static final String HYPHEN = "-";
	private static final String UTF_8 = "UTF-8";

	@PostMapping("/network-reports")
	@PreAuthorize("hasAuthority('View Network Reports')")
	public String showNetworkReportsPage(Model model) {
		AuditBatchLogDTO auditBatchLogDTO = new AuditBatchLogDTO();
		model.addAttribute(CommonConstants.NETWORK_TYPES, sysParams.getSystemKeyValue(CommonConstants.NETWORK_TYPE, CommonConstants.INTL_NETWORKS).split(","));
		model.addAttribute("transactionStatusOptions",
				baseLookupDTOCache.getLookupListOfType(CommonConstants.TRANSACTION_STATUS_LOOKUP_TYPE).stream().map(CodeValueDTO::getCode).toList());
		model.addAttribute(BaseCommonConstants.AUDIT_BATCH_LOG, auditBatchLogDTO);
		return getView(model, SETTLENXT_NETWORK_REPORTS);
	}

	@PostMapping("/settleNxtNetworkFileDownload")
	@PreAuthorize("hasAuthority('View Network Reports')")
	public ResponseEntity<Resource> settleNxtNetworkFileDownload(@RequestParam String fromDateStr, @RequestParam String toDateStr,
																 @RequestParam String networkType, @RequestParam(required = false) String status) throws IOException {
		String fileName = org.npci.settlenxt.adminportal.common.util.Utils.generateFileName(fromDateStr, toDateStr, networkType, status);
		InputStreamResource file = new InputStreamResource(networkFileService.downloadCsv(fromDateStr, toDateStr, networkType, status));
		return ResponseEntity.ok().header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName + ".csv")
				.contentType(MediaType.parseMediaType("application/csv")).body(file);

	}

	@PostMapping("/settleNxtNetworkFileSearch")
	@PreAuthorize("hasAuthority('View Network Reports')")
	public String settleNxtNetworkFileSearch(@RequestParam String fromDateStr, @RequestParam String toDateStr,
											 @RequestParam String networkType, @RequestParam(required = false) String status, Model model) throws SettleNxtException {

		AuditBatchLogDTO auditBatchLogDTO = AuditBatchLogDTO.builder().fromDateStr(fromDateStr).toDateStr(toDateStr).networkType(networkType).status(status).build();

		model.addAttribute(CommonConstants.NETWORK_TYPES, sysParams.getSystemKeyValue(CommonConstants.NETWORK_TYPE, CommonConstants.INTL_NETWORKS).split(","));
		model.addAttribute("transactionStatusOptions",
				baseLookupDTOCache.getLookupListOfType(CommonConstants.TRANSACTION_STATUS_LOOKUP_TYPE).stream().map(CodeValueDTO::getCode).toList());
		model.addAttribute(CommonConstants.NETWORK_FILE_DATA, networkFileService.getNetworkFileDetails(fromDateStr, toDateStr, networkType, status));
		model.addAttribute(BaseCommonConstants.AUDIT_BATCH_LOG, auditBatchLogDTO);

		return getView(model, SETTLENXT_NETWORK_REPORTS);

	}

	@PostMapping("/settleNxtfileDownload")
	@PreAuthorize("hasAuthority('View SettleNxt Report Download')")
	public String settleNxtfileDownload(Model model) {

		AuditBatchLogDTO auditBatchLogDTO = new AuditBatchLogDTO();
		model.addAttribute(BaseCommonConstants.FILE_TYPE_LIST, reportService.getSettlementFileType());
		model.addAttribute(CommonConstants.SETTLEMENT_CYCLE_LIST_NO, reportService.getSettlementCycleList());
		List<AuditBatchLogDTO> memberList = reportService.getMemberListDoc();
		model.addAttribute(BaseCommonConstants.MEMBER_LIST, memberList);
		model.addAttribute(BaseCommonConstants.AUDIT_BATCH_LOG, auditBatchLogDTO);
		return getView(model, SETTLENXT_REPORT_DOWNLOAD);

	}

	@PostMapping("/settleNxtfileSearchMembrStr")
	@PreAuthorize("hasAuthority('View SettleNxt Report Download')")
	public String settleNxtfileSearchMembrStr(@RequestParam(value = "fromDateStr", required = false) String fromDateStr,
			@RequestParam(value = "toDateStr", required = false) String toDateStr,
			@RequestParam(value = "fileType", required = false) String fileType,
			@RequestParam(value = "cycleNum", required = false) String cycleNum,
			@RequestParam(value = "memberName", required = false) String memberName, Model model) {

		AuditBatchLogDTO auditBatchLogDTO = settleNxtbulkFileList(fromDateStr, toDateStr, fileType, cycleNum,
				memberName);
		List<AuditBatchLogDTO> reportList = reportService.settleNxtreportGeneratedList(auditBatchLogDTO);
		model.addAttribute(CommonConstants.SETTLEMENT_CYCLE_LIST, reportList);

		model.addAttribute(BaseCommonConstants.AUDIT_BATCH_LOG, auditBatchLogDTO);
		model.addAttribute(BaseCommonConstants.FILE_TYPE_LIST, reportService.getSettlementFileType());
		model.addAttribute(CommonConstants.SETTLEMENT_CYCLE_LIST_NO, reportService.getSettlementCycleList());
		List<AuditBatchLogDTO> memberList = reportService.getMemberListDoc();
		model.addAttribute(BaseCommonConstants.MEMBER_LIST, memberList);
		return getView(model, SETTLENXT_REPORT_DOWNLOAD);

	}

	public AuditBatchLogDTO settleNxtbulkFileList(String fromDateStr, String toDateStr, String fileType,
			String cycleNum, String memberName) {

		AuditBatchLogDTO auditBatchLogDTO = new AuditBatchLogDTO();
		auditBatchLogDTO.setFromDateStr(fromDateStr);
		auditBatchLogDTO.setToDateStr(toDateStr);
		auditBatchLogDTO.setCycleNum(cycleNum);
		auditBatchLogDTO.setFileType(fileType);
		auditBatchLogDTO.setMemberName(memberName);
		return auditBatchLogDTO;

	}

	@PostMapping("/settleNxtfileContentDownload")
	@PreAuthorize("hasAuthority('View SettleNxt Report Download')")
	public ResponseEntity<Object> settleNxtdownloadDetails(
			@RequestParam(value = "filePath", required = false) String filePath,
			@ModelAttribute("auditBatchLogDTO") AuditBatchLogDTO auditBatchLogDTO, Model model) {

		Resource resource = null;
		try {
			filePath = URLDecoder.decode(filePath,UTF_8);
			Path path = Paths.get(filePath);
			resource = new UrlResource(path.toUri());
		} catch (MalformedURLException | UnsupportedEncodingException e) {
			handleErrorCodeAndForward(model, SETTLENXT_REPORT_DOWNLOAD, e);
		}
		String filename = null;
		if(resource!=null) {
		filename= resource.getFilename();}
		return ResponseEntity.ok().contentType(MediaType.parseMediaType("application/octet-stream"))
				.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
				.body(resource);

	}

	@PostMapping("/settleNxtfileDownloadVerify")
	@PreAuthorize("hasAuthority('View SettleNxt Report Download')")
	public ResponseEntity<Object> settleNxtverifyFileAvailable(
			@RequestParam(value = "filePath", required = false) String filePath, Model model) {
		JsonObject jsonResponse = new JsonObject();
		try {
			reportService.verifyFileAvailableInLocation(filePath);
		} catch (Exception ex) {
			jsonResponse.addProperty(CommonConstants.STATUS, CommonConstants.TRANSACT_FAIL);
			handleErrorCodeAndForward(model, SETTLENXT_REPORT_DOWNLOAD, ex);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
		jsonResponse.addProperty(CommonConstants.STATUS, CommonConstants.TRANSACT_SUCCESS);
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PostMapping("/settleNxtfileDownloadError")
	@PreAuthorize("hasAuthority('View SettleNxt Report Download')")
	public String settleNxtshowFileNotFoundError(Model model,
			@RequestParam(value = "fromDateStr", required = false) String fromDateStr,
			@RequestParam(value = "toDateStr", required = false) String toDateStr,
			@RequestParam(value = "cycleNum", required = false) String cycleNum,
			@RequestParam(value = "memberName", required = false) String memberName,
			@RequestParam(value = "fileType", required = false) String fileType) {
		AuditBatchLogDTO auditBatchLogDTO = settleNxtbulkFileList(fromDateStr, toDateStr, fileType, cycleNum,
				memberName);
		List<AuditBatchLogDTO> reportList = reportService.settleNxtreportGeneratedList(auditBatchLogDTO);
		model.addAttribute(BaseCommonConstants.FILE_TYPE_LIST, reportService.getSettlementFileType());
		model.addAttribute(CommonConstants.SETTLEMENT_CYCLE_LIST, reportList);
		model.addAttribute(BaseCommonConstants.AUDIT_BATCH_LOG, auditBatchLogDTO);
		List<AuditBatchLogDTO> memberList = reportService.getMemberListDoc();
		model.addAttribute(BaseCommonConstants.MEMBER_LIST, memberList);
		model.addAttribute(CommonConstants.ERROR_STATUS, CommonConstants.ERR_STATUS);
		return getView(model, SETTLENXT_REPORT_DOWNLOAD);
	}

	@PostMapping("/SettlementReportContentDownload")
	@PreAuthorize("hasAuthority('View SettleNxt Report Download')")
	public void downloadAllSettlementReportFiles(HttpServletResponse response,
			@RequestParam String bulkdownloadIdList, Model model,
			@RequestParam(value = "cycleNum", required = false) String cycleNum,
			@RequestParam(value = "memberName", required = false) String memberName,
			@RequestParam(value = "fileType", required = false) String fileType,
			@RequestParam(value = "fromDateStr", required = false) String fromDateStr) {

		if ("".equals(bulkdownloadIdList)) {

			throw new SettleNxtException("File Ids are null ", "");

		}
		String[] idArray = bulkdownloadIdList.split("\\|");
		int[] values = Arrays.stream(idArray).mapToInt(Integer::parseInt).toArray();
		List<Integer> bulkIdlist = Arrays.stream(values).boxed().toList();

		List<AuditBatchLogDTO> fileList = reportService.fetchFilePathList(bulkIdlist);
		try {
			getAllDownloads(fileList, response, memberName, cycleNum, fileType, fromDateStr);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SETTLENXT_REPORT_DOWNLOAD, ex);
		}
	}

	public void getAllDownloads(List<AuditBatchLogDTO> fileDtlsList, HttpServletResponse response, String memberName,
			String cycleNum, String fileType, String fromDateStr) throws IOException {

		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		ZipOutputStream out = new ZipOutputStream(baos);
		FileInputStream in = null;
		try {
			out.setLevel(Deflater.DEFAULT_COMPRESSION);
			File input;

			for (AuditBatchLogDTO auditBatchLogDTO : fileDtlsList) {
				String path = auditBatchLogDTO.getFilePath();
				path = new File(path).getCanonicalPath();

				input = new File(path);
				in = handleEntry(out, in, input, auditBatchLogDTO);

			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			out.close();
			if (in != null) {
				in.close();
			}

		}
		OutputStream sos = response.getOutputStream();
		String zipName;
		try {
			response.setContentType("application/zip");

			if (ZERO.equalsIgnoreCase(fileType)) {
				fileType = null;
			}
			if (ZERO.equalsIgnoreCase(cycleNum)) {
				cycleNum = null;
			}

			if (!ZERO.equalsIgnoreCase(memberName)) {
				zipName = Utils.generateDelimiteredString(DELIMITER, memberName, fileType, fromDateStr, cycleNum);
			} else {
				zipName = Utils.generateDelimiteredString(DELIMITER, ALL_ZIP, fileType, fromDateStr, cycleNum);
			}

			zipName = zipName + ZIP;
 			response.setHeader("Content-Disposition","attachment; filename=" + zipName );
 			
			sos.write(baos.toByteArray());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			out.flush();
			out.close();

			sos.flush();
			sos.close();
		}
	}

	private FileInputStream handleEntry(ZipOutputStream out, FileInputStream in, File input,
			AuditBatchLogDTO auditBatchLogDTO) {
		try(FileInputStream fs = new FileInputStream(input)) {
			in=fs;
			out.putNextEntry(new ZipEntry(getOutputFileName(auditBatchLogDTO, input)));
			IOUtils.copy(in, out);

			out.closeEntry();
			in.close();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return in;
	}

	private String getOutputFileName(AuditBatchLogDTO auditBatchLogDTO, File inputFile) {
		String outputFileName = inputFile.getName();
		if (StringUtils.equalsIgnoreCase(outputFileName, ALL_DISPUTES_FILENAME)) {
			String participantId = auditBatchLogDTO.getParticipantId();
			String cycleNumber = auditBatchLogDTO.getCycleNum();
			outputFileName = ALL_DISPUTES + participantId + HYPHEN + cycleNumber + CSV_FILE_EXTENSION;
		}
		return outputFileName;

	}
}
