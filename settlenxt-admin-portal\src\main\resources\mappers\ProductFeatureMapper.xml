<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.ProductFeatureRepository">
	
	<select id="getProductFeeDataByBinNo" resultType="ProductFeatureDTO">
		select bin_no as binNo,count,product_fee as productFee,feature_fee as featureFee,product_fee+feature_fee as totalFee  
		from  MCPR_BIN_WISE_FEE where submitted_date=#{submittedMonth} and bin_no=#{binNo}
	</select>
	<select id="getProductFeeDataByBankName" resultType="ProductFeatureDTO">
		select bin_no as binNo,count,product_fee as productFee,feature_fee as featureFee,product_fee+feature_fee as totalFee  
		from  MCPR_BIN_WISE_FEE where submitted_date=#{submittedMonth} 
		and bin_no in (select bin_number from  membin_details where bin_type='I' and status!='D'
		and  participant_id in (select participant_id  from  participant where  unique_bank_name=#{bankName}))
	</select>
	<select id="getFeatureFeeDataByBankandBin" resultType="ProductFeatureDTO">
        select COALESCE( mffc.feature,'') featureName, mffc.details as details,mffc.feature_fee fee, b.feature_fee as totalFee from mcpr_bin_feature_mapping a
inner join mcpr_feature_fee_config mffc on mffc.card_config_id =a.feature_id 
left outer join mcpr_bin_feature_wise_fee b on mffc.card_config_id =b.feature_id and trim(b.submitted_date)=#{submittedMonth}  and b.bin_no=a.bin
where #{submittedMonth}::numeric between a.from_Date::numeric and a.to_Date::numeric and a.bin=#{binNo}
	</select>
		<select id="getBinBankCheck" resultType="String">
		select m.bin_number from  membin_details m  
		inner join  participant p on p.participant_id = m.participant_id 
		where  p.unique_bank_name=#{bankName} and m.bin_type='I'  and m.bin_number =#{binNo} and m.status!='D'
	</select>
</mapper>	