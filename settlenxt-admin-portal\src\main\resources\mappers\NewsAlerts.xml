<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="org.npci.settlenxt.adminportal.repository.NewsAlertsRepository">

	<select id="fetchNewsIdSeq" resultType="int">
		SELECT
		nextval('news_alert_id_seq')
	</select>
	<select id="fetchReferenceNoSeq" resultType="int">
		SELECT
		nextval('reference_number_seq')
	</select>
	<insert id="addNewsAlerts">
		INSERT INTO
		NEWS_ALERTS_STG(news_alert_id,news_alert_type,title,subtitle,valid_from,valid_to,summary,details,footer_data,is_public,reference_number,status,created_by,created_on,request_state,last_operation,trigger_mail,is_critical)
		VALUES
		(#{newsId},#{isType},#{title},#{subTitle},#{fromDate},#{toDate},#{summary},#{details},#{footerData},#{publishType},#{referenceNumber},#{status},#{createdBy},#{createdOn},#{requestState},#{lastOperation},#{sendMail},#{critical})
	</insert>
	<insert id="addNewsAlertsFinal">
		INSERT INTO
		NEWS_ALERTS(news_alert_id,news_alert_type,title,subtitle,valid_from,valid_to,summary,details,footer_data,is_public,reference_number,status,created_by,created_on,request_state,trigger_mail,is_critical,period_type,comments,freq_from,freq_to)
		VALUES
		(#{newsId},#{isType},#{title},#{subTitle},#{fromDate},#{toDate},#{summary},#{details},#{footerData},#{publishType},#{referenceNumber},#{status},#{createdBy},#{createdOn},#{requestState},#{sendMail},#{critical},#{periodType},#{comment},#{freqFrom},#{freqTo})
	</insert>

	<update id="updateNewsInfo">
		update NEWS_ALERTS_STG set
		is_critical=#{critical},trigger_mail=#{sendMail},
		last_operation=#{lastOperation},news_alert_type=#{isType},title=#{title},subtitle=#{subTitle},valid_from=#{fromDate},valid_to=#{toDate},summary=#{summary},details=#{details},footer_data=#{footerData},is_public=#{publishType},status=#{status},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},request_state=#{requestState},period_type=#{periodType},comments=#{comment},freq_from=#{freqFrom},freq_to=#{freqTo}
		where news_alert_id = #{newsId}
	</update>
	


	<update id="updateSchedulerInfo">
		update NEWS_ALERTS_STG set
		period_type=#{periodType},comments=#{comment},freq_from=#{freqFrom},freq_to=#{freqTo},status=#{status},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},request_state=#{requestState}
		where news_alert_id=#{newsId}
	</update>
	<update id="updateNewsAlertsInfo">
		update NEWS_ALERTS_STG set
		is_critical=#{critical},trigger_mail=#{sendMail},
		last_operation=#{lastOperation},news_alert_type=#{isType},title=#{title},subtitle=#{subTitle},valid_from=#{fromDate},valid_to=#{toDate},summary=#{summary},details=#{details},footer_data=#{footerData},is_public=#{publishType},status=#{status},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},request_state=#{requestState},period_type=#{periodType},comments=#{comment},freq_from=#{freqFrom},freq_to=#{freqTo}
		where news_alert_id = #{newsId}
	</update>
	<update id="updateSchedulerDetailsInfo">
		update NEWS_ALERTS_STG set
		period_type=#{periodType},comments=#{comment},freq_from=#{freqFrom},freq_to=#{freqTo},status=#{status},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},request_state=#{requestState}
		where news_alert_id=#{newsId}
	</update>

	<update id="newsAlertsApproveStg">
		UPDATE NEWS_ALERTS_STG SET request_state =
		'A',checker_comments=#{rejectReason} WHERE news_alert_id = #{newsId}
	</update>

	<update id="newsAlertsRejectStg">
		UPDATE NEWS_ALERTS_STG SET request_state ='R',last_operation=#{lastOp},checker_comments=#{rejectReason} WHERE news_alert_id = #{newsId}
	</update>

	<select id="checkNewsInfoSaved" resultType="int">
		select count(*) from
		NEWS_ALERTS_STG where title = #{title}
	</select>
	<select id="getTotalCount" resultType="long">
		select count(*) from
		NEWS_ALERTS_STG where request_state in ('A')
	</select>

	<select id="getApprovalTotalCount" resultType="long">
		select count(*)
		from NEWS_ALERTS_STG where request_state in ('P','R')
	</select>
	<select id="getSavedTotalCount" resultType="long">
		select count(*) from
		NEWS_ALERTS_STG where request_state in ('I')
	</select>
	<select id="getDeleteTotalCount" resultType="long">
		select count(*) from
		NEWS_ALERTS_STG where request_state in ('D')
	</select>


	<select id="getNewsAlertsEdit" resultType="newsDTO">
		SELECT N.is_critical
		as critical,N.news_alert_id as newsId,N.news_alert_type
		as
		isType,N.title as title,N.subtitle as
		subTitle,to_char(N.valid_from,'DD-MM-YYYY') as
		fromDateStr,to_char(N.valid_to,'DD-MM-YYYY') as toDateStr,N.summary as
		summary,N.details as details,N.footer_data as footerData,N.is_public
		as publishType,N.reference_number as referenceNumber,N.status as
		status,N.request_state as requestState,N.trigger_mail as
		sendMail,N.period_type as periodType,N.comments as comment,N.freq_from
		as freqFrom,N.freq_to as freqTo FROM NEWS_ALERTS_STG N WHERE
		reference_number=#{refNumber}
	</select>

	<select id="getNewsAlerts" resultType="newsAlertsDTO">
		SELECT distinct on(
		nas.news_alert_id) nas.news_alert_id as newsId,
		nas.is_critical as
		critical,nas.news_alert_type as isType,nas.title as
		title,nas.subtitle
		as subTitle,nas.valid_from as fromDate,nas.valid_to
		as
		toDate,nas.summary as summary,nas.details as
		details,nas.footer_data as
		footerData,nas.is_public as
		publishType,nas.reference_number as
		referenceNumber,nas.status as
		status,nas.created_on as
		createdOn,nas.created_by as
		createdBy,nas.request_state as
		requestState,nas.trigger_mail as
		sendMail ,nas.period_type as
		periodType,nas.comments as comment
		,nas.freq_from as
		freqFrom,nas.freq_to as freqTo,nas.last_operation as
		lastOperation,dds.participant_id as participantId ,dds.user_name as
		userName,dds.publish_type as publishType from NEWS_ALERTS_STG nas left
		join distribution_details_stg dds on dds.news_alert_id =
		nas.news_alert_id where reference_number=#{referenceNumber}
	</select>

	<select id="getNewsAlertsInfoList" resultType="newsDTO">
		SELECT distinct on( nas.news_alert_id) nas.news_alert_id as newsId,
		nas.is_critical as critical,nas.news_alert_type as isType,nas.title as
		title,nas.subtitle as subTitle,nas.valid_from as fromDate,nas.valid_to
		as toDate,nas.summary as summary,nas.details as
		details,nas.footer_data as footerData,nas.is_public as
		publishType,nas.reference_number as referenceNumber,nas.status as
		status,nas.created_on as createdOn,nas.created_by as
		createdBy,nas.request_state as requestState,nas.trigger_mail as
		sendMail ,nas.period_type as periodType,nas.comments as comment
		,nas.freq_from as freqFrom ,nas.freq_to as freqTo,nas.last_operation
		as lastOperation,dds.participant_id as participantId ,dds.user_name as
		userName,dds.publish_type as publishType from NEWS_ALERTS_STG nas left
		join distribution_details_stg dds on dds.news_alert_id =
		nas.news_alert_id where reference_number in
		<foreach item='item' index='index'
			collection='referenceNumberList' open='(' separator=',' close=')'>#{item}
		</foreach>
	</select>

	<select id="getFinalNewsAlertsList" resultType="newsAlertsDTO">
		select
		s.news_alert_id as newsId,s.news_alert_type as isType,s.title as
		
		title,s.subtitle as subTitle,TO_CHAR(s.valid_from,'DD/MM/YYYY HH24:MI:SS') as fromDateStr,TO_CHAR(s.valid_to,'DD/MM/YYYY HH24:MI:SS') as toDateStr,s.summary as summary,s.details as
		details,s.footer_data as footerData,s.is_public as
		publishType,s.reference_number as
		referenceNumber,TO_CHAR(s.created_on,'DD/MM/YYYY HH24:MI:SS') as
		createdDate,s.created_by as createdBy,s.status as status from
		NEWS_ALERTS_STG n inner join news_alerts s on
		s.news_alert_id=n.news_alert_id where n.request_state in ('A') ORDER
		BY s.news_alert_id DESC
	</select>

	<select id="getTempNewsAlertsList" resultType="newsAlertsDTO">
		SELECT 
		news_alert_id as newsId,news_alert_type as isType,title as
		title,subtitle as subTitle,TO_CHAR(valid_from,'DD/MM/YYYY HH24:MI:SS') as fromDateStr,TO_CHAR(valid_to,'DD/MM/YYYY HH24:MI:SS') as toDateStr,summary as summary,details as
		details,footer_data as footerData,is_public as
		publishType,reference_number as referenceNumber,status as status
		,TO_CHAR(created_on,'DD/MM/YYYY HH24:MI:SS') as
		createdDate,created_by as createdBy,request_state as
		requestState,checker_comments as
		checkerComments,TO_CHAR(last_updated_on,'DD/MM/YYYY HH24:MI:SS') as
		modifiedDate from NEWS_ALERTS_STG  where request_state in
		('P','R') ORDER BY news_alert_id DESC
	</select>

	<select id="getSavedNewsAlertsList" resultType="newsAlertsDTO">
		SELECT news_alert_type as isType,title as title,subtitle as
		subTitle,TO_CHAR(s.valid_from,'DD/MM/YYYY HH24:MI:SS') as
		fromDateStr,TO_CHAR(s.valid_to,'DD/MM/YYYY HH24:MI:SS') as
		toDateStr,summary as summary,details as details,footer_data as
		footerData,is_public as publishType,reference_number as
		referenceNumber,status as status,TO_CHAR(s.created_on,'DD/MM/YYYY HH24:MI:SS') as createdDate,request_state as requestState,s.created_by
		as createdBy from NEWS_ALERTS_STG s where s.request_state in ('I') ORDER BY news_alert_id DESC
	</select>

	<select id="getDeletedNewsAlertsList" resultType="newsAlertsDTO">
		SELECT news_alert_type as isType,title as title,subtitle as
		subTitle,TO_CHAR(valid_from,'DD/MM/YYYY HH24:MI:SS') as
		fromDateStr,TO_CHAR(valid_to,'DD/MM/YYYY HH24:MI:SS') as
		toDateStr,summary as summary,details as details,footer_data as
		footerData,is_public as publishType,reference_number as
		referenceNumber,status as status,TO_CHAR(created_on,'DD/MM/YYYY HH24:MI:SS') as createdDate,request_state as requestState,created_by
		as createdBy from NEWS_ALERTS_STG s where request_state in ('D') ORDER BY news_alert_id DESC
	</select>

	<select id="getnewsAlertsMain" resultType="newsAlertsDTO">
		SELECT news_alert_id
		as newsId,news_alert_type as isType,title as
		title,subtitle as
		subTitle,valid_from as fromDate,valid_to as
		toDate,summary as
		summary,details as details,footer_data as
		footerData,is_public as
		publishType,reference_number as
		referenceNumber,status as
		status,created_on as
		createdDate,request_state as
		requestState,trigger_mail as
		sendMail,is_critical as
		critical,period_type as periodType,comments as
		comment,freq_from as
		freqFrom ,freq_to as freqTo from NEWS_ALERTS
		WHERE
		reference_number=#{referenceNumber}
	</select>

	<select id="getNewsAlertsInfo" resultType="newsDTO">
		SELECT distinct on(
		nas.news_alert_id) nas.news_alert_id as newsId,
		nas.is_critical as
		critical,nas.news_alert_type as isType,nas.title as
		title,nas.subtitle
		as subTitle,nas.valid_from as fromDate,nas.valid_to
		as
		toDate,nas.summary as summary,nas.details as
		details,nas.footer_data as
		footerData,nas.is_public as
		publishType,nas.reference_number as
		referenceNumber,nas.status as
		status,nas.created_on as
		createdOn,nas.created_by as
		createdBy,nas.status as
		status,nas.request_state as
		requestState,nas.trigger_mail as sendMail
		,nas.period_type as
		periodType,nas.comments as comment ,nas.freq_from
		as freqFrom
		,nas.freq_to as freqTo,nas.last_operation as
		lastOperation,dds.participant_id as participantId ,dds.user_name as
		userName,dds.publish_type as publishType from NEWS_ALERTS_STG nas left
		join distribution_details_stg dds on dds.news_alert_id =
		nas.news_alert_id where reference_number=#{referenceNumber}
	</select>

	<select id="getParticipantByUserName" resultType="newsDTO">
		Select
		bank_participant_id as bankName,maker_checker_flag as roles from
		user_detail where login_id=#{users} limit 1
	</select>

	<insert id="addNewsDistributionDetails">
		INSERT INTO distribution_details_stg (news_alert_id,
		participant_id,
		user_name, role_desc, created_on,
		created_by,status,publish_type)
		VALUES(#{newsId}, #{bankName},
		#{users}, #{roles}, #{createdOn},
		#{createdBy},#{status},#{publishType})
	</insert>

	<delete id="deleteDiscardedEntry">
		DELETE FROM NEWS_ALERTS_STG WHERE reference_number=
		#{referenceNumber}
	</delete>

	<delete id="deleteExistingDistributiondetails">
		DELETE from distribution_details_stg where
		news_alert_id=#{newsId}
	</delete>

	<select id="checkIfExistsInMain" resultType="long">
		select count(*) from
		news_alerts where news_alert_id=#{newsId}
	</select>

	<update id="updateNewsAlertStgDeactivate">
		update NEWS_ALERTS_STG set last_operation=
		#{lastOperation},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},request_state=#{requestState},status=#{status}
		where news_alert_id = #{newsId}
	</update>

	<update id="updateNewsAlertStgDeactivateMain">
		update NEWS_ALERTS set
		last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},request_state=#{requestState},status=#{status}
		where news_alert_id = #{newsId}
	</update>

	<update id="updateDistributionStgDeactivate">
		update distribution_details_stg set status=#{status}
		where news_alert_id = #{newsId}
	</update>

	<update id="updateDistributionStgDeactivateMain">
		update distribution_details set status=#{status}
		where news_alert_id = #{newsId}
	</update>

	<update id="updateDistributionStg">
		update distribution_details_stg set status=#{status}
		, last_operation = #{lastOperation} where news_alert_id = #{newsId}
	</update>

	<insert id="addNewsDistributionInfo">
		INSERT INTO distribution_details (news_alert_id,
		participant_id, user_name, role_desc, created_on,status,
		created_by,publish_type) VALUES(#{newsId}, #{participantId},
		#{userName}, #{roleDesc}, #{createdOn},#{status},
		#{createdBy},#{publishType})
	</insert>

	<select id="getSchedulerDetail" resultType="newsDTO">
		SELECT news_alert_id
		as newsId ,valid_from as fromDate,period_type as periodType,comments
		as comment,created_on as createdOn from NEWS_ALERTS_STG WHERE
		news_alert_id=#{newsId}
	</select>

	<select id="getNewsAlertsListPublic" resultType="newsAlertsDTO">
		SELECT
		na.is_critical as critical,na.news_alert_id as
		newsId,na.news_alert_type as isType,na.title as title,na.subtitle as
		subTitle,na.valid_from as fromDate,na.valid_to as toDate,na.summary as
		summary,na.details as details,na.footer_data as
		footerData,na.is_public as publishType,na.reference_number as
		referenceNumber,na.status as status,na.created_on as
		createdOn,na.created_by as createdBy,na.request_state as
		requestState,na.period_type as periodType ,na.freq_from as
		freqFrom,na.freq_to as freqTo from NEWS_ALERTS na inner join
		distribution_details dd on (na.news_alert_id = dd.news_alert_id) where
		now() between na.valid_from and na.valid_to and na.is_Public='Public'
		AND na.request_state='A' order by na.created_on desc
	</select>

	<select id="getNewsAlertsListSpecific"
		resultType="newsAlertsDTO">
		SELECT na.is_critical as critical,na.news_alert_id as
		newsId,na.news_alert_type as isType,na.title as title,na.subtitle as
		subTitle,na.valid_from as fromDate,na.valid_to as toDate,na.summary as
		summary,na.details as details,na.footer_data as
		footerData,na.is_public as publishType,na.reference_number as
		referenceNumber,na.status as status,na.created_on as
		createdOn,na.created_by as createdBy,na.request_state as
		requestState,na.period_type as periodType ,na.freq_from as
		freqFrom,na.freq_to as freqTo from NEWS_ALERTS na inner join
		distribution_details dd on (na.news_alert_id = dd.news_alert_id) where
		now() between na.valid_from and na.valid_to and
		na.is_Public='Specific' AND na.request_state='A' and dd.user_name =
		#{userName} order by na.created_on desc
	</select>

	<select id="getNewsAlertsListCommon" resultType="newsAlertsDTO">
		SELECT
		na.is_critical as critical,na.news_alert_id as
		newsId,na.news_alert_type as isType,na.title as title,na.subtitle as
		subTitle,na.valid_from as fromDate,na.valid_to as toDate,na.summary as
		summary,na.details as details,na.footer_data as
		footerData,na.is_public as publishType,na.reference_number as
		referenceNumber,na.status as status,na.created_on as
		createdOn,na.created_by as createdBy,na.request_state as
		requestState,na.period_type as periodType ,na.freq_from as
		freqFrom,na.freq_to as freqTo from NEWS_ALERTS na inner join
		distribution_details dd on (na.news_alert_id = dd.news_alert_id) where
		now() between na.valid_from and na.valid_to and na.is_Public='Common'
		AND na.request_state='A' order by na.created_on desc
	</select>

<select id="checkIfNewsAlertsExistReports" resultType="long">
SELECT count(news_alert_id) FROM NEWS_ALERTS_STG WHERE REQUEST_STATE=#{requestState} AND VALID_FROM >= TO_TIMESTAMP(#{fromDate},'YYYY-MM-DD HH24:MI:SS')<![CDATA[and VALID_TO <= TO_TIMESTAMP(#{toDate},'YYYY-MM-DD HH24:MI:SS')]]>
</select>
	<select id="getDistributionDetails" resultType="newsAlertsDTO">
		select distinct
		CONCAT (ud.PARTICIPANT_ID,'-', ud.bank_name) as bank_name
		,dds.participant_id as participantId,dds.user_name as userName,dds.role_desc as roleDesc,dds.publish_type as publishType
		from distribution_details_stg dds left join participant ud on
		dds.participant_id=ud.participant_id where dds.status='A' and
		dds.news_alert_id =#{newsId}
	</select>
	<update id="updateNewsAlertsInfoMain">
		update NEWS_ALERTS set is_critical=#{critical},trigger_mail=#{sendMail},news_alert_type=#{isType},title=#{title},subtitle=#{subTitle},valid_from=#{fromDate},valid_to=#{toDate},summary=#{summary},details=#{details},footer_data=#{footerData},is_public=#{publishType},status=#{status},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},request_state=#{requestState},period_type=#{periodType},comments=#{comment},freq_from=#{freqFrom},freq_to=#{freqTo} where news_alert_id = #{newsId}
	</update>
	<update id="updateNewsAlertApproveStg">
		update NEWS_ALERTS_STG set  last_operation=#{lastOperation},status=#{status},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},request_state=#{requestState},checker_comments=#{approvalRemarks} where news_alert_id = #{newsId}
	</update>
	<select id="checkIfExistsInStg" resultType="long">
		select count(*) from news_alerts_stg where news_alert_id=#{newsId}  
	</select>
	<delete id="deleteDistributionMain">
		delete from distribution_details where news_alert_id  =#{newsId}
	</delete>
	<select id="fetchDistributionList" resultType="newsAlertsDTO">
		select news_alert_id as newsId,participant_id as participantId,user_name as userName,status as status ,role_desc as roleDesc ,created_on as createdOn,created_by as createdBy,publish_type as publishType from distribution_details_stg where news_alert_id  =#{newsId}
	</select>
</mapper>