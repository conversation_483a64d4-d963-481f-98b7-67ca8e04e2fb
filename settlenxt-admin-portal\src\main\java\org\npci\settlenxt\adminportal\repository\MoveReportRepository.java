package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.adminportal.dto.MoveReportDTO;
import org.npci.settlenxt.adminportal.model.MoveReportModel;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;

@Mapper
public interface MoveReportRepository {
	 void saveMoveReportToDatabase(MoveReportDTO moveReportDTO);

	 List<MoveReportDTO> getReportList();

	 List<MoveReportDTO> searchReportList(MoveReportDTO movereportDTO, boolean isRepoNet);

	 Integer fetchIdFromSeq();

	 List<MoveReportDTO> getMonthFromLookup();

	 List<CodeValueDTO> getMonths();

	 String getBasePath(String sysKey);

	 String getDestPath(String fileType);

	 void insertProductReportStatus(MoveReportModel moveReportModel);

	 List<String> getAllCyclesByProductId(String productId);

}
