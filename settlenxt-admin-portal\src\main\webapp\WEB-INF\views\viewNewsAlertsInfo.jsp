<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript"
	src="./static/js/validation/viewNewsAlerts.js"></script>
<style>
.form-control {
	background-color: white !important;
}
</style>
<div class="body-content">
	<div class="bs-example">
		<form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="addEditNewsALerts" modelAttribute="newsDTO"
			action="${submitNewsALerts}" autocomplete="off">
			<br />
			<form:hidden path="reqType" id="reqType" />
			<form:hidden path="newsId" id="newsId"
				value="${newsAlertsDTO.newsId}" />
			<form:hidden path="referenceNumber" id="referenceNumber"
				value="${newsAlertsDTO.referenceNumber}" />

			<div class="panel panel-default no_margin">

				<div class="panel-heading clearfix">
					<strong><span class="glyphicon glypicon-th"></span><span
						data-il8n="Data"><spring:message code="am.lbl.newsAlerts" /></span></strong>
				</div>

				<div class="panel-body">
					<div class="bs-example">
						<div class="panel-group" id="accordion">


							<div class="panel panel-default">
								<div class="panel-heading">
									<h4 class="panel-title">
										<a data-toggle="collapse" data-parent="#accordion"
											href="#collapseOne">News and Alerts <span
											class="glyphicon glyphicon-plus"></span></a>
									</h4>
								</div>
								<div id="collapseOne" class="panel-collapse collapse in">
									<div class="panel-body">
										<div class="">
											<div class="row">
												<div class="col-md-12">
													<div class="card">

														<div class="row">
															<div class="col-xs-12">
																<div class="col-sm-6">
																	<div class="form-group">
																		<label for="squareInput">Type<span class="red"></span>
																		</label>
																		<form:input path="isType" id="isType" name="isType"
																			cssClass="form-control input-square viewOnly" />
																	</div>
																</div>
															</div>
														</div>

														<div class="row">
															<div class="col-xs-12">
																<c:if test="${newsAlertsDTO.isType eq 'Alerts'}">
																	<div class="col-md-3" id="criticalDiv">
																		<div class="form-group">
																			<label for="squareSelect">Criticality<span
																				class="red"></span></label>
																			<form:input path="critical" id="critical"
																				name="critical"
																				cssClass="form-control input-square viewOnly" />

																		</div>
																	</div>
																</c:if>
																<div class="row">
																	<div class="col-xs-12">
																		<div class="col-sm-6">
																			<label for="squareInput">Title <span
																				class="red">*</span></label>
																			<form:input path="title" id="title" name="title"
																				cssClass="form-control input-square viewOnly" />

																		</div>
																	</div>
																</div>

																<div class="row">
																	<div class="col-xs-12">
																		<div class="col-sm-6">
																			<label for="squareInput">Sub Title <span
																				class="red">*</span></label>
																			<form:input path="subTitle" id="subTitle"
																				name="subTitle"
																				cssClass="form-control input-square viewOnly" />

																		</div>
																	</div>
																</div>

																<div class="row">
																	<div class="col-xs-12">
																		<div class="col-sm-6">
																			<label for="squareSelect">From Date <span
																				class="red">*</span></label>
																			<form:input path="fromDate" id="fromDate"
																				name="fromDate"
																				cssClass="form-control input-square viewOnly" />

																		</div>
																	</div>
																</div>

																<div class="row">
																	<div class="col-xs-12">
																		<div class="col-sm-6">
																			<label for="squareSelect">To Date <span
																				class="red">*</span></label>
																			<form:input path="toDate" id="toDate" name="toDate"
																				cssClass="form-control input-square viewOnly" />

																		</div>
																	</div>
																</div>

																<div class="row">
																	<div class="col-xs-12">
																		<div class="col-sm-6">
																			<label for="squareInput">Summary <span
																				class="red">*</span></label>
																			<form:input path="summary" id="summary"
																				name="summary"
																				cssClass="form-control input-square viewOnly" />

																		</div>
																	</div>
																</div>

																<div class="row">
																	<div class="col-xs-12">
																		<div class="col-sm-6">
																			<label for="squareInput">Details <span
																				class="red">*</span></label>
																			<form:textarea path="details" id="details"
																				name="details"
																				style="height: 121px; width: 608px;"
																				cssClass="form-control input-square viewOnly" />

																		</div>
																	</div>
																</div>

																<div class="row">
																	<div class="col-xs-12">
																		<div class="col-sm-6">
																			<label for="squareInput">Footer Data <span
																				class="red">*</span></label>
																			<form:input path="footerData" id="footerData"
																				name="footerData"
																				cssClass="form-control input-square viewOnly" />

																		</div>
																	</div>
																</div>

															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="panel panel-default">
								<div class="panel-heading">
									<h4 class="panel-title">
										<a data-toggle="collapse" data-parent="#accordion"
											href="#collapseTwo" id="collapseTwoLink">Distribution
											Details <span class="glyphicon glyphicon-plus"></span>
										</a>
									</h4>
								</div>
								<div id="collapseTwo" class="panel-collapse collapse">
									<div class="">
										<div class="row">
											<div class="col-md-12">
												<div class="card">

													<div class="card-body">
														<div class="row">

															<div class="row">
																<div class="col-xs-12">
																	<div class="col-sm-6">
																		<div class="form-group">
																			<label for="squareInput">Type</label>
																			<form:input path="isType" id="isType" name="isType"
																				cssClass="form-control input-square viewOnly" />

																		</div>
																	</div>
																</div>
															</div>

															<div class="row">
																<div class="col-xs-12">
																	<div class="col-sm-6">
																		<label for="squareInput">Title</label>
																		<form:input path="title" id="title" name="title"
																			cssClass="form-control input-square viewOnly" />

																	</div>
																</div>
															</div>

															<div class="row">
																<div class="col-xs-12">
																	<div class="col-sm-6">
																		<label for="squareInput">Sub Title </label>
																		<form:input path="subTitle" id="subTitle"
																			name="subTitle"
																			cssClass="form-control input-square viewOnly" />

																	</div>
																</div>
															</div>

															<div class="row">
																<div class="col-xs-12">
																	<div class="col-sm-6">
																		<label for="squareInput">Publish Type <span
																			class="red">*</span></label>
																		<form:input path="publishType" id="publishType"
																			name="publishType"
																			cssClass="form-control input-square viewOnly" />

																	</div>
																</div>
															</div>
															<c:if test="${newsAlertsDTO.publishType ne 'Specific'}">
																<div id="sendMailDiv">
																	<div class="col-xs-12">
																		<div class="col-md-3">
																			<div class="form-group">
																				<label for="squareSelect">Send Mail <span
																					class="red">*</span></label>
																				<form:input path="sendMail" id="sendMail"
																					name="sendMail"
																					cssClass="form-control input-square viewOnly" />
																				<div></div>
																			</div>
																		</div>
																	</div>
																</div>
															</c:if>

														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>



						<div class="panel panel-default">
							<div class="panel-heading">
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#accordion"
										href="#collapseThree" id="collapseThreeLink">Scheduler
										Details <span class="glyphicon glyphicon-plus"></span>
									</a>
								</h4>
							</div>
							<div id="collapseThree" class="panel-collapse collapse">

								<div class="">
									<div class="row">
										<div class="col-md-12">
											<div class="card">

												<div class="card-body">
													<div class="row">

														<div class="row">
															<div class="col-xs-12">
																<div class="col-md-2">
																	<div class="form-group">
																		<label for="squareSelect">Period Type</label>
																		<form:input path="periodType" id="periodType"
																			name="periodType"
																			cssClass="form-control input-square viewOnly" />
																	</div>
																</div>
															</div>
														</div>

														<div class="row">
															<div class="col-xs-12">
																<div class="col-sm-6">
																	<div class="form-group">
																		<label for="squareInput">Comments <span
																			class="red">*</span>
																		</label>
																		<form:input path="comment" id="comment" name="comment"
																			cssClass="form-control input-square viewOnly" />

																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>




						<div id="collapseFour"
							class="panel-collapse collapse in panelHideShow">
							<div class="panel-body">
								<div class="">
									<div class="row">
										<div class="col-md-12">
											<div class="card">

												<div class="card-body">
													<div class="row">
														<sec:authorize
															access="hasAuthority('Approve Reject News And Alerts')">
															<c:if test="${not empty apporveRejectButton}">

																<div class="alert alert-danger appRejMust" role="alert">Please
																	Select Approve/Reject Action.</div>
																<div class="alert alert-danger remarkMust" role="alert">Please
																	Enter Remarks.</div>
																<div class="col-md-3">
																	<div class="form-group">
																		<label><spring:message
																				code="AM.lbl.approveReject" /><span class="red">*</span>
																		</label> <select name="select" id="apprej"
																			onchange="enadisReject()"
																			class="form-control input-square">
																			<option value="N"><spring:message
																					code="AM.lbl.select" /></option>
																			<option value="A" id="approve"><spring:message
																					code="AM.lbl.approve" /></option>
																			<option value="R" id="reject"><spring:message
																					code="AM.lbl.reject" /></option>
																		</select>
																	</div>
																</div>
																<div class="col-md-6">
																	<div class="form-group">
																		<label for="squareSelect">Remarks <span
																			class="red">*</span></label>
																		<textarea rows="4" cols="50"
																			class="form-control input-square" maxlength="100"
																			id="rejectReason"></textarea>
																		<div id="errorrejectReason" class="error"></div>
																	</div>
																</div>
															</c:if>
														</sec:authorize>

													</div>
													<div class="row">
														<div class="col-sm-12 text-center">
															<sec:authorize
																access="hasAuthority('Approve Reject News And Alerts')">
																<c:if test="${not empty apporveRejectButton}">
																	<input name="button10" type="button"
																		class="btn btn-success" id="approveNews"
																		value="Submit"
																		onclick="postAction('/approveNewsAlerts');" />

																</c:if>
															</sec:authorize>

															<c:if test="${requestState eq 'A'}">
																<button type="button" class="btn btn-danger"
																	onclick="backAction('N','/getNewsAlertsList');">Back</button>
															</c:if>
															<c:if test="${requestState eq 'I'}">
																<button type="button" class="btn btn-danger"
																	onclick="backAction('N','/getSavedNewsList');">Back</button>
															</c:if>
															<c:if
																test="${requestState eq 'P' or requestState eq 'R' or Approved eq 'Yes'}">
																<button type="button" class="btn btn-danger"
																	onclick="backAction('N','/getPendingNewsAlertsList');">Back</button>
															</c:if>
															<c:if test="${requestState eq 'D'}">
																<button type="button" class="btn btn-danger"
																	onclick="backAction('N','/getDeletedNewsList');">Back</button>
															</c:if>
														</div>
													</div>

												</div>
											</div>
										</div>
									</div>

								</div>
							</div>
						</div>



					</div>

				</div>
			</div>
	</div>
	</form:form>
</div>
</div>
