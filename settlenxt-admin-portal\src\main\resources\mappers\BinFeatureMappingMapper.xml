<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.BinFeatureMappingRepository">
	
<select id="getBinDetailsMain" resultType="binFeatureMappingDTO">
            select R.bin_feature_id as binFeatureId,R.BIN as bin,R.feature_id as featureId,fc.feature as featureName,R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,S.unique_bank_name  as bankName
			,R.from_Date_full as fromDateComplate ,R.to_Date_full as toDateComplate
			from  mcpr_bin_feature_mapping R 
			inner join mcpr_bin_feature_mapping_stg stg on R.bin_feature_id=stg.bin_feature_id
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID 
			inner join  mcpr_feature_fee_config fc on R.feature_id=fc.card_config_id
			where  mb.status!='D' ORDER BY R.last_updated_on desc
</select>



<select id="getPendingBinFeatureMapping" resultType="binFeatureMappingDTO">
	       select   R.bin_feature_id as binFeatureId,R.BIN as bin,R.feature_id as featureId,fc.feature as featureName,R.participant_id,R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate
		  ,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,S.unique_bank_name  as bankName,R.checker_comments as checkerComments,R.last_operation as lastOperation,R.request_state as requestState 
		  ,R.from_Date_full as fromDateComplate ,R.to_Date_full as toDateComplate
		  from  mcpr_bin_feature_mapping_stg R
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
		   INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID
			inner join  mcpr_feature_fee_config fc on R.feature_id=fc.card_config_id
		   where R.REQUEST_STATE in ('P', 'R') and mb.status!='D'
</select>


<insert id="addBinFeatureMapping">
INSERT INTO  mcpr_bin_feature_mapping_stg 
(bin_feature_id,PARTICIPANT_ID,BIN, feature_id,FROM_DATE,TO_DATE,CREATED_BY,CREATED_ON,LAST_UPDATED_BY,
LAST_UPDATED_ON, request_state, last_operation, status,from_Date_full,to_Date_full) VALUES (#{binFeatureId},#{participantName},#{bin}, 
#{featureId}, #{fromDate}, #{toDate},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{requestState}, #{lastOperation}, 
#{status} ,to_date(concat('01',#{fromMonth},#{fromYear}),'ddmmyyyy') ,to_date(concat(case  when #{toMonth} in ('03','12') then '31' else '30' end,
#{toMonth},#{toYear}),'ddmmyyyy'))
</insert>



<select id="getBinFeatureMappingById" resultType="binFeatureMappingDTO">
			SELECT R.bin_feature_id as binFeatureId,R.BIN as bin,R.feature_id as featureId,fc.feature as featureName,R.participant_id as participantId, R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate,R.status as status
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,R.checker_comments as checkerComments,R.last_operation as 
			 lastOperation,R.request_state as requestState,S.unique_bank_name  as bankName
			 ,R.from_Date_full as fromDateComplate ,R.to_Date_full as toDateComplate
			  FROM  mcpr_bin_feature_mapping_stg R
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			inner join  mcpr_feature_fee_config fc on R.feature_id=fc.card_config_id
			 INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID where bin_feature_id = #{binFeatureId} and mb.status!='D'
	
</select>
<select id="getBinFeatureMappingByIdMain" resultType="binFeatureMappingDTO">
			SELECT R.bin_feature_id as binFeatureId,R.BIN as bin,R.feature_id as featureId,fc.feature as featureName,R.participant_id as participantId, R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate,R.status as status
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn , S.unique_bank_name  as bankName
			 ,R.from_Date_full as fromDateComplate ,R.to_Date_full as toDateComplate,stg.request_state as requestState
			  FROM  mcpr_bin_feature_mapping R
			  inner join mcpr_bin_feature_mapping_stg stg on R.bin_feature_id=stg.bin_feature_id
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			inner join  mcpr_feature_fee_config fc on R.feature_id=fc.card_config_id
			 INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID where R.bin_feature_id = #{binFeatureId} and mb.status!='D'
	
</select>


<select id="getPendingBinFeatureMappingById" resultType="binFeatureMappingDTO">
			 SELECT R.bin_feature_id as binFeatureId,R.BIN as bin,R.feature_id as featureId,fc.feature as featureName,R.participant_id as participantId, R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate,R.status as status
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,R.checker_comments as checkerComments,R.last_operation as 
			 lastOperation,R.request_state as requestState,S.unique_bank_name  as bankName
			 ,R.from_Date_full as fromDateComplate ,R.to_Date_full as toDateComplate
			  FROM  mcpr_bin_feature_mapping_stg R
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			inner join  mcpr_feature_fee_config fc on R.feature_id=fc.card_config_id
			 INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID where bin_feature_id = #{binFeatureId} and mb.status!='D'
			
</select>


<insert id="saveBinFeatureMapping">
			INSERT INTO  mcpr_bin_feature_mapping(bin_feature_id,PARTICIPANT_ID,BIN,feature_id,FROM_DATE,TO_DATE,LAST_UPDATED_BY,last_updated_on,created_on,created_by,status,from_Date_full,to_Date_full) 
			values( #{binFeatureId},#{participantId},
			#{bin},#{featureId},#{fromDate},#{toDate},#{lastUpdatedBy},#{lastUpdatedOn},#{createdOn},#{createdBy}, #{status},#{fromDateComplate},#{toDateComplate})
	
</insert>

<update id="updateBinFeatureMapping">
			UPDATE  mcpr_bin_feature_mapping_stg SET BIN =#{bin},feature_id = #{featureId},FROM_DATE = #{fromDate},TO_DATE =#{toDate} 
		   ,LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},REQUEST_STATE =#{requestState} ,
			last_operation = #{lastOperation},checker_comments = '' 
			,from_Date_full=to_date(concat('01',#{fromMonth},#{fromYear}),'ddmmyyyy'),to_Date_full=to_date(concat(case  when #{toMonth} in ('03','12') then '31' else '30' end,#{toMonth},#{toYear}),'ddmmyyyy')
			where bin_feature_id = #{binFeatureId}
</update>

<update id="updateBinFeatureMappingMain">
			UPDATE  mcpr_bin_feature_mapping SET BIN =#{bin},feature_id = #{featureId},FROM_DATE = #{fromDate},TO_DATE =#{toDate} 
		   ,LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},status =#{status} 
			,from_Date_full=#{fromDateComplate},to_Date_full=#{toDateComplate}
			 where bin_feature_id = #{binFeatureId}
</update>

<update id="updateBinFeatureMappingDiscard">
			UPDATE  mcpr_bin_feature_mapping_stg SET BIN =#{bin},feature_id = #{featureId},FROM_DATE = #{fromDate},TO_DATE =#{toDate} 
		   ,LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},REQUEST_STATE =#{requestState} ,
			last_operation = #{lastOperation},checker_comments = #{checkerComments}
			,from_Date_full=#{fromDateComplate},to_Date_full=#{toDateComplate}
			 where bin_feature_id = #{binFeatureId}
</update>

<update id="updateBinFeatureMappingStgState">
			 UPDATE  mcpr_bin_feature_mapping_stg SET LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},REQUEST_STATE =#{requestState} ,last_operation = #{lastOperation},
			checker_comments = #{checkerComments} where bin_feature_id = #{binFeatureId}
</update>



<select id="getBinFeatureMappingStgData" resultType="binFeatureMappingDTO">
		    SELECT R.bin_feature_id as binFeatureId,R.BIN as bin,R.feature_id as featureId,fc.feature as featureName,R.participant_id as participantId, R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate,R.status as status
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,R.checker_comments as checkerComments,R.last_operation as 
			 lastOperation,R.request_state as requestState,S.unique_bank_name  as bankName
			 ,R.from_Date_full as fromDateComplate ,R.to_Date_full as toDateComplate
			  FROM  mcpr_bin_feature_mapping_stg R
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			inner join  mcpr_feature_fee_config fc on R.feature_id=fc.card_config_id
			 INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID where bin_feature_id = #{binFeatureId} and mb.status!='D'
	
</select>

<select id="getBinFeatureMappingMainData" resultType="binFeatureMappingDTO">
		    SELECT R.bin_feature_id as binFeatureId,R.BIN as bin,R.feature_id as featureId,fc.feature as featureName,R.participant_id as participantId, R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate,R.status as status
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,S.unique_bank_name  as bankName
			 ,R.from_Date_full as fromDateComplate ,R.to_Date_full as toDateComplate
			  FROM  mcpr_bin_feature_mapping R
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			inner join  mcpr_feature_fee_config fc on R.feature_id=fc.card_config_id
			 INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID where bin_feature_id = #{binFeatureId} and mb.status!='D'
</select>	


<delete id="deleteDiscardedEntry">
			DELETE FROM  mcpr_bin_feature_mapping_stg WHERE bin_feature_id = #{binFeatureId}
</delete>


<select id="getBegMonths" resultType="binFeatureMappingDTO">
		SELECT TYPE as type ,CODE as code ,DESCRIPTION as description FROM  LOOKUP where type = 'BeginingQtrMonths'
</select>

<select id="getEndMonths" resultType="binFeatureMappingDTO">
		 SELECT TYPE as type ,CODE as code ,DESCRIPTION as description FROM  LOOKUP where type = 'EndingQtrMonths'	
</select>

<select id="getYearsList" resultType="String">
			SELECT DESCRIPTION as description FROM  LOOKUP where type = 'BaseFeeYears'
</select>

<select id="getParticipantList" resultType="binFeatureMappingDTO">
		SELECT distinct UNIQUE_BANK_NAME as participantId, UNIQUE_BANK_NAME as participantName FROM  PARTICIPANT where UNIQUE_BANK_NAME is not null	
</select>



<select id="getBinListForBanks" resultType="binFeatureMappingDTO">
		<!--SELECT BIN_ID as binId , BIN_NUMBER as binNumber FROM  MEMBIN_DETAILS WHERE BIN_NUMBER IS NOT NULL AND PARTICIPANT_ID  in (SELECT PARTICIPANT_ID FROM  PARTICIPANT where UNIQUE_BANK_NAME in (select UNIQUE_BANK_NAME from  PARTICIPANT  where PARTICIPANT_ID=#{participantId}))-->
			SELECT BIN_ID as binId , BIN_NUMBER as binNumber , concat(BIN_NUMBER ,' ( ',ct.description,' -- ',cv.description, ' )' )as binDetail  FROM  MEMBIN_DETAILS r 
				LEFT JOIN lookup ct on R.bin_card_type=ct.code and ct.type='CARD_TYPE' left join lookup cv on R.bin_card_variant=cv.code and cv.type='CARD_VARIANT' 
				WHERE BIN_NUMBER IS NOT NULL AND PARTICIPANT_ID  in (SELECT PARTICIPANT_ID FROM  PARTICIPANT where UNIQUE_BANK_NAME =#{participantId})	and r.status!='D'
</select>

<select id="getFeatureList" resultType="featureFeeDTO">
		SELECT f.card_config_id as cardConfigId,concat(f.feature,' (', f.details,')') as feature 
		FROM mcpr_feature_fee_config f 
		inner join MEMBIN_DETAILS md on f.card_variant=md.bin_card_variant and f.card_type=md.bin_card_type
		where md.bin_number=#{binNumber}  and md.status!='D'
		and ((to_Date::numeric between #{fromDate}::numeric  and #{toDate}::numeric) or (from_Date::numeric between #{fromDate}::numeric  and #{toDate}::numeric))
</select>

<select id="fetchBinFromDb" resultType="binFeatureMappingDTO">
	SELECT * FROM  mcpr_bin_feature_mapping WHERE BIN = #{binNumber}		
</select>



<select id="fetchIdFrombinFeatureIdSequence" resultType="int">
		SELECT nextval('bin_FeatureId_seq')	
</select>



<select id="validateCombination" resultType="binFeatureMappingDTO">
		SELECT bin_feature_id as binFeatureId,bin as bin  FROM  mcpr_bin_feature_mapping_stg WHERE bin_feature_id != #{binFeatureId} and BIN=#{bin} and feature_id=#{featureId}
</select>


<select id="validateDuplicateCheckList" resultType="binFeatureMappingDTO">
		SELECT bin_feature_id, BIN FROM  mcpr_bin_feature_mapping_stg WHERE bin_feature_id != #{binFeatureId} and BIN=#{bin}
		and feature_id=#{featureId}
		and ((from_date_full=to_date(#{fromYearMonth},'ddmmyyyy') and to_date_full=to_date(#{toYearMonth},'ddmmyyyy')) 
		or (to_date(#{fromYearMonth},'ddmmyyyy') between from_date_full and to_Date_full )
		or (to_date(#{toYearMonth},'ddmmyyyy') between from_date_full and to_Date_full ) 
		or (from_date_full between to_date(#{fromYearMonth},'ddmmyyyy') and to_date(#{toYearMonth},'ddmmyyyy') ) 
		or (to_Date_full between to_date(#{fromYearMonth},'ddmmyyyy') and to_date(#{toYearMonth},'ddmmyyyy')  ) )
		
</select>



<select id="validateFromDateList" resultType="binFeatureMappingDTO">
		SELECT bin_feature_id as binFeatureId, BIN as bin FROM  mcpr_bin_feature_mapping_stg  WHERE bin_feature_id != #{binFeatureId} and BIN=#{bin}
	    and to_Date_full= (to_date(#{fromYearMonth},'ddmmyyyy')) -interval '1 day'	
</select>

<select id="getBinFeatureMappingMainById" resultType="binFeatureMappingDTO">
		     SELECT  R.bin_feature_id as binFeatureId,R.participant_id as participantId,R.BIN as bin,R.feature_id as featureId,fc.feature as featureName,R.last_updated_by as lastUpdatedBy,R.from_date as fromDate,R.to_date as toDate
			,R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,S.unique_bank_name  as bankName
			,R.from_Date_full as fromDateComplate ,R.to_Date_full as toDateComplate
			from mcpr_bin_feature_mapping R 
			inner join  MEMBIN_DETAILS mb on R.bin=mb.bin_number
			inner join  mcpr_feature_fee_config fc on R.feature_id=fc.card_config_id
			INNER JOIN  participant S on mb.PARTICIPANT_ID = S.PARTICIPANT_ID WHERE bin_feature_id = #{binFeatureId} and mb.status!='D'
		     
		  
			
</select>

<select id="getParticipantNameById" resultType="String">
		SELECT BANK_NAME as bankName, participant_id as participantId FROM  PARTICIPANT WHERE PARTICIPANT_ID = #{participantId}	
</select>



	<update id="updatedeletestatusbinFeatureMapping">
		UPDATE mcpr_bin_feature_mapping_stg SET 
		LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status},request_state =#{requestState}
		,last_operation= #{lastOperation}, CHECKER_COMMENTS='' WHERE bin_feature_id = #{binFeatureId}
	</update>


	<delete id="deleteBinFeatureMappingMain">
		delete from mcpr_bin_feature_mapping WHERE bin_feature_id = #{binFeatureId}
	</delete>
	<delete id="deleteBinFeatureMappingStg">
		delete from mcpr_bin_feature_mapping_stg WHERE bin_feature_id = #{binFeatureId}
	</delete>



</mapper>