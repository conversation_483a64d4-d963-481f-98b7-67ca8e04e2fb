var entry = {};
var allPages = 0;
$(document).ready(function() {
	$('#transitionErrorMsg').hide();
	$('#transitionErrorMsg1').hide();

	var cursorPosition = null;
	/* Initialization of datatables */
	$(document).ready(function() {

		var myTable = $("#tabnew").DataTable({

			initComplete: function() {
				var api = this.api();

				// For each column
				api
					.columns()
					.eq(0)
					.each(function(colIdx) {
						//If first column to be skipped to include the filter for the reasons line check box 
						if (!(colIdx == 0 && firstColumnToBeSkippedInFilterAndSort)) {
							// Set the header cell to contain the input element
							var cell = $('#tabnew thead tr th').eq(
								$(api.column(colIdx).header()).index()
							);
							var title = $(cell).text();
							cursorPosition = searchBoxFunc(colIdx, cell, title, api, cursorPosition);
						}
					});
				$('#tabnew_filter').hide();
			},

			// Disabled ordering for first column in case
			columnDefs: [
				{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
			],
			"order": [],
			dom: 'lBfrtip',
			buttons: [

				{
					extend: 'excelHtml5',
					text: 'Export',
					filename: 'JSON VALIDATOR',
					header: 'false',
					title: null,
					sheetName: 'JSON VALIDATOR',
					className: 'defaultexport',
					exportOptions: {
						columns: 'th:not(:last-child)'
					}
				},
				{
					extend: 'csvHtml5',
					text: 'Export',
					filename: 'JSON VALIDATOR',
					header: 'false',
					title: null,
					sheetName: 'JSON VALIDATOR',
					className: 'defaultexport',
					exportOptions: {
						columns: 'th:not(:last-child)'
					}
				}

			],

			searching: true,
			info: true,
			lengthChange: true,
			bLengthChange: true,
		});
		allPages = myTable.rows().nodes();
		
		$("#excelExport").on("click", function () {
	        $(".buttons-excel").trigger("click");
	    });
	    
	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
	 
	     $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
		
		$("#selectAll1").click(function() {
			if ($(this).hasClass('allChecked')) {
				$('.selectedId', allPages).prop('checked', false);
			} else {
				$('.selectedId', allPages).prop('checked', true);
			}

			$(this).toggleClass('allChecked');

			var footer = document.getElementById("ruless");
			
			
			var temp = document.getElementById("detailsHeaderss").value ;
			footer.innerHTML = $('<div>').text(temp).html()+ "     " + "records are selected";
			
		
			showAndHideModel();

		});
	});
});


function showAndHideModel() {
    if (document.getElementById("detailsHeaderss").value > 0) {
        if ($('#selectAll1').is(':checked')) {
            $("#toggleModal1").modal('show');
        }
        else {
            $("#toggleModal1").modal('hide');
        }
    }
    else {
        if ($('#selectAll').is(':checked')) {
            $('#transitionErrorMsg').show();
        }
        else {
            $('#transitionErrorMsg').hide();
        }
    }
}

function searchBoxFunc(colIdx, cell, title, api, cursorPosition) {
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function() {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})|([*])';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value.replace('*', '\\*') + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
    return cursorPosition;
}

function viewMcc(seqId, type) {
	var url;
	if (type == 'V')
		url = '/editJsonValidator';
	else if (type == 'P')
		url = '/getJsonValidator';
	else if (type == 'G')
		url = '/getPendingJsonValidator';
	
var data = "seqId," + seqId ;
	postData(url, data);
}



function submitForm(url) {
	
	var data = "userType," + $('#userType').val();
	postData(url, data);
}

function backAction(type, action) {
	var url = action;
	
	var data = "userType," + type ;
	postData(url, data);
}

function deselectAll() {
	$('#selectAll1').prop('checked', false);
	var ele = document.getElementsByName('type');
	for (let val of ele) {
		if (val.type == 'checkbox')
			val.checked = false
	}
}

function approveorRejectBulk(type) {
	var url = '/bulkApproveJsonValidator';
	var LoginIdList = "";
	$('.selectedId', allPages).filter(":checked").each(function() {
		LoginIdList = LoginIdList + $(this).val() + "|";
		console.log(LoginIdList);
	});
	if (LoginIdList === "") {
		$('#transitionErrorMsg1').show();
	} else {
		var data = "status," + type + ",jsonValidatorList," + LoginIdList.slice(0, -1);
		postData(url, data);
	}
}



