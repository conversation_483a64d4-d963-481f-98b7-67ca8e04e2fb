<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/viewFunctionCode.js"
	type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">
		 <form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewMccTipSurcharge" modelAttribute="functionCodeDto"
			action="${approveFunctionCodeStatus}" autocomplete="off"> 
			<input type="hidden" id="funcCodeId" value="${functionCodeDto.funcCodeId}" />
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="functionCode.viewscreen.title" /></span></strong>
						</div>
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">Function Code</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
										
									<td><label><spring:message code="functionCode.mti" /></label></td>
									<td>${functionCodeDto.mti }</td>
									<td><label><spring:message code="functionCode.procCode" /></label></td>
									<td >${functionCodeDto.procCode }</td>
									<td><label><spring:message code="functionCode.funcCode" /></label></td>
									<td >${functionCodeDto.funcCode }</td>
									<td></td>
									<td></td>
									</tr>
									<tr>
										
									<td><label><spring:message code="functionCode.funcCodeDesc" /></label></td>
									<td>${functionCodeDto.funcCodeDesc }</td>
									<td><label><spring:message code="functionCode.feeType" /></label></td>
									<td >${functionCodeDto.feeType }</td>
									<td><label><spring:message code="functionCode.fundMovement" /></label></td>
									<td >${functionCodeDto.fundMovement }</td>
									<td></td>
									<td></td>
									</tr>
									<tr>
										
									<td><label><spring:message code="functionCode.fundMovementSide" /></label></td>
									<td>${functionCodeDto.fundMovementSide }</td>
									<td><label><spring:message code="functionCode.recalculate" /></label></td>
									<td >${functionCodeDto.recalculate }</td>
									<td><label><spring:message code="functionCode.transactionType" /></label></td>
									<td >${functionCodeDto.transactionType }</td>
									<td><label><spring:message code="functionCode.networkTxnType" /></label></td>
									<td >${functionCodeDto.networkTxnType }</td>
									</tr>
									
								</tbody>
							</table>
						</div>
					</div>
				</div>
					</div>	
		</form:form>
		<div class="row">
			<div class="col-sm-12 bottom_space ">
				<hr />
				<div style="text-align:center">
					<button type="button" class="btn btn-danger"
						onclick="userAction('N','/functionCodeMain');">
						<spring:message code="functionCode.backBtn" /></button>
					<c:if test="${functionCodeDto.requestState =='A' }">	
					<sec:authorize access="hasAuthority('Edit Function Code')">
						<input name="editButton" type="button" class="btn btn-success"
						 id="approveRole" value="Edit" 
						onclick="EditFunctionCode('/editFunctionCode','${functionCodeDto.funcCodeId}');"/>
					</sec:authorize>
					</c:if>

				</div>
			</div>
		</div>
	</div>

</div>
