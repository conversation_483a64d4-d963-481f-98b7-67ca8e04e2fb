$(document).ready(function() {
	
	 $('#certTable').dataTable({
		responsive: true,
		bSort:false
	});
	
	loadDataTable();
	
});

function loadDataTable() {
	
	$("#certTable").dataTable({
		"bServerSide": true,
		"bSort":false,
		"bFilter" : true,
		"sAjaxSource": "fetchCert",
		"bProcessing": true,
		"bJQueryUI": true,
		"sServerMethod": "POST",
		"bDestroy": true,
		"bAutowidth": false,
		"bScrollCollapse": true,
		"pageLength": 10,
		"searching": false,
		"lengthChange": false,
		"fnServerParams": function ( aoData ) {
			aoData.push( { "name": "orgId", "value": $('#orgId').val() == "" ? "" : $('#orgId').val() },
					{ "name": "_TransactToken", "value": document.getElementsByName("_TransactToken")[0].value}
			);
		},
		"fnServerData": function ( sSource, aoData, fnCallback ) {
			$.ajax({
				"dataType": 'json',
				"type": "POST",
				"url": sSource,
				"data": aoData,
				"success": function(json){
					
					var errorMessage=json.errorMessage;
					var errorStatus=json.errorStatus;
					fnCallback(json);
					if(errorMessage != "" || errorStatus !="")
					{
						
						$('.alert-danger').html(errorMessage);
						$('.alert-danger').show();
						
					}
					
					else
					{
						$('.alert-danger').html('');
						$('.alert-danger').hide();
						$('.trn').show();
					}
				}
			});               
		}
	});
}