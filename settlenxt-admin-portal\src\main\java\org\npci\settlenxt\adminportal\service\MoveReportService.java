package org.npci.settlenxt.adminportal.service;

import java.io.File;
import java.util.Date;
import java.util.List;

import org.npci.settlenxt.adminportal.dto.MoveReportDTO;
import org.npci.settlenxt.adminportal.model.MoveReportModel;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;

public interface MoveReportService {

	void saveMoveReportToDatabase(MoveReportDTO moveReportDTO,String reportNetwork, String year, String month, MoveReportModel moveReportModel);

	List<MoveReportDTO> getReportList();

	void addDefaultData(MoveReportDTO moveReportDTO, Date fromDateStr, Date toDateStr, String reportNetwork);

	List<MoveReportDTO> searchReportList(MoveReportDTO movereportDTO);

	void moveReportRequest(MoveReportDTO movereportDTO,String reportNetwork, String[] date, MoveReportModel moveReportModel,File[] files,String path,String destPath);

	List<MoveReportDTO> getMonthFromLookup();

	List<CodeValueDTO> getMonths();

	int getYear(String year);

}
