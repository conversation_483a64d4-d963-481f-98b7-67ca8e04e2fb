package org.npci.settlenxt.adminportal.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.gateway.RestGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

/**
 * Service used for
 * <li>Fetch Report Progress Status</li>
 * 
 * <AUTHOR>
 *
 */
@Transactional(rollbackFor = Throwable.class)
@Service
public class ReportProgressStatusServiceImpl implements IReportProgressStatusService {

	private static final Logger logger = LogManager.getLogger(ReportProgressStatusServiceImpl.class);

	@Autowired
	private Environment environment;

	@Autowired
	RestGateway restGateway;

	/**
	 * Get the report status of summary reports
	 */
	@Override
	public CycleManagementDTO getReportProgressStatusList(CycleManagementDTO cycleManagementDTO) {
		try {
			JsonObject requestBody = getReportProgressJsonRequest(cycleManagementDTO);

			String url = environment.getProperty("REPORT_ORCHESTRATION_DOMAIN") + CommonConstants.SETTLENXT_ALL_REPORTS;

			List<Map<String, String>> cycleDetails = new ArrayList<>();
			
			String result = restGateway.postAPIForJsonObject(url, requestBody);
			logger.info("Value for response : {}", result);
			if (StringUtils.isBlank(result)) {
				cycleManagementDTO.setCycleData(cycleDetails);
				return cycleManagementDTO;
			}
			JsonArray respBody = (JsonArray) JsonParser.parseString(result);
			cycleDetails = new Gson().fromJson(respBody, ArrayList.class);
			
		
			Collections.sort(cycleDetails,(o1,o2)-> o1.get("cycleNumber").compareTo(o2.get("cycleNumber")));
			Collections.sort(cycleDetails, (o1,o2)-> o1.get("viewPriority").compareTo(o2.get("viewPriority")));
			
			cycleManagementDTO.setCycleData(cycleDetails);
		} catch (Exception e) {
			logger.error("Error occured while fetching report status list from RO : {}", e.getMessage(), e);
		}
		return cycleManagementDTO;
	}

	private JsonObject getReportProgressJsonRequest(CycleManagementDTO cycleManagementDTO) {

		if (StringUtils.isBlank(cycleManagementDTO.getCycleDate())) {
			LocalDateTime date = LocalDateTime.now();
			DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
			cycleManagementDTO.setCycleDate(date.format(dateTimeFormatter));
		}
		if (StringUtils.isBlank(cycleManagementDTO.getBankType())) {
			cycleManagementDTO.setBankType("NPCI");
		}
		if (cycleManagementDTO.getReportStatusArr() == null || cycleManagementDTO.getReportStatusArr().length == 0) {
			String[] reportStatusArr = new String[] { "FAILED" };
			cycleManagementDTO.setReportStatusArr(reportStatusArr);
		}
		if (StringUtils.isBlank(cycleManagementDTO.getCycleNumber())) {
			cycleManagementDTO.setCycleNumber("C1");
		}
		if (StringUtils.isBlank(cycleManagementDTO.getSettlementProductId())) {
			cycleManagementDTO.setSettlementProductId("RPY");
		}
		JsonObject requestBody = new JsonObject();
		requestBody.addProperty(CommonConstants.CYCLE_DATE, cycleManagementDTO.getCycleDate());
		requestBody.addProperty(CommonConstants.BANK_TYPE, cycleManagementDTO.getBankType());
		requestBody.addProperty(CommonConstants.REPORT_STATUS,
				String.join(",", cycleManagementDTO.getReportStatusArr()));
		requestBody.addProperty(CommonConstants.CYCLE_NUMBER, cycleManagementDTO.getCycleNumber());
		requestBody.addProperty(CommonConstants.SETTLEMENT_PRODUCT_ID, cycleManagementDTO.getSettlementProductId());
		return requestBody;
	}

	/**
	 * Fetch the product id with respective cycle numbers
	 */
	@Override
	public String fetchProductIdDetails(CycleManagementDTO cycleManagementDTO) {
		String result = "";
		try {
			JsonObject requestBody = new JsonObject();
			String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
					+ CommonConstants.SETTLENXT_FETCH_PRODUCT_ID_DETAILS;
			result = restGateway.postAPIForJsonObject(url, requestBody);
			logger.info("Response of product id And there respective cycle numbers {}", result);
		} catch (Exception e1) {
			logger.error("Error occured while getting report cycle status {}", e1.getMessage(), e1);
		}
		return result;
	}
}
