package org.npci.settlenxt.adminportal.controllers;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.ReportArtifactService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.util.FileNameUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class ReportArtifactController extends BaseController {

	@Autowired
	SessionDTO sessionDTO;

	@Autowired
	private ReportArtifactService reportArtifactSvc;

	
	private static final String FOLDERS = "folders";
	private static final String FILES = "files";
	private static final String FOLDER_PATH = "folderpath";
	private static final String VIEW_REPORTS_JSP = "viewReportsArtifact";
	private static final String BACK = "back";
	private static final String ESCAPES = "../";
	private static final String CRLF_CHAR = "\n";
	private static final String ESCAPE = "..";
	
	private static final String OLD_REPORTS_ARTIFACT_DOWNLOAD = "OLD_REPORTS_ARTIFACT_DOWNLOAD";

	@PostMapping("/viewOldReportsArtifact")
	@PreAuthorize("hasAuthority('View Report Artifact')")
	public String viewOldReportsCert(Model model, HttpServletRequest request) {
		String path = env.getProperty(OLD_REPORTS_ARTIFACT_DOWNLOAD);
		
		if (null != path && StringUtils.isNotBlank(path)) {
			path = path.replace(ESCAPES, "").replace(ESCAPE, "").replace(CRLF_CHAR, "");
		}
		List<String> foldersList = reportArtifactSvc.getFolders(path);
		model.addAttribute(FOLDERS, foldersList);
		model.addAttribute(FOLDER_PATH, path);

		return getView(model, VIEW_REPORTS_JSP);
	}

	@PostMapping("/openFolder")
	@PreAuthorize("hasAuthority('View Report Artifact')")
	public String openFolder(Model model, HttpServletRequest request, HttpServletResponse response,
			@RequestParam String filePath) {

		try {
			List<String> foldersList = null;
			List<String> fileList = null;

			if (null != filePath && StringUtils.isNotBlank(filePath)) {
				filePath = FileNameUtil.sanitizeFileNameReportArtifact(filePath);
				foldersList = reportArtifactSvc.getFolders(filePath);
				fileList = reportArtifactSvc.getFiles(filePath);
				
				model.addAttribute(FOLDERS, foldersList);
				model.addAttribute(FILES, fileList);
				model.addAttribute(FOLDER_PATH, filePath);

				String path = env.getProperty(OLD_REPORTS_ARTIFACT_DOWNLOAD);
				if (null != path && StringUtils.isNotBlank(path)) {
					path = FileNameUtil.sanitizeFileNameReportArtifact(path);
					model.addAttribute(BACK, true);
					if (path.equals(filePath)) {
						model.addAttribute(BACK, false);
					}

				}
			}

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_REPORTS_JSP, ex);
		}
		return getView(model, VIEW_REPORTS_JSP);

	}

	@PostMapping("/downloadReportFile")
	@PreAuthorize("hasAuthority('View Report Artifact')")
	public void downloadReportFile(Model model, HttpServletRequest request, HttpServletResponse response,
			@RequestParam String filePath) {

		try {
			if (null != filePath && StringUtils.isNotBlank(filePath)) {
				
				filePath = FileNameUtil.sanitizeFileNameReportArtifact(filePath);
				reportArtifactSvc.downloadFile(filePath, request, response);
			}
		} catch (Exception ex) {

			handleErrorCodeAndForward(model, VIEW_REPORTS_JSP, ex);

		}
	}

}