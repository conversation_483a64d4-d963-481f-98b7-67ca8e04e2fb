function fileUpload() {
	const input = document.querySelector('#file');
	const allowedFileNameExtensions=[".pdf", ".jpg", ".doc", ".docx", ".csv", ".png", ".txt", ".jpeg",".bmp", ".tiff", ".tif" ];
	 $("#errfileupload").find('.error').html('');
     $("#errfileupload").hide();
     var invalidFileNames=[];
     var invalidFileNameArr=[];
     let isValidFileName = false;
     var fileNamePattern = /^[a-zA-Z0-9()%&\-_. ,]+$/;
     var i;
     var fileName;
     var extnIndex;
     for ( i of input.files) {		
		fileName= i.name;
		 isValidFileName = fileNamePattern.test(fileName);
 		if(!isValidFileName) {
 			invalidFileNameArr.push(fileName);
 		}
		extnIndex=allowedFileNameExtensions.findIndex(extn =>  fileName.toUpperCase().endsWith(extn.toUpperCase()));
		if(extnIndex==-1){
			invalidFileNames.push(fileName);		   
		}
	 }
	  if(invalidFileNames.length>0){
	      $("#errfileupload").find('.error').html('Invalid file type for '+invalidFileNames.map(item => item).join());
		    $("#errfileupload").show();
		    return;
	  }else if(invalidFileNameArr.length>0)  {
		  $("#errfileupload").find('.error').html('Invalid file Name for '+invalidFileNameArr.map(item => item).join());
		    $("#errfileupload").show();
		    return;
	 	  }
		  
	for ( i of input.files) {
		
		fileName= i.name;
		 extnIndex=allowedFileNameExtensions.findIndex(extn =>  fileName.toUpperCase().endsWith(extn.toUpperCase()));
		if(extnIndex==-1){
		    $("#errfileupload").html('Invalid file type for '+fileName);
		    $("#errfileupload").show();
		    return;
		}
		var documentIndex=documentData.findIndex(obj => obj.documentName == fileName && obj.status == 'A');
		var documentModel = documentData[documentIndex];
		if(documentModel){
		 	documentModel.fileContent = i;
		}else{
			documentModel={};
			documentModel.documentName =i.name;
			documentModel.fileContent = i;
			documentModel.isNew = true;
			documentModel.status = 'A';
			documentModel.documentId=0;
			documentData.push(documentModel);
		}
	}
	input.value = '';
	renderFileTable();
}
function resetDocumentData() {
	documentData.forEach(function (documentModel) {
		documentModel.isNew = false;
	});
	documentData = documentData.filter(obj => obj.status != 'D');
	renderFileTable();
}
function deleteFile(fileName) {
openConfirmDialog('Do you want to delete the selected document?', deleteFileAction, fileName);
}
function deleteFileAction(fileName) {
    var documentIndex=documentData.findIndex(obj => obj.documentName == fileName);
	var documentModel = documentData[documentIndex];
	if(documentModel){
		if (documentModel.isNew) {
			documentData.splice(documentIndex, 1);
		} else {
			documentModel.status = 'D';
		}
	}
	renderFileTable();
}

function deleteMemberDocumentBatch() {
	
	
	documentData = documentData.filter(obj => !obj.isNew);
	for (var i = 0; i < documentData.length; ++i) {
		var documentModel = documentData[i];
		if(documentModel){
			if (documentModel.isNew) {
				documentData.splice(i, 1);
			} else {
				documentModel.status = 'D';
			}
		}
	}
	renderFileTable();
}
function disableAllDeteleDownload(){
	var flag=0;
	for (var i of documentData) {
		var documentModel = i;
		if(documentModel.status == 'A'){
			flag=1;
		}
	}
	if(flag==0)
	{
		if (typeof downloadFiles != "undefined") {
			document.getElementById("downloadFiles").disabled = true;
		}
		if (typeof deleteAllFiles != "undefined") {
			document.getElementById("deleteAllFiles").disabled = true;
		}
	}
	else
	{
		if (typeof downloadFiles != "undefined") {
			document.getElementById("downloadFiles").disabled = false;
		}
		if (typeof deleteAllFiles != "undefined") {
			document.getElementById("deleteAllFiles").disabled = false;
		}
	}	
}

function renderFileTable() {
	var fileTableData = "";
	for (var i of documentData) {
		var documentModel = i;
		if (documentModel.status != 'D') {
			var statusDesc = documentModel.isNew ? 'To be uploaded' : 'Uploaded';
			var rowData = '<tr><td>' ;
			var downloadLink= documentModel.isNew ? '' : ' &nbsp; &nbsp; <a href="javascript:downloadMemberDocument(\'' + documentModel.documentName + '\')"><span class="glyphicon glyphicon-download my-tooltip" title="DOWNLOAD"></span></a>';
			if(!documentModel.isNew)
			{
				 rowData+='<input type="checkbox" name="selectMemberDocument" id="' + documentModel.documentName + '"  onclick="toggleCheckAllDocuments()"/>' ;
			 }			   
			rowData+='</td><td>' + documentModel.documentName + '</td>' +
				'<td> ' + statusDesc + '</td>' +
				'<td><a href="javascript:deleteFile(\'' + documentModel.documentName + '\')"><span class="glyphicon glyphicon-trash my-tooltip" title="DELETE"></span></a> '+downloadLink+'</td></tr>';
			fileTableData += rowData;
		}
	}
	
	$('#documentList').html(fileTableData);
	 $('#checkAllDocument').prop("checked", false);
	unableSave();
	disableAllDeteleDownload();
}
function toggleCheckAllDocuments(){
  var checkBoxElements=document.getElementsByName("selectMemberDocument");
  let checkedCount=0;
   for(var i of checkBoxElements){
     if(i.checked){
       checkedCount++;
     }
   }
   if(checkedCount>0){
   $('#downloadFiles').attr('disabled',false);
   }else{
    $('#downloadFiles').attr('disabled',true)
   }
   if(checkedCount==checkBoxElements.length){
   $('#checkAllDocument').prop("checked",true);
   }else{
   $('#checkAllDocument').prop("checked", false);
   }
}



function deleteFiles() {
openConfirmDialog('Do you want to delete all Files?', deleteMemberDocumentBatch);
}