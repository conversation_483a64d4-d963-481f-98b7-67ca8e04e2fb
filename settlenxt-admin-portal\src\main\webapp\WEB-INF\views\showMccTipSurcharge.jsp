<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript">
	var actionColumnIndex = 3;
	var firstColumnToBeSkippedInFilterAndSort = false;
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 7;
	firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 6;
	firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
</script>

<script>
	var referenceNoListPendings = [];
	<c:if test="${not empty pendingMccTipSurchargeList}">
	<c:forEach items="${pendingMccTipSurchargeList}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	referenceNoListPendings.push('${operator.mccTipSurchargeId}');
	</c:if>
	</c:forEach>
	</c:if>
</script>
<script src="./static/js/validation/showMccTipSurcharge.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="./static/js/jszip.min.js"></script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js"></script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: bottom;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>
<!-- Model -->
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalNews" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Mcc Tip
					Surcharge Approval/Rejection</label>
				<p id="newsIds" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulkMccTipSurcharge('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulkMccTipSurcharge('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Model -->
<!-- Model -->
<input:hidden id="refNum" />
<div class="row">


	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/mccTipSurchargeMain');"
			role="tab" data-toggle="tab"><span
			class="glyphicon glyphicon-credit-card"> </span> <spring:message
				code="mccTipSurcharge.mainTab.title" /></a>

		<c:choose>
			<c:when test="${pendingAppMccTipSurcharge eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" role="tab" onclick="getPendingMccTipSurchargeList();"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> </span> <spring:message
				code="mccTipSurcharge.approvalTab.title" /></a>

	</ul>


	<div class="tab-content">
		<div role="tabpanel" class="tab-pane active" id="home">
			<c:if test="${showMainTab eq 'Yes'}">
				<div class="row">
					<div class="col-sm-12">
						<sec:authorize access="hasAuthority('Add MCC Tip Surcharge')">
							<c:if test="${addMccTipSurcharge eq 'Yes'}">
								<a class="btn btn-success pull-right btn_align" href="#"
									onclick="submitForm('/mccTipSurchargeCreation','P');"
									style="margin: -5px 0px 2px 0px;"><em
									class="glyphicon-plus"></em> <spring:message
										code="mccTipSurcharge.addMccTipSurchargeBtn" /></a>
							</c:if>
						</sec:authorize>
						<div class="row">
							<div class="col-sm-12">
								<button class="btn  pull-right btn_align" id="clearFilters">
									<spring:message code="mccTipSurcharge.clearBtn" />
								</button>
								&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
									id="csvExport"> <spring:message
										code="mccTipSurcharge.csvBtn" />
								</a> <a class="btn btn-success pull-right btn_align" href="#"
									id="excelExport"><spring:message
										code="mccTipSurcharge.exportBtn" /> </a>
							</div>
						</div>
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="mccTipSurcharge.listscreen.title" /></span></strong>
							</div>
							<div class="panel-body">
								<div class="row">
									<div class="col-sm-12"></div>
								</div>

								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Mcc Tip Surcharge</caption>
										<thead>
											<tr>
												<th scope="col"><label><spring:message
															code="mccTipSurcharge.mccTipSurchargeId" /></label></th>
												<th scope="col"><label><spring:message
															code="mccTipSurcharge.tipSurchargeId" /></label></th>
												<th scope="col"><label><spring:message
															code="mccTipSurcharge.mccId" /></label></th>
											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty mccTipSurchargeList}">
												<c:forEach var="mccTipSurcharge"
													items="${mccTipSurchargeList}">

													<tr>
														<td
															onclick="javascript:viewTipSurcharge('${mccTipSurcharge.mccTipSurchargeId}','V')">${mccTipSurcharge.mccTipSurchargeId}</td>
														<td
															onclick="javascript:viewTipSurcharge('${mccTipSurcharge.mccTipSurchargeId}','V')">${mccTipSurcharge.tipSurchargeLookup}</td>
														<td
															onclick="javascript:viewTipSurcharge('${mccTipSurcharge.mccTipSurchargeId}','V')">${mccTipSurcharge.mccNameLookup}</td>
													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>
			<c:if test="${showApprovalTab eq 'Yes'}">
				<div class="row">
				<div class="col-sm-12">
										<button class="btn  pull-right btn_align" id="clearFilters">
											<spring:message code="mccTipSurcharge.clearBtn" />
										</button>
										&nbsp; <a class="btn btn-success pull-right btn_align"
											href="#" id="csvExport"> <spring:message
												code="mccTipSurcharge.csvBtn" />
										</a> <a class="btn btn-success pull-right btn_align" href="#"
											id="excelExport"><spring:message
												code="mccTipSurcharge.exportBtn" /> </a>
									</div>
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="mccTipSurcharge.listscreen.title" /></span></span></strong>
								<c:if test="${not empty pendingMccTipSurchargeList}">
									<sec:authorize
										access="hasAuthority('Approve MCC Tip Surcharge')">
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkMccTipSurcharge('A','No')"
											id="submitButtonA"
											value="<spring:message code="feeRate.Approve" />" />
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkMccTipSurcharge('R','No')"
											id="submitButtonR"
											value="<spring:message code="feeRate.Reject" />" />
									</sec:authorize>
								</c:if>
							</div>
							<div class="panel-body">
								
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Mcc Tip Surcharge</caption>
										<thead>
											<tr>
												<sec:authorize
													access="hasAuthority('Approve MCC Tip Surcharge')">
													<th scope = "col"><input type=checkbox name='selectAllCheck'
														id="selectAll" data-target="toggleModalNews" value="All"></input></th>
												</sec:authorize>
												<th scope="col"><label><spring:message
															code="mccTipSurcharge.mccTipSurchargeId" /></label></th>
												<th scope="col"><label><spring:message
															code="mccTipSurcharge.tipSurchargeId" /></label></th>
												<th scope="col"><label><spring:message
															code="mccTipSurcharge.mccId" /></label></th>
												<th scope="col"><label><spring:message
															code="mccTipSurcharge.requestType" /></label></th>
												<th scope="col"><label><spring:message
															code="mccTipSurcharge.status" /></label></th>
												<th scope="col"><label><spring:message
															code="mccTipSurcharge.checkerComents" /></label></th>
											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty pendingMccTipSurchargeList}">

												<c:forEach var="mccTipSurcharges"
													items="${pendingMccTipSurchargeList}">
													<tr>
														<sec:authorize
															access="hasAuthority('Approve MCC Tip Surcharge')">
															<c:if test="${mccTipSurcharges.requestState =='P' }">
																<td><input type=checkbox name='type'
																	id="selectSingle" onclick="mySelect();"
																	value="${mccTipSurcharges.mccTipSurchargeId}"></input></td>
															</c:if>
															<c:if test="${mccTipSurcharges.requestState !='P' }">
																<td></td>
															</c:if>
														</sec:authorize>
														<td
															onclick="javascript:viewTipSurcharge('${mccTipSurcharges.mccTipSurchargeId}','P')">${mccTipSurcharges.mccTipSurchargeId}</td>
														<td
															onclick="javascript:viewTipSurcharge('${mccTipSurcharges.mccTipSurchargeId}','P')">${mccTipSurcharges.tipSurchargeLookup}</td>
														<td
															onclick="javascript:viewTipSurcharge('${mccTipSurcharges.mccTipSurchargeId}','P')">${mccTipSurcharges.mccNameLookup}</td>
														<td
															onclick="javascript:viewTipSurcharge('${mccTipSurcharges.mccTipSurchargeId}','P')">${mccTipSurcharges.lastOperation}</td>
														<td
															onclick="javascript:viewTipSurcharge('${mccTipSurcharges.mccTipSurchargeId}','P')">
															<c:if test="${mccTipSurcharges.requestState =='A' }">
																<spring:message
																	code="mccTipSurcharge.requestState.approved.description" />
															</c:if> <c:if test="${mccTipSurcharges.requestState =='P' }">
																<spring:message
																	code="mccTipSurcharge.requestState.pendingApproval.description" />
															</c:if> <c:if test="${mccTipSurcharges.requestState =='R' }">
																<spring:message
																	code="mccTipSurcharge.requestState.rejected.description" />
															</c:if> <c:if test="${mccTipSurcharges.requestState =='D' }">
																<spring:message
																	code="mccTipSurcharge.requestState.discared.description" />
															</c:if>
														</td>
														<td
															onclick="javascript:viewTipSurcharge('${mccTipSurcharges.mccTipSurchargeId}','P')">${mccTipSurcharges.checkerComments}</td>
													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>
		</div>
	</div>
</div>
