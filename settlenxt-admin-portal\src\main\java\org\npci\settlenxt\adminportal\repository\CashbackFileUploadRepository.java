package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.portal.common.dto.CashBackFileUploadDTO;

@Mapper
public interface CashbackFileUploadRepository {

	 void saveFile(CashBackFileUploadDTO cashbackDTO);
	
	 List<CashBackFileUploadDTO> getFileUploadByYearMonth(String year, String month);
	
	 List<CashBackFileUploadDTO> getAllCashBackFiles();
	
	 List<CashBackFileUploadDTO> getPendingCashBackFiles(String site,String instance);
	
	 void updateFile(Integer fileId, String status);
	
	 void saveCashBackFile(CashBackFileUploadDTO cashbackDTO);
}
