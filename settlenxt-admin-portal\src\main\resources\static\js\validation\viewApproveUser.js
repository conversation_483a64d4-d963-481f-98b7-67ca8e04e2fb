	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	

	function userAction(_type, action) {
		var url = action;
		var uid = document.getElementById("userId").value;
		var data = "uid," + uid  + ",status,"
				+ status+ ",userType," + $('#userType').val();
		postData(url, data);
	}

	function display() {
		$(".appRejMust").hide();
	}
	function backAction(type, action) {
		var url = action;
		var data = "userType," + type ;
		postData(url, data);
	}
	
	function postAction(_action) {
		var crtuser;
		var data;
		var remarks;
		var url;
		var userId;
	
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
			document.querySelector(".button").disabled = true;
				crtuser = $("#crtuser").val();
				userId = $("#userId").val();
				remarks = $("#rejectReason").val();
		
				url = '/approveUserStatus';
				data = "status," + "A" + ",crtuser,"
						+ crtuser + ",remarks,"
						+ remarks+ ",userId,"
						+ userId;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
			document.querySelector(".button").disabled = true;
				postRejectedData(crtuser, remarks, userId, url, data);
	
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}


function postRejectedData(crtuser, remarks, userId, url, data) {
    if ('${requestInfo.requestName}' == 'USER FUNCTIONALITY MAP') {
        crtuser = $("#crtuser").val();
        remarks = $("#rejectReason").val();
        userId = $("#userId").val();
        url = '/rejectUserStatus';

        data = "status," + "R"
            + ",crtuser," + crtuser + ",rejectReason," + remarks + ",userId,"
            + userId;

        postData(url, data);
    }
    else {
        crtuser = $("#crtuser").val();
        remarks = $("#rejectReason").val();
        userId = $("#userId").val();
        url = '/approveUserStatus';

        data = "status," + "R"
            + ",crtuser," + crtuser + ",remarks," + remarks + ",userId,"
            + userId;
        postData(url, data);
    }
   
}
	