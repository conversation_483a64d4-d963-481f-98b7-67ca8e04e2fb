$(document).ready(function () {
  	setAttributes();
	
		$('#allowedActncdToRemove').multiselect({
		buttonWidth: '280px',
		paddingLeft: '0px',
		paddingRight: '15px',
		nonSelectedText: 'Select Allowed Action Code To Remove',
		includeSelectAllOption: true,
		enableCaseInsensitiveFiltering: true,	});
	
	
	forEditFlow();
	
	if($('#addFlow').val() == "Y" && $('#showButton').val()!= "Y"){
	$('#tatPeriod').val("");}


$('#allowedActncdToRemove').on('keyup keypress blur change',function () {
unableSave();
  var actncd = $('#allowedActncdToRemove option:selected').toArray().map(item => item.value).join();
				
				var arr = actncd.split(',');
			    if( actncd==null || arr.length==0 || actncd==""){
					  
			    	
					$("#errallowedActncdToRemove").find('.error').html("");
					
						}
				 else{
						$("#errallowedActncdToRemove").hide();
						$("#errallowedActncdToRemove").find('.error').html('');
				 }
     
	});


disableSave();
$('#clearActionCode').click(function()
    			{
    		
    	
    	 	 $('#mti').val("0");
    	  	$('#funcCode').val("0");
    		$('#actionCode').val("");
    		$('#raisedBy').val("0");
    		$('#funcCodeDesc').val("");
    		$('#actionCodeDesc').val("");
    		$('#tatPeriodDayType').val("0");
    		
    		document.getElementById('capAmtCalReq').checked = false;
$("#tatPeriod").val("");
$("#transitionActionCode").val("0");
$("#defaultReasonRejCode").val("0");
$("#allowedActncdToRemove").multiselect("clearSelection");
$("#allowedActncdToRemove").multiselect( 'refresh' );
    	 	 $("#errmti").hide();
 		    $("#erractionCode").hide();
 		    $("#errraisedBy").hide();
 		    $("#errfuncCode").hide();
 		    $("#errfuncCodeDesc").hide();
 		    $("#erractionCodeDesc").hide();
 		    $("#errtatPeriodDayType").hide();
$("#errtatPeriod").hide();
$("#errtransitionActionCode").hide();
$("#errallowedActncdToRemove").hide();
$("#errcapAmtCalReq").hide();
$("#errdefaultReasonRejCode").hide();
 		   	document.getElementById('transitionActionCode').required = false;
document.querySelector(".requiretransitionActionCode").style.display = "none";
document.getElementById('tatPeriodDayType1').required = false;
document.querySelector(".require").style.display = "none"
 		   
    		    			});


$('#mti').on('keyup keypress blur change',function () {
        validateFromCommonVal('mti', true, "SelectionBox", 20, false);
         unableSave();
       });
       
    
        $('#funcCode').on('keyup keypress blur change',function () {
        validateFromCommonVal('funcCode', true, "SelectionBox", 20, false);
         unableSave();
       });
       
       
       addEventListenersOnActionCode();
           
           
          

    
     $('#funcCodeDesc').on('keyup keypress blur change',function () {
      unableSave();
        validateFromCommonVal('funcCodeDesc', true, "streetaddress", 11 , false);
    });
    
     $('#actionCodeDesc').on('keyup keypress blur change',function () {
      unableSave();
        validateFromCommonVal('actionCodeDesc', true, "streetaddress", 11 , false);
    });
    
     $('#raisedBy').on('keyup keypress blur change',function () {
      unableSave();
       validateFromCommonVal('raisedBy', true, "SelectionBox", 20, false);
    });
    
    
    $('#actionCodeDesc').on('keyup keypress blur change',function () {
     unableSave();
        validateFromCommonVal('actionCodeDesc', true, "streetaddress", 11 , false);
    });
    $('#tatPeriod').on('keyup keypress blur change',function () {
        validateFromCommonVal('tatPeriod', false, "NumberOnly", 3 , false);
        
         unableSave();
        if($('#tatPeriod').val()!=''){
        	document.getElementById('tatPeriodDayType1').required = true;
		document.querySelector(".require").style.display = "initial";
document.getElementById('transitionActionCode').required = true;
document.querySelector(".requiretransitionActionCode").style.display = "initial";}
		else{
		document.getElementById('tatPeriodDayType1').required = false;
		document.querySelector(".require").style.display = "none";
document.getElementById('transitionActionCode').required = false;
document.querySelector(".requiretransitionActionCode").style.display = "none";
}
        
    });
     
       
          $('#transitionActionCode').on('keyup keypress blur change',function () {
           unableSave();
        validateFromCommonVal('transitionActionCode', false, "SelectionBox", 20, false);
       });
       
          $('#defaultReasonRejCode').on('keyup keypress blur change',function () {
           unableSave();
        validateFromCommonVal('defaultReasonRejCode', true, "NumberOnly", 4, true);
       });


if($('#tatPeriod').val()!=''  || $('#tatPeriod').val()!=0 ){
        	  document.getElementById('tatPeriodDayType1').required = true;
      		document.querySelector(".require").style.display = "initial";
      		document.getElementById('transitionActionCode').required = true;
document.querySelector(".requiretransitionActionCode").style.display = "initial";
          $('#tatPeriodDayType').on('keyup keypress blur change',function () {
              unableSave();
               validateFromCommonVal('tatPeriodDayType', true, "SelectionBox", 20, false);
            });
            $('#transitionActionCode').on('keyup keypress blur change',function () {
unableSave();
validateFromCommonVal('transitionActionCode', true, "SelectionBox", 20, false);
});
          } else {
        	  $('#tatPeriodDayType').on('keyup keypress blur change',function () {
                  unableSave();
                   validateFromCommonVal('tatPeriodDayType', false, "SelectionBox", 20, false);
                });
                
                $('#transitionActionCode').on('keyup keypress blur change',function () {
unableSave();
validateFromCommonVal('transitionActionCode', false, "SelectionBox", 20, false);
});
          }       
       



});

function addEventListenersOnActionCode() {
    $('#actionCode').on('keyup keypress blur change', function() {

        unableSave();
        if (validateFromCommonVal('actionCode', true, "AlphaNumericNoSpace", 2, true)) {
            if ($('#addFlow').val() == 'Y') {
                checkDupActionCode();
            }
            else if ($('#editFlow').val() == 'Y') {
                console.log("edit");
            }
        }
    });
}

function forEditFlow() {
	var y="";
	var dataarray="";
    if ($('#editFlow').val() == "Y") {
        if ($("#capAmtCalReqFlag").val() == 1) {
            $("#capAmtCalReq").prop("checked", true);
        }

        if ($('#editFlow').val() == "Y") {

            var checker = document.getElementById('capAmtCalReq');

            checker.onchange = function() {
                unableSave();
            };

        }


        if (document.getElementById("editActionCode") != null) {

            y = document.getElementById("editActionCode").value;

            if (y != null) {
                dataarray = y.split(",");

            }

            $('#allowedActncdToRemove').multiselect('refresh');
            $('#allowedActncdToRemove').multiselect('select', dataarray);

        }

    }
}

function setAttributes() {
	var y="";
	var dataarray="";
    if (($('#showButton').val() == "Y")) {
        $("input").prop('disabled', true);
        $("select").prop('disabled', true);
        if ($('#tatPeriod').val() != '') {
            document.getElementById('tatPeriodDayType1').required = true;
            document.querySelector(".require").style.display = "initial";
            document.getElementById('transitionActionCode').required = true;
            document.querySelector(".requiretransitionActionCode").style.display = "initial";

        }
        if ($("#capAmtCalReqFlag").val() == 1) {
            $("#capAmtCalReq").prop("checked", true);
        }
        $('#allowedActncdToRemove').attr("disabled", true);
        if (document.getElementById("editActionCode") != null) {

            y = document.getElementById("editActionCode").value;

            if (y != null) {
                dataarray = y.split(",");

            }

            $('#allowedActncdToRemove').multiselect('refresh');
            $('#allowedActncdToRemove').multiselect('select', dataarray);

        }
    }
}

function validateActionCode(action){

var check = false;

  check = validateFields(check);
     
  

	if (!validateFromCommonVal('actionCode', 
	     true, "AlphaNumericNoSpace", "2", true)) {

		check = true;
	} else {
		if ($('#addFlow').val() == 'Y') {

			checkDupActionCode();

			if (ajaxValidActionCode) {

				check = true;
			}


		}
		else if ($('#editFlow').val() == 'Y') {

		//TO DO REJC FIRST
		}
	}
    
     
    
  var actncd = $('#allowedActncdToRemove option:selected').toArray().map(item => item.value).join();
				
				var arr = actncd.split(',');
			    if( actncd==null || arr.length==0 || actncd==""){
					  
					$("#errallowedActncdToRemove").find('.error').html("");
				
						}
				 else{
					 
						$("#errallowedActncdToRemove").hide();
						$("#errallowedActncdToRemove").find('.error').html('');
				 }
     
     check = setErrFields(check);


 if (!check) {
      
      addOrUpdateActionCode(action)
      }else{
      return false;
      }
 
}


function setErrFields(check) {
    if ($("#tatPeriod").val() != '') {
        var dayType = $("#tatPeriodDayType").val();
        var transitionActionCode = $("#transitionActionCode").val();
        if (dayType == "0" || transitionActionCode == "0") {
            check = true;
            $("#errtatPeriodDayType").show();
            $("#errtatPeriodDayType").find('.error').html("Please Select Period Day Type");
            $("#errtransitionActionCode").show();
            $("#errtransitionActionCode").find('.error').html("Please Select Transition Action Code");
        }
        else {
            $("#errtatPeriodDayType").hide();
            $("#errtransitionActionCode").hide();
        }
    }
    return check;
}

function validateFields(check) {
    if (!validateFromCommonVal('mti', true, "SelectionBox", "", false)) {
        check = true;
    }

    if (!validateFromCommonVal('funcCode', true, "SelectionBox", "", false)) {
        check = true;

    }

    if (!validateFromCommonVal('actionCodeDesc', true, "streetaddress", "", false)) {
        check = true;

    }
    if (!validateFromCommonVal('raisedBy', true, "SelectionBox", "", false)) {
        check = true;

    }



    if (!validateFromCommonVal('funcCodeDesc',
        true, "streetaddress", "", false)) {
        check = true;

    }

    if (!validateFromCommonVal('tatPeriod', false, "NumberOnly", 3, false)) {
        check = true;

    }
    if (!validateFromCommonVal('transitionActionCode', false, "SelectionBox", 20, false)) {
        check = true;

    }
    if (!validateFromCommonVal('defaultReasonRejCode', true, "NumberOnly", 4, true)) {
        check = true;

    }
    return check;
}

function addOrUpdateActionCode(action){


var capAmtCalReq="";
if($("#capAmtCalReq").prop("checked"))
{capAmtCalReq="1";
}
var tatPeriod="";
if($('#tatPeriod').val()==""){
tatPeriod=0;
}
else{
tatPeriod=$('#tatPeriod').val();
}

        var data = "funcCode," + $('#funcCode').val() + ",actionCode," + $('#actionCode').val() + ",actionCodeId," + $('#actionCodeId').val() + ",mti," + $('#mti').val() + ",actionCodeDesc," + $('#actionCodeDesc').val() + ",funcCodeDesc," + $('#funcCodeDesc').val() + ",raisedBy," + $('#raisedBy').val()+",tatPeriodDayType,"+$("#tatPeriodDayType").val()+",tatPeriod,"+tatPeriod+",transitionActionCode,"+$("#transitionActionCode").val()+",capAmtCalReq,"+capAmtCalReq+",defaultReasonRejCode,"+$("#defaultReasonRejCode").val()+",allowedActncdToRemove,"+escape($("#allowedActncdToRemove").val())+",parentPage,"+$("#hparentPage").val();                                   
      
        postData(action, data);

}



function validateactionCode(_msgId){



  	if($('#actionCode').val() == '0' || $('#actionCode').val() == '' )
  	{
		$("#erractionCode").find('.error').html('Value cannot be null for Action Code');
				 $("#erractionCode").show();
				 
				 return false;
	}
	else{
	 $("#erractionCode").find('.error').html('');
		$("#erractionCode").hide();
	
	}

return true;
}

function checkDupActionCode() {
	var actionCode = $('#actionCode').val();
	var validactionCode = false;
	
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	if (actionCode != "") {

		$.ajax({
			url: "checkDupActionCode",
			type: "POST",
			dataType: "json",
			data: {

				"actionCode": actionCode,
				"_TransactToken": tokenValue
			},
			success: function(response) {

				if (response.status == "BSUC_0001") {
					validactionCode = true;

					$('#erractionCode').show();

					$('#erractionCode').find('.error').html('Action Code should be Unique');

					callBack(true);




				} else {
					validactionCode = false;

					$('#erractionCode').find('.error').html('');
					callBack(false);

				}
			},
			error: function(_request, _status, _error) {
				$('#erractionCode').find('.error').html('');
				callBack(false);
			}
		});

	} else {
		validactionCode = false;
	}

	return validactionCode;
}

var ajaxValidActionCode;

function callBack(flag) {
	ajaxValidActionCode = flag;
}


function submitForm(url) {
     var data = "";
    postData(url, data);
}


 function userAction(action) {
	let url = action;
	var actionCodeId = $("#actionCodeId").val();
 	var data =  "actionCodeId,"+ actionCodeId;
	postData(url, data);

}

function disableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = true;
}
}


function unableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = false;
}
}
