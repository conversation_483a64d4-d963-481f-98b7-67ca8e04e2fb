package org.npci.settlenxt.adminportal.config.kafka.config;

import org.npci.settlenxt.portal.common.config.kafka.config.BaseKafkaPublisherConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
@PropertySource("/application.properties")
public class KafkaPublisherConfigurations extends BaseKafkaPublisherConfiguration {

}