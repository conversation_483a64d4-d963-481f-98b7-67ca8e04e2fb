$(document).ready(
		function() {

			window.history.forward();
			function noBack() {
				window.history.forward();
			}

			$('#password').bind("cut copy paste", function(e) {
				e.preventDefault();
			});

			$('#passwordText').bind("cut copy paste", function(e) {
				e.preventDefault();
			});
			$('#loginIDText').bind("cut copy paste", function(e) {
				e.preventDefault();
			});

			$(".disableRClick").on("contextmenu", function() {
				return false;
			});

			$('.btn1').hide();
			$('#errMsg').hide();
			$('#errMsgPwd').hide();
			$('#errMLid').hide();

			$('#login').attr('autocomplete', 'off');

			$('#loginBtn').click(
					function() {

						//$('.alert').hide();

						var tokenValue = document
								.getElementsByName("_TransactToken")[0].value;

						if ($('#loginIDText').val() != ""
								&& $('#passwordText').val() != "") {
							$('#loginID').val($('#loginIDText').val());
							$('#password').val($('#passwordText').val());
							
							//$('#loginIDText').val('');
							//$('#passwordText').val('');
							$('#loginToken').val(tokenValue);

							sendform();

						} else if ($('#loginIDText').val() == ""
								&& $('#passwordText').val() == "") {
							$('#errMsg').show();
							$('#loginIDText').focus();

						} else if ($('#loginIDText').val() == "") {
							$('#errMLid').show();
							$('#loginIDText').focus();
						} else if ($('#passwordText').val() == "") {
							$('#errMsgPwd').show();
							$('#passwordText').focus();
						}
					});

			
		});

$(document).keypress(function(e) {
	if (e.which == 13) {
		$('#loginBtn').click();
	}
});

window.onload = function() {
	var pass = document.getElementById('password').value;
	pass.onpaste = function(e) {
		e.preventDefault();
	}
}

function hidePara() {
	$('#errMsg').hide();
	$('#errMsgPwd').hide();
	$('#errMLid').hide();
}

function sendform() {
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var loginID = document.getElementById("loginIDText");

	$('#loginID').val(base64Encode(loginID.value));

	$('#loginToken').val(tokenValue);
	$('#password').val(sha256(sha256($('#passwordText').val()) + tokenValue));
	$('#loginIDText').val('');
	$('#passwordText').val('');
	$('#login').attr('action', 'doLogin');
	$('#submit').click();
}

//Forgot Password
function postAction(url) {
			 var tokenValue = document.getElementsByName("_TransactToken")[0].value;
			var data = "_TransactToken,"+tokenValue;
			if ($('#loginIDText').val() != "") {
				var loginID = document
						.getElementById("loginIDText");
				$('#loginID').val(base64Encode(loginID.value));
				$('#loginToken').val(tokenValue);
				var data = "loginid," + $('#loginID').val()
						+ ",_TransactToken," + tokenValue;
				postData(url, data);

			} else {
				$('#errMLid').show();
				$('#loginIDText').focus();
			}
		}


var enc64List, dec64List;

function initBase64() {
	enc64List = new Array();
	dec64List = new Array();
	var i;
	for (i = 0; i < 26; i++) {
		enc64List[enc64List.length] = String.fromCharCode(65 + i);
	}
	for (i = 0; i < 26; i++) {
		enc64List[enc64List.length] = String.fromCharCode(97 + i);
	}
	for (i = 0; i < 10; i++) {
		enc64List[enc64List.length] = String.fromCharCode(48 + i);
	}
	enc64List[enc64List.length] = "+";
	enc64List[enc64List.length] = "/";
	for (i = 0; i < 128; i++) {
		dec64List[dec64List.length] = -1;
	}
	for (i = 0; i < 64; i++) {
		dec64List[enc64List[i].charCodeAt(0)] = i;
	}
}

function base64Encode(str) {
	var c, d, e, end = 0;
	var u, v, w, x;
	var ptr = -1;
	var input = str.split("");
	var output = "";
	while (end == 0) {
		c = (typeof input[++ptr] != "undefined") ? input[ptr].charCodeAt(0)
				: ((end = 1) ? 0 : 0);
		d = (typeof input[++ptr] != "undefined") ? input[ptr].charCodeAt(0)
				: ((end += 1) ? 0 : 0);
		e = (typeof input[++ptr] != "undefined") ? input[ptr].charCodeAt(0)
				: ((end += 1) ? 0 : 0);
		u = enc64List[c >> 2];
		v = enc64List[(0x00000003 & c) << 4 | d >> 4];
		w = enc64List[(0x0000000F & d) << 2 | e >> 6];
		x = enc64List[e & 0x0000003F];

		// handle padding to even out unevenly divisible string lengths
		if (end >= 1) {
			x = "=";
		}
		if (end == 2) {
			w = "=";
		}

		if (end < 3) {
			output += u + v + w + x;
		}
	}
	// format for 76-character line lengths per RFC
	var formattedOutput = "";
	var lineLength = 76;
	$('#passwordText').val()
	while (output.length > lineLength) {
		formattedOutput += output.substring(0, lineLength) + "\n";
		output = output.substring(lineLength);
	}
	formattedOutput += output;
	return formattedOutput;
}

// Self-initialize the global variables
initBase64();
