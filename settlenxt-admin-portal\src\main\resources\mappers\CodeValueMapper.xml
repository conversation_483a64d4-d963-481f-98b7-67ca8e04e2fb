<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.CodeValueRepository">

	<select id="getCodeValueDataRepo" resultType="codeValueDTO">
		select code as code, description as description from 
		lookup where 
		type=#{code}
	</select>

	
	<select id="getBankNameData" resultType="codeValueDTO">
		select distinct unique_bank_name as code, unique_bank_name as description from 
		participant 
	</select>
	
	
	<select id="getTipSurchargeNameList" resultType="codeValueDTO">
		select CONCAT(t.tip_surcharge_id, '-',t.tip_surcharge_name) as description, t.tip_surcharge_id as code from  tip_surcharge t 
	</select>
	
	<select id="getMccNameList" resultType="codeValueDTO">
		select CONCAT(m.mcc_code, '-',m.mcc_desc) as description, m.mcc_id as code from  mcc_config m 
	</select>
	
	
	<select id="getTipSurchargeNameListRepo"
		resultType="codeValueDTO">
		select CONCAT(t.tip_surcharge_id, '-',t.tip_surcharge_name)
		as description, t.tip_surcharge_id as code from 
		tip_surcharge t
	</select>

	<select id="getMccNameListRepo" resultType="codeValueDTO">
		select
		CONCAT(m.mcc_code, '-',m.mcc_desc) as description, m.mcc_id as code from 
		mcc_config m
	</select>

	<select id="getMccGroupListRepo" resultType="codeValueDTO">
		select code as code, description as description from lookup where
		type=#{code}
	</select>
	
	<select id="getForexIdList" resultType="codeValueDTO">
		select CONCAT(forex_id, '-',description) as description, forex_id as code
		from forex_master
	</select>
	
</mapper>