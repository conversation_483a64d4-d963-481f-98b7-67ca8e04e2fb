package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.dto.CashBackFileUploadDTO;
import org.npci.settlenxt.portal.common.service.BaseFileUploadService;

/**
 * Service Interface used for
 * <li>Bulk file upload</li>
 * <li>Search file from fileupload table</li>
 * <li>Stage uploaded file</li>
 * <li>Get Current Cycle details</li>
 * <li>Get File upload details by documentId</li>
 * <AUTHOR>
 *
 */
public interface IFileUploadService extends BaseFileUploadService {
	
	 String cashBackFileUpload(CashBackFileUploadDTO cashbackDTO);
	
	 List<CashBackFileUploadDTO> searchCashBackFiles();
}
