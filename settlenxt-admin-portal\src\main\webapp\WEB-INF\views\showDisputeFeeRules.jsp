<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<sec:authorize access="hasAuthority('Approve Dispute Fee Rule')"
	var="hasApproveAuthority"></sec:authorize>
<sec:authorize access="hasAuthority('Edit Dispute Fee Rule')"
	var="hasEditAuthority"></sec:authorize>
<script type="text/javascript"> 
var actionColumnIndex=7;
var firstColumnToBeSkippedInFilterAndSort = false;
<c:if test="${pendingDisputeFeeRule eq 'Yes'}">
	var actionColumnIndex=7;
	<c:if test="${hasApproveAuthority}">
	var firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
</c:if>
var viewScreen = 'G';
</script>


<script src="./static/js/validation/showDisputeFeeRules.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
</script>

<script type="text/javascript" src="./static/js/buttons.html5.min.js">
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />

<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>
<div class="modal fade" id="toggleModal1" tabindex="-1" role="dialog"
	aria-labelledby="toggleApproveTransitionRules" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div style="display: flex; justify-content: center;">
				<p id="ruless" />
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="approveorRejectBulk('R')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="approveorRejectBulk('A')">Approve</button>
			</div>
		</div>
	</div>
</div>

<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showDisputeFeeRule eq 'YES' }">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" onclick="submitForm('/showDisputeFeeRule');"
			role="tab" data-toggle="tab"> <span
			class="glyphicon glyphicon-credit-card">&nbsp;</span>
		<spring:message code="disputeFeeRule.mainTab.title" />
		</a>

		<c:choose>
			<c:when test="${pendingDisputeFeeRule eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#profile" role="tab"
			onclick="submitForm('/disputeFeeRulePendingForApproval');"
			data-toggle="tab"> <span class="glyphicon glyphicon-ok">&nbsp;</span>
		<spring:message code="binexcl.approvalPanel.title" />
		</a>



	</ul>

	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
			<div class="col-sm-12">
			<sec:authorize access="hasAuthority('Add Dispute Fee Rule')">
									<c:if test="${addDisputeFeeRule eq 'Yes'}">
										<a class="btn btn-success pull-right btn_align" href="#"
											onclick="submitForm('/createDisputeFeeRule');"
											style="margin-top: -5px;"><em class="glyphicon-plus"></em>
											Add Dispute Fee Rule</a>
									</c:if>
								</sec:authorize>
								</div>
				<div class="col-sm-12" style="margin-top: 5px;">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="ifsc.clearFiltersBtn" />
					</button>


					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="excelExport"><spring:message code="ifsc.exportBtn" /> </a>

					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="csvExport"><spring:message code="ifsc.csvBtn" /> </a>

				</div>
			</div>
			<div class="row">
				<div class="col-sm-12"></div>
			</div>

			<c:if test="${not empty showDisputeFeeRule}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<sec:authorize
									access="hasAuthority('Approve Dispute Transition Rules')">
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="approveorRejectBulk('A')" id="appButton"
										value="<spring:message code="dispute.transition.label.approve" />" />
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="approveorRejectBulk('R')" id="rejButton"
										value="<spring:message code="dispute.transition.label.reject" />" />
								</sec:authorize>
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="disputeFeeRule.mainTab.title" /></span></strong>
								
							</div>
							<div class="panel-body">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width:100%;">
										<caption style="display:none;">Show Dispute Fee Rule</caption>
										<thead>
											<tr>
												<th scope = "col"><spring:message code="disputeFeeRules.actionCode" /></th>
												<th scope = "col"><spring:message code="disputeFeeRules.feeType" /></th>
												<th scope = "col"><spring:message code="disputeFeeRules.feeCode" /></th>
												<th scope = "col"><spring:message code="disputeFeeRules.fieldName" /></th>
												<th scope = "col"><spring:message
														code="disputeFeeRules.relationalOperator" /></th>
												<th scope = "col"><spring:message code="disputeFeeRules.status" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="disputeFee" items="${disputeFeeRulelist}">

												<tr>

													<td onclick="javascript:view('${disputeFee.seqId}','P')">${disputeFee.actionCode}</td>
													<td onclick="javascript:view('${disputeFee.seqId}','P')">${disputeFee.feeType}</td>
													<td onclick="javascript:view('${disputeFee.seqId}','P')">${disputeFee.feeCode}</td>
													<td onclick="javascript:view('${disputeFee.seqId}','P')">${disputeFee.fieldName}</td>
													<td onclick="javascript:view('${disputeFee.seqId}','P')">${disputeFee.relationalOperator}</td>
													<td onclick="javascript:view('${disputeFee.seqId}','P')">
														<c:if test="${disputeFee.requestState =='A' }">
															<spring:message
																code="binFeatureMapping.requestState.approved.description" />
														</c:if> <c:if test="${disputeFee.requestState =='R' }">
															<spring:message
																code="binFeatureMapping.requestState.rejected.description" />
														</c:if> <c:if test="${disputeFee.requestState =='P' }">
															<spring:message
																code="binFeatureMapping.requestState.pendingApproval.description" />
														</c:if>
													</td>

													<%-- 
													<td><c:if test="${showDisputeFeeRule eq 'YES'}">
															<sec:authorize access="hasAuthority('Edit Reason Code')">
													
															<a
																href="javascript:view('${reasonCode.reasonCode}','V')"
																data-i18n="Data"><spring:message code="sm.lbl.edit" /></span></strong></a>
															</sec:authorize> &nbsp; 

															<a
																href="javascript:view('${disputeFee.seqId}','P')"
																onclick="clickAndDisable(this);"><strong><span
																	data-i18n="Data"><spring:message
																			code="sm.lbl.view" /></span></strong></a>

														</c:if> <c:if test="${showPendingDisputeFeeRule eq 'Yes'}">
															<a
																href="javascript:view('${disputeFee.seqId}','G')"
																onclick="clickAndDisable(this);"><strong><span
																	data-i18n="Data"><spring:message
																			code="sm.lbl.view" /></span></strong></a>
														</c:if></td>
 --%>
												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
			</c:if>



			<c:if test="${not empty pendingDisputeFeeRule}">
				<input type="hidden" id="detailsHeaderss" name="headr"
					value="${tranSize}" />
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">

							<div class="panel-heading">
								<sec:authorize access="hasAuthority('Approve Dispute Fee Rule')">
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="approveorRejectBulk('A')" id="appButton"
										value="<spring:message code="dispute.transition.label.approve" />" />
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="approveorRejectBulk('R')" id="rejButton"
										value="<spring:message code="dispute.transition.label.reject" />" />
								</sec:authorize>
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="disputeFeeRule.approvalTab.title" /></span></strong>
							</div>
							<div class="panel-body">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width:100%;">
										<caption style="display:none;">Show Dispute Fee Rule</caption>
										<thead>
											<tr>

												<sec:authorize
													access="hasAuthority('Approve Dispute Fee Rule')">

													<th id="selectalldisp"><input type=checkbox
														name='selectAllCheck' id="selectAll1" data-toggle="modal"
														data-target="toggleModal1" value=""></input></th>
												</sec:authorize>
												<th scope = "col"><spring:message code="disputeFeeRules.actionCode" /></th>
												<th scope = "col"><spring:message code="disputeFeeRules.feeType" /></th>
												<th scope = "col"><spring:message code="disputeFeeRules.feeCode" /></th>
												<th scope = "col"><spring:message code="disputeFeeRules.fieldName" /></th>
												<th scope = "col"><spring:message
														code="disputeFeeRules.relationalOperator" /></th>
												<th scope = "col"><spring:message code="disputeFeeRules.status" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="disputeFee"
												items="${disputeFeerulePendingList}">
												<tr>

													<sec:authorize
														access="hasAuthority('Edit Dispute Fee Rule')">
														<c:if test="${disputeFee.requestState  eq 'R'}">
															<script>
																	viewScreen = 'V';
																</script>
														</c:if>
													</sec:authorize>

													<sec:authorize
														access="hasAuthority('Approve Dispute Fee Rule')">
														<td onclick=event.stopPropagation()><c:if
																test="${disputeFee.requestState  eq 'P'}">
																<input type=checkbox name='type' id=""
																	class="selectedId" value='${disputeFee.seqId}'></input>
															</c:if></td>
													</sec:authorize>

													<c:choose>
														<c:when
															test="${hasEditAuthority && disputeFee.requestState  eq 'R'}">
															<td onclick="javascript:view('${disputeFee.seqId}','V')">${disputeFee.actionCode}</td>
															<td onclick="javascript:view('${disputeFee.seqId}','V')">${disputeFee.feeType}</td>
															<td onclick="javascript:view('${disputeFee.seqId}','V')">${disputeFee.feeCode}</td>
															<td onclick="javascript:view('${disputeFee.seqId}','V')">${disputeFee.fieldName}</td>
															<td onclick="javascript:view('${disputeFee.seqId}','V')">${disputeFee.relationalOperator}</td>
															<td onclick="javascript:view('${disputeFee.seqId}','V')">
																<c:if test="${disputeFee.requestState =='A' }">
																	<spring:message
																		code="binFeatureMapping.requestState.approved.description" />
																</c:if> <c:if test="${disputeFee.requestState =='R' }">
																	<spring:message
																		code="binFeatureMapping.requestState.rejected.description" />
																</c:if> <c:if test="${disputeFee.requestState =='P' }">
																	<spring:message
																		code="binFeatureMapping.requestState.pendingApproval.description" />
																</c:if>
															</td>
														</c:when>
														<c:otherwise>
															<td onclick="javascript:view('${disputeFee.seqId}','G')">${disputeFee.actionCode}</td>
															<td onclick="javascript:view('${disputeFee.seqId}','G')">${disputeFee.feeType}</td>
															<td onclick="javascript:view('${disputeFee.seqId}','G')">${disputeFee.feeCode}</td>
															<td onclick="javascript:view('${disputeFee.seqId}','G')">${disputeFee.fieldName}</td>
															<td onclick="javascript:view('${disputeFee.seqId}','G')">${disputeFee.relationalOperator}</td>
															<td onclick="javascript:view('${disputeFee.seqId}','G')">
																<c:if test="${disputeFee.requestState =='A' }">
																	<spring:message
																		code="binFeatureMapping.requestState.approved.description" />
																</c:if> <c:if test="${disputeFee.requestState =='R' }">
																	<spring:message
																		code="binFeatureMapping.requestState.rejected.description" />
																</c:if> <c:if test="${disputeFee.requestState =='P' }">
																	<spring:message
																		code="binFeatureMapping.requestState.pendingApproval.description" />
																</c:if>
															</td>
														</c:otherwise>
													</c:choose>
													<%-- <td onclick="javascript:view('${disputeFee.seqId}','G')">${disputeFee.actionCode}</td>
													<td onclick="javascript:view('${disputeFee.seqId}','G')">${disputeFee.feeType}</td>
													<td onclick="javascript:view('${disputeFee.seqId}','G')">${disputeFee.feeCode}</td>
													<td onclick="javascript:view('${disputeFee.seqId}','G')">${disputeFee.fieldName}</td>
													<td onclick="javascript:view('${disputeFee.seqId}','G')">${disputeFee.relationalOperator}</td>
													<td onclick="javascript:view('${disputeFee.seqId}','G')">
														<c:if test="${disputeFee.requestState =='A' }">
															<spring:message
																code="binFeatureMapping.requestState.approved.description" />
														</c:if> <c:if test="${disputeFee.requestState =='R' }">
															<spring:message
																code="binFeatureMapping.requestState.rejected.description" />
														</c:if> <c:if test="${disputeFee.requestState =='P' }">
															<spring:message
																code="binFeatureMapping.requestState.pendingApproval.description" />
														</c:if>
													</td> --%>


													<%-- <sec:authorize
														access="hasAuthority('Edit Dispute Fee Rule')">
														<c:choose>
															<c:when test="${disputeFee.requestState  eq 'R'}">

																<td><a
																	href="javascript:view('${disputeFee.seqId}','V')"
																	onclick="clickAndDisable(this);"><strong><span
																			data-i18n="Data"><spring:message
																					code="sm.lbl.edit" /></span></strong></a></td>
															</c:when>
															<c:otherwise>

																<td><a
																	href="javascript:view('${disputeFee.seqId}','G')"
																	onclick="clickAndDisable(this);"><strong><span
																			data-i18n="Data"><spring:message
																					code="sm.lbl.view" /></span></strong></a></td>
															</c:otherwise>
														</c:choose>
													</sec:authorize>





													<sec:authorize
														access="hasAuthority('Approve Dispute Fee Rule')">
														<td><a
															href="javascript:view('${disputeFee.seqId}','G')"
															onclick="clickAndDisable(this);"><strong><span
																	data-i18n="Data"><spring:message
																			code="sm.lbl.view" /></span></strong></a>
													</sec:authorize>

													</td> --%>
												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>

		</div>

	</div>



</div>
