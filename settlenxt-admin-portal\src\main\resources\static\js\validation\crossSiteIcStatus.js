$(document).ready(function() {
	var today = new Date();
	$("#systemDate").datepicker({
		dateFormat: 'yyyy-MM-dd',
		todayHighlight: true,
		autoclose: true,
		changeMonth: true,
		changeYear: true,
		endDate: "today",
        maxDate: today
        }).on('changeDate', function (_ev) {
                $(this).datepicker('hide');
            });


        $('.datepicker').keyup(function () {
            if (this.value.match(/\D/g)) {
                this.value = this.value.replace(/[^0-9^-]/g, '');
            }
        });

	$("#send").click(function() {
		var systemDate = $('#systemDate').val();
		var url = "/showCrossSiteIcStatus";
		var data = "systemDate," + systemDate;
		postData(url, data);
	});
	
	var oTable = $("#tabnew").DataTable({
		"fnRowCallback": function(nRow, _aData, _iDisplayIndex, _iDisplayIndexFull) {
			var status = $(nRow).attr("data-status");
			if (status == "SUCCESS") {
				$('td', nRow).addClass('bg-success');
			} else {
				$('td', nRow).addClass('bg-info');
			}
		},
		dom: 'lBfrtip',
		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true,
		stateSave: true
	});
	
	$("#submit").click(function() {
		var url = getURL('/retryIcnOrOutgoingFile');
		var forceOperation = "Retry";
		var myObject = new Object();
		var icStatus = null;
		var cycleNumbers = [];
		var rowcollection = oTable.$(".reprocess-checkbox:checked", { "page": "all" });
		rowcollection.each(function(_index, elem) {
			var checkbox_value = $(elem).val();
			cycleNumbers.push(checkbox_value);
			icStatus = $(elem).attr("data-icStatus");
		});
		var systemDate = $('#systemDate').val();
		var internalCycleNumber = cycleNumbers.toString();
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		myObject.forceOperation = forceOperation;
		myObject.systemDate = systemDate;
		myObject.internalCycleNumber = internalCycleNumber;
		myObject.icStatus = icStatus;
		$.ajax({
			type: "POST",
			contentType: "application/json",
			url: url,
			data: JSON.stringify(myObject),
			dataType: 'json',
			"beforeSend": function(xhr) {
				xhr.setRequestHeader('_TransactToken', tokenValue);
			},
			success: function(response) {
				$("#alertNotification").removeClass("hide");
				if (response == null || response == '' || response == undefined) {
					$("#alertNotification").addClass("alert-danger");
					$("#alertMessageStrong").text("Error! ");
					$("#alertMessage").text("Failed to send message to retry the ICN: " + "Internal server error");
				}else {
					if (response.Status == "FAILED" || response.Status == "503") {
						$("#alertNotification").addClass("alert-danger");
						$("#alertMessageStrong").text("Error! ");
						$("#alertMessage").text("Failed to send message to retry the ICN : " + response.errorMessage);
					} else if (response.Status == "SUCCESS" || response.Status == "200") {
						$("#alertNotification").addClass("alert-success");
						$("#alertMessageStrong").text("Success! ");
						$("#alertMessage").text("Successfully send message to Retry the ICN.");
					} else {
						$("#alertNotification").addClass("alert-danger");
						$("#alertMessageStrong").text("Error! ");
						$("#alertMessage").text("Failed to send message to retry the ICN: " + "Internal server error");
					}
				}
			},
			error: function(_e) {
				$("#btn-save").prop("disabled", false);
			}
		});
		$("#okAction").click(function() {
			window.setTimeout(function() { location.reload() }, 100)
		});
	});

	function getURL(url) {
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		
		return  (pathName + url);
	}

	$('#tabnew').on('click', '#checked', function() {
		if (this.checked) {
			oTable.$("input[name='checkedBox']").prop('checked', true);
		} else {
			oTable.$("input[name='checkedBox']").prop('checked', false);
		}
	});

	$('#tabnew').on('click', '.reprocess-checkbox, .hCheckbox', function() {
		if ($('.reprocess-checkbox:checked').length > 0) {
			$("#btn").removeAttr("disabled");
		} else {
			$("#btn").attr("disabled", "disabled");
		}
		if ($('.reprocess-checkbox:checked').length == $('.reprocess-checkbox').length) {
			$('#checked').prop('checked', true);
		} else {
			$('#checked').prop('checked', false);
		}
	});

});