<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>

<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script src="./static/js/validation/ViewUserInfo.js"
	type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">





		<c:url value="approveUserStatus" var="approveUserStatus" />
		<form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewUser" modelAttribute="userInfoDto"
			action="${approveUserStatus}" autocomplete="off">
			<input type="hidden" id="userType" value='${reqType}' />
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data">User Information </span></strong>
							<div class="icon_bar">
							<sec:authorize access="hasAuthority('Edit User') ">
								<c:if test="${empty Pending}">
									<a data-toggle="tooltip" title="Edit"
										onclick="userAction('N','/editUser');" href="#"><img
										src="./static/images/edit-grey.png" alt="edit"></a>
								</c:if>
								<c:if test="${not empty deactivate}">
									<a data-toggle="tooltip" title="User Deactivate"
										onclick="userActivateDeactivate('N','/deactivateUser');"
										href="#"><img
										src="./static/images/user-deactivate-grey.png"
										alt="deactivate"></a>
								</c:if>
								<c:if test="${not empty activate}">
									<a data-toggle="tooltip" title="User activate"
										onclick="userActivateDeactivate('N','/activateUser');"
										href="#"><img src="./static/images/user-activate-grey.png"
										alt="activate"></a>
								</c:if>
								<c:if test="${not empty lockuser}">
									<a data-toggle="tooltip" title="Lock User"
										onclick="userActivateDeactivate('N','/unlockUser');" href="#"><img
										src="./static/images/usreblock-grey.png" alt="lockuser"></a>
								</c:if>

								<c:if test="${not empty unlockuser}">
									<a data-toggle="tooltip" title="Unlock User"
										onclick="userActivateDeactivate('N','/unlockUser');" href="#"><img
										src="./static/images/usreblock-grey.png" alt="Unlockuser"></a>
								</c:if>

								<c:if test="${empty Pending}">
									<c:if test="${not empty resetPassword}">
										<a data-toggle="tooltip" title="Reset Password"
											onclick="userAction('N','/resetPasswordsUser');" href="#"><img
											src="./static/images/reset-password-gray.png"
											alt="Reset Password"></a>
									</c:if>
								</c:if>
	</sec:authorize>
							</div>					
						</div>
						<div class="panel-body">
							<form:hidden path="userId" id="userId" />
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">View Ifsc Info</caption> 
							<thead style="display:none;"><th scope="col"></th></thead>
							<caption style="display:none;">View Ifsc Info</caption> 
								<tbody>
									<tr>
										<td><label>Login ID<span style="color: red"></span></label></td>
										<td id="loginId">${userInfoDto.userName }</td>
										<td><label>Salutation <span style="color: red"></span></label></td>
										<td id="salutation">${userInfoDto.salutation }</td>
										<td><label>First Name<span style="color: red"></span></label></td>
										<td id="firstName">${userInfoDto.firstName }</td>
										
									</tr>

									<tr>
									<td><label>Middle Name<span style="color: red"></span></label></td>
										<td id="displayName">${userInfoDto.middleName }</td>
										<td><label>Last Name<span style="color: red"></span></label></td>
										<td id="lastName">${userInfoDto.lastName }</td>
											<td><label>Date of Birth<span style="color: red"></span></label></td>
										<td id="dobId">${userInfoDto.dob}</td>
										
										
									</tr>
									<tr>
									<td>
									<label>Email ID<span style="color: red"></span></label></td>
										<td id="emailId">${userInfoDto.emailId }</td>
										<td><label>Mobile No<span style="color: red"></span></label></td>
										<td id="mobileNo">${userInfoDto.mobileNo }</td>
										<td><label>Landline No. <span style="color: red"></span></label></td>
										<td id="contactNo">${userInfoDto.contactNo }</td>
										
										
									</tr>

									<tr>
									<td><label>Street Address<span style="color: red"></span></label></td>
										<td id="streetAddress">${userInfoDto.streetAddress }</td>
									<td><label>City<span style="color: red"></span></label></td>
										<td id="city">${userInfoDto.cityName }</td>
										<td><label>State<span style="color: red"></span></label></td>
										<td id="state">${userInfoDto.stateName }</td>
										
										
									</tr>
								

									<tr>
									<td><label>Pin Code<span style="color: red"></span></label></td>
										<td id="pinNo">${userInfoDto.pincode}</td>
												<td><label>MakerCheckerFlag<span
												style="color: red"></span></label></td>
										<td id="makChk" >${userInfoDto.makChkFlag}</td>
										<td><label>Employee ID<span style="color: red"></span></label></td>
										<td id="empId">${userInfoDto.empId}</td>
									

									</tr>

									<tr>
									

										<td><label>Account Status<span style="color: red"></span></label></td>
										<c:choose>
												<c:when test="${userInfoDto.activeFlag eq 'A'}">
													<td style="color: green">Active</td>
												</c:when>
												<c:when test="${userInfoDto.activeFlag eq 'I'}">
													<td style="color: red">Inactive</td>
												</c:when>
												<c:otherwise>
													<td>Null</td>
												</c:otherwise>
											</c:choose>
											<td><label>Last Updated By<span
												style="color: red"></span></label></td>
										<td>${userInfoDto.lastUpdatedBy}</td>
										<td><label>Last Updated On<span
												style="color: red"></span></label></td>
										<td>${userInfoDto.lastUpdatedOn}</td>
									
									</tr>
								</tbody>
							</table>
							<input type="hidden" name="userType" id="userType"
								value="${userInfoDto.userType}"></input>
							<!-- User to role map -->
							<div class="panel-body">


								<%--div role="tabpanel" class="tab-pane active" id="home"--%>


								<form:form modelAttribute="userrole" method="POST"
									name="assignRoleToUser" action="${assignRoleToUser}"
									onsubmit="removeSpace(this);encodeForm(this);" id="userDTO"
									autocomplete="off">
									<div class="row">
										<form:input path="userId" id="userId" type="hidden"
											value="${userrole.userId}" />

										<input type="hidden" id="changeFlag" />
										<form:hidden path="loginId" id="loginId"
											value="${userrole.loginId}" />

									</div>
								</form:form>
							</div>
							<div role="tabpanel" class="tab-pane active" id="home">
								<div class="panel panel-default no_margin">
									<div class="panel-heading clearfix">
										<strong><span class="glyphicon glyphicon-th"></span>
											<span data-i18n="Data">User To Role Mapping</span></strong>
									</div>
								</div>
								<div class="panel-body">
									<div class="col-sm-3"></div>
									<div class="form-group clearfix" style="padding-top: 20px">
										<div class="col-xs-6">
											<div class="panel panel-default">
												<div class="panel-heading">
													<strong><span class="glyphicon glyphicon-th"></span>
														<span data-i18n="Data">Available Roles</span></strong>
												</div>

												<div class="table-responsive">
													<c:if test="${empty successList }">
														<table id="tabnew"
															class="table table-striped table-bordered" style="width:100%;">
															<caption style="display:none;">View Ifsc Info</caption> 
															<thead>
																<tr>
																	<th scope="col">Role Name</th>
																	<th scope="col">Add</th>
																</tr>
															</thead>
															<tbody id="optionList">
																<c:if test="${not empty roleOptionList}">
																	<c:forEach var="list" items="${roleOptionList}">
																		<tr class="selectedRoles" id="add${list.roleId}"
																			value="${list.roleId}">
																			<td>${list.roleName}</td>
																			<td><em
																				class="glyphicon glyphicon-circle-arrow-right"
																				onclick="addToAssignedList('${list.roleId}','${list.roleName}')">
																			</em></td>
																		</tr>
																	</c:forEach>
																</c:if>

															</tbody>
														</table>
													</c:if>
													<c:if test="${not empty successList}">
														<table id="tabnew"
															class="table table-striped table-bordered" style="width:100%;">
															<caption style="display:none;">View Ifsc Info</caption> 
															<thead>
																<tr>
																	<th scope="col">Role Name</th>
																</tr>
															</thead>
															<tbody id="optionList">
																<c:if test="${not empty roleOptionList}">

																	<c:forEach var="list" items="${roleOptionList}">
																		<tr class="selectedRoles" id="add${list.roleId}"
																			value="${list.roleId}">
																			<td>${list.roleName}</td>
																		</tr>
																	</c:forEach>

																</c:if>
															</tbody>
														</table>
													</c:if>


												</div>
											</div>
										</div>


										<div class="col-xs-6">

											<div class="panel panel-default">
												<div class="panel-heading">
													<strong><span class="glyphicon glyphicon-th"></span>
														<span data-i18n="Data">Selected Roles</span></strong>
												</div>

												<div class="table-responsive">
													<c:if test="${empty successList }">
														<table id="tabnew1"
															class="table table-striped table-bordered" style="width:100%;">
																<caption style="display:none;">View Ifsc Info</caption> 
															<thead>
																<tr>
																	<th scope="col">Role Name</th>
																	<th scope="col">Remove</th>
																</tr>
															</thead>
															<tbody id="assignedList">
																<c:if test="${not empty aRoleOptionList}">
																	<c:forEach var="list" items="${aRoleOptionList}">
																		<tr class="selectedRoles" id="remove${list.roleId}"
																			value="${list.roleName}">
																			<c:if test="${ list.status eq 'E'}">
																				<td style="color: blue">${list.roleName}</td>
																			</c:if>
																			<c:if test="${list.status ne 'E'}">
																				<td>${list.roleName}</td>
																			</c:if>
																			<c:if
																				test="${not empty successStatus or errorStatus eq  'User is already pending for approval.' }">
																				<td><em
																					class="glyphicon glyphicon-remove-circle"></em></td>
																			</c:if>
																			<sec:authorize access="hasAuthority('Edit User') ">
																				<c:if
																					test="${empty successStatus and  errorStatus ne  'User is already pending for approval.' }">
																					<td><em
																						class="glyphicon glyphicon-remove-circle"
																						onclick="removeTag('${list.roleId}','${list.roleName}')"></em>
																					</td>
																				</c:if>
																			</sec:authorize>
																		</tr>

																	</c:forEach>
																</c:if>
															</tbody>
														</table>
													</c:if>
													<c:if test="${not empty successList }">
														<table id="tabnew1"
															class="table table-striped table-bordered" style="width:100%;">
															<caption style="display:none;">View Ifsc Info</caption> 
															<thead>
																<tr>
																	<th scope="col">Role Name</th>
																</tr>
															</thead>
															<tbody id="assignedList">
																<c:if test="${not empty aRoleOptionList}">
																	<c:forEach var="list" items="${aRoleOptionList}">
																		<tr class="selectedRoles" id="remove${list.roleId}"
																			value="${list.roleName}">
																			<c:if test="${ list.status eq 'E'}">
																				<td style="color: blue">${list.roleName}</td>
																			</c:if>
																			<c:if test="${list.status ne 'E'}">
																				<td>${list.roleName}</td>
																			</c:if>

																		</tr>

																	</c:forEach>
																</c:if>
															</tbody>
														</table>



													</c:if>
												</div>
											</div>
										</div>

									</div>

								</div>
							</div>


						</div>
					</div>
		</form:form>
		<div class="row">
			<div class="col-sm-12 bottom_space ">
				<hr />
				<div style="text-align:center">


					<button type="button" class="btn btn-danger"
						onclick="userAction('P','/showUsers');">Back</button>


				</div>
			</div>
		</div>
	</div>

</div>
