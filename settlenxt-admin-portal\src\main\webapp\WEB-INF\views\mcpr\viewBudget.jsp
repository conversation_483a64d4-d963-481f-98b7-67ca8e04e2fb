<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script src="./static/js/validation/mcpr/viewApproveBinExclConfig.js"
	type="text/javascript"></script>
<script src="./static/js/validation/mcpr/viewBinExclRGCS.js"
	type="text/javascript"></script>
<script src="./static/js/validation/mcpr/viewBudget.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	
	<c:url value="getBudget" var="getBudget" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveBudgetConfig" modelAttribute="budgetDTO"
		action="${getBudget}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"></span><spring:message code="budget.viewscreen.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="excId" value="${budgetDTO.budgetId}">

						<input type="hidden" id="crtuser"
							value="${budgetDTO.createdBy}" />

						<table class="table table-striped infobold" style="font-size: 12px">
							<caption style="display:none;">Budget</caption>
							<thead style="display:none;"><th scope = "col"></th></thead>
							<tbody>
								<tr>
									<td><label><spring:message code="budget.budgetId" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.budgetId}</td>
									<td><label><spring:message code="budget.vendor" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.vendor}</td>
									<td><label><spring:message code="budget.budgetName" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.budget}</td>
									<td><label><spring:message code="budget.year" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.displayYear}</td>
								</tr>
								
								
								
							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">

									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/showBudget');"><spring:message code="budget.backBtn" /></button>

								<c:if test="${budgetDTO.requestState =='A' }">
								<sec:authorize access="hasAuthority('Edit Budget')">
								<c:if test="${editBudgetFlag eq 'Y'}">
								<button type="button" class="btn btn-success"
										onclick="viewBudget('${budgetDTO.budgetId}','V','mainTab')">
										<spring:message code="sm.lbl.edit" /></button>
								</c:if>
								</sec:authorize>
								</c:if>
								
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

