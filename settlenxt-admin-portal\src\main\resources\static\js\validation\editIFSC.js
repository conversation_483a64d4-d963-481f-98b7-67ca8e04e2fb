$(document).ready(function () {
    $("#errifscDescription").hide();
    $("#errbankCode").hide();
    $("#errifscCode").hide();
    $("#errnfsCode").hide();
    $("#errsavingsAccId").hide();
    $("#errcurrAccId").hide();
    $("#errrtgsAccId").hide();


    $('#ifscCode').on('keyup keypress blur change',function () {
        validateField('ifscCode', true, "Alphabet", 4, true);
    });
    $('#ifscDescription').on('keyup keypress blur change',function () {
        validateField('ifscDescription', true, "Any", 100, false);
    });
    $('#bankCode').on('keyup keypress blur change',function () {
        validateField('bankCode', true, "Integer", 3, true);
    });
    $('#nfsCode').on('keyup keypress blur change',function () {
        validateField('nfsCode', true, "Alphabet", 3, true);
    });
    $('#savingsAccId').on('keyup keypress blur change',function () {
        validateField('savingsAccId', true, "AlphanumericNoSpace", 35, false);
    });
    $('#currAccId').on('keyup keypress blur change',function () {
        validateField('currAccId', true, "AlphanumericNoSpace", 35, false);
    });
    $('#rtgsAccId').on('keyup keypress blur change',function () {
        validateField('rtgsAccId', true, "AlphanumericNoSpace", 11, true);
    });

disableSave();
	$("#ifscCode").on("keyup keypress blur change", function () {
        enableSave();
    });
	$("#ifscDescription").on("keyup keypress blur change", function () {
        enableSave();
    });
	$("#bankCode").on("keyup keypress blur change", function () {
        enableSave();
    });
	$("#nfsCode").on("keyup keypress blur change", function () {
        enableSave();
    });
	$("#savingsAccId").on("keyup keypress blur change", function () {
        enableSave();
    });
	$("#currAccId").on("keyup keypress blur change", function () {
        enableSave();
    });
	$("#rtgsAccId").on("keyup keypress blur change", function () {
        enableSave();
    });
 
 	$("#status").on("keyup keypress blur change", function () {
        enableSave();
    });
    

});

function disableSave()
{
	if (typeof submitButton != "undefined") {
		document.getElementById("submitButton").disabled = true;
	}
}

function enableSave()
{
	if (typeof submitButton != "undefined") {
		document.getElementById("submitButton").disabled = false;
	}
}


window.history.forward();
function noBack() {
    window.history.forward();
}
function clearFields(){
    $("#ifscDescription").val("");
    $("#bankCode").val("");
    $("#ifscCode").val("");
    $("#nfsCode").val("");
    $("#savingsAccId").val("");
    $("#currAccId").val("");
    $("#rtgsAccId").val("");
    $("#status").val("A");
	$("#errifscDescription").hide();
    $("#errbankCode").hide();
    $("#errifscCode").hide();
    $("#errnfsCode").hide();
    $("#errsavingsAccId").hide();
    $("#errcurrAccId").hide();
    $("#errrtgsAccId").hide();
    }
function display() {
    $(".appRejMust").hide();

}

function addOrUpdateIFSC(action) {
    var isValid = true;

    if (!validateField('ifscCode', true, "AlphabetWithSpace", 100, false) && isValid) {
        isValid = false;
    }
    if (!validateField('ifscDescription', true, "Any", 100, false) && isValid) {
        isValid = false;
    }
    if (!validateField('bankCode', true, "Integer", 3, true) && isValid) {
        isValid = false;
    }

    if (!validateField('nfsCode', true, "Alphabet", 3, true) && isValid) {
        isValid = false;
    }
    if (!validateField('savingsAccId', true, "AlphanumericNoSpace", 35, false) && isValid) {
        isValid = false;
    }
    if (!validateField('currAccId', true, "AlphanumericNoSpace", 35, false) && isValid) {
        isValid = false;
    }
    if (!validateField('rtgsAccId', true, "AlphanumericNoSpace", 11, true) && isValid) {
        isValid = false;
    }
    if (isValid) {
        var data = "ifscCode," + $('#ifscCode').val() + ",ifscDescription," + $('#ifscDescription').val() + ",bankCode," + $('#bankCode').val() + ",nfsCode," + $('#nfsCode').val() + ",savingsAccId," + $('#savingsAccId').val() + ",currAccId," + $('#currAccId').val() + ",rtgsAccId," + $('#rtgsAccId').val() + ",status," + $('#status').val();
        postData(action, data);
    }
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength) {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    var regEx;
    if (isMandatory && fieldValue.trim() == "") {
        isValid = false;
    }
    ( isValid = validateFieldTypeIntAlpha(fieldType, fieldValue, isValid, regEx));
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
    	if(ifscValidationMessages[fieldId]){
    		$("#err" + fieldId).find('.error').html(ifscValidationMessages[fieldId]);
    	}
        $("#err" + fieldId).show();
    }
    return isValid;
}
function validateFieldTypeIntAlpha(fieldType, fieldValue, isValid, regEx) {
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    if (fieldType == "Alphabet") {
        regEx = /^[A-Z]+$/i;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "AlphabetWithSpace") {
        regEx = /^[A-Z ]+$/i;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "AlphanumericNoSpace") {
        regEx = /^[A-Za-z0-9]+$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "Integer") {
        regEx = /^\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return isValid;
}

function navigateTo(action) {
      var data = "";
    postData(action, data);
}
function viewIFSCInfo(ifscCode, action) {
    var data = "ifscCode," + ifscCode ;
    postData(action, data);
}

