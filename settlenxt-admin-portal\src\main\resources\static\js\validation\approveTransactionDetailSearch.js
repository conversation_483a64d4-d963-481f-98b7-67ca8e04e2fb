var pendingStagingIdsList;
var pendingRecordsList;
let isSearchBtnClicked = false;

$(document).ready(function() {
	
	var currentDate = new Date();
	

	var searchFlag = false;
	
	$("#fromDate").datepicker({
		dateFormat : "yy-mm-dd",
		changeMonth : true,
		changeYear : true,
		maxDate: 0,
	//	minDate : 0,
		/*onClose : function(selectedDate) {
			$("#toDate").datepicker("option", "minDate", selectedDate);
		},*/
	onSelect: function(selectedDate){
		const selectedDateTime = new Date($("#fromDate").val());
		const maxDate = calculateMinimumDate(selectedDateTime);
		
		$("#toDate").val($(this).val());
		$("#toDate").datepicker("option", "minDate", selectedDate);
		$("#toDate").datepicker("option", "maxDate", maxDate);
		validateDate();
    }
	});
		
	$("#toDate").datepicker({
		dateFormat : "yy-mm-dd",
		changeMonth : true,
		changeYear : true,
		maxDate: 0,
		onSelect: function(_selectedDate){
			
			validateDate();
	    }
	});
	
	$("#fromDate").val(currentDate.getFullYear() + '-' + ('0' + (currentDate.getMonth() + 1)).slice(-2) + '-' + ('0' + currentDate.getDate()).slice(-2));
	$("#toDate").val(currentDate.getFullYear() + '-' + ('0' + (currentDate.getMonth() + 1)).slice(-2) + '-' + ('0' + currentDate.getDate()).slice(-2));

	$("#rrn").change(function(_e){
		validateRrn();
	});
	
	$("#tokenPan").change(function(_e){
		validateRrn();
	});
	
	if($('#searchType').val() != 'N'){
		$("#status").prop("disabled",false)
	}
	
	
	

	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var searchedFromDate = $('#fromDate').val();
	var searchedToDate = $('#toDate').val();
	var searchedRrn = $('#rrn').val();
	var searchedTokenPan = $('#tokenPan').val();
	var searchedActionCode = $('#searchedActionCode').val();
	if(searchedFromDate !== '' && searchedToDate !== '' && (searchedRrn !== '' || searchedTokenPan !== '' || searchedActionCode !== '')){
		searchFlag = true;
	}else{
	$("#memberBody").addClass("loadingdata");
			myTable1 = $("#example").DataTable({
				"bServerSide": false,
				"bProcessing": true,
				 paging: true,
		         pageLength: 10,
				"sAjaxSource": "approveTransactionData",
				"bJQueryUI": true,
				"sServerMethod": "POST",
				"bDestroy": true,
				"language": {
				    "search": "Search : "
				  },
				  "fnRowCallback": function( nRow, aData, _iDisplayIndex ) {
						var data  = aData[12] + '&' + aData[13] + '&' + aData[14]+ '&' + aData[15]+ '&' + aData[16]+ '&' + aData[17] + '&' + aData[18] + '&' + aData[19] + '&' + aData[20];
						if($('#searchType').val() != 'N'){
							$(nRow).attr('data-row-id', aData[0]); 
							$('td:eq(0)', nRow).html('<input type=checkbox name="type" class="selectBulk" id="selectSingle" onclick="mySelect();" value=\''+aData[0]+'\'></input>');
							$(nRow).unbind('dblclick');
	          		    	$(nRow).bind('dblclick', () => {
								
	                       		viewTransaction(data);
	                    	});
						}/*else{
							$('td:eq(0)', nRow).html('<input type=checkbox style="display:none;" name="type" value=\''+aData[0]+'\'></input>');
						}*/
						return nRow;
					},
				"fnServerParams": function ( aoData ) {
					aoData.push( { "name": "searchType", "value": encodeURI($('#searchType').val() == "" ? "" : $('#searchType').val())},
							{ "name": "_TransactToken", "value": tokenValue}
					);
				},
				"fnServerData": function ( sSource, aoData, fnCallback ) {
					$.ajax({
						"dataType": 'json',
						"type": "POST",
						"url": sSource,
						"data": aoData,
						"success": function(json){
							
							
							
							$("#memberBody").removeClass("loadingdata");
							pendingStagingIdsList = [];
							pendingRecordsList = [];
							if($('#searchType').val() != 'N'){
								pendingRecordsList = json.aaData;
								pendingRecordsList.forEach(function(stageId) {
 									pendingStagingIdsList.push(stageId[0]);
								});
							}
							showBulkApproval();
							fnCallback(json);
						},
						error: function(_jqXHR, _textStatus, _errorThrown) {
							$("#memberBody").removeClass("loadingdata");
 							$('#transitionFetchMsg').show();
						}
					});               
				},
				initComplete: function () {
					 
			            var api = this.api();
			            $('#IsLastLevel').val(NaN);
			 
			            // For each column
			            api
			                .columns()
			                .eq(0)
			                .each(function (colIdx) {
			                    //If first column to be skipped to include the filter for the reasons line check box 
               					 if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
			                    // Set the header cell to contain the input element
			                	 var cell = $('#example thead tr th').eq(
			                        $(api.column(colIdx).header()).index()
			                    );
			                    var title = $(cell).text();
			                    
			                    if(colIdx<actionColumnIndex){
			                    $(cell).html(title+'<br><input class="search-box"   type="text" />');
			 
			                    // On every keypress in this input
			                    $(
			                        'input',
			                        $('#example thead tr th').eq($(api.column(colIdx).header()).index())
			                    )
			                        .off('keyup change')
			                        .on('change', function (_e) {
			                            // Get the search value
			                            $(this).attr('title', $(this).val());
			                            var regexr = '({search})'; 
			 
			                            cursorPosition = this.selectionStart;
			                            // Search the column for that value
			                            api
			                                .column(colIdx)
			                                .search(
			                                    this.value != ''
			                                        ? regexr.replace('{search}', '(((' + this.value + ')))')
			                                        : '',
			                                    this.value != '',
			                                    this.value == ''
			                                )
			                                .draw();
			                        })
									.on('click', function (e) {
			                            e.stopPropagation();})
			                        .on('keyup', function (e) {
			                           e.stopPropagation();
										$(this).trigger('change');
										if (cursorPosition && cursorPosition != null) {
											$(this).focus()[0].setSelectionRange(cursorPosition, cursorPosition);
										}
			                        });
			                        }else{
			                         $(cell).html(title+'<br> &nbsp;');
								}
								}
			                });
			                $('#example_filter').hide();
			                $('.dt-buttons').hide();
			        },
					columnDefs: [
			          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
			        ],
         			"order": [],
			        dom: 'lBfrtip',
					
			        buttons: [
			            {
			                        extend: 'excelHtml5',
			                        text: 'Export',
			                        filename: 'Transaction' ,
									header:'false', 
									title: null,
									sheetName:'Transaction',
									className:'defaultexport',
									exportOptions: {
										 columns: 'th:not(:last-child)'
							         }
			                    },
			                      {
			                        extend: 'csvHtml5',
			                        text: 'Export',
			                        filename: 'Transaction' ,
									header:'false', 
									title: null,
									sheetName:'Transaction',
									className:'defaultexport',
									exportOptions: {
							            columns: 'th:not(:last-child)'
							         }
			                    }
			        ],    
					 
					searching: true,
					info: true
			});
}
			$('#resetBtn').click(function(){
				isSearchBtnClicked = false;
				var type = $('#status').val() == 'P'?'P':'N';
				$('#fromDate').val('');
				$('#toDate').val('');
				$('#rrn').val('');
				$('#tokenPan').val('');
				$('#status').val('');
				$('#funcCode').val(0);
				document.querySelectorAll(".error").forEach(ele => {
	    			ele.innerHTML = ''
				});
				let url = '/approveTransaction';
				var data = "searchType," + type;
				postData(url, data);
		});
	   
	   $("#excelExport").on("click", function () {
	        $(".buttons-excel").trigger("click");
	    });
	    
	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
	 
	     $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
		
	$('#searchButon').click(function() {
		isSearchBtnClicked = true;
	$('#transitionFetchMsg').hide();
	 tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var validDate = validateDate();
	var validRrn = false;
	if(validDate){
		validRrn = validateRrn();
	}

	if (validDate && validRrn ){
			$("#memberBody").addClass("loadingdata");
		    $('#tableList').show(); 
			myTable2 =$("#example").DataTable({
				"bServerSide": false,
				"bProcessing": false,
				/*"bSort":false,
				"bFilter":true,	*/
				 paging: true,
		         pageLength: 10,
				"sAjaxSource": "searchPendingApprRejectTran",
				
				"bJQueryUI": true,
				"sServerMethod": "POST",
				"bDestroy": true,
				"language": {
				    "search": "Search : "
				  },
				"fnRowCallback": function( nRow, aData, _iDisplayIndex ) {
					var data  = aData[13] + '&' + aData[14] + '&' + aData[15]+ '&' + aData[16]+ '&' + aData[17]+ '&' + aData[18] + '&' + aData[19] + '&' + aData[20] ;
					
					if(aData[12] == 'P' || aData[12] == 'RPA'){
							$(nRow).attr('data-row-id', aData[0]); 
							$('td:eq(0)', nRow).html('<input type=checkbox name="type" class="selectBulk" id="selectSingle" onclick="mySelect();" value=\''+aData[0]+'\'></input>');
							$(nRow).unbind('dblclick');
	          		    	$(nRow).bind('dblclick', () => {
	                       		viewTransaction(data);
	                    	});
						}
					return nRow;
				},
				"fnServerParams": function ( aoData ) {
					aoData.push( { "name": "fromDate", "value": encodeURI($('#fromDate').val() == "" ? "" : $('#fromDate').val())},
							{ "name": "toDate", "value": encodeURI($('#toDate').val() == "" ? "" : $('#toDate').val())},
							{ "name": "rrn", "value": encodeURI($('#rrn').val() == "" ? "" : $('#rrn').val())},
							{ "name": "tokenPan", "value": encodeURI($('#tokenPan').val() == "" ? "" : $('#tokenPan').val())},
							{ "name": "actionCode", "value": encodeURI($('#searchedActionCode').val() == "" ? "" : $('#searchedActionCode').val())},
							{ "name": "partId", "value": encodeURI($('#partId').val() == "" ? "" : $('#partId').val())},
							{ "name": "status", "value": encodeURI($('#status').val() == "" ? "" : $('#status').val())},
							{ "name": "_TransactToken", "value":tokenValue}
					);
				},
				"fnServerData": function ( sSource, aoData, fnCallback ) {
					$.ajax({
						"dataType": 'json',
						"beforeSend": function (xhr) {
		                    xhr.setRequestHeader('_TransactToken', tokenValue);
		                },
						"type": "POST",
						"url": sSource,
						"data": aoData,
						"success": function(json){
							
							
							
							$("#memberBody").removeClass("loadingdata");
							pendingStagingIdsList = [];
							pendingRecordsList = [];
							if($('#status').val() == 'P'){
								pendingRecordsList = json.aaData;
								pendingRecordsList.forEach(function(stageId) {
 									pendingStagingIdsList.push(stageId[0]);
							});
							}
							showBulkApproval();
							fnCallback(json);
						},
						error: function(_jqXHR, _textStatus, _errorThrown) {
							$("#memberBody").removeClass("loadingdata");
 							$('#transitionFetchMsg').show();
						}
					});               
				},
				initComplete: function () {
					 
			            var api = this.api();
			            
			            $('#IsLastLevel').val(NaN);
			 
			            // For each column
			            api
			                .columns()
			                .eq(0)
			                .each(function (colIdx) {
			                   //If first column to be skipped to include the filter for the reasons line check box 
               					 if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
			                    // Set the header cell to contain the input element
			                	 var cell = $('#example thead tr th').eq(
			                        $(api.column(colIdx).header()).index()
			                    );
			                    var title = $(cell).text();
			                    
			                    if(colIdx<actionColumnIndex){
			                    	$(cell).html(title+'<br><input class="search-box"   type="text" />');
			 
			                    // On every keypress in this input
			                    $(
			                        'input',
			                        $('#example thead tr th').eq($(api.column(colIdx).header()).index())
			                    )
			                        .off('keyup change')
			                        .on('change', function (_e) {
			                            // Get the search value
			                            $(this).attr('title', $(this).val());
			                            var regexr = '({search})'; 
			 
			                            cursorPosition = this.selectionStart;
			                            // Search the column for that value
			                            api
			                                .column(colIdx)
			                                .search(
			                                    this.value != ''
			                                        ? regexr.replace('{search}', '(((' + this.value + ')))')
			                                        : '',
			                                    this.value != '',
			                                    this.value == ''
			                                )
			                                .draw();
			                        })
									.on('click', function (e) {
			                            e.stopPropagation();})
			                        .on('keyup', function (e) {
			                            e.stopPropagation();
										$(this).trigger('change');
										if (cursorPosition && cursorPosition != null) {
											$(this).focus()[0].setSelectionRange(cursorPosition, cursorPosition);
										}
			                        });
			                        }else{
			                         $(cell).html(title+'<br> &nbsp;');
								}
								}
			                });
			                $('#example_filter').hide();
			                $('.dt-buttons').hide();
			        },
					columnDefs: [
			          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
			        ],
         			"order": [],
			        dom: 'Bfrtip',
					
			        buttons: [
			            {
			                        extend: 'excelHtml5',
			                        text: 'Export',
			                        filename: 'Transaction' ,
									header:'false', 
									title: null,
									sheetName:'Transaction',
									className:'defaultexport',
									exportOptions: {
										 columns: 'th:not(:last-child)'
							         }
			                    },
			                      {
			                        extend: 'csvHtml5',
			                        text: 'Export',
			                        filename: 'Transaction' ,
									header:'false', 
									title: null,
									sheetName:'Transaction',
									className:'defaultexport',
									exportOptions: {
							            columns: 'th:not(:last-child)'
							         }
			                    }
			        ],    
					 
					searching: true,
					info: true
			});
 }
});

$("#selectAll").click(function(){
	var myTable;		  
	if(!isSearchBtnClicked){
		myTable = myTable1;
	}else{
		myTable = myTable2;
	}
			
	// 	Fetch all row based on filter
	var filteredData = myTable.rows({ search: 'applied' }).nodes();

	if ($(this).hasClass('allChecked')) {
		// Uncheck all filtered checkboxes
		$(filteredData).find('input.selectBulk').prop('checked', false);
	} else {
		// Check all filtered checkboxes
		$(filteredData).find('input.selectBulk').prop('checked', true);
	}
	$(this).toggleClass('allChecked');

	//	 Access the custom attribute
	pendingStagingIdsList = [];
	$(filteredData).each(function() {
		var rowId = $(this).attr('data-row-id');
		pendingStagingIdsList.push(rowId);
	});	
	     		if( $('#selectAll').is(':checked') )
			  	{	
				  	$("#toggleModalDispute").modal('show');	
			 	}
			 else
		     {
				 $("#toggleModalDispute").modal('hide');
				 
			 var ele=document.getElementsByName('type');  
		     for(var i of ele){  
		         if(i.type=='checkbox')  
		             i.checked=false;  
		     } 
		     } 
			   var stageIdNoList = document.getElementById("stagingIds");
			   stageIdNoList.innerHTML = pendingStagingIdsList.length+"     "+"records are selected";

			
		});
		
		if(searchFlag){
			$("#searchButon").click();
		}
});


function showBulkApproval() {
	if($("#showCheckBox").val() == 'Y' && pendingStagingIdsList.length == 0){
		document.getElementById("selectAll").style.display = "none";
		document.querySelectorAll("#submitButton").forEach(ele => {
			ele.disabled = true;
		});
	} else if ($("#showCheckBox").val() == 'Y' && pendingStagingIdsList.length != 0) {
		document.getElementById("selectAll").style.display = "inline";
		document.querySelectorAll("#submitButton").forEach(ele => {
			ele.disabled = false;
		});
	}
}

function calculateMinimumDate(selectedDate) {
	const currentDateTime = new Date();
	const newDateTime = new Date(selectedDate.getTime() + 86400000*7);
	if (newDateTime.getTime() > currentDateTime.getTime()) {
		return currentDateTime;
	} else {
		return newDateTime;
	}
}


function viewTransaction(data){
	
	var url;
	url = '/transactionDetail';
	
	var txnId = data.split('&')[0];
	var funcCode = data.split('&')[1];
	var mti = data.split('&')[2];
	var originalTableName = data.split('&')[3];
	var acquirerReferenceData = data.split('&')[4];

	
	var caseNumber = data.split('&')[5];
	var orgFuncCode = data.split('&')[6];
	var mappedDupData = data.split('&')[7];
	var txnStatus = data.split('&')[8];
	
	data = "txnId,"+txnId +",funcCode,"+funcCode +",mti,"+mti + ",originalTableName," +originalTableName + ",acquirerReferenceData," + acquirerReferenceData + ",caseNumber," + caseNumber + ",orgFuncCode," + orgFuncCode + ",mappedDupData," + mappedDupData + ",txnStatus," + txnStatus;

	postData(url, data);
}


function clickAndDisable(link) {
    // disable subsequent clicks
    link.onclick = function(event) {
       event.preventDefault();
    }
  } 

function urlPostActionWithData(action, additionalData) {
	var data;
	if(additionalData != undefined && additionalData != null){
		data=data+","+additionalData;
	}
	postData(action, data);
}

function validateDate()
{
	var result = true;
	
	if (($('#fromDate').val() == "") && ($('#toDate').val() == "")) {
		$('#dterr').text('Please select from and to date');
		result =  false;
		} else {	$('#dterr').text('');
		}
		if (($('#fromDate').val() == "") && ($('#toDate').val() != "")) {
		$('#errFromDate').text('Please select from date');
		result =  false;
		} else {
			$('#errFromDate').text('');

		}
		if (($('#fromDate').val() != "") && ($('#toDate').val() == "")) {
		$('#errToDate').text('Please select to date');
		result =  false;
		} else {
		$('#errToDate').text('');
		}
		if (Date.parse($('#fromDate').val()) > Date.parse($('#toDate').val())) {
		$('#errDate').text('Invalid date range');
		result =  false;
		} else {
		$('#errDate').text('');
		}
	
	return result;
}

function validateRrn() {
	var result = true;
	var valueRrn=$('#rrn').val().trim();
	var valuePan=$('#tokenPan').val().trim();
	
	var valueStatus = $('#status').val();
	var regEx = /^\d+$/i;
	
	

	if(valueRrn != ""){
		if (!regEx.test(valueRrn)) {
			$('#errrrn').html('Please enter only numbers');
			result =  false;
		}else if(valueRrn.length > 12 || valueRrn.length < 12){
			$('#errrrn').html('RRN should be of 12 digits');
			result =  false;
		}
	}

	
	
	
	if(valuePan != ""){
		if(valuePan.length<12 && valuePan.length>0){
			$('#errtokenPan').html('Please enter atleast 12 characters');
			result =  false;
		}
	}else{
		$('#errtokenPan').html("");
	}
	
	if(result){
		$('#errrrn').html("");
		$('#errtokenPan').html("");
	}
	if(valueStatus == ""){
		$('#errStatus').html('Please select the status');
		result =  false;
	}else{
		$('#errStatus').html("");
	}
	return result;
}

function getPendingApproveRejectList(type){
	var url = '/approveTransaction';
	var data = "searchType," + type;
	postData(url, data);
}

function mySelect(){
	 var array = [];
	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	  var stageIdList = document.getElementById("stagingIds");
     
	 if(array.length==pendingStagingIdsList.length){
		 $('#selectAll').prop('checked', true);
		   stageIdList.innerHTML = pendingStagingIdsList.length+"     "+"records are selected";

			 $("#toggleModalDispute").modal('show');
	 }
	 else{
		 $("#toggleModalDispute").modal('hide');
		 
	 }
	
}

function deselectAll() {
	$('#selectAll').prop('checked', false);
	$('#selectAll').removeClass('allChecked');
	var ele=document.getElementsByName('type');  
   for(var i of ele){  
       if(i.type=='checkbox')  
           i.checked=false;  
   } 
   
}

function ApproveorRejectDispute(type,action){
	$('.approveButton').prop("disabled",true);
	$('.rejectButton').prop("disabled",true)
	$('#toggleModalDispute').modal('hide');
	 var url = '/approveRejectDisputeBulk';
	var data;
	 var array = [];

	 if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(action=='All'){
		 array=pendingStagingIdsList; 
	 }

	var stageIdList = "";
	for ( var i of array) {
			stageIdList = stageIdList + i + "|";
	}
		
	if(array.length > 0){
		if(type=='A'){
				
			var data =  "status,"+encodeURI("IP") + ",bulkApprovalStageIdList," + stageIdList;
		}
		else if(type=='R'){
			
			var data = "status,"+encodeURI("FAILED") + ",bulkApprovalStageIdList," + stageIdList;
		}
		$("#memberBody").addClass("loadingdata");
		postData(url, data);
	}else{
		$("#emptyListMsg").show();
		$('.approveButton').prop("disabled",false);
		$('.rejectButton').prop("disabled",false);
		document.getElementById("navbar").scrollIntoView({behavior:"smooth"});
	}
}

function refreshButton(type, action) {
	let url = action;
	var data = "searchType," + type;
	postData(url,data);
}


