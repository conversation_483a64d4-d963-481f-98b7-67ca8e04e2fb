package org.npci.settlenxt.adminportal.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.portal.common.dto.BinDTO;
import org.npci.settlenxt.portal.common.dto.BinDetailsDTO;
import org.npci.settlenxt.portal.common.dto.CityDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.CountryDTO;
import org.npci.settlenxt.portal.common.dto.FeatureFeeDTO;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;
import org.npci.settlenxt.portal.common.dto.MemberContactInfoDTO;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.MemberOnBoardingDTO;
import org.npci.settlenxt.portal.common.dto.SettlementBinDTO;
import org.npci.settlenxt.portal.common.dto.StateDTO;
import org.npci.settlenxt.portal.common.repository.BaseMemberRepository;

@Mapper
public interface MemberRepository extends BaseMemberRepository {
	void addMember(MemberDTO memberDTO);

	void addMemberMain(MemberDTO memberDTO);

	long checkIfParticipantStgExists(String participantId);

	long checkIfParticipantExists(String participantId);

	long checkIfParticipantContactExists(String participantId);

	long checkIfParticipantContactExistsMain(String participantId);

	int addMemberMainTable(MemberDTO memberDTO);

	Integer addMemberContactInfo(MemberDTO memberDTO);

	Integer addMemberContactInfoMainTable(MemberDTO memberDTO);

	int addSettlementBin(SettlementBinDTO settlementBin);

	int addSettlementBinMainTable(SettlementBinDTO settlementBin);

	Integer updateDefaultSettlementBin(String participantId);

	Integer addAcqBin(BinDetailsDTO binDetails);

	Integer addAcqBinMain(BinDetailsDTO binDetails);

	Integer updateAcqBin(BinDetailsDTO binDetails);

	Integer addIssBin(BinDetailsDTO binDetails);

	Integer addIssBinMain(BinDetailsDTO binDetails);

	Integer updateIssBin(BinDetailsDTO binDetails);

	Integer addFileDetails(FileUploadDTO fileUpload);

	Integer addFileDetailsMain(FileUploadDTO fileUpload);

	List<String> getFilesPaths(@Param("fileNames") List<String> fileNames,
			@Param("participantId") String participantId);

	Integer updateFileDetails(FileUploadDTO fileUpload);

	Integer finalMemberSubmit(Integer memberId);

	Integer memberApproveStg(@Param("participantId") String participantId, @Param("rejectReason") String rejectReason,
			@Param("lastUpdatedOn") Date lastUpdatedOn, @Param("userName") String userName,
			@Param("recordStatus") String recordStatus, @Param("rejectedOn") Date rejectedOn);

	Integer finalMemberReject(@Param("participantId") String participantId);

	Integer stgMemberReject(@Param("participantId") String participantId, @Param("rejectReason") String rejectReason);

	List<BinDetailsDTO> getSettlementBins(@Param("memberId") Integer memberId, @Param("binType") String binType);

	List<SettlementBinDTO> getsettlementBinsList(@Param("participantId") String participantId,
			@Param("statusList") List<String> statusList);

	int updateUnallocatedBin(@Param("binList") List<BinDetailsDTO> binList, @Param("status") String status,
			@Param("userId") String userId);

	List<BinDetailsDTO> getAcqBinDetails(@Param("participantId") String participantId, @Param("binType") String binType,
			@Param("status") String status);

	List<BinDetailsDTO> getAcqBinDetailsForPendingForApproval(@Param("participantId") String participantId,
			@Param("binType") String binType, @Param("status") String status);

	List<BinDetailsDTO> getAcqBinDetailsMain(@Param("participantId") String participantId,
			@Param("binType") String binType, @Param("status") String status);

	List<BinDetailsDTO> getBinDetails(@Param("participantId") String participantId, @Param("binType") String binType,
			@Param("status") String status,@Param("isInternational")boolean isInternational);

	List<BinDetailsDTO> getBinDetailsForPendingForApproval(@Param("participantId") String participantId,
			@Param("binType") String binType, @Param("status") String status,@Param("isInternational")boolean isInternational);

	List<BinDetailsDTO> getUnallocatedIssuerAndTokenBinDetails();

	List<BinDetailsDTO> getIssuerAndTokenBinDetails(@Param("participantId") String participantId,@Param("isInternational")boolean isInternational);

	List<BinDetailsDTO> getIssuerAndTokenBinDetailsForPendingForApproval(@Param("participantId") String participantId,@Param("isInternational")boolean isInternational);

	List<BinDetailsDTO> getIssuerAndTokenBinDetailsMain(@Param("participantId") String participantId,@Param("isInternational")boolean isInternational);

	List<BinDetailsDTO> getIssBinDetailsFetch(@Param("participantId") String participantId,
			@Param("binType") String binType, @Param("status") String status);

	List<BinDetailsDTO> getAcqBinList(@Param("participantId") String participantId,
			@Param("statusList") List<String> statusList);

	List<BinDetailsDTO> getAcqBinListMain(@Param("participantId") String participantId,
			@Param("statusList") List<String> statusList);

	List<BinDetailsDTO> getIssBinList(@Param("participantId") String participantId,
			@Param("statusList") List<String> statusList,@Param("isInternational")boolean isInternational);

	List<BinDetailsDTO> getIssBinListMain(@Param("participantId") String participantId,
			@Param("statusList") List<String> statusList);

	List<FileUploadDTO> getFilesList(@Param("participantId") String participantId,
			@Param("fileIDs") List<Integer> fileIDs);

	Integer checkMemberContactInfoSaved(String participantId);

	int fetchParticipantIdSeq(String ifscCode);

	MemberDTO getMember(@Param("participantId") String participantId);

	MemberDTO getMemberMain(@Param("participantId") String participantId);

	MemberContactInfoDTO getMemberContactInfo(@Param("participantId") String participantId);

	MemberContactInfoDTO getMemberContactInfoMain(@Param("participantId") String participantId);

	List<MemberDTO> getMembers();

	List<MemberDTO> getPendingMembers(@Param("startVal") int startVal, @Param("endVal") int endVal,
			@Param("sSearch") String sSearch);

	List<StateDTO> getStateNameList(@Param("participantId") String participantId);

	List<CityDTO> getCityNameList(@Param("participantId") String participantId);

	List<CountryDTO> getCountNameList(@Param("participantId") String participantId);

	List<CountryDTO> getGstCountryNameList(@Param("participantId") String participantId);

	List<StateDTO> getGstStateNameList(@Param("participantId") String participantId);

	List<CityDTO> getGstCityNameList(@Param("participantId") String participantId);

	Integer getMemberCount(@Param("participantId") String participantId);

	int fetchMemberIdSeq();

	Integer addAcquirerBinListMainTable(@Param("acqBinList") List<BinDetailsDTO> acqBinList);

	Integer addIssBinListMainTable(@Param("issBinList") List<BinDetailsDTO> issBinList);

	List<MemberDTO> getFinalMemberList(@Param("startVal") int startVal, @Param("endVal") int endVal,
			@Param("sSearch") String sSearch);

	List<MemberDTO> getTempMemberList();

	Integer updateSettlementBin(SettlementBinDTO settlementBin);

	Integer updateMemberBin(BinDetailsDTO binDetails);

	Integer updateIssMemberBin(BinDetailsDTO binDetails);

	List<MemberDTO> getMemberFinal();

	MemberOnBoardingDTO getMemberEdit(@Param("participantId") String participantId,@Param("isInternational")boolean isInternationals);

	void deleteFileList(@Param("participantId") String participantId);

	MemberOnBoardingDTO getMemberMainView(@Param("participantId") String participantId,@Param("isInternational")boolean isInternational);

	int updateMemberInfo(MemberOnBoardingDTO memberOnBoardingDTO);

	int updateMemberInfoMain(MemberOnBoardingDTO memberOnBoardingDTO);

	Integer updateMemberContactInfo(MemberOnBoardingDTO memberOnBoardingDTO);

	Integer updateMemberContactInfoMain(MemberOnBoardingDTO memberOnBoardingDTO);

	int updateMemberStgInfo(MemberOnBoardingDTO memberOnBoardingDTO);

	List<MemberDTO> getMembersSB();

	int fetchBinIdSeq();

	Integer updateMemberMainTable(MemberDTO memberDTO);

	Integer updateMemberContactFinal(MemberDTO memberDTO);

	Integer deleteDocumentMain(@Param("participantId") String participantId);

	Integer deleteMemberBin(@Param("participantId") String participantId);

	Integer deleteUserBinMain(@Param("participantId") String participantId, @Param("binId") Integer binId);

	Integer deleteUserBinStg(@Param("participantId") String participantId, @Param("binId") Integer binId);

	Integer deleteUserBinMainWithParticipantID(@Param("participantId") String participantId);

	Integer deleteUserBinStgWithParticipantID(@Param("participantId") String participantId);

	int duplicateSettlementBinCheck(@Param("settleBinNo") String settleBinNo);

	List<SettlementBinDTO> getSettlmentBinsForOtherParticipants(@Param("participantId") String participantId,
			@Param("settlementBinNumberList") List<String> settlementBinNumberList);

	List<SettlementBinDTO> getSettlmentBinsForParticipants(@Param("participantId") String participantId);

	List<BinDetailsDTO> getAcquirerBinsForOtherParticipants(@Param("participantId") String participantId,
			@Param("acqBinList") List<String> acqBinList);

	List<BinDetailsDTO> getAcquirerBinsForParticipants(@Param("participantId") String participantId);

	List<BinDetailsDTO> getAcquirerBinsForParticipantsDuplicateCheck(@Param("participantId") String participantId);

	List<SettlementBinDTO> getSettlementBins(@Param("participantId") String participantId);

	List<SettlementBinDTO> getSettlementBinsMain(@Param("participantId") String participantId, boolean isInternational);

	List<BinDetailsDTO> getIssuerBinsForOtherParticipants(@Param("participantId") String participantId,
			@Param("issBinList") List<String> issBinList);

	List<BinDetailsDTO> getIssuerBinsForParticipants(@Param("participantId") String participantId);

	List<BinDetailsDTO> getIssuerBinsForParticipantsDuplicateCheck(@Param("participantId") String participantId);

	int duplicateAcqBinCheck(@Param("acquirerId") String acqId, @Param("participantId") String participantId);

	int duplicateIssBinCheck(@Param("binNo") String binNo, @Param("participantId") String participantId);

	Integer deleteIssBinStg(@Param("binNo") String binNo, @Param("participantId") String participantId,
			@Param("status") String status);

	Integer deleteAcqBinStg(@Param("acqId") String acqId, @Param("participantId") String participantId,
			@Param("status") String status);

	Integer deleteIssBinFinal(@Param("binNo") Integer binNo, @Param("participantId") String participantId);

	Integer deleteAcqBinFinal(@Param("acqId") Integer acqId, @Param("participantId") String participantId);

	Integer deleteSettlementBinStg(@Param("settleBinNo") String settleBinNo,
			@Param("participantId") String participantId, @Param("status") String status);

	Integer deleteSettlementBinFinal(@Param("participantId") String participantId);

	Integer blockUnblockIssBinStg(@Param("binNo") String binNo, @Param("participantId") String participantId,
			@Param("status") String status);

	Integer blockUnblockAcqBinStg(@Param("acqId") String acqId, @Param("participantId") String participantId,
			@Param("status") String status);

	Integer blockUnblockSettlementBinStg(@Param("settleBinNo") String settleBinNo,
			@Param("participantId") String participantId, @Param("status") String status);

	int discardMemberData(MemberOnBoardingDTO memberOnBoardingDTO);

	Integer deleteMemberStg(MemberOnBoardingDTO memberOnBoardingDTO);

	int updateMemberInfoStg(MemberOnBoardingDTO memberOnBoardingDTO);

	Integer updateMemberContactInfoStg(MemberOnBoardingDTO memberOnBoardingDTO);

	MemberOnBoardingDTO getMemberFinalDiscard(@Param("participantId") String participantId);

	Integer deleteMemberBinStgAll(MemberOnBoardingDTO memberOnBoardingDTO);

	Integer deleteSettlementBinStgAll(MemberOnBoardingDTO memberOnBoardingDTO);

	List<SettlementBinDTO> getsettlementBinsListAll(@Param("participantId") String participantId);

	List<BinDetailsDTO> getBinDetailsFinal(@Param("participantId") String participantId,
			@Param("binType") String binType,@Param("isInternational")boolean isInternational);

	List<BinDetailsDTO> getAcqBinDetailsFinal(@Param("participantId") String participantId,
			@Param("binType") String binType);

	Integer addBinListStg(@Param("binList") List<BinDetailsDTO> binList);

	Integer addAcqBinListStg(@Param("binList") List<BinDetailsDTO> binList);

	Integer addIssBinListStg(@Param("binList") List<BinDetailsDTO> binList,@Param("isInternational")boolean isInternational);

	Integer addSettlementBinListStg(@Param("binList") List<SettlementBinDTO> binList);

	List<MemberDTO> getSavedMemberList(@Param("startVal") int startVal, @Param("endVal") int endVal,
			@Param("sSearch") String sSearch);

	long getTotalMemberCount();

	long getPendingMemberCount();

	long getSavedMemberCount();

	Integer addSettlementBinFinal(@Param("binList") List<SettlementBinDTO> binList);

	int checkSettlementBinReference(@Param("settleBinNo") String settleBinNo,
			@Param("participantId") String participantId);

	List<FileUploadDTO> getFileList(@Param("participantId") String participantId);

	List<FileUploadDTO> getFileListMain(@Param("participantId") String participantId);

	void deleteSettlementBin(String string);

	List<FeatureFeeDTO> getFeatureListForAddandEditMember();

	int duplicateDataForSettlementBinCheck(@Param("settleBinNo") String settleBinNo,
			@Param("isDefault") String isDefault);

	List<BinDTO> getUserBinStg(@Param("participantId") String participantId);

	List<BinDTO> getUserBinMain(@Param("participantId") String participantId);

	List<BinDTO> getUserBinStgWithSettlementBin(@Param("participantId") String participantId);

	List<BinDTO> getUserBinStgWithAcquireBin(@Param("participantId") String participantId);

	List<BinDTO> getUserBinStgWithIssuerBin(@Param("participantId") String participantId);

	List<BinDTO> getUserBinStgWithAcquireBinMain(@Param("participantId") String participantId);

	int deleteMemberContact(@Param("participantId") String participantId);

	List<BinDTO> getUserBinStgWithIssuerBinMain(@Param("participantId") String participantId);

	void addUserBinMain(@Param("userbinMain") List<BinDTO> userbinMain);

	void addUserBinStg(@Param("userbin") List<BinDTO> userbin);

	List<BinDetailsDTO> getAcqBinListBulk(@Param("participantIdList") List<String> participantIdList,
			@Param("statusList") List<String> statusList);

	List<BinDetailsDTO> getIssBinListBulk(@Param("participantIdList") List<String> participantIdList,
			@Param("statusList") List<String> statusList);

	List<MemberDTO> getMemberBulk(@Param("participantIdList") List<String> participantIdList);

	List<MemberContactInfoDTO> getMemberContactInfoBulk(@Param("participantIdList") List<String> participantIdList);

	Integer deleteIssBinStgForNonActiveMember(@Param("binNo") String binNo,
			@Param("participantId") String participantId);

	Integer deleteAcqBinStgForNonActiveMember(@Param("acqId") String acqId,
			@Param("participantId") String participantId);

	Integer deleteSettlementBinStgForNonActiveMember(@Param("settleBinNo") String settleBinNo,
			@Param("participantId") String participantId);

	Integer getparticipantContactCount(@Param("participantId") String participantId);

	Integer getparticipantSettlementBinCount(@Param("participantId") String participantId);

	void insertNetwBinRange(BinDetailsDTO binDetailsDto);

	void updateNetBinRange(String binNumber, String status, String dxsCode, String licenseId);

	Optional<BinDetailsDTO> getNetBinDetail(String bin);

	void clearCurrentDeleteFlag(@Param("participantId") String participantId);

	int duplicateSettlementBinCheckSave(String participantId, String settlementBinNumber);

	List<BinDetailsDTO> getAcqBinDetailsDeleteInsert(@Param("participantId") String participantId,
			@Param("binType") String binType, @Param("status") String status);

	List<BinDetailsDTO> getBinDetailsForDeleteBin(String participantId, String binType, String activeBinStatus,boolean isInternational);

	List<BinDetailsDTO> getIssuerAndTokenBinDetailsForDeleteBin(String participantId,boolean isInternational);

	List<CodeValueDTO> getNetworkSelection();

	List<CodeValueDTO> getForexId();

	void addBinNetworkMappingDetails(List<String> networkList, BinDetailsDTO issBinList, String participantId,
			String status);

	List<String> getNetworkSelectionList(String binNumber, String participantId);

	void deletePreviousBin(String binNumber);

	List<String> getNetworkSelectionListMain(String binNumber,String participantId);

	void addBinNetworkMappingDetailsMain(List<BinDetailsDTO> networkList, BinDetailsDTO issBinList,
			String participantId);

	void deletePreviousBinMain(String binNumber);

	List<String> getNetworkSelectionListAll(String binNumber, String participantId);

	List<BinDetailsDTO> getNetworkSelectionListMainAll(String binNumber, String participantId);

	void deletePreviousBinByParticipantId(String participantId);

	List<BinDetailsDTO> getNetworkSelectionListStg(String binNumber, String participantId);

	void updateBinNetworkMappingDetails(String network, BinDetailsDTO binDetailsDTO,
			String participantId, String status);

	void deleteStgDetailsBySeqId(int seqId);

	Optional<BinDetailsDTO> getMemberBinDetailsByAcqId(String acquirerId);

	Optional<BinDetailsDTO> getNetBinDetailsByAcqId(String acquirerId);

	void updateNetBinDetailsForAcqId(BinDetailsDTO binDetailsDto);

	
}
