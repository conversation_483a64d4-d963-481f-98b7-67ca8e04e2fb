$(document).ready(function() {
	
	
	  $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});


	
	
	

	
});function display() {
	$(".appRejMust").hide();
}


function submitForm(url) {
	var data = "" ;
urlPostAction(data,url);
}

function viewEscalationInfo(memberId, url,editFlag) {
	
	var data = "memberId," + escape(memberId) +",editFlag,"+editFlag;
	postData(url, data);
}


function getEscalationList(){
	let url = '/pendingEscalationForApproval';
	var data = "";
urlPostAction(data,url);
}
function postAction(_action) {


let data="";
let memberId="";
let remarks="";
let url="";
	if(maxLengthTextArea('rejectReason')){
	if ($('#apprej option:selected').val() == "A") {
		if ($("#rejectReason").val() != "") {
			
		 memberId = $("#memberId").val();
			 remarks = $("#rejectReason").val();
			 url = '/approveOrRejectEscalation';
			 data = "status," + "A" + ",remarks,"
					+ remarks+ ",memberId," + memberId;
			postData(url, data);
		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else if ($('#apprej option:selected').val() == "R") {
		if ($("#rejectReason").val() != "") {
				
				 remarks = $("#rejectReason").val();
				 memberId = $("#memberId").val();
				 url = '/approveOrRejectEscalation';
				 data = "status," + "R"  + ",remarks,"
						+ remarks + ",memberId," + memberId;
				postData(url, data);
			

		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else {
		$(".appRejMust").show();
		$('html, body').animate({ scrollTop: 0 }, 'slow');
		return false;
	}
	}
}