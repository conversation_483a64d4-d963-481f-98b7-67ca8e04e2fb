package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import org.osgi.service.component.annotations.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Component
@Data
@EqualsAndHashCode(callSuper=false)
@ToString

public class ForexRateDTO {

	
	private int forexRateId;
	private String networkId;
	private double rateConversion;
	private Date settleDate;
	private Date date;
	private String currencyFrom;
	private String currencyTo;
	private String networkIdLookup;
	private String currencyFromLookup;
	private String currencyToLookup;
	
	private String checkerComments;
	private String lastOperation;
	private String requestState;
	private String addEditFlag;
	
	private String status;
	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private String userName;

}
