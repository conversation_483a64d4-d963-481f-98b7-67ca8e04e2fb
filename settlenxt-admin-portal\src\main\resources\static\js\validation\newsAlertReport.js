$(document).ready(function() {

$("#fromDateStr").datepicker({
        
       
        changeMonth : true,
        changeYear : true,
        minDate: "-3y",
        yearRange: '1900:2099',
        dateFormat: 'yy-mm-dd',
        onClose: function (selectedDate, instance) {
            if (selectedDate != '') { //added this to fix the issue               
             $("#toDateStr").datepicker("option", "minDate", selectedDate);
                var date = $.datepicker.parseDate(instance.settings.dateFormat, selectedDate, instance.settings);
                date.setMonth(date.getMonth() + 1);
                console.log(selectedDate, date);
                $("#toDateStr").val(selectedDate);
                $("#toDateStr").datepicker("option", "minDate", selectedDate);
                
            }
            validateFromDate('fromDateStrErr');
        }
    });
    $("#toDateStr").datepicker({
        
    	changeMonth : true,
    	changeYear : true,
    	yearRange: "1900:2099",
      
        dateFormat: 'yy-mm-dd',
        endDate: "today",
        onClose: function (_selectedDate) {
        	validateToDate('toDateStrErr');  
        }
    });


});

function submitForm(_url) {

var check=false;
if (!validateFromDate('fromDateStrErr')) {
check=true;
}
if (!validateToDate('toDateStrErr')) {
check=true;
}
if(!check){
if(!validateFromToDate('fromDateStrErr')){
check=true;
}else{
checkCount();
}


}						

	
return check;
	
}

function validateFromDate(_msgID) {
var dateString = (document.getElementById("fromDateStr").value).replace("/^\s*|\s*$/g", '');
if (dateString == "") {
	
	$("#fromDateStrErr").find('.error').html("Please enter From Date");
	
	$('#fromDateStrErr').show();


return false;
} else{
	
$("#fromDateStrErr").find('.error').html(" ");
	
	$('#fromDateStrErr').hide();

}
return true;
}
function validateToDate(_msgID) {
var dateString = (document.getElementById("toDateStr").value).replace("/^\s*|\s*$/g", '');
if (dateString == "") {
	
$("#toDateStrErr").find('.error').html("Please enter To Date");
	
	$('#toDateStrErr').show();

return false;
} else{
	

	$("#toDateStrErr").find('.error').html("");
		
		$('#toDateStrErr').hide();

}
return true;
}
function validateFromToDate(_msgID) {
var fromDateStr = $('#fromDateStr').val() ;
var toDateStr = $('#toDateStr').val() ;
if(fromDateStr!="" && toDateStr!=""){
if (Date.parse(fromDateStr) >  Date.parse(toDateStr)) {
	
$("#fromDateStrErr").find('.error').html("From Date cannot be greater than To Date");
	
	$('#fromDateStrErr').show();

return false;
}
$("#fromDateStrErr").find('.error').html("");

$('#fromDateStrErr').hide();

return true;
}
}

function checkCount(){

var fromDateStr = document.getElementById("fromDateStr").value;
var toDateStr = document.getElementById("toDateStr").value;
var data = "fromDateStr," + fromDateStr + ",toDateStr," + toDateStr ;
	let url="";
	
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	 $.ajax({
    	 url : "getCountNewsAndAlertReports",
        type: "POST",
		dataType : "json",
				data: {"fromDateStr" : fromDateStr,"toDateStr" : toDateStr,
					    "_TransactToken" : tokenValue},
       success: function(response) {

					if (response.status == "BSUC_0001") {
						
						$('#snxtErrorMessage').hide();
				url = "/downloadNewsAlertsBirtReport";
	postData(url, data);
			}else{
				$('#snxtErrorMessage').show();
			 url = "/newsAlertsNoRecordsError";			
	postData(url, data);
			}
}
})
	
	
	
}
