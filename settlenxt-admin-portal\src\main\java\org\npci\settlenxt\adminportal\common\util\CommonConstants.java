
package org.npci.settlenxt.adminportal.common.util;

import org.npci.settlenxt.portal.common.util.BaseCommonConstants;

public final class CommonConstants extends BaseCommonConstants {
	private CommonConstants() {
	}

	public static final String YYYY_MM_DD_PATTERN = "yyyy-MM-dd";
	public static final String YYYY_MM_DD_HH_MM_SS_PATTERN = "yyyy-MM-dd HH:mm:ss";

	public static final String FEELIST = "feeRatelist";
	public static final String CAPPING_AMOUNT_LIST = "cappingAmountList";
	public static final String YES_FLAG = "Y";
	public static final String STATUS_APPROVE = "A";
	public static final String STATUS_REJECT = "R";

	public static final String SHOW_MAIN_TAB = "showMainTab";
	public static final String SHOW_APPROVAL_TAB = "showApprovalTab";
	public static final String BULK_APPROVE = "bulkApproval";
	public static final String BULK_REJECT = "bulkRejection";
	public static final String PARENT_PAGE = "parentPage";
	public static final String SHOW_ADD_BUTTON = "showAddButton";
	public static final String FEE_RATE_INFO_DTO = "feeRateDto";

	public static final String EDIT_FLOW = "editFlow";

	public static final String FEE_TYPE = "FEE_TYPE";

	public static final String TXN_CURR_CODE = "CUR_CODE";
	public static final String PARTY_TYPE_CODE = "ENTITY";
	public static final int BATCH_ID = 2;
	public static final String ADD_FLOW = "addFlow";
	public static final String BATCH_UPLOAD_LIST_DTO = "feesUplList";
	public static final String DUPLICATE_ERROR = "BERR_0007";
	public static final String CHECK_FILEPATH = "../";
	public static final String CRLF_CHAR = "\n";
	public static final String FEE_DESCRIPTION = "feeDescription";
	public static final String FEE_CODE = "feeCode";
	public static final String TXN_CURRENCY = "txnCurrency";
	public static final String CASHFEES_FLAT = "cashFeesFlat";
	public static final String CASHFEES_PERCENT = "cashFeesPercent";
	public static final String CASHFEES_MIN = "cashFeesMin";
	public static final String CASHFEES_MAX = "cashFeesMax";
	public static final String PURFEES_FLAT = "purFeesFlat";
	public static final String PURFEES_PERCENT = "purFeesPercent";
	public static final String PURFEES_MIN = "purFeesMin";
	public static final String PURFEES_MAX = "purFeesMax";
	public static final String MULTIPLIER = "multiplier";
	public static final String GST_CODE = "gstCode";
	public static final String STATUS = "status";
	public static final String CREDIT_TO = ",creditTo";
	public static final String DEBIT_TO = "debitTo";
	public static final String VALID_FROM = "validFrom";
	public static final String VALID_TO = "validTo";
	public static final String REVERSE_CASHFEES = "reversCashFees";
	public static final String FEE_TYPE_CODE = "feeTypeCode";

	public static final String FEECONIFG = "feeConfig";
	public static final String MAJOR = "major";
	public static final String MINOR = "minor";
	public static final String SHOW_FEE_CONFIG = "showFeeConfig";
	public static final String ADD_FEE_CONFIG = "addFeeConfig";
	public static final String FEE_CONFIG_PENDING_LIST = "pendingFeeConfigList";
	public static final String MAJOR_TYPE = "M";
	public static final String MINOR_TYPE = "N";
	public static final String SCHEME_TYPE = "SCHEME_CODE";
	public static final String SCHEME_CODE_FEE_MAJOR = "SCHEME_CODE_FEE_MAJOR";
	public static final String PROD_TYPE = "CARD_PRODUCT";
	public static final String CARD_TYPE = "CARD_TYPE";
	public static final String CARD_BRAND = "SCHEME_CODE";
	public static final String REL_OPR = "REL_OPERATOR_LIST";
	public static final String FIELD_EX = "FIELD_NAME";
	public static final String DISPUTE_FIELD_OP = "DISPUTE_FIELD_OP";
	public static final String SUB_FIELD_NAME = "SUB_FIELD_NAME";
	public static final String ADD_MAJOR = "addMajor";
	public static final String ADD_MINOR = "addMinor";

	public static final String EDIT_MAJOR = "editMajor";
	public static final String EDIT_MINOR = "editMinor";
	public static final String FEE_CONFIG_LIST_DTO = "feeConfigListDto";

	public static final String ADD_FEECONFIG_LIST = "addFeeConfigList";
	public static final String EDIT_FEECONFIG_LIST = "editFeeConfigList";
	public static final String SAVED_FEE_CONFIG = "savedFeeConfigList";

	public static final String REQUEST_STATE_SAVED = "I";

	public static final String SHOW_SAVED_FEE_CONFIG = "showSavedFeeConfig";
	public static final String INTERCHANGE_FEE_CODE = "0001";
	public static final String ASSESSMENT_FEE_CODE = "0002";
	public static final String TRANSACTION_FEE_CODE = "0004";
	public static final String PROCESSING_FEE_CODE = "0003";
	public static final String AUTH_FEE_CODE = "0004";
	public static final String ADD_FEE_CONFIG_LIST = "addFeeConfigList";

	public static final String SAVE_BUTTON = "savebutton";
	public static final String FEE_RATE_CONFIG_LIST = "feeConfigList";
	public static final String REMOVE_MINOR_MAPPING = "D";
	public static final String GST_SYS_TYPE = "GSTRate";


	public static final String ADD_BUDGET = "addBudget";

	public static final String TRANSACTION_ISS_AUTH_FEE_CODE = "0005";

	public static final String TRANSACTION_ACQ_AUTH_FEE_CODE = "0006";

	public static final String ADD_NEWS_ALERTS = "ADDNEWSALERTS";
	public static final String PERIOD_TYPE = "PERIOD_TYPE";
	public static final String WEEK_LIST = "WEEK_LIST";
	public static final String MONTH_LIST = "MONTH_LIST";


	public static final String PUBLISH_TYPE_SPECIFIC = "Specific";
	public static final String EDIT_NEWS = "E";
	public static final String TRANSACT_NEWS_ALL_USERS = "ALLUSERS";

	public static final String DOWNLOAD_BUTTON = "downloadButton";
	public static final String NEWS_AND_ALERTS_INFO = "newsAndAlertInfoDTO";
	public static final String FILE_DOWNLOAD_PATH = "FILE_DOWNLOAD_PATH";
	public static final String NEWS_ALERT_REPORTS_RPT_NAME = "newsAlertsReport.rptdesign";
	public static final String NEWS_ALERT_REPORTS_TITLE = "News And Alerts Reports";
	public static final String FILE_TYPE_XLS = "xls";
	public static final String ADD_MCC = "addMcc";

	public static final String MCC_PENDING_LIST = "pendingMccList";

	public static final String MCC_APP_PENDING = "pendingMcc";

	public static final String MCC_DTO = "mccDTO";

	public static final String ADD_MCC_LAST_OPERATION = "Add MCC";

	public static final String EDIT_MCC = "editMcc";
	public static final String NEW_MCC = "newMcc";
	public static final String EDIT_MCC_LAST_OPERATION = "Edit MCC";

	public static final String MCC_LIST = "mccList";
	public static final String SEARCH = "search";
	public static final String PERIOD_TYPE_DAILY = "Daily";
	public static final String PERIOD_TYPE_WEEKLY = "Weekly";
	public static final String PERIOD_TYPE_MONTHLY = "Monthly";

	public static final String RECORD_APPROVED = "A";
	public static final String RECORD_REJECTED = "R";

	public static final String FEEIDLIST = "feeIdListPending";
	public static final String PENDING_CAP_AMOUNT_LIST = "pendingCapAmountList";
	public static final String CAPPING_AMOUNT_DTO = "cappingAmountDto";
	public static final String FUNCTION_CODE_LIST = "functionCodeList";
	public static final String ADD_CAPPING = "ADDCAPPING";
	public static final String EDIT_CAPPING = "EDITCAPPING";

	public static final String NPCI_MEMBER = "NPCI";
	public static final String ESCALATION_PENDING_LIST = "pendingEscalationList";
	public static final String USER_ACTIVE_STATUS = "A";
	public static final int NEWS_ALERT_NEW_DAYS = 7;
	public static final String ROLE_TYPE_MAKER = "MAKER";
	public static final String ROLE_TYPE_CHECKER = "CHECKER";
	public static final String BLOCK_BIN_STATUS = "B";
	public static final String ROLE_APPROVED = "roleApproved";
	public static final String ROLE_SUBMITTED = "roleSubmitted";

	public static final String REASON_CODE_DTO = "reasonCodeDto";
	public static final String REASON_CODE_STG_LIST = "reasonCodeStgList";
	public static final String REASON_CODE_MASTER_LIST = "reasonCodeMasterlist";
	public static final String ADD_REASON_CODE_MASTER = "addReasonCodeMaster";
	public static final String ADD_REASON_CODE = "addReasonCode";
	public static final String ADD_REASON_CODE_LAST_OPERATION = "Add Reason Code";
	public static final String EDIT_REASON_CODE = "editReasonCode";
	public static final String EDIT_REASON_CODE_LAST_OPERATION = "Edit Reason Code";
	public static final String REASON_CODE_PENDING_LIST = "reasonCodePendingList";
	public static final String REASON_CODE_APP_PENDING = "pendingReasonCode";

	public static final String JSON_VALIDATOR_DTO = "jsonValidatorDto";
	public static final String JSON_VALIDATOR_LIST = "jsonValidatorList";
	public static final String ADD_JSON_VALIDATOR_DETAIL = "addJsonValidatorDetail";
	public static final String ADD_JSON_VALIDATOR = "addJsonValidator";
	public static final String ADD_JSON_VALIDATOR_LAST_OPERATION = "Add Json Validator";
	public static final String EDIT_JSON_VALIDATOR = "editJsonValidator";
	public static final String EDIT_JSON_VALIDATOR_LAST_OPERATION = "Edit Json Validator";
	public static final String JSON_VALIDATOR_PENDING_LIST = "jsonValidatorPendingList";
	public static final String JSON_VALIDATOR_APP_PENDING = "pendingJsonValidator";

	public static final String CARD_PROD_TYPE = "PRODUCT";

	public static final String USER_STATUS_RESET = "R";
	public static final String MINOR_REJECT_EDIT = "minorRejectEdit";
	public static final String SETTLENXT_ADMIN_PORTAL = "SNxtAdminPortal";

	public static final String BIRT_REPORT_USERNAME = "odaUser";
	public static final String BIRT_REPORT_DB_URL = "odaURL";
	public static final String BIRT_REPORT_PD = "odaPassword";
	public static final String BIRT_REPORT_DRIVERCLASS = "odaDriverClass";
	public static final String DB_CONFIG_USERNAME = "spring.datasource.username";
	public static final String DB_CONFIG_PASSWORD = "spring.datasource.password";
	public static final String DB_CONFIG_URL = "spring.datasource.url";
	public static final String DB_CONFIG_DRIVERCLASS = "spring.datasource.driverClassName";

	public static final String CYCLE_DATE = "cycleDate";
	public static final String CYCLE_DATA = "cycleData";
	public static final String SETTLENXT_FETCH_CYCLE_STATUS = "settlenxt/fetchCycleStatus";
	public static final String CYCLE_NUMBER = "cycleNumber";
	public static final String REPORT_STATUS = "reportStatus";
	public static final String INTERNAL_CYCLE_NUMBER = "internalCycleNumber";
	public static final String SETTLENXT_UPDATE_CYCLE_STATUS = "settlenxt/updateCycleStatus";
	public static final String FORCE_OPERATION = "forceOperation";
	public static final String BANK_TYPE = "bankType";
	public static final String SETTLENXT_ALL_REPORTS = "settlenxt/allReports";
	public static final String REPORT_ORCHESTRATION_DOMAIN = "REPORT_ORCHESTRATION_DOMAIN";
	public static final String VIEW_PRIORITY = "viewPriority";
	public static final String RETRY = "Retry";
	public static final String SETTLENXT_FETCH_REPORCESS_ICN_DATA = "settlenxt/fetchReporcessICNData";
	public static final String REPORT_CYCLE_STATUS_LIST = "reportCycleStatusList";
	public static final String DEFFERED_INTERNAL_CYCLE = "defferedInternalCycle";
	public static final String SETTLENXT_REPORCESS_ICN_DATA = "settlenxt/reprocessIcnData";
	public static final String SETTLENXT_FETCH_PRODUCT_ID_DETAILS = "settlenxt/fetchProductIdDetails";

	// ACTION CODE
	public static final String ACTION_CODE_LIST = "actionCodeList";
	public static final String PENDING_ACTION_CODE_LIST = "pendingActionCodeList";
	public static final String ACTION_CODE_DTO = "actionCodeDTO";
	public static final String ADD_ACTION_CODE = "ADDACTIONCODE";
	public static final String EDIT_ACTION_CODE = "EDITACTIONCODE";
	public static final String FC_MTI_TYPE = "FC_MTI_TYPE";
	public static final String RAISED_BY = "RAISED_BY";
	public static final String CAP_AMT_FLAG_LIST = "capAmountFlagList";
	public static final String CAPPING_AMT_FLAG = "CAPPING_AMT_FLAG";

	/// SETTLEMENT CYCLE
	public static final String ADD_SETTLEMENT_CYCLE = "ADDSETTLEMENTCYCLE";
	public static final String EDIT_SETTLEMENT_CYCLE = "EDITSETTLEMENTCYCLE";
	public static final String ADD_DISPUTE_FEE_RULE = "addDisputeFeeRule";
	public static final String DISPUTE_FEE_RULE_LIST = "disputeFeeRulelist";
	public static final String DISPUTE_FEE_RULE_INFO = "disputeFeeRuleInfo";
	public static final String DISPUTE_FEE_RULE_DTO = "disputeFeeRuleDto";

	public static final String DISPUTE_ENTITY = "DISPUTE_ENTITY";
	public static final String DISPUTE_STATE = "DISPUTE_STATE";
	public static final String DISPUTE_FIELD_NAME = "DISPUTE_FIELD_NAME";
	public static final String DISPUTE_RELATIONAL_OP = "DISPUTE_REL_OP";
	public static final String DISPUTE_RELATIONAL_OP_COMP = "DISPUTE_REL_OP_COMP";
	public static final String DISPUTE_FEE_TYPE = "DISPUTE_FEE_TYPE";

	public static final String SHOW_DISPUTE_TRANSITION_RULE = "showTransitionRule";
	public static final String SHOW_CHECK_BOX = "showCheckBox";
	public static final String DISPUTE_TRANSITION_LIST = "transitionlist";
	public static final String REL_OP_CMPLX = "REL_OPERATOR_CMPLX";
	public static final String ADD_EDIT_DISPUTE_TRANSITION_RULE = "addEditTransitionRule";
	public static final String ADD_TRANSITION = "addTransition";
	public static final String EDIT_TRANSITION = "editTransition";
	public static final String DISPUTE_TRANSITION_DTO = "disputeTransitionDTO";
	public static final String VIEW_DISPUTE_TRANSITION_RULE = "viewTransition";

	public static final String REL_OPERATOR_LIST = "relOpList";
	public static final String REL_OPERATOR_LIST_COMP = "relCompOpList";
	public static final String DISP_FEE_LIST = "dispFeeList";
	public static final String DISP_FIELD_NAME_LIST = "fieldNameList";
	public static final String DISP_FIELD_OP_LIST = "fieldOperatorList";

	// Reason Code Rules
	public static final String ADD_REASON_CODE_RULES = "addReasonCodeRules";
	public static final String LOGICAL_REASON_CODE = "LOGICAL_REASON_CODE";

	public static final String KAKFA_MESSAGE_MODEL_KEY = "model";

	// FCFD
	public static final String FCFD_ACTION_FC = "FC";
	public static final String FCFD_ACTION_FD = "FD";
	public static final String FCFD_ACTION = "FCFD_ACTION";
	public static final String CALL_TYPE = "CALL_TYPE";
	public static final String FEES = "FC_FUND_MOVEMENT_FLAG";
	public static final String IMPACTED_TO = "IMPACTED_TO";
	public static final String BIN_TYPE = "NPCI_BIN_TYPE";
	public static final String TXN_ORG_ID = "National Payments Corporation Of India";
	public static final String FCFD_FEE_COLLECTION = "Pre-compliance";
	public static final String FCFD_FEE_DISBURSEMENT = "Compliance";

	public static final String REJECT_REASON_CODE_RULE_DTO = "rejectReasonCodeRuleDto";
	public static final String REJECT_REASON_CODE_RULE_LIST = "rejectReasonCodeRuleList";
	public static final String ADD_REJECT_REASON_CODE_RULE = "addRejectReasonCodeRule";
	public static final String ADD_REJECT_REASON_CODE = "addRejectReasonCode";
	public static final String ADD_REJECT_REASON_CODE_RULE_LAST_OPERATION = "Add Reject Reason Code Rule";
	public static final String EDIT_REJECT_REASON_CODE_RULE = "editRejectReasonCodeRule";
	public static final String EDIT_REJECT_REASON_CODE_RULE_LAST_OPERATION = "Edit Reject Reason Code Rule";
	public static final String REJECT_REASON_CODE_RULE_PENDING_LIST = "rejectReasonCodeRulePendingList";
	public static final String REJECT_REASON_CODE_RULE_APP_PENDING = "pendingRejectReasonCodeRule";
	public static final String ERR_STATUS = "File not available in location";

	// AES Constants

	public static final String CRYPT_CONNECTION_KEY = "QWERTYUIOPLKJHGF";
	public static final String IV_PARAM_SPEC_STRING = "0123456701234567";
	public static final String AESCBCALGO = "AES/CBC/PKCS5PADDING";

	public static final String BIN_FEATURE_MAPPING_DTO = "binFeatureMappingDTO";
	public static final String EDIT_BIN_FEATURE_MAPPING = "editBinFeatureMapping";

	public static final String ADD_FUND_COLLECT_DISBURSE = "addFundCollectDisburse";

	public static final String EDIT_DISPUTE_FEE_RULE = "editDisputeFeeRule";
	public static final String PENDING_DISPUTE_FEE_RULE_LIST = "disputeFeerulePendingList";
	public static final String SHOW_PENDING_DISPUTE_FEE_RULE_LIST = "showPendingDisputeFeeRule";
	public static final String PENDING_DISPUTE_FEE_RULE = "pendingDisputeFeeRule";

	public static final String MAK_CHK_FLAG = "makChkFlag";
	public static final String RESULT_SUCCESS = "Success";
	public static final String RESULT_ERROR = "Error";
	public static final String RECORD_PENDING = "P";
	public static final String DISPUTE_TRANS_LIST_SIZE = "tranSize";
	public static final String DISPUTE_TRANS_ENTITY_LIST = "entityList";
	public static final String DISPUTE_TRANS_STATE_LIST = "stateList";
	public static final String DISPUTE_TRANS_FIELD_NAME_LIST = "fieldNameList";
	public static final String DISPUTE_TRANS_RELOP_LIST = "relOpList";
	public static final String DISPUTE_TRANS_FIELD_OP_LIST = "fieldOpList";
	public static final String DISPUTE_TRANS_REQ_STATE_FORW = "approveOrPending";
	public static final String DISPUTE_TRANS_SHOW_EDIT_DISCARD_BTN = "showEditDiscardBtn";
	public static final String DISPUTE_TRANS_OP_NONE_DESC = "NONE";
	public static final String DISPUTE_TRANS_OP_AND_DESC = "AND";
	public static final String DISPUTE_TRANS_OP_OR_DESC = "OR";
	public static final String DISPUTE_TRANS_OP_NONE = "N";
	public static final String DISPUTE_TRANS_OP_AND = "A";
	public static final String DISPUTE_TRANS_OP_OR = "O";
	public static final String DISPUTE_TRANS_KEY_SEPERATOR = "_";
	public static final String DISPUTE_TRANS_FIELD_SEPERATOR = ",";
	public static final String DISPUTE_TRANS_RELOP_CMPLX_NAME = "_1";
	public static final String DISPUTE_TRANS_RELOP_CMPLX_OPER = "_2";
	public static final String DROPDOWN_SELECT = "SELECT";
	public static final String RECORD_DESC_REJECTED = "Rejected";
	public static final String RECORD_DESC_APPROVED = "Approved";
	public static final String RECORD_PENDING_FOR_APPROVAL = "Pending for approval";

	public static final String PAN_SUFFIX = "9999";

	public static final String YES = "YES";
	public static final String NO = "NO";
	public static final String TRANSACT_YES = "Yes";
	public static final String SUCCESS_STATUS = "successStatus";
	public static final String ERROR_STATUS = "errorStatus";
	public static final String REQ_TYPE = "reqType";
	public static final String REQUEST_STATE_SUBMITTED = "P";
	public static final String REQUEST_STATE_APPROVED = "A";
	public static final String REQUEST_STATE_REJECTED = "R";
	public static final String REQUEST_STATE_DISCARDED = "D";
	public static final String TRANSACT_SUCCESS = "BSUC_0001";
	public static final String TRANSACT_FAIL = "BERR_0002";
	public static final String USER_STATUS_DEACT = "I";
	public static final String USER_STATUS_ACTIVE = "A";

	public static final String CARD_CONFIG_APP_PENDING = "pendingAppCardConfig";
	public static final String USER_PD_RESET_PD_STATUS = "Z";

	// bulkupload
	public static final String BULK_SAMPLE_DOWNLOAD = "D:\\WFH\\userchanged-07\\settlenxt\\settlenxt-bank-portal\\mcprDownloadFile\\";
	public static final String BULK_MODULE = "BulkUpload";
	public static final String BULK_UPLOAD = "bulkUpload";
	public static final String MCPR_REPORT_DOWNLOAD = "D:\\MCPRReports\\";
	public static final String SETTLEMENT_CYCLE_LIST_NO = "settlementCycleNumberList";
	public static final String SETTLEMENT_CYCLE_LIST = "settlementCycleList";

	// fileUpload
	public static final String DOCUMENT_TYPE = "RGCS_DOCUMENT_TYPE";
	public static final String RGCS_DOCUMENTS = "RGCS Documents";

	public static final String PARTICIPANT_TYPE_ALL = "ALL";
	public static final String BULK_DATA_ENTRY = "Bulk Data Entry";

	// tipSurcharge

	public static final String ADD_TIP_SURCHARGE = "addTipSurcharge";
	public static final String EDIT_TIP_SURCHARGE = "editTipSurcharge";
	public static final String DISCARD_TIP_SURCHARGE = "discardTipSurcharge";
	public static final String TIP_SURCHARGE_DTO = "tipSurchargeDto";
	public static final String TIP_SURCHARGE_LIST = "tipSurchargeList";
	public static final String TIP_SURCHARGE_ADD = "Add Tip Surcharge";
	public static final String TIP_SURCHARGE_EDIT = "Edit Tip Surcharge";
	public static final String TIP_SURCHARGE = "tipSurcharge";
	public static final int TRANSACT_FUCTIONALITY_ADD_TIP_SURCHARGE_CONFIG = 49;
	public static final int TRANSACT_FUCTIONALITY_EDIT_TIP_SURCHARGE_CONFIG = 50;
	public static final String TIP_SURCHARGE_PENDING_LIST = "pendingTipSurchargeList";
	public static final String TIP_SURCHARGE_APP_PENDING = "pendingAppTipSurcharge";
	public static final String TIP_SURCHARGE_ACTION = "14";
	// MCCtipSurcharge

	public static final String ADD_MCC_TIP_SURCHARGE = "addMccTipSurcharge";
	public static final String EDIT_MCC_TIP_SURCHARGE = "editMccTipSurcharge";
	public static final String DISCARD_MCC_TIP_SURCHARGE = "discardMccTipSurcharge";
	public static final String MCC_TIP_SURCHARGE_DTO = "mccTipSurchargeDto";
	public static final String MCC_TIP_SURCHARGE_LIST = "mccTipSurchargeList";
	public static final String MCC_TIP_SURCHARGE_ADD = "Add Mcc Tip Surcharge";
	public static final String MCC_TIP_SURCHARGE_EDIT = "Edit Mcc Tip Surcharge";
	public static final String MCC_TIP_SURCHARGE = "mccTipSurcharge";
	public static final int TRANSACT_FUCTIONALITY_ADD_MCC_TIP_SURCHARGE_CONFIG = 51;
	public static final int TRANSACT_FUCTIONALITY_EDIT_MCC_TIP_SURCHARGE_CONFIG = 52;
	public static final String MCC_TIP_SURCHARGE_PENDING_LIST = "pendingMccTipSurchargeList";
	public static final String MCC_TIP_SURCHARGE_APP_PENDING = "pendingAppMccTipSurcharge";

	public static final String DISCARD_MCC = "discardMcc";
	public static final String MCC_BACK = "back";

	// currency master
	public static final String ADD_CURRENCY_MASTER = "addCurrencyMaster";
	public static final String EDIT_CURRENCY_MASTER = "editCurrencyMaster";
	public static final String DISCARD_CURRENCY_MASTER = "discardCurrencyMaster";
	public static final String CURRENCY_MASTER_DTO = "currencyMasterDto";
	public static final String CURRENCY_MASTER_LIST = "currencyMasterList";
	public static final String CURRENCY_MASTER_ADD = "Add Currency Master";
	public static final String CURRENCY_MASTER_EDIT = "Edit Currency Master";
	public static final String CURRENCY_MASTER = "currencyMaster";
	public static final int TRANSACT_FUCTIONALITY_ADD_CURRENCY_MASTER_CONFIG = 53;
	public static final int TRANSACT_FUCTIONALITY_EDIT_CURRENCY_MASTER_CONFIG = 54;
	public static final String CURRENCY_MASTER_PENDING_LIST = "pendingCurrencyMasterList";
	public static final String CURRENCY_MASTER_APP_PENDING = "pendingAppCurrencyMaster";

	// MCCtipSurcharge

	public static final String ADD_FUNCTION_CODE = "addFunctionCode";
	public static final String EDIT_FUNCTION_CODE = "editFunctionCode";
	public static final String DISCARD_FUNCTION_CODE = "discardFunctionCode";
	public static final String FUNCTION_CODE_DTO = "functionCodeDto";

	public static final String FUNCTION_CODE_ADD = "Add Function Code";
	public static final String FUNCTION_CODE_EDIT = "Edit Function Code";
	public static final String FUNCTION_CODE = "FunctionCode";
	public static final int TRANSACT_FUCTIONALITY_ADD_FUNCTION_CODE_CONFIG = 1120;
	public static final int TRANSACT_FUCTIONALITY_EDIT_FUNCTION_CODE_CONFIG = 1121;
	public static final String FUNCTION_CODE_PENDING_LIST = "pendingFunctionCodeList";
	public static final String FUNCTION_CODE_APP_PENDING = "pendingAppFunctionCode";

	public static final String SETTLEMENT_PRODUCT_ID = "settlementProductId";
	public static final String ICN_TYPE = "icnType";
	public static final String SYSTEM_DATE = "systemDate";
	public static final String REPROCESS_ICN = "reprocessICN";
	public static final String ICN = "ICN";

	// Settlement Cycle
	public static final String MAIN_TAB = "mainTab";
	public static final String APPROVAL_TAB = "approvalTab";

	public static final String SETTLEMENT_DATE = "RGCSSettlementDate";
	public static final String LOCAL_DATE_TIME = "DtTmLcTxn";
	public static final String ISSUER_IIN = "IssuerIIN";
	public static final String PID = "PID";
	public static final String PAN = "PAN";
	public static final String RRN = "RRN";
	public static final String CARD_ACC_TERM_ID = "CrdAcptTrmId";
	public static final String MCC = "MCC";
	public static final String ISSUER_BANK = "IssuerBank";
	public static final String APPROVAL_CODE = "ApprvlCd";
	public static final String TXN_AMOUNT = "AmtTxn";
	public static final String AMOUNT_IN_RUPEES = "Amt in Rs.";
	public static final String EA = "EA";
	public static final String FINAL_AMOUNT = "Final Amount";
	public static final String REMARKS = "Remarks";

	// ReasonCode

	public static final String REASON_CODE_KEY_SEPERATOR = "_";
	public static final String REASON_CODE_FIELD_SEPERATOR = ",";
	public static final String REASON_CODE_RELOP_CMPLX_NAME = "_1";
	public static final String REASON_CODE_RELOP_CMPLX_OPER = "_2";
	public static final String REASON_CODE_OP_NONE_DESC = "NONE";
	public static final String REASON_CODE_OP_AND_DESC = "AND";
	public static final String REASON_CODE_OP_OR_DESC = "OR";
	public static final String REASON_CODE_OP_NONE = "N";
	public static final String REASON_CODE_OP_AND = "A";
	public static final String REASON_CODE_OP_OR = "O";

	// lookup

	public static final String SHOW_ADD_BTN = "showAddButton";
	public static final String FINAL_LOOK_UP = "finalLookUp";
	public static final String SHOW_ACTIVE = "showActive";
	public static final String APPROVAL_ACTIVE = "approvalActive";
	public static final String LOOKUP_LIST = "lookUpList";
	public static final String SHOW_BTN = "showbutton";
	public static final String ADD_LOOK_UP = "addLookUp";
	public static final String SUBMIT_LOOKUP_DETAILS = "submitLookUpDetails";
	public static final String ADD_BACK_BTN = "addBackBtn";
	public static final String SAVE_BACK_BTN = "saveBackBtn";
	public static final String LOOK_UP_DTO = "lookUpDTO";
	public static final String PENDING_LOOK_UP_LIST = "pendingLookUpList";
	public static final String EDIT_LOOK_UP = "editLookUp";
	public static final String VIEW_LOOK_UP = "viewLookUp";
	public static final String EDIT_LOOPKUP = "EDITLOOKUP";
	public static final String LOOKUP_LISTS = "lookupList";
	public static final String ADD_LOOKUP = "ADD LOOKUP";

	// Regeneration and recalculation ui screen
	public static final String REPORT_TYPE = "reportType";
	public static final String REQUEST_TYPE = "requestType";
	public static final String SETTLENXT_FETCH_REGEN_AND_RECAL = "settlenxt/regenAndRecalReports";
	public static final String PARTICIPANT_ID = "participantId";
	public static final String REGENERATION = "regeneration";
	public static final String REQUEST_DATE = "requestDate";
	public static final String RAW_AUTH = "RAW_AUTH";
	public static final String SETTLENXT_REPORT_REGEN_AND_RECAL_STATUS = "settlenxt/getReportRegenAndRecalStatus";
	public static final String REGENERATION_STATUS = "regenerationStatus";
	public static final String PRODUCT_ID_RPY = "RPY";
	public static final String CYCLE_NUMBER_C1 = "C1";
	public static final String SHOW_PENDING_DISPUTE = "showPendingDispute";
	public static final String SHOW_APPROVE_REJECT_DISPUTE = "showApproveRejectDispute";
	public static final String DELETED_FLAG = "D";
	public static final String REPORT_STATUS_DTO = "reportStatusDTO";
	public static final String REPORTSTATUS = "REPORTSTATUS";

	public static final String RECALCULATION = "recalculation";
	public static final String RECALCULATION_STATUS = "recalculationStatus";
	public static final String MERGE_TABLES = "mergeTables";
	public static final String UID = "uid";
	public static final String SETTLENXT_DELETE_RECAL_TABLES_TXN = "settlenxt/deleteRecalTablesTxn";
	public static final String CYCLE_MANAGEMENT_DTO = "cycleManagementDTO";

	// Holiday Master
	public static final String HOLIDAY_MASTER_LIST = "holidayMasterList";
	public static final String HOLIDAY_MASTER_DTO = "holidayMasterDTO";
	public static final String EDIT_HOLIDAY = "EDIT_HOLIDAY";
	public static final String HOLIDAY_MASTER_APP_REJ_DTO = "holidayMasterDTO";
	public static final String REQUEST_STATE_REJECT_HOLIDAY = "REJECT_HOLIDAY_MASTER";
	public static final String REQUEST_STATE_APPROVE_HOLIDAY = "APPROVE_HOLIDAY_MASTER";

	public static final String SETTLENXT_FETCH_INPROGRESS_LIST_OF_FILES = "settlenxt/getInprogressListOfFiles";
	public static final String FILE_NAMES = "fileNames";
	public static final String SETTLENXT_ABORT_OUTGOING_FILE = "settlenxt/abortOutgoingFile";
	public static final String FILE_NAME = "fileName";
	public static final String RESP_STATUS = "Status";
	public static final String SEARCH_FLAG = "search";
	public static final String STATUS_PENDING = "I";
	public static final String REQUEST_STATE_DELETED = "P";
	public static final String ADD_HOLIDAY = "ADD_HOLIDAY";
	public static final String SETTLENXT_FETCH_CROSS_SITE_IC_DETAILS = "/settlenxt/fetchCrossSiteIcStatus";
	public static final String CROSS_SITE_IC_DETAILS = "crossSiteICDetails";
	public static final String IC_STATUS = "icStatus";
	public static final String INTEGRITY_CHECKER_DOMAIN = "INTEGRITY_CHECKER_DOMAIN";
	public static final String SUSPECTED = "SUSPECTED";

	public static final String PROCESSING_CODE = "PROCESSING_CODE";

	public static final String UTF = "UTF-8";

	// last operation
	public static final String LAST_OPERATION_ADD = "ADD";
	public static final String LAST_OPERATION_EDIT = "EDIT";
	public static final String LAST_OPERATION_APPROVE = "APPROVE";
	public static final String LAST_OPERATION_REJECT = "REJECT";
	public static final String LAST_OPERATION_DISCARD = "DISCARD";
	public static final String LAST_OPERATION_DELETE = "DELETE";
	public static final String NETWORK_PRIVATE_INFO = "NETWORK_PRIVATE_INFO";
	public static final String NETWORK_PRIVATE_CRED = "NETWORK_PRIVATE_CRED";
	public static final String FILE_EXTENSION_PGP = "pgp";
	public static final String FILE_EXTENSION_XML = "xml";
	public static final String FILE_EXTENSION_CSV = "csv";
	public static final String DOT=".";
	public static final String SYSTEM = "SYSTEM";
	public static final String PROCESS_ENCRYPTED_FILE = "PROCESS_ENCRYPTED_FILE";
	public static final String ERROR = "ERROR";

	public static final String CSV_FILE_FOOTER = "---End of Report---";
	public static final String REQUEST_STATE_PENDING = "P";
	public static final String FILE_STATUS_VIEW = "V";
	public static final String CSV_FILE_ZEROS = "000";

	// ForexRate
		public static final int TRANSACT_FUCTIONALITY_ADD_FOREX_RATE_CONFIG = 12005;
		public static final int TRANSACT_FUCTIONALITY_EDIT_FOREXRATE_CONFIG = 12006;
		public static final String FOREX_RATE_APP_PENDING = "pendingAppForexRate";
		public static final String SHOW_FOREX_RATE_LIST = "forexRateList";
		public static final String PENDING_FOREX_RATE_LIST = "pendingForexRateList";
		public static final String FOREX_RATE_DTO = "forexRateDto";
		public static final String DISCARD_FOREX_RATE = "discardForexRate";
		public static final String EDIT_FOREX_RATE = "editForexRate";
		public static final String ADD_FOREX_RATE = "addForexRate";
		public static final String EDIT_FOREX_RATE_OPERATION = "Edit ForexRate";
		public static final String ADD_FOREX_RATE_OPERATION = "Add ForexRate";
		public static final String SHOW_MAIN_TABS = "showMainTab";
		public static final String SHOW_APPROVAL_TABS = "showApprovalTab";

        public static final String NETWORK_TYPES = "networkTypes";
        public static final String TRANSACTION_STATUS_LOOKUP_TYPE = "TRANSACTION_STATUS";
        public static final String NETWORK_FILE_DATA = "networkFileData";
        public static final String INTL_NETWORKS = "INTL_NETWORKS";
        public static final String NETWORK_TYPE = "NETWORK_TYPE";
		public static final String NETWORK_FILE_NAME_SEPARATOR = "_";

}