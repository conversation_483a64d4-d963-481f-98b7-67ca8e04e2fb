package org.npci.settlenxt.adminportal.controllers;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.FeeDTO;
import org.npci.settlenxt.adminportal.dto.FeeMajorMinorPriorityDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.FeeService;
import org.npci.settlenxt.adminportal.service.MasterService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.BaseFeeDTO;
import org.npci.settlenxt.portal.common.dto.BaseFeeMajorMinorPriorityDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.FeeRateConfigDTO;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

@Controller
public class FeeController extends BaseController {

	@Autowired
	FeeService feeService;

	@Autowired
	private MessageSource messageSource;
	@Autowired
	BaseLookupService lookupService;

	@Autowired
	MasterService masterService;

	@Autowired
	private SessionDTO sessionDto;

	private static final String SHOW_FEE_RATE = "showFeeRate";
	private static final String VIEW_FEE_RATE = "viewFeeRateInfo";
	private static final String VIEW_APPROVE_FEE_RATE = "viewApproveFeeRate";
	private static final String ADD_EDIT_FEE_RATE = "addEditFeeRate";
	private static final String SHOW_FEE_CONFIG = "showFeeConfig";
	private static final String VIEW_APPROVE_FEE_MAJOR = "viewApproveFeeMajor";
	private static final String VIEW_APPROVE_FEE_MINOR = "viewApproveFeeMinor";
	private static final String ADD_EDIT_FEE_MAJOR_MINOR = "addEditFeeMajorMinor";
	private static final String ADD_EDIT_FEE_MINOR_PRIOR = "addEditFeeMinorPrior";
	private static final String FUNC_CODE_MAP = "FuncCodeMap";
	private static final String BIN_CARD_BRAND = "binCardBrandMap";
	private static final String BIN_PROD_TYPE = "binProdTypeMap";
	private static final String SCHEME_TYPE_MAP = "schemeTypeMap";
	private static final String BIN_CARD_TYPE_MAP = "binCardTypeMap";
	private static final String FEE_CONFIG_LIST = "feeConfigList";
	private static final String FIELD_COLUMN_LIST = "fieldColumnList";
	private static final String FEE_DTO = "feeDto";
	private static final String SHOW_EDIT = "showEdit";
	private static final String SIGNIFICANCE = "significance";
	private static final String STATUS = "status";
	private static final String WAVIER_DAY_TYPE = "waiverDayTypeMap";
	private static final String COMPOUND_FEE_MAP = "compoundFeeMap";
	private static final String IMPACT_TO_MAP = "impactToMap";
	private static final String SHOW_CHECKBOX = "showCheckBox";
	private static final String SHOW_APPROVAL_TAB = "showApprovalTab";
	private static final String SHOW="show";
	private static final String ENABLE="E";
	private static final String FEE_CODE="feecode";
	private static final String TXN_CURRENCY="txnCurrency";
	private static final String PARTY_TYPE="partyType";
	private static final String GST_CODE="gstCode";
	private static final String WAIVER_TYPE="waiverDayType";
	private static final String PENALTY_TYPE="penaltyDayType";
	private static final String COMPOUND_FINE="compoundFee";
	private static final String DAILY_FEE="dailyFee";
	private static final String IMPACT_TO= "impactTo";
	private static final String DATE_ACTION="dateAction";
	private static final String ADD="ADD";
	private static final String EDIT="EDIT";
	private static final String EDIT_FIRST_REJ_MAJ="editFirstRejectMajor";
	private static final String EDIT_FIRST_REJ_MINOR="editFirstRejectMinor";
	private static final String FEE_RATE_CONFIG_LIST="feeRateConfigList";
	private static final String EDIT_FIRST_REJ_FEE_RATE="editFirstRejectFeeRate";
	private static final String SCHEME_CODE="Schemecode";
	private static final String PROD_C_LIST="prodCdList";
	private static final String CARD_TYPE_LIST="cardTypeList";
	private static final String CARD_BRAND_LIST="cardBrandList";
	private static final String REL_OPR_LIST="relOperatorsList";
	private static final String FUNC_CODE_LIST="funcCodeList";
	private static final String INTERCHANGE_FEE_ID="interchangeFeeID";
	private static final String ACQ_ASS_FEE_ID="acqAssessmentFeeID";
	private static final String ACQ_PROC_FEE_ID="acqProcessingFeeID";
	private static final String ISS_PROC_FEE_ID="issProcessingFeeID";
	private static final String ISS_ASS_FEE_ID="issAssessmentFeeID";
	private static final String ISS_AUTH_FEE_ID="issAuthFeeID";
	private static final String ACQ_AUTH_FEE_ID="acqAuthFeeID";
	private static final String FEE_MINOR="feeMinor";

	private static final String MAJORMINOR = "majorminor";

	@PostMapping("/showFeeRates")
	@PreAuthorize("hasAuthority('View Fees')")
	public String getFeesList(Model model) {
		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.YES_FLAG);
		List<FeeDTO> approvedFeeDTOList = feeService.getApprovedFeeRateList();
		model.addAttribute(CommonConstants.FEELIST, approvedFeeDTOList);

		return getView(model, SHOW_FEE_RATE);
	}

	@PostMapping("/getFeePendingForApproval")
	@PreAuthorize("hasAuthority('View Fees')")
	public String getPendingFeeList(Model model) {
		model.addAttribute(SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		List<FeeDTO> pendingFeeDTOList = feeService.getPendingForAppovalFeeRateList();

		model.addAttribute(CommonConstants.FEELIST, pendingFeeDTOList);
		validateCheckboxForMakerChecker(model);

		return getView(model, SHOW_FEE_RATE);
	}

	@PostMapping("/getFeeRate")
	@PreAuthorize("hasAuthority('View Fees')")
	public String getFeeInfo(@RequestParam("feeId") int feeId, Model model) {

		FeeDTO feesRateDTO = feeService.getFeeMain(feeId);
		loadFeeRate(feeId, model, feesRateDTO);

		return getView(model, VIEW_FEE_RATE);
	}

	private void loadFeeRate(int feeId, Model model, FeeDTO feesRateDTO) {
		feesRateDTO.setFeeId(feeId);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feesRateDTO);

		prepareLookupArray(model);
	}

	private void prepareLookupArray(Model model) {
		List<String> lookUpList = new ArrayList<>();
		lookUpList.add(CommonConstants.CALL_TYPE);
		lookUpList.add(CommonConstants.FEES);
		lookUpList.add(CommonConstants.IMPACTED_TO);

		List<Map<String, String>> lookUpMapArr = masterService.prepareLookUpMap(lookUpList);

		if (null != lookUpMapArr && !lookUpMapArr.isEmpty()) {
			model.addAttribute(WAVIER_DAY_TYPE, lookUpMapArr.get(0));
			model.addAttribute(COMPOUND_FEE_MAP, lookUpMapArr.get(1));
			model.addAttribute(IMPACT_TO_MAP, lookUpMapArr.get(2));
		}
	}

	@PostMapping("/viewApproveFeeRate")
	@PreAuthorize("hasAuthority('View Fees')")
	public String getPendingFee(@RequestParam("feeId") int feeId, Model model) {
		FeeDTO feesRateDTO = feeService.getFee(feeId);
		loadFeeRate(feeId, model, feesRateDTO);

		feesRateDTO.setFeeId(feeId);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feesRateDTO);

		return getView(model, VIEW_APPROVE_FEE_RATE);
	}

	@PostMapping("/viewRejFeeRate")
	@PreAuthorize("hasAuthority('View Fees')")
	public String getRejectFee(@RequestParam("feeId") int feeId, Model model) {
		FeeDTO feesRateDTO = feeService.getFee(feeId);
		feesRateDTO.setFeeId(feeId);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feesRateDTO);
		model.addAttribute(SHOW, ENABLE);

		feesRateDTO.setFeeId(feeId);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feesRateDTO);
		model.addAttribute(SHOW, ENABLE);

		prepareLookupArray(model);

		return getView(model, VIEW_FEE_RATE);
	}

	@PostMapping("/approveFeeRateStatus")
	@PreAuthorize("hasAuthority('Approve Fees')")
	public String approveUserStatus(@RequestParam("feeId") int feeId, @RequestParam(STATUS) String status,
			@RequestParam("remarks") String remarks, Model model) {
		try {
			FeeDTO feeRateDto = feeService.updateApproveOrRejectFeeRate(feeId, status, remarks);
			checkFeeRateApproveStatus(feeRateDto, model);
			model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
			model.addAttribute(SHOW, ENABLE);
			prepareLookupArray(model);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FEE_RATE, ex);
		}
		return getView(model, VIEW_APPROVE_FEE_RATE);
	}

	private void checkFeeRateApproveStatus(FeeDTO feeRateDto, Model model) {
		Locale locale=Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(feeRateDto.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_FeeRateApproved", null, locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_FeeRateRejected", null, locale));
		}
	}

	@PostMapping("/approveOrRejectBulkFees")
	@PreAuthorize("hasAuthority('Approve Fees')")
	public String approveUserStatus(@RequestParam("bulkApprovalFeeIdList") String bulkApprovalFeeIdList,
			@RequestParam(STATUS) String status, Model model) {
		try {
			String remarks = "";
			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}

			FeeDTO feeRateDto = feeService.updateApproveOrRejectFeeRateBulk(bulkApprovalFeeIdList, status, remarks);
			checkFeeRateApproveStatus(feeRateDto, model);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FEE_RATE, ex);
		}

		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		List<FeeDTO> pendingFeeDTOList = feeService.getPendingForAppovalFeeRateList();
		model.addAttribute(CommonConstants.FEELIST, pendingFeeDTOList);

		validateCheckboxForMakerChecker(model);

		return getView(model, SHOW_FEE_RATE);

	}

	@PostMapping("/discardFeeRate")
	@PreAuthorize("hasAuthority('Edit Fees')")
	public String discardFeeRate(@RequestParam("feeId") int feeId, Model model) {
		FeeDTO feeRatedto = new FeeDTO();
		try {
			feeRatedto = feeService.discardFeeRate(feeId);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_FEE_RATE, ex);
		}
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRatedto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("feeRate.discardSuccess.msg"));
		prepareLookupArray(model);
		return getView(model, VIEW_APPROVE_FEE_RATE);
	}

	@PostMapping("/createFeeRate")
	@PreAuthorize("hasAuthority('Add Fees')")
	public String addFees(Model model) {
		FeeDTO feeRateDto = new FeeDTO();
		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
		addFeeListData(model);
		return getView(model, ADD_EDIT_FEE_RATE);
	}

	public void addFeeListData(Model model) {
		List<FeeDTO> feeCodeList = feeService.getFeeCodeList();
		List<FeeDTO> txnCurrency = feeService.getTxnCurrencyList();
		List<FeeDTO> gstCodeList = feeService.getGstCodeList();
		List<FeeDTO> partyType = feeService.getPartyTypeList();
		List<CodeValueDTO> callType = lookupService.getLookupData(CommonConstants.CALL_TYPE);
		List<CodeValueDTO> fees = lookupService.getLookupData(CommonConstants.FEES);
		List<CodeValueDTO> impactTo = lookupService.getLookupData(CommonConstants.IMPACTED_TO);
		List<CodeValueDTO> dateAction = feeService.getDateActionList();


		model.addAttribute(FEE_CODE, feeCodeList);
		model.addAttribute(TXN_CURRENCY, txnCurrency);
		model.addAttribute(PARTY_TYPE, partyType);
		model.addAttribute(GST_CODE, gstCodeList);
		model.addAttribute(WAIVER_TYPE, callType);
		model.addAttribute(PENALTY_TYPE, callType);
		model.addAttribute(COMPOUND_FINE, fees);
		model.addAttribute(DAILY_FEE, fees);
		model.addAttribute(IMPACT_TO, impactTo);
		model.addAttribute(DATE_ACTION, dateAction);
	}

	@PostMapping("/addFeeRate")
	@PreAuthorize("hasAuthority('Add Fees')")
	public String saveFeeRate(@ModelAttribute FeeDTO feeRateDto, Model model) {
		try {

			String x = feeRateDto.getFeeDesc().replace('*', ',');
			feeRateDto.setFeeDesc(x);
			if (!feeRateDto.getDateAction().isEmpty()) {
				String[] dateAction = feeRateDto.getDateAction().split("\\|");

				StringBuilder action = new StringBuilder(dateAction[0]);

				for (int i = 1; i < dateAction.length; i++) {
					action.append(",");
					action.append(dateAction[i]);
				}
				feeRateDto.setDateAction(action.toString());
			}
			feeService.addFeeRate(feeRateDto);
		} catch (Exception ex) {
			model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
			model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_EDIT_FEE_RATE, ex);
		}
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(feeRateDto.getStatusCode())) {
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES);
		}
		addFeeListData(model);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("feeRate.addSuccess.msg"));
		return getView(model, ADD_EDIT_FEE_RATE);
	}

	@PostMapping("/checkDupFeeCode")
	@PreAuthorize("hasAuthority('View Fees')")
	public ResponseEntity<Object> checkDupfeeCode(Model model, @RequestParam("feeCode") int feeCode) {

		boolean result = feeService.checkDistinctFeecode(feeCode);

		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/editFeeRate")
	@PreAuthorize("hasAuthority('Edit Fees')")
	public String editFeeRate(@RequestParam("feeId") int feeId, Model model) {
		FeeDTO feeRateDto = new FeeDTO();
		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES_FLAG);
		addFeeListData(model);
		try {
			feeRateDto = feeService.getEditFeeRate(feeId);

			if (feeRateDto.getRequestState().equalsIgnoreCase(BaseCommonConstants.REQUEST_STATE_REJECTED)) {
				boolean result = feeService.checkFeeRateAvailable(feeRateDto.getFeeId());
				if (result) {
					model.addAttribute(EDIT_FIRST_REJ_FEE_RATE, CommonConstants.YES_FLAG);
				}
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_FEE_RATE, ex);
		}
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
		return getView(model, ADD_EDIT_FEE_RATE);
	}

	@PostMapping("/updateFeeRate")
	@PreAuthorize("hasAuthority('Edit Fees')")
	public String updateFeeRate(@RequestParam("feeId") int feeId, @ModelAttribute FeeDTO feeRateDto, Model model) {
		try {
			String x = feeRateDto.getFeeDesc().replace('*', ',');
			feeRateDto.setFeeDesc(x);
			feeRateDto.setFeeId(feeId);
			if (!feeRateDto.getDateAction().isEmpty()) {
				String[] dateAction = feeRateDto.getDateAction().split("\\|");

				StringBuilder action = new StringBuilder(dateAction[0]);

				for (int i = 1; i < dateAction.length; i++) {
					action.append(",");
					action.append(dateAction[i]);
				}
				feeRateDto.setDateAction(action.toString());
			}
			feeService.updateEditFeeRate(feeRateDto);
			addFeeListData(model);
		} catch (Exception ex) {
			model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_EDIT_FEE_RATE, ex);
		}
		feeRateDto = feeService.getEditFeeRate(feeId);

		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("feeRate.updateSuccess.msg"));
		return getView(model, ADD_EDIT_FEE_RATE);
	}

	@PostMapping("/showFeeConfig")
	@PreAuthorize("hasAuthority('View Fees')")
	public String showMajorMinor(
			@RequestParam(value = SIGNIFICANCE, required = false, defaultValue = "M") String significance,
			Model model) {

		FeeDTO feeRateDto = new FeeDTO();

		List<FeeDTO> feeConfigList = feeService.getMajorMinorList(significance);

		model.addAttribute(CommonConstants.FEECONIFG, feeConfigList);
		prepareLookupFeeMajor(model);

		if (CommonConstants.MAJOR_TYPE.equalsIgnoreCase(significance)) {
			model.addAttribute(CommonConstants.MAJOR, CommonConstants.YES);

		} else {
			model.addAttribute(CommonConstants.MINOR, CommonConstants.YES);

		}
		feeRateDto.setSignificance(significance);
		model.addAttribute(CommonConstants.SHOW_FEE_CONFIG, CommonConstants.YES);
		model.addAttribute(CommonConstants.ADD_FEE_CONFIG, CommonConstants.YES);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
		model.addAttribute(SIGNIFICANCE, feeRateDto.getSignificance());

		return getView(model, SHOW_FEE_CONFIG);

	}

	@PostMapping("/showMinorConfig")
	@PreAuthorize("hasAuthority('View Fees')")
	public String showFeeMinorConfig(@RequestParam String feeConfigId, Model model) {

		List<FeeRateConfigDTO> feesMinorConfigList = feeService.getFeeMinor(feeConfigId);
		model.addAttribute(FEE_RATE_CONFIG_LIST, feesMinorConfigList);
		List<FeeDTO> feeConfigList = feeService.getMajorMinorList(CommonConstants.MINOR_TYPE);

		model.addAttribute(CommonConstants.FEECONIFG, feeConfigList);
		FeeDTO feeRateDto = new FeeDTO();
		feeRateDto.setSignificance(CommonConstants.MINOR_TYPE);
		model.addAttribute(CommonConstants.MINOR, CommonConstants.YES);
		model.addAttribute(CommonConstants.SHOW_FEE_CONFIG, CommonConstants.YES);
		model.addAttribute(CommonConstants.ADD_FEE_CONFIG, CommonConstants.YES);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
		model.addAttribute(SIGNIFICANCE, feeRateDto.getSignificance());
		return getView(model, SHOW_FEE_CONFIG);

	}

	@PostMapping("/feeConfigPendingForApproval")
	@PreAuthorize("hasAuthority('View Fees')")
	public String feeConfigPendingForApproval(Model model,
			@RequestParam(value = SIGNIFICANCE, required = false, defaultValue = "M") String significance) {

		FeeDTO feeRateDto = new FeeDTO();

		model.addAttribute(SHOW_APPROVAL_TAB, CommonConstants.YES);
		List<FeeDTO> pendingFeeConfigList = feeService.getPendingFeeConfig(significance);
		model.addAttribute(CommonConstants.FEE_CONFIG_PENDING_LIST, pendingFeeConfigList);
		if (CommonConstants.MAJOR_TYPE.equalsIgnoreCase(significance)) {
			model.addAttribute(CommonConstants.MAJOR, CommonConstants.YES);
			model.addAttribute(MAJORMINOR, CommonConstants.MAJOR);
		} else {
			model.addAttribute(CommonConstants.MINOR, CommonConstants.YES);
			model.addAttribute(MAJORMINOR, CommonConstants.MINOR);
		}
		validateCheckboxForMakerChecker(model);

		prepareLookupFeeMajor(model);
		feeRateDto.setSignificance(significance);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
		model.addAttribute(SIGNIFICANCE, feeRateDto.getSignificance());
		return getView(model, SHOW_FEE_CONFIG);
	}

	private void validateCheckboxForMakerChecker(Model model) {
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}
	}

	@PostMapping("/createFeeMajorMinor")
	@PreAuthorize("hasAuthority('Add Fees')")
	public String createFeeMajorMinor(Model model,
			@RequestParam(value = SIGNIFICANCE, defaultValue = "M") String significance) {

		FeeDTO feeDto = new FeeDTO();
		feeDto.setSignificance(significance);
		addFeeMajorMinorListData(model, ADD);
		if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MAJOR_TYPE)) {
			model.addAttribute(CommonConstants.ADD_MAJOR, CommonConstants.YES_FLAG);
		} else if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MINOR_TYPE)) {
			model.addAttribute(CommonConstants.ADD_MINOR, CommonConstants.YES_FLAG);
		}
		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(FEE_DTO, feeDto);

		return getView(model, ADD_EDIT_FEE_MAJOR_MINOR);
	}

	@PostMapping("/addFeeMajorMinor")
	@PreAuthorize("hasAuthority('Add Fees')")
	public String addFeeMajorMinor(@ModelAttribute FeeDTO feeDto, Model model) {

		try {

			if (StringUtils.isNotEmpty(feeDto.getSignificance())) {

				feeService.addFeeMajorMinor(feeDto);
			}
			addFeeMajorMinorListData(model, ADD);

			if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MAJOR_TYPE)) {
				model.addAttribute(CommonConstants.ADD_MAJOR, CommonConstants.YES_FLAG);
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("feeMajor.addSuccess.msg"));
			} else if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MINOR_TYPE)) {
				model.addAttribute(CommonConstants.ADD_MINOR, CommonConstants.YES_FLAG);
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("feeMinor.addSuccess.msg"));
			}
		} catch (Exception ex) {
			addFeeMajorMinorListData(model, ADD);
			model.addAttribute(FEE_DTO, feeDto);
			model.addAttribute(CommonConstants.ADD_MAJOR, CommonConstants.YES_FLAG);
			model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES);

			return handleErrorCodeAndForward(model, ADD_EDIT_FEE_MAJOR_MINOR, ex);
		}
		List<CodeValueDTO> fieldColumnList = feeService.getUnmappedFieldNameData(feeDto.getFeeConfigId());
		model.addAttribute(FIELD_COLUMN_LIST, fieldColumnList);
		model.addAttribute(FEE_CONFIG_LIST, feeDto.getFeeConfigDto());
		model.addAttribute(FEE_DTO, feeDto);
		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES);

		return getView(model, ADD_EDIT_FEE_MAJOR_MINOR);
	}

	@PostMapping("/editFeeMajorMinor")
	@PreAuthorize("hasAuthority('Edit Fees')")
	public String editFeeMajorMinor(@RequestParam("feeConfigId") String feeConfigId,
			@RequestParam("requestId") int requestId,
			@RequestParam(value = SIGNIFICANCE, defaultValue = "M") String significance, Model model) {
		FeeDTO feeDto = new FeeDTO();
		feeDto.setRequestId(requestId);
		feeDto.setSignificance(significance);
		feeDto.setFeeConfigId(feeConfigId);
		feeDto = feeService.getEditFeeMajorMinor(feeDto);
		if (feeDto.getRequestState().equalsIgnoreCase(CommonConstants.REQUEST_STATE_REJECTED)) {

			if (CommonConstants.MAJOR_TYPE.equalsIgnoreCase(significance)) {
				boolean result = feeService.checkFeeMajorAvailable(feeDto.getFeeMajorId());
				if (result) {
					model.addAttribute(EDIT_FIRST_REJ_MAJ, CommonConstants.YES_FLAG);
				}

			} else if (CommonConstants.MINOR_TYPE.equalsIgnoreCase(significance)) {

				boolean result = feeService.checkFeeMinorAvailable(feeDto.getFeeMinorId());
				if (result) {

					model.addAttribute(EDIT_FIRST_REJ_MINOR, CommonConstants.YES_FLAG);

				}
			}

		}
		addFeeMajorMinorListData(model, EDIT);
		List<CodeValueDTO> fieldColumnList = feeService.getUnmappedFieldNameData(feeConfigId);

		model.addAttribute(FIELD_COLUMN_LIST, fieldColumnList);

		model.addAttribute(FEE_CONFIG_LIST, feeDto.getFeeConfigDto());

		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);

		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES_FLAG);

		if (CommonConstants.MAJOR_TYPE.equalsIgnoreCase(significance)) {
			model.addAttribute(CommonConstants.EDIT_MAJOR, CommonConstants.YES_FLAG);
			feeDto.setSignificance(significance);
			addFeeConfig(model, feeConfigId);
		} else if (CommonConstants.MINOR_TYPE.equalsIgnoreCase(significance)) {
			model.addAttribute(CommonConstants.MINOR_REJECT_EDIT, BaseCommonConstants.NO_FLAG);
			model.addAttribute(CommonConstants.EDIT_MINOR, CommonConstants.YES_FLAG);
			feeDto.setSignificance(significance);
		}
		model.addAttribute(FEE_DTO, feeDto);
		return getView(model, ADD_EDIT_FEE_MAJOR_MINOR);
	}

	@PostMapping("/updateFeeMajorMinor")
	@PreAuthorize("hasAuthority('Edit Fees')")
	public String updateFeeMajorMinor(@ModelAttribute FeeDTO feeDto, Model model) {
		try {

			if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MINOR_TYPE)) {
				if (feeDto.getRequestState().equalsIgnoreCase(CommonConstants.REQUEST_STATE_REJECTED)) {
					model.addAttribute(CommonConstants.MINOR_REJECT_EDIT, CommonConstants.YES_FLAG);
				} else {
					model.addAttribute(CommonConstants.MINOR_REJECT_EDIT, BaseCommonConstants.NO_FLAG);
				}
			}
			feeService.updateFeeMajorMinor(feeDto);
			addFeeMajorMinorListData(model, EDIT);
			model.addAttribute(EDIT_FIRST_REJ_MAJ, CommonConstants.YES_FLAG);

			model.addAttribute(SHOW, ENABLE);

			FeeDTO fee = feeService.getEditFeeMajoRConfigInfo(feeDto);

			model.addAttribute(FEE_CONFIG_LIST, fee.getFeeConfigDto());

		} catch (Exception ex) {
			model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES);
			return handleErrorCodeAndForward(model, ADD_EDIT_FEE_MAJOR_MINOR, ex);
		}
		List<CodeValueDTO> fieldColumnList = feeService.getUnmappedFieldNameData(feeDto.getFeeConfigId());
		model.addAttribute(FIELD_COLUMN_LIST, fieldColumnList);

		model.addAttribute(FEE_DTO, feeDto);
		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES);
		if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MAJOR_TYPE)) {

			model.addAttribute(CommonConstants.EDIT_MAJOR, CommonConstants.YES_FLAG);
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("feeMajor.updateSuccess.msg"));
		} else if (feeDto.getSignificance().equalsIgnoreCase(CommonConstants.MINOR_TYPE)) {

			model.addAttribute(CommonConstants.EDIT_MINOR, CommonConstants.YES_FLAG);
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("feeMinor.updateSuccess.msg"));
		}

		return getView(model, ADD_EDIT_FEE_MAJOR_MINOR);
	}

	public void addFeeMajorMinorListData(Model model, String addOrEditFlag) {
		List<CodeValueDTO> schemecode = lookupService.getLookupData(CommonConstants.SCHEME_CODE_FEE_MAJOR);
		List<CodeValueDTO> prodCdList = lookupService.getLookupData(BaseCommonConstants.PRODUCT_TYPE);
		List<CodeValueDTO> cardTypeList = lookupService.getLookupData(CommonConstants.CARD_TYPE);
		List<CodeValueDTO> cardBrandList = lookupService.getLookupData(CommonConstants.CARD_BRAND);
		List<CodeValueDTO> relOperatorsList = lookupService.getLookupData(CommonConstants.REL_OPR);

		List<CodeValueDTO> funcCodeList = lookupService.getLookupData(BaseCommonConstants.FEE_FUNC_CODE);
		List<CodeValueDTO> fieldColumnList = new ArrayList<>();
		if (ADD.equalsIgnoreCase(addOrEditFlag)) {
			fieldColumnList = lookupService.getLookupData(CommonConstants.FIELD_EX);
		}

		model.addAttribute(SCHEME_CODE, schemecode);
		model.addAttribute(PROD_C_LIST, prodCdList);
		model.addAttribute(CARD_TYPE_LIST, cardTypeList);
		model.addAttribute(CARD_BRAND_LIST, cardBrandList);
		model.addAttribute(REL_OPR_LIST, relOperatorsList);
		model.addAttribute(FIELD_COLUMN_LIST, fieldColumnList);
		model.addAttribute(FUNC_CODE_LIST, funcCodeList);
	}

	@PostMapping("/addFeeConfig")
	@PreAuthorize("hasAuthority('Add Fees')")
	public String addFeeConfig(Model model, String feeMajorId) {
		FeeMajorMinorPriorityDTO feeMajorMinorPrior = new FeeMajorMinorPriorityDTO();
		model.addAttribute(CommonConstants.ADD_FEECONFIG_LIST, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.FEE_CONFIG_LIST_DTO, feeMajorMinorPrior);
		if (StringUtils.isNotEmpty(feeMajorId)) {
			addFeeConfigData(model, feeMajorId);
		}
		return getView(model, ADD_EDIT_FEE_MINOR_PRIOR);
	}

	public void addFeeConfigData(Model model, String feeMajorId) {
		List<FeeMajorMinorPriorityDTO> interchangeFeeID = feeService.getFeeIdList(CommonConstants.INTERCHANGE_FEE_CODE);
		List<FeeMajorMinorPriorityDTO> acqProcessingFeeID = feeService
				.getFeeIdList(CommonConstants.PROCESSING_FEE_CODE);
		List<FeeMajorMinorPriorityDTO> acqAssessmentFeeID = feeService
				.getFeeIdList(CommonConstants.ASSESSMENT_FEE_CODE);
		List<FeeMajorMinorPriorityDTO> issProcessingFeeID = feeService
				.getFeeIdList(CommonConstants.PROCESSING_FEE_CODE);
		List<FeeMajorMinorPriorityDTO> issAssessmentFeeID = feeService
				.getFeeIdList(CommonConstants.ASSESSMENT_FEE_CODE);
		List<FeeMajorMinorPriorityDTO> issAuthFeeID = feeService.getFeeIdList(CommonConstants.AUTH_FEE_CODE);
		List<FeeMajorMinorPriorityDTO> acqAuthFeeID = feeService.getFeeIdList(CommonConstants.AUTH_FEE_CODE);

		List<FeeMajorMinorPriorityDTO> feeMinorId = feeService.getFeeMinorIdList(feeMajorId);

		
		model.addAttribute(INTERCHANGE_FEE_ID, interchangeFeeID);
		model.addAttribute(ACQ_ASS_FEE_ID, acqAssessmentFeeID);
		model.addAttribute(ACQ_PROC_FEE_ID, acqProcessingFeeID);
		model.addAttribute(ISS_PROC_FEE_ID, issProcessingFeeID);
		model.addAttribute(ISS_ASS_FEE_ID, issAssessmentFeeID);
		model.addAttribute(ISS_AUTH_FEE_ID, issAuthFeeID);
		model.addAttribute(ACQ_AUTH_FEE_ID, acqAuthFeeID);
		model.addAttribute(FEE_MINOR, feeMinorId);

		List<FeeMajorMinorPriorityDTO> savedFeeConfigList = feeService.getSavedFeeConfigByMajorId(feeMajorId,
				CommonConstants.REQUEST_STATE_APPROVED);
		if (savedFeeConfigList.isEmpty()) {
			List<FeeMajorMinorPriorityDTO> savedFeeConfigLists = feeService.getSavedFeeConfigByMajorId(feeMajorId,
					CommonConstants.REQUEST_STATE_SAVED);

			if (savedFeeConfigLists.isEmpty()) {
				List<FeeMajorMinorPriorityDTO> savedFeeConfigListsS = feeService.getSavedFeeConfigByMajorId(feeMajorId,
						CommonConstants.REQUEST_STATE_SUBMITTED);

				if (savedFeeConfigListsS.isEmpty()) {
					List<FeeMajorMinorPriorityDTO> savedFeeConfigListsSS = feeService
							.getSavedFeeConfigByMajorId(feeMajorId, CommonConstants.REQUEST_STATE_REJECTED);

					model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigListsSS);
				} else {

					model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigListsS);
				}
			} else {

				model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigLists);
			}
		} else {

			model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigList);
		}
		model.addAttribute(CommonConstants.SHOW_SAVED_FEE_CONFIG, CommonConstants.YES);
	}

	@PostMapping("/editFeeConfig")
	@PreAuthorize("hasAuthority('Edit Fees')")
	public String editFeeConfig(@RequestParam("majorId") String majorId, @RequestParam("priority") int priority,
			Model model) {
		FeeMajorMinorPriorityDTO feeMajorMinor = new FeeMajorMinorPriorityDTO();
		model.addAttribute(CommonConstants.EDIT_FEECONFIG_LIST, CommonConstants.YES_FLAG);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.FEE_CONFIG_LIST_DTO, feeMajorMinor);
		addFeeConfigData(model, majorId);
		try {
			feeMajorMinor = feeService.getEditFeeConfig(Integer.parseInt(majorId), priority);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_FEE_MINOR_PRIOR, ex);
		}
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeMajorMinor);
		return getView(model, ADD_EDIT_FEE_MINOR_PRIOR);
	}

	@PostMapping("/checkDupFeeMajorMinorId")
	@PreAuthorize("hasAuthority('View Fees')")
	public ResponseEntity<Object> checkDupFeeMajorMinorId(Model model, @RequestParam("feeConfigId") String feeConfigId,
			@RequestParam(SIGNIFICANCE) String significance) {

		boolean result = feeService.checkDistinctFeeMajorMinorId(feeConfigId, significance);

		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/showSavedFeeConfig")
	@PreAuthorize("hasAuthority('View Fees')")
	public String showSavedMembers(Model model) {

		List<FeeMajorMinorPriorityDTO> savedFeeConfigList = feeService.getSavedFeeConfig();
		model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigList);
		model.addAttribute(CommonConstants.SHOW_SAVED_FEE_CONFIG, CommonConstants.YES_FLAG);
		return getView(model, SHOW_FEE_CONFIG);
	}

	@PostMapping("/viewRejFeeMajor")
	@PreAuthorize("hasAuthority('View Fees')")
	public String getRejectedFeeMajor(@RequestParam("feeConfigId") String majorId, Model model) {

		prepareLookupFeeMajor(model);
		FeeDTO feesRateDTO = feeService.getFeeMajorForView(majorId);
		feesRateDTO.setFeeMajorId(majorId);
		List<FeeRateConfigDTO> feesDto = feeService.getFeeMajorConfig(majorId);

		feesRateDTO.setFeeConfigDto(feesDto);
		feesRateDTO.setSignificance(CommonConstants.MAJOR_TYPE);
		feesRateDTO.setRequestId(feesDto.get(0).getRequestId());
		feesRateDTO.setRequestState(CommonConstants.RECORD_APPROVED);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feesRateDTO);
		model.addAttribute(SHOW_EDIT, CommonConstants.YES_FLAG);
		model.addAttribute(SHOW, ENABLE);
		List<FeeMajorMinorPriorityDTO> savedFeeConfigList = feeService.getSavedFeeConfigByMajorId(majorId,
				CommonConstants.REQUEST_STATE_APPROVED);

		if (!savedFeeConfigList.isEmpty()) {
			savedFeeConfigList = feeService.prepareFeeDescriptionForMinorPrior(savedFeeConfigList);
			model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigList);
		} else {

			List<FeeMajorMinorPriorityDTO> savedFeeConfigListRej = feeService.getSavedFeeConfigByMajorId(majorId,
					CommonConstants.REQUEST_STATE_REJECTED);
			if (savedFeeConfigListRej != null) {
				savedFeeConfigListRej = feeService.prepareFeeDescriptionForMinorPrior(savedFeeConfigListRej);
				model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigListRej);
			}
		}

		return getView(model, VIEW_APPROVE_FEE_MAJOR);
	}

	@PostMapping("/viewFeeMajor")
	@PreAuthorize("hasAuthority('View Fees')")
	public String viewFeeMajor(@RequestParam("feeConfigId") String majorId, Model model) {

		prepareLookupFeeMajor(model);
		FeeDTO feesRateDTO = feeService.getFeeMajor(majorId);
		feesRateDTO.setFeeMajorId(majorId);
		List<FeeRateConfigDTO> feesDto = new ArrayList<>();
		try {
			feesDto = feeService.prepareFeeConfigMajorFromJson(majorId);
		} catch (Exception e) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FEE_MAJOR, e);
		}
		setCreatedByLastUpdatedByInConfig(feesDto, feesRateDTO);
		feesRateDTO.setFeeConfigDto(feesDto);
		feesRateDTO.setSignificance(CommonConstants.MAJOR_TYPE);
		feesRateDTO.setRequestState(CommonConstants.RECORD_APPROVED);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feesRateDTO);
		model.addAttribute(SHOW_EDIT, CommonConstants.YES_FLAG);
		List<FeeMajorMinorPriorityDTO> savedFeeConfigList = feeService.getFeeMinorPriorityMain(majorId);

		if (!savedFeeConfigList.isEmpty()) {
			savedFeeConfigList = feeService.prepareFeeDescriptionForMinorPrior(savedFeeConfigList);
			model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigList);
		}

		return getView(model, VIEW_APPROVE_FEE_MAJOR);
	}

	public void prepareLookupFeeMajor(Model model) {
		List<String> lookUpList = new ArrayList<>();
		lookUpList.add(CommonConstants.CARD_TYPE);
		lookUpList.add(BaseCommonConstants.PRODUCT_TYPE);
		lookUpList.add(CommonConstants.CARD_BRAND);
		lookUpList.add(BaseCommonConstants.FEE_FUNC_CODE);
		lookUpList.add(CommonConstants.SCHEME_CODE_FEE_MAJOR);

		List<Map<String, String>> lookUpMapArr = masterService.prepareLookUpMap(lookUpList);

		if (null != lookUpMapArr && !lookUpMapArr.isEmpty()) {
			model.addAttribute(BIN_CARD_TYPE_MAP, lookUpMapArr.get(0));
			model.addAttribute(BIN_PROD_TYPE, lookUpMapArr.get(1));

			model.addAttribute(BIN_CARD_BRAND, lookUpMapArr.get(2));
			model.addAttribute(FUNC_CODE_MAP, lookUpMapArr.get(3));
			model.addAttribute(SCHEME_TYPE_MAP, lookUpMapArr.get(4));
		}
	}

	@PostMapping("/viewApproveFeeMajor")
	@PreAuthorize("hasAuthority('View Fees')")
	public String getPendingFee(@RequestParam("feeConfigId") String majorId, Model model) {

		prepareLookupFeeMajor(model);
		FeeDTO feesRateDTO = feeService.getFeeMajorForView(majorId);

		feesRateDTO.setFeeMajorId(majorId);
		List<FeeRateConfigDTO> feesDto = feeService.getFeeMajorConfig(majorId);

		feesRateDTO.setFeeConfigDto(feesDto);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feesRateDTO);
		List<FeeMajorMinorPriorityDTO> savedFeeConfigList = feeService.getSavedFeeConfigByMajorId(majorId,
				CommonConstants.REQUEST_STATE_SUBMITTED);
		if (savedFeeConfigList != null) {
			savedFeeConfigList = feeService.prepareFeeDescriptionForMinorPrior(savedFeeConfigList);
			model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigList);
		}

		return getView(model, VIEW_APPROVE_FEE_MAJOR);
	}

	@PostMapping("/approveFeeMajorStatus")
	@PreAuthorize("hasAuthority('Approve Fees')")
	public String approveFeeMajorStatus(@RequestParam("feeMajorId") String feeMajorId,
			@RequestParam(STATUS) String status, @RequestParam("remarks") String remarks, Model model) {
		try {
			prepareLookupFeeMajor(model);
			FeeDTO feeRateDto = feeService.updateApproveOrRejectFeeMajor(feeMajorId, status, remarks);
			checkFeeMajorApproveStatus(feeRateDto, model);
			List<FeeRateConfigDTO> feesDto = feeService.getFeeMajorConfig(feeMajorId);

			feeRateDto.setFeeConfigDto(feesDto);

			model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
			feeRateDto.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			model.addAttribute(SHOW, ENABLE);

			if (status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_REJECTED)) {
				List<FeeMajorMinorPriorityDTO> savedFeeConfigListReqRej = feeService
						.getSavedFeeConfigByMajorId(feeMajorId, CommonConstants.REQUEST_STATE_REJECTED);
				if (savedFeeConfigListReqRej != null) {
					savedFeeConfigListReqRej = feeService.prepareFeeDescriptionForMinorPrior(savedFeeConfigListReqRej);
					model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigListReqRej);
				}
			} else if (status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_APPROVED)) {
				List<FeeMajorMinorPriorityDTO> savedFeeConfigList = feeService.getSavedFeeConfigByMajorId(feeMajorId,
						CommonConstants.REQUEST_STATE_APPROVED);
				if (savedFeeConfigList != null) {
					savedFeeConfigList = feeService.prepareFeeDescriptionForMinorPrior(savedFeeConfigList);
					model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigList);
				}

			}

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FEE_MAJOR, ex);
		}
		return getView(model, VIEW_APPROVE_FEE_MAJOR);
	}

	private void checkFeeMajorApproveStatus(FeeDTO feeRateDto, Model model) {
		Locale locale=Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(feeRateDto.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_FeeMajorApproved", null, locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_FeeMajorRejected", null, locale));
		}
	}

	@PostMapping("/viewFeeMinor")
	@PreAuthorize("hasAuthority('View Fees')")
	public String getFeeMinor(@RequestParam("feeConfigId") String minorId, Model model) {

		List<FeeRateConfigDTO> feesRateDTO = feeService.getFeeMinorForView(minorId);

		FeeDTO feeDTO = new FeeDTO();
		feeDTO.setFeeMinorId(minorId);
		feeDTO.setFeeConfigDto(feesRateDTO);
		feeDTO.setRequestState(feesRateDTO.get(0).getRequestState());
		feeDTO.setSignificance(CommonConstants.MINOR_TYPE);
		feeDTO.setRequestId(feesRateDTO.get(0).getRequestId());
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeDTO);
		model.addAttribute(SHOW_EDIT, CommonConstants.YES_FLAG);

		return getView(model, VIEW_APPROVE_FEE_MINOR);
	}

	@PostMapping("/viewApproveFeeMinor")
	@PreAuthorize("hasAuthority('View Fees')")
	public String getPendingFeeMinor(@RequestParam("feeConfigId") String minorId, Model model) {
		List<FeeRateConfigDTO> feesRateDTO = feeService.getFeeMinor(minorId);
		FeeDTO feeDTO = new FeeDTO();
		feeDTO.setFeeMinorId(minorId);
		feeDTO.setRequestState(feesRateDTO.get(0).getRequestState());
		feeDTO.setSignificance(CommonConstants.MINOR_TYPE);
		feeDTO.setFeeConfigDto(feesRateDTO);
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeDTO);

		return getView(model, VIEW_APPROVE_FEE_MINOR);
	}

	@PostMapping("/viewRejFeeMinor")
	@PreAuthorize("hasAuthority('View Fees')")
	public String getFeeRejMinor(@RequestParam("feeConfigId") String minorId, Model model) {

		List<FeeRateConfigDTO> feesRateDTO = feeService.getFeeMinor(minorId);

		FeeDTO feeDTO = new FeeDTO();
		feeDTO.setFeeMinorId(minorId);
		feeDTO.setFeeConfigDto(feesRateDTO);
		feeDTO.setRequestState(feesRateDTO.get(0).getRequestState());
		feeDTO.setSignificance(CommonConstants.MINOR_TYPE);
		feeDTO.setRequestId(feesRateDTO.get(0).getRequestId());
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeDTO);
		model.addAttribute(SHOW_EDIT, CommonConstants.YES_FLAG);

		return getView(model, VIEW_APPROVE_FEE_MINOR);
	}

	@PostMapping("/approveFeeMinorStatus")
	@PreAuthorize("hasAuthority('Approve Fees')")
	public String approveFeeMinorStatus(@RequestParam("feeMinorId") String minorId, @RequestParam(STATUS) String status,
			@RequestParam("remarks") String remarks, Model model) {
		try {

			FeeDTO feeRateDto = feeService.updateApproveOrRejectFeeMinor(minorId, status, remarks);
			checkFeeMinorApproveStatus(feeRateDto, model);

			feeRateDto.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			List<FeeRateConfigDTO> feesRateDTO = feeService.getFeeMinor(minorId);
			feeRateDto.setFeeConfigDto(feesRateDTO);
			model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FEE_MINOR, ex);
		}
		return getView(model, VIEW_APPROVE_FEE_MINOR);
	}

	private void checkFeeMinorApproveStatus(FeeDTO feeRateDto, Model model) {
		Locale locale=Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(feeRateDto.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_FeeMinorApproved", null, locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_FeeMinorRejected", null, locale));
		}
	}

	@PostMapping("/discardFeeMinor")
	@PreAuthorize("hasAuthority('Edit Fees')")
	public String discardFeeMinor(@RequestParam("feeMinorId") String feeMinorId, Model model) {

		try {
			List<FeeRateConfigDTO> feesRateDTO = feeService.getFeeMinor(feeMinorId);
			FeeDTO feeDTO = new FeeDTO();
			feeDTO.setFeeMinorId(feeMinorId);
			feeDTO.setRequestState(feesRateDTO.get(0).getRequestState());
			feeDTO.setSignificance(CommonConstants.MINOR_TYPE);
			feeDTO.setFeeConfigDto(feesRateDTO);
			model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeDTO);
			feeService.discardFeeMinor(feeMinorId);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_FEE_MINOR, ex);
		}

		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("feeMinor.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_FEE_MINOR);
	}

	@PostMapping("/discardFeeMajor")
	@PreAuthorize("hasAuthority('Edit Fees')")
	public String discardFeeMajor(@RequestParam("feeMajorId") String feeMajorId, Model model) {
		FeeDTO feeRatedto = new FeeDTO();
		try {
			feeRatedto = feeService.discardFeeMajor(feeMajorId);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_FEE_MAJOR, ex);
		}

		prepareLookupFeeMajor(model);
		List<FeeRateConfigDTO> feesDto = feeService.getFeeMajorConfig(feeMajorId);

		feeRatedto.setFeeConfigDto(feesDto);
		List<FeeMajorMinorPriorityDTO> savedFeeConfigList = feeService.getSavedFeeConfigByMajorId(feeMajorId,
				CommonConstants.REQUEST_STATE_APPROVED);
		if (savedFeeConfigList != null) {
			savedFeeConfigList = feeService.prepareFeeDescriptionForMinorPrior(savedFeeConfigList);
			model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigList);
		}
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRatedto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("feeMajor.discardSuccess.msg"));

		return getView(model, VIEW_APPROVE_FEE_MAJOR);
	}

	@PostMapping("/saveFeeConfig")
	@PreAuthorize("hasAuthority('Edit Fees')")
	public String saveFeeConfig(@ModelAttribute("feeConfigListDto") BaseFeeMajorMinorPriorityDTO feeConfigListDto,
			@ModelAttribute(FEE_DTO) BaseFeeDTO feeDto, Model model, @RequestParam("feeMajorId") String feeMajorId,
			@RequestParam("reqType") String reqType) {

		try {

			FeeDTO updatemajor = new FeeDTO();
			updatemajor.setFeeMajorId(feeDto.getFeeMajorId());
			updatemajor.setRequestState(reqType);
			updatemajor.setRequestId(feeConfigListDto.getRequestId());
			updatemajor.setSignificance(CommonConstants.MAJOR_TYPE);
			updatemajor.setSchemeCode(feeDto.getSchemeCode());
			updatemajor.setFunCd(feeDto.getFunCd());
			updatemajor.setProductCode(feeDto.getProductCode());
			updatemajor.setCardType(feeDto.getCardType());
			updatemajor.setCardBrand(feeDto.getCardBrand());
			updatemajor.setPriority(feeDto.getPriority());
			updatemajor.setFeeConfigs(feeDto.getFeeConfigs());
			updatemajor.setOldFeeMajorId(feeDto.getOldFeeMajorId());

			feeService.updateFeeMajorMinor(updatemajor);

			model.addAttribute(FEE_CONFIG_LIST, updatemajor.getFeeConfigDto());
			feeConfigListDto.setFeeMajorId(feeMajorId);

			feeService.addFeeConfig(feeConfigListDto, reqType);

		} catch (Exception ex) {
			addFeeMajorMinorListData(model, EDIT);
			model.addAttribute(CommonConstants.ADD_FEECONFIG_LIST, CommonConstants.YES_FLAG);
			model.addAttribute(CommonConstants.FEE_CONFIG_LIST_DTO, feeConfigListDto);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES);
			return handleErrorCodeAndForward(model, ADD_EDIT_FEE_MINOR_PRIOR, ex);
		}
		FeeDTO feeDtos = new FeeDTO();
		feeDtos.setSignificance(CommonConstants.MAJOR_TYPE);
		feeDtos.setRequestId(feeConfigListDto.getRequestId());
		feeDtos.setFeeConfigId(feeMajorId);
		feeDtos = feeService.getEditFeeMajorMinor(feeDtos);
		model.addAttribute(FEE_DTO, feeDtos);
		addFeeConfigData(model, feeMajorId);
		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
		if (CommonConstants.REQUEST_STATE_SUBMITTED.equalsIgnoreCase(reqType)) {
			model.addAttribute(CommonConstants.SAVE_BUTTON, ENABLE);
			List<FeeMajorMinorPriorityDTO> savedFeeConfigList = feeService.getSavedFeeConfigByMajorId(feeMajorId,
					CommonConstants.REQUEST_STATE_SUBMITTED);
			if (savedFeeConfigList != null) {
				savedFeeConfigList = feeService.prepareFeeDescriptionForMinorPrior(savedFeeConfigList);
				model.addAttribute(CommonConstants.SAVED_FEE_CONFIG, savedFeeConfigList);
			}
			model.addAttribute(CommonConstants.SHOW_SAVED_FEE_CONFIG, CommonConstants.YES);
		}

		model.addAttribute(CommonConstants.EDIT_MAJOR, CommonConstants.YES_FLAG);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.YES);
		addFeeMajorMinorListData(model, EDIT);
		model.addAttribute("showButton2", CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.ADD_FEECONFIG_LIST, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.FEE_CONFIG_LIST_DTO, feeConfigListDto);
		List<CodeValueDTO> fieldColumnList = feeService.getUnmappedFieldNameData(feeMajorId);
		model.addAttribute(FIELD_COLUMN_LIST, fieldColumnList);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("feeConfig.addSuccess.msg"));
		return getView(model, ADD_EDIT_FEE_MAJOR_MINOR);
	}

	@PostMapping("/getMinorInfo")
	@PreAuthorize("hasAuthority('View Fees')")
	public ResponseEntity<Object> getMinorInfo(Model model, @RequestBody FeeDTO feeDto) {
		String[] strArray = feeDto.getMinorIdsArr();
		List<FeeDTO> minorList = feeService.getMinorInfo(strArray);
		return new ResponseEntity<>(minorList, HttpStatus.OK);
	}

	@PostMapping("/removeMajorMinorMapping")
	@PreAuthorize("hasAuthority('Edit Fees')")
	public ResponseEntity<Object> removeMajorMinorMapping(Model model,
			@RequestParam(required = true, name = "feeMinorId") String feeMinorId,
			@RequestParam(required = true, name = "feeMajorId") String feeMajorId) {

		boolean result = feeService.removeMajorMinorMapping(feeMinorId, feeMajorId);
		feeService.updateStgStatus(feeMajorId);
		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_SUCCESS);
			jsonResponse.addProperty("feeMinorIdValue", feeMinorId);
		} else {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PostMapping("/checkPriorityMapwithMajorId")
	@PreAuthorize("hasAuthority('View Fees')")
	public ResponseEntity<Object> checkPriorityMapwithMajorId(Model model, @RequestParam("priority") String priority,
			@RequestParam("feeMajorId") String feeMajorId, @RequestParam("cardType") String cardType) {

		Pair<List<String>, String> p = feeService.checkDuplicatePriority(Integer.parseInt(priority), feeMajorId,
				cardType);

		JsonObject jsonResponse = new JsonObject();

		if ("true".equalsIgnoreCase(p.getValue())) {
			String majorId = "";
			List<String> feeIdList = p.getKey();
			for (String i : feeIdList) {
				majorId = String.valueOf(i);

			}
			jsonResponse.addProperty("feeMajorId", majorId);
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_SUCCESS);

		} else {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/approveFeeMajorBulkStatus")
	@PreAuthorize("hasAuthority('Approve Fees')")
	public String approveFeeMajorBulkStatus(@RequestParam("feeMajorIdList") String feeMajorIdList,
			@RequestParam(STATUS) String status, @RequestParam("remarks") String remarks, Model model) {

		FeeDTO feeRateDto = new FeeDTO();

		try {
			feeRateDto = feeService.updateApproveOrRejectFeeMajorBulk(feeMajorIdList, status, remarks);
			checkFeeMajorBulkApproveStatus(feeRateDto, model);
			feeRateDto.setSignificance(CommonConstants.MAJOR_TYPE);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FEE_MAJOR, ex);
		}

		try {
			loadApproveFeeMinorBulkPage(model, feeRateDto);

			prepareLookupFeeMajor(model);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_FEE_CONFIG, ex);
		}
		feeRateDto.setSignificance(feeRateDto.getSignificance());
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
		model.addAttribute(SIGNIFICANCE, feeRateDto.getSignificance());
		return getView(model, SHOW_FEE_CONFIG);

	}

	private void checkFeeMajorBulkApproveStatus(FeeDTO feeRateDto, Model model) {
		Locale localeNew=Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(feeRateDto.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_FeeMajorApproved", null, localeNew));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_FeeMajorRejected", null, localeNew));
		}
	}

	@PostMapping("/approveFeeMinorBulkStatus")
	@PreAuthorize("hasAuthority('Approve Fees')")
	public String approveFeeMinorBulkStatus(@RequestParam("feeMinorIdList") String minorIdList,
			@RequestParam(STATUS) String status, @RequestParam("remarks") String remarks, Model model) {

		FeeDTO feeRateDto = new FeeDTO();
		try {

			feeRateDto = feeService.updateApproveOrRejectFeeMinorBulk(minorIdList, status, remarks);
			checkFeeMinorApproveBulkStatus(feeRateDto, model);
			feeRateDto.setSignificance(CommonConstants.MINOR_TYPE);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FEE_MINOR, ex);
		}

		try {
			loadApproveFeeMinorBulkPage(model, feeRateDto);

			prepareLookupFeeMajor(model);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_FEE_CONFIG, ex);
		}
		feeRateDto.setSignificance(feeRateDto.getSignificance());
		model.addAttribute(CommonConstants.FEE_RATE_INFO_DTO, feeRateDto);
		model.addAttribute(SIGNIFICANCE, feeRateDto.getSignificance());
		return getView(model, SHOW_FEE_CONFIG);

	}

	private void loadApproveFeeMinorBulkPage(Model model, FeeDTO feeRateDto) {
		model.addAttribute(SHOW_APPROVAL_TAB, CommonConstants.YES);
		List<FeeDTO> pendingFeeConfigList = feeService.getPendingFeeConfig(feeRateDto.getSignificance());
		model.addAttribute(CommonConstants.FEE_CONFIG_PENDING_LIST, pendingFeeConfigList);
		if (CommonConstants.MAJOR_TYPE.equalsIgnoreCase(feeRateDto.getSignificance())) {
			model.addAttribute(CommonConstants.MAJOR, CommonConstants.YES);
		} else {
			model.addAttribute(CommonConstants.MINOR, CommonConstants.YES);
		}

		validateCheckboxForMakerChecker(model);
	}

	private void checkFeeMinorApproveBulkStatus(FeeDTO feeRateDto, Model model) {
		Locale localeNew=Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(feeRateDto.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_FeeMinorApproved", null, localeNew));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_FeeMinorRejected", null, localeNew));
		}
	}

	public void setCreatedByLastUpdatedByInConfig(List<FeeRateConfigDTO> feesDto, FeeDTO feesRateDTO) {
		feesDto.forEach(feeConfigDto -> {

			feeConfigDto.setCreatedBy(feesRateDTO.getCreatedBy());
			feeConfigDto.setCreatedOn(feesRateDTO.getCreatedOn());
			feeConfigDto.setLastUpdatedBy(feesRateDTO.getLastUpdatedBy());
			feeConfigDto.setLastUpdatedOn(feesRateDTO.getLastUpdatedOn());
		});
	}
}
