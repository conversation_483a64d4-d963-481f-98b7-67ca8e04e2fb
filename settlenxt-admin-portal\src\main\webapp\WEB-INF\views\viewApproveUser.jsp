<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/viewApproveUser.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">Please
		Select Approve/Reject action.</div>
	<div class="alert alert-danger remarkMust" role="alert">Please
		Enter Remarks.</div>
	<c:url value="approveUserStatus" var="approveUserStatus" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveUser" modelAttribute="userInfoDto"
		action="${approveUserStatus}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"> Pending User</span></strong>
							
								<div class="icon_bar">
								<sec:authorize access="hasAuthority('Edit User') ">
							<c:if test="${userInfoDto.requestState eq 'R'}">
										<a data-toggle="tooltip" title="Edit"
											onclick="userAction('N','/editRejectedUser');" href="#"><img
											src="./static/images/edit-grey.png" alt="edit"></a>
									</c:if>
									</sec:authorize>
									</div>
					</div>

					<div class="panel-body">
						<input type="hidden" id="userId" value="${userInfoDto.userId}" />
<input type="hidden" id="userType" value='${reqType}' />
						<input type="hidden" id="crtuser"
							value="${userInfoDto.lastUpdatedBy}" />
						<table class="table table-striped infobold"
							style="font-size: 12px">
								<caption style="display:none;">USERS</caption>
								<thead style="display:none;">
								<th scope="col"></th>
								</thead>
							<tbody>
								<tr>
									<td colspan="10"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data">Request Information</span></strong>
										</div></td>

								</tr>
								<tr>
									<td><label><spring:message
												code="sm.lbl.requestType" /></label></td>
									<td>${userInfoDto.lastOperation}</td>
									<td><label><spring:message
												code="sm.lbl.requestDate" /></label></td>
												    <td>${userInfoDto.lastUpdatedOn.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>
								<%-- 	<td>${userInfoDto.lastUpdatedOn}</td> --%>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>

								</tr>
								<tr>
									<td><label><spring:message
												code="sm.lbl.requestStatus" /></label></td>
									<c:if test="${userInfoDto.requestState=='A' }">
										<td>Approved</td>
									</c:if>
									<c:if test="${userInfoDto.requestState=='P' }">
										<td>Pending for Approval</td>
									</c:if>
									<c:if test="${userInfoDto.requestState=='R' }">
										<td>Rejected</td>
									</c:if>
									<c:if test="${userInfoDto.requestState=='D' }">
										<td>Discarded</td>
									</c:if>

									<td><label><spring:message code="sm.lbl.requestBy" /></label></td>
									<td>${userInfoDto.lastUpdatedBy}</td>

									<td><label><spring:message
												code="sm.lbl.checkerComments" /></label></td>
									<td colspan=2>${userInfoDto.checkerComments}</td>
									<td></td>
									<td></td>
									<td></td>
								</tr>


								<td colspan="10"><div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-user"></span> <span
											data-i18n="Data">User Information</span></strong>
									</div></td>


								
								
								
								
										<tr>

<td><label><spring:message code="am.lbl.loginId" /></label></td>
										<td id="loginId">${userInfoDto.loginId }</td>
										<td><label><spring:message code="sm.lbl.bankName" /></label></td>
										<td>${userInfoDto.bankName }</td>
										<c:if test="${not empty userInfoDto.accessLevel }">
										
										<td><label> <spring:message
													code="sm.lbl.accessLevel" /></label></td>
										<c:if test="${userInfoDto.accessLevel=='P' }">
											<td>Participant</td>
										</c:if>
										<c:if test="${userInfoDto.accessLevel=='B' }">
											<td>Settlement</td>
										</c:if>
								</c:if>
<c:if test="${empty userInfoDto.accessLevel }">
<td></td>

										<td></td>
										</c:if>
										<td></td>

									


									</tr>
									
									
								<tr>
								
										<c:if test="${userInfoDto.accessLevel=='B' }">
											<td><label><spring:message
														code="sm.lbl.binFlags" /></label></td>
											<td><select>
  												<c:forEach items="${userInfoDto.bins}" var="item">
    												<option value="${item}">${item}</option>
  												</c:forEach>
											</select>
											</td>
										</c:if>
									
									</tr>
									<tr>
											<td><label><spring:message code="am.lbl.empId" /></label></td>
										<td id="empId">${userInfoDto.empId}</td>
										<td><label><spring:message
													code="am.lbl.salutation" /></label></td>
										<td id="salutation">${userInfoDto.salutation }</td>
										<td><label><spring:message
													code="am.lbl.firstName" /></label></td>
										<td id="firstName">${userInfoDto.firstName }</td>

									</tr>

									<tr>
										<td><label><spring:message
													code="am.lbl.middleName" /></label></td>
										<td id="displayName">${userInfoDto.middleName }</td>
										<td><label><spring:message code="am.lbl.lastName" /></label></td>
										<td id="lastName">${userInfoDto.lastName }</td>
									


									</tr>
									<tr>
										<td><label><spring:message code="msg.lbl.emailId" /></label></td>
										<td id="emailId">${userInfoDto.emailId }</td>
										<td><label><spring:message code="am.lbl.mobileNo" /></label></td>
										<td id="mobileNo">${userInfoDto.mobileNo }</td>
										<td><label><spring:message
													code="msg.lbl.contactNo" /></label></td>
										<td id="contactNo">${userInfoDto.contactNo }</td>


									</tr>

									<%-- <tr>

										<td><label><spring:message code="sm.lbl.bankName" /></label></td>
										<td>${userInfoDto.bankName }</td>
										<c:if test="${not empty userInfoDto.accessLevel }">
										
										<td><label> <spring:message
													code="sm.lbl.accessLevel" /></label></td>
										<c:if test="${userInfoDto.accessLevel=='P' }">
											<td>Participant</td>
										</c:if>
										<c:if test="${userInfoDto.accessLevel=='B' }">
											<td>Settlement</td>
										</c:if>
										<c:if test="${userInfoDto.accessLevel=='B' }">
											<td><label><spring:message
														code="sm.lbl.binFlags" /></label></td>
											<td>${userInfoDto.bins }</td>
										</c:if>
</c:if>
<c:if test="${empty userInfoDto.accessLevel }">
<td></td>

										<td></td>
										</c:if>
										<td></td>

										<td></td>

										<td></td>


									</tr> --%>

									<tr>
										<td><label><spring:message
													code="cmn.lbl.streetAddress" /></label></td>
										<td id="streetAddress">${userInfoDto.streetAddress }</td>
										<td><label><spring:message
													code="cmn.lbl.cityName" /></label></td>
										<td id="city">${userInfoDto.cityName }</td>
										<td><label><spring:message code="msg.lbl.state" /></label></td>
										<td id="state">${userInfoDto.stateName }</td>


									</tr>


									<tr>
										<td><label><spring:message code="am.lbl.pinCode" /></label></td>
										<td id="pinNo">${userInfoDto.pincode}</td>
										<td><label><spring:message
													code="am.lbl.makChkFlag" /></label></td>
										<td id="makChk">${userInfoDto.makChkFlag}</td>
									

<td></td>
										<td></td>
										
										<td></td>
										<td></td>
									</tr>

									<tr>


										<td><label><spring:message code="am.lbl.acStatus" /></label></td>
											<c:if test="${ not empty userInfoDto.status }">
											<c:choose>
												<c:when test="${userInfoDto.lockStatus eq 'L' }">
													<td style="color: red"><spring:message
															code="am.msg.locked" /></td>
												</c:when>
												<c:when test="${userInfoDto.status eq 'A'}">
													<td style="color: green"><spring:message
															code="common.msg.lbl.active" /></td>
												</c:when>
												<c:when test="${userInfoDto.status eq 'S'}">
													<td style="color: red">Suspended</td>
												</c:when>
												<c:when test="${userInfoDto.status eq 'Z'}">
													<td style="color: blue">Reset</td>
												</c:when>
												<c:when test="${userInfoDto.status eq 'R'}">
													<td style="color: blue">Reset</td>
												</c:when>
												<c:when test="${userInfoDto.status eq 'D'}">
													<td style="color: blue">Deleted</td>
												</c:when>
												<c:otherwise>
													<td>Null</td>
												</c:otherwise>
											</c:choose>
										</c:if>
										<td><label><spring:message
													code="am.lbl.lastUpdatedBy" /></label></td>
										<td>${userInfoDto.lastUpdatedBy}</td>
										<td><label><spring:message
													code="am.lbl.lastUpdatedOn" /></label></td>
													
													    <td>${userInfoDto.lastUpdatedOn.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>
									<%-- 	<td>${userInfoDto.lastUpdatedOn}</td> --%>
									</tr>
								


								<c:if test="${fn:length(userroles)>0}">

									<tr>
										<td colspan="6"><div class="panel-heading-red  clearfix">
												<strong><span class="glyphicon glyphicon-info-sign"></span> <span
													data-i18n="Data">Role Information</span></strong>
											</div></td>
									</tr>

									<tr>
										<td colspan="6">
											<table id="tabnew" class="table table-striped table-bordered"
												style="width:100%;">
													<caption style="display:none;">USERS</caption>
												<thead>
													<tr>
														<th scope="col">Role Name</th>
														<th scope="col">Role Description</th>

													</tr>
												</thead>
												<tbody>
													<c:if test="${fn:length(userroles)>0}">
														<c:forEach var="role" items="${userroles}">
															<tr>
																<c:if test="${ role.status ne 'a'}">
																	<td style="color: green">${role.roleName}</td>
																	<td style="color: green">${role.roleDesc}</td>
																</c:if>
																<c:if test="${role.status eq 'a' }">
																	<td>${role.roleName}</td>
																	<td>${role.roleDesc}</td>
																</c:if>

															</tr>
														</c:forEach>
													</c:if>
												</tbody>
											</table>
										</td>
									</tr>


								</c:if>
								<sec:authorize access="hasAuthority('Approve User')">
									<c:if test="${userInfoDto.requestState eq 'P'}">
										<tr>
											<td colspan="6"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"><spring:message
																code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
										</tr>

										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="AM.lbl.reject" /></option>
											</select></td>
											<td><div style="float: right;">
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div></td>
											<!-- //Added by deepak on 31-03-2016 -->
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										</tr>
									</c:if>
								</sec:authorize>

							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve User')">
										<c:if test="${userInfoDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success button"
												id="approveUser" value="Submit"
												onclick="postAction('/approveUserStatus');" />
										</c:if>
									</sec:authorize>
									<button type="button" class="btn btn-danger"
										onclick="backAction('${userInfoDto.userType}','/userPendingForApproval');">Back</button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

