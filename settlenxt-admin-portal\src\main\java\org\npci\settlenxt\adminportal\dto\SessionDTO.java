package org.npci.settlenxt.adminportal.dto;

import org.npci.settlenxt.portal.common.dto.BaseSessionDTO;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Scope(value = "session", proxyMode = ScopedProxyMode.TARGET_CLASS)
@Data
@JGlobalMap
@EqualsAndHashCode(callSuper = false)
@ToString
public class SessionDTO extends BaseSessionDTO {

}
