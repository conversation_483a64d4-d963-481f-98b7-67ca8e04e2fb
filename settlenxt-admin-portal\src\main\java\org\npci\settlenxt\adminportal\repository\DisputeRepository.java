package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.adminportal.dto.DisputeFeeRuleDTO;
import org.npci.settlenxt.adminportal.dto.DisputeTransitionDTO;
import org.npci.settlenxt.portal.common.dto.ActionCodeDTO;

@Mapper
public interface DisputeRepository {

	List<DisputeFeeRuleDTO> getDisputeFeeRuleList();

	DisputeFeeRuleDTO getDisputeFeeRuleInfo(int seqId);

	List<ActionCodeDTO> getActionCodeList();

	List<String> getRelationComplexOpList(String type);

	DisputeFeeRuleDTO fetchSeqId();

	void addDisputeFeeRule(DisputeFeeRuleDTO disputeFeeRuleDto);

	void updateDisputeFeeRuleStg(DisputeFeeRuleDTO disputeFeeRuleDto);

	List<DisputeFeeRuleDTO> getPendingDisputeFeeRuleList();

	DisputeFeeRuleDTO getDisputeFeeRule(int seqId);

	void saveDisputeFeeRule(DisputeFeeRuleDTO disputeFeeRuleDto);

	void updateDisputeFeeRule(DisputeFeeRuleDTO disputeFeeRuleDto);

	void deleteDisputeFeeRule(int seqId);

	List<DisputeFeeRuleDTO> fetchDisputeFeeRuleUsingLogicalFeeCode(String logicalFeeCode);
	
	List<DisputeTransitionDTO> getTransitionRulesList();
	
	List<DisputeTransitionDTO> getPendingTransitionList(String primType, String secType);
	
	DisputeTransitionDTO getTransitionById(int id);
	
	void addTransition(DisputeTransitionDTO disputeDTO);
	
	void editTransition(DisputeTransitionDTO disputeDTO);
	
	List<DisputeTransitionDTO> getTransitionRulesInId(String tranID);
	
	void updateTransitionById(DisputeTransitionDTO disputeDTO);
	
	void addTransitionInMaster(DisputeTransitionDTO disputeDTO);
	
	List<Integer> getSeqID();
	
	void updateTransRule(DisputeTransitionDTO disputeDTO);
	
	void deleteRule(int id);
}
