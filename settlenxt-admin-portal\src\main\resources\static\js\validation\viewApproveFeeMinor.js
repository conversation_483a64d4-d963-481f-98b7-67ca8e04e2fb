$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	}

function userAction(action) {
    let url = action;
    let significance = 'N';
    var data = "significance,"  + significance + ",status,"
            + status;
    postData(url, data);
}

function viewMajorMinorInfo(feeMinorId, requestId,significance) {
	
	let url = '/editFeeMajorMinor';
	var data = "feeConfigId," + feeMinorId + ",significance," + significance + ",requestId," + requestId ;
	postData(url, data);
}


function postAction(action) {

var remarks="";
var url="";
var data="";
var feeMinorId="";

	if(maxLengthTextArea('rejectReason')){
	if ($('#apprej option:selected').val() == "A") {
		if ($("#rejectReason").val() != "") {
			
			 feeMinorId = $("#feeMinorId").val();
			 remarks = $("#rejectReason").val();
	 url = action;
	 data = "status," + "A" + ",remarks,"
					+ remarks+ ",feeMinorId,"
					+ feeMinorId;
			postData(url, data);
		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else if ($('#apprej option:selected').val() == "R") {
		if ($("#rejectReason").val() != "") {
				
				 remarks = $("#rejectReason").val();
				 feeMinorId = $("#feeMinorId").val();
				 url = action;
				 data =  "status," + "R" + ",remarks," + remarks + ",feeMinorId,"
						+ feeMinorId;

				postData(url, data);
			

		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else {
		$(".appRejMust").show();
		$('html, body').animate({ scrollTop: 0 }, 'slow');
		return false;
	}
	}
}