package org.npci.settlenxt.adminportal.controllers;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.ForexRateDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.ForexRateService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.service.BaseLookupServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.fasterxml.jackson.databind.ObjectMapper;

@Controller

public class ForexRateController extends BaseController {

	private static final String A = "A";
	private static final String R = "R";
	private static final String EMPTY = "";
	private static final String SHOW_FOREX_RATE = "showForexRate";
	private static final String FOREX_RATE_VIEW_PAGE = "viewForexRate";
	private static final String VIEW_APPROVE_FOREX_RATE = "viewApproveForexRate";
	private static final String ADD_EDIT_FOREX_RATE = "addEditForexRate";
	private static final String FOREX_RATE_DTO = "forexRateDto";
	private static final String ADD_FLOW = "addFlow";
	private static final String VIEWFLOW = "viewFlow";
	private static final String FOREX_RATE_NETWORK_ID = "ForexRate_NetworkId";

	@Autowired
	private MessageSource messageSource;
	@Autowired
	BaseLookupServiceImpl lookUpService;
	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	private ForexRateService forexRateService;

	// show ForexRate in Main tab
	@PostMapping("/forexRates")
	@PreAuthorize("hasAuthority('View Forex Rates')")
	public String showForexRate(Model model) {
		try {
			ForexRateDTO forexRateDto = new ForexRateDTO();
			fetchDropdownValues(model);
			List<ForexRateDTO> forexRateList = new ArrayList<>();
			model.addAttribute(CommonConstants.SHOW_MAIN_TABS, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.SHOW_FOREX_RATE_LIST, forexRateList);
			model.addAttribute(CommonConstants.ADD_FOREX_RATE, CommonConstants.TRANSACT_YES);
			model.addAttribute(FOREX_RATE_DTO, forexRateDto);
			model.addAttribute("searchFlag", "N");
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, SHOW_FOREX_RATE, ex);
		}

		return getView(model, SHOW_FOREX_RATE);
	}

	@PostMapping("/searchForexRate")
	public String forexRateSearch(String networkId, Date settleDate, Model model) {
		fetchDropdownValues(model);
		ForexRateDTO forexRateDto = new ForexRateDTO();
		forexRateDto.setNetworkId(networkId);
		forexRateDto.setSettleDate(settleDate);
		List<ForexRateDTO> forexRateList = forexRateService.getforexRateList(forexRateDto);
		model.addAttribute(FOREX_RATE_DTO, forexRateDto);
		model.addAttribute("searchFlag", "Y");
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.ADD_FOREX_RATE, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.SHOW_FOREX_RATE_LIST, forexRateList);
		return getView(model, SHOW_FOREX_RATE);
	}

	// show ForexRate in Approval tab
	@PostMapping("/forexRateForApproval")
	@PreAuthorize("hasAuthority('View Forex Rates')")
	public String forexRatePendingForApproval(Model model) {
		try {
			List<ForexRateDTO> pendingForexRateList = forexRateService.getPendingForexRateList();

			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(CommonConstants.SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(CommonConstants.SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TABS, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.FOREX_RATE_APP_PENDING, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.PENDING_FOREX_RATE_LIST, pendingForexRateList);

		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, SHOW_FOREX_RATE, ex);
		}

		return getView(model, SHOW_FOREX_RATE);
	}

	// view ForexRate in Main tab
	@PostMapping("/viewForexRate")
	@PreAuthorize("hasAuthority('View Forex Rates')")
	public String getDisputes(@RequestParam("forexRateId") String forexRateId, Model model) {
		try {
			ForexRateDTO forexRateDto;
			forexRateDto = forexRateService.viewForexRateTab(Integer.valueOf(forexRateId));
			model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDto);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, FOREX_RATE_VIEW_PAGE, ex);
		}
		return getView(model, FOREX_RATE_VIEW_PAGE);
	}

	// view ForexRate in Approval tab
	@PostMapping("/viewPendingForexRate")
	@PreAuthorize("hasAuthority('View Forex Rates')")
	public String getPendingDispute(@RequestParam("forexRateId") String forexRateId, Model model,
			HttpServletRequest request) {
		ForexRateDTO forexRateDto = new ForexRateDTO();
		try {
			forexRateDto = forexRateService.getForexRateStgInfo(forexRateId);
			model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FOREX_RATE, ex);
		}
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.DISCARD_FOREX_RATE);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_FOREX_RATE);
		return getView(model, VIEW_APPROVE_FOREX_RATE);
	}

	// Add ForexRate
	@PostMapping("/forexRateCreation")
	@PreAuthorize("hasAuthority('Add Forex Rates')")
	public String createForexRate(Model model) {
		try {
			fetchDropdownValues(model);
			ForexRateDTO forexRateDto = new ForexRateDTO();
			model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDto);
			model.addAttribute(ADD_FLOW, BaseCommonConstants.YES_FLAG);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, ADD_EDIT_FOREX_RATE, ex);
		}
		return getView(model, ADD_EDIT_FOREX_RATE);
	}

	@PostMapping("/getConversionTypes")
	public ResponseEntity<List<ForexRateDTO>> getConversionTypes(@RequestParam("networkId") String networkId) {
		String networkIdDesc = lookUpService.getLookupDesc(FOREX_RATE_NETWORK_ID, networkId);
		List<ForexRateDTO> forexRateList = new ArrayList<>();
		List<CodeValueDTO> codeValuList = lookUpService.getLookupData("FOREX_RATE_CURR_" + networkIdDesc);
		for (CodeValueDTO codeValueDTO : codeValuList) {
			ForexRateDTO forexRateDTO = new ForexRateDTO();
			forexRateDTO.setNetworkId(networkId);
			forexRateDTO.setCurrencyFrom(codeValueDTO.getCode());
			forexRateDTO.setCurrencyTo(codeValueDTO.getDescription());
			String conversionRate = forexRateService.getConversionRate(networkId, codeValueDTO.getCode(),
					codeValueDTO.getDescription());
			if (StringUtils.isNotEmpty(conversionRate)) {
				forexRateDTO.setRateConversion(Double.parseDouble(conversionRate));
			}
			forexRateList.add(forexRateDTO);
		}
		return new ResponseEntity<>(forexRateList, HttpStatus.OK);

	}

	// To save given input
	@PostMapping("/addForexRate")
	@PreAuthorize("hasAuthority('Add Forex Rates')")
	public String addForexRate(@RequestParam("json") String jsonString, Model model) {

		// Add commas between key-value pairs
		jsonString = jsonString.replace("\"\"", "\",\"");

		// Add commas between JSON objects
		jsonString = jsonString.replaceAll("}\\{", "},{");

		ForexRateDTO forexRateDto = new ForexRateDTO();
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			List<ForexRateDTO> forexRates = objectMapper.readValue(jsonString,
					objectMapper.getTypeFactory().constructCollectionType(List.class, ForexRateDTO.class));
			fetchDropdownValues(model);
			forexRateService.addForexRate(forexRates);
			model.addAttribute("forexRateListForView", forexRates);
			forexRateDto.setNetworkId(forexRates.get(0).getNetworkId());
		} catch (Exception e) {
			model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDto);
			model.addAttribute(VIEWFLOW, BaseCommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_EDIT_FOREX_RATE, e);
		}
		model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDto);

		model.addAttribute(VIEWFLOW, BaseCommonConstants.YES_FLAG);
		model.addAttribute(BaseCommonConstants.SUCCESS_STATUS, getMessageFromBundle("forexRate.addSuccess.msg"));
		return getView(model, ADD_EDIT_FOREX_RATE);
	}


	// For Checker Approval
	@PostMapping("/approveForexRate")
	@PreAuthorize("hasAuthority('Approve Forex Rates')")
	public String approveForexRate(@RequestParam("forexRateId") String forexRateId,
			@RequestParam("status") String status, @RequestParam("remarks") String remarks, Model model) {
		ForexRateDTO forexRateDto = new ForexRateDTO();
		try {
			forexRateDto = forexRateService.approveOrRejectForexRate(Integer.valueOf(forexRateId), status, remarks);
			model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FOREX_RATE, ex);
		}
		model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDto);
		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("forexRate.approvalSuccess.msg"));
		} else if (BaseCommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("forexRate.rejectionSuccess.msg"));
		}
		return getView(model, VIEW_APPROVE_FOREX_RATE);
	}

	// To Edit ForexRate
	@PostMapping("/editForexRate")
	@PreAuthorize("hasAuthority('Edit Forex Rates')")
	public String editForexRate(@RequestParam("forexRateId") String forexRateId,
			@RequestParam("parentPage") String parentPage, Model model) {
		ForexRateDTO forexRateDto = new ForexRateDTO();

		try {
			fetchDropdownValues(model);
			forexRateDto = forexRateService.getForexRateForEdit(Integer.valueOf(forexRateId));
			forexRateDto.setAddEditFlag(CommonConstants.EDIT_FOREX_RATE);
			model.addAttribute(CommonConstants.EDIT_FOREX_RATE, CommonConstants.EDIT_FOREX_RATE);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_FOREX_RATE);
			model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDto);
			model.addAttribute("parentPage", parentPage);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_FOREX_RATE, ex);
		}

		model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDto);
		return getView(model, ADD_EDIT_FOREX_RATE);
	}

	// To save edited ForexRate
	@PostMapping("/updateForexRate")
	@PreAuthorize("hasAuthority('Edit Forex Rates')")
	public String updateForexRate(@ModelAttribute("forexRateDto") ForexRateDTO forexRateDto, BindingResult result,
			@RequestParam("parentPage") String parentPage, Model model, HttpServletRequest request, Locale locale) {
		ForexRateDTO forexRateDtolocal;
		try {
			fetchDropdownValues(model);

			forexRateDto.setAddEditFlag(CommonConstants.EDIT_FOREX_RATE);
			forexRateService.addEditForexRate(forexRateDto);

			forexRateDtolocal = forexRateService.getForexRateStg(forexRateDto.getForexRateId());

			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("forexRate.updateSuccess.msg", null, locale));
		} catch (Exception ex) {
			forexRateDtolocal = forexRateDto;
			handleErrorCodeAndForward(model, ADD_EDIT_FOREX_RATE, ex);
		}
		model.addAttribute(CommonConstants.EDIT_FOREX_RATE, CommonConstants.EDIT_FOREX_RATE);
		model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDtolocal);
		model.addAttribute("parentPage", parentPage);
		return getView(model, ADD_EDIT_FOREX_RATE);
	}

	// For Discard FOrexRate
	@PostMapping("/discardForexRate")
	@PreAuthorize("hasAuthority('Edit Forex Rates')")
	public String discardForexRate(@RequestParam("forexRateId") String forexRateId, Model model) {
		ForexRateDTO forexRateDto = new ForexRateDTO();
		try {
			forexRateDto = forexRateService.discardForexRate(Integer.valueOf(forexRateId));
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_FOREX_RATE, ex);
		}
		model.addAttribute(CommonConstants.FOREX_RATE_DTO, forexRateDto);
		model.addAttribute(BaseCommonConstants.SUCCESS_STATUS, getMessageFromBundle("forexRate.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_FOREX_RATE);
	}

	// For Checker Bulk Approval
	@PostMapping("/approveForexRateForBulk")
	@PreAuthorize("hasAuthority('Approve Forex Rates')")
	public String approveForexRateForBulk(
			@RequestParam("bulkApprovalReferenceNoList") String bulkApprovalReferenceNoList,
			@RequestParam("status") String status, Model model) {
		String successStatus = EMPTY;

		try {
			String remarks = EMPTY;

			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}
			if (!EMPTY.equals(bulkApprovalReferenceNoList)) {
				successStatus = forexRateService.approveOrRejectForexRateBulk(bulkApprovalReferenceNoList, status,
						remarks);
			}
			List<ForexRateDTO> pendingForexRateList = forexRateService.getPendingForexRateList();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(CommonConstants.SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(CommonConstants.SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.PENDING_FOREX_RATE_LIST, pendingForexRateList);
			model.addAttribute(CommonConstants.FOREX_RATE_APP_PENDING, CommonConstants.TRANSACT_YES);
			if (EMPTY.equals(successStatus)) {
				model.addAttribute("errorStatus", "Please select atleast one data to Approve/Reject");
			} else if (successStatus.equalsIgnoreCase(CommonConstants.YES_FLAG) && R.equalsIgnoreCase(status)) {
				model.addAttribute("successStatus", "ForexRate Rejected Successfully");
			} else if (successStatus.equalsIgnoreCase(CommonConstants.YES_FLAG) && A.equalsIgnoreCase(status)) {
				model.addAttribute("successStatus", "ForexRate Approved Successfully");
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_FOREX_RATE, ex);
		}
		return getView(model, SHOW_FOREX_RATE);
	}

	// DropDown
	private void fetchDropdownValues(Model model) {
		List<CodeValueDTO> networkIdList = lookUpService.getLookupData(FOREX_RATE_NETWORK_ID);
		List<CodeValueDTO> currencyFromList = lookUpService.getLookupData("FromForexRate_Currency");
		List<CodeValueDTO> currencyToList = lookUpService.getLookupData("ToForexRate_Currency");
		model.addAttribute("networkIdList", networkIdList);
		model.addAttribute("currencyFromList", currencyFromList);
		model.addAttribute("currencyToList", currencyToList);
	}

}
