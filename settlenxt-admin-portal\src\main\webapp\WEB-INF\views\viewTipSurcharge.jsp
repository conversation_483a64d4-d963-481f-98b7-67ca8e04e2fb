<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/viewTipSurcharge.js"
	type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">
		 <form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewTipSurcharge" modelAttribute="tipSurchargeDto"
			action="${approveTipSurchargeStatus}" autocomplete="off"> 
			
			<input type="hidden" id="tipSurchargeId" value="${tipSurchargeDto.tipSurchargeId}" />
			
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="tipSurcharge.viewscreen.title" /></span></strong>
						</div>
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px">
							 <caption style="display:none;">Tip Surcharge</caption>
							 <thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
										<td></td>
										<td><label><spring:message code="tipSurcharge.tipSurchargeType" /></label></td>
										<td id="tipSurchargeType">${tipSurchargeDto.tipSurType }</td>
										<td><label><spring:message code="tipSurcharge.tipSurchargeName" /></label></td>
										<td id="tipSurchargeName">${tipSurchargeDto.tipSurchargeName }</td>
										<td></td>
									</tr>
									<tr>
										<td></td>
										<td><label><spring:message code="tipSurcharge.operator" /></label></td>
										<td id="operator">${tipSurchargeDto.operatorName }</td>
										<td><label><spring:message code="tipSurcharge.settlementAmount" /></label></td>
										<td id="settlementAmount">${tipSurchargeDto.settlementAmount }</td>
										<td></td>
									</tr>
									<tr>
										<td></td>
										<td><label><spring:message code="tipSurcharge.amountPercentFlag" /></label></td>
										<td id="amountPercentFlag">${tipSurchargeDto.amtFlag }</td>
										<c:if test="${tipSurchargeDto.amtFlag  eq 'Percent'}">
										<td><label><spring:message code="tipSurcharge.percentage" /></label></td>
										<td >${tipSurchargeDto.percentage }</td>
										</c:if>
										<c:if test="${tipSurchargeDto.amtFlag  eq 'Amount'}">
										<td><label><spring:message code="tipSurcharge.amount" /></label></td>
										<td >${tipSurchargeDto.amount }</td>
										</c:if>
										<td></td>
									</tr>
									<tr>
										<td></td>
										<td><label><spring:message code="tipSurcharge.binCardBrand" /></label></td>
										<td id="binCardBrand">${tipSurchargeDto.cardBrand }</td>
										<td><label><spring:message code="tipSurcharge.binCardType" /></label></td>
										<td id="binCardType">${tipSurchargeDto.cardType }</td>
										<td></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
					</div>	
		</form:form>
		<div class="row">
			<div class="col-sm-12 bottom_space ">
				<hr />
				<div style="text-align:center">
					<button type="button" class="btn btn-danger"
						onclick="userAction('N','/tipSurchargeMain');">
						<spring:message code="tipSurcharge.backBtn" /></button>
					<c:if test="${tipSurchargeDto.requestState =='A' }">
					<sec:authorize access="hasAuthority('Edit Tip Surcharge')">
						<input name="editButton" type="button" class="btn btn-success"
						 id="approveRole" value="Edit" 
						onclick="EditTipSurcharge('/editTipSurcharge','${tipSurchargeDto.tipSurchargeId}','mainPage');"/>
					</sec:authorize>
					</c:if>

				</div>
			</div>
		</div>
	</div>

</div>
