function issuerBinTypeChange()
{
if ($('#issBinType').val() == 'T') {
			$('#issSettlementBin').hide();
			$('#settlementBinDiv').hide();
		}
		else {
			$('#issSettlementBin').show();
			$('#settlementBinDiv').show();
		}
}

function bindIssuerBinOnloadFunctions() {
	$('#issBinType').change(function () {
		if ($('#issBinType').val() == '0') {
			$('#errissBinType').find('.error').html('Please select Bin Type');
			$('#errissBinType').show();

		} else {
			$('#errissBinType').hide();
		}
		if ($('#issBinType').val() == 'T') {
			$('#issSettlementBin').hide();
			$('#settlementBinDiv').hide();
		}
		else {
			$('#issSettlementBin').show();
			$('#settlementBinDiv').show();
		}
	})
	
	$('#binNumber').on('keyup keypress change',function () {
		if($('#binLength').val()==$('#binNumber').val().length) {
			validateAndPopulateBinData();
			setHighAndLowBinData();
		} else {
			$('#errbinNumber').find('.error').html('Please select Bin Number of Selected Bin Length');
			if(document.getElementById("networkUsed").value=='Y'){
				validateAndPopulateBinData();
			}
			return;
		}
	});
	
	$('#binLength').change(function () {
		if ($('#binLength').val() == '0') {
			$('#errbinLength').find('.error').html('Please select Bin Number Length');
			$('#errbinLength').show();
			$('#binNumber').prop('disabled', true);
			$('#binNumber').val('');

		} else if($('#binLength').val()!=$('#binNumber').val().length && document.getElementById('binNumber').value != '') {
			$('#binNumber').prop('disabled', false);
			$('#errbinNumber').find('.error').html('Please select Bin Number of Selected Bin Length');
			$('#errbinNumber').show();
		} else {
			$('#errbinLength').hide();
			$('#errbinNumber').hide();
			$('#binNumber').prop('disabled', false);
		}
	  });
		if(document.getElementById("networkUsed").value!='Y'){
				$('#binNumber').autocomplete({
               source:unallocatedIssBins,
               autoFocus:true,
               select: setBinData             
            });  
			}
			
	

	
	
	
	$('#binCardType').change(function () {
		$("#featureMultiple").empty();
		$("#featureMultiple").multiselect("rebuild");
		$("#featureMultiple").multiselect("refresh");
		getFeatureList("");
	})

	$('#binCardVariant').change(function () {
		$("#featureMultiple").empty();
		$("#featureMultiple").multiselect("rebuild");
		$("#featureMultiple").multiselect("refresh");
		getFeatureList("");

	})
	
	$('#featureMultiple').multiselect({
		buttonWidth: '200px',
		nonSelectedText: 'Select Features',
		includeSelectAllOption: true,
		
	});
	$('#additionalParams').multiselect({
		buttonWidth: '200px',
		nonSelectedText: 'Select Additional Params',
		maxHeight:300,
		includeSelectAllOption: true,
		onChange: function (options, selected) {
			if (selected) {
				$('#erradditionalParams').hide();
				$('#erradditionalParams').find('.error').html('');
			} else {
				$('#erradditionalParams').show();
				$('#erradditionalParams').find('.error').html(validationMessages['additionalParams']);
			}
		},
	});
				
		$('#networkSelection').multiselect({
		buttonWidth: '200px',
		nonSelectedText: 'Select network Params',
		maxHeight:300,
		includeSelectAllOption: true,
		onChange: function (options, selected) {
			if (selected) {
				$('#errnetworkSelection').hide();
				$('#errnetworkSelection').find('.error').html('');
			} else {
				$('#errnetworkSelection').show();
				$('#errnetworkSelection').find('.error').html(validationMessages['additionalParams']);
			}
		},
	});			
				
	$('#saveIssuerBin')
		.click(
			function () {

				var check = false;


				if ($('#issBankGroup').val() == '0') {
					$('#errissBankGroup').find('.error').html('Please select Bank Group');
					$('#errissBankGroup').show();
					check = true;
				} else {
					$('#errissBankGroup').hide();
				}
				
				if ($('#binLength').val() == '0') {
					$('#errbinLength').find('.error').html('Please select Bin Number Length');
					$('#errbinLength').show();
					check = true;
				} else {
					$('#errbinLength').hide();
				}
				if (!validateFromCommonVal('binNumber',
						true, "NumericsOnly", $('#binLength').val(), false)) {
						check = true;
					}
				if($('#binLength').val()!=$('#binNumber').val().length) {
					$('#errbinNumber').find('.error').html('Please select Bin Number of Select Bin Length');
					$('#errbinNumber').show();
					check = true;
				} else {
					$('#errbinNumber').hide();
				}
				
			if(document.getElementById("networkUsed").value=='Y'&&!validateFromCommonVal('markUp', true, "ZeroToHundredWithTwoDecimal", "", false)) {
					check = true;
				}
				
					if(document.getElementById("networkUsed").value=='Y'&&!validateFromCommonVal('networkLicenseId', false, "NumericsOnly", "6", true)) {
					check = true;
				}
				
					if(document.getElementById("networkUsed").value=='Y'&&!validateFromCommonVal('networkIssId', false, "NumericsOnly", "6", true)) {
					check = true;
				}
				check = handleBinData(check);

				if ($('#issDomainUsage').val() == '0') {
					$('#errissDomainUsage').find('.error').html('Please select Domain Usage');
					$('#errissDomainUsage').show();
					check = true;
				} else {
					$('#errissDomainUsage').hide();
				}

				if ($('#messageType').val() == '0') {
					$('#errmessageType').find('.error').html('Please select Message Type');
					$('#errmessageType').show();
					check = true;
				} else {
					$('#errmessageType').hide();
				}
				if ($('#cardTechnology').val() == '0') {
					$('#errcardTechnology').find('.error').html('Please select Card Technology');
					$('#errcardTechnology').show();
					check = true;
				} else {
					$('#errcardTechnology').hide();
				}
				check = handleSaveMember(check);

				if ($('#formFactor').val() == '0'&&document.getElementById("networkUsed").value!='Y') {

					$("#errformFactor").find('.error').html("Please select Form Factor");

					$('#errformFactor').show();
					check = true;
				} else {
					$('#errformFactor').hide();
				}


				check = handleCheck(check);

				if (!check) {
					saveIssuerBin();
				}
				else {
					return false;
				}

			});
	$('#clearIssuerBin').click(function () {
		clearIssueBinData();
	});

}
function handleSaveMember(check) {
	if ($('#authMechanism').val() == '0') {
		$('#errauthMechanism').find('.error').html('Please select Authentication Mechanism');
		$('#errauthMechanism').show();
		check = true;
	} else {
		$('#errauthMechanism').hide();
	}


	if ($('#issProductType').val() == '') {
		$('#errissProductType').find('.error').html('Please select Product Type');
		$('#errissProductType').show();
		check = true;
	} else {
		$('#errissProductType').hide();
	}

	if ($('#subScheme').val() == '0') {
		$('#errsubScheme').find('.error').html('Please select Sub-Scheme');
		$('#errsubScheme').show();
		check = true;
	} else {
		$('#errsubScheme').hide();
	}
	if ($('#issBinType').val() == 'I') {
		if (($('#issSettlementBin').val() == '0') || ($('#issSettlementBin').val() == '') || ($('#issSettlementBin').val() == null)) {
			$('#errissSettlementBin').find('.error').html('Please select Settlement Bin');
			$('#errissSettlementBin').show();
			check = true;
		} else {
			$('#errissSettlementBin').hide();
		}
	}
	if(document.getElementById("networkUsed").value!='Y'){
	if ($('#cardSubVariant').val() == '0') {
		$('#errcardSubVariant').find('.error').html('Please select Card Sub-Variant');
		$('#errcardSubVariant').show();
		check = true;
	} else {
		$('#errcardSubVariant').hide();
	}

	if ($('#programDetails').val() == '0') {
		$("#errprogramDetails").find('.error').html("Please select Program Details");

		$('#errprogramDetails').show();
		check = true;
	} else {
		$('#errprogramDetails').hide();
	}}
	return check;
}

function handleCheck(check) {
	if(document.getElementById("networkUsed").value=='Y'){
	if ($('#networkSelection').val().length == 0) {
		check = true;
		$('#errnetworkSelection').show();
		$('#errnetworkSelection').find('.error').html(validationMessages['networkSelection']);					
	}else{
		$('#errnetworkSelection').hide();
		$('#errnetworkSelection').find('.error').html('');
	}}
	if ($('#additionalParams').val().length == 0) {
		check = true;
		$('#erradditionalParams').show();
		$('#erradditionalParams').find('.error').html(validationMessages['additionalParams']);					
	}else{
		$('#erradditionalParams').hide();
		$('#erradditionalParams').find('.error').html('');
	}
	
if (!validateFromCommonVal('lowBin',
true, "NumericsOnly", 9, true)) {
check = true;
}
if (!validateFromCommonVal('highBin',
true, "NumericsOnly", 9, true)) {
check = true;
}
if (!validateFromCommonVal('issFrmDate',
true, "DateFormat", 10, false)) {
check = true;
}
if (!validateFromCommonVal('issToDate',
true, "DateFormat", 10, false)) {
check = true;
}
if (!validateFromCommonVal('panLength',
true, "NumericBtwn8to20", 20, false)) {
check = true;
}
	return check;
}

function handleBinData(check) {
	if ($('#issBinType').val() == '0') {
		$('#errissBinType').find('.error').html('Please select Bank Group');
		$('#errissBinType').show();
		check = true;
	} else {
		$('#errissBinType').hide();
	}

	if ($('#issBinType').val() == 'I') {
		if ($('#issSettlementBin').val() == '0') {
			check = true;
			$('#errissSettlementBin').find('.error').html(
				'Please select Settlement Bin');
			$('#errissSettlementBin').show();

		}
	} else {
		$('#errissSettlementBin').hide();
		$('#issSettlementBin').val('');
	}
	if ($('#binCardType').val() == '0') {
		$('#errbinCardType').find('.error').html('Please select BIN Card Type');
		$('#errbinCardType').show();
		check = true;
	} else {
		$('#errbinCardType').hide();
	}

	if ($('#binProductType').val() == '0') {
		$('#errbinProductType').find('.error').html('Please select BIN Product Type');
		$('#errbinProductType').show();
		check = true;
	} else {
		$('#errbinProductType').hide();
	}



	if ($('#binCardVariant').val() == '0') {
		$('#errbinCardVariant').find('.error').html('Please select BIN Card Variant');
		$('#errbinCardVariant').show();
		check = true;
	} else {
		$('#errbinCardVariant').hide();
	}

	if ($('#binCardBrand').val() == '0') {
		$('#errbinCardBrand').find('.error').html('Please select BIN Card Brand');
		$('#errbinCardBrand').show();
		check = true;
	} else {
		$('#errbinCardBrand').hide();
	}
	return check;
}

function setBinData(_event, ui){
 $("#binNumber").val(ui.item.value);
  validateAndPopulateBinData();
  setHighAndLowBinData();
}
function validateAndPopulateBinData(){
    
    if($('#binLength').val()!= $('#binNumber').val().length){
    $('#errbinNumber').find('.error').html('Please select Bin Number of Selected Bin Length');
    $('#errbinNumber').show();
    }else if(validateFromCommonVal('binNumber', true, 'NumericsOnly', $('#binLength').val(), true)){
   $('#errbinNumber').hide();
   var binNumber=$("#binNumber").val();   
   if((unallocatedIssBins.length==0 || !unallocatedIssBins.includes(binNumber))&&document.getElementById("networkUsed").value!='Y' ){
    $('#errbinNumber').find('.error').html('The entered bin is not available');
	$('#errbinNumber').show();
   }	
 }
}
function clearIssueBinData(){
		

		$('#issBankGroup').data('selectize').setValue(0);
		$('#issBinType').attr('disabled', false);
		$('#issBinType').val(0);
		$('#binNumber').val('');
		$('#binLength').attr('disabled', false);
		$('#oldBinNumber').val('');
		$('#lowBin').val('*********');
		$('#highBin').val('*********');
		$('#binNumber').attr('disabled', true);
		$('#binLength').val('0');
		$('#lowBin').attr('disabled', false);
		$('#highBin').attr('disabled', false);
		$('#panLength').val('');
		$('#binCardType').val(0);
		$('#binProductType').val(0);
		$('#binCardVariant').val(0);
		$('#binCardBrand').val(0);
		$('#issDomainUsage').val(0);
		$('#messageType').val(0);
		$('#cardTechnology').val(0);
		$('#authMechanism').val(0);
		$('#subScheme').val(0);
		$('#cardSubVariant').val(0);
		$('#programDetails').val(0);
		$('#formFactor').val(0);
		$('#markUp').val("");
		$('#networkLicenseId').val("");
		$('#networkIssId').val("");
		
		$('#issSettlementBin').val(0);
		$('#issFrmDate').val('');
		$("#issToDate").val('31-12-2099');
		$('#feturesIssuerbin').val('');
		$('#oldOrNewBinFlag').val('NewBin');
		$('#featureMultiple').empty();
		
		$("#featureMultiple").multiselect("rebuild");
		$("#featureMultiple").multiselect("refresh");
		$('#additionalParams').multiselect('deselectAll', false);
	    $('#additionalParams').multiselect('refresh');
	    		$('#networkSelection').multiselect('deselectAll', false);
	$('#networkSelection').multiselect("refresh");
		$('#issOfflineAllowed').val('N');

		$('#errmessageType').hide();
		$('#errissBankGroup').hide();
		$('#errissBinType').hide();
		$('#errbinNumber').hide();
		$('#errbinLength').hide();
		$('#errlowBin').hide();
		$('#errhighBin').hide();
		$('#errpanLength').hide();
		$('#errbinCardType').hide();
		$('#errbinProductType').hide();
		$('#errbinCardVariant').hide();
		$('#errbinCardBrand').hide();
		$('#errissDomainUsage').hide();
		$('#errcardTechnology').hide();
		$('#errauthMechanism').hide();
		$('#errsubScheme').hide();
		$('#errcardSubVariant').hide();
		$('#errprogramDetails').hide();
		$('#errformFactor').hide();
		
		$('#errissSettlementBin').hide();
		$('#errissFrmDate').hide();
		$("#errissToDate").hide();
		$('#errissOfflineAllowed').hide();
		$('#erradditionalParams').hide();

}
function editIssuerBin(binNumber,domesticFlag) {
	clearIssueBinData();
	var issuerDataIndex = issuerBinData.findIndex(obj => obj.binNumber == binNumber);
	var issuerModel = issuerBinData[issuerDataIndex];
	if (issuerModel) {
		 binNumber = issuerModel.binNumber;
		$('#oldBinNumber').val(binNumber);
		$('#issBinType').val(issuerModel.issBinType);
		$('#issBankGroup').data('selectize').setValue(issuerModel.issBankGroup);
		$('#binLength').val(issuerModel.binLength);
		$('#lowBin').val(issuerModel.lowBin);
		$('#highBin').val(issuerModel.highBin);
		$('#panLength').val(issuerModel.panLength)
		$('#binCardType').val(issuerModel.binCardType);
		$('#binProductType').val(issuerModel.binProductType);
		$('#binCardBrand').val(issuerModel.binCardBrand);
		$('#binCardVariant').val(issuerModel.binCardVariant);
		$('#issDomainUsage').val(issuerModel.issDomainUsage);
		$('#subScheme').val(issuerModel.subScheme);
		$('#messageType').val(issuerModel.messageType)
		$('#cardTechnology').val(issuerModel.cardTechnology);
		$('#authMechanism').val(issuerModel.authMechanism);
		$('#cardSubVariant').val(issuerModel.cardSubVariant);
		$('#programDetails').val(issuerModel.programDetails);
		$('#formFactor').val(issuerModel.formFactor);
		$('#issProductType').val(issuerModel.issProductType);
		$('#issSettlementBin').val(issuerModel.issSettlementBin);
		$('#issFrmDate').val(issuerModel.issFrmDateStr);
		$('#issToDate').val(issuerModel.issToDateStr);
		$("#issOfflineAllowed").val(issuerModel.offlineAllowed);
		$('#binNumber').val(binNumber);
		$('#markUp').val(issuerModel.markUp);
		$('#networkLicenseId').val(issuerModel.networkLicenseId);
		$('#networkIssId').val(issuerModel.networkIssId);
		$('#networkSelection').multiselect('select', issuerModel.networkSelection);
		if (!issuerModel.isNew) {
		$('#binNumber').prop("disabled", true);
		$('#binLength').prop("disabled", true);
		let addtionalParamsArr = [];
		let str = issuerModel.additionalParams;
		for (let i = 0; i < str.length; i++) {
		    if (str.charAt(i) == '1') {
		    	addtionalParamsArr.push(i + 1);
		    }
		}
		
		$('#additionalParams').multiselect('select', addtionalParamsArr);
	
		$('#additionalParams').multiselect('refresh');
		}
		
		getFeatureList(issuerModel.featureIssBin);
issuerBinTypeChange();
	}
	if(document.getElementById("networkUsed").value=='Y'){
	if(domesticFlag&&domesticFlag=='Y'){
	$('#panLength, #binCardType,#binProductType,#binCardVariant,#binCardBrand,#issDomainUsage,#messageType,#cardTechnology,#authMechanism').prop("disabled", true);
	}
	else{
	$('#panLength, #binCardType,#binProductType,#binCardVariant,#binCardBrand,#issDomainUsage,#messageType,#cardTechnology,#authMechanism').prop("disabled", false);
	}
	}
}

function viewIssuerBin(binNumber) {
	var issuerDataIndex = issuerBinData.findIndex(obj => obj.binNumber == binNumber);
	 binNumber = $('#binNumber').val();
	var issuerModel = issuerBinData[issuerDataIndex];
	if (issuerModel) {
		 binNumber = issuerModel.binNumber;
		$('#issBinTypeTD').html(issuerModel.issBinTypeName);
		$('#issBankGroupTD').html(issuerModel.bankGroupName);
		$('#binLengthTD').html(issuerModel.binLength);
		$('#additionalParamsTD').html(issuerModel.additionalParams);
		$('#lowBinTD').html(issuerModel.lowBin);
		$('#highBinTD').html(issuerModel.highBin);
		$('#panLengthTD').html(issuerModel.panLength)
		$('#binCardTypeTD').html(issuerModel.binCardTypeName);
		$('#binProductTypeTD').html(issuerModel.binProductTypeName);
		$('#binCardBrandTD').html(issuerModel.binCardBrandName);
		$('#binCardVariantTD').html(issuerModel.binCardVariantName);
		$('#issDomainUsageTD').html(issuerModel.domainUsageName);
		$('#subSchemeTD').html(issuerModel.subSchemeName);
		$('#messageTypeTD').html(issuerModel.messageTypeName)
		$('#cardTechnologyTD').html(issuerModel.cardTechnologyName);
		$('#authMechanismTD').html(issuerModel.authMechanismName);
		$('#cardSubVariantTD').html(issuerModel.cardSubVariantName);
		$('#programDetailsTD').html(issuerModel.programDetailsName);
		$('#formFactorTD').html(issuerModel.formFactorName);
		$('#issProductTypeTD').html(issuerModel.productTypeName);
		$('#issSettlementBinTD').html(issuerModel.issSettlementBin)
		$('#issFrmDateTD').html(issuerModel.issFrmDateStr);
		$('#issToDateTD').html(issuerModel.issToDateStr);
		$('#featureIssBinTD').html(issuerModel.featureIssBin);
		$('#isIssOfflineAllowedTD').html(issuerModel.offlineAllowed=='Y'?'Yes':'No');
		$('#binNumberTD').html(binNumber);
		$('#markUp').html(issuerModel.markUp);
		$('#networkLicenseId').html(issuerModel.networkLicenseId);
		$('#networkIssId').html(issuerModel.networkIssId);
		$('#networkSelectionTD').html(issuerModel.networkSelection);

	}
}

function blockIssuerBin(binNumber) {
openConfirmDialog('Do you want to block the selected issuer bin?', blockIssuerBinAction, binNumber);
}
function blockIssuerBinAction(binNumber) {

	var issuerDataIndex = issuerBinData.findIndex(obj => obj.binNumber == binNumber);
	var issuerModel = issuerBinData[issuerDataIndex];
	if (issuerModel) {
		issuerModel.status = 'B';
		issuerModel.actionType = 'Edit';
	}
	renderIssuerBinTable();
	unableSave();
}
function unblockIssuerBin(binNumber) {
openConfirmDialog('Do you want to unblock the selected issuer bin?', unblockIssuerBinAction, binNumber);
}
function unblockIssuerBinAction(binNumber) {
	var issuerDataIndex = issuerBinData.findIndex(obj => obj.binNumber == binNumber);
	var issuerModel = issuerBinData[issuerDataIndex];
	if (issuerModel) {
		issuerModel.status = 'A';
		issuerModel.actionType = 'Edit';
	}
	renderIssuerBinTable();
	unableSave();
}
function deleteIssuerBin(binNumber) {
openConfirmDialog('Do you want to delete the selected issuer bin?', deleteIssuerBinAction, binNumber);
}
function deleteIssuerBinAction(binNumber) {
	var issuerDataIndex = issuerBinData.findIndex(obj => obj.binNumber == binNumber);
	var issuerModel = issuerBinData[issuerDataIndex];
	if (issuerModel) {
		if (issuerModel.isNew) {
			issuerBinData = issuerBinData.filter(obj => obj.binNumber != binNumber);
		} else {
			issuerModel.status = 'D';
			issuerModel.actionType = 'Edit';
		}
	}
	clearIssueBinData();
	renderIssuerBinTable();
	unableSave();
}
function resetIssuerBinData() {
	issuerBinData.forEach(function (issuerModel) { issuerModel.isNew = false; });

	renderIssuerBinTable();
}

function saveIssuerBin() {
	
	var additioanlParam = $('#additionalParams').val();
	var additionalParamValue = "***************************000" ;
	let outputArr = Array.from(additionalParamValue);
	additioanlParam.forEach(index => {
		  outputArr[index-1] = '1';
		});

		// Convert the output array back to a string
	let additioanlParamFinalValue = outputArr.join('');

	var binNumber = $('#binNumber').val();
	
	var issuerModel = {};
    var oldBinNumber=$('#oldBinNumber').val();

			var vissBinType = $('#issBinType').val();
			var vissBankGroup = $('#issBankGroup').val();
			var vlowBin = $('#lowBin').val();
			var vhighBin = $('#highBin').val();
			var vpanLength = $('#panLength').val();
			var vbinCardType = $('#binCardType').val();
			var vbinProductType = $('#binProductType').val();
			var vbinCardBrand = $('#binCardBrand').val();
			var vbinCardVariant = $('#binCardVariant').val();
			var vissDomainUsage = $('#issDomainUsage').val();
			var vsubScheme = $('#subScheme').val();
			var vmessageType = $('#messageType').val();
			var vcardTechnology = $('#cardTechnology').val();
			var vauthMechanism = $('#authMechanism').val();
			var vcardSubVariant = $('#cardSubVariant').val();
			var vprogramDetails = $('#programDetails').val();
			var vformFactor = $('#formFactor').val();
			var vissProductType = $('#issProductType').val();
			var vissSettlementBin = $('#issSettlementBin').val();
			var vissFrmDateStr = $('#issFrmDate').val();
			var vissToDateStr = $('#issToDate').val();
			var vfeatureIssBin = $('#featureMultiple option:selected').toArray().map(item => item.value).join();
			var additionalParams = additioanlParamFinalValue;
			var vofflineAllowed = $("#issOfflineAllowed").val();
			var networkSel=$("#networkSelection").val();
			


	oldBinNumber = setOldBinNumber(oldBinNumber, binNumber);
	var issuerDataIndex = issuerBinData.findIndex(obj => obj.binNumber == oldBinNumber);
	issuerDataIndex = setIssuerDataIndex(issuerDataIndex, oldBinNumber);
	if (issuerDataIndex < 0) {
		issuerModel = {};
		issuerModel.binNumber = binNumber;
		
		issuerModel.issBinType = $('#issBinType').val();
		setIssuerBinTypeName(issuerModel);
		if(!unallocatedIssBins.includes(binNumber)&&document.getElementById("networkUsed").value!='Y') {
		$('#errbinNumber').find('.error').html('The entered bin is not available');
		$('#errbinNumber').show();
		return FALSE;
	   } 
		issuerModel.issBankGroup = $('#issBankGroup').val();
		issuerModel.binLength = $('#binLength').val();
		issuerModel.lowBin = $('#lowBin').val();
		issuerModel.highBin = $('#highBin').val();
		issuerModel.panLength = $('#panLength').val();
		issuerModel.binCardType = $('#binCardType').val();
		issuerModel.binProductType = $('#binProductType').val();
		issuerModel.binCardBrand = $('#binCardBrand').val();
		issuerModel.binCardVariant = $('#binCardVariant').val();
		issuerModel.issDomainUsage = $('#issDomainUsage').val();
		issuerModel.messageType = $('#messageType').val();
		issuerModel.cardTechnology = $('#cardTechnology').val();
		issuerModel.authMechanism = $('#authMechanism').val();
		issuerModel.cardSubVariant = $('#cardSubVariant').val();
		issuerModel.programDetails = $('#programDetails').val();
		issuerModel.subScheme = $('#subScheme').val();
		issuerModel.formFactor = $('#formFactor').val();
		issuerModel.issProductType = $('#issProductType').val();
		issuerModel.issSettlementBin = $('#issSettlementBin').val();
		issuerModel.issFrmDateStr = $('#issFrmDate').val();
		issuerModel.issToDateStr = $('#issToDate').val();
		issuerModel.featureIssBin = $('#featureMultiple option:selected').toArray().map(item => item.value).join();
		issuerModel.additionalParams = additioanlParamFinalValue;
		issuerModel.offlineAllowed = $("#issOfflineAllowed").val();
		issuerModel.status = 'A';
		issuerModel.isNew = true;
		issuerModel.networkSelection=networkSel
		issuerModel.markUp= $('#markUp').val();
		issuerModel.networkLicenseId= $('#networkLicenseId').val();
		issuerModel.networkIssId= $('#networkIssId').val();
		issuerModel.bankGroup=$('#bankGroup').val();
		issuerModel.actionType = 'Add';
		issuerModel.new
		issuerBinData.push(issuerModel);
		
	}
	else {
	
	if($('#oldBinNumber').val()!=binNumber){
	
	   		var otherIssuerDataIndex = issuerBinData.findIndex(obj => obj.binNumber== binNumber);
	   		var otherAcquirDataIndex = acquirerBinData.findIndex(obj => obj.acquirerId == binNumber);

		    if(otherIssuerDataIndex!=-1 || otherAcquirDataIndex !=-1){
		    $("#errbinNumber").find('.error').html("Issuer Bin Number is existing already");
			$('#errbinNumber').show();
			return false;
			}
			 if(!unallocatedIssBins.includes(binNumber)&&document.getElementById("networkUsed").value!='Y' ){
				$('#errbinNumber').find('.error').html('The entered bin is not available');
				$('#errbinNumber').show();
				return false;
			} 
		}
		
		issuerModel = issuerBinData[issuerDataIndex];
		if((issuerModel.binNumber != binNumber) || (issuerModel.binLength != binLength) || (issuerModel.additionalParams != additionalParams) || (issuerModel.issBinType != vissBinType) || (issuerModel.issBankGroup != vissBankGroup) || (issuerModel.lowBin != vlowBin) || (issuerModel.highBin != vhighBin) || (issuerModel.panLength != vpanLength) || (issuerModel.binCardType != vbinCardType) || (issuerModel.binProductType != vbinProductType) || (issuerModel.binCardBrand != vbinCardBrand) || (issuerModel.binCardVariant != vbinCardVariant) || (issuerModel.issDomainUsage != vissDomainUsage) || (issuerModel.subScheme != vsubScheme) || (issuerModel.messageType != vmessageType) || (issuerModel.cardTechnology != vcardTechnology) || (issuerModel.authMechanism != vauthMechanism) || (issuerModel.cardSubVariant != vcardSubVariant) || (issuerModel.programDetails != vprogramDetails) || (issuerModel.formFactor != vformFactor) || (issuerModel.issProductType != vissProductType) || (issuerModel.issSettlementBin != vissSettlementBin) || (issuerModel.issFrmDateStr != vissFrmDateStr) || (issuerModel.issToDateStr != vissToDateStr) || (issuerModel.featureIssBin != vfeatureIssBin ) || (issuerModel.offlineAllowed != vofflineAllowed)  )
		{ 
			issuerModel.binNumber=binNumber;
			issuerModel.issBinType = $('#issBinType').val();
			setIssuerBinTypeName(issuerModel);
			issuerModel.issBankGroup = $('#issBankGroup').val();
			
			issuerModel.binLength = $('#binLength').val();
			issuerModel.lowBin = $('#lowBin').val();
			issuerModel.highBin = $('#highBin').val();
			issuerModel.panLength = $('#panLength').val();
			issuerModel.binCardType = $('#binCardType').val();
			issuerModel.binProductType = $('#binProductType').val();
			issuerModel.binCardBrand = $('#binCardBrand').val();
			issuerModel.binCardVariant = $('#binCardVariant').val();
			issuerModel.issDomainUsage = $('#issDomainUsage').val();
			issuerModel.subScheme = $('#subScheme').val();
			issuerModel.messageType = $('#messageType').val();
			issuerModel.cardTechnology = $('#cardTechnology').val();
			issuerModel.authMechanism = $('#authMechanism').val();
			issuerModel.cardSubVariant = $('#cardSubVariant').val();
			issuerModel.programDetails = $('#programDetails').val();
			issuerModel.formFactor = $('#formFactor').val();
			issuerModel.issProductType = $('#issProductType').val();
			issuerModel.issSettlementBin = $('#issSettlementBin').val();
			issuerModel.issFrmDateStr = $('#issFrmDate').val();
			issuerModel.issToDateStr = $('#issToDate').val();
			issuerModel.featureIssBin = $('#featureMultiple option:selected').toArray().map(item => item.value).join();
			issuerModel.additionalParams = additioanlParamFinalValue;			
			issuerModel.offlineAllowed = $("#issOfflineAllowed").val();
			issuerModel.actionType = 'Edit';
			issuerModel.networkSelection=networkSel;
		issuerModel.markUp= $('#markUp').val();
		issuerModel.networkLicenseId= $('#networkLicenseId').val();
		issuerModel.networkIssId= $('#networkIssId').val();
	issuerModel.bankGroup= $('#issBankGroup').val();
		
		}	
	}



	$('#issBinType').val(0);
	$('#issBankGroup').data('selectize').setValue(0);
	$('#binNumber').val('');
	$('#lowBin').val('*********');
	$('#highBin').val('*********');
	$('#panLength').val('');
	$('#binCardType').val(0);
	$('#binProductType').val(0);
	$('#binCardVariant').val(0);
	$('#binCardBrand').val(0);
	$('#issDomainUsage').val(0);
	$('#messageType').val(0);
	$('#cardTechnology').val(0);
	$('#authMechanism').val(0);
	$('#subScheme').val(0);
	$('#cardSubVariant').val(0);
	$('#programDetails').val(0);
	$('#formFactor').val(0);
	$('#issProductType').val(0);
	$('#issSettlementBin').val(0);
	$('#issFrmDate').val('');
	$('#lowBin').val('*********');
	$('#highBin').val('*********');
	$('#issToDate').val('31-12-2099');
	$('#featureIssBin').val('');
	$('#featureMultiple').empty();
	$('#featureMultiple').multiselect("rebuild");
	$('#featureMultiple').multiselect("refresh");
	$('#additionalParams').multiselect("rebuild");
	$('#additionalParams').multiselect("refresh");
		$('#networkSelection').multiselect("rebuild");
	$('#networkSelection').multiselect("refresh");
	$('#markUp').val("");
	$('#networkLicenseId').val("");
	$('#networkIssId').val("");
	$('#binNumber').attr('disabled', false);
	$('#issOfflineAllowed').val('N');
	$("#errbinNumber").hide();
	$("#errbinNumber").find('.error').html('');
	clearIssueBinData();
	renderIssuerBinTable();
	unableSave();
}
function setOldBinNumber(oldBinNumber, binNumber) {
	if (oldBinNumber == '') {
		oldBinNumber = binNumber;
	}
	return oldBinNumber;
}

function setIssuerBinTypeName(issuerModel) {
	if (issuerModel.issBinType == "I") {
		issuerModel.issBinTypeName = "Issuer";
	}
	else if (issuerModel.issBinType == "T") {
		issuerModel.issBinTypeName = "Token";
	}
}


function setIssuerDataIndex(issuerDataIndex, oldBinNumber) {
	if (issuerDataIndex < 0) {
		issuerDataIndex = acquirerBinData.findIndex(obj => obj.acquirerId == oldBinNumber);
	}
	return issuerDataIndex;
}

function renderIssuerBinTable() {
	var issuerBinTableData = "";
	issuerBinData.forEach(function (issuerModel) {
		if (issuerModel.status != 'D') {
			var statusDesc = issuerModel.status == 'B' ? 'Blocked' : 'Active';
			var rowData = '<tr>' +
				'<td>' + getTrimmedString(issuerModel.binNumber) + '</td>' +
				'<td>' + getTrimmedString(binCardTypeMapping[issuerModel.binCardType]) + '</td>' +
				'<td>' + getTrimmedString(binCardProductTypeMapping[issuerModel.binProductType]) + '</td>' +
				'<td>' + getTrimmedString(binCardVariantMapping[issuerModel.binCardVariant]) + '</td>' +
				'<td>' + getTrimmedString(binCardBrandMapping[issuerModel.binCardBrand]) + '</td>' +
				'<td>' + getTrimmedString( issuerModel.issBinTypeName) + '</td>' +
				'<td>' + statusDesc + '</td>' +
				'<td>' +
				'<a href="javascript:editIssuerBin(\'' + issuerModel.binNumber + '\')"><span class="glyphicon glyphicon-pencil my-tooltip" title="EDIT"></span></a>';

			if (issuerModel.status == 'B') {
				rowData += ' &nbsp; &nbsp; <a href="javascript:unblockIssuerBin(\'' + issuerModel.binNumber + '\')"><span class="glyphicon glyphicon-ok-circle my-tooltip" title="UNBLOCK"></span></a>';
			} else {
				rowData += ' &nbsp; &nbsp; <a href="javascript:blockIssuerBin(\'' + issuerModel.binNumber + '\')"><span class="glyphicon glyphicon-ban-circle my-tooltip " title="BLOCK"></span></a>';
			}
			rowData += ' &nbsp; &nbsp; <a href="javascript:deleteIssuerBin(\'' + issuerModel.binNumber + '\')"><span class="glyphicon glyphicon-trash my-tooltip" title="DELETE"></span></a></td>';

			rowData += '</tr> ';
			issuerBinTableData += rowData;
		}
	});
	
	$('#issuerBinsList').html(issuerBinTableData);
}
function getFeatureList(featureList) {
    $("#featureMultiple").empty();
    $("#featureMultiple").multiselect("rebuild");
    $("#featureMultiple").multiselect("refresh");
    var binCardType = document.getElementById('binCardType').value;
    var binCardVariant = document.getElementById('binCardVariant').value;
    if(featureList==null || featureList=="null"){
    	featureList="";
    }
    var selectedFeatures=featureList.split(",");
    if (binCardType != 0) {
        if (binCardVariant != 0) {
            var cardFeatures = featureMap[binCardType + "#" + binCardVariant];
            var data = [];
            if (cardFeatures) {
                data = cardFeatures.split(',');
            }            
            $("#featureMultiple").empty();
                $.each(data, function (_index, item) {
                    var isSelectedAlready=selectedFeatures.includes(item);
                    var opt = $('<option />', {
                        value: item,
                        text: item,
                        id: item,
                        selected: isSelectedAlready
                    });
                    $('#featureMultiple').append(opt);
                    $('#featureMultiple').multiselect('rebuild');
                });

        }

    }
}
 
function isSettlementAssociatedToIssuerBin(settlementBin){
 	var issuerDataIndex = issuerBinData.findIndex(obj => obj.issSettlementBin == settlementBin);
	return (issuerDataIndex >= 0);
}
function setHighAndLowBinData(){
var vlowBin=$('#binNumber').val();
		
			if(vlowBin.length==6)
			{
				$('#lowBin').val(vlowBin + "000");
				$('#highBin').val(vlowBin + "999");
			}
			else if(vlowBin.length==7)
			{
				$('#lowBin').val(vlowBin + "00");
				$('#highBin').val(vlowBin + "99");
			}
			else if(vlowBin.length==8)
			{
				$('#lowBin').val(vlowBin + "0");
				$('#highBin').val(vlowBin + "9");
			}
			else if(vlowBin.length==9)
			{
				$('#lowBin').val(vlowBin) ;
				$('#highBin').val(vlowBin) ;
			}
	}