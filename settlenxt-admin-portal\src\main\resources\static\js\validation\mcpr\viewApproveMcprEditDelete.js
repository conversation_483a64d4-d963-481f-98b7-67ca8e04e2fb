	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});

function display() {
    $(".appRejMust").hide();

}

function userAction(action, mcprId) {
	 
	var data = "mcprId," + mcprId ;
	postData(action, data);
}



function backAction(_type, action) {
	var data =  "status,"
			+ status;
	postData(action, data);
}

 function postAction(_action, type) {
	var mcprId;
	var remarks;
	var lastOperation;
	var url;
	var data;
	 if (type == 'E')
		{

	if(maxLengthTextArea('rejectReason')){
	if ($('#apprej option:selected').val() == "A") {
		if ($("#rejectReason").val() != "") {
			 mcprId = $("#mcprId").val(); 
			 remarks=$("#rejectReason").val();
			 lastOperation = $("#lastOperation").val(); 
	
			 url = '/approveMcprBinDetailsEdit';
			 data = "mcprId," + mcprId + ",status," + "A"  + ",remarks,"
					+ remarks + ",lastOperation," + lastOperation;
			
			postData(url, data);
		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else if ($('#apprej option:selected').val() == "R") {
		if ($("#rejectReason").val() != "") {
			 mcprId = $("#mcprId").val(); 
			 remarks=$("#rejectReason").val();
			 lastOperation = $("#lastOperation").val(); 
	
			 url = '/approveMcprBinDetailsEdit';
			 data = "mcprId," + mcprId + ",status," + "R"  + ",remarks,"
					+ remarks + ",lastOperation," + lastOperation;
			postData(url, data);
		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}

		 
	} else {
		$(".appRejMust").show();
		$('html, body').animate({ scrollTop: 0 }, 'slow');
		return false;
	}
	}
		}
	}
		