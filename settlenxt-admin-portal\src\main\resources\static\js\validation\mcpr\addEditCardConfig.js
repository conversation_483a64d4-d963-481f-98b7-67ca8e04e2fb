var isdisableflag=0;
var isdisableflag1=0;
var cardConfigIdLocal=0;
$(document).ready(
		function() {
			$("form :input").change(function() {
				$(this).closest('form').data('changed', true);
			});
			
			cardConfigIdLocal	=$("#cardConfigId").val();
			var toDate=parseInt($("#toYear").val()+""+$("#toMonth").val());
			var fromDate=parseInt($("#fromYear").val()+""+$("#fromMonth").val());
		    var date = new Date();
		    var sys_year = date.getFullYear();
		    var sys_month = date.getMonth()+1;
		    var sysDate = "" + sys_year + (padTo2Digits(sys_month));
		   
		    if($("#cardConfigId").val()>0){
		    if(fromDate<=sysDate ){
				   $('#fromMonth').attr('disabled', true);
				   $('#fromYear').attr('disabled', true);  
			    	document.getElementById('baseFee').readOnly = true;
					isdisableflag=1;
					
				}
		    if(toDate<sysDate ){
				   $('#toMonth').attr('disabled', true);
				  $('#toYear').attr('disabled', true);  
			    	
			    	isdisableflag1=1;
				}
		    }
		    if(isdisableflag==1 && isdisableflag1==1){
		    	$('#submitButton').attr('disabled', true);  
		    }
		    
		    $("#errcardType").hide();
		    $("#errcardVariant").hide();
		    $("#errbaseFee").hide();
		    $("#errfromYear").hide();
		    $("#errtoYear").hide();
		    $("#errtoMonth").hide();
		    $("#errfromMonth").hide();
		    
		    $('#cardType').on('keyup keypress blur change', function () {
		        validateField('cardType', true, "Number",0,false,1,99999999999,true);
		    });
		    $('#cardVariant').on('keyup keypress blur change', function () {
		        validateField('cardVariant', true,"Number",0,false,1,99999999999,true);
		    });
		    $('#baseFee').on('keyup keypress blur change', function () {
		        validateField('baseFee', true, "Decimal",0, false,0.0,99999999.99,true);
		    });
		    $('#fromYear').on('keyup keypress blur change', function () {
		        validateField('fromYear', true,"Number",0,false,1,99999999999,true);
		    });
		    $('#toYear').on('keyup keypress blur change', function () {
		        validateField('toYear', true, "Number",0,false,1,99999999999,true);
		    });
		    $('#fromMonth').on('keyup keypress blur change', function () {
		        validateField('fromMonth', true,"Number",0,false,1,99999999999,true);
		    });
		    $('#toMonth').on('keyup keypress blur change', function () {
		        validateField('toMonth', true,"Number",0,false,1,99999999999,true);
		    });
		  
		disableSave();		  
	$("#cardType").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#cardVariant").on('keyup keypress blur change', function () {
        unableSave();
    });
	$("#baseFee").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#feature").on('keyup keypress blur change', function () {
        unableSave();
    });	$("#details").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#fromYear").on('keyup keypress blur change', function () {
        unableSave();
    });	$("#toYear").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#fromMonth").on('keyup keypress blur change', function () {
        unableSave();
    });	$("#toMonth").on('keyup keypress blur change', function () {
        unableSave();
    });						
						
		});

function disableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = true;
}
}

function unableSave()
{
    if(isdisableflag==1 && isdisableflag1==1){
    	$('#submitButton').attr('disabled', true);  
    }
	else
	{
		if (typeof bEdit != "undefined") {
			document.getElementById("bEdit").disabled = false;
		}
	}	
}
window.history.forward();
function noBack() {
	window.history.forward();
}
function padTo2Digits(sys_month) {
	  return sys_month.toString().padStart(2, '0');
	}

function resetAction() {
$("#cardType").val("");
	$("#cardVariant").val("");
	$("#baseFee").val("");
	$("#fromYear").val("0");
	$("#toYear").val("0");
	$("#toMonth").val("0");
	$("#fromMonth").val("0");
	
	$("#snxtErrorMessage").hide();
	
	$("#errcardType").find('.error').html('');
	$("#errcardVariant").find('.error').html('');
	$("#errbaseFee").find('.error').html('');
	$("#errfromYear").find('.error').html('');
	$("#errtoYear").find('.error').html('');
	$("#errtoMonth").find('.error').html('');
	$("#errfromMonth").find('.error').html('');
	}

function viewCardAdd(url, type) {
	var toDate;var fromDate;var sysDate;
    var date = new Date();
    var sys_year = date.getFullYear();
    var sys_month = date.getMonth()+1;
   
	var isValid = true;

    if (!validateField('cardType', true, "Number",0,false,1,99999999999,true) && isValid) {
        isValid = false;
    }
    if (!validateField('cardVariant', true, "Number",0,false,1,99999999999,true) && isValid) {
        isValid = false;
    }
    if (!validateField('baseFee', true, "Decimal",0, false,0.0,99999999.99,true) && isValid) {
        isValid = false;
    }
    if(isdisableflag==0)
    {
	    if (!validateField('fromMonth', true, "Number",0,false,1,99999999999,true) && isValid) {
	        isValid = false;
	    }
	    if (!validateField('fromYear', true, "Number",0,false,1,99999999999,true) && isValid) {
	        isValid = false;
	    }
	}       
    if (!validateField('toMonth', true, "Number",0,false,1,99999999999,true) && isValid) {
        isValid = false;
    }
    if (!validateField('toYear', true, "Number",0,false,1,99999999999,true) && isValid) {
        isValid = false;
    }
    
    
     toDate=parseInt($("#toYear").val()+""+$("#toMonth").val());
    fromDate=parseInt($("#fromYear").val()+""+$("#fromMonth").val());
    if(isValid  && isdisableflag==0){
    if(fromDate>toDate){
    	if(cardConfigValidationMessages['fromToDateValidation']){
    		$("#errfromMonth").find('.error').html(cardConfigValidationMessages['fromToDateValidation']);
    	}
    	$("#errfromMonth").show();
    	 isValid = false;
    }}

    if(isValid  && isdisableflag1==0){
        if(fromDate>toDate){
        	if(cardConfigValidationMessages['toFromDateValidation']){
        		$("#errtoMonth").find('.error').html(cardConfigValidationMessages['toFromDateValidation']);
        	}
        	$("#errtoMonth").show();
        	 isValid = false;
        }}
    
    
   if(isValid  && isdisableflag==0){
	    
		 fromDate=parseInt($("#fromYear").val()+""+$("#fromMonth").val());
	    
	     sys_year = date.getFullYear();
	     sys_month = date.getMonth()+1;
	     sysDate = "" + sys_year + (padTo2Digits(sys_month));
		if(cardConfigIdLocal>0)
		{	   
			if(fromDate<=sysDate){
				if(cardConfigValidationMessages['pastMonthValidation']){
		    		$("#errfromMonth").find('.error').html(cardConfigValidationMessages['pastMonthValidation']);
		    	}
		    	$("#errfromMonth").show();
		    	 isValid = false;
		 	}
		 }	
		else	
		{	   
			if(parseInt(sysDate) -parseInt(fromDate) > 2){
				if(cardConfigValidationMessages['pastMonthValidation']){
		    		$("#errfromMonth").find('.error').html(cardConfigValidationMessages['pastMonthValidation']);
		    	}
		    	$("#errfromMonth").show();
		    	 isValid = false;
		 	}
		}
   }
 
	var data="";
if (isValid ) {
    toDate=parseInt($("#toYear").val()+""+$("#toMonth").val());
    fromDate=parseInt($("#fromYear").val()+""+$("#fromMonth").val());
	if(type=='E'){
		
	 data = "cardConfigId," + $("#cardConfigId").val() + ",cardType," +  $("#cardType").val()  
	+ ",cardTypeName," +  $("#cardTypeName").val() + ",cardVariant,"+ $("#cardVariant").val() 
	+",cardVariantName," + $("#cardVariantName").val() + ",baseFee," + $("#baseFee").val()  
	+",fromMonth," + $("#fromMonth").val() +",fromYear," + $("#fromYear").val() +",toYear," + $("#toYear").val() 
	+",toMonth," + $("#toMonth").val() +",fromDate," + fromDate  +",toDate," +toDate 
	+",parentPage," + $("#hparentPage").val();
	
}
	else if(type=='A'){
	 data ="cardType," +  $("#cardType").val() + ",cardTypeName," +  $("#cardTypeName").val()
	+ ",cardVariant," + $("#cardVariant").val() +",cardVariantName," + $("#cardVariantName").val() 
	+ ",baseFee," + $("#baseFee").val() +",fromMonth," + $("#fromMonth").val() +",fromYear," + $("#fromYear").val() 
	+",toYear," + $("#toYear").val() +",toMonth," + $("#toMonth").val() +",fromDate," + fromDate  +",toDate," +toDate
	+",parentPage," + $("#hparentPage").val();
	}
	postData(url,data)

}
}




function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    isValid = checkMandatory(isMandatory, fieldValue, fieldType, isValid);
    isValid = checkNaN(fieldType, fieldValue, isValid);
    isValid  = isAlphabet(fieldType, fieldValue, isValid);
    isValid  = isAlphanumericNoSpace(fieldType, fieldValue, isValid);
    isValid  = isAlphabetWithSpace(fieldType, fieldValue, isValid);
    var regEx;
    if (fieldType == "Integer") {
        regEx =/^\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
   
    
    if (fieldType == "Decimal") {
        regEx = /^\d*(\.\d{0,2})?$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
      if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
      

    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
        if(cardConfigValidationMessages[fieldId]){
            $("#err" + fieldId).find('.error').html(cardConfigValidationMessages[fieldId]);
        }
        $("#err" + fieldId).show();
    }
    return isValid;
}


function isAlphanumericNoSpace(fieldType, fieldValue, isValid) {
    if (fieldType == "AlphanumericNoSpace") {
        var regEx = /^[A-Za-z0-9]+$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return  isValid ;
}

function isAlphabetWithSpace(fieldType, fieldValue, isValid) {
    if (fieldType == "AlphabetWithSpace") {
        var regEx = /^[a-zA-Z ]+$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return  isValid ;
}

function isAlphabet(fieldType, fieldValue, isValid) {
    if (fieldType == "Alphabet") {
        var regEx = /^[a-zA-Z]+$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return isValid ;
}

function checkNaN(fieldType, fieldValue, isValid) {
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    return isValid;
}

function checkMandatory(isMandatory, fieldValue, fieldType, isValid) {
    if ((isMandatory && fieldValue.trim() == "" && (fieldType != "SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType == "SelectionBox")) {
        isValid = false;
    }
    return isValid;
}

function userAction(_type, action) {
		
	var data =  "status," + status;
	postData(action, data);
}

function discard(action, cardId) {
	 
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "cardId," + cardId + ",_vTransactToken,"
			+ tokenValue;
	postData(action, data);
}


function postDiscardAction(action,_id) {
			var url = action;
	var cardId = $("#cardId").val();
	var data = "cardId," + cardId  ;
	postData(url, data);
}

