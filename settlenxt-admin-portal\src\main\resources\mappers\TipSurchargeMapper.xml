<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.TipSurchargeRepository">
	
	<select id="getTipSurchargeListMain" resultType="TipSurchargeDTO">
		SELECT t.tip_surcharge_id as tipSurchargeId,t.tip_surcharge_name as tipSurchargeName, t.tip_surcharge_type as tipSurchargeType, t.operator, t.settlement_amount as settlementAmount, t.flat as amount, t.percentage, t.bin_card_brand_id as binCardBrand, t.bin_card_type_id as binCardType,t.amount_flag as amountPercentFlag, t.tip_surcharge_name, 
		ct.description as cardBrand,cv.description as cardType,tst.description as tipSurType,o.description as operatorName, 
		af.description as amtFlag, stg.request_state as requestState 
		from  tip_surcharge t 
		inner join tip_surcharge_stg stg on t.tip_surcharge_id=stg.tip_surcharge_id 
		left join  lookup ct on t.bin_card_brand_id=ct.code and ct.type='SCHEME_CODE'  
		left join  lookup cv on t.bin_card_type_id=cv.code and cv.type='CARD_TYPE' 
		left join  lookup tst on t.tip_surcharge_type=tst.code and tst.type='TypeList'
		left join  lookup o on t.operator =o.code and o.type='OperatorList' 
		left join  lookup af on t.amount_flag =af.code and af.type='AmountPercentList' 
		 ORDER BY t.last_updated_on desc
	</select>
	<select id="getTipSurchargePendingForApproval" resultType="TipSurchargeDTO">
		SELECT t.tip_surcharge_id as tipSurchargeId, t.tip_surcharge_type as tipSurchargeType, t.operator, t.settlement_amount as settlementAmount, t.flat as amount, t.percentage, t.bin_card_brand_id as binCardBrand, t.bin_card_type_id as binCardType,t.amount_flag as amountPercentFlag, t.tip_surcharge_name as tipSurchargeName, t.request_state as requestState, t.CHECKER_COMMENTS as checkerComments, t.status,t.last_operation as lastOperation, 
		ct.description as cardBrand,cv.description as cardType,tst.description as tipSurType,o.description as operatorName, 
		af.description as amtFlag from  tip_surcharge_stg t  
		left join  lookup ct on bin_card_brand_id=ct.code and ct.type='SCHEME_CODE'
		left join  lookup cv on bin_card_type_id=cv.code and cv.type='CARD_TYPE'
		left join  lookup tst on tip_surcharge_type=tst.code and tst.type='TypeList'
		left join  lookup o on operator =o.code and o.type='OperatorList' 
		left join  lookup af on amount_flag =af.code and af.type='AmountPercentList'
		WHERE t.request_state in ('P','R')
	</select>
	<select id="getTipSurchargeProfileMain" resultType="TipSurchargeDTO">
		SELECT t.tip_surcharge_id  as tipSurchargeId, t.tip_surcharge_type as tipSurchargeType, t.operator, 
		t.settlement_amount as settlementAmount, t.flat as amount, t.percentage, 
		t.bin_card_brand_id as binCardBrand, t.bin_card_type_id as binCardType,t.amount_flag as amountPercentFlag,
		t.tip_surcharge_name as tipSurchargeName,stg.request_state as requestState, t.flat as amount, t.percentage, 
		ct.description as cardBrand,cv.description as cardType,tst.description as tipSurType,
		o.description as operatorName,af.description as amtFlag 
		from  tip_surcharge t 
		inner join tip_surcharge_stg stg on t.tip_surcharge_id=stg.tip_surcharge_id 
		left join  lookup ct on t.bin_card_brand_id=ct.code and ct.type='SCHEME_CODE'  
		left join  lookup cv on t.bin_card_type_id=cv.code and cv.type='CARD_TYPE' 
		left join  lookup tst on t.tip_surcharge_type=tst.code and tst.type='TypeList' 
		left join  lookup o on t.operator =o.code and o.type='OperatorList'  
		left join  lookup af on t.amount_flag =af.code and af.type='AmountPercentList'
		WHERE t.tip_surcharge_id = #{tipSurchargeId}
	</select>
	<select id="getTipSurchargeStgInfoById" resultType="TipSurchargeDTO">
		SELECT t.tip_surcharge_id  as tipSurchargeId, t.tip_surcharge_type as tipSurchargeType, t.operator, t.settlement_amount as settlementAmount, t.flat as amount, t.percentage, t.bin_card_brand_id as binCardBrand, t.bin_card_type_id as binCardType,t.amount_flag as amountPercentFlag,t.tip_surcharge_name as tipSurchargeName,t.request_state as requestState, t.flat as amount, t.percentage,  
		t.created_by as createdBy, t.created_on as createdOn,t.last_operation as lastOperation,t.last_updated_by as lastUpdatedBy, t.last_updated_on as lastUpdatedOn, t.status,checker_comments as checkerComments,
		ct.description as cardBrand,cv.description as cardType,tst.description as tipSurType,o.description as operatorName,af.description as amtFlag from  tip_surcharge_stg t 
		left join  lookup ct on bin_card_brand_id=ct.code and ct.type='SCHEME_CODE' 
		left join  lookup cv on bin_card_type_id=cv.code and cv.type='CARD_TYPE' 
		left join  lookup tst on tip_surcharge_type=tst.code and tst.type='TypeList'
		left join  lookup o on operator =o.code and o.type='OperatorList' 
		left join  lookup af on amount_flag =af.code and af.type='AmountPercentList'
		WHERE t.tip_surcharge_id = #{tipSurchargeId}
	</select>
	<select id="getTipSurchargeMain" resultType="TipSurchargeDTO">
		SELECT t.tip_surcharge_id  as tipSurchargeId, t.tip_surcharge_type as tipSurchargeType, t.operator, t.settlement_amount as settlementAmount, t.flat as amount, t.percentage, t.bin_card_brand_id as binCardBrand, t.bin_card_type_id as binCardType,t.amount_flag as amountPercentFlag,t.tip_surcharge_name as tipSurchargeName, t.flat as amount, t.percentage,  
		ct.description as cardBrand,cv.description as cardType,tst.description as tipSurType,o.description as operatorName,af.description as amtFlag
		,t.last_updated_by as lastUpdatedBy, t.last_updated_on as lastUpdatedOn, t.status,t.created_by as createdBy, t.created_on as createdOn
		 from  tip_surcharge t 
		left join  lookup ct on bin_card_brand_id=ct.code and ct.type='SCHEME_CODE' 
		left join  lookup cv on bin_card_type_id=cv.code and cv.type='CARD_TYPE' 
		left join  lookup tst on tip_surcharge_type=tst.code and tst.type='TypeList'
		left join  lookup o on operator =o.code and o.type='OperatorList' 
		left join  lookup af on amount_flag =af.code and af.type='AmountPercentList'
		WHERE t.tip_surcharge_id = #{tipSurchargeId}
	</select>
	<select id="getTipSurchargeStg" resultType="TipSurchargeDTO">
		SELECT t.tip_surcharge_id  as tipSurchargeId, t.tip_surcharge_type as tipSurchargeType, t.operator, t.settlement_amount as settlementAmount, t.flat as amount, t.percentage, t.bin_card_brand_id as binCardBrand, t.bin_card_type_id as binCardType,t.amount_flag as amountPercentFlag,t.tip_surcharge_name as tipSurchargeName,t.request_state as requestState, 
		t.created_by  as createdBy, t.created_on as createdOn,t.last_operation as lastOperation,t.last_updated_by as lastUpdatedBy, t.last_updated_on as lastUpdatedOn, t.status,checker_comments as checkerComments, 
		ct.description as cardBrand,cv.description as cardType,tst.description as tipSurType,o.description as operatorName,af.description as amtFlag 
		from  tip_surcharge_stg t 
		left join  lookup ct on bin_card_brand_id=ct.code and ct.type='SCHEME_CODE' 
		left join  lookup cv on bin_card_type_id=cv.code and cv.type='CARD_TYPE' 
		left join  lookup tst on tip_surcharge_type=tst.code and tst.type='TypeList' 
		left join  lookup o on operator =o.code and o.type='OperatorList' 
		left join  lookup af on amount_flag =af.code and af.type='AmountPercentList'
		WHERE t.tip_surcharge_id = #{tipSurchargeId}
	</select>
	<select id="fetchTipSurchargeIdSequence" resultType="int">	
		SELECT nextval('tip_surcharge_id_seq')
	</select>
	<insert id="insertTipSurchargeStg" >
		INSERT INTO   tip_surcharge_stg (tip_surcharge_id, tip_surcharge_type, operator, settlement_amount, flat,percentage,bin_card_brand_id,bin_card_type_id,CREATED_BY,CREATED_ON, request_state, last_operation, status,tip_surcharge_name,amount_flag) VALUES
		(#{tipSurchargeId}, #{tipSurchargeType}, #{operator}, #{settlementAmount}, #{amount},#{percentage}, #{binCardBrand},#{binCardType}, #{createdBy},#{createdOn}, #{requestState}, #{lastOperation}, #{status},#{tipSurchargeName},#{amountPercentFlag})
	</insert>
	<insert id="insertTipSurchargeMain">
		INSERT INTO   tip_surcharge (tip_surcharge_id, tip_surcharge_type, operator, settlement_amount, flat,percentage,bin_card_brand_id,bin_card_type_id,CREATED_BY,CREATED_ON, LAST_UPDATED_BY,LAST_UPDATED_ON,status,tip_surcharge_name,amount_flag) VALUES
		(#{tipSurchargeId}, #{tipSurchargeType}, #{operator}, #{settlementAmount},#{amount},#{percentage}, #{binCardBrand},#{binCardType}, #{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{status},#{tipSurchargeName},#{amountPercentFlag})
	</insert>
	<update id="updateTipSurchargeMain">
		UPDATE  tip_surcharge SET tip_surcharge_id=#{tipSurchargeId}, tip_surcharge_type=#{tipSurchargeType},operator=#{operator}, settlement_amount=#{settlementAmount},flat=#{amount},percentage=#{percentage},bin_card_brand_id =#{binCardBrand}, bin_card_type_id =#{binCardType},  LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status}, tip_surcharge_name=#{tipSurchargeName},amount_flag =#{amountPercentFlag}  WHERE tip_surcharge_id = #{tipSurchargeId}
	</update>
	<update id="updateTipSurcharge">
		UPDATE  tip_surcharge_stg SET tip_surcharge_id=#{tipSurchargeId}, tip_surcharge_type=#{tipSurchargeType},operator=#{operator}, settlement_amount=#{settlementAmount}, flat=#{amount},percentage=#{percentage},bin_card_brand_id =#{binCardBrand}, bin_card_type_id =#{binCardType}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status},request_state =#{requestState}, last_operation= #{lastOperation}, CHECKER_COMMENTS='', tip_surcharge_name=#{tipSurchargeName},amount_flag =#{amountPercentFlag} WHERE tip_surcharge_id = #{tipSurchargeId}
	</update>
	<update id="updateTipSurchargeDiscard">
		UPDATE  tip_surcharge_stg SET tip_surcharge_id=#{tipSurchargeId}, tip_surcharge_type=#{tipSurchargeType},operator=#{operator}, settlement_amount=#{settlementAmount}, flat=#{amount},percentage=#{percentage},bin_card_brand_id =#{binCardBrand}, bin_card_type_id =#{binCardType}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},request_state =#{requestState},last_operation= #{lastOperation}, CHECKER_COMMENTS='', tip_surcharge_name=#{tipSurchargeName},amount_flag =#{amountPercentFlag} WHERE tip_surcharge_id = #{tipSurchargeId}
	</update>
	<update id="updateTipSurchargeRequestState">
		UPDATE  tip_surcharge_stg SET  request_state= #{requestState},status= #{status}, CHECKER_COMMENTS= #{checkerComments}, LAST_UPDATED_BY= #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, last_operation= #{lastOperation} WHERE tip_surcharge_id = #{tipSurchargeId}
	</update>
	<delete id="deleteDiscardedEntry">
		DELETE FROM  tip_surcharge_stg WHERE tip_surcharge_id = #{tipSurchargeId}
	</delete>
	<select id="getDuplicateTipSurchargeData" resultType="TipSurchargeDTO">
		SELECT tip_surcharge_id as tipSurchargeId,t.tip_surcharge_name as tipSurchargeName, t.tip_surcharge_type as tipSurchargeType
		from  tip_surcharge_stg t where tip_surcharge_id !=#{tipSurchargeId} and tip_surcharge_name =#{tipSurchargeName}
	</select>
</mapper>


