package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import org.osgi.service.component.annotations.Component;

import lombok.Data;
import lombok.ToString;

@Component
@Data
@ToString

public class MoveReportDTO {
	
	private Integer requestId;
	Date requestTime;
	private String reportType;
	private String reportNetwork;
	private String filePath;
	private String status;
	private String month;
	private String year;
	private Date fromDateStr;
	private Date toDateStr;
}
