package org.npci.settlenxt.adminportal.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.repository.LookupRepository;
import org.npci.settlenxt.portal.common.dto.BaseSessionDTO;
import org.npci.settlenxt.portal.common.dto.LookUpDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseLookupServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LookupServiceImpl extends BaseLookupServiceImpl implements LookupService {

	@Autowired
	LookupRepository lookupRepo;

	@Autowired
	private BaseSessionDTO sessionDTO;

	public List<LookUpDTO> getLookupList() {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_APPROVED);
		return lookupRepo.getLookupListMain(requestStateList);
	}

	@Override
	public LookUpDTO addEditLookUp(LookUpDTO lookUpDTO) {
		Date currentDate = new Date();
		lookUpDTO.setCreatedBy(sessionDTO.getUserName());
		lookUpDTO.setLookupId(lookupRepo.getNextSequenceId());
		lookUpDTO.setCreatedOn(currentDate);
		lookUpDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
		lookUpDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		lookupRepo.insertLookUpList(lookUpDTO);
		return lookUpDTO;

	}

	@Override
	public List<LookUpDTO> getPendingLookupListStg() {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_REJECTED);
		return lookupRepo.getPendingLookupListStg(requestStateList);
	}

	@Override
	public LookUpDTO getLookUpInfoFromMain(int lookupId) {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_APPROVED);
		return lookupRepo.getLookUpInfoFromMain(lookupId);
	}

	@Override
	public LookUpDTO getLookUpInfoFromStg(int lookupId) {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
		return lookupRepo.getLookUpInfoFromStg(lookupId);
	}

	@Override
	public LookUpDTO getEditLookUpInfoFromMain(int lookupId) {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_APPROVED);
		return lookupRepo.getEditLookUpInfoFromMain(lookupId);
	}

	@Override
	public int updateLookUp(LookUpDTO lookUpDTO) {
		lookUpDTO.setLastUpdatedBy(sessionDTO.getUserName());
		lookUpDTO.setLastUpdatedOn(new Date());
		lookUpDTO.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
		lookUpDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		return lookupRepo.updateLookUpStg(lookUpDTO);
	}

	@Override
	public LookUpDTO getLookUpFrmStg(int lookupId) {
		return lookupRepo.getLookUpFrmStg(lookupId);
	}

	@Override
	public LookUpDTO approveOrRejectLookUp(int lookupId, String status, String remarks) {
		LookUpDTO lookUpDTO = getLookUpFrmStg(lookupId);

		lookUpDTO.setRequestState(status);
		lookUpDTO.setCheckerComments(remarks);
		lookUpDTO.setLastUpdatedOn(new Date());
		lookUpDTO.setLastUpdatedBy(sessionDTO.getUserName());
		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			LookUpDTO lookUpDTOMain = lookupRepo.approveLookUpFromMain(lookupId);
			lookUpDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			if (lookUpDTOMain != null) {
				lookupRepo.updateLookUpApproval(lookUpDTO);
			} else {
				lookUpDTO.setCreatedOn(new Date());
				lookupRepo.insertLookUpListMain(lookUpDTO);
			}
			lookUpDTO.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
		} else {
			lookUpDTO.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
		}
		lookupRepo.updateLookUpReqState(lookUpDTO);
		return lookUpDTO;
	}

	@Override
	public LookUpDTO discardLookUp(int lookupId) {
		LookUpDTO lookUpDTO = getLookUpFrmStg(lookupId);
		LookUpDTO lookUpDTOMain = lookupRepo.getLookUpInfoFromMain(lookupId);

		if (lookUpDTOMain != null) {
			lookUpDTOMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			lookUpDTOMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			lookUpDTOMain.setLastUpdatedBy(sessionDTO.getUserName());
			lookUpDTOMain.setLastUpdatedOn(new Date());
			lookupRepo.updateStgLookUp(lookUpDTOMain);
			return lookUpDTOMain;
		} else {
			lookupRepo.deleteDiscardedEntry(lookupId);
		}
		return lookUpDTO;
	}

	@Override
	public LookUpDTO updateApproveOrRejectBulkLookUp(String bulkLookUpList, String status, String remarks) {
		String[] idArray = bulkLookUpList.split("\\|");
		LookUpDTO lookUpDTO = new LookUpDTO();
		int[] values = Arrays.stream(idArray).mapToInt(Integer::parseInt).toArray();
		List<Integer> lookUpList = Arrays.stream(values).boxed().collect(Collectors.toList());
		List<LookUpDTO> lookUpToArr = lookupRepo.fetchLookUpStgList(lookUpList);
		Map<Integer, List<LookUpDTO>> cappingMap = lookUpToArr.stream()
				.collect(Collectors.groupingBy(LookUpDTO::getLookupId));
		for (String id:idArray) {
			try {
				List<LookUpDTO> lookupdto = cappingMap.get(Integer.parseInt(id));
				LookUpDTO lookUpDto = lookupdto.get(0);
				if (lookUpDto == null) {
					throw new SettleNxtException("Exception occurred with cappingAmountId" + id, "");
				} else {
					lookUpDto = approveOrRejectLookUp(lookUpDto.getLookupId(), status, remarks);
					lookUpDTO.setStatusCode(lookUpDto.getStatusCode());
				}
			} catch (Exception ex) {
				throw new SettleNxtException("Exception for BulkDataEntryId" + id, "", ex);
			}
		}
		return lookUpDTO;

	}

	@Override
	public List<LookUpDTO> getLookupListType() {

		return lookupRepo.getLookUpTypeListFrmMain();
	}

	@Override
	public boolean checkDupLookUpDetails(String lkpType, String lkpVal, String lkpDesc, String flow) {

		LookUpDTO lookUpDTO = new LookUpDTO();
		lookUpDTO.setLkpValue(lkpVal);
		lookUpDTO.setLkpType(lkpType);
		lookUpDTO.setLkpDesc(lkpDesc);

		int i = 0;
		int j = 0;

		if ("add".equals(flow)) {
			i = lookupRepo.checkDupLookUpDetails(lookUpDTO);
			j = lookupRepo.checkDupLookUpDetailsAdd(lookUpDTO);
			int z = lookupRepo.checkDupLookUpDetailsMain(lookUpDTO);
			return i > 0 || j > 0 || z > 0;
		} else if ("edit".equals(flow)) {
			i = lookupRepo.checkDupLookUpDetails(lookUpDTO);
			return i > 0;
		}
		return false;

	}

}
