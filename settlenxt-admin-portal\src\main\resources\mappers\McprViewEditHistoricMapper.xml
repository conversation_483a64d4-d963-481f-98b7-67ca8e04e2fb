<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.McprViewEditHistoricRepository">
	
	<select id="getMcprData" resultType="McprViewEditHistoricDTO">
		SELECT m.MCPR_BIN_DATA_DETAILS_ID as mcprBinDataDetailsId, m.MONTH_ENDING as monthEnding, m.BIN_NO as binNumber,
		m.phy_contact_card_cumm_total as phyContactCardCummRuPayCard, m.PHY_CONTACT_CARD_INCREM as phyContactCardIncrementalCard, 
		m.phy_contact_less_card_total as phyContactlessCummRuPayCard, m.PHY_CONTACT_LESS_CARD_INCREM as phyContactlessIncrementalCard,
		 m.virtual_card_cumm_total as virtualCardCummRuPayCard, m.virtual_card_increm as virtualCardIncrementalCard, 
		 m.LAST_UPDATED_BY as lastUpdatedBy,m.LAST_UPDATED_ON as lastUpdatedOn FROM MCPR_BIN_DATA_DETAILS_stg m WHERE m.request_state ='A' 
		 and  m.BIN_NO in (SELECT b.BIN_NUMBER FROM MEMBIN_DETAILS b inner join participant p on b.participant_id =p.participant_id 
		 WHERE b.status!='D' and b.BIN_NUMBER IS NOT null and p.unique_bank_name =#{bankName}) ORDER BY m.last_updated_on DESC
	</select>
	<select id="getMcprDataMainTable" resultType="McprViewEditHistoricDTO">
		SELECT m.MCPR_BIN_DATA_DETAILS_ID as mcprBinDataDetailsId, m.MONTH_ENDING as monthEnding, m.BIN_NO as binNumber,
		m.phy_contact_card_cumm_total as phyContactCardCummRuPayCard, m.PHY_CONTACT_CARD_INCREM as phyContactCardIncrementalCard,
		 m.phy_contact_less_card_total as phyContactlessCummRuPayCard, m.PHY_CONTACT_LESS_CARD_INCREM as phyContactlessIncrementalCard,
		  m.virtual_card_cumm_total as virtualCardCummRuPayCard, m.virtual_card_increm as virtualCardIncrementalCard,
		   m.LAST_UPDATED_BY as lastUpdatedBy,m.LAST_UPDATED_ON as lastUpdatedOn 
		   FROM MCPR_BIN_DATA_DETAILS m 
		   inner join MCPR_BIN_DATA_DETAILS_STG stg on m.MCPR_BIN_DATA_DETAILS_ID=stg.MCPR_BIN_DATA_DETAILS_ID
		 WHERE    m.MONTH_ENDING =#{monthEnding} and
		   m.BIN_NO in (SELECT b.BIN_NUMBER FROM MEMBIN_DETAILS b 
		   inner join participant p on b.participant_id =p.participant_id 
		   WHERE b.status!='D' and b.BIN_NUMBER IS NOT null and p.unique_bank_name =#{bankName})  
		   ORDER BY m.last_updated_on DESC
	</select>
	<select id="getPendingMcprDataEdit" resultType="McprViewEditHistoricDTO">
		SELECT  STG.MCPR_BIN_DATA_DETAILS_ID as mcprBinDataDetailsId, STG.MONTH_ENDING as monthEnding, STG.BIN_NO as binNumber, 
		STG.phy_contact_card_cumm_total as phyContactCardCummRuPayCard, STG.PHY_CONTACT_CARD_INCREM as phyContactCardIncrementalCard,
		 STG.phy_contact_less_card_total as phyContactlessCummRuPayCard, STG.PHY_CONTACT_LESS_CARD_INCREM as phyContactlessIncrementalCard,
		  STG.virtual_card_cumm_total as virtualCardCummRuPayCard, STG.virtual_card_increm as virtualCardIncrementalCard,
		   STG.LAST_UPDATED_BY as lastUpdatedBy,STG.LAST_UPDATED_ON as lastUpdatedOn, STG.LAST_OPERATION as lastOperation, 
		   STG.REQUEST_STATE as requestState 
		   FROM MCPR_BIN_DATA_DETAILS_STG STG  
		   INNER JOIN MCPR_BIN_DATA_DETAILS MAIN ON (STG.MCPR_BIN_DATA_DETAILS_ID = MAIN.MCPR_BIN_DATA_DETAILS_ID) 
		   WHERE STG.MONTH_ENDING =#{monthEnding} and
		   STG.request_state in ('P','R','D') and edit_by = 'Npci'  
		   and case when (to_char(now(),'dd')::numeric) between 1 and (select sum(description::numeric) from lookup where type ='MCPR_TAT' ) then 1 else 0 end =1
	</select>
	<select id="getMcprDataHistoric" resultType="McprViewEditHistoricDTO">
		SELECT m.MCPR_BIN_DATA_DETAILS_ID as mcprBinDataDetailsId, m.MONTH_ENDING as monthEnding, m.BIN_NO as binNumber,m.phy_contact_card_cumm_total as phyContactCardCummRuPayCard, m.PHY_CONTACT_CARD_INCREM as phyContactCardIncrementalCard, m.phy_contact_less_card_total as phyContactlessCummRuPayCard, m.PHY_CONTACT_LESS_CARD_INCREM as phyContactlessIncrementalCard, m.virtual_card_cumm_total as virtualCardCummRuPayCard, m.virtual_card_increm as virtualCardIncrementalCard, 
		m.LAST_UPDATED_BY as lastUpdatedBy,m.LAST_UPDATED_ON as lastUpdatedOn FROM MCPR_BIN_DATA_DETAILS_STG m WHERE m.request_state =#{requestType} ORDER BY last_updated_on DESC
	</select>
	<select id="getMcprDataHistory" resultType="McprViewEditHistoricDTO">
		
	 SELECT m.MCPR_BIN_DATA_DETAILS_ID as mcprBinDataDetailsId, m.MONTH_ENDING as monthEnding, m.BIN_NO as binNumber,
	 m.phy_contact_card_cumm_total as phyContactCardCummRuPayCard, m.PHY_CONTACT_CARD_INCREM as phyContactCardIncrementalCard,
 	m.phy_contact_less_card_total as phyContactlessCummRuPayCard, m.PHY_CONTACT_LESS_CARD_INCREM as phyContactlessIncrementalCard,
 	m.virtual_card_cumm_total as virtualCardCummRuPayCard, m.virtual_card_increm as virtualCardIncrementalCard, 
	 m.LAST_UPDATED_BY as lastUpdatedBy,m.LAST_UPDATED_ON as lastUpdatedOn 
 	FROM MCPR_BIN_DATA_DETAILS m 
 	WHERE m.MONTH_ENDING=#{monthYear} and COALESCE(reason,'') != 'NoUpload'
	and m.BIN_NO in (SELECT b.BIN_NUMBER FROM MEMBIN_DETAILS b 
 	inner join participant p on b.participant_id =p.participant_id 
 	WHERE b.status!='D' and b.BIN_NUMBER IS NOT null and p.unique_bank_name  =#{bankName}) 
 	ORDER BY m.last_updated_on desc 
	</select>
	<select id="getMcprDataProfile" resultType="McprViewEditHistoricDTO">
		select m.MCPR_BIN_DATA_DETAILS_ID as mcprBinDataDetailsId, m.MONTH_ENDING as monthEnding, m.BIN_NO as binNumber,m.PARTICIPANT_ID as participantId, m.phy_contact_card_cumm_total as phyContactCardCummRuPayCard, m.PHY_CONTACT_CARD_INCREM as phyContactCardIncrementalCard, m.phy_contact_less_card_total as phyContactlessCummRuPayCard, m.PHY_CONTACT_LESS_CARD_INCREM as phyContactlessIncrementalCard, m.virtual_card_cumm_total as virtualCardCummRuPayCard, m.virtual_card_increm as virtualCardIncrementalCard,
		m.ecomm_txn_onus_count as ecommTxnOnusCount,m.ecomm_txn_onus_amt as ecommTxnOnusAmt,m.dpos_contact_card_count as posConcactCardPresentDomTxnOnusCount, m.dpos_contact_card_amt as posConcactCardPresentDomTxnOnusAmt,m.dpos_clessc_onl_ret_onus_count as posContactlessOnlRetailsDomTxnOnusCount,m.dpos_clessc_onl_ret_onus_amt as posContactlessOnlRetailsDomTxnOnusAmt,m.dpos_clessc_onl_trsit_onus_count as posContactlessOnlTransitDomTxnOnusCount,m.dpos_clessc_onl_trsit_onus_amt as posContactlessOnlTransitDomTxnOnusAmt,
		m.dpos_clessc_offl_ret_onus_count as posContactlessOffRetailsDomTxnOnusCount,m.dpos_clessc_offl_ret_onus_amt as posContactlessOffRetailsDomTxnOnusAmt,m.dpos_clessc_offl_trsit_onus_count as posContactlessOffTransitDomTxnOnusCount,m.dpos_clessc_offl_trsit_onus_amt as posContactlessOffTransitDomTxnOnusAmt,m.domistic_atm_card_present_count as atmCardPresentDomTxnOnusCount,m.domistic_atm_card_present_amt as atmCardPresentDomTxnOnusAmt,m.total_cumulative_cards as totalCumulativeCards, m.total_incremental_cards as totalIncrementalCards
		 FROM MCPR_BIN_DATA_DETAILS m where m.MCPR_BIN_DATA_DETAILS_ID =#{mcprId}  
	</select>
	<select id="getMcprDataProfileMain" resultType="McprViewEditHistoricDTO">
		select m.MCPR_BIN_DATA_DETAILS_ID as mcprBinDataDetailsId, m.MONTH_ENDING as monthEnding, m.BIN_NO as binNumber,m.PARTICIPANT_ID as participantId, m.phy_contact_card_cumm_total as phyContactCardCummRuPayCard, m.PHY_CONTACT_CARD_INCREM as phyContactCardIncrementalCard, m.phy_contact_less_card_total as phyContactlessCummRuPayCard, m.PHY_CONTACT_LESS_CARD_INCREM as phyContactlessIncrementalCard, m.virtual_card_cumm_total as virtualCardCummRuPayCard, m.virtual_card_increm as virtualCardIncrementalCard,
		m.ecomm_txn_onus_count as ecommTxnOnusCount,m.ecomm_txn_onus_amt as ecommTxnOnusAmt,m.dpos_contact_card_count as posConcactCardPresentDomTxnOnusCount, m.dpos_contact_card_amt as posConcactCardPresentDomTxnOnusAmt,m.dpos_clessc_onl_ret_onus_count as posContactlessOnlRetailsDomTxnOnusCount,m.dpos_clessc_onl_ret_onus_amt as posContactlessOnlRetailsDomTxnOnusAmt,m.dpos_clessc_onl_trsit_onus_count as posContactlessOnlTransitDomTxnOnusCount,m.dpos_clessc_onl_trsit_onus_amt as posContactlessOnlTransitDomTxnOnusAmt,
		m.dpos_clessc_offl_ret_onus_count as posContactlessOffRetailsDomTxnOnusCount,m.dpos_clessc_offl_ret_onus_amt as posContactlessOffRetailsDomTxnOnusAmt,m.dpos_clessc_offl_trsit_onus_count as posContactlessOffTransitDomTxnOnusCount,m.dpos_clessc_offl_trsit_onus_amt as posContactlessOffTransitDomTxnOnusAmt,m.domistic_atm_card_present_count as atmCardPresentDomTxnOnusCount,m.domistic_atm_card_present_amt as atmCardPresentDomTxnOnusAmt,m.total_cumulative_cards as totalCumulativeCards, m.total_incremental_cards as totalIncrementalCards,
		stg.REQUEST_STATE as requestState 
		FROM MCPR_BIN_DATA_DETAILS m 
		inner join MCPR_BIN_DATA_DETAILS_STG stg on m.MCPR_BIN_DATA_DETAILS_ID=stg.MCPR_BIN_DATA_DETAILS_ID
		where m.MCPR_BIN_DATA_DETAILS_ID =#{mcprId}  
	</select>
	
	<select id="getMcprDataStgByMcprId" resultType="McprViewEditHistoricDTO">
		select m.MCPR_BIN_DATA_DETAILS_ID as mcprBinDataDetailsId, m.MONTH_ENDING as monthEnding, m.BIN_NO as binNumber, m.phy_contact_card_cumm_total as phyContactCardCummRuPayCard, m.PHY_CONTACT_CARD_INCREM as phyContactCardIncrementalCard, m.phy_contact_less_card_total as phyContactlessCummRuPayCard, m.PHY_CONTACT_LESS_CARD_INCREM as phyContactlessIncrementalCard, m.virtual_card_cumm_total as virtualCardCummRuPayCard, m.virtual_card_increm as virtualCardIncrementalCard, 
		m.ecomm_txn_onus_count as ecommTxnOnusCount,m.ecomm_txn_onus_amt as ecommTxnOnusAmt,m.dpos_contact_card_count as posConcactCardPresentDomTxnOnusCount, m.dpos_contact_card_amt as posConcactCardPresentDomTxnOnusAmt,m.dpos_clessc_onl_ret_onus_count as posContactlessOnlRetailsDomTxnOnusCount,m.dpos_clessc_onl_ret_onus_amt as posContactlessOnlRetailsDomTxnOnusAmt,m.dpos_clessc_onl_trsit_onus_count as posContactlessOnlTransitDomTxnOnusCount,m.dpos_clessc_onl_trsit_onus_amt as posContactlessOnlTransitDomTxnOnusAmt,
		m.dpos_clessc_offl_ret_onus_count as posContactlessOffRetailsDomTxnOnusCount,m.dpos_clessc_offl_ret_onus_amt as posContactlessOffRetailsDomTxnOnusAmt,m.dpos_clessc_offl_trsit_onus_count as posContactlessOffTransitDomTxnOnusCount,m.dpos_clessc_offl_trsit_onus_amt as posContactlessOffTransitDomTxnOnusAmt,m.domistic_atm_card_present_count as atmCardPresentDomTxnOnusCount,m.domistic_atm_card_present_amt as atmCardPresentDomTxnOnusAmt,
		m.total_cumulative_cards as totalCumulativeCards, m.total_incremental_cards as totalIncrementalCards,m.CREATED_BY as createdBy,m.CREATED_ON as createdOn, m.LAST_UPDATED_BY as lastUpdatedBy,m.LAST_UPDATED_ON as lastUpdatedOn,m.request_state as requestState,m.last_operation as lastOperation, m.status,m.CHECKER_COMMENTS as checkerComments
		FROM MCPR_BIN_DATA_DETAILS_STG m WHERE m.MCPR_BIN_DATA_DETAILS_ID =#{mcprId}
	</select>
	<select id="getMcprDataMain" resultType="McprViewEditHistoricDTO">
		select m.MCPR_BIN_DATA_DETAILS_ID as mcprBinDataDetailsId, m.MONTH_ENDING as monthEnding, m.BIN_NO as binNumber, m.phy_contact_card_cumm_total as phyContactCardCummRuPayCard, m.PHY_CONTACT_CARD_INCREM as phyContactCardIncrementalCard, m.phy_contact_less_card_total as phyContactlessCummRuPayCard, m.PHY_CONTACT_LESS_CARD_INCREM as phyContactlessIncrementalCard, m.virtual_card_cumm_total as virtualCardCummRuPayCard, m.virtual_card_increm as virtualCardIncrementalCard,
		m.ecomm_txn_onus_count as ecommTxnOnusCount,m.ecomm_txn_onus_amt as ecommTxnOnusAmt,m.dpos_contact_card_count as posConcactCardPresentDomTxnOnusCount, m.dpos_contact_card_amt as posConcactCardPresentDomTxnOnusAmt,m.dpos_clessc_onl_ret_onus_count as posContactlessOnlRetailsDomTxnOnusCount,m.dpos_clessc_onl_ret_onus_amt as posContactlessOnlRetailsDomTxnOnusAmt,m.dpos_clessc_onl_trsit_onus_count as posContactlessOnlTransitDomTxnOnusCount,m.dpos_clessc_onl_trsit_onus_amt as posContactlessOnlTransitDomTxnOnusAmt,
		m.dpos_clessc_offl_ret_onus_count as posContactlessOffRetailsDomTxnOnusCount,m.dpos_clessc_offl_ret_onus_amt as posContactlessOffRetailsDomTxnOnusAmt,m.dpos_clessc_offl_trsit_onus_count as posContactlessOffTransitDomTxnOnusCount,m.dpos_clessc_offl_trsit_onus_amt as posContactlessOffTransitDomTxnOnusAmt,m.domistic_atm_card_present_count as atmCardPresentDomTxnOnusCount,m.domistic_atm_card_present_amt as atmCardPresentDomTxnOnusAmt,
		m.total_cumulative_cards as totalCumulativeCards, m.total_incremental_cards as totalIncrementalCards, m.CREATED_BY as createdBy,m.CREATED_ON as createdOn, m.LAST_UPDATED_BY as lastUpdatedBy,m.LAST_UPDATED_ON as lastUpdatedOn,m.status
		FROM MCPR_BIN_DATA_DETAILS m where m.MCPR_BIN_DATA_DETAILS_ID = #{mcprId}
	</select>
	
	<insert id="insertMcprDataMain" >
		INSERT INTO MCPR_BIN_DATA_DETAILS
		(MCPR_BIN_DATA_DETAILS_ID, MONTH_ENDING , BIN_NO, phy_contact_card_cumm_total, PHY_CONTACT_CARD_INCREM, phy_contact_less_card_total, PHY_CONTACT_LESS_CARD_INCREM, virtual_card_cumm_total, virtual_card_increm as virtualCardIncrementalCard,
		ecomm_txn_onus_count,ecomm_txn_onus_amt,dpos_contact_card_count, dpos_contact_card_amt,dpos_clessc_onl_ret_onus_count,dpos_clessc_onl_ret_onus_amt,dpos_clessc_onl_trsit_onus_count,dpos_clessc_onl_trsit_onus_amt,
		dpos_clessc_offl_ret_onus_count,dpos_clessc_offl_ret_onus_amt,dpos_clessc_offl_trsit_onus_count,dpos_clessc_offl_trsit_onus_amt,domistic_atm_card_present_count,domistic_atm_card_present_amt,
		total_cumulative_cards, total_incremental_cards, CREATED_BY,CREATED_ON, LAST_UPDATED_BY,LAST_UPDATED_ON, status,submitted_date,month_ending_in_num,document_id) VALUES
		(#{mcprBinDataDetailsId}, #{monthEnding}, #{binNumber}, #{phyContactCardCummRuPayCard},#{phyContactCardIncrementalCard},#{phyContactlessCummRuPayCard}, #{phyContactlessIncrementalCard}, #{virtualCardCummRuPayCard},#{virtualCardIncrementalCard},
		#{ecommTxnOnusCount}, #{ecommTxnOnusAmt}, #{posConcactCardPresentDomTxnOnusCount}, #{posConcactCardPresentDomTxnOnusAmt},#{posContactlessOnlRetailsDomTxnOnusCount},#{posContactlessOnlRetailsDomTxnOnusAmt}, #{posContactlessOnlTransitDomTxnOnusCount}, #{posContactlessOnlTransitDomTxnOnusAmt},
		#{posContactlessOffRetailsDomTxnOnusCount}, #{posContactlessOffRetailsDomTxnOnusAmt}, #{posContactlessOffTransitDomTxnOnusCount}, #{posContactlessOffTransitDomTxnOnusAmt},#{atmCardPresentDomTxnOnusCount},#{atmCardPresentDomTxnOnusAmt},
		#{totalCumulativeCards}, #{totalIncrementalCards},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{status},#{monthEndingInNumber},#{monthEndingInNumber},#{documentId})   
	</insert>
	<update id="updateMcprData">
		UPDATE MCPR_BIN_DATA_DETAILS_STG SET MCPR_BIN_DATA_DETAILS_ID=#{mcprBinDataDetailsId}, MONTH_ENDING=#{monthEnding}, BIN_NO=#{binNumber},phy_contact_card_cumm_total=#{phyContactCardCummRuPayCard}, PHY_CONTACT_CARD_INCREM=#{phyContactCardIncrementalCard}, phy_contact_less_card_total =#{phyContactlessCummRuPayCard},  PHY_CONTACT_LESS_CARD_INCREM =#{phyContactlessIncrementalCard}, virtual_card_cumm_total =#{virtualCardCummRuPayCard}, virtual_card_increm= #{virtualCardIncrementalCard},
		ecomm_txn_onus_count =#{ecommTxnOnusCount},ecomm_txn_onus_amt =#{ecommTxnOnusAmt},dpos_contact_card_count=#{posConcactCardPresentDomTxnOnusCount}, dpos_contact_card_amt=#{posConcactCardPresentDomTxnOnusAmt},dpos_clessc_onl_ret_onus_count=#{posContactlessOnlRetailsDomTxnOnusCount},dpos_clessc_onl_ret_onus_amt=#{posContactlessOnlRetailsDomTxnOnusAmt},dpos_clessc_onl_trsit_onus_count=#{posContactlessOnlTransitDomTxnOnusCount},dpos_clessc_onl_trsit_onus_amt=#{posContactlessOnlTransitDomTxnOnusAmt},
		dpos_clessc_offl_ret_onus_count=#{posContactlessOffRetailsDomTxnOnusCount},dpos_clessc_offl_ret_onus_amt =#{posContactlessOffRetailsDomTxnOnusAmt},dpos_clessc_offl_trsit_onus_count=#{posContactlessOffTransitDomTxnOnusCount},dpos_clessc_offl_trsit_onus_amt=#{posContactlessOffTransitDomTxnOnusAmt},domistic_atm_card_present_count=#{atmCardPresentDomTxnOnusCount},domistic_atm_card_present_amt=#{atmCardPresentDomTxnOnusAmt},
		total_cumulative_cards=#{totalCumulativeCards}, total_incremental_cards=#{totalIncrementalCards}, CREATED_BY =#{createdBy},CREATED_ON =#{createdOn}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status},request_state =#{requestState},last_operation= #{lastOperation},edit_by = #{editBy}, CHECKER_COMMENTS='' WHERE MCPR_BIN_DATA_DETAILS_ID = #{mcprBinDataDetailsId}
	</update>
	<update id="updateMcprDataMain">
		UPDATE MCPR_BIN_DATA_DETAILS SET MCPR_BIN_DATA_DETAILS_ID=#{mcprBinDataDetailsId}, MONTH_ENDING=#{monthEnding}, BIN_NO=#{binNumber},phy_contact_card_cumm_total=#{phyContactCardCummRuPayCard}, PHY_CONTACT_CARD_INCREM=#{phyContactCardIncrementalCard}, phy_contact_less_card_total =#{phyContactlessCummRuPayCard},  PHY_CONTACT_LESS_CARD_INCREM =#{phyContactlessIncrementalCard}, virtual_card_cumm_total =#{virtualCardCummRuPayCard}, virtual_card_increm= #{virtualCardIncrementalCard},
		ecomm_txn_onus_count =#{ecommTxnOnusCount},ecomm_txn_onus_amt =#{ecommTxnOnusAmt},dpos_contact_card_count=#{posConcactCardPresentDomTxnOnusCount}, dpos_contact_card_amt=#{posConcactCardPresentDomTxnOnusAmt},dpos_clessc_onl_ret_onus_count=#{posContactlessOnlRetailsDomTxnOnusCount},dpos_clessc_onl_ret_onus_amt=#{posContactlessOnlRetailsDomTxnOnusAmt},dpos_clessc_onl_trsit_onus_count=#{posContactlessOnlTransitDomTxnOnusCount},dpos_clessc_onl_trsit_onus_amt=#{posContactlessOnlTransitDomTxnOnusAmt},
		dpos_clessc_offl_ret_onus_count=#{posContactlessOffRetailsDomTxnOnusCount},dpos_clessc_offl_ret_onus_amt =#{posContactlessOffRetailsDomTxnOnusAmt},dpos_clessc_offl_trsit_onus_count=#{posContactlessOffTransitDomTxnOnusCount},dpos_clessc_offl_trsit_onus_amt=#{posContactlessOffTransitDomTxnOnusAmt},domistic_atm_card_present_count=#{atmCardPresentDomTxnOnusCount},domistic_atm_card_present_amt=#{atmCardPresentDomTxnOnusAmt},
		total_cumulative_cards=#{totalCumulativeCards}, total_incremental_cards=#{totalIncrementalCards}, CREATED_BY =#{createdBy},CREATED_ON =#{createdOn}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status},REASON='UpdatdNPCI' WHERE MCPR_BIN_DATA_DETAILS_ID = #{mcprBinDataDetailsId}
	</update>
	<update id="updateMcprDataDelete">
		UPDATE MCPR_BIN_DATA_DETAILS_STG SET MCPR_BIN_DATA_DETAILS_ID=#{mcprBinDataDetailsId}, MONTH_ENDING=#{monthEnding}, BIN_NO=#{binNumber},phy_contact_card_cumm_total=#{phyContactCardCummRuPayCard}, PHY_CONTACT_CARD_INCREM=#{phyContactCardIncrementalCard}, phy_contact_less_card_total =#{phyContactlessCummRuPayCard},  PHY_CONTACT_LESS_CARD_INCREM =#{phyContactlessIncrementalCard}, virtual_card_cumm_total =#{virtualCardCummRuPayCard}, virtual_card_increm= #{virtualCardIncrementalCard},
		ecomm_txn_onus_count =#{ecommTxnOnusCount},ecomm_txn_onus_amt =#{ecommTxnOnusAmt},dpos_contact_card_count=#{posConcactCardPresentDomTxnOnusCount}, dpos_contact_card_amt=#{posConcactCardPresentDomTxnOnusAmt},dpos_clessc_onl_ret_onus_count=#{posContactlessOnlRetailsDomTxnOnusCount},dpos_clessc_onl_ret_onus_amt=#{posContactlessOnlRetailsDomTxnOnusAmt},dpos_clessc_onl_trsit_onus_count=#{posContactlessOnlTransitDomTxnOnusCount},dpos_clessc_onl_trsit_onus_amt=#{posContactlessOnlTransitDomTxnOnusAmt},
		total_cumulative_cards=#{totalCumulativeCards}, total_incremental_cards=#{totalIncrementalCards}, CREATED_BY =#{createdBy},CREATED_ON =#{createdOn}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status},request_state =#{requestState},last_operation= #{lastOperation},edit_by = #{editBy}, CHECKER_COMMENTS='' WHERE MCPR_BIN_DATA_DETAILS_ID = #{mcprBinDataDetailsId}
	</update>
	<update id="updateMcprDataRequestState">
		UPDATE MCPR_BIN_DATA_DETAILS_STG SET  REQUEST_STATE=#{requestState}, CHECKER_COMMENTS=#{checkerComments}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, last_operation= #{lastOperation}    WHERE MCPR_BIN_DATA_DETAILS_ID= #{mcprBinDataDetailsId}
	</update>
	<delete id="deleteDiscardedEntry">
		DELETE FROM MCPR_BIN_DATA_DETAILS_STG WHERE MCPR_BIN_DATA_DETAILS_ID= #{mcprBinDataDetailsId}
	</delete>
	<select id="getBinNoList" resultType="McprViewEditHistoricDTO">
		
		select  distinct m.bin_number as binNumber from MEMBIN_DETAILS m where m.status!='D' and m.bin_number IS NOT null and m.participant_id 
        in ( select p.participant_id from participant p  where p.unique_bank_name in  (select x.unique_bank_name 
        from  participant x where  x.participant_id in (select u.bank_participant_id from user_detail u where u.login_id=#{participantId} ) )) 
        and status='A' and to_char(activation_Date,'yyyymm')::numeric between 0 and (to_char(now(),'yyyymm')::numeric-1)
 	</select>
	<select id="getEditTAT" resultType="String">
		select description from lookup where type ='MCPR_TAT' and code='editWTAT'
	 </select>
	<select id="getUploadTAT" resultType="String">
		select description from lookup where type ='MCPR_TAT' and code='uploadWTAT'
	 </select>
	 
	 <delete id="deleteMcprBinDetailsMain" >
		DELETE FROM MCPR_BIN_DATA_DETAILS WHERE MCPR_BIN_DATA_DETAILS_ID= #{mcprBinDataDetailsId}
	</delete>
	<delete id="deleteMcprBinDetailsStg">
		DELETE FROM MCPR_BIN_DATA_DETAILS_STG WHERE MCPR_BIN_DATA_DETAILS_ID= #{mcprBinDataDetailsId}
	</delete>
	 
	 
</mapper>










	