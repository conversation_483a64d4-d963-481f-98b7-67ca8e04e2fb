<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<title></title>
<%-- <title><spring:message code="am.lbl.title" /></title>
<meta http-equiv="Cache-control" content="no-cache" />
<meta http-equiv="Cache-control" content="no-store" />
<meta http-equiv="Expires" content="0" />
<meta http-equiv="pragma" content="no-cache" />
<%@include file="Header.jsp"%> --%>


<script type="text/javascript"> 
var actionColumnIndex=27;
</script>
<script type="text/javascript">
var validationMessages = {};      
validationMessages['schemeCodeBank'] = "<spring:message code='txn.scheme.validation.msg' javaScriptEscape='true' />";

</script>

<script type="text/javascript" src=	"./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src=	"./static/js/jszip.min.js">
</script>
 
<script type="text/javascript" src=	"./static/js/buttons.html5.min.js">
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />

<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />
<script type="text/javascript"
	src="./static/js/validation/transactionDetailSearch.js"></script>
<script type="text/javascript" src="./static/js/custom_js/vTransact.js"></script>
<script type="text/javascript"
	src="./static/js/validation/commonValidation.js"></script>


<style>
#title {
	color: red;
	font-size: 10px;
	text-indent: 10px;
}
.defaultexport {
  visibility: hidden;
}

table.dataTable thead  { vertical-align: top;}
table.dataTable thead .sorting { vertical-align: bottom; background: url('./static/images/sort_both.png') no-repeat center right; }
table.dataTable thead .sorting_asc { vertical-align: bottom;background: url('./static/images/sort_asc.png') no-repeat center right; }
table.dataTable thead .sorting_desc { vertical-align: bottom;background: url('./static/images/sort_desc.png') no-repeat center right; }
table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before{ vertical-align: top;content:""}
table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after{ vertical-align: top;content:""}
.search-box  {	
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;	
	background-color: transparent;
    border-width:1px;
	border-style:inset;
	width:100%;
    }
</style>

</head>

<input type="hidden" id="networkType" name="networkType" value="${networkType}"></input>
<!-- <body> -->
<div class="container-fluid height-min">
	<div class="row">
		<div class="body-content">
			<div class="">
				<div class="col-md-12">

					<c:if test="${not empty successStatus}">
						<div class="alert alert-success" role="alert">${successStatus}</div>
					</c:if>
					<%-- <c:if test="${not empty errorStatus}">
							<div class="alert alert-danger" role="alert">${errorStatus}</div>
						</c:if>
                       <div class="alert alert-danger" style="display: none;" role="alert">${errorStatus}</div> --%>


					<div class="card">
						<div class="card-header">
							<div class="card-title">
								Transaction Search<label id="title"> (Either PAN or RRN or Function Code is mandatory) </label>
							</div>
						</div>
						<div class="card-body">
							<form:form onsubmit="removeSpace(this); encodeForm(this);"
								method="POST" id="searchForm" action="" autocomplete="off"
								modelAttribute="txnSettlementDTO">

									<div id="errMsg" class="error"></div>
									<div class="row">
									<div class="col-md-3">
										<div class="form-group">
											<label for="squareSelect">From Date<span class="red">*</span>
											</label> <form:input type="text" path="fromDate" id="fromDate"
												name="fromDate" class="form-control input-square" />
											<div>
												<span id="errFromDate" class="error"></span>
												<form:errors path="fromDate" class="error" />
											</div>
											<div>
												<span id="dterr" class="error"></span>
												<form:errors path="fromDate" class="error" />
											</div>
										</div>
									</div>

									<div class="col-md-3">
										<div class="form-group">
											<label for="squareSelect">To Date<span class="red">*</span>
											</label> <form:input type="text" path="toDate" id="toDate" name="toDate"
												class="form-control input-square" />
											<div>
												<span id="errToDate" class="error"></span>
												<form:errors path="toDate" class="error" />
											</div>
										</div>
									</div>


									<div class="col-md-3">
										<div class="form-group">
											<label for="squareInput">RRN(Retrieval Reference
												Number) </label>
											<form:input path="rrn" class="form-control " id="rrn"
												maxlength="12" />
											<div id="errrrn" class="error" style="color: red"></div>
										</div>
									</div>

									<div class="col-md-3">
										<div class="form-group">
											<label for="squareInput">Pan/Token Pan </label>
											<form:input path="tokenPan" class="form-control "
												id="tokenPan" maxlength="19" />
											<div id="errtokenPan" class="error" style="color: red"></div>
										</div>
									</div>
									</div>

								<div class="row">
									<div class="col-md-3">
										<div class="form-group">
											<label for="squareSelect">Function Code</label>
											<form:select path="searchedActionCode"
												id="searchedActionCode" class="form-control">
												<form:option value="">
													<spring:message code="msg.lbl.select"></spring:message>
												</form:option>
												<%-- <form:options itemLabel="functionCodeDescription" itemValue="actionCode"
												items="${funcCodeList}" /> --%>
												<c:forEach items="${funcCodeList}" var="functionCode">
													<form:option value="${functionCode.key}"
														label="${functionCode.value}"></form:option>
												</c:forEach>
											</form:select>
											<div>
												<span id="funcCodeErr" class="error"></span>
												<form:errors path="funcCode" class="error" />
											</div>
										</div>
									</div>

									<div class="col-md-3">
										<div class="form-group">
											<label for="squareSelect">Participant Id</label>
											<form:select path="partId" id="partId" class="form-control">
												<form:option value="0">
													<spring:message code="msg.lbl.select"></spring:message>
												</form:option>
												<form:options itemLabel="bankName" itemValue="participantId"
													items="${participantlist}" />
											</form:select>
											<div>
												<span id="partIdErr" class="error"></span>
												<form:errors path="partId" class="error" />
											</div>
										</div>
									</div>


									
									
									<c:choose>
										<c:when test="${networkType eq 'Y' }">
									<div class="col-md-3">
										<div class="form-group ">
											<label for="squareSelect">Scheme Code</label>
											<form:select path="schemeCodeBank"
												id="schemeCodeBank" class="form-control">
												<form:option value="">
													<spring:message code="msg.lbl.select"></spring:message>
												</form:option>
												<c:forEach items="${schemeCodeList}" var="schemeCodes">
													<form:option value="${schemeCodes.code}"
														label="${schemeCodes.value}"></form:option>
												</c:forEach>
											</form:select>
											<div>
												<span id="errschemeCodeBank" class="error"></span>
												<form:errors path="schemeCodeBank" class="error" />
											</div>
										</div>
									</div>
										</c:when>


										<c:otherwise>
										
									<div class="col-md-3">
										<div class="form-group">
											<label for="squareInput">CRN</label>
											<form:input path="complaintNumber" class="form-control "
												id="crn" maxlength="16" />
											<div id="errcrn" class="error" style="color: red"></div>
										</div>
									</div>
										</c:otherwise>
									</c:choose>
									
								</div>
								<div class="row">
									<div class="col-md-12" style="text-align:center">
										<div class="form-group">
											<button class="btn btn-primary" type="button"
												id="searchButon" style="margin-top: 25px;">Search</button>
											<button class="btn btn-default" id="resetBtn" type="button">Reset</button>
										</div>
									</div>
								</div>
							</form:form>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-12" id="tableList" hidden>
				<div class="card-white table-responsive"">
				
				<div class="row">
				<div class="col-sm-12">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="ifsc.clearFiltersBtn" />
					</button>


					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="excelExport"><spring:message code="ifsc.exportBtn" /> </a>
						
						&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="csvExport"><spring:message code="ifsc.csvBtn" /> </a>
						

				</div>
			</div>
					<table id="example" class="table table-striped table-bordered"
						style="width: 100%">
						<thead>
							<tr>
								<th>Pan/Token Pan</th>
								<th>Local Date/Time</th>
								<th>Acquirer Reference Data/RRN</th>
								<th>Processing Code</th>
								<th>Currency Code,Transaction</th>
								<th>Amount Transaction</th>
								<th>Amount Additional</th>
								<th>Card Acceptor Name</th>
								<th>Response Code</th>
							</tr>
						</thead>
						<tbody >
							
						</tbody>
					</table>
				</div>



			</div>
		</div>
	</div>
</div>


<!-- Modal -->
<div class="modal fade" id="exampleModalLong" data-backdrop="static"
	tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle"
	aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered modal-lg"
		role="document">
		<div class="modal-content">
			<div class="modal-header">
				<span class="modal-title" id="exampleModalLongTitle">Transaction
					Details</span>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="col-md-12">
						<div class="card">
							<div class="card-header">
								<div class="card-title">Transaction Summary</div>
							</div>
							<div class="card-body">

								<div class="row">
									<div class="col-md-6">
										<form class="form-inline"
											style="border-right: solid 1px #bcd1f1;">
											<div class="form-group">
												<label>Primary Account Number</label> <input type="text"
													class="form-control" id="txnId">
											</div>
											<!-- 	<div class="form-group">
													<label>RRN/Transaction ID</label> <input type="text"
														class="form-control" id="trnDate">
												</div> -->
											<div class="form-group">
												<label>Beneficiary PSP</label> <input type="text"
													class="form-control" id="currStatusCode">
											</div>
											<div class="form-group">
												<label>Payer PSP</label> <input type="text"
													class="form-control" id="custRefNo">
											</div>
											<div class="form-group">
												<label>Originator</label> <input type="text"
													class="form-control" id="prIfscCode">
											</div>
											<div class="form-group">
												<label>Status</label> <input type="text"
													class="form-control" id="txnType">
											</div>
											<div class="form-group">
												<label>UPI Transaction ID</label> <input type="text"
													class="form-control" id="txnSubType">
											</div>
											<div class="form-group">
												<label for="exampleInputEmail2">Processed in Cycle</label> <input
													type="text" class="form-control" id="pyIfscCode">
											</div>

										</form>
									</div>



									<div class="col-md-6">
										<form class="form-inline">
											<div class="form-group">
												<label>Date &amp; Time</label> <input type="text"
													class="form-control" id="txnAmount">
											</div>
											<div class="form-group">
												<label>Transaction Amount</label> <input type="text"
													class="form-control" id="pyeMemeberId">
											</div>
											<div class="form-group">
												<label>Remitter PSP</label> <input type="text"
													class="form-control" id="pyrMemeberId">
											</div>
											<div class="form-group">
												<label>Payee PSP</label> <input type="text"
													class="form-control" id="drMemberId">
											</div>
											<div class="form-group">
												<label>Destination</label> <input type="text"
													class="form-control" id="crMemeberId">
											</div>
											<div class="form-group">
												<label>Action Code</label> <input type="text"
													class="form-control" id="" value="Approved" placeholder="">
											</div>
											<div class="form-group">
												<label>Type of UPI Transaction</label> <input type="text"
													class="form-control" id="" value="Pay" placeholder="">
											</div>
										</form>
									</div>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer text-center btnDiv">
				<button type="button" class="btn btn-primary" style="display: none;"
					id="actionButton" data-value="">Charge Back</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal">Back</button>
			</div>
		</div>
	</div>
</div>
<!-- </body> -->
</html>