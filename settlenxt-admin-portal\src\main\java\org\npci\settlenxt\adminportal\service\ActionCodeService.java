package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.ActionCodeDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;

public interface ActionCodeService  {

	List<ActionCodeDTO> getPendingForApprovalActionCode();

	List<CodeValueDTO> getFuncCodeList();

	ActionCodeDTO addEditActionCode(ActionCodeDTO actionCodeDTO);

	ActionCodeDTO getActionCodeInfoFromMain(int actionCodeId);

	ActionCodeDTO getActionCode(int parseInt);

	List<ActionCodeDTO> getApprovedActionCodeFromMain();

	ActionCodeDTO approveOrRejectActionCode(int actionCodeId, String status, String remarks);

	ActionCodeDTO getActionCodeFromMainEdit(int actionCodeId);

	int updateActionCode(ActionCodeDTO actionCodeDTO);

	ActionCodeDTO discardActionCode(int actionCodeId);

	ActionCodeDTO updateApproveOrRejectBulkActionCode(String bulkActionCodeIdList, String status, String remarks);

	List<CodeValueDTO> getMccFromFuncCode();

	List<CodeValueDTO> getActionCodeList();

	boolean checkDistinctActioncode(String actionCode);

	List<CodeValueDTO> getReasonCodeList();

}