package org.npci.settlenxt.adminportal.service;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.ErrorConstants;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.exception.SettleNxtSystemException;
import org.npci.settlenxt.portal.common.util.FileNameUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class MemberFileServiceImpl implements MemberFileService {
	@Autowired
	private Environment environment;
	private static final String[] ALLOWED_FILE_EXTENSIONS = { "pdf", "jpg", "doc", "docx", "csv", "png", "txt", "jpeg",
			"bmp", "tiff", "tif" };

	private Path getUploadDirectory() {
		String basePath = environment.getProperty("FILE_UPLOAD_PATH");

		try {
			
			Path baseDir = Paths.get(basePath);
			if (!Files.isDirectory(baseDir)) {
				Files.createDirectory(baseDir);
			}
			return baseDir;
		} catch (IOException e) {
			throw new SettleNxtSystemException(ErrorConstants.GENERIC_ERROR_CODE,
					"Could not initialize folder for upload!", e);
		}
	}

	@Override
	public List<FileUploadDTO> saveDocuments(MultipartFile[] files, String bankMasterCode) {
		try {
			Path uploadDir = getUploadDirectory();
			SimpleDateFormat sdf = new SimpleDateFormat("ddMMyyyyHHmmss");
			Calendar calendar = Calendar.getInstance();

			List<String> invalidFileNames = new ArrayList<>();
			List<FileUploadDTO> uploadedDocuments = new ArrayList<>();
			for (MultipartFile multipartFile : files) {

				if (!StringUtils.endsWithAny(StringUtils.lowerCase(multipartFile.getOriginalFilename()),
						ALLOWED_FILE_EXTENSIONS)) {
					invalidFileNames.add(multipartFile.getOriginalFilename());
				}

			}
			if (!CollectionUtils.isEmpty(invalidFileNames)) {
				throw new SettleNxtApplicationException(ErrorConstants.MEMBER_DOCUMENT_INVALID_FILETYPE_ERROR_CODE,
						ErrorConstants.MEMBER_DOCUMENT_INVALID_FILETYPE_ERROR_MESSAGE + ":"
								+ StringUtils.join(invalidFileNames, ","));
			}
			Integer i = 0;
			for (MultipartFile multipartFile : files) {
				String filePrefix = FileNameUtil.generateDelimiteredString("_", bankMasterCode,
						sdf.format(calendar.getTime()));

				FileUploadDTO uploadedDocument = new FileUploadDTO();
				String documentName = multipartFile.getOriginalFilename();

				filePrefix = filePrefix + i.toString();
				i = i + 1;
				String destinationFilePath = FileNameUtil.generateDelimiteredString("_", filePrefix,
						FileNameUtil.sanitizeFileName(documentName));
				String sanitizedDestinationFilePath = sanitizePath(destinationFilePath);
				Files.copy(multipartFile.getInputStream(), uploadDir.resolve(sanitizedDestinationFilePath));
				uploadedDocument.setDocumentName(documentName);

				uploadedDocument.setDocumentPath(sanitizedDestinationFilePath);
				uploadedDocuments.add(uploadedDocument);
			}
			return uploadedDocuments;
		} catch (SettleNxtException settleNxtException) {
			throw settleNxtException;
		} catch (Exception e) {
			throw new SettleNxtSystemException(ErrorConstants.MEMBER_DOCUMENT_UPLOAD_FAILURE_ERROR_CODE,
					ErrorConstants.MEMBER_DOCUMENT_UPLOAD_FAILURE_ERROR_MESSAGE, e);
		}
	}

	public static String sanitizePath(String path) {
		
		String sanitizedPath = path.replaceAll("[\\\\/:*?\"<>|]", "_").replaceAll("\\.+", ".");
		
		Path normalizedPath = Paths.get(sanitizedPath).normalize();
		
		Path sanitizedNormalizedPath = FileSystems.getDefault().getPath("").relativize(normalizedPath);
		return sanitizedNormalizedPath.toString();
	}
}
