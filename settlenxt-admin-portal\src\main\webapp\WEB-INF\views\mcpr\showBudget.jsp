<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript">
	var actionColumnIndex = 3;
	var firstColumnToBeSkippedInFilterAndSort = false;
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 6;
	firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 5;
	firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
</script>
<script>
	var referenceNoListPendings = [];
	<c:if test="${not empty pendingBudgetList}">
	<c:forEach items="${pendingBudgetList}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	referenceNoListPendings.push('${operator.budgetId}');
	</c:if>
	</c:forEach>
	</c:if>
</script>

<script src="./static/js/validation/mcpr/viewBudget.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>

<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>
<!-- Model -->
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalNews" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Budget
					Approval/Rejection</label>
				<p id="newsIds" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulkBudget('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulkBudget('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Model -->

<input:hidden id="refNum" />
<div class="row">
	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showBudget eq 'YES' }">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" onclick="submitForm('/showBudget');" role="tab"
			data-toggle="tab"> <span class="glyphicon glyphicon-credit-card">&nbsp;</span>
			<spring:message code="budget.mainTab.title" />
		</a>

		<c:choose>
			<c:when test="${showPendingBudget eq 'YES'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#profile" role="tab"
			onclick="submitForm('/budgetPendingForApproval');" data-toggle="tab">
			<span class="glyphicon glyphicon-ok">&nbsp;</span> <spring:message
				code="binexcl.approvalPanel.title" />
		</a>
	</ul>

	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
			<div class="col-sm-12">
			<sec:authorize access="hasAuthority('Add Budget')">
									<c:if test="${addBudget eq 'Yes'}">
										<a class="btn btn-success pull-right btn_align" href="#"
											onclick="submitForm('/createBudget');"
											style="margin-top: -5px 0px 2px 0px;"><em class="glyphicon-plus"></em>
											Add Budget</a>
									</c:if>
								</sec:authorize>
								</div>
				<div class="col-sm-12">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="ifsc.clearFiltersBtn" />
					</button>
					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="csvExport"> <spring:message code="ifsc.csvBtn" /></a> <a
						class="btn btn-success pull-right btn_align" href="#"
						id="excelExport">Excel</a>
				</div>
			</div>
			<c:if test="${showBudget eq 'YES'}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="budget.viewscreen.title" /></span></strong>
								
							</div>
							<div class="panel-body">

								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Budget</caption>
										<thead>
											<tr>
												<th scope = "col"><spring:message code="budget.vendor" /></th>
												<th scope = "col"><spring:message code="budget.budgetName" /></th>
												<th scope = "col"><spring:message code="budget.year" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="eachbudget" items="${budgetList}">

												<tr>
													<td
														onclick="javascript:viewBudget('${eachbudget.budgetId}','P')">${eachbudget.vendor}</td>
													<td
														onclick="javascript:viewBudget('${eachbudget.budgetId}','P')">${eachbudget.budget}</td>
													<td
														onclick="javascript:viewBudget('${eachbudget.budgetId}','P')">${eachbudget.displayYear}</td>
												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>

			<c:if test="${pendingBudget eq 'YES'}">

				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="budget.mainTab.title" /></span></strong>
								<c:if test="${not empty pendingBudget}">
									<sec:authorize access="hasAuthority('Approve Budget')">
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkBudget('A','No')"
											id="submitButtonA"
											value="<spring:message code="feeRate.Approve" />" />
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkBudget('R','No')"
											id="submitButtonR"
											value="<spring:message code="feeRate.Reject" />" />
									</sec:authorize>
								</c:if>
							</div>
							<div class="panel-body">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Budget</caption>
										<thead>
											<tr>
												<sec:authorize access="hasAuthority('Approve Budget')">
													<th scope = "col"><input type=checkbox name='selectAllCheck'
														id="selectAll" data-target="toggleModalNews" value="All"></input></th>
												</sec:authorize>
												<th scope = "col"><spring:message code="budget.vendor" /></th>
												<th scope = "col"><spring:message code="budget.budgetName" /></th>
												<th scope = "col"><spring:message code="budget.year" /></th>
												<th scope = "col"><spring:message code="budget.requestStatus" /></th>
												<th scope = "col"><spring:message code="binexcl.checkerComments" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="eachbudget" items="${pendingBudgetList}">
												<tr>
													<sec:authorize access="hasAuthority('Approve Budget')">
														<c:if test="${eachbudget.requestState =='P' }">
															<td><input type=checkbox name='type'
																id="selectSingle" onclick="mySelect();"
																value="${eachbudget.budgetId}"></input></td>
														</c:if>
														<c:if test="${eachbudget.requestState !='P' }">
															<td></td>
														</c:if>
													</sec:authorize>
													<td
														onclick="javascript:viewBudget('${eachbudget.budgetId}','G')">${eachbudget.vendor}</td>
													<td
														onclick="javascript:viewBudget('${eachbudget.budgetId}','G')">${eachbudget.budget}</td>
													<td
														onclick="javascript:viewBudget('${eachbudget.budgetId}','G')">${eachbudget.displayYear}</td>
													<td
														onclick="javascript:viewBudget('${eachbudget.budgetId}','G')">
														<c:if test="${eachbudget.requestState=='P' }">
															<spring:message
																code="budget.requestState.pendingApproval.description" />
														</c:if> <c:if test="${eachbudget.requestState=='R' }">
															<spring:message
																code="budget.requestState.rejected.description" />
														</c:if>
													</td>
													<td
														onclick="javascript:viewBudget('${eachbudget.budgetId}','G')">${eachbudget.checkerComments}</td>

												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>

		</div>

	</div>



</div>
