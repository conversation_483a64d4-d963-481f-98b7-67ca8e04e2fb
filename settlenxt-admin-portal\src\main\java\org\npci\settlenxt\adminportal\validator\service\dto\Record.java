package org.npci.settlenxt.adminportal.validator.service.dto;

import java.text.SimpleDateFormat;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.mapping.DataElementNameMapping;
import org.npci.settlenxt.adminportal.common.mapping.NameMappingContext;
import org.npci.settlenxt.portal.common.util.Utils;

public class Record {
	private String name;
	private String value;
	private String format;
	private String regexPattern;
	private String type;
	private String size;
	private int minLength;
	private int maxLength;
	
	private static final Logger logger = LogManager.getLogger(Record.class);
	public static final String VALUE_NUMERIC = "N";
	
	public static final String VALUE_STRING = "S";
	
	public static final String VALUE_ALPHA_NUMERIC = "AN";
	
	DataElementNameMapping dataElementMapping;
	
	public Record() {
		super();
	}

	public Record(String name, String value) {
		super();
		this.name = name;
		this.value = value;
		dataElementMapping = CustomMappingUtils.getDataElementNameMapping(NameMappingContext.DATAELEMENTS, name);
	}

	public Record(String name, String value, String format,
			String regexPattern, String type, String size) {
		super();
		this.name = name;
		this.value = value;
		this.format = format;
		this.regexPattern = regexPattern;
		this.type = type;
		this.size = size;
		dataElementMapping = CustomMappingUtils.getDataElementNameMapping(NameMappingContext.DATAELEMENTS, name);
	}
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;

		dataElementMapping = CustomMappingUtils.getDataElementNameMapping(NameMappingContext.DATAELEMENTS, name);
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getFormat() {
		return format;
	}
	public void setFormat(String format) {
		this.format = format;
	}
	public String getRegexPattern() {
		return regexPattern;
	}
	public void setRegexPattern(String regexPattern) {
		this.regexPattern = regexPattern;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getSize() {
		return size;
	}
	public void setSize(String size) {
		this.size = size;
	}
	
	public boolean isRecordValid(){
		boolean validRecord = false;
		dataElementMapping = CustomMappingUtils.getDataElementNameMapping(NameMappingContext.DATAELEMENTS, name);
		if(dataElementMapping != null){
			validRecord = isLengthValid() && isPatternValid();
			
		}
		return validRecord;
	}
	
	public boolean isLengthValid() {
		boolean isLengthValid = true;
		int minLen = getMinLength();
		int maxLen = getMaxLength();
		int recordLen = value.length();
		
		if(StringUtils.isBlank(value))
			{
			isLengthValid = false;
			}
		if(recordLen < minLen || recordLen > maxLen)
			{
			isLengthValid = false;
			}
	
		return isLengthValid;
	}
	
	public boolean isPatternValid() {
		boolean isPatternValid = false;
		this.regexPattern = dataElementMapping.getRegexPattern();
		this.type = dataElementMapping.getDataType();
		String dateFormat = dataElementMapping.getDateFormat();
		if(!"D".equals(type)){
		
		if(StringUtils.isBlank(this.regexPattern))
		{
			isPatternValid = true;
		}
		else
			{
			isPatternValid = Utils.isValueMatchesPattern(regexPattern, value);
			}
		}else{
			if(StringUtils.isBlank(this.regexPattern) && StringUtils.isBlank(dateFormat)){
				isPatternValid = true;
			}else{
				isPatternValid = Utils.isValueMatchesPattern(regexPattern, value);
				if(isPatternValid){
					isPatternValid = this.isDatePatternValid();
				}
			}
			
		}
		return isPatternValid;
	}
	
	public boolean isDatePatternValid() {
		boolean isDatePatternValid = false;
		this.type = dataElementMapping.getDataType();
		if(!"D".equals(type))
			{
			isDatePatternValid = true;
			}
		else{
			String dateFormat = dataElementMapping.getDateFormat();
			if(StringUtils.isBlank(dateFormat)){
				isDatePatternValid = true;
			}else{
				try{
				    if(StringUtils.isNotBlank(value)){
				    SimpleDateFormat dateFormatter = new SimpleDateFormat(dateFormat);
				    dateFormatter.setLenient(false);
				   					
				    isDatePatternValid = true;
				    }
				}
				catch(Exception ee){
					isDatePatternValid = false;
				}
			}
		}
			
		return isDatePatternValid;
	}

	public int getMinLength() {
		String minLen = null;
		try {
			minLen = dataElementMapping.getMinLength();
		} catch (Exception ex) {
			logger.error("Exception",ex);
		}
		if(StringUtils.isBlank(minLen)){ 
			return 0;
		}else{
			try{
				if(!Utils.isInteger(minLen))
					{
					return 0;
					}
				else
					{
					minLength = Utils.parseInt(minLen);
					}
			}catch(Exception e){
				return 0;
			}
		}	
		return minLength;
	}

	public void setMinLength(int minLength) {
		this.minLength = minLength;
	}

	public int getMaxLength() {
		String maxLen = dataElementMapping.getMaxLength();
		if(StringUtils.isBlank(maxLen)){ 
			return 0;
		}else{
			try{
				if(!Utils.isInteger(maxLen))
					{
					return 0;
					}
				else
					{
					maxLength = Utils.parseInt(maxLen);
					}
			}catch(Exception e){
				return 0;
			}
		}
		return maxLength;
	}

	public void setMaxLength(int mxLength) {
		this.maxLength = mxLength;
	}
	
	public DataElementNameMapping getMapping() {
		return dataElementMapping;
	}

	@Override
	public String toString() {
		return "Record [name=" + name + ", value=" + value + ", format=" + format + ", regexPattern=" + regexPattern
				+ ", type=" + type + ", size=" + size + ", minLength=" + minLength + ", maxLength=" + maxLength
				+ ", dataElementMapping=" + dataElementMapping + "]";
	}
	
	
}
