$(document).ready(function () {
	if (document.getElementById("editFlow").value == "Y") {
		$('form')
		.each(function(){
			$(this).data('serialized', $(this).serialize())
		})
		.on('change input', function(){
		$(this)
		.find('input:submit, button:submit')
					.prop('disabled', $(this).serialize() == $(this).data('serialized'));
			$('#bEdit').prop('disabled', false);
			})
	$('#bEdit').prop('disabled', true);
	}
	var valueFlag = $('#capAmountFlag').val();

	setAmtFlatPerc(valueFlag);

	$('#clearCappingAmt').click(function() {
		$('#capAmountFlag').val("0")
		$('#actionCode').val("0")
		$('#mccGroup').val("0")
		$('#capName').val("0")
		$('#capType').val("")
		$('#fieldName').val("0")
		$('#relOperator').val("0")
		$('#amountCapFlat').val("")
		$('#amountCapPercent').val("")
		$("#binCardBrandId option:selected").prop("selected", false);
		$("#binCardTypeId option:selected").prop("selected", false);
		$('#fieldValue').val("")
		$('#amountCapMax').val("")
		$('#amountCapMin').val("")
		$('.jqueryError').hide();

		$('#funcCode').val("0")

		$('#status').val("A")
		$('#priority').val("0")

		$("#binCardBrandId").multiselect("rebuild");
		$("#binCardBrandId").multiselect("refresh");
		document.getElementById("binCardBrandId").value = "SELECT";
		
		$("#binCardTypeId").multiselect("rebuild");
		$("#binCardTypeId").multiselect("refresh");
		document.getElementById("binCardTypeId").value = "SELECT";
		
		$("#errbinCardBrandId").hide();
		$("#errcapAmountFlag").hide();
		$("#errbinCardBrandId").hide();
		$("#errbinCardTypeId").hide();
		$("#errpriority").hide();
		$("#errstatus").hide();
		$("#erractionCode").hide();
		$("#errrelOperator").hide();
		$("#errfieldName").hide();
		$("#errcapName").hide();
		$("#errmccGroup").hide();
		$("#errfieldValue").hide();
		$("#erramountCapMin").hide();
		$("#erramountCapMax").hide();
		$("#erramountCapFlat").hide();
		$("#errtoMonth").hide();
		$("#errcapType").hide();
		$("#erramountCapPercent").hide();

			});

	   $('#capType').on('keyup keypress blur change',function () {
           validateFromCommonVal('capType', true, "AlphaNumericNoSpace", 20, false);
          });
	$('#capName').on('change',function () {
		
		$('#capType').val($('#capName').val())
		
	});
	
	$('#capAmountFlag').on('change',function () {
		
	var value=	$('#capAmountFlag').val();
	$("#amtPerc,#amtFlat").show();
	
	setAmtFlatPerc(value);
	
		
	});



	$('#amountCapFlat').on('keyup keypress blur change',function () {
	
		validateFromCommonVal('amountCapFlat', true, "NumericwithPrecisionwith0From10to2", 11 , false);
	});

	$('#amountCapPercent').on('keyup keypress blur change',function () {
		validateFromCommonVal('amountCapPercent', true, "NumericwithPrecisionwith0From2to2", 11 , false);
	});

	$('#capAmountFlag').on('keyup keypress blur change',function () {
		validateFromCommonVal('capAmountFlag', true, "SelectionBox", 20, false);
	});

		$('#actionCode').on('keyup keypress blur change',function () {
        validateFromCommonVal('actionCode', true, "SelectionBox", 20, false);
       });
       
       $('#capName').on('keyup keypress blur change',function () {
        validateFromCommonVal('capName', true, "SelectionBox", 20, false);
       });
           
       $('#mccGroup').on('keyup keypress blur change',function () {
        validateFromCommonVal('mccGroup', true, "SelectionBox", 20, false);
       });
       
       $('#fieldName').on('keyup keypress blur change',function () {
        validateFromCommonVal('fieldName', true, "SelectionBox", 20, false);
       });
       
       $('#relOperator').on('keyup keypress blur change',function () {
        validateFromCommonVal('relOperator', true, "SelectionBox", 20, false);
       });
       
       $('#fieldValue').on('keyup keypress blur change',function () {
        validateFromCommonVal('fieldValue', true, "AlphaNumericWithSpace", 20, false);
       });
       
       
	$('#amountCapMax').on('keyup keypress blur change',function () {
		validateFromCommonVal('amountCapMax', false, "NumericwithPrecisionwith0From10to2", 11 , false);
	});

	$('#amountCapMin').on('keyup keypress blur change',function () {
		validateFromCommonVal('amountCapMin', false, "NumericwithPrecisionwith0From10to2", 11 , false);
	});
	


	
	

	setShowBtnProps();


	$('#amountCapPercent').on('change', function(){
		$('#amtcapmin').show();
	});



	$('#binCardBrandId').multiselect({
		buttonWidth : '280px',
		paddingLeft: '0px',
		paddingRight: '15px',
		nonSelectedText:'Select  Id',
		includeSelectAllOption: true,
		enableFiltering: true,
		
		onChange : function(selected) {
			
			if (selected) {
		
				
			$('#errbinCardBrandId').hide();
				$('#errbinCardBrandId').find('.error').html('');
								 
			} 
			else{
			//not selected
			}
		},
	});
	
		$('#binCardTypeId').multiselect({
		buttonWidth : '280px',
		paddingLeft: '0px',
		paddingRight: '15px',
		nonSelectedText:'Select binCardTypeId Id',
		includeSelectAllOption: true,
		enableFiltering: true,
		
		onChange : function(selected) {
			
			if (selected) {
		
			$('#errbinCardTypeId').hide();
				$('#errbinCardTypeId').find('.error').html('');

								 
			} 
			else{
			//not selected
			}
		},
	});
    setEventListenerOn('binCardBrandId');
    setEventListenerOn('binCardTypeId');

	var edit = document.getElementById('amountCapPercent').value;

	if(edit!="")
	{
		$('#amtcapmin').show();
	}


	if(document.getElementById("editMccId")!=null){

		var y = document.getElementById("editMccId").value;
var dataarray=[];
		if(y!=null){
			 dataarray=y.split(",");

		}

		$('#binCardBrandId').multiselect('refresh');
		$('#binCardBrandId').multiselect('select', dataarray);

	}


if(document.getElementById("editMccTypeId")!=null){

		var y1 = document.getElementById("editMccTypeId").value;
	var dataarray2=[];
		if(y1!=null){
			 dataarray2=y1.split(",");

		}

		$('#binCardTypeId').multiselect('refresh');
		$('#binCardTypeId').multiselect('select', dataarray2);

	}

})



var ajaxValidPriority;



function setEventListenerOn(fieldId) {
	var field = $("#" + fieldId);

    field.on('keyup keypress change', function() {
        if (field.val().length != 0) {
            $("#err"+fieldId).hide();
            $("#err"+fieldId).find('.error').html('');
        }
        else {
            $("#err"+fieldId).show();
            $("#err"+fieldId).find('.error').html(validationMessages[fieldId]);

        }
    });
}

function setShowBtnProps() {
    if (($('#showButton').val() == "Y")) {
        $("input").prop('disabled', true);
        $("select").prop('disabled', true);
        if (document.getElementById("clearCappingAmt")) {
            document.getElementById("clearCappingAmt").disabled = true;
        }
    }
}

function setAmtFlatPerc(value) {
    if (value == 'P') {
        $("#amtFlat").hide();
    }
    if (value == 'F') {
        $("#amtPerc").hide();
    }
}

function callBackCappingAmt(flag){
	ajaxValidPriority=flag;
}


function checkDuplicateRecords(){

var actionCode= $('#actionCode').val();
var mccGroup = $('#mccGroup').val();
var binCardBrandId=$('#binCardBrandId').val().toString();
var binCardTypeId=$('#binCardTypeId').val().toString();
var relOperator = $('#relOperator').val();
var fieldName = $('#fieldName').val();
var fieldValue = $('#fieldValue').val();
var tokenValue = document.getElementsByName("_TransactToken")[0].value;
let flag2=false;
$.ajax({
			url: "checkDuplicateRecords",
			type: "POST",
			dataType: "json",
			data: {
				
				"actionCode":actionCode,
				"mccGroup":mccGroup,
				"binCardBrandId":binCardBrandId,
				"binCardTypeId":binCardTypeId,
				"relOperator":relOperator,
				"fieldName":fieldName,
				"fieldValue":fieldValue,
				"_TransactToken": tokenValue
			},
			async:false,
	        cache: false,
	        timeout: 30000,
			success: function(response) {

				if (response.status == "BSUC_0001") {
				
				$('#errorStatus').html("Data already exists");
				$('#jqueryError').show();
				$('#jquerySuccess').hide();
				flag2=true;
				
				}
				else{
				
					$('#errorStatus').html("");
					 $('#jquerySuccess').hide();
					 $('#jqueryError').hide();
					flag2=false;
				}
				},
			error: function(_request, _status, _error) {
				 $('#errorStatus').html("");
				 $('#jqueryError').hide();
			}
		});
					
return flag2;


}



function validateCappingAmt(action){



var value=	$('#capAmountFlag').val();
	var check = false;
	
	
	
	if (($('#editFlow').val() != "Y")&&!check)
	{
		check = checkDuplicateRecords()	;
	

	
	
	}
		
	check = showErrMsgForBrandId(check);
	
	check = showErrMsgForCardTypeId(check);
		
	check = validateFieldss(check);
	
	
	if(value=='P')
	{
		if (!validateFromCommonVal('amountCapFlat',false,"NumericwithPrecisionwith0From10to2", 11, false))
			 {
				check = true;
			 }
		if (!validateFromCommonVal('amountCapPercent',true,"NumericwithPrecisionwith0From2to2", 11, false))
		 {
		check = true;
	     }
	
	} else if(value=='F'){
		
		if (!validateFromCommonVal('amountCapFlat',true,"NumericwithPrecisionwith0From10to2", 11, false))
			 {
				check = true;
			 }
		if (!validateFromCommonVal('amountCapPercent',false,"NumericwithPrecisionwith0From2to2", 11, false))
		 {
		check = true;
	     }
	
	} else {
		
			if (!validateFromCommonVal('amountCapFlat',true,"NumericwithPrecisionwith0From10to2", 11, false))
					 {
						check = true;
					 }
			if (!validateFromCommonVal('amountCapPercent',true,"NumericwithPrecisionwith0From2to2", 11, false))
				 {
				check = true;
			     }
			
		
	}	
	if (!validateFromCommonVal('amountCapMax', 
			false, "NumericwithPrecisionwith0From10to2", 11, false)) {
		check = true;
	}


	if($('#amountCapPercent').val() != "" && !validateFromCommonVal('amountCapMin',
				false, "NumericwithPrecisionwith0From10to2", 11, false)) {
				
			check = true;
		
	}

	if (!check) {

		addOrUpdateCappingAmount(action)
	}else{
		return false;
	}
}


function showErrMsgForCardTypeId(check) {
    if (($('#editFlow').val() != "Y")) {
        if ($('#binCardTypeId').val().length == 0) {
            check = true;

            $('#errbinCardTypeId').show();
            $('#errbinCardTypeId').find('.error').html(validationMessages['binCardTypeId']);
        }
        else {

            $('#errbinCardTypeId').hide();
        }
    }
    return check;
}

function showErrMsgForBrandId(check) {
    if (($('#editFlow').val() != "Y")) {
        if ($('#binCardBrandId').val().length == 0) {
            check = true;

            $('#errbinCardBrandId').show();
            $('#errbinCardBrandId').find('.error').html(validationMessages['binCardBrandId']);
        }
        else {

            $('#errbinCardBrandId').hide();
        }
    }
    return check;
}

function validateFieldss(check) {
    if (!validateFromCommonVal('capAmountFlag',
        true, "SelectionBox", "", false)) {

        check = true;

    }

    if (!validateFromCommonVal('actionCode',
        true, "SelectionBox", "", false)) {

        check = true;

    }

    if (!validateFromCommonVal('capName',
        true, "SelectionBox", "", false)) {

        check = true;

    }
    if (!validateFromCommonVal('mccGroup',
        true, "SelectionBox", "", false)) {

        check = true;

    }
    if (!validateFromCommonVal('capType',
        true, "AlphaNumericNoSpace", "", false)) {

        check = true;

    }


    if (!validateFromCommonVal('fieldName',
        true, "SelectionBox", "", false)) {

        check = true;

    }

    if (!validateFromCommonVal('relOperator',
        true, "SelectionBox", "", false)) {

        check = true;

    }



    if (!validateFromCommonVal('fieldValue',
        true, "AlphaNumericWithSpace", "", false)) {
        check = true;
    }
    return check;
}

function addOrUpdateCappingAmount(action){


var binCardTypeIdList = "";
var binCardBrandIdList = "";


	if($('#binCardBrandId').val()!=0){

		var a = $('#binCardBrandId option:selected').toArray().map(item => item.value).join();

		var arr1= a.split(",");

	
 		for (var value of arr1) {
			binCardBrandIdList = binCardBrandIdList + value + "|";
		}

	}
	
		if($('#binCardTypeId').val()!=0){

		var b = $('#binCardTypeId option:selected').toArray().map(item => item.value).join();

		var arr2= b.split(",");


		

		
			for (var val of arr2) {
			binCardTypeIdList = binCardTypeIdList + val + "|";
		}


	}
	
	var data = "actionCode," + $('#actionCode').val()  + ",mccGroup," + $('#mccGroup').val()+",capName," + $('#capName').val()+ ",capType," + $('#capType').val() +",fieldName," + $('#fieldName').val() +",relOperator," + $('#relOperator').val() +",fieldValue," + $('#fieldValue').val() + ",amountFlag," + $('#capAmountFlag').val() + ",binCardBrandId," + binCardBrandIdList+ ",binCardTypeId," + binCardTypeIdList+ ",flat," + $('#amountCapFlat').val() +  ",amountCapPercent," + $('#amountCapPercent').val() + ",amountCapMax," + $('#amountCapMax').val() + ",amountCapMin," + $('#amountCapMin').val() ;                                 

	postData(action, data);

}

function submitForm(url) {
	
	var data = "" ;
	postData(url, data);
}
function userAction(actionCode,mccGroup,binCardBrandId,binCardTypeId,fieldName,relOperator,fieldValue,action) {

var brandId = binCardBrandId;
var typeId = binCardTypeId;
	let url = action;
	 var data = "actionCode," + actionCode +  ",mccGroup," + mccGroup +  ",binCardBrandId," + escape(brandId) +  ",binCardTypeId," + escape(typeId) +  ",fieldName," + fieldName +  ",relOperator," + relOperator +  ",fieldValue," + fieldValue ;
	
	postData(url, data);

}



function checkPriorityMapwithCappingId() {
	var validPriority = false;
	var priority =  $('#priority').val();

	var errPriority = document.getElementById('errpriority');
	var cappingId =  $('#cappingId').val();

	var tokenValue = document.getElementsByName("_TransactToken")[0].value;

	if(priority==''){
		$('#errpriority').show();	
		validPriority = true;	 
		errPriority.className = 'error';			
		errPriority.innerHTML ="Priority is mandatory and Numeric";

		return validPriority;
	}
	if (priority != "") {


		$.ajax({
			url : "checkPriorityMapwithCappingId",
			type : "POST",
			dataType : "json",
			data: {"priority" : priority,"cappingId" : cappingId,
				"_TransactToken" : tokenValue},
				success : function(response){
					if (response.status == "BSUC_0001") {
						validPriority = true;			
						errPriority.className = 'error';			
						errPriority.innerHTML = "Priority is been set for cappingId : "+response.cappingId ;
						$('#errpriority').show();
						callBackCappingAmt(true);
					} else {
						validPriority = false;
						errPriority.className = 'error';
						errPriority.innerHTML = "";
						callBackCappingAmt(false);
					}
				},
				error : function() {
					$('#errpriority').show();
					errPriority.className = 'error';
					errPriority.innerHTML = "";
					callBackCappingAmt(false);
				}
		});

	} else {
		validPriority=true;
	}
	
	return validPriority;
}





function disableSave()
{
if (typeof bEdit != "undefined") {
document.getElementById("bEdit").disabled = true;
}
}
function unableSave()
{
if (typeof bEdit != "undefined") {
document.getElementById("bEdit").disabled = false;
}
}
