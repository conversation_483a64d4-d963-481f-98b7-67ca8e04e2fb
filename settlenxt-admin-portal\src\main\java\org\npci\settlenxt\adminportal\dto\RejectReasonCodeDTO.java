package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JGlobalMap
public class RejectReasonCodeDTO {
	
	private static final long serialVersionUID = 1L;
	
	private int seqId;
	private String funcCode;
	private String fieldName;
	private String fieldValue;
	private String relationalOperator;
	private String fieldOperator;
	private String subFieldName;
	private String rejectCode;
	private String status;
	private String statusCode;
	private String requestState;
	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private String lastOperation;
	private String checkerComments;
}
