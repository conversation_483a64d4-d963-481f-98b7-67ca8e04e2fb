<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.panTokenPanNo" /></label></td>
	<td id="pan"><c:if test="${empty disTxnList.maskPan}">N/A</c:if>${disTxnList.maskPan}</td>
	<td><label><spring:message
				code="txn.detail.lbl.acquirerReferenceData" /> / <spring:message
				code="txn.detail.lbl.rrn" /></label></td>
	<td id="rrn"><c:choose>
			<c:when test="${not empty disTxnList.acqRefData}">${disTxnList.acqRefData}</c:when>
			<c:otherwise>N/A</c:otherwise>
		</c:choose> <strong>/</strong> <c:choose>
			<c:when test="${not empty disTxnList.rrn}">${disTxnList.rrn }</c:when>
			<c:otherwise>N/A</c:otherwise>
		</c:choose></td>
</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.acquirerInstitutionIDCode" /></label></td>
	<td id="acquirerInstitutionIDCode"><c:if
			test="${empty disTxnList.instIdAcq}">N/A</c:if>${disTxnList.instIdAcq}</td>
	<td><label><spring:message
				code="txn.detail.lbl.cardAcceptorTerminalID" /></label></td>
	<td id="cardAcceptorTerminalID"><c:if
			test="${empty disTxnList.cardAcptTermId}">N/A</c:if>${disTxnList.cardAcptTermId}</td>

</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.amountTransaction" /></label></td>
	<td id="txnAmount"><c:choose>
			<c:when
				test="${not empty disTxnList.amtTran && not empty disTxnList.tranCurDesc }">${disTxnList.tranCurDesc} ${disTxnList.amountTran}</c:when>
			<c:otherwise>
				<c:choose>
					<c:when
						test="${not empty disTxnList.amtTran &&  empty disTxnList.tranCurDesc}">N/A ${disTxnList.amountTran}</c:when>
				</c:choose>
				<c:choose>
					<c:when test="${empty disTxnList.amtTran}">N/A</c:when>
				</c:choose>
			</c:otherwise>
		</c:choose></td>
	<td><label><spring:message
				code="txn.detail.lbl.currencyCodeTransaction" /></label></td>
	<td id="currencyCodeTransaction"><c:if
			test="${empty disTxnList.tranCur}">N/A</c:if>${disTxnList.tranCur} <c:if
			test="${not empty disTxnList.tranCurDesc}"> - ${disTxnList.tranCurDesc}</c:if>
	</td>
</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.transactionOriginatorInstitutionIDCode" /></label></td>
	<td id="transactionOriginatorInstitutionIDCode"><c:choose>
			<c:when
				test="${not empty disTxnList.txnOrgInstId && not empty disTxnList.txnOrgInstIdDesc }">${disTxnList.txnOrgInstId}-${disTxnList.txnOrgInstIdDesc}</c:when>
			<c:otherwise>
				<c:choose>
					<c:when
						test="${not empty disTxnList.txnOrgInstId &&  empty disTxnList.txnOrgInstIdDesc}">${disTxnList.txnOrgInstId}</c:when>
				</c:choose>
				<c:choose>
					<c:when test="${empty disTxnList.txnOrgInstId}">N/A</c:when>
				</c:choose>
			</c:otherwise>
		</c:choose>
	</td>
	<td><label><spring:message
				code="txn.detail.lbl.transactionDestinationInstitutionIDCode" /></label></td>
	<td id="transactionDestinationInstitutionIDCode"><c:choose>
			<c:when
				test="${not empty disTxnList.txnDestInstId && not empty disTxnList.txnDestInstIdDesc }">${disTxnList.txnDestInstId}-${disTxnList.txnDestInstIdDesc}</c:when>
			<c:otherwise>
				<c:choose>
					<c:when
						test="${not empty disTxnList.txnDestInstId &&  empty disTxnList.txnDestInstIdDesc}">${disTxnList.txnDestInstId}</c:when>
				</c:choose>
				<c:choose>
					<c:when test="${empty disTxnList.txnDestInstId}">N/A</c:when>
				</c:choose>
			</c:otherwise>
		</c:choose>
	</td>
</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.raiseDateAndTime" /></label></td>
	<td class="raiseDateAndTime" id="raiseDateAndTime"><c:if
			test="${empty disTxnList.tstampLocal}">N/A</c:if>${disTxnList.tstampLocal}</td>
	<td><label><spring:message
				code="txn.detail.lbl.currencyCodeSettlement" /></label></td>
	<td id="currencyCodeBilling"><c:choose>
			<c:when
				test="${not empty disTxnList.cardBillCur && not empty disTxnList.tranCurDesc }">${disTxnList.cardBillCur} - ${disTxnList.tranCurDesc}</c:when>
			<c:otherwise>
				<c:choose>
					<c:when
						test="${not empty disTxnList.cardBillCur &&  empty disTxnList.tranCurDesc}">${disTxnList.cardBillCur} - N/A </c:when>
				</c:choose>
				<c:choose>
					<c:when test="${empty disTxnList.cardBillCur}">N/A</c:when>
				</c:choose>
			</c:otherwise>
		</c:choose></td>
</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.amountSettlement" /></label></td>
	<td id="settlementAmount"><c:choose>
			<c:when
				test="${not empty disTxnList.settlementAmount && not empty disTxnList.tranCurDesc }">${disTxnList.tranCurDesc} ${disTxnList.settlementAmount}</c:when>
			<c:otherwise>
				<c:choose>
					<c:when
						test="${not empty disTxnList.settlementAmount &&  empty disTxnList.tranCurDesc}">N/A ${disTxnList.settlementAmount}</c:when>
				</c:choose>
				<c:choose>
					<c:when test="${empty disTxnList.settlementAmount}">N/A</c:when>
				</c:choose>
			</c:otherwise>
		</c:choose></td>
	<td><label><spring:message code="txn.detail.lbl.rgcsSettlementDate" /></label></td>
	<td class="rgcsSettlementDate"  id="rgcsSettlementDate"><c:if test="${empty disTxnList.netReconDate}">N/A</c:if>${disTxnList.netReconDate}</td>
</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.raiseUserId" /></label></td>
	<td id="raiseUserId"><c:if test="${empty disTxnList.makerId}">N/A</c:if>${disTxnList.makerId}</td>
	<td><label><spring:message code="txn.detail.lbl.checkerUserName" /></label></td>
	<td id="checkerUserName"><c:if test="${empty disTxnList.checkerId}">N/A</c:if>${disTxnList.checkerId}</td>

</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.internalTrackingNo" /></label></td>
	<td id="internalTrackingNumber"><c:if
			test="${empty disTxnList.internalTrackNo}">N/A</c:if>${disTxnList.internalTrackNo}</td>
	<td><label><spring:message
				code="txn.detail.lbl.processingCode" /></label></td>
	<td id="pcode"><c:if test="${empty disTxnList.pcodeDesc}">N/A</c:if>${disTxnList.pcodeDesc }</td>
</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.memberMsgText" /></label></td>
	<td id="memberMessageText"><c:if
			test="${empty disTxnList.memMsgTxt}">N/A</c:if>${disTxnList.memMsgTxt}</td>
	<td><label><spring:message
				code="txn.detail.lbl.reasonCodeAndDescription" /></label></td>
	<td class="reasonCodeAndDescription"><c:if
			test="${empty disTxnList.reasonCodeDesc}">N/A</c:if>${disTxnList.reasonCodeDesc }</td>
</tr>