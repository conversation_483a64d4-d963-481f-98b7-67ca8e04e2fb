	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});

function display() {
    $(".appRejMust").hide();

}

function userAction(action, currencyId) {
	
	var data = "currencyId," + currencyId ;
	postData(action, data);
}


function edit(action, currencyId,parentPage) {
	 
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "currencyId," + currencyId + ",_vTransactToken,"
			+ tokenValue +",parentPage," + parentPage;
	postData(action, data);
}


function backAction(_type, action) {
	var data = "status,"
			+ status;
	postData(action, data);
}

 function postAction(_action) {
	var data ="";
	var remarks ="";
	var currencyId;
	var url ="";
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 currencyId = $("#currencyId").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveCurrencyMaster';
				 data = "currencyId," + currencyId + ",status," + "A"  + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				 currencyId = $("#currencyId").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveCurrencyMaster';
				 data = "currencyId," + currencyId + ",status," + "R"+ ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
	
			 
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		