<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<title></title>
<script>
   function exportICCData(applId,applIdLnt,tranCurrencyCode,applIntrchgProf,dedicatedFileName,TS91LN,TS91,TS95,TS9A,TS9C,TS9F02,TS9F03,TS9F06,TS9F07,TS9F10,TS9F1A,TS9F1E,
		   TS9F26,TS9F27,TS9F33,TS9F34,TS9F35,TS9F36,TS9F37,TSAIDL,TSDYNL,TSPISI,TSRAKY,TSSTAN){
	  
	   
	   const dataMap = new Map([
		   ['',''],
		   ['TS4F- ICC Application ID', applId!=''?applId:"N/A"],
		   ['TS4FLN- N. A', applIdLnt!=''?applIdLnt:"N/A"],
			['TS5F2A- Transaction Currency Code', tranCurrencyCode != '' ? tranCurrencyCode: "N/A"],
		   ['TS82- Application Interchange Profile (AIP)',applIntrchgProf != '' ? applIntrchgProf:"N/A"],
		   ['TS84- Dedicated File Name',dedicatedFileName !='' ? dedicatedFileName:"N/A"],
		   ['TS91LN - N.A',TS91LN],
		   ['TS91- Issuer Authentication Data',TS91 !=''? TS91 : "N/A"],
		   ['TS95- Terminal Verification Results (TVR)',TS95 != '' ? TS95 :"N/A"],
		   ['TS9A- Transaction Date',TS9A != '' ? TS9A :"N/A"],
		   ['TS9C- Transaction Type',TS9C != '' ? TS9C :"N/A"],
		   ['TS9F02- Amount Authorized',TS9F02 != '' ? TS9F02 :"N/A"],
		   ['TS9F03- Amount Other',TS9F03 != '' ? TS9F03 :"N/A"],
		   ['TS9F06- Application Identifier (AID) - Terminal',TS9F06],
		   ['TS9F07- Application Identifier (AID) - Terminal',TS9F07],
		   ['TS9F10- Issuer Application Data (IAD)',TS9F10 != '' ? TS9F10 :"N/A"],
		   ['TS9F1A- Terminal Country Code',TS9F1A != '' ? TS9F1A :"N/A"],
		   ['TS9F1E- Interface Device (IFD) Serial number',TS9F1E != '' ? TS9F1E :"N/A"],
		   ['TS9F26- Application Cryptogram(ARQC/TC/AAC)', TS9F26 != '' ? TS9F26 :"N/A"],
		   ['TS9F27- Cryptogram Information Data (CID)', TS9F27 != '' ? TS9F27 :"N/A"],
		   [' TS9F33- Terminal Capabilities', TS9F33 != '' ? TS9F33 :"N/A"],
		   ['TS9F34- CVM Results', TS9F34 != '' ? TS9F34 :"N/A"],
		   ['TS9F35- Terminal Type', TS9F35 != '' ? TS9F35 :"N/A"],
		   ['TS9F36- Application Transaction Counter (ATC)', TS9F36 != '' ? TS9F36 :"N/A"],
		   ['TS9F37- Unpredictable Number', TS9F37 != '' ? TS9F37 :"N/A"],
		   ['TSAIDL- N.A.', TSAIDL != '' ? TSAIDL :"N/A"],
		   
		   ['TSDYNL- N.A.',TSDYNL],
		   ['TSPISI- N.A.',TSPISI],
		   ['TSRAKY- N.A.',TSRAKY],
		   ['TSSTAN- N.A.', TSSTAN != '' ? TSSTAN :"N/A"]
		   
		   
		 ]);

		 const dataArray = Array.from(dataMap, ([key, value]) => [key, value]);

		 const workbook = XLSX.utils.book_new();

		 const worksheet = XLSX.utils.aoa_to_sheet([['ICC DATA', ''], ...dataArray]);

		 XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');

		 XLSX.writeFile(workbook, 'ICCdata.xlsx');
   }
 
</script>
</head>
<div class="panel panel-default">
															<div class="panel-heading">
																<h4 class="panel-title">
																	<a data-toggle="collapse" data-parent="#childAccordion"
																		href="#collapseICC${disTxnList.mti}${disTxnList.funcCode}${disTxnList.txnId}" class="collapsed"> ICC Data 
																		<span class="glyphicon glyphicon-plus accor-icc-icon"></span>	
																	</a>
																		
																</h4>
															</div>
															<div id="collapseICC${disTxnList.mti}${disTxnList.funcCode}${disTxnList.txnId}" class="panel-collapse collapse">
																<div class="panel-body">
																	<div class="">
																		<div class="row">
																			<div class="col-md-12">
																				<div class="card">
																					<div class="card-body">
																						<c:if test="${not empty transactionDetailSummary.txnIccDTO}">
																						<c:set var="txnIccDTO" value="${transactionDetailSummary.txnIccDTO}" scope="page" />
																						<table name="tabNew" class="table table-striped" style="font-size: 12px">
																						<caption style="display:none;">ICC DATA</caption>
																							<thead style="display: none;">
																								<th scope="col"></th>
																							</thead>

																							<tbody>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS4FICCApplicationID" /></label></td>
																									<td id="TS4FICCApplicationID"><c:if test="${empty txnIccDTO.applId}">N/A</c:if>${txnIccDTO.applId}</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS4FLNNA" /></label></td>
																									<td id="TS4FLNNA"><c:if test="${empty txnIccDTO.applIdLnt}">N/A</c:if>${txnIccDTO.applIdLnt }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS5F2ATransactionCurrencyCode" /></label></td>
																									<td id="TS5F2ATransactionCurrencyCode"><c:if test="${empty txnIccDTO.tranCurrencyCode}">N/A</c:if>${txnIccDTO.tranCurrencyCode }</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS82ApplicationInterchangeProfileAIP" /></label></td>
																									<td id="TS82ApplicationInterchangeProfileAIP"><c:if test="${empty txnIccDTO.applIntrchgProf}">N/A</c:if>${txnIccDTO.applIntrchgProf}</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS84DedicatedFileName" /></label></td>
																									<td id="TS84DedicatedFileName"><c:if test="${empty txnIccDTO.dedicatedFileNam}">N/A</c:if>${txnIccDTO.dedicatedFileNam }</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS91LNNA" /></label></td>
																									<td id="TS91LNNA">N/A</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS91IssuerAuthenticationData" /></label></td>
																									<td id="TS91IssuerAuthenticationData"><c:if test="${empty txnIccDTO.issAuthData}">N/A</c:if>${txnIccDTO.issAuthData }</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS95TerminalVerificationResultsTVR" /></label></td>
																									<td id="TS95TerminalVerificationResultsTVR"><c:if test="${empty txnIccDTO.termVerifyResult}">N/A</c:if>${txnIccDTO.termVerifyResult }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9ATransactionDate" /></label></td>
																									<td id="TS9ATransactionDate"><c:if test="${empty txnIccDTO.tranDate}">N/A</c:if>${txnIccDTO.tranDate }</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9CTransactionType" /></label></td>
																									<td id="TS9CTransactionType"><c:if test="${empty txnIccDTO.tranType}">N/A</c:if>${txnIccDTO.tranType }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F02AmountAuthorized" /></label></td>
																									<td id="TS9F02AmountAuthorized"><c:if test="${empty txnIccDTO.cryptogramAmount}">N/A</c:if>${txnIccDTO.cryptogramAmount }</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F03AmountOther" /></label></td>
																									<td id="TS9F03AmountOther"><c:if test="${empty txnIccDTO.amountOther}">N/A</c:if>${txnIccDTO.amountOther }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F06ApplicationIdentifierAIDTerminal" /></label></td>
																									<td id="TS9F06ApplicationIdentifierAIDTerminal">N/A</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F07ApplicationUsageControl" /></label></td>
																									<td id="TS9F07ApplicationUsageControl">N/A</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F10IssuerApplicationDataIAD" /></label></td>
																									<td id="TS9F10IssuerApplicationDataIAD"><c:if test="${empty txnIccDTO.issApplData}">N/A</c:if>${txnIccDTO.issApplData }</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F1ATerminalCountryCode" /></label></td>
																									<td id="TS9F1ATerminalCountryCode"><c:if test="${empty txnIccDTO.termCountryCode}">N/A</c:if>${txnIccDTO.termCountryCode }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F1EInterfaceDeviceIFDSerialnumber" /></label></td>
																									<td id="TS9F1EInterfaceDeviceIFDSerialnumber"><c:if test="${empty txnIccDTO.termSerialNo}">N/A</c:if>${txnIccDTO.termSerialNo }</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F26ApplicationCryptogram" /></label></td>
																									<td id="TS9F26ApplicationCryptogram"><c:if test="${empty txnIccDTO.applCryptogram}">N/A</c:if>${txnIccDTO.applCryptogram }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F27CryptogramInformationData" /></label></td>
																									<td id="TS9F1ATerminalCountryCode"><c:if test="${empty txnIccDTO.cryptInfoData}">N/A</c:if>${txnIccDTO.cryptInfoData }</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F33TerminalCapabilities" /></label></td>
																									<td id="TS9F33TerminalCapabilities"><c:if test="${empty txnIccDTO.termCapabilities}">N/A</c:if>${txnIccDTO.termCapabilities }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F34CVMResults" /></label></td>
																									<td id="TS9F34CVMResults"><c:if test="${empty txnIccDTO.cardhVerResult}">N/A</c:if>${txnIccDTO.cardhVerResult }</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F35TerminalType" /></label></td>
																									<td id="TS9F35TerminalType"><c:if test="${empty txnIccDTO.terminalType}">N/A</c:if>${txnIccDTO.terminalType }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F36ApplicationTransactionCounter" /></label></td>
																									<td id="TS9F36ApplicationTransactionCounter"><c:if test="${empty txnIccDTO.applTranCounter}">N/A</c:if>${txnIccDTO.applTranCounter }</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TS9F37UnpredictableNumber" /></label></td>
																									<td id="TS9F37UnpredictableNumber"><c:if test="${empty txnIccDTO.unpredictableNo}">N/A</c:if>${txnIccDTO.unpredictableNo }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TSAIDLNA" /></label></td>
																									<td id="TSAIDLNA"><c:if test="${empty txnIccDTO.applIdLnt}">N/A</c:if>${txnIccDTO.applIdLnt}</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TSDYNLNA" /></label></td>
																									<td id="TSDYNLNA">0</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TSPISINA" /></label></td>
																									<td id="TSPISINA">0</td>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TSRAKYNA" /></label></td>
																									<td id="TSRAKYNA">0</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.iccData.lbl.TSSTANNA" /></label></td>
																									<td id="TSSTANNA"><c:if test="${empty txnIccDTO.tsStan}">N/A</c:if>${txnIccDTO.tsStan}</td>
																									<td></td>
																									<td></td>
																								</tr>
																							</tbody>
																						</table>
																						</c:if>
																					</div>
																					<button style="margin-top: 10px"
																						class="btn btn-success pull-left btn_align"
																						onclick="exportICCData('${txnIccDTO.applId}','${txnIccDTO.applIdLnt}','${txnIccDTO.tranCurrencyCode}',
																																'${txnIccDTO.applIntrchgProf}','${txnIccDTO.dedicatedFileNam}','N/A','${txnIccDTO.issAuthData}','${txnIccDTO.termVerifyResult}','${txnIccDTO.tranDate}',
																																'${txnIccDTO.tranType}','${txnIccDTO.cryptogramAmount}','${txnIccDTO.amountOther}','N/A','N/A','${txnIccDTO.issApplData}','${txnIccDTO.termCountryCode}',
																																'${txnIccDTO.termSerialNo}','${txnIccDTO.applCryptogram}','${txnIccDTO.cryptInfoData}','${txnIccDTO.termCapabilities}','${txnIccDTO.cardhVerResult}',
																																'${txnIccDTO.terminalType}','${txnIccDTO.applTranCounter}','${txnIccDTO.unpredictableNo}','${txnIccDTO.applIdLnt}','0','0','0','${txnIccDTO.tsStan}');">
																						<spring:message code="ifsc.exportBtn" />
																					</button>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>