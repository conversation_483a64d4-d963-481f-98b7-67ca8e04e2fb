package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class FunctionCodeDTO {

	private int funcCodeId;
	private String mti;
	private String procCode;
	private String funcCode;
	private String funcCodeDesc;
	private String feeType;
	private String fundMovement;
	private String fundMovementSide;
	private String recalculate;
	private String transactionType;
	private String networkTxnType;

	private String fundMovementLookup;
	private String recalculateLookup;

	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private String checkerComments;
	private String lastOperation;
	private String requestState;
	private String addEditFlag;
	private String status;
	private String userName;

}
