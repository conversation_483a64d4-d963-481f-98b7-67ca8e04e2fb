$(document).ready(function() {
	//
});

function isEmpty(val) {
	return (val == undefined || val == null || val.length <= 0 || val == ''
		|| val == 'NaN' || val == 0) ? true : false;
}

function validateFromCommonVal(fieldId, isMandatory, fieldType, length,
	isExactLength) {
	var fieldValue = $("#" + fieldId).val();

	var isValid = true;
	if (isMandatory && fieldValue.trim() == "") {
		isValid = false;
	}
	
	 var regExAll = /^.*?(?=[\^#&$\*:<>\?/\{\|\}]).*$/;
		if (regExAll.test(fieldValue)) {
			isValid = false;
		}



	if ((isMandatory && fieldValue.trim() == "" && (fieldType != "SelectionBox"))
		|| (isMandatory && fieldValue.trim() == "SELECT" && fieldType == "SelectionBox")
		|| (isMandatory && fieldValue.trim() == '0' && fieldType == "SelectionBox")) {
		isValid = false;
	}

	if ((isMandatory && fieldValue.trim() == "" && (fieldType != "SelectionBoxHoliday"))
		|| (isMandatory && fieldValue.trim() == "SELECT" && fieldType == "SelectionBoxHoliday")
	) {
		isValid = false;
	}

	if (fieldType == "Number" && isNaN(fieldValue)) {
		isValid = false;
	}


	if (regexMap.hasOwnProperty(fieldType)) {
		if (!regexMap[fieldType].test(fieldValue)) {
			isValid = false;
		}
	}

	if (fieldType == "NumericwithPrecision") {
		var regEx = NumericwithPrecision;
		if (fieldValue != '0' && !regEx.test(fieldValue)) {
			isValid = false;
		}
	}

	if (fieldType == "NumericwithPrecisionAmount") {
		var regEx = NumericwithPrecisionAmount;
		if (fieldValue != '0' && !regEx.test(fieldValue)) {
			isValid = false;
		}
	}

	if (fieldType == "loginId") {
		var regEx = loginId;
		var minLn = 5;
		var maxLn = 50;

		if (!regEx.test(fieldValue)) {
			isValid = false;
		}

		if (fieldValue.length < minLn) {
			isValid = false;
		}
		if (fieldValue.length > maxLn) {
			isValid = false;
		}

	}

	if (fieldType == "DateFormat") {
		var regEx = DateFormat;

		if (isMandatory && fieldValue.trim() == "" || (!regEx.test(fieldValue))) {
			isValid = false;
		}

	}


	if (fieldType == "regex") {
		var regEx = regex;
		if (fieldValue.trim() == "" || (!regEx.test(fieldValue))) {
			isValid = false;
		}
		
	}

	if (!isMandatory) {
		if (fieldValue.length == 0) {
			isValid = true;
		}
	}
	if (isExactLength && fieldValue.length != length) {
		isValid = false;
		if (fieldValue.length == 0 && !isMandatory) {
			isValid = true;
		}
	}

	if (isValid) {

		$("#err" + fieldId).hide();
		if (fieldId.includes('Level')) {
			$("#err" + fieldId).find('.error').html('');

		}

	} else {

		var x = '';
		var flag = false;
		x = fieldId;

		if (fieldId.includes('Level_1')) {
			x = fieldId.replace('Level_1', '');
			flag = true;

		} else if (fieldId.includes('Level_2')) {
			x = fieldId.replace('Level_2', '');
			flag = true;
		} else if (fieldId.includes('Level_3')) {
			x = fieldId.replace('Level_3', '');
			flag = true;
		} else if (fieldId.includes('Level_4')) {
			x = fieldId.replace('Level_4', '');
			flag = true;
		} else if (fieldId.includes('Level_5')) {
			x = fieldId.replace('Level_5', '');
			flag = true;
		}

		if (flag) {
			var y = x.slice(-1);
			if (!isNaN(y)) {
				x = x.replace(y, '');
			}
		}
		if (validationMessages[x]) {
			$("#err" + fieldId).find('.error').html(validationMessages[x]);
		}

		$("#err" + fieldId).show();
	}
	return isValid;
}
