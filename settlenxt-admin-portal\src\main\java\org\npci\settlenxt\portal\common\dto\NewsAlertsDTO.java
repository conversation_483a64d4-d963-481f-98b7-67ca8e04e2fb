package org.npci.settlenxt.portal.common.dto;

import java.time.LocalDate;
import java.util.Date;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JGlobalMap
public class NewsAlertsDTO {

	private int newsId;
	private String isType;
	private String title;
	private String subTitle;
	private Date fromDate;
	private Date toDate;
	private String type;
	private String title1;
	private String subTitle1;
	private String fromDateStr;
	private String toDateStr;
	private String summary;
	private String details;
	private String footerData;
	private String publishType;
	private String sendMail;
	private String status;
	private String referenceNumber;
	private String freqFrom;
	private String freqTo;
	private String critical;
	private String weekFrom;
	private String weekTo;
	private String monthFrom;
	private String monthTo;
	private Date createdOn;
	private String createdBy;
	private Date lastUpdatedOn;
	private String lastUpdatedBy;
	private String periodType;
	private String comment;
	private String reqType;
	private String buttonType;
	private String saveNewsResult;
	private String requestState;
	private String validFrom;
	private String statusCode;
	private String bankName;
	private String users;
	private String roles;
	private String validTo;
	private String schedulerId;
	private int distributionId;
	private String lastOperation;
	private String participantId;
	private String userName;
	private String rptFileName;
	private String fileName;
	private String filePath;
	private String roleDesc;
	private String participantName;
	private String editUserList;
	private String editBankList;
	private String editRoleList;
	private String makChkFlag;
	private String clearId;
	private String oldOrNewFlag;
	private String checkerComments;
	private String createdDate;
	private String modifiedDate;
	private String screenName;
	private LocalDate frmDate;
}
