package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.adminportal.dto.CurrencyMasterDTO;
import org.npci.settlenxt.portal.common.service.BaseCurrencyMasterMapper;

@Mapper
public interface CurrencyMasterRepository extends BaseCurrencyMasterMapper {

	 List<CurrencyMasterDTO> getCurrencyMasterListMain();

	 List<CurrencyMasterDTO> getCurrencyMasterPendingForApproval();

	 CurrencyMasterDTO getCurrencyMasterProfileMain(int currencyId);

	 CurrencyMasterDTO getCurrencyMasterStgInfoById(int currencyId);

	 CurrencyMasterDTO getCurrencyMasterMain(int currencyId);

	 CurrencyMasterDTO getCurrencyMasterStg(int currencyId);

	 int fetchCurrencyMasterIdSequence();

	 void insertCurrencyMasterStg(CurrencyMasterDTO currencyMasterDto);

	 void insertCurrencyMasterMain(CurrencyMasterDTO currencyMasterDto);

	 void updateCurrencyMasterMain(CurrencyMasterDTO currencyMasterDto);

	 void updateCurrencyMaster(CurrencyMasterDTO currencyMasterDto);

	 void updateCurrencyMasterRequestState(CurrencyMasterDTO currencyMasterDto);

	 void deleteDiscardedEntry(CurrencyMasterDTO currencyMasterDto);

	 int validateDuplicateCheck(CurrencyMasterDTO currencyMasterDto);
}
