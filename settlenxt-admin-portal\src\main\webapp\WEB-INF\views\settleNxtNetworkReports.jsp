<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
	<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
		<%@ taglib uri="http://www.springframework.org/tags" prefix="spring" %>

			<head>
				<title>
					<spring:message code="am.lbl.title" />
				</title>
				<%@ page trimDirectiveWhitespaces="true" %>
					<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
						<%@ page import="java.time.format.DateTimeFormatter" %>
							<script type="text/javascript"
								src="./static/js/validation/settlenxtNetworkReports.js"></script>


							<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
							<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
							<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />
							
							<script type="text/javascript" src="./static/js/dataTables.buttons.min.js">

							</script>
							<script type="text/javascript" src="./static/js/jszip.min.js">
							</script>


							<script type="text/javascript" src="./static/js/buttons.html5.min.js">

							</script>
							<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
							<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />


			</head>
			<div id="errorStatus2" class="alert alert-danger" role="alert" style="display: none"></div>
			<style>
				table.dataTable thead {
					vertical-align: top;
				}
			</style>
			<style>
				.form-control {
					background-color: white !important;
				}
			</style>

			<body onload="noBack();">


				<div class="container-fluid height-min">
					<div class="row">

						<div class="body-content">

							<div class="card">
								<div class="card-header">
									<div class="card-title">
										Network Reports
									</div>
								</div>

								<div class="card-body">
									<form:form onsubmit="encodeForm(this);" enctype="multipart/form-data"
										modelAttribute="auditBatchLogDTO" method="POST" autocomplete="off">

										<div class="row">

											<div class="col-md-3">
												<div class="form-group">
													<label for="">Network Type <span class="red">*</span></label>
													<form:select path="networkType" id="networkType"
														class="form-control">
														<form:option label="SELECT" value="" />
														<form:options items="${networkTypes}" />
													</form:select>
													<div id="errNetworkType">
														<span for=networkType class="error">
															<form:errors path="networkType" />
														</span>
													</div>
												</div>
											</div>

											<div class="col-md-3">
												<div class="form-group">
													<label for="">
														<spring:message code="am.lbl.file.fromDate" /> <span
															class="red">*</span>
													</label>

													<form:input path="fromDateStr" id="fromDateStr" name="fromDateStr"
														cssClass="form-control" readonly="true" />

													<div id="errFromDate">
														<span for=fromDateStr class="error">
															<form:errors path="fromDateStr" />
														</span>
													</div>
												</div>
											</div>

											<div class="col-md-3">
												<div class="form-group">
													<label for="">
														<spring:message code="am.lbl.file.toDate" /> <span
															class="red">*</span>
													</label>

													<form:input path="toDateStr" id="toDateStr" name="toDateStr"
														cssClass="form-control" readonly="true" />

													<div id="errToDate">
														<span for=toDateStr class="error">
															<form:errors path="toDateStr" />
														</span>
													</div>
												</div>
											</div>

											<div class="col-md-3">
												<div class="form-group">
													<label for="">Status</label>
													<form:select path="status" id="status"
														class="form-control">
														<form:option label="All" value="" />
														<form:options items="${transactionStatusOptions}" />
													</form:select>
												</div>
											</div>

										</div>
									</form:form>
								</div>

								<div class="card-action">
									<button type="button" class="btn btn-primary" id="searchBtn">
										Search
									</button><button type="button" value="Submit" class="btn btn-success"
										onclick="resetAction();">
										<spring:message code="am.lbl.reset" />
									</button>

								</div>

							</div>
						</div>
					</div>


					<div class="col-sm-12">
						<div class="card-white">
							<div class="panel panel-default">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped dataTable table-bordered order"
										style="width:100%;">
										<caption style="display:none;">reportdownload</caption>
										<thead>
											<tr>
												<th scope="col">Total Transactions</th>
												<th scope="col">No of Outgoing Transactions</th>
												<th scope="col">No of Acknowledged Transactions</th>
												<th scope="col">No of Suspended Transactions</th>
												<th scope="col">No of Rejected Transactions</th>
												<th scope="col">
													<spring:message code="am.lbl.file.action" />
												</th>

											</tr>
										</thead>
										<tbody id="afterReset">

											<c:forEach var="report" items="${networkFileData}">

												<tr>
													<td style="width:15%;">${report.totalTransactions}</td>
													<td style="width:15%;">${report.totalOutGoing}</td>
													<td style="width:15%;">${report.totalAcknowledged}</td>
													<td style="width:15%;">${report.totalSuspended}</td>
													<td style="width:15%;">${report.totalRejected}</td>
													<td style="width:10%;"><a
															href="javascript:getRecordDetails('${report.fileName}')">
															<spring:message code="am.lbl.file.download" />
														</a></td>
												</tr>
											</c:forEach>
										</tbody>

									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
				</div>
				</div>
			</body>

			</html>