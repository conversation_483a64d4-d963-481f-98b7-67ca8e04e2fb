package org.npci.settlenxt.adminportal.config.kafka.consumer;

import java.text.ParseException;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.npci.settlenxt.portal.common.config.kafka.consumer.BaseConsumerRecordsAction;
import org.springframework.stereotype.Component;



@Component("disputeConsumer")
public class DisputeConsumer implements BaseConsumerRecordsAction {

	@Override
	public void processConsumerRecord(ConsumerRecord<String, String> consumerRecord) throws ParseException {
		//do nothing
	}

}
