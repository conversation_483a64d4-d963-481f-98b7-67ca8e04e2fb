<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<script src="./static/js/validation/viewApproveSysParam.js"
	type="text/javascript"></script>

<div class="container-fluid height-min">
	<c:url value="approveSysParam" var="approveSysParam" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveSysParam" modelAttribute="sysParamsDTO"
		action="${approveSysParam}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message
									code="sysParam.viewscreen.title" /></span></strong>
					</div>
					<div class="panel-body">
						<input type="hidden" id="sysType" value="${sysParamsDTO.sysType}" />
						<input type="hidden" id="sysKey" value="${sysParamsDTO.sysKey}" />

						<table class="table table-striped infobold"
							style="font-size: 12px">
							<caption style="display: none;">
								<spring:message code="sysParam.main.title" />
							</caption>
							<thead style="display: none;">
								<th scope="col"></th>
							</thead>
							<tbody>
								<tr>
									<td colspan="6">
										<div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span>
												<span data-i18n="Data"><spring:message
														code="sysParam.requestInformation" /></span></strong>
										</div>
									</td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message
												code="sm.lbl.requestType" /><span style="color: red"></span></label></td>
									<td>${sysParamsDTO.lastOperation}</td>
									<td><label><spring:message
												code="sm.lbl.requestDate" /><span style="color: red"></span></label></td>
									<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
											value="${sysParamsDTO.lastUpdatedOn}" /></td>
									<td><label><spring:message code="sm.lbl.status" /><span
											style="color: red"></span></label></td>
									<td><c:if test="${sysParamsDTO.requestState =='A' }">
											<spring:message
												code="sysParam.requestState.approved.description" />
										</c:if> <c:if test="${sysParamsDTO.requestState =='P' }">
											<spring:message
												code="sysParam.requestState.pendingApproval.description" />
										</c:if> <c:if test="${sysParamsDTO.requestState =='R' }">
											<spring:message
												code="sysParam.requestState.rejected.description" />
										</c:if> <c:if test="${sysParamsDTO.requestState =='D' }">
											<spring:message
												code="sysParam.requestState.discared.description" />
										</c:if></td>
								</tr>
								<tr>
									<td><label><spring:message code="sm.lbl.requestBy" /><span
											style="color: red"></span></label></td>
									<td>${sysParamsDTO.lastUpdatedBy}</td>
									<td><label><spring:message
												code="sm.lbl.checkersComment" /><span style="color: red"></span></label></td>
									<td colspan=2>${sysParamsDTO.checkersComment}</td>
									<td></td>
									<td></td>

								</tr>
								<td colspan="6">
									<div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-credit-card"></span>
											<span data-i18n="Data"> <spring:message
													code="sysParam.viewscreen.title" /></span></strong>
									</div>
								</td>
								<tr>
									<sec:authorize access="hasAuthority('Approve Sys Params')">
										<td><label><spring:message code="sm.lbl.sysType" />
												<span style="color: red"></span></label></td>
										<td>${sysParamsDTO.sysType}</td>
									</sec:authorize>
									<td><label><spring:message code="sm.lbl.sysKey" /><span
											style="color: red"></span></label></td>
									<td>${sysParamsDTO.sysKey}</td>
									<td><label><spring:message code="sm.lbl.sysValue" /><span
											style="color: red"></span></label></td>
									<td>${sysParamsDTO.sysValue}</td>
									<td></td>
									<td></td>
									<td></td>

								</tr>
								<tr>
									<td><label><spring:message code="sm.lbl.desc" /><span
											style="color: red"></span></label></td>
									<td>${sysParamsDTO.desc}</td>
									<td><label><spring:message code="sm.lbl.addProp" /><span
											style="color: red"></span></label></td>
									<td>${sysParamsDTO.addProp}</td>

								</tr>
								<sec:authorize access="hasAuthority('Approve Sys Params')">
									<c:if test="${sysParamsDTO.requestState eq 'P'}">
										<tr>
											<td colspan="6">
												<div class="panel-heading-red  clearfix">
													<strong><span
														class="glyphicon glyphicon-info-sign"></span> <span
														data-i18n="Data"> <spring:message
																code="sysParam.approvalPanel.title" /></span></strong>
												</div>
											</td>
										</tr>
										<tr>
											<td><label><spring:message
														code="sm.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="sm.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="sm.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="sm.lbl.reject" /></option>
											</select></td>
											<td>
												<div style="text-align: center">
													<label><spring:message code="sm.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										</tr>
									</c:if>
								</sec:authorize>
							</tbody>
						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<div style="text-align: center">
									<sec:authorize access="hasAuthority('Approve Sys Params')">
										<c:if test="${sysParamsDTO.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approvecard" value="Submit"
												onclick="postAction('/approveSysParams');" />
										</c:if>
									</sec:authorize>

									<sec:authorize access="hasAuthority('Edit Sys Params')">
										<c:if
											test="${sysParamsDTO.requestState  eq 'R' and not empty showbutton}">
											<%-- <input name="discardButton" type="button"
												class="btn btn-danger" id="approveRole" value="Discard"
												onclick="userAction('/discardSysParam','${sysParamsDTO.sysType}','${sysParamsDTO.sysKey}');" />
											 --%>
											<input name="editButton" type="button"
												class="btn btn-success" id="approveRole" value="Edit"
												onclick="userAction('/editSysParam','${sysParamsDTO.sysType}','${sysParamsDTO.sysKey}');" />
										</c:if>
									</sec:authorize>

									<button type="button" class="btn btn-danger"
										onclick="backAction('/showSysParamsApproval');">
										<spring:message code="sysParam.backBtn" />
									</button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

