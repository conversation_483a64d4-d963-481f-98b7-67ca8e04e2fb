const today = new Date();
const sixMonthsAgo = new Date(today);
sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);

$(document).ready(function () {
  /* Initialization of datatables */
  $(document).ready(function () {
    $('#tabnew').DataTable({
      searching: false,
      ordering: false,
      paging: false,
      info: false,
    });
  });

  function addDays(date, days) {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  $('#networkType').on('keyup keypress blur change', function () {
    validateNetworkType('networkType');
  });

  $('#fromDateStr').on('keyup keypress blur change', function () {
    validateFromDate('errFromDate');
  });

  $('#toDateStr').on('keyup keypress blur change', function () {
    validateToDate('errtoDate');
  });

  $('#fromDateStr').datepicker({
    dateFormat: 'yy-mm-dd',
    changeMonth: true,
    changeYear: true,
    minDate: sixMonthsAgo,
    maxDate: today,
    onSelect: function (selectedDate) {
      if (selectedDate != '') {
        const fromDateValue = new Date(selectedDate);
        const maxDate = new Date(
          Math.min(addDays(fromDateValue, 6).getTime(), today.getTime())
        );
        $('#toDateStr').datepicker('option', 'minDate', selectedDate);
        $('#toDateStr').datepicker('option', 'maxDate', maxDate);
      }
    },
    onClose: function () {
      validateFromDate('errFromDate');
      validateToDate('errtoDate');
    },
  });

  $('#toDateStr').datepicker({
    dateFormat: 'yy-mm-dd',
    changeMonth: true,
    changeYear: true,
    minDate: sixMonthsAgo,
    maxDate: today,
    onSelect: function (selectedDate) {
      if (selectedDate != '') {
        const toDateValue = new Date(selectedDate);
        const minDate = new Date(
          Math.max(addDays(toDateValue, -6).getTime(), sixMonthsAgo.getTime())
        );
        $('#fromDateStr').datepicker('option', 'minDate', minDate);
        $('#fromDateStr').datepicker('option', 'maxDate', selectedDate);
      }
    },
    onClose: function () {
      validateToDate('errtoDate');
      validateFromDate('errFromDate');
    },
  });

  $('#searchBtn').click(function () {
    var check = false;
    if (!validateFromDate('errFromDate')) {
      check = true;
    }
    if (!validateToDate('errtoDate')) {
      check = true;
    }

    if (!validateNetworkType('errNetworkType')) {
      check = true;
    }

    if (!check) {
      const fromDateStr = $('#fromDateStr').val();
      const toDateStr = $('#toDateStr').val();
      const networkType = $('#networkType').val();
      const status = $('#status').val();

      const url = '/settleNxtNetworkFileSearch';

      const data =
        'fromDateStr,' +
        fromDateStr +
        ',toDateStr,' +
        toDateStr +
        ',networkType,' +
        networkType +
        (status !== '' ? ',status,' + status : '');

      postData(url, data);
    } else {
      return false;
    }
  });
});

function resetAction() {
  $('#fromDateStr').val('');
  $('#toDateStr').val('');
  $('#networkType').val('');
  $('#fromDateStr').datepicker('option', 'minDate', sixMonthsAgo);
  $('#fromDateStr').datepicker('option', 'maxDate', today);
  $('#toDateStr').datepicker('option', 'minDate', sixMonthsAgo);
  $('#toDateStr').datepicker('option', 'maxDate', today);
}

function getRecordDetails(fileName) {
  var url = '/settleNxtNetworkFileDownload';
  const searchAttributes = fileName.split('_');

  const data =
    'fromDateStr,' +
    searchAttributes[1] +
    ',toDateStr,' +
    searchAttributes[2] +
    ',networkType,' +
    searchAttributes[0] +
    (searchAttributes.length > 3 ? ',status,' + searchAttributes[3] : '');

  postData(url, data);
}

function validateNetworkType(_msgID) {
  const value = document.getElementById('networkType').value.replace('/^s*|s*$/g', '');
  if (value == '') {
    $('#errNetworkType').show();
    $('#errNetworkType').find('.error').html('Please select Network Type');

    return false;
  } else {
    $('#errNetworkType').hide();
    $('#errNetworkType').find('.error').html(' ');
  }
  return true;
}

function validateFromDate(_msgID) {
  var dateString = document.getElementById('fromDateStr').value.replace('/^s*|s*$/g', '');
  if (dateString == '') {
    $('#errFromDate').show();
    $('#errFromDate').find('.error').html('Please enter From Date');

    return false;
  } else {
    $('#errFromDate').hide();
    $('#errFromDate').find('.error').html(' ');
  }
  return true;
}

function validateToDate(_msgID) {
  var dateString = document.getElementById('toDateStr').value.replace('/^s*|s*$/g', '');
  if (dateString == '') {
    $('#errToDate').show();
    $('#errToDate').find('.error').html('Please enter To Date');
    return false;
  } else {
    $('#errToDate').hide();
    $('#errToDate').find('.error').html(' ');
  }
  return true;
}
