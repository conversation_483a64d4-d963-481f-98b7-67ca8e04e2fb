$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('form')
    .each(function(){
        $(this).data('serialized', $(this).serialize())
    })
    .on('change input', function(){
        $(this)             
            .find('input:submit, button:submit')
                .prop('disabled', $(this).serialize() == $(this).data('serialized'))
        ;
        $('#forms').prop('disabled', false);
       
     })
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	}

function fcfdAction(action) {
	let url = action;
 	var data =  "status,"
			+ status;
	postData(url, data);
}


function postAction(_action) {
let url="";
let data="";
let id="";
let remarks="";
	if(maxLengthTextArea('rejectReason')){
	if ($('#apprej option:selected').val() == "A") {
		if ($("#rejectReason").val() != "") {
			
			 id = $("#id").val();
			remarks = $("#rejectReason").val();
	 url = '/approveFcfdStatus';
			 data = "status," + "A"  + ",remarks,"
					+ remarks+ ",id,"
					+ id;
			postData(url, data);
		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else if ($('#apprej option:selected').val() == "R") {
		if ($("#rejectReason").val() != "") {
				
			remarks = $("#rejectReason").val();
				 id = $("#id").val();
				 url = '/approveFcfdStatus';
				 data =  "status," + "R"  + ",remarks," + remarks + ",id,"
						+ id;

				postData(url, data);
			

		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else {
		$(".appRejMust").show();
		$('html, body').animate({ scrollTop: 0 }, 'slow');
		return false;
	}
	}
}

function editData(fcfdActions,id) {
	let url = '/editFcfd';
	var data = "fcfdAction," + fcfdActions +",id,"+ id ; 
	postData(url, data);
	 
}

