$(document).ready(function() {

	
	 fcfdIdArr=[];
	/* Initialization of datatables */
	$(document).ready(function () {
    	
     $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                //If first column to be skipped to include the filter for the reasons line check box 
                if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   searchBoxFunc(colIdx, cell, title, api);
                   }
                });
            $('#tabnew_filter').hide();
        },
        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'FCFD',
                header: 'false',
                title: null,
                sheetName: 'FCFD',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'FCFD' ,
				header:'false', 
				title: null,
				sheetName:'FCFD',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });
});	
	
	
	 
	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	
	 $("#excelExport").on("click", function () {
	        $(".buttons-excel").trigger("click");
	    });
	    
	 $("#csvExport").on("click", function () {
            $(".buttons-csv").trigger("click");
        });   

		 
		 
$("#selectAll").click(function(){	 

			 $('#errorStatus4').hide();
			 	        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
			 	      
			 		   var fcfdIdList = document.getElementById("fcfdIds");
					     fcfdIdList.innerHTML = fcfdIdsPending.length+"     "+"records are selected";

			 	if(fcfdIdArr.length>0){
			 		fcfdIdList.innerHTML = fcfdIdArr.length+"     "+"records are selected";
			 		
			 		if( $('#selectAll').is(':checked') ){
			 	 $("#toggleModalFcfd").modal('show');
			 	        
			 	}
			 	else{
			 	   $("#toggleModalFcfd").modal('hide');
			 	        
			 	}}else{
			 		var i=0;
			 		var fcfdIdArr2=[];
			 		 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
			 			fcfdIdArr2.push(this.value);
			 				                    i++;
			 				                });
			 		 
			 		 if(fcfdIdArr2.length>0){


			 		if(fcfdIdsPending.length>0){
			 		
			 			
			 			fcfdIdList.innerHTML = fcfdIdsPending.length+"     "+"records are selected";
			 			
			 			if( $('#selectAll').is(':checked') ){
			 		 $("#toggleModalFcfd").modal('show');
			 		        
			 		}
			 		else{
			 		   $("#toggleModalFcfd").modal('hide');
			 		        
			 		}
			 		 }}}
		
	});
	    
		
});
	 
	     $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
	    
	
function searchBoxFunc(colIdx, cell, title, api) {
	var cursorPosition =null;
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function(_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
                fcfdIdArr = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
                        fcfdIdArr.push(this.value);
                        i++;
                    });
                }
                else {
                    fcfdIdArr = [];
                }

            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
    
}

function mySelect(){
	
	
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	    
	 
	 var fcfdIdList = document.getElementById("fcfdIds");
     
	 if(array.length==fcfdIdsPending.length){
		 $('#selectAll').prop('checked', true);
		 
		 fcfdIdList.innerHTML = fcfdIdsPending.length+"     "+"records are selected";
			 $("#toggleModalFcfd").modal('show');
	 }
	 else{
		 $("#toggleModalFcfd").modal('hide');
		 
	 }
	
}


function submitForm(url) {
	var data =  "bulkAction," + $('#bulkAction').val();
	postData(url, data);
}


function getFCFD() {
	
	var url = "/showFcfd";
var data =  "fcfdAction,"
		+ $('#fcfdAction').val();
	postData(url, data);
}

function viewFCFDInfo(id, type) {
	let url="";
	if (type == 'A')
		 url = '/viewFCFDInfo';
	else if (type == 'P')
		url = '/viewApproveFCFD';
    else if (type == 'R'){
		url = '/viewRejFcfd';}	
	var data = "id," + id  ;
	postData(url, data);
}

function getPendingFcfd() {
	
	let url = '/fcfdPendingApproval';
	var data =  "fcfdAction,"
		+ $('#fcfdAction').val();
	postData(url, data);
}

function approveOrRejectFCFD(type,action){
	
	 var url = '/approveOrRejectFCFD';
	let data="";
	
	 var array = [];

	 if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(action=='All'){
		 if(fcfdIdArr.length>0){
				 array= fcfdIdArr;
				  }
					 else{
						  array= fcfdIdsPending;}
	 }
	   
	 
		var fcfdIds = "";
		for ( var arr of array) {
			fcfdIds = fcfdIds + arr + "|"
					;
		}
	if(array.length!=0){
	if(type=='A'){
			
		 data =  "status,"+"A"+",bulkApprovalFcfdIdList,"+fcfdIds;
	}
	else if(type=='R'){
		
		data = "status,"+"R"+",bulkApprovalFcfdIdList,"+fcfdIds;
	}
	

	postData(url, data);
	$('#errorStatus2').hide();
	$('#errorStatus2').html("");
	
	
	}
	else{
	
	$('#errorStatus2').html("Please select one or more records to bulk approve/reject records");
	$('#errorStatus2').show();
	}
	
}


function deselectAll() {

	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  
   for(var e of ele){  
       if(e.type=='checkbox')  
           e.checked=false;  
   } 
   
}



