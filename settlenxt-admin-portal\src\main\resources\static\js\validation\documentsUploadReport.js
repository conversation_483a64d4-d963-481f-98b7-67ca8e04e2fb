$(document).ready(function() {

$('#deleteAllFiles').hide();
 $('#participantId').selectize({
      });
	  
   $('#participantId').on('keyup keypress blur change',function () {
    validateFromCommonVal('participantId', true, "SelectionBox", "" , false);
   });
   $('#documentType').on('keyup keypress blur change',function () {
     validateDocumentTypeField('documentType');
   });
});

function validateDocumentTypeField(fieldId) {
	let fieldValue = $("#" + fieldId).val();
	let isValid = true;
	if (fieldValue === "0") {
		isValid = false;
	}
	return createErrorMsg(isValid, fieldId);
}

function createErrorMsg(isValid, fieldId) {
    if (isValid) {
        $("#err" + fieldId).hide();
    } else {
        const errMsg = validationMessages[fieldId];
  
                if (errMsg) {
                    $("#err" + fieldId).find('.error').html(errMsg);
                }
        
        $("#err" + fieldId).show();
    }
    return isValid;
}
function updDocument(_inputFileID){
	
var check=false;
	
	
	
	let i="";
	let documentModel="";
	
	check = validateFields(check);
	
	if(documentData.length > 0) {
	
if(!check){
	$('#errdocumentupload').hide();
	$('#errorStatus3').hide();
		
var tokenValue = document.getElementsByName("_TransactToken")[0].value;
tokenValue = $('<div>').text(tokenValue).html();


var model = {
        "documentType": $('#documentType').val(),
        "participantId": $('#participantId').val(),
        "fileType": $('#fileType').val(),
        "documents":documentData
       }


const formData = new FormData();
formData.append('model', JSON.stringify(model));
for ( i = 0; i < documentData.length; ++i) {
	if(documentData[i].status!='D')
    formData.append('file', documentData[i].fileContent);
}
  formData.append('documentType', escape($('#documentType').val()));
   formData.append('participantId', escape($('#participantId').val()));
       formData.append('_TransactToken',tokenValue);
    
     $.ajax({
    	 url: "uploadDocument",
        type: "POST",
        enctype: 'multipart/form-data',
        data : formData,
        processData: false,
        contentType: false,
        cache : false,
        timeout: 600000,
		dataType : "json",
        success:function(data){	
        ({ i, documentModel } = handleDataSuccess(data, i, documentModel));
            
            if(data.error){
            	 document.getElementsByClassName("alert")[0].innerHTML = "";
                 
                 $('#successStatus2').hide();
                 $('.alert-danger').hide();
                 $('.alert-success').hide();
                  $('#successStatus').hide();
                  document.getElementsByClassName("alert")[0].innerHTML = "";
                 $('#errorStatus2').html(data.error);
                 $('#errorStatus2').show();
               
         
           
         }
      	

}
})

	} 
	} else {
		 $('#successStatus2').hide();
         $('.alert-danger').hide();
         $('.alert-success').hide();
         $('#errorStatus3').html("Please choose a file to upload");
         $('#errorStatus3').show();
	}
}


// addded new function for uploading file

var documentData=[];
function validateFields(check) {
	if (documentData.length == 0) {
		$('#errdocumentupload').show();
		$('#errdocumentupload').find('.error').html('Please Upload Files');
		check = true;

	}
	if (!validateFromCommonVal('participantId', true, "SelectionBox", "", false)) {
		check = true;
	}
	if (!validateDocumentTypeField('documentType')) {
		check = true;
	}
	return check;
}

function handleDataSuccess(data, i, documentModel) {
	if (data.success) {
		dataSubmittedApproval = true;

		$('#errorStatus2').hide();
		$('.alert-danger').hide();
		$('#successStatus').hide();
		document.getElementsByClassName("alert")[0].innerHTML = "";
		$('.alert-success').hide();
		$('#successStatus2').html(data.success);
		$('#successStatus2').show();
		for (i = 0; i < documentData.length; i++) {
			documentModel = documentData[i];
			if (documentModel) {
				documentModel.isNew = false;
			}
		}
		renderFileTable();
		$('#deleteAllOptions').hide();
		$('#deleteAllFiles').hide();
		var select = $("#participantId").selectize();
		var selectize = select[0].selectize;
		selectize.disable();
		$('#documentType').attr('disabled', 'disabled');


		$('#uploadButton').hide();
		$('#title').hide();
		$('#file').hide();

	}
	return { i, documentModel };
}

function documentsUploadBulk(_id) {
	$('#deleteAllFiles').show();
	 	const input = document.querySelector('#file');
	 	
	 	
	 	var fileExtnList = document.getElementById("fileExtnList").value.split(",");
	 	var allowedFileNameExtensions=[".pgp", ".doc", ".docx",  ".rar",".zip", ".tiff", ".pdf", ".jpeg", ".jpg" ];
	 	 if(fileExtnList.length>0){
	 	  allowedFileNameExtensions = fileExtnList;
	 	}
	 	
	 	 $("#errdocumentupload").find('.error').html('');
	      $("#errdocumentupload").hide();
	      $("#errorStatus3").find('.error').html('');
	      $('#errorStatus3').hide();
	      var invalidFileNames=[];
	      let i ="";
	      let fileName="";
	      let extnIndex="";
	      
	     var invalidFileNameArr=[];
		 let isValidFileName = false;
	      var fileNamePattern = /^[a-zA-Z0-9()%&\-_. ,]+$/;
	      
	      for ( i = 0; i < input.files.length; ++i) {		
	 		 fileName= input.files[i].name;
	 		 isValidFileName = fileNamePattern.test(fileName);
	 		if(!isValidFileName) {
	 			invalidFileNameArr.push(fileName);
	 		}
	 		 extnIndex=allowedFileNameExtensions.findIndex(extn =>  fileName.toUpperCase().endsWith(extn.toUpperCase()));
	 		 
	 		if(extnIndex==-1){
	 			invalidFileNames.push(fileName);		
	 			isValidFileName = false;   
	 		}
	 		
	 		var documentIndex=documentData.findIndex(obj => obj.documentName == fileName);
	 		var documentModel = documentData[documentIndex];
	 		if(documentModel){
	 		 	documentModel.fileContent = input.files[i];
	 		}else{
	 			documentModel={};
	 			documentModel.documentName = input.files[i].name;
	 			documentModel.fileContent = input.files[i];
	 			documentModel.isNew = true;
	 			documentModel.status = (isValidFileName == true?'A':'D');
	 			documentModel.documentId=0;
	 			documentData.push(documentModel);
	 		}
	 	 }
	 	  if(invalidFileNames.length>0){
	 	      $("#errdocumentupload").find('.error').html('Invalid file type for '+invalidFileNames.map(item => item).join());
	 		    $("#errdocumentupload").show();
	 		    $("#deleteAllFiles").hide();
	 		
	 	  }else if(invalidFileNameArr.length>0)  {
	 		 $("#errdocumentupload").find('.error').html('Invalid file Name for '+invalidFileNameArr.map(item => item).join());
	 		    $("#errdocumentupload").show();
	 		    $("#deleteAllFiles").hide();
	 	
	 	  }
	 
	 		
	 	
	 	input.value = '';
	 	renderFileTable();
	 }

function renderFileTable() {
	var fileTableData = "";
	for (var i of documentData) {
		var documentModel = i;
		if (documentModel.status != 'D') {
			var statusDesc = documentModel.isNew ? 'To be uploaded' : 'Uploaded';
			var rowData = '<tr>' ;
			var downloadLink= documentModel.isNew ? '' : ' &nbsp; &nbsp; <a href="javascript:downloadMemberDocument(\'' + documentModel.documentName + '\')"><span class="glyphicon glyphicon-download my-tooltip" title="DOWNLOAD"></span></a>';
			
			if(documentModel.isNew) {
			rowData+='<td>' + documentModel.documentName + '</td>' +
				'<td> ' + statusDesc + '</td>' +
				'<td><a href="javascript:deleteFile(\'' + documentModel.documentName + '\')"><span class="glyphicon glyphicon-trash my-tooltip" title="DELETE"></span></a> '+downloadLink+'</td></tr>';
			}
			
			if(!documentModel.isNew) {
				rowData+='</td><td>' + documentModel.documentName + '</td>' +
				'<td> ' + statusDesc + '</td>';
			}
			fileTableData += rowData;
		}
	}
	
	$('#documentList').html(fileTableData);
	 $('#checkAllDocument').prop("checked", false);
	unableSave();
	disableAllDeteleDownload();
}

function disableAllDeteleDownload(){
	var flag=0;
	for (var i of documentData) {
		var documentModel = i;
		if(documentModel.status == 'A'){
			flag=1;
		}
	}
	if(flag==0)
	{
		if (typeof downloadFiles != "undefined") {
			document.getElementById("downloadFiles").disabled = true;
		}
		if (typeof deleteAllFiles != "undefined") {
			document.getElementById("deleteAllFiles").disabled = true;
		}
	}
	else
	{
		if (typeof downloadFiles != "undefined") {
			document.getElementById("downloadFiles").disabled = false;
		}
		if (typeof deleteAllFiles != "undefined") {
			document.getElementById("deleteAllFiles").disabled = false;
		}
	}	
}

function deleteFile(fileName) {
	openConfirmDialog('Do you want to delete the selected document?', deleteFileAction, fileName);
	}
	function deleteFileAction(fileName) {
	    var documentIndex=documentData.findIndex(obj => obj.documentName == fileName);
		var documentModel = documentData[documentIndex];
		if(documentModel){
			if (documentModel.isNew) {
				documentData.splice(documentIndex, 1);
			} else {
				documentModel.status = 'D';
			}
		}
		renderFileTable();
	}
function unableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = false;
}
}



function openConfirmDialog(message, action, data) {
	 if(dataSubmittedApproval){
	  return;
	 }
		$('<div></div>').appendTo('body')
			.html('<div><h6>' + message + '</h6></div>')
			.dialog({
				modal: true,
				title: 'Confirm',
				zIndex: 10000,
				autoOpen: true,
				width: 'auto',
				resizable: false,
				buttons: {
					Yes: function () {
						$(this).dialog("close");
						action(data);
						
					},
					No: function () {
						$(this).dialog("close");
					}
				},
				close: function (_event, _ui) {
					$(this).remove();
				}
			});
	}

var dataSubmittedApproval=false;
function submitForm(url)
{
    var data = "";
    postData(url, data);
}
function deleteFiles() {
	openConfirmDialog('Do you want to delete all Files?', deleteMemberDocumentBatch);
}

function deleteMemberDocumentBatch() {
	
    documentData=[];
	renderFileTable();
	mySelect();
}

function mySelect(){
	
	
	var check=false;
		
		
		
		for(var i=0;i<documentData.length;i++){
			var documentModel = documentData[i];
				if(documentModel){
					var id="documentType"+i;
				if($('#'+id).val()=="0"){
					check=true;
					
				}
				 	
				}
		}
		
		
		if(check)
		{$('#errfileupload').show();
			$('#errfileupload').find('.error').html('Please Select Document Types for Every Document');
		}
		else{
			$('#errfileupload').hide();
		}
			
		
	}
