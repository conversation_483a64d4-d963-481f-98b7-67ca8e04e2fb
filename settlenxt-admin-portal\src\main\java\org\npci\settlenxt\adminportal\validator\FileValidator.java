package org.npci.settlenxt.adminportal.validator;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import org.npci.settlenxt.adminportal.repository.FileUploadRepository;
import org.npci.settlenxt.adminportal.validator.service.IValidationService;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Configuration
@EnableScheduling
@PropertySource("/application.properties")
public class FileValidator {
	
	@Autowired
	private Environment environment;
	
	@Autowired
	IValidationService validationService;
	
	@Autowired
	FileUploadRepository fileUploadRepository;
	
	
	// removed as it's handled in fileUploadService
	//@Scheduled(cron = "${FILE_VALIDATION_BATCH_TIME_CRON}")
	public void fileValidator() {
		if(Boolean.TRUE.toString().equals(environment.getProperty("FileValidationThreadStart"))) {
			Instant start = Instant.now();
			List<FileUploadDTO> fileUplodedList = getUploedFiles();
			// Code Create Thread Here
			ExecutorService executorService = Executors.newFixedThreadPool(4);
			List<Future<String>> result = new ArrayList<>();
			try {
				//Loop into groups
				for(FileUploadDTO fileUploadDTO:fileUplodedList) {
					result.add(executorService.submit(new AsyncFileValidator( validationService,fileUploadDTO,null))) ;
				}
			}finally {
				executorService.shutdown();
				if(executorService.isShutdown()) {
					Instant end = Instant.now();
					Duration timeElapsed = Duration.between(start, end);
					LocalTime t = LocalTime.MIDNIGHT.plus(timeElapsed);
					String diff = DateTimeFormatter.ofPattern("m:ss:A").format(t);
					log.info("Total Time taken " + diff );
				}
			}
			
		}
	}
	
	private List<FileUploadDTO> getUploedFiles() {
		try {
			return fileUploadRepository.getFilesByStatus(
					BaseCommonConstants.FileUploadStatusEnum.UPLOADED.getValue(), "Bank", environment.getProperty("SITE_ID"),
					environment.getProperty("INSTANCE_ID"));
		} catch (Exception e) {
			log.error("Error while getting files :", e);
			return Collections.emptyList();
		}
	}

}
