<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.npci.settlenxt.adminportal.service.ResponseCodeMapper">
	<select id="getAll" resultType="org.npci.settlenxt.portal.common.dto.ResponseCodeMapDTO">
		select src_interface as srcInterface,src_response as srcResponse,
		dest_interface as destInterface,dest_response as destResponse,
		dest_response_code_description as destResponseDesc
		from response_code_map;
	</select>
</mapper>