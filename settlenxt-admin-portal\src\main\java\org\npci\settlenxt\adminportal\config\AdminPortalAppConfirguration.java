package org.npci.settlenxt.adminportal.config;

import org.npci.settlenxt.common.interceptors.SettleNxtPortalInteceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@EnableAsync
public class AdminPortalAppConfirguration implements WebMvcConfigurer {
	@Autowired
	SettleNxtPortalInteceptor settleNxtPortalInteceptor;

	@Bean
	public MessageSource messageSource() {
		ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
		messageSource.setBasenames("classpath:/labels/adminportal_common_labels",
				"classpath:/labels/adminportal_configuration_labels",
				"classpath:/labels/adminportal_usermanagement_labels",
				"classpath:/labels/admiportal_membermanagement_labels",
				"classpath:/messages/adminportal_common_messages",
				"classpath:/messages/adminportal_configuration_messages",
				"classpath:/messages/adminportal_usermanagement_messages");
		messageSource.setDefaultEncoding("UTF-8");
		return messageSource;
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(settleNxtPortalInteceptor);
	}

}