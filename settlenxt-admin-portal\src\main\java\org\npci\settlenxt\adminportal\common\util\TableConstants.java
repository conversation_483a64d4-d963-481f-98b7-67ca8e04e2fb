package org.npci.settlenxt.adminportal.common.util;

import org.npci.settlenxt.portal.common.util.BaseTableConstants;

public final class TableConstants extends BaseTableConstants {
	private TableConstants() {
	}

	public static final String CURRENT_ACCT_ID = "current_acct_id";
	public static final String SAVINGS_ACCT_ID = "savings_acct_id";

	// Lookup Table
	public static final String LKP_TYPE = "lkptype";
	public static final String LKP_VALUE = "lkpvalue";
	public static final String LKP_DESC = "lkpdesc";

	public static final String STATE_ID = "state_id";
	public static final String STATE_NAME = "state_name";
	public static final String CITY_ID = "city_id";
	public static final String CITY_NAME = "city_name";
	public static final String COUNTRY_ID = "country_id";
	public static final String COUNTRY_NAME = "country_name";
	public static final String STATE_CODE = "state_code";
	public static final String BANK_MASTER_CODE = "bank_code";
	public static final String RTGS_ACCT_CODE = "rtgs_acct_id";

	// MC<PERSON> fields
	public static final String SUB_SCHEME = "sub_scheme";
	public static final String CARD_SUB_VAR = "card_sub_variant";
	public static final String PRGRM_DETAILS = "program_details";
	public static final String FORM_FACTOR = "form_factor";

	// IFSC Fields
	public static final String MAKER_COMMENTS = "maker_comments";
	public static final String NFS_CODE = "nfs_code";
	public static final String IFSC_DESCRIPTION = "ifsc_description";

	public static final String PHONE_NUMBER1 = "phone_number1";
	public static final String PHONE_NUMBER2 = "phone_number2";
	public static final String MOBILE_NUMBER1 = "mobile_number1";
	public static final String MOBILE_NUMBER2 = "mobile_number2";
	public static final String EMAIL_ID1 = "email_id1";
	public static final String EMAIL_ID2 = "email_id2";
	public static final String LEGAL_ADDRESS = "legal_address";
	public static final String ZIP_CODE = "zip_code";
	public static final String SAVINGS_ACC_NO = "savingsAccNumber";
	public static final String CURRENT_ACC_NO = "currentAccNumber";
	public static final String ADDR_TYPE = "address_type";
	public static final String CNT_NAME = "name";
	public static final String CNT_ADR = "cntAdd";
	public static final String CNT_PIN_CODE = "cntPincode";
	public static final String CARD_VARIANT = "card_sub_variant";
	public static final String PROG_DTLS = "program_details";
	public static final String ACTIV_DATE = "activation_date";
	public static final String DEACTIV_DATE = "deactivation_date";

	public static final String REC_COUNT = "REC_COUNT";
	public static final String FEATURES_BIN = "issuerbin_features";
	public static final String VARIANT_BIN = "bin_card_variant";

//	Fee Rate Table
	public static final String FEE_ID = "fee_id";
	public static final String MULTIPLIER = "multiplier";
	public static final String SETTLEMENT_BIN_ID = "settlement_bin_id";
	public static final String IS_COMPLETE = "is_complete";
	public static final String IS_ACTIVE = "is_active";
	public static final String CODE = "code";
	public static final String TXN_CURRENCY_DESC = "currencyDesc";
	public static final String UNIQUE_BANK_NAME = "UNIQUE_BANK_NAME";
	public static final String BIN_ID = "bin_id";

	public static final String FIELD_NAME = "field_name";
	public static final String RELATIONAL_OPERATOR = "relational_operator";
	public static final String FIELD_VALUE = "field_value";
	public static final String SIGNIFICANCE = "significance";
	public static final String SUB_FIELD_NAME = "sub_field_name";
	public static final String MAJOR_ID = "major_id";
	public static final String MINOR_ID = "minor_Id";
	public static final String SCHEME_CODE = "scheme_code";
	public static final String PRODUCT_CODE = "product_code";
	public static final String CARD_TYPE = "card_type";
	public static final String CARD_BRAND = "card_brand";
	public static final String FUNCTION_CODE = "function_code";
	public static final String PRIORITY = "priority";
	public static final String FEE_CONFIGS = "fee_configs";
	public static final String FEE_CONFIG_ID = "fee_config_id";
	public static final String REQUEST_ID = "request_id";
	public static final String REMARKS = "remarks";
	// FEE MAJOR MINOR

	public static final String ACQ_PROC_FEE_ID = "acq_processing_fee_id";
	public static final String ACQ_ASS_FEE_ID = "acq_assessment_fee_id";
	public static final String ISS_PROC_FEE_ID = "iss_processing_fee_id";
	public static final String ISS_ASS_FEE_ID = "iss_assessment_fee_id";
	public static final String ACQ_AUTH_FEE_ID = "acq_auth_fee_id";
	public static final String ISS_AUTH_FEE_ID = "iss_auth_fee_id";
	public static final String OFFLINE_ALLOWED = "offline_allowed";
	
	public static final String WEB_SITE = "website";
	public static final String CNT_EMAIL = "email_id";
	public static final String AUTH_OFFICER_DESG = "auth_officer_desg";
	public static final String ISS_OFFLINE_ALLOWED = "offline_allowed";

	// card config
	public static final String CARD_CONFIG_ID = "card_config_id";

	// public static final String CARD_TYPE ="card_type"
	public static final String CARD_CONFIG_VARIANT = "card_variant";
	public static final String BASE_FEE = "base_fee";
	public static final String FROM_DATE = "from_date";
	public static final String TO_DATE = "to_date";

	// feature fee
	public static final String DETAILS = "details";
	public static final String FEATURE_FEE = "feature_fee";
	public static final String FEATURE = "feature";

	// MCPR BIN DETAILS
	public static final String MCPR_BIN_DATA_DETAILS_ID = "mcpr_bin_data_details_id";
	public static final String MONTH_ENDING = "month_ending";
	public static final String MONTH = "month";
	public static final String YEAR = "year";
	public static final String BIN_NO = "bin_no";

	public static final String GST_CODE = "sys_key";

	public static final String TRANSACTION_CYCLE = "func_code_desc";
	// News & Alerts
	public static final String NEWS_ID = "news_alert_id";
	public static final String NEWS_TYPE = "news_alert_type";
	public static final String TITLE = "title";
	public static final String SUB_TITLE = "subtitle";
	public static final String FROM_DATE_NEWS = "valid_from";
	public static final String TO_DATE_NEWS = "valid_to";
	public static final String SUMMARY = "summary";
	public static final String FOOTER_DATA = "footer_data";
	public static final String REFERENCE_NUMBER = "reference_number";
	public static final String PUBLISH_TYPE = "is_public";
	public static final String PERIOD_TYPE = "period_type";
	public static final String TRIGGER_MAIL = "trigger_mail";
	public static final String COMMENT = "comments";
	public static final String SCHEDULE_ID = "schedule_id";
	public static final String CREATED_ON = "created_on";
	public static final String FREQ_FROM = "freq_from";
	public static final String FREQ_TO = "freq_to";
	public static final String SCHEDULE_NAME = "schedule_name";
	public static final String SCHEDULE_DESC = "schedule_desc";
	public static final String VALID_FROM_DATE = "valid_from";

	public static final String FEATURE_ISS_BIN = "features";
	public static final String PUBLISH = "publish_type";
	public static final String USER_NAME = "user_name";
	public static final String CRITICAL = "is_critical";
	public static final String GST_CODES = "gst_code";
	public static final String NET_MIN = "net_min";
	public static final String NET_MAX = "net_max";
	public static final String PARTICIPANT_ID_NFS = "nfs_id";
	public static final String FEE_TYPE_CODE = "fee_type_code";
}
