package org.npci.settlenxt.adminportal.controllers;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.service.ICrossSiteIcStatusService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import lombok.extern.slf4j.Slf4j;

/**
 * Controller used for 
 * <li> get Cross site IC status details</li>
 * <li> retry the internal cycle number or outgoing files</li>
 * <AUTHOR>
 *
 */
@Slf4j
@Controller
public class CrossSiteIcStatusController extends BaseController {
	
	private static final Logger logger = LogManager.getLogger(CrossSiteIcStatusController.class);

	@Autowired
	ICrossSiteIcStatusService crossSiteIcStatusService;

	/**
	 * <li> Fetch cross site IC status details from Integrity checker service and redirect 
	 * to crossSiteIcStatus JSP page.</li>
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/showCrossSiteIcStatus")
	public String getICStatusDataForCrossSite(@ModelAttribute(CommonConstants.CYCLE_MANAGEMENT_DTO) CycleManagementDTO cycleManagementDTO, Model model) throws SettleNxtException {
		logger.info("CrossSiteIcStatusController :getICStatusDataForCrossSite() : in");
		cycleManagementDTO = crossSiteIcStatusService.getCrossSiteIcStatusDetails(cycleManagementDTO);
		model.addAttribute(CommonConstants.CYCLE_MANAGEMENT_DTO, cycleManagementDTO);
		logger.info("CrossSiteIcStatusController :getICStatusDataForCrossSite() : out");
		return getView(model, "crossSiteIcStatus");
	}
	
	/**
	 * This method is used for retry the internal cycle number or outgoing files
	 * @param requestBody
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/retryIcnOrOutgoingFile")
	@ResponseBody
	public String retryICNOrOutgoingFiles(@RequestBody CycleManagementDTO cycleManagementDTO) throws SettleNxtException {
		return crossSiteIcStatusService.retryIcnOrOutgoingFiles(cycleManagementDTO);
	}


}
