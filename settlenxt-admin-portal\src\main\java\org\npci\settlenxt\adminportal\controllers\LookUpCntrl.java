package org.npci.settlenxt.adminportal.controllers;

import java.util.List;
import java.util.Locale;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.service.LookupService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.BaseSessionDTO;
import org.npci.settlenxt.portal.common.dto.LookUpDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
public class LookUpCntrl extends BaseController {

	@Autowired
	private LookupService lookupService;

	@Autowired
	private BaseSessionDTO sessionDTO;

	@Autowired
	private MessageSource messageSource;

	private static final String LOOK_UP = "lookUp";
	private static final String ADD_EDIT_LOOKUP = "addEditLookUp";
	private static final String VIEW_LOOKUP = "viewLookUp";
	private static final String VIEW_APPROVAL_LOOKUP = "viewApprovalLookUp";
	private static final String LOOKUP_TYPE = "lookUpType";
	public static final String SHOW_CHECKBOX = "showCheckBox";

	private static final String STATUS = "status";

	@PostMapping("/getLookUpList")
	@PreAuthorize("hasAuthority('View LookUp')")
	public String getLookUpList(Model model,
			@RequestParam(value = "requestState", required = false) String requestState) {
		List<LookUpDTO> lookUpInfoList = lookupService.getLookupList();
		model.addAttribute(CommonConstants.SHOW_ADD_BTN, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.FINAL_LOOK_UP, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.SHOW_ACTIVE, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.LOOKUP_LIST, lookUpInfoList);
		model.addAttribute(CommonConstants.ADD_FUNCTION_CODE, CommonConstants.TRANSACT_YES);
		return getView(model, LOOK_UP);

	}

	@PostMapping("/addLookUp")
	public String addLookUp(Model model) {
		LookUpDTO lookUpDTO = new LookUpDTO();
		List<LookUpDTO> lookUpListType = lookupService.getLookupListType();
		model.addAttribute(LOOKUP_TYPE, lookUpListType);

		model.addAttribute(CommonConstants.SUBMIT_LOOKUP_DETAILS, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.ADD_LOOK_UP, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.ADD_BACK_BTN, CommonConstants.ADD_BACK_BTN);
		model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
		return getView(model, ADD_EDIT_LOOKUP);
	}

	@PostMapping("/submitLookUpDetails")
	@PreAuthorize("hasAuthority('Add LookUp')")
	public String submitLookUpDetails(@ModelAttribute LookUpDTO lookUpDTO, Model model) {

		model.addAttribute(CommonConstants.ADD_LOOK_UP, CommonConstants.TRANSACT_YES);
		try {
			lookUpDTO = lookupService.addEditLookUp(lookUpDTO);
		} catch (Exception ex) {
			model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
			model.addAttribute(CommonConstants.ADD_LOOK_UP, CommonConstants.TRANSACT_YES);
			return handleErrorCodeAndForward(model, ADD_EDIT_LOOKUP, ex);

		}
		List<LookUpDTO> lookUpListType = lookupService.getLookupListType();
		model.addAttribute(LOOKUP_TYPE, lookUpListType);
		model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
		model.addAttribute(CommonConstants.SHOW_BTN, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.SAVE_BACK_BTN, CommonConstants.SAVE_BACK_BTN);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("lookUp.addSuccess.msg"));
		return getView(model, ADD_EDIT_LOOKUP);

	}

	@PostMapping("/getPendingLookUpList")
	@PreAuthorize("hasAuthority('View LookUp')")
	public String getPendingLookUpList(Model model) {
		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		List<LookUpDTO> pendingLookUpList = lookupService.getPendingLookupListStg();
		model.addAttribute(CommonConstants.PENDING_LOOK_UP_LIST, pendingLookUpList);
		model.addAttribute(CommonConstants.APPROVAL_ACTIVE, CommonConstants.TRANSACT_YES);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}

		return getView(model, LOOK_UP);
	}

	@PostMapping("/viewLookUp")
	@PreAuthorize("hasAuthority('View LookUp')")
	public String viewLookUp(Model model, @RequestParam("lookupId") int lookupId) {
		LookUpDTO lookUpDTO = lookupService.getLookUpInfoFromMain(lookupId);
		model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
		return getView(model, VIEW_LOOKUP);

	}

	@PostMapping("/viewApprovalLookUp")
	@PreAuthorize("hasAuthority('View LookUp')")
	public String viewApprovalLookUp(Model model, @RequestParam("lookupId") int lookupId) {
		model.addAttribute(CommonConstants.EDIT_LOOK_UP, CommonConstants.TRANSACT_YES);
		LookUpDTO lookUpDTO = lookupService.getLookUpInfoFromStg(lookupId);
		model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
		return getView(model, VIEW_APPROVAL_LOOKUP);

	}

	@PostMapping("/editLookUp")
	@PreAuthorize("hasAuthority('Edit LookUp')")
	public String editLookUp(Model model, @RequestParam(value = "lookupId", required = false) int lookupId) {
		model.addAttribute(CommonConstants.EDIT_LOOK_UP, CommonConstants.TRANSACT_YES);
		LookUpDTO lookUpDTO = lookupService.getEditLookUpInfoFromMain(lookupId);
		model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
		return getView(model, ADD_EDIT_LOOKUP);

	}

	@PostMapping("/updateLookUpDetails")
	@PreAuthorize("hasAuthority('Edit LookUp')")
	public String updateLookUpDetails(@ModelAttribute LookUpDTO lookUpDTO, Model model) {
		model.addAttribute(CommonConstants.EDIT_LOOK_UP, CommonConstants.TRANSACT_YES);
		try {
			lookupService.updateLookUp(lookUpDTO);
		} catch (Exception ex) {
			model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
			model.addAttribute(CommonConstants.EDIT_LOOK_UP, CommonConstants.TRANSACT_YES);
			return handleErrorCodeAndForward(model, ADD_EDIT_LOOKUP, ex);
		}
		model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
		model.addAttribute(CommonConstants.SHOW_BTN, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.SAVE_BACK_BTN, CommonConstants.SAVE_BACK_BTN);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("lookUp.editSuccess.msg"));
		return getView(model, ADD_EDIT_LOOKUP);

	}

	@PostMapping("/approveLookUp")
	@PreAuthorize("hasAuthority('Approve LookUp')")
	public String approveActionCode(@RequestParam("lookupId") int lookupId, @RequestParam("status") String status,
			@RequestParam("remarks") String remarks, Model model) {
		LookUpDTO lookUpDTO = lookupService.approveOrRejectLookUp(lookupId, status, remarks);
		model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("lookup.approveSuccess.msg"));
		} else if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("lookUp.reject.msg"));
		}
		return getView(model, VIEW_APPROVAL_LOOKUP);
	}

	@PostMapping("/getRejLookUp")
	@PreAuthorize("hasAuthority('View LookUp')")
	public String getRejLookUp(@RequestParam("lookupId") int lookupId, Model model) {
		LookUpDTO lookUpDTO = lookupService.getLookUpInfoFromStg(lookupId);
		model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
		return getView(model, VIEW_LOOKUP);
	}

	@PostMapping("/discardLookUp")
	@PreAuthorize("hasAuthority('Edit LookUp')")
	public String discardLookUp(@RequestParam("lookupId") int lookupId, Model model) {
		LookUpDTO lookUpDTO = lookupService.discardLookUp(lookupId);
		model.addAttribute(CommonConstants.LOOK_UP_DTO, lookUpDTO);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("lookup.discard.msg"));
		return getView(model, VIEW_APPROVAL_LOOKUP);
	}

	@PostMapping("/approveOrRejectBulkLookUp")
	@PreAuthorize("hasAuthority('Approve LookUp')")
	public String approveOrRejectBulkLookUp(@RequestParam("bulkLookUpList") String bulkLookUpList,
			@RequestParam("status") String status, Model model) {
		String remarks = "";
		if (status.equals(CommonConstants.STATUS_APPROVE)) {
			remarks = CommonConstants.BULK_APPROVE;
		} else if (status.equals(CommonConstants.STATUS_REJECT)) {
			remarks = CommonConstants.BULK_REJECT;
		}
		LookUpDTO lookUpDTO = lookupService.updateApproveOrRejectBulkLookUp(bulkLookUpList, status, remarks);
		checkActionCodeApproveStatus(lookUpDTO, model);
		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		List<LookUpDTO> pendingLookUpList = lookupService.getPendingLookupListStg();
		model.addAttribute(CommonConstants.PENDING_LOOK_UP_LIST, pendingLookUpList);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(BaseCommonConstants.SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(BaseCommonConstants.SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}
		return getView(model, LOOK_UP);
	}

	private void checkActionCodeApproveStatus(LookUpDTO lookUpDTO, Model model) {
		Locale locale=Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(lookUpDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_LookUpApproved", null, locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_LookUpRejected", null, locale));
		}
	}

	@PostMapping("/checkDupLookUpDetails")
	@PreAuthorize("hasAuthority('View LookUp')")
	public ResponseEntity<Object> checkDupLookUpDetails(@RequestParam("lkpType") String lkpType,
			@RequestParam("lkpVal") String lkpVal, @RequestParam("lkpDesc") String lkpDesc,
			@RequestParam("flow") String flow) {

		boolean result = lookupService.checkDupLookUpDetails(lkpType, lkpVal, lkpDesc, flow);

		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}
}
