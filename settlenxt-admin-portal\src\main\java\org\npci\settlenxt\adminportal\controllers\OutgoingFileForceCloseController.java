package org.npci.settlenxt.adminportal.controllers;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.service.IOutgoingFileForceCloseService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import lombok.extern.slf4j.Slf4j;

/**
 * Controller used for 
 * <li> Force close the outgoing files</li>
 * <AUTHOR>
 *
 */
@Slf4j
@Controller
public class OutgoingFileForceCloseController extends BaseController {
	
	private static final Logger logger = LogManager.getLogger(OutgoingFileForceCloseController.class);

	@Autowired
	IOutgoingFileForceCloseService outgoingFileForceCloseService;

	/**
	 * Fetch list of failed/inprogress/forceCloseSuccess/forceCloseFailed outgoing files.
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/getFailedInprogressOutgoingFiles")
	public String getFailedIprogerssOutgoingFileList(@ModelAttribute(CommonConstants.CYCLE_MANAGEMENT_DTO) CycleManagementDTO cycleManagementDTO, Model model) throws SettleNxtException {
		logger.info("getFailedIprogerssOutgoingFileList : in");
		cycleManagementDTO = outgoingFileForceCloseService.getInprogressFailedOutgoingFileList(cycleManagementDTO);
		model.addAttribute(CommonConstants.CYCLE_MANAGEMENT_DTO, cycleManagementDTO);
		logger.info("getFailedIprogerssOutgoingFileList : out");
		return getView(model, "outgoingFileForceClose");
	}
	
	/**
	 * This method is used to force close the outgoing file
	 * 
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/forceCloseOutgoingFiles")
	@ResponseBody
	public String getReportRegenStatus(@RequestBody CycleManagementDTO cycleManagementDTO, Model model)
			throws SettleNxtException {
		return outgoingFileForceCloseService.forceCloseOutgoingFiles(cycleManagementDTO);
	}

}
