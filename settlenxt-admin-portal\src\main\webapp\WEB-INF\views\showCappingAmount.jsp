<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ page import="java.time.format.DateTimeFormatter"%>

<script type="text/javascript">
<c:if test="${showMainTab eq 'Y'}">
var actionColumnIndex = 19;
var firstColumnToBeSkippedInFilterAndSort=false;
</c:if>
<c:if test="${showApprovalTab eq 'Y'}">
<c:if test="${showCheckBox eq 'Y'}">
var actionColumnIndex = 21;
var firstColumnToBeSkippedInFilterAndSort=true;
</c:if>
<c:if test="${showCheckBox eq 'N'}">
var actionColumnIndex = 20;
var firstColumnToBeSkippedInFilterAndSort=false;
</c:if>
</c:if>
</script>


<div id="errorStatus2" class="alert alert-danger" role="alert"
	style="display: none"></div>

<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>
<script src="./static/js/validation/SearchCappingAmount.js"
	type="text/javascript"></script>


<script>
	var cappingIdListPendings = [];
	

	<c:if test="${not empty pendingCapAmountList}">
	<c:forEach items="${pendingCapAmountList}" var="user">
	<c:if test="${user.requestState eq 'P' }">
	
	cappingIdListPendings.push('${user.actionCode}${user.mccGroup}${user.binCardBrandId}${user.binCardTypeId}${user.fieldName}${user.relOperator}${user.fieldValue}');
	
	</c:if>
	</c:forEach>
	</c:if>
	

	</script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />



<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
	width: 150px;
}
</style>

<div class="modal fade" id="toggleModal" tabindex="-1" role="dialog"
	aria-labelledby="toggleApproveCappingAmount" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Capping IDs</label>
				<p id="cappingIds" />
			</div>



			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveOrRejectBulk('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveOrRejectBulk('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>

<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Y'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/showCappingAmount');" role="tab"
			data-toggle="tab"><span class="glyphicon glyphicon-list-alt">
		</span> <spring:message code="cap.MainTab.title" /></a>


		<c:choose>
			<c:when test="${showApprovalTab eq 'Y'}">
				<li role="presentation" class="active">
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" role="tab"
			onclick="submitForm('/cappingAmountPendingForApproval');"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> </span> <spring:message
				code="ifsc.approvalTab.title" /></a>
	</ul>




	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
	<div class="row">
	<div class="col-sm-12">
	<sec:authorize access="hasAuthority('Add Capping Amount')">
								<c:if test="${showAddButton eq 'Y'}">

									<a class="btn btn-success pull-right btn_align" href="#"
										onclick="submitForm('/addCappingAmount');"
										style="margin-top: -5px 0px 2px 0px;"><em class="glyphicon-plus"></em> <spring:message
											code="cap.addCappingBtn" /> </a>

								</c:if>
							</sec:authorize>
							</div>

	<div class="col-sm-12">
		<button class="btn  pull-right btn_align" id="clearFilters">
			<spring:message code="ifsc.clearFiltersBtn" />
		</button>


		&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
			id="excelExport"><spring:message code="ifsc.exportBtn" /> </a>
		&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
			id="csvExport"><spring:message code="ifsc.csvBtn" /> </a>


	</div>
	</div>
	
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default">
						<div class="panel-heading">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message
										code="msg.lbl.CappingAmountList" /></span></strong>

							
							<c:if test="${showApprovalTab eq 'Y'}">
								<sec:authorize access="hasAuthority('Approve Capping Amount')">


									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveOrRejectBulk('A','No')" id="submitButton"
										value="<spring:message code="am.lbl.Approve" />" />
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveOrRejectBulk('R','No')" id="submitButton"
										value="<spring:message code="am.lbl.Reject" />" />
								</sec:authorize>
							</c:if>
						</div>

						<c:if test="${not empty cappingAmountList and showMainTab eq 'Y'}">
							<div class="panel-body">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width:150%;">
										<caption style="display:none;">cappingamount</caption>
										<thead>
											<tr>
												<th scope = "col"><spring:message code="am.lbl.actionCode" /></th>
												<th scope = "col"><spring:message code="am.lbl.mccGroup" /></th>
												<th scope = "col"><spring:message code="am.lbl.binCardBrandId" /></th>
												<th scope = "col"><spring:message code="am.lbl.binCardTypeId" /></th>
												<th scope = "col"><spring:message code="am.lbl.capName" /></th>
												<th scope = "col"><spring:message code="am.lbl.capType" /></th>
												<th scope = "col"><spring:message code="am.lbl.fieldName" /></th>
												<th scope = "col"><spring:message code="am.lbl.relOperator" /></th>
												<th scope = "col"><spring:message code="am.lbl.fieldValue" /></th>
												<th scope = "col"><spring:message code="am.lbl.amountFlag" /></th>
												<th scope = "col"><spring:message code="am.lbl.flat" /></th>
												<th scope = "col"><spring:message code="am.lbl.percentage" /></th>
												<th scope = "col"><spring:message code="am.lbl.amountCapMax" /></th>
												<th scope = "col"><spring:message code="am.lbl.amountCapMin" /></th>
												<th scope = "col"><spring:message code="am.lbl.createdBy" /></th>
												<th scope = "col"><spring:message code="am.lbl.createdOn" /></th>
												<th scope = "col"><spring:message code="am.lbl.lastUpdatedBy" /></th>
												<th scope = "col"><spring:message code="am.lbl.lastUpdatedOn" /></th>
												<th scope = "col"><spring:message code="am.lbl.status" /></th>
											</tr>
										</thead>
										<tbody>

											<c:forEach var="user" items="${cappingAmountList}">

												<tr
													onclick="javascript:viewCappingAmountInfo('${user.actionCode}','${user.mccGroup}','${user.binCardBrandId}','${user.binCardTypeId}','${user.fieldName}','${user.relOperator}','${user.fieldValue}','/getCappingAmount')">
													
													<td>${user.actionCode}</td>
													<td>${user.mccGroup}</td>
													<td>${user.binCardBrandId}</td>
													<td>${user.binCardTypeId}</td>
													<td>${user.capName}</td>
													<td>${user.capType}</td>
													<td>${user.fieldName}</td>
													<td>${user.relOperator}</td>
													<td>${user.fieldValue}</td>
													<td>${user.amountFlag}</td>
													<td>${user.flat}</td>
													<td>${user.percentage}</td>
													<td>${user.amountCapMax}</td>
													<td>${user.amountCapMin}</td>
													<td>${user.createdBy}</td>
													<td>${user.createdOn}</td>
													<td>${user.lastUpdatedBy}</td>
													<td>${user.lastUpdatedOn}</td>
													<td><c:if test="${user.status=='A' }">
															<spring:message code="ifsc.activeStatus" />
														</c:if> <c:if test="${user.status=='I' }">
															<spring:message code="ifsc.inactiveStatus" />
														</c:if></td>

												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</c:if>


						<c:if test="${empty cappingAmountList and  showMainTab eq 'Y'}">
							<div class="table-responsive">
								<table id="tabnew" class="table table-striped table-bordered"
									style="width:100%;">
									<caption style="display:none;">cappingamount</caption>
									<thead>
										<tr>
												<th scope = "col"><spring:message code="am.lbl.actionCode" /></th>
												<th scope = "col"><spring:message code="am.lbl.mccGroup" /></th>
												<th scope = "col"><spring:message code="am.lbl.binCardBrandId" /></th>
												<th scope = "col"><spring:message code="am.lbl.binCardTypeId" /></th>
												<th scope = "col"><spring:message code="am.lbl.capName" /></th>
												<th scope = "col"><spring:message code="am.lbl.capType" /></th>
												<th scope = "col"><spring:message code="am.lbl.fieldName" /></th>
												<th scope = "col"><spring:message code="am.lbl.relOperator" /></th>
												<th scope = "col"><spring:message code="am.lbl.fieldValue" /></th>
												<th scope = "col"><spring:message code="am.lbl.amountFlag" /></th>
												<th scope = "col"><spring:message code="am.lbl.flat" /></th>
												<th scope = "col"><spring:message code="am.lbl.percentage" /></th>
												<th scope = "col"><spring:message code="am.lbl.amountCapMax" /></th>
												<th scope = "col"><spring:message code="am.lbl.amountCapMin" /></th>
												<th scope = "col"><spring:message code="am.lbl.createdBy" /></th>
												<th scope = "col"><spring:message code="am.lbl.createdOn" /></th>
												<th scope = "col"><spring:message code="am.lbl.lastUpdatedBy" /></th>
												<th scope = "col"><spring:message code="am.lbl.lastUpdatedOn" /></th>
												<th scope = "col"><spring:message code="am.lbl.status" /></th>
											</tr>
									</thead>
									<tbody>
									</tbody>
								</table>
							</div>
						</c:if>






						<div class="row">
							<div class="col-md-12">
								<div class="panel panel-default">

									<div class="panel-body">

										<c:if
											test="${not empty pendingCapAmountList and showApprovalTab eq 'Y'}">
											<div class="table-responsive">
												<table id="tabnew"
													class="table table-striped table-bordered" style="width:100%;">
													<caption style="display:none;">cappingamount</caption>
													<thead>
														<tr>
															<sec:authorize
																access="hasAuthority('Approve Capping Amount')">
																<th scope = "col"><input type=checkbox name='selectAllCheck'
																	id="selectAll" value='Hi' data-toggle="modal"
																	data-target="toggleModal"></input></th>
															</sec:authorize>
															<th scope = "col"><spring:message code="am.lbl.actionCode" /></th>
															<th scope = "col"><spring:message code="am.lbl.mccGroup" /></th>
															<th scope = "col"><spring:message code="am.lbl.binCardBrandId" /></th>
															<th scope = "col"><spring:message code="am.lbl.binCardTypeId" /></th>
															<th scope = "col"><spring:message code="am.lbl.capName" /></th>
															<th scope = "col"><spring:message code="am.lbl.capType" /></th>
															<th scope = "col"><spring:message code="am.lbl.fieldName" /></th>
															<th scope = "col"><spring:message code="am.lbl.relOperator" /></th>
															<th scope = "col"><spring:message code="am.lbl.fieldValue" /></th>
															<th scope = "col"><spring:message code="am.lbl.amountFlag" /></th>
															<th scope = "col"><spring:message code="am.lbl.flat" /></th>
															<th scope = "col"><spring:message code="am.lbl.percentage" /></th>
															<th scope = "col"><spring:message code="am.lbl.amountCapMax" /></th>
															<th scope = "col"><spring:message code="am.lbl.amountCapMin" /></th>
															<th scope = "col"><spring:message code="am.lbl.requestState" /></th>
															<th scope = "col"><spring:message code="am.lbl.checkerComments" /></th>
															<th scope = "col"><spring:message code="am.lbl.createdBy" /></th>
															<th scope = "col"><spring:message code="am.lbl.createdOn" /></th>
															<th scope = "col"><spring:message code="am.lbl.lastUpdatedBy" /></th>
															<th scope = "col"><spring:message code="am.lbl.lastUpdatedOn" /></th>
														</tr>
													</thead>
													<tbody>
														<c:forEach var="user" items="${pendingCapAmountList}">

															<c:if test="${user.requestState  eq 'P'}">
																<tr
																	onclick="javascript:viewCappingAmountInfo('${user.actionCode}','${user.mccGroup}','${user.binCardBrandId}','${user.binCardTypeId}','${user.fieldName}','${user.relOperator}','${user.fieldValue}','/viewApproveCappingAmount')">
																	<sec:authorize
																		access="hasAuthority('Approve Capping Amount')">
																		<td onclick=event.stopPropagation()><input
																			type=checkbox name='type' id="selectSingle"
																			onclick="mySelect();" value='${user.actionCode}${user.mccGroup}${user.binCardBrandId}${user.binCardTypeId}${user.fieldName}${user.relOperator}${user.fieldValue}'></input></td>
																	</sec:authorize>
															</c:if>
															<c:if test="${ user.requestState  eq 'R'}">
																<tr
																	onclick="javascript:viewRejCappingAmountInfo('${user.actionCode}','${user.mccGroup}','${user.binCardBrandId}','${user.binCardTypeId}','${user.fieldName}','${user.relOperator}','${user.fieldValue}','/getRejCappingAmount')">
																	<sec:authorize
																		access="hasAuthority('Approve Capping Amount')">
																		<td><input type=checkbox name='types'
																			style="display: none;" value='${user}'></input></td>
																	</sec:authorize>
															</c:if>
															    <td>${user.actionCode}</td>
																<td>${user.mccGroup}</td>
																<td>${user.binCardBrandId}</td>
																<td>${user.binCardTypeId}</td>
																<td>${user.capName}</td>
																<td>${user.capType}</td>
																<td>${user.fieldName}</td>
																<td>${user.relOperator}</td>
																<td>${user.fieldValue}</td>
																<td>${user.amountFlag}</td>
																<td>${user.flat}</td>
																<td>${user.percentage}</td>
																<td>${user.amountCapMax}</td>
																<td>${user.amountCapMin}</td>

																<td>${user.requestState =='P' ? 'Pending for Approval' : 'Rejected'}</td>
																<td>${user.checkerComments}</td>
																<td>${user.createdBy}</td>
																<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
																		value="${user.createdOn}" /></td>
																<td>${user.lastUpdatedBy}</td>
																<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
																		value="${user.lastUpdatedOn}" /></td>
															</tr>
														</c:forEach>
													</tbody>
												</table>
											</div>
										</c:if>

										<c:if
											test="${empty pendingCapAmountList and showApprovalTab eq 'Y'}">
											<div class="table-responsive">
												<table id="tabnew"
													class="table table-striped table-bordered" style="width:100%;">
													<caption style="display:none;">cappingamount</caption>
													<thead>
														<tr>
															<th scope = "col"><spring:message code="am.lbl.actionCode" /></th>
															<th scope = "col"><spring:message code="am.lbl.mccGroup" /></th>
															<th scope = "col"><spring:message code="am.lbl.binCardBrandId" /></th>
															<th scope = "col"><spring:message code="am.lbl.binCardTypeId" /></th>
															<th scope = "col"><spring:message code="am.lbl.capName" /></th>
															<th scope = "col"><spring:message code="am.lbl.capType" /></th>
															<th scope = "col"><spring:message code="am.lbl.fieldName" /></th>
															<th scope = "col"><spring:message code="am.lbl.relOperator" /></th>
															<th scope = "col"><spring:message code="am.lbl.fieldValue" /></th>
															<th scope = "col"><spring:message code="am.lbl.amountFlag" /></th>
															<th scope = "col"><spring:message code="am.lbl.flat" /></th>
															<th scope = "col"><spring:message code="am.lbl.percentage" /></th>
															<th scope = "col"><spring:message code="am.lbl.amountCapMax" /></th>
															<th scope = "col"><spring:message code="am.lbl.amountCapMin" /></th>
															<th scope = "col"><spring:message code="am.lbl.requestState" /></th>
															<th scope = "col"><spring:message code="am.lbl.checkerComments" /></th>
															<th scope = "col"><spring:message code="am.lbl.createdBy" /></th>
															<th scope = "col"><spring:message code="am.lbl.createdOn" /></th>
															<th scope = "col"><spring:message code="am.lbl.lastUpdatedBy" /></th>
															<th scope = "col"><spring:message code="am.lbl.lastUpdatedOn" /></th>
														
														</tr>
													</thead>
													<tbody>
													</tbody>
												</table>
											</div>
										</c:if>
									</div>
								</div>
							</div>
						</div>







					</div>
				</div>
			</div>



		</div>

	</div>







</div>