package org.npci.settlenxt.adminportal.controllers;

import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.EscalationService;
import org.npci.settlenxt.adminportal.service.MasterService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.BaseEscalationDTO;
import org.npci.settlenxt.portal.common.dto.LookUpDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class EscalationController extends BaseController {

	@Autowired
	private MessageSource messageSource;
	@Autowired
	EscalationService escalationSvc;

	@Autowired
	private MasterService masterService;

	@Autowired
	private SessionDTO sessionDTO;

	private static final String ADD_ESCALATION = "addEditEscalation";
	private static final String VIEW_ESCALATION = "viewEscalation";
	private static final String VIEW_ESCALATION_MATRIX = "viewEscalationMatrix";
	private static final String ESCALATION_LIST = "escalationList";
	private static final String BASE_ESC_DTO = "baseEscalationDTO";
	private static final String MEM_LIST = "memberList";
	private static final String DEPT_LIST = "departmentList";
	private static final String LVL_LIST = "levelList";
	private static final String SHOW_CHECKBOX = "showCheckBox";
	private static final String SUCCESS_STATUS = "successStatus";
	private static final String ERROR_STATUS = "errorStatus";
	private static final String IS_BANK_LIST = "bankList";
	private static final String STATE_LIST="stateList";
	private static final String SHOW= "show";
	private static final String ESCALATION_DTO="escalationDTO";
	private static final String ACQ_FLAG="ackFlag";
	private static final String SAVE_ESCALATION="saveEscalation"; 
	private static final String ESC_APPROVAL_MSG="Escalation Details Sent For Approval";
	private static final String SAVE="save";
	private static final String ESC_ERROR_SAVE_DATA= "Error saving data!";
	private static final String ESC_MSG_APPROVED="Escalation Details Approved";
	private static final String ESC_MSG_REJECTED="Escalation Details Rejected";
	private static final String ESC_ERROR_APP_DATA="Error Approving data!";
	
	
	@PostMapping("/addEscalation")
	@PreAuthorize("hasAnyAuthority('Add Escalation Matrix' , 'Approve Escalation Matrix')")
	public String addEscalation(@RequestParam String memberId,
			@RequestParam(value = "editFlag", required = false, defaultValue = "N") String editFlag, Model model) {

		try {
			String pId = URLDecoder.decode(memberId, "UTF-8");
			List<String> levelList = new ArrayList<>();
			levelList.add("Level_1");
			levelList.add("Level_2");
			levelList.add("Level_3");
			levelList.add("Level_4");
			levelList.add("Level_5");
			List<LookUpDTO> stateList = masterService.getStateMasterList();
			model.addAttribute(STATE_LIST, stateList);
			List<BaseEscalationDTO> departmentList = escalationSvc.getDepartmentList();

			List<BaseEscalationDTO> memberList = null;

			List<BaseEscalationDTO> escalationList = escalationSvc.getContactListStg(pId);
			if (!escalationList.isEmpty() && !escalationList.get(0).getRequestState().equals(CommonConstants.REQUEST_STATE_REJECTED)) {

				escalationList = escalationSvc.getContactListMain(pId);

			}
			if (editFlag.equals(CommonConstants.YES_FLAG)) {
				memberList = escalationSvc.getMemberListForReject();
			} else {
				memberList = escalationSvc.getMemberList();
			}
			BaseEscalationDTO escalationDTO = new BaseEscalationDTO();
			escalationDTO.setMemberId(pId);
			escalationDTO.setCreatedOn(LocalDateTime.now());
			Map<String, String> lookUpMap;
			lookUpMap = memberList.stream()
					.collect(Collectors.toMap(BaseEscalationDTO::getValue, BaseEscalationDTO::getLabel));
			escalationDTO.setBankMemberName(lookUpMap.get(pId));
			List<BaseEscalationDTO> escalationLi = escalationSvc.getContactList(pId);
			if (!escalationLi.isEmpty()) {
				escalationDTO.setRequestState(escalationLi.get(0).getRequestState());
			}
			if (!escalationList.isEmpty()) {

				escalationList = checkAndSetlevel(escalationList, levelList, departmentList);

				model.addAttribute(ESCALATION_LIST, escalationList);
			} else {

				List<BaseEscalationDTO> escalationLists = escalationSvc.getContactList(pId);
				if (!escalationLists.isEmpty()) {

					escalationLists = checkAndSetlevel(escalationLists, levelList, departmentList);
					model.addAttribute(ESCALATION_LIST, escalationLists);
				}
			}
			// add npci member

			model.addAttribute(SHOW, CommonConstants.REQUEST_STATE_REJECTED);
			BaseEscalationDTO nPCIData = new BaseEscalationDTO();
			nPCIData.setLabel(CommonConstants.NPCI_MEMBER);
			nPCIData.setValue("999");
			memberList.add(nPCIData);
			model.addAttribute(MEM_LIST, memberList);
			model.addAttribute(DEPT_LIST, departmentList);
			model.addAttribute(LVL_LIST, levelList);
			model.addAttribute(IS_BANK_LIST, CommonConstants.YES_FLAG);

			model.addAttribute(ESCALATION_DTO, escalationDTO);
			model.addAttribute(ACQ_FLAG, true);
			model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.YES_FLAG);

		} catch (Exception ex) {

			throw new SettleNxtException("Error Adding Escalation", "",ex);
		}
		return getView(model, ADD_ESCALATION);

	}

	public List<BaseEscalationDTO> checkAndSetlevel(List<BaseEscalationDTO> escalationList, List<String> levelList,
			List<BaseEscalationDTO> departmentList) {
		List<BaseEscalationDTO> newList = new ArrayList<>();

		Map<String, List<BaseEscalationDTO>> groupedList = escalationList.stream()
				.collect(Collectors.groupingBy(BaseEscalationDTO::getDepartmentId));

		if (CollectionUtils.isNotEmpty(escalationList)) {

			for (Map.Entry<String, List<BaseEscalationDTO>> entry : groupedList.entrySet()) {
				List<BaseEscalationDTO> list = entry.getValue();
				List<String> levels = list.stream().map(BaseEscalationDTO::getEscLevel).collect(Collectors.toList());
				List<String> differences = levelList.stream().filter(element -> !levels.contains(element))
						.collect(Collectors.toList());
				for (String str : differences) {
					BaseEscalationDTO base = new BaseEscalationDTO();
					base.setDepartmentId(entry.getKey());
					base.setEscLevel(str);
					newList.add(base);
				}
			}

			newList.addAll(setDepartmentList(groupedList, departmentList, levelList));

		}
		if (CollectionUtils.isNotEmpty(newList)) {
			escalationList.addAll(newList);
		}

		return escalationList;
	}

	private List<BaseEscalationDTO> setDepartmentList(Map<String, List<BaseEscalationDTO>> groupedList,
			List<BaseEscalationDTO> departmentList, List<String> levelList) {

		List<BaseEscalationDTO> newList = new ArrayList<>();
		Set<String> department = groupedList.keySet();
		List<String> departMentList = new ArrayList<>();
		for (String x : department) {
			departMentList.add(x);
		}
		List<String> depId = departmentList.stream().map(BaseEscalationDTO::getDepartmentId)
				.collect(Collectors.toList());

		List<String> differencesDep = depId.stream().filter(element -> !departMentList.contains(element))
				.collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(differencesDep)) {

			for (String str : differencesDep) {
				for (String st : levelList) {
					BaseEscalationDTO base = new BaseEscalationDTO();
					base.setDepartmentId(str);
					base.setEscLevel(st);

					newList.add(base);
				}

			}
		}
		return newList;
	}

	@PostMapping("/addEditEscalation")
	@PreAuthorize("hasAnyAuthority('Edit Escalation Matrix','View Escalation Matrix')")
	public String addviewEscalation(Model model) {
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.YES_FLAG);
		} else {
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
			getpendingEscalation(model);
		}

		List<BaseEscalationDTO> memberList = null;
		memberList = escalationSvc.getMemberList();
		BaseEscalationDTO nPCIData = new BaseEscalationDTO();
		nPCIData.setLabel(CommonConstants.NPCI_MEMBER);
		nPCIData.setValue("999");
		memberList.add(nPCIData);
		model.addAttribute(ESCALATION_DTO, new BaseEscalationDTO());
		model.addAttribute(MEM_LIST, memberList);

		return getView(model, ADD_ESCALATION);
	}

	@PostMapping("/pendingEscalationForApproval")
	@PreAuthorize("hasAuthority('View Escalation Matrix')")
	public String getpendingEscalation(Model model) {

		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);

		List<BaseEscalationDTO> escalationList = escalationSvc.getContactListPending();

		model.addAttribute(CommonConstants.ESCALATION_PENDING_LIST, escalationList);
		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}

		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		return getView(model, ADD_ESCALATION);
	}

	@PostMapping("/updateEscalation")
	@PreAuthorize("hasAuthority('Add Escalation Matrix')")
	public String saveEscalation(@ModelAttribute("escalationDTO") BaseEscalationDTO escalationDTO, BindingResult result,
			Model model) {
		Locale locale=Locale.ROOT;
		if (result.hasErrors()) {
			model.addAttribute(ERROR_STATUS, messageSource.getMessage("AM_MSG_formValidationFailed", null, locale));

		} else {

			try {

				escalationDTO.setMemberId(escalationDTO.getMemberId());
				List<BaseEscalationDTO> memberList = escalationSvc.getMemberListForReject();
				Map<String, String> lookUpMap;
				lookUpMap = memberList.stream()
						.collect(Collectors.toMap(BaseEscalationDTO::getValue, BaseEscalationDTO::getLabel));
				escalationDTO.setBankMemberName(lookUpMap.get(escalationDTO.getMemberId()));
				escalationSvc.updateEscalation(escalationDTO);
				List<BaseEscalationDTO> departmentList = escalationSvc.getDepartmentList();
				List<BaseEscalationDTO> escalationList = escalationSvc.getContactList(escalationDTO.getMemberId());
				model.addAttribute(DEPT_LIST, departmentList);
				model.addAttribute(ESCALATION_LIST, escalationList);
				model.addAttribute(SAVE_ESCALATION, SAVE);
				model.addAttribute(SUCCESS_STATUS, ESC_APPROVAL_MSG);
				List<LookUpDTO> stateList = masterService.getStateMasterList();
				model.addAttribute(STATE_LIST, stateList);
				model.addAttribute(SHOW, CommonConstants.REQUEST_STATE_REJECTED);
				model.addAttribute(IS_BANK_LIST, CommonConstants.YES_FLAG);
			} catch (Exception e) {

				model.addAttribute(ERROR_STATUS,ESC_ERROR_SAVE_DATA);

			}

			model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.YES_FLAG);
		}
		return getView(model, ADD_ESCALATION);
	}

	private void checkEscalationApproveStatus(Model model, String status) {
		Locale locale=Locale.ROOT;
		if (status.equalsIgnoreCase(CommonConstants.STATUS_APPROVE)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_EscalationApproved", null, locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_EscalationRejected", null, locale));
		}
	}

	@PostMapping("/approveOrRejectEscalationBulk")
	@PreAuthorize("hasAuthority('Approve Escalation Matrix')")
	public String approveOrRejectEscalationStatus(
			@RequestParam("bulkApprovalMemberIdList") String bulkApprovalMemberIdList,
			@RequestParam("status") String status, Model model) {
		String remarks = "";
		if (status.equals(CommonConstants.STATUS_APPROVE)) {
			remarks = CommonConstants.BULK_APPROVE;
		} else if (status.equals(CommonConstants.STATUS_REJECT)) {
			remarks = CommonConstants.BULK_REJECT;
		}
		String[] idArray = bulkApprovalMemberIdList.split("\\|");
		BaseEscalationDTO baseEscalationDTO = escalationSvc.updateApproveOrRejectBulk(idArray, status, remarks);
		checkEscalationApproveStatus(model, status);
		model.addAttribute(BaseCommonConstants.BASE_ESCALATION_DTO, baseEscalationDTO);
		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		List<BaseEscalationDTO> pendingEscalationDTOList = escalationSvc.getContactListPending();
		model.addAttribute(CommonConstants.ESCALATION_PENDING_LIST, pendingEscalationDTOList);

		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}

		return getView(model, ADD_ESCALATION);

	}

	@PostMapping("/approveOrRejectEscalation")
	@PreAuthorize("hasAuthority('Approve Escalation Matrix')")
	public String saveEscalation(@RequestParam String memberId,
			@RequestParam("status") String status, @RequestParam("remarks") String remarks, Model model) {

		try {

			String statusCode = escalationSvc.approveOrRejectEscalation(memberId, status, remarks);

			if (statusCode.equalsIgnoreCase(CommonConstants.TRANSACT_SUCCESS)) {
				if (status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_APPROVED)) {

					model.addAttribute(SUCCESS_STATUS, ESC_MSG_APPROVED);
				} else {
					model.addAttribute(SUCCESS_STATUS, ESC_MSG_REJECTED);
				}
			}
			model.addAttribute(SHOW, CommonConstants.REQUEST_STATE_REJECTED);

		} catch (Exception e) {
			model.addAttribute(ERROR_STATUS, ESC_ERROR_APP_DATA);

		}

		List<BaseEscalationDTO> departmentList = escalationSvc.getDepartmentList();
		List<BaseEscalationDTO> escalationList = escalationSvc.getContactList(memberId);

		BaseEscalationDTO baseEscalation = escalationList.get(0);
		List<BaseEscalationDTO> memberList = escalationSvc.getMemberList();
		baseEscalation.setMemberId(memberId);

		model.addAttribute(BASE_ESC_DTO, baseEscalation);
		model.addAttribute(MEM_LIST, memberList);
		model.addAttribute(DEPT_LIST, departmentList);
		model.addAttribute(ESCALATION_LIST, escalationList);

		return getView(model, VIEW_ESCALATION);
	}

	@PostMapping("/fetchEscalation")
	@PreAuthorize("hasAuthority('View Escalation Matrix')")
	public String fetchEscalation(Model model, @RequestParam String memberId) {

		List<BaseEscalationDTO> escalationList = escalationSvc.getContactList(memberId);
		BaseEscalationDTO baseEscalationDTO = escalationList.get(0);

		List<BaseEscalationDTO> memberList = escalationSvc.getMemberList();
		List<BaseEscalationDTO> departmentList = escalationSvc.getDepartmentList();
		baseEscalationDTO.setMemberId(memberId);

		model.addAttribute(BASE_ESC_DTO, baseEscalationDTO);
		model.addAttribute(MEM_LIST, memberList);
		model.addAttribute(DEPT_LIST, departmentList);
		model.addAttribute(ESCALATION_LIST, escalationList);

		return getView(model, VIEW_ESCALATION);
	}

	@PostMapping("/discardEscalationData")
	@PreAuthorize("hasAuthority('Edit Escalation Matrix')")
	public String discardEscalation(@RequestParam("memberId") String memberId, Model model) {

		List<BaseEscalationDTO> baseescalationdtolist = escalationSvc.discardEscalationInfo(memberId);
		BaseEscalationDTO baseescalationdto = baseescalationdtolist.get(0);
		baseescalationdto.setMemberId(memberId);
		List<BaseEscalationDTO> memberList = escalationSvc.getMemberList();
		List<BaseEscalationDTO> departmentList = escalationSvc.getDepartmentList();

		model.addAttribute(MEM_LIST, memberList);
		model.addAttribute(DEPT_LIST, departmentList);
		model.addAttribute(ESCALATION_LIST, baseescalationdtolist);

		model.addAttribute(BaseCommonConstants.BASE_ESCALATION_DTO, baseescalationdto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("escalation.discardSuccess.msg"));
		return getView(model, VIEW_ESCALATION);
	}

	// ViewMatrix

	@PostMapping("/viewEscalation")
	@PreAuthorize("hasAuthority('View Escalation Matrix')")
	public String viewEscalation(Model model) {

		BaseEscalationDTO baseEscalationDTO = new BaseEscalationDTO();
		List<BaseEscalationDTO> memberList = escalationSvc.getMembersList();
		Collections.sort(memberList, Comparator.comparing(BaseEscalationDTO::getValue,String.CASE_INSENSITIVE_ORDER));
		model.addAttribute(BaseCommonConstants.BASE_ESCALATION_DTO, baseEscalationDTO);
		model.addAttribute(MEM_LIST, memberList);
		return getView(model, VIEW_ESCALATION_MATRIX);

	}

	@PostMapping("/fetchEscalationMatrix")
	@PreAuthorize("hasAuthority('View Escalation Matrix')")
	public String fetchEscalationMatrix(Model model, @RequestParam String memberId) {

		BaseEscalationDTO baseEscalationDTO = new BaseEscalationDTO();

		List<BaseEscalationDTO> escalationList = escalationSvc.getContactListMain(memberId);
		List<BaseEscalationDTO> memberList = escalationSvc.getMembersList();
		List<BaseEscalationDTO> departmentList = escalationSvc.getDepartmentList();
		baseEscalationDTO.setMemberId(memberId);
		model.addAttribute(BASE_ESC_DTO, baseEscalationDTO);
		model.addAttribute(MEM_LIST, memberList);
		model.addAttribute(DEPT_LIST, departmentList);
		model.addAttribute(ESCALATION_LIST, escalationList);

		return getView(model, VIEW_ESCALATION_MATRIX);
	}

}