$(document)
		.ready(
				function() {
					$('.appRejMust').hide();
					$('.remarkMust').hide();
					function clearErrMsg() {
						$('.appRejMust').hide();
						$('.remarkMust').hide();
					}
					$('.backAction').click(
							function() {
								$('button').prop('disabled', true);
								var a = $(this).data("value");
								var b = a.split(",");
								var action = b[0];
									var data = "";
								postData(action, data);
							});

					$('.postAction')
							.click(
									function() {
										$('button').prop('disabled', true);
										

										if ($('#apprej option:selected').val() == "A") {
											if ($("#rejectReason").val() != "") {
												var reqID = $("#reqId").val();
												var wfId = $("#wfId").val();

												if (reqID == 68 || reqID == 67
														|| reqID == 65
														|| reqID == 98
														|| reqID == 99 || 103) {
													var url = '/approveBroadCastMessage';
													var data = "wfId,"
															+ wfId
															+ ",status,"
															+ "A"
															+ ",remarks,"
															+ remarks;
													postData(url, data);
												}
											} else {
												$('button').prop('disabled',
														false);
												$('.remarkMust').show();
											}
										} else if ($('#apprej option:selected')
												.val() == "R") {
											if ($("#rejectReason").val() != "") {
												var wfId = $("#wfId").val();
												var remarks = $("#rejectReason")
														.val();
												var url = '/approveBroadCastMessage';
												var data = "wfId," + wfId
														+ ",status," + "R"
														+ ",remarks," + remarks;
												postData(url, data);
											} else {
												$('.remarkMust').show();
												$('button').prop('disabled',
														false);
											}
										} else {
											$('.appRejMust').show();
											$('button').prop('disabled', false);
										}
									});
					$('#downloadReport').click(function() {

						userAction('/downloadBraodCastFile');

					});

				});
function userAction(action) {
	url = action;
	var data = "workflowid," + $('#wfId').val() ;

	postData(url, data);
}
