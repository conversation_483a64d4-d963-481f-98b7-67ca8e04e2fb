	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	
	}
	function backAction(type, action) {
		var url = action;
		var data = "userType," + type ;
		postData(url, data);
	}
	
	function postAction(_action) {
	var binFeatureId;
	var crtuser;
	var remarks;
	var url;
	var data;
	if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 binFeatureId = $("#binFeatureId").val();
				 crtuser = $("#crtuser").val();
				 remarks=$("#rejectReason").val();
		
				 url = '/approveBinFeatureMapping';
				 data = "binFeatureId," + binFeatureId + ",status," + "Approved" + ",crtuser,"
						+ crtuser + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				
						 binFeatureId = $("#binFeatureId").val();
					  crtuser= $("#crtuser").val();
					 remarks = $("#rejectReason").val();
					 url = '/approveBinFeatureMapping';
	
					 data = "binFeatureId," + binFeatureId + ",status," + "Rejected"
							+ ",crtuser," + crtuser  + ",remarks," + remarks;
					postData(url, data);
				
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		
	
	function postDiscardBinAction(action) {
		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		var url = action;
		var binFeatureId = document.getElementById("binFeatureId").value;
		var data = "binFeatureId," + binFeatureId + ",_vTransactToken," + tokenValue ;
		postData(url, data);
		
	}	

	function viewBinFeatureMapping(binFeatureId, type,parentPage) {
	var url;
		if (type == 'V')
			url = '/editBinFeatureMapping';
		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		var data = "binFeatureId," + binFeatureId + ",viewType," + type + ",_vTransactToken,"
			+ tokenValue +",parentPage," + parentPage;
		postData(url, data);
	}