package org.npci.settlenxt.adminportal.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.repository.FileRejectRepository;
import org.npci.settlenxt.portal.common.dto.FileRejectDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FileRejectServiceImpl implements FileRejectService {
	private static final Logger log = LogManager.getLogger(FileRejectServiceImpl.class);

	@Autowired
	private FileRejectRepository fileRejectRepository;
	
	@Override
	public void insertFileReject(FileRejectDTO fileReject) {
		try {
			 fileRejectRepository.insertFileReject(fileReject);
		} catch (Exception e) {
			log.error("Error while insertFileReject: ", e);
		}
	}
}
