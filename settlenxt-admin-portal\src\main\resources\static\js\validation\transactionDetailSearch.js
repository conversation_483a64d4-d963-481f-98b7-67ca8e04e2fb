$(document).ready(function() {
	
	$("#fromDate").datepicker({
		dateFormat : "yy-mm-dd",
		changeMonth : true,
		changeYear : true,
		maxDate: 0,
	//	minDate : 0,
		/*onClose : function(selectedDate) {
			$("#toDate").datepicker("option", "minDate", selectedDate);
		},*/
		onSelect: function(selectedDate){
		const selectedDateTime = new Date($("#fromDate").val());
		const maxDate = calculateMinimumDate(selectedDateTime);
		
		$("#toDate").val($(this).val());
		$("#toDate").datepicker("option", "minDate", selectedDate);
		$("#toDate").datepicker("option", "maxDate", maxDate);
		validateDate();
    }
	});
		
	$("#toDate").datepicker({
		dateFormat : "yy-mm-dd",
		changeMonth : true,
		changeYear : true,
	/*	minDate : 0, */
		maxDate: 0,
		onSelect: function(_selectedDate){
			
			validateDate();
	    }
	});
	
	$('#fromDate').on('keyup keypress blur change', function() {
			validateDate();
	});
	
	$('#toDate').on('keyup keypress blur change', function() {	
			validateDate();
	});
	
	$('#rrn').on('keyup keypress blur change', function() {
		validateRrn();
	});
	
	$('#tokenPan').on('keyup keypress blur change', function() {
		validateRrn();
	});
		if($('#networkType').val()=='Y'){
		$('#schemeCodeBank').on('keyup keypress blur change', function() {
	 validateFromCommonVal('schemeCodeBank', true, "SelectionBox", "", false);
	});
	}
	
	$('#searchedActionCode').on('keyup keypress blur change', function() {
				$('#crn').prop('disabled', true);
		
				validateCRN();
				
				var funcCode = $("#searchedActionCode").val().trim();
				var valueRrn=$('#rrn').val();
				if(document.getElementById('networkType').value=='N'){
				valueRrn=$('#rrn').val().trim();}
				var valuePan=$('#tokenPan').val().trim();
				if(funcCode === '01' || funcCode === '02' || funcCode === '03'){
					if(valueRrn == "" && valuePan == ""){
						$('#errrrn').text('Either RRN or PAN is mandatory');
					}
				}else{
					$('#errrrn').text('');
				}
	});
		if($('#networkType').val()!='Y'){
	$('#crn').on('keyup keypress blur change', function() {
		var crn = $("#crn").val();
		if(crn !== ""){
			validateCRN();
			$('#rrn').prop('disabled', true);
			$('#tokenPan').prop('disabled', true);
			$('#searchedActionCode').prop('disabled', true);
			$('#partId').prop('disabled', true);
			$('#errrrn').text('');
			$('#dterr').text('');
			$('#partIdErr').hide();
		}else{
			$('#rrn').prop('disabled', false);
			$('#tokenPan').prop('disabled', false);
			$('#searchedActionCode').prop('disabled', false);
			$('#partId').prop('disabled', false);
		}
	});
	
	}
	
	$('#searchButon').click(function() {
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		var validDate = false;
		var validRrn = false;
		var validCrn = false;
		var validPardId = false;
		var validateSC=false;
		if($('#networkType').val()==='Y'){
			validDate = validateDate();
			if(validDate){
				validRrn = validateRrn();
				validPardId = validatePartId();
			}
			validateSC=	validateSchemeCode();
		}else{
			validDate = validateDate();
			
			validCrn = validateCRN();
		}
		if ((validDate && validRrn && validPardId && validateSC) || (validCrn && validDate)){
		    $('#tableList').show(); 
		     $('#example').DataTable().clear().draw();
			$("#example").dataTable({
				
				"bServerSide": false,
				
				 paging: true,
		         pageLength: 10,
				"sAjaxSource": "searchTransactionData",
				"bProcessing": true,
				"bJQueryUI": true,
				"sServerMethod": "POST",
				"bDestroy": true,
				"language": {
				    "search": "Search : "
				  },
				"fnRowCallback": function( nRow, aData, _iDisplayIndex ) {
					var data  = aData[9] + '&' + aData[10] + '&' + aData[11]+ '&' + aData[12]+ '&' + aData[13]+ '&' + aData[14] + '&' + aData[15] + '&' + aData[16];
					$(nRow).unbind('dblclick');
          		    $(nRow).bind('dblclick', () => {
                       viewTransaction(data);
                    });
					return nRow;
				},
				"fnServerParams": function ( aoData ) {
					aoData.push( { "name": "fromDate", "value": $('#fromDate').val() == "" ? "" : $('#fromDate').val() },
							{ "name": "toDate", "value": $('#toDate').val() == "" ? "" : $('#toDate').val()},
							{ "name": "rrn", "value": $('#rrn').val() == "" ? "" : $('#rrn').val()},
							{ "name": "tokenPan", "value": $('#tokenPan').val() == "" ? "" : $('#tokenPan').val()},
							{ "name": "actionCode", "value": $('#searchedActionCode').val() == "" ? "" : $('#searchedActionCode').val()},
							{ "name": "partId", "value": $('#partId').val() == "" ? "" : $('#partId').val()},
							{ "name": "complaintNumber", "value": $('#crn').val() == "" ? "" : $('#crn').val()},
							{ "name": "schemeCodeBank", "value": $('#schemeCodeBank').val() == "" ? "" : $('#schemeCodeBank').val()},
							{ "name": "_TransactToken", "value":tokenValue}
					);
				},
				"fnServerData": function ( sSource, aoData, fnCallback ) {
					$.ajax({
						"dataType": 'json',
						"beforeSend": function (xhr) {
		                    xhr.setRequestHeader('_TransactToken', tokenValue);
		                },
						"type": "POST",
						"url": sSource,
						"data": aoData,
						"success": function(json){
							
							var errorMessage=json.errorMessage;
							
							fnCallback(json);
							if(errorMessage != "")
							{
							
								$('.appr').hide();
							}
							
							
							
						}
					});               
				},
				   initComplete: function () {
				   
				    var cursorPosition;
		            var api = this.api();
		            
		            $('#IsLastLevel').val(NaN);
		 
		            // For each column
		            api
		                .columns()
		                .eq(0)
		                .each(function (colIdx) {
		                    // Set the header cell to contain the input element
		                	 var cell = $('#example thead tr th').eq(
		                        $(api.column(colIdx).header()).index()
		                    );
		                    var title = $(cell).text();
		                    
		                    if(colIdx<actionColumnIndex){
		                    $(cell).html(title+'<br><input class="search-box"   type="text" />');
		 
		                    // On every keypress in this input
		                    $(
		                        'input',
		                        $('#example thead tr th').eq($(api.column(colIdx).header()).index())
		                    )
		                        .off('keyup change')
		                        .on('change', function (_e) {
		                            // Get the search value
		                            $(this).attr('title', $(this).val());
		                            var regexr = '({search})'; 
		 
		                            cursorPosition = this.selectionStart;
		                            // Search the column for that value
		                            api
		                                .column(colIdx)
		                                .search(
		                                    this.value != ''
		                                        ? regexr.replace('{search}', '(((' + this.value + ')))')
		                                        : '',
		                                    this.value != '',
		                                    this.value == ''
		                                )
		                                .draw();
		                        })
								.on('click', function (e) {
		                            e.stopPropagation();})
		                        .on('keyup', function (e) {
		                            e.stopPropagation();
		 
		                            $(this).trigger('change');
									if(cursorPosition && cursorPosition!=null){
		                            $(this)
		                                .focus()[0]
		                                .setSelectionRange(cursorPosition, cursorPosition);
										}
		                        });
		                        }else{
		                         $(cell).html(title+'<br> &nbsp;');}
		                });
		                $('#example_filter').hide();
		                $('.dt-buttons').hide();
		        },
		        dom: 'Bfrtip',
				
		        buttons: [
		            {
		                        extend: 'excelHtml5',
		                        text: 'Export',
		                        filename: 'Transaction' ,
								header:'false', 
								title: null,
								sheetName:'Transaction',
								className:'defaultexport',
								exportOptions: {
									 columns: 'th:not(:last-child)'
						         }
		                    },
		                      {
		                        extend: 'csvHtml5',
		                        text: 'Export',
		                        filename: 'Transaction' ,
								header:'false', 
								title: null,
								sheetName:'Transaction',
								className:'defaultexport',
								exportOptions: {
						            columns: 'th:not(:last-child)'
						         }
		                    }
		        ],    
				 
				searching: true,
				info: true
			});
 }
});

function validateSchemeCode(){
	var resultSchemeCode = true;
	if($('#networkType').val()==='Y'){
				 if (!validateFromCommonVal('schemeCodeBank', true, "SelectionBox", "", false)) {
					resultSchemeCode = false;}
					$('#errschemeCodeBank').html('Please enter Scheme code');
    	  return resultSchemeCode;
     }
}
function validateCRN(){
	
	var rrn = $('#rrn').val();
	var tokenPan = $('#tokenPan').val();
	var actionCode = $('#searchedActionCode').val();
	var partId = $('#partId').val();
	var valCrn = $('#crn').val();
	var resultCrn = false;
	if(rrn === '' && tokenPan === '' && actionCode === '' && partId === '0'){
		$('#crn').prop('disabled', false);
	}
	if(valCrn !== ''&& valCrn !== undefined){
			var regEx = /^[A-Za-z0-9]+$/;
			if (!regEx.test(valCrn)) {
				$('#errcrn').html('Please enter valid crn');
				
			}else if(valCrn.length > 16 || valCrn.length < 16){
				$('#errcrn').html('CRN should be of 16 digits');
				
			}else{
				$('#errcrn').html('');
				resultCrn = true;
			}
	}else{
		$('#errcrn').html('');
		resultCrn = true;
	}
	return resultCrn;
}

function viewTransaction(data){
	
	
	var url = '/transactionDetail';
	var txnId = data.split('&')[0];
	var funcCode = data.split('&')[1];
	var mti = data.split('&')[2];
	var originalTableName = data.split('&')[3];
	var acquirerReferenceData = data.split('&')[4];
	var caseNumber = data.split('&')[5];
	var orgFuncCode = data.split('&')[6];
	var mappedDupData = data.split('&')[7];
	var fromDate = $('#fromDate').val();
	var toDate = $('#toDate').val();
	var rrn = $('#rrn').val();
	var tokenPan =  $('#tokenPan').val();
	var actionCode = $('#searchedActionCode').val();
	var partId = $('#partId').val();
	var crn = $('#crn').val();
	var schemeCodeBank=$('#schemeCodeBank').val();
	
	var datas = "txnId,"+txnId +",funcCode,"+funcCode +",mti,"+mti + ",originalTableName," +originalTableName + ",acqRefData," + acquirerReferenceData + ",caseNo," + caseNumber + ",orgFuncCode," + orgFuncCode +
	",fromDate," + fromDate + ",toDate," + toDate + ",rrn," + rrn + ",tokenPan," + tokenPan + ",actionCode," + actionCode + ",partId," + partId + ",complaintNumber," + crn + ",mappedDupData," + mappedDupData+",schemeCodeBank," + schemeCodeBank;

	postData(url, datas);
}

function calculateMinimumDate(selectedDate) {
	const currentDateTime = new Date();
	const newDateTime = new Date(selectedDate.getTime() + *********7);
	if (newDateTime.getTime() > currentDateTime.getTime()) {
		return currentDateTime;
	} else {
		return newDateTime;
	}
}

function validateDate()
{
	var result = true;
	var regxDate = /^\d{4}-\d{2}-\d{2}$/;
	if (($('#fromDate').val() == "") && ($('#toDate').val() == "")) {
		$('#dterr').text('Please select from and to date');
		$('#errFromDate').text('');
		$('#errToDate').text('');
		result =  false;
	}else{	
		$('#dterr').text('');
	}
	if (($('#fromDate').val() == "") && ($('#toDate').val() != "")) {
		if(!regxDate.test($('#toDate').val())){
			$('#errToDate').text('Please enter the valid date');
		}else{
			$('#errFromDate').text('Please select from date');
		}
		result =  false;
	}
	if (($('#fromDate').val() != "") && ($('#toDate').val() == "")) {
		if(!regxDate.test($('#fromDate').val())){
			$('#errFromDate').text('Please enter the valid date');
		}else{
			$('#errToDate').text('Please select to date');
		}
		result =  false;
	}
	if (($('#fromDate').val() != "") && ($('#toDate').val() != "")) {
		if(!regxDate.test($('#fromDate').val())){
			$('#errFromDate').text('Please enter the valid date');
			result =  false;
		}else{
			$('#errFromDate').text('');
		}
		if(!regxDate.test($('#toDate').val())){
			$('#errToDate').text('Please enter the valid date');
			result =  false;
		}else{
			$('#errToDate').text('');
		}
		if(result){		
			if (Date.parse($('#fromDate').val()) > Date.parse($('#toDate').val())) {
				$('#dterr').text('Invalid date range');
				result =  false;
			}else{
				$('#errToDate').text('');
				$('#errFromDate').text('');
				$('#dterr').text('');
			}
		}
	}	
	return result;
}


function validateRrn() {
	$('#crn').prop('disabled', true);
	if($('#networkType').val()!='Y'){
	validateCRN();
	}
	var resultRrn = true;
	var resultPan = true;
	var valueRrn=$('#rrn').val();
	if(document.getElementById('networkType').value=='N'){
		valueRrn=$('#rrn').val().trim();
	}
	var valuePan=$('#tokenPan').val().trim();
	var valueFuncCode = $('#searchedActionCode').val().trim();
	var regEx = /^\d+$/i;
	
	if(valueFuncCode != ""){
		if(valueFuncCode == '01' || valueFuncCode == '02' || valueFuncCode == '03'){
			if(valueRrn == "" && valuePan == "" ){
				$('#errrrn').text('Either RRN or PAN is mandatory');
				resultRrn =  false;
				resultPan = false;
			}
		}
	}

	if(valueRrn != ""&&document.getElementById('networkType')&&document.getElementById('networkType').value=='N'){
		if (!regEx.test(valueRrn)) {
			$('#errrrn').html('Please enter only numbers');
			resultRrn =  false;
		}else if(valueRrn.length > 12 || valueRrn.length < 12){
			$('#errrrn').html('RRN should be of 12 digits');
			resultRrn =  false;
		}
		
	}
	
	if(valueRrn != ""&&document.getElementById('networkType')&&document.getElementById('networkType').value=='Y'){
		var regExIrgcs = /^[a-zA-Z\d ]+$/i;
		if (!regExIrgcs.test(valueRrn)) {
			$('#errrrn').html('Please enter only numbers');
			resultRrn =  false;
		}else if(valueRrn.length <4  || valueRrn.length > 12){
			$('#errrrn').html('RRN should be of between 4-12 digits');
			resultRrn =  false;
		}
		
	}
	
	if (valueRrn == "" && valuePan == "" && valueFuncCode == "") {			
	    $('#errrrn').text('Either PAN or RRN or Function Code is mandatory');
	 	resultRrn =  false;
		resultPan = false;
	} 
	
	if(valuePan != ""){
		if(valuePan.length<12 && valuePan.length>0){
			$('#errtokenPan').html('Please enter atleast 12 characters');
			resultPan =  false;
		}
	}else{
		$('#errtokenPan').html("");
	}
	if(resultRrn){
		$('#errrrn').html("");
	}
	if(resultPan){
		$('#errtokenPan').html("");
	}
	return (resultRrn && resultPan);
}

function validatePartId() {
	var check = true;
	if ($('#partId').val() == undefined || $('#partId').val() == '0') {
			check = false;
			$('#partIdErr').text('Please select Participant Id');
			$('#partIdErr').show();
	}
	 else {
		$('#partIdErr').hide();
	}
	return check;
}




	
$('#example').on('click', '.tabRowId', function() {
	   
	   
	   var txnId=$(this).attr('data-value');
	
	   var trnDateStr= $('#transactionDate').val();
	   var txntype=$('#txntype').val();
	   var url = "/getTransaction";
	   var data ="txnId,"+ txnId+",trnDateStr,"+trnDateStr+",txntype,"+txntype;
		postData(url, data);
	   
	   
	  

	});
	   
	   $("#chargeBack").click(function (){
			
		   var txnId = $('#txnId').val();
		   var chargeFlag= "C";
			var tokenValue = document.getElementsByName("_TransactToken")[0].value;
			
			 $.ajax({
                 url : "getchargeBackData",
                 type : "POST",
                 dataType : "json",
                 "beforeSend": function (xhr) {
                     xhr.setRequestHeader('_TransactToken', tokenValue);
                 },
                 data : {
                     "_TransactToken" : tokenValue,
                     "txnId" : txnId,
                     "chargeFlag" : chargeFlag
                 },
                 
             });
	
			});
	   
	   $('#backButton').click(function(){
			
			var data = "";
			postData('/viewTransactionSearch',data);
		});
	   
	   $('#resetBtn').click(function(){
			$('#fromDate').val('');
			$('#toDate').val('');
			$('#rrn').val('');
			$('#tokenPan').val('');
			$('#partId').val('0');
			$('#searchedActionCode').val('');
			$('#crn').val('');
			$('#crn').prop('disabled', false);
			$('#schemeCodeBank').val('');
			$('#schemeCodeBank').prop('disabled', false);
			$('#fromDate').prop('disabled', false);
			$('#toDate').prop('disabled', false);
			$('#rrn').prop('disabled', false);
			$('#tokenPan').prop('disabled', false);
			$('#searchedActionCode').prop('disabled', false);
			$('#partId').prop('disabled', false);
			$('#tableList').hide();
			document.querySelectorAll(".error").forEach(ele => {
    			ele.innerHTML = '';
			});
		});
	   
	   $("#excelExport").on("click", function () {
	        $(".buttons-excel").trigger("click");
	    });
	    
	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
	 
	     $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
	   
		var searchedFromDate = $('#fromDate').val();
		var searchedToDate = $('#toDate').val();
		var searchedRrn = $('#rrn').val();
		var searchedTokenPan = $('#tokenPan').val();
		var searchedActionCode = $('#searchedActionCode').val();
		var searchedPartId = $('#partId').val();
		
			var searchedCrn = $('#crn').val();
			var searchedSchemeCodeBank = $('#schemeCodeBank').val();
		if(searchedFromDate !== '' && searchedToDate !== '' && searchedPartId !== '' && (searchedRrn !== '' || searchedTokenPan !== '' || searchedActionCode !== '') && searchedSchemeCodeBank !== ''){
			document.getElementById("searchButon").click();
		}else if(searchedCrn !== '' && searchedCrn !==undefined){
			$('#rrn').prop('disabled', true);
			$('#tokenPan').prop('disabled', true);
			$('#searchedActionCode').prop('disabled', true);
			$('#partId').prop('disabled', true);
			document.getElementById("searchButon").click();
			
		}
});

