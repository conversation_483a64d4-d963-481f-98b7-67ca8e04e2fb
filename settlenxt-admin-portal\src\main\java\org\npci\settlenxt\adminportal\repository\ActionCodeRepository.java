package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.adminportal.dto.ActionCodeDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.repository.BaseActionCodeRepository;

@Mapper
public interface ActionCodeRepository extends BaseActionCodeRepository{

	 List<ActionCodeDTO> getApprovedActionCodeFromMain();

	 List<ActionCodeDTO> getPendingForApprovalActionCodeFromStg(
			@Param("requestStateList") List<String> requestStateList);

	 List<CodeValueDTO> getFuncCodeList();

	 void insertActionCodeIntoStg(ActionCodeDTO actionCodeDTO);

	 int getActionCodeSeqId();

	 ActionCodeDTO getActionCodeInfoFromMain(@Param("actionCodeId") int actionCodeId);

	 ActionCodeDTO getActionCodeInfoFromStg(@Param("actionCodeId") int actionCodeId);

	 ActionCodeDTO getActionCodeFromMainEdit(@Param("actionCodeId") int actionCodeId);

	 int updateActionCodeStg(ActionCodeDTO actionCodeDTO);

	 ActionCodeDTO discardActionCode(@Param("actionCodeId") int actionCodeId);

	 void updateActionCode(ActionCodeDTO actionCodeDTO);

	 void saveActionCodeMain(ActionCodeDTO actionCodeDTO);

	 void updateActionCodeRequestState(ActionCodeDTO actionCodeDTO);

	 void deleteDiscardedEntry(@Param("actionCodeId") int actionCodeId);

	 void updateStgActionCode(ActionCodeDTO actionCodeDTOmain);

	 List<ActionCodeDTO> fetchCappingAmountStgAppList(@Param("actionCodeIdList") List<Integer> actionCodeIdList);

	 List<CodeValueDTO> getMCCList();

	 List<CodeValueDTO> getActionCodeList();

	 int checkDistinctActionCode(ActionCodeDTO actDto);

	 int checkDistinctActionCodeStg(ActionCodeDTO actDto);

	 List<CodeValueDTO> getReasonCodeList();
}