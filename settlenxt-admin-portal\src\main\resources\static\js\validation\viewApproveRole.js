	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	
	}
	function backAction(type, action) {
		var url = action;
		var data = "userType," + type ;
		postData(url, data);
	}
	
	function postAction(_action) {
		var roleId;
		var crtuser;
		var remarks;
		var url;
		var data;
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
			    document.querySelector(".button").disabled = true;
				roleId = $("#roleId").val();
				crtuser = $("#crtuser").val();
				remarks=$("#rejectReason").val();
		
				url = '/approveRoleStatus';
				data = "roleId," + roleId + ",status," + "Approved" + ",crtuser,"
						+ crtuser  + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
			    document.querySelector(".button").disabled = true;
				postRejectedData(roleId, crtuser, remarks, url, data);
	
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		
function postRejectedData(roleId, crtuser, remarks, url, data) {
    if ('${requestInfo.requestName}' == 'ROLE FUNCTIONALITY MAP') {
        

        crtuser = $("#crtuser").val();

        remarks = $("#rejectReason").val();
        roleId = '${roleInfoDto.roleId }';
        url = '/rejectRoleStatus';

        data = "roleId," + roleId + ",status," + "Rejected"
            + ",crtuser," + crtuser + ",rejectReason," + remarks + ",roleId,"
            + roleId;

        postData(url, data);
    }
    else {
        roleId = $("#roleId").val();
        crtuser = $("#crtuser").val();
        remarks = $("#rejectReason").val();
        url = '/approveRoleStatus';

        data = "roleId," + roleId + ",status," + "Rejected"
            + ",crtuser," + crtuser + ",remarks," + remarks;
        postData(url, data);
    }
    return { roleId, crtuser, remarks, url, data };
}

	function postActionBank(_action) {
		var workFlowId;
		var crtuser;
		var url;
		var data;
		var remarks;
	
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				workFlowId = $("#wfId").val();
				crtuser = $("#crtuser").val();
		
				url = '/approveBankRoleStatus';
				data = "workflowid," + workFlowId + ",status," + "A" + ",crtuser,"
						+ crtuser + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				postBankRejectedData(workFlowId, crtuser, remarks, url, data);
	
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}


function postBankRejectedData(workFlowId, crtuser, remarks, url, data) {
    if ('${requestInfo.requestName}' == 'ROLE FUNCTIONALITY MAP') {
        workFlowId = $("#wfId").val();
        crtuser = $("#crtuser").val();

        remarks = $("#rejectReason").val();
        var roleId = '${roleInfoVO.vRoleID }';
        url = '/rejectBankRoleStatus';

        data = "workflowid," + workFlowId + ",status," + "R"
            + ",crtuser," + crtuser + ",rejectReason," + remarks + ",roleID,"
            + roleId;

        postData(url, data);
    }
    else {
        workFlowId = $("#wfId").val();
        crtuser = $("#crtuser").val();
        remarks = $("#rejectReason").val();
        url = '/approveBankRoleStatus';

        data = "workflowid," + workFlowId + ",status," + "R"
            + ",crtuser," + crtuser + ",remarks," + remarks;
        postData(url, data);
    }
    
}
