<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/editDeleteMcprData.js" type="text/javascript"></script>
<script type="text/javascript">
var mcprValidationMessages={};
mcprValidationMessages['binNumber']="<spring:message code='mcprBinDetails.binNumber.validation.msg' javaScriptEscape='true' />";

mcprValidationMessages['phyContactCardCummRuPayCard']="<spring:message code='mcprBinDetails.phyContactCardCummRuPayCard.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['phyContactCardIncrementalCard']="<spring:message code='mcprBinDetails.phyContactCardIncrementalCard.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['phyContactlessCummRuPayCard']="<spring:message code='mcprBinDetails.phyContactlessCummRuPayCard.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['phyContactlessIncrementalCard']="<spring:message code='mcprBinDetails.phyContactlessIncrementalCard.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['virtualCardCummRuPayCard']="<spring:message code='mcprBinDetails.virtualCardCummRuPayCard.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['virtualCardIncrementalCard']="<spring:message code='mcprBinDetails.virtualCardIncrementalCard.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['ecommTxnOnusCount']="<spring:message code='mcprBinDetails.ecommTxnOnusCount.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['ecommTxnOnusAmt']="<spring:message code='mcprBinDetails.ecommTxnOnusAmt.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['posConcactCardPresentDomTxnOnusCount']="<spring:message code='mcprBinDetails.posConcactCardPresentDomTxnOnusCount.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['posConcactCardPresentDomTxnOnusAmt']="<spring:message code='mcprBinDetails.posConcactCardPresentDomTxnOnusAmt.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['posContactlessOnlRetailsDomTxnOnusCount']="<spring:message code='mcprBinDetails.posContactlessOnlRetailsDomTxnOnusCount.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['posContactlessOnlRetailsDomTxnOnusAmt']="<spring:message code='mcprBinDetails.posContactlessOnlRetailsDomTxnOnusAmt.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['posContactlessOnlTransitDomTxnOnusCount']="<spring:message code='mcprBinDetails.posContactlessOnlTransitDomTxnOnusCount.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['posContactlessOnlTransitDomTxnOnusAmt']="<spring:message code='mcprBinDetails.posContactlessOnlTransitDomTxnOnusAmt.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['posContactlessOffRetailsDomTxnOnusCount']="<spring:message code='mcprBinDetails.posContactlessOffRetailsDomTxnOnusCount.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['posContactlessOffRetailsDomTxnOnusAmt']="<spring:message code='mcprBinDetails.posContactlessOffRetailsDomTxnOnusAmt.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['posContactlessOffTransitDomTxnOnusCount']="<spring:message code='mcprBinDetails.posContactlessOffTransitDomTxnOnusCount.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['posContactlessOffTransitDomTxnOnusAmt']="<spring:message code='mcprBinDetails.posContactlessOffTransitDomTxnOnusAmt.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['atmCardPresentDomTxnOnusCount']="<spring:message code='mcprBinDetails.atmCardPresentDomTxnOnusCount.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['atmCardPresentDomTxnOnusAmt']="<spring:message code='mcprBinDetails.atmCardPresentDomTxnOnusAmt.validation.msg' javaScriptEscape='true' />";

</script>

<div class="row">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span data-i18n="Data">
				<c:if test="${not empty editMcprBinDetails}"><spring:message code="mcprBinDetails.editscreen.title" /></c:if>
				<c:if test="${not empty deleteMcprBinDetails}"><spring:message code="mcprBinDetails.deletescreen.title" /></c:if> </span>
			</strong>	
		</div>
		
		<div class="panel-body">
			<form:form onsubmit="return encodeForm(this);" method="POST"
				id="addEditMcprBinDetails" modelAttribute="mcprViewEditHistoricDto"
				action="${submitCardDetails}" autocomplete="off">
				<br />
				<form:hidden path="mcprBinDataDetailsId" id="mcprBinDataDetailsId"
					name="mcprBinDataDetailsId" value="${mcprViewEditHistoricDto.mcprBinDataDetailsId}" />

 
		
		<c:if test="${not empty showbutton}">		
				<table class="table table-striped" style="font-size: 12px">
				<caption style="display:none;">MCPR Bin Data</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
					<tbody>
						<tr>
						<td><label><spring:message code="mcprBinDetails.monthEnding" /><span style="color: red">*</span></label>
						</td>
						<td><form:input path="monthEnding" id="monthEnding" name="monthEnding" readonly="true"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.monthEnding}" />
						</td>
						<td>
						<label><spring:message code="mcprBinDetails.binNumber" /><span style="color: red">*</span></label>
						</td>
						<td>
						<c:if test="${not empty editMcprBinDetails}">
							<form:input path="binNumber" id="binNumber"
							value="${mcprViewEditHistoricDto.binNumber}" name="binNumber" readonly="true"
							maxlength="10" cssClass="form-control medantory" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="binNumber" id="binNumber"
							value="${mcprViewEditHistoricDto.binNumber}" name="binNumber" readonly="true"
							maxlength="10" cssClass="form-control medantory" />
						</c:if>	
					
					<div id="errbinNumber">
							<span for="binNumber" class="error"><form:errors
							path="binNumber" /> </span>
					</div>
					</td>
				</tr>
				<tr>
					<td>
						<label><spring:message code="mcprBinDetails.physicalContactCardCumulativeRpay" /><span style="color: red">*</span></label>
					</td>
					<td>	
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="phyContactCardCummRuPayCard" id="phyContactCardCummRuPayCard" name="phyContactCardCummRuPayCard"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.phyContactCardCummRuPayCard}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
						<form:input path="phyContactCardCummRuPayCard" id="phyContactCardCummRuPayCard" name="phyContactCardCummRuPayCard"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.phyContactCardCummRuPayCard}" />
						</c:if>
					
					<div id="errphyContactCardCummRuPayCard">
							<span for="phyContactCardCummRuPayCard" class="error"><form:errors
							path="phyContactCardCummRuPayCard" /> </span>
					</div>
					</td>
					<td>
						<label><spring:message code="mcprBinDetails.physicalContactCardIncremental" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="phyContactCardIncrementalCard" id="phyContactCardIncrementalCard" name="phyContactCardIncrementalCard"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.phyContactCardIncrementalCard}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
						<form:input path="phyContactCardIncrementalCard" id="phyContactCardIncrementalCard" name="phyContactCardIncrementalCard"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.phyContactCardIncrementalCard}" />
						</c:if>
					
					<div id="errphyContactCardIncrementalCard">
							<span for="phyContactCardIncrementalCard" class="error"><form:errors
							path="phyContactCardIncrementalCard" /> </span>
					</div>
				</td>
				</tr>
				<tr>
					<td>
						<label><spring:message code="mcprBinDetails.physicalContactlessCardCumulativeRpay" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="phyContactlessCummRuPayCard" id="phyContactlessCummRuPayCard" name="phyContactlessCummRuPayCard"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.phyContactlessCummRuPayCard}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="phyContactlessCummRuPayCard" id="phyContactlessCummRuPayCard" name="phyContactlessCummRuPayCard"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.phyContactlessCummRuPayCard}" />
						</c:if>
					
					<div id="errphyContactlessCummRuPayCard">
							<span for="phyContactlessCummRuPayCard" class="error"><form:errors
							path="phyContactlessCummRuPayCard" /> </span>
					</div>
				</td>
				<td>
					<label><spring:message code="mcprBinDetails.physicalContactlessCardIncremental" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="phyContactlessIncrementalCard" id="phyContactlessIncrementalCard" name="phyContactlessIncrementalCard"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.phyContactlessIncrementalCard}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="phyContactlessIncrementalCard" id="phyContactlessIncrementalCard" name="phyContactlessIncrementalCard"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.phyContactlessIncrementalCard}" />
						</c:if>
					
					<div id="errphyContactlessIncrementalCard">
							<span for="phyContactlessIncrementalCard" class="error"><form:errors
							path="phyContactlessIncrementalCard" /> </span>
					</div>
				</td>
				</tr>
				<tr>
					<td>
						<label><spring:message code="mcprBinDetails.virtualCardCumulativeRpay" /><span style="color: red">*</span></label>
						</td>
						<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="virtualCardCummRuPayCard" id="virtualCardCummRuPayCard" name="virtualCardCummRuPayCard"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.virtualCardCummRuPayCard}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="virtualCardCummRuPayCard" id="virtualCardCummRuPayCard" name="virtualCardCummRuPayCard"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.virtualCardCummRuPayCard}" />
						</c:if>
					
					<div id="errvirtualCardCummRuPayCard">
							<span for="virtualCardCummRuPayCard" class="error"><form:errors
							path="virtualCardCummRuPayCard" /> </span>
					</div>
				</td>
					<td>
						<label><spring:message code="mcprBinDetails.virtualCardIncremental" /><span style="color: red">*</span></label>
					</td>
					<td>	
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="virtualCardIncrementalCard" id="virtualCardIncrementalCard" name="virtualCardIncrementalCard"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.virtualCardIncrementalCard}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="virtualCardIncrementalCard" id="virtualCardIncrementalCard" name="virtualCardIncrementalCard"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.virtualCardIncrementalCard}" />
						</c:if>
					
					<div id="errvirtualCardIncrementalCard">
							<span for="virtualCardIncrementalCard" class="error"><form:errors
							path="virtualCardIncrementalCard" /> </span>
					</div>
					</td>
				</tr>
				<tr>
				<td>
					<label><spring:message code="mcprBinDetails.ecomTxnOnusCount" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="ecommTxnOnusCount" id="ecommTxnOnusCount" name="ecommTxnOnusCount"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.ecommTxnOnusCount}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="ecommTxnOnusCount" id="ecommTxnOnusCount" name="ecommTxnOnusCount"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.ecommTxnOnusCount}" />
						</c:if>
					
					<div id="errecommTxnOnusCount">
							<span for="ecommTxnOnusCount" class="error"><form:errors
							path="ecommTxnOnusCount" /> </span>
					</div>
				</td>
				<td>
					<label><spring:message code="mcprBinDetails.ecomTxnOnusAmt" /><span style="color: red">*</span></label>
					</td>
					<td>	
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="ecommTxnOnusAmt" id="ecommTxnOnusAmt" name="ecommTxnOnusAmt"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.ecommTxnOnusAmt}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="ecommTxnOnusAmt" id="ecommTxnOnusAmt" name="ecommTxnOnusAmt"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.ecommTxnOnusAmt}" />
						</c:if>
					
					<div id="errecommTxnOnusAmt">
							<span for="ecommTxnOnusAmt" class="error"><form:errors
							path="ecommTxnOnusAmt" /> </span>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<label><spring:message code="mcprBinDetails.posContactDomesticTxnCount" /><span style="color: red">*</span></label>
				</td>
				<td>		
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="posConcactCardPresentDomTxnOnusCount" id="posConcactCardPresentDomTxnOnusCount" name="posConcactCardPresentDomTxnOnusCount"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.posConcactCardPresentDomTxnOnusCount}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="posConcactCardPresentDomTxnOnusCount" id="posConcactCardPresentDomTxnOnusCount" name="posConcactCardPresentDomTxnOnusCount"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.posConcactCardPresentDomTxnOnusCount}" />
						</c:if>
					
					<div id="errposConcactCardPresentDomTxnOnusCount">
							<span for="posConcactCardPresentDomTxnOnusCount" class="error"><form:errors
							path="posConcactCardPresentDomTxnOnusCount" /> </span>
					</div>
				</td>
				<td>
					<label><spring:message code="mcprBinDetails.posContactDomesticTxnAmt" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="posConcactCardPresentDomTxnOnusAmt" id="posConcactCardPresentDomTxnOnusAmt" name="posConcactCardPresentDomTxnOnusAmt"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.posConcactCardPresentDomTxnOnusAmt}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="posConcactCardPresentDomTxnOnusAmt" id="posConcactCardPresentDomTxnOnusAmt" name="posConcactCardPresentDomTxnOnusAmt"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.posConcactCardPresentDomTxnOnusAmt}" />
						</c:if>
					
					<div id="errposConcactCardPresentDomTxnOnusAmt">
							<span for="posConcactCardPresentDomTxnOnusAmt" class="error"><form:errors
							path="posConcactCardPresentDomTxnOnusAmt" /> </span>
					</div>
				</td>
				</tr>
				<tr>
				<td>
					<label><spring:message code="mcprBinDetails.posContactlessOnlineRetailsDomesticTxnCount" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="posContactlessOnlRetailsDomTxnOnusCount" id="posContactlessOnlRetailsDomTxnOnusCount" name="posContactlessOnlRetailsDomTxnOnusCount"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.posContactlessOnlRetailsDomTxnOnusCount}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="posContactlessOnlRetailsDomTxnOnusCount" id="posContactlessOnlRetailsDomTxnOnusCount" name="posContactlessOnlRetailsDomTxnOnusCount"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.posContactlessOnlRetailsDomTxnOnusCount}" />
						</c:if>
					
					<div id="errposContactlessOnlRetailsDomTxnOnusCount">
							<span for="posContactlessOnlRetailsDomTxnOnusCount" class="error"><form:errors
							path="posContactlessOnlRetailsDomTxnOnusCount" /> </span>
					</div>
				</td>
				<td>
					<label><spring:message code="mcprBinDetails.posContactlessOnlineRetailsDomesticTxnAmt" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="posContactlessOnlRetailsDomTxnOnusAmt" id="posContactlessOnlRetailsDomTxnOnusAmt" name="posContactlessOnlRetailsDomTxnOnusAmt"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.posContactlessOnlRetailsDomTxnOnusAmt}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="posContactlessOnlRetailsDomTxnOnusAmt" id="posContactlessOnlRetailsDomTxnOnusAmt" name="posContactlessOnlRetailsDomTxnOnusAmt"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.posContactlessOnlRetailsDomTxnOnusAmt}" />
						</c:if>
					
					<div id="errposContactlessOnlRetailsDomTxnOnusAmt">
							<span for="posContactlessOnlRetailsDomTxnOnusAmt" class="error"><form:errors
							path="posContactlessOnlRetailsDomTxnOnusAmt" /> </span>
					</div>
				</td>
				</tr>
			<tr>
				<td>
						<label><spring:message code="mcprBinDetails.posContactlessOnlineTransitDomesticTxnCount" /><span style="color: red">*</span></label>
					</td>
					<td>	
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="posContactlessOnlTransitDomTxnOnusCount" id="posContactlessOnlTransitDomTxnOnusCount" name="posContactlessOnlTransitDomTxnOnusCount"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.posContactlessOnlTransitDomTxnOnusCount}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="posContactlessOnlTransitDomTxnOnusCount" id="posContactlessOnlTransitDomTxnOnusCount" name="posContactlessOnlTransitDomTxnOnusCount"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.posContactlessOnlTransitDomTxnOnusCount}" />
						</c:if>
					
					<div id="errposContactlessOnlTransitDomTxnOnusCount">
							<span for="posContactlessOnlTransitDomTxnOnusCount" class="error"><form:errors
							path="posContactlessOnlTransitDomTxnOnusCount" /> </span>
					</div>
				</td>
				<td>
					<label><spring:message code="mcprBinDetails.posContactlessOnlineTransitDomesticTxnAmt" /><span style="color: red">*</span></label>
					</td>
					<td>	
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="posContactlessOnlTransitDomTxnOnusAmt" id="posContactlessOnlTransitDomTxnOnusAmt" name="posContactlessOnlTransitDomTxnOnusAmt"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.posContactlessOnlTransitDomTxnOnusAmt}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="posContactlessOnlTransitDomTxnOnusAmt" id="posContactlessOnlTransitDomTxnOnusAmt" name="posContactlessOnlTransitDomTxnOnusAmt"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.posContactlessOnlTransitDomTxnOnusAmt}" />
						</c:if>
					
					<div id="errposContactlessOnlTransitDomTxnOnusAmt">
							<span for="posContactlessOnlTransitDomTxnOnusAmt" class="error"><form:errors
							path="posContactlessOnlTransitDomTxnOnusAmt" /> </span>
					</div>
				</td>
				</tr>
				<tr>
				<td>
					<label><spring:message code="mcprBinDetails.posContactlessOfflineRetailsDomesticTxnCount" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="posContactlessOffRetailsDomTxnOnusCount" id="posContactlessOffRetailsDomTxnOnusCount" name="posContactlessOffRetailsDomTxnOnusCount"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.posContactlessOffRetailsDomTxnOnusCount}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="posContactlessOffRetailsDomTxnOnusCount" id="posContactlessOffRetailsDomTxnOnusCount" name="posContactlessOffRetailsDomTxnOnusCount"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.posContactlessOffRetailsDomTxnOnusCount}" />
						</c:if>
					
					<div id="errposContactlessOffRetailsDomTxnOnusCount">
							<span for="posContactlessOffRetailsDomTxnOnusCount" class="error"><form:errors
							path="posContactlessOffRetailsDomTxnOnusCount" /> </span>
					</div>
				</td>
				<td>
					<label><spring:message code="mcprBinDetails.posContactlessOfflineRetailsDomesticTxnAmt" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="posContactlessOffRetailsDomTxnOnusAmt" id="posContactlessOffRetailsDomTxnOnusAmt" name="posContactlessOffRetailsDomTxnOnusAmt"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.posContactlessOffRetailsDomTxnOnusAmt}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="posContactlessOffRetailsDomTxnOnusAmt" id="posContactlessOffRetailsDomTxnOnusAmt" name="posContactlessOffRetailsDomTxnOnusAmt"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.posContactlessOffRetailsDomTxnOnusAmt}" />
						</c:if>
					
					<div id="errposContactlessOffRetailsDomTxnOnusAmt">
							<span for="posContactlessOffRetailsDomTxnOnusAmt" class="error"><form:errors
							path="posContactlessOffRetailsDomTxnOnusAmt" /> </span>
					</div>
				</td>
				</tr>
		
				<tr>
				<td>
					<label><spring:message code="mcprBinDetails.posContactlessOfflineTransitDomesticTxnCount" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="posContactlessOffTransitDomTxnOnusCount" id="posContactlessOffTransitDomTxnOnusCount" name="posContactlessOffTransitDomTxnOnusCount"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.posContactlessOffTransitDomTxnOnusCount}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="posContactlessOffTransitDomTxnOnusCount" id="posContactlessOffTransitDomTxnOnusCount" name="posContactlessOffTransitDomTxnOnusCount"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.posContactlessOffTransitDomTxnOnusCount}" />
						</c:if>
					
					<div id="errposContactlessOffTransitDomTxnOnusCount">
							<span for="posContactlessOffTransitDomTxnOnusCount" class="error"><form:errors
							path="posContactlessOffTransitDomTxnOnusCount" /> </span>
					</div>
				</td>
				<td>
					<label><spring:message code="mcprBinDetails.posContactlessOfflineTransitDomesticTxnAmt" /><span style="color: red">*</span></label>
						</td>
						<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="posContactlessOffTransitDomTxnOnusAmt" id="posContactlessOffTransitDomTxnOnusAmt" name="posContactlessOffTransitDomTxnOnusAmt"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.posContactlessOffTransitDomTxnOnusAmt}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="posContactlessOffTransitDomTxnOnusAmt" id="posContactlessOffTransitDomTxnOnusAmt" name="posContactlessOffTransitDomTxnOnusAmt"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.posContactlessOffTransitDomTxnOnusAmt}" />
						</c:if>
					
					<div id="errposContactlessOffTransitDomTxnOnusAmt">
							<span for="posContactlessOffTransitDomTxnOnusAmt" class="error"><form:errors
							path="posContactlessOffTransitDomTxnOnusAmt" /> </span>
					</div>
				</td>
				</tr>
				<tr>
				<td>
					<label><spring:message code="mcprBinDetails.atmTxnCount" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="atmCardPresentDomTxnOnusCount" id="atmCardPresentDomTxnOnusCount" name="atmCardPresentDomTxnOnusCount"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.atmCardPresentDomTxnOnusCount}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="atmCardPresentDomTxnOnusCount" id="atmCardPresentDomTxnOnusCount" name="atmCardPresentDomTxnOnusCount"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.atmCardPresentDomTxnOnusCount}" />
						</c:if>
					
					<div id="erratmCardPresentDomTxnOnusCount">
							<span for="atmCardPresentDomTxnOnusCount" class="error"><form:errors
							path="atmCardPresentDomTxnOnusCount" /> </span>
					</div>
				</td>
				<td>
					<label><spring:message code="mcprBinDetails.atmTxnAmt" /><span style="color: red">*</span></label> 
					</td>
					<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="atmCardPresentDomTxnOnusAmt" id="atmCardPresentDomTxnOnusAmt" name="atmCardPresentDomTxnOnusAmt"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.atmCardPresentDomTxnOnusAmt}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="atmCardPresentDomTxnOnusAmt" id="atmCardPresentDomTxnOnusAmt" name="atmCardPresentDomTxnOnusAmt"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.atmCardPresentDomTxnOnusAmt}" />
						</c:if>
					
					<div id="erratmCardPresentDomTxnOnusAmt">
							<span for="atmCardPresentDomTxnOnusAmt" class="error"><form:errors
							path="atmCardPresentDomTxnOnusAmt" /> </span>
					</div>
				</td>
				</tr>
		
				<tr>
				<td>
					<label><spring:message code="mcprBinDetails.totalCumulativeCards" /><span style="color: red">*</span></label>
					</td>
					<td>
						<c:if test="${ not empty editMcprBinDetails}">
						<form:input path="totalCumulativeCards" id="totalCumulativeCards" name="totalCumulativeCards"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.totalCumulativeCards}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="totalCumulativeCards" id="totalCumulativeCards" name="totalCumulativeCards"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.totalCumulativeCards}" />
						</c:if>
					
					<div id="errtotalCumulativeCards">
							<span for="totalCumulativeCards" class="error"><form:errors
							path="totalCumulativeCards" /> </span>
					</div>
				
				</td>
				<td>
					<label><spring:message code="mcprBinDetails.totalIncrementalCards" /><span style="color: red">*</span></label>
						</td>
						<td>
						<c:if test="${not empty editMcprBinDetails}">
						<form:input path="totalIncrementalCards" id="totalIncrementalCards" name="totalIncrementalCards"
							cssClass="form-control medantory" value="${mcprViewEditHistoricDto.totalIncrementalCards}" />
						</c:if>
						<c:if test="${not empty deleteMcprBinDetails}">
							<form:input path="totalIncrementalCards" id="totalIncrementalCards" name="totalIncrementalCards"
							cssClass="form-control medantory" readonly="true" value="${mcprViewEditHistoricDto.totalIncrementalCards}" />
						</c:if>
					
					<div id="errtotalIncrementalCards">
							<span for="totalIncrementalCards" class="error"><form:errors
							path="totalIncrementalCards" /> </span>
					</div>
				</td>
			</tr> 
		
		</tbody>
		</table>
		</c:if>
		
		
		<c:if test="${empty showbutton}">
		
		<table class="table table-striped" style="font-size: 12px">
		<caption style="display:none;">MCPR Bin Data</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
							<tr>
								<td><label><spring:message code="mcprBinDetails.mcprBinDetailsId" /></label></td>
								<td id="mcprBinDataDetailsId">${mcprViewEditHistoricDto.mcprBinDataDetailsId }</td>
								<td><label><spring:message code="mcprBinDetails.monthEnding" /></label></td>
								<td id="monthEnding">${mcprViewEditHistoricDto.monthEnding }</td>
								<td><label><spring:message code="mcprBinDetails.binNumber" /></label></td>
								<td id="binNumber">${mcprViewEditHistoricDto.binNumber}</td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.physicalContactCardCumulativeRpay" /></label></td>
								<td id="phyContactCardCummRuPayCard">${mcprViewEditHistoricDto.phyContactCardCummRuPayCard }</td>
								<td><label><spring:message code="mcprBinDetails.physicalContactCardIncremental" /></label></td>
								<td id="phyContactCardIncrementalCard">${mcprViewEditHistoricDto.phyContactCardIncrementalCard }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.physicalContactlessCardCumulativeRpay" /></label></td>
								<td id="phyContactlessCummRuPayCard">${mcprViewEditHistoricDto.phyContactlessCummRuPayCard}</td>
								<td><label><spring:message code="mcprBinDetails.physicalContactlessCardIncremental" /></label></td>
								<td id="phyContactlessIncrementalCard">${mcprViewEditHistoricDto.phyContactlessIncrementalCard }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.virtualCardCumulativeRpay" /></label></td>
								<td id="virtualCardCummRuPayCard">${mcprViewEditHistoricDto.virtualCardCummRuPayCard }</td>
								<td><label><spring:message code="mcprBinDetails.virtualCardIncremental" /></label></td>
								<td id="virtualCardIncrementalCard">${mcprViewEditHistoricDto.virtualCardIncrementalCard}</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.ecomTxnOnusCount" /></label></td>
								<td id="ecommTxnOnusCount">${mcprViewEditHistoricDto.ecommTxnOnusCount }</td>
								<td><label><spring:message code="mcprBinDetails.ecomTxnOnusAmt" /></label></td>
								<td id="ecommTxnOnusAmt">${mcprViewEditHistoricDto.ecommTxnOnusAmt }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.posContactDomesticTxnCount" /></label></td>
								<td id="posConcactCardPresentDomTxnOnusCount">${mcprViewEditHistoricDto.posConcactCardPresentDomTxnOnusCount}</td>
								<td><label><spring:message code="mcprBinDetails.posContactDomesticTxnAmt" /></label></td>
								<td id="posConcactCardPresentDomTxnOnusAmt">${mcprViewEditHistoricDto.posConcactCardPresentDomTxnOnusAmt }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.posContactlessOnlineRetailsDomesticTxnCount" /></label></td>
								<td id="posContactlessOnlRetailsDomTxnOnusCount">${mcprViewEditHistoricDto.posContactlessOnlRetailsDomTxnOnusCount }</td>
								<td><label><spring:message code="mcprBinDetails.posContactlessOnlineRetailsDomesticTxnAmt" /></label></td>
								<td id="posContactlessOnlRetailsDomTxnOnusAmt">${mcprViewEditHistoricDto.posContactlessOnlRetailsDomTxnOnusAmt}</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.posContactlessOnlineTransitDomesticTxnCount" /></label></td>
								<td id="posContactlessOnlTransitDomTxnOnusCount">${mcprViewEditHistoricDto.posContactlessOnlTransitDomTxnOnusCount }</td>
								<td><label><spring:message code="mcprBinDetails.posContactlessOnlineTransitDomesticTxnAmt" /></label></td>
								<td id="posContactlessOnlTransitDomTxnOnusAmt">${mcprViewEditHistoricDto.posContactlessOnlTransitDomTxnOnusAmt }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.posContactlessOfflineRetailsDomesticTxnCount" /></label></td>
								<td id="posContactlessOffRetailsDomTxnOnusCount">${mcprViewEditHistoricDto.posContactlessOffRetailsDomTxnOnusCount}</td>
								<td><label><spring:message code="mcprBinDetails.posContactlessOfflineRetailsDomesticTxnAmt" /></label></td>
								<td id="posContactlessOffRetailsDomTxnOnusCount">${mcprViewEditHistoricDto.posContactlessOffRetailsDomTxnOnusAmt }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.posContactlessOfflineTransitDomesticTxnCount" /></label></td>
								<td id="posContactlessOffTransitDomTxnOnusCount">${mcprViewEditHistoricDto.posContactlessOffTransitDomTxnOnusCount }</td>
								<td><label><spring:message code="mcprBinDetails.posContactlessOfflineTransitDomesticTxnAmt" /></label></td>
								<td id="posContactlessOffTransitDomTxnOnusAmt">${mcprViewEditHistoricDto.posContactlessOffTransitDomTxnOnusAmt}</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.atmTxnCount" /></label></td>
								<td id="atmCardPresentDomTxnOnusCount">${mcprViewEditHistoricDto.atmCardPresentDomTxnOnusCount }</td>
								<td><label><spring:message code="mcprBinDetails.atmTxnAmt" /></label></td>
								<td id="atmCardPresentDomTxnOnusAmt">${mcprViewEditHistoricDto.atmCardPresentDomTxnOnusAmt }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.totalCumulativeCards" /></label></td>
								<td id="totalCumulativeCards">${mcprViewEditHistoricDto.totalCumulativeCards }</td>
								<td><label><spring:message code="mcprBinDetails.totalIncrementalCards" /></label></td>
								<td id="totalIncrementalCards">${mcprViewEditHistoricDto.totalIncrementalCards }</td>
								<td></td>
								<td></td>
							</tr>
							</tbody>
						</table>
		
		
		</c:if>
		
		<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center">
					
						
					<c:if test="${EditMenu eq 'Yes'}">
					<sec:authorize access="hasAuthority('Edit MCPR Data')">
							<c:if test="${not empty editMcprBinDetails and not empty showbutton}">
								<c:if test="${viewEditButton eq 'Y'}">
									<input type="button" class="btn btn-success" id="bEdit"
										onclick="viewMcprBinDetailsEdit('/updateEditViewMcprData','E')" id="submitButton"
										value="Submit" /> 
								</c:if>
							</c:if>
							<c:if test="${not empty deleteMcprBinDetails and not empty showbutton}">
								<c:if test="${viewEditButton eq 'Y'}">
									<input type="button" class="btn btn-success"
										onclick="viewMcprBinDetailsEdit('/deleteUpdateMcprData','E')" id="submitButton"
										value="Submit" /> 
								</c:if>
							</c:if>
						</sec:authorize>
						
						<button type="button" class="btn btn-danger"
							onclick="userAction('N','/dataViewEditDelete');"><spring:message code="mcprBinDetails.backBtn" /></button>
						
						</c:if>
					</div>
				</div>
				</form:form>
			</div>
			</div>	
	</div>
			

	

						
						