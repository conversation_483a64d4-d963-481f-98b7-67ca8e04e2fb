$(document).ready(function() {
	var today = new Date();
	var tomorrow = new Date(today.getFullYear(), today.getMonth(), (today.getDate() + 1));
	$("#systemDate").datepicker({
		dateFormat: 'yyyy-MM-dd',
		endDate: tomorrow,
		maxDate: tomorrow,
		todayHighlight: true,
		autoclose: true,
		changeMonth: true,
		changeYear: true
	});

	$("#send").click(function() {
		var systemDate = $('#systemDate').val();
		var url = "/getFailedInprogressOutgoingFiles";
		var data = "systemDate," + systemDate;
		postData(url, data);
	});
	
	$(".forceClose").click(function() {
		var id = $(this).attr('data-id');
		$('#conf-force-close-id').val(id);
		});

	$("#submit").click(function() {
		var myObject = new Object();
		var url = getURL('/forceCloseOutgoingFiles');
		var systemDate = $('#systemDate').val();
		var forceCloseBtnId = $('#conf-force-close-id').val();
		var fileName = $('#btn-forceClose' +forceCloseBtnId).attr("data-fileName");
		var outgoingFileStatus = $('#btn-forceClose' +forceCloseBtnId).attr("data-outgoingFileStatus");
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		myObject.systemDate = systemDate;
		myObject.fileName = fileName;
		myObject.outgoingFileStatus = outgoingFileStatus;
		$.ajax({
			type: "POST",
			contentType: "application/json",
			url: url,
			data: JSON.stringify(myObject),
			dataType: 'json',
			"beforeSend": function(xhr) {
				xhr.setRequestHeader('_TransactToken', tokenValue);
			},
			success: function(response) {
				$("#alertNotification").removeClass("hide");
				if (response == null || response == '' || response == undefined) {
					$("#alertNotification").addClass("alert-danger");
					$("#alertMessageStrong").text("Error! ");
					$("#alertMessage").text("Failed to send message for force close the outgoing file: " + "Internal server error");
				} else {
					if (response.Status == "FAILED" || response.Status == "503") {
						$("#alertNotification").addClass("alert-danger");
						$("#alertMessageStrong").text("Error! ");
						$("#alertMessage").text("Failed to send message for force close the outgoing file : " + response.errorMessage);
					} else if (response.Status == "SUCCESS" || response.Status == "200") {
						$("#alertNotification").addClass("alert-success");
						$("#alertMessageStrong").text("Success! ");
						$("#alertMessage").text("Successfully send message to force close the outgoing file");
					} else {
						$("#alertNotification").addClass("alert-danger");
						$("#alertMessageStrong").text("Error! ");
						$("#alertMessage").text("Failed to send message for force close the outgoing file: " + "Internal server error");
					}
				}
			},
			error: function(_e) {
				$("#btn-save").prop("disabled", false);
			}
		});
		$("#okAction").click(function() {
			window.setTimeout(function() { location.reload() }, 100)
		});

	});

	function getURL(url) {
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		
		return pathName + url;
	}

	$("#dataTable").DataTable({
		"fnRowCallback": function(nRow, _aData, _iDisplayIndex, _iDisplayIndexFull) {
			var fileNames = $(nRow).attr("data-fileNames");
			if (fileNames == null) {
				$('td', nRow).addClass('bg-danger');
			} else {
				$('td', nRow).addClass('bg-info');
			}
		},
		dom: 'lBfrtip',
		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true,
		order: [[1, 'asc']],
	});


});