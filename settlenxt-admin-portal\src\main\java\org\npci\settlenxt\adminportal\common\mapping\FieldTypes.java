package org.npci.settlenxt.adminportal.common.mapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FieldTypes {
	
	private List<String> requiredFields;
	private List<String> optionalFields;
	private List<String> conditionalFields;
	private List<String> allFields;
	
	public List<String> getRequiredFields() {
		return requiredFields;
	}
	public void setRequiredFields(List<String> requiredFields) {
		this.requiredFields = requiredFields;
	}
	public List<String> getOptionalFields() {
		return optionalFields;
	}
	public void setOptionalFields(List<String> optionalFields) {
		this.optionalFields = optionalFields;
	}
	public List<String> getConditionalFields() {
		return conditionalFields;
	}
	public void setConditionalFields(List<String> conditionalFields) {
		this.conditionalFields = conditionalFields;
	}
	
	public Map<String, String> getRequiredFieldsMap(){
		Map<String, String> reqFieldsMap = new HashMap<>();
		String key = "";
		if(requiredFields != null && !requiredFields.isEmpty()){
			for(String reqField:requiredFields){
				key = reqField;
				reqFieldsMap.put(key, key);
			}
		}
		
		return reqFieldsMap;
	}
	
	public Map<String, String> getOptionalFieldsMap(){
		Map<String, String> optFieldsMap = new HashMap<>();
		String key = "";
		if(optionalFields != null && !optionalFields.isEmpty()){
			for(String optField:optionalFields){
				key = optField;
				optFieldsMap.put(key, key);
			}
		}
		
		return optFieldsMap;
	}
	
	public Map<String, String> getConditionalFieldsMap(){
		Map<String, String> condFieldsMap = new HashMap<>();
		String key = "";
		if(conditionalFields != null && !conditionalFields.isEmpty()){
			for(String condtField:conditionalFields){
				key = condtField;
				condFieldsMap.put(key, key);
			}
		}
		
		return condFieldsMap;
	}
	public List<String> getAllFields() {
		allFields.addAll(requiredFields);
		allFields.addAll(optionalFields);
		allFields.addAll(conditionalFields);
		
		return allFields;
	}
	public void setAllFields(List<String> allFields) {
		this.allFields = allFields;
	}
}
