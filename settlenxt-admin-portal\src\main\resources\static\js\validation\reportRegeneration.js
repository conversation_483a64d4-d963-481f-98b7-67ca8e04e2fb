$(document).ready(function() {
	
	$("#cycleDate").datepicker({
		dateFormat: 'yy-mm-dd',
		todayHighlight: true,
		autoclose: true,
		changeMonth: true,
		changeYear: true,
		endDate: "today",
        maxDate: 1
        })


        $('.datepicker').keyup(function () {
            if (this.value.match(/\D/g)) {
                this.value = this.value.replace(/[^0-9^-]/g, '');
            }
        });

	$("#select-settlementProductId").change(function() {
			let productId=$('#select-settlementProductId option:selected').attr("value");
		if(productId != ""){
			 $("#errsettlementProductId").find('.error').html('');
			getReportTypes();
		}
		var prodId = $('#select-settlementProductId option:selected').attr("data-product_id");
		var split_string = prodId.split(",");
		var sortedProductId = split_string.sort();
		var dropdown = $('#select-cycleNumber');
		$(dropdown).empty();
	$('#select-cycleNumber').append('<option value="" disabled selected>Select Cycle Number</option>');
		$.each(sortedProductId, function(_key, item) {
			$("#select-cycleNumber").append(new Option(item, item));
		});

	});
	
	
	 
	   

	$('.participant').on('change', function() {
		getReportTypes();

	});

   
	$('.participant').on('change', function() {
		enableSubmitBtn();
	});
	
	$('.settleProductId').on('change', function() {
		enableSubmitBtn();
	});
	
	 $('.reportType').on('change', function() {
			enableSubmitBtn();
	});
	
	
function getReportTypes() {
	let participantId =document.getElementById('select-participantId').value // Get the selected participant ID
	let tokenValue = document.getElementsByName("_TransactToken")[0].value;
	let prodId = $('#select-settlementProductId option:selected').attr("value");
	if (prodId != "" && participantId !="") {
		$.ajax({
			type: "GET",
			url: "getReportTypes",
			data: {
				participantId: participantId,
				prodId: prodId
			}, // Pass the participant ID to the controller
			"beforeSend": function(xhr) {
				xhr.setRequestHeader('_TransactToken', tokenValue);
			},
			success: function(response) {
				$("#submit").prop("disabled", true);
				$('#select-reportType').empty(); // Clear existing options
				$('#select-reportType').append('<option value="" disabled selected>Select Report Type</option>');
				$.each(response, function(index, reportType) {
					$('#select-reportType').append('<option value="' + reportType.description + '">' + reportType.code + '</option>');
				});
			},
			error: function(xhr, status, error) {
				$("#submit").prop("disabled", true);
			}
		});
	} else if(prodId == "") {
		$("#errsettlementProductId").find('.error').html("Please select product id");
		$("#errsettlementProductId").show();
	}
}

	
	function enableSubmitBtn() {
		var settlementProductId = $('#select-settlementProductId').val();
		var participantId = $('.participant option:selected').val();
		var reportType = $('#select-reportType').val();
		if ((participantId == null || participantId == '' || participantId == undefined) || 
		(settlementProductId == null || settlementProductId == '' || settlementProductId == undefined)||
		(reportType == null || reportType == '' || reportType == undefined)) {
			$("#submit").attr("disabled", "disabled");
		} else {
			$("#submit").removeAttr("disabled");
		}
	}
	
	$("#submit").click(function() {
		var myObject = new Object();
		var url = getURL('/regenerationReportStatus');

		var date = $('#cycleDate').val();
		var cycleDate = moment(date).format('DDMMYY');
		var reportType = $('#select-reportType').val();
		var cycleNumber = $('#select-cycleNumber').val();
		var settlementProductId = $('#select-settlementProductId').val();
		var participantId = $('.participant option:selected').val();
		
		
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		myObject.cycleDate = cycleDate;
		myObject.reportType = reportType;
		console.log("The report type value is "+reportType);
		myObject.cycleNumber = cycleNumber;
		myObject.settlementProductId = settlementProductId;
		myObject.participantId = participantId;
		$.ajax({
			type: "POST",
			contentType: "application/json",
			url: url,
			data: JSON.stringify(myObject),
			dataType: 'json',
			"beforeSend": function(xhr) {
				xhr.setRequestHeader('_TransactToken', tokenValue);
			},
			success: function(response) {
				$("#alertNotification").removeClass("hide");
				if (response == null || response == '' || response == undefined) {
					$("#alertNotification").addClass("alert-danger");
					$("#alertMessageStrong").text("Error! ");
					$("#alertMessage").text("Failed to send message to regenerate the reports: " + "Internal server error");
				} else {
					if (response.Status == "FAILED" || response.Status == "503") {
						$("#alertNotification").addClass("alert-danger");
						$("#alertMessageStrong").text("Error! ");
						$("#alertMessage").text("Failed to send message to regenerate the reports : " + response.errorMessage);
					} else if (response.Status == "SUCCESS" || response.Status == "200") {
						$("#alertNotification").addClass("alert-success");
						$("#alertMessageStrong").text("Success! ");
						$("#alertMessage").text("Successfully send message to regenerate the reports.");
					} else {
						$("#alertNotification").addClass("alert-danger");
						$("#alertMessageStrong").text("Error! ");
						$("#alertMessage").text("Failed to send message to regenerate the reports: " + "Internal server error");
					}
				}
			},
			error: function(_e) {
				$("#btn-save").prop("disabled", false);
			}
		});
		$("#okAction").click(function() {
			window.setTimeout(function() { location.reload() }, 100)
		});

	});

	function getURL(url) {
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		
		return pathName + url;
	}



    	
	$("#dataTable").dataTable({
	
		"fnRowCallback": function(nRow, _aData, _iDisplayIndex, _iDisplayIndexFull) {
			var regenStatus = $(nRow).attr("data-regenerationStatus");
			if ((regenStatus == "COMPLETED") || (regenStatus == "MERGED")) {
				$('td', nRow).addClass('bg-success');
			} else {
				$('td', nRow).addClass('bg-info');
			}
		},
		
		
		   initComplete: function () {
            var api = this.api();
          
            
            $('#IsLastLevel').val(NaN);
 
            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#dataTable thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    
                   
                    if(colIdx<actionColumnIndex){
                    $(cell).html(title+'<br><input class="search-box"   type="text" />');
 
                    // On every keypress in this input
                    $(
                        'input',
                        $('#dataTable thead tr th').eq($(api.column(colIdx).header()).index())
                    )
                        .off('keyup change')
                        .on('change', function (_e) {
                            // Get the search value
                            $(this).attr('title', $(this).val());
                            var regexr = '({search})'; 
 
                            
                            // Search the column for that value
                            api
                                .column(colIdx)
                                .search(
                                    this.value != ''
                                        ? regexr.replace('{search}', '(((' + this.value + ')))')
                                        : '',
                                    this.value != '',
                                    this.value == ''
                                )
                                .draw();
                        })
						.on('click', function (e) {
                            e.stopPropagation();})
                        .on('keyup', function (e) {
                            e.stopPropagation();
 
                            $(this).trigger('change');
							if(cursorPosition && cursorPosition!=null){
                            $(this)
                                .focus()[0]
                                .setSelectionRange(cursorPosition, cursorPosition);
								}
                        });
                        }else{
                         $(cell).html(title+'<br> &nbsp;');}
                });
                $('#dataTable_filter').hide();
                $('.dt-buttons').hide();
        },
        
         columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         
         "order": [],
        
        dom: 'lBfrtip',
		
        buttons: [
            {
                        extend: 'excelHtml5',
                        text: 'Export',
                        filename: 'Report Regeneration' ,
						header:'false', 
						title: null,
						sheetName:'Report Regeneration',
						className:'defaultexport',
						exportOptions: {
							 columns: 'th:not(:last-child)'
				         }
                    },
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Report Regeneration' ,
						header:'false', 
						title: null,
						sheetName:'Report Regeneration',
						className:'defaultexport',
						exportOptions: {
				            columns: 'th:not(:last-child)'
				         }
                    }
        ],    
		 
		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true,
		order: [[1, 'desc']]
	});
	



});


function getRegenerationStatus() {

	let url = '/reportRegeneration';
	var data = "";
	postData(url, data);
	
}