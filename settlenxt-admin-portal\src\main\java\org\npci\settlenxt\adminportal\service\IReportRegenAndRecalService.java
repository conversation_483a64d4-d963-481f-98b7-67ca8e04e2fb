package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;

/**
 * Service Interface used for
 * <li>Report Regeneration Status</li>
 * <li>Fetch ProductId Details</li>
 * <li>Fetch participant Id </li>
 * <li>Delete recalculation transactions </li>
 * <AUTHOR>
 *
 */
public interface IReportRegenAndRecalService {

	String sendDataToReportRegeneration(CycleManagementDTO cycleManagementDTO);
	
	List<MemberDTO> getParticipantList(SearchCriteriaDTO searchCriteriaDTO);
	
	CycleManagementDTO getRegenerationReportsStatus(CycleManagementDTO cycleManagementDTO);
	
	String deleteRecalTxns(CycleManagementDTO cycleManagementDTO);
	
}
