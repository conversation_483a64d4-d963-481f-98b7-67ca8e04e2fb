package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.portal.common.dto.DisputeTxnModel;
import org.npci.settlenxt.portal.common.dto.IncomingTransactionDetailDTO;
import org.npci.settlenxt.portal.common.repository.BaseIncomingTransactionDetailRepo;

@Mapper
public interface IncomingTransactionDetailRepo extends BaseIncomingTransactionDetailRepo{
	void approveRejectMaker(IncomingTransactionDetailDTO transaction);

	void approveRejectChecker(IncomingTransactionDetailDTO transaction);

	List<DisputeTxnModel> getDisputesDetailsAdmin(@Param("transaction") IncomingTransactionDetailDTO transaction,
			@Param("isSchemeCode") boolean isSchemeCode, @Param("isFuncCode") boolean isFuncCode,
			@Param("isPan") boolean isPan, @Param("isRrn") boolean isRrn);
}
