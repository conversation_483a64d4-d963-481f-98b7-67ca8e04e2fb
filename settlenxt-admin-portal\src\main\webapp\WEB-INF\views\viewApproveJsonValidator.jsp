<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

	<script src="./static/js/validation/viewApproveJsonValidator.js"
	type="text/javascript"></script>

<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert"><spring:message code="budget.apprejecterrormsg" /></div>
	<div class="alert alert-danger remarkMust" role="alert"><spring:message code="budget.remarkserror" /></div>
	<c:url value="approveReasonCode" var="approveReasonCode" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveJsonValidator" modelAttribute="jsonValidatorDto"
		action="${approveJsonValidator}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="jsonValidator.approvalPendingViewScreen.title" /></span></strong>
					</div>

					<div class="panel-body">
						<input type="hidden" id="seqId" value="${jsonValidatorDto.seqId}">
						<input type="hidden" id="api" value="${jsonValidatorDto.api}">
						<input type="hidden" id="code" value="${jsonValidatorDto.code}">
						<input type="hidden" id="fieldName" value="${jsonValidatorDto.fieldName}">
						<input type="hidden" id="pcode" value="${jsonValidatorDto.pcode}">

						<input type="hidden" id="crtuser"
							value="${jsonValidatorDto.lastUpdatedBy}" />

						<table class="table table-striped infobold"
							style="font-size: 12px">
							<caption style="display:none;">View Approve Json Validator</caption> 
							<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								
								<tr>
									<td colspan="6">
										<div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-user"></span> <span
												data-i18n="Data"><spring:message code="jsonValidator.viewscreen.title" /></span></strong>
										</div>
									</td>

									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>


								<tr>
									<td><label><spring:message code="jsonValidator.api" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.api}</td>
									<td><label><spring:message code="jsonValidator.code" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.code}</td>
									
									
									<td><label><spring:message code="jsonValidator.fieldName" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.fieldName}</td>
									<td><label><spring:message code="jsonValidator.regexps" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.regexps}</td>
									</tr>
									<tr>
									<td><label><spring:message code="jsonValidator.reasonCode" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.reasonCode}</td>
									<td><label><spring:message code="jsonValidator.reasonCodeDesc" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.reasonCodeDesc}</td>
									
									<td><label><spring:message code="jsonValidator.pcode" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.pcode}</td>
									</tr>
									<tr>
									<td><label><spring:message code="binexcl.status" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty jsonValidatorDto.status}">N/A</c:if>${jsonValidatorDto.status}</td>
									<td><label><spring:message code="jsonValidator.priority" /><span style="color: red"></span></label></td>
									<td>${jsonValidatorDto.priority}</td>
									<td><label><spring:message code="ifsc.checkerComments" /></label></td>
									<td ><c:if test="${empty jsonValidatorDto.checkerComments}">N/A</c:if>${jsonValidatorDto.checkerComments }</td>
							
									<td></td>
									<td></td>
									<td></td>
								</tr>

								
								
								<sec:authorize access="hasAuthority('Approve Json Validator')">
									<c:if test="${jsonValidatorDto.requestState eq 'P'}">
										<tr>
											<td colspan="6"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"><spring:message
																code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
										</tr>
										

										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="AM.lbl.reject" /></option>
											</select></td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											<!-- //Added by deepak on 31-03-2016 -->
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										</tr>
									</c:if>
							</sec:authorize>	

							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Json Validator')">
										<c:if test="${jsonValidatorDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approveRole" value="Submit"
												onclick="postAction('/approveJsonValidator');" />
										</c:if>
								</sec:authorize>
									<c:choose>
										<c:when test="${editJsonValidator eq 'Yes'}">
											<button type="button" class="btn btn-danger"
										onclick="backAction('P','/showJsonValidator');"><spring:message code="budget.backBtn" /></button>
										</c:when>
										<c:otherwise>
											<button type="button" class="btn btn-danger"
										onclick="backAction('P','/jsonValidatorPendingForApproval');"><spring:message code="budget.backBtn" /></button>
										</c:otherwise>
									</c:choose>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

