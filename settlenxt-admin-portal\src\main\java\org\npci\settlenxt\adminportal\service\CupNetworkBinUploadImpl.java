package org.npci.settlenxt.adminportal.service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.cache.CountryCache;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.MemberRepository;
import org.npci.settlenxt.common.cache.BaseLookupDTOCache;
import org.npci.settlenxt.portal.common.dto.BinDetailsDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class CupNetworkBinUploadImpl implements NetWorkBinUploadInterface{
	
	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	MemberRepository memberRepository;
	@Autowired
	CountryCache countryCache;
	@Autowired
	private BaseLookupDTOCache lookupCache;
	
	private static final String NETWORK_CUP = "NETWORK_CUP";

	private static final String UNIQUE_CONSTRAINT_ERROR="duplicate key value violates unique constraint";
	
	@Override
	public String processNetworkBinFile(List<MultipartFile> files) {
		try {
			for (MultipartFile file : files) {
				try (BufferedReader lineReader = new BufferedReader(new InputStreamReader(file.getInputStream()));) {
				String lineText = null;
				String header = lineReader.readLine();
				log.debug("Header : "+ header);
				while ((lineText = lineReader.readLine()) != null && lineText.length() >= 111) {
					String cupData = lineText.substring(111);
					String data1 = cupData.replaceAll("\\s{2,}", " ").trim();
					String[] data = data1.split(" ");
					if (data.length > 1) {
						BinDetailsDTO binDetailsDto = new BinDetailsDTO();
						String binlength = cupData.split(" ")[0].substring(0, 2);
						String bin = cupData.split(" ")[0].substring(2, Integer.parseInt(binlength) + 2);
						Optional<BinDetailsDTO> binDetailsDTO1 = memberRepository.getNetBinDetail(bin);
						if (!binDetailsDTO1.isPresent()) {
							populateCupBinDetail(lineText, binDetailsDto, binlength, bin);
							insertNtwrkBinRangeExtracted(binDetailsDto);
						} else {
							log.debug("Details with this bin number is already present");
						}
					}
				}
				
			}
		}
		} catch (Exception e) {
			log.error("Network Bin File Upload Exception " + e.getMessage() + " - {}", e);
			return BaseCommonConstants.FAIL_STATUS;
		}
		return BaseCommonConstants.PROCESS_STATUS_SUCCESS;
	}

	private void insertNtwrkBinRangeExtracted(BinDetailsDTO binDetailsDto) {
		try {
			memberRepository.insertNetwBinRange(binDetailsDto);
		}catch(Exception ex) {
			handleGenericException(ex);
		}
	}

	private void handleGenericException(Exception ex) {
		String errorDesc;
		if (ex.getCause() != null) {
			errorDesc = ex.getCause().toString();
		} else {
			errorDesc = ex.getMessage();
		}
		if (StringUtils.contains(errorDesc,UNIQUE_CONSTRAINT_ERROR)) {
			log.info("Duplicate Data Entry - {}",ex.getMessage());
		}else {
			log.info("Error - {}:",ex.getMessage());
		}
	}

	private void populateCupBinDetail(String lineText, BinDetailsDTO binDetailDto, String binlength, String bin)
			throws ParseException {
		String panLen = lineText.substring(125, 127);
		String cardType = lineText.substring(127, 128);
		if ("C".equals(cardType)) {
			cardType = "02";
		} else if ("D".equals(cardType)) {
			cardType = "01";
		} else if ("P".equals(cardType)) {
			cardType = "03";
		}
		String currencyCode = lineText.substring(73, 76);
		binDetailDto.setBinNumber(bin);
		binDetailDto.setBinId(fetchBinIdSeq());
		if (Integer.parseInt(binlength) < 9) {
			binDetailDto.setLowBin(StringUtils.rightPad(bin, 9, "0"));
			binDetailDto.setHighBin(StringUtils.rightPad(bin, 9, "9"));
		}else {
			binDetailDto.setLowBin(bin.substring(0, 9));
			binDetailDto.setHighBin(bin.substring(0,9));
		}
		
		String participantId = lookupCache.getDescription(NETWORK_CUP, "DEF_ISS_PARTICIPANT");
		String bankGroup=lookupCache.getDescription(NETWORK_CUP, "DEF_ISS_BANKGRP");
		String binProductType = lookupCache.getDescription(NETWORK_CUP, "BIN_PROD_TYPE");
		String binCardVarient = lookupCache.getDescription(NETWORK_CUP, "BIN_CARD_VARIENT");
		String binCardTechnology = lookupCache.getDescription(NETWORK_CUP, "BIN_CARD_TECHNOLOGY");
		String binCardBrand = lookupCache.getDescription(NETWORK_CUP, "BIN_CARD_BRAND");
		String productType = lookupCache.getDescription(NETWORK_CUP, "PROD_TYPE");
		String settlementBin = lookupCache.getDescription(NETWORK_CUP, "SETTLEMENT_BIN");

		binDetailDto.setAcqBinActivationDate(new Date());
		binDetailDto.setDectivationDate(new SimpleDateFormat("yyyy-MM-dd").parse("2099-12-31"));
		binDetailDto.setBinType("I");
		binDetailDto.setCreatedBy(sessionDTO.getUserName());
		binDetailDto.setCreatedOn(new Date());
		binDetailDto.setLastUpdatedBy(sessionDTO.getUserName());
		binDetailDto.setLastUpdatedOn(new Date());
		if(participantId != null) {
			binDetailDto.setParticipantId(participantId);
		}else {
			binDetailDto.setParticipantId("NUPI5010001");

		}
		binDetailDto.setDomainUsage("2");
		binDetailDto.setBankGroup(bankGroup);
		binDetailDto.setOfflineAllowed("N");
		binDetailDto.setPanLength(Integer.parseInt(panLen));
		binDetailDto.setBinCardType(cardType);
		binDetailDto.setBinProductType(binProductType);
		binDetailDto.setBinCardVariant(binCardVarient);
		binDetailDto.setCardTechnology(binCardTechnology);
		binDetailDto.setBinCardBrand(binCardBrand);
		binDetailDto.setMessageType("D");
		binDetailDto.setProductType(productType);
		binDetailDto.setSettlementBin(settlementBin);
		binDetailDto.setStatus("A");
		String countryCode = countryCache.getCountryCodeByISO(lineText.substring(73, 76));
		binDetailDto.setCountryCode(StringUtils.isNotBlank(countryCode) ? countryCode :"");
		binDetailDto.setCurrencyCode(currencyCode);
	}
	
	private int fetchBinIdSeq() {
		return memberRepository.fetchBinIdSeq();
	}

}
