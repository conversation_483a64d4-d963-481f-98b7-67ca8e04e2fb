package org.npci.settlenxt.adminportal.service;

import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.repository.ReportRepository;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.FileNameUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;



@Slf4j
@Service
public class ReportArtifactServiceImpl implements ReportArtifactService {

	
	public static final String CRLF_CHAR = "\n";

	private static final String APPLICATION_OCTET = "application/octet-stream";
	private static final String CONTENT_DISP = "Content-Disposition";
	private static final String CONTENT_LENGTH = "Content-Length";

	@Autowired
	ReportRepository reportRepo;

	@Override
	public List<String> getFiles(String path) {

		List<String> fileList = new ArrayList<>();
		try {
			path = new File(path).getCanonicalPath();
			File pathfolder = new File(path);
			File[] files = pathfolder.listFiles();
			if (files != null) {
				for (File file : files) {
					
					if (file.isFile())
						{
						fileList.add(FileNameUtil.sanitizeFileNameReportArtifact(file.getName()));
						}
					
				}
			}
		} catch (Exception ex) {
			throw new SettleNxtException("Error while Getting Participant Files  {}", ex.getMessage(), ex);

		}
		return fileList;
	}




	
	
	
	@Override
	public List<String> getFolders(String path) {

		List<String> foldersList = new ArrayList<>();
		try {
			path = new File(path).getCanonicalPath();
			File pathfolder = new File(path);
			File[] folders = null;


			
			FileFilter fileFilter = File::isDirectory;

			folders = pathfolder.listFiles(fileFilter);

			if (folders != null) {
				for (File fol:folders) {
					File folder = fol;
					foldersList.add(folder.getName());
				}
			}
		} catch (Exception ex) {
			throw new SettleNxtException("Error while Getting Participant Files  {}", ex.getMessage(), ex);

		}
		return foldersList;

	}

	@Override
	public void downloadFile(String filePath, HttpServletRequest request, HttpServletResponse response)
			throws SettleNxtException, IOException {
		 

		try {
			if (request != null && request.getServletContext() != null && null != filePath
					&& StringUtils.isNotBlank(filePath)) {
				filePath = FileNameUtil.sanitizeFileNameReportArtifact(filePath);
				filePath = new File(filePath).getCanonicalPath();
				ServletContext context = request.getServletContext();
				File downloadFile = new File(filePath);
				try(FileInputStream inputStream = new FileInputStream(downloadFile)){
				
				String mimeType = context.getMimeType(filePath);
				
				if (mimeType == null) {
					mimeType = APPLICATION_OCTET;
				}
				if (!mimeType.contains(CRLF_CHAR)) {
					response.setContentType(mimeType);
					String docLength = String.valueOf(downloadFile.length());
					response.setHeader(CONTENT_LENGTH, docLength);
					String headerKey = CONTENT_DISP;
					String fileName = FileNameUtil.sanitizeFileNameReportArtifact(downloadFile.getName());
					
					String headerValue = String.format("attachment; filename=\"%s\"", fileName);
					response.setHeader(headerKey, headerValue);
					if (response.getOutputStream() != null) {
						try (OutputStream outStream = response.getOutputStream()) {
							byte[] buffer = new byte[100];
							int bytesRead = -1;

							while ((bytesRead = inputStream.read(buffer)) != -1) {
								outStream.write(buffer, 0, bytesRead);

							}
						}
					}

					}
				}
			}

		} catch (Exception ex) {

			throw new SettleNxtException("Error while Downloading Files  {}", ex.getMessage(), ex);

		} 

	}
}
