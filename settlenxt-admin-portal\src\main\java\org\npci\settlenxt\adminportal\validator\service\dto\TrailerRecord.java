package org.npci.settlenxt.adminportal.validator.service.dto;

import java.util.List;

public class TrailerRecord {
	private List<Record> records;
	

	public List<Record> getRecords() {
		return records;
	}

	public void setRecords(List<Record> records) {
		this.records = records;
	}

	public String getTrailerDataAsString() {
		StringBuilder allRecord = new StringBuilder();
		Record recod = null;
		for(int i=0; i<records.size(); i++){
			recod = records.get(i);
			allRecord.append(recod.getName()+"="+recod.getValue());
			if(i < (records.size()-1))
				{
				allRecord.append(",");
				}
		}
		
		
		return allRecord.toString();
	}
	
	public String getTrailerDataAsXMLString() {
		StringBuilder allRecord = new StringBuilder();
		Record recod = null;
		for(Record rec:records){
			recod = rec;
			allRecord.append("<" + recod.getName() + ">" + recod.getValue() + "</" + recod.getName() + ">");
		}		
		
		return allRecord.toString();
	}

	 
}
