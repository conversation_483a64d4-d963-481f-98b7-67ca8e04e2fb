package org.npci.settlenxt.adminportal.service;

import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;

/**
 * Service Interface used for
 * <li>Get the cross site status details</li>
 * <li>retry the icn or outgoing files</li>
 * <AUTHOR>
 *
 */
public interface ICrossSiteIcStatusService {

	CycleManagementDTO getCrossSiteIcStatusDetails(CycleManagementDTO cycleManagementDTO);
		
	String retryIcnOrOutgoingFiles(CycleManagementDTO cycleManagementDTO);

}
