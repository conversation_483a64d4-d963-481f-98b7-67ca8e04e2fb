package org.npci.settlenxt.adminportal.service;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.npci.settlenxt.adminportal.config.kafka.utils.KafkaUtilService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

@Component

public class FileMoverHandler {
	@Autowired
	ApplicationContext appContext;
	
	@Autowired
	private Environment env;
	
	private static final Logger logger = LogManager.getLogger(FileMoverHandler.class);
	
	ExecutorService executorService;
	
	@Autowired
	private KafkaUtilService kafkaUtils;
	public void handle(String topic,JSONObject kafkaMessage) {											
		executorService.submit(new FileMoverKafkaPublishTask(topic,kafkaMessage));
	}
	
	@PostConstruct
	void init() {
		String workerThreads = env.getProperty("FileMoverThreads", "8");
		executorService = Executors.newFixedThreadPool(Integer.valueOf(workerThreads));
	}
	
	
	public class FileMoverKafkaPublishTask implements Callable<String> {

		JSONObject kafkaMessage;
		String topicName;
		
		public FileMoverKafkaPublishTask(String topic,JSONObject kafkaMessage) {
			this.kafkaMessage = kafkaMessage;
			this.topicName = topic;
		}

		@Override
		public String call() throws Exception {
			try {
				logger.info("Publishing File Mover Info");
				ObjectMapper objectMapper = new ObjectMapper();
				kafkaUtils.sendKafkaMessage(topicName,objectMapper.writeValueAsString(kafkaMessage));
			} catch (Exception e) {
				logger.error("Error while publishing File Mover Info - {}", e.getMessage(), e);
			}
			return null;
		}

	}
}
