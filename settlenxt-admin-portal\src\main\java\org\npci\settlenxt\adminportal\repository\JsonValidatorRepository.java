package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.portal.common.dto.JsonValidatorDTO;

@Mapper
public interface JsonValidatorRepository {

	List<JsonValidatorDTO> getJsonValidatorList();
	
	List<JsonValidatorDTO> getJsonValidatorMainList();

	void addJsonValidatorStg(JsonValidatorDTO jsonValidatorDto);

	JsonValidatorDTO getJsonValidator(int seqId);

	List<JsonValidatorDTO> getJsonValidatorPendingList();

	JsonValidatorDTO getJsonValidatorStgDetail(int seqId);

	void updateJsonValidatorStg(JsonValidatorDTO jsonValidatorDto);
	
	void updateJsonValidator(JsonValidatorDTO jsonValidatorDto);

	void saveJsonValidator(JsonValidatorDTO jsonValidatorDto);

	void deleteDiscardedJsonValidator(int seqId);

	JsonValidatorDTO fetchSeqId();

	List<String> getPcodeList();

}
