package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.adminportal.dto.CycleStatusMonitorDTO;

@Mapper
public interface CycleStatusMonitorRepository {
	
	List<CycleStatusMonitorDTO> getCycleDetailsByGuid(@Param("guid") int guid);
	
	CycleStatusMonitorDTO getCycleStatusByGuid(@Param("guid") int guid);
	
	List<CycleStatusMonitorDTO> getCycleStatusDetails(CycleStatusMonitorDTO activityModuleDto);
	
	
	

}
