package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.dto.MccDTO;
import org.springframework.stereotype.Service;

@Service
public interface MccService {

	List<MccDTO> getMccList();
	List<MccDTO> getPendingMccConfigs();
	MccDTO addMccConfig(MccDTO mccDTO);
	MccDTO updateApproveOrRejectMcc(Integer mccId, String status, String remarks);
	MccDTO getMccConfigById(Integer mccId);
	MccDTO updateMccConfig(MccDTO mccDTO);
	MccDTO getPendingMccConfigById(Integer mccId);
	MccDTO discardMcc(Integer mccId);
	 String approveOrRejectMccBulk(String bulkApprovalReferenceNoList, String status,String remarks);
	
	 int checkDuplicateData(MccDTO mccDTO);
	MccDTO getMccConfigStgById(Integer mccId);
}