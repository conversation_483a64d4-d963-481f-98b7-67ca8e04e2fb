package org.npci.settlenxt.adminportal.controllers;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URLDecoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.birt.core.exception.BirtException;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.NewsAlertsService;
import org.npci.settlenxt.adminportal.service.UserService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.NewsAlertsDTO;
import org.npci.settlenxt.portal.common.dto.NewsDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.npci.settlenxt.portal.common.dto.UserDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.npci.settlenxt.portal.validator.TransactValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;

@Controller
public class NewsAlertsController extends BaseController {

	@Autowired
	private NewsAlertsService newsAlertsService;
	@Autowired
	BaseLookupService lookUpService;

	@Autowired
	private UserService userService;

	@Autowired
	private SessionDTO sessionDto;

	private static final String ADD_EDIT_NEWS_ALERTS = "addNewsAlerts";
	private static final String SHOW_NEWS_ALERTS = "previewNewsAlerts";
	private static final String VIEW_NEWS_ALERTS = "viewNewsAlerts";
	private static final String VIEW_NEWS_ALERTS_INFO = "viewNewsAlertsInfo";
	private static final String NEWS_ALERTS_REPORT = "newsAlertsReport";
	private static final String ERROR_MESSAGE = "errorMessage";
	private static final String ERROR_STATUS = "errorStatus";
	private static final String SHOW_BUTTON="showbutton";
	private static final String BACK_BUTTON="backbutton";
	private static final String YES="yes";
	private static final String CLEAR_ID="clearId";
	private static final String SHOW_ACTIVE="showActive";
	private static final String A="A";
	private static final String C="C";
	private static final String P="P";
	private static final String APPROVAL_ACTIVE="approvalActive";
	private static final String NEWS_LIST_PENDING="newsListPending";
	private static final String PENDING="Pending";
	private static final String REJECTED="Rejected";
	private static final String CHECKER="Checker";
	private static final String T_SAVED_NEWS_LIST="TsavednewsList";
	private static final String SHOW_SAVED_NEWS="showSavedNews";
	private static final String IN_PROGRESS="In Progress";
	private static final String D_NEWS_LIST="DnewsList";
	private static final String SHOW_DELETED_NEWS="showDeletedNews";
	private static final String M="M";
	private static final String SUCCESS_MSG_REJECTED="News and Alerts Bulk Rejected Successfully";
	private static final String SUCCESS_MSG_APPROVED="News and Alerts Bulk Approved Successfully";
	private static final String ROLE="ROLE";
	private static final String BANK="BANK";
	private static final String USER="USER";
	private static final String ERROR_MSG_NO_RECORDS_FOUND="No Records Found";
	private static final String SUBMIT="submit";
	private static final String E="E";
	private static final String SAVE="SAVE";
	private static final String SAVE_LOWER="save";
	private static final String NEWS_ALERTS_NOT_SAVED_MSG="News/Alerts information not saved yet";
	private static final String NEWS_ID="newsId";
	private static final String NEWS_ALERT_SAVED="News/Alerts Saved";
	private static final String SUCCESS_MSG_NEWS_ALERT="Save News/Alerts Success";
	private static final String FAILED_MSG_NEWS_ALERT="Save News/Alerts Failed";
	private static final String ERROR_MSG="News/Alerts required ";
	private static final String ERROR_FINAL_MSG_NEWS_ALERT="Final News/Alerts Submit Error ";
	private static final String FAILED_FINAL_MSG_NEWS_ALERT="Submit News/Alerts Failed";
	private static final String IN_ACTIVE="I";
	private static final String APPROVE_NEWS_ALERTS="approveNewsAlerts";
	private static final String APPROVE_REJECT_BUTTON="apporveRejectButton";
	private static final String SUCCESS_MSG_DELETE_NEWS_ALERT="News and Alerts delete request sent for approval successfully";
	private static final String SAVED_SCREEN="savedScreen";
	private static final String YES_CAPITALIZE="Yes";
	private static final String FAILED_MSG_DELETE_NEWS_ALERT="News and Alerts delete Failed";
	private static final String SHOW="show";
	private static final String ROLE_LIST="RoleList";
	private static final String BANK_NAMES="bankNames";
	private static final String MONTH_LIST="monthList";
	private static final String WEEK_LIST="weekList";
	private static final String PERIOD_TYPE_LIST="periodTypeList";
	private static final String DISTRIBUTIONL_LIST="distributionList";
	private static final String V="V";
	private static final String SHOW_EDIT="showEdit";
	private static final String USER_LIST="userList";
	private static final String NEWS_TYPE="newsType";
	private static final String PUBLISH_TYPED="publishType";
	private static final String SEND_MAIL_FLAG="sendMailFlag";
	private static final String SPECIFIC="Specific";
	private static final String EDIT="EDIT";
	private static final String ADD="ADD";
	private static final String UTF_8 = "UTF-8";
	
	
	private static final String TOTAL_DISPLAY_RECORDS = "iTotalDisplayRecords";
	private static final String TOTAL_RECORDS = "iTotalRecords";

	@PostMapping("/newsCreation")
	@PreAuthorize("hasAuthority('Add News And Alerts')")
	public String newsCreation(Model model) {
		NewsAlertsDTO newsAlertsDTO = new NewsAlertsDTO();
		newsAlertsService.addDefaultListData(model);
		model.addAttribute(ADD_EDIT_NEWS_ALERTS, CommonConstants.ADD_NEWS_ALERTS);
		model.addAttribute(SHOW_BUTTON, CommonConstants.ADD_NEWS_ALERTS);
		model.addAttribute(CommonConstants.REQ_TYPE, ADD);
		model.addAttribute(BACK_BUTTON, YES);
		model.addAttribute(CLEAR_ID, CommonConstants.YES_FLAG);
		model.addAttribute(BaseCommonConstants.NEWS_ALERT_DTO, newsAlertsDTO);
		return getView(model, ADD_EDIT_NEWS_ALERTS);
	}

	@PostMapping("/getNewsAlertsList")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public String previewNewsAlertsList(Model model) {
		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, CommonConstants.YES);
		model.addAttribute(BaseCommonConstants.NEWS_LIST, BaseCommonConstants.NEWS_LIST);
		model.addAttribute(SHOW_ACTIVE, CommonConstants.YES);
		model.addAttribute(BaseCommonConstants.MAKER, CommonConstants.YES);
		return getView(model, SHOW_NEWS_ALERTS);
	}

	@PostMapping("/searchNewsAlerts")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public ResponseEntity<Object> searchNewsAlerts(
			@ModelAttribute(BaseCommonConstants.NEWS_ALERT_DTO) NewsAlertsDTO newsAlertsDTO, HttpServletResponse response,
			BindingResult result, Locale locale, ModelAndView modelAndView, ModelMap model) throws SettleNxtException {

		JsonObject jsonResponse = new JsonObject();
		int iDisplayStart = 0;
		int iDisplayLength = 0;
		String sSearch = "";
		String sEcho = "";
		JsonArray data = new JsonArray();
		String errorMessage = "";
		String errorStatus = "";
		int iTotalRecords = 0;
		long iTotalDisplayRecords = 0;
		List<NewsAlertsDTO> newsList = null;
		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();

		if (!result.hasErrors()) {

			setInputValueForSearchCriteria(searchCriteriaDTO, iDisplayStart, iDisplayLength, sSearch);
			newsList = newsAlertsService.getFinalNewsAlertsList(searchCriteriaDTO);

			iTotalRecords = 10;

			iTotalDisplayRecords = newsAlertsService.getRowCount();

			if (newsList != null && !newsList.isEmpty()) {

				jsonResponse.addProperty(BaseCommonConstants.S_ECHO, sEcho);
				jsonResponse.addProperty(TOTAL_RECORDS, iTotalRecords);
				jsonResponse.addProperty(TOTAL_DISPLAY_RECORDS, iTotalDisplayRecords);
				for (NewsAlertsDTO c : newsList) {
					addData(data, c);
				}
			}
		}
		// Sending JSON response to page
		addJsonReponse(jsonResponse, sEcho, data, errorMessage, errorStatus, iTotalRecords, iTotalDisplayRecords);

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	private void addJsonReponse(JsonObject jsonResponse, String sEcho, JsonArray data, String errorMessage,
			String errorStatus, int iTotalRecords, long iTotalDisplayRecords) {
		jsonResponse.addProperty(ERROR_STATUS, errorStatus);
		jsonResponse.addProperty(ERROR_MESSAGE, errorMessage);
		jsonResponse.addProperty(BaseCommonConstants.S_ECHO, sEcho);
		jsonResponse.addProperty(TOTAL_RECORDS, iTotalRecords);
		jsonResponse.addProperty(TOTAL_DISPLAY_RECORDS, iTotalDisplayRecords);
		jsonResponse.add(BaseCommonConstants.AA_DATA, data);
	}

	private void addData(JsonArray data, NewsAlertsDTO c) {
		JsonArray row = new JsonArray();
		prepareJsonForNewsAlertInfo(c, row);

		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
			row.add(new JsonPrimitive(A));
		} else {
			row.add(new JsonPrimitive(C));
		}
		data.add(row);
	}

	private void prepareJsonForNewsAlertInfo(NewsAlertsDTO c, JsonArray row) {
		setDataInRow(c, row);
		row.add(new JsonPrimitive(c.getStatus() == null ? "" : c.getStatus() + ""));
		row.add(new JsonPrimitive(c.getCreatedBy() == null ? "" : c.getCreatedBy() + ""));
		row.add(new JsonPrimitive(c.getCreatedDate() == null ? "" : c.getCreatedDate()));
	}

	@PostMapping("/getPendingNewsAlertsList")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public String previewPendingNewsAlertsList(Model model) {
		model.addAttribute(BaseCommonConstants.TNEWS_LIST, BaseCommonConstants.TNEWS_LIST);
		model.addAttribute(APPROVAL_ACTIVE, CommonConstants.YES);
		setPendingNewsAlert(model);
		validateMakerCheckerForCheckbox(model);
		return getView(model, SHOW_NEWS_ALERTS);
	}

	private void validateMakerCheckerForCheckbox(Model model) {
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
			model.addAttribute(CommonConstants.SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(CommonConstants.SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
		}
	}

	public void setPendingNewsAlert(Model model) {
		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
		List<NewsAlertsDTO> newsListPending = prepareNewsPendingList(searchCriteriaDTO);

		model.addAttribute(NEWS_LIST_PENDING, newsListPending);
	}

	private List<NewsAlertsDTO> prepareNewsPendingList(SearchCriteriaDTO searchCriteriaDTO) {
		int iDisplayStart = 0;
		int iDisplayLength = 0;
		String sSearch = "";
		return prepareNewsAlertTempList(searchCriteriaDTO, iDisplayStart, iDisplayLength,
				sSearch);
	}

	private List<NewsAlertsDTO> prepareNewsAlertTempList(SearchCriteriaDTO searchCriteriaDTO, int iDisplayStart,
			int iDisplayLength, String sSearch) {

		setInputValueForSearchCriteria(searchCriteriaDTO, iDisplayStart, iDisplayLength, sSearch);
		return newsAlertsService.getTempNewsAlertsList(searchCriteriaDTO);
	}

	private void setInputValueForSearchCriteria(SearchCriteriaDTO searchCriteriaDTO, int iDisplayStart,
			int iDisplayLength, String sSearch) {
		int startval = iDisplayStart + 1;
		int endval = iDisplayStart + iDisplayLength;
		searchCriteriaDTO.setStartVal(startval);
		searchCriteriaDTO.setEndVal(endval);
		searchCriteriaDTO.setSearchName(sSearch);
	}

	@PostMapping("/searchPendingNewsAlerts")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public ResponseEntity<Object> searchPendingNewsAlerts(
			@ModelAttribute(BaseCommonConstants.NEWS_ALERT_DTO) NewsAlertsDTO newsAlertsDTO, BindingResult result) {
		JsonObject jsonResponse = new JsonObject();
		try {

			JsonArray data = new JsonArray();
			String errorMessage = "";
			String errorStatus = "";
			int iTotalRecords = 0;
			long iTotalDisplayRecords = 0;
			List<NewsAlertsDTO> newsList = null;
			SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();

			int iDisplayStart = 0;
			int iDisplayLength = 0;
			String sSearch = "";
			String sEcho = "";

			if (!result.hasErrors()) {
				newsList = prepareNewsAlertTempList(searchCriteriaDTO, iDisplayStart, iDisplayLength, sSearch);

				iTotalRecords = 10;
				iTotalDisplayRecords = newsAlertsService.getRowCountApproval();

				if (newsList != null && !newsList.isEmpty()) {

					jsonResponse.addProperty(BaseCommonConstants.S_ECHO, sEcho);
					jsonResponse.addProperty(TOTAL_RECORDS, iTotalRecords);
					jsonResponse.addProperty(TOTAL_DISPLAY_RECORDS, iTotalDisplayRecords);
					for (NewsAlertsDTO c : newsList) {
						JsonArray row = new JsonArray();
						if (!CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {

							row.add("");
						}
						addRowsearchPendingNewsAlerts(data, c, row);
					}
				}
			}
			addJsonReponse(jsonResponse, sEcho, data, errorMessage, errorStatus, iTotalRecords, iTotalDisplayRecords);
		} catch (Exception ex) {
			jsonResponse.addProperty(CommonConstants.ERROR, BaseCommonConstants.ERROR_FETCHING_NEWS);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	private void addRowsearchPendingNewsAlerts(JsonArray data, NewsAlertsDTO c, JsonArray row) {
		setDataInRow(c, row);
		if (c.getRequestState().equalsIgnoreCase("P")) {
			row.add(new JsonPrimitive(PENDING));
		} else {
			row.add(new JsonPrimitive(REJECTED));
		}
		row.add(new JsonPrimitive(c.getCreatedBy() == null ? "" : c.getCreatedBy() + ""));
		row.add(new JsonPrimitive(c.getCreatedDate() == null ? "" : c.getCreatedDate()));
		row.add(new JsonPrimitive(c.getCheckerComments() == null ? "" : c.getCheckerComments() + ""));
		row.add(new JsonPrimitive(c.getModifiedDate() == null ? "" : c.getModifiedDate() + ""));

		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
			row.add(new JsonPrimitive(""));
		} else {
			row.add(new JsonPrimitive(CHECKER));
		}

		data.add(row);
	}

	private void setDataInRow(NewsAlertsDTO c, JsonArray row) {
		row.add(new JsonPrimitive(c.getReferenceNumber() == null ? "" : c.getReferenceNumber() + ""));
		row.add(new JsonPrimitive(c.getIsType() == null ? "" : c.getIsType()));
		row.add(new JsonPrimitive(c.getTitle() == null ? "" : c.getTitle() + ""));
		row.add(new JsonPrimitive(c.getSubTitle() == null ? "" : c.getSubTitle()));
		row.add(new JsonPrimitive(c.getFromDateStr() == null ? "" : c.getFromDateStr()));
		row.add(new JsonPrimitive(c.getToDateStr() == null ? "" : c.getToDateStr()));
		row.add(new JsonPrimitive(c.getFooterData() == null ? "" : c.getFooterData() + ""));
	}

	@PostMapping("/getSavedNewsList")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public String previewSavedNewsAlertsList(Model model) {
		model.addAttribute(T_SAVED_NEWS_LIST, T_SAVED_NEWS_LIST);
		model.addAttribute(SHOW_SAVED_NEWS, CommonConstants.YES);
		model.addAttribute(BaseCommonConstants.MAKER, CommonConstants.YES);
		return getView(model, SHOW_NEWS_ALERTS);
	}

	@PostMapping("/searchSavedNewsAlerts")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public ResponseEntity<Object> searchSavedNewsAlerts(
			@ModelAttribute(BaseCommonConstants.NEWS_ALERT_DTO) NewsAlertsDTO newsAlertsDTO, BindingResult result) {
		JsonObject jsonResponse = new JsonObject();
		try {

			JsonArray data = new JsonArray();
			String errorMessage = "";
			String errorStatus = "";
			int iTotalRecords = 0;
			long iTotalDisplayRecords = 0;
			List<NewsAlertsDTO> newsList = null;
			SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();

			String sEcho = "";

			if (!result.hasErrors()) {

				newsList = newsAlertsService.getSavedNewsAlertsList(searchCriteriaDTO);

				iTotalRecords = 10;

				iTotalDisplayRecords = newsAlertsService.getSavedRowCount();

				if (newsList != null && !newsList.isEmpty()) {

					jsonResponse.addProperty(BaseCommonConstants.S_ECHO, sEcho);
					jsonResponse.addProperty(TOTAL_RECORDS, iTotalRecords);
					jsonResponse.addProperty(TOTAL_DISPLAY_RECORDS, iTotalDisplayRecords);
					for (NewsAlertsDTO c : newsList) {
						addingRows(data, c);
					}
				}
			}
			addJsonReponse(jsonResponse, sEcho, data, errorMessage, errorStatus, iTotalRecords, iTotalDisplayRecords);
		} catch (Exception ex) {
			jsonResponse.addProperty(CommonConstants.ERROR, BaseCommonConstants.ERROR_FETCHING_NEWS);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	private void addingRows(JsonArray data, NewsAlertsDTO c) {
		JsonArray row = new JsonArray();
		setDataInRow(c, row);
		if (c.getRequestState().equalsIgnoreCase("I")) {
			row.add(new JsonPrimitive(IN_PROGRESS));
		} else {
			row.add(new JsonPrimitive(""));
		}
		row.add(new JsonPrimitive(c.getCreatedBy() == null ? "" : c.getCreatedBy() + ""));
		row.add(new JsonPrimitive(c.getCreatedDate() == null ? "" : c.getCreatedDate()));

		row.add(new JsonPrimitive(BaseCommonConstants.VIEW));
		data.add(row);
	}

	@PostMapping("/getDeletedNewsList")
	@PreAuthorize("hasAuthority('Delete News And Alerts')")
	public String previewDeletedNewsAlertsList(Model model) {
		model.addAttribute(D_NEWS_LIST, D_NEWS_LIST);
		model.addAttribute(SHOW_DELETED_NEWS, CommonConstants.YES);
		model.addAttribute(BaseCommonConstants.MAKER, CommonConstants.YES);
		return getView(model, SHOW_NEWS_ALERTS);
	}

	@PostMapping("/searchDeletedNewsAlerts")
	@PreAuthorize("hasAuthority('Delete News And Alerts')")
	public ResponseEntity<Object> searchDeletedNewsAlerts(
			@ModelAttribute(BaseCommonConstants.NEWS_ALERT_DTO) NewsAlertsDTO newsAlertsDTO, BindingResult result) {
		JsonObject jsonResponse = new JsonObject();

		try {
			JsonArray data = new JsonArray();
			String errorMessage = "";
			String errorStatus = "";
			int iTotalRecords = 0;
			long iTotalDisplayRecords = 0;
			List<NewsAlertsDTO> newsList = null;
			SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();

			int iDisplayStart = 0;
			int iDisplayLength = 0;
			String sSearch = "";
			String sEcho = "";

			if (!result.hasErrors()) {
				setInputValueForSearchCriteria(searchCriteriaDTO, iDisplayStart, iDisplayLength, sSearch);
				newsList = newsAlertsService.getDeletedNewsAlertsList(searchCriteriaDTO);

				iTotalRecords = newsList.size();
				iTotalDisplayRecords = newsAlertsService.getDeleteRowCount();

				if (!newsList.isEmpty()) {

					jsonResponse.addProperty(BaseCommonConstants.S_ECHO, sEcho);
					jsonResponse.addProperty(TOTAL_RECORDS, iTotalRecords);
					jsonResponse.addProperty(TOTAL_DISPLAY_RECORDS, iTotalDisplayRecords);
					for (NewsAlertsDTO c : newsList) {
						addRow(data, c);
					}
				}
			}
			addJsonReponse(jsonResponse, sEcho, data, errorMessage, errorStatus, iTotalRecords, iTotalDisplayRecords);
		} catch (Exception ex) {
			jsonResponse.addProperty(CommonConstants.ERROR, BaseCommonConstants.ERROR_FETCHING_NEWS);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	private void addRow(JsonArray data, NewsAlertsDTO c) {
		JsonArray row = new JsonArray();
		prepareJsonForNewsAlertInfo(c, row);

		row.add(new JsonPrimitive(M));
		row.add(new JsonPrimitive(A));
		data.add(row);
	}

	@PostMapping("/saveNewsAlerts")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public String saveNewsAlerts(@ModelAttribute(BaseCommonConstants.NEWS_ALERT_DTO) NewsAlertsDTO newsAlertsDTO,
			Model model) {
		newsAlertsService.addDefaultListData(model);
		model.addAttribute(ADD_EDIT_NEWS_ALERTS, CommonConstants.ADD_NEWS_ALERTS);
		model.addAttribute(SHOW_BUTTON, CommonConstants.ADD_NEWS_ALERTS);
		return getView(model, ADD_EDIT_NEWS_ALERTS);
	}

	@PostMapping("/addNewsAlerts")
	@PreAuthorize("hasAnyAuthority('Add News And Alerts','Edit News And Alerts')")
	public ResponseEntity<String> addNewsAlerts(Model model, @RequestParam("objectData") String newsInfo) {
		JsonObject jsonResponse = new JsonObject();
		if (!checkForAnyScripts(newsInfo)) {
		try {

			ObjectMapper mapper = new ObjectMapper();
			NewsAlertsDTO newsAlertsDTO = mapper.readValue(newsInfo, NewsAlertsDTO.class);
			newsAlertsDTO.setTitle(URLDecoder.decode(newsAlertsDTO.getTitle(), UTF_8));
			newsAlertsDTO.setSubTitle(URLDecoder.decode(newsAlertsDTO.getSubTitle(), UTF_8));
			newsAlertsDTO.setSummary(URLDecoder.decode(newsAlertsDTO.getSummary(), UTF_8));
			newsAlertsDTO.setFooterData(URLDecoder.decode(newsAlertsDTO.getFooterData(), UTF_8));
			newsAlertsDTO.setDetails(URLDecoder.decode(newsAlertsDTO.getDetails(), UTF_8));
			newsAlertsDTO.setComment(URLDecoder.decode(newsAlertsDTO.getComment(), UTF_8));
			if (SAVE_LOWER.equals(newsAlertsDTO.getButtonType())
					&& !SAVE.equalsIgnoreCase(newsAlertsDTO.getSaveNewsResult())) {

				
				model.addAttribute(CLEAR_ID, E);
				return handleFirstSaveNewsAlerts(newsAlertsDTO, jsonResponse);

			} else if (SAVE_LOWER.equals(newsAlertsDTO.getButtonType())
					&& SAVE.equalsIgnoreCase(newsAlertsDTO.getSaveNewsResult())) {
				return firstSaveDistributionSch(newsAlertsDTO, jsonResponse);

			} else if (SUBMIT.equals(newsAlertsDTO.getButtonType())) {

				newsAlertsDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
				if (ADD.equalsIgnoreCase(newsAlertsDTO.getReqType())) {

					return editSaveNewsAlerts(newsAlertsDTO, jsonResponse);

				} else if (E.equals(newsAlertsDTO.getReqType())) {
					return editSubmitNewsAlerts(newsAlertsDTO, jsonResponse);
				}
			} else {

				jsonResponse.addProperty(CommonConstants.ERROR, NEWS_ALERTS_NOT_SAVED_MSG);

			}
		} catch (Exception ex) {

			jsonResponse.addProperty(CommonConstants.ERROR, NEWS_ALERTS_NOT_SAVED_MSG);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
		}
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	private boolean checkForAnyScripts(String value) {
		value = value.toLowerCase(Locale.ROOT);
		return value.contains("<iframe") || value.contains("<script") || value.contains("<img")
				|| value.contains("%3Cscript%3E") || value.contains("%3Cimg%3E") || value.contains("J3NjcmlwdCc=")
				|| value.contains("prompt(") || value.contains("alert(") || regexMatchForHTMLTags(value);
	}

	private ResponseEntity<String> handleFirstSaveNewsAlerts(NewsAlertsDTO newsAlertsDTO, JsonObject jsonResponse) {
		if (0 != newsAlertsDTO.getNewsId()) {

			long count = newsAlertsService.checkIfExistsInStg(newsAlertsDTO.getNewsId());
			if (count > 0) {
				return firstSaveDistributionSch(newsAlertsDTO, jsonResponse);
			} else {
				return firstTimeSaveNewsAlerts(newsAlertsDTO, jsonResponse);
			}
		} else {
			return firstTimeSaveNewsAlerts(newsAlertsDTO, jsonResponse);
		}
	}

	private boolean regexMatchForHTMLTags(String value) {
		String regexOpenHTML = ".*<[^/][^>]*>.*";

		Pattern openHTMLPattern = Pattern.compile(regexOpenHTML);

		Matcher matchCaseOpenTags = openHTMLPattern.matcher(value);

		String regexCloseHTML = ".*</[^>]+>.*";

		Pattern closeHTMLPattern = Pattern.compile(regexCloseHTML);

		Matcher matchCaseCloseTags = closeHTMLPattern.matcher(value);
		return matchCaseOpenTags.matches() || matchCaseCloseTags.matches();
	}
	public void updateNewsAlertsData(NewsAlertsDTO newsAlertsDTO) {

		try {
			newsAlertsService.updateNewsInfo(newsAlertsDTO);
			newsAlertsService.updateSchedulerInfo(newsAlertsDTO);
			newsAlertsService.addDistributionDetails(newsAlertsDTO);
		} catch (Exception ex) {
			throw new SettleNxtException("", "Error Updating News Alerts Data" + ex.getMessage(), ex);
		}
	}

	public ResponseEntity<String> firstTimeSaveNewsAlerts(NewsAlertsDTO newsAlertsDTO, JsonObject jsonResponse) {
		NewsDTO savedNews = null;
		try {
			savedNews = newsAlertsService.addNewsInfo(newsAlertsDTO);
			jsonResponse.addProperty(NEWS_ID, savedNews.getNewsId());
			if (NEWS_ALERT_SAVED.equals(savedNews.getSaveNewsResult())) {
				jsonResponse.addProperty(BaseCommonConstants.SAVE_NEWS_RESULT, SAVE);
				jsonResponse.addProperty(BaseCommonConstants.SUCCESS, SUCCESS_MSG_NEWS_ALERT);
			}

			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		} catch (Exception e) {
			jsonResponse.addProperty(BaseCommonConstants.SAVE_NEWS_RESULT, "");
			jsonResponse.addProperty(CommonConstants.ERROR, FAILED_MSG_NEWS_ALERT);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
	}

	public ResponseEntity<String> firstSaveDistributionSch(NewsAlertsDTO newsAlertsDTO, JsonObject jsonResponse) {
		try {

			newsAlertsDTO.setRequestState(IN_ACTIVE);
			newsAlertsDTO.setStatus(IN_ACTIVE);
			jsonResponse.addProperty(BaseCommonConstants.SAVE_NEWS_RESULT, SAVE);
			updateNewsAlertsData(newsAlertsDTO);
			jsonResponse.addProperty(BaseCommonConstants.SUCCESS, SUCCESS_MSG_NEWS_ALERT);
			jsonResponse.addProperty(NEWS_ID, newsAlertsDTO.getNewsId());
			jsonResponse.addProperty(BaseCommonConstants.SCREEN_NAME, newsAlertsDTO.getScreenName());
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		} catch (Exception e) {
			jsonResponse.addProperty(BaseCommonConstants.SAVE_NEWS_RESULT, "");
			jsonResponse.addProperty(CommonConstants.ERROR, FAILED_MSG_NEWS_ALERT);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
	}

	public ResponseEntity<String> editSaveNewsAlerts(NewsAlertsDTO newsAlertsDTO, JsonObject jsonResponse) {
		if (Boolean.FALSE.equals(newsAlertsService.checkNewsInfoSaved(newsAlertsDTO.getTitle()))) {
			jsonResponse.addProperty(CommonConstants.ERROR, ERROR_MSG);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
		try {

			updateNewsAlertsData(newsAlertsDTO);
			jsonResponse.addProperty(BaseCommonConstants.SUCCESS, "News/Alerts submitted successfully and sent for checker's approval");
			jsonResponse.addProperty(BaseCommonConstants.REQUEST_STATE, newsAlertsDTO.getRequestState());
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		} catch (Exception e) {

			jsonResponse.addProperty(CommonConstants.ERROR, ERROR_FINAL_MSG_NEWS_ALERT);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
	}

	public ResponseEntity<String> editSubmitNewsAlerts(NewsAlertsDTO newsAlertsDTO, JsonObject jsonResponse) {
		try {
			updateNewsAlertsData(newsAlertsDTO);
			jsonResponse.addProperty(BaseCommonConstants.SUCCESS, "News/Alerts submitted successfully and sent for checker's approval");
			jsonResponse.addProperty(BaseCommonConstants.SCREEN_NAME, newsAlertsDTO.getScreenName());
			jsonResponse.addProperty(BaseCommonConstants.REQUEST_STATE, newsAlertsDTO.getRequestState());
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

		} catch (Exception e) {
			jsonResponse.addProperty(CommonConstants.ERROR, FAILED_FINAL_MSG_NEWS_ALERT);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
	}

	@PostMapping("/getNewsAlerts")
	@PreAuthorize("hasAnyAuthority('Add News And Alerts','Edit News And Alerts','View News And Alerts')")
	public String getNewsAlerts(@RequestParam(name = "newsId", required = false) Integer newsId,
			@RequestParam(CommonConstants.REQ_TYPE) String reqType,
			@RequestParam(name = "editFlag", required = false) String editFlag,
			@RequestParam("refNumber") String refNumber,
			@RequestParam(name = BaseCommonConstants.SCREEN_NAME, required = false) String screenName, Model model) {

		NewsDTO newsDTO = null;
		NewsAlertsDTO newsAlertsDTO = null;
		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
		reqType = handleReqType(newsId, reqType, refNumber, screenName, model, searchCriteriaDTO);

		if (E.equals(reqType)) {
			newsDTO = newsAlertsService.getNewsAlerts(refNumber);
			model.addAttribute(CommonConstants.REQ_TYPE, reqType);
			model.addAttribute(NEWS_TYPE, newsDTO.getIsType());
			model.addAttribute(PUBLISH_TYPED, newsDTO.getPublishType());
			model.addAttribute(BACK_BUTTON, YES);
			model.addAttribute(SEND_MAIL_FLAG, newsDTO.getSendMail());
			model.addAttribute(BaseCommonConstants.REQUEST_STATE, newsDTO.getRequestState());
			model.addAttribute(BaseCommonConstants.NEWS_ALERT_DTO, newsDTO);
			model.addAttribute(BaseCommonConstants.SAVE_NEWS_RESULT, EDIT);
			if (SPECIFIC.equalsIgnoreCase(newsDTO.getPublishType())) {
				List<NewsAlertsDTO> newsAlertsReport = newsAlertsService.fetchDistributionDetails(newsDTO.getNewsId());
				if (CollectionUtils.isNotEmpty(newsAlertsReport)) {
					StringBuilder users = new StringBuilder(String.valueOf(newsAlertsReport.get(0).getUserName()));
					StringBuilder roles = new StringBuilder(String.valueOf(newsAlertsReport.get(0).getRoleDesc()));
					StringBuilder banks = new StringBuilder(
							String.valueOf(newsAlertsReport.get(0).getParticipantName()));

					for (int i = 0; i < newsAlertsReport.size(); i++) {
						appendAsPerSize(newsAlertsReport, users, roles, banks, i);
					}
					newsDTO.setEditUserList(users.toString());
					newsDTO.setEditRoleList(roles.toString());
					newsDTO.setEditBankList(banks.toString());

				}
			}
			newsAlertsService.addDefaultListData(model);
			return getView(model, ADD_EDIT_NEWS_ALERTS);
		} else {
			Map<Integer, Object> newsMap = newsAlertsService.getNewsAlerts(searchCriteriaDTO);
			newsAlertsDTO = (NewsAlertsDTO) newsMap.get(1);
			newsAlertsDTO.setReqType(reqType);
			model.addAttribute(CommonConstants.REQ_TYPE, reqType);
			model.addAttribute(BaseCommonConstants.REQUEST_STATE, newsAlertsDTO.getRequestState());
			model.addAttribute(BaseCommonConstants.NEWS_ALERT_DTO, newsAlertsDTO);
			model.addAttribute(DISTRIBUTIONL_LIST,
					newsAlertsService.fetchDistributionDetails(newsAlertsDTO.getNewsId()));
			checkForEdit(reqType, model);
			return getView(model, VIEW_NEWS_ALERTS);
		}

	}

	private void appendAsPerSize(List<NewsAlertsDTO> newsAlertsReport, StringBuilder users, StringBuilder roles,
			StringBuilder banks, int i) {
		if (!"-1".equals(newsAlertsReport.get(i).getUserName())) {
			users.append(",");
			users.append(newsAlertsReport.get(i).getUserName());
			roles.append(",");
			roles.append(newsAlertsReport.get(i).getRoleDesc());
			banks.append(",");
			banks.append(newsAlertsReport.get(i).getParticipantId());

		}
	}

	private void checkForEdit(String reqType, Model model) {
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())
				&& V.equalsIgnoreCase(reqType)) {
			model.addAttribute(SHOW_EDIT, CommonConstants.YES_FLAG);
		}
	}

	private String handleReqType(Integer newsId, String reqType, String refNumber, String screenName, Model model,
			SearchCriteriaDTO searchCriteriaDTO) {
		reqType = TransactValidator.checkForString(reqType) ? reqType : "";

		if (reqType == null) {
			throw new SettleNxtException("Empty Req Type", "");
		}
		if (!StringUtils.isEmpty(screenName) && screenName.equalsIgnoreCase(BaseCommonConstants.SAVED)) {

			model.addAttribute(BaseCommonConstants.SCREEN_NAME, BaseCommonConstants.SAVED);

		}
		if (P.equals(reqType)) {
			searchCriteriaDTO.setNewsId(newsId);
			searchCriteriaDTO.setReferenceNumber(refNumber);
			searchCriteriaDTO.setSearchType(BaseCommonConstants.TRANSACT_SEARCH_FROM_TEMP_REPOSITORY);
			String methodName = APPROVE_NEWS_ALERTS;
			model.addAttribute(APPROVE_REJECT_BUTTON, methodName);
		} else {
			searchCriteriaDTO.setReferenceNumber(refNumber);
			searchCriteriaDTO.setSearchType(BaseCommonConstants.TRANSACT_SEARCH_FROM_FINAL_REPOSITORY);
		}
		return reqType;
	}

	@PostMapping("/approveNewsAlerts")
	@PreAuthorize("hasAnyAuthority('Approve Reject News And Alerts')")
	public String approveNewsAlerts(@RequestParam(name = "newsId", required = false) Integer newsId,
			@RequestParam(CommonConstants.STATUS) String status,
			@RequestParam(name = "referenceNumber", required = false) String referenceNumber,
			@RequestParam("remarks") String remarks, Model model, HttpServletRequest request, HttpSession session) {

		try {
			if (status != null) {
				NewsDTO newsDTO = new NewsDTO();

				newsDTO.setNewsId(newsId);
				newsDTO.setReferenceNumber(referenceNumber);
				newsDTO.setRemarks(remarks);
				newsDTO.setStatus(status);

				newsAlertsService.approveNewsAlerts(newsDTO, model);
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_NEWS_ALERTS_INFO, ex);
		}

		return getView(model, VIEW_NEWS_ALERTS_INFO);
	}

	@PostMapping("/getUserList")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public ResponseEntity<Object> getUserList(Model model, @RequestBody UserDTO userDto) {

		String[] bankNameArr = userDto.getBankList();// participantIdarr

		String[] roleListArr = userDto.getRoleList();

		List<UserDTO> userList = userService.getUserList(bankNameArr, roleListArr);
		model.addAttribute(USER_LIST, userList);
		return new ResponseEntity<>(userList, HttpStatus.OK);
	}

	public void addDefaultListData(Model model) {
		model.addAttribute(PERIOD_TYPE_LIST, lookUpService.getLookupData(CommonConstants.PERIOD_TYPE));
		model.addAttribute(WEEK_LIST, lookUpService.getLookupData(CommonConstants.WEEK_LIST));
		model.addAttribute(MONTH_LIST, lookUpService.getLookupData(CommonConstants.MONTH_LIST));
		model.addAttribute(BANK_NAMES, userService.getParticipantList());
		model.addAttribute(ROLE_LIST, userService.getRoleTypes());

	}

	@PostMapping("/discardNewsAndAlerts")
	@PreAuthorize("hasAuthority('Edit News And Alerts')")
	public String discardNewsAndAlerts(@RequestParam("referenceNumber") String referenceNumber, Model model) {
		NewsAlertsDTO newsAlertsDTO = new NewsAlertsDTO();
		try {
			newsAlertsDTO = newsAlertsService.discardNewsAndAlerts(referenceNumber);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_NEWS_ALERTS, ex);

		}

		model.addAttribute(BaseCommonConstants.NEWS_ALERT_DTO, newsAlertsDTO);
		model.addAttribute(SHOW, CommonConstants.EDIT_NEWS);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("newsAlert.discardSuccess.msg"));
		return getView(model, VIEW_NEWS_ALERTS);
	}

	@PostMapping("/deleteNewsAlerts")
	@PreAuthorize("hasAuthority('Delete News And Alerts')")
	public String deleteNewsAlerts(@RequestParam("refNumber") String refNumber,
			@RequestParam(name = BaseCommonConstants.SCREEN_NAME, required = false) String screenName, Model model) {

		NewsDTO newsDto = new NewsDTO();

		try {

			newsDto = newsAlertsService.getNewsAlerts(refNumber);
			newsAlertsService.deleteNewsAlert(newsDto);
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					SUCCESS_MSG_DELETE_NEWS_ALERT);

			if (screenName.equalsIgnoreCase(BaseCommonConstants.SAVED)) {
				model.addAttribute(SAVED_SCREEN, YES_CAPITALIZE);
				model.addAttribute(SHOW_SAVED_NEWS, CommonConstants.YES);
			} else {
				model.addAttribute(SHOW_ACTIVE, CommonConstants.YES);
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_NEWS_ALERTS, ex);
			model.addAttribute(ERROR_STATUS, FAILED_MSG_DELETE_NEWS_ALERT);

		}
		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, CommonConstants.YES);
		model.addAttribute(BaseCommonConstants.NEWS_LIST, BaseCommonConstants.NEWS_LIST);

		model.addAttribute(BaseCommonConstants.MAKER, CommonConstants.YES);
		return getView(model, SHOW_NEWS_ALERTS);
	}

	@PostMapping("/showNewsAndAlertReports")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public String getNewsAndAlertReports(Model model) {
		NewsAlertsDTO newsAndAlertInfoDTO = new NewsAlertsDTO();

		model.addAttribute(CommonConstants.NEWS_AND_ALERTS_INFO, newsAndAlertInfoDTO);
		return getView(model, NEWS_ALERTS_REPORT);

	}

	@PostMapping("/getCountNewsAndAlertReports")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public ResponseEntity<Object> getCountNewsAndAlertReports(
			@RequestParam(value = "fromDateStr", required = false) String fromDateStr,
			@RequestParam(value = "toDateStr", required = false) String toDateStr, Model model) {
		JsonObject jsonResponse = new JsonObject();
		try {
			if (!newsAlertsService.fetchCountNewsAlerts(fromDateStr, toDateStr)) {
				jsonResponse.addProperty(CommonConstants.STATUS, CommonConstants.TRANSACT_FAIL);
				return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
			}

		} catch (Exception e) {
			jsonResponse.addProperty(CommonConstants.STATUS, CommonConstants.TRANSACT_FAIL);
			handleErrorCodeAndForward(model, NEWS_ALERTS_REPORT, e);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
		jsonResponse.addProperty(CommonConstants.STATUS, CommonConstants.TRANSACT_SUCCESS);
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/newsAlertsNoRecordsError")
	@PreAuthorize("hasAuthority('View News And Alerts')")
	public String noRecordsErrorNewsAndAlertReports(
			@RequestParam(value = "fromDateStr", required = false) String fromDateStr,
			@RequestParam(value = "toDateStr", required = false) String toDateStr,
			@ModelAttribute("newsAndAlertInfoDTO") NewsAlertsDTO newsAndAlertInfoDTO, Model model) {

		model.addAttribute(ERROR_STATUS, ERROR_MSG_NO_RECORDS_FOUND);
		return getView(model, NEWS_ALERTS_REPORT);

	}

	@PostMapping("/downloadNewsAlertsBirtReport")
	@PreAuthorize("hasAuthority('News And Alert Reports')")
	public ResponseEntity<Object> searchNewsAndAlertsReportsDetails(
			@RequestParam(value = "fromDateStr", required = false) String fromDateStr,
			@RequestParam(value = "toDateStr", required = false) String toDateStr,
			@ModelAttribute("newsAndAlertInfoDTO") NewsAlertsDTO newsAndAlertInfoDTO, Model model) throws SettleNxtException, BirtException, IOException {
		NewsAlertsDTO newsDTO = newsAlertsService.newsAlertBirtReportGeneration(fromDateStr, toDateStr);
		if (newsDTO.getFilePath().lastIndexOf('.') <= newsDTO.getFilePath().lastIndexOf('/')){
			throw new SettleNxtException("Error in File Path", "");
		}
		Path path = Paths.get(newsDTO.getFilePath()).normalize();

		Resource resource = null;
		try {
			resource = new UrlResource(path.toUri());
		} catch (MalformedURLException e) {
			throw new SettleNxtException("Exception in download Report", "", e);

		}
		return ResponseEntity.ok().contentType(MediaType.parseMediaType("application/octet-stream"))
				.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
				.body(resource);

	}

	public Map<String, List<String>> getDistributionList(int newsId) {
		List<NewsAlertsDTO> newsAlertsReport = newsAlertsService.fetchDistributionDetails(newsId);
		Map<String, List<String>> distMap = new HashMap<>();
		List<String> roleDescList = newsAlertsReport.stream().map(NewsAlertsDTO::getRoleDesc)
				.collect(Collectors.toList());
		List<String> bankNameList = newsAlertsReport.stream().map(NewsAlertsDTO::getParticipantId)
				.collect(Collectors.toList());

		List<String> userList = newsAlertsReport.stream().map(NewsAlertsDTO::getUserName).collect(Collectors.toList());
		distMap.put(ROLE, roleDescList);
		distMap.put(BANK, bankNameList);
		distMap.put(USER, userList);
		return distMap;
	}

	@PostMapping("/approveNewsAlertsBulk")
	@PreAuthorize("hasAnyAuthority('Approve Reject News And Alerts')")
	public String approveNewsAlertsBulk(@RequestParam(CommonConstants.STATUS) String status,
			@RequestParam("bulkApprovalReferenceNoList") String bulkApprovalReferenceNoList, Model model) {

		try {
			if (status != null) {

				String remarks = "";

				if (status.equals(CommonConstants.STATUS_APPROVE)) {
					remarks = CommonConstants.BULK_APPROVE;
				} else if (status.equals(CommonConstants.STATUS_REJECT)) {
					remarks = CommonConstants.BULK_REJECT;
				}
				NewsDTO newsDTO = new NewsDTO();
				newsDTO.setRemarks(remarks);
				newsDTO.setStatus(status);

				String successStatus = newsAlertsService.approveNewsAlertsBulk(bulkApprovalReferenceNoList, newsDTO,
						model);
				if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(successStatus) && status.equalsIgnoreCase(CommonConstants.STATUS_APPROVE)) {
					model.addAttribute(CommonConstants.SUCCESS_STATUS, SUCCESS_MSG_APPROVED);
				} else if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(successStatus)
						&& status.equalsIgnoreCase(CommonConstants.STATUS_REJECT)) {
					model.addAttribute(CommonConstants.SUCCESS_STATUS, SUCCESS_MSG_REJECTED);
				}

			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_NEWS_ALERTS, ex);
		}
		List<NewsAlertsDTO> newsListPending = null;
		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
		newsListPending = prepareNewsPendingList(searchCriteriaDTO);
		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, CommonConstants.YES);
		model.addAttribute(BaseCommonConstants.TNEWS_LIST, BaseCommonConstants.TNEWS_LIST);
		model.addAttribute(APPROVAL_ACTIVE, CommonConstants.YES);
		model.addAttribute(NEWS_LIST_PENDING, newsListPending);

		model.addAttribute(BaseCommonConstants.MAKER, CommonConstants.YES);
		validateMakerCheckerForCheckbox(model);
		return getView(model, SHOW_NEWS_ALERTS);

	}

}
