package org.npci.settlenxt.adminportal.service;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.MccRepository;
import org.npci.settlenxt.portal.common.dto.MccDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Throwable.class)
public class MccServiceImpl implements MccService {

	@Autowired
	MccRepository mccRepository;

	@Autowired
	SessionDTO sessionDTO;

	@Override
	public List<MccDTO> getMccList() {
		return mccRepository.getMccListMain();
	}

	@Override
	public List<MccDTO> getPendingMccConfigs() {
		return mccRepository.getPendingMccConfigs();
	}

	@Override
	public MccDTO addMccConfig(MccDTO mccDTO) {
		mccDTO.setCreatedOn(new Date());
		mccDTO.setCreatedBy(sessionDTO.getUserName());
		mccDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		mccDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
		mccDTO.setMccId(mccRepository.fetchIdFromMccIdSequence());

		int returnValue = checkDuplicateData(mccDTO);
		if (returnValue > 0) {
			throw new SettleNxtApplicationException("ERR_MCC_EXISTS1", "Duplicate Record");
		}

		mccRepository.addMccConfig(mccDTO);

		return mccRepository.getMccStgData(mccDTO.getMccId());

	}

	@Override
	public MccDTO getMccConfigById(Integer mccId) {
		return mccRepository.getMccConfigByIdMain(mccId);
	}

	@Override
	public MccDTO getMccConfigStgById(Integer mccId) {
		return mccRepository.getPendingMccConfigById(mccId);
	}

	@Override
	public MccDTO updateMccConfig(MccDTO mccDTO) {

		mccDTO.setLastUpdatedBy(sessionDTO.getUserName());
		mccDTO.setLastUpdatedOn(new Date());

		mccDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		mccDTO.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
		mccRepository.updateMccConfigEdit(mccDTO);
		return mccRepository.getPendingMccConfigById(mccDTO.getMccId());

	}

	@Override
	public MccDTO getPendingMccConfigById(Integer mccId) {
		return mccRepository.getPendingMccConfigById(mccId);
	}

	@Override
	public MccDTO discardMcc(Integer mccId) {
		MccDTO mccDTO = mccRepository.getMccStgData(mccId);
		MccDTO mccDTOMain = mccRepository.getMccMainData(mccId);

		if (mccDTOMain != null) {
			mccDTOMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			mccDTOMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			mccRepository.updateMccConfigDiscard(mccDTOMain);
		} else {
			mccRepository.deleteDiscardedMccEntry(mccId);
		}

		return mccDTO;
	}

	@Override
	public MccDTO updateApproveOrRejectMcc(Integer mccId, String status, String remarks) {
		MccDTO mccDto = getPendingMccConfigById(mccId);
		mccDto.setRequestState(status);
		mccDto.setCheckerComments(remarks);

		if ("Rejected".equals(status)) {
			mccDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
		}
		if ("Approved".equals(status)) {
			mccDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			mccDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			mccDto.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
		} else {

			mccDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
			mccDto.setRequestState(CommonConstants.REQUEST_STATE_REJECTED);
		}

		if (mccDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {

			try {
				updateApprovedMcc(mccDto);

			} catch (Exception e) {

				throw new SettleNxtApplicationException("ERR_MCC", "Unable to Add MCC", e);
			}
		}
		
		
		mccDto.setLastUpdatedOn(new Date());
		mccDto.setLastUpdatedBy(sessionDTO.getUserName());
		
		mccRepository.updateMccStgState(mccDto);
		return mccDto;

	}

	private void updateApprovedMcc(MccDTO mccDTO) {
		MccDTO mccDTODB = mccRepository.getApprovedMccConfigById(mccDTO.getMccId());

		mccDTO.setCreatedOn(new Date());

		if (ObjectUtils.isEmpty(mccDTODB)) {
			mccRepository.saveMccConfig(mccDTO);
		} else {
			mccDTO.setLastUpdatedOn(new Date());
			mccDTO.setLastUpdatedBy(sessionDTO.getUserName());
			mccRepository.updateMccMain(mccDTO);
		}

		mccDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

	}

	// for bulk checker
	@Override
	public String approveOrRejectMccBulk(String bulkApprovalReferenceNoList, String status, String remarks) {
		String[] referenceNoArr = bulkApprovalReferenceNoList.split("\\|");
		int mccId = 0;

		for (String refNum:referenceNoArr) {

			try {
				if (!StringUtils.isEmpty(refNum)) {
					mccId = Integer.parseInt(refNum);
					MccDTO mccDto = getPendingMccConfigById(mccId);
					if (mccDto == null) {

						throw new SettleNxtException("Exception occurred with Ref No" + refNum, "");
					}
					approveOrRejectBulk(status, remarks, mccId, mccDto);
				}

			} catch (Exception ex) {

				throw new SettleNxtException("Exception for Ref no" + refNum, "", ex);

			}
		}

		return CommonConstants.YES_FLAG;
	}

	private void approveOrRejectBulk(String status, String remarks, int mccId, MccDTO mccDto) {
		if (mccDto != null) {
			mccDto.setRequestState(status);
			mccDto.setCheckerComments(remarks);

			mccDto.setCheckerComments(remarks);
			mccDto.setLastOperation(mccDto.getLastOperation());
			Date lt = new Date();
			mccDto.setLastUpdatedOn(lt);
			mccDto.setLastUpdatedBy(sessionDTO.getUserName());
			mccDto.setCreatedOn(new Date());

			if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
				mccDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
				MccDTO mccDtoMain = mccRepository.getMccMainData(mccId);
				if (mccDtoMain != null) {
					mccRepository.updateMccMain(mccDto);
				} else {
					mccRepository.saveMccConfig(mccDto);
				}
			}
			if (CommonConstants.REQUEST_STATE_REJECTED.equals(status)) {
				mccDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
				}
			
					
			mccRepository.updateMccStgState(mccDto);

		}
	}

	@Override
	public int checkDuplicateData(MccDTO mccDTO) {

		return mccRepository.validateDuplicateCheck(mccDTO);
	}
}
