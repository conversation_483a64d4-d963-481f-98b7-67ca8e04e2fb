package org.npci.settlenxt.adminportal.model;

import lombok.Data;

@Data
public class RecordError {
	private int errorNo;
	private String errorDescription;
	private String name;
	private String value;
	private String xml;
	private String binNo;
	private String settledMonth;
	

	public RecordError(int errorNo, String errorDescription, String name,
			String value, String xml,String binNo,String settledMonth) {
		super();
		this.errorNo = errorNo;
		this.errorDescription = errorDescription;
		this.name = name;
		this.value = value;
		this.binNo = binNo;
		this.settledMonth = settledMonth;
		
		this.xml = xml.replace("&amp;", "&");
	}
	
}
