package org.npci.settlenxt.adminportal.service;

import java.io.UnsupportedEncodingException;
import java.util.List;

import org.npci.settlenxt.adminportal.dto.CappingAmountDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;

public interface CappingAmountService {

	 List<CappingAmountDTO> getApprovedCappingAmount();

	 List<CappingAmountDTO> getPendingForApprovalCappingAmountList();

	 List<CappingAmountDTO> getFunctionCodeList();

	 CappingAmountDTO addEditCapping(CappingAmountDTO cappingAmountDto);

	 CappingAmountDTO getCappingAmount(String actionCode, String mccGroup, String binCardBrandId,
			String binCardTypeId, String fieldName, String relOperator, String fieldValue)
			throws UnsupportedEncodingException;

	 CappingAmountDTO approveOrRejectCappingAmount(String capId, String status, String remarks);

	

	 int updateCappingAmount(CappingAmountDTO cappingAmountDto);

	 CappingAmountDTO discardCappingAmount(String actionCode, String mccGroup, String binCardBrandId,
			String binCardTypeId, String fieldName, String relOperator, String fieldValue)
			throws UnsupportedEncodingException;

	 CappingAmountDTO getCappingInfoFromMainEdit(String cappingId);

	 CappingAmountDTO updateApproveOrRejectBulk(String[] idArray, String status, String remarks) throws SettleNxtException;

	 List<CodeValueDTO> getActionCodeList();

	 List<CodeValueDTO> getMccGroupList();

	 CappingAmountDTO getCappingInfoFromStgEdit(String actionCode, String mccGroup, String binCardBrandId,
			String binCardTypeId, String fieldName, String relOperator, String fieldValue)
			throws UnsupportedEncodingException;

	 boolean checkDuplicateRecords(String actionCode, String mccGroup, String binCardBrandId,
			String binCardTypeId, String fieldName, String fieldValue, String relOperator);
}
