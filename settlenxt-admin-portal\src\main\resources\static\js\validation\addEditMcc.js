$(document).ready(function () {
	$("#errmccGroup").hide();
    $("#errmccCode").hide();
    $("#errmccDesc").hide();
   

    $('#mccGroup').on('keyup keypress blur change', function () {
        validateField('mccGroup', true, "SelectionBox",0,false);
    });
    $('#mccCode').on('keyup keypress blur change', function () {
        validateField('mccCode', true, "Integer", 4, true);
    });
    $('#mccDesc').on('keyup keypress blur change', function () {
        validateField('mccDesc', true, "Alphanumeric", 10, false);
    });
    
 	disableSave();
 	$("#mccGroup").on('keyup keypress change', function () {
        unableSave();
    });
	$("#mccCode").on('keyup keypress change', function () {
        unableSave();
    });
 	$("#mccDesc").on('keyup keypress change', function () {
        unableSave();
    });
 	$("#status").on('keyup keypress change', function () {
        unableSave();
    });
 	
 
});

function disableSave()
{
if (typeof bSubmit != "undefined") {
	document.getElementById("bSubmit").disabled = true;
}
if (typeof bUpdate != "undefined") {
	document.getElementById("bUpdate").disabled = true;
}
}

function unableSave()
{
if (typeof bSubmit != "undefined") {
	document.getElementById("bSubmit").disabled = false;
}
if (typeof bUpdate != "undefined") {
	document.getElementById("bUpdate").disabled = false;
}
}

function resetAction() {
document.getElementById("addMccConfig").reset();
$("#errmccGroup").find('.error').html('');
$("#errmccCode").find('.error').html('');
$("#errmccDesc").find('.error').html('');
}

window.history.forward();
function noBack() {
    window.history.forward();
}

function display() {
    $(".appRejMust").hide();

}

function userAction(_type, action) {
	var url = action;
	var mccId = document.getElementById("mccId").value;
	var data = "mccId," + mccId  + ",status,"
		+ status;
	postData(url, data);
} 

function addOrUpdateMcc(id) {
	var url='';
	
	if (id == 'A') {
		url = '/addMccConfig';

	} else if (id == 'E') {
		url = '/updateMccConfig';
	}
    var isValid = true;

    
    if (!validateField('mccGroup', true, "SelectionBox",0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('mccCode', true, "Integer", 4, true) && isValid) {
        isValid = false;
    }
    if (!validateField('mccDesc', true, "Alphanumeric", 10, false) && isValid) {
        isValid = false;
    }
    
   
    if (isValid) {
    	var mccGroupName = $("#mccGroup option:selected").text();
     
        var data = "mccId," + $('#mccId').val()+ ",mccGroup," + $('#mccGroup').val()+ ",mccGroupName," + mccGroupName+ ",mccCode," + $('#mccCode').val()  + ",mccDesc," + $('#mccDesc').val() + ",status," + $('#status').val() +",parentPage," + $("#hparentPage").val();
       
        postData(url, data);
    }
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength) {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if (isMandatory && fieldValue.trim() == "") {
        isValid = false;
    }
    isValid = checkNaN(fieldType, fieldValue, isValid);
    if (fieldType == "Number" && fieldValue < 0) {
    	isValid = false;
    }
    if (fieldType == "Integer") {
        var regEx =/^\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldValue == "SELECT") {
        isValid = false;
    }
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
    	if(mccValidationMessages[fieldId]){
    		$("#err" + fieldId).find('.error').html(mccValidationMessages[fieldId]);
    	}
        $("#err" + fieldId).show();
    }
    
    return isValid;
}


function checkNaN(fieldType, fieldValue, isValid) {
	if (fieldType == "Number" && isNaN(fieldValue)) {
		isValid = false;
	}
	return isValid;
}

function postDiscardMccAction(action) {
	var url = action;
	var mccId = document.getElementById("mccId").value;
	var data = "mccId," + mccId  ;
	postData(url, data);
	
}


