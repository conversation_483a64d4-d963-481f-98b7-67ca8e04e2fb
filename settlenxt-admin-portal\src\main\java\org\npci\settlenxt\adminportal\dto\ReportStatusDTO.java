package org.npci.settlenxt.adminportal.dto;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class ReportStatusDTO {

	private String guid;
	private String productCode;
	private String reportName;
	private String reportType;
	private String status;
	private String cycleDate;
	private String cycleNumber;
	private String regenFlag;
	private String engineCode;
	private String accumCode;
	private String pgpStatus;
	private String parentFileId;
	private String createdTs;
	private String updatedTs;
	private String genStartTs;
	private String genEndTs;
	private String accumStartTs;
	private String accumEndTs;
	private String pullStartTs;
	private String pullEndTs;
	private String participantId;
	private String count;

}