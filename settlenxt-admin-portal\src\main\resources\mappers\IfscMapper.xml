<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.IFSCRepository">

	<select id="getApprovedIFSCList" resultType="IFSCDTO">
	select ifsc_code as ifscCode, request_state as requestState, bank_code as bankCode,nfs_code as nfsCode,
	ifsc_description as ifscDescription,status as status, CHECKER_COMMENTS as checkerComments, 
	CREATED_BY as createdBy from ifsc_stg where request_state='A'
	</select>
	
	<select id="getApprovedIFSCListMain" resultType="IFSCDTO">
	select i.ifsc_code as ifscCode, stg.request_state as requestState, i.bank_code as bankCode,i.nfs_code as nfsCode, 
	i.ifsc_description as ifscDescription,i.status as status, stg.CHECKER_COMMENTS as checkerComments, 
	i.CREATED_BY as createdBy
	from ifsc i 
	inner join ifsc_stg stg on i.ifsc_code=stg.ifsc_code
	ORDER BY i.last_updated_on desc
	</select>
	
	<select id="getPendingForAppovalIFSCList" resultType="IFSCDTO">
	select ifsc_code as ifscCode,request_state as requestState, bank_code as bankCode,nfs_code as nfsCode,
	ifsc_description as ifscDescription, status as status, CHECKER_COMMENTS as checkerComments, 
	CREATED_BY as createdBy from ifsc_stg where request_state in ('P','R')
	</select>
	
	<select id="getIFSC" resultType="IFSCDTO">
	SELECT IFSC_CODE as ifscCode, IFSC_DESCRIPTION as ifscDescription, BANK_CODE as bankCode, NFS_CODE as nfsCode, SAVINGS_ACCT_ID as savingsAccId, CURRENT_ACCT_ID as currAccId, RTGS_ACCT_ID as rtgsAccId,
	STATUS, REQUEST_STATE as requestState , MAKER_COMMENTS as makerComments, CHECKER_COMMENTS as checkerComments, CREATED_BY as createdBy, CREATED_ON as createdOn, LAST_UPDATED_BY as lastUpdatedBy, LAST_UPDATED_ON as lastUpdatedOn,
	LAST_OPERATION as lastOperation FROM IFSC_STG WHERE IFSC_CODE=#{ifscCode}
	</select>
	
	<select id="getIFSCDataMain" resultType="IFSCDTO">
	SELECT i.IFSC_CODE as ifscCode, i.IFSC_DESCRIPTION as ifscDescription, i.BANK_CODE as bankCode, i.NFS_CODE as nfsCode, i.SAVINGS_ACCT_ID as savingsAccId, i.CURRENT_ACCT_ID as currAccId,  
	i.RTGS_ACCT_ID as rtgsAccId, i.STATUS , stg.REQUEST_STATE as requestState, stg.MAKER_COMMENTS as makerComments, stg.CHECKER_COMMENTS as checkerComments, i.CREATED_BY as createdBy, 
	i.CREATED_ON as createdOn, i.LAST_UPDATED_BY as lastUpdatedBy, i.LAST_UPDATED_ON as lastUpdatedOn, stg.LAST_OPERATION as lastOperation
	FROM IFSC i 
	inner join ifsc_stg stg on i.ifsc_code=stg.ifsc_code WHERE i.IFSC_CODE=#{ifscCode}
	</select>
	
	<select id="getIFSCByBankCode" resultType="IFSCDTO">
	SELECT IFSC_CODE as ifscCode, IFSC_DESCRIPTION as ifscDescription, BANK_CODE as bankCode FROM IFSC_STG WHERE BANK_CODE=#{bankCode}
	</select>
	
	<select id="getIFSCMain" resultType="IFSCDTO">
	SELECT IFSC_CODE as ifscCode, IFSC_DESCRIPTION as ifscDescription, BANK_CODE as bankCode, NFS_CODE as nfsCode, 
	SAVINGS_ACCT_ID as savingsAccId, CURRENT_ACCT_ID as currAccId, RTGS_ACCT_ID as rtgsAccId,
	STATUS, CREATED_BY as createdBy, CREATED_ON as createdOn, LAST_UPDATED_BY as lastUpdatedBy, LAST_UPDATED_ON as lastUpdatedOn
 	FROM IFSC WHERE IFSC_CODE=#{ifscCode}
	</select>
	
	<insert id="saveIFSC" >
	INSERT INTO  ifsc_stg(ifsc_code, ifsc_description, bank_code, nfs_code, savings_acct_id, current_acct_id, 
	rtgs_acct_id, status, request_state, created_by, created_on, last_updated_by, last_updated_on, last_operation)	
	VALUES (#{ifscCode}, #{ifscDescription}, #{bankCode}, #{nfsCode}, #{savingsAccId}, #{currAccId}, 
	#{rtgsAccId}, #{status}, #{requestState}, #{createdBy}, #{createdOn}, #{lastUpdatedBy}, #{lastUpdatedOn}, #{lastOperation})
	</insert>
	
	<insert id="saveIFSCMain">
	INSERT INTO  ifsc(ifsc_code, ifsc_description, bank_code, nfs_code, savings_acct_id, current_acct_id,
	rtgs_acct_id, status, created_by, created_on, last_updated_by, last_updated_on)	
	VALUES (#{ifscCode}, #{ifscDescription}, #{bankCode}, #{nfsCode}, #{savingsAccId}, #{currAccId},
	#{rtgsAccId}, #{status}, #{createdBy}, #{createdOn}, #{lastUpdatedBy}, #{lastUpdatedOn})
	</insert>
	<update id="updateIFSC" >
	UPDATE  IFSC_STG 	SET  IFSC_DESCRIPTION=#{ifscDescription}, BANK_CODE=#{bankCode}, NFS_CODE=#{nfsCode},
	SAVINGS_ACCT_ID=#{savingsAccId}, CURRENT_ACCT_ID=#{currAccId}, RTGS_ACCT_ID=#{rtgsAccId}, STATUS=#{status}, 
	REQUEST_STATE=#{requestState}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON=#{lastUpdatedOn}, 
	CHECKER_COMMENTS='', LAST_OPERATION=#{lastOperation} WHERE IFSC_CODE= #{ifscCode}
	</update>
	<update id="updateIFSCMain">
	UPDATE  IFSC SET  IFSC_DESCRIPTION=#{ifscDescription}, BANK_CODE=#{bankCode}, NFS_CODE=#{nfsCode}, 
	SAVINGS_ACCT_ID=#{savingsAccId}, CURRENT_ACCT_ID=#{currAccId}, RTGS_ACCT_ID=#{rtgsAccId}, STATUS=#{status}, 
	LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON=#{lastUpdatedOn}  WHERE IFSC_CODE= #{ifscCode}
	</update>
	<update id="updateIFSCRequestState">
	UPDATE IFSC_STG SET  REQUEST_STATE=#{requestState}, CHECKER_COMMENTS=#{checkerComments},LAST_OPERATION=#{lastOperation},
	LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON=#{lastUpdatedOn}	WHERE IFSC_CODE= #{ifscCode}
	</update>
	
	<delete id="deleteDiscardedEntry">
	DELETE FROM IFSC_STG WHERE IFSC_CODE= #{ifscCode}
	</delete>
	
</mapper>	
	