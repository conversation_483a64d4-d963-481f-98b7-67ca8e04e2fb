package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class SysParamsDTO {
	private String sysType;
	private String sysKey;
	private String sysValue;
	private String desc;
	private String addProp;
	private String createdBy;
	private String lastUpdatedBy;
	private Date createdOn;
	private Date lastUpdatedOn;
	private String checkersComment;
	private String requestState;
	private String status;
	private String lastOperation;
	private String statusCode;


}
