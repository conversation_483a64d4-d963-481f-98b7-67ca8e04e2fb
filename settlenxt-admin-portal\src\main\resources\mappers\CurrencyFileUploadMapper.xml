<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.CurrencyFileUploadRepository">

	<select id="searchFileUploadList"
		resultType="CurrencyFileUploadDTO">
			SELECT
			fu.file_id as fileId,
			fu.network, fu.forex_id as forexId,
			fu.file_name as fileName,
			fu.file_path as file_path,
			fu.PARTICIPANT_ID as participantId,
			fu.total_no_record as totalNoRecord,
			fu.STATUS as status,
			fu.created_on as createdOn,
			fu.CREATED_BY as createdBy,
			fu.LAST_UPDATED_BY as lastUpdatedBy
			FROM currency_file_upload fu
			WHERE
			participant_id = #{participantId} AND status !='D'
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
			<if test="fromDate != null">
				AND created_on &gt;= #{fromDate}
			</if>
	
			<if test="toDate != null">
				AND created_on &lt;= #{toDate}
			</if>
			<if test="fileName != null and fileName != ''">
				<bind name="pattern" value="'%' + fileName + '%'" />
					AND file_name LIKE #{pattern}
			</if>
			ORDER BY fu.file_id DESC
	</select>
	
	<select id="checkDuplicateFileName" resultType="int">
		SELECT count(*) FROM currency_file_upload WHERE file_name = #{fileName} and status!='D' and network = #{network}
	</select>
	
	
	<insert id="insertFileUpload" parameterType="CurrencyFileUploadDTO"
		useGeneratedKeys="true" keyProperty="fileId"
		keyColumn="file_id">
		INSERT INTO currency_file_upload
		(network,forex_id,file_name, file_path,participant_id,total_no_record,status,
		 created_by, last_updated_by, portal, site, instance) 
		VALUES(#{network},#{forexId},#{fileName},#{filePath},#{participantId}, #{totalNoRecord}, #{status},
		#{createdBy},#{lastUpdatedBy},#{portal}, #{site}, #{instance})
		RETURNING file_id
	</insert>
	
	
	<select id="getFilesByFileIds" resultType="CurrencyFileUploadDTO">
		SELECT fu.file_id as fileId,
		fu.network,fu.forex_id as forexId,
		fu.file_name  as fileName,
		fu.file_path  as filePath,
		fu.PARTICIPANT_ID as participantId,
		fu.total_no_record  as totalNoRecord,
		fu.STATUS as status,
		fu.CREATED_BY as createdBy,
		fu.LAST_UPDATED_BY as lastUpdatedBy
		FROM currency_file_upload fu WHERE
		fu.file_id  IN (${stageFileIds})
	</select>
	
	<select id="getFileStatusById" resultType="String">
		SELECT STATUS from currency_file_upload WHERE file_id  = #{fileId}
	</select>
	
	<update id="updateFileUploadInfo">
		UPDATE currency_file_upload SET
		file_path=#{filePath},
		total_no_record  =#{totalNoRecord},
		LAST_UPDATED_ON=#{lastUpdatedOn}
		WHERE
		file_id  = #{fileId}
	</update>
	
	<select id="getCurrencyFileDetails" resultType="CurrencyFileUploadDTO">
        SELECT 
            file_id AS fileId,
            network AS network,
            forex_id AS forexId,
            file_name AS fileName,
            file_path AS filePath,
            participant_id AS participantId,
            total_no_record AS totalNoRecord,
            status as status,
            created_by AS createdBy,
            created_on AS createdOn,
            last_updated_by AS lastUpdatedBy,
            last_updated_on AS lastUpdatedOn,
            portal as portal,
            site as site,
            instance as site,
            fp_instance_name AS fpInstanceName,
            reprocess_kafka_topic AS reprocessKafkaTopic
        FROM currency_file_upload
        WHERE file_id = #{fileId}
    </select>
	
	 <select id="getCurrencyRateFieldModelList" resultType="CurrencyRateFieldModel">
	   SELECT
            id AS currencyRateId,
            file_id AS fileId,
            scheme_name AS schemeName,
            forex_id AS forexId,
            processing_date AS processingDate,
            sell_currency AS fromCurrency,
            buy_currency AS toCurrency,
            rate AS centralRate,
            settlement_date AS validDate,
            file_uploaded_date AS fileUploadedDate,
            file_name AS fileName,
            status as status,
            error_code AS errorCode
        FROM currency_file_upload_data
        WHERE file_id = #{fileId}
    </select>
    
    <delete id="discardFileReject">
		DELETE FROM currency_file_upload_data WHERE file_id = #{fileId}
	</delete>
	
	<update id="updateCurrencyFileUploadStatusD">
		UPDATE currency_file_upload SET
		status=#{status},
		LAST_UPDATED_ON=#{lastUpdatedOn}
		WHERE
		file_id  = #{fileId}
	</update>
</mapper>