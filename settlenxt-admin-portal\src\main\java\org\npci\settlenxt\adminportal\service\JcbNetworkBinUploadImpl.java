package org.npci.settlenxt.adminportal.service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.cache.CountryCache;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.MemberRepository;
import org.npci.settlenxt.common.cache.BaseLookupDTOCache;
import org.npci.settlenxt.common.cache.CacheReloaderConstants;
import org.npci.settlenxt.portal.common.dto.BinDetailsDTO;
import org.npci.settlenxt.portal.common.service.BaseCacheReloaderService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class JcbNetworkBinUploadImpl implements NetWorkBinUploadInterface{
	
	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	MemberRepository memberRepository;
	@Autowired
	CountryCache countryCache;
	@Autowired
	private BaseCacheReloaderService baseCacheReloaderSvc;
	@Autowired
	private BaseLookupDTOCache lookupCache;
	
	private static final String NETWORK_JCB = "NETWORK_JCB";

	
	private static final String JCB_OPR_SIGN_INSERT = "01";
	private static final String JCB_OPR_SIGN_DELETE = "02";
	private static final String JCB_OPR_SIGN_UPDATE = "03";
	private static final String UNIQUE_CONSTRAINT_ERROR="duplicate key value violates unique constraint";
	
	@Override
	public String processNetworkBinFile(List<MultipartFile> files) {
		try {
			for (MultipartFile file : files) {
				try (BufferedReader lineReader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
					String lineText = null;
					String header = lineReader.readLine();
					log.debug(header);
					while ((lineText = lineReader.readLine()) != null) {
						if (lineText.trim().length() == 70) {
							processJcbFullFile(lineText);
						} else if (lineText.trim().length() > 70) {
							processJcbMaintainanceFile(lineText);
						}
					}
				}
			}
			baseCacheReloaderSvc.updateReloadCacheCounter(new String[] { CacheReloaderConstants.PARTICIPANT,
					CacheReloaderConstants.PARTICIPANT_SETTLEMENT_BIN, CacheReloaderConstants.MEMBINDETAILS });
		} catch (Exception e) {
			log.error("Network Bin File Upload Exception " + e.getMessage() + " - {}", e);
			return BaseCommonConstants.FAIL_STATUS;
		}
		return BaseCommonConstants.PROCESS_STATUS_SUCCESS;
	}
	
	private void processJcbMaintainanceFile(String lineText) throws ParseException {
		String data1 = lineText.replaceAll("\\s{2,}", " ").trim();
		String[] data = data1.split(" ");
		if (data.length > 1) {
			BinDetailsDTO binDetailsDto = new BinDetailsDTO();
			String bin = data[0].substring(2);
			Optional<BinDetailsDTO> binDetailsDTO1 = memberRepository.getNetBinDetail(bin);
			String sign = lineText.substring(67, 69);
			String licenseId = lineText.substring(21, 26);
			if (binDetailsDTO1.isPresent()
					&&(JCB_OPR_SIGN_DELETE.equals(sign)	
							|| (JCB_OPR_SIGN_INSERT.equals(sign) && "A".equals(binDetailsDTO1.get().getStatus())))) {
				if (JCB_OPR_SIGN_DELETE.equals(sign)) {
					memberRepository.updateNetBinRange(binDetailsDTO1.get().getBinNumber(), "D",null,licenseId);
					log.debug("updated the bin number to inactive status as sign is 02");
				} else {
					log.debug("Skipping this bin number as sign is 01 and status is Active");
				}

			} else {
				populateJcbBinDetail(lineText, binDetailsDto, bin);
				if (JCB_OPR_SIGN_UPDATE.equals(sign)) {
					updateJcbRecord(binDetailsDto, binDetailsDTO1);
				}else if (JCB_OPR_SIGN_INSERT.equals(sign)) {
					
					memberRepository.insertNetwBinRange(binDetailsDto);
					log.debug("Inserted bin number to active status with new data as sign is 01");
				}
			}
		}
	}

	private void updateJcbRecord(BinDetailsDTO binDetailsDto, Optional<BinDetailsDTO> binDetailsDTO1) {
		if (binDetailsDTO1.isPresent()) {
			memberRepository.updateNetBinRange(binDetailsDTO1.get().getBinNumber(), "I",null,binDetailsDto.getLicenseId());
			log.debug("updated the bin number to inactive status as sign is 03");
			binDetailsDto.setCreatedBy(binDetailsDTO1.get().getCreatedBy());
			binDetailsDto.setCreatedOn(binDetailsDTO1.get().getCreatedOn());
			binDetailsDto.setAcqBinActivationDate(binDetailsDTO1.get().getAcqBinActivationDate());
		}
		
		memberRepository.insertNetwBinRange(binDetailsDto);
		log.debug("Inserted bin number to active status with new data as sign is 03");
	}

	private void processJcbFullFile(String lineText) throws ParseException {
		String data1 = lineText.replaceAll("\\s{2,}", " ").trim();
		String[] data = data1.split(" ");
		if (data.length > 1) {
			BinDetailsDTO binDetailsDto = new BinDetailsDTO();
			String bin = data[0].substring(2);
			Optional<BinDetailsDTO> binDetailsDTO1 = memberRepository.getNetBinDetail(bin);
			if (!binDetailsDTO1.isPresent()) {
				populateJcbBinDetail(lineText, binDetailsDto, bin);
					try {
						memberRepository.insertNetwBinRange(binDetailsDto);
					}catch(Exception ex) {
						handleGenericException(ex);
					}
					log.debug("Inserted bin number to active status with new data as sign is 01");
				}
			}
	}
	
	private void handleGenericException(Exception ex) {
		String errorDesc;
		if (ex.getCause() != null) {
			errorDesc = ex.getCause().toString();
		} else {
			errorDesc = ex.getMessage();
		}
		if (StringUtils.contains(errorDesc,UNIQUE_CONSTRAINT_ERROR)) {
			log.info("Duplicate Data Entry - {}",ex.getMessage());
		}else {
			log.info("Error - {}:",ex.getMessage());
		}
	}
	
	private void populateJcbBinDetail(String lineText, BinDetailsDTO binDetailsDto, String bin) throws ParseException {
		int panLength = Integer.parseInt(lineText.substring(55, 57));
		binDetailsDto.setBinNumber(bin);
		binDetailsDto.setBinId(fetchBinIdSeq());
		if (bin.length() < 9) {
			binDetailsDto.setLowBin(StringUtils.rightPad(bin, 9, "0"));
			binDetailsDto.setHighBin(StringUtils.rightPad(bin, 9, "9"));
		}else {
			binDetailsDto.setLowBin(bin.substring(0, 9));
			binDetailsDto.setHighBin(bin.substring(0,9));
		}
		String participantId = lookupCache.getDescription(NETWORK_JCB, "DEF_ISS_PARTICIPANT");
		String bankGroup=lookupCache.getDescription(NETWORK_JCB, "DEF_ISS_BANKGRP");
		String binProductType = lookupCache.getDescription(NETWORK_JCB, "BIN_PROD_TYPE");
		String binCardVarient = lookupCache.getDescription(NETWORK_JCB, "BIN_CARD_VARIENT");
		String binCardTechnology = lookupCache.getDescription(NETWORK_JCB, "BIN_CARD_TECHNOLOGY");
		String binCardBrand = lookupCache.getDescription(NETWORK_JCB, "BIN_CARD_BRAND");
		String productType = lookupCache.getDescription(NETWORK_JCB, "PROD_TYPE");
		String settlementBin = lookupCache.getDescription(NETWORK_JCB, "SETTLEMENT_BIN");
		String binCardType = lookupCache.getDescription(NETWORK_JCB, "BIN_CARD_TYPE");

		
		binDetailsDto.setAcqBinActivationDate(new Date());
		binDetailsDto.setDectivationDate(new SimpleDateFormat("yyyy-MM-dd").parse("2099-12-31"));
		binDetailsDto.setBinType("I");
		binDetailsDto.setCreatedBy(sessionDTO.getUserName());
		binDetailsDto.setCreatedOn(new Date());
		binDetailsDto.setLastUpdatedBy(sessionDTO.getUserName());
		binDetailsDto.setLastUpdatedOn(new Date());
		if(participantId !=null) {
			binDetailsDto.setParticipantId(participantId);
		}else {
			binDetailsDto.setParticipantId("NJCB5010001");
		}
		binDetailsDto.setDomainUsage("2");
		binDetailsDto.setBankGroup(bankGroup);
		binDetailsDto.setOfflineAllowed("N");
		binDetailsDto.setPanLength(panLength);
		binDetailsDto.setBinCardType(binCardType);
		binDetailsDto.setBinProductType(binProductType);
		binDetailsDto.setBinCardVariant(binCardVarient);
		binDetailsDto.setCardTechnology(binCardTechnology);
		binDetailsDto.setBinCardBrand(binCardBrand);
		binDetailsDto.setMessageType("D");
		binDetailsDto.setProductType(productType);
		binDetailsDto.setSettlementBin(settlementBin);
		binDetailsDto.setStatus("A");
		String countryCode = countryCache.getCountryCodeByAlpha(lineText.substring(60, 63));
		binDetailsDto.setCountryCode(StringUtils.isNotBlank(countryCode) ? countryCode :"");
		binDetailsDto.setCurrencyCode(lineText.substring(63, 66));
		binDetailsDto.setLicenseId(lineText.substring(21, 27));
	}
	
	private int fetchBinIdSeq() {
		return memberRepository.fetchBinIdSeq();
	}

}
