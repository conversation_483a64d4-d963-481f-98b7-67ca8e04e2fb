$(document).ready(function() {
	$("#systemDate").datepicker({
		dateFormat: 'yyyy-MM-dd',
		todayHighlight: true,
		autoclose: true,
		changeMonth: true,
		changeYear: true
	});

	$("#refresh").click(function() {
		var systemDate = $('#systemDate').val();
		var url = "/showReprocessInternalCycle";
		var data = "systemDate," + systemDate;
		postData(url, data);
	});

	$("#tabnew").DataTable({
		"fnRowCallback": function(nRow, aData, _iDisplayIndex, _iDisplayIndexFull) {
			if (aData[3] == 'Failed' || aData[3] == 'ForceClose' || aData[3] == 'Retry' || aData[3] == 'ForceMerge') {
				$('td', nRow).addClass('bg-danger');
			} else {
				$('td', nRow).addClass('bg-success');
			}
		},
		"aoColumnDefs": [
			{
				bSortable: false,
				aTargets: [0]
			}
		],
		dom: 'lBfrtip',
		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true
	});

	$("#submit").click(function() {
		var valuesArray = $('input.reprocess-checkbox:checkbox:checked').map(function() {
			return this.value;
		}).get().join("-");
		var systemDate = $('#systemDate').val();
		cycleNumbers.push(valuesArray);
		var internalCycleNumber = cycleNumbers.toString();
		var url = "/reprocessInternalCycle";
		var data = "systemDate," + systemDate
			+ ",internalCycleNumber," + internalCycleNumber;
		postData(url, data);
	});
	$("#checkAll").click(function() {
		if (this.checked) {
			$('.reprocess-checkbox').each(function() {
				this.checked = true;
			});
		} else {
			$('.reprocess-checkbox').each(function() {
				this.checked = false;
			});
		}

	});
	$('.reprocess-checkbox, .hCheckbox').on('click', function() {
		if ($('.reprocess-checkbox:checked').length > 0) {
			$("#reprocessBtn").removeAttr("disabled");
		} else {
			$("#reprocessBtn").attr("disabled", "disabled");
		}
		if ($('.reprocess-checkbox:checked').length == $('.reprocess-checkbox').length) {
			$('#checkAll').prop('checked', true);
		} else {
			$('#checkAll').prop('checked', false);
		}
	});

	$("#reprocessBtn").click(function() {
		var icn = $('input.reprocess-checkbox:checkbox:checked').map(function() {
			return this.value;
		}).get().join(", ");
		$("#lableIntCyc").text(icn);
	});

	$("#submitBtn").click(function() {
		var url = getURL('/reprocessInternalCycle');
		var systemDate = $('#systemDate').val();
		let retryData = [];
		$('input.reprocess-checkbox:checkbox:checked').each(function() {
			var jsonObject = {};
			jsonObject["systemDate"] = systemDate;
			jsonObject["internalCycleNumber"] = this.value;
			retryData.push(jsonObject);
		});
		var retryDataJsonObject = {};
		retryDataJsonObject["retryData"] = retryData;
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		var request = JSON.stringify(retryDataJsonObject);
		$.ajax({
			type: "POST",
			contentType: "application/json",
			url: url,
			data: request,
			dataType: 'json',
			timeout: 600000,
			headers: {
				'_TransactToken': tokenValue
			},
			success: function(data) {
				$("#alertNotification").removeClass("hide");
				if (data.Status === 'FAILED' || data.Status === '503') {
					$("#alertNotification").addClass("alert-danger");
					$("#alertMessageStrong").text("Error!");
					$("#alertMessage").text(data.errorMessage);
				} else if (data.Status === 'SUCCESS' || data.Status === '200') {
					$("#alertNotification").addClass("alert-success");
					$("#alertMessageStrong").text("Success!");
					$("#alertMessage").text("Initiated reprocess request.");
					setTimeout(function() { window.location.reload(); }, 5000);
				}

				$("#reprocessModel .close").click();
				$("#alertNotification").fadeTo(4000, 500).slideUp(500, function() {
					$("#alertNotification").slideUp(500);
				});
			},
			error: function(e) {
				alert(e);
			}
		});
	});
});

function getURL(url) {
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	
	return (pathName + url);
}


