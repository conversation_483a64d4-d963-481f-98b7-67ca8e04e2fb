<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.FileUploadRepository">
	<insert id="insertFileUpload" parameterType="FileUploadDTO"
		useGeneratedKeys="true" keyProperty="documentId"
		keyColumn="document_id">
		INSERT INTO file_upload
		(document_name, document_path,
		participant_id,
		total_no_trans,
		total_amount, rejected_no_trans,
		full_file_rejected,
		is_active,
		status, created_by, last_updated_by,
		version,
		valid_no_trans,
		file_generated_on, portal, site, instance,
		insert_day,
		org_document_name) VALUES(#{documentName},
		#{documentPath},
		#{participantId}, #{totalNoTrans}, #{totalAmount},
		#{rejectedNoTrans},
		#{fullFileRejected}, #{isActive}, #{status},
		#{createdBy},
		#{lastUpdatedBy}, #{version}, #{validNoTrans},
		#{fileGenDateTime},
		#{portal}, #{siteId}, #{instanceId}, #{insertDay},
		#{orgDocumentName})
		RETURNING document_id
	</insert>

	<select id="getFilesByStatus" resultType="FileUploadDTO">
		SELECT
		fu.DOCUMENT_ID as
		documentId,
		fu.DOCUMENT_NAME as
		documentName,fu.ORG_DOCUMENT_NAME as
		orgDocumentName,
		fu.DOCUMENT_PATH
		as documentPath,
		fu.PARTICIPANT_ID as
		participantId,
		fu.TOTAL_NO_TRANS as
		totalNoTrans,
		fu.TOTAL_AMOUNT as
		totalAmount,
		fu.REJECTED_NO_TRANS
		as
		rejectedNoTrans,
		fu.FULL_FILE_REJECTED as fullFileRejected,
		fu.IS_ACTIVE as isActive,
		fu.STATUS as status,
		fu.CREATED_BY as
		createdBy,fu.created_on as
		createdOn, fu.LAST_UPDATED_BY as
		lastUpdatedBy,
		fu.last_updated_on as
		lastUpdatedOn FROM
		FILE_UPLOAD fu
		WHERE status = #{status} AND portal =
		#{portal} AND
		site = #{site} AND
		instance = #{instance}
	</select>
	<update id="updateFileUploadInfo">UPDATE file_upload SET
		DOCUMENT_PATH=#{documentPath},
		TOTAL_NO_TRANS=#{totalNoTrans},
		TOTAL_AMOUNT=#{totalAmount},
		REJECTED_NO_TRANS=#{rejectedNoTrans},
		FULL_FILE_REJECTED=#{fullFileRejected}, STATUS=#{status},
		LAST_UPDATED_ON=#{lastUpdatedOn}, VERSION=#{version},
		VALID_NO_TRANS=#{validNoTrans}, FILE_GENERATED_ON=#{fileGenDateTime},
		RECON_CYCLE=#{cycleNumber}, NET_RECON_DATE=#{cycleDate} WHERE
		DOCUMENT_ID = #{documentId}
	</update>



	<select id="searchFileUploadList" resultType="FileUploadDTO">
		SELECT
		fu.DOCUMENT_ID as documentId,
		fu.DOCUMENT_NAME as
		documentName,fu.ORG_DOCUMENT_NAME as orgDocumentName,
		fu.DOCUMENT_PATH
		as documentPath,
		fu.PARTICIPANT_ID as participantId, fu.TOTAL_NO_TRANS
		as totalNoTrans,
		fu.TOTAL_AMOUNT as totalAmount,
		fu.REJECTED_NO_TRANS as
		rejectedNoTrans, fu.FULL_FILE_REJECTED as fullFileRejected,
		fu.IS_ACTIVE as isActive, fu.STATUS as status,
		fu.CREATED_BY as
		createdBy, fu.LAST_UPDATED_BY as lastUpdatedBy,
		fu.INSERT_DAY as
		insertDate, fu.VERSION as version,
		fu.VALID_NO_TRANS as validNoTrans,
		fu.FILE_GENERATED_ON as fileGenDateTime FROM
		FILE_UPLOAD fu WHERE
		participant_id = #{participantId} AND (#{status} =
		'' OR STATUS =
		#{status}) AND ( #{fromDate} IS NULL OR INSERT_DAY >=
		#{fromDate}) AND
		( #{toDate} IS NULL OR INSERT_DAY &lt;= #{toDate}) AND
		(
		#{documentName}
		= NULL OR #{documentName} = '' OR DOCUMENT_NAME LIKE
		#{documentName}
		OR ORG_DOCUMENT_NAME LIKE #{documentName} ) AND portal
		= #{portal}
		ORDER BY fu.DOCUMENT_ID DESC
	</select>

	<select id="getFilesByFileIds" resultType="FileUploadDTO">
		SELECT fu.DOCUMENT_ID
		as documentId,
		fu.DOCUMENT_NAME as documentName,
		fu.DOCUMENT_PATH as documentPath,
		fu.PARTICIPANT_ID as participantId,
		fu.TOTAL_NO_TRANS as totalNoTrans,
		fu.TOTAL_AMOUNT as totalAmount,
		fu.REJECTED_NO_TRANS as rejectedNoTrans,
		fu.FULL_FILE_REJECTED as fullFileRejected,
		fu.IS_ACTIVE as isActive,
		fu.STATUS as status,
		fu.CREATED_BY as createdBy,
		fu.LAST_UPDATED_BY as lastUpdatedBy, fu.INSERT_DAY as
		insertDate, fu.VERSION as version, fu.VALID_NO_TRANS as validNoTrans,
		fu.FILE_GENERATED_ON as fileGenDateTime FROM FILE_UPLOAD fu WHERE
		fu.DOCUMENT_ID IN (${stageFileIds})
	</select>

	<select id="getFileUploadDtoByDocId" resultType="FileUploadDTO">
		SELECT
		fu.DOCUMENT_ID as documentId,
		fu.DOCUMENT_NAME as documentName,
		fu.DOCUMENT_PATH as documentPath,
		fu.PARTICIPANT_ID as participantId,
		fu.TOTAL_NO_TRANS as totalNoTrans,
		fu.TOTAL_AMOUNT as totalAmount,
		fu.REJECTED_NO_TRANS as rejectedNoTrans,
		fu.FULL_FILE_REJECTED as
		fullFileRejected,
		fu.IS_ACTIVE as isActive,
		fu.STATUS as status,
		fu.CREATED_BY as createdBy,
		fu.LAST_UPDATED_BY as lastUpdatedBy,
		fu.INSERT_DAY as insertDate,
		fu.VERSION as version,
		fu.VALID_NO_TRANS as validNoTrans,
		fu.FILE_GENERATED_ON as
		fileGenDateTime
		FROM FILE_UPLOAD fu WHERE
		fu.DOCUMENT_ID IN (${documentId})
	</select>

	<delete id="discardFileUpload">
		DELETE FROM file_upload WHERE DOCUMENT_ID = #{documentId}
	</delete>

	<select id="getFilesByPortal" resultType="FileUploadDTO">
		SELECT
		fu.DOCUMENT_ID as
		documentId,
		fu.DOCUMENT_NAME as documentName,
		fu.DOCUMENT_PATH as
		documentPath,
		fu.PARTICIPANT_ID as participantId,
		fu.TOTAL_NO_TRANS as
		totalNoTrans,
		fu.TOTAL_AMOUNT as totalAmount,
		fu.REJECTED_NO_TRANS as
		rejectedNoTrans,
		fu.FULL_FILE_REJECTED as fullFileRejected,
		fu.IS_ACTIVE as isActive,
		fu.STATUS as status,
		fu.CREATED_BY as createdBy,
		fu.created_on as createdOn,
		fu.LAST_UPDATED_BY as lastUpdatedBy,
		fu.last_updated_on
		as lastUpdatedOn,
		fu.ORG_DOCUMENT_NAME as orgDocumentName
		FROM
		file_upload fu WHERE portal = #{portal}
	</select>

	<select id="getFileStatusById" resultType="String">
		SELECT STATUS from
		FILE_UPLOAD WHERE DOCUMENT_ID = #{documentId}
	</select>

</mapper>	

	
