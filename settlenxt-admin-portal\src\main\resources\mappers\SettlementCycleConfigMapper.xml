<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.SettlementCycleConfigRepository">
<select id="getSettlementCycleConfig"
	resultType="SettlementCycleConfigDTO">
	SELECT
	sr_no as srNo,
	cycle_number as cycleNumber,
	start_hour as startHour,
	end_hour as endHour,
	is_active as isActive,
	total_icn_keys as totalICNkeys,
	date_increment as dateIncrement,
	product_id as productId
	FROM
	settlement_cycle_config
	ORDER BY product_id,sr_no ASC;
</select>
	<select id="getApprovedDataFromMain" resultType="SettlementCycleConfigDTO">
		SELECT s.product_id as productId,s.sr_no as srNo,s.cycle_number as cycleNumber ,s.start_hour as startHour,s.end_hour as endHour, s.is_active as isActive ,s.is_active as isActiveValue, s.total_icn_keys as totalICNkeys  ,s.date_increment as dateIncrement from settlement_cycle_config s join settlement_cycle_config_stg st on s.sr_no=st.sr_no where st.request_state='A'
	</select>
<select id="getPendingSettlementFromStg" resultType="SettlementCycleConfigDTO">
		SELECT product_id as productId,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,sr_no as srNo,cycle_number as cycleNumber ,start_hour as startHour,end_hour as endHour, is_active as isActive ,is_active as isActiveValue,  total_icn_keys as totalICNkeys  ,date_increment as dateIncrement,created_on as createdOn,created_by as createdBy,request_state as requestState,checker_comments as checkerComments from settlement_cycle_config_stg st where st.request_state in ('P','R')
	</select>	
	
	<select id="fetchSrNoList" resultType="SettlementCycleConfigDTO">
		SELECT product_id as productId,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,sr_no as srNo,cycle_number as cycleNumber ,start_hour as startHour,end_hour as endHour, is_active as isActive ,is_active as isActiveValue,  total_icn_keys as totalICNkeys  ,date_increment as dateIncrement,created_on as createdOn,created_by as createdBy,request_state as requestState from settlement_cycle_config_stg  where sr_no in <foreach item='item' index='index' collection='srNoList' open='(' separator=',' close=')'>#{item}</foreach>
		 
	</select>	
	
	<select id="getSettlementDetails"
		resultType="SettlementCycleConfigDTO">
		select
		sr_no as srNo,cycle_number as cycleNumber,
		start_hour as
		startHour, end_hour as endHour,is_active as
		isActive,total_icn_keys as totalICNkeys,date_increment as
		dateIncrement,is_active as isActiveValue, product_id as productId,
		request_state as requestState
		from
		settlement_cycle_config_stg
		where
		sr_no = #{srNo};
	</select>
	<update id="updateRequestStateInStg">
		update
		settlement_cycle_config_stg
		set
		request_state = #{requestState},last_operation = #{lastOperation},
		checker_comments = #{checkerComments}
		where
		sr_no = #{srNo};
	</update>
	
	
	<select id="searchSrNoInMain" resultType="SettlementCycleConfigDTO">
		select
		sr_no as srNo,cycle_number as cycleNumber,
		start_hour as
		startHour, end_hour as endHour,is_active as
		isActive,total_icn_keys as totalICNkeys,date_increment as
		dateIncrement,is_active as isActiveValue,product_id as productId 
		from
		settlement_cycle_config
		where
		sr_no = #{srNo};
	</select>
	
	<update id="updateSrNoInMain">
		update 
		settlement_cycle_config
		set 
		cycle_number = #{cycleNumber}, 
		start_hour = #{startHour},
		end_hour = #{endHour},is_active = #{isActive},
		total_icn_keys=#{totalICNkeys},date_increment=#{dateIncrement},
		product_id=#{productId}
		where 
		sr_no = #{srNo};
	</update>
	
	<insert id="insertSrNoInMain">
		insert into settlement_cycle_config 
		(sr_no,cycle_number,start_hour,
		end_hour,is_active,total_icn_keys,
		date_increment,product_id,created_by,created_on)
		values
		(#{srNo},#{cycleNumber},#{startHour},#{endHour},#{isActive},#{totalICNkeys},#{dateIncrement},#{productId},#{createdBy},#{createdOn});
	</insert>
	

	
	<delete id="deleteSettlement">
		delete from settlement_cycle_config_stg
		where
		sr_no = #{srNo};
	</delete>
	<select id="getSrNoInfoFromMain" resultType="SettlementCycleConfigDTO">
		  SELECT st.request_state as requestState,s.sr_no as srNo ,s.product_id as productId, s.cycle_number as cycleNumber , s.start_hour as startHour, s.end_hour as endHour,s.is_active as isActiveValue,  s.is_active as isActive, s.total_icn_keys as totalICNkeys,
		  s.date_increment as dateIncrement FROM settlement_cycle_config s join settlement_cycle_config_stg st on s.sr_no=st.sr_no  where s.sr_no=#{srNo};
</select>
	<select id = "getLookupDataSorted" resultType = "CodeValueDTO">
	SELECT code as code, description as description from lookup where type = #{data} order by code asc;
</select>

<insert id="insertSettlementCycleIntoStg">
	INSERT INTO settlement_cycle_config_stg
(sr_no, product_id, cycle_number, start_hour, end_hour,  date_increment, is_active, created_by, created_on, request_state,total_icn_keys,last_operation)
VALUES(#{srNo}, #{productId}, #{cycleNumber}, #{startHour},  #{endHour}, #{dateIncrement}, #{isActive}, #{createdBy}, #{createdOn},  #{requestState},#{totalICNkeys},#{lastOperation});
</insert>


<select id="getSettlementCycleConfigSeqId" resultType="int">
		SELECT nextval('settlementcycleconfigidseq')
	</select>
	
<update id = "updateSettlementCycleStg">
	update 
		settlement_cycle_config_stg
		set 
		start_hour = #{startHour},
		product_id = #{productId},
		end_hour = #{endHour},
		is_active = #{isActive},
		total_icn_keys=#{totalICNkeys},
		date_increment=#{dateIncrement},
		last_updated_by = #{lastUpdatedBy},
		last_updated_on = #{lastUpdatedOn},
		last_operation = #{lastOperation},
		request_state = #{requestState}
		where 
		sr_no = #{srNo};
	</update>
	
	<update id = "editSettlement">
	update 
		settlement_cycle_config_stg
		set 
		start_hour = #{startHour},
		product_id = #{productId},
		end_hour = #{endHour},
		is_active = #{isActive},
		total_icn_keys=#{totalICNkeys},
		date_increment=#{dateIncrement},
		last_operation = #{lastOperation},
		request_state = #{requestState}
		where 
		sr_no = #{srNo}
	</update>
<select id="getSrNoInfoFromStg" resultType="SettlementCycleConfigDTO">
SELECT s.request_state as requestState,s.sr_no as srNo ,s.product_id as productId, s.cycle_number as cycleNumber , s.start_hour as startHour, s.end_hour as endHour,s.is_active as isActiveValue,s.total_icn_keys as totalICNkeys,s.date_increment as dateIncrement FROM settlement_cycle_config_stg s where s.sr_no=#{srNo}</select>

<select id = "validateSettlementTime" resultType = "int">
SELECT COUNT(sr_no) FROM settlement_cycle_config_stg WHERE start_hour=#{startHour} and end_hour=#{endHour} and product_id=#{productId}
</select>	

<select id = "validateSettlementTimeEdit" resultType = "int">
SELECT COUNT(sr_no) FROM settlement_cycle_config_stg WHERE start_hour=#{startHour} and end_hour=#{endHour} and product_id=#{productId}
</select>

<select id="getAllTimeIntervals" resultType="SettlementCycleConfigDTO">
select start_hour as startHour, end_hour as endHour from settlement_cycle_config_stg where product_id =#{prodId} and cycle_number=#{cycleNum}
</select>
	
	
</mapper>