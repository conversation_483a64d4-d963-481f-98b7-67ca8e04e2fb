$(document).ready(function () {
	
	$('select#instIdAcq').prop('disabled', true);
	$('select#instIdIss').prop('disabled', true);


	$('#actionCode').on('keyup keypress blur change', function() {
		validateFromCommonVal('actionCode', true, "SelectionBox", 10, false);
	});
	$('#txnDestinationInstId').on('keyup keypress blur change', function() {
		validateFromCommonVal('txnDestinationInstId', true, "SelectionBox", 100, false);
	});	
	$('#entityType').on('keyup keypress blur change', function() {
		validateFromCommonVal('entityType', true, "SelectionBox", 10, false);
	});
	$('#feeName').on('keyup keypress blur change', function() {
		validateFromCommonVal('feeName', true, "SelectionBox", 100, false);
	});
	$('#feeCcy').on('keyup keypress blur change', function() {
		validateFromCommonVal('feeCcy', true, "SelectionBox", 10, false);
	});
	$('#memMsgTxt').on('keyup keypress blur change', function() {
		validateMemMsgTxt();
	});
	$('#amtTran').on('keyup keypress blur change', function() {
		validateFromCommonVal('amtTran', true, "NumericwithPrecisionAmount", 10, false);
	});
	
	
	const acqIdList = document.getElementById("acqIdList").value;
	const issIdList = document.getElementById("issIdList").value;
	
	const jsonAcqIdList = JSON.parse(acqIdList);
	const jsonIssIdList = JSON.parse(issIdList);
	
	$("#txnDestinationInstId").change(function(_e){
		$('select#entityType').prop('selectedIndex', 0);
		document.getElementById("settlementBin").value = '';
		document.getElementById("instIdAcq").value = '';
		document.getElementById("instIdIss").value = '';
		$("select#instIdIss").empty();
		$("select#instIdAcq").empty();
		$("#errinstIdAcq").find('.error').html('');
		$('#errinstIdIss').find('.error').html('');
		$('#defaultIssErrorMsg').hide();
		$('#defaultAcqErrorMsg').hide();
		let newOptionAcq = new Option('--Select--','');
		const selectAcq = document.querySelector('select#instIdAcq');
		selectAcq.add(newOptionAcq,undefined);
		let newOptionIss = new Option('--Select--','');
		const selectIss = document.querySelector('select#instIdIss');
		selectIss.add(newOptionIss,undefined);
		$('select#instIdAcq').prop('disabled', true);
		$('select#instIdIss').prop('disabled', true);
	});
	
	$("#entityType").change(function(_e){
		document.getElementById("settlementBin").value = '';
		$('#errinstIdAcq').find('.error').html('');
		$('#errinstIdIss').find('.error').html('');
		let newOption = new Option('--Select--','');
		var txnDestInstId =  document.getElementById("txnDestinationInstId").value;
		if(txnDestInstId === ''){ 
			alert("Please select the transaction Destination Id");
			var elements = document.getElementById("entityType").options;
			for(let i of elements){
     			 i.selected = false;
   			 }
		}else{
		var entityType = document.getElementById("entityType").value;
		handleUsingEntityType(entityType, jsonIssIdList, txnDestInstId, newOption, jsonAcqIdList);
		}
	});
	
	$("#instIdIss").change(function(_e){
		document.getElementById("settlementBin").value = '';
		var txnDestInstID = $('#txnDestinationInstId').val();
		var entityType = $('#entityType').val();
		var instIdIss = $('#instIdIss').val();
		if(txnDestInstID !== '' && entityType !== '' && instIdIss != ''){
			fetchSettlementBin(txnDestInstID,entityType,instIdIss);
		}
	});
	
	$("#instIdAcq").change(function(_e){
		document.getElementById("settlementBin").value = '';
		var txnDestInstID = $('#txnDestinationInstId').val();
		var entityType = $('#entityType').val();
		var instIdAcq = $('#instIdAcq').val();
		if(txnDestInstID !== '' && entityType !== '' && instIdAcq != ''){
			fetchSettlementBin(txnDestInstID,entityType,instIdAcq);
		}
	});
});

function handleUsingEntityType(entityType, jsonIssIdList, txnDestInstId, newOption, jsonAcqIdList) {
	if (entityType == 'I') {
		$('#errinstIdAcq').find('.error').html('');
		document.getElementById("instIdAcq").value = '';
		const select = document.querySelector('select#instIdIss');
		$("select#instIdIss").empty();
		var issId = jsonIssIdList[txnDestInstId];
		select.add(newOption, undefined);
		if (!isEmpty(issId)) {
			$('#defaultAcqErrorMsg').hide();
			for (let i of issId) {
				newOption = new Option(i);
				select.add(newOption, undefined);
			}
			$('select#instIdAcq').prop('disabled', true);
			$('select#instIdIss').prop('disabled', false);
		} else {
			document.getElementById("navbar").scrollIntoView({ behavior: "smooth" });
			$('#defaultIssErrorMsg').show();
			$('select#instIdAcq').prop('disabled', true);
			$('select#instIdIss').prop('disabled', true);
		}
	} else {
		$('#errinstIdIss').find('.error').html('');
		document.getElementById("instIdIss").value = '';
		const select = document.querySelector('select#instIdAcq');
		$("select#instIdAcq").empty();
		var acqId = jsonAcqIdList[txnDestInstId];
		select.add(newOption, undefined);
		if (!isEmpty(acqId)) {
			$('#defaultIssErrorMsg').hide();
			for (let i of acqId) {
				newOption = new Option(i);
				select.add(newOption, undefined);
			}
			$('select#instIdIss').prop('disabled', true);
			$('select#instIdAcq').prop('disabled', false);
		} else {
			document.getElementById("navbar").scrollIntoView({ behavior: "smooth" });
			$('#defaultAcqErrorMsg').show();
			$('select#instIdIss').prop('disabled', true);
			$('select#instIdAcq').prop('disabled', true);
		}
	}
}

function validateMemMsgTxt(){
	var isValid = true;
	var memMsgTxt = document.getElementById("memMsgTxt").value;
	
	const regEx = new RegExp(document.getElementById("memTxtMsgRegex").value);
	if(memMsgTxt.length > 0){
		if (!regEx.test(memMsgTxt)) {
			$('#errmemMsgTxt').html('Please enter valid text message');
			$('#errmemMsgTxt').show();
			isValid = false;
		}
		if(isValid){
			if(memMsgTxt.length > 100){
				$('#errmemMsgTxt').html('Message length should be less than 100 characters');
				$('#errmemMsgTxt').show();
				isValid = false;
			}	
		}
	}else{
		isValid = false;
		$('#errmemMsgTxt').html('Please enter the text message');
		$('#errmemMsgTxt').show();
	}
	if(isValid){
		$('#errmemMsgTxt').html('');
		$('#errmemMsgTxt').hide();
		return true;
	}
	return false;
}
function fetchSettlementBin(txnDestInstID,entityType,instId){
	
	
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	var linkurl = pathName + "/getSettlementBin";
	var submitData = "txnDestInstID:"+ txnDestInstID + ",entityType:" +entityType + ",instId:" + instId + ",_TransactToken:"+ tokenValue;
	console.log(submitData);
	$.ajax({
		url : linkurl,
		type : "POST",
		dataType : "text",
		data : {
			"txnDestInstID" : txnDestInstID,
			"entityType" :entityType,
			"instId" :instId,
			"_TransactToken": tokenValue
		},
		"beforeSend": function (xhr) {
            xhr.setRequestHeader('_TransactToken', tokenValue);
        },
		cache: false,
		success : function(data){
			if(data.length > 0 ){
				$('#errsettlementBin').html('');
				document.getElementById("settlementBin").value = data;
			}else{
				$('#errsettlementBin').html('Settlement Bin not found');
			}	
		},
		 error: function(request, _status, error) {
	                console.log("REQUEST" + request);
					console.log("ERROR" + error);
	   }
	});
}
function resetAction() {
document.getElementById("addFundCollectDisburse").reset();

	$('#erractionCode').find('.error').html('');
	$('#errtxnOrgInstCd').find('.error').html('');
	$('#errfeeName').find('.error').html('');
	$('#errtxnDestinationInstId').find('.error').html('');
	$("#errmemMsgTxt").find('.error').html('');
	$('#errentityType').find('.error').html('');
	$('#errfeeCcy').find('.error').html('');
	$("#errinstIdIss").find('.error').html('');
	$('#erramtTran').find('.error').html('');
	$('#errinstIdAcq').find('.error').html('');
	$('#errfeeTypeCd').find('.error').html('');
	$('#errsettlementBin').find('.error').html('');
	$('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
	$('#defaultIssErrorMsg').hide();
	$('#defaultAcqErrorMsg').hide();

}
function setFeeCode() {
	var feeCode = document.getElementById("feeName").value;
	if(feeCode != 'SELECT'){
    	document.getElementById("feeTypeCd").value = feeCode;
    }
}
function validateNpciFundFeeForm() {
	var isValid = true;
	if(!validateFromCommonVal('actionCode', true, "SelectionBox", 10, false)){
		isValid = false;
	}	
	if(!validateFromCommonVal('txnDestinationInstId', true, "SelectionBox", 100, false)){
		isValid = false;
	}
	
	if(!validateFromCommonVal('entityType', true, "SelectionBox", 10, false)){
		isValid = false;
	}
	
	if(!validateFromCommonVal('feeName', true, "SelectionBox", 100, false)){
		isValid = false;
	}
	
	if(!validateFromCommonVal('feeCcy', true, "SelectionBox", 10, false)){
		isValid = false;
	}
	
	if(!validateMemMsgTxt()){
		isValid = false;
	}
	
	if(!validateFromCommonVal('amtTran', true, "NumericwithPrecisionAmount", 10, false)){
		isValid = false;
	}
	
	var entityType = document.getElementById("entityType").value;
	if(entityType === 'I'){
		if(!validateFromCommonVal('instIdIss', true, "SelectionBox", 10, false)){
			inValid = false;
		}
	}else if(entityType === 'A'){
		if(!validateFromCommonVal('instIdAcq', true, "SelectionBox", 10, false)){
			inValid = false;
		}
	}
	if(!validateFromCommonVal('settlementBin', true, "AlphaNumericNoSpace", 10, false)){
		isValid = false;
	}
	return isValid;
}
function addFundCollectDisburseInfo(actionUrl){
	
	var isValid = validateNpciFundFeeForm();
	if(isValid){
		document.getElementById("submitFundCollectDisburse").disabled = true;
		document.getElementById("resetFundFee").disabled = true;
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		var actionCode = document.getElementById("actionCode").value;
		var txnDestinationInstId = document.getElementById("txnDestinationInstId").value;
		var entityType = document.getElementById("entityType").value;
		var instIdIss = document.getElementById("instIdIss").value;
		var instIdAcq = document.getElementById("instIdAcq").value;
		var settlementBin = document.getElementById("settlementBin").value;
		var memMsgTxt = document.getElementById("memMsgTxt").value;
		var feeCcy = document.getElementById("feeCcy").value;
		var amtTran = document.getElementById("amtTran").value;
		var feeTypeCd = document.getElementById("feeTypeCd").value;
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		var linkurl = pathName + actionUrl;
		$.ajax({
						url : linkurl,
						type : "POST",
						dataType : "json",
						data: {"actionCode" : actionCode,"txnDestInstId" : txnDestinationInstId,"entityType" :entityType,"instIdIss" :instIdIss,
							    "instIdAcq":instIdAcq,"settlementBin":settlementBin,"memMsgTxt":memMsgTxt,"feeCcy":feeCcy,"amtTran":amtTran,
							    "feeTypCode":feeTypeCd,"_TransactToken" : tokenValue},
						cache: false,
						success : function(response){
							 if (response.status == "BSUC_0001") {
							 	var form = document.getElementById("addFundCollectDisburse")
							 	var elements = form.elements;
								for (var i = 0, len = elements.length; i < len; ++i) {
	    							elements[i].disabled = true;
								}
							 	document.getElementById("resetFundFee").style.visibility = 'hidden';
								document.getElementById("submitFundCollectDisburse").style.visibility = 'hidden';
								document.getElementById("backButton").style.visibility = 'hidden';
								
								document.getElementById("refreshBackBtn").disabled = false;
								$("#refreshBackBtn").show();
								document.getElementById("navbar").scrollIntoView({behavior:"smooth"});
								$('#transitionSuccessMsg').show();
							 } 
							 else {
								document.getElementById("navbar").scrollIntoView({behavior:"smooth"});
								$('#transitionErrorMsg').show();
					 		} 
						},
				 		error: function(_request, _status, _error) {
		               		 document.getElementById('transitionErrorMsg').style.display = 'block';
		               		 $('.panel').hide();
		           		}
			});
		}else{
			alert('Please provide all data properly');
		}
		
}

function backAction(type, action) {
	let url = action;
	var data = "userType," + type;
	postData(url, data);
}