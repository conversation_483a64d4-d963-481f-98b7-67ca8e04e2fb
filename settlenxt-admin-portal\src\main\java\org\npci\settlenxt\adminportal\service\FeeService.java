package org.npci.settlenxt.adminportal.service;


import java.util.List;

import org.apache.commons.lang3.tuple.Pair;
import org.json.simple.parser.ParseException;
import org.npci.settlenxt.adminportal.dto.FeeDTO;
import org.npci.settlenxt.adminportal.dto.FeeMajorMinorPriorityDTO;
import org.npci.settlenxt.portal.common.dto.BaseFeeMajorMinorPriorityDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.FeeRateConfigDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;

public interface FeeService {

	 List<FeeDTO> getApprovedFeeRateList();

	 List<FeeDTO> getPendingForAppovalFeeRateList();

	 FeeDTO getFee(int feeId);

	 FeeDTO getFeeMain(int feeId);

	 FeeDTO updateApproveOrRejectFeeRate(int feeId, String status, String remarks) throws SettleNxtException;

	 FeeDTO discardFeeRate(int feeId) throws SettleNxtException;

	 FeeDTO updateApproveOrRejectFeeRateBulk(String feeIdList, String status, String remarks) throws SettleNxtException;

	 List<FeeDTO> getFeeCodeList();

	 List<FeeDTO> getGstCodeList();

	 List<FeeDTO> getTxnCurrencyList();

	 List<FeeDTO> getPartyTypeList();

	 long addFeeRate(FeeDTO feeRateDto);

	 boolean checkDistinctFeecode(int feeCode);

	 FeeDTO getEditFeeRate(int feeId);

	 long updateEditFeeRate(FeeDTO feeRateDto);

	 List<FeeDTO> getMajorMinorList(String significance);

	 List<FeeDTO> getPendingFeeConfig(String significance);

	 void updateFeeMajorMinor(FeeDTO feeDto) throws SettleNxtException;

	 FeeDTO getEditFeeMajorMinor(FeeDTO feeDto);

	 void addFeeMajorMinor(FeeDTO feeDto) throws SettleNxtException;

	 List<FeeMajorMinorPriorityDTO> getFeeIdList(String feeTypeCode);

	 List<FeeMajorMinorPriorityDTO> getFeeMinorIdList(String feeMajorId);

	 void addFeeConfig(BaseFeeMajorMinorPriorityDTO feeMajorMinor, String reqType) throws SettleNxtException;

	 FeeMajorMinorPriorityDTO getEditFeeConfig(int majorId, int priority);

	 boolean checkDistinctFeeMajorMinorId(String feeConfigId, String significance);

	 List<FeeMajorMinorPriorityDTO> getSavedFeeConfig();

	 FeeDTO updateApproveOrRejectFeeMajor(String majorId, String status, String remarks) throws SettleNxtException;

	 FeeDTO updateApproveOrRejectFeeMinor(String minorId, String status, String remarks) throws SettleNxtException;

	 FeeDTO discardFeeMinor(String minorId) throws SettleNxtException;

	 FeeDTO discardFeeMajor(String feeMajorId) throws SettleNxtException,ParseException;

	 FeeDTO getFeeMajor(String majorId);

	 List<FeeRateConfigDTO> getFeeMinor(String minorId);

	 List<FeeMajorMinorPriorityDTO> getSavedFeeConfigByMajorId(String feeMajorId, String reqType);

	 List<FeeRateConfigDTO> getFeeMajorConfig(String majorId);

	 List<FeeDTO> getMinorInfo(String[] minorIdsArr);

	 List<CodeValueDTO> getUnmappedFieldNameData(String feeConfigId);

	 boolean removeMajorMinorMapping(String feeMinorId, String feeMajorId);

	 Pair<List<String>, String> checkDuplicatePriority(int priority, String majorId, String cardType);

	 boolean checkFeeMajorAvailable(String feeMajorId);

	 boolean checkFeeMinorAvailable(String feeMinorId);

	FeeDTO getEditFeeMajoRConfigInfo(FeeDTO feeDto);

	 void updateStgStatus(String feeMajorId);

	 List<FeeRateConfigDTO> getFeeMinorForView(String minorId);

	 FeeDTO getFeeMajorForView(String majorId);

	 FeeDTO updateApproveOrRejectFeeMajorBulk(String feeMajorIdList, String status, String remarks)
			throws SettleNxtException;

	 FeeDTO updateApproveOrRejectFeeMinorBulk(String minorIdList, String status, String remarks) throws SettleNxtException;

	 List<FeeRateConfigDTO> prepareFeeConfigMajorFromJson(String feeMajorId) throws SettleNxtException,ParseException;

	 List<FeeMajorMinorPriorityDTO> getFeeMinorPriorityMain(String feeMajorId);

	 List<FeeMajorMinorPriorityDTO> getFeeDescriptionForMinor();

	 List<FeeMajorMinorPriorityDTO> prepareFeeDescriptionForMinorPrior(
			List<FeeMajorMinorPriorityDTO> savedFeeConfigList);

	 boolean checkFeeRateAvailable(int feeId);

	 List<CodeValueDTO> getDateActionList();

}
