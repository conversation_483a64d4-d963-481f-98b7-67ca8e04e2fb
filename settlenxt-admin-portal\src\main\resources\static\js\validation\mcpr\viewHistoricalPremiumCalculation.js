var showbuttonflag=0;
var feeReportName= '';
$(document).ready(function () {

    var cursorPosition = null;
     $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    cursorPosition = searchBoxFunc(colIdx, cell, title, api, cursorPosition);
                });
            $('#tabnew_filter').hide();
            
        },
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: feeReportName,
                header: 'false',
                title: null,
                sheetName: feeReportName,
                className: 'defaultexport'
                
            },
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: feeReportName ,
						header:'false', 
						title: null,
						sheetName: feeReportName,
						className:'defaultexport'
                    }
        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true
    });

    $("#excelExport").on("click", function () {
        $(".buttons-excel").trigger("click");
    });
 	     $("#csvExport").on("click", function () {	
	        $(".buttons-csv").trigger("click");
	    });
 
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });
     
     $("#vendorDiv").hide();
     $("#variantDiv").hide();
     $("#vendorButton").hide();
     $("#variantButton").hide();
     $("#adjustButton").hide();
     
     
     $('#historyMonth').on('keyup keypress blur change', function () {
         validateField('historyMonth', true, "Number", 100, false);
     });
     $('#historyYear').on('keyup keypress blur change', function () {
         validateField('historyYear', true, "Alphanumeric", 10, false);
     });
     
     $('#vendorId').on('keyup keypress blur change', function () {
         validateField('vendorId', true, "Number", 10, false);
     });
     
     $('#cardVariant').on('keyup keypress blur change', function () {
         validateField('cardVariant', true, "Alphanumeric", 10, false);
     });
     $('#cardType').on('keyup keypress blur change', function () {
         validateField('cardType', true, "Alphanumeric", 10, false);
     });
     
     loadData();
     var  datainfo;
     if (typeof hidenDiv != "undefined") {
	     datainfo = document.getElementById('hidenDiv'); 
		 datainfo.style.display = 'none';
	 }	 
     if (typeof exportdatainfo != "undefined") {
	      datainfo = document.getElementById('exportdatainfo'); 
		 datainfo.style.display = 'none';
	 }	 
});


function searchBoxFunc(colIdx, cell, title, api, cursorPosition) {
    if (colIdx < actionColumnIndex) {
        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function(_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
    return cursorPosition;
}

function loadData()
{   

	var hyear = $("#hyear").val();
	var hmonth =  $("#hmonth").val();
	var hVariant= $("#hvariant").val();
	var hcardtype = $("#hcardtype").val();
	var hvendor = $("#hvendor").val();
	var htype= $("#htype").val();
	if (htype!="")
	{
		
		var i;
		var j;
		const ids = ["historyMonth","historyYear","cardVariant","cardType","vendorId"];
		const values = [hmonth,hyear,hVariant,hcardtype,hvendor];
		showControl(htype);
		
		for(j=0; j<ids.length; j++){
		var selectObj = document.getElementById(ids[j]);
     	for (i = 0; i < selectObj.options.length; i++) {
        if (selectObj.options[i].value == values[j]) {
            selectObj.options[i].selected = true;
            break;
        	}
        }
        }
        
        
	    
	
	
		
	   	showbuttonflag=1;

	}
}


window.history.forward();
function noBack() {
    window.history.forward();
}

function display() {
    $(".appRejMust").hide();

}

function padTo2Digits(sys_month) {
	  return sys_month.toString().padStart(2, '0');
	}

function showAllButton()
{
	var quaterValue = $("#historyMonth").val();
	var yearValue = $("#historyYear").val();
	if(showbuttonflag==0 && (quaterValue != "SELECT" && yearValue != "SELECT")){
		
		$("#vendorButton").show();
		$("#variantButton").show();
		if(quaterValue =="02" || quaterValue =="03" ){  
			$("#adjustButton").show();
		}
		else
		{
			$("#adjustButton").hide();
		}

	}
}

function resetAction1() {
	showbuttonflag=0
	document.getElementById("historyMonth").options[0].selected = true;
	document.getElementById("historyYear").options[0].selected = true;
    $("#vendorButton").hide();
	$("#variantButton").hide();
	$("#adjustButton").hide();
	$("#variantDiv").hide();
	$("#vendorDiv").hide();
	$("#errhistoryYear").find('.error').html('');
	$("#errhistoryMonth").find('.error').html('');
	$("#errvendorId").find('.error').html('');
	$("#errcardVariant").find('.error').html('');
	$("#errcardType").find('.error').html('');
	$("#cardVariant").val("");
	$("#cardType").val("");
	$("#vendorId").val("");
var datainfo;
     if (typeof hidenDiv != "undefined") {
	      datainfo = document.getElementById('hidenDiv'); 
		 datainfo.style.display = 'none';
		 

	    $("#hidenDiv").hide();
	    $("#byVendor").hide();

	 }	 
     if (typeof exportdatainfo != "undefined") {
	      datainfo = document.getElementById('exportdatainfo'); 
		 datainfo.style.display = 'none';

		
	    $("#byCardTypeM").hide();
	    $("#byCardTypeB").hide();
		 
	 }	 
	
	
}



function showControl(type){

	document.getElementById('showControl1').value = type;
	if(type == 'vendor'){
		feeReportName = 'Net Premium Payable/Receivable';
		$("#vendorDiv").show();
	    $("#variantDiv").hide();
	}
	if(type == 'variant'){
		feeReportName = 'Historical Premium Calculation';
		$("#vendorDiv").hide();
		$("#variantDiv").show();
	}
	if(type == 'adjust'){
		feeReportName = 'Historical Adjustment Calculation';
		$("#vendorDiv").hide();
		$("#variantDiv").show();
	}
	
	
}


function viewTotalPremium(action) {
	
	var url = action;
	var vendorId = document.getElementById("vendorId").value;
	var historyMonth = document.getElementById("historyMonth").value;
	var historyYear = document.getElementById("historyYear").value;
	
	var data = "type," + type + ",vendorId," + vendorId + "historyMonth," + historyMonth + "historyYear," + historyYear  + ",status,"
		+ status;
	
	postData(url, data);				

	} 





function viewTotalHistoricalPremium() {
	
	var url;
	var type = document.getElementById('showControl1').value;
	
	var isValid = true;
	if(type == 'vendor'){
		if (!validateField('vendorId', true, "Number", 3, false) && isValid) {
		      isValid = false;
		}
	}
	isValid = validateVariant(type, isValid);
	
	
	if (!validateField('historyMonth', true, "Alphanumeric", 100, false) && isValid) {
		  isValid = false;
	}
	if (!validateField('historyYear', true, "Number", 100, false) && isValid) {
		 isValid = false;
	}
	
	
	if(isValid)  {
	var vendorId = document.getElementById("vendorId").value;
	var cardVariant = document.getElementById("cardVariant").value;
	var cardType = document.getElementById("cardType").value;
	var historyMonth = document.getElementById("historyMonth").value;
	var historyYear = document.getElementById("historyYear").value;
	var data = "type," + type + ",vendorId," + vendorId + ",historyMonth," + historyMonth + ",historyYear," + historyYear + ",cardVariant," + cardVariant + ",cardType," + cardType  + ",status,"
		+ status;
	if(type =='vendor'){
		 url = '/getTotalHistoricalPremiumByVendor';
	}
	else if(type =='variant'){
	     url = '/getTotalHistoricalPremiumByVariant';
	}
	else if(type =='adjust'){
	     url = '/getHistoricalAdjustmentByVariant';
	}
	
	postData(url, data);
	} 
	}

function validateVariant(type, isValid) {
    if (type == 'variant') {

        if (!validateField('cardVariant', true, "Alphanumeric", 100, false) && isValid) {
            isValid = false;
        }
        if (!validateField('cardType', true, "Alphanumeric", 100, false) && isValid) {
            isValid = false;
        }
    }
    return isValid;
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength) {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if (isMandatory && fieldValue.trim() == "") {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
    	isValid = false;
    }
    if (fieldType == "Number" && fieldValue < 0) {
    	isValid = false;
    }
    if (fieldValue == "SELECT") {
        isValid = false;
    }
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
    	if(historicalPremiumValidationMessages[fieldId]){
    		$("#err" + fieldId).find('.error').html(historicalPremiumValidationMessages[fieldId]);
    	}
        $("#err" + fieldId).show();
    }
    
    return isValid;
}
