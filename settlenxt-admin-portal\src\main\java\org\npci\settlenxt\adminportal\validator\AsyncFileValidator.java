package org.npci.settlenxt.adminportal.validator;

import java.util.concurrent.Callable;

import org.npci.settlenxt.adminportal.validator.service.IValidationService;
import org.npci.settlenxt.portal.common.dto.CashBackFileUploadDTO;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;

public class AsyncFileValidator  implements Callable<String> {
	
	private final IValidationService validationService;
	
	private final FileUploadDTO fileUploadDTO;
	
	private final CashBackFileUploadDTO cashbackFileUploadDTO;
	
	public AsyncFileValidator(IValidationService validationService, FileUploadDTO fileUploadDTO, CashBackFileUploadDTO cashbackFileUploadDTO) {
		super();
		this.validationService = validationService;
		this.fileUploadDTO = fileUploadDTO;
		this.cashbackFileUploadDTO = cashbackFileUploadDTO;
	}
	
	@Override
	public String call() throws Exception {
		if (this.fileUploadDTO != null) {
			return validationService.validateFile(this.fileUploadDTO).toString();
		}
		if (this.cashbackFileUploadDTO != null) {
			return validationService.parseAndSplitFile(this.cashbackFileUploadDTO);
		}
		return null;
	}


}
