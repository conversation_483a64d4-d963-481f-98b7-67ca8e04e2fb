<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.CashbackFileUploadRepository">
	<insert id="saveFile">
		INSERT INTO
		CASHBACK_FILE_UPLOAD_STATUS(FILE_NAME,FILE_PATH,YEAR,MONTH,SIZE,SITE,INSTANCE,CREATED_BY,STATUS)
		VALUES(
		#{fileName},#{fileLocation},#{year},#{month},#{fileSize},#{siteId},#{instanceId},#{createdBy},#{status})
	</insert>

	<select id="getFileUploadByYearMonth"
		resultType="CashBackFileUploadDTO">
		SELECT FILE_NAME as fileName, FILE_PATH as fileLocation,
		SITE as siteId, INSTANCE as instanceId , STATUS as status FROM
		CASHBACK_FILE_UPLOAD_STATUS WHERE STATUS
		IN('C','P','S') and YEAR =
		#{year} and MONTH = #{month}
	</select>

	<select id="getAllCashBackFiles"
		resultType="CashBackFileUploadDTO">
		SELECT FILE_ID as fileId, FILE_NAME as fileName, FILE_PATH
		as fileLocation, YEAR as year, MONTH
		as month, CREATED_BY as createdBy,
		CREATED_ON as createdOn,
		SIZE as fileSize,
		SITE as siteId, INSTANCE as
		instanceId , STATUS as
		status FROM
		CASHBACK_FILE_UPLOAD_STATUS
	</select>

	<select id="getPendingCashBackFiles"
		resultType="CashBackFileUploadDTO">
		SELECT FILE_ID as fileId, FILE_NAME as fileName, FILE_PATH
		as fileLocation, YEAR as year, MONTH
		as month, CREATED_BY as createdBy,
		CREATED_ON as createdOn,
		SIZE as fileSize,
		SITE as siteId, INSTANCE as
		instanceId , STATUS as
		status FROM
		CASHBACK_FILE_UPLOAD_STATUS WHERE
		STATUS='P' and SITE=#{site} and INSTANCE=#{instance}
	</select>

	<update id="updateFile">
		UPDATE CASHBACK_FILE_UPLOAD_STATUS SET
		STATUS=#{status} WHERE FILE_ID =
		#{fileId}
	</update>

	<insert id="saveCashBackFile">
		INSERT INTO
		CASHBACK_FILE_UPLOAD(FILE_NAME,FILE_PATH,YEAR,MONTH,SIZE,SITE,INSTANCE,CREATED_BY)
		VALUES(
		#{fileName},#{fileLocation},#{year},#{month},#{fileSize},#{siteId},#{instanceId},#{createdBy})
	</insert>
</mapper>