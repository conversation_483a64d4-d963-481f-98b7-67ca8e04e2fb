<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript"
	src="./static/js/validation/ApproveIFSC.js"></script>
 <c:if test="${ifscDTO.requestState eq 'P'}"> 
	<sec:authorize access="hasAuthority('Approve IFSC')">
		<div class="alert alert-danger appRejMust" role="alert">
			<spring:message code="ifsc.apprejecterror.msg" />
		</div>
		<div class="alert alert-danger remarkMust" role="alert">
			<spring:message code="ifsc.remarksMandatory.msg" />
		</div>
	 </sec:authorize> 
</c:if> 
<form:form onsubmit="removeSpace(this); encodeForm(this);" method="POST"
	id="viewApproveIfsc" modelAttribute="ifscDTO" action="/approveIFSC"
	autocomplete="off">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="ifsc.viewscreen.title" /></span></strong>
		</div>
		<div class="panel-body">
			<form:hidden path="ifscCode" id="ifscCode" />
			<table class="table table-striped infobold" style="font-size: 12px">
			<caption style="display:none;">IFSC</caption>
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
						<td colspan="6"><div class="panel-heading-red clearfix">
								<strong><span class="glyphicon glyphicon-info-sign"></span> <span
									data-i18n="Data"><spring:message
											code="ifsc.requestInformation" /></span></strong>
							</div></td>
						<td></td>
					</tr>
					<tr>
						<td><label><spring:message code="ifsc.requestType" /><span
								style="color: red"></span></label></td>
						<td>${ifscDTO.lastOperation}</td>
						<td><label><spring:message code="ifsc.requestDate" /><span
								style="color: red"></span></label></td>
						<td>${ifscDTO.lastUpdatedOn}</td>
						<td><label><spring:message code="ifsc.requestStatus" /><span
								style="color: red"></span></label></td>
						<td><c:if test="${ifscDTO.requestState=='A' }">
								<spring:message code="ifsc.requestState.approved.description" />
							</c:if> <c:if test="${ifscDTO.requestState=='P' }">
								<spring:message
									code="ifsc.requestState.pendingApproval.description" />
							</c:if> <c:if test="${ifscDTO.requestState=='R' }">
								<spring:message code="ifsc.requestState.rejected.description" />
							</c:if> <c:if test="${ifscDTO.requestState=='D' }">
								<spring:message code="ifsc.requestState.discarded.description" />
							</c:if> &nbsp;</td>
						<td></td>



					</tr>
					<tr>
						<td><label><spring:message code="ifsc.requestBy" /><span
								style="color: red"></span></label></td>
						<td>${ifscDTO.lastUpdatedBy}</td>

						<td><label><spring:message
									code="ifsc.approverComments" /><span style="color: red"></span></label></td>
						<td colspan=2>${ifscDTO.checkerComments}</td>
						<td></td>
						<td></td>

					</tr>
				</tbody>
			</table>
			<table class="table table-striped infobold" style="font-size: 12px">
			<caption style="display:none;">IFSC</caption>
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
						<td colspan="6">
							<div class="panel-heading-red clearfix">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="ifsc.viewscreen.title" /></span></strong>
							</div>
						</td>

						<td></td>
						<td></td>

					</tr>

					<tr>
						<td><label><spring:message code="ifsc.ifscCode" /><span
								style="color: red"></span></label></td>
						<td>${ifscDTO.ifscCode }</td>
						<td><label><spring:message code="ifsc.bankName" /><span
								style="color: red"></span></label></td>
						<td>${ifscDTO.ifscDescription }</td>
						<td><label><spring:message code="ifsc.bankCode" /><span
								style="color: red"></span></label></td>
						<td>${ifscDTO.bankCode}</td>
						<td><label><spring:message code="ifsc.status" /><span
								style="color: red"></span></label></td>
						<td><c:if test="${ifscDTO.status=='A' }">
								<spring:message code="ifsc.activeStatus" />
							</c:if> <c:if test="${ifscDTO.status=='I' }">
								<spring:message code="ifsc.inactiveStatus" />
							</c:if></td>

					</tr>
					<tr>
						<td><label><spring:message code="ifsc.nfsCode" /><span
								style="color: red"></span></label></td>
						<td>${ifscDTO.nfsCode }</td>
						<td><label><spring:message code="ifsc.savingsAccId" /><span
								style="color: red"></span></label></td>
						<td>${ifscDTO.savingsAccId }</td>
						<td><label><spring:message code="ifsc.currAccId" /><span
								style="color: red"></span></label></td>
						<td>${ifscDTO.currAccId}</td>
						<td><label><spring:message code="ifsc.rtgsAccId" /><span
								style="color: red"></span></label></td>
						<td>${ifscDTO.rtgsAccId}</td>
					</tr>
			</table>
			<sec:authorize access="hasAuthority('Approve IFSC')">
				<c:if test="${ifscDTO.requestState eq 'P'}">
					<table class="table table-striped infobold" style="font-size: 12px">
					<caption style="display:none;">IFSC</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
						<tbody>
							<tr>
								<td colspan="6"><div class="panel-heading-red  clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> <span
											data-i18n="Data"><spring:message
													code="ifsc.approvalPanel.title" /></span></strong>
									</div></td>
							</tr>
							<tr>
								<td><label><spring:message
											code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
								<td><select name="select" id="apprej" onchange="display()">
										<option value="N"><spring:message
												code="AM.lbl.select" /></option>
										<option value="A" id="approve"><spring:message
												code="AM.lbl.approve" /></option>
										<option value="R" id="reject"><spring:message
												code="AM.lbl.reject" /></option>
								</select></td>
								<td>
									<div style="text-align:center">
										<label><spring:message code="AM.lbl.remarks" /><span
											style="color: red">*</span></label>
									</div>
								</td>
								<td colspan="5"><textarea rows="4" cols="50"
										maxlength="100" id="rejectReason"></textarea>
									<div id="errorrejectReason" class="error"></div></td>
							</tr>
						</tbody>
					</table>
				</c:if>
			</sec:authorize>

			<div class="row">
				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center">
						<sec:authorize access="hasAuthority('Approve IFSC')">
							<c:if test="${ifscDTO.requestState eq 'P'}">
								<input name="button10" type="button" class="btn btn-success"
									id="approveRole"
									value="<spring:message
							code="ifsc.submitBtn" />"
									onclick="postAction('/approveIFSC?landing=${landing}');" />
							</c:if>
						</sec:authorize>
						<sec:authorize access="hasAuthority('Edit IFSC')">
							<c:if
								test="${ifscDTO.requestState eq 'R' and ViewApproveBaseScreen eq 'Y' }">
								<input name="discardButton" type="button" class="btn btn-danger"
									id="approveRole"
									value="<spring:message
							code="ifsc.discardBtn" />"
									onclick="userAction('/discardIFSC?landing=${landing}');" />
								<input name="editButton" type="button" class="btn btn-success"
									id="approveRole"
									value="<spring:message
							code="ifsc.editBtn" />"
									onclick="userAction('/editIFSC?landing=${landing}');" />
							</c:if>
						</sec:authorize>
						<button type="button" class="btn btn-danger"
							onclick="userAction('/ifscPendingForApproval');">
							<spring:message code="ifsc.backBtn" />
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</form:form>
