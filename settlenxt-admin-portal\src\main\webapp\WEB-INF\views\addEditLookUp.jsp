<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>

<head>
<title><spring:message code="am.lbl.title" /></title>
<meta http-equiv="Cache-control" content="no-cache" />
<meta http-equiv="Cache-control" content="no-store" />
<meta http-equiv="Expires" content="0" />
<meta http-equiv="pragma" content="no-cache" />

<script src="./static/js/selectize.min.js"></script>
<link rel="stylesheet" href="./static/css/selectize.bootstrap3.min.css" />
<script type="text/javascript">
	var validationMessages = {};
	validationMessages['lkpValue'] = "<spring:message code='lookup.lkpValue.validation.msg' javaScriptEscape='true' />";
	validationMessages['lkpDesc'] = "<spring:message code='lookup.lkpDesc.validation.msg' javaScriptEscape='true' />";
	validationMessages['lkpType'] = "<spring:message code='lookup.lkpType.validation.msg' javaScriptEscape='true' />";
	validationMessages['lkpType1'] = "<spring:message code='lookup.lkpType1.validation.msg' javaScriptEscape='true' />";
	</script>

<script type="text/javascript"
	src="./static/js/validation/commonValidation.js"></script>
<script src="./static/js/validation/addEditLookUp.js"
	type="text/javascript"></script>
</head>

			<div id="errorStatus2" style="display:none;" class="alert alert-danger" role="alert">${errorStatus}</div>
		
<div class="modal fade" id="toggleModalDiscardActionCode" tabindex="-1"
	role="dialog" aria-labelledby="toggleModalDiscardActionCode"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to discard?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">NO</button>
				<button type="button" class="btn btn-primary"
					onclick="userAction('/discardLookUp');">YES</button>
			</div>
		</div>
	</div>
</div>

<div class="container-fluid height-min">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="sm.lbl.lkpConfig" /></span></strong>
		</div>
		<c:if test="${not empty addLookUp}">
			<c:url value="submitLookUpDetails" var="submitLookUpDetails" />
		</c:if>
		<c:if test="${not empty editLookUp}">
			<c:url value="updateLookUpDetails" var="submitLookUpDetails" />
		</c:if>
		<div class="panel-body">
			<form:form onsubmit="removeSpace(this); encodeForm(this);"
				method="POST" id="addEditLookUp" modelAttribute="lookUpDTO"
				action="${submitLookUpDetails}" autocomplete="off">


<input type="hidden" id="showbutton" value="${showbutton}" />
<input type="hidden" id="editLookUp" value="${editLookUp}" />
<input type="hidden" id="editFlow" value="${editLookUp}" />
<input type="hidden" id="addFlow" value="${addLookUp}" />

<input type="hidden" id="lkpValueEdit" value="${lookUpDTO.lkpValue}" />
<input type="hidden" id="lkpDescEdit" value="${lookUpDTO.lkpDesc}" />
<input type="hidden" id="lkpTypeEdit" value="${lookUpDTO.lkpType}" />


				<br />
				<div class="row">
					<div class="col-sm-12">
						<form:hidden path="lookupId" id="lookupId" />

						<div class="col-sm-3">
							<div class="form-group">

								<label><spring:message code="sm.lbl.lkpType1" /><span
									style="color: red">*</span></label>


								<c:if test="${addLookUp eq 'Yes'}">
								<c:if test="${empty showbutton}">
									<form:select path="lkpType" id="lkpType"
										name="lkpType" class="form-control"
										>
										<form:option value="SELECT">
											<spring:message code="msg.lbl.select"></spring:message>
										</form:option>
										<form:option value="Others">
											Other
										</form:option>
										<form:options itemLabel="lkpType"
											itemValue="lkpType" items="${lookUpType}" />
									</form:select>
									</c:if>
									<c:if test="${not empty showbutton}">
									<form:input path="lkpType" id="lkpType"
										 name="lkpType" maxlength="30"
										cssClass="form-control medantory"
										/>
										</c:if>
							
								</c:if>

								<c:if test="${editLookUp eq 'Yes'}">
									<form:input path="lkpType" id="lkpType" name="lkpType"
										maxlength="30" cssClass="form-control medantory"
										readonly="true" />
								</c:if>

								<div id="errlkpType">
									<span for="lkpType" class="error"><form:errors
											path="lkpType" /></span>
								</div>

							</div>
						</div>
							<div id="lookUpTypeDiv" style="display:none;">
						<div class="col-sm-3">
							<div class="form-group">
							<label><spring:message code="sm.lbl.lkpType1" /><span
									style="color: red">*</span></label>

									<form:input path="lkpType" id="lkpType1"
										 name="lkpType1" maxlength="30"
										cssClass="form-control medantory"
										/>
										<div id="errlkpType1">
									<span for="lkpType" class="error"><form:errors
											path="lkpType" /></span>
								</div>
										</div>
										</div>
										</div>
						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="sm.lbl.lkpValue" /><span
									style="color: red">*</span></label>
								
								
									<form:input path="lkpValue" id="lkpValue" name="lkpValue"
										maxlength="10" cssClass="form-control medantory" />
								
								<div id="errlkpValue">
									<span for="lkpValue" class="error"><form:errors
											path="lkpValue" /></span>
								</div>
							</div>
						</div>
						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="sm.lbl.lkpDesc" /><span
									style="color: red">*</span></label>
								<form:input path="lkpDesc" id="lkpDesc" name="lkpDesc"
									maxlength="100" cssClass="form-control medantory"
									 />
								<div id="errlkpDesc">
									<span for="lkpDesc" class="error"> <form:errors
											path="lkpDesc" /></span>
								</div>
							</div>
						</div>
						
					</div>
				</div>
				<div class="row">
				<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="sm.lbl.lkpStatus" /><span
									style="color: red">*</span> </label>

								<form:select id="status" name="status" path="status"
									class="form-control medantory">
									<form:option label="Active" value="A" />
									<form:option label="Inactive " value="I" />
								</form:select>
								<div id="errstatus">
									<span for="status" class="error"><form:errors
											path="status" /></span>
								</div>
							</div>
						</div>
						</div>
				<div class="row">
					<div class="col-sm-12 bottom_space">

						<div style="text-align:center">
							<c:if test="${editLookUp eq 'Yes'}">
							<c:if test="${empty showbutton}">
								<sec:authorize access="hasAuthority('Edit LookUp')">
								<input type="button" class="btn btn-success"
										onclick="addEdit('E','/updateLookUpDetails')" id="submitButton"
										value="<spring:message code="action.lbl.update" />" />
								
									
								</sec:authorize>
								</c:if>
							</c:if>
<c:if test="${addLookUp eq 'Yes'}">
							<c:if test="${empty showbutton}">
								<button type="button" class="btn btn-success" id="lookupForm"
									onclick="validateAddEditLookup('A');">
									<spring:message code="msg.lbl.submit" />
								</button>
							</c:if>
							</c:if>

							<sec:authorize access="hasAuthority('Edit LookUp')">
								<c:if test="${lookUpDTO.requestState eq 'R' }">
									<button type="button" class="btn btn-danger"
										data-toggle="modal"
										data-target="#toggleModalDiscardActionCode">
										<spring:message code="ifsc.discardBtn" />
									</button>
								</c:if>
							</sec:authorize>
	<c:if test="${addLookUp eq 'Yes'}">
								<sec:authorize access="hasAuthority('Add LookUp')">
									<input type="button" id="clearLookUp" value="Clear"
										class="btn btn-success"></input>
								
								</sec:authorize>
							</c:if>
							<button type="button" class="btn btn-danger"
								onclick="submitFormMain('/getLookUpList');">
								<spring:message code="ifsc.backBtn" />
							</button>


						</div>
					</div>
				</div>
			</form:form>
		</div>
	</div>

</div>
