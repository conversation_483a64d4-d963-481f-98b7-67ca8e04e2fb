package org.npci.settlenxt.adminportal.service;

import java.util.ArrayList;
import java.util.Collections;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.DateUtils;
import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.gateway.RestGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

/**
 * Service Class used for
 * <li>View Internal cycle status</li>
 * <li>Reprocess Internal cycle status</li>
 * 
 * <AUTHOR>
 *
 */
@Service
@PropertySource("/application-dev.properties")
public class ReprocessInternalCycleServiceImpl implements IReprocessInternalCycleService {

	private static final Logger logger = LogManager.getLogger(ReprocessInternalCycleServiceImpl.class);

	@Autowired
	private Environment environment;

	@Autowired
	RestGateway restGateway;

	/**
	 * This method is used to View Internal cycle status
	 */
	@Override
	public CycleManagementDTO getInternalCycleData(CycleManagementDTO cycleManagementDTO) {
		logger.info("getInternalCycleData : in");
		if (StringUtils.isBlank(cycleManagementDTO.getSystemDate())) {
			String formattedRequestDate = DateUtils.getTodayLocalDate(DateUtils.YYYY_MM_DD);
			cycleManagementDTO.setSystemDate(formattedRequestDate);
		}
		try {
			JsonObject requestBody = new JsonObject();
			requestBody.addProperty(CommonConstants.SYSTEM_DATE, cycleManagementDTO.getSystemDate());
			requestBody.addProperty(CommonConstants.ICN_TYPE, CommonConstants.REPROCESS_ICN);
			String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
					+ CommonConstants.SETTLENXT_FETCH_REPORCESS_ICN_DATA;
			List<Map<String, String>> cycleDetails = new ArrayList<>();
			String result = restGateway.postAPIForJsonObject(url, requestBody);
			if (StringUtils.isBlank(result)) {
				cycleManagementDTO.setCycleData(cycleDetails);
				return cycleManagementDTO;
			}
			JsonObject respBody = (JsonObject) JsonParser.parseString(result);
			JsonArray cycleData = respBody.getAsJsonArray(CommonConstants.DEFFERED_INTERNAL_CYCLE);
			cycleDetails = new Gson().fromJson(cycleData, ArrayList.class);
			
			Collections.sort(cycleDetails, (o1,o2)->o1.get(CommonConstants.VIEW_PRIORITY).compareTo(o2.get(CommonConstants.VIEW_PRIORITY)));
			
			cycleManagementDTO.setCycleData(cycleDetails);
			
		} catch (Exception e1) {
			logger.error("Error occured while getting report cycle status {}", e1.getMessage(), e1);
		}
		logger.info("getInternalCycleData : out");
		return cycleManagementDTO;
	}

	/**
	 * This method is used for reprocess the internal cycle for force close and
	 * force merge
	 */
	@Override
	public String reprocessInternalCycleData(String requestBody) {
		logger.info("reprocessInternalCycleData : in");
		String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
				+ CommonConstants.SETTLENXT_REPORCESS_ICN_DATA;
		
		String result = restGateway.postDataToURL(url, requestBody);
		
		logger.info("reprocessInternalCycleData : out");
		return result;
	}

}
