$(document).ready(function() {
$('#dateAction').multiselect({
		buttonWidth: '280px',
		paddingLeft: '0px',
		paddingRight: '15px',
		nonSelectedText: 'Select Date Action',
		includeSelectAllOption: true,
		enableFiltering: true,
	});
	$('#feeType').on('change input', function() {
		if ($(this).val() == "MF" || $(this).val() == "CC" || $(this).val() == "PN" || $(this).val() == "FF") {
			$('#daystobewaivedId').show();
			$('#waiverDayTypeId').show();
			$('#penaltyDayTypeId').show();
			$('#compoundFeeId').show();
			$('#dailyFeeId').show();
			$('#impactToId').show();
			$('#multiDateAction').next().show();
			$('#multiDateAction').show();
			$("#dateAction").multiselect("clearSelection");
			$('#dailyFee').val("");
			$('#impactTo').val("");
			$('#daystobewaived').val("");
			$('#waiverDayType').val("");
			$('#penaltyDayType').val("");
			$('#compoundFee').val("");
		} else {
			$('#daystobewaivedId').hide();
			$('#waiverDayTypeId').hide();
			$('#penaltyDayTypeId').hide();
			$('#compoundFeeId').hide();
			$('#dailyFeeId').hide();
			$('#impactToId').hide();
			$('#dailyFee').val("")
			$('#impactTo').val("")
			$('#multiDateAction').next().hide();
			$('#multiDateAction').hide();
			$("#dateAction").multiselect("clearSelection");
 			$("#dateAction").multiselect( 'refresh' );
			$('#dailyFee').val("");
			$('#impactTo').val("");
			$('#daystobewaived').val("");
			$('#waiverDayType').val("");
			$('#penaltyDayType').val("");
			$('#compoundFee').val("");
		}
		/*else {
			$('#otherFieldDiv').hide();
			$('#otherField').removeAttr('required');
			$('#otherField').removeAttr('data-error');
		  }*/
	});
	
			if ($('#feeType').val() == "MF" || $('#feeType').val() == "CC" || $('#feeType').val() == "PN" || $('#feeType').val() == "FF") {
			console.log($(this).val());
			$('#daystobewaivedId').show();
			$('#waiverDayTypeId').show();
			$('#penaltyDayTypeId').show();
			$('#compoundFeeId').show();
			$('#dailyFeeId').show();
			$('#impactToId').show();
			$('#multiDateAction').next().show();
			$('#multiDateAction').show();
			$("#dateAction").multiselect("clearSelection");
 			$("#dateAction").multiselect( 'refresh' );
		
			
		} else {
			$('#daystobewaivedId').hide();
			$('#waiverDayTypeId').hide();
			$('#penaltyDayTypeId').hide();
			$('#compoundFeeId').hide();
			$('#dailyFeeId').hide();
			$('#impactToId').hide();
			$('#multiDateAction').next().hide();
			$('#multiDateAction').hide();
			$("#dateAction").multiselect("clearSelection");
 			$("#dateAction").multiselect( 'refresh' );
			
		}
		/*else {
			$('#otherFieldDiv').hide();
			$('#otherField').removeAttr('required');
			$('#otherField').removeAttr('data-error');
		  }*/
			 
	$('#feeTypCode').on('keyup keypress blur change', function() {
		validateFromCommonVal('feeTypCode', true, "NumberOnly", 4, true);
	});

	$('#feeType').on('keyup keypress blur change', function() {
		validateFromCommonVal('feeType', true, "SelectionBox", 4, false);
	});

$('#daystobewaived').on('keyup keypress blur change', function() {
if($("#daystobewaived").val()!= 0){
		validateFromCommonVal('daystobewaived', false, "NumberOnly", 4, true);
		}
	});
	$('#feeCode').on('keyup keypress blur change', function() {
		validateFromCommonVal('feeCode', true, "NumberOnly", 4, true);
	});

	$('#feeDesc').on('keyup keypress blur change', function() {
		validateFromCommonVal('feeDesc', true, "streetaddress", 100, false);
	});

	$('#txnCurrency').on('keyup keypress blur change', function() {
		validateFromCommonVal('txnCurrency', true, "SelectionBox", 4, false);
	});

	
	$('#cashFeeFlat').on('keyup keypress blur change', function() {
		validateFromCommonVal('cashFeeFlat', true, "NumericwithPrecision", 7, false);
	});

	$('#cashFeePercent').on('keyup keypress blur change', function() {
		validateFromCommonVal('cashFeePercent', true, "NumericwithPrecisionwith0From2to3", 6, false);
	});

	$('#cashFeeMin').on('keyup keypress blur change', function() {
		validateFromCommonVal('cashFeeMin', true, "NumericwithPrecision", 7, false);
	});

	$('#cashFeeMax').on('keyup keypress blur change', function() {
		validateFromCommonVal('cashFeeMax', true, "NumericwithPrecision", 7, false);
	});

	$('#purFeeFlat').on('keyup keypress blur change', function() {
		validateFromCommonVal('purFeeFlat', true, "NumericwithPrecision", 7, false);
	});

	$('#purFeePercent').on('keyup keypress blur change', function() {
		validateFromCommonVal('purFeePercent', true, "NumericwithPrecisionwith0From2to3", 6, false);
	});

	$('#purFeeMin').on('keyup keypress blur change', function() {
		validateFromCommonVal('purFeeMin', true, "NumericwithPrecision", 7, false);
	});

	$('#purFeeMax').on('keyup keypress blur change', function() {
		validateFromCommonVal('purFeeMax', true, "NumericwithPrecision", 7, false);
	});

	$('#netMin').on('keyup keypress blur change', function() {
		validateFromCommonVal('netMin', true, "NumericwithPrecision", 10, false);
	});

	$('#netMax').on('keyup keypress blur change', function() {
		validateFromCommonVal('netMax', true, "NumericwithPrecision", 10, false);
	});

	$('#creditTo').on('keyup keypress blur change', function() {
		
		validateFromCommonVal('creditTo', true, "SelectionBox", 7, false);
			var creditTo = document.getElementById("creditTo").value;
	var debitTo = document.getElementById("debitTo").value;
		if (creditTo != 'SELECT' && debitTo != 'SELECT'){
		validateCreditDebit('errdebitTo');
		}
		
	});

	$('#debitTo').on('keyup keypress blur change', function() {
		
		validateFromCommonVal('debitTo', true, "SelectionBox", 7, false);
			var creditTo = document.getElementById("creditTo").value;
	var debitTo = document.getElementById("debitTo").value;
			if (creditTo != 'SELECT' && debitTo != 'SELECT'){
			validateCreditDebit('errdebitTo');
		}
	});

	
if(document.getElementById("hdateAction")!=null){
					var y = document.getElementById("hdateAction").value;
											             var dataarray="";
											if(y!=null){
								             dataarray=y.split(",");
								        	 
								        	  }
											$('#dateAction').multiselect('select', dataarray);
											 $('#dateAction').multiselect('refresh');
											}	


	if ($('#editFlow').val() == 'Y' || $('#showButton').val() == "YES") {
		var value1 = document.getElementById('revCashes').value;

		if (value1 == 'Y') {
			$('#revCash').prop('checked', true);
		}
	}


	$('form')
		.each(function() {
			$(this).data('serialized', $(this).serialize())
		})
		.on('change input', function() {
			$(this)
				.find('input:submit, button:submit')
				.prop('disabled', $(this).serialize() == $(this).data('serialized'))
				;
			$('#submitButton').prop('disabled', false);
			

		})
	$('#submitButton').prop('disabled', true);

	if ($('#addFlow').val() == 'Y') {
		if (($('#showButton').val() == "YES")) {
			$("input").prop('disabled', true);
			$("select").prop('disabled', true);
			$('#dateAction').attr("disabled", true); 
		}
		else {
			document.querySelectorAll(".numeric").forEach(element => (element.value = 0));
		}

	}
	
	if ($('#editFlow').val() == 'Y') {
		if (($('#showButton').val() == "YES")) {
			$("input").prop('disabled', true);
			$("select").prop('disabled', true);
			$('#dateAction').attr("disabled", true); 
		}
		

	}


	let today = new Date().toISOString().substr(0, 10);
	let defaults = "2099-12-31";


	if ($('#addFlow').val() == 'Y') {
		if (($('#showButton').val() == "YES")) {
			$("#validToDt").datepicker({
				dateFormat: "yy-mm-dd",
				changeMonth: true,
				changeYear: true,
				yearRange: "2022:2099",
				minDate: 0,
				
				
				
			});


			$("#validFromDt").datepicker({
				dateFormat: "yy-mm-dd",
				changeMonth: true,
				changeYear: true,
				yearRange: "2022:2099",
				minDate: 0,
				
				
			});

		}
		else {

			$("#validToDt").datepicker({
				dateFormat: "yy-mm-dd",
				changeMonth: true,
				changeYear: true,
				yearRange: "2022:2099",
				  minDate: "dateToday",
				
			}).datepicker("setDate", defaults);


			$("#validFromDt").datepicker({
				dateFormat: "yy-mm-dd",
				changeMonth: true,
				changeYear: true,
				yearRange: "2022:2099",
				  minDate: "dateToday",
				 onClose: function (selectedDate, _instance) {
            if (selectedDate != '') { //added this to fix the issue               
           
             
                $("#validToDt").datepicker("option", "minDate", selectedDate);
                $("#validToDt").datepicker("option", "maxDate", defaults);
            }
				}
			}).datepicker("setDate", today);

		}

	}


	if ($('#editFlow').val() == 'Y') {
		
		
		$("#validFromDt").datepicker({
			dateFormat: "yy-mm-dd",
			changeMonth: true,
			changeYear: true,
			yearRange: "2022:2099",
			 minDate: "dateToday",
			 onClose: function (selectedDate, _instance) {
		            if (selectedDate != '') { //added this to fix the issue  
		            console.log("i");             
		                $("#validToDt").datepicker("option", "minDate", selectedDate);
		                $("#validToDt").datepicker("option", "maxDate", defaults);
		                
		            }
						}
		});

		$("#validToDt").datepicker({
			dateFormat: "yy-mm-dd",
			changeMonth: true,
			changeYear: true,
			yearRange: "2022:2099",
			 minDate: "dateToday",
			
		}).datepicker("setDate", defaults);
		
		
	}


	if (($('#showButton').val() == "YES")) {
		$("input").prop('disabled', true);
		$("select").prop('disabled', true);

	}


	if ($('#feeType').val() == "0001") {

		document.getElementById('multiplier').required = true;
		document.querySelector(".require").style.display = "initial";

	} else {

		if ($('#showButton').val() != "YES") {



			if ($('#editFlow').val() != "Y") {

				document.getElementById('multiplier').value = 0;
			}
		}
		document.getElementById('multiplier').required = false;
		document.querySelector(".require").style.display = "none";

	}

	$("#feeType").change(function(e) {
		if (e.target.value == "0001") {
			document.getElementById('multiplier').required = true;
			document.querySelector(".require").style.display = "initial";

		}
		else {
			document.getElementById('multiplier').value = 0;
			document.getElementById('multiplier').required = false;
			document.querySelector(".require").style.display = "none";
		}
	});

	$('#feeCode').blur(function() {
		if (!validateFromCommonVal('feeCode', true, 'NumberOnly', 4, true)) {

console.log("i");
		} else {
			if ($('#addFlow').val() == 'Y') {
				checkDupFeeCode();
			}
			else if ($('#editFlow').val() == 'Y') {
				if ($('#discardRejec').val() == 'R') {
					var fee = $('#fee').val();
					var fee2 = $('#feeCode').val();
					if (fee != fee2) {
						checkDupFeeCode();
					}
				}
			}
		}

	});




	$('#clearFeeRate').click(function() {

		$(".error").each(function() {
			if ($(this).text().trim().length > 0) {

				$(this).empty();

			}
		});
		$('.jqueryError').hide();
		$('#feeTypCode').val("")
		$('#feeCode').val("")
		$('#feeDesc').val("")
		$('#feeType').val("SELECT")
		$('#txnCurrency').val("SELECT")
		$('#cashFeeFlat').val("0")
		$('#txnCurrency').val("SELECT")
		$('#daystobewaived').val("")
		$('#waiverDayType').val("")
		$('#penaltyDayType').val("")
		$('#compoundFee').val("")
		$('#dailyFee').val("")
		$('#impactTo').val("")
$("#dateAction").multiselect("rebuild");
		$("#dateAction").multiselect("refresh");
		document.getElementById("dateAction").value = "";
		$("#dateAction").multiselect("rebuild");
		$('#cashFeePercent').val("0")
		$('#cashFeeMin').val("0")
		$('#cashFeeMax').val("0")
		$('#purFeeFlat').val("0")
		$('#purFeePercent').val("0")
		$('#purFeeMin').val("0")
		$('#purFeeMax').val("0")
		$('#netMin').val("0")
		$('#netMax').val("0")
		$('#status').val("A")
		$('#multiplier').val("")
		$('#gstCode').val("0")
		$('#revCash').prop('checked', false);
		$('#creditTo').val("SELECT")
		$('#debitTo').val("SELECT")
		document.querySelector("#validFromDt").value = today;

		document.querySelector("#validToDt").value = '2099-12-31';
		document.getElementById("revCash").value = 'N';
	});




});


var ajaxValidFeeCode;

function callBack(flag) {
	ajaxValidFeeCode = flag;
}


function addOrUpdateFee(action) {

	var feedesc1 = document.getElementById("feeDesc").value;
	var feedesc2 = feedesc1.split(",");
	var feedesc = "";
	var dateActionList = "";
	feedesc = feedesc2[0];
	
	
	for (var i = 1; i < feedesc2.length; i++) {
		feedesc = feedesc + "*" + feedesc2[i];

	}
	if ($('#dateAction').val() != "") {

		var a = $('#dateAction option:selected').toArray().map(item => item.value).join();

		var arr1 = a.split(",");


	
		let j=0;
		for(j of arr1){

			dateActionList = dateActionList + j + "|"
				;
		}


	} else {
		 dateActionList = [];
	}
	var revcash = "";
	if ($("#revCash").is(":checked")) {
		revcash = 'Y';

	}
	else {

		revcash = 'N';

	}

 if($("#daystobewaived").val() == ''){
		$("#daystobewaived").val("0");
	}
	var data = "feeTypCode," + $('#feeTypCode').val() + ",feeType," + $('#feeType').val() + ",feeDesc," + feedesc + ",txnCurrency," + $('#txnCurrency').val() +
		",cashFeeFlat," + $('#cashFeeFlat').val() + ",cashFeePercent," + $('#cashFeePercent').val() +
		",cashFeeMin," + $('#cashFeeMin').val() + ",cashFeeMax," + $('#cashFeeMax').val() +
		",purFeeFlat," + $('#purFeeFlat').val() + ",purFeePercent," + $('#purFeePercent').val() + ",purFeeMin," +
		$('#purFeeMin').val() + ",purFeeMax," + $('#purFeeMax').val() + ",multiplier," +
		$('#multiplier').val() + ",gstCode," + $('#gstCode').val() + ",creditTo," + $('#creditTo').val() +
		",debitTo," + $('#debitTo').val() + ",validFromDt," + $('#validFromDt').val() + ",validToDt," + $('#validToDt').val() +
		",reversCashFee," + revcash + ",feeCode," + $('#feeCode').val() + ",status," + $('#status').val() + ",feeId," + $('#feeId').val() + ",netMax," + $('#netMax').val() + ",netMin," + $('#netMin').val() + ",daystobewaived," + $('#daystobewaived').val()  + ",waiverDayType," + $('#waiverDayType').val()  + ",penaltyDayType," + $('#penaltyDayType').val()  + ",dailyFee," + $('#dailyFee').val()  + ",impactTo," + $('#impactTo').val()   + ",dateAction," + dateActionList + ",compoundFee," + $('#compoundFee').val();
console.log(data);


	postData(action, data);

}




function navigateTo(action) {

	var data = "status,"
		+ status
	postData(action, data);
}


function validateCreditDebit(_msgID) {


	var creditTo = document.getElementById("creditTo").value;
	var debitTo = document.getElementById("debitTo").value;
	
	if (creditTo != 'SELECT' && debitTo != 'SELECT'){
		if (creditTo == debitTo) {
			
			$("#errdebitTo").find('.error').html('Credit and Debit parties should not be same');
$('#errdebitTo').show();
			
			return false;

		} else {
		$("#errdebitTo").find('.error').html('');
			$('#errdebitTo').hide();
		}
}
	return true;
}


function checkDupFeeCode() {
	var feeCode = $('#feeCode').val();
	var validfeeCode = false;
	
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	if (feeCode != "") {

		$.ajax({
			url: "checkDupFeeCode",
			type: "POST",
			dataType: "json",
			data: {

				"feeCode": feeCode,
				"_TransactToken": tokenValue
			},
			success: function(response) {

				if (response.status == "BSUC_0001") {
					validfeeCode = true;

					$('#errfeeCode').show();

					$('#errfeeCode').find('.error').html('Fee Code should be Unique');

					callBack(true);




				} else {
					validfeeCode = false;

					$('#errfeeCode').find('.error').html('');
					callBack(false);

				}
			},
			error: function(_request, _status, _error) {
				$('#errfeeCode').find('.error').html('');
				callBack(false);
			}
		});

	} else {
		validfeeCode = false;
	}

	return validfeeCode;
}




function validateMultiplier(_msgID) {
	var multiplier = (document.getElementById("multiplier").value).replace(
		"/^\s*|\s*$/g", '');

	

	var regEx = /^\d\d?(?:\.\d{1,4})?$/;
	var feeType = document.getElementById("feeType").value;

	var flag = true;
	if (feeType == '0001' && multiplier == "") {
		$("#errmultiplier").find('.error').html('Multiplier is Mandatory');
		$("#errmultiplier").show();

		return false;
	}
	else if (feeType != '0001' && (multiplier == "0" || multiplier == "")) {
	
		$("#errmultiplier").find('.error').html('');
		$("#errmultiplier").hide();
return true;

	}
	else if (flag && !regEx.test(multiplier)) {

		$("#errmultiplier").find('.error').html('Please Provide valid Input upto before decimal 2 and after decimal 4');
		$("#errmultiplier").show();
		return false;
	}
	else {
		$("#errmultiplier").find('.error').html('');
		$("#errmultiplier").hide();

	}
	return true;



}

function validateAddFees(action) {


	$('.jqueryError').text("");
	$('.jqueryError').hide();
	var check = false;

	if (!validateFromCommonVal('feeType', true, 'SelectionBox', 4, false)) {
		check = true;
	}
	if (!validateFromCommonVal('feeTypCode', true, 'NumberOnly', 4, true)) {
		check = true;
	}


	if (!validateFromCommonVal('feeCode', true, 'NumberOnly', 4, true)) {
		check = true;

	}

		check = validateFields(check);

	if (!validateFromCommonVal('netMin', true, 'NumericwithPrecision', 10, false)) {
		return false
	}
	if (!validateFromCommonVal('netMax', true, 'NumericwithPrecision', 10, false)) {
		return false
	}


	if (!validateFromCommonVal('feeCode', true, 'NumberOnly', 4, true)) {

		check = true;
	} else {
	var fee = $('#fee').val();
	var fee2 = $('#feeCode').val();
		if (($('#addFlow').val() == 'Y'&&ajaxValidFeeCode) ||($('#editFlow').val() == 'Y'&&$('#discardRejec').val() == 'R'&&fee != fee2&&ajaxValidFeeCode)) {

						check = true;
					
				
			
		}
	}




	if (!validateMultiplier('errmultiplier')) {
		check = true;
	}


	if (!check) {


		addOrUpdateFee(action);
		
	} else {
		return false;
	}
}

function validateFields(check) {

	if (!validateFromCommonVal('daystobewaived', false, 'NumberOnly', 4, true)&&$("#daystobewaived").val() == 0){
		$("#daystobewaived").val(0);
				check = false;
	}
	if (!validateFromCommonVal('feeDesc', true, 'streetaddress', 100, false)) {
		check = true;
	}


	if (!validateFromCommonVal('txnCurrency', true, 'SelectionBox', 4, false)) {
		check = true;
	}

	if (!validateFromCommonVal('cashFeeFlat', true, 'NumericwithPrecision', 7, false)) {
		check = true;
	}


	if (!validateFromCommonVal('cashFeePercent', true, 'NumericwithPrecisionwith0From2to3', 6, false)) {
		check = true;
	}

	if (!validateFromCommonVal('cashFeeMin', true, 'NumericwithPrecision', 7, false)) {

		check = true;
	}


	if (!validateFromCommonVal('cashFeeMax', true, 'NumericwithPrecision', 7, false)) {
		check = true;
	}



	if (!validateFromCommonVal('purFeeFlat', true, 'NumericwithPrecision', 7, false)) {
		check = true;
	}

	if (!validateFromCommonVal('purFeePercent', true, 'NumericwithPrecisionwith0From2to3', 6, false)) {
		check = true;
	}

	if (!validateFromCommonVal('purFeeMin', true, 'NumericwithPrecision', 7, false)) {
		check = true;
	}

	if (!validateFromCommonVal('purFeeMax', true, 'NumericwithPrecision', 7, false)) {
		check = true;
	}

	if (!validateFromCommonVal('creditTo', true, 'SelectionBox', 7, false)) {
		check = true;
	}

	if (!validateFromCommonVal('debitTo', true, 'SelectionBox', 7, false)) {
		check = true;
	}

	if (!validateCreditDebit('errdebitTo')) {

		check = true;
	}
	return check;
}

function userAction(action) {


	var data = "feeId," + $('#feeId').val() + ",status,"
		+ status;
	postData(action, data);
}
