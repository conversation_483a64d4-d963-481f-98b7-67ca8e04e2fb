<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<!DOCTYPE html>


<html>
<head>
<meta charset="ISO-8859-1">
<title>Incoming Transaction Details</title>
<sec:authorize access="hasAuthority('Approve Disputes as Admin Maker')">
	<script type="text/javascript">
		firstColumnToBeSkippedInFilterAndSort = false;
		var dataMap = [];
		<c:if test="${showMakerTab eq 'Y'}">
		firstColumnToBeSkippedInFilterAndSort = true;
		</c:if>
		<c:if test="${showCheckerTab eq 'Y'}">
		firstColumnToBeSkippedInFilterAndSort = false;

		</c:if>
		var actionColumnIndex = 9;
		var skipLevel = 3;
	</script>
</sec:authorize>

<sec:authorize
	access="hasAuthority('Approve Disputes as Admin Checker')">
	<script type="text/javascript">
		var dataMap = [];
		firstColumnToBeSkippedInFilterAndSort = false;
		<c:if test="${showCheckerTab eq 'Y'}">
		firstColumnToBeSkippedInFilterAndSort = true;
		</c:if>
		<c:if test="${showMakerTab eq 'Y'}">
		firstColumnToBeSkippedInFilterAndSort = false;

		</c:if>
		var actionColumnIndex = 9;
		var skipLevel = 2;
	</script>
</sec:authorize>

<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />
<script type="text/javascript"
	src="./static/js/validation/incomingTransactionDetail.js"></script>
<script type="text/javascript" src="./static/js/custom_js/vTransact.js"></script>

<style>
#title {
	color: red;
	font-size: 10px;
	text-indent: 10px;
}

.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: bottom;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: bottom;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: bottom;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>


</head>
<body>
	<div class="container-fluid height-min">
		<div class="row">
			<div class="body-content">
				<div class="">
					<div class="col-md-12">




						<div class="card">
							<div class="card-header">
								<div class="card-title">
									<label for="squareSelect"><spring:message
											code="msg.lbl.incomingTransactionDetails" /><span
										style="color: red; display: none;" class="require">*</span><span
										class="red">*</span> </label>
								</div>
							</div>
							<div class="card-body">
								<form:form onsubmit="removeSpace(this); encodeForm(this);"
									method="POST" id="searchForm" action="" autocomplete="off"
									modelAttribute="incomingTransactionDetailDTO">
									<div id="errMsg" class="error"></div>
									<div class="row">
										<div class="col-md-3">
											<div class="form-group">
												<label for="squareSelect">Outgoing Creation Date<span
													style="color: red; display: none;" class="require">*</span><span
													class="red">*</span>
												</label>
												<form:input type="text" path="outgoingCreatedDate"
													id="outgoingCreatedDate" name="outgoingCreatedDate"
													class="form-control input-square" />
												<div>
													<span id="erroutgoingCreatedDate" class="error"></span>
													<form:errors path="outgoingCreatedDate" class="error" />
												</div>

											</div>
										</div>
										<c:choose>
											<c:when test="${showMakerTab eq 'Y'}">
												<input type="hidden" name="isInput" id="isInput" value="Y">
											</c:when>
											<c:otherwise>
												<input type="hidden" name="isInput" id="isInput" value="N">
											</c:otherwise>
										</c:choose>



										<div class="col-md-3">
											<div class="form-group">
												<label for="squareInput"><spring:message
														code="msg.lbl.schemeCode" /><span
													style="color: red; display: none;" class="require">*</span><span
													class="red">*</span></label>


												<form:select path="schemeCode" id="schemeCode"
													name="schemeCode" maxlength="4"
													cssClass="form-control medantory">
													<form:option value="0" label="SELECT" />
													<form:options items="${subnetList}" itemValue="code"
														itemLabel="description" />
												</form:select>


												<div id="errSchemeCode" class="error" style="color: red"></div>
											</div>
										</div>

										<div class="col-md-3">
											<div class="form-group">
												<label for="squareInput"><spring:message
														code="msg.lbl.funcCodeDesc" /><span
													style="color: red; display: none;" class="require">*</span></label>


												<form:select path="funcCodeDescription"
													id="funcCodeDescription" name="funcCodeDescription"
													maxlength="4" cssClass="form-control medantory">
													<form:option value="0" label="SELECT" />
													<form:options items="${funcCodeList}" itemValue="code"
														itemLabel="description" />
												</form:select>


												<div id="errFuncCodeDescription" class="error"
													style="color: red"></div>
											</div>
										</div>


										<div class="col-md-3">
											<div class="form-group ">
												<label for="squareSelect"><spring:message
														code="msg.lbl.Pan" /></label>


												<form:input path="pan" id="pan" name="pan" maxlength="18"
													cssClass="form-control medantory" />



												<div>
													<span id="errpan" class="error"></span>

												</div>
											</div>
										</div>



									</div>





									<div class="row">
										<div class="col-md-3">
											<div class="form-group ">
												<label for="squareSelect"><spring:message
														code="msg.lbl.rrn" /></label>


												<form:input path="rrn" id="rrn" name="rrn" maxlength="14"
													cssClass="form-control medantory" />



												<div>
													<span id="errrrn" class="error"></span>

												</div>
											</div>
										</div>


									</div>



									<div class="row">
										<div class="col-sm-12 bottom_space">
											<div class="form-group" style="text-align: center">
												<button class="btn btn-primary" type="button"
													id="searchButon" style="margin-top: 25px;"
													onclick="searchData();">
													<spring:message code="msg.lbl.search" />
												</button>
												<button class="btn btn-default" id="resetBtn" type="button">
													<spring:message code="msg.lbl.reset" />
												</button>
											</div>
										</div>
									</div>

								</form:form>
							</div>

						</div>
					</div>
				</div>
				<c:if test="${show eq 'Y'}">


					<div class="space_block" id = "tableDiv">
						<ul class="nav nav-tabs" role="tablist" id="myTab">
							<c:choose>
								<c:when test="${showMakerTab eq 'Y'}">
									<li role="presentation" class="active" >
								</c:when>
								<c:otherwise>
									<li role="presentation" >
								</c:otherwise>
							</c:choose>
							<a href="#home"
								onclick="submitForm('/incomingTransactionDetails','M');"
								role="tab" ><span
								class="glyphicon glyphicon-list-alt"> </span> <spring:message
									code="msg.lbl.maker" /></a>
							</li>

							<c:choose>
								<c:when test="${showCheckerTab eq 'Y'}">
									<li role="presentation" class="active">
								</c:when>
								<c:otherwise>
									<li role="presentation" >
								</c:otherwise>
							</c:choose>
							<a href="#home" role="tab"
								onclick="submitForm('/incomingTransactionDetails','C');"
								><span class="glyphicon glyphicon-ok">
							</span> <spring:message code="msg.lbl.checker" /></a>
						</ul>
							</li>

						<div class="tab-content" id="tableList">
							<sec:authorize
								access="hasAuthority('Approve Disputes as Admin Maker')">
								<c:if test="${showMakerTab eq 'Y'}">
									<div class="row">

										<div class="col-sm-8" style="width: 50%">
											<div class="col-sm-2 "></div>
											<div class="col-sm-6 pull-right">

												<label for="rejectReason"><spring:message
														code="msg.lbl.rejectReason" /></label> <select id="rejectReason"
													name="rejectReason" class="form-control-select"
													style="width: 40%">
													<option value="0">SELECT</option>
													<c:forEach var="item" items="${reasonCodeList}">
														<option value="${item.code}">${item.description}</option>
													</c:forEach>
												</select>


												<div id="errrejectReason" class="error"></div>
											</div>
										</div>

										<div class="col-sm-6">


											<button class="btn  btn-success pull-right btn_align"
												id="approve">
												<spring:message code="msg.lbl.approve" />
											</button>


											&nbsp;
											<button class="btn  btn-danger pull-right btn_align"
												id="reject">
												<spring:message code="msg.lbl.reject" />
											</button>

										</div>

									</div>
								</c:if>
							</sec:authorize>
							<sec:authorize
								access="hasAuthority('Approve Disputes as Admin Checker')">
								<c:if test="${showCheckerTab eq 'Y' }">
									<div class="row">
										<div class="col-sm-8" style="width: 50%">
											<div class="col-sm-2 "></div>
											<div class="col-sm-6 pull-right">

												<label for="rejectReason">Reject Reason</label> <select
													id="rejectReason" name="rejectReason" style="width: 40%">
													<option value="0">SELECT</option>
													<c:forEach var="item" items="${reasonCodeList}">
														<option value="${item.code}">${item.description}</option>
													</c:forEach>
												</select>


												<div id="errrejectReason" class="error"></div>
											</div>
										</div>
										<div class="col-sm-6">
											<button class="btn  btn-success pull-right btn_align"
												id="approveC">
												<spring:message code="msg.lbl.approve" />
											</button>


											&nbsp;
											<button class="btn  btn-danger pull-right btn_align"
												id="rejectC">
												<spring:message code="msg.lbl.reject" />
											</button>

										</div>
									</div>
								</c:if>
							</sec:authorize>

							<div role="tabpanel" class="card-white table-responsive"
								id="home">


								<div class="row">
									<div class="col-sm-12">
										<button class="btn  pull-right btn_align" id="clearFilters">
											<spring:message code="ifsc.clearFiltersBtn" />
										</button>


										&nbsp; <a class="btn btn-success pull-right btn_align"
											href="#" id="excelExport"><spring:message
												code="ifsc.exportBtn" /> </a> &nbsp; <a
											class="btn btn-success pull-right btn_align" href="#"
											id="csvExport"><spring:message code="ifsc.csvBtn" /> </a>

									</div>
								</div>


								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%">
										<caption style="display: none;">TRANSACTION DETAIL
											SEARCH</caption>
										<thead>
											<tr>
												<sec:authorize
													access="hasAuthority('Approve Disputes as Admin Maker')">

													<c:if test="${showMakerTab eq 'Y'}">
														<th><input type="checkbox" id="selectAll"></th>
													</c:if>
												</sec:authorize>
												<sec:authorize
													access="hasAuthority('Approve Disputes as Admin Checker')">

													<c:if test="${showCheckerTab eq 'Y'}">
														<th><input type="checkbox" id="selectAll"></th>
													</c:if>
												</sec:authorize>
												<c:if test="${showMakerTab eq 'Y'}">
													<th><spring:message code="msg.lbl.makerAmount" /></th>
												</c:if>
												<th><spring:message code="msg.lbl.remark" /></th>
												<th scope="col"><spring:message code="msg.lbl.funcCode" /></th>
												<th scope="col"><spring:message
														code="msg.lbl.transactionAmount" /></th>
												<th scope="col"><spring:message
														code="msg.lbl.currentStatus" /></th>
												<th scope="col"><spring:message
														code="msg.lbl.acknowledgementStatus" /></th>
												<th scope="col"><spring:message
														code="msg.lbl.markerStatus" /></th>
												<th scope="col"><spring:message
														code="msg.lbl.reasonCode" /></th>
												<th scope="col"><spring:message code="msg.lbl.Pan" /></th>
												<th scope="col"><spring:message
														code="msg.lbl.transactionTime" /></th>
												<th scope="col"><spring:message
														code="msg.lbl.acquirewrReferenceDataOrRRN" /></th>
												<th scope="col"><spring:message
														code="msg.lbl.approvalCode" /></th>
												<th scope="col"><spring:message code="msg.lbl.dciOrRRN" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="disputeModel" items="${disputeTxnModel}">
												<c:set var="combinedString" value="${disputeModel.txnId}" />



												<c:set var="sanitizedTxnId"
													value="${fn:replace(disputeModel.txnId, ' ', '')}" />
												<c:set var="sanitizedTxnId"
													value="${fn:replace(sanitizedTxnId, '/', '')}" />
												<c:set var="txnId"
													value="${fn:replace(sanitizedTxnId, ':', '')}" />

												<%-- <c:set var="txnId" value="${disputeModel.acqRefData}" /> --%>
												<%-- 	<tr
																	onclick="javascript:viewTransaction('${txnId}')"> --%>


												<sec:authorize
													access="hasAuthority('Approve Disputes as Admin Maker')">
													<c:choose>
														<c:when
															test="${disputeModel.status eq 'PR' && showMakerTab eq 'Y'}">
															<td onclick="event.stopPropagation()"><input
																type="checkbox" name="type" class="selectSingle"
																value="${txnId}"></td>
														</c:when>
														<c:when test="${showCheckerTab eq 'Y'}">

														</c:when>
														<c:otherwise>
															<td></td>
														</c:otherwise>
													</c:choose>
												</sec:authorize>
												<sec:authorize
													access="hasAuthority('Approve Disputes as Admin Checker')">
													<c:choose>
														<c:when
															test="${disputeModel.status == 'RPA' && showCheckerTab eq 'Y' }">
															<td onclick="event.stopPropagation()"><input
																type="checkbox" name="type" class="selectSingle"
																value="${txnId}"></td>
														</c:when>
														<c:when test="${showMakerTab eq 'Y'}">

														</c:when>
														<c:otherwise>
															<td></td>
														</c:otherwise>
													</c:choose>
												</sec:authorize>
												<c:if test="${showMakerTab eq 'Y'}">
													<td><input type="text" name="input1_${txnId}"
														id="input1_${txnId}" value="${disputeModel.amountTran}">

														<div id="errinput1_${txnId}" style="display: none;">
															<span for="input1_${txnId}" class="error"></span>
														</div></td>
												</c:if>
												<c:choose>
													<c:when test="${showMakerTab eq 'Y'}">
														<td><input type="text" name="input2_${txnId}"
															id="input2_${txnId}" value="${disputeModel.memMsgTxt}">


															<div id="errinput2_${txnId}" style="display: none;">
																<span for="input2_${txnId}" class="error"></span>
															</div></td>
													</c:when>
													<c:otherwise>
														<td>${disputeModel.memMsgTxt}</td>
													</c:otherwise>
												</c:choose>

												<td>${disputeModel.orgFuncCode}</td>
												<td>${disputeModel.amountTran}</td>
												<td>${disputeModel.status =='PR'||disputeModel.status =='RPA' ? 'Pending For settlement' : disputeModel.status}</td>

												<td>${(disputeModel.status =='SUCCESS'||disputeModel.status =='FAILED')? disputeModel.status =='SUCCESS'?'SETTLED' : 'NOT SETTLED':'NOT RECEIVED'}</td>

												<td>${disputeModel.status =='RPA' || disputeModel.status == 'PR'?disputeModel.status == 'PR'?'Pending for Maker approval': 'Pending for checker approval' : ''}</td>
												<td>${disputeModel.reasonCode}</td>
												<td>${disputeModel.encryptedPan}</td>
												<td>${disputeModel.dateTimeLocal}</td>
												<td>${disputeModel.rrn}</td>
												<td></td>
												<td></td>


												</tr>
											</c:forEach>
										</tbody>



									</table>

								</div>
							</div>


						</div>
					</div>

				</c:if>
			</div>
		</div>
	</div>
</body>
</html>