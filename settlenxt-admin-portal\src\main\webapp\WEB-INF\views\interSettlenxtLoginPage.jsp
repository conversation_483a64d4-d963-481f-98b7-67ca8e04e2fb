<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<title></title>
<meta http-equiv="Cache-control" content="no-cache" />
<meta http-equiv="Cache-control" content="no-store" />
<meta http-equiv="Expires" content="0" />
<meta http-equiv="pragma" content="no-cache" />

<script type="text/javascript">
urlPostAction("","/settlenxtHome");
	window.history.forward();
	function noBack() { window.history.forward(); }

</script>

</head>

<body onload="noBack(); userAction('/settlenxtHome');">
loading...
 <form:form method="post" id="authLogin" name="authLogin" action=""> 
	</form:form>
</body>
</html>