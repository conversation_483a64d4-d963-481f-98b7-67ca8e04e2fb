$(document).ready(function() {
	
	 $("#cycleDate").datepicker({
		 changeMonth: true,
		dateFormat: 'dd-mm-yy',
		maxDate: 0,
    });		
		
	 $("#prodCode1").change(function(){
		 if ($("#prodCode1").val()== 0){
			 document.querySelector(".prodCode1-error").innerHTML = "Please enter a Product Type";
		      document.querySelector(".prodCode1-error").style.display = "block";
		      event.preventDefault();
		      return false;
		 }
		 if ($("#prodCode1").val()!= 0){
			 document.querySelector(".prodCode1-error").style.display = "none";
		      event.preventDefault();
		      return false;
		 }
			 
	 });
	 
	 $("#cycleNumber1").change(function(){
		 if ($("#cycleNumber1").val()== 0){
			 document.querySelector(".cycleNumber1-error").innerHTML = "Please enter a Cycle Number";
		      document.querySelector(".cycleNumber1-error").style.display = "block";
		      event.preventDefault();
		      return false;
		 }
		 if ($("#cycleNumber1").val()!= 0){
			 document.querySelector(".cycleNumber1-error").style.display = "none";
		      event.preventDefault();
		      return false;
		 }			 
		 
	 });
		
});



function viewData()
{
	if($("#prodCode1").val()==0){
	      document.querySelector(".prodCode1-error").innerHTML = "Please enter a Product Type";
	      document.querySelector(".prodCode1-error").style.display = "block";
	      event.preventDefault();
	      return false;
	   }
	if($("#cycleNumber1").val()==0){
	      document.querySelector(".cycleNumber1-error").innerHTML = "Please enter a Cycle Number";
	      document.querySelector(".cycleNumber1-error").style.display = "block";
	      event.preventDefault();
	      return false;
	   }
	
	
	else{
	let url= "/getDataBasedOnProdCode";
	let productCode=$("#prodCode1").val();
	let cycleNumber=$("#cycleNumber1").val();
	let cycleDate=$("#cycleDate").val();
	var	data = "productCode," + productCode +",cycleDate," + cycleDate +",cycleNumber," + cycleNumber ;
	
	postData(url, data);
	}
	
}
		
		






