var showbuttonflag = 0;
$(document).ready(function() {

	$('#binNumber').on('keyup keypress blur change', function() {
		$('#errselect-participantId').hide();
		validateFromCommonVal('binNumber', false, "NumericsOnly", 6, true);
	});
	var cursorPosition = null;


	$(document).ready(function() {

		$("#tabnew").DataTable({

			initComplete: function() {
				var api = this.api();

				// For each column
				api
					.columns()
					.eq(0)
					.each(function(colIdx) {
						// Set the header cell to contain the input element
						var cell = $('#tabnew thead tr th').eq(
							$(api.column(colIdx).header()).index()
						);
						var title = $(cell).text();
						if (colIdx < actionColumnIndex) {
							$(cell).html(title + '<br><input class="search-box"   type="text" />');

							// On every keypress in this input
							$(
								'input',
								$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
							)
								.off('keyup change')
								.on('change', function(_e) {
									// Get the search value
									$(this).attr('title', $(this).val());
									var regexr = '({search})';

									cursorPosition = this.selectionStart;
									// Search the column for that value
									api
										.column(colIdx)
										.search(
											this.value != ''
												? regexr.replace('{search}', '(((' + this.value + ')))')
												: '',
											this.value != '',
											this.value == ''
										)
										.draw();
								})
								.on('click', function(e) {
									e.stopPropagation();
								})
								.on('keyup', function(e) {
									e.stopPropagation();

									$(this).trigger('change');
									if (cursorPosition && cursorPosition != null) {
										$(this)
											.focus()[0]
											.setSelectionRange(cursorPosition, cursorPosition);
									}
								});
						} else {
							$(cell).html(title + '<br> &nbsp;');
						}
					});
				$('#tabnew_filter').hide();

			},
			dom: 'lBfrtip',
			buttons: [
				{
					extend: 'excelHtml5',
					text: 'Export',
					filename: 'Member Directory',
					header: 'false',
					title: null,
					sheetName: 'Member Directory',
					className: 'defaultexport',
					exportOptions: {
						columns: 'th:not(:last-child)'
					}
				},
				{
					extend: 'csvHtml5',
					text: 'Export',
					filename: 'Member Directory',
					header: 'false',
					title: null,
					sheetName: 'Member Directory',
					className: 'defaultexport',
					exportOptions: {
						columns: 'th:not(:last-child)'
					}
				}

			],

			searching: true,
			info: true,
			lengthChange: true,
			bLengthChange: true,
		});
	});

	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});

	$("#excelExport").on("click", function() {
		$(".buttons-excel").trigger("click");
	});

	$("#csvExport").on("click", function() {
		$(".buttons-csv").trigger("click");
	});

	$("#clearFilters").on("click", function() {
		$(".search-box").each(function() {
			$(this).val("");
			$(this).trigger("change");
		});
	});
showbuttonflag = 0;


	
	
	$('#select-participantId').select2({
		placeholder: "Select a Participant",
		sorter: data => data.sort((a, b) => a.text.localeCompare(b.text)),
	});


	$('#select-participantId')
		.change(
			function() {

				var tokenValue = document.getElementsByName("_TransactToken")[0].value;

				console.log("hurray");
				if ($('#select-participantId').val() != ' ') {
					$('#errselect-participantId').hide();
					var participantId = $("#select-participantId").val();


					$.ajax({
						url: "getSettlementBinList",
						type: "POST",
						data: {
							participantId: participantId,
							_TransactToken: tokenValue
						},
						dataType: "json",
						success: function(data) {

							$("#settlementBinNumber").empty();
							$("#settlementBinNumber").append('<option value="SELECT">--Select--</option>');
							$.each(data, function(_index, option) {
								if (option != null) {
									$("#settlementBinNumber").append(
										'<option value="' + option.settlementBin + '">'
										+ option.settlementBin + '</option>');
								}
							});


						}


					});



				}
			});






});




function resetAction() {
$("#select-participantId").val('').trigger('change')

	
	$('#errselect-participantId').hide();
	$('#select-participantId').val("");
	$('#settlementBinNumber').empty();
	$("#settlementBinNumber").append('<option value="SELECT">--Select--</option>');

	$('#errbinNumber').hide();
	$('#binNumber').val("")

	$('#afterSave').hide();
}







function viewData(url) {



	var isValid = true;

	if (!validateFromCommonVal('select-participantId', false, 'SelectionBox', 100, false)) {
		isValid = false;
	}

	if (!validateFromCommonVal('binNumber', false, "NumericsOnly", 6, true)) {
		isValid = false;
	}

	if ($('#select-participantId').val() == "" && $('#settlementBinNumber').val() == "SELECT" && $('#binNumber').val() == "") {
		isValid = false;
		$('#afterSave').hide();
	}





	if (isValid) {


		$('#errselect-participantId').hide();

		var participantId = $("#select-participantId").val();
		var settlementBin = $("#settlementBinNumber").val();



		var binNo = $("#binNumber").val();

		url = "/memberDirectoryFetchData";

		var data = "participantId," + participantId
			+ ",settlementBinNumber," + settlementBin + ",binNumber," + binNo;


		postData(url, data);

	}

	else {
		$('#afterSave').hide();
		$('#errselect-participantId').show();
		$("#errselect-participantId").find('.error').html('Please Enter Atleast One Field');
	}


}




function viewMember(id, binNumber) {
	var url = '/viewMember';

	var participantId = id
	var data = "participantId," + participantId + ",binNumber," + binNumber + ",isParticipant," + $("#isParticipant").val() + ",isBin," + $("#isBin").val() + ",isSetBin," + $("#isSetBin").val();
	postData(url, data);
}



