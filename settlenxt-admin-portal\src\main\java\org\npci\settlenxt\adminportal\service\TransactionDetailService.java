package org.npci.settlenxt.adminportal.service;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

import org.json.simple.JSONObject;
import org.npci.settlenxt.adminportal.dto.FeeDTO;
import org.npci.settlenxt.portal.common.dto.DisputeDocumentDTO;
import org.npci.settlenxt.portal.common.dto.DisputeTxnModel;
import org.npci.settlenxt.portal.common.dto.ParticipantDTO;
import org.npci.settlenxt.portal.common.dto.TxnSettlementDTO;
import org.npci.settlenxt.portal.common.service.BaseTransactionDetailService;
import org.springframework.ui.Model;
import org.springframework.web.multipart.MultipartFile;


public interface TransactionDetailService extends BaseTransactionDetailService {

	     List<ParticipantDTO> getParticipantIdList();
		
		 List<TxnSettlementDTO> getTransactionDetails(TxnSettlementDTO txnSettlementDTO);

		 String saveMemberActionData(TxnSettlementDTO transactionDetail,JSONObject jsonDocInfo);

		 List<TxnSettlementDTO> getTransactionDetailsStaging(String searchType);	

		 TxnSettlementDTO getTxnInfoStaging(String txnId, String status);

		 TxnSettlementDTO setTransactionData(TxnSettlementDTO txnDetail, DisputeTxnModel txnActionDetail);

		 TxnSettlementDTO getTxnInfoStagingStatus(TxnSettlementDTO disputePendingInfo);

		 List<TxnSettlementDTO> getPendingDisputeTxnList(TxnSettlementDTO transactionDetailDTO, String txnId);

			List<TxnSettlementDTO> getPendingDisputeNpciFundCashRevInfo(String txnId, String partId,
					String searchParticipant);

		 List<FeeDTO> getFeeNameList();

		 Map<String, String> getPartSettleBinList();

		 List<TxnSettlementDTO> getPendingDisputeBulkTxnInfo(TxnSettlementDTO transactionDetailDTO);

		 Map<String, List<String>> getAcquirerIdList();

		 Map<String, List<String>> getIssuerIdList();

		 String addFundCollectDisburse(TxnSettlementDTO netFundTxnDto);

		String getSettlementBin(String txnDestinationInstId, String entityType, String instId);

		 String getNpciPartId();

		 void approveRejectDisputeInfo(TxnSettlementDTO disputePendingInfo) throws InterruptedException;

		 List<TxnSettlementDTO> getPendingApprRejectTranDetails(TxnSettlementDTO txnSettlementDTO);

		 String approveRejectDisputesbulk(String bulkApprovalStageIdList, DisputeTxnModel disputeTxnDto,
				Model model) throws InterruptedException;

		 List<DisputeDocumentDTO> saveUploadedDocument(List<MultipartFile> document, String txnId, String tstampLocal,
				String participantId, List<String> docTypeList, JSONObject jsonDocInfo) throws IOException, ParseException;

		 String insertDispDocInfo(List<DisputeDocumentDTO> docfilePathList, String prevDocFilePath, String txnId, String status,
				JSONObject jsonDocInfo);
	
		 List<TxnSettlementDTO> formatTxnFileds(List<TxnSettlementDTO> filteredTxnInfoList,
				TxnSettlementDTO transactionDetailDTO);

}
