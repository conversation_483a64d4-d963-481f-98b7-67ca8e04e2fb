<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.npci.settlenxt.adminportal.repository.FeeRepository">
	
	
		<select id="getApprovedFeeRateList" resultType="FeeDTO">
	select  frs.fee_type as feeType, frs.net_max as netMax ,frs.fee_type_code as feeTypCode,frs.net_min as netMin ,frs.fee_id as feeId , frs.status  as status ,ps.request_state as requestState , frs.fee_description as feeDesc , frs.fee_code as feeCode  , frs.txn_currency as txnCurrency , frs.cashfees_flat as cashFeeFlat , 
			frs.cashfees_percent as  cashFeePercent , frs.cashfees_min as cashFeeMin , frs.cashfees_max as cashFeeMax, frs.purfees_flat as purFeeFlat , frs.purfees_percent as purFeePercent , 
			frs.purfees_min as purFeeMin , frs.purfees_max as purFeeMax , frs.multiplier as multiplier , frs.gst_code as gstCode  ,frs.credit_to as creditTo , frs.debit_to as debitTo , frs.last_updated_on as lastUpdatedOn, 
			frs.valid_from as validFromDt , frs.valid_to as validToDt, frs.reverse_cashfees as reversCashFee , RL.description as feeTypes 
			 from fee_rate frs inner join fee_rate_stg ps on (frs.fee_id=ps.fee_id)  left join lookup RL on RL.type=#{type} AND RL.code=frs.fee_type  where ps.request_state = #{requestState} order by frs.last_updated_on desc
	
	</select>
	
	
	
	<select id="getPendingForApprovalFeeRateList" resultType="FeeDTO">
	select  frs.fee_type as feeType, frs.net_max as netMax ,frs.fee_type_code as feeTypCode,frs.net_min as netMin ,frs.fee_id as feeId , frs.status  as status ,frs.request_state as requestState , frs.fee_description as feeDesc , frs.fee_code as feeCode  , frs.txn_currency as txnCurrency , frs.cashfees_flat as cashFeeFlat , 
			frs.cashfees_percent as  cashFeePercent , frs.cashfees_min as cashFeeMin , frs.cashfees_max as cashFeeMax, frs.purfees_flat as purFeeFlat , frs.purfees_percent as purFeePercent , 
			frs.purfees_min as purFeeMin , frs.purfees_max as purFeeMax , frs.multiplier as multiplier , frs.gst_code as gstCode  ,frs.credit_to as creditTo , frs.debit_to as debitTo , frs.last_updated_on as lastUpdatedOn, 
			frs.valid_from as validFromDt , frs.valid_to as validToDt, frs.reverse_cashfees as reversCashFee , frs.checker_comments as checkerComments, RL.description as feeTypes 
            from fee_rate_stg frs, lookup RL where RL.type=#{type} 
			AND RL.code=frs.fee_type and frs.request_state in <foreach item='item' index='index' collection='requestState' open='(' separator=',' close=')'>#{item} </foreach> order by frs.last_updated_on desc 
	
	</select>
	
	<select id="getFeesDetails" resultType="FeeDTO">
	select 	RF.fee_type as feeType ,RF.fee_id as feeId,RF.fee_type_code as feeTypCode,RF.status as status,RF.request_state as requestState, RF.fee_description  as feeDesc,RF.fee_code as feeCode , RF.txn_currency as txnCurrency,
			RF.cashfees_flat as cashFeeFlat,RF.cashfees_percent as  cashFeePercent ,RF.cashfees_min as cashFeeMin ,RF.gst_code as gstCode,RF.cashfees_max as cashFeeMax,RF.purfees_flat as purFeeFlat,RF.purfees_percent as purFeePercent,
			RF.purfees_min as purFeeMin,RF.net_min as netMin,RF.net_max as netMax,RF.purfees_max as purFeeMax,RF.multiplier as multiplier,RF.credit_to as creditTo ,RF.debit_to as debitTo,RF.days_to_be_waived as daystobewaived ,RF.waiver_day_type as waiverDayType,RF.compound_fees as compoundFee,RF.daily_fees as dailyFee,RF.penalty_day_type as penaltyDayType ,RF.impact_to as impactTo,RF.to_date_action as dateAction,RF.valid_from as validFromDt,RF.valid_to as validToDt,
			RF.reverse_cashfees as reversCashFee ,RF.fee_type_code as feeTypCode,RL.description as feeTypes,RF.created_by as createdBy,RL2.description as currencyDesc from fee_rate_stg RF,lookup RL,lookup RL2 where RL.type=#{feeType}  and RF.fee_type=RL.code and  RL2.type=#{currencyCode} and  RL2.code = RF.txn_currency AND RF.fee_id = #{feeId}
	</select>
	
	
	
	<select id="getFeesDetailsMain" resultType="FeeDTO">
		select 	RF.fee_type as feeType ,RF.fee_id as feeId,RF.fee_type_code as feeTypCode,RF.status as status, RF.fee_description  as feeDesc,RF.fee_code as feeCode , RF.txn_currency as txnCurrency,
			RF.cashfees_flat as cashFeeFlat,RF.cashfees_percent as  cashFeePercent ,RF.cashfees_min as cashFeeMin ,RF.gst_code as gstCode,RF.cashfees_max as cashFeeMax,RF.purfees_flat as purFeeFlat,RF.purfees_percent as purFeePercent,
			RF.purfees_min as purFeeMin,RF.net_min as netMin,RF.net_max as netMax,RF.purfees_max as purFeeMax,RF.multiplier as multiplier,RF.credit_to as creditTo ,RF.debit_to as debitTo,RF.days_to_be_waived as daystobewaived ,RF.waiver_day_type as waiverDayType,RF.compound_fees as compoundFee,RF.daily_fees as dailyFee,RF.penalty_day_type as penaltyDayType ,RF.impact_to as impactTo,RF.to_date_action as dateAction,RF.valid_from as validFromDt,RF.valid_to as validToDt,
			RF.reverse_cashfees as reversCashFee ,RF.fee_type_code as feeTypCode,RL.description as feeTypes,RF.created_by as createdBy,RL2.description as currencyDesc from fee_rate RF,lookup RL,lookup RL2 where RL.type=#{feeType}  and RF.fee_type=RL.code and  RL2.type=#{currencyCode} and  RL2.code = RF.txn_currency AND RF.fee_id = #{feeId}
	
		</select>
		
		<select id="getFeeRateStgInfoByFeeId" resultType="FeeDTO">
		select 	RF.fee_type as feeType ,RF.fee_id as feeId,RF.fee_type_code as feeTypCode,RF.status as status, RF.fee_description  as feeDesc,RF.fee_code as feeCode , RF.txn_currency as txnCurrency,
			RF.cashfees_flat as cashFeeFlat,RF.cashfees_percent as  cashFeePercent ,RF.cashfees_min as  cashFeeMin  ,RF.gst_code as gstCode,RF.cashfees_max as cashFeeMax,RF.purfees_flat as purFeeFlat,RF.purfees_percent as purFeePercent,
			RF.purfees_min as purFeeMin,RF.net_min as netMin,RF.net_max as netMax,RF.purfees_max as purFeeMax,RF.multiplier as multiplier,RF.credit_to as creditTo ,RF.debit_to as debitTo,RF.days_to_be_waived as daystobewaived ,RF.waiver_day_type as waiverDayType,RF.compound_fees as compoundFee,RF.daily_fees as dailyFee,RF.penalty_day_type as penaltyDayType ,RF.impact_to as impactTo,RF.to_date_action as dateAction,RF.valid_from as validFromDt,RF.valid_to as validToDt,
			RF.reverse_cashfees as reversCashFee ,RF.fee_type_code as feeTypCode,RL.description as feeTypes,RF.created_by as createdBy,RL2.description as currencyDesc
		, RF.created_by as createdBy , RF.created_on  as createdOn, RF.last_updated_by as lastUpdatedBy , RF.last_updated_on as lastUpdatedOn , RF.request_state as requestState , RF.last_operation as lastOperation, RF.maker_comments as makerComments , 
			RF.checker_comments as checkerComments  from fee_rate_stg RF,lookup RL,lookup RL2 where RL.type=#{feeType} AND RF.fee_type=RL.code and  RL2.type=#{currencyCode} and RL2.code = RF.txn_currency AND RF.fee_id = #{feeId}
			</select>
		
		
		
		
			<insert id="saveFee">
			Insert Into fee_rate (  fee_id, fee_code,fee_description,txn_currency,valid_from,valid_to,days_to_be_waived ,compound_fees,waiver_day_type ,penalty_day_type ,daily_fees ,impact_to ,to_date_action ,status,created_by,created_on,gst_code,credit_to,debit_to ,cashfees_flat ,  cashfees_percent , cashfees_min , cashfees_max ,  purfees_flat , purfees_percent , purfees_min , purfees_max , multiplier , reverse_cashfees , fee_type_code,net_min,net_max,fee_type  ) values (#{feeId},#{feeCode},#{feeDesc},#{txnCurrency},#{validFromDt},#{validToDt},#{daystobewaived},#{compoundFee},#{waiverDayType},#{penaltyDayType},#{dailyFee},#{impactTo},#{dateAction},#{status},#{createdBy},#{createdOn},#{gstCode},#{creditTo},#{debitTo},#{cashFeeFlat},#{cashFeePercent},#{cashFeeMin},#{cashFeeMax},#{purFeeFlat},#{purFeePercent},#{purFeeMin},#{purFeeMax},#{multiplier},#{reversCashFee},#{feeTypCode},#{netMin},#{netMax},#{feeType})
			
			</insert>
			<update id = "updateFee">
			UPDATE FEE_RATE SET fee_type=#{feeType},net_min=#{netMin},net_max=#{netMax}, fee_description=#{feeDesc}, fee_code=#{feeCode}, txn_currency=#{txnCurrency}, cashfees_flat=#{cashFeeFlat}, cashfees_percent=#{cashFeePercent}, cashfees_min=#{cashFeeMin}, cashfees_max=#{cashFeeMax}, purfees_flat=#{purFeeFlat}, purfees_percent=#{purFeePercent}, purfees_min=#{purFeeMin}, purfees_max=#{purFeeMax}, multiplier=#{multiplier}, gst_code=#{gstCode}, status=#{status}, credit_to=#{creditTo}, debit_to=#{debitTo}, valid_from=#{validFromDt}, valid_to=#{validToDt},days_to_be_waived=#{daystobewaived} ,compound_fees=#{compoundFee},waiver_day_type=#{waiverDayType} ,penalty_day_type=#{penaltyDayType} ,daily_fees =#{dailyFee},impact_to=#{impactTo} ,to_date_action=#{dateAction} , reverse_cashfees=#{reversCashFee}, fee_type_code=#{feeTypCode},  LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn} WHERE FEE_ID =  #{feeId}
			</update>
			
			
			<update id = "updateFeeStgState">
			UPDATE FEE_RATE_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments},LAST_OPERATION=#{lastOperation} WHERE FEE_ID =  #{feeId}
</update>





	<select id="getFeeRateInfoByFeeId" resultType="FeeDTO">
Select fee_type as feeType ,fee_type_code  as feeTypCode,net_min as netMin,net_max as netMax, fee_description  as feeDesc , fee_code as feeCode , txn_currency as txnCurrency, cashfees_flat as cashFeeFlat , cashfees_percent as  cashFeePercent , cashfees_min as  cashFeeMin , cashfees_max as cashFeeMax , purfees_flat as purFeeFlat, purfees_min as purFeeMin , purfees_percent as purFeePercent , purfees_max as purFeeMax , multiplier as multiplier , gst_code as gstCode ,days_to_be_waived as daystobewaived,compound_fees as compoundFee ,waiver_day_type as waiverDayType ,penalty_day_type as penaltyDayType ,daily_fees as dailyFee ,impact_to  as impactTo,to_date_action  as dateAction, status as status , credit_to as creditTo , debit_to as debitTo , valid_from as validFromDt , valid_to as validToDt , reverse_cashfees as  reversCashFee, created_by as createdBy , created_on as createdOn , last_updated_by as lastUpdatedBy , last_updated_on as lastUpdatedOn , fee_type_code as feeTypCode from fee_rate where fee_id=#{feeId}
</select>


<select id="getFeeRate" resultType="FeeDTO">

select frs.fee_type as feeType,frs.fee_type_code  as feeTypCode,frs.net_max as netMax,frs.net_min as netMin, frs.fee_id as feeId,frs.status as status ,frs.request_state as requestState, frs.fee_description  as feeDesc, frs.fee_code  as feeCode  , frs.txn_currency as txnCurrency, frs.cashfees_flat  as cashFeeFlat  , frs.cashfees_percent as  cashFeePercent  , frs.days_to_be_waived as daystobewaived,frs.waiver_day_type  as waiverDayType,frs.compound_fees as compoundFee,frs.daily_fees as dailyFee,frs.penalty_day_type  as penaltyDayType,frs.impact_to as impactTo,frs.to_date_action as dateAction,frs.cashfees_min as  cashFeeMin, frs.cashfees_max  as cashFeeMax, frs.purfees_flat as purFeeFlat , frs.purfees_percent as purFeePercent , frs.purfees_min  as purFeeMin, frs.purfees_max  as purFeeMax, frs.multiplier as multiplier, frs.gst_code as gstCode,frs.credit_to as creditTo, frs.debit_to as debitTo , frs.valid_from as validFromDt , frs.valid_to  as validToDt , frs.reverse_cashfees as reversCashFee ,frs.fee_type_code as feeTypCode , RL.description as feeTypes , frs.CREATED_BY as createdBy, frs.CREATED_ON as createdOn, frs.LAST_UPDATED_BY as lastUpdatedBy, frs.LAST_UPDATED_ON as lastUpdatedOn   from fee_rate_stg frs, lookup RL where RL.type=#{type}  AND RL.code=frs.fee_type and fee_id=#{feeId}
</select>





<select id="getFeeRateMain" resultType="FeeDTO">
select fee_type  as feeType ,fee_type_code as feeTypCode,net_max as netMax,net_min as netMin,fee_id as feeId,fee_description as feeDesc,fee_code as feeCode, txn_currency as txnCurrency , cashfees_flat as cashFeeFlat , cashfees_percent as cashFeePercent, cashfees_min as cashFeeMin , cashfees_max as cashFeeMax, purfees_flat as purFeeFlat, purfees_percent as purFeePercent, purfees_min as purFeeMin, purfees_max as purFeeMax, multiplier as multiplier, gst_code as gstCode,credit_to as creditTo, debit_to as debitTo , valid_from as validFromDt , valid_to as validToDt , days_to_be_waived as daystobewaived,waiver_day_type  as waiverDayType,compound_fees as compoundFee ,daily_fees as dailyFee,penalty_day_type  as penaltyDayType,impact_to,to_date_action as dateAction, reverse_cashfees as reversCashFee,STATUS as status, CREATED_BY as createdBy, CREATED_ON as createdOn, LAST_UPDATED_BY as lastUpdatedBy, LAST_UPDATED_ON as lastUpdatedOn,fee_type_code as feeTypCode from fee_rate where fee_id=#{feeId}
</select>





<update id="updateFeeRate" >
UPDATE fee_rate_stg SET fee_type=#{feeType},net_min=#{netMin},net_max=#{netMax}, fee_description=#{feeDesc}, fee_code=#{feeCode}, txn_currency=#{txnCurrency}, cashfees_flat=#{cashFeeFlat}, cashfees_percent=#{cashFeePercent}, cashfees_min=#{cashFeeMin}, cashfees_max=#{cashFeeMax}, purfees_flat=#{purFeeFlat}, purfees_percent=#{purFeePercent}, purfees_min=#{purFeeMin}, purfees_max=#{purFeeMax}, multiplier=#{multiplier}, gst_code=#{gstCode}, status=#{status}, credit_to=#{creditTo}, debit_to=#{debitTo}, valid_from=#{validFromDt}, valid_to=#{validToDt},days_to_be_waived=#{daystobewaived} ,compound_fees=#{compoundFee},waiver_day_type=#{waiverDayType} ,penalty_day_type=#{penaltyDayType} ,daily_fees =#{dailyFee},impact_to=#{impactTo} ,to_date_action=#{dateAction} , reverse_cashfees=#{reversCashFee}, created_by=#{createdBy}, created_on=#{createdOn}, last_updated_by=#{lastUpdatedBy}, last_updated_on=#{lastUpdatedOn}, request_state=#{requestState}, fee_type_code=#{feeTypCode},last_operation=#{lastOperation} WHERE fee_id=#{feeId}
</update>



<delete id="deleteDiscardedEntry" >
DELETE FROM fee_rate_stg WHERE fee_id= #{feeId}
</delete>


<select id="getFeeCodeList" resultType="FeeDTO">
select code as feeTypCode ,description as feeTypes from lookup where type=#{type}
</select>



<select id="getGstCodeList" resultType="FeeDTO">
select description as description,sys_key as gstCode from sys_params where sys_type=#{sysType} 
</select>


<select id="getTxnCurrency" resultType="FeeDTO">
select description as txnCurrency,code as txnCode from lookup where type=#{type}
</select>



<select id="getPartyTypeList" resultType="FeeDTO">
select code  as creditTo,description as description from lookup where type=#{type}
</select>


	<insert id="addfee">
			INSERT INTO fee_rate_stg ( fee_id,fee_code,fee_description, txn_currency, cashfees_flat, cashfees_percent, cashfees_min, cashfees_max, purfees_flat,purfees_percent, purfees_min, purfees_max, multiplier, gst_code, status,credit_to,debit_to,valid_from,valid_to, reverse_cashfees,days_to_be_waived ,compound_fees,waiver_day_type ,penalty_day_type ,daily_fees ,impact_to ,to_date_action  ,created_by,created_on,last_updated_by,last_updated_on,request_state,last_operation,fee_type_code,net_min,net_max,fee_type) values (#{feeId},#{feeCode}, #{feeDesc}, #{txnCurrency}, #{cashFeeFlat}, #{cashFeePercent}, #{cashFeeMin}, #{cashFeeMax }, #{purFeeFlat}, #{purFeePercent}, #{purFeeMin}, #{purFeeMax}, #{multiplier}, #{gstCode}, #{status},#{creditTo},#{debitTo}, #{validFromDt}, #{validToDt},#{reversCashFee},#{daystobewaived},#{compoundFee},#{waiverDayType},#{penaltyDayType},#{dailyFee},#{impactTo},#{dateAction},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{requestState},#{lastOperation},#{feeTypCode},#{netMin},#{netMax},#{feeType})
			
			</insert>



<select id="fetchIdFromFeeRateSequence" resultType="int">
SELECT nextval('FEERATE_ID_SEQ')
</select>



<select id="checkDistinctFeecodeStg" resultType="int">
SELECT count(fee_id) FROM fee_rate_stg   WHERE fee_code = #{feeCode} 
</select>


<select id="checkDistinctFeecode" resultType="int">
SELECT COUNT(fee_id) FROM fee_rate WHERE fee_code = #{feeCode} 
</select>




<select id="getEditFeeRate" resultType="FeeDTO">
Select fee_type as feeType,fee_type_code as feeTypCode ,net_min as netMin,net_max as netMax, fee_id as feeId, fee_description as feeDesc ,
 fee_code as feeCode , txn_currency as txnCurrency, cashfees_flat as cashFeeFlat , cashfees_percent as cashFeePercent , cashfees_min  as cashFeeMin, 
 cashfees_max as cashFeeMax , purfees_flat as purFeeFlat , purfees_min as purFeeMin , purfees_percent as purFeePercent , purfees_max as purFeeMax,
  multiplier as multiplier , gst_code as gstCode , status as status , credit_to as creditTo , debit_to as debitTo , valid_from as validFromDt , valid_to as validToDt , 
  reverse_cashfees  as reversCashFee,days_to_be_waived as daystobewaived ,compound_fees as compoundFee,waiver_day_type as waiverDayType,
  penalty_day_type as penaltyDayType  ,daily_fees as dailyFee ,impact_to as impactTo ,to_date_action as dateAction ,  fee_type_code  as feeTypCode
  ,request_state as requestState, created_by as createdBy , created_on  as createdOn
  from fee_rate_stg where fee_id=#{feeId} 
</select>


<update id="updateEditFeeRate" >
UPDATE fee_rate_stg SET  fee_type=#{feeType},net_min=#{netMin},net_max=#{netMax},fee_description=#{feeDesc}, fee_code=#{feeCode}, txn_currency=#{txnCurrency}, cashfees_flat=#{cashFeeFlat}, cashfees_percent=#{cashFeePercent}, cashfees_min=#{cashFeeMin}, cashfees_max=#{cashFeeMax}, purfees_flat=#{purFeeFlat}, purfees_percent=#{purFeePercent}, purfees_min=#{purFeeMin}, purfees_max=#{purFeeMax}, multiplier=#{multiplier}, gst_code=#{gstCode}, status=#{status}, credit_to=#{creditTo}, debit_to=#{debitTo}, valid_from=#{validFromDt}, valid_to=#{validToDt},days_to_be_waived=#{daystobewaived} ,compound_fees=#{compoundFee},waiver_day_type=#{waiverDayType} ,penalty_day_type=#{penaltyDayType} ,daily_fees =#{dailyFee},impact_to=#{impactTo} ,to_date_action=#{dateAction} ,  reverse_cashfees=#{reversCashFee}, last_updated_by=#{lastUpdatedBy}, last_updated_on=#{lastUpdatedOn}, request_state=#{requestState},last_operation=#{lastOperation}, fee_type_code=#{feeTypCode} WHERE fee_id=#{feeId}
</update>


<select id="getMinorList" resultType="FeeDTO">
select distinct fc.fee_id as feeConfigId , fc.created_by as createdBy, fc.created_on as createdOn ,  fc.last_updated_by as lastUpdatedBy ,  fc.last_updated_on as lastUpdatedOn 
			 from fee_config_stg frs, fee_master_stg fms,fee_config fc  where
			 fms.fee_config_id=frs.fee_config_id and frs.fee_config_id=fc.fee_id and frs.significance=#{significance} and fms.last_operation like '%MINOR%'   and fms.request_state =#{requestState} order by  fc.last_updated_on desc

</select>

<select id="getMajorList" resultType="FeeDTO">
	 select distinct on(fm.major_id) fm.major_id as feeMajorId,fm.scheme_code as schemeCode ,fm.product_code as productCode,fms.request_id as requestId   , fm.card_type as cardType ,fm.card_brand as cardBrand ,fm.function_code as funCd , fm.priority as priority,
			 fm.fee_configs as feeConfigs , fm.status as status,fm.last_updated_on as lastUpdatedOn, fms.request_state as requestState
			 from fee_major_stg frs, fee_master_stg fms,fee_major fm where
			 fms.fee_config_id=frs.major_id and frs.major_id=fm.major_id and fms.last_operation like '%MAJOR%'  and fms.request_state in <foreach item='item' index='index' collection='requestState' open='(' separator=',' close=')'>#{item}</foreach>

</select>

<select id="getPendingMajorList" resultType="FeeDTO">
 select distinct(frs.major_id) as feeMajorId,fms.request_id as requestId,fms.last_updated_on as lastUpdatedOn ,frs.scheme_code as schemeCode,frs.product_code as productCode,frs.card_type  as cardType,frs.card_brand as cardBrand, frs.function_code as funCd , frs.priority  as priority ,
			 frs.status as status,fms.request_state as requestState,fms.checker_comments as checkerComments
			 from fee_major_stg frs, fee_master_stg fms  where 
			 fms.fee_config_id=frs.major_id and fms.last_operation like '%MAJOR%' and  fms.request_state in <foreach item='item' index='index' collection='requestState' open='(' separator=',' close=')'>#{item}</foreach>  ORDER BY fms.last_updated_on DESC
</select>

<select id="getEditFeeMinor" resultType="FeeRateConfigDTO">
SELECT  fcs.fee_config_id as feeConfigId, fms.status as status,fcs.field_name as fieldName, fcs.relational_operator as relationalOperator,
			fms.request_state as requestState ,fcs.field_value as fieldValue,  fcs.significance as significance ,fms.request_id as requestId
  FROM fee_config_stg fcs  INNER JOIN fee_master_stg fms
  ON fcs.fee_config_id = fms.fee_config_id where fms.fee_config_id=#{feeMinorId} and fms.last_operation like '%MINOR%'   and fcs.significance=#{significance}

</select>






<select id="getPendingMinorList" resultType="FeeDTO">
	 select distinct frs.fee_config_id as feeConfigId ,fms.last_operation as lastOperation,fms.request_state as requestState, fms.created_by as createdBy
			, fms.created_on as createdOn ,  fms.last_updated_by as lastUpdatedBy , fms.request_id as requestId,fms.checker_comments as checkerComments, fms.last_updated_on as lastUpdatedOn
			 from fee_config_stg frs, fee_master_stg fms  where 
			 fms.fee_config_id=frs.fee_config_id and fms.last_operation like '%MINOR%' and frs.significance=#{significance}  and fms.request_state in <foreach item='item' index='index' collection='requestState' open='(' separator=',' close=')'>#{item}</foreach> order by fms.last_updated_on desc
</select>


<update id="updateEditFeeMajorStg" >
 UPDATE fee_major_stg SET major_id=#{feeMajorId},scheme_code=#{schemeCode}, product_code=#{productCode}, card_type=#{cardType}, card_brand=#{cardBrand}, function_code=#{funCd}, priority=#{priority}, status=#{status},fee_configs=(#{feeConfigs}::json) WHERE major_id= #{oldFeeMajorId}


</update>



<select id="fetchIdFromFeeMasterSequence" resultType="int">
SELECT nextval('fee_master_id_seq')
</select>



<insert id="addFeeMajorStg">
INSERT INTO fee_major_stg ( major_id, scheme_code, product_code, card_type, card_brand, function_code, priority, status,fee_configs) VALUES (#{feeMajorId},#{schemeCode}, #{productCode}, #{cardType}, #{cardBrand}, #{funCd}, #{priority}, #{status},(#{feeConfigs}::json))
</insert>





<insert id="addFeeConfigStg">
INSERT INTO fee_config_stg ( fee_config_id, field_name, relational_operator, field_value, significance, status) VALUES (#{feeConfigId},#{fieldName}, #{relationalOperator}, #{fieldValue}, #{significance}, #{status})
</insert>





<insert id="addFeeMasterStg">
INSERT INTO fee_master_stg ( request_id, status, created_by, created_on, last_updated_by, last_updated_on, request_state, last_operation, maker_comments, checker_comments, fee_config_id) VALUES (#{requestId}, #{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{requestState},#{lastOperation},#{makerComments},#{checkerComments},#{feeConfigId})
</insert>




<select id="getFeeIdList" resultType="FeeMajorMinorPriorityDTO">
select CONCAT (fee_code,'-', fee_description) as feeDesc ,fee_id as feeId from fee_rate where fee_type=#{feeType} order by fee_id 
</select>









<select id="getEditFeeConfig" resultType="FeeMajorMinorPriorityDTO">
select fee_id as feeId,major_id as feeMajorId, status as status, priority as priority, minor_id as feeMinorId from fee_minor_priority_stg where major_id=#{majorId} and priority=#{priority}
</select>




<select id="checkDistinctFeeMajorMinorStg" resultType="int">
SELECT count(fcs.fee_config_id) FROM fee_config_stg fcs, fee_master_stg fms  WHERE fms.fee_config_id=fcs.fee_config_id AND fcs.fee_config_id = #{feeConfigId}  AND fcs.significance=#{significance} 
</select>






<select id="checkDistinctFeeMajorMinor" resultType="int">
SELECT COUNT(fcs.fee_id) FROM fee_config fcs WHERE  fcs.fee_id = #{feeConfigId} AND fcs.significance=#{significance}   
</select>



<select id="getSavedMinorList" resultType="FeeMajorMinorPriorityDTO">
select mps.major_id as feeMajorId,mps.minor_id as feeMinorId,mps.priority as priority,mps.fee_id as feeId,fms.request_id as requestId,mps.acq_processing_fee_id as acqProcessingFeeID , mps.acq_assessment_fee_id as acqAssessmentFeeID , mps.iss_processing_fee_id as issProcessingFeeID , mps.iss_assessment_fee_id as issAssessmentFeeID,mps.iss_auth_fee_id as issAuthFeeID ,mps.acq_auth_fee_id as acqAuthFeeID,mps.status as status  from fee_minor_priority_stg mps,fee_master_stg fms where mps.request_id=fms.request_id and mps.status!='D' and  fms.request_state=#{requestState} and mps.major_id=#{feeMajorId} order by mps.priority
</select>


<select id="getFeeMajorStgInfoByMajorId" resultType="FeeDTO">
Select distinct on (fm.major_id)fm.major_id as feeMajorId,fm.scheme_code as schemeCode, fm.product_code as productCode, fm.card_type as cardType, fm.card_brand as cardBrand, fm.function_code as funCd, fm.priority as priority , fm.status as status ,fs.created_by as createdBy,fs.created_on as createdOn,fs.last_updated_by as lastUpdatedBy,fs.last_updated_on as lastUpdatedOn,fs.request_state as requestState,fm.fee_configs as feeConfigs from fee_major_stg fm inner join fee_master_stg fs on fs.fee_config_id=fm.major_id where fm.major_id = #{majorId} and fs.last_operation like '%MAJOR%'
</select>




<update id="updateFeeMasterStgMajor" >
UPDATE FEE_MASTER_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} WHERE fee_config_id =  #{majorId} and last_operation like '%MAJOR%' 


</update>







<update id="updateFeeMasterStgMinor" >
UPDATE FEE_MASTER_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} WHERE fee_config_id =  #{majorId} and last_operation like '%MINOR%' 


</update>




<select id="getFeeMajorInfoByMajorId" resultType="FeeDTO">
Select fm.major_id as feeMajorId, fm.scheme_code as schemeCode, fm.product_code as productCode, fm.card_type as cardType, fm.card_brand as cardBrand, fm.function_code as funCd, fm.priority as priority , fm.status as status, fm.created_by as createdBy, fm.created_on as createdOn , fm.last_updated_by as lastUpdatedBy , fm.last_updated_on as lastUpdatedOn from fee_major fm  where  fm.major_id = #{feeMajorId}


</select>


<insert id="saveFeeMajor">
INSERT INTO fee_major ( major_id,scheme_code,product_code, card_type, card_brand , function_code , priority , status , created_by , created_on , last_updated_by , last_updated_on,fee_configs) VALUES (#{feeMajorId},#{schemeCode},#{productCode},#{cardType},#{cardBrand},#{funCd},#{priority},#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},(#{feeConfigs}::json))
</insert>


<update id="updateFeeMajor">

UPDATE FEE_MAJOR SET  STATUS=#{status}, LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},scheme_code = #{schemeCode} , product_code = #{productCode} , card_type = #{cardType} , card_brand = #{cardBrand} , function_code = #{funCd} , priority = #{priority},fee_configs=(#{feeConfigs}::json)  WHERE major_id =  #{feeMajorId}
</update>





<insert id="saveFeeConfig">
INSERT INTO fee_config ( fee_id,field_name,relational_operator, field_value, sub_field_name , significance , status , created_by , created_on , last_updated_by , last_updated_on) VALUES (#{feeMajorId},#{fieldName},#{relationalOperator},#{fieldValue},#{subFieldName},#{significance},#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn})
</insert>



<insert id="saveFeeConfigMinor">
INSERT INTO fee_config ( fee_id,field_name,relational_operator, field_value, sub_field_name , significance , status , created_by , created_on , last_updated_by , last_updated_on) VALUES (#{feeMinorId},#{fieldName},#{relationalOperator},#{fieldValue},#{subFieldName},#{significance},#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn})
</insert>


<select id="getFeeMinorStgInfoByminorId" resultType="FeeRateConfigDTO">
Select fcs.fee_config_id as feeMinorId,fcs.field_name as fieldName,fcs.relational_operator as relationalOperator ,fcs.field_value as fieldValue,fcs.sub_field_name as subFieldName,fcs.significance as significance ,fcs.status as status,fms.created_by as createdBy,fms.created_on as createdOn,fms.last_updated_by as lastUpdatedBy,fms.last_updated_on as lastUpdatedOn,fms.request_state as requestState from fee_config_stg fcs inner join fee_master_stg fms ON fcs.fee_config_id = fms.fee_config_id where fcs.fee_config_id  = #{minorId} and fcs.significance=#{significance} and fms.last_operation like '%MINOR%' 
</select>



<select id="getFeeMinorInfoByMinorId" resultType="FeeRateConfigDTO">
Select fee_config.fee_id as feeMinorId,fee_config.fee_id as feeConfigId,fee_config.field_name as fieldName,fee_config.relational_operator as relationalOperator,fee_config.field_value as fieldValue,fee_config.sub_field_name as subFieldName,fee_config.significance as significance,fee_config.status as status,fee_config.created_by as createdBy,fee_config.created_on as createdOn,fee_config.last_updated_by as lastUpdatedBy,fee_config.last_updated_on as lastUpdatedOn from fee_config where fee_config.fee_id  = #{feeMinorId} 
</select>



<select id="getMinorPriorityStgInfo" resultType="FeeMajorMinorPriorityDTO">
select fmps.major_id as feeMajorId, fmps.priority as priority, fmps.minor_id as feeMinorId, fmps.acq_processing_fee_id as acqProcessingFeeID  , fmps.acq_assessment_fee_id  as acqAssessmentFeeID, fmps.iss_processing_fee_id  as issProcessingFeeID, fmps.iss_assessment_fee_id as issAssessmentFeeID, fmps.acq_auth_fee_id as acqAuthFeeID, fmps.iss_auth_fee_id as issAuthFeeID,fmps.status as status,fmps.request_id as requestId, fms.created_by as createdBy , fms.created_on as createdOn, fms.last_updated_by as lastUpdatedBy , fms.last_updated_on as lastUpdatedOn , fms.request_state as requestState , fms.last_operation as lastOperation , fms.maker_comments as makerComments,fms.checker_comments as checkerComments,fmps.fee_id as interchangeFeeID from fee_minor_priority_stg fmps inner join fee_master_stg fms on fms.request_id  = fmps.request_id where fmps.major_id = #{feeMajorId} 
</select>



<insert id="saveMinorPriority">
INSERT into FEE_MINOR_PRIORITY (major_id , priority, minor_id , acq_processing_fee_id , acq_assessment_fee_id , iss_processing_fee_id , iss_assessment_fee_id, acq_auth_fee_id, iss_auth_fee_id, status , created_by , created_on , last_updated_by , last_updated_on,fee_id) values (#{feeMajorId},#{priority},#{feeMinorId},#{acqProcessingFeeID},#{acqAssessmentFeeID},#{issProcessingFeeID},#{issAssessmentFeeID},#{acqAuthFeeID},#{issAuthFeeID},#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{interchangeFeeID})
</insert>



<insert id="updateFeeMinorPriorityStg">
insert into  fee_minor_priority_stg (major_id, priority,minor_id, fee_id , acq_processing_fee_id,acq_assessment_fee_id,
			iss_processing_fee_id,iss_assessment_fee_id,
			acq_auth_fee_id,iss_auth_fee_id,request_id,status ) VALUES(#{feeMajorId}, #{priority},#{feeMinorId},#{interchangeFeeID}, #{acqProcessingFeeID}, #{acqAssessmentFeeID},#{issProcessingFeeID}, #{issAssessmentFeeID},#{ acqAuthFeeID},#{issAuthFeeID},#{requestId},#{status})

</insert>




<update id="updateFeeMasterStg">
UPDATE fee_master_stg SET status=#{status},  created_by=#{createdBy}, created_on=#{createdOn}, last_updated_by=#{lastUpdatedBy}, last_updated_on=#{lastUpdatedOn},request_state=#{requestState}, last_operation=#{lastOperation} WHERE request_id=#{requestId} and last_operation like '%MAJOR%'
</update>






<delete id="deleteFeeMinorPriority">
DELETE FROM fee_minor_priority WHERE major_id=#{majorId}
</delete>




<delete id="deleteDiscardedMinorEntry">
DELETE FROM fee_config_stg WHERE fee_config_id= #{minorId} and significance='N' 
</delete>





<update id="updateFeeConfigStg">
UPDATE FEE_CONFIG SET  STATUS=#{status}, LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, field_name = #{fieldName} , relational_operator = #{relationalOperator} , field_value = #{fieldValue} , sub_field_name = #{subFieldName} , significance = #{significance}  WHERE fee_id =  #{feeMajorId}
</update>




<delete id="deleteDiscardedEntryMajor">
DELETE FROM fee_major_stg WHERE major_id= #{feeMajorId} 
</delete>



<delete id="deleteDiscardedFeeConfigEntry">
DELETE FROM fee_config_stg WHERE fee_config_id = #{feeMajorId} and significance='M' 
</delete>




<delete id="deleteDiscardedMasterEntry">
DELETE FROM FEE_MASTER_STG where fee_config_id=#{feeMajorId} and last_operation like '%MAJOR%' 
</delete>

<select id="getRequestIdInFeeMasterStg" resultType="FeeMajorMinorPriorityDTO">
SELECT request_id as requestId FROM fee_master_stg WHERE  fee_config_id = #{feeMajorId} AND request_state=#{requestState} and last_operation like '%MAJOR%'  
</select>


<delete id="deleteFeeMinorPriorityStg">
DELETE FROM fee_minor_priority_stg WHERE major_id =#{feeMajorId}   
</delete>


 <select id="checkFeeMinorAvilable" resultType="int">
SELECT count(fee_id) FROM fee_config   WHERE fee_id=#{feeConfigId}  and status=#{status} and significance=#{significance}  
</select>
 
 
 
 
 <delete id="deleteMinorEntry">
DELETE FROM fee_config WHERE fee_id= #{minorId}   
</delete>
 
 
 
 <select id="getFeeMajorConfigStgInfoByMajorId" resultType="FeeRateConfigDTO">
Select fcs.fee_config_id as feeMajorId ,fcs.field_name as fieldName,fcs.relational_operator as relationalOperator,fcs.field_value as fieldValue,fcs.sub_field_name as subFieldName,fcs.significance as significance,fcs.status as status,fms.created_by as createdBy,fms.created_on as createdOn,fms.last_updated_by as lastUpdatedBy,fms.last_updated_on as lastUpdatedOn,fms.request_id as requestId from fee_config_stg fcs inner join fee_master_stg fms on fcs.fee_config_id=fms.fee_config_id where fcs.fee_config_id  = #{majorId} and fcs.significance=#{significance} and fms.last_operation like '%MAJOR%'  
</select>
 
 
 
 
 
  <select id="getFeeMajorConfigStgInfoByMinorId" resultType="FeeRateConfigDTO">
Select fcs.fee_config_id as feeMinorId ,fcs.field_name as fieldName,fcs.relational_operator as relationalOperator,fcs.field_value as fieldValue,fcs.sub_field_name as subFiledName,fcs.significance as significance,fcs.status as status,fms.created_by as createdBy,fms.created_on as createdOn,fms.last_updated_by as lastUpdatedBy,fms.last_updated_on from fee_config_stg fcs inner join fee_master_stg fms on fcs.fee_config_id=fms.fee_config_id where fcs.fee_config_id  = #{majorId} and fcs.significance=#{significance} and fms.last_operation like '%MINOR%'  
</select>
 
 
 
  <select id="checkFeeMajorAvilable" resultType="int">
SELECT count(major_id) FROM fee_major   WHERE major_id=#{feeConfigId}  and status=#{status}   
</select>
 
 
 <insert id="addFeeConfig">
 INSERT INTO fee_minor_priority_stg 
		(major_id,priority,minor_id,fee_id, acq_processing_fee_id , acq_assessment_fee_id , iss_processing_fee_id , iss_assessment_fee_id,iss_auth_fee_id,acq_auth_fee_id,status,request_id)
			values (#{feeMajorId},#{priority},#{feeMinorId},#{interchangeFeeID},#{acqProcessingFeeID},#{acqAssessmentFeeID},#{issProcessingFeeID},#{issAssessmentFeeID},#{issAuthFeeID},#{acqAuthFeeID},#{status},#{requestId})
 </insert>
 
 
 
 
  <select id="getMinorInfo" resultType="FeeDTO">
 select distinct  fee_id as feeConfigId  ,field_name as fieldName,relational_operator as relationalOperator , field_value as fieldValue , sub_field_name as subFieldName , significance as significance, status as status 
			 from fee_config  where significance='N' and fee_id in <foreach item='item' index='index' collection='minorIdsArr' open='(' separator=',' close=')'>#{item}</foreach>
 </select>
 
 
 
  <select id="getSavedMinorListByMajorId" resultType="FeeMajorMinorPriorityDTO">
 select mps.major_id as feeMajorId,mps.minor_id as feeMinorId,mps.priority as priority,mps.fee_id as interchangeFeeID,fms.request_id as requestId,mps.acq_processing_fee_id as acqProcessingFeeID, mps.acq_assessment_fee_id as acqAssessmentFeeID , mps.iss_processing_fee_id as issProcessingFeeID , mps.iss_assessment_fee_id as issAssessmentFeeID,mps.iss_auth_fee_id as issAuthFeeID ,mps.acq_auth_fee_id as acqAuthFeeID,mps.status as status  from fee_minor_priority_stg mps,fee_master_stg fms where mps.request_id=fms.request_id and mps.status!='D' and  fms.request_state=#{requestState} and mps.major_id=#{feeMajorId} order by mps.priority </select>
 
 
 
 
  <select id="getUnmappedFieldNameData" resultType="CodeValueDTO">
  SELECT code as code, type as type, description as description from LOOKUP where type =#{type} and status ='A' and code not in
		 (select distinct field_name  from fee_config_stg  where fee_config_id =#{feeConfigId})
  
  </select>
  
  
  
  
  
<update id="updateFeeMasterStgStateMajor">  
  UPDATE FEE_MASTER_STG SET    fee_config_id=#{majorId}, LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, last_operation=#{lastOperation} WHERE fee_config_id =  #{oldFeeMajorId} and last_operation like '%MAJOR%'
</update>


<update id="updateFeeMasterStgStateMajor2">  
 UPDATE FEE_MASTER_STG SET  LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, last_operation=#{lastOperation} WHERE fee_config_id =  #{majorId} and last_operation like '%MAJOR%'
</update>




<update id="updateFeeMasterStgStateMinor">  
UPDATE  FEE_MASTER_STG SET fee_config_id=#{majorId}, LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, last_operation=#{lastOperation} WHERE fee_config_id =#{oldFeeMinorId} and last_operation like '%MINOR%'
</update>






<delete id="deleteDiscardedFeeMinorPriorityStg">
DELETE FROM fee_minor_priority_stg WHERE major_id=#{feeMajorId}
</delete>



<select id="getFeeMinorPriorityStg" resultType="FeeMajorMinorPriorityDTO">
select major_id as feeMajorId,priority,minor_id as feeMinorId, fee_id as interchangeFeeID , acq_processing_fee_id as acqProcessingFeeID  ,acq_assessment_fee_id as acqAssessmentFeeID ,iss_processing_fee_id as issProcessingFeeID,iss_assessment_fee_id as issAssessmentFeeID,acq_auth_fee_id as acqAuthFeeID , iss_auth_fee_id as issAuthFeeID , status as status,request_id as requestId from fee_minor_priority_stg where  major_id=#{feeMajorId}  order by priority
</select>



<select id="getFeeMasterStg" resultType="FeeDTO">
select  request_id as requestId,status as status,created_by as createdBy,created_on as createdOn,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,request_state as requestState,last_operation as lastOperation,maker_comments as makerComments,checker_comments as checkerComments,fee_config_id as feeConfigId from fee_master_stg where fee_config_id=#{feeMajorId}  and last_operation like '%MAJOR%' 
</select>



<select id="getFeeMinorPriorityMain" resultType="FeeMajorMinorPriorityDTO">
select major_id as feeMajorId, priority as priority, minor_id as feeMinorId  , fee_id  as interchangeFeeID, acq_processing_fee_id as acqProcessingFeeID  , acq_assessment_fee_id   as acqAssessmentFeeID, iss_processing_fee_id as issProcessingFeeID, iss_assessment_fee_id as issAssessmentFeeID , acq_auth_fee_id as acqAuthFeeID, iss_auth_fee_id as issAuthFeeID,STATUS as status, CREATED_BY as createdBy, CREATED_ON as createdOn, LAST_UPDATED_BY as lastUpdatedBy, LAST_UPDATED_ON as lastUpdatedOn   from fee_minor_priority where  major_id=#{feeMajorId} and status !='D' order by priority 
</select>




<select id="getEditFeeMajor" resultType="FeeDTO">
SELECT fms.major_id as feeMajorId,fms.scheme_code as schemeCode,f.request_state as requestState,f.last_operation as lastOperation, fms.product_code as productCode, fms.card_type as cardType, fms.card_brand as cardBrand,
			fms.function_code as funCd, fms.priority as priority, fms.status as status ,f.request_id as requestId FROM fee_major_stg fms
			  INNER JOIN fee_master_stg f
			  ON fms.major_id = f.fee_config_id where fms.major_id=#{feeMajorId} and f.request_id=#{requestId} 
</select>




<update id="removeMajorMinorMapping">

Update fee_minor_priority_stg set status=#{status} where major_id=#{feeMajorId} and minor_id=#{feeMinorId} 

</update>




<select id="getMinorIdList" resultType="FeeMajorMinorPriorityDTO">
select DISTINCT(fee_id) as feeMinorId  from fee_config fm where significance='N' and fee_id not in(select minor_id from fee_minor_priority where major_id =#{feeMajorId}) order by fee_id </select>







<select id="checkDuplicatePriority" resultType="int">
SELECT count(*)  FROM fee_major WHERE priority = #{priority} and card_type=#{cardType}

</select>





<select id="checkDuplicatePriorityStg" resultType="int">
SELECT count(*) FROM fee_major_stg fms,fee_master_stg fm    WHERE fms.major_id=fm.fee_config_id and card_type=#{cardType} and priority = #{priority} AND fm.request_state  in ('P')

</select>




<select id="getFeeMajorId" resultType="String">
SELECT fms.major_id   FROM fee_major_stg fms,fee_master_stg fm   WHERE fms.major_id=fm.fee_config_id and fms.priority = #{priority} AND fm.request_state  in('P','A') 

</select>

<update id="updatePriorityForMajorIdMain">
Update fee_major set priority=#{priority} where major_id=#{feeMajorId}
</update>






<update id="updatePriorityForMajorIdStg">
Update fee_major_stg set priority=#{priority} where major_id=#{feeMajorId}
</update>


<update id="updateFeeConfig">
Update fee_minor_priority_stg SET priority=#{priority}, fee_id=#{interchangeFeeID} ,acq_processing_fee_id=#{acqProcessingFeeID}, acq_assessment_fee_id=#{acqAssessmentFeeID},iss_processing_fee_id=#{issProcessingFeeID},iss_assessment_fee_id=#{acqAssessmentFeeID},iss_auth_fee_id=#{issAuthFeeID},acq_auth_fee_id=#{acqAuthFeeID},status=#{status}  where major_id=#{feeMajorId} and minor_id=#{feeMinorId} 
</update>



<select id="getFeeMajorMappedMinorIDS" resultType="String">
select minor_id as feeMinorId from fee_minor_priority_stg where major_id=#{feeMajorId}
</select>




 

<select id="fetchFeeMajorJson" resultType="String">
select fee_configs  from fee_major where major_id =#{majorId} and status=#{status}
</select>

<select id="checkDuplicateMajorID" resultType="int">
SELECT count(*)  FROM fee_major WHERE major_id = #{feeConfigId} and status=#{status} 
</select>




<select id="getFeeMinorByminorIdForView" resultType="FeeRateConfigDTO">
Select fc.fee_id as feeMinorId ,fc.field_name as fieldName,fc.relational_operator as relationalOperator,fc.field_value as fieldValue,fc.sub_field_name as subFieldName,fc.significance as significance,fc.status as status,fc.created_by as createdBy,fc.created_on as createdOn,fc.last_updated_by as lastUpdatedBy,fc.last_updated_on as lastUpdatedOn ,fms.request_id as requestId from fee_config fc inner join fee_master_stg fms ON fc.fee_id = fms.fee_config_id    where fc.fee_id  = #{minorId} and fc.significance=#{significance} 
</select>





<select id="getFeeMajorByMajorIdForView" resultType="FeeDTO">
Select distinct on (fm.major_id)fm.major_id as feeMajorId ,fm.scheme_code as schemeCode, fm.product_code as productCode, fm.card_type as cardType, fm.card_brand as cardBrand, fm.function_code as funCd, fm.priority as priority , fm.status as status,fm.created_by as createdBy,fm.created_on as createdOn,fm.last_updated_by as lastUpdatedBy,fm.last_updated_on as lastUpdatedOn,fm.fee_configs as feeConifgs,fms.request_id as requestId from fee_major fm inner join fee_master_stg fms ON fm.major_id = fms.fee_config_id   where fm.major_id = #{majorId} 
</select>



<select id="getFeeRateStgInfoList" resultType="FeeDTO">
 select RF.fee_type as feeType, RF.fee_type_code as feeTypCode,RF.net_max as netMax,RF.net_min as netMin, RF.fee_id as feeId,RF.status as status,RF.request_state as requestState, RF.fee_description as feeDesc,RF.fee_code as feeCode, RF.txn_currency as txnCurrency,  
			RF.cashfees_flat as cashFeeFlat,RF.cashfees_percent as cashFeePercent,RF.cashfees_min as cashFeeMin ,RF.cashfees_max as cashFeeMax,RF.purfees_flat as purFeeFlat ,RF.purfees_percent as purFeePercent, 
			RF.purfees_min as purFeeMin,RF.purfees_max as purFeeMax,RF.multiplier as multiplier,RF.gst_code as gstCode,RF.credit_to as creditTo,RF.debit_to as debitTo,RF.valid_from as validFromDt ,RF.valid_to as validToDt,RF.days_to_be_waived as daystobewaived ,RF.compound_fees as compoundFee,RF.waiver_day_type ,RF.penalty_day_type as penaltyDayType ,RF.daily_fees   as dailyFee ,RF.impact_to as impactTo ,RF.to_date_action as dateAction, 
			RF.reverse_cashfees as reversCashFee,RF.fee_type_code,RL.description as feeTypes ,RF.created_by as createdBy ,RL2.description as currencyDesc, RF.created_on as createdOn, RF.last_updated_by as lastUpdatedBy, RF.last_updated_on as lastUpdatedOn , RF.request_state as requestState , RF.last_operation as lastOperation , RF.maker_comments  as makerComments, 
			RF.checker_comments as checkerComments  from fee_rate_stg RF,lookup RL,lookup RL2 where RL.type=#{feeType} AND RF.fee_type=RL.code and  RL2.type=#{currencyCode} and RL2.code = RF.txn_currency AND RF.fee_id  in <foreach item='item' index='index' collection='feeIdList' open='(' separator=',' close=')'>#{item}</foreach>;

</select>


<select id="getFeeMajorStgInfoByMajorIdList" resultType="FeeDTO">
Select distinct on (fm.major_id)fm.major_id as feeMajorId,fm.scheme_code as schemeCode, fm.product_code as productCode, fm.card_type as cardType, fm.card_brand as cardBrand, fm.function_code as funCd, fm.priority as priority, fm.status as status ,fs.created_by as createdBy,fs.created_on as createdOn,fs.last_updated_by as lastUpdatedBy,fs.last_updated_on as lastUpdatedOn,fs.request_state as requestState,fm.fee_configs as feeConfigs from fee_major_stg fm inner join fee_master_stg fs on fs.fee_config_id=fm.major_id where fm.major_id in <foreach item='item' index='index' collection='list' open='(' separator=',' close=')'>#{item}</foreach> and fs.last_operation like '%MAJOR%'

</select>


<select id="getFeeMinorStgInfoByminorIdList" resultType="FeeRateConfigDTO">
Select fcs.fee_config_id as feeMinorId,fcs.field_name as fieldName,fcs.relational_operator as relationalOperator,fcs.field_value as fieldValue,fcs.sub_field_name as subFieldName,fcs.significance as significance,fcs.status as status,fms.created_by as createdBy,fms.created_on as createdOn,fms.last_updated_by as lastUpdatedBy,fms.last_updated_on as lastUpdatedOn,fms.request_state as requestState from fee_config_stg fcs inner join fee_master_stg fms ON fcs.fee_config_id = fms.fee_config_id where fcs.fee_config_id in <foreach item='item' index='index' collection='list' open='(' separator=',' close=')'>#{item}</foreach>  and fcs.significance=#{significance} and fms.last_operation like '%MINOR%'

</select>

<select id="getFeeMajorStgByMajorIdForView" resultType="FeeDTO">
Select distinct on (fm.major_id)fm.major_id as feeMajorId,fm.scheme_code as schemeCode, fm.product_code as productCode, fm.card_type as cardType, fm.card_brand as cardBrand, fm.function_code as funCd, fm.priority as priority , fm.status as status ,fms.created_by as createdBy,fms.created_on as createdOn,fms.last_updated_by as lastUpdatedBy,fms.last_updated_on as lastUpdatedOn,fm.fee_configs as feeConfigs,fms.request_state as requestState from fee_major_stg fm inner join fee_master_stg fms on fm.major_id=fms.fee_config_id where fm.major_id  = #{majorId} and fms.last_operation like '%MAJOR%'  

</select>

<select id="getFeeDescriptionForMinor" resultType="FeeMajorMinorPriorityDTO">
select CONCAT (fee_code,'-', fee_description) as feeDesc ,fee_id as feeId from fee_rate_stg
</select>

<select id="checkFeeRateAvilable" resultType="int">
SELECT count(fee_id) FROM fee_rate   WHERE fee_id=#{feeId} 
</select>



<select id="getDateActionList" resultType="CodeValueDTO">
select action_code as code, action_code_description as description  from action_code
</select>

<select id="getFeeMinorPriorityMainByMajorAndMinorId"
	resultType="FeeMajorMinorPriorityDTO">
	Select major_id as feeMajorId,
	priority as priority,
	minor_id as feeMinorId, 
	fee_id as feeId,
	acq_processing_fee_id as acqProcessingFeeID,
	acq_assessment_fee_id as acqAssessmentFeeID,
	iss_processing_fee_id as issProcessingFeeID,
	iss_assessment_fee_id as issAssessmentFeeID,
	acq_auth_fee_id as acqAuthFeeID,
	iss_auth_fee_id as issAuthFeeID,
	status as status,
	created_by as createdBy,
	created_on as createdOn,
	last_updated_by as lastUpdatedBy,
	last_updated_on as lastUpdatedOn FROM 
	fee_minor_priority WHERE 
	major_id = #{majorId} AND
	minor_id = #{minorId}
</select>
<select id="getFeeRateDetails" resultType="FeeDTO">
	select RF.fee_id as feeId,
	RF.status as status, 
	RF.fee_description as feeDesc,
	RF.fee_code as feeCode, 
	RF.txn_currency as txnCurrency,
	RF.cashfees_flat as cashFeeFlat,
	RF.cashfees_percent as cashFeePercent,
	RF.cashfees_min as cashFeeMin,
	RF.cashfees_max as cashFeeMax,
	RF.purfees_flat as purFeeFlat,
	RF.purfees_percent as purFeePercent,
	RF.purfees_min as purFeeMin,
	RF.purfees_max as purFeeMax,
	RF.multiplier as multiplier,
	RF.gst_code as gstCode,
	RF.credit_to as creditTo,
	RF.debit_to as debitTo,
	RF.valid_from as validFromDt,
	RF.valid_to as validToDt,
	RF.reverse_cashfees as reversCashFee,
	RF.fee_type_code as feeTypCode,
	RL.description as description,
	RF.created_by as createdBy,
	RL.description as txnCurrencyDesc from 
	fee_rate RF,lookup RL WHERE 
	RL.code = RF.txn_currency AND 
	RL.type='NPCI_CUR_CODEB' AND 
	RF.fee_id = #{feeId}
</select>

</mapper>