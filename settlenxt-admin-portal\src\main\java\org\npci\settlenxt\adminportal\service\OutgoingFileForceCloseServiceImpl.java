package org.npci.settlenxt.adminportal.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.DateUtils;
import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.gateway.RestGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

/**
 * <li>This service gives response to api called from ui screen. It is used for
 * getting all details of failed/inprogress/forceCloseSuccess/forceCloseFailed
 * outgoing files</li>
 * 
 * <AUTHOR>
 *
 */
@Service
public class OutgoingFileForceCloseServiceImpl implements IOutgoingFileForceCloseService {

	private static final Logger logger = LogManager.getLogger(OutgoingFileForceCloseServiceImpl.class);

	@Autowired
	private Environment environment;

	@Autowired
	RestGateway restGateway;

	/**
	 * <li>This method is received data from UI screen and prepare request and send
	 * request to the reportOrchestation service for getting all details of
	 * failed/inprogress/forceCloseSuccess/forceCloseFailed outgoing files</li>
	 */
	@Override
	public CycleManagementDTO getInprogressFailedOutgoingFileList(CycleManagementDTO cycleManagementDTO) {
		String result = "";
		JsonObject requestBody = new JsonObject();
		if (StringUtils.isBlank(cycleManagementDTO.getSystemDate())) {
			String formattedRequestDate = DateUtils.getTodayLocalDate(DateUtils.YYYY_MM_DD);
			cycleManagementDTO.setSystemDate(formattedRequestDate);
		}
		requestBody.addProperty(CommonConstants.SYSTEM_DATE, DateUtils
				.getChangeDateFormat(cycleManagementDTO.getSystemDate(), DateUtils.YYYY_MM_DD, DateUtils.DD_MM_YYYY));
		try {
			String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
					+ CommonConstants.SETTLENXT_FETCH_INPROGRESS_LIST_OF_FILES;
			List<Map<String, String>> fileDetails = new ArrayList<>();
			result = restGateway.postAPIForJsonObject(url, requestBody);
			logger.info("Response of list of failed/inprogress/forceCloseSuccess/forceCloseFailed outgoing file is {}",
					result);
			if (StringUtils.isBlank(result)) {
				cycleManagementDTO.setCycleData(fileDetails);
				return cycleManagementDTO;
			}
			JsonObject respBody = (JsonObject) JsonParser.parseString(result);
			JsonArray outgoingFileNames = respBody.getAsJsonArray(CommonConstants.FILE_NAMES);
			fileDetails = new Gson().fromJson(outgoingFileNames, ArrayList.class);
			cycleManagementDTO.setCycleData(fileDetails);
		} catch (Exception e1) {
			logger.error(
					"Error occured while getting list of failed/inprogres/forceCloseSuccess/forceCloseFailed outgoing files {}",
					e1.getMessage(), e1);
		}
		return cycleManagementDTO;
	}

	/**
	 * This method is used for forceClose the outgoing files
	 *
	 */
	@Override
	public String forceCloseOutgoingFiles(CycleManagementDTO cycleManagementDTO) {
		JsonObject requestBody = new JsonObject();
		String result = "";
		if (StringUtils.isBlank(cycleManagementDTO.getSystemDate())) {
			String formattedRequestDate = DateUtils.getTodayLocalDate(DateUtils.YYYY_MM_DD);
			cycleManagementDTO.setSystemDate(formattedRequestDate);
		}
		requestBody.addProperty(CommonConstants.SYSTEM_DATE, DateUtils
				.getChangeDateFormat(cycleManagementDTO.getSystemDate(), DateUtils.YYYY_MM_DD, DateUtils.DD_MM_YYYY));
		requestBody.addProperty(CommonConstants.FILE_NAME, cycleManagementDTO.getFileName());
		requestBody.addProperty(CommonConstants.RESP_STATUS, cycleManagementDTO.getOutgoingFileStatus());
		try {
			String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
					+ CommonConstants.SETTLENXT_ABORT_OUTGOING_FILE;
			result = restGateway.postAPIForJsonObject(url, requestBody);
			logger.info("Response of force close outgoing file is {}", result);
		} catch (Exception e1) {
			logger.error("Error occured while force close the outgoing file {}", e1.getMessage(), e1);
		}
		return result;
	}

}
