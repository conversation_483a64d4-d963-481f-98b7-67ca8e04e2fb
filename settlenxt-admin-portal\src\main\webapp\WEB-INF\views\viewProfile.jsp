<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>

<script src="./static/js/validation/SearchRole.js"
	type="text/javascript"></script>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
	</ul>
	
	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default">
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">USER PROFILE</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
										<td colspan="6"><div class="panel-heading-red clearfix">
												<strong><span class="glyphicon glyphicon-user"></span> <span
													data-i18n="Data">User Information</strong> </span>
											</div>
										</td>
									</tr>
									<tr>
										<td><label>User ID</label></td>
										<td>${userDTO.userName} </td>
										<td><label>Full Name</label></td>
										<td>
											<c:if test="${not empty userDTO.salutation}">${userDTO.salutation} </c:if>
											<c:if test="${not empty userDTO.firstName}">${userDTO.firstName} </c:if>
											<c:if test="${not empty userDTO.middleName}">${userDTO.middleName} </c:if>
											<c:if test="${not empty userDTO.lastName}">${userDTO.lastName}</c:if>
										</td>
										<%-- <td><label>Middle Name</label></td>
										<td>${userDTO.middleName}</td>
										<td><label>Last Name</label></td>
										<td>${userDTO.lastName}</td> --%>
										<td><label>Email ID</label></td>
										<td>${userDTO.emailId}</td>
									</tr>
									<tr>
										<td><label>Mobile No</label></td>
										<td>${userDTO.mobileNo}</td>
										<td><label>Contact No </label></td>
										<td>${userDTO.contactNo}</td>
										<td><label>Street Address</label></td>
										<td>${userDTO.streetAddress}</td>
									</tr>
									<tr>
										<td><label>City</label></td>
										<td>${userDTO.cityName}</td>
										<td><label>State</label></td>
										<td>${userDTO.stateName}</td>
										
									</tr>
									<tr>
										<td><label>Pin No</label></td>
										<td>${userDTO.pincode}</td>
										<td><label>Employee ID</label></td>
										<td>${userDTO.empId}</td>
										<td></td>
										<td></td>
									<%-- 	<td><label>Designation</label></td>
										<td>${userDTO.designation}</td> --%>
									</tr>
									<tr>
										<td><label>User Type</label></td>
										<td>${userDTO.userType}</td>
										<td></td>
										<td></td>
										<td></td>
										<td></td>
									</tr>
									<tr>
										<td colspan="6">
											<div class="panel-heading-red  clearfix">
												<strong><span class="glyphicon glyphicon-info-sign"></span> <span
													data-i18n="Data">Role Information</strong> </span>
											</div>
										</td>
									</tr>
									<tr>
										<td colspan="6">
										
												<table class="table table-striped table-bordered" style="font-size: 12px">
													<caption style="display:none;">USER PROFILE</caption>
													<thead>
														<tr>
															<th scope="col">Role Name</th>
															<th scope="col">Role Description</th>
														</tr>
													</thead>
													<tbody>
														<c:if test="${not empty userDTO.roles}">
															<c:forEach var="role" items="${userDTO.roles}">
																<tr>
																	<td>${role.roleName}</td>
																	<td>${role.roleDesc}</td>
																</tr>
															</c:forEach>
														</c:if>
													</tbody>
												</table>
										
										</td>
									</tr>
							
									
									
								</tbody>
							</table>
							<div style ="text-align: center;">
	<button type="button" class="btn btn-danger"
		onclick="urlGetAction('/settleNxtHome')">
		<spring:message code="msg.lbl.back" />
	</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
