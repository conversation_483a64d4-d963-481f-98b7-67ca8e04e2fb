$(document).ready(function() {
	
	if ($('#refreshFlag').val() == "YES") {
		$('#autoReload').prop('checked', true);
	}
	
	setTimeout(autoRefresh,60000);
	
	$("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

         // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    searchBoxFunc(colIdx, cell, title, api);
                });
            $('#tabnew_filter').hide();

        },
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Report Status',
                header: 'false',
                title: null,
                sheetName: 'Report Status',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'Report Status' ,
                header:'false', 
                title: null,
                sheetName:'Report Status',
                className:'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                 }
            }

 

        ],

 

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });

 

        
        $('#button').click(function() {
        table.row('.selected').remove().draw(false);
    });

     $("#excelExport").on("click", function () {
                $(".buttons-excel").trigger("click");
            });

             $("#csvExport").on("click", function () {
                $(".buttons-csv").trigger("click");
            });

             $("#clearFilters").on("click", function () {
               $(".search-box").each(function() {
                       $(this).val("");
                         $(this).trigger("change");
                    });
            });

	if ($('#flag').val() == "YES") {
		getDataforCycle();
	}
	
	if($('#flag').val() != "YES"){
		$('select option[value="RPY"]').attr("selected",true);
		
		var defaultDate = $('#defaultDate').val();
		document.getElementById('cycleDate').value = defaultDate;
		
		var runningCycleInfo = $('#runningCycleInfo').val();
		 var option = $("<option>").attr("value",runningCycleInfo).text(runningCycleInfo);
		    $("#cycleNumber").append(option);
		    
		    
		
		
		}

	$("#cycleDate").datepicker({
		
		dateFormat : 'yy-mm-dd',
		todayHighlight: true,
		changeMonth : true,
		changeYear: true,
		maxDate:'0'
	});

	 $('#productCode').on('keyup keypress blur change',function () {
		 validateFromCommonVal('productCode', true, "SelectionBox", 11 , false);
		 getDataforCycle();
		 
	 });
	 
	 $('#cycleDate').on('keyup keypress blur change',function(){
	        validateFromCommonVal('cycleDate', true, "", "", false);
	        });
	 
	 $('#cycleNumber').on('keyup keypress blur change',function () {
		 validateFromCommonVal('cycleNumber', true, 'SelectionBox', 11 , false);
		 
	 });



});




function searchBoxFunc(colIdx, cell, title, api) {
    if (colIdx < actionColumnIndex) {
        $(cell).html(title + '<br><input class="search-box"   type="text" />');



        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function(_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';



                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();



                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
}

function autoRefresh(){
	
	
	if($('#autoReload').prop("checked")){
     refreshStatus();
	 }
	 setTimeout(autoRefresh,60000);
}

function refreshStatus(){
	
	 var productCode = $('#productCode').val();
		var cycleNumber = $('#cycleNumber').val();
		var refresh = $('#autoReload').is(':checked');
		
		var cycleDate = $('#cycleDate').val();
		var url = "/fetchCycleStatusDetails";
		var data = "productCode," + productCode + ",cycleNumber," + cycleNumber
		+ ",cycleDate," + cycleDate + ",refresh,"+refresh;
		
		postData(url, data);
	}

function resetAction() {


    $('#productCode').val("");
    $('#cycleNumber').val("");
    $('#cycleDate').val("");
    $('#errproductCode').hide();
    $('#errcycleDate').hide();
    $('#errcycleNumber').hide();
    $("#tabnew").DataTable().clear().draw();
}

function getDataforCycle() {

	
	var productCode = $("#productCode").val();
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;

	$.ajax({
		url : "getCycleData",
		type : "POST",
		data : {
			productCode : productCode,
			"_TransactToken" : tokenValue
		},
		dataType : "json",
		success : function(result) {

			for (var res of result) {
				
				var key = res;
				var value = res;

				
				     $('#cycleNumber')
				          .append($('<option>', { value : key })
				          .text(value));

			}
			 let a = $('#cycleNumbers').val();
			 document.getElementById('cycleNumber').value=a;


		}
	});

	$("#cycleNumber").empty();

}

function viewData() {
	

	var isValid = true;

	if (!validateFromCommonVal('productCode', true, 'SelectionBox', 100, false)) {
		isValid = false;
	}
	if (!validateFromCommonVal('cycleNumber', true, 'SelectionBox', 100, false)) {
		isValid = false;
	}

	if (!validateFromCommonVal('cycleDate', true, '', "", false)) {
		isValid = false;
	}

	
	
	
	if (isValid) {
		var productCode = $("#productCode").val();
		var cycleNumber = $("#cycleNumber").val();
		var cycleDate = $("#cycleDate").val();
		var refresh = $('#autoReload').is(':checked'); 

		let url = "/fetchCycleStatusDetails";

		var data = "productCode," + productCode + ",cycleNumber," + cycleNumber
				+ ",cycleDate," + cycleDate + ",refresh,"+refresh;
		
		postData(url, data);

	}
}
function viewChildInfo(guid) {
	
	let url = "/getCycleByGuid";
	let data = "guid," + guid;
	postData(url, data);
}