package org.npci.settlenxt.adminportal.service;

import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;

/**
 * Service Interface used for
 * <li>Report Internal cycle status</li>
 * <li>Update cycle status</li>
 * <AUTHOR>
 *
 */
public interface IReportInternalCycleStatusService {

	CycleManagementDTO getReportInternalCycleStatus(CycleManagementDTO cycleManagementDTO);

	String updateCycleStatus(CycleManagementDTO cycleManagementDTO);
}
