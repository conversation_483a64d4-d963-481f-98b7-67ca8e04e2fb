<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<script src="./static/js/moment.min.js"></script>
<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<script type="text/javascript" src="./static/js/jszip.min.js"></script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js"></script>

<script type="text/javascript" src="./static/js/validation/nfsFileUpload.js"></script>
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="./static/js/custom_js/vTransact.js"></script>
<style>
#overlay {
	display: none;
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 999;
	background: rgba(255, 255, 255, 0.8) url("./static/images/spinner.gif")
		center no-repeat;
}
</style>
<div id="body-content">
	<div class="space_block">
		<div class="alert alert-success" role="alert"
			id="netBinFileUploadInprogressMsg">
			<spring:message code="networkbin.fileUpload.inProgress.msg" />
		</div>
		<div class="alert alert-success" role="alert"
			id="netBinFileUploadSuccessMsg">
			<spring:message code="networkbin.fileUpload.success.msg" />
		</div>
		<div class="alert alert-danger" role="alert"
			id="netBinFileUploadErrorMsg">
			<spring:message code="E00001" />
		</div>
	</div>
</div>
<div>
	<div class="row" id="pgstatus"></div>
	<div id="overlay"></div>
	<div class="row">
		<div class="body-content">
			<div class="">
				<%-- <sec:authorize access="hasAuthority('Fees Master Bulk Upload')"> --%>
				<div class="col-md-12" id="uploadDiv">
					<div class="card">
						<div class="card-header">
							<div class="card-title">
								<spring:message code="fileUpload.uploadFile" />
							</div>
						</div>
						<div class="card-body">
							<div class="row">
								<form:form onsubmit="encodeForm(this);"
									action="bulkNFSFilesUpload" enctype="multipart/form-data"
									method="POST" id="bulkFilesUpload"
									modelAttribute="binDetailsDto" autocomplete="off">



									<div class="col-md-2">
										<div class="form-group">
											<label for="squareSelect">File Type</label>
											<form:select path="fileType" id="fileType"
												class="form-control">
												<form:option value="">
													<spring:message code="msg.lbl.select"></spring:message>
												</form:option>
												<form:options itemLabel="code" itemValue="code"
																			items="${netBinfiletypeList}" />
											</form:select>
											<div id="errfileType" class="error"></div>
										</div>
									</div>


									<div class="col-md-3">
										<div class="form-group">
											<label> <spring:message code="fileUpload.selectFile" />
												<span style="color: red">*</span>
											</label>
											<form:input name="fileArr[]" id="fileArr" path="fileArr"
												type="file" multiple="true" />
											<label id="title">(.dat or .txt or .xml formats
												allowed.)</label>
											<div id="errFile" class="error"></div>
										</div>
									</div>
									<div class="col-md-3">
										<div class="form-group">
											<div class="form-group">
												<button type="button" class="btn btn-primary" id="uploadId">
													<spring:message code="fileUpload.uploadFile" />
												</button>
												<button class="btn btn-default" id="reset" type="reset">
													<spring:message code="fileUpload.reset" />
												</button>
												<button type="button" class="btn btn-danger" id = "back"
										onclick="backCall('/netBinFileUpload');"><spring:message code="budget.backBtn" /></button>
												
											</div>
										</div>
									</div>
								</form:form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>