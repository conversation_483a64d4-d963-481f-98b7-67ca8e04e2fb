package org.npci.settlenxt.adminportal.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NetworkFileSummaryDTO {
    private String fileName;
    private int totalSuspended;
    private int totalOutGoing;
    private int totalAcknowledged;
    private int totalRejected;
    private int totalTransactions;
}
