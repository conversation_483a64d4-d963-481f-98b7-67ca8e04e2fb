<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/viewMccTipSurcharge.js"
	type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">
		 <form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewMccTipSurcharge" modelAttribute="mccTipSurchargeDto"
			action="${approveMccTipSurchargeStatus}" autocomplete="off"> 
			<input type="hidden" id="mccTipSurchargeId" value="${mccTipSurchargeDto.mccTipSurchargeId}" />
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="mccTipSurcharge.viewscreen.title" /></span></strong>
						</div>
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">Mcc Tip Surcharge</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
										
									<td><label><spring:message code="mccTipSurcharge.mccTipSurchargeId" /></label></td>
									<td>${mccTipSurchargeDto.mccTipSurchargeId }</td>
									<td><label><spring:message code="mccTipSurcharge.tipSurchargeId" /></label></td>
									<td >${mccTipSurchargeDto.tipSurchargeLookup }</td>
									<td><label><spring:message code="mccTipSurcharge.mccId" /></label></td>
									<td >${mccTipSurchargeDto.mccNameLookup }</td>
									
									</tr>
									
								</tbody>
							</table>
						</div>
					</div>
				</div>
					</div>	
		</form:form>
		<div class="row">
			<div class="col-sm-12 bottom_space ">
				<hr />
				<div style="text-align:center">
					<button type="button" class="btn btn-danger"
						onclick="userAction('N','/mccTipSurchargeMain');">
						<spring:message code="mccTipSurcharge.backBtn" /></button>
					<c:if test="${mccTipSurchargeDto.requestState =='A' }">	
					<sec:authorize access="hasAuthority('Edit MCC Tip Surcharge')">
						<input name="editButton" type="button" class="btn btn-success"
						 id="approveRole" value="Edit" 
						onclick="EditMccTipSurcharge('/editMccTipSurcharge','${mccTipSurchargeDto.mccTipSurchargeId}','mainPage');"/>
					</sec:authorize>
					</c:if>

				</div>
			</div>
		</div>
	</div>

</div>
