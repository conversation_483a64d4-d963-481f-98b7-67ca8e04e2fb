var jsonValidatorList = [];
var index = 0;
var datas = {};
var api;var loc;
var fieldName;
var code;
var regexps;
var reasonCode;
var reasonCodeDesc;
var pcode;
var priority;
var isMandatory;
var status;
var reasonCodeArr = [];
$(document).ready(function () {
	api = $('#api').val();
	fieldName = $('#fieldName').val();
	code = $('#code').val();
	regexps = $('#regexps').val();
	reasonCode = $('#reasonCode').val();
	reasonCodeDescValue();	
	reasonCodeDesc = $('#reasonCodeDesc').val();
	pcode = $('#pcode').val();
	priority = $('#priority').val();
	isMandatory = $('#isMandatory').val();
	status = $('#status').val();
    $("#errreasonCode").hide();
    $("#errreasonCodeDesc").hide();
    $("#errapi").hide();
    $("#errcode").hide();
    $("#errfieldName").hide();
    $("#errisMandatory").hide();
    $("#errpcode").hide();
    $("#errpriority").hide();
    $("#errregexps").hide();
    $('#bSubmit').prop('disabled', true);
    $('#bUpdate').prop('disabled', true);
    $('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
    $('#api').on('keyup keypress blur change', function() {
    	validateFromCommonVal('api', true, "SelectionBox", 3, false);
    	validateEditJsonValidator();
	});
    $('#fieldName').on('keyup keypress blur change', function() {
    	validateFromCommonVal('fieldName', true, "Alphabet", 2, false);
    	validateEditJsonValidator();
	});
    $('#code').on('keyup keypress blur change', function() {
    	validateFromCommonVal('code', true, "NumberOnly", 3, true);
    	validateEditJsonValidator();
	});
    $('#code').on('keyup keypress blur change', function() {
    	validateEditJsonValidator();
	});
    $('#regexps').on('keyup keypress blur change', function() {
    	validateRegExpsVal('regexps');
    	validateEditJsonValidator();
	});
    $('#reasonCode').on('keyup keypress blur change', function() {
    	validateReasonAndProcessingCodeField('reasonCode');
    	reasonCodeArr = $('#reasonCode').val().split("|");
    	document.getElementById("reasonCodeDesc").value = reasonCodeArr[1];
    	validateEditJsonValidator();
    	});
    
    $('#pcode').on('keyup keypress blur change', function() {
    	validateReasonAndProcessingCodeField('pcode');
    	validateEditJsonValidator();
	});
    $('#priority').on('keyup keypress blur change', function() {
    	validateFromCommonVal('priority', true, "NumberOnly", 2, false);
    	validateEditJsonValidator();
	});
    $('#isMandatory').on('keyup keypress blur change', function() {
    	validateEditJsonValidator();
	});
    $('#status').on('keyup keypress blur change', function() {
    	validateEditJsonValidator();
	});
   
});

function validateRegExpsVal(fieldId){
		let isValid = true;
		let fieldValue = $("#" + fieldId).val();
		if (fieldValue.trim() == "" || (!regex.test(fieldValue))) {
			isValid = false;
		}
		return createErrorMsg(isValid, fieldId);
}

function validateReasonAndProcessingCodeField(fieldId) {
	let fieldValue = $("#" + fieldId).val();
	let isValid = true;
	if (fieldValue === "") {
		isValid = false;
	}
	return createErrorMsg(isValid, fieldId);
}

function createErrorMsg(isValid, fieldId) {
    if (isValid) {
        $("#err" + fieldId).hide();
    } else {
        const errMsg = validationMessages[fieldId];
  
                if (errMsg) {
                    $("#err" + fieldId).find('.error').html(errMsg);
                }
        
        $("#err" + fieldId).show();
    }
    return isValid;
}



function resetAction() {
document.getElementById("addJsonValidator").reset();
$("#errreasonCode").find('.error').html('');
$("#errreasonCodeDesc").find('.error').html('');
$("#errapi").find('.error').html('');
$("#errcode").find('.error').html('');
$("#errfieldName").find('.error').html('');
$("#errisMandatory").hide();
$("#errpcode").find('.error').html('');
$("#errpriority").find('.error').html('');
$("#errregexps").find('.error').html('');
}


function userAction(_type, action) {
	let url = action;
	var data = "seqId,"
		+ parseInt($('#seqId').val());
	postData(url, data);
} 

function addOrUpdateReasonCode(id) {
	loc = window.location;
	
	if (id == 'A' && jsonValidatorList.length >= 1) {		
		addOrUpdateJsonValidatorAll();
	}else if (id == 'E') {
		let url = '/updateJsonValidator';
		if (validateField()) {
	    	if(reasonCodeArr.length == 0){
	    		reasonCodeArr = $("#reasonCode").val().split("|"); 
	    	}
	    	regexps  = $('#regexps').val();
	    	console.log( $('#isMandatory').val())
	        var data = "reasonCode," + reasonCodeArr[0] +",status,"+ $('#status').val() + ",reasonCodeDesc," + $('#reasonCodeDesc').val() + ",api," + $('#api').val() +",code," + $('#code').val() +",fieldName," + $('#fieldName').val()  +",pcode," + $('#pcode').val() +",priority," + $('#priority').val()+",isMandatory,"+$('#isMandatory').val()+",seqId,"+$('#seqId').val() +",regexps," + regexps;
	        console.log(data)
	    	addJsonValidator(url, data);
	    }
	}else{
		alert("Please add the record first");
	}
}

function validateField() {
	var isValid = true;

	if (!validateFromCommonVal('api', true, "SelectionBox", 3, false)) {
		isValid = false;
	}
	if (!validateFromCommonVal('fieldName', true, "Alphabet", 2, false)) {
		isValid = false;
	}
	if (!validateFromCommonVal('code', true, "NumberOnly", 3, true)) {
		isValid = false;
	}
	if (!validateRegExpsVal('regexps')) {
		isValid = false;
	}
	if (!validateReasonAndProcessingCodeField('reasonCode')) {
		isValid = false;
	}
	
	if (!validateReasonAndProcessingCodeField('pcode')) {
		isValid = false;
	}
	if (!validateFromCommonVal('priority', true, "NumberOnly", 2, false)) {
		isValid = false;
	}
	return isValid;
}


function postDiscardReasonCodeAction(action) {
	
	var url = action;
	
	var data = "seqId," + $('#seqId').val();
  postData(url, data);
	
}


function addJsonValidator(action, data) {
	loc = window.location;var dynInput;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	let linkurl = pathName + action;
	var form = document.createElement("form");
	form.method = "POST";
	var tokenValue=null
	if(document.getElementsByName("_TransactToken")[0])
		tokenValue= document.getElementsByName("_TransactToken")[0].value;
	tokenValue = $('<div>').text(tokenValue).html();
	var parameters = data.split(",");

	for (var i = 0; i < 19; ++i) {
		dynInput = document.createElement("input");
		dynInput.setAttribute("type", "hidden");
		dynInput.setAttribute("id", parameters[i]);
		dynInput.setAttribute("name", parameters[i]);
		++i;
		dynInput.setAttribute("value", parameters[i]);

		form.appendChild(dynInput);
	}
	dynInput = document.createElement("input");
	dynInput.setAttribute("type", "hidden");
	dynInput.setAttribute("id", parameters[20]);
	dynInput.setAttribute("name", parameters[20]);
	var regex = parameters.slice(21).join();
	console.log(regex);
	dynInput.setAttribute("value", regex);
	form.appendChild(dynInput);
	dynInput = document.createElement("input");
	dynInput.setAttribute("type", "hidden");
	dynInput.setAttribute("id", "_TransactToken");
	dynInput.setAttribute("name", "_TransactToken");
	dynInput.setAttribute("value", tokenValue);
	form.appendChild(dynInput);
	
	document.body.appendChild(form); // added this	for firefox Browser
	encodeForm(form);	//Added by piyush for form encode

	form.action = linkurl;
	form.submit();
}


function saveRejectReasonCode() {
	 $('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
	$('#snxtSuccessMessage').hide();
	if(validateField()){
	$('#bSubmit').prop('disabled', false);
	datas['id'] = index; 
	datas['api'] = document.getElementById('api').value;
	datas['fieldName'] = document.getElementById('fieldName').value;
	datas['code'] = document.getElementById('code').value;
	datas['regexps'] = document.getElementById('regexps').value;
	datas['status'] = document.getElementById('status').value;
	datas['reasonCode'] = reasonCodeArr[0];
	datas['reasonCodeDesc'] = document.getElementById('reasonCodeDesc').value;
	datas['priority'] = document.getElementById('priority').value;
	datas['pcode'] = document.getElementById('pcode').value;
	datas['isMandatory'] = document.getElementById('isMandatory').value;
	jsonValidatorList.push(datas);
	
	
	var apiNode = document.createTextNode(datas['api']);
var fieldNameNode = document.createTextNode(datas['fieldName']);
var codeNode = document.createTextNode(datas['code']);
var regexpsNode = document.createTextNode(datas['regexps']);
var reasonCodeNode = document.createTextNode(datas['reasonCode']);
var reasonCodeDescNode = document.createTextNode(datas['reasonCodeDesc']);
var priorityNode = document.createTextNode(datas['priority']);
var pcodeNode = document.createTextNode(datas['pcode']);
var isMandatoryNode = document.createTextNode(datas['isMandatory']);

	if (jsonValidatorList.length > 0) {
		var id = jsonValidatorList.map(e => e.id).indexOf(datas['id']);
		$('#tabnew').hide();
			
	
	$('#tabnew').append(
                  $('<tr>').attr('id', 'tabnew_' + id).append(
                    $('<td>').append(apiNode),
                    $('<td>').append(fieldNameNode),
                    $('<td>').append(codeNode),
                    $('<td>').append(regexpsNode),
                    $('<td>').append(reasonCodeNode),
                    $('<td>').append(reasonCodeDescNode),
                    $('<td>').append(priorityNode),
                    $('<td>').append(pcodeNode),
                    $('<td>').append(isMandatoryNode),
                    $('<td>').append($('<input>').attr({
                      'type': 'button',
                      'class': 'btn btn-danger remJsonValidator',
                      'onclick': 'removeJsonValidatorList(' + id + ')',
                      'value': 'Remove',
                    }))
                  )
                );

 
	
	$('#tabnew').show();
		index++;
	} else {
		$('#tabnew').hide();
	}
	clear();
	}

}

function removeJsonValidatorList(id) {
	if (jsonValidatorList.length > 0) {
		$(`#tabnew_${id}`).remove();
	} else {
		$(`#tabnew_${id}`).remove();
		$('#tabnew').hide();
	}
	jsonValidatorList.splice(id, 1);
}

function addOrUpdateJsonValidatorAll() {
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	if (jsonValidatorList.length > 0) {
		loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		var linkurl = pathName + "/addAllJsonValidator";
		console.log(jsonValidatorList)
		$.ajax({
			url: linkurl,
			type: "POST",
			dataType: "json",
			data: JSON.stringify(jsonValidatorList),
			"beforeSend": function (xhr) {
               xhr.setRequestHeader('_TransactToken', tokenValue);
           },
			contentType: "application/json; charset=utf-8",
			cache: false,
			success: function(response) {
				console.log(response)
				 if (response.status == "BSUC_0001") {
					 document.querySelectorAll('.remJsonValidator').forEach(item => item.disabled = true);
				document.getElementById('addmultiple').disabled = true;
				document.getElementById('bSubmit').disabled = true;
				document.getElementById('resetJsonValidator').disabled = true;
				document.getElementById('addJsonValidator').disabled = true;
				[...document.querySelectorAll("#addJsonValidator .row")].splice(0, 3).forEach(item => item.remove())
				$('#transitionSuccessMsg').show();
		
				 }
				 else {
					 $('#transitionErrorMsg').show();
				 }
				 
			},
			 error: function(_request, _status, _error) {
	                document.getElementById('transitionErrorMsg').style.display = 'block';
	                $('.panel').hide();
	            }
		});
	} else {
		alert("Please add the record first");
	}
}

function clear() {
	$('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
	$('#snxtSuccessMessage').hide();
	document.getElementById('api').value = "";
	document.getElementById('fieldName').value = "";
	document.getElementById('code').value = "";
	document.getElementById('regexps').value = "";
	document.getElementById('reasonCode').value = "";
	document.getElementById('reasonCodeDesc').value = "";
	document.getElementById('pcode').value = "";
	document.getElementById('priority').value = "";
	document.getElementById('isMandatory').value = "false";
	datas = {};
}

function validateEditJsonValidator() {
	if (api != document.getElementById("api").value || fieldName != document.getElementById("fieldName").value || code != document.getElementById("code").value || regexps != document.getElementById("regexps").value || reasonCode != reasonCodeArr[0] || reasonCodeDesc != document.getElementById("reasonCodeDesc").value || pcode != document.getElementById("pcode").value || isMandatory != document.getElementById("isMandatory").value || priority != document.getElementById("priority").value ||  status != document.getElementById("status").value) {
		if ($("#bUpdate")) {
			$("#bUpdate").prop("disabled", false);
		}
	} else {
		if ($("#bUpdate")) {
			$("#bUpdate").prop("disabled", true);
		}
	}
}

function reasonCodeDescValue(){
	if($('#reasonCode').val().includes("|"))
		document.getElementById("reasonCodeDesc").value=  $('#reasonCode').val().split("|")[1];
}
