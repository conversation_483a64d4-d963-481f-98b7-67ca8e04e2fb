package org.npci.settlenxt.adminportal.controllers;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.service.IFileUploadService;
import org.npci.settlenxt.portal.common.controllers.BaseFileUploadController;
import org.npci.settlenxt.portal.common.dto.CashBackFileUploadDTO;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants.MonthEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;



/**
 * Controller used for
 * <li>View file upload screed</li>
 * <li>Bulk file upload</li>
 * <li>Search file from fileupload table</li>
 * <li>Stage uploaded file</li>
 * <li>View rejected file details</li>
 * <AUTHOR>
 *
 */

@Controller
public class FileUploadController extends BaseFileUploadController {
	
	@Autowired
	IFileUploadService fileUploadService;

	@PostMapping("/filesUploadView")
	public String memberFileBulkUploadList(Model model) {
		return memberFileBulkUploadListCommon(model);
	}
	
	
	
	@PostMapping("/searchFileUploadList")
	public String searchFileUploadList(Model model, @ModelAttribute("fileUploadDTO") FileUploadDTO fileUploadDTO,
			@RequestParam(value = "fromDateStrr", required = false) String fromDateStr,
			@RequestParam(value = "toDateStrr", required = false) String toDateStr,
			@RequestParam(value = "docNamer", required = false) String docName,
			@RequestParam(value = "statusr", required = false) String status,
			@RequestParam(value = "fromDateDb", required = false) String fromDateDb,
			@RequestParam(value = "toDateDb", required = false) String toDateDb) {
		FileUploadDTO fileUploadDTOParams = new FileUploadDTO();
		setFileUploadDTOCommon(fromDateStr, toDateStr, docName, status, fileUploadDTOParams);
		return searchFileUploadListCommon(model, fileUploadDTO, fileUploadDTOParams, fromDateDb,
				toDateDb);
	}
	@PostMapping("/bulkFilesUpload")
	public ResponseEntity<Object> memberFileBulkUpload(
			@RequestParam("document") List<MultipartFile> files) {
		return memberFileBulkUploadCommon(files,null);
	}

	@PostMapping("/stageUploadedFiles")
	public String stageUploadedFiles(Model model, @ModelAttribute("fileUploadDTO") FileUploadDTO fileUploadDTO) {
		return stageUploadedFilesCommon(model, fileUploadDTO);
	}
	
	
	
	@PostMapping("/filesRejectView")
	public String fileRejectViewByFileId(Model model, @RequestParam("documentId") String documentId,
			@RequestParam("fromDateStr") String fromDateStr, @RequestParam("toDateStr") String toDateStr,
			@RequestParam("docName") String docName, @RequestParam("status") String status,
			@RequestParam("fromDate") String fromDate, @RequestParam("toDate") String toDate) {
		FileUploadDTO fileUploadDTOParams = new FileUploadDTO();
		setFileUploadDTOCommon(fromDateStr, toDateStr, docName, status, fileUploadDTOParams);
		return fileRejectViewByFileIdCommon(model, documentId, fileUploadDTOParams, fromDate,
				toDate);
	}
	@PostMapping("/fileUpload")
	@PreAuthorize("hasAuthority('CashBack File Upload')")
	public ResponseEntity<String> cashBackFileUpload(Model model, @RequestParam("year") String year,
			@RequestParam("month") String month, @RequestParam("document") MultipartFile file) {
		CashBackFileUploadDTO cashbackDTO = new CashBackFileUploadDTO();
		cashbackDTO.setYear(year);
		cashbackDTO.setMonth(month);
		cashbackDTO.setFileArr(file);
		String result = fileUploadService.cashBackFileUpload(cashbackDTO);
		if(StringUtils.equals(result, SUCCESS)) {
			return new ResponseEntity<>(SUCCESS, HttpStatus.OK);
		} else if(StringUtils.equals(result, "RECMISMATCH")) {
			return new ResponseEntity<>("RecMismatch", HttpStatus.OK);
		} else {
			return new ResponseEntity<>("Error", HttpStatus.SERVICE_UNAVAILABLE);
		}
	}	
	@PostMapping("/getFileUploadCycleNumber")
	public ResponseEntity<Object> getFileUploadCycleNumber() {
		return getFileUploadCycleNumberCommon();
	}
	@PostMapping("/discardFile")
	public String discardFile(Model model, @RequestParam("documentId") String documentId,
			@RequestParam("documentName") String documentName) {
		return discardFileCommon(model, documentId, documentName);
	}
	
	@PostMapping("/cashbackFileUpload")
	@PreAuthorize("hasAuthority('CashBack File Upload')")
	public String cashBackFileUploadView(Model model) {
		CashBackFileUploadDTO cashbackDTO = new CashBackFileUploadDTO();
		Map<String, String> monthMap = MonthEnum.getMonthMap();
		Map<String, String> yearMap = new HashMap<>();
		List<CashBackFileUploadDTO> cashBackFilesList = fileUploadService.searchCashBackFiles();
		yearMap.put(String.valueOf(LocalDate.now().getYear()), "Current Year");
		yearMap.put(String.valueOf(LocalDate.now().getYear() - 1), "Previous Year");
		model.addAttribute("monthList", monthMap);
		model.addAttribute("yearList", yearMap);
		model.addAttribute("cashBackDTO", cashbackDTO);
		model.addAttribute("cashBackFilesList", cashBackFilesList);
		return getView(model, "cashbackFileUpload");
	}
	
	
}
