$(document).ready(function () {    

    if ($("#apprej").length) {

        $('.appRejMust').hide();
        $('.remarkMust').hide();

        $('#apprej').change(function () {
            if ($("#apprej").val() != "N") {
                $(".appRejMust").hide();
            } else {
                $(".appRejMust").show();
                $(".remarkMust").hide();
            }
        });

    }

});



window.history.forward();
function noBack() {
    window.history.forward();
}

function display() {
    $(".appRejMust").hide();

}
function userAction(action) {

	var holidaySeqId = $("#holidaySeqId").val(); 
	
		holidaySeqId=btoa(holidaySeqId);
	var data = "holidaySeqId," + holidaySeqId;
	console.log(data);
	postData(action, data);
}

function loadDelete(hid,reqst,action) {
    let url = action;
    hid=btoa(hid);
    var data = "hid," + hid +",reqst,"+ reqst + ",userType," + 'N';
    postData(url, data);
}


 function postAction(_action) {
	var data="";
	var holidaySeqId;
	var url="";
	var remarks="";
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 holidaySeqId = $("#holidaySeqId").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveOrRejectHoliday';
				 holidaySeqId=btoa(holidaySeqId);
				 data = "holidaySeqId," + holidaySeqId + ",status," + "A" + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				 holidaySeqId = $("#holidaySeqId").val(); 
				 remarks=$("#rejectReason").val();
		 holidaySeqId=btoa(holidaySeqId);
				 url = '/approveOrRejectHoliday';
				 data = "holidaySeqId," + holidaySeqId + ",status," + "R"  + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
	
			 
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		