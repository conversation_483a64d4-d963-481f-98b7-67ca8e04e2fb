<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="ISO-8859-1">
<title>Insert title here</title>
</head>
<body>
	<table class="table table-striped">
		<caption style="display:none;">View Issuer Bin List</caption> 
		<thead>
			<tr>
				<th scope="col"><label for="squareInput">Participant ID</label></th>
				<th scope="col"><label for="squareInput">Bin Number </label></th>
				<th scope="col"><label for="squareInput">Bin Card Type </label></th>
				<th scope="col"><label for="squareInput">Bin Product Type </label></th>
				<th scope="col"><label for="squareInput">Bin Card Variant</label></th>
				<th scope="col"><label for="squareInput">Product Type</label></th>
				<th scope="col"><label for="squareInput">Bin Card Brand</label></th>
				<th scope="col"><label for="squareInput">Message Type</label></th>
				<th scope="col"><label for="squareInput">Card Technology</label></th>
				<th scope="col"><label for="squareInput">Authentication Mechanism</label></th>
				<th scope="col"><label for="squareInput">Settlement BIN</label></th>
				<th scope="col"><label for="sqareInput">Bin Type</label>
				<th scope="col"><label for="sqareInput">Status</label>
				<th scope="col"><label for="squareInput">Action</label></th>
			</tr>
		</thead>
		<tbody id="issuerBinsList">
			<c:forEach var="bin" items="${memberDTO.issBinList}"
				varStatus="status">
				<tr id="${status.index}">
					<td>${bin.participantId}</td>
					<td>${bin.binNumber}</td>

					<td><c:out value="${binCardTypeMap[bin.binCardType]}" /></td>

					<td><c:out value="${binProdTypeMap[bin.binProductType]}" /></td>

					<td><c:out value="${binCardVariantMap[bin.binCardVariant]}" />
					<td><c:out value="${issProductTypeMap[bin.issProductType]}" />
				</td>
					<td><c:out value="${binCardBrandMap[bin.binCardBrand]}" /></td>
					<td><c:out value="${messageTypeMap[bin.messageType]}" /></td>
						<td><c:out value="${cardTechnologyMap[bin.cardTechnology]}" /></td>
				<td><c:out value="${authMechanismMap[bin.authMechanism]}" /></td>
					<td>${bin.issSettlementBin}</td>
					<c:if test="${(bin.binType eq 'I')}">
						<td>Issuer Bin</td>
					</c:if>
					<c:if test="${(bin.binType eq 'T')}">
						<td>Token Bin</td>
					</c:if>
					
						<c:if test="${(bin.status eq 'A')}">
						<td>Active</td>
					</c:if>
					<c:if test="${(bin.status eq 'B')}">
						<td>Blocked</td>
					</c:if>
					
					
					<td><input type="button" id="viewIssBin" value="VIEW"
						class="btn btn-success"
					    onclick="populateIssBinDtls('${bin.isIssOfflineAllowed}','${bin.featureIssBin}','${bin.binType}','${bin.issBankGroup}','${bin.binNumber}','${binCardTypeMap[bin.binCardType]}','${binProdTypeMap[bin.binProductType]}','${binCardVariantMap[bin.binCardVariant]}',
																								'${issProductTypeMap[bin.issProductType]}','${binCardBrandMap[bin.binCardBrand]}','${messageTypeMap[bin.messageType]}','${cardTechnologyMap[bin.cardTechnology]}','${authMechanismMap[bin.authMechanism]}','${bin.issSettlementBin}','${bin.lowBin}','${bin.highBin}','${bin.panLength}',
																								'${domainUsageMap[bin.issDomainUsage]}','${bin.subScheme}','${bin.cardSubVariant}','${bin.programDetails}','${bin.formFactor}','${bin.issFrmDateStr}','${bin.issToDateStr}')">  
				
				</tr>
			</c:forEach>
		</tbody>
	</table>

</body>
</html>