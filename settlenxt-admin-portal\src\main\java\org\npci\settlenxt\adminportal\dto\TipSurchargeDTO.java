package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class TipSurchargeDTO {

	private int tipSurchargeId;
	private String tipSurchargeName;
	private String tipSurchargeType;
	private String operator;
	private String binCardType;
	private String binCardBrand;
	private Double settlementAmount;
	private Double flat;
	private Double amount;
	private Double percentage;
	private String title;
	private String amountPercentFlag;
	private String addEditFlag;
	private String checkerComments;
	private String lastOperation;
	private String requestState;

	private String cardType;
	private String cardBrand;
	private String tipSurType;
	private String operatorName;
	private String amtFlag;

	private String status;
	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private String userName;
}
