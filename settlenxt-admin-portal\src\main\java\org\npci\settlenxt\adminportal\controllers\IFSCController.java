package org.npci.settlenxt.adminportal.controllers;

import java.util.Date;
import java.util.List;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.IFSCDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.IFSCService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Controller class for IFSC configurations including approval workflow
 *
 */
@Controller
public class IFSCController extends BaseController {

	private static final String LANDING_MAIN = "main";
	private static final String LANDING_APPROVE = "approve";
	private static final String LANDING_PARAM = "landing";
	private static final String VIEW_APPROVE_BASE_SCREEN = "ViewApproveBaseScreen";
	private static final String ADD_FLOW = "addFlow";
	private static final String EDIT_FLOW = "editFlow";
	private static final String VIEW_APPROVE_IFSC = "ViewApproveIFSC";
	private static final String IFSC_DTO = "ifscDTO";
	private static final String ADD_EDIT_VIEW_IFSC = "addEditViewIFSC";
	private static final String SHOW_IFSC = "showIFSC";
	private static final String YES_FLAG = "Y";
	private static final String IFSCLIST = "ifsclist";
	private static final String SHOW_MAIN_TAB = "showMainTab";
	private static final String SHOW_ADD_BUTTON = "showAddButton";
	private static final String AFTERSAVE = "afterSave";
	private static final String SHOW_CHECK_BOX = "showCheckBox";
	private static final String APPROVE = "approve";
	
	@Autowired
	IFSCService ifscService;

	@Autowired
	private SessionDTO sessionDTO;

	/**
	 * Controller method for getting approved IFSC list for main tab
	 * 
	 * @param model Model
	 * @return Show IFSC View Name
	 */
	@PostMapping(SHOW_IFSC)
	@PreAuthorize("hasAuthority('View IFSC')")
	public String getIFSCList(
			@RequestParam(value = LANDING_PARAM, required = false, defaultValue = LANDING_MAIN) String landingParam,
			Model model) {
		model.addAttribute(LANDING_PARAM, LANDING_MAIN);
		try {
			model.addAttribute(SHOW_ADD_BUTTON, YES_FLAG);
			model.addAttribute(SHOW_MAIN_TAB, YES_FLAG);
			List<IFSCDTO> approvedIFSCDTOList = ifscService.getApprovedIFSCList();
			model.addAttribute(IFSCLIST, approvedIFSCDTOList);

		} catch (Exception ex) {
			model.addAttribute(IFSCLIST, null);
			return handleErrorCodeAndForward(model, SHOW_IFSC, ex);
		}

		return getView(model, SHOW_IFSC);
	}

	/**
	 * Controller method for getting IFSC list that are pending for approval or
	 * rejected for approval tab
	 * 
	 * @param model Model
	 * @return Show IFSC View Name
	 */
	@PostMapping("ifscPendingForApproval")
	@PreAuthorize("hasAuthority('View IFSC')")
	public String getPendingssIFSCList(
			@RequestParam(value = LANDING_PARAM, required = false, defaultValue = LANDING_MAIN) String landingParam,
			Model model) {
		model.addAttribute(LANDING_PARAM, LANDING_APPROVE);
		try {
			model.addAttribute("showApprovalTab", YES_FLAG);
			List<IFSCDTO> pendingIFSCDTOList = ifscService.getPendingForAppovalIFSCList();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(IFSCLIST, pendingIFSCDTOList);

		} catch (Exception ex) {
			model.addAttribute(IFSCLIST, null);
			return handleErrorCodeAndForward(model, SHOW_IFSC, ex);
		}
		return getView(model, SHOW_IFSC);
	}

	/**
	 * Controller method for getting IFSC details for View screen
	 * 
	 * @param ifscCode IFSC Code
	 * @param model    Model
	 * @return View IFSC view name
	 */
	@PostMapping("/getIFSC")
	@PreAuthorize("hasAuthority('View IFSC')")
	public String getIFSC(
			@RequestParam(value = LANDING_PARAM, required = false, defaultValue = LANDING_APPROVE) String landingParam,
			@RequestParam("ifscCode") String ifscCode, Model model) {
		IFSCDTO ifscdto = new IFSCDTO();
		model.addAttribute(LANDING_PARAM, landingParam);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, "");
		try {
			if (LANDING_MAIN.equals(landingParam)) {
				ifscdto = ifscService.getIFSC(ifscCode);
			}
			if (APPROVE.equals(landingParam)) {
				ifscdto = ifscService.getIFSCStg(ifscCode);
			}

		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, ADD_EDIT_VIEW_IFSC, ex);
		}
		model.addAttribute(IFSC_DTO, ifscdto);
		model.addAttribute(AFTERSAVE, BaseCommonConstants.NO_FLAG);
		return getView(model, ADD_EDIT_VIEW_IFSC);
	}

	/**
	 * Controller method for getting IFSC details for edit screen
	 * 
	 * @param ifscCode IFSC Code
	 * @param model    Model
	 * @return Edit IFSC view name
	 */
	@PostMapping("/editIFSC")
	@PreAuthorize("hasAuthority('Edit IFSC')")
	public String editIFSC(
			@RequestParam(value = LANDING_PARAM, required = false, defaultValue = LANDING_MAIN) String landingParam,
			@RequestParam("ifscCode") String ifscCode, Model model) {
		IFSCDTO ifscdto = new IFSCDTO();
		model.addAttribute(EDIT_FLOW, YES_FLAG);
		model.addAttribute(LANDING_PARAM, landingParam);
		try {

			ifscdto = ifscService.getIFSCStg(ifscCode);
			String status = ifscdto.getRequestState();

			if (status.equals(CommonConstants.REQUEST_STATE_REJECTED)) {
				ifscdto = ifscService.getIFSCStg(ifscCode);

			} else {
				ifscdto = ifscService.getIFSC(ifscCode);
			}

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_VIEW_IFSC, ex);
		}
		model.addAttribute(IFSC_DTO, ifscdto);
		return getView(model, ADD_EDIT_VIEW_IFSC);
	}

	/**
	 * Controller method for getting IFSC details for Approval View screen
	 * 
	 * @param ifscCode IFSC Code
	 * @param model    Model
	 * @return View IFSC view name for approval
	 */
	@PostMapping("/viewApproveIFSC")
	@PreAuthorize("hasAuthority('View IFSC')")
	public String viewApproveIFSC(
			@RequestParam(value = LANDING_PARAM, required = false, defaultValue = LANDING_MAIN) String landingParam,
			@RequestParam("ifscCode") String ifscCode, Model model) {

		IFSCDTO ifscdto = new IFSCDTO();
		model.addAttribute(EDIT_FLOW, YES_FLAG);
		model.addAttribute(LANDING_PARAM, landingParam);
		try {
			if (LANDING_MAIN.equals(landingParam)) {
				ifscdto = ifscService.getIFSC(ifscCode);
			}
			if (APPROVE.equals(landingParam)) {
				ifscdto = ifscService.getIFSCStg(ifscCode);
			}

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_IFSC, ex);
		}
		model.addAttribute(VIEW_APPROVE_BASE_SCREEN, YES_FLAG);
		model.addAttribute(IFSC_DTO, ifscdto);
		return getView(model, VIEW_APPROVE_IFSC);
	}

	/**
	 * Controller method for loading add screen
	 * 
	 * @param model Model
	 * @return Add IFSC view name
	 */
	@PostMapping("/addIFSC")
	@PreAuthorize("hasAuthority('Add IFSC')")
	public String addIFSC(
			@RequestParam(value = LANDING_PARAM, required = false, defaultValue = LANDING_MAIN) String landingParam,
			Model model) {
		IFSCDTO ifscdto = new IFSCDTO();
		model.addAttribute(ADD_FLOW, YES_FLAG);
		model.addAttribute(IFSC_DTO, ifscdto);
		model.addAttribute(LANDING_PARAM, landingParam);
		return getView(model, ADD_EDIT_VIEW_IFSC);
	}

	/**
	 * Controller method for updating IFSC details
	 * 
	 * @param ifscdto IFSC Data
	 * @param model   Model
	 * @return View IFSC view name
	 */
	@PostMapping("/updateIFSC")
	@PreAuthorize("hasAuthority('Edit IFSC')")
	public String updateIFSC(
			@RequestParam(value = LANDING_PARAM, required = false, defaultValue = LANDING_MAIN) String landingParam,
			@ModelAttribute IFSCDTO ifscdto, Model model) {
		model.addAttribute(LANDING_PARAM, landingParam);
		try {
			ifscdto.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
			ifscdto.setLastUpdatedBy(sessionDTO.getUserName());
			ifscdto.setLastUpdatedOn(new Date());
			ifscdto.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
			ifscdto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
			ifscService.updateIFSC(ifscdto);

		} catch (Exception ex) {
			model.addAttribute(EDIT_FLOW, YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_EDIT_VIEW_IFSC, ex);
		}
		model.addAttribute(IFSC_DTO, ifscdto);
		model.addAttribute(AFTERSAVE, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("ifsc.updateSuccess.msg"));
		return getView(model, ADD_EDIT_VIEW_IFSC);
	}

	/**
	 * Controller method for saving IFSC details
	 * 
	 * @param ifscdto IFSC Data
	 * @param model   Model
	 * @return View IFSC view name
	 */
	@PostMapping("/saveIFSC")
	@PreAuthorize("hasAuthority('Add IFSC')")
	public String saveIFSC(
			@RequestParam(value = LANDING_PARAM, required = false, defaultValue = LANDING_MAIN) String landingParam,
			@ModelAttribute IFSCDTO ifscdto, Model model) {
		model.addAttribute(LANDING_PARAM, landingParam);
		try {
			Date currentDate = new Date();
			ifscdto.setCreatedBy(sessionDTO.getUserName());
			ifscdto.setCreatedOn(currentDate);
			ifscdto.setLastUpdatedBy(sessionDTO.getUserName());
			ifscdto.setLastUpdatedOn(currentDate);
			ifscdto.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
			ifscdto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
			ifscdto.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
			ifscService.saveIFSC(ifscdto);

		} catch (Exception ex) {
			model.addAttribute(IFSC_DTO, ifscdto);
			model.addAttribute(ADD_FLOW, YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_EDIT_VIEW_IFSC, ex);

		}
		model.addAttribute(IFSC_DTO, ifscdto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("ifsc.addSuccess.msg"));
		return getView(model, ADD_EDIT_VIEW_IFSC);
	}

	/**
	 * Controller method for approving IFSC details by checker
	 * 
	 * @param ifscCode IFSC Code
	 * @param status   Request status (A - Approved. R - Rejected)
	 * @param remarks  Checker comments
	 * @param model    Model
	 * @return View IFSC view name for approval
	 */
	@PostMapping("/approveIFSC")
	@PreAuthorize("hasAuthority('Approve IFSC')")
	public String approveIFSC(
			@RequestParam(value = LANDING_PARAM, required = false, defaultValue = LANDING_MAIN) String landingParam,
			@RequestParam("ifscCode") String ifscCode, @RequestParam("status") String status,
			@RequestParam("remarks") String remarks, Model model) {
		IFSCDTO ifscdto = new IFSCDTO();
		model.addAttribute(LANDING_PARAM, landingParam);
		try {
			ifscdto = ifscService.approveOrRejectIFSC(ifscCode, status, remarks);

		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_IFSC, ex);
		}
		model.addAttribute(IFSC_DTO, ifscdto);
		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("ifsc.approvalSuccess.msg"));
		} else if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("ifsc.rejectionSuccess.msg"));
		}
		return getView(model, VIEW_APPROVE_IFSC);
	}

	// For Checker Approval
	@PostMapping("/approveIFSCForBulk")
	@PreAuthorize("hasAuthority('Approve IFSC')")
	public String approveIFSCForBulk(@RequestParam("bulkApprovalReferenceNoList") String bulkApprovalReferenceNoList,
			@RequestParam("status") String status, Model model) {
		String successStatus = "";

		try {
			String remarks = "";

			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}
			successStatus = ifscService.approveOrRejectIFSCForBulk(bulkApprovalReferenceNoList, status, remarks);
			List<IFSCDTO> pendingIFSCDTOList = ifscService.getPendingForAppovalIFSCList();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute("showApprovalTab", YES_FLAG);
			model.addAttribute(IFSCLIST, pendingIFSCDTOList);
			model.addAttribute(CommonConstants.CARD_CONFIG_APP_PENDING, CommonConstants.TRANSACT_YES);
			if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_APPROVED)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("ifsc.approvalSuccess.msg"));
			} else if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_REJECTED)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("ifsc.rejectionSuccess.msg"));
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_IFSC, ex);
		}
		return getView(model, SHOW_IFSC);
	}

	/**
	 * Controller method for discarding Rejected IFSC entry by maker
	 * 
	 * @param ifscCode IFSC Code
	 * @param model    Model
	 * @return View IFSC view name for approval
	 */
	@PostMapping("/discardIFSC")
	@PreAuthorize("hasAuthority('Edit IFSC')")
	public String discardIFSC(
			@RequestParam(value = LANDING_PARAM, required = false, defaultValue = LANDING_MAIN) String landingParam,
			@RequestParam("ifscCode") String ifscCode, Model model) {
		IFSCDTO ifscdto = new IFSCDTO();
		model.addAttribute(LANDING_PARAM, landingParam);
		try {
			ifscdto = ifscService.discardIFSC(ifscCode);

		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_IFSC, ex);
		}
		model.addAttribute(IFSC_DTO, ifscdto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("ifsc.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_IFSC);
	}

}
