package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.SysParamsDTO;

public interface SysParamService {

	List<SysParamsDTO> getSysParamApprovedList();

	List<SysParamsDTO> getSysParamPendingList();

	SysParamsDTO getSysParamsInfoFromMain(String sysType, String sysKey);

	SysParamsDTO approveOrRejectSysParam(String sysType, String sysKey, String status, String remarks);

	SysParamsDTO getSysParamStgInfo(String sysType, String sysKey);

	void addSysParam(SysParamsDTO sysParamDTO);

	void updateSysParam(SysParamsDTO sysParamDTO);

	boolean checkDupSysDetails(String sysType, String sysKey);

	List<SysParamsDTO> getSysParamListType();

	SysParamsDTO discardSysParamInfo(String sysType, String sysKey);
	SysParamsDTO updateApproveOrRejectSysParamBulk(String[] bulkSysParamList, String status, String remarks);

}
