<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix = "fmt" uri = "http://java.sun.com/jsp/jstl/fmt" %>
<script src="./static/js/validation/viewForexRate.js"
type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">
		 <form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewForexRate" modelAttribute="forexRateDto"
			action="${approveForexRateStatus}" autocomplete="off"> 
			
			<input type="hidden" id="forexRateId" value="${forexRateDto.forexRateId}" />
			
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="forexRate.viewscreen.title" /></span></strong>

							<div class="icon_bar">
								<sec:authorize access="hasAuthority('Edit Forex Rates') ">
									<a data-toggle="tooltip" title="Edit"
										onclick="viewForexRateInfo('${forexRateDto.forexRateId}')"
										href="#"><img src="./static/images/edit-grey.png"
										alt="edit"></a>

								</sec:authorize>
							</div>
						</div>
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px"> <caption style="display:none;">FOREX RATE </caption>
								<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
										<sec:authorize access="hasAuthority('Approve Forex Rates')">
										<td><label><spring:message code="forexRate.forexRateId" /></label></td>
										<td id="forexRateId">${forexRateDto.forexRateId}</td>
										</sec:authorize>
										<td><label><spring:message code="forexRate.networkId" /></label></td>
										<td id="networkId">${forexRateDto.networkIdLookup}</td>
										<td></td>
										<td></td>
									</tr>
									<tr>
									<td><label><spring:message code="forexRate.rateConversion" /></label></td>
										<td id="rateConversion">${forexRateDto.rateConversion}</td>
										<td><label><spring:message code="forexRate.dateSettle" /></label></td>
										<td id="dateSettle"><fmt:formatDate pattern="yyyy-MM-dd" value="${forexRateDto.settleDate}" /></td>
										<td></td>
										<td></td>
									</tr>
									<tr>
										<td><label><spring:message code="forexRate.currencyFrom" /></label></td>
										<td id="currencyFrom">${forexRateDto.currencyFromLookup}</td>
										<td><label><spring:message code="forexRate.currencyTo" /></label></td>
										<td id="currencyTo">${forexRateDto.currencyToLookup}</td>
										</tr>
									</tbody>
							</table>
						</div>
					</div>
				</div>
					</div>	
		</form:form>
		<div class="row">
			<div class="col-sm-12 bottom_space ">
				<hr />
				<div style="text-align:center">
					<button type="button" class="btn btn-danger"
						onclick="userAction('/forexRates');">
						<spring:message code="forexRate.backBtn" /></button>
					
				</div>
			</div>
		</div>
	</div>

</div>
