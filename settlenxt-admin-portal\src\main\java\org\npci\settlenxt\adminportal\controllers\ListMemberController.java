package org.npci.settlenxt.adminportal.controllers;



import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.MemberService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;

@Controller
public class ListMemberController extends BaseController {
	@Autowired
	private SessionDTO sessionDTO;
	@Autowired
	private MemberService memberService;
	
	private static final String SEARCH_MEMBERS="searchMembers";
	private static final String IS_MAKER="IsMaker";
	private static final String  SHOW_FINAL_MEMBERS="showFinalMembers";
	private static final String MEMBER_LIST="memberList";
	private static final String SHOW_DOWNLOAD_BUTTON="showDownloadButton";
	private static final String SHOW_ADD_BUTTON="showAddButton";
	private static final String NETWORK="Network";
	private static final String SUB_MEMBER_BANK="Sub Member Bank";
	private static final String SPONSER_BANK="Sponsor Bank";
	private static final String I_TOTL_DISPLAY_RECORD="iTotalDisplayRecords";
	private static final String I_TOTA_RECORD="iTotalRecords";
	private static final String S_ECHO="sEcho";
	private static final String DATA="aaData";
	private static final String ERROR_MSG="errorMessage";
	private static final String ERROR_STATUS="errorStatus";
	
	private static final String MEM_LIST_PENDING="memListPending";
	private static final String DD_MM_YY="dd/MM/yyyy HH:mm:ss";
	private static final String EEE_MMM="EEE MMM dd HH:mm:ss zzz yyyy";
	private static final String NO="No";
	
	private static final String PENDING_MEMB_FR_APP="pendingMembersFrAppr";
	private static final String T_MEMB_LIST="TmemberList";
	private static final String SHOW_CHECK_BOX="showCheckBox";
	private static final String REJECT="R";
	private static final String IN_PROGRESS="In Progress";
	private static final String INACTIVE="I";
	private static final String ACTIVE="A";
	private static final String MEMBER="M";
	private static final String SHOW_SAVED_MEMB="showSavedMembers";
	private static final String SAVED_MEMB_LIST="savedMemberList";
	private static final String BULK_REJ_MSG="Member Bulk Rejected Successfully";
	private static final String BULK_APPROVE_MSG="Member Bulk Approved Successfully";
	private static final String NETWORK_BANK_TYPE = "network.bank.type";
	private static final String SPONSER_BANK_TYPE = "sponsor.bank.type";
	private static final String SUBMEM_BANK_TYPE = "submember.bank.type";
	private String isInternational;
	
	@PostConstruct
	void init() {
		isInternational = env.getProperty("is.international.enabled", "N");
	}
	
	@PostMapping("/showFinalMembers")
	@PreAuthorize("hasAnyAuthority('View To Add Member','View-Edit Member','View Member pending for approval')")
	public String showFinalMembers(Model model) {
		model.addAttribute(SHOW_DOWNLOAD_BUTTON, CommonConstants.YES);
		model.addAttribute(SHOW_ADD_BUTTON, CommonConstants.YES);
		model.addAttribute(MEMBER_LIST, MEMBER_LIST);
		model.addAttribute(SHOW_FINAL_MEMBERS, CommonConstants.YES);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(IS_MAKER, CommonConstants.YES_FLAG);
		} else {
			model.addAttribute(IS_MAKER, BaseCommonConstants.NO_FLAG);
		}
		return getView(model, SEARCH_MEMBERS);
	}

	@PostMapping("/searchFinalMembers")
	@PreAuthorize("hasAnyAuthority('View To Add Member','View-Edit Member','View Member pending for approval')")
	public ResponseEntity<Object> searchFinalMembers(@ModelAttribute("memberDTO") MemberDTO memberDTO,
			HttpServletResponse response, BindingResult result, Locale locale, ModelAndView modelAndView,
			ModelMap model) {

		String[] submemberBankTypes = StringUtils.split(env.getProperty(SUBMEM_BANK_TYPE), ",");
		String[] sponsorBankTypes = StringUtils.split(env.getProperty(SPONSER_BANK_TYPE), ",");
		String[] networkTypes = StringUtils.split(env.getProperty(NETWORK_BANK_TYPE), ",");

		int iDisplayStart = 0;
		int iDisplayLength = 0;
		String sSearch = "";
		String sEcho = "";

		JsonObject jsonResponse = new JsonObject();
		JsonArray data = new JsonArray();
		String errorMessage = "";
		String errorStatus = "";
		int iTotalRecords = 0;
		long iTotalDisplayRecords = 0;
		List<MemberDTO> memberList = null;
		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();

		if (!result.hasErrors()) {

			// Fetching request from datatable
			int startval = iDisplayStart + 1;
			int endval = iDisplayStart + iDisplayLength;
			searchCriteriaDTO.setStartVal(startval);
			searchCriteriaDTO.setEndVal(endval);
			searchCriteriaDTO.setSearchName(sSearch);
			memberList = memberService.getFinalMemberList(searchCriteriaDTO);

			iTotalRecords = 10; 
			 
			iTotalDisplayRecords = memberService.getRowCount();

			if (memberList != null && !memberList.isEmpty()) {
				jsonResponse.addProperty(S_ECHO, sEcho);
				jsonResponse.addProperty(I_TOTA_RECORD, iTotalRecords);
				jsonResponse.addProperty(I_TOTL_DISPLAY_RECORD, iTotalDisplayRecords);
				for (MemberDTO c : memberList) {
					handleRowAddForFinalMem(submemberBankTypes, sponsorBankTypes, networkTypes, data, c);
				}
			} 
		}
		
		jsonResponse.addProperty(ERROR_STATUS, errorStatus);
		jsonResponse.addProperty(ERROR_MSG, errorMessage);
		jsonResponse.addProperty(S_ECHO, sEcho);
		jsonResponse.addProperty(I_TOTA_RECORD, iTotalRecords);
		jsonResponse.addProperty(I_TOTL_DISPLAY_RECORD, iTotalDisplayRecords);
		jsonResponse.add(DATA, data);

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	private void handleRowAddForFinalMem(String[] submemberBankTypes, String[] sponsorBankTypes, String[] networkTypes,
			JsonArray data, MemberDTO c) {
		JsonArray row = new JsonArray();

		row.add(new JsonPrimitive(c.getParticipantId() == null ? "" : c.getParticipantId() + ""));
		row.add(new JsonPrimitive(c.getParticipantName() == null ? "" : c.getParticipantName()));

		if (StringUtils.containsAnyIgnoreCase(c.getBankType(), sponsorBankTypes)) {
			row.add(new JsonPrimitive(SPONSER_BANK));
		} else if (StringUtils.containsAnyIgnoreCase(c.getBankType(), submemberBankTypes)) {
			row.add(new JsonPrimitive(SUB_MEMBER_BANK));
		} else if (StringUtils.containsAnyIgnoreCase(c.getBankType(), networkTypes)) {
			row.add(new JsonPrimitive(NETWORK));
		} else {
			row.add(new JsonPrimitive(""));
		}
		row.add(new JsonPrimitive(c.getBankCode() == null ? "" : c.getBankCode()));

		row.add(new JsonPrimitive(c.getBnkAdd() == null ? "" : c.getBnkAdd()));
		row.add(new JsonPrimitive(c.getBnkPhone() == null ? "" : c.getBnkPhone()));
		row.add(new JsonPrimitive(c.getBnkEmail() == null ? "" : c.getBnkEmail()));
		if (c.getStatus().equalsIgnoreCase(CommonConstants.REQUEST_STATE_APPROVED)) {
			row.add(new JsonPrimitive(CommonConstants.YES));
		} else {
			row.add(new JsonPrimitive(NO));
		}

		row.add(new JsonPrimitive(
				c.getCreatedOn() == null ? "" : modifyDateLayout(c.getCreatedOn().toString()) + ""));

		row.add(new JsonPrimitive(c.getLastUpdatedOn() == null ? ""
				: modifyDateLayout(c.getLastUpdatedOn().toString()) + ""));

		row.add(new JsonPrimitive(MEMBER));
		row.add(new JsonPrimitive(ACTIVE));
		data.add(row);
	}

	private String modifyDateLayout(String inputDate) {
		
		
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(EEE_MMM);
		LocalDateTime localdate = LocalDateTime.parse(inputDate, formatter);
		DateTimeFormatter dtf = DateTimeFormatter.ofPattern(DD_MM_YY);
		
		return dtf.format(localdate);
		
		
	}

	@PostMapping("/pendingMembersFrAppr")
	@PreAuthorize("hasAnyAuthority('View To Add Member','View-Edit Member','View Member pending for approval')")
	public String pendingMembersFrAppr(Model model) {
		model.addAttribute(T_MEMB_LIST, T_MEMB_LIST);
		model.addAttribute(PENDING_MEMB_FR_APP, CommonConstants.YES);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
		}
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(IS_MAKER, CommonConstants.YES_FLAG);
		} else {
			model.addAttribute(IS_MAKER, BaseCommonConstants.NO_FLAG);
		}
		return getView(model, SEARCH_MEMBERS);
	}

	@PostMapping("/searchPendingMembers")
	@PreAuthorize("hasAnyAuthority('View To Add Member','View-Edit Member','View Member pending for approval')")
	public ResponseEntity<Object> searchPendingMembers(@ModelAttribute("memberDTO") MemberDTO memberDTO,
			HttpServletResponse response, BindingResult result, Locale locale, ModelAndView modelAndView, Model model)
			 {
		JsonObject jsonResponse = new JsonObject();
		JsonArray data = new JsonArray();
		String errorMessage = "";
		String errorStatus = "";
		int iTotalRecords = 0;
		long iTotalDisplayRecords = 0;
		List<MemberDTO> memberList = null;
		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
		
		String sEcho = "";
		String[] submemberBankTypes = StringUtils.split(env.getProperty(SUBMEM_BANK_TYPE), ",");
		String[] sponsorBankTypes = StringUtils.split(env.getProperty(SPONSER_BANK_TYPE), ",");
		String[] networkTypes = StringUtils.split(env.getProperty(NETWORK_BANK_TYPE), ",");
		String[] rrbBankTypes = StringUtils.split(env.getProperty("RRB.bank.type"), ",");


 

		if (!result.hasErrors()) {
			memberList = memberService.getMembers(searchCriteriaDTO);

			model.addAttribute(MEM_LIST_PENDING, memberList);
			iTotalRecords = 10; 

			iTotalDisplayRecords = memberService.getRowCount();

 

			if (memberList != null && !memberList.isEmpty()) {
				
				jsonResponse.addProperty(S_ECHO, sEcho);
				jsonResponse.addProperty(I_TOTA_RECORD, iTotalRecords);
				jsonResponse.addProperty(I_TOTL_DISPLAY_RECORD, iTotalDisplayRecords);
				for (MemberDTO c : memberList) {
					handleRowAddForPendingMem(data, submemberBankTypes, sponsorBankTypes, networkTypes, rrbBankTypes,c);
				}
			} 
		}
		
		jsonResponse.addProperty(ERROR_STATUS, errorStatus);
		jsonResponse.addProperty(ERROR_MSG, errorMessage);
		jsonResponse.addProperty(S_ECHO, sEcho);
		jsonResponse.addProperty(I_TOTA_RECORD, iTotalRecords);
		jsonResponse.addProperty(I_TOTL_DISPLAY_RECORD, iTotalDisplayRecords);
		jsonResponse.add(DATA, data);

 

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

 

	}

	private void handleRowAddForPendingMem(JsonArray data, String[] submemberBankTypes, String[] sponsorBankTypes,
			String[] networkTypes, String[] rrbBankTypes, MemberDTO c) {
		JsonArray row = new JsonArray();
		if (!CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			row.add("");
		}
		handleMemType(submemberBankTypes, sponsorBankTypes, networkTypes, rrbBankTypes, c, row);

		row.add(new JsonPrimitive(c.getBankMasterCode() == null ? "" : c.getBankMasterCode()));

		row.add(new JsonPrimitive(c.getBnkAdd() == null ? "" : c.getBnkAdd()));
		row.add(new JsonPrimitive(c.getBnkPhone() == null ? "" : c.getBnkPhone()));
		row.add(new JsonPrimitive(c.getBnkEmail() == null ? "" : c.getBnkEmail()));
		if (c.getStatus().equalsIgnoreCase("A")) {
			row.add(new JsonPrimitive("YES"));
		} else {
			row.add(new JsonPrimitive("No"));
		}

		row.add(new JsonPrimitive(
				c.getCreatedOn() == null ? "" : modifyDateLayout(c.getCreatedOn().toString()) + ""));

		row.add(new JsonPrimitive(c.getLastUpdatedOn() == null ? ""
				: modifyDateLayout(c.getLastUpdatedOn().toString()) + ""));
		handleRecordStatus(c, row);
		row.add(new JsonPrimitive(""));
		
		row.add(new JsonPrimitive(c.getMemberId() == 0 ? "" : c.getMemberId() + ""));

		data.add(row);
	}

	private void handleRecordStatus(MemberDTO c, JsonArray row) {
		if (c.getRecordStatus().equalsIgnoreCase("P")) {
			row.add(new JsonPrimitive("Pending"));
		} else {
			row.add(new JsonPrimitive("Rejected"));
		}

		if (c.getRecordStatus().equalsIgnoreCase("P")) {
			row.add(new JsonPrimitive(""));
			row.add(new JsonPrimitive(""));
		} else {
			row.add(new JsonPrimitive(c.getRejectReason() == null ? "" : c.getRejectReason()));
			row.add(new JsonPrimitive(
					c.getRejectedOn() == null ? "" : modifyDateLayout(c.getRejectedOn().toString()) + ""));

		}
	}

	private void handleMemType(String[] submemberBankTypes, String[] sponsorBankTypes, String[] networkTypes,
			String[] rrbBankTypes, MemberDTO c, JsonArray row) {
		row.add(new JsonPrimitive(c.getParticipantId() == null ? "" : c.getParticipantId() + ""));
		row.add(new JsonPrimitive(c.getMemberName() == null ? "" : c.getMemberName()));
		if (StringUtils.containsAnyIgnoreCase(c.getMemberType(), sponsorBankTypes)) {
			row.add(new JsonPrimitive(SPONSER_BANK));
		} else if (StringUtils.containsAnyIgnoreCase(c.getMemberType(), submemberBankTypes)) {
			row.add(new JsonPrimitive(SUB_MEMBER_BANK));
		} else if (StringUtils.containsAnyIgnoreCase(c.getMemberType(), networkTypes)) {
			row.add(new JsonPrimitive(NETWORK));
		}else if (StringUtils.containsAnyIgnoreCase(c.getMemberType(), rrbBankTypes)) {
			row.add(new JsonPrimitive("RRB"));
		} else {
			row.add(new JsonPrimitive(""));
		}
	}

	@PostMapping("/showSavedMembers")
	@PreAuthorize("hasAnyAuthority('View To Add Member','View-Edit Member','View Member pending for approval')")
	public String showSavedMembers(Model model) {
		model.addAttribute(SAVED_MEMB_LIST, SAVED_MEMB_LIST);
		model.addAttribute(SHOW_SAVED_MEMB, CommonConstants.YES);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(IS_MAKER, CommonConstants.YES_FLAG);
		} else {
			model.addAttribute(IS_MAKER, BaseCommonConstants.NO_FLAG);
		}
		return getView(model, SEARCH_MEMBERS);
	}

	@PostMapping("/searchSavedMembers")
	@PreAuthorize("hasAnyAuthority('View To Add Member','View-Edit Member','View Member pending for approval')")
	public ResponseEntity<Object> searchSavedMembers(@ModelAttribute("memberDTO") MemberDTO memberDTO,
			HttpServletResponse response, BindingResult result, Locale locale, ModelAndView modelAndView,
			ModelMap model) {
		JsonObject jsonResponse = new JsonObject();
		JsonArray data = new JsonArray();
		String errorMessage = "";
		String errorStatus = "";
		int iTotalRecords = 0;
		long iTotalDisplayRecords = 0;
		List<MemberDTO> savedMemberList = null;
		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
		
		
		String sEcho = "";

		if (!result.hasErrors()) {
			
			savedMemberList = memberService.getSavedMemberList(searchCriteriaDTO);
			iTotalRecords = 10; 
			 

			String[] submemberBankTypes = StringUtils.split(env.getProperty(SUBMEM_BANK_TYPE), ",");
			String[] sponsorBankTypes = StringUtils.split(env.getProperty(SPONSER_BANK_TYPE), ",");
			
			String[] networkTypes = StringUtils.split(env.getProperty(NETWORK_BANK_TYPE), ",");

			iTotalDisplayRecords = memberService.getSavedRowCount();
			if (savedMemberList != null && !savedMemberList.isEmpty()) {
				
				jsonResponse.addProperty(S_ECHO, sEcho);
				jsonResponse.addProperty(I_TOTA_RECORD, iTotalRecords);
				jsonResponse.addProperty(I_TOTL_DISPLAY_RECORD, iTotalDisplayRecords);
				for (MemberDTO c : savedMemberList) {
					handleRowAddForSavedMem(data, submemberBankTypes, sponsorBankTypes, networkTypes, c);
				}
			} 
		}
		
		jsonResponse.addProperty(ERROR_STATUS, errorStatus);
		jsonResponse.addProperty(ERROR_MSG, errorMessage);
		jsonResponse.addProperty(S_ECHO, sEcho);
		jsonResponse.addProperty(I_TOTA_RECORD, iTotalRecords);
		jsonResponse.addProperty(I_TOTL_DISPLAY_RECORD, iTotalDisplayRecords);
		jsonResponse.add(DATA, data);

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	private void handleRowAddForSavedMem(JsonArray data, String[] submemberBankTypes, String[] sponsorBankTypes,
			String[] networkTypes, MemberDTO c) {
		JsonArray rows = new JsonArray();

		rows.add(new JsonPrimitive(c.getParticipantId() == null ? "" : c.getParticipantId() + ""));
		if ("Y".equalsIgnoreCase(isInternational)) {
			rows.add(c.getDomesticCreatedDate() != null ? c.getDomesticCreatedDate().toLocaleString() : "");
		}
		rows.add(new JsonPrimitive(c.getMemberName() == null ? "" : c.getMemberName()));

		if (StringUtils.containsAnyIgnoreCase(c.getMemberType(), sponsorBankTypes)) {
			rows.add(new JsonPrimitive(SPONSER_BANK));
		} else if (StringUtils.containsAnyIgnoreCase(c.getMemberType(), submemberBankTypes)) {
			rows.add(new JsonPrimitive(SUB_MEMBER_BANK));
		} else if (StringUtils.containsAnyIgnoreCase(c.getMemberType(), networkTypes)) {
			rows.add(new JsonPrimitive(NETWORK));
		} else {
			rows.add(new JsonPrimitive(""));
		}

		rows.add(new JsonPrimitive(c.getBankMasterCode() == null ? "" : c.getBankMasterCode()));

		rows.add(new JsonPrimitive(c.getBnkAdd() == null ? "" : c.getBnkAdd()));
		rows.add(new JsonPrimitive(c.getBnkPhone() == null ? "" : c.getBnkPhone()));
		rows.add(new JsonPrimitive(c.getBnkEmail() == null ? "" : c.getBnkEmail()));
		rows.add(c.getStatus().equalsIgnoreCase(ACTIVE) ? new JsonPrimitive(CommonConstants.YES) : new JsonPrimitive(NO));
		
		rows.add(new JsonPrimitive(c.getCreatedOn() == null ? "" : modifyDateLayout(c.getCreatedOn().toString()) + ""));

		rows.add(new JsonPrimitive(c.getLastUpdatedOn() == null ? ""
				: modifyDateLayout(c.getLastUpdatedOn().toString()) + ""));
		
		if (c.getRecordStatus().equalsIgnoreCase(INACTIVE)) {
			rows.add(new JsonPrimitive(IN_PROGRESS));
		} else {
			rows.add(new JsonPrimitive(""));
		}

		rows.add(new JsonPrimitive(""));


		data.add(rows);
	}

	@PostMapping("/approveorRejectBulkMem")
	@PreAuthorize("hasAnyAuthority('Approve Member')")
	public String approveMemberBulk(@RequestParam("status") String status,
			@RequestParam("bulkApprovalIdList") String bulkApprovalIdList, Model model, HttpServletRequest request,
			HttpSession session) {

		try {
			if (status != null) {

				String remarks = "";

				if (status.equals(CommonConstants.STATUS_APPROVE)) {
					remarks = CommonConstants.BULK_APPROVE;
				} else if (status.equals(CommonConstants.STATUS_REJECT)) {
					remarks = CommonConstants.BULK_REJECT;
				}
				MemberDTO memberDTOs = new MemberDTO();
				memberDTOs.setApprovalStatus(status);
				memberDTOs.setApprovalRemarks(remarks);

				String successStatus = memberService.approveMemberBulk(bulkApprovalIdList, memberDTOs);
				if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && ACTIVE.equalsIgnoreCase(status)) {
					model.addAttribute(CommonConstants.SUCCESS_STATUS, BULK_APPROVE_MSG);
				} else if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && REJECT.equalsIgnoreCase(status)) {
					model.addAttribute(CommonConstants.SUCCESS_STATUS, BULK_REJ_MSG);
				}

			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SEARCH_MEMBERS, ex);
		}
		List<MemberDTO> memberList = null;
		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
		int iDisplayStart = 0;
		int iDisplayLength = 0;
		String sSearch = "";
		int startval = iDisplayStart + 1;
		int endval = iDisplayStart + iDisplayLength;
		searchCriteriaDTO.setStartVal(startval);
		searchCriteriaDTO.setEndVal(endval);
		searchCriteriaDTO.setSearchName(sSearch);

		memberList = memberService.getMembers(searchCriteriaDTO);
		model.addAttribute(T_MEMB_LIST, T_MEMB_LIST);
		model.addAttribute(PENDING_MEMB_FR_APP, CommonConstants.YES);
		model.addAttribute(SHOW_ADD_BUTTON, CommonConstants.YES);
		model.addAttribute(MEM_LIST_PENDING, memberList);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
		}
		return getView(model, SEARCH_MEMBERS);

	}

}
