package org.npci.settlenxt.adminportal.service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.MemberRepository;
import org.npci.settlenxt.portal.common.dto.BinDetailsDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class MemberBinFileUpload implements NetWorkBinUploadInterface{
	
	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	MemberRepository memberRepository;
	@Autowired
	BaseLookupService baseLookService;
	
	private Map<String,String> cardTechnologyMap = new HashMap<>();
	
	private static final String ACTIVE_STATUS = "A";
	private static final String DELETE_STATUS = "D";
	private static final String MODIFY_STATUS = "M";
	private static final String INACTIVE_STATUS = "I";
	private static final String UNIQUE_CONSTRAINT_ERROR="duplicate key value violates unique constraint";
	
	@Override
	public String processNetworkBinFile(List<MultipartFile> files) {
		mapCardTechnology();
		for (MultipartFile file : files) {
			try (BufferedReader lineReader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
				String lineText = null;
				String header = lineReader.readLine();
				log.debug(header);
				while ((lineText = lineReader.readLine()) != null) {
					if(lineText.length()>60) {
						processRupayData(lineText);
					}
				}
			}catch (Exception e) {
				log.error("Network Bin File Upload Exception " + e.getMessage() + " - {}", e);
				return BaseCommonConstants.FAIL_STATUS;
			}
		}
		return BaseCommonConstants.PROCESS_STATUS_SUCCESS;
	}

	private void mapCardTechnology() {
		List<CodeValueDTO> cardTechnologyList = baseLookService.getLookupData("CARD_TECH");
		cardTechnologyList.forEach(data->
			cardTechnologyMap.put(data.getDescription(), data.getCode())
		);
		
	}

	private void processRupayData(String lineText) {
		BinDetailsDTO binDetailsDto = new BinDetailsDTO();
		try {
			String lowbin = lineText.substring(11, 20);
			String highbin = lineText.substring(20, 29);
			String bin = null;
			binDetailsDto.setLowBin(lineText.substring(11, 20));
			binDetailsDto.setHighBin(lineText.substring(20, 29));
			for (int index = 0; index < lowbin.length(); index++) {
				if (lowbin.charAt(index) != highbin.charAt(index)) {
					bin = lowbin.substring(0, index);
					break;
				}
			}
			if(StringUtils.isEmpty(bin)) {
				bin = lowbin;
			}
			binDetailsDto.setBinNumber(bin);
			String status = lineText.substring(61);
			populateBinDetails(lineText, binDetailsDto);
			Optional<BinDetailsDTO> binDetailsDTO1 = memberRepository.getNetBinDetail(bin);
			if (!binDetailsDTO1.isPresent() && !StringUtils.equals(status, DELETE_STATUS)) {
				memberRepository.insertNetwBinRange(binDetailsDto);
			}else if(binDetailsDTO1.isPresent() && StringUtils.equals(status, MODIFY_STATUS)) {
				log.debug("Data is already present in network bin table.Hence set the previous bins status to I and inserted new Detail.");
				memberRepository.updateNetBinRange(binDetailsDTO1.get().getBinNumber(), INACTIVE_STATUS, null, null);
				binDetailsDto.setCreatedBy(binDetailsDTO1.get().getCreatedBy());
				binDetailsDto.setCreatedOn(binDetailsDTO1.get().getCreatedOn());
				insertNetwkBinRangeExtracted(binDetailsDto);
			}else if(binDetailsDTO1.isPresent() && StringUtils.equals(status, DELETE_STATUS)) {
				log.debug("Data is already present in network bin table.Henced updated the status to D.");
				memberRepository.updateNetBinRange(binDetailsDTO1.get().getBinNumber(), DELETE_STATUS,null,null);
			}else {
				log.debug("Data is already present in network bin table so Skipped");
			}
			
		} catch (ParseException e) {
			log.error("Error while populating rupay data into bin model " + e.getMessage() + " - {}", e);
		}
	}

	private void insertNetwkBinRangeExtracted(BinDetailsDTO binDetailDto) {
		try {
			memberRepository.insertNetwBinRange(binDetailDto);
		}catch(Exception exx) {
			handleGenericException(exx);
		}
	}

	private void handleGenericException(Exception ex) {
		String errorDesc;
		if (ex.getCause() != null) {
			errorDesc = ex.getCause().toString();
		} else {
			errorDesc = ex.getMessage();
		}
		if (StringUtils.contains(errorDesc,UNIQUE_CONSTRAINT_ERROR)) {
			log.info("Duplicate Data Entry - {}",ex.getMessage());
		}else {
			log.info("Error - {}:",ex.getMessage());
		}
	}
	
	private void populateBinDetails(String lineText, BinDetailsDTO binDetailsDto) throws ParseException {
		binDetailsDto.setBinId(fetchBinIdSeq());
		binDetailsDto.setParticipantId(lineText.substring(0, 11));
		binDetailsDto.setPanLength(Integer.parseInt(lineText.substring(29, 31)));
		binDetailsDto.setMessageType(lineText.substring(31, 32));
		binDetailsDto.setBinCardType(lineText.substring(32, 34));
		binDetailsDto.setBinProductType(lineText.substring(34, 36));
		binDetailsDto.setBinCardVariant(lineText.substring(36, 38));
		String cardTechnology = cardTechnologyMap.get(lineText.substring(38, 41));				
		binDetailsDto.setCardTechnology(cardTechnology);
		binDetailsDto.setSubScheme(lineText.substring(41, 43));
		binDetailsDto.setDomainUsage(lineText.substring(43, 44));
		binDetailsDto.setCurrencyCode(lineText.substring(44, 47));
		binDetailsDto.setCountryCode(lineText.substring(47, 49));
		binDetailsDto.setAcqBinActivationDate(
				new SimpleDateFormat("yyMMddHHmmss").parse(lineText.substring(49, 61)));
		binDetailsDto.setStatus(ACTIVE_STATUS);
		binDetailsDto.setDectivationDate(new SimpleDateFormat("yyyy-MM-dd").parse("2099-12-31"));
		binDetailsDto.setBinType("I");
		binDetailsDto.setCreatedBy(sessionDTO.getUserName());
		binDetailsDto.setCreatedOn(new Date());
		binDetailsDto.setLastUpdatedBy(sessionDTO.getUserName());
		binDetailsDto.setLastUpdatedOn(new Date());
		binDetailsDto.setBankGroup("999");
		binDetailsDto.setOfflineAllowed("N");
		binDetailsDto.setProductType("POS01");
		binDetailsDto.setSettlementBin("RUPAY01");
		binDetailsDto.setBinCardBrand("1");
	}
	
	private int fetchBinIdSeq() {
		return memberRepository.fetchBinIdSeq();
	}

}
