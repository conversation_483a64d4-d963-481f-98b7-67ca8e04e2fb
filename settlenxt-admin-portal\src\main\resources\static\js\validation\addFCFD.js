$(document).ready(function() {

$('form')
    .each(function(){
        $(this).data('serialized', $(this).serialize())
    })
    .on('change input', function(){
        $(this)             
            .find('input:submit, button:submit')
                .prop('disabled', $(this).serialize() == $(this).data('serialized'))
        ;
        $('#forms').prop('disabled', false);
       
     })
     


$('#fcfdAction').on('keyup keypress blur change',function () {
        validateFromCommonVal('fcfdAction', true, "SelectionBox", "", false);
    });
    
    
 $('#txnDestId').on('keyup keypress blur change',function () {
        validateFromCommonVal('txnDestId', false, "SelectionBox", "", false);
    });        
   
    
 $('#binType').on('keyup keypress blur change',function () {
        validateFromCommonVal('binType', true, "SelectionBox", "" , false);
    });
    

     $('#settlementBin').on('keyup keypress blur change',function () {
        validateFromCommonVal('settlementBin', true, "SelectionBox", "" , false);
    });
  $('#feeNames').on('keyup keypress blur change',function () {
        validateFromCommonVal('feeNames', true, "SelectionBox", "" , false);
    });
$('#feeCcyCode').on('keyup keypress blur change',function () {
        validateFromCommonVal('feeCcyCode', true, "SelectionBox", "" , false);
    });
$('#feeCategoryCode').on('keyup keypress blur change',function () {
        validateFromCommonVal('feeCategoryCode', true, "NumericwithPrecisionwith0From10to2", 11 , false);
    });
 $('#amount').on('keyup keypress blur change',function () {
        validateFromCommonVal('amount', true, "NumericwithPrecisionwith0From10to2", 11 , false);
    });
    
 $('#npciMsgText').on('keyup keypress blur change',function () {
        validateFromCommonVal('npciMsgText', true, "AlphaNumerSpecialChar", 100 , false);
    }); 
    
if (($('#button').val() == "Y")) {
		$("input").prop('disabled', true);
		$("select").prop('disabled', true);
	}
$('#clearFcfd').click(function(){

     document.getElementById("txnDestId").value = "";

     document.getElementById("binType").value = "";

     document.getElementById("issuerId").value = "";

     document.getElementById("acquirerId").value = "";

     document.getElementById("feeNames").value = "";

     document.getElementById("settlementBin").value = "";
     document.getElementById("amount").value = "";
     document.getElementById("npciMsgText").value = "";
     document.getElementById("feeCcyCode").value = "";

     document.getElementById("feeCategoryCode").value = "";
		$("#errbinType").hide();
		$("#errissuerId").hide();
		$("#erracquirerId").hide();
		$("#errsettlementBin").hide();
		$("#errfeeNames").hide();
		$("#errfeeCcyCode").hide();
		$("#errfeeCategoryCode").hide();
		$("#errccyCode").hide();
		$("#erramount").hide();
		$("#errnpciMsgText").hide();

     });	
	

  
       
   
	$("#Clear").click(function() {
	
	  $('#errfcfdAction').html('');
	  $('#errbinType').html('');
	  $('#errissuerId').html('');
	  $('#erracquirerId').html('');
	  $('#errsettlementBin').html('');
	  $('#errfeeNames').html('');
	  $('#errnpciMsgText').html('');
	  $('#errfeeCcyCode').html('');
	  $('#errfeeCategoryCode').html('');
	  $('#errccyCode').html('');
	  $('#erramount').html('');
	  $('#errmemberMsgTxt').html('');
	  
	 
	});
	
	
	$('#feeNames')
								.change(
										function() {
	
											if ($('#feeNames').val() != '0') {
												var feeId = $("#feeNames").val();
											
									var tokenValue = document.getElementsByName("_TransactToken")[0].value;
												$
														.ajax({
															url : "getAmountCategory",
															type : "POST",
															data : {
																feeNames : feeId,
																"_TransactToken" : tokenValue
															},
															dataType : "json",
															success : function(data) {
															
															
																$("#amount").val(data.amount);
												$("#feeCategoryCode").val(data.feeCategoryCode)
															}
														});
	
											} else {
												$("#amount").empty();
												$("#feeCategoryCode").empty();
											}
	
										});
										
						$("#binType").change( function(e){
     if(e.target.value == "1") {
         document.getElementById('issuerId').required = true;
         document.querySelector(".issrequire").style.display = "initial";
         document.getElementById('acquirerId').required = false;
         document.querySelector(".acqrequire").style.display = "none";
         document.getElementById('acquirerId').value = "" ;

     }
     else {
         document.getElementById('issuerId').required = false;
         document.querySelector(".issrequire").style.display = "none";
         document.getElementById('issuerId').value = "" ;
         document.getElementById('acquirerId').required = true;
         document.querySelector(".acqrequire").style.display = "initial";
     }
});				
										
								
										
});
					
	  
	
	window.history.forward();
	function noBack() {
		window.history.forward();
	}
	
	

	
function checkBulkDataIssBin() {
	var validPanNumber = false;
	var panNumber =  $('#panNumber').val();
		var errpanNumber = document.getElementById('errpanNumber');
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		  var errorMsg="";
		if (panNumber != "") {

			$.ajax({
				url : "checkBulkDataIssBin",
				type : "POST",
				dataType : "json",
				data: {"panNumber" : panNumber,
					    "_TransactToken" : tokenValue},
					  
				success : function(response){

					if (response.status == "BSUC_0001") {
						validPanNumber = false;
						errpanNumber.className = 'error';
						errpanNumber.innerHTML = "";	
errorMsg="";
 $('#errpanNumber').hide();
					} else {
					 $('#errpanNumber').show();
						validPanNumber = true;
						errpanNumber.className = 'error';
						 errorMsg=response.status;
						errpanNumber.innerHTML = errorMsg;
						$('#panNumber').focus();
						
					}
				},
				error : function(_request, _status, _error) {
					errpanNumber.className = 'error';
					errpanNumber.innerHTML = errorMsg;
				}
			});

		} else {
			validPanNumber = false;
		}
		return validPanNumber;
	}	
	
	
	function checkBulkDataAcqBin() {
	var validAcqId = false;
	var acqId =  $('#acqId').val();
		var erracqId = document.getElementById('erracqId');
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		var errorMsg="";
		if (acqId != "") {

			$.ajax({
				url : "checkBulkDataAcqBin",
				type : "POST",
				dataType : "json",
				data: {"acqId" : acqId,
					    "_TransactToken" : tokenValue},
				success : function(response){

					if (response.status == "BSUC_0001") {
						validAcqId = false;
						erracqId.className = 'error';
						erracqId.innerHTML = "";	
errorMsg="";
$('#erracqId').hide();
					} else {
					 $('#erracqId').show();
						validAcqId = true;
						erracqId.className = 'error';
						 errorMsg=response.status;
						erracqId.innerHTML = errorMsg;
						$('#acqId').focus();
					}
				},
				error : function(_request, _status, _error) {
					erracqId.className = 'error';
					erracqId.innerHTML = errorMsg;
				}
			});

		} else {
			validAcqId = false;
		}
		return validAcqId;
	}	
	
	
	function validateCity(msgID) {
	
		var cityId = (document.getElementById("cityId").value)
				.replace("/^\s*|\s*$/g", '');
		var errCity = document.getElementById(msgID);
	
		if (cityId == "") {
	
			errCity.className = 'error';
			errCity.innerHTML = "";
	
			return true;
	
		} else {
	
			errCity.className = 'error';
			errCity.innerHTML = "";
				return false;
		}
	
	
	}
	
	function validateState(msgID) {
	
		var stateName = (document.getElementById("stateId").value).replace("/^\s*|\s*$/g",
				'');
		var errState = document.getElementById(msgID);
	
		if (stateName == "") {
	
			errState.className = 'error';
			errState.innerHTML = "";
	
			return true;
	
		} else {
	
			errState.className = 'error';
			errState.innerHTML = "";
			return false;
		}
	
		
	}
	
	
	
	
function updDocument(inputFileID){
	
var formData = new FormData();
const input = document.querySelector('#'+inputFileID);
    
     for (var i of input.files) {
        formData.append('file', i);
}
    formData.append('documentType', $('#documentType').val());
   formData.append('participantId', $('#participantId').val());
   formData.append('bulkDataEntryId', $('#bulkDataEntryId').val());
    
     $.ajax({
    	 url: "uploadDocument",//?_TransactToken="+tokenValue,
        type: "POST",
        enctype: 'multipart/form-data',
        data : formData,
        processData: false,
        contentType: false,
        cache : false,
        timeout: 600000,
		dataType : "json",
        success:function(data){	
        
			if(data.success)
			{
				 $('#successStatus').html(data.success);
				   $('#jqueryError2').hide();
				 $('#jquerySuccess').show();
			}
			
			if(data.error){
				$('#errorStatus2').html(data.error);
				 $('#jquerySuccess').hide();
        $('#jqueryError2').show();
         }
        	
      	

}
})


}		
	
	
	function loadFCFDAction( ) {
		let url = '/loadFCFDAction';
		var data = "fcfdAction," + $('#fcfdAction').val(); 
		postData(url, data);
	}
	
	function validateFCFDForm(action){
	var check = false;
	
 var fcfdAction=$('#fcfdAction').val();	
	
	
	
	  if(!validateFromCommonVal('fcfdAction', 
	     true, "SelectionBox", "", false)) {
	  check = true;
	  
     }
      
 if(!validateFromCommonVal('binType', 
	     true, "SelectionBox", "", false)) {
	  check = true;
	  
     }
 
     if(!validateFromCommonVal('feeCcyCode', 
	  true, "SelectionBox", "", false)) {
	  check = true;
     }
     
    if (!validateAcquirerIdAndIssuerId()) {
		check = true;
	}
	
	
if (!validateFromCommonVal('settlementBin', 
	  true, "SelectionBox", "", false)) {
	  check = true;
     }
     if (!validateFromCommonVal('feeNames', 
	  true, "SelectionBox", "", false)) {
	  check = true;
     }
    
     
     if (!validateFromCommonVal('amount', 
	  true, "NumericwithPrecisionwith0From10to2", 11, false)) {
	  check = true;
     }
     
    if (!validateFromCommonVal('npciMsgText', 
	  true, "AlphaNumerSpecialChar", 100, false)) {
	  check = true;
     }     

  if (!validateFromCommonVal('feeCategoryCode', 
	  true, "NumericwithPrecisionwith0From10to2", 11, false)) {
	  check = true;
     }
    
     if (!check) {
     
     
    let  url = action;
    
     var data = "";
    
   
       data = "fcfdAction," +fcfdAction +",txnOriginatorInstId,"+$('#txnOriginatorInstId').val()+",txnDestId,"+$('#txnDestId').val()
      +",binType,"+$('#binType').val()+",issuerId,"+$('#issuerId').val()+",acquirerId,"+$('#acquirerId').val()+",settlementBin,"+$('#settlementBin').val()+",feeNames,"+$('#feeNames').val()
      +",amount,"+$('#amount').val()+",npciMsgText,"+$('#npciMsgText').val()+",feeCcyCode,"+$('#feeCcyCode').val()+ ",feeCategoryCode,"+$('#feeCategoryCode').val() +",id,"+$('#id').val();   
     
    
    
      
		postData(url, data);
	
     }else{
     return false;
     }
     
	}
	
	function dataAction(action) {
	let url = action;
 var data = "status,"
			+ status;
	postData(url, data);
}


function fcfdDiscardAction(action) {
	let url = action;
	var id = $("#id").val();
	var data =  "id,"+ id;
	postData(url, data);

}
	
	
function validateAcquirerIdAndIssuerId() {



var bintype = document.getElementById("binType").value;
var acquirerid= document.getElementById("acquirerId").value;

var issuerid= document.getElementById("issuerId").value;


if(bintype == '1')
{

		if(issuerid == ""){
				$("#errissuerId").find('.error').html('Issuer Id is Mandatory');
			 	$("#errissuerId").show();
		  		$("#erracquirerId").hide();
		 		return false;

			}else{

				$("#errissuerId").find('.error').html('');
		 		$("#errissuerId").hide();
			}

}

if(bintype == '2')
{
			if(acquirerid == ""){
			
			$("#erracquirerId").find('.error').html('Acquirer Id is Mandatory');
		 	$("#erracquirerId").show();
		  	$("#errissuerId").hide();
			return false;
			
			}else{
			
			
			$("#erracquirerId").find('.error').html();
		 	$("#erracquirerId").hide();
			
			}
		

}

return true;


}	

	





















	
	