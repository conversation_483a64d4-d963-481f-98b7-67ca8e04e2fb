package org.npci.settlenxt.adminportal.security;

import java.util.Locale;

import org.npci.settlenxt.adminportal.service.UserService;
import org.npci.settlenxt.portal.common.dto.UserDTO;
import org.npci.settlenxt.portal.common.security.BaseSettleNxtAuthProvider;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SettleNxtAuthProvider extends BaseSettleNxtAuthProvider {

	@Autowired
	private UserService userService;

	@Autowired
	private MessageSource messageSource;

	@Override
	public Authentication authenticate(Authentication authentication) throws AuthenticationException {
		String username = authentication.getName();

		UserDTO userDto = null;
		try {
			userDto = userService.getUser(username.toUpperCase(Locale.ROOT));
		} catch (Exception e) {
			throw new BadCredentialsException(
					messageSource.getMessage("login.invalidCredentials", null, "Invalid Username or password", Locale.ROOT),e);

		}
		if (userDto == null || !(BaseCommonConstants.USER_TYPE_NPCI_ADMIN.equalsIgnoreCase(userDto.getUserType())
				|| BaseCommonConstants.USER_TYPE_SUPER_ADMIN.equalsIgnoreCase(userDto.getUserType()))) {

			throw new BadCredentialsException(
					messageSource.getMessage("login.invalidCredentials", null, "Invalid Username or password", Locale.ROOT));
		}
		return super.authenticate(authentication);
	}

}