package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.MCCTipSurchargeDTO;
import org.springframework.stereotype.Service;

@Service
public interface MCCTipSurchargeService {

	 List<MCCTipSurchargeDTO> getTipSurchargeList();

	 List<MCCTipSurchargeDTO> getPendingTipSurcharge();

	 MCCTipSurchargeDTO getMccTipSurchargeMainInfo(int mccTipSurchargeId);

	 MCCTipSurchargeDTO getMccTipSurchargeStgInfo(String mccTipSurchargeId);

	 MCCTipSurchargeDTO addEditMccTipSurcharge(MCCTipSurchargeDTO mccTipSurchargeDto);

	 MCCTipSurchargeDTO getMccTipSurchargeForEdit(int mccTipSurchargeId);

	 MCCTipSurchargeDTO approveOrRejectMccTipSurcharge(int mccTipSurchargeId, String status, String remarks);

	 MCCTipSurchargeDTO discardMccTipSurcharge(int mccTipSurchargeId);

	 MCCTipSurchargeDTO getMccTipSurchargeStg(int mccTipSurchargeId);

	 int checkDuplicateData(MCCTipSurchargeDTO mccTipSurchargeDto);

	 String approveOrRejectMccTipSurchargeBulk(String bulkApprovalReferenceNoList, String status, String remarks);

}
