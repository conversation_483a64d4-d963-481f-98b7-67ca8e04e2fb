package org.npci.settlenxt.adminportal.dto;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class MemberRequestDTO {
 private Integer memberId;
 private String participantId;
 private String bankMasterCode;
 private String reqType;
 private String editFlag;
 private Integer reqId;
 
 
	
}
