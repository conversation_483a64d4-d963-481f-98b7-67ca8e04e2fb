package org.npci.settlenxt.adminportal.controllers;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.cache.SysParams;
import org.npci.settlenxt.adminportal.dto.MoveReportDTO;
import org.npci.settlenxt.adminportal.model.MoveReportModel;
import org.npci.settlenxt.adminportal.service.MoveReportService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.service.BaseLookupServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@Controller
public class MoveReportController extends BaseController {

	public static final Logger log = LogManager.getLogger(MoveReportController.class);

	private static final String MOVE_REPORT_ADD_REQUEST = "moveReportAddRequest";
	private static final String MOVE_REPORT_MAIN_PAGE = "moveReportMainPage";
	private static final String REPORT_LIST = "reportList";
	private static final String REPORT_TYPE = "reportType";
	private static final String YEAR = "year";
	private static final String MONTH = "month";
	private static final String NETWORK = "NETWORK";

	private static final String MOVE_REPORT_DTO = "MoveReportDto";
	private static final String REPORT_NETWORK = "reportNetwork";
	private static final String SOURCE_BASE_FOLDER_FOR_MOVEMENT = "SOURCE_BASE_FOLDER_FOR_MOVEMENT";

	private static final String MAX_FILE_SIZE = "maxFileSize";
	private static final String SPRING_MAX_FILE_SIZE = "spring.servlet.multipart.max-file-size";

	@Autowired
	BaseLookupServiceImpl lookUpService;

	@Autowired
	MoveReportService moveReportService;

	@Autowired
	SysParams sysParams;
	

	@PostMapping("/moveReports")
	@PreAuthorize("hasAuthority('View Reports Movement and Signing')")
	public String viewMoveReport(Model model) {
		MoveReportDTO moveReportDTO = new MoveReportDTO();
		addDeafultLookupListData(model);
		List<MoveReportDTO> reportList = moveReportService.getReportList();
		model.addAttribute(REPORT_LIST, reportList);
		model.addAttribute(MOVE_REPORT_DTO, moveReportDTO);
		return getView(model, MOVE_REPORT_MAIN_PAGE);

	}

	@PostMapping("/reportSearch")
	@PreAuthorize("hasAuthority('View Reports Movement and Signing')")
	public String reportSearch(Date fromDateStr, Date toDateStr, String reportNetworks, Model model) {
		MoveReportDTO moveReportDTO = new MoveReportDTO();
		moveReportService.addDefaultData(moveReportDTO, fromDateStr, toDateStr, reportNetworks);
		List<MoveReportDTO> reportList = moveReportService.searchReportList(moveReportDTO);
		model.addAttribute(REPORT_LIST, reportList);
		List<CodeValueDTO> reportNetwork = lookUpService.getLookupData(NETWORK);
		model.addAttribute(REPORT_NETWORK, reportNetwork);

		model.addAttribute(MOVE_REPORT_DTO, moveReportDTO);

		return getView(model, MOVE_REPORT_MAIN_PAGE);

	}

	@PostMapping("/addReportAndSignment")
	@PreAuthorize("hasAuthority('Add Reports Movement and Signing')")
	public String addReportRequest(Model model) {
		MoveReportDTO moveReportDTO = new MoveReportDTO();
		addDeafultLookupListData(model);
		
		String maxFileSize = env.getProperty(SPRING_MAX_FILE_SIZE);
		if(maxFileSize!=null) {
			int end=maxFileSize.length() -2;
			maxFileSize = maxFileSize.substring(0,end );
		}
		int maxFileSizeBytes = Integer.parseInt(maxFileSize) * 1024 * 1024;
		
		model.addAttribute(MAX_FILE_SIZE,maxFileSizeBytes);
		model.addAttribute(MOVE_REPORT_DTO, moveReportDTO);
		return getView(model, MOVE_REPORT_ADD_REQUEST);
	}

	private void addDeafultLookupListData(Model model) {
		List<CodeValueDTO> reportType = lookUpService.getLookupData(REPORT_TYPE);

		List<CodeValueDTO> month = moveReportService.getMonths();

		List<CodeValueDTO> year = lookUpService.getLookupData(YEAR);
		List<CodeValueDTO> reportNetwork = lookUpService.getLookupData(NETWORK);
		model.addAttribute(REPORT_NETWORK, reportNetwork);
		model.addAttribute(YEAR, year);
		

		model.addAttribute(MONTH, month);

		model.addAttribute(REPORT_TYPE, reportType);
	}

	@PostMapping("/submitReportMovementAndSigning")
	@PreAuthorize("hasAuthority('Add Reports Movement and Signing')")
	public String saveFile(@RequestParam("reportNetwork") String reportNetwork, @RequestParam("year") String year,
			@RequestParam("month") String month, Model model) {

		try {
			MoveReportDTO moveReportDTO = new MoveReportDTO();
			MoveReportModel moveReportModel = new MoveReportModel();

			moveReportService.saveMoveReportToDatabase(moveReportDTO, reportNetwork, year, month, moveReportModel);
			List<MoveReportDTO> reportList = moveReportService.getReportList();
			List<CodeValueDTO> reportNetworks = lookUpService.getLookupData(NETWORK);
			model.addAttribute(REPORT_NETWORK, reportNetworks);
			model.addAttribute(REPORT_LIST, reportList);
			model.addAttribute(MOVE_REPORT_DTO, moveReportDTO);
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS, "Move Report Request Added Successfully");

		} catch (Exception ex) {

			handleErrorCodeAndForward(model, MOVE_REPORT_MAIN_PAGE, ex);
			model.addAttribute(BaseCommonConstants.ERROR_STATUS, "Error in Directory");

		}

		return getView(model, MOVE_REPORT_MAIN_PAGE);

	}

	@PostMapping("/uploadReportandSignment")
	@PreAuthorize("hasAuthority('Add Reports Movement and Signing')")
	public ResponseEntity<String> uploadFile(@RequestParam("file") MultipartFile file,
			@RequestParam("reportNetwork") String reportNetwork) {
		try {

			String uploadFolder = sysParams.getSystemKeyValue(SOURCE_BASE_FOLDER_FOR_MOVEMENT, reportNetwork);

			byte[] bytes = file.getBytes();
			Path path = Paths.get(uploadFolder + file.getOriginalFilename());
			Files.write(path, bytes);
			return new ResponseEntity<>("File uploaded successfully", HttpStatus.OK);
		} catch (Exception e) {
			log.info("Exception found : {}",e.getMessage(), e);

		}
		return new ResponseEntity<>("File uploaded not success", HttpStatus.OK);

	}

}
