<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/viewCardConfiguration.js"
	type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">
		 <form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewCardConfiguration" modelAttribute="cardDto"
			action="${approveCardConfigStatus}" autocomplete="off"> 
			<input type="hidden" id="cardId" value="${cardDto.cardConfigId}" />
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="cardConfig.viewscreen.title" /></span></strong>
						</div>
						<div class="panel-body">
							
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">Base Fee</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
										<td><label><spring:message code="cardConfig.cardConfigId" /></label></td>
										<td id="cardConfigId">${cardDto.cardConfigId }</td>
										<td><label><spring:message code="cardConfig.cardType" /></label></td>
										<td id="cardType">${cardDto.cardTypeName }</td>
										<td><label><spring:message code="cardConfig.cardVariant" /></label></td>
										<td id="cardVariant">${cardDto.cardVariantName }</td>
									</tr>
									<tr>
										<td><label><spring:message code="cardConfig.baseFee" /></label></td>
										<td id="baseFee">${cardDto.baseFee }</td>
										<td><label><spring:message code="cardConfig.fromDate" /></label></td>
										<td id="fromDate">${cardDto.fromDate }</td>
										<td><label><spring:message code="cardConfig.toDate" /></label></td>
										<td id="toDate">${cardDto.toDate }</td>
									</tr>
								</tbody>
							</table>
							
							<div style="text-align:center;">
				<hr />
				<div style="text-align:center">
					<button type="button" class="btn btn-danger"
						onclick="userAction('N','/baseFeeConfiguration');">
						<spring:message code="cardConfig.backBtn" /></button>
						
					<c:if test="${cardDto.requestState =='A' }">	
					<sec:authorize access="hasAuthority('Edit Base Fee')">
						<input name="editButton" type="button" class="btn btn-success"
						 id="approveRole" value="Edit" 
						onclick="EditCardConfig('/editCardConfig','${cardDto.cardConfigId}','maintab');"/>
					</sec:authorize>
					</c:if>

			</div>
		</div>
							
						</div>
					</div>
					</div>
					</div>
		</form:form>
		
	</div>

</div>
