$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	}

function userAction(action) {
	var url = action;
 	var data = "status,"
			+ status;
	postData(url, data);
}

function postAction(_action) {



var feeId="";
var remarks="";
var data="";
var url="";
	if(maxLengthTextArea('rejectReason')){
	if ($('#apprej option:selected').val() == "A") {
		if ($("#rejectReason").val() != "") {
			
			 feeId = $("#feeId").val();
			 remarks = $("#rejectReason").val();
	url = '/approveFeeRateStatus';
			data = "status," + "A" + ",remarks,"
					+ remarks+ ",feeId,"
					+ feeId;
			postData(url, data);
		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else if ($('#apprej option:selected').val() == "R") {
		if ($("#rejectReason").val() != "") {
				
				remarks = $("#rejectReason").val();
				 feeId = $("#feeId").val();
				 url = '/approveFeeRateStatus';
				 data =  "status," + "R" + ",remarks," + remarks + ",feeId,"
						+ feeId;

				postData(url, data);
			

		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else {
		$(".appRejMust").show();
		$('html, body').animate({ scrollTop: 0 }, 'slow');
		return false;
	}
	}
}