<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.SysParamRepository">

	<select id="getSysParamApprovedList" resultType="SysParamsDTO">
		SELECT
		sp.sys_type as sysType, sp.sys_key as sysKey, sp.sys_value as
		sysValue, sp.description as desc, sp.add_prop as addProp,
		sp.created_by as createdBy,
		sp.last_updated_by as lastUpdatedBy,
		sp.created_on as createdOn, sp.updated_on as lastUpdatedOn,
		sps.checker_comments as checkersComment,
		sps.status as status,
		sps.last_operation as lastOperation
		FROM sys_params sp join
		sys_params_stg sps on sp.sys_type = sps.sys_type and sp.sys_value =
		sps.sys_value
		where sps.request_state=#{requestState} order by
		sp.updated_on desc;
	</select>

	<select id="getSysParamPendingList" resultType="SysParamsDTO">
		SELECT sys_type as sysType, sys_key as sysKey, sys_value as sysValue,
		description as desc, add_prop as addProp, created_by as createdBy,
		last_updated_by as lastUpdatedBy, created_on as createdOn,
		last_updated_on as lastUpdatedOn, checker_comments as checkersComment,
		request_state as requestState, status as status, last_operation as
		lastOperation
		FROM sys_params_stg where request_state in
		<foreach item='item' index='index'
			collection='requestStateList' open='(' separator=',' close=')'>#{item}
		</foreach>
		order by last_updated_on desc;
	</select>
	<select id="getSysParamsInfoFromMain" resultType="SysParamsDTO">
		SELECT
		sp.sys_type as sysType, sp.sys_key as sysKey, sp.sys_value as
		sysValue, sp.description as desc, sp.add_prop as addProp,
		sp.created_by as createdBy,
		sp.last_updated_by as lastUpdatedBy,
		sp.created_on as createdOn, sp.updated_on as lastUpdatedOn,
		sps.checker_comments as checkersComment,
		sps.status as status,
		sps.last_operation as lastOperation
		FROM sys_params sp join
		sys_params_stg sps on sp.sys_type = sps.sys_type and sp.sys_key =
		sps.sys_key
		where sps.sys_type = #{sysType} and sps.sys_key =
		#{sysKey};
	</select>




	<select id="getSysParamStgInfoBy" resultType="SysParamsDTO">
		SELECT
		sp.sys_type
		as sysType, sp.sys_key as sysKey, sp.sys_value as
		sysValue,
		sp.description as desc, sp.add_prop as addProp,
		sp.created_by as
		createdBy,
		sp.last_updated_by as lastUpdatedBy,
		sp.created_on as
		createdOn, sp.last_updated_on as lastUpdatedOn,
		sp.checker_comments as
		checkersComment,
		sp.status as status,
		sp.last_operation as
		lastOperation,
		sp.request_state as requestState
		FROM sys_params_stg sp
		where sp.sys_type=#{sysType} and sp.sys_key=#{sysKey}
	</select>

	<select id="getSysParamListType" resultType="SysParamsDTO">
		SELECT
		DISTINCT
		sys_type as sysType
		FROM
		sys_params
	</select>

	<insert id="addSysParam">
		INSERT into sys_params_stg(sys_type,
		sys_key,
		sys_value,
		description,
		add_prop,
		created_by,
		last_updated_by,
		created_on,
		last_updated_on,
		checker_comments,
		request_state,
		status,
		last_operation)
		values
		(
		#{sysParamsDto.sysType},
		#{sysParamsDto.sysKey},
		#{sysParamsDto.sysValue},
		#{sysParamsDto.desc},
		#{sysParamsDto.addProp},
		#{sysParamsDto.createdBy},
		#{sysParamsDto.lastUpdatedBy},
		#{sysParamsDto.createdOn},
		#{sysParamsDto.lastUpdatedOn},
		#{sysParamsDto.checkersComment},
		#{sysParamsDto.requestState},
		#{sysParamsDto.status},
		#{sysParamsDto.lastOperation})
	</insert>

	<update id="updateSysParamMain">
        UPDATE sys_params SET
        sys_type=#{sysParamsDto.sysType},sys_key=#{sysParamsDto.sysKey},sys_value=#{sysParamsDto.sysValue},
        description=#{sysParamsDto.desc},
        add_prop=#{sysParamsDto.addProp},last_updated_by=#{sysParamsDto.lastUpdatedBy}, updated_on
        = #{sysParamsDto.lastUpdatedOn} WHERE sys_type=#{sysParamsDto.sysType} and
        sys_key=#{sysParamsDto.sysKey}
</update>

	<update id="updateStgSysParam">
		UPDATE
		sys_params_stg
		SET
		sys_value=#{sysParamsDto.sysValue},
		description=#{sysParamsDto.desc},
		add_prop=#{sysParamsDto.addProp},
		last_updated_on=#{sysParamsDto.lastUpdatedOn},
		last_updated_by=#{sysParamsDto.lastUpdatedBy},
		request_state=#{sysParamsDto.requestState},
		status=#{sysParamsDto.status},
		last_operation=#{sysParamsDto.lastOperation}
		WHERE
		sys_type=#{sysParamsDto.sysType} and sys_key=#{sysParamsDto.sysKey};
	</update>
	<select id="checkDuplicateinStg" resultType="int">
		SELECT
		count(*)
		FROM
		sys_params_stg
		WHERE
		sys_type=#{sysType} and sys_key=#{sysKey}
	</select>
	<delete id="deleteDiscardedEntry">
		DELETE
		FROM
		sys_params_stg
		WHERE
		sys_type=#{sysType} and
		sys_key=#{sysKey};
	</delete>

	<insert id="insertSysParamMain">
		INSERT INTO sys_params(sys_type, sys_key,sys_value,description,add_prop,
		created_by,created_on,last_updated_by,updated_on)
		VALUES
		(#{sysParamsDto.sysType}, #{sysParamsDto.sysKey}, #{sysParamsDto.sysValue}, #{sysParamsDto.desc},#{sysParamsDto.addProp},
		#{sysParamsDto.createdBy},#{sysParamsDto.createdOn},
		#{sysParamsDto.lastUpdatedBy},#{sysParamsDto.lastUpdatedOn})
	</insert>

	<update id="updateSysParamRequestState">
		UPDATE sys_params_stg SET request_state= #{sysParamsDto.requestState}, checker_comments=
		#{sysParamsDto.checkersComment}, LAST_UPDATED_BY=#{sysParamsDto.lastUpdatedBy},
		LAST_UPDATED_ON = #{sysParamsDto.lastUpdatedOn}, last_operation= #{sysParamsDto.lastOperation}
		WHERE sys_type=#{sysParamsDto.sysType} and sys_key=#{sysParamsDto.sysKey}
	</update>
	
	<select id="getSysParamStgInfoList" resultType="SysParamsDTO">
		SELECT sys_type as sysType, sys_key as sysKey, sys_value as sysValue,
		description as desc, add_prop as addProp, created_by as createdBy,
		last_updated_by as lastUpdatedBy, created_on as createdOn,
		last_updated_on as lastUpdatedOn, checker_comments as checkersComment,
		request_state as requestState, status as status, last_operation as
		lastOperation FROM sys_params_stg where sys_type || sys_key in
		<foreach item='item' index='index' collection='sysParamlist'
			open='(' separator=',' close=')'>#{item} </foreach>
		order by last_updated_on desc
	</select>
	
	<update id="updateSysParamStgState">
		UPDATE sys_params_stg SET LAST_UPDATED_BY =
		#{sysParamsDto.lastUpdatedBy},
		LAST_UPDATED_ON = #{sysParamsDto.lastUpdatedOn},
		REQUEST_STATE=#{sysParamsDto.requestState},
		CHECKER_COMMENTS=#{sysParamsDto.checkersComment},LAST_OPERATION=#{sysParamsDto.lastOperation}
		WHERE sys_type=#{sysParamsDto.sysType} and sys_key=#{sysParamsDto.sysKey}
	</update>


</mapper>	
	