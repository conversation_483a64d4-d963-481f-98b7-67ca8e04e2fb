<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.npci.settlenxt.adminportal.repository.TransactionDetailRepository">

	<select id="getNpciFundCashRevInfo" resultType="TxnSettlementDTO">
		select 
		txn_id as txnId,org_txn_id as orgTxnId,mti as mti,func_code as funcCode,pan_prefix as panPrefix,pan_suffix as panSuffix,
		encrypted_token_pan as encryptedtokenPan,token_pan_prefix as tokenPanPrefix,token_pan_suffix as tokenPanSuffix,acq_ref_data as acqRefData,
		transaction_amount as amtTran,transaction_ts as tstampLocal,acquirer_id as instIdAcq,issuer_bin as instIdIss,fee_type_code as feeTypCode,
		settlement_dr_cr_indicator as settlementDrCrIndicator,fee_ccy as feeCcy,mem_msg_txt as memMsgTxt,org_transaction_date as orgDateCapture,
		entity_type as entityType,unique_filename as uniqueFileName,txn_originator_inst_id as txnOrgInstId,txn_destination_inst_id as txnDestInstId,
		txn_initiator as txnInitiator,is_ack as ack, is_incoming as incoming,record_number as recordNo,user_id as makerId,checker_id as checkerId,
		current_state as currentState,to_state as toState,tran_cur as tranCur,encrypted_pan as encryptedPan,encrypted_token_pan as encryptedtokenPan,
		acquirer_participant_id as participantIdAcq,issuer_participant_id as participantIdIss,net_recon_date as netReconDate,doc_file_path as docFilePath,
		date_rcvd as dateRcvd,case_number as caseNo,processing_code as pcode,reason_code as reasonCode,internal_track_no as internal_track_no,
		filing_dispute_fee_acq as filingDisputeFeeAcq,filing_dispute_fee_iss as filingDisputeFeeIss,filing_dispute_fee_gst_acq as filingDisputeFeeGSTAcq,
		filing_dispute_fee_gst_iss as filingDisputeFeeGSTIss,filing_dispute_fee_code  as filingDisputeFeeCode
		FROM net_fund_txn s 
		where 
		org_txn_id = #{txnId} 
		limit 1
	</select>

	<select id="getDisputeNpciFundCashRevInfo"  resultType="TxnSettlementDTO">
		select 
		txn_id as txnId,mti as mti,func_code as funcCode,pan_prefix as panPrefix,pan_suffix as panSuffix,rrn as rrn,acq_ref_data as acqRefData,
		transaction_amount as amtTran,transaction_ts as tstampLocal,acquirer_id as instIdAcq,issuer_bin as instIdIss,fee_type_code as feeTypCode,
		filing_dispute_fee_code as filingDisputeFeeCode,settlement_dr_cr_indicator as settlementDrCrIndicator,fee_ccy as feeCcy,mem_msg_txt as memMsgTxt,
		org_transaction_date as orgDateCapture,entity_type as entityType,unique_filename as uniqueFileName,txn_originator_inst_id as txnOrgInstId,
		txn_destination_inst_id as txnDestInstId,txn_initiator as txnInitiator,is_ack as ack, is_incoming as incoming,record_number as recordNo,
		user_id as makerId,checker_id as checkerId,current_state as currentState,to_state as toState,tran_cur as tranCur,encrypted_pan as encryptedPan,
		encrypted_token_pan as encryptedtokenPan,acquirer_participant_id as participantIdAcq,issuer_participant_id as participantIdIss,token_pan_prefix as tokenPanPrefix,
		token_pan_suffix as tokenPanSuffix,net_recon_date as netReconDate,doc_file_path as docFilePath,date_rcvd as dateRcvd,case_number as caseNo,
		processing_code as pcode,reason_code as reasonCode,internal_track_no as internal_track_no,filing_dispute_fee_acq as filingDisputeFeeAcq,
		filing_dispute_fee_iss as filingDisputeFeeIss,filing_dispute_fee_gst_acq as filingDisputeFeeGSTAcq,filing_dispute_fee_gst_iss as filingDisputeFeeGSTIss
		FROM net_fund_txn s 
		where org_txn_id = #{txnId} 
		and 
		org_transaction_date=#{orgTranDate} 
		order by transaction_ts
	</select>
	
	<select id="getTransactionDetailByTxnIdOrMappedDupData" resultType="TxnSettlementDTO">
		Select 
		txn_id as txnId,func_code as funcCode,mti as mti,pan_prefix as panPrefix,pan_suffix as panSuffix,token_pan_prefix as tokenPanPrefix,
		token_pan_suffix as tokenPanSuffix,processing_code as pcode,acq_ref_data as acqRefData,amt_card_billing as amtCardBilling,amt_acq_convert_billing as amtAcqConvertBilling,
		amt_acq_convert_billing_conv_rate as amtAcqConvertBillingConvRate,amt_npci_convert_billing_conv_rate as amtNpciConvertBillingConvRate,transaction_ts as tstampLocal,
		transaction_date as dateCapture,mcc as mcc,acq_inst_country as acqInstCountry,amt_markup as amtMarkup,additional_amount as amtAdd,acquirer_id as instIdAcq,
		issuer_bin as instIdIss,rrn as rrn,approval_code as approvalCode,service_code as serviceCode,card_acceptor_terminal_id as cardAcptTermId,card_acceptor_id as cardAcptId,
		card_acceptor_address as cardAcptAddress,card_acceptor_city as cardAcptCity,card_acceptor_region as cardAcptRegion,card_acceptor_country as cardAcptCountry,
		reason_code as reasonCode,cavv_code as cavvResult,cvv2_cvc2_code as cvv2Cvc2Result,cvv_cvc_code as cvvCvcResult, transaction_amount as amtTran,
		merchant_cat_ind as merchantCatInd,eci_indicator as eciInd,stip_indicator as stipInd,tran_cur as tranCur,additional_amount_curr as amtAddCur,
		card_bill_cur as cardBillCur,card_markup_cur as cardMarkupCur,stan as stan, card_acceptor_postal_code as cardAcptPstCode,deposit_id as depositId,
		response_code as respCode,itm_response_code as itmRespCode,issuer_resp_code as issuerRespCode,msg_reason_code_acq as msgReasonCodeAcq,addl_data48 as addlData48,
		addl_data55 as addlData55,is_reversed as revBy,online_txn_id as mappedDupData,card_acceptor_additional_address as mappedDupData,issuer_participant_id as participantIdIss,
		acquirer_participant_id as participantIdAcq,acquirer_settlement_bin as procIdAcq,issuer_settlement_bin as procIdIss,transaction_type as networkProgram,product_code as productCode,
		card_type as cardType,card_product_id as cardProductId,card_variant_id as cardVariantId,card_brand_id as cardBrandId,scheme_code as schemeCode,bin_type_id as binTypeId,
		bin_card_type_id as binCardTypeId,message_type_id as messageTypeId,issuer_country as countryIssInst,acquirer_bankgroup_id as bankGroupIdAcq,issuer_bankgroup_id as bankGroupIdIss,
		acquirer_subnet as netIdAcq,issuer_subnet as netIdIss,initiated_from as acqPlatProdId,internal_track_no as internalTrackNo,acq_recon_amt as acqReconAmt,
		acq_recon_conv as acqReconConv,acq_recon_cur as acqReconCur,iss_recon_amt as issReconAmt,iss_recon_conv as issReconConv,iss_recon_cur as issReconCur,
		net_recon_amt as netReconAmt,net_recon_conv as netReconConv,net_recon_cur as netReconCur,net_recon_date as netReconDate,recon_cycle_int as reconCycleInt,
		recon_cycle as reconCycle,processing_fee_acq as processingFeeAcq,processing_fee_iss as processingFeeIss,assessment_fee_acq as assessmentFeeAcq,assessment_fee_iss as assessmentFeeIss,
		intchg_fee_acq as intchgFeeAcq,intchg_fee_iss as intchgFeeIss,auth_fee_acq as authFeeAcq,auth_fee_iss as authFeeIss,fee_minor_id as feeMinorId,fee_major_id as feeMajorId,
		addl_data61 as addlData61,onus_fee_id as onusFeeId,auth_fee_gst_iss as authFeeGstIss,auth_fee_gst_acq as authFeeGstAcq,intchg_fee_gst_iss as intchgFeeGstIss,
		intchg_fee_gst_acq as intchgFeeGstAcq,processing_fee_gst_iss as processingFeeGstIss,pos_entry_cap_code as posEntryCapCode,processing_fee_gst_acq as processingFeeGstAcq,
		assessment_fee_gst_iss as assessmentFeeGstIss,assessment_fee_gst_acq as assessmentFeeGstAcq,pos_entry_mode as posEntryMode,presentment_indicator as presentment,
		presentment_recvd_date as presentmentRecvdDate,org_amt_prsntmt as orgAmtPrsntmt,pos_condition_code as posCondCode,is_document_present as docInd,stlmnt_amt as settlementAmount,
		oct_data as octData,encrypted_pan as encryptedPan,encrypted_token_pan as encryptedtokenPan,transaction_date as rGCSReceivedDate,fraud_score as fraudScore,transaction_amount as orgAmtPrsntmt
		FROM 
		${originalTableName} 
		WHERE 
		txn_id = #{txnId};
	</select>
	
	<select id="getBulkTxnInfo" resultType="TxnSettlementDTO">
		SELECT 
		dts.txn_id as txnId,dts.org_txn_id as orgTxnId,dts.func_code as funcCode,dts.record_number as recordNumber,dts.mti as mti,dts.pan_prefix as panPrefix,dts.pan_suffix as panSuffix,
		dts.encrypted_token_pan as encryptedtokenPan,dts.token_pan_prefix as tokenPanPrefix,dts.token_pan_suffix as tokenPanSuffix,dts.transaction_amount as amtTran,
		dts.date_time_local as dateTimeLocal,dts.acq_ref_data as acqRefData,dts.card_acceptor_terminal_id as cardAcptTermId,dts.txn_originator_inst_id as txnOrgInstId,
		dts.transaction_ts as tstampLocal,dts.acquirer_id as instIdAcq,dts.txn_destination_inst_id as txnDestInstId,dts.net_recon_date as netReconDate,dts.stlmnt_amt as settlementAmount,
		dts.amt_npci_convert_billing_conv_rate as amtNpciConvertBillingConvRate,dts.partial_indicator as partialInd,dts.case_number as caseNo,dts.processing_code as pcode,
		dts.ctrl_no as ctrlNo,dts.mem_msg_txt as memMsgTxt,dts.is_document_present as docInd,dts.unique_filename as uniqueFileName,dts.processing_status as processingStatus,
		dts.internal_track_no as internalTrackNo,dts.rgcs_received_date as rGCSReceivedDate,dts.card_acceptor_address as cardAcptAddress,dts.card_acceptor_additional_address as cardAcptAdnlAddress,
		dts.additional_amount as amtAdd,dts.amt_card_billing as amtCardBilling,dts.card_bill_cur as cardBillCur,dts.processing_fee_acq as processingFeeAcq,dts.processing_fee_gst_acq as processingFeeGstAcq,
		dts.processing_fee_iss as processingFeeIss,dts.processing_fee_gst_iss as processingFeeGstIss,dts.assessment_fee_acq as assessmentFeeAcq,dts.assessment_fee_gst_acq as assessmentFeeGstAcq,
		dts.assessment_fee_iss as assessmentFeeIss,dts.assessment_fee_gst_iss as assessmentFeeGstIss,dts.intchg_fee_acq as intchgFeeAcq,dts.intchg_fee_gst_acq as intchgFeeGstAcq,
		dts.intchg_fee_iss as intchgFeeIss,dts.intchg_fee_gst_iss as intchgFeeGstIss,dts.auth_fee_acq as authFeeAcq,dts.auth_fee_gst_acq as authFeeGstAcq,dts.auth_fee_iss as authFeeIss,
		dts.auth_fee_gst_iss as authFeeGstIss,dts.entity_type as entityType,dts.fee_major_id as feeMajorId,dts.fee_minor_id as feeMinorId,dts.user_id as makerId,dts.checker_id as checkerId,
		dts.deadline_date as deadlineDate,dts.date_rcvd as dateRcvd,dts.to_state as toState,dts.org_net_recon_date as orgNetReconDate,dts.current_state as currentState,
		dts.issuer_bankgroup_id as bankGroupIdIss,dts.acquirer_bankgroup_id as bankGroupIdAcq,dts.pos_entry_mode as posEntryMode,dts.pos_condition_code as posCondCode,
		dts.merchant_cat_ind as merchantCatInd,dts.card_product_id as cardProductId,dts.acquirer_participant_id as participantIdAcq,dts.issuer_participant_id as participantIdIss,
		dts.card_variant_id as cardVariantId,dts.mcc as mcc,dts.org_amt_tran as orgAmtTran,dts.org_amt_prsntmt as orgAmtPrsntmt,dts.response_code as respCode,dts.original_tablename as originalTableName,
		dts.issuer_bin as instIdIss,dts.tran_cur as tranCur,dts.card_acceptor_country as cardAcptCountry,dts.acq_inst_country as acqInstCountry,dts.org_transaction_date as orgDateCapture,
		dts.encrypted_pan as encryptedPan,dts.encrypted_token_pan as encryptedtokenPan,dts.dispute_name as disputeName,dts.org_transaction_ts as orgTstampLocal,dts.approval_code as approvalCode,
		dts.org_transaction_date as dateCapture,dts.stlmnt_amt as orgStlmntAmt,dts.acq_recon_cur as acqReconCur,dts.iss_recon_cur as issReconCur ,dts.org_amt_tran as orgTranAmtInternational
		FROM 
		dispute_txn_settlement dts 
		where dts.org_txn_id = #{txnId} 
		order by transaction_ts ASC 
		limit 1;
	</select>
	
	<select id="getTxnPresentmentDetails" resultType="TxnSettlementDTO">
		Select txn_id as txnId,func_code as funcCode,mti as mti,pan_prefix as panPrefix,pan_suffix as panSuffix,encrypted_token_pan as encryptedtokenPan,token_pan_prefix as tokenPanPrefix,
		token_pan_suffix as tokenPanSuffix,processing_code as pcode,amt_card_billing as amtCardBilling,amt_acq_convert_billing as amtAcqConvertBilling,amt_acq_convert_billing_conv_rate as amtAcqConvertBillingConvRate,
		amt_npci_convert_billing_conv_rate as amtNpciConvertBillingConvRate,transaction_ts as tstampLocal,transaction_date as dateCapture,transaction_date as rGCSReceivedDate,
		mcc as mcc,acq_inst_country as acqInstCountry,amt_markup as amtMarkup,additional_amount as amtAdd,acquirer_id as instIdAcq,rrn as rrn,approval_code as approvalCode,
		service_code as serviceCode,card_acceptor_terminal_id as cardAcptTermId,card_acceptor_id as cardAcptId,card_acceptor_address as cardAcptAddress,card_acceptor_city as cardAcptCity,
		card_acceptor_region as cardAcptRegion,card_acceptor_country as cardAcptCountry,reason_code as reasonCode,cavv_code as cavvResult,cvv2_cvc2_code as cvv2Cvc2Result,
		cvv_cvc_code as cvvCvcResult,transaction_amount as amtTran, transaction_amount as orgAmtPrsntmt,merchant_cat_ind as merchantCatInd,eci_indicator as eciInd,
		stip_indicator as stipInd,tran_cur as tranCur,additional_amount_curr as amtAddCur,card_bill_cur as cardBillCur,card_markup_cur as cardMarkupCur,stan as stan,
		card_acceptor_postal_code as cardAcptPstCode,deposit_id as depositId,response_code as respCode,itm_response_code as itmRespCode,issuer_resp_code as issuerRespCode,
		msg_reason_code_acq as msgReasonCodeAcq,addl_data48 as addlData48,addl_data55 as addlData55,issuer_bin as instIdIss,is_reversed as revBy,online_txn_id as mappedDupData,
		card_acceptor_additional_address as cardAcptAdnlAddress,issuer_participant_id as participantIdIss,acquirer_participant_id as participantIdAcq,acquirer_settlement_bin as procIdAcq,
		issuer_settlement_bin as procIdIss,transaction_type as networkProgram,product_code as productCode,card_type as cardType,card_product_id as cardProductId,
		card_variant_id as cardVariantId,card_brand_id as cardVariantId,scheme_code as schemeCode,bin_type_id as binTypeId,bin_card_type_id as binCardTypeId,message_type_id as messageTypeId,
		issuer_country as countryIssInst,acquirer_bankgroup_id as bankGroupIdAcq,issuer_bankgroup_id as bankGroupIdIss,acquirer_subnet as netIdAcq,issuer_subnet as netIdIss,
		initiated_from as acqPlatProdId,internal_track_no as internalTrackNo,acq_recon_amt as acqReconAmt,acq_recon_conv as acqReconConv,acq_recon_cur as acqReconCur,iss_recon_amt as issReconAmt,
		iss_recon_conv as issReconConv,iss_recon_cur as issReconCur,net_recon_amt as netReconAmt,net_recon_conv as netReconConv,net_recon_cur as netReconCur,net_recon_date as netReconDate,
		recon_cycle_int as reconCycleInt,recon_cycle as reconCycle,processing_fee_acq as processingFeeAcq,processing_fee_iss as processingFeeIss,assessment_fee_acq as assessmentFeeAcq,
		assessment_fee_iss as assessmentFeeIss,intchg_fee_acq as intchgFeeAcq,intchg_fee_iss as intchgFeeIss,auth_fee_acq as authFeeAcq,auth_fee_iss as authFeeIss,fee_minor_id as feeMinorId,
		fee_major_id as feeMajorId,addl_data61 as addlData61,onus_fee_id as onusFeeId,auth_fee_gst_iss as authFeeGstIss,auth_fee_gst_acq as authFeeGstAcq,intchg_fee_gst_iss as intchgFeeGstIss,
		intchg_fee_gst_acq as intchgFeeGstAcq,processing_fee_gst_iss as processingFeeGstIss,processing_fee_gst_acq as processingFeeGstAcq,assessment_fee_gst_iss as assessmentFeeGstIss,
		assessment_fee_gst_acq as assessmentFeeGstAcq,pos_entry_mode as posEntryMode,pos_condition_code as posCondCode,is_document_present as docInd,stlmnt_amt as settlementAmount,
		fraud_score as fraudScore,org_amt_tran as orgAmtTran,org_amt_prsntmt as orgAmtPrsntmt,user_id as makerId,checker_id as checkerId,date_rcvd as dateRcvd,acq_ref_data as acqRefData,
		late_ind as latePresentmentIndicator,org_txn_id as orgTxnId
		<if test="isInternational">
		,fee_details as feeDetails,acq_recon_cur as acqReconCurrency,iss_recon_cur as issReconCurrency,org_amt_tran as orgTranAmtInternational
		</if>
		FROM 
		${originalTableName} 
		WHERE 
		org_txn_id = #{txnId} 
		order by transaction_ts desc
	</select>
	
	<select id="getDisputeBulkTxnInfo" resultType="TxnSettlementDTO">
		SELECT dts.txn_id as txnId,dts.func_code as funcCode,dts.mti as mti,dts.pan_prefix as panPrefix,dts.pan_suffix as panSuffix,dts.encrypted_token_pan as encryptedtokenPan,
		dts.token_pan_prefix as tokenPanPrefix,dts.token_pan_suffix as tokenPanSuffix,dts.record_number as recordNo,dts.transaction_amount as amtTran,dts.date_time_local as dateTimeLocal,
		dts.rrn as rrn,dts.acq_ref_data as acqRefData,dts.card_acceptor_terminal_id as cardAcptTermId,dts.txn_originator_inst_id as txnOrgInstId,dts.acquirer_id as instIdAcq,
		dts.transaction_ts as tstampLocal,dts.txn_destination_inst_id as txnDestInstId,dts.stlmnt_amt as settlementAmount,dts.amt_npci_convert_billing_conv_rate as amtNpciConvertBillingConvRate,
		dts.partial_indicator as partialInd,dts.case_number as caseNo,dts.processing_code as pcode,dts.ctrl_no as ctrlNo,dts.mem_msg_txt as memMsgTxt,dts.is_document_present as docInd,
		dts.reason_code as reasonCode,dts.unique_filename as uniqueFileName,dts.processing_status as processingStatus,dts.internal_track_no as internalTrackNo,dts.rgcs_received_date as rGCSReceivedDate,
		dts.card_acceptor_address as cardAcptAddress,dts.card_acceptor_additional_address as cardAcptAdnlAddress,dts.additional_amount as amtAdd,dts.amt_card_billing as amtCardBilling,
		dts.card_bill_cur as cardBillCur,dts.processing_fee_acq as processingFeeAcq,dts.processing_fee_gst_acq as processingFeeGstAcq,dts.processing_fee_iss as processingFeeIss,
		dts.processing_fee_gst_iss as processingFeeGstIss,dts.assessment_fee_acq as assessmentFeeAcq,dts.assessment_fee_gst_acq as assessmentFeeGstAcq,dts.assessment_fee_iss as assessmentFeeIss,
		dts.assessment_fee_gst_iss as assessmentFeeGstIss,dts.intchg_fee_acq as intchgFeeAcq,dts.intchg_fee_gst_acq as intchgFeeGstAcq,dts.intchg_fee_iss as intchgFeeIss,dts.intchg_fee_gst_iss as intchgFeeGstIss,
		dts.auth_fee_acq as authFeeAcq,dts.auth_fee_gst_acq as authFeeGstAcq,dts.auth_fee_iss as authFeeIss,dts.auth_fee_gst_iss as authFeeGstIss,dts.entity_type as entityType,dts.user_id as makerId,
		dts.checker_id as checkerId,dts.deadline_date as deadlineDate,dts.net_recon_date as netReconDate,dts.date_rcvd as dateRcvd,dts.to_state as toState,dts.fee_major_id as feeMajorId,
		dts.fee_minor_id as feeMinorId,dts.current_state as currentState,dts.issuer_bankgroup_id as bankGroupIdIss,dts.acquirer_bankgroup_id as bankGroupIdAcq,dts.pos_entry_mode as posEntryMode,
		dts.pos_condition_code as posCondCode,dts.merchant_cat_ind as merchantCatInd,dts.card_product_id as cardProductId,dts.acquirer_participant_id as participantIdAcq,
		dts.issuer_participant_id as participantIdIss,dts.card_variant_id as cardVariantId,dts.mcc as mcc,dts.org_amt_tran as orgAmtTran,dts.org_amt_prsntmt as orgAmtPrsntmt,
		dts.org_net_recon_date as orgNetReconDate,dts.response_code as respCode,dts.doc_file_path as docFilePath,dts.tran_cur as tranCur,dts.document_upload_date as documentUploadDate,
		dts.complaint_number as complaintNumber,dts.customer_mobile_no as customerMobileNo,dts.customer_email_id as customerEmailId,dts.dispute_name as disputeName,
		dts.filing_dispute_fee_code as filingDisputeFeeCode,dts.filing_dispute_fee_acq as filingDisputeFeeAcq,dts.filing_dispute_fee_iss as filingDisputeFeeIss,dts.filing_dispute_fee_gst_acq as filingDisputeFeeGSTAcq,
		dts.filing_dispute_fee_gst_iss as filingDisputeFeeIss,dts.member_dispute_fee_code as memberDisputeFeeCode,dts.member_dispute_fee_acq as memberDisputeFeeAcq,
		dts.member_dispute_fee_iss as memberDisputeFeeIss,dts.member_dispute_fee_gst_acq as memberDisputeFeeGSTAcq,dts.member_dispute_fee_gst_iss as memberDisputeFeeGSTIss,dts.cust_comp_dispute_fee_code as custCompDisputeFeeCode,
		dts.cust_comp_dispute_fee_acq as custCompDisputeFeeAcq,dts.cust_comp_dispute_fee_iss as custCompDisputeFeeIss,dts.cust_comp_dispute_fee_gst_acq as custCompDisputeFeeGSTAcq,dts.cust_comp_dispute_fee_gst_iss as custCompDisputeFeeGSTIss,
		dts.penalty_dispute_fee_code as penaltyDisputeFeeCode,dts.penalty_dispute_fee_acq as penaltyDisputeFeeAcq,dts.penalty_dispute_fee_iss as penaltyDisputeFeeIss,
		dts.penalty_dispute_fee_gst_acq as penaltyDisputeFeeGSTAcq,dts.penalty_dispute_fee_gst_iss as penaltyDisputeFeeGSTIss,dts.card_acceptor_country as cardAcptCountry,
		dts.card_acceptor_region as cardAcptRegion,dts.card_acceptor_city as cardAcptCity,dts.approval_code as approvalCode,dts.org_action as orgAction,dts.acq_recon_cur as acqReconCur,dts.iss_recon_cur as issReconCur,dts.org_amt_tran as orgTranAmtInternational
		FROM 
		dispute_txn_settlement dts 
		where org_txn_id = #{txnId} 
		and 
		org_transaction_date=#{orgTranDate} 
		order by transaction_ts;
	</select>
	
	<select id="getReversalTxnData" resultType="TxnSettlementDTO">
		Select txn_id as txnId,func_code as funcCode,mti as mti,pan_prefix as panPrefix,pan_suffix as panSuffix,token_pan_prefix as tokenPanPrefix,token_pan_suffix as tokenPanSuffix,
		processing_code as pcode,transaction_amount as amtTran,amt_card_billing as amtCardBilling,amt_acq_convert_billing as amtAcqConvertBilling,amt_acq_convert_billing_conv_rate as amtAcqConvertBillingConvRate,
		amt_npci_convert_billing_conv_rate as amtNpciConvertBillingConvRate,stan as stan,transaction_ts as tstampLocal,transaction_date as dateCapture,mcc as mcc,
		acq_inst_country as acqInstCountry,pos_entry_cap_code as posEntryCapCode,pos_condition_code as posCondCode,amt_markup as amtMarkup,additional_amount as amtAdd,acquirer_id as instIdAcq,
		rrn as rrn,approval_code as approvalCode,service_code as serviceCode,card_acceptor_terminal_id as cardAcptTermId,card_acceptor_id as cardAcptId,card_acceptor_address as cardAcptAddress,
		card_acceptor_additional_address as cardAcptAdnlAddress,card_acceptor_city as cardAcptCity,card_acceptor_region as cardAcptRegion,card_acceptor_country as cardAcptCountry,
		reason_code as reasonCode,cavv_code as cavvResult,cvv2_cvc2_code as cvv2Cvc2Result,cvv_cvc_code as cvvCvcResult, merchant_cat_ind as merchantCatInd,eci_indicator as eciInd,
		stip_indicator as stipInd,tran_cur as tranCur,additional_amount_curr as amtAddCur,card_bill_cur as cardBillCur,card_markup_cur as cardMarkupCur,card_acceptor_postal_code as cardAcptPstCode,
		deposit_id as depositId,response_code as respCode,itm_response_code as itmRespCode,issuer_resp_code as issuerRespCode,msg_reason_code_acq as msgReasonCodeAcq,
		addl_data48 as addlData48,addl_data55 as addlData55,issuer_bin as instIdIss,is_reversed as revBy,online_txn_id as mappedDupData,issuer_participant_id as participantIdIss,
		acquirer_participant_id as participantIdAcq,acquirer_settlement_bin as procIdAcq,issuer_settlement_bin as procIdIss,transaction_type as networkProgram,product_code as productCode,
		card_type as cardType,card_product_id as cardProductId,card_variant_id as cardVariantId,card_brand_id as cardBrandId,scheme_code as schemeCode,bin_type_id as binTypeId,
		bin_card_type_id as binCardTypeId,message_type_id as messageTypeId,issuer_country as countryIssInst,acquirer_bankgroup_id as bankGroupIdAcq,issuer_bankgroup_id as bankGroupIdIss,
		acquirer_subnet as netIdAcq,issuer_subnet as netIdIss,initiated_from as acqPlatProdId,internal_track_no as internalTrackNo,acq_recon_amt as acqReconAmt,acq_recon_conv as acqReconConv,
		acq_recon_cur as acqReconCur,iss_recon_amt as issReconAmt,iss_recon_conv as issReconConv,iss_recon_cur as issReconCur,net_recon_amt as netReconAmt,net_recon_conv as netReconConv,
		net_recon_cur as netReconCur,net_recon_date as netReconDate,recon_cycle_int as reconCycleInt,recon_cycle as reconCycle,processing_fee_acq as processingFeeAcq,
		processing_fee_iss as processingFeeIss,assessment_fee_acq as assessmentFeeAcq,assessment_fee_iss as assessmentFeeIss,intchg_fee_acq as intchgFeeAcq,intchg_fee_iss as intchgFeeIss,
		auth_fee_acq as authFeeAcq,auth_fee_iss as authFeeIss,fee_minor_id as feeMinorId,fee_major_id as feeMajorId,addl_data61 as addlData61,onus_fee_id as onusFeeId,
		auth_fee_gst_iss as authFeeGstIss,auth_fee_gst_acq as authFeeGstAcq,intchg_fee_gst_iss as intchgFeeGstIss,intchg_fee_gst_acq as intchgFeeGstAcq,processing_fee_gst_iss as processingFeeGstIss,
		processing_fee_gst_acq as processingFeeGstAcq,assessment_fee_gst_iss as assessmentFeeGstIss,assessment_fee_gst_acq as assessmentFeeGstAcq,pos_entry_mode as posEntryMode,
		transaction_date as rGCSReceivedDate,stlmnt_amt as settlementAmount
		FROM
		REV_TXN_SETTLEMENT 
		where org_txn_id = #{txnId};
	</select>
	
	<select id="getDisputeTxnDetailByTxnId" resultType="TxnSettlementDTO">
		SELECT dts.txn_id as txnId,dts.func_code as funcCode,dts.mti as mti,dts.pan_prefix as panPrefix,dts.pan_suffix as panSuffix,dts.encrypted_token_pan as encryptedtokenPan,
		dts.token_pan_prefix as tokenPanPrefix,dts.token_pan_suffix as tokenPanSuffix,dts.record_number as recordNo,dts.transaction_amount as amtTran,dts.date_time_local as dateTimeLocal,
		dts.rrn as rrn,dts.acq_ref_data as acqRefData,dts.card_acceptor_terminal_id as cardAcptTermId,dts.txn_originator_inst_id as txnOrgInstId,dts.acquirer_id as instIdAcq,
		dts.txn_destination_inst_id as txnDestInstId,dts.stlmnt_amt as settlementAmount,dts.amt_npci_convert_billing_conv_rate as amtNpciConvertBillingConvRate,dts.partial_indicator as partialInd,
		dts.case_number as caseNo,dts.processing_code as pcode,dts.ctrl_no as ctrlNo,dts.mem_msg_txt as memMsgTxt,dts.is_document_present as docInd,dts.reason_code as reasonCode,
		dts.unique_filename as uniqueFileName,dts.processing_status as processingStatus,dts.internal_track_no as internalTrackNo,dts.rgcs_received_date as rGCSReceivedDate,
		dts.card_acceptor_address as cardAcptAddress,dts.card_acceptor_additional_address as cardAcptAdnlAddress,dts.additional_amount as amtAdd,dts.amt_card_billing as amtCardBilling,
		dts.card_bill_cur as cardBillCur,dts.processing_fee_acq as processingFeeAcq,dts.processing_fee_gst_acq as processingFeeGstAcq,dts.processing_fee_iss as processingFeeIss,
		dts.processing_fee_gst_iss as processingFeeGstIss,dts.assessment_fee_acq as assessmentFeeAcq,dts.assessment_fee_gst_acq as assessmentFeeGstAcq,dts.assessment_fee_iss as assessmentFeeIss,
		dts.assessment_fee_gst_iss as assessmentFeeGstIss,dts.intchg_fee_acq as intchgFeeAcq,dts.intchg_fee_gst_acq as intchgFeeGstAcq,dts.intchg_fee_iss as intchgFeeIss,
		dts.intchg_fee_gst_iss as intchgFeeGstIss,dts.auth_fee_acq as authFeeAcq,dts.auth_fee_gst_acq as authFeeGstAcq,dts.auth_fee_iss as authFeeIss,dts.auth_fee_gst_iss as authFeeGstIss,
		dts.entity_type as entityType,dts.user_id as makerId,dts.checker_id as checkerId,dts.deadline_date as deadlineDate,dts.net_recon_date as netReconDate,dts.date_rcvd as dateRcvd,
		dts.to_state as toState,dts.fee_major_id as feeMajorId,dts.fee_minor_id as feeMinorId,dts.transaction_ts as tstampLocal,dts.current_state as currentState,
		dts.issuer_bankgroup_id as bankGroupIdIss,dts.acquirer_bankgroup_id as bankGroupIdAcq,dts.pos_entry_mode as posEntryMode,dts.merchant_cat_ind as merchantCatInd,
		dts.card_product_id as cardProductId,dts.acquirer_participant_id as participantIdAcq,dts.issuer_participant_id as participantIdIss,dts.card_variant_id as cardVariantId,
		dts.mcc as mcc,dts.org_amt_tran as orgAmtTran,dts.org_amt_prsntmt as orgAmtPrsntmt,dts.org_net_recon_date as orgNetReconDate,dts.response_code as respCode,
		dts.original_tablename as originalTableName,dts.doc_file_path as docFilePath,dts.tran_cur as tranCur,dts.document_upload_date as documentUploadDate,dts.complaint_number as complaintNumber,
		dts.customer_email_id as customerEmailId,dts.customer_mobile_no as customerMobileNo,dts.dispute_name as disputeName,dts.filing_dispute_fee_code as filingDisputeFeeCode,dts.filing_dispute_fee_acq as filingDisputeFeeAcq,
		dts.filing_dispute_fee_iss as filingDisputeFeeIss,dts.filing_dispute_fee_gst_acq as filingDisputeFeeGSTAcq,dts.filing_dispute_fee_gst_iss as filingDisputeFeeIss,dts.member_dispute_fee_code as memberDisputeFeeCode,
		dts.member_dispute_fee_acq as memberDisputeFeeAcq,dts.member_dispute_fee_iss as memberDisputeFeeIss,dts.member_dispute_fee_gst_acq as memberDisputeFeeGSTAcq,dts.member_dispute_fee_gst_iss as memberDisputeFeeGSTIss,
		dts.cust_comp_dispute_fee_code as custCompDisputeFeeCode,dts.cust_comp_dispute_fee_acq as custCompDisputeFeeAcq,dts.cust_comp_dispute_fee_iss as custCompDisputeFeeIss,
		dts.cust_comp_dispute_fee_gst_acq as custCompDisputeFeeGSTAcq,dts.cust_comp_dispute_fee_gst_iss as custCompDisputeFeeGSTIss,dts.penalty_dispute_fee_code as penaltyDisputeFeeCode,
		dts.penalty_dispute_fee_acq as penaltyDisputeFeeAcq,dts.penalty_dispute_fee_iss as penaltyDisputeFeeIss,dts.penalty_dispute_fee_gst_acq as penaltyDisputeFeeGSTAcq,
		dts.penalty_dispute_fee_gst_iss as penaltyDisputeFeeGSTIss,dts.org_action as orgAction,dts.acq_recon_cur as acqReconCur,dts.iss_recon_cur as issReconCur,dts.org_amt_tran as orgTranAmtInternational
		FROM 
		dispute_txn_settlement dts 
		where org_txn_id = #{txnId} 
		and 
		org_transaction_date = #{orgTranDate} 
		order by transaction_ts
	</select>
	
	<select id="fetchTxnDestId" resultType="String">
		select participant_id as txnDestInstId 
		from 
		participant 
		where 
		participant_id in 
		(select participant_id from membin_details where bin_number = #{instIdIss} and status='A');
	</select>
	
	<select id="fetchTxnOrgId" resultType="String">
		select participant_id as txnOrgInstId 
		from 
		participant 
		where 
		participant_id in 
		(select participant_id from membin_details where acquirer_id = #{instIdAcq} and status='A');
	</select>
	
	<select id="fetchDisputeCaseNo" resultType="String">
		select case_number as caseNo
		from 
		dispute_txn_settlement 
		where 
		org_txn_id = #{txnId} and org_transaction_date=#{orgTranDate} LIMIT 1;
	</select>
	
	<select id="fetchIdFromDisputeStagSequence" resultType="int">
		SELECT nextval('dispute_txn_settlement_staging_seq')
	</select>
	
	<select id="getMccDesc" resultType="String">
		select mcc_desc 
		from 
		mcc_config where mcc_code=#{mcc};
	</select>
	
	<select id="fetchOriginatorInstId" resultType="String">
		select sys_value 
		from 
		sys_params 
		where sys_type = 'NPCI' 
		and
		sys_key = 'Participant_Id'
	</select>
	
	<select id="getBankName" resultType="String">
		SELECT bank_name 
		from 
		participant 
		where participant_id =#{bankPartId} 
		LIMIT 1;
	</select>
	
	<select id="getBinNumberAcq" resultType="String">
		select acquirer_id 
		from 
		membin_details 
		where participant_id = #{participantId} 
		and 
		bin_type = 'A' 
		and status = 'A';
	</select>
	
	<select id="getBinNumberIss" resultType="String">
		select bin_number 
		from 
		membin_details 
		where 
		participant_id = #{participantId} 
		and 
		bin_type = 'I' 
		and 
		status = 'A'; 
	</select>
	
	<select id="getCapAmtCalReqInfo" resultType="org.npci.settlenxt.portal.common.dto.ActionCodeDTO">
		select 
		action_code as actionCode,
		cap_amt_cal_req as capAmtCalReq
		from 
		action_code 
		where 
		action_code in 
		<foreach item='id' index='index' collection='actionList' open='(' separator=',' close=')'>#{id}
		</foreach>
	</select>
	
	<select id="getFilePathList" resultType="org.npci.settlenxt.portal.common.dto.FileUploadDTO">
		select 
		document_path as documentPath,
		upload_date as createdOn,
		user_id as participantId,
		doc_type as documentType
		from 
		dispute_document_info 
		where 
		txn_id = #{txnId};
	</select>
	
	<select id="getFilePathListStg" resultType="org.npci.settlenxt.portal.common.dto.FileUploadDTO">
		select 
		document_path as documentPath,
		upload_date as createdOn,
		user_id as participantId,
		doc_type as documentType
		from 
		dispute_document_info_staging 
		where 
		txn_id = #{txnId};
	</select>
	
	<select id="getFeeNameByFeeCd" resultType="String">
		select 
		fee_description 
		from 
		fee_rate 
		where fee_code = #{feeCd} 
		and 
		status = 'A' 
		limit 1;
	</select>
	<!-- Not Common -->	
	<select id="getTransactionDetails" resultType="TxnSettlementDTO">
		select
		pan_prefix as panPrefix,pan_suffix as panSuffix,encrypted_token_pan as encryptedtokenPan,token_pan_prefix as tokenPanPrefix,
		token_pan_suffix as tokenPanSuffix,product_code as productCode,transaction_ts as tstampLocal,rrn as rrn,tran_cur as tranCur,
		transaction_amount as amtTran,additional_amount as amountTran,approval_code as approvalCode,pos_entry_mode as posEntryMode,
		card_acceptor_terminal_id as cardAcptTermId,card_acceptor_postal_code as cardAcptPstCode,card_acceptor_id as cardAcptId,
		card_acceptor_additional_address as cardAcptAdnlAddress,response_code as respCode,txn_id as txnId,online_txn_id as mappedDupData,
		func_code as funcCode,processing_code as pcode,transaction_date as receivedDate,transaction_date as dateCapture,mcc as mcc,card_acceptor_address as cardAcptAddress,
		card_acceptor_city as cardAcptCity,card_acceptor_region as cardAcptRegion,card_acceptor_country as cardAcptCountry,addl_data48 as addlData48,addl_data61 as addlData61,card_type as cardType,card_brand_id as cardBrandId,mti as mti,presentment_indicator as presentment,
		date_rcvd as dateRcvd, net_recon_date as netReconDate,acq_ref_data as acqRefData,initiated_from as acqPlatProdId,
		presentment_recvd_date as presentmentRecvdDate,acquirer_participant_id as participantIdAcq,issuer_participant_id as participantIdIss
		FROM ${originalTableName} s
		WHERE (
		<if test="funcCode!= null and funcCode!='' and tokenPan == '' and rrn == '' ">
			s.func_code=#{funcCode} 
		</if>
		<if test="tokenPan!= null and tokenPan!='' and rrn == '' and funcCode == '' "> 
			(s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) 
		</if>
		<if test="rrn!= null and rrn!='' and tokenPan == '' and funcCode == '' ">
			s.rrn=#{rrn} 
		</if>
		<if test="tokenPan!='' and funcCode!='' and rrn!='' ">
			s.func_code=#{funcCode} AND s.rrn=#{rrn} AND (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan})
		</if>
		<if test="tokenPan != '' and rrn != '' and funcCode == '' ">
			s.rrn=#{rrn} AND (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan})
		</if>
		<if test="funcCode !='' and rrn !='' and tokenPan == '' ">
			s.func_code=#{funcCode} AND s.rrn=#{rrn}
		</if>
		<if test="funcCode !='' and tokenPan !='' and rrn =='' ">
			(s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) AND s.func_code=#{funcCode} 
		</if>)
		AND 
		((s.issuer_participant_id=#{partId}) OR (s.acquirer_participant_id= #{partId}))
	</select>
	
	<select id="getRevTranDetails" resultType="TxnSettlementDTO">
		select
		pan_prefix as panPrefix,pan_suffix as panSuffix,encrypted_token_pan as encryptedtokenPan,token_pan_prefix as tokenPanPrefix,
		token_pan_suffix as tokenPanSuffix,product_code as productCode,transaction_ts as tstampLocal,rrn as rrn,tran_cur as tranCur,
		transaction_amount as amtTran,additional_amount as amtAdd,approval_code as approvalCode,pos_entry_mode as posEntryMode,
		card_acceptor_terminal_id as cardAcptTermId,card_acceptor_postal_code as cardAcptPstCode,card_acceptor_id as cardAcptId,
		card_acceptor_additional_address as cardAcptAdnlAddress,response_code as respCode,txn_id as txnId,online_txn_id as mappedDupData,
		func_code as funcCode,processing_code as pcode,transaction_date as receivedDate,transaction_date as dateCapture,mcc as mcc,card_acceptor_address as cardAcptAddress,
		card_acceptor_city as cardAcptCity,card_acceptor_region as cardAcptRegion,card_acceptor_country as cardAcptCountry,addl_data48 as addlData48,addl_data61 as addlData61,
		card_type as cardType,card_brand_id as cardBrandId,mti as mti,net_recon_date as netReconDate,initiated_from as acqPlatProdId,
		acquirer_participant_id as participantIdAcq,issuer_participant_id as participantIdIss,org_txn_id as orgTxnId,acq_ref_data as acqRefData 
		FROM 
		rev_txn_settlement s
		WHERE (
		<if test="funcCode!= null and funcCode!='' and tokenPan == '' and rrn == '' ">s.func_code=#{funcCode} </if> 
		<if test="tokenPan!= null and tokenPan!='' and rrn == '' and funcCode == '' "> (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) </if>
		<if test="rrn!= null and rrn!='' and tokenPan == '' and funcCode == '' ">  s.rrn=#{rrn} </if>
		<if test="tokenPan!='' and funcCode!='' and rrn!='' ">s.func_code=#{funcCode} AND s.rrn=#{rrn} AND (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan})</if>
		<if test="tokenPan != '' and rrn != '' and funcCode == '' ">s.rrn=#{rrn} AND (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) </if>
		<if test="funcCode !='' and rrn !='' and tokenPan == '' ">s.func_code=#{funcCode} AND s.rrn=#{rrn}  </if>
		<if test="funcCode !='' and tokenPan !='' and rrn =='' ">(s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) AND s.func_code=#{funcCode}  </if>)
		AND  ((s.issuer_participant_id=#{partId}) OR (s.acquirer_participant_id= #{partId})) and
		(date(s.transaction_date) BETWEEN  #{fromDate} AND #{toDate});
	</select>
	
	<select id="getPresentmentDetails" resultType="TxnSettlementDTO">
		select
		pan_prefix as panPrefix,pan_suffix as panSuffix,encrypted_token_pan as encryptedtokenPan,token_pan_prefix as tokenPanPrefix,
		token_pan_suffix as tokenPanSuffix,product_code as productCode,transaction_ts as tstampLocal,rrn as rrn,tran_cur as tranCur,
		transaction_amount as amtTran,additional_amount as amtAdd,approval_code as approvalCode,pos_entry_mode as posEntryMode,
		card_acceptor_terminal_id as cardAcptTermId,card_acceptor_postal_code as cardAcptPstCode,card_acceptor_id as cardAcptId,
		card_acceptor_additional_address as cardAcptAdnlAddress,response_code as respCode,txn_id as txnId,online_txn_id as mappedDupData,
		func_code as funcCode,processing_code as pcode,transaction_date as receivedDate,transaction_date as dateCapture,mcc as mcc,card_acceptor_address as cardAcptAddress,
		card_acceptor_city as cardAcptCity,card_acceptor_region as cardAcptRegion,card_acceptor_country as cardAcptCountry,addl_data48 as addlData48,addl_data61 as addlData61,card_type as cardType,card_brand_id as cardBrandId,mti as mti,presentment_indicator as presentment,
		org_txn_id as orgTxnId,date_rcvd as dateRcvd, net_recon_date as netReconDate, acq_ref_data as acqRefData,initiated_from as  acqPlatProdId,acquirer_participant_id as participantIdAcq,issuer_participant_id as participantIdIss,org_transaction_ts as orgTstampLocal
		FROM 
		${originalTableName} s
		WHERE (
		<if test="funcCode!= null and funcCode!='' and tokenPan == '' and rrn == '' ">s.func_code=#{funcCode} </if>
		<if test="tokenPan!= null and tokenPan!='' and rrn == '' and funcCode == '' ">  (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) </if>
		<if test="rrn!= null and rrn!='' and tokenPan == '' and funcCode == '' ">  s.rrn=#{rrn} </if>
		<if test="tokenPan!='' and funcCode!='' and rrn!='' ">s.func_code=#{funcCode} AND s.rrn=#{rrn} AND (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan})</if>
		<if test="tokenPan != '' and rrn != '' and funcCode == '' ">s.rrn=#{rrn} AND (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) </if>
		<if test="funcCode !='' and rrn !='' and tokenPan == '' ">s.func_code=#{funcCode} AND s.rrn=#{rrn}  </if>
		<if test="funcCode !='' and tokenPan !='' and rrn =='' ">(s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) AND s.func_code=#{funcCode}  </if>)
		AND 
		((s.issuer_participant_id=#{partId}) OR (s.acquirer_participant_id= #{partId})) order by transaction_ts desc;
	</select>
	
	<select id="getApprovedRejectedTxnDetails" resultType="TxnSettlementDTO">
		select 
		pan_prefix as panPrefix,pan_suffix as panSuffix,acq_ref_data as acqRefData,product_code as productCode,transaction_ts as tstampLocal,rrn as rrn,tran_cur as tranCur,
		transaction_amount as amtTran,transaction_amount as amountTran,additional_amount as amtAdd,approval_code as approvalCode,pos_entry_mode as posEntryMode,card_acceptor_terminal_id as cardAcptTermId,
		card_acceptor_id as cardAcptId,card_acceptor_additional_address as cardAcptAdnlAddress,reason_code as reasonCode,txn_id as txnId,org_txn_id as orgTxnId,
		func_code as funcCode,processing_code as pcode,mcc as mcc,card_acceptor_address as cardAcptAddress,card_acceptor_city as cardAcptCity,card_acceptor_region as cardAcptRegion,
		card_acceptor_country as cardAcptCountry,card_type as cardType,card_brand_id as cardBrandId,mti as mti,original_tablename as originalTableName,
		rgcs_received_date as rGCSReceivedDate,entity_type as entityType,to_state as toState,case_number as caseNo,org_func_code as orgFuncCode,status as status,
		processing_status as processingStatus
		FROM 
		dispute_txn_settlement_staging 
		where
		entity_type='N' and status in ('IP','FAILED','SUCCESS');
	</select>
	
	<select id="getTxnInfoStaging" resultType="TxnSettlementDTO">
		select 
		txn_id as txnId,func_code as funcCode,org_func_code as orgFuncCode,mti as mti,record_number as recordNumber,transaction_amount as amountTran,date_time_local as dateTimeLocal,
		acq_ref_data as acqRefData,card_acceptor_terminal_id as cardAcptTermId,txn_originator_inst_id as txnOrgInstId,acquirer_id as instIdAcq,issuer_bin as instIdIss,
		processing_code as pcode,user_id as makerId,transaction_ts as tstampLocal,rrn as rrn,rgcs_received_date as rGCSReceivedDate,tran_cur as ccOfTransaction,
		additional_amount as amtAdd,entity_type as entityType,acquirer_participant_id as participantIdAcq,issuer_participant_id as participantIdIss,stage_id as stageId,
		date_local as dateLocal,partial_indicator as partialInd,to_state as toState,reason_code as reasonCode,mem_msg_txt as memMsgTxt,is_document_present as docInd,
		doc_file_path as docFilePath,internal_track_no as internalTrackNo,case_number as caseNo,fee_type_code as feeTypCode,response_code as respCode,ctrl_no as ctrlNo,encrypted_pan as encryptedPan,
		merchant_cat_ind as merchantCatInd
		from 
		dispute_txn_settlement_staging 
		where 
		org_txn_id = #{txnId} 
		and 
		status in ('P','RPA','PR');
	</select>
	
	<select id="getDisputeTxnStatus" resultType="TxnSettlementDTO">
		select 
		dts.mti as mti,dts.func_code as funcCode,dts.to_state as toState,dts.rgcs_received_date as rGCSReceivedDate 
		from 
		dispute_txn_settlement dts
		where 
		dts.acq_ref_data =#{acqRefData} 
		and
		((dts.issuer_participant_id=#{partId}) OR (dts.acquirer_participant_id= #{partId}))
		and
		dts.date_rcvd >= #{fromDate} 
		order by transaction_ts desc 
		limit 1;
	</select>
	
	<select id="getDisputeTransactionDetails" resultType="TxnSettlementDTO">
		select 
		ds.pan_prefix as panPrefix,ds.pan_suffix as panSuffix,ds.encrypted_token_pan as encryptedtokenPan,ds.token_pan_prefix as tokenPanPrefix,ds.token_pan_suffix as tokenPanSuffix,
		ds.product_code as productCode,ds.transaction_ts as tstampLocal,ds.rrn as rrn,ds.tran_cur as tranCur,ds.transaction_amount as amountTran,ds.additional_amount as amtAdd,
		ds.approval_code as approvalCode,ds.pos_entry_mode as posEntryMode,ds.card_acceptor_terminal_id as cardAcptTermId,ds.card_acceptor_id as cardAcptId,
		ds.card_acceptor_address as cardAcptAddress,ds.card_acceptor_additional_address as cardAcptAdnlAddress,ds.response_code as respCode,ds.txn_id as txnId,
		ds.processing_code as pcode,ds.mcc as mcc,ds.card_acceptor_city as cardAcptCity,ds.card_acceptor_region as cardAcptRegion,ds.card_acceptor_country as cardAcptCountry,
		ds.card_type as cardType,ds.case_number as caseNo,ds.card_brand_id as cardBrandId,ds.func_code as funcCode,ds.mti as mti,ds.original_tablename as originalTableName,
		ds.acq_ref_data as acqRefData, ds.org_transaction_date as orgDateCapture,ds.org_txn_id as orgTxnId,ds.entity_type as entityType,ds.to_state as toState,
		ds.rgcs_received_date as rGCSReceivedDate,ds.date_rcvd as dateRcvd,ds.net_recon_date as netReconDate,ds.initiated_from as acqPlatProdId,
		ds.acquirer_participant_id as participantIdAcq,ds.issuer_participant_id as participantIdIss,ds.org_transaction_ts as orgTstampLocal,ds.acq_recon_cur as acqReconCur,ds.iss_recon_cur as issReconCur,ds.org_amt_tran as orgTranAmtInternational
		from
		dispute_txn_settlement ds  
		where
		(
		<if test="funcCode!= null and funcCode!='' and tokenPan == '' and rrn == '' ">ds.func_code=#{funcCode} </if>
		<if test="tokenPan!= null and tokenPan!='' and funcCode == ''  and rrn == '' ">(ds.encrypted_pan=#{tokenPan} or ds.encrypted_token_pan = #{tokenPan}) </if>
		<if test="rrn!=null and rrn!='' and funcCode == ''  and tokenPan == '' ">ds.rrn=#{rrn} </if>
		<if test="rrn!=null and rrn!='' and tokenPan!=null and tokenPan!='' and funcCode == '' ">ds.rrn=#{rrn} and (ds.encrypted_pan=#{tokenPan} or ds.encrypted_token_pan = #{tokenPan}) </if>
		<if test="rrn!=null and rrn!='' and funcCode!=null and funcCode!='' and tokenPan == '' ">ds.rrn=#{rrn} and ds.func_code=#{funcCode} </if>
		<if test="tokenPan!=null and tokenPan!='' and funcCode!=null and funcCode!='' and rrn == '' ">ds.func_code=#{funcCode} AND (ds.encrypted_pan=#{tokenPan} or ds.encrypted_token_pan = #{tokenPan}) </if>
		<if test="tokenPan!='' and funcCode!='' and rrn!='' ">ds.func_code=#{funcCode} AND (ds.encrypted_pan=#{tokenPan} or ds.encrypted_token_pan = #{tokenPan}) AND ds.rrn=#{rrn} </if>
		AND ((ds.issuer_participant_id=#{partId}) OR (ds.acquirer_participant_id= #{partId}))
		and
		(ds.date_rcvd BETWEEN  #{fromDate} AND #{toDate}));
	</select>
	
	<select id="getTransactionDetailsStage" resultType="TxnSettlementDTO">
		select stage_id as stageId,pan_prefix as  panPrefix,pan_suffix as panSuffix,acq_ref_data as acqRefData,product_code as productCode,transaction_ts as tstampLocal,
		rrn as rrn,tran_cur as tranCur,transaction_amount as amtTran,transaction_amount as amountTran,additional_amount as amtAdd,approval_code as approvalCode,pos_entry_mode as posEntryMode,
		card_acceptor_terminal_id as cardAcptTermId,card_acceptor_id as cardAcptId,card_acceptor_additional_address as cardAcptAdnlAddress,reason_code as reasonCode,
		txn_id as txnId,org_txn_id as orgTxnId,func_code as funcCode,processing_code as pcode,mcc as mcc,card_acceptor_address as cardAcptAddress,
		card_acceptor_city as cardAcptCity,card_acceptor_region as cardAcptRegion,card_acceptor_country as cardAcptCountry,card_type as cardType,
		card_brand_id as cardBrandId,mti as mti,original_tablename as originalTableName,rgcs_received_date as rGCSReceivedDate,entity_type as entityType,
		to_state as toState,case_number as caseNo,org_func_code as orgFuncCode,status as status,processing_status as processingStatus, encrypted_token_pan as encryptedtokenPan,
		token_pan_prefix as tokenPanPrefix, token_pan_suffix as tokenPanSuffix,acquirer_participant_id as participantIdAcq 
		FROM 
		dispute_txn_settlement_staging 
		where
		(entity_type='N' and status='P') or status='RPA';
	</select>
	
	<select id="getAcqSettlementBin" resultType="String">
		select settlement_bin from membin_details where acquirer_id = #{acqBin} and status = 'A';
	</select>
	
	<select id="getIssSettlementBin" resultType="String">
		select settlement_bin from membin_details where bin_number = #{issBin} and status = 'A';
	</select>
	
	<select id="getAcqSettlementBinFrmNetworkBin" resultType="String">
		select settlement_bin from network_bin_details where acquirer_id = #{instIdAcq} and status = 'A';
	</select>
	
	<select id="getIssSettlementBinFrmNetworkBin" resultType="String">
		select settlement_bin from network_bin_details where bin_number = #{instIdIss} and status = 'A';
	</select>
	
	<select id="getPendingDisputeTxnListStatus" resultType="TxnSettlementDTO">
		SELECT 
		dts.status as status,dts.processing_status as processingStatus
		FROM dispute_txn_settlement_staging dts 
		where dts.txn_id=#{txnId} 
		and 
		dts.func_code = #{funcCode} 
		and 
		stage_id = #{stageId};
	</select>
	
	<select id="getPendingDisputeBulkTxnInfo" resultType="TxnSettlementDTO">
		SELECT 
		dts.txn_id as txnId,dts.func_code as funcCode,dts.record_number as recordNumber,dts.mti as mti,dts.pan_prefix as panPrefix,dts.pan_suffix as panSuffix,
		dts.encrypted_token_pan as encryptedtokenPan,dts.token_pan_prefix as tokenPanPrefix,dts.token_pan_suffix as tokenPanSuffix,dts.transaction_amount as amtTran,
		dts.date_time_local as dateTimeLocal,dts.rrn as rrn,dts.acq_ref_data as acqRefData,dts.card_acceptor_terminal_id as cardAcptTermId,dts.txn_originator_inst_id as txnOrgInstId,
		dts.transaction_ts as tstampLocal,dts.acquirer_id as instIdAcq,dts.txn_destination_inst_id as txnDestInstId,dts.net_recon_date as netReconDate,dts.stlmnt_amt as settlementAmount,
		dts.amt_npci_convert_billing_conv_rate as amtNpciConvertBillingConvRate,dts.partial_indicator as partialInd,dts.case_number as caseNo,dts.processing_code as pcode,
		dts.ctrl_no as ctrlNo,dts.mem_msg_txt as memMsgTxt,dts.is_document_present as docInd,dts.doc_file_path as docFilePath,dts.reason_code as reasonCode,
		dts.unique_filename as uniqueFileName,dts.processing_status as processingStatus,dts.internal_track_no as internalTrackNo,dts.rgcs_received_date as rGCSReceivedDate,
		dts.card_acceptor_address  cardAcptAddress,dts.card_acceptor_additional_address as cardAcptAdnlAddress,dts.additional_amount as amtAdd,dts.amt_card_billing as amtCardBilling,
		dts.card_bill_cur as cardBillCur,dts.processing_fee_acq as processingFeeAcq,dts.processing_fee_gst_acq as processingFeeGstAcq,dts.processing_fee_iss as processingFeeIss,
		dts.processing_fee_gst_iss as processingFeeGstIss,dts.assessment_fee_acq as assessmentFeeAcq,dts.assessment_fee_gst_acq as assessmentFeeGstAcq,
		dts.assessment_fee_iss as assessmentFeeIss,dts.assessment_fee_gst_iss as assessmentFeeGstIss,dts.intchg_fee_acq as intchgFeeAcq,dts.intchg_fee_gst_acq as intchgFeeGstAcq,
		dts.intchg_fee_iss as intchgFeeIss,dts.intchg_fee_gst_iss as intchgFeeGstIss,dts.auth_fee_acq as authFeeAcq,dts.auth_fee_gst_acq as authFeeGstAcq,
		dts.auth_fee_iss as authFeeIss,dts.auth_fee_gst_iss as authFeeGstIss,dts.entity_type as entityType,dts.user_id as makerId,dts.checker_id as checkerId,
		dts.deadline_date as deadlineDate,dts.status as status,dts.checker_comments as checkerComments,dts.to_state as toState,dts.tran_cur as tranCur,dts.card_hldr_uid as cardHldrUID,
		dts.card_hldr_income_tax_pan as cardHldrInTaxPan,dts.card_acceptor_postal_code as cardAcptPstCode,dts.merch_tel_no as merchTelNo, dts.merchant_cat_ind  as merchantCatInd
		FROM 
		dispute_txn_settlement_staging dts 
		where 
		dts.org_txn_id = #{txnId} 
		and 
		dts.org_transaction_date = #{orgTranDate} 
		and 
		status in ('P','PR','RPA');
	</select>
	
	<select id="getNetFundTxnDetails" resultType="TxnSettlementDTO">
		select 
		txn_id as txnId,org_txn_id as orgTxnId,mti as mti,func_code as funcCode, pan_prefix as panPrefix,pan_suffix as panSuffix,encrypted_token_pan as encryptedtokenPan,
		token_pan_prefix as tokenPanPrefix,token_pan_suffix as tokenPanSuffix,acq_ref_data as acqRefData,transaction_amount as amountTran,transaction_ts as tstampLocal,
		org_transaction_date as orgDateCapture,tran_cur as tranCur,net_recon_date as netReconDate,acquirer_participant_id as participantIdAcq,issuer_participant_id as participantIdIss,
		rrn as rrn,initiated_from as acqPlatProdId,date_rcvd as dateRcvd,filing_dispute_fee_acq as filingDispFee
		FROM 
		net_fund_txn s 
		WHERE
		(
		<if test="funcCode!= null and funcCode!='' and tokenPan == '' and rrn == '' ">s.func_code=#{funcCode} </if>
		<if test="tokenPan!= null and tokenPan!='' and funcCode == ''  and rrn == '' ">(s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) </if>
		<if test="rrn!=null and rrn!='' and funcCode == ''  and tokenPan == '' ">s.rrn=#{rrn} </if>
		<if test="rrn!=null and rrn!='' and tokenPan!=null and tokenPan!='' and funcCode == '' ">s.rrn=#{rrn} and (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) </if>
		<if test="rrn!=null and rrn!='' and funcCode!=null and funcCode!='' and tokenPan == '' ">s.rrn=#{rrn} and s.func_code=#{funcCode} </if>
		<if test="tokenPan!=null and tokenPan!='' and funcCode!=null and funcCode!='' and rrn == '' ">s.func_code=#{funcCode} AND (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) </if>
		<if test="tokenPan!='' and funcCode!='' and rrn!='' ">s.func_code=#{funcCode} AND (dts.encrypted_pan=#{tokenPan} or dts.encrypted_token_pan = #{tokenPan}) AND s.rrn=#{rrn} </if>
		and ((s.issuer_participant_id=#{partId}) or (s.acquirer_participant_id= #{partId})))
		and (s.date_rcvd BETWEEN  #{fromDate} AND #{toDate});
	</select>
	
	<select id="getPendingDisputeNpciFundCashRevInfo" resultType="TxnSettlementDTO">
		SELECT 
		dts.txn_id as txnId,dts.func_code as funcCode,dts.record_number as recordNumber,dts.mti as mti,dts.pan_prefix as panPrefix,dts.pan_suffix as panSuffix,
		dts.encrypted_token_pan as encryptedtokenPan,dts.token_pan_prefix as tokenPanPrefix,dts.token_pan_suffix as tokenPanSuffix,dts.transaction_amount as amtTran,
		dts.date_time_local as dateTimeLocal,dts.rrn as rrn,dts.acq_ref_data as acqRefData,dts.card_acceptor_terminal_id as cardAcptTermId,dts.txn_originator_inst_id as txnOrgInstId,
		dts.transaction_ts as tstampLocal,dts.acquirer_id as instIdAcq,dts.txn_destination_inst_id as txnDestInstId,dts.net_recon_date as netReconDate,dts.stlmnt_amt as settlementAmount,
		dts.amt_npci_convert_billing_conv_rate as amtNpciConvertBillingConvRate,dts.partial_indicator as partialInd,dts.case_number as caseNo,dts.processing_code as pcode,
		dts.ctrl_no as ctrlNo,dts.mem_msg_txt as memMsgTxt,dts.is_document_present as docInd,dts.doc_file_path as docFilePath,dts.reason_code as reasonCode,
		dts.unique_filename as uniqueFileName,dts.processing_status as processingStatus,dts.internal_track_no as internalTrackNo,dts.rgcs_received_date as rGCSReceivedDate,
		dts.card_acceptor_address  cardAcptAddress,dts.card_acceptor_additional_address as cardAcptAdnlAddress,dts.additional_amount as amtAdd,dts.amt_card_billing as amtCardBilling,
		dts.card_bill_cur as cardBillCur,dts.processing_fee_acq as processingFeeAcq,dts.processing_fee_gst_acq as processingFeeGstAcq,dts.processing_fee_iss as processingFeeIss,
		dts.processing_fee_gst_iss as processingFeeGstIss,dts.assessment_fee_acq as assessmentFeeAcq,dts.assessment_fee_gst_acq as assessmentFeeGstAcq,
		dts.assessment_fee_iss as assessmentFeeIss,dts.assessment_fee_gst_iss as assessmentFeeGstIss,dts.intchg_fee_acq as intchgFeeAcq,dts.intchg_fee_gst_acq as intchgFeeGstAcq,
		dts.intchg_fee_iss as intchgFeeIss,dts.intchg_fee_gst_iss as intchgFeeGstIss,dts.auth_fee_acq as authFeeAcq,dts.auth_fee_gst_acq as authFeeGstAcq,
		dts.auth_fee_iss as authFeeIss,dts.auth_fee_gst_iss as authFeeGstIss,dts.entity_type as entityType,dts.user_id as makerId,dts.checker_id as checkerId,
		dts.deadline_date as deadlineDate,dts.status as status,dts.checker_comments as checkerComments,dts.to_state as toState,dts.tran_cur as tranCur,dts.card_hldr_uid as cardHldrUID,
		dts.card_hldr_income_tax_pan as cardHldrInTaxPan,dts.card_acceptor_postal_code as cardAcptPstCode,dts.merch_tel_no as merchTelNo,
		dts.acquirer_participant_id as participantIdAcq,dts.issuer_participant_id as participantIdIss,dts.fee_type_code as feeTypCode, dts.transaction_amount as filingDisputeFeeAcq
		FROM 
		dispute_txn_settlement_staging dts 
		where 
		dts.org_txn_id = #{txnId} 
		and
		status IN ('P','IP','FAILED','PR','RPA');
	</select>
	
	<select id="getPendingDisputeTxnList" resultType="TxnSettlementDTO">
		SELECT 
		dts.txn_id as txnId,dts.func_code as funcCode,dts.record_number as recordNumber,dts.mti as mti,dts.pan_prefix as panPrefix,dts.pan_suffix as panSuffix,
		dts.encrypted_token_pan as encryptedtokenPan,dts.token_pan_prefix as tokenPanPrefix,dts.token_pan_suffix as tokenPanSuffix,dts.transaction_amount as amtTran,
		dts.date_time_local as dateTimeLocal,dts.rrn as rrn,dts.acq_ref_data as acqRefData,dts.card_acceptor_terminal_id as cardAcptTermId,dts.txn_originator_inst_id as txnOrgInstId,
		dts.transaction_ts as tstampLocal,dts.acquirer_id as instIdAcq,dts.txn_destination_inst_id as txnDestInstId,dts.net_recon_date as netReconDate,dts.stlmnt_amt as settlementAmount,
		dts.amt_npci_convert_billing_conv_rate as amtNpciConvertBillingConvRate,dts.partial_indicator as partialInd,dts.case_number as caseNo,dts.processing_code as pcode,
		dts.ctrl_no as ctrlNo,dts.mem_msg_txt as memMsgTxt,dts.is_document_present as docInd,dts.doc_file_path as docFilePath,dts.reason_code as reasonCode,
		dts.unique_filename as uniqueFileName,dts.processing_status as processingStatus,dts.internal_track_no as internalTrackNo,dts.rgcs_received_date as rGCSReceivedDate,
		dts.card_acceptor_address as cardAcptAddress,dts.card_acceptor_additional_address as cardAcptAdnlAddress,dts.additional_amount as amtAdd,dts.amt_card_billing as amtCardBilling,
		dts.card_bill_cur as cardBillCur,dts.processing_fee_acq as processingFeeAcq,dts.processing_fee_gst_acq as processingFeeGstAcq,dts.processing_fee_iss as processingFeeIss,
		dts.processing_fee_gst_iss as processingFeeGstIss,dts.assessment_fee_acq as assessmentFeeAcq,dts.assessment_fee_gst_acq as assessmentFeeGstAcq,
		dts.assessment_fee_iss as assessmentFeeIss,dts.assessment_fee_gst_iss as assessmentFeeGstIss,dts.intchg_fee_acq as intchgFeeAcq,dts.intchg_fee_gst_acq as intchgFeeGstAcq,
		dts.intchg_fee_iss as intchgFeeIss,dts.intchg_fee_gst_iss as intchgFeeGstIss,dts.auth_fee_acq as authFeeAcq,dts.auth_fee_gst_acq as authFeeGstAcq,dts.auth_fee_iss as authFeeIss,
		dts.auth_fee_gst_iss as authFeeGstIss,dts.entity_type as entityType,dts.merchant_cat_ind as merchantCatInd,dts.user_id as makerId,dts.checker_id as checkerId,
		dts.deadline_date as deadlineDate,dts.status as status,dts.checker_comments as checkerComments,dts.to_state as toState,dts.tran_cur as tranCur,dts.card_hldr_uid as cardHldrUID,
		dts.card_hldr_income_tax_pan as cardHldrInTaxPan,dts.card_acceptor_postal_code as cardAcptPstCode,dts.merch_tel_no as merchTelNo
		FROM 
		dispute_txn_settlement_staging dts 
		where 
		dts.org_txn_id = #{txnId} 
		and 
		dts.org_transaction_date = #{orgTranDate} 
		and 	
		status IN ('P','RPA','PR')
	</select>
	
	<select id="getPendingDisputesInfoList" resultType="TxnSettlementDTO">
		select 
		encrypted_pan as encryptedPan,txn_id as txnId,rrn as rrn,func_code as funcCode,org_func_code as orgFuncCode,mti as mti,record_number as recordNumber,transaction_amount as amountTran,
		date_time_local as dateTimeLocal,acq_ref_data as acqRefData,card_acceptor_terminal_id as cardAcptTermId,txn_originator_inst_id as txnOrgInstId,acquirer_id as instIdAcq,
		issuer_bin as instIdIss,processing_code as pcode,user_id as makerId,transaction_ts as tstampLocal,rgcs_received_date as rGCSReceivedDate,tran_cur as ccOfTransaction,
		additional_amount as amtAdd,entity_type as entityType,acquirer_participant_id as participantIdAcq,issuer_participant_id as participantIdIss,stage_id as stageId,
		date_local as dateLocal,partial_indicator as partialInd,to_state as toState,reason_code as reasonCode, mem_msg_txt as memMsgTxt,is_document_present as docInd,
		doc_file_path as docFilePath,internal_track_no as internalTrackNo,case_number as caseNo,response_code as respCode,fee_type_code as feeTypCode,ctrl_no as ctrlNo,
		merchant_cat_ind  as merchantCatInd 
		from  
		dispute_txn_settlement_staging 
		where 
		stage_id in <foreach item='id' index='index' collection='stageIdList' open='(' separator=',' close=')'>#{id}</foreach> 
	</select>
	
	<update id="updateDisputeStgByStageId">
		UPDATE 
		dispute_txn_settlement_staging 
		SET 
		status = #{status}, 
		checker_comments =  #{checkerComments} , 
		checker_id = #{checkerId}
		WHERE 
		stage_id = #{stageId} 
	</update>
	
	<select id="getDisputesApproveRejectStatus" resultType="String">
		select 
		status 
		from 
		dispute_txn_settlement_staging 
		where stage_id IN 
		<foreach item='id' index='index' collection = 'stageIdList' open='(' separator=',' close=')'>#{id}</foreach> 
	</select>
	
	<select id="getDisputeTranDetailsStage" resultType="TxnSettlementDTO">
		select s.stage_id as stageId,s.pan_prefix as panPrefix,s.pan_suffix as panSuffix,s.acq_ref_data as acqRefData,s.product_code as productCode,s.transaction_ts as tstampLocal,
		s.rrn as rrn,s.tran_cur as tranCur,s.transaction_amount as amountTran,s.additional_amount as amtAdd,s.approval_code as approvalCode,s.pos_entry_mode as posEntryMode,
		s.card_acceptor_terminal_id as cardAcptTermId,s.card_acceptor_id as cardAcptId,s.card_acceptor_additional_address as cardAcptAdnlAddress,s.reason_code as reasonCode,
		s.txn_id as txnId,s.func_code as funcCode,s.processing_code as pcode,s.mcc as mcc,s.card_acceptor_address as cardAcptAddress,s.card_acceptor_city as cardAcptCity,
		s.card_acceptor_region as cardAcptRegion,s.card_acceptor_country as cardAcptCountry,s.card_type as cardType,s.card_brand_id as cardBrandId,s.mti as mti,
		s.original_tablename as originalTableName,s.rgcs_received_date as rGCSReceivedDate,s.entity_type as entityType,s.to_state as toState,s.case_number as caseNo,
		s.org_func_code as orgFuncCode,s.status as status,s.org_txn_id as orgTxnId,s.processing_status as 	processingStatus
		FROM dispute_txn_settlement_staging s 
		where 
		(
		<if test="funcCode!= null and funcCode!='' and tokenPan == '' and rrn == '' ">s.func_code=#{funcCode} AND </if>  
		<if test="tokenPan!= null and tokenPan!='' and rrn == '' and funcCode == '' "> (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) AND </if> 
		<if test="rrn!= null and rrn!='' and tokenPan == '' and funcCode == '' ">  s.rrn=#{rrn} AND </if> 
		<if test="tokenPan!='' and funcCode!='' and rrn!='' ">s.func_code=#{funcCode} AND s.rrn=#{rrn} AND (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) AND </if> 
		<if test="tokenPan != '' and rrn != '' and funcCode == '' ">s.rrn=#{rrn} AND (s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) AND </if> 
		<if test="funcCode !=''  and rrn !='' and tokenPan == '' ">s.func_code=#{funcCode} AND s.rrn=#{rrn}  AND </if> 
		<if test="funcCode !='' and tokenPan !='' and rrn =='' ">(s.encrypted_pan=#{tokenPan} or s.encrypted_token_pan = #{tokenPan}) AND s.func_code=#{funcCode} AND </if> 
		((entity_type='N' and s.status in ('P','IP','FAILED','SUCCESS')) or s.status in ('P','RPA')) and (s.date_local BETWEEN  #{fromDate} AND #{toDate}) and s.status=#{status});
	</select>
	
	<insert id="insertDocPath">
		insert into dispute_document_info ( 
		txn_id,
		txn_originator_inst_id,
		upload_date,document_path,
		document_extension,
		doc_type,
		user_id)
		values 
		<foreach collection='docfilePathList' item='model' index='index' open='(' separator='),(' close=')'>
			#{model.txnId},
			#{model.txnOriginatorInstId},
			#{model.uploadDate,jdbcType=DATE},
			#{model.documentPath},
			#{model.documentExtension},
			#{model.documentType},
			#{model.userId}
		</foreach>;
	</insert>
	
	<insert id="insertDocPathStg">
		insert into dispute_document_info_staging (
		txn_id,
		txn_originator_inst_id,
		upload_date,
		document_path,
		document_extension,
		doc_type,
		user_id)
		values 
		<foreach collection='docfilePathList' item='model' index='index' open='(' separator='),(' close=')'>
			#{model.txnId},
			#{model.txnOriginatorInstId},
			#{model.uploadDate,jdbcType=DATE},
			#{model.documentPath},
			#{model.documentExtension},
			#{model.documentType},
			#{model.userId}
		</foreach>
	</insert>
	
	<update id="updateDisputeDocFilePath">
		UPDATE dispute_txn_settlement 
		set 
		doc_file_path = 'Y' 
		where 
		txn_id=#{txnId};
	</update>
	
	<update id="updateDisputeDocFilePathStg">
		UPDATE dispute_txn_settlement_staging 
		set 
		doc_file_path = 'Y' 
		where 
		txn_id=#{txnId};
	</update>
	
	<select id="getDisputeTxnDetailByCRN" resultType="TxnSettlementDTO">
		select 
		ds.pan_prefix as panPrefix,ds.pan_suffix as panSuffix,ds.encrypted_token_pan as encryptedtokenPan,ds.token_pan_prefix as tokenPanPrefix,ds.token_pan_suffix as tokenPanSuffix,
		ds.product_code as productCode,ds.transaction_ts as tstampLocal,ds.rrn as rrn,ds.tran_cur as tranCur,ds.transaction_amount as amountTran,ds.additional_amount as amtAdd,
		ds.approval_code as approvalCode,ds.pos_entry_mode as posEntryMode,ds.card_acceptor_terminal_id as cardAcptTermId,ds.card_acceptor_id as cardAcptId,
		ds.card_acceptor_address as cardAcptAddress,ds.card_acceptor_additional_address as cardAcptAdnlAddress,ds.response_code as respCode,ds.txn_id as txnId,
		ds.processing_code as pcode,ds.mcc as mcc,ds.card_acceptor_city as cardAcptCity,ds.card_acceptor_region as cardAcptRegion,ds.card_acceptor_country as cardAcptCountry,
		ds.card_type as cardType,ds.case_number as caseNo,ds.card_brand_id as cardBrandId,ds.func_code as funcCode,ds.mti as mti,ds.original_tablename as originalTableName,
		ds.acq_ref_data as acqRefData,ds.org_transaction_date as orgDateCapture,ds.org_txn_id as orgTxnId,ds.entity_type as entityType,ds.to_state as toState,
		ds.rgcs_received_date as rGCSReceivedDate,ds.date_rcvd as dateRcvd,ds.net_recon_date as netReconDate,ds.initiated_from as acqPlatProdId,
		ds.acquirer_participant_id as participantIdAcq,ds.issuer_participant_id as participantIdIss,ds.org_transaction_ts as orgTstampLocal,ds.acq_recon_cur as acqReconCur,ds.iss_recon_cur as issReconCur,ds.org_amt_tran as orgTranAmtInternational
		from
		dispute_txn_settlement ds
		where 
		ds.complaint_number = #{complaintNumber} 
		and
		(ds.date_rcvd BETWEEN  #{fromDate} AND #{toDate});
	</select>
	
	<insert id="saveTxnActionDetail">
		insert into dispute_txn_settlement_staging 
		(txn_id,
		func_code,
		record_number,
		mti,
		pan_prefix,
		pan_suffix,
		processing_code,
		transaction_amount,
		date_time_local,
		date_rcvd,
		transaction_ts,
		rgcs_received_date,
		acq_inst_country,
		pos_entry_mode,
		pos_condition_code,
		rrn,
		card_acceptor_terminal_id,
		card_acceptor_id,
		card_acceptor_address,
		card_acceptor_additional_address,
		card_acceptor_city,
		card_acceptor_region,
		card_acceptor_country,
		product_code,
		merchant_cat_ind,
		tran_cur,
		additional_amount_curr,
		token_pan_prefix,
		token_pan_suffix,
		to_state,
		acq_ref_data,
		case_number,
		ctrl_no,
		internal_track_no,
		org_txn_id,
		user_id,
		original_tablename,
		status,
		txn_originator_inst_id,
		txn_destination_inst_id,
		entity_type,
		acquirer_id,
		issuer_bin,
		stage_id,
		date_local,
		partial_indicator,
		reason_code,
		is_document_present,
		mem_msg_txt,
		acquirer_participant_id,
		issuer_participant_id,
		org_func_code,
		acquirer_settlement_bin,
		issuer_settlement_bin,
		doc_file_path,
		card_bill_cur,
		response_code,
		additional_amount,
		org_transaction_date,
		card_hldr_uid,
		card_hldr_income_tax_pan,
		card_acceptor_postal_code,
		merch_tel_no,
		encrypted_pan,
		encrypted_token_pan,
		stlmnt_amt,
		acquirer_subnet,
		issuer_subnet)
		values(
		#{model.txnId},
		#{model.funcCode},
		#{model.recordNumber},
		#{model.mti},
		#{model.panPrefix},
		#{model.panSuffix},
		#{model.pcode},
		#{model.amtTran},
		#{model.dateTimeLocal},
		#{model.dateTimeLocal},
		#{model.tstampLocal},
		#{model.rGCSReceivedDate},
		#{model.acqInstCountry},
		#{model.posEntryMode},
		#{model.posCondCode},
		#{model.rrn},
		#{model.cardAcptTermId},
		#{model.cardAcptId},
		#{model.cardAcptAddress},
		#{model.cardAcptAdnlAddress},
		#{model.cardAcptCity},
		#{model.cardAcptRegion},
		#{model.cardAcptCountry},
		#{model.productCode},
		#{model.merchantCatInd},
		#{model.tranCur},
		#{model.amtAddCur},
		#{model.tokenPanPrefix},
		#{model.tokenPanSuffix},
		#{model.toState},
		#{model.acqRefData},
		#{model.caseNo},
		#{model.ctrlNo},
		#{model.internalTrackNo},
		#{model.orgTxnId},
		#{model.makerId},
		#{model.originalTableName},
		#{model.status},
		#{model.txnOrgInstId},
		#{model.txnDestInstId},
		#{model.entityType},
		#{model.instIdAcq},
		#{model.instIdIss},
		#{model.stageId},
		#{model.dateLocal},
		#{model.partialInd},
		#{model.reasonCode},
		#{model.docInd},
		#{model.memMsgTxt},
		#{model.participantIdAcq},
		#{model.participantIdIss},
		#{model.orgFuncCode},
		#{model.procIdAcq},
		#{model.procIdIss},
		#{model.docFilePath},
		#{model.cardBillCur},
		#{model.respCode},
		#{model.amtAdd},
		#{model.orgDateCapture},
		#{model.cardHldrUID},
		#{model.cardHldrInTaxPan},
		#{model.cardAcptPstCode},
		#{model.merchTelNo},
		#{model.encryptedPan},
		#{model.encryptedtokenPan},
		#{model.orgStlmntAmt},
		#{model.netIdAcq},
		#{model.netIdIss});
	</insert>
	
	<update id="updateDisputeStgInfoByTxnId">
		UPDATE dispute_txn_settlement_staging
		SET 
		status = #{status}, 
		checker_comments =  #{checkerComments}, 
		checker_id = #{checkerId}
		WHERE 
		txn_id=#{txnId} 
		and 
		func_code = #{funcCode} 
		and 
		stage_id = #{stageId};
	</update>
	
	<!-- admin portal  -->
	<select id="getParticipantIdList" resultType="org.npci.settlenxt.portal.common.dto.ParticipantDTO">
		SELECT 
		participant_id as participantId,
		concat(participant_id, ' - ',bank_name) as bankName 
		FROM 
		participant 
		order by participant_id;
	</select>
	
	<select id="getFeeNameList" resultType="org.npci.settlenxt.adminportal.dto.FeeDTO">
		select 
		fee_description as feeDesc, 
		fee_code as feeCode
		from 
		fee_rate 
		where 
		status = 'A' 
		and 
		fee_type_code='7777';
	</select>
	
	<select id="getSettlementBinList" resultType="org.npci.settlenxt.portal.common.dto.SettlementBinDTO">
		select 
		participant_id as participantId, 
		settlement_bin_no as settlementBinNumber
		from 
		participant_settlementbin 
		where 
		is_default = 'Y';
	</select>
	
	<select id="getAcquirerIdList" resultType="org.npci.settlenxt.portal.common.dto.BinDTO">
		select 
		participant_id as participantId,
		acquirer_id as acquirerId
		from 
		membin_details 
		where 
		bin_type = 'A';
	</select>
	
	<select id="getIssuerIdList" resultType="org.npci.settlenxt.portal.common.dto.BinDTO">
		select 
		participant_id as participantId,
		bin_number as binNumber
		from 
		membin_details 
		where 
		bin_type = 'I';
	</select>
	
	<select id="getMtiFuncCode" resultType="org.npci.settlenxt.portal.common.dto.ActionCodeDTO">
		select 
		mti as mti,
		function_code as functionCode
		from 
		action_code 
		where 
		action_code = #{actionCode};
	</select>
	
	<select id="getSettlementBinAcq" resultType="String">
		select 
		settlement_bin 
		from 
		membin_details 
		where 
		participant_id = #{participantId} 
		and 
		bin_type = #{entityType} 
		and 
		acquirer_id = #{instId} 
		and 
		status = 'A' 
		limit 1;
	</select>
	
	<select id="getSettlementBinIss" resultType="String">
		select 
		settlement_bin 
		from 
		membin_details 
		where 
		participant_id = #{participantId} 
		and 
		bin_type = #{entityType} 
		and 
		bin_number = #{instId} 
		and 
		status = 'A' 
		limit 1;
	</select>
	
	<insert id="saveFundCollectDisburse">
		insert into dispute_txn_settlement_staging 
		(txn_id,
		func_code,
		mti,
		pan_prefix,
		pan_suffix,
		transaction_amount,
		transaction_ts,
		rgcs_received_date,
		acq_inst_country,
		tran_cur,
		to_state,
		org_txn_id,
		user_id,
		original_tablename,
		status,
		txn_originator_inst_id,
		txn_destination_inst_id,
		entity_type,
		acquirer_id,
		issuer_bin,
		stage_id,
		date_local,
		mem_msg_txt,
		acquirer_participant_id,
		issuer_participant_id,
		org_func_code,
		acquirer_settlement_bin,
		issuer_settlement_bin,
		fee_type_code,
		processing_code,
		encrypted_pan,
		date_time_local) 
		values(
		#{model.txnId},
		#{model.funcCode},
		#{model.mti},
		#{model.panPrefix},
		#{model.panSuffix},
		#{model.amtTran},
		#{model.tstampLocal},
		#{model.rGCSReceivedDate},
		#{model.feeCcy},
		#{model.feeCcy},
		#{model.toState},
		#{model.txnId},
		#{model.makerId},
		#{model.originalTableName},
		#{model.status},
		#{model.txnOrgInstId},
		#{model.txnDestInstId},
		#{model.entityType},
		#{model.instIdAcq},
		#{model.instIdIss},
		#{model.stageId},
		#{model.dateLocal},
		#{model.memMsgTxt},
		#{model.participantIdAcq},
		#{model.participantIdIss},
		#{model.funcCode},
		#{model.procIdAcq},
		#{model.procIdIss},
		#{model.feeTypCode},
		#{model.pcode},
		#{model.encryptedPan},
		#{model.dateTimeLocal});
	</insert>
	
	<select id="getTransactionIccByTxnId" resultType="TxnIccDTO">
	select txn_id
	as txnId,amount_other as amountOther,appl_cryptogram as
	applCryptogram,appl_intrchg_prof as applIntrchgProf,appl_tran_counter
	as applTranCounter,appl_version_no as applVersionNo,appl_id as
	applId,cardh_ver_result as cardhVerResult,crypt_info_data as
	cryptInfoData,cryptogram_amount as cryptogramAmount,dedicated_file_nam
	as dedicatedFileNam,term_capabilities as
	termCapabilities,term_country_code as termCountryCode,term_serial_no
	as termSerialNo,term_verify_result as termVerifyResult,terminal_type
	as terminalType,tran_category_code as tranCategoryCode,tran_type as
	tranType,unpredictable_no as unpredictableNo,derivation_key_id as
	derivationKeyId,arpc_cryptogram as arpcCryptogram,arpc_respcode as
	arpcRespcode,iss_appl_data as issApplData,iss_auth_data as
	issAuthData,iss_discr_data as issDiscrData,iss_script_result as
	issScriptResult,iss_script1_data as issScript1Data,iss_script2_data as
	issScript2Data,tran_currency_code as tranCurrencyCode,tran_seq_counter
	as tranSeqCounter,tran_date as tranDate FROM ${txnIccTable} WHERE
	txn_id=#{txnId}
</select>

	<select id="getIntrntlNetFundTxnDetails"
		resultType="TxnSettlementDTO">
		select
		txn_id as txnId,org_txn_id as orgTxnId,mti as mti,func_code as
		funcCode,
		pan_prefix as panPrefix,pan_suffix as
		panSuffix,encrypted_token_pan as
		encryptedtokenPan,
		token_pan_prefix as
		tokenPanPrefix,token_pan_suffix
		as tokenPanSuffix,acq_ref_data as
		acqRefData,transaction_amount as
		amountTran,transaction_ts as
		tstampLocal,
		org_transaction_date as
		orgDateCapture,tran_cur as
		tranCur,net_recon_date as
		netReconDate,acquirer_participant_id as
		participantIdAcq,issuer_participant_id as participantIdIss,
		rrn as
		rrn,initiated_from as acqPlatProdId,date_rcvd as
		dateRcvd,filing_dispute_fee_acq as filingDispFee
		FROM
		net_fund_txn ds
		WHERE 1=1
		<if test="txnSettlementDTO.funcCode!= null and txnSettlementDTO.funcCode!='' "> and ds.func_code=#{txnSettlementDTO.funcCode} </if>
		<if test="txnSettlementDTO.tokenPan!= null and txnSettlementDTO.tokenPan!='' "> and (ds.encrypted_pan=#{txnSettlementDTO.tokenPan} or
			ds.encrypted_token_pan = #{txnSettlementDTO.tokenPan})
		</if>
		<if test="rrn!= null and rrn!=''"> and ds.rrn ilike #{rrn}</if>
		<if test="txnSettlementDTO.schemeCodeBank!= null and txnSettlementDTO.schemeCodeBank!=''"> and (ds.issuer_participant_id=#{txnSettlementDTO.schemeCodeBank} or
			ds.acquirer_participant_id = #{txnSettlementDTO.schemeCodeBank}) </if>
		AND ((ds.issuer_participant_id=#{txnSettlementDTO.partId}) OR
		(ds.acquirer_participant_id= #{txnSettlementDTO.partId}))
		and
		(ds.date_rcvd BETWEEN
		#{txnSettlementDTO.fromDate} AND #{txnSettlementDTO.toDate});
	</select>


	<select id="getIntrntlDisputeTransactionDetails"
		resultType="TxnSettlementDTO">
		select
		ds.pan_prefix as panPrefix,ds.pan_suffix as
		panSuffix,ds.encrypted_token_pan as
		encryptedtokenPan,ds.token_pan_prefix as
		tokenPanPrefix,ds.token_pan_suffix as tokenPanSuffix,
		ds.product_code
		as productCode,ds.transaction_ts as tstampLocal,ds.rrn as
		rrn,ds.tran_cur as tranCur,ds.transaction_amount as
		amountTran,ds.additional_amount as amtAdd,
		ds.approval_code as
		approvalCode,ds.pos_entry_mode as
		posEntryMode,ds.card_acceptor_terminal_id as
		cardAcptTermId,ds.card_acceptor_id as cardAcptId,
		ds.card_acceptor_address as
		cardAcptAddress,ds.card_acceptor_additional_address as
		cardAcptAdnlAddress,ds.response_code as respCode,ds.txn_id as txnId,
		ds.processing_code as pcode,ds.mcc as mcc,ds.card_acceptor_city as
		cardAcptCity,ds.card_acceptor_region as
		cardAcptRegion,ds.card_acceptor_country as cardAcptCountry,
		ds.card_type as cardType,ds.case_number as caseNo,ds.card_brand_id as
		cardBrandId,ds.func_code as funcCode,ds.mti as
		mti,ds.original_tablename as originalTableName,
		ds.acq_ref_data as
		acqRefData, ds.org_transaction_date as orgDateCapture,ds.org_txn_id as
		orgTxnId,ds.entity_type as entityType,ds.to_state as toState,
		ds.rgcs_received_date as rGCSReceivedDate,ds.date_rcvd as
		dateRcvd,ds.net_recon_date as netReconDate,ds.initiated_from as
		acqPlatProdId,
		ds.acquirer_participant_id as
		participantIdAcq,ds.issuer_participant_id as
		participantIdIss,ds.org_transaction_ts as orgTstampLocal,ds.acq_recon_cur as acqReconCur,ds.iss_recon_cur as issReconCur,ds.org_amt_tran as orgTranAmtInternational
		from
		dispute_txn_settlement ds
		WHERE 1=1 
		<if test="txnSettlementDTO.funcCode!= null and txnSettlementDTO.funcCode!='' "> and ds.func_code=#{txnSettlementDTO.funcCode} </if>
		<if test="txnSettlementDTO.tokenPan!= null and txnSettlementDTO.tokenPan!='' "> and (ds.encrypted_pan=#{txnSettlementDTO.tokenPan} or
			ds.encrypted_token_pan = #{txnSettlementDTO.tokenPan})
		</if>
		<if test="rrn!= null and rrn!=''"> and ds.rrn ilike #{rrn} </if>
		<if test="txnSettlementDTO.schemeCodeBank!= null and txnSettlementDTO.schemeCodeBank!=''"> and (ds.issuer_participant_id=#{txnSettlementDTO.schemeCodeBank} or
			ds.acquirer_participant_id = #{txnSettlementDTO.schemeCodeBank}) </if>
	AND ((ds.issuer_participant_id=#{txnSettlementDTO.partId}) OR
		(ds.acquirer_participant_id= #{txnSettlementDTO.partId}))
		and
		(ds.date_rcvd BETWEEN
		#{txnSettlementDTO.fromDate} AND #{txnSettlementDTO.toDate});
	</select>



	<select id="getIntrntlTransactionDetails" resultType="TxnSettlementDTO">
		select
		pan_prefix as panPrefix,pan_suffix as
		panSuffix,encrypted_token_pan as encryptedtokenPan,token_pan_prefix as
		tokenPanPrefix,
		token_pan_suffix as tokenPanSuffix,product_code as
		productCode,transaction_ts as tstampLocal,rrn as rrn,tran_cur as
		tranCur,
		transaction_amount as amtTran,additional_amount as
		amountTran,approval_code as approvalCode,pos_entry_mode as
		posEntryMode,
		card_acceptor_terminal_id as
		cardAcptTermId,card_acceptor_postal_code as
		cardAcptPstCode,card_acceptor_id as cardAcptId,
		card_acceptor_additional_address as cardAcptAdnlAddress,response_code
		as respCode,txn_id as txnId,online_txn_id as mappedDupData,
		func_code
		as funcCode,processing_code as pcode,transaction_date as
		receivedDate,transaction_date as dateCapture,mcc as
		mcc,card_acceptor_address as cardAcptAddress,
		card_acceptor_city as
		cardAcptCity,card_acceptor_region as
		cardAcptRegion,card_acceptor_country as cardAcptCountry,addl_data48 as
		addlData48,addl_data61 as addlData61,card_type as
		cardType,card_brand_id as cardBrandId,mti as mti,presentment_indicator
		as presentment,
		date_rcvd as dateRcvd, net_recon_date as
		netReconDate,acq_ref_data as acqRefData,initiated_from as
		acqPlatProdId,
		presentment_recvd_date as
		presentmentRecvdDate,acquirer_participant_id as
		participantIdAcq,issuer_participant_id as participantIdIss
		FROM
		${txnSettlementDTO.originalTableName} s
			WHERE 1=1
		<if test="txnSettlementDTO.funcCode!= null and txnSettlementDTO.funcCode!='' "> and s.func_code=#{txnSettlementDTO.funcCode} </if>
		<if test="txnSettlementDTO.tokenPan!= null and txnSettlementDTO.tokenPan!='' "> and (s.encrypted_pan=#{txnSettlementDTO.tokenPan} or
			s.encrypted_token_pan = #{txnSettlementDTO.tokenPan})
		</if>
		<if test="rrn!= null and rrn!=''"> and s.rrn ilike #{rrn} </if>
		<if test="txnSettlementDTO.schemeCodeBank!= null and txnSettlementDTO.schemeCodeBank!=''">and (s.issuer_participant_id=#{txnSettlementDTO.schemeCodeBank} or
			s.acquirer_participant_id = #{txnSettlementDTO.schemeCodeBank}) </if>
		AND
		((s.issuer_participant_id=#{txnSettlementDTO.partId}) OR
		(s.acquirer_participant_id= #{txnSettlementDTO.partId}))
	</select>
	
	<select id="getIntrntlRevTranDetails" resultType="TxnSettlementDTO">
		select
		pan_prefix as panPrefix,pan_suffix as
		panSuffix,encrypted_token_pan as encryptedtokenPan,token_pan_prefix as
		tokenPanPrefix,
		token_pan_suffix as tokenPanSuffix,product_code as
		productCode,transaction_ts as tstampLocal,rrn as rrn,tran_cur as
		tranCur,
		transaction_amount as amtTran,additional_amount as
		amtAdd,approval_code as approvalCode,pos_entry_mode as posEntryMode,
		card_acceptor_terminal_id as cardAcptTermId,card_acceptor_postal_code
		as cardAcptPstCode,card_acceptor_id as cardAcptId,
		card_acceptor_additional_address as cardAcptAdnlAddress,response_code
		as respCode,txn_id as txnId,online_txn_id as mappedDupData,
		func_code
		as funcCode,processing_code as pcode,transaction_date as
		receivedDate,transaction_date as dateCapture,mcc as
		mcc,card_acceptor_address as cardAcptAddress,
		card_acceptor_city as
		cardAcptCity,card_acceptor_region as
		cardAcptRegion,card_acceptor_country as cardAcptCountry,addl_data48 as
		addlData48,addl_data61 as addlData61,
		card_type as
		cardType,card_brand_id as cardBrandId,mti as mti,net_recon_date as
		netReconDate,initiated_from as acqPlatProdId,
		acquirer_participant_id
		as participantIdAcq,issuer_participant_id as
		participantIdIss,org_txn_id as orgTxnId,acq_ref_data as acqRefData
		FROM
		rev_txn_settlement s 
		WHERE 1=1
		<if test="txnSettlementDTO.funcCode!= null and txnSettlementDTO.funcCode!='' "> and s.func_code=#{txnSettlementDTO.funcCode} </if>
		<if test="txnSettlementDTO.tokenPan!= null and txnSettlementDTO.tokenPan!='' "> and (s.encrypted_pan=#{txnSettlementDTO.tokenPan} or
			s.encrypted_token_pan = #{txnSettlementDTO.tokenPan})
		</if>
		<if test="rrn!= null and rrn!=''"> and s.rrn ilike #{rrn}</if>
		<if test="txnSettlementDTO.schemeCodeBank!= null and txnSettlementDTO.schemeCodeBank!=''"> and (s.issuer_participant_id=#{txnSettlementDTO.schemeCodeBank} or
			s.acquirer_participant_id = #{txnSettlementDTO.schemeCodeBank}) </if>
		AND ((s.issuer_participant_id=#{txnSettlementDTO.partId}) OR
		(s.acquirer_participant_id= #{txnSettlementDTO.partId})) and
		(date(s.transaction_date)
		BETWEEN #{txnSettlementDTO.fromDate} AND #{txnSettlementDTO.toDate});
	</select>
<select id="getIntrntlPresentmentDetails" resultType="TxnSettlementDTO">
		select
		pan_prefix as panPrefix,pan_suffix as
		panSuffix,encrypted_token_pan as encryptedtokenPan,token_pan_prefix as
		tokenPanPrefix,
		token_pan_suffix as tokenPanSuffix,product_code as
		productCode,transaction_ts as tstampLocal,rrn as rrn,tran_cur as
		tranCur,
		transaction_amount as amtTran,additional_amount as
		amtAdd,approval_code as approvalCode,pos_entry_mode as posEntryMode,
		card_acceptor_terminal_id as cardAcptTermId,card_acceptor_postal_code
		as cardAcptPstCode,card_acceptor_id as cardAcptId,
		card_acceptor_additional_address as cardAcptAdnlAddress,response_code
		as respCode,txn_id as txnId,online_txn_id as mappedDupData,
		func_code
		as funcCode,processing_code as pcode,transaction_date as
		receivedDate,transaction_date as dateCapture,mcc as
		mcc,card_acceptor_address as cardAcptAddress,
		card_acceptor_city as
		cardAcptCity,card_acceptor_region as
		cardAcptRegion,card_acceptor_country as cardAcptCountry,addl_data48 as
		addlData48,addl_data61 as addlData61,card_type as
		cardType,card_brand_id as cardBrandId,mti as mti,presentment_indicator
		as presentment,
		org_txn_id as orgTxnId,date_rcvd as dateRcvd,
		net_recon_date as netReconDate, acq_ref_data as
		acqRefData,initiated_from as acqPlatProdId,acquirer_participant_id as
		participantIdAcq,issuer_participant_id as
		participantIdIss,org_transaction_ts as orgTstampLocal
		FROM
		${txnSettlementDTO.originalTableName} s
		WHERE 1=1
		<if test="txnSettlementDTO.funcCode!= null and txnSettlementDTO.funcCode!='' "> and s.func_code=#{txnSettlementDTO.funcCode} </if>
		<if test="txnSettlementDTO.tokenPan!= null and txnSettlementDTO.tokenPan!='' "> and (s.encrypted_pan=#{txnSettlementDTO.tokenPan} or
			s.encrypted_token_pan = #{txnSettlementDTO.tokenPan})
		</if>
		<if test="rrn!= null and rrn!=''"> and s.rrn ilike #{rrn}</if>
		<if test="txnSettlementDTO.schemeCodeBank!= null and txnSettlementDTO.schemeCodeBank!=''"> and (s.issuer_participant_id=#{txnSettlementDTO.schemeCodeBank} or
			s.acquirer_participant_id = #{txnSettlementDTO.schemeCodeBank}) </if>
		AND
		((s.issuer_participant_id=#{txnSettlementDTO.partId}) OR
		(s.acquirer_participant_id= #{txnSettlementDTO.partId})) order by transaction_ts
		desc;
	</select>
	<select id="fetchDisputeCountByTxnId" resultType="int">
	select count(1) from dispute_multileg where txn_id =#{txnId}
	</select>
	<select id="fetchStatusOfDisputeCountTxnId" resultType="int">
		select count(1) from dispute_txn_settlement_staging where txn_id
		=#{txnId} and status in
		<foreach item='status' index='index' collection='statusList'
			open='(' separator=',' close=')'>#{status}
		</foreach>

	</select>
</mapper>
