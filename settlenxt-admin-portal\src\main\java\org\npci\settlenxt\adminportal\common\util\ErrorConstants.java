
package org.npci.settlenxt.adminportal.common.util;

import org.npci.settlenxt.portal.common.util.BaseErrorConstants;

public final class ErrorConstants extends BaseErrorConstants {
	private ErrorConstants() {
		super();
	}

	public static final String GENERIC_ERROR_CODE = "E00001";
	public static final String GENERIC_ERROR_MESSAGE = "Internal error occured. Please contact system administator";
	public static final String SETTLEMENT_BIN_EXISTS_CODE = "EM0001";
	public static final String SETTLEMENT_BIN_EXISTS_MESSAGE = "Settlement bin(s) already exists for other participants";
	public static final String ACQUIRER_ID_EXISTS_CODE = "EM0002";
	public static final String ACQUIRER_ID_EXISTS_MESSAGE = "Acquirer Id(s) already exists for other participants";
	public static final String ISSUER_BIN_EXISTS_CODE = "EM0003";
	public static final String ISSUER_BIN_EXISTS_MESSAGE = "Issuer bin(s) already exists for other participants";
	public static final String MEMBER_DOCUMENT_UPLOAD_DIRECTORY_ERROR_CODE = "EM0004";
	public static final String MEMBER_DOCUMENT_UPLOAD_DIRECTORY_ERROR_MESSAGE = "Unable to create the upload directory";
	public static final String MEMBER_DOCUMENT_INVALID_FILETYPE_ERROR_CODE = "EM0005";
	public static final String MEMBER_DOCUMENT_INVALID_FILETYPE_ERROR_MESSAGE = "Invalid file type for the documents ";
	public static final String MEMBER_DOCUMENT_UPLOAD_FAILURE_ERROR_CODE = "EM0006";
	public static final String MEMBER_DOCUMENT_UPLOAD_FAILURE_ERROR_MESSAGE = "Unable to store the document(s) ";
	public static final String MEMBER_DOCUMENT_NOT_FOUND_ERROR_CODE = "EM0007";
	public static final String MEMBER_DOCUMENT_NOT_FOUND_ERROR_MESSAGE = "Requested document is not present in the server ";
	public static final String MEMBER_DOCUMENT_DOWNLOAD_ERROR_CODE = "EM0008";
	public static final String MEMBER_DOCUMENT_DOWNLOAD_ERROR_MESSAGE = "Unable to download the member documents";
}