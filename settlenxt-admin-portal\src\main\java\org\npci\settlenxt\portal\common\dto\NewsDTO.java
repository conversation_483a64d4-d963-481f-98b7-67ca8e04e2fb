package org.npci.settlenxt.portal.common.dto;

import java.util.Date;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JGlobalMap
public class NewsDTO {

	private int newsId;
	private String isType;
	private String title;
	private String subTitle;
	private String type;
	private String title1;
	private String subTitle1;
	private Date fromDate;
	private Date toDate;
	private String fromDateStr;
	private String toDateStr;
	private String summary;
	private String details;
	private String footerData;
	private String publishType;
	private String sendMail;
	private String status;
	private String referenceNumber;
	private String freqFrom;
	private String freqTo;
	private String critical;
	private String weekFrom;
	private String weekTo;
	private String monthFrom;
	private String monthTo;
	private Date createdOn;
	private String createdBy;
	private Date lastUpdatedOn;
	private String lastUpdatedBy;
	private String periodType;
	private String comment;
	private String reqType;
	private String buttonType;
	private String saveNewsResult;
	private String requestState;
	private String remarks;
	private String approvalRemarks;
	private String approvalStatus;
	private String statusCode;
	private String validFrom;
	private String validTo;
	private String bankName;
	private String roles;
	private String users;
	private String lastOperation;
	private String participantId;
	private String userName;
	private String participantName;
	private String editUserList;
	private String editBankList;
	private String editRoleList;
	private String clearId;
	private String checkerComments;
private String screenName;
}
