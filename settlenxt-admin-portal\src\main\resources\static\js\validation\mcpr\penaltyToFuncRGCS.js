$(document).ready(function() {

	disableSave();		  
	$("#vpenaltyForModifyUpload").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#vpenaltyForNonModifyUpload").on('keyup keypress blur change', function () {
        unableSave();
    });
	
});
function disableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = true;
}
}

function unableSave()
{
   	if (typeof bEdit != "undefined") {
			document.getElementById("bEdit").disabled = false;
		}	
		
		
}

function savePenalty(_userID, type) {
   
	var penaltyId = document.getElementById("hpenaltyId").value;
	var data = "";
		

	var isValid = true;

    if (!validateField('vpenaltyForModifyUpload', true, "Decimal", 100, false,0,999999999,true) && isValid) {
        isValid = false;
    }
    if (!validateField('vpenaltyForNonModifyUpload', true, "Decimal", 100, false,0,999999999,true) && isValid) {
        isValid = false;
    }
    if(!isValid)
    {
    	return false;
    }

	penaltyId="1";
	let url = '/asignPenaltyToFunctionAdd';
	 data = "penaltyId," + penaltyId + ",penaltyForModifyUpload," + document.getElementById("vpenaltyForModifyUpload").value + ",penaltyForNonModifyUpload," + document.getElementById("vpenaltyForNonModifyUpload").value  +",type,"+type;
	postData(url, data);
}


function submitForm(url,_userType) {
	var data = "userType," + "P";
	postData(url, data);
}

function urlPostAction(_type, action) {
	var data = "";
	postData(action, data);
}
function homePenalty(_userID, _type) {
	let url = '/showPenaltyConfig';
	var data = "";

	postData(url, data);
}

	function postAction(_action) {
		
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 let penaltyId = $("#hpenaltyId").val();
				 let crtuser = $("#crtuser").val();
				 let remarks=$("#rejectReason").val();
		
				 let url = '/approvePenaltyStatus';
				 let data = "penaltyId," + penaltyId + ",status," + "Approved" + ",crtuser,"
						+ crtuser  + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				reqNameRoleMap();
	
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}

var optionFunctionalityList = new Array();


function reqNameRoleMap() {
	if ('${requestInfo.requestName}' == 'ROLE FUNCTIONALITY MAP') {
		let penaltyId = $("#hpenaltyId").val();
		let remarks = $("#rejectReason").val();
		let crtuser = $("#crtuser").val();

		let url = '/approvePenaltyStatus';

		let data = "penaltyId," + penaltyId + ",status," + "Rejected"
			+ ",crtuser," + crtuser + ",rejectReason," + remarks;

		postData(url, data);
	} else {
		let penaltyId = $("#hpenaltyId").val();
		let crtuser = $("#crtuser").val();
		let remarks = $("#rejectReason").val();
		let url = '/approvePenaltyStatus';

		let data = "penaltyId," + penaltyId + ",status," + "Rejected"
			+ ",crtuser," + crtuser + ",remarks," + remarks;
		postData(url, data);
	}
}

function postDiscardAction(action) {
		var url = action;
		var penaltyId = document.getElementById("hpenaltyId").value;
		var data = "penaltyId," + penaltyId  ;
		postData(url, data);
 }
	

function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && fieldType!="SelectionBox") || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    isValid = handleFieldTypeIntAndAlpha(fieldType, fieldValue, isValid);
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
  	if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
    if(fieldId=="vNewCardCount2")
    {
	    var fieldValue1 = $("#vNewCardCount1").val();
	    if(Number(fieldValue1) > Number(fieldValue))
	    {
        	isValid = false;
    	}
    }
    handleErrorMsg(isValid, fieldId);
    return isValid;
}
function handleErrorMsg(isValid, fieldId) {
	if (isValid) {
		$("#err" + fieldId).hide();
	} else {
		if (rebateValidationMessages[fieldId]) {
			$("#err" + fieldId).find('.error').html(rebateValidationMessages[fieldId]);
		}
		$("#err" + fieldId).show();
	}
}

function handleFieldTypeIntAndAlpha(fieldType, fieldValue, isValid) {
	if (fieldType == "Alphabet") {
		let regEx = /^[a-zA-Z]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphabetWithSpace") {
		let regEx = /^[a-zA-Z ]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphanumericNoSpace") {
		let regEx = /^[A-Za-z0-9]+$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "Integer") {
		let regEx = /^\D*$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "Decimal") {
		let regEx = /^\d+\.?\d*$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

