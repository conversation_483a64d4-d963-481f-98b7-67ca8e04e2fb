package org.npci.settlenxt.adminportal.repository;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;

import org.npci.settlenxt.portal.common.dto.FileUploadDTO;
import org.npci.settlenxt.portal.common.repository.BaseFileUploadRepository;

@Mapper
public interface FileUploadRepository extends BaseFileUploadRepository {

	List<FileUploadDTO> getFilesByStatus(String status, String portal, String site, String instance);

}
