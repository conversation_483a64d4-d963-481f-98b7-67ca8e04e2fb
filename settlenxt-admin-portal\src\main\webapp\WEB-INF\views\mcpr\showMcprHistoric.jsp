<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript"> 
var actionColumnIndex=9;
var firstColumnToBeSkippedInFilterAndSort=false;
<c:if test="${showCheckBox eq 'Y'}">
actionColumnIndex = 12;
firstColumnToBeSkippedInFilterAndSort=true;
</c:if>	
<c:if test="${showCheckBox eq 'N'}">
actionColumnIndex = 11;
firstColumnToBeSkippedInFilterAndSort=false;
</c:if>
</script>

 <script>
	var referenceNoListPendings = [];

<c:if test="${not empty pendingMcprBinDetailsList}">
	<c:forEach items="${pendingMcprBinDetailsList}" var="operator">
		<c:if test="${operator.requestState eq 'P' }">
			referenceNoListPendings.push('${operator.mcprBinDataDetailsId}');
		</c:if>
	</c:forEach>
</c:if>
</script>


<script src="./static/js/validation/mcpr/showMcprHistoric.js"
	type="text/javascript"></script>
	
<script type="text/javascript">
var mcprValidationMessages={};
mcprValidationMessages['month']="<spring:message code='mcprBinDetails.month.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['year']="<spring:message code='mcprBinDetails.year.validation.msg' javaScriptEscape='true' />";
mcprValidationMessages['bankName']="<spring:message code='mcprBinDetails.bankName.validation.msg' javaScriptEscape='true' />";

</script>

<script type="text/javascript" src=	"./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src=	"./static/js/jszip.min.js">
</script>
<script type="text/javascript" src=	"./static/js/buttons.html5.min.js">
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
	 
	.defaultexport {
  visibility: hidden;
}

table.dataTable thead  { vertical-align: top;}
table.dataTable thead .sorting { vertical-align: bottom; background: url('./static/images/sort_both.png') no-repeat center right; }
table.dataTable thead .sorting_asc { vertical-align: top;background: url('./static/images/sort_asc.png') no-repeat center right; }
table.dataTable thead .sorting_desc { vertical-align: top;background: url('./static/images/sort_desc.png') no-repeat center right; }
table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before{ vertical-align: top;content:""}
table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after{ vertical-align: top;content:""}
.search-box  {	
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;	
	background-color: transparent;
    width: 100%;
    border-width:1px;
	border-style:inset;
    }
</style>
<!-- Model -->	
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog" aria-labelledby="toggleModalNews" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Are you sure you want to Approve/Reject these records?</h5>
        <button type="button" class="close" data-dismiss="modal"  aria-label="Close" onclick="deselectAll()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
     <div>
          <label style="color:blue;font-weight:bold;" >MCPR Data Approval/Rejection</label>
          <p id="newsIds"/>
          </div>

     <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="ApproveorRejectBulkMcprBinDetailsEdit('R','All')">Reject</button>
        <button type="button" class="btn btn-primary" onclick="ApproveorRejectBulkMcprBinDetailsEdit('A','All')">Approve</button>
      </div>
    </div>
  </div>
</div>
<!-- Model -->
<!-- Model -->
<input:hidden id="refNum" />
<div class="row">
	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>
<input type="hidden" id="lastOperation" value="${mcprViewEditHistoricDto.lastOperation}" />	
	
<div class="space_block">
<c:if test="${EditMenu eq 'Yes'}">	
	<ul class="nav nav-tabs" role="tablist" id="myTab">
	<c:choose>
		<c:when test="${showMainTab eq 'Yes'}">
			<li role="presentation" class="active" />
		</c:when>
		<c:otherwise>
			<li role="presentation" />
		</c:otherwise>
	</c:choose>
		<a href="#home" onclick="submitForm('/dataViewEditDelete');" role="tab" data-toggle="tab"> 
		<span class="glyphicon glyphicon-credit-card"><spring:message code="mcprBinDetails.listscreen.title" /></span> </a>
	<c:choose>
		<c:when test="${pendingAppMcprBinDetails eq 'Yes'}">
			<li role="presentation" class="active" />
		</c:when>
		<c:otherwise>
			<li role="presentation" />
		</c:otherwise>
	</c:choose>
		<a href="#home" role="tab" onclick="getPendingViewEditMcprDataList();" data-toggle="tab">
		<span class="glyphicon glyphicon-ok"><spring:message code="mcprBinDetails.approvalTab.title" /></span></a>
	</ul>
	<c:if test="${showMainTab eq 'Yes'}">
	<div class="tab-content">
		<div role="tabpanel" class="tab-pane active" id="home">
		 <sec:authorize access="hasAuthority('View MCPR Data')">  
				<div class="row">
				<div class="col-sm-12">
				<div role="tabpanel" class="tab-pane active" id="home">	
			<table class="table table-striped" style="font-size: 12px">
			<caption style="display:none;">MCPR Bin Data</caption>
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr >
					<td></td>
					<td style="width:10%;">
							<label><spring:message code="mcprBinDetails.bankName" /><span style="color: red">*</span></label></td>
						<td style="width:20%;"><form:select path="bankName" id="bankName" name="bankName"
								maxlength="10" value="${bankName}" 
								cssClass="form-control medantory">
								<form:option value="SELECT" label="SELECT" />
									<form:options items="${bankNameList}"  itemValue="code" itemLabel="description"/>
								</form:select>
								
							<div id="errbankName" class="error">
								<span for="bankName" class="error"><form:errors
									path="bankName" /></span>
							</div>
						</td>
					<td></td>
				</tr>
			</tbody>
			</table>
			</div>
		</div>		
	 </div>
				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center">
						<c:if test="${not empty showbutton}">
							<button type="button" class="btn btn-success"
								onclick="viewDataEdit('N','/dataViewEditDeleteSearch');"><spring:message code="mcprBinDetails.searchBtn" /></button>
						</c:if>
							<button type="button" class="btn btn-danger"
							onclick="userAction('N','/dataViewEditDelete');"><spring:message code="mcprBinDetails.backBtn" /></button>
					</div>
				</div>
				  </sec:authorize>  
			</div>
		</div>
		<div class="row">
		<div class="col-sm-12">
				<div class="panel panel-default">
					<div class="panel-heading">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="mcprBinDetails.listscreenList.title" /></span></strong>
					</div>
						<div class="panel-body">
						
						<div class="row">
					<div class="col-sm-12">
						<button class="btn  pull-right btn_align" 
							id="clearFilters"><spring:message code="mcprBinDetails.clearFiltersBtn" /></button>
							&nbsp; <a class="btn btn-success pull-right btn_align" href="#" id="csvExport">
									<spring:message code="mcprBinDetails.csvBtn" /> </a>
								<a class="btn btn-success pull-right btn_align" href="#"
									id="excelExport"  ><spring:message code="mcprBinDetails.exportBtn" /> </a>
					</div>
				</div>
						
						<div class="table-responsive">
							<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
							<caption style="display:none;">MCPR Bin Data</caption>
								<thead>
									<tr>
										<th scope = "col"><label><spring:message code="mcprBinDetails.mcprBinDetailsId" /></label></th>
										<th scope = "col"><label><spring:message code="mcprBinDetails.monthEnding" /></label></th>
										<th scope = "col"><label><spring:message code="mcprBinDetails.binNumber" /></label></th>
										<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactCardCumulativeRpay" /></label></th>
										<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactCardIncremental" /></label></th>
										<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactlessCardCumulativeRpay" /></label></th>
										<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactlessCardIncremental" /></label></th>
										<th scope = "col"><label><spring:message code="mcprBinDetails.virtualCardCumulativeRpay" /></label></th>
										<th scope = "col"><label><spring:message code="mcprBinDetails.virtualCardIncremental" /></label></th>
										<%-- <th scope = "col"><label><spring:message code="mcprBinDetails.actionTitle" /></label></th> --%>
									</tr>
								</thead>
							<tbody>
									<c:if test="${not empty mcprBinDetailsList}">
										<c:forEach var="mcpr" items="${mcprBinDetailsList}">
											<tr>
												<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','E')">${mcpr.mcprBinDataDetailsId}</td>
												<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','E')">${mcpr.monthEnding}</td>
												<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','E')">${mcpr.binNumber}</td>
												<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','E')">${mcpr.phyContactCardCummRuPayCard}</td>
												<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','E')">${mcpr.phyContactCardIncrementalCard}</td>
												<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','E')">${mcpr.phyContactlessCummRuPayCard}</td>
												<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','E')">${mcpr.phyContactlessIncrementalCard}</td>
												<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','E')">${mcpr.virtualCardCummRuPayCard}</td>
												<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','E')">${mcpr.virtualCardIncrementalCard}</td>
												<%-- <td><a href="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','E')"
													onclick="clickAndDisable(this);"><spring:message code="mcprBinDetails.viewBtn" /></a>
												</td> --%>
											</tr>
										</c:forEach>
									</c:if>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</c:if>
	
		
		<c:if test="${showApprovalTab eq 'Yes'}">
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default">
						<div class="panel-heading">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="mcprBinDetails.listscreenList.title" /></span></span></strong>
							<c:if test="${not empty pendingMcprBinDetailsList}">
								<sec:authorize access="hasAuthority('Approve MCPR Data')">
										<input type="button" class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkMcprBinDetailsEdit('A','No')" id="submitButtonA"
											value="<spring:message code="feeRate.Approve" />" />
										<input type="button" class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkMcprBinDetailsEdit('R','No')" id="submitButtonR"
											value="<spring:message code="feeRate.Reject" />" />
								</sec:authorize>
							</c:if>
						</div>
						<div class="panel-body">
						
					<div class="row">
						<div class="col-sm-12">
						<button class="btn  pull-right btn_align" 
							id="clearFilters"><spring:message code="mcprBinDetails.clearFiltersBtn" /></button>
							&nbsp; <a class="btn btn-success pull-right btn_align" href="#" id="csvExport">
										<spring:message code="mcprBinDetails.csvBtn" /> </a>
									<a class="btn btn-success pull-right btn_align" href="#"
									id="excelExport"  ><spring:message code="mcprBinDetails.exportBtn" /></a>
						</div>
					</div>
						
							<c:choose>
								<c:when test="${not empty pendingMcprBinDetailsList}">
									<div class="table-responsive">
											<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
												<caption style="display:none;">MCPR Bin Data</caption>
												<thead>
													<tr>
													<sec:authorize access="hasAuthority('Approve MCPR Data')">
														<th scope = "col"><input type=checkbox name='selectAllCheck'
															 id="selectAll" data-target="toggleModalNews" value="All"></input></th>
													</sec:authorize>
													<th scope = "col"><label><spring:message code="mcprBinDetails.mcprBinDetailsId" /></label></th>
													<th scope = "col"><label><spring:message code="mcprBinDetails.monthEnding" /></label></th>
													<th scope = "col"><label><spring:message code="mcprBinDetails.binNumber" /></label></th>
													<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactCardCumulativeRpay" /></label></th>
													<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactCardIncremental" /></label></th>
													<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactlessCardCumulativeRpay" /></label></th>
													<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactlessCardIncremental" /></label></th>
													<th scope = "col"><label><spring:message code="mcprBinDetails.virtualCardCumulativeRpay" /></label></th>
													<th scope = "col"><label><spring:message code="mcprBinDetails.virtualCardIncremental" /></label></th>
													<th scope = "col"><label><spring:message code="mcprBinDetails.requestType" /></label></th>
													<th scope = "col"><label><spring:message code="mcprBinDetails.status" /></label></th>
													<%-- <th scope = "col"><label><spring:message code="mcprBinDetails.actionTitle" /></label></th> --%>
													</tr>
												</thead>
											<tbody>
												<c:forEach var="mcprs" items="${pendingMcprBinDetailsList}">
													<tr>
													<sec:authorize access="hasAuthority('Approve MCPR Data')">
															<c:if test="${mcprs.requestState =='P' }">
																<td><input type=checkbox name='type' id="selectSingle" 
																onclick="mySelect();" value="${mcprs.mcprBinDataDetailsId}"></input>
																</td>
															</c:if>
															<c:if test="${mcprs.requestState !='P' }">
															<td></td>
															</c:if>
														</sec:authorize>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">${mcprs.mcprBinDataDetailsId}</td>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">${mcprs.monthEnding}</td>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">${mcprs.binNumber}</td>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">${mcprs.phyContactCardCummRuPayCard}</td>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">${mcprs.phyContactCardIncrementalCard}</td>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">${mcprs.phyContactlessCummRuPayCard}</td>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">${mcprs.phyContactlessIncrementalCard}</td>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">${mcprs.virtualCardCummRuPayCard}</td>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">${mcprs.virtualCardIncrementalCard}</td>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">${mcprs.lastOperation}</td>
														<td onclick="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')">
															<c:if test="${mcprs.requestState =='A' }"><spring:message code="mcprBinDetails.requestState.approved.description" /></c:if>
															<c:if test="${mcprs.requestState =='P' }"><spring:message code="mcprBinDetails.requestState.pendingApproval.description" /></c:if>
															<c:if test="${mcprs.requestState =='R' }"><spring:message code="mcprBinDetails.requestState.rejected.description" /></c:if>
															<c:if test="${mcprs.requestState =='D' }"><spring:message code="mcprBinDetails.requestState.deleted.description" /></c:if>
														</td>
														<%-- <sec:authorize access="hasAuthority('Approve MCPR Data')">
															<td><a href="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')"
																onclick="clickAndDisable(this);"><spring:message code="mcprBinDetails.viewBtn" /></a></td>
														</sec:authorize>
														<sec:authorize access="hasAuthority('Edit MCPR Data')">
															<td><a href="javascript:viewMcprBinDetails('${mcprs.mcprBinDataDetailsId}','F')"
																onclick="clickAndDisable(this);"><spring:message code="mcprBinDetails.viewBtn" /></a></td>
														</sec:authorize> --%>
													</tr>
												</c:forEach>
											</tbody>
										</table>
									</div>
								</c:when>
							<c:otherwise>
								<div class="table-responsive">
									<table id="tabnew2"	class="table table-striped table-bordered" style="width:100%;">
									<caption style="display:none;">MCPR Bin Data</caption>
										<thead>
											<tr>
												<th scope = "col"><label><spring:message code="mcprBinDetails.mcprBinDetailsId" /></label></th>
												<th scope = "col"><label><spring:message code="mcprBinDetails.monthEnding" /></label></th>
												<th scope = "col"><label><spring:message code="mcprBinDetails.binNumber" /></label></th>
												<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactCardCumulativeRpay" /></label></th>
												<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactCardIncremental" /></label></th>
												<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactlessCardCumulativeRpay" /></label></th>
												<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactlessCardIncremental" /></label></th>
												<th scope = "col"><label><spring:message code="mcprBinDetails.virtualCardCumulativeRpay" /></label></th>
												<th scope = "col"><label><spring:message code="mcprBinDetails.virtualCardIncremental" /></label></th>
												<th scope = "col"><label><spring:message code="mcprBinDetails.requestType" /></label></th>
												<th scope = "col"><label><spring:message code="mcprBinDetails.status" /></label></th>
												<%-- <th scope = "col"><label><spring:message code="mcprBinDetails.actionTitle" /></label></th> --%>
											</tr>
										</thead>
									</table>
								</div>
							</c:otherwise>
						</c:choose>
					</div>
				</div>
			</div>
		</div>
	</c:if>
</c:if>
	
 <c:if test="${EditMenu eq 'No'}">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/historicMcprDataView');" role="tab" data-toggle="tab"> 
		<span class="glyphicon glyphicon-credit-card"><spring:message code="mcprBinDetails.listscreen.title" /></span></a>

	</ul>
	
	<div class="tab-content">
		<div role="tabpanel" class="tab-pane active" id="home">
			<c:if test="${showMainTab eq 'Yes'}">
				<div class="row">
				<div class="col-sm-12">
				<div role="tabpanel" class="tab-pane active" id="home">	
			<table class="table table-striped" style="font-size: 12px">
			<caption style="display:none;">MCPR Bin Data</caption>
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr >
					<td></td>
					<td style="width:5%;"> 								
						<label><spring:message code="mcprBinDetails.month" /><span style="color: red">*</span></label></td>
					<td style="width:20%;"><form:select path="month" id="month" name="month"
							maxlength="10"  value="${month}"
							cssClass="form-control medantory">
							<form:option value="SELECT" label="SELECT" />
							<form:options items="${monthList}"  itemValue="description" itemLabel="description"/>
							</form:select>	
						<div id="errmonth">
						<span for="month" class="error"><form:errors
							path="month" /> </span>
						</div>
					</td>
					<td></td>
					<td style="width:5%;"> 								
						<label><spring:message code="mcprBinDetails.year" /><span style="color: red">*</span></label></td>
					<td style="width:20%;"><form:select path="year" id="year" name="year"
							maxlength="10" value="${year}" 
							cssClass="form-control medantory">
							<form:option value="SELECT" label="SELECT" />
							<form:options items="${yearList}"  itemValue="description" itemLabel="description"/>
							</form:select>
						<div id="erryear" class="error">
							<span for="year" class="error"><form:errors
									path="year" /></span>
						</div>
					</td>
					<td></td>
					<td style="width:8%;">
							<label><spring:message code="mcprBinDetails.bankName" /><span style="color: red">*</span></label></td>
						<td style="width:20%;"><form:select path="bankName" id="bankName" name="bankName"
								maxlength="10" value="${bankName}" 
								cssClass="form-control medantory">
								<form:option value="SELECT" label="SELECT" />
									<form:options items="${bankNameList}"  itemValue="code" itemLabel="description"/>
								</form:select>
								
							<div id="errbankName" class="error">
								<span for="bankName" class="error"><form:errors
									path="bankName" /></span>
							</div>
						</td>
					<td></td>
				</tr>
			</tbody>
			</table>
			</div>
		</div>
		</div>
			<div class="col-sm-12 bottom_space">
			<hr />
				<div style="text-align:center">
				 <input type="hidden" id="hiddenMonth" value="${month}">
				 <input type="hidden" id="hiddenYear" value="${year}">
				 <input type="hidden" id="hiddenBank" value="${bankName}"> 
					<c:if test="${not empty showbutton}">
						<button type="button" class="btn btn-success"
						onclick="viewData('N','/historicMcprDataViewSearch');"><spring:message code="mcprBinDetails.searchBtn" /></button>
					</c:if>
						<button type="button" class="btn btn-danger"
						onclick="userAction('N','/historicMcprDataView');"><spring:message code="mcprBinDetails.backBtn" /></button>
				</div>
			</div>
			
			</c:if>	
		</div>
	</div>
	<div class="row">
		<div class="col-sm-12">	
			<div class="panel panel-default">
				<c:if test="${search eq 'No'}">
				<div class="panel-heading">
					<strong><span class="glyphicon glyphicon-th"></span> 
					<span data-i18n="Data"><spring:message code="mcprBinDetails.listscreenList.title" /></span></strong>
				</div>
				<div class="panel-body">
													
					<div class="table-responsive">
						<table id="tabnew2" class="table table-striped table-bordered" style="width:100%;">
						<caption style="display:none;">MCPR Bin Data</caption>
							<thead>
								<tr>
									<th scope = "col"><label><spring:message code="mcprBinDetails.mcprBinDetailsId" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.monthEnding" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.binNumber" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactCardCumulativeRpay" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactCardIncremental" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactlessCardCumulativeRpay" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactlessCardIncremental" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.virtualCardCumulativeRpay" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.virtualCardIncremental" /></label></th>
									<%-- <th scope = "col"><label><spring:message code="mcprBinDetails.actionTitle" /></label></th> --%>
							</tr>
							</thead>
						</table>
					</div>
				</div>
				</c:if>
				<c:if test="${search eq 'Yes'}">
					<div class="panel-heading">
						<strong><span class="glyphicon glyphicon-th"></span> 
						<span data-i18n="Data"><spring:message code="mcprBinDetails.listscreenList.title" /></span></strong>
					</div>
					<div class="panel-body">
					<div class="row">
					<div class="col-sm-12">
						<button class="btn  pull-right btn_align" 
							id="clearFilters"><spring:message code="mcprBinDetails.clearFiltersBtn" /></button>
							&nbsp; <a class="btn btn-success pull-right btn_align" href="#" id="csvExport">
									<spring:message code="mcprBinDetails.csvBtn" /> </a>
								<a class="btn btn-success pull-right btn_align" href="#"
									id="excelExport"  ><spring:message code="mcprBinDetails.exportBtn" /> </a>
					</div>
				</div>
					<div class="table-responsive">
						<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
						<caption style="display:none;">MCPR Bin Data</caption>
							<thead>
								<tr>
									<th scope = "col"><label><spring:message code="mcprBinDetails.mcprBinDetailsId" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.monthEnding" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.binNumber" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactCardCumulativeRpay" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactCardIncremental" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactlessCardCumulativeRpay" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.physicalContactlessCardIncremental" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.virtualCardCumulativeRpay" /></label></th>
									<th scope = "col"><label><spring:message code="mcprBinDetails.virtualCardIncremental" /></label></th>
									<%-- <th scope = "col"><label><spring:message code="mcprBinDetails.actionTitle" /></label></th> --%>
								</tr>
							</thead>
							<tbody>
								<c:if test="${not empty mcprBinDetailsList}">
									<c:forEach var="mcpr" items="${mcprBinDetailsList}">
									<tr>
										<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','H')">${mcpr.mcprBinDataDetailsId}</td>
										<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','H')">${mcpr.monthEnding}</td>
										<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','H')">${mcpr.binNumber}</td>
										<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','H')">${mcpr.phyContactCardCummRuPayCard}</td>
										<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','H')">${mcpr.phyContactCardIncrementalCard}</td>
										<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','H')">${mcpr.phyContactlessCummRuPayCard}</td>
										<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','H')">${mcpr.phyContactlessIncrementalCard}</td>
										<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','H')">${mcpr.virtualCardCummRuPayCard}</td>
										<td onclick="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','H')">${mcpr.virtualCardIncrementalCard}</td>
										<%-- <td><a href="javascript:viewMcprBinDetails('${mcpr.mcprBinDataDetailsId}','H')"
											onclick="clickAndDisable(this);"><spring:message code="mcprBinDetails.viewBtn" /></a>
										</td> --%>
									</tr>
									</c:forEach>
								</c:if>
							</tbody>
					</table>
					</div>
				</div>
			</c:if>
			</div>
		</div>
	</div>
</c:if>	
</div>
