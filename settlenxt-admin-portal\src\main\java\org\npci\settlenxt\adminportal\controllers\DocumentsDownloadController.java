package org.npci.settlenxt.adminportal.controllers;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.Deflater;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.util.IOUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.service.MasterService;
import org.npci.settlenxt.adminportal.service.ReportService;
import org.npci.settlenxt.adminportal.service.UserService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.AuditBatchLogDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseLookupServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.google.gson.JsonObject;

import lombok.extern.slf4j.Slf4j;

@Controller
@Slf4j
public class DocumentsDownloadController extends BaseController {

	private static final String FILE_EXTENSION = "FILE_EXTENSION";

	private static final String UTF_8 = "UTF-8";

	@Autowired
	private ReportService reportService;

	@Autowired
	private MasterService masterService;

	@Autowired
	private UserService userService;
	private static final Logger logger = LogManager.getLogger(DocumentsDownloadController.class);
	
	@Autowired
	BaseLookupServiceImpl lookUpService;

	private static final String SHOW_DOC_DOWNLOAD = "documentsDownloadReport";
	private static final String SHOW_UPLOAD_DOC_DOWNLOAD = "documentsUploadReport";
	private static final String ERROR_STATUS = "errorStatus";
	private static final String STATUS = "status";
	private static final String DOCUMENT_TYPE = "documentType";
	private static final String FILE_NOT_AVL_MSG = "File not available in location";
	private static final String FILE_DELETE_MSG = "File deleted successfully";
	private static final String FILE_DELETE_ERR_MSG = "Please select file to delete";

	@PostMapping("/fileDownload")
	@PreAuthorize("hasAuthority('View Document Download')")
	public String fileDownload(Model model) {

		AuditBatchLogDTO auditBatchLogDTO = new AuditBatchLogDTO();
		model.addAttribute(BaseCommonConstants.FILE_TYPE_LIST, reportService.getFileTypeList());
		model.addAttribute(BaseCommonConstants.AUDIT_BATCH_LOG, auditBatchLogDTO);
		List<AuditBatchLogDTO> memberList = reportService.getMemberListDoc();
		model.addAttribute(BaseCommonConstants.MEMBER_LIST, memberList);
		model.addAttribute(BaseCommonConstants.SHOW_CHECKBOX, CommonConstants.YES_FLAG);

		return getView(model, SHOW_DOC_DOWNLOAD);

	}

	@PostMapping("/fileSearchMembrStr")
	@PreAuthorize("hasAuthority('View Document Download')")
	public String fileSearchMembrStr(@RequestParam(value = "fromDateStr", required = false) String fromDateStr,
			@RequestParam(value = "toDateStr", required = false) String toDateStr,
			@RequestParam(value = "fileType", required = false) String fileType,
			@RequestParam(value = "memberName", required = false) String memberName, Model model) {

		AuditBatchLogDTO auditBatchLogDTO = bulkFileList(fromDateStr, toDateStr, fileType, memberName);

		List<AuditBatchLogDTO> reportList = new ArrayList<>();

		List<AuditBatchLogDTO> memberList = reportService.getMemberListDoc();
		model.addAttribute(BaseCommonConstants.MEMBER_LIST, memberList);
		try {
			reportList = reportService.reportGeneratedList(auditBatchLogDTO);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_DOC_DOWNLOAD, ex);
		}
		model.addAttribute(BaseCommonConstants.REPORT_LIST, reportList);
		model.addAttribute(BaseCommonConstants.AUDIT_BATCH_LOG, auditBatchLogDTO);
		model.addAttribute(BaseCommonConstants.FILE_TYPE_LIST, reportService.getFileTypeList());
		model.addAttribute(CommonConstants.SEARCH_FLAG, CommonConstants.YES_FLAG);
		model.addAttribute(BaseCommonConstants.SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		return getView(model, SHOW_DOC_DOWNLOAD);

	}

	

	@PostMapping("/fileContentDownload")
	@PreAuthorize("hasAuthority('View Document Download')")
	public ResponseEntity<Object> downloadDetails(@RequestParam(value = "filePath", required = false) String filePath,
			@ModelAttribute("auditBatchLogDTO") AuditBatchLogDTO auditBatchLogDTO, Model model) {

		if (filePath.lastIndexOf('.') <= filePath.lastIndexOf('/')) {
			throw new SettleNxtException("Error in File Path", "");
		}

		
		Resource resource = null;

		try {
			filePath = URLDecoder.decode(filePath, UTF_8);
			Path path = Paths.get(filePath);
			resource = new UrlResource(path.toUri());

			return ResponseEntity.ok().contentType(MediaType.parseMediaType("application/octet-stream"))
					.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
					.body(resource);
		} catch (

		MalformedURLException | UnsupportedEncodingException e) {
			handleErrorCodeAndForward(model, SHOW_DOC_DOWNLOAD, e);
		}

		return null;
	}
	public AuditBatchLogDTO bulkFileList(String fromDateStr, String toDateStr, String fileType, String memberName) {

		AuditBatchLogDTO auditBatchLogDTO = new AuditBatchLogDTO();
		auditBatchLogDTO.setFromDateStr(fromDateStr);
		auditBatchLogDTO.setToDateStr(toDateStr);
		auditBatchLogDTO.setFileType(fileType);
		auditBatchLogDTO.setMemberName(memberName);
		return auditBatchLogDTO;

	}
	@PostMapping("/fileDownloadVerify")
	@PreAuthorize("hasAuthority('View Document Download')")
	public ResponseEntity<Object> verifyFileAvailable(
			@RequestParam(value = "filePath", required = false) String filePath, Model model) throws SettleNxtException {
		JsonObject jsonResponse = new JsonObject();
		try {

			filePath = URLDecoder.decode(filePath, UTF_8);
			reportService.verifyFileAvailableInLocation(filePath);
		} catch (Exception ex) {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_FAIL);
			handleErrorCodeAndForward(model, SHOW_DOC_DOWNLOAD, ex);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
		jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_SUCCESS);
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PostMapping("/fileDownloadError")
	@PreAuthorize("hasAuthority('View Document Download')")
	public String showFileNotFoundError(Model model,
			@RequestParam(value = "fromDateStr", required = false) String fromDateStr,
			@RequestParam(value = "toDateStr", required = false) String toDateStr,
			@RequestParam(value = "fileType", required = false) String fileType,
			@RequestParam(value = "memberName", required = false) String memberName) {
		AuditBatchLogDTO auditBatchLogDTO = bulkFileList(fromDateStr, toDateStr, fileType, memberName);
		List<AuditBatchLogDTO> reportList = reportService.reportGeneratedList(auditBatchLogDTO);
		model.addAttribute(BaseCommonConstants.REPORT_LIST, reportList);
		List<AuditBatchLogDTO> memberList = reportService.getMemberListDoc();
		model.addAttribute(BaseCommonConstants.MEMBER_LIST, memberList);
		model.addAttribute(BaseCommonConstants.AUDIT_BATCH_LOG, auditBatchLogDTO);
		model.addAttribute(ERROR_STATUS, FILE_NOT_AVL_MSG);
		model.addAttribute(CommonConstants.SEARCH_FLAG, CommonConstants.YES_FLAG);
		return getView(model, SHOW_DOC_DOWNLOAD);
	}

	// fileupload

	@PostMapping("/bulkFileUpload")
	@PreAuthorize("hasAuthority('Upload Document')")
	public String uploadFiles(Model model) {
		AuditBatchLogDTO auditBatchLogDTO = new AuditBatchLogDTO();

		List<CodeValueDTO> fileExtnList = lookUpService.getLookupData(FILE_EXTENSION);
		
		if (!ObjectUtils.isEmpty(fileExtnList)) {

			model.addAttribute("fileExtnList",
					fileExtnList.stream().map(CodeValueDTO::getDescription).collect(Collectors.joining(",")));
		}
		model.addAttribute(BaseCommonConstants.PARTICIPANT_LISTS, userService.getParticipantList());

		model.addAttribute(DOCUMENT_TYPE, masterService.getLookUpData(CommonConstants.DOCUMENT_TYPE));
		auditBatchLogDTO.setFileType(BaseCommonConstants.RGCS_DOCUMENTS);
		model.addAttribute(BaseCommonConstants.AUDIT_BATCH_LOG, auditBatchLogDTO);
		return getView(model, SHOW_UPLOAD_DOC_DOWNLOAD);
	}

	@PostMapping("/uploadDocument")
	@PreAuthorize("hasAuthority('Upload Document')")
	public ResponseEntity<Object> uploadDocument(@RequestParam(value = "file", required = false) MultipartFile[] file,
			@RequestParam String participantId,
			@RequestParam String documentType,
			@ModelAttribute("auditBatchLogDTO") AuditBatchLogDTO auditBatchLogDTO, Model model) {

		JsonObject jsonResponse = new JsonObject();

		try {
			documentType = URLDecoder.decode(documentType, UTF_8);
			participantId = URLDecoder.decode(participantId, UTF_8);
		} catch (UnsupportedEncodingException ex) {
			throw new SettleNxtException("Error Encoding doc type and participant", "", ex);

		}

		auditBatchLogDTO.setDocumentType(documentType);
		auditBatchLogDTO.setParticipantId(participantId);
		List<String> result = reportService.verifyFileName(file);
		if (CollectionUtils.isEmpty(result)) {
			try {
				result = reportService.uploadDocument(file, auditBatchLogDTO);
			} catch (Exception e) {
				handleErrorCodeAndForward(model, SHOW_UPLOAD_DOC_DOWNLOAD, e);
			}
			if (CollectionUtils.isEmpty(result)) {
				jsonResponse.addProperty("success", "Successfully uploaded");
			} else {
				jsonResponse.addProperty("error", "File Already Exists " + String.join(",", result));
			}

		} else {
			jsonResponse.addProperty("error",
					"Invalid File Extension/Filename should be alphanumeric only " + String.join(",", result));
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PostMapping("/allfilesContentDownload")
	@PreAuthorize("hasAuthority('View Document Download')")
	public void downloadAllFiles(HttpServletResponse response,
			@RequestParam String bulkdownloadIdList, Model model) {

		checkBulkDownloadIdList(bulkdownloadIdList);
		String[] idArray = bulkdownloadIdList.split("\\|");
		int[] values = Arrays.stream(idArray).mapToInt(Integer::parseInt).toArray();
		List<Integer> bulkIdlist = Arrays.stream(values).boxed().collect(Collectors.toList());
		List<AuditBatchLogDTO> fileList = reportService.fetchFilePathList(bulkIdlist);
		try {
			List<AuditBatchLogDTO> bulkdownloadList = fileList;
			getAllDownloads(bulkdownloadList, response);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_DOC_DOWNLOAD, ex);
		}
	}

	@SuppressWarnings({ "null", "unused" })
	@PostMapping("/deleteDocDownloadFiles")
	@PreAuthorize("hasAuthority('Delete Document Download')")
	public String deleteAllFiles(@RequestParam String bulkdownloadIdList,
			@RequestParam(value = "fromDateStr", required = false) String fromDateStr,
			@RequestParam(value = "toDateStr", required = false) String toDateStr,
			@RequestParam(value = "fileType", required = false) String fileType, Model model) {

		try {
			if (!StringUtils.isBlank(bulkdownloadIdList)) {
				StringBuilder fileName = new StringBuilder();
				checkBulkDownloadIdList(bulkdownloadIdList);
				String[] idArray = bulkdownloadIdList.split("\\|");
				int[] values = Arrays.stream(idArray).mapToInt(Integer::parseInt).toArray();
				List<Integer> bulkIdlist = Arrays.stream(values).boxed().toList();
				List<AuditBatchLogDTO> fileList = reportService.fetchFilePathList(bulkIdlist);
				for (AuditBatchLogDTO str : fileList) {

					String path = str.getFilePath();
					path = new File(path).getCanonicalPath();
					File f = new File(path);
					
					fileName = deleteOpForFile(fileName, str, path);

				}
				handleStatus(model, fileName);
			} else {
				model.addAttribute(ERROR_STATUS, FILE_DELETE_ERR_MSG);
			}
			AuditBatchLogDTO auditBatchLogDTO = bulkFileList(fromDateStr, toDateStr, fileType, null);
			List<AuditBatchLogDTO> reportList = reportService.reportGeneratedList(auditBatchLogDTO);
			model.addAttribute(BaseCommonConstants.REPORT_LIST, reportList);
			model.addAttribute(BaseCommonConstants.AUDIT_BATCH_LOG, auditBatchLogDTO);
			List<AuditBatchLogDTO> memberList = reportService.getMemberListDoc();
			model.addAttribute(BaseCommonConstants.MEMBER_LIST, memberList);
			model.addAttribute(BaseCommonConstants.FILE_TYPE_LIST, reportService.getFileTypeList());
		} catch (Exception e) {
			handleErrorCodeAndForward(model, SHOW_DOC_DOWNLOAD, e);

		}
		return getView(model, SHOW_DOC_DOWNLOAD);
	}

	private StringBuilder deleteOpForFile(StringBuilder fileName, AuditBatchLogDTO str, String path)
			throws IOException {
		try {
			Files.delete(Paths.get(path));
			reportService.updateFileDelStatus(str.getLogId());
		}
		catch(NoSuchFileException e) {
			fileName = setTheFileName(fileName, str);
		}
		return fileName;
	}

	private StringBuilder setTheFileName(StringBuilder fileName, AuditBatchLogDTO str) {
		if (fileName.length() != 0) {
			fileName = fileName.append(",").append(str.getReportName());
		} else {
			fileName = fileName.append(str.getReportName());
		}
		return fileName;
	}

	private void handleStatus(Model model, StringBuilder fileName) {
		if (!fileName.isEmpty()) {
			model.addAttribute(ERROR_STATUS, "Files " + fileName.toString() + " cannot be deleted");
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, FILE_DELETE_MSG);
		}
	}

	private void checkBulkDownloadIdList(String bulkdownloadIdList) {
		if ("".equals(bulkdownloadIdList)) {

			throw new SettleNxtException("File Ids are null ", "");

		}
	}

	public void getAllDownloads(List<AuditBatchLogDTO> fileDtlsList, HttpServletResponse response) throws IOException {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		ZipOutputStream out = new ZipOutputStream(baos);

		OutputStream sos = null;
		try {

			out.setLevel(Deflater.DEFAULT_COMPRESSION);

			for (AuditBatchLogDTO fileStr : fileDtlsList) {
				String path = fileStr.getFilePath();
				path = new File(path).getCanonicalPath();
				File input = new File(path);

				out.putNextEntry(new ZipEntry(input.getName()));
				copyFiles(out, input);

			}
			out.closeEntry();

			out.close();
			sos = response.getOutputStream();

			response.setContentType("application/zip");
			response.setHeader("Content-Disposition", "attachment; filename=\"DocumentsDownloads.ZIP\"");

			sos.write(baos.toByteArray());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		} finally {
			if(sos!=null) {
				sos.flush();
				sos.close();
			}

		}
	}

	private void copyFiles(ZipOutputStream out, File input) {
		try (FileInputStream in = new FileInputStream(input)) {
			IOUtils.copy(in, out);
 
		} catch (IOException ioe) {
			throw new SettleNxtException("","",ioe);
 
		}
	}
}
