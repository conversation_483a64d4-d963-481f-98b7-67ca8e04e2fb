function userAction(_type, action) {
	var tokenValue = "lIpLsRjLtLDPSzoS2xPf9WXiF/M=";
	
	var funcCodeId = $("#funcCodeId").val();
	var data = "funcCodeId," + funcCodeId + ",_vTransactToken," + tokenValue + ",status,"
			+ status;
	postData(action, data);
}		
function EditFunctionCode(action, funcCodeId) {
	
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "funcCodeId," + funcCodeId + ",_vTransactToken,"
			+ tokenValue;
	postData(action, data);
}
