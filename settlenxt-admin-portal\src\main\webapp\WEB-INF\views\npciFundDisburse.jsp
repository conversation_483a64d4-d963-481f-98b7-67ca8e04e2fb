<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">

													<tr>
														<td><label><spring:message code="txn.detail.fees.lbl.feeAmount" /></label></td>
														<td id="feeAmt"><c:if test="${empty disTxnList.filingDispFee}">N/A</c:if>${disTxnList.filingDispFee}</td>
														<td><label><spring:message code="txn.detail.lbl.transactionDestinationInstitutionIDCode" /></label></td>
														<td id="transactionDestinationInstitutionIDCode">
																<c:choose>
																	<c:when test="${not empty disTxnList.txnDestInstId && not empty disTxnList.txnDestInstIdDesc }">${disTxnList.txnDestInstId}-${disTxnList.txnDestInstIdDesc}</c:when>
																	<c:otherwise>
																		<c:choose>
																			<c:when test="${not empty disTxnList.txnDestInstId &&  empty disTxnList.txnDestInstIdDesc}">${disTxnList.txnDestInstId}</c:when>
																		</c:choose>
																		<c:choose>
																			<c:when test="${empty disTxnList.txnDestInstId}">N/A</c:when>
																		</c:choose>
																	</c:otherwise>
																</c:choose>
														</td>
													</tr>
													<tr>
														<td><label><spring:message code="txn.detail.lbl.memberMsgText" /></label></td>
														<td id="memberMessageText"><c:if test="${empty disTxnList.memMsgTxt}">N/A</c:if>${disTxnList.memMsgTxt}</td>	
														<td><label><spring:message code="txn.detail.lbl.raiseUserId" /></label></td>
														<td id="raiseUserId"><c:if test="${empty disTxnList.makerId}">N/A</c:if>${disTxnList.makerId}</td>
													</tr>
													<tr>
														<td><label><spring:message code="txn.detail.fees.lbl.feeName" /></label></td>
														<td class="feeName">
																	<c:choose>
																		<c:when test="${not empty disTxnList.feeTypCode && not empty disTxnList.feeName }">${disTxnList.feeTypCode} - ${disTxnList.feeName}</c:when>
																		<c:otherwise>
																			<c:choose>
																				<c:when test="${not empty disTxnList.feeTypCode &&  empty disTxnList.feeName}">${disTxnList.feeTypCode}</c:when>
																			</c:choose>
																			<c:choose>
																				<c:when test="${empty disTxnList.feeTypCode}">N/A</c:when>
																			</c:choose>
																		</c:otherwise>
																	</c:choose>
														</td>
														<td><label><spring:message code="txn.detail.lbl.checkerUserName" /></label></td>
														<td id="checkerUserName"><c:if test="${empty disTxnList.checkerId}">N/A</c:if>${disTxnList.checkerId}</td>
													</tr>
													<tr>
														<td><label><spring:message code="txn.detail.lbl.rgcsSettlementDate" /></label></td>
														<td class="rgcsSettlementDate" id="rgcsSettlementDate"><c:if test="${empty disTxnList.netReconDate}">N/A</c:if>${disTxnList.netReconDate}</td>	
														<td></td>
														<td></td>
													</tr>
													