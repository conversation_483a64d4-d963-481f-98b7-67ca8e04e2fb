var i;
$(document).ready(function () {
	$('form')
    .each(function(){
        $(this).data('serialized', $(this).serialize())
    })
    .on('change input', function(){
        $(this)             
            .find('input:submit, button:submit')
                .prop('disabled', $(this).serialize() == $(this).data('serialized'))
        ;
        $('#forms').prop('disabled', false);
       
     })
$('#forms').prop('disabled', true);
$('#tabnew').on('click', function(){
   $('#forms').prop('disabled', false);
});

$('#tabnew1').on('click', function(){
   $('#forms').prop('disabled', false);
   
 
});




if (($('#showButton2').val() == "Y")) {


	$("input").prop('disabled', true);
	$("select").prop('disabled', true);
	
	}


	  
		if(document.getElementById('categoryorders')!=null){
	  $("#categoryorders").sortable({ 
	         opacity: 0.6, 
	         cursor: 'move',
	         stop: function(_e, _ui) {
	        	 $('#forms').prop('disabled', false);
	             console.log($('#categoryorder').sortable('toArray'));
	             
	           }	
	         });}
		else{
			$("#categoryorder").sortable({ 
		         opacity: 0.6, 
		         cursor: 'move',
		         stop: function(_e, _ui) {
		        	 $('#forms').prop('disabled', false);
		             console.log($('#categoryorder').sortable('toArray'));
		           }	
		         });
		}
		
		
		$(document).on("click", "a.edit2" , function() {
			   $('#forms').prop('disabled', false);
			 
			 
			 if ($(this).html() == 'Edit') {  
		        	
		                var arr=[];
		                var valueArr=[];
		               	i=0;
		                var tt=$(this).closest('tr').attr('id');
		               
		                var rowIndex=$(this).parents("tr").index();
		               
		                $(this).closest('tr').find("input").each(function() {
		                    var id=this.id;
		                    var value=this.value;
		                    document.getElementById(id).readOnly = false;
		                    arr[i] = id;
		                    valueArr[i]=value;
		                    i++;
		                });
		               
		                
		        	  $(this).parents("tr").remove();
		        	  var table = document.getElementById('categoryorders');
		        	  var row = table.insertRow(rowIndex);
		        	  row.id = tt;
		    	      
		  	        var cell1 = row.insertCell(0);
		  	        var length=rowIndex;
		  	        var selectList = document.createElement("input");
		  	        selectList.id = "addFeeConfigList["+length+"].feeMinorId";
		  	        selectList.name = "addFeeConfigList["+length+"].feeMinorId";
		  	        selectList.style.cssText = 'width: 150px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
		  	        selectList.value= valueArr[0];
		  	  
		  	        cell1.appendChild(selectList);
		  	        
		  	        
		  	         var cell8 = row.insertCell(1);
		        
		        var selectList8 = document.createElement("select");
		        selectList8.id = "interchangeFeeID"+length;
		        selectList8.name = "addFeeConfigList["+length+"].interchangeFeeID";
		        selectList8.style.cssText = 'width: 200px;height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
		      
		        cell8.appendChild(selectList8);
		         var option = document.createElement("option");
		option.value = 999;
		option.text = "Select";
		selectList8.appendChild(option);

		        for ( i of interchnageFeeIDList) {
		            option = document.createElement("option");
		            option.value = i.value;
		            option.text = i.label;
		            if(i.value==valueArr[1]){
		  	  	          option.setAttribute("selected", "selected");}
		            selectList8.appendChild(option);
		        }
		        
		  	    var cell5 = row.insertCell(2);
		        
		        var selectList5 = document.createElement("select");
		        selectList5.id = "issAssessmentFeeID"+length;
		        selectList5.name = "addFeeConfigList["+length+"].issAssessmentFeeID";
		        selectList5.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
		        cell5.appendChild(selectList5);
		        
		        option = document.createElement("option");
		        option.value = 999;
		        option.text = "Select";
		        selectList5.appendChild(option);
		        for (i of issAssessmentFeeIDList) {
		            option = document.createElement("option");
		            option.value = i.value;
		            option.text = i.label;
		             if(i.value==valueArr[2]){
		  	  	          option.setAttribute("selected", "selected");}
	            selectList5.appendChild(option);
		        }
		        
		        var cell3 = row.insertCell(3);
		        
		        var selectList3 = document.createElement("select");
		        selectList3.id = "acqAssessmentFeeID"+length;
		        selectList3.name = "addFeeConfigList["+length+"].acqAssessmentFeeID";
		        selectList3.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
		        cell3.appendChild(selectList3);
		        
		        option = document.createElement("option");
		        option.value = 999;
		        option.text = "Select";
		        selectList3.appendChild(option);
		        for (i of acqAssessmentFeeIDList) {
		            option = document.createElement("option");
		            option.value = i.value;
		            option.text = i.label;
		            if(i.value==valueArr[3]){
		  	  	          option.setAttribute("selected", "selected");}
		            selectList3.appendChild(option);
		        }
		        
		        var cell4 = row.insertCell(4);
		        
		        var selectList4 = document.createElement("select");
		        selectList4.id = "issProcessingFeeID"+length;
		        selectList4.name = "addFeeConfigList["+length+"].issProcessingFeeID";
		        selectList4.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
		        cell4.appendChild(selectList4);
		        
		        option = document.createElement("option");
		        option.value = 999;
		        option.text = "Select";
		        selectList4.appendChild(option);
		        for (i of issProcessingFeeIDList) {
		            option = document.createElement("option");
		            option.value = i.value;
		            option.text = i.label;
		            if(i.value==valueArr[4]){
		  	  	          option.setAttribute("selected", "selected");}
		            selectList4.appendChild(option);
		        }
		  	        var cell2 = row.insertCell(5);
		        
		        var selectList2 = document.createElement("select");
		        selectList2.id = "acqProcessingFeeID"+length;
		        selectList2.name = "addFeeConfigList["+length+"].acqProcessingFeeID";
		        selectList2.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
		      
		        cell2.appendChild(selectList2);
		         option = document.createElement("option");
		option.value = 999;
		option.text = "Select";
		selectList2.appendChild(option);

		        for ( i of acqProcessingFeeIDList) {
		            option = document.createElement("option");
		            option.value = i.value;
		            option.text = i.label;
		           if(i.value==valueArr[5]){
		  	  	          option.setAttribute("selected", "selected");}
		            selectList2.appendChild(option);
		        }
		        
		         var cell7 = row.insertCell(6);
		                
		                var selectList7 = document.createElement("select");
		                selectList7.id = "issAuthFeeID"+length;
		                selectList7.name = "addFeeConfigList["+length+"].issAuthFeeID";
		                selectList7.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
		                cell7.appendChild(selectList7);
		                option = document.createElement("option");
		                option.value = 999;
		                option.text = "Select";
		                selectList7.appendChild(option);
		                for (i of issAuthFeeIDList) {
		                    option = document.createElement("option");
		                    option.value = i.value;
		                    option.text = i.label;
		                    if(i.value==valueArr[6]){
		  	  	          option.setAttribute("selected", "selected");}
                 selectList7.appendChild(option);
		                }
		        
		    
		        var cell6 = row.insertCell(7);
		                
		                var selectList6 = document.createElement("select");
		                selectList6.id = "acqAuthFeeID"+length;
		                selectList6.name = "addFeeConfigList["+length+"].acqAuthFeeID";
		                selectList6.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
		                cell6.appendChild(selectList6);
		                option = document.createElement("option");
		                option.value = 999;
		                option.text = "Select";
		                selectList6.appendChild(option);
		                for (i of acqAuthFeeIDList) {
		                    option = document.createElement("option");
		                    option.value = i.value;
		                    option.text = i.label;
		                    if(i.value==valueArr[7]){
		  	  	          option.setAttribute("selected", "selected");}
		                    selectList6.appendChild(option);
		                }
		
		              if(valueArr[8]=='Active' || valueArr[8]=='A' ){ 
		       
		                $(row).append( '<td> <select id="status" name="addFeeConfigList['+length+'].status" style="width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;">'
		        				+ '<option value="Active" selected>Active</option>'
		        				+ '<option value="InActive">InActive</option>'
		        				+'</select> </td>');}
		              else{
		            	    $(row).append( '<td> <select id="status" name="addFeeConfigList['+length+'].status"  style="width: 200px;height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;">'
			        				+ '<option value="Active" >Active</option>'
			        				+ '<option value="InActive" selected>InActive</option>'
			        				+'</select> </td>');
		              }
			 
			
		  	    $(row).append('<td> <a  class="edit2" style="width: 100px;" > Edit </a> </td>'); 
			        $(row).append('<td> <a  class="editbtns" style="width: 100px;" >Remove</a></td>'); 
			 
			        
			        var xyz=""+length; 

			        $("#interchangeFeeID"+xyz).select2();

			        $("#issAssessmentFeeID"+xyz).select2();
			        $("#acqAssessmentFeeID"+xyz).select2();

			        $("#acqProcessingFeeID"+xyz).select2();
			        $("#issProcessingFeeID"+xyz).select2();

			        $("#acqAuthFeeID"+xyz).select2();
			        $("#issAuthFeeID"+xyz).select2();
			 
			 }
			 
			 
			 
			 });
			 
			 

	
	  $(document).on("click", "a.editbtns" , function() {
		    $('#forms').prop('disabled', false);
		var arr=[];
		i=0;
		var valueArr=[];
		  if ($(this).html() == 'Remove') {  
			  
			  
			  
				 $(this).closest('tr').find("input").each(function() {
			         var id=this.id;
			         var value=this.value;
			        
			         arr[i] = id;
			         valueArr[i]=value;
			         i++;
			     });

				 
				 var feeMinorId=valueArr[0];
		
					var x='#'+'availableMinorIds'+feeMinorId;
					var y=" "+x;	
					 $(this).parents("tr").remove();
					 $('#feeMinorList').multiselect('deselect', feeMinorId);
					 $('#feeMinorList').multiselect('refresh');
					 $(y).remove();
					 
					 $('#feeMinorList option[value="'+feeMinorId+'"]').remove();
					
					 $("#feeMinorList").append("<option value='" + feeMinorId + "' id='" + feeMinorId + "'>" + feeMinorId + "</option>");
					 $('#feeMinorList').multiselect('refresh');
					 $('#feeMinorList').multiselect('rebuild');
				 
				 
		  }
		  else if($(this).html() == 'Save'){
			  
		 $(this).closest('tr').find("input").each(function() {
	         var id=this.id;
	         
	         document.getElementById(id).readOnly = true;
	         
	     });
	          }
        
      });
	  
	  
	  

		  $('#feeMinorList').multiselect({
			  includeSelectAllOption: true,
			buttonWidth : '250px',
			nonSelectedText:'Select Minor Ids',
			enableFiltering: true,
			
			onChange : function(_options, _selected) {
				//
			

			},
		});

		$('#getMinorInfo').click( function(e) {
			
			e.preventDefault();
			
			 
					 var id = $('#feeMinorList option:selected').toArray().map(item => item.value).join();
					
					 
					    var minorIdsArr = id.split(',');
					    console.log(minorIdsArr);
					 
				 if ( minorIdsArr == '') {
						$('#errfeeMinorList').text('Please select Fee Minor Ids ');
						$('#errfeeMinorList').show();
						
						}
				 else {
							$('#errfeeMinorList').hide();
						} 	
						  
						var tokenValue = document.getElementsByName("_TransactToken")[0].value;
					var model = {	
							"minorIdsArr":minorIdsArr,
							}
					$.ajax({
						url : "getMinorInfo",
						type : "POST",
						 data : JSON.stringify(model),
						contentType: "application/json; charset=utf-8",
						dataType : "json",
						headers: {
					'_TransactToken': tokenValue
				},					
						success : function(data) {
							$('#minorList').empty();
							
							const unique = [...new Set(data.map(item => item.feeConfigId))]
						
							for (i of data) {	
								
							$('#minorList').append('<tr class="availableMinorIds" id="availableMinorIds'+i.feeConfigId+'" value="'+i.feeConfigId+'">'
									+'	<td>'+i.feeConfigId+'</td>'
									+'	<td>'+i.fieldName+'</td>'
									+'	<td>'+i.relationalOperator+'</td>'
									+'	<td>'+i.fieldValue+'</td>'
									+'	<td>'+i.status+'</td>'
									+' </tr>');
								
									
						
							}
							


							var arr = [];
							
							
						
							if(document.getElementById('categoryorders')!=null){

								$("#categoryorders tr").each(function() {
								  arr.push(this.id);
								});

								
							}
								else{
									$("#categoryorder tr").each(function() {
										  arr.push(this.id);
										});
								}
							
							

var minorSet = [];





for(i=0;i<arr.length;i++){
	
	var x=arr[i]+".feeMinorId";
if(null!=document.getElementById(x)){
	

var value1 = document.getElementById(x).value;
console.log("value is "+ value1);
minorSet.push(value1);

}else{
console.log("its null");
}
}

							
							for (i of unique){
							if(!minorSet.includes(i)){
								addRow(i);
								
								}
}
						},
						error : function() {
						
							//error
						},
					
					});
					
				});
				

});



function validatefield2(){


var arr =[]
var flag = true



			$("#assignedList tr").each(function() {
                                          arr.push(this.id);
                                        });
                                                        
                              
                                        
                                        for(let s in arr){
                                     
                                        	if(!validateField("fieldValue" + arr[s].replace("remove",""))){
                                        	
                                        		flag = false;
                                        	}
                                        }

		return flag;

}



function changeAction(_url, type) {

	$('#reqType').val(type);
	var arr = [];
	var feeConfigs = getValues();
	if(feeConfigs==0){
	$("#errConfigs").show();
var error="Please insert  Fields/Values for FeeConfig Details";
document.getElementById("errConfigs").innerHTML=error.fontcolor("red");
return false;
	}
	if(document.getElementById('categoryorders')!=null){
	 $("#categoryorders tr").each(function() {
	  arr.push(this.id);
	});
	}
	else{
	 $("#categoryorder tr").each(function() {
	  arr.push(this.id);
	});
	}
	 document.getElementById('sortPriorArr').value=arr;
	
	 document.getElementById('feeConfigs').value=feeConfigs;
}

function validateMajorId(msgID) {
	var majorId = (document.getElementById("feeMajorId").value).replace(
			/(^\s*)|(\s*$)/g, '');
			var errMajorId = document.getElementById(msgID);
			if (majorId == "0") {
				errMajorId.className = 'error';
				errMajorId.innerHTML = "Please select Major Id";
			return false;
			}
			else {
				errMajorId.className = 'error';
				errMajorId.innerHTML = "";
			}
			return true;	
}

function validateMinorId(msgID) {
	var minorId = (document.getElementById("feeMinorId").value).replace(
			/(^\s*)|(\s*$)/g, '');
			var errMinorId = document.getElementById(msgID);
			if (minorId == "0") {
				errMinorId.className = 'error';
				errMinorId.innerHTML = "Please select Minor Id";
			return false;
			}
			else {
				errMinorId.className = 'error';
				errMinorId.innerHTML = "";
			}
			return true;	
}

function validateFeeId(msgID) {
	var feeId = (document.getElementById("feeId").value).replace(
			/(^\s*)|(\s*$)/g, '');
			var errfeeId = document.getElementById(msgID);
			if (feeId == "-1") {
				errfeeId.className = 'error';
				errfeeId.innerHTML = "Please select Fee Type";
			return false;
			}
			else {
				errfeeId.className = 'error';
				errfeeId.innerHTML = "";
			}
			return true;	
}

function maxValue(arr) {
	  let max = arr[0];

	  for (let val of arr) {
	    if (val > max) {
	      max = val;
	    }
	  }
	  return max;
	}
function addRow(feeMinorId){

	 var tableTrArray=[];
	 var table;
	if(document.getElementById('categoryorders')!=null){
		
		$("#categoryorders tr").each(function() {
			  tableTrArray.push(this.id);
			});
	  table = document.getElementById('categoryorders');}
	else{
		$("#categoryorder tr").each(function() {
			tableTrArray.push(this.id);
			});
		table = document.getElementById('categoryorder');
	}
	
	

        var rowCount = table.rows.length;
        var row = table.insertRow(rowCount);

        var cell1 = row.insertCell(0);
        var length=(table.rows.length)-1;
        
        if(tableTrArray.length>0)
    	{
        	var arrId=[];
    	for(var z=0;z<tableTrArray.length;z++){
    		arrId[z] =parseInt(tableTrArray[z].replace(/\D/g, ''));
    	}
    	  
    	var length2=maxValue(arrId);
    	length=length2+1;
    	
    	}
        row.id= "addFeeConfigList["+length+"]";
        var selectList = document.createElement("input");
   
        selectList.id = "addFeeConfigList["+length+"].feeMinorId";
        selectList.name = "addFeeConfigList["+length+"].feeMinorId";
        selectList.style.cssText = 'width: 150px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
        selectList.setAttribute("type", "text");
        selectList.setAttribute("value", feeMinorId);
        selectList.setAttribute("readonly", "true");
        cell1.appendChild(selectList);

   
        
        var cell8 = row.insertCell(1);
        
        var selectList8 = document.createElement("select");
        selectList8.id = "interchangeFeeID"+length;
        selectList8.name = "addFeeConfigList["+length+"].interchangeFeeID";
        selectList8.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
        cell8.appendChild(selectList8);
         var option = document.createElement("option");
option.value = 999;
option.text = "Select";
selectList8.appendChild(option);
        for (i of interchnageFeeIDList) {
            option = document.createElement("option");
            option.value = i.value;
            option.text = i.label;
            selectList8.appendChild(option);
        }
        
        var cell5 = row.insertCell(2);
        
        var selectList5 = document.createElement("select");
        selectList5.id = "issAssessmentFeeID"+length;
        selectList5.name = "addFeeConfigList["+length+"].issAssessmentFeeID";
        selectList5.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
        cell5.appendChild(selectList5);
        
        option = document.createElement("option");
        option.value = 999;
        option.text = "Select";
        selectList5.appendChild(option);
        for (i of issAssessmentFeeIDList) {
            option = document.createElement("option");
            option.value = i.value;
            option.text = i.label;
            selectList5.appendChild(option);
        }
        
        var cell3 = row.insertCell(3);
        
        var selectList3 = document.createElement("select");
        selectList3.id = "acqAssessmentFeeID"+length;
        selectList3.name = "addFeeConfigList["+length+"].acqAssessmentFeeID";
        selectList3.style.cssText = 'width: 200px;height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
        cell3.appendChild(selectList3);
        
        option = document.createElement("option");
        option.value = 999;
        option.text = "Select";
        selectList3.appendChild(option);
        for (i of acqAssessmentFeeIDList) {
            option = document.createElement("option");
            option.value = i.value;
            option.text = i.label;
            selectList3.appendChild(option);
        }
var cell4 = row.insertCell(4);
        
        var selectList4 = document.createElement("select");
        selectList4.id = "issProcessingFeeID"+length;
        selectList4.name = "addFeeConfigList["+length+"].issProcessingFeeID";
        selectList4.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
        cell4.appendChild(selectList4);
        
        option = document.createElement("option");
        option.value = 999;
        option.text = "Select";
        selectList4.appendChild(option);
        for (i of issProcessingFeeIDList) {
            option = document.createElement("option");
            option.value = i.value;
            option.text = i.label;
            selectList4.appendChild(option);
        }

var cell2 = row.insertCell(5);
        
        var selectList2 = document.createElement("select");
        selectList2.id = "acqProcessingFeeID"+length;
        selectList2.name = "addFeeConfigList["+length+"].acqProcessingFeeID";
        selectList2.style.cssText = 'width: 200px;height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
        cell2.appendChild(selectList2);
        option = document.createElement("option");
option.value = 999;
option.text = "Select";
selectList2.appendChild(option);
        for (i of acqProcessingFeeIDList) {
            option = document.createElement("option");
            option.value = i.value;
            option.text = i.label;
            selectList2.appendChild(option);
        }

var cell7 = row.insertCell(6);
                
                var selectList7 = document.createElement("select");
                selectList7.id = "issAuthFeeID"+length;
                selectList7.name = "addFeeConfigList["+length+"].issAuthFeeID";
                selectList7.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
                cell7.appendChild(selectList7);
                option = document.createElement("option");
                option.value = 999;
                option.text = "Select";
                selectList7.appendChild(option);
                for (i of issAuthFeeIDList) {
                    option = document.createElement("option");
                    option.value = i.value;
                    option.text = i.label;
                    selectList7.appendChild(option);
                }
               

        var cell6 = row.insertCell(7);
                
                var selectList6 = document.createElement("select");
                selectList6.id = "acqAuthFeeID"+length;
                selectList6.name = "addFeeConfigList["+length+"].acqAuthFeeID";
                selectList6.style.cssText = 'width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;';
                cell6.appendChild(selectList6);
                option = document.createElement("option");
                option.value = 999;
                option.text = "Select";
                selectList6.appendChild(option);
                for (i of acqAuthFeeIDList) {
                    option = document.createElement("option");
                    option.value = i.value;
                    option.text = i.label;
                    selectList6.appendChild(option);
                }
 
                $(row).append( '<td> <select id="status" name="addFeeConfigList['+length+'].status"  style="width: 200px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;">'
				+ '<option value="Active">Active</option>'
				+ '<option value="InActive">InActive</option>'
				+'</select> </td>');
  
        $(row).append('<td> <a  class="editbtns" style="width: 100px;">Remove</a></td>'); 


       var xyz=""+length; 

        $("#interchangeFeeID"+xyz).select2();

        $("#issAssessmentFeeID"+xyz).select2();
        $("#acqAssessmentFeeID"+xyz).select2();

        $("#acqProcessingFeeID"+xyz).select2();
        $("#issProcessingFeeID"+xyz).select2();

        $("#acqAuthFeeID"+xyz).select2();
        $("#issAuthFeeID"+xyz).select2();

}

function validatePriority(msgID) {
	var priority = (document.getElementById("priority").value).replace(
			/(^\s*)|(\s*$)/g, '');
	var errPriority = document.getElementById(msgID);
	var regEx = /^\d*$/i;
	
	if (priority == "") {
		errPriority.className = 'error';
		errPriority.innerHTML = "Please Provide Priority";
	return false;
	}
	else if (!regEx.test(priority)) {
		errPriority.className = 'error';
		errPriority.innerHTML = "Priority should be numeric and  Mandatory";
	return false;
	}
	else {
		errPriority.className = 'error';
		errPriority.innerHTML = "";
	}
	return true;
	
	
	
}
function validateStatus(msgID) {
	var status = (document.getElementById("status").value).replace(
			/(^\s*)|(\s*$)/g, '');
			var errStatus = document.getElementById(msgID);
			if (status == "0") {
				errStatus.className = 'error';
				errStatus.innerHTML = "Please select status";
			return false;
			}
			else {
				errStatus.className = 'error';
				errStatus.innerHTML = "";
			}
			return true;	
}

function validateAddFeeConfig() {
	
	$('.jqueryError').text("");
	$('.jqueryError').hide();
	var check = false;
    var collapseThree = false;

	if (!check) {
		//
	} else {
	if (collapseThree) {

									if ($("#collapseThree").attr(
											'class').indexOf(
											'collapse in') == -1) {
										$("#collapseThreeLink")
												.trigger("click");
									}

								}
		return false;
	}
	
}

function addOrUpdateFeeConfig(action,reqType){
    
	var data = "feeMajorId," + $('#feeMajorId').val()   + ",status," + $('#status').val()+ ",reqType," +reqType;
	
	postData(action, data);
	   
	}

function getRowIndex( el ) {
	    while( (el = el.parentNode) && el.nodeName.toLowerCase() !== 'tr' );

	    if( el ) 
	        return el.rowIndex;
	}
var edit = true;


function inputToggle(e,x) {
  e.preventDefault();
  
  $(x).prop('contenteditable', true);
}

function setSelectedValue(idNo){
	
	const inputElement = document.querySelector(idNo);
	inputElement.value = "-1";
}