<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/viewCurrencyMaster.js"
	type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">
		 <form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewCurrencyMaster" modelAttribute="currencyMasterDto"
			action="${approveCurrencyMasterStatus}" autocomplete="off"> 
			<input type="hidden" id="currencyId" value="${currencyMasterDto.currencyId}" />
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> 
							<span data-i18n="Data"><spring:message code="currencyMaster.viewscreen.title" /></span></strong>
						</div>
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">Currency Master</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
										
									<td><label><spring:message code="currencyMaster.currencyId" /></label></td>
									<td>${currencyMasterDto.currencyId }</td>
									<td><label><spring:message code="currencyMaster.currencyMasterCode" /></label></td>
									<td >${currencyMasterDto.currencyCode }</td>
									<td><label><spring:message code="currencyMaster.currencyMasterDescription" /></label></td>
									<td >${currencyMasterDto.currencyDescription }</td>
									
									</tr>
									<tr>
									<td><label><spring:message code="currencyMaster.currencyAlpha" /></label></td>
									<td>${currencyMasterDto.currencyAlpha }</td>
									<td><label><spring:message code="currencyMaster.currencyDecimalPosition" /></label></td>
									<td>${currencyMasterDto.currencyDecimalPosition }</td>
									<td></td>
									<td></td>
									</tr>
									
									
								</tbody>
							</table>
							
							
							<div style="text-align:center;">
				<hr />
				<div style="text-align:center">
					<button type="button" class="btn btn-danger"
						onclick="userAction('N','/currencyMasterMain');">
						<spring:message code="currencyMaster.backBtn" /></button>
					<c:if test="${currencyMasterDto.requestState =='A' }">	
					<sec:authorize access="hasAuthority('Edit Currency Master')">
						<input name="editButton" type="button" class="btn btn-success"
						 id="approveRole" value="Edit" 
						onclick="EditCurrencyMaster('/editCurrencyMaster','${currencyMasterDto.currencyId}','mainPage');"/>
					</sec:authorize>
					</c:if>
					
				</div>
		</div>
							
						</div>
					</div>
				</div>
					</div>	
		</form:form>
		
	</div>

</div>
