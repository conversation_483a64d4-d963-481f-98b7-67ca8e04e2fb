<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript">
var rebateValidationMessages = {};
rebateValidationMessages['vuploadWTAT'] = "<spring:message code='mcprTAT.dataUploadWindow.validation.msg' javaScriptEscape='true' />";
rebateValidationMessages['veditWTAT']= "<spring:message code="mcprTAT.dataEditWindow.validation.msg" javaScriptEscape='true' />";
</script>
<script type="text/javascript"
	src="./static/js/validation/mcpr/mcprTATToFuncRGCS.js"></script>
	<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${approvedlist eq 'Y' }">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" onclick="submitForm('/showMcprTATConfig');" role="tab"
			data-toggle="tab"> <span class="glyphicon glyphicon-credit-card">&nbsp;</span><spring:message code="mcprTAT.tabname" />
		</a>

		<c:choose>
			<c:when test="${approvedlist eq 'N'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#profile" role="tab"  onclick="submitForm('/mcprTATPendingForApproval');"
			data-toggle="tab"> <span class="glyphicon glyphicon-ok">&nbsp;</span><spring:message code="mcprTAT.approval" />
		</a>


	</ul>
	<form:form id="mcprTATSearch" name="mcprTATSearch">
	</form:form>
	<c:if	test="${approvedlist eq 'Y'}">
										
		<div class="tab-content">
			
			<div role="tabpanel" class="tab-pane active" id="home">
				<div class="row">
					<div class="space_block">
						<div class="container-fluid height-min">
							<c:url value="approveUserStatus" var="approveUserStatus" />
							<form:form onsubmit="removeSpace(this); encodeForm(this);"
								method="POST" id="viewMcprTAT" 
								action="${approveUserStatus}" autocomplete="off" >
							<div id="errvErrorInfo" class="error">
												<span for="ErrorInfo" class="error"><form:errors
														 /></span>
							</div>
							<div class="row">
								<div class="col-sm-12">
									<div class="panel panel-default no_margin">
										<div class="panel-heading clearfix">
											<strong><span class="glyphicon glyphicon-th"></span> <span
												data-i18n="Data"><spring:message code="mcprTAT.mcprTATInformation" /></span></strong>
										</div>
										<div class="panel-body">
											<input type="hidden" id="htaTId" value="${mcprTATData.taTId}"/>
											<table class="table table-striped" style="font-size: 12px">
											<caption style="display:none;">Show MCPR TAT</caption>
												<tbody>
												<th scope="col"></th>
													<tr>
													<td></td>
													<td><label><spring:message code="mcprTAT.TAT" /><span style="color: red"></span></label></td>
													</tr>
													<tr>
													<td><label><spring:message code="mcprTAT.dataUploadWindow" /><span style="color: red"></span></label></td>
													<c:if test="${(pendingAppMcprTATData.requestState != 'P') && (pendingAppMcprTATData.requestState != 'R') }">
														<sec:authorize access="hasAuthority('Add MCPR TAT')">
															<td><input id="vuploadWTAT" value="${mcprTATData.uploadWTAT}"/>
															<div id="errvuploadWTAT" class="error">
															<span for="vuploadWTAT" class="error"><form:errors id="vuploadWTAT" /></span></div></td>
														</sec:authorize>
														<sec:authorize access="hasAuthority('Approve MCPR TAT')">
															<td><label>${mcprTATData.uploadWTAT}</label></td>
														</sec:authorize>
													</c:if>
													<c:if test="${(pendingAppMcprTATData.requestState == 'P') || (pendingAppMcprTATData.requestState == 'R') }">
														<td><label>${mcprTATData.uploadWTAT}</label></td>
													</c:if>


													</tr>
													<tr>
													<td><label><spring:message code="mcprTAT.dataEditWindow" /><span style="color: red"></span></label></td>
													<c:if test="${(pendingAppMcprTATData.requestState != 'P') && (pendingAppMcprTATData.requestState != 'R') }">
														<sec:authorize access="hasAuthority('Add MCPR TAT')">
															<td><input id="veditWTAT" value="${mcprTATData.editWTAT}"/>
															<div id="errveditWTAT" class="error">
															<span for="veditWTAT" class="error"><form:errors id="veditWTAT" /></span></div></td>
														</sec:authorize>
														<sec:authorize access="hasAuthority('Approve MCPR TAT')">
															<td><label>${mcprTATData.editWTAT}</label></td>
														</sec:authorize>
													</c:if>
													<c:if test="${(pendingAppMcprTATData.requestState == 'P') || (pendingAppMcprTATData.requestState == 'R') }">
														<td><label>${mcprTATData.editWTAT}</label></td>
													</c:if>


													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>				

							<div class="row">
								<div class="col-sm-12 bottom_space">
									<hr />
									<div style="text-align:center">

										<sec:authorize access="hasAuthority('Add MCPR TAT')">
													<c:if test="${(pendingAppMcprTATData.requestState != 'P') && (pendingAppMcprTATData.requestState != 'R') }">
												<button type="button" class="btn btn-success" id="bEdit"
													onclick="saveMcprTAT('','V')"><spring:message code="mcprTAT.submit" /></button>
											</c:if>
										</sec:authorize>
										<button type="button" class="btn btn-danger"
											onclick="homeMcprTAT('N','/showMcprTATConfig');"><spring:message code="mcprTAT.back" /></button>


									</div>
								</div>
							</div>
							</form:form>
						</div>
					</div>
				</div>	
			</div>
		</div>
	</c:if>
	<c:if	test="${approvedlist eq 'N'}">
		<c:if	test="${approveddata eq 'Y'}">
										
		<div class="tab-content">
			<!-- tabpanel -->
			<div role="tabpanel" class="tab-pane active" id="home">
				<div class="row">
					<div class="space_block">
						<div class="container-fluid height-min">
							<c:url value="approveUserStatus" var="approveUserStatus" />
							<form:form onsubmit="removeSpace(this); encodeForm(this);"
								method="POST" id="appMcprTAT" 
								action="${approveUserStatus}" autocomplete="off" >
							<div id="errvErrorInfo" class="error">
												<span for="ErrorInfo" class="error"><form:errors
														 /></span>
							</div>
							<div class="row">
								<div class="col-sm-12">
									<div class="panel panel-default no_margin">
										<div class="panel-heading clearfix">
											<strong><span class="glyphicon glyphicon-th"></span> <span
												data-i18n="Data"><spring:message code="mcprTAT.mcprTATInformation" /></span></strong>
										</div>
										
										<sec:authorize access="hasAuthority('Approve MCPR TAT')">
											<div class="panel-body">
												<table class="table table-striped infobold" style="font-size: 12px">
												<caption style="display:none;">Show MCPR TAT</caption>
												<tbody>
												<th scope="col"></th>
													<tr>
														<td colspan="6"><div class="panel-heading-red clearfix">
																<strong><span class="glyphicon glyphicon-info-sign"></span> <span
																	data-i18n="Data"><spring:message code="mcprTAT.requestInformation" /></span></strong>
															</div></td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
													</tr>
													<tr>
														
														<td><label><spring:message code="mcprTAT.requestType" /><span style="color: red"></span></label></td>
														<td>${pendingAppMcprTATData.lastOperation}</td>
														<td><label><spring:message code="mcprTAT.requestDate" /><span style="color: red"></span></label></td>
														<td>${pendingAppMcprTATData.lastUpdatedOn}</td>
														<td><label><spring:message code="mcprTAT.requestStatus" /><span style="color: red"></span></label></td>
														<%-- <td>${pendingAppMcprTATData.requestState}</td> --%>
														<td><c:if test="${pendingAppMcprTATData.requestState =='A' }"><spring:message	code="currencyMaster.requestState.approved.description" /></c:if>
															<c:if test="${pendingAppMcprTATData.requestState =='P' }"><spring:message code="currencyMaster.requestState.pendingApproval.description" /></c:if>
															<c:if test="${pendingAppMcprTATData.requestState =='R' }"><spring:message code="currencyMaster.requestState.rejected.description" /></c:if>
															</td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
														
													</tr>
													<tr>
														<td><label><spring:message code="mcprTAT.requestBy" /><span style="color: red"></span></label></td>
														<td>${pendingAppMcprTATData.lastUpdatedBy}</td>
														
														<td><label><spring:message code="mcprTAT.approverComments" /><span style="color: red"></span></label></td>
														<td colspan=2>${pendingAppMcprTATData.checkerComments}</td>									
														<td></td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
													</tr>
												</tbody>	
												</table>
											</div>
										</sec:authorize>
										
										<div class="panel-body">
											<input type="hidden" id="htaTId" value="${pendingAppMcprTATData.taTId}"/>
											<table class="table table-striped" style="font-size: 12px">
											<caption style="display:none;">Show MCPR TAT</caption>
												<tbody>
												<th scope="col"></th>
													<tr>
													<td></td>
													<td><label><spring:message code="mcprTAT.TAT" /><span style="color: red"></span></label></td>
													</tr>
													<tr>
													<td><label><spring:message code="mcprTAT.dataUploadWindow" /><span style="color: red"></span></label></td>
													<c:if test="${pendingAppMcprTATData.requestState == 'R' }">
														<sec:authorize access="hasAuthority('Add MCPR TAT')">
															<td><input id="vuploadWTAT" value="${pendingAppMcprTATData.uploadWTAT}"/>
															<div id="errvuploadWTAT" class="error">
															<span for="vuploadWTAT" class="error"><form:errors id="vuploadWTAT" /></span></div></td>
														</sec:authorize>
														<sec:authorize access="hasAuthority('Approve MCPR TAT')">
															<td><label>${pendingAppMcprTATData.uploadWTAT}</label></td>
														</sec:authorize>
													</c:if>
													<c:if test="${(pendingAppMcprTATData.requestState == 'P') || (pendingAppMcprTATData.requestState == 'A') }">
														<td><label>${pendingAppMcprTATData.uploadWTAT}</label></td>
													</c:if>
													<tr>
													<td><label><spring:message code="mcprTAT.dataEditWindow" /><span style="color: red"></span></label></td>
													<c:if test="${pendingAppMcprTATData.requestState == 'R' }">
														<sec:authorize access="hasAuthority('Add MCPR TAT')">
															<td><input id="veditWTAT" value="${pendingAppMcprTATData.editWTAT}"/>
															<div id="errveditWTAT" class="error">
															<span for="veditWTAT" class="error"><form:errors id="veditWTAT" /></span></div></td>
														</sec:authorize>
														<sec:authorize access="hasAuthority('Approve MCPR TAT')">
															<td><label>${pendingAppMcprTATData.editWTAT}</label></td>
														</sec:authorize>
													</c:if>
													<c:if test="${(pendingAppMcprTATData.requestState == 'P') || (pendingAppMcprTATData.requestState == 'A') }">
														<td><label>${pendingAppMcprTATData.editWTAT}</label></td>
													</c:if>
													</tr>
												</tbody>
											</table>
										</div>
										<sec:authorize access="hasAuthority('Approve MCPR TAT')">
											<c:if test="${pendingAppMcprTATData.requestState eq 'P'}">
												<div class="panel-body">
												<table class="table table-striped" style="font-size: 12px">
												<caption style="display:none;">Show MCPR TAT</caption>
													<tbody>
													<th scope="col"></th>
													<tr>
														<td colspan="6"><div class="panel-heading-red  clearfix">
																<strong><span class="glyphicon glyphicon-info-sign"></span> <span
																	data-i18n="Data"><spring:message
																			code="AM.lbl.reqInfo" /></span></strong>
															</div></td>
													</tr>

													<tr>										<td><label><spring:message
																	code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
														<td><select name="select" id="apprej"
															onchange="display()">
																<option value="N"><spring:message
																		code="AM.lbl.select" /></option>
																<option value="A" id="approve"><spring:message
																		code="AM.lbl.approve" /></option>
																<option value="R" id="reject"><spring:message
																		code="AM.lbl.reject" /></option>
														</select></td>
														<td>
														<div style="text-align:center">
																<label><spring:message code="AM.lbl.remarks" /><span
																	style="color: red">*</span></label>
															</div></td>
														<!-- //Added by deepak on 31-03-2016 -->
														<td colspan="2"><textarea rows="4" cols="50"
																maxlength="100" id="rejectReason"></textarea>
															<div id="errorrejectReason" class="error"></div></td>
													</tr>
												</tbody>
												</table>					
											</c:if>
										</sec:authorize>
									</div>
								</div>
							</div>				

							<div class="row">
								<div class="col-sm-12 bottom_space">
									<hr />
									<div style="text-align:center">
										<sec:authorize access="hasAuthority('Approve MCPR TAT')">
										<c:if test="${pendingAppMcprTATData.requestState eq 'P'}">
											<button type="button" class="btn btn-success" id="approveRole" 
												onclick="postAction('/approveMcprTATStatus');"><spring:message code="mcprTAT.submit" /></button>
										</c:if>
										</sec:authorize>
										<sec:authorize access="hasAuthority('Add MCPR TAT')">
										<c:if test="${pendingAppMcprTATData.requestState eq 'R'}">
											<button type="button" class="btn btn-success"  id="bEdit" onclick="saveMcprTAT('','A')"><spring:message code="mcprTAT.submit" /></button>
											<button type="button" id="approveRole" class="btn btn-success"
											onclick="postDiscardAction('/discardRejectedMcprTATEntry');"><spring:message code="mcprTAT.discard" /></button>
										</c:if>
										</sec:authorize>
										<button type="button" class="btn btn-danger"
											onclick="homeMcprTAT('N','/showMcprTATConfig');"><spring:message code="mcprTAT.back" /></button>
									</div>
								</div>
							</div>
							</form:form>
						</div>
					</div>
				</div>	
			</div>
		</div>
		</c:if>
		<c:if	test="${approveddata eq 'N'}">
		<div class="tab-content">
			<!-- tabpanel -->
			<div role="tabpanel" class="tab-pane active" id="home">
				<div class="row">
					<div class="space_block">
						<div class="container-fluid height-min">
							<div class="panel-heading clearfix">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message code="mcprTAT.noDataForApproval" /></span></strong>
							</div>
						</div>
					</div>
				</div>	
			</div>
		</div>
		</c:if>
	</c:if>
		
</div>
