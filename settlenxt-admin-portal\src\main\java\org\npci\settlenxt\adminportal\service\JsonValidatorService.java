package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.dto.JsonValidatorDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.stereotype.Service;

@Service
public interface JsonValidatorService {

	List<JsonValidatorDTO> getJsonValidatorDetails();

	void addJsonValidatorStg(JsonValidatorDTO jsonValidatorDto);

	JsonValidatorDTO getJsonValidator(int seqId);

	List<JsonValidatorDTO> getPendingReasonCode();

	JsonValidatorDTO getJsonValidatorStgDetail(int seqId);

	JsonValidatorDTO updateJsonValidatorStg(JsonValidatorDTO jsonValidatorDto);

	JsonValidatorDTO updateApproveOrRejectJsonValidator(int seqId ,String status, String remarks);

	JsonValidatorDTO discardJsonValidator(int seqId);

	String updateBulkStgJsonValidator(String jsonValidatorList, String status);

	List<String> getPcodeList() throws SettleNxtException;

	List<JsonValidatorDTO> getJsonValidatorMainDetails();

}
