package org.npci.settlenxt.adminportal.service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.axis.utils.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.ForexRateDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.ForexRateRepository;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
@Service
public class ForexRateServiceImpl implements ForexRateService {
	@Autowired
	ForexRateRepository forexRateRepository;
	@Autowired
	SessionDTO sessionDTO;

	
	// showMain Tab
	@Override
	public List<ForexRateDTO> getforexRateList(ForexRateDTO forexRateDto){
		return forexRateRepository.getForexRateList(forexRateDto);
	}
	//show Approval Tab
	@Override
	public List<ForexRateDTO> getPendingForexRateList(){
		return forexRateRepository.getPendingForexRateList();
	}
	//view MainTab info
	@Override
	@Transactional(readOnly=true)
	public ForexRateDTO viewForexRateTab(int forexRateId){
		ForexRateDTO forexRateDto=forexRateRepository.getForexRateViewPage(forexRateId);
		if (forexRateDto != null) {
			return forexRateDto;
		} else {
			return null;
		}
	}
	// view approval tab info
	@Override
	public ForexRateDTO getForexRateStgInfo(String forexRateId){
		ForexRateDTO forexRateStg = forexRateRepository.getForexRateStgInfoById(Integer.parseInt(forexRateId));
		if (forexRateStg == null) {
			throw new SettleNxtException("No Data Exist", "");
		}
		return forexRateStg;
	}
	
	// add edit ForexRate
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ForexRateDTO addEditForexRate(ForexRateDTO forexRateDto){
		int funcId = CommonConstants.TRANSACT_FUCTIONALITY_ADD_FOREX_RATE_CONFIG;
		if (CommonConstants.EDIT_FOREX_RATE.equalsIgnoreCase(forexRateDto.getAddEditFlag())) {
			funcId = CommonConstants.TRANSACT_FUCTIONALITY_EDIT_FOREXRATE_CONFIG;
		}
		Date lt = new Date();
		forexRateDto.setStatus("A");
		forexRateDto.setRequestState("P");
		forexRateDto.setLastUpdatedOn(lt);
		forexRateDto.setLastUpdatedBy(sessionDTO.getUserName());
		forexRateDto.setRequestState(BaseCommonConstants.REQUEST_STATE_SUBMITTED);

		if (funcId == CommonConstants.TRANSACT_FUCTIONALITY_EDIT_FOREXRATE_CONFIG) {
			forexRateDto.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
			forexRateRepository.updateForexRate(forexRateDto);
		}else{
			int returnValue = checkDuplicateData(forexRateDto);
									 
								       if (returnValue != 0) {
											throw new SettleNxtApplicationException("ERR_ForexRate_EXISTS1", "Duplicate Record");
									}
			forexRateDto.setCreatedOn(lt);
			forexRateDto.setCreatedBy(sessionDTO.getUserName());
			forexRateDto.setForexRateId(forexRateRepository.fetchForexRateIdSequence());
			forexRateDto.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
			forexRateRepository.insertForexRateStg(forexRateDto);
		}
		return forexRateRepository.getForexRateStg(forexRateDto.getForexRateId());
	}

	@Override
	public void addForexRate(List<ForexRateDTO> forexRates) {
		Date date = new Date();
		for (ForexRateDTO forexRateDTO : forexRates) {
			forexRateDTO.setAddEditFlag(CommonConstants.ADD_FOREX_RATE);
			forexRateDTO.setSettleDate(date);
			forexRateDTO.setStatus("A");
			forexRateDTO.setRequestState("P");
			forexRateDTO.setLastUpdatedOn(date);
			forexRateDTO.setLastUpdatedBy(sessionDTO.getUserName());
			forexRateDTO.setRequestState(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
			forexRateDTO.setCreatedOn(date);
			forexRateDTO.setCreatedBy(sessionDTO.getUserName());
			forexRateDTO.setForexRateId(forexRateRepository.fetchForexRateIdSequence());
			forexRateDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);

		}
		forexRateRepository.insertForexRateListStg(forexRates);
	}
	public int checkDuplicateData(ForexRateDTO forexRateDto) {
		List<ForexRateDTO> duplicateList=forexRateRepository.validateDuplicate(forexRateDto);
		if(duplicateList.isEmpty()) {
			return 0;
		}else {
			return 1;
		}
	}
	// for checker
	@Override
	public ForexRateDTO approveOrRejectForexRate(int forexRateId, String status, String remarks) {
		ForexRateDTO forexRateDto = getForexRateStg(forexRateId);
		forexRateDto.setLastUpdatedBy(sessionDTO.getUserName());
		forexRateDto.setRequestState(status);
		forexRateDto.setCheckerComments(remarks);
		forexRateDto.setStatus("A");
		Date lt = new Date();
		forexRateDto.setLastUpdatedOn(lt);
		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			ForexRateDTO ifscdtoMain = forexRateRepository.getForexRateMain(forexRateId);
			if (ifscdtoMain != null) {
				forexRateRepository.updateForexRateMain(forexRateDto);
			} else {
				forexRateRepository.insertForexRateMain(forexRateDto);
				

			}
		}
		forexRateDto.setCheckerComments(remarks);
		forexRateDto.setLastOperation(forexRateDto.getLastOperation());
		forexRateRepository.updateForexRateRequestState(forexRateDto);
		return forexRateDto;
	}

	@Override
	public ForexRateDTO getForexRateStg(int forexRateId) {
		return forexRateRepository.getForexRateStg(forexRateId);
	}
	
	@Override
	@Transactional(readOnly = true)
	public ForexRateDTO getForexRateForEdit(int forexRateId) {
		return forexRateRepository.getForexRateStgInfoById(forexRateId);
	}
	// for discard
    @Override
    public ForexRateDTO discardForexRate(int forexRateId) {
        ForexRateDTO forexRateDto = getForexRateStg(forexRateId);
        ForexRateDTO forexRateDtoMain = forexRateRepository.getForexRateMain(forexRateId);
        if (forexRateDtoMain != null) {
            forexRateDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
            forexRateRepository.updateForexRateDiscard(forexRateDtoMain);
        } else {
            forexRateRepository.deleteForexRateDiscardedEntry(forexRateDto);
        }
        return forexRateDto;
    }
    //for bulk checker
    @Override
    public String approveOrRejectForexRateBulk(String bulkApprovalReferenceNoList, String status,String remarks) {
    	String[] referenceNoArr = bulkApprovalReferenceNoList.split("\\|");
    	int forexRateId=0;
    	List<String> referenceNoArrList = Arrays.asList(referenceNoArr);

    	for (int i = 0; i < referenceNoArrList.size(); i++) {
    		try {
    			if (!StringUtils.isEmpty(referenceNoArr[i])) {
    				forexRateId=Integer.parseInt(referenceNoArr[i]);
    				ForexRateDTO forexRateDto = getForexRateStg(forexRateId);
    				if (forexRateDto == null) {
						forexRateDto = new ForexRateDTO();
    					forexRateDto.setStatus(CommonConstants.TRANSACT_FAIL);
    					throw new SettleNxtException("Exception occurred with Ref No" + referenceNoArr[i], "");
    				}
    				else{
    					forexRateDto.setRequestState(status);
    					forexRateDto.setCheckerComments(remarks);
    					forexRateDto.setStatus("A");
    					forExApprovalScenario(status, forexRateId, forexRateDto);
    					forexRateDto.setCheckerComments(remarks);
    					forexRateDto.setLastOperation(forexRateDto.getLastOperation());
    					Date lt = new Date();
    					forexRateDto.setLastUpdatedOn(lt);
    					forexRateDto.setLastUpdatedBy(sessionDTO.getUserName());
    					forexRateRepository.updateForexRateRequestState(forexRateDto);
    				}
    			}

    		} catch (Exception ex) {
    			throw new SettleNxtException("Exception for Ref no" + referenceNoArr[i], "", ex);

    		}
    	}
    	return CommonConstants.YES_FLAG;
    }
	private void forExApprovalScenario(String status, int forexRateId, ForexRateDTO forexRateDto) {
		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			ForexRateDTO forexRatedtoMain = forexRateRepository.getForexRateMain(forexRateId);
			if (forexRatedtoMain != null) {
				forexRateDto.setLastUpdatedOn(new Date());
				forexRateDto.setLastUpdatedBy(sessionDTO.getUserName());
				forexRateRepository.updateForexRateMain(forexRateDto);
			} else {
				forexRateDto.setLastUpdatedOn(new Date());
				forexRateDto.setLastUpdatedBy(sessionDTO.getUserName());
				forexRateRepository.insertForexRateMain(forexRateDto);
				
			}
		}
	}

	@Override
	public String getConversionRate(String networkId, String code, String description) {
		return forexRateRepository.getLatestConversionRate(networkId, code, description);
	}

}
