package org.npci.settlenxt.adminportal.service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

import java.util.List;
import java.util.stream.Collectors;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.ReportStatusDTO;
import org.npci.settlenxt.adminportal.repository.ReportRepository;
import org.npci.settlenxt.portal.common.dto.AuditBatchLogDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseReportServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportServiceImpl extends BaseReportServiceImpl implements ReportService {
	@Autowired
	ReportRepository reportRepo;

	private static final String GENERATION_FAILED = "GENERATION_FAILED";

	private static final String GENERATION_INITIATED = "GENERATION_INITIATED";

	@Override
	public List<AuditBatchLogDTO> reportGeneratedList(AuditBatchLogDTO auditBatchLogDTO) {
		auditBatchLogDTO.setParticipantType(BaseCommonConstants.PARTICIPANT_TYPE_ALL);
		SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

		String todate = "";
		try {
			auditBatchLogDTO.setFromDate(dateformat.parse(auditBatchLogDTO.getFromDateStr()));
			if (!auditBatchLogDTO.getToDateStr().contains("23:59:59")) {
				todate = auditBatchLogDTO.getToDateStr() + " 23:59:59";
			}
			auditBatchLogDTO.setToDate(dateFormat1.parse(todate));
			auditBatchLogDTO.setStatus(CommonConstants.REQUEST_STATE_APPROVED);
		} catch (ParseException e) {
			throw new SettleNxtException("report.date.error", "", e);
		}

		List<String> fileTypeList = getFileTypeList().stream().map(AuditBatchLogDTO::getFileType)
				.collect(Collectors.toList());

		boolean isFileType = !"0".equalsIgnoreCase(auditBatchLogDTO.getFileType());
		boolean isMemberName = !"0".equalsIgnoreCase(auditBatchLogDTO.getMemberName());
		return reportRepo.reportGeneratedList(auditBatchLogDTO,isFileType,isMemberName, fileTypeList);

	}

	@Override
	public List<AuditBatchLogDTO> settleNxtreportGeneratedList(AuditBatchLogDTO auditBatchLogDTO) {

		auditBatchLogDTO.setParticipantType(BaseCommonConstants.PARTICIPANT_TYPE_ALL);

		SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
		String todate = "";
		try {
			auditBatchLogDTO.setFromDate(dateformat.parse(auditBatchLogDTO.getFromDateStr()));
			if (!auditBatchLogDTO.getToDateStr().contains("23:59:59")) {
				todate = auditBatchLogDTO.getToDateStr() + " 23:59:59";
			}
			auditBatchLogDTO.setToDate(dateFormat1.parse(todate));
		} catch (ParseException e) {
			throw new SettleNxtException("report.date.error", "", e);
		}
		boolean isFileType = !"0".equalsIgnoreCase(auditBatchLogDTO.getFileType());
		boolean isCycle = !"0".equalsIgnoreCase(auditBatchLogDTO.getCycleNum());
		boolean isMemberName = !"0".equalsIgnoreCase(auditBatchLogDTO.getMemberName());
		return reportRepo.generatedList(auditBatchLogDTO, isFileType, isCycle, isMemberName);

	}

	@Override
	public List<String> getSettlementFileType() {
		return reportRepo.getSettlementFileTypeList(BaseCommonConstants.YES_FLAG);

	}

	@Override
	public List<String> getProductId() {

		return reportRepo.getProductId();

	}

	@Override
	public List<ReportStatusDTO> getReportStatusData(ReportStatusDTO reportStatusDto) {

		boolean isParticipant;
		boolean isStatus;
		boolean isReportType;

		if (reportStatusDto.getParticipantId() == null || reportStatusDto.getParticipantId().isEmpty()) {

			isParticipant = false;
		} else {
			isParticipant = true;
		}

		if (reportStatusDto.getStatus() == null || reportStatusDto.getStatus().isEmpty()) {

			isStatus = false;
		} else {
			isStatus = true;
		}

		if (reportStatusDto.getReportType() == null || reportStatusDto.getReportType().isEmpty()) {

			isReportType = false;
		} else {
			isReportType = true;
		}

		return reportRepo.getReportStatusData(reportStatusDto, isStatus, isReportType, isParticipant);
	}

	@Override
	public List<ReportStatusDTO> udpatestatus(List<ReportStatusDTO> statusData, int configureTime) {
		try {
			DateTimeFormatter dtf = DateTimeFormatter.ofPattern("HH:mm:ss");
			LocalTime today = LocalTime.now();
			String timeString = today.format(dtf);
			int dataSize = 0;

			if (!statusData.isEmpty()) {
				dataSize = statusData.size();
			}

			if (dataSize > 0) {
				for (int i = 0; i < dataSize; i++) {

					if (GENERATION_INITIATED.equals(statusData.get(i).getStatus())) {

						String[] arr = statusData.get(i).getCreatedTs().split(" ");
						String hour = arr[1];
						int seconds = Integer.parseInt(hour.split(":")[0]) * 60
								+ Integer.parseInt(hour.split(":")[1]) * 60 + Integer.parseInt(hour.split(":")[2]);
						int seconds2 = Integer.parseInt(timeString.split(":")[0]) * 60
								+ Integer.parseInt(timeString.split(":")[1]) * 60
								+ Integer.parseInt(timeString.split(":")[2]);

						seconds = seconds + (configureTime * 60);

						compareAndSetSec(statusData, i, seconds, seconds2);
					}

					else {

						String[] arr = statusData.get(i).getCreatedTs().split(" ");
						String hour = arr[1];
						int seconds = Integer.parseInt(hour.split(":")[0]) * 60
								+ Integer.parseInt(hour.split(":")[1]) * 60 + Integer.parseInt(hour.split(":")[2]);

						seconds = seconds + (configureTime * 60);

						String[] arr1 = statusData.get(i).getUpdatedTs().split(" ");
						String hour1 = arr1[1];
						int seconds1 = Integer.parseInt(hour1.split(":")[0]) * 60
								+ Integer.parseInt(hour1.split(":")[1]) * 60 + Integer.parseInt(hour1.split(":")[2]);

						compareAndSetSec(statusData, i, seconds, seconds1);
					}

				}
			}

		} catch (Exception e) {
			throw new SettleNxtException("Error Setting Report Data" + e.getMessage(), "", e);
		}
		return statusData;
	}

	private void compareAndSetSec(List<ReportStatusDTO> statusData, int i, int seconds, int seconds1) {
		if (seconds <= seconds1) {
			statusData.get(i).setStatus(GENERATION_FAILED);
		}
	}

	@Override
	public List<ReportStatusDTO> getStatusCount(String productCode, String cycleDate, String cycleNumber) {

		return reportRepo.getStatusCount(productCode, cycleDate,cycleNumber);
	}

	@Override
	public List<ReportStatusDTO> getStatusDataForProd(ReportStatusDTO reportStatusDTO) {

		return reportRepo.getStatusDataForProd(reportStatusDTO);
	}

	@Override
	public List<String> getSettlementCycleListBasedOnProdCode(String productCode) {
		return reportRepo.getSettlementCycleListBasedOnProdCode(productCode);
	}

	@Override
	public ReportStatusDTO getLatestCycleDN(ReportStatusDTO reportStatusDTO) {
		return reportRepo.getLatestCycleDN(reportStatusDTO);
	}
	
	@Override
	public ReportStatusDTO getCycleInfo(String productCode) {
		return reportRepo.getCycleInfo(productCode);
	}
	
}
