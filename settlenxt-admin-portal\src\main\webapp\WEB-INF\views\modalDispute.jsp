<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head><title></title></head>
<style>
.input__container {
	display: flex;
	justify-content: space-between;
	margin: 5pt;
}

.sub_select {
	width: fit-content;
	min-width: 25rem;
	max-width: 25rem;
}

.sub_input {
	width: fit-content;
	min-width: 25rem;
}

.file_upload {
	margin-left: 6px;
}

.file_select {
	margin-left: 32rem;
}
</style>
<input type="hidden" id="mti" name="mti" />
<input type="hidden" id="funcCode" name="funcCode" />
<input type="hidden" id="amountTransaction" name="amountTransaction" />
<input type="hidden" id="fullPartial" name="fullPartial" />
<input type="hidden" id="messageReasonCode" style="max-width: 10rem;" name="messageReasonCode" />
<input type="hidden" id="internalTrackingNumber" name="internalTrackingNumber" />
<input type="hidden" id="amountAdditional" name="amountAdditional" />
<input type="hidden" id="memberMessageText" name="memberMessageText" />
<input type="hidden" id="documentIndicator" name="documentIndicator" />
<input type="hidden" id="actionCode" name="actionCode" />
<%-- <c:url value="transactionDetailSummary" var="submitUserDetails" />
			<form:form onsubmit="return encodeForm(this);" method="POST"
			id="transactionDetailSummary" modelAttribute="transactionDetailSummary"
			action="${submitUserDetails}" autocomplete="off"> --%>
<div class="modal fade" id="createPresentment" tabindex="-1"
	role="dialog" aria-labelledby="createPresentment" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="presentmentTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">
				<!-- Create Presentment -->
				<div>
					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionPresentment"
							class="sub_input amountTransactionHandler"
							value="${transactionDetailSummary.amtTran}" />
					</div>
					<div class="input__container">
						<label for="cardAcceptorZipCode"><spring:message
								code="txn.detail.lbl.cardAcceptorZipCode" /></label> <input
							id="cardAcceptorZipCode" class="sub_input"
							value="${transactionDetailSummary.cardAcptPstCode}"></input>
					</div>
					<div class="input__container">
						<label for="merchantTelephoneNumber"><spring:message
								code="txn.detail.lbl.merchantTelephoneNumber" /></label> <input
							id="merchantTelephoneNumber" class="sub_input"
							value="${transactionDetailSummary.merchTelNo}"></input>
					</div>
					<div class="input__container">
						<label for="cardHolderUID"><spring:message
								code="txn.detail.lbl.cardHolderUID" /></label> <input
							id="cardHolderUID" class="sub_input"></input>
					</div>
					<div class="input__container">
						<label for="cardHolderIncomeTaxPan"><spring:message
								code="txn.detail.lbl.cardHolderIncomeTaxPan" /></label> <input
							id="cardHolderIncomeTaxPan" class="sub_input"></input>
					</div>
					<div class="input__container">
						<label for="cardAcceptorAdditionalAddress"><spring:message
								code="txn.detail.lbl.cardAcceptorAdditionalAddress" /></label> <input
							id="cardAcceptorAdditionalAddress" class="sub_input"></input>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.merchantIndicator" /></label> <select
							class="form-control-select sub_select " id="merchantIndicator">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="D"><spring:message code="msg.lbl.default"></spring:message></option>
							<option value="S"><spring:message
									code="txn.detail.lbl.smallMerchant"></spring:message></option>
							<option value="L"><spring:message
									code="txn.detail.lbl.licMerchant"></spring:message></option>
						</select>

					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtoncreatePresentment" id="createPresentment"
					onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>
<!-- Presentment Reversal -->
<div class="modal fade" id="createPresentmentRev" tabindex="-1"
	role="dialog" aria-labelledby="createPresentmentRev" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="presentmentRevTitle" />
			
			</div>

			<div class="modal-body" id="data_modal">
				<div>
					<div class="input__container">
						<label for="amountTransactionPresentRev"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionPresentmentRev"
							class="sub_input amountTransactionHandler"
							value="${transactionDetailSummary.amtTran}" />
					</div>
					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.messageReasonCode" /></label> <select
							id="messageReasonCode" style="max-width: 10rem;"
							class="form-control sub_select messageReasonCodeHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>

						</select>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtoncreatePresentmentRev"
					id="createPresentmentRev" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="createVoidAcpAuthCancel" tabindex="-1"
	role="dialog" aria-labelledby="createVoidAcpAuthCancel"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="createVoidAcpAuthCancelTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">
				<div>
					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionVoid"
							class="sub_input amountTransactionHandler"
							value="${transactionDetailSummary.amtTran}" />
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtoncreateVoidAcpAuthCancel"
					id="createVoidAcpAuthCancel" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>
<!-- Create Chargeback , create Pre-compilance -->
<div class="modal fade" id="createChargeBackPreComp" tabindex="-1"
	role="dialog" aria-labelledby="createChargeBackPreComp"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="chargeBackCompTitle" />
				
			</div>
			<div class="modal-body" id="data_modal">
				<div>

					<div class="input__container">
						<label for="amountTransaction"> <spring:message
								code="txn.detail.lbl.amountTransaction" />
						</label> <input id="amountTransactionPreCompCharge"
							class="sub_input amountTransactionHandler"
							value="${transactionDetailSummary.amtTran}" />

					</div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.reasonSubtype" /></label>
						<select id="reasonSubtype" class="form-control sub_select">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
						</select>
					</div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.fullPartial" /></label>
						<select id="fullPartialPreCompCharge"
							class="form-control sub_select fullPartialHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="F"><spring:message
									code="txn.detail.lbl.full"></spring:message></option>
							<option value="P"><spring:message
									code="txn.detail.lbl.Partial"></spring:message></option>
						</select>
					</div>


					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.messageReasonCode" /></label> <select
							id="messageReasonCode" style="max-width: 10rem;"
							class="form-control sub_select messageReasonCodeHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>

						</select>
					</div>

					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberPreCompCharge"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>
					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							id="documentIndicatorPreCompCharge"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>
					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextPreCompCharge"
							class="sub_input memberMessageTextHandler"></input>
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtoncreateChargeBackPreComp"
					id="createChargeBackPreComp" onclick="submitData(event)">Ok</button>
			</div>
		</div>
	</div>
</div>
<!-- end of Create Chargeback , create Pre-compilance -->

<!-- ChargeBack Refund -->
<div class="modal fade" id="chargeBackRefund" tabindex="-1"
	role="dialog" aria-labelledby="chargeBackRefund" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="chargeBackRefundTitle" />
				
			</div>
			<div class="modal-body" id="data_modal">
				<div>

					<div class="input__container">
						<label for="amountTransaction"> <spring:message
								code="txn.detail.lbl.amountTransaction" />
						</label> <input id="amountTransactionRefundCB"
							class="sub_input amountTransactionHandler"
							value="${transactionDetailSummary.amtTran}" />

					</div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.reasonSubtype" /></label>
						<select id="reasonSubtype" class="form-control sub_select">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
						</select>
					</div>

					<div class="input__container">
						<label for="amountAdditional"> <spring:message
								code="txn.detail.lbl.amountsAdditional" />
						</label> <input id="amountAdditionalRefundCB"
							class="sub_input amountAdditionalHandler"
							value="${transactionDetailSummary.amtAdd}" />

					</div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.fullPartial" /></label>
						<select id="fullPartialRefundCB"
							class="form-control sub_select fullPartialHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="F"><spring:message
									code="txn.detail.lbl.full"></spring:message></option>
							<option value="P"><spring:message
									code="txn.detail.lbl.Partial"></spring:message></option>
						</select>
					</div>


					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.messageReasonCode" /></label> <select
							id="messageReasonCode" style="max-width: 10rem;"
							class="form-control sub_select messageReasonCodeHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>

						</select>
					</div>

					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberRefundCB"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>
					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.controlNo" /></label>
						<input id="controlNo" class="sub_input"> </input>
					</div>

					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextRefundCB"
							class="sub_input memberMessageTextHandler"></input>
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonchargeBackRefund" id="chargeBackRefund"
					onclick="submitData(event)">Ok</button>
			</div>
		</div>
	</div>
</div>
<!-- End of ChargeBack Refund -->




<!-- Create Pre-Arbitraction , create Representment , create Arbitration Case Filing & Accept Pre-Arbitration -->

<div class="modal fade" id="createArbRepresent" tabindex="-1"
	role="dialog" aria-labelledby="createArbRepresent" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="arbRepresentTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">

				<div>

					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionPreArb"
							class="sub_input amountTransactionHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							id="documentIndicatorPreArb"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>
					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.fullPartial" /></label>
						<select id="fullPartialPreArb"
							class="form-control sub_select fullPartialHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="F"><spring:message
									code="txn.detail.lbl.full"></spring:message></option>
							<option value="P"><spring:message
									code="txn.detail.lbl.Partial"></spring:message></option>
						</select>
					</div>




					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberPreArb"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>
					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextPreArb"
							class="sub_input memberMessageTextHandler"></input>
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtoncreateArbRepresent"
					id="createArbRepresent" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>
<!--  end of Pre-Arbitartion , create Representment , create Arbitration Case Filing & Accept Pre-Arbitration -->




<!-- Deny Pre-Arbitartion -->

<div class="modal fade" id="denyPreArbitration" tabindex="-1"
	role="dialog" aria-labelledby="denyPreArbitration" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="denyPreArbitrationTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">


				<div>
					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionDenyPreArb"
							class="sub_input amountTransactionHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							id="documentIndicatorDenyPreArb"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>
					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextDenyPreArb"
							class="sub_input memberMessageTextHandler"></input>
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtondenyPreArbitration"
					id="denyPreArbitration" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<!-- end of  Deny Pre-Arbitartion -->

<!-- Accept Chargeback , Representment -->
<div class="modal fade" id="acceptChargeBackRepresentment" tabindex="-1"
	role="dialog" aria-labelledby="acceptChargeBackRepresentment"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="chargeBackRepresentmentTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">


				<div class="input__container">
					<label for="amountTransaction"><spring:message
							code="txn.detail.lbl.amountTransaction" /></label> <input
						id="amountTransactionRePresentment"
						class="sub_input amountTransactionHandler"></input>
				</div>


			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonacceptChargeBackRepresentment"
					id="acceptChargeBackRepresentment" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>
<!-- end of Chargeback , Representment -->

<div class="modal fade" id="createTipSurcharge" tabindex="-1"
	role="dialog" aria-labelledby="createTipSurcharge"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="tipChargeTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">


				<div class="input__container">
					<label for="amountTransaction"><spring:message
							code="txn.detail.lbl.amountTransaction" /></label> <input
						id="amountTransactionTipSurcharge"
						class="sub_input amountTransactionHandler" value="${tipAmount}"></input>
				</div>


			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtoncreateTipSurcharge"
					id="createTipSurcharge" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<!-- Auth Cancellation Request -->
<div class="modal fade" id="authCancellationReqCashBackRev" tabindex="-1"
	role="dialog" aria-labelledby="authCancellationReq" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="authCancellationReqTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">


				<div class="input__container">
					<label for="amountTransaction"><spring:message
							code="txn.detail.lbl.amountTransaction" /></label> <input
						id="amountTransactionAuthCancel"
						class="sub_input amountTransactionHandler"
						value="${transactionDetailSummary.amtTran}"></input>
				</div>

				<div class="input__container">
					<label for="memberMessageText"><spring:message
							code="txn.detail.lbl.memberMessageText" /></label> <input
						id="memberMessageTextAuthCancel"
						class="sub_input memberMessageTextHandler"></input>
				</div>

			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonauthCancellationReqCashBackRev"
					id="authCancellationReqCashBackRev" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>



<!--  Create Debit Adjustment -->

<div class="modal fade" id="debitAdjust" tabindex="-1" role="dialog"
	aria-labelledby="debitAdjust" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="debitAdjustTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">


				<div>

					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionDebitAdjust"
							class="sub_input amountTransactionHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.reasonSubtype" /></label>
						<select id="reasonSubtype" class="form-control sub_select">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
						</select>
					</div>


					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							id="documentIndicatorDebitAdjust"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>
					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.messageReasonCode" /></label> <select
							id="messageReasonCode" style="max-width: 10rem;"
							class="form-control sub_select messageReasonCodeHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>

						</select>
					</div>


				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtondebitAdjust" id="debitAdjust"
					onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<!-- end  Debit Adjustment-->


<!-- create Credit Adjustment -->
<div class="modal fade" id="creditAdjust" tabindex="-1" role="dialog"
	aria-labelledby="creditAdjust" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="creditAdjustTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">
				<div>
					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionCreAdjust"
							class="sub_input amountTransactionHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.reasonSubtype" /></label>
						<select id="reasonSubtype" class="form-control sub_select">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
						</select>
					</div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.fullPartial" /></label>
						<select id="fullPartialCreAdjust"
							class="form-control sub_select fullPartialHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="F"><spring:message
									code="txn.detail.lbl.full"></spring:message></option>
							<option value="P"><spring:message
									code="txn.detail.lbl.Partial"></spring:message></option>
						</select>
					</div>


					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.messageReasonCode" /></label> <select
							id="messageReasonCode" style="max-width: 10rem;"
							class="form-control sub_select messageReasonCodeHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>

						</select>
					</div>



					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberCreAdjust"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							id="documentIndicatorCreAdjust"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>
					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextCreAdjust"
							class="sub_input memberMessageTextHandler"></input>
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtoncreditAdjust" id="creditAdjust"
					onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<!-- end Credit Adjustment ,create Debit Adjustment  -->


<!--  create Refund -->

<div class="modal fade" id="raiseRefund" tabindex="-1" role="dialog"
	aria-labelledby="raiseRefund" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="raiseRefundTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">
				<div>
					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionRefund"
							class="sub_input amountTransactionHandler"
							value="${transactionDetailSummary.amtTran}" />
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.messageReasonCode" /></label> <select
							id="messageReasonCode" style="max-width: 10rem;"
							class="form-control sub_select messageReasonCodeHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>

						</select>
					</div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.fullPartial" /></label>
						<select id="fullPartialRefund"
							class="form-control sub_select fullPartialHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="F"><spring:message
									code="txn.detail.lbl.full"></spring:message></option>
							<option value="P"><spring:message
									code="txn.detail.lbl.Partial"></spring:message></option>
						</select>
					</div>

					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberRefund"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>
				</div>

			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonraiseRefund" id="raiseRefund"
					onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<!-- end create Refund -->






<!--  create Good Faith -->
<div class="modal fade" id="raiseGoodFaith" tabindex="-1" role="dialog"
	aria-labelledby="raiseGoodFaith" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="raiseGoodFaithTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">

				<div>

					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionGoodFaith"
							class="sub_input amountTransactionHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							id="documentIndicatorGoodFaith"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>
					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.fullPartial" /></label>
						<select id="fullPartialGoodFaith"
							class="form-control sub_select fullPartialHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="F"><spring:message
									code="txn.detail.lbl.full"></spring:message></option>
							<option value="P"><spring:message
									code="txn.detail.lbl.Partial"></spring:message></option>
						</select>
					</div>

					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberGoodFaith"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>


					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextGoodFaith"
							class="sub_input memberMessageTextHandler"></input>
					</div>
				</div>

			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonraiseGoodFaith" id="raiseGoodFaith"
					onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<!--  end of create Faith -->



<!--  create Retrival Request -->
<div class="modal fade" id="raiseRetrievalReq" tabindex="-1"
	role="dialog" aria-labelledby="raiseRetrievalReq" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="raiseRetrievalReqTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">


				<div>

					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberRetriReq"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.reasonSubtype" /></label>
						<select id="reasonSubtype" class="form-control sub_select">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
						</select>
					</div>



					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.messageReasonCode" /></label> <select
							id="messageReasonCode" style="max-width: 10rem;"
							class="form-control sub_select messageReasonCodeHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>

						</select>
					</div>


				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonraiseRetrievalReq" id="raiseRetrievalReq"
					onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>
<!--  end of Retrival Request -->




<!--  create retrival Fullfilment -->
<div class="modal fade" id="retrievalFullFilment" tabindex="-1"
	role="dialog" aria-labelledby="retrievalFullFilment" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="retrievalFullFilmentTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">

				<div>

					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberRetrieFull"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							id="documentIndicatorRetrieFull"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>
					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextRetrieFull"
							class="sub_input memberMessageTextHandler"></input>
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal"  onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal"  onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonretrievalFullFilment"
					id="retrievalFullFilment" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<!--  end of create retrival fullfilment -->



<!--  create retrival non-fullfilment  -->
<div class="modal fade" id="retrievalNonFullFilment" tabindex="-1"
	role="dialog" aria-labelledby="retrievalNonFullFilment"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="retrievalNonFullFilmentTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">


				<div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.reasonSubtype" /></label>
						<select id="reasonSubtype" class="form-control sub_select">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
						</select>
					</div>


					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.messageReasonCode" /></label> <select
							path="messageReasonCode" id="messageReasonCode" style="max-width: 10rem;"
							class="form-control sub_select messageReasonCodeHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>

						</select>
					</div>




					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberRetriNonFull"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							path="documentIndicator" id="documentIndicatorRetriNonFull"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>

					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextRetriNonFull"
							class="sub_input memberMessageTextHandler"></input>
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonretrievalNonFullFilment"
					id="retrievalNonFullFilment" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="retrievalNonFullFilmentTxnAcpt" tabindex="-1"
	role="dialog" aria-labelledby="retrievalNonFullFilmentTxnAcpt"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="retrievalNonFullFilmentTxnAcptTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">

				<div>

					<div class="input__container">
						<label><spring:message code="txn.detail.lbl.reasonSubtype" /></label>
						<select id="reasonSubtype" class="form-control sub_select">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
						</select>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.messageReasonCode" /></label> <input
							path="messageReasonCode" id="messageReasonCode" style="max-width: 10rem;"
							class="form-control sub_select messageReasonCodeRRNF" readonly="true" />
						</select>
					</div>

					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberRetriNonFull"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							path="documentIndicator" id="documentIndicatorRetriNonFull"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>

					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextRetriNonFull"
							class="sub_input memberMessageTextHandler"></input>
					</div>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal"  onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonretrievalNonFullFilmentTxnAcpt"
					id="retrievalNonFullFilmentTxnAcpt" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<!-- Arbitration Verdict -->
<div class="modal fade" id="arbitratiotionVerdict" tabindex="-1"
	role="dialog" aria-labelledby="arbitratiotionVerdict"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="arbitratiotionVerdictTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">
				<div>
					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionArbVerdict"
							class="sub_input amountTransactionHandler"
							value="${transactionDetailSummary.amtTran}" />
					</div>

					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberArbVerdict"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							path="documentIndicator" id="documentIndicatorArbVerdict"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>

					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextArbVerdict"
							class="sub_input memberMessageTextHandler"></input>
					</div>

				</div>

			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonarbitratiotionVerdict"
					id="arbitratiotionVerdict" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<!--  Arbitration Verdict  -->

<!-- Accept Arbitration -->
<div class="modal fade" id="arbitrationAccept" tabindex="-1"
	role="dialog" aria-labelledby="arbitrationAccept" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="arbitrationAcceptTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">
				<div>
					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionArbAccept"
							class="sub_input amountTransactionHandler"
							value="${transactionDetailSummary.amtTran}" />
					</div>

					<div class="input__container">
						<label for="amountAdditional"> <spring:message
								code="txn.detail.lbl.amountsAdditional" />
						</label> <input id="amountAdditionalArbAccept"
							class="sub_input amountAdditionalHandler"
							value="${transactionDetailSummary.amtAdd}" />

					</div>

					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberArbAccept"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>

					<div class="input__container">
						<label><spring:message
								code="txn.detail.lbl.documentIndicator" /></label> <select
							path="documentIndicator" id="documentIndicatorArbAccept"
							class="form-control sub_select documentIndicatorHandler">

							<option value="0">
								<spring:message code="msg.lbl.select"></spring:message>
							</option>
							<option value="Y"><spring:message code="msg.lbl.yes"></spring:message></option>
							<option value="N"><spring:message code="msg.lbl.no"></spring:message></option>
						</select>

					</div>
					<!-- File upload -->
					<div class="card-body-document">
						<div class="row">
								<div class="col-md-12 file_upload">
									<div>
										<label> <spring:message code="fileUpload.selectFile" />
											<span style="color: red">*</span>
										</label>
									</div>
									<div class="file_select">
										<input name="fileArr[]" id="fileArr" type="file" multiple="true" />
										<label id="title">(.xml format allowed.)</label>
										<div id="errFile" class="error"></div>
									</div>
									<div class="file_select">
										<button class="btn btn-default" id="reset" type="reset">
											<spring:message code="fileUpload.reset" />
										</button>
									</div>
									<%-- <div>
														<spring:message code="fileUpload.samplefileName" />
														<a href="javascript:sampleFileDownload()">
															<spring:message code="fileUpload.download" />
														</a>
													</div> --%>
								</div>
								<div class="col-md-6">
									<c:if test="${not empty rejectedFileList}">
										<div>
											<span style="font-size: small;" id="errFromDate"
												class="error">Please upload unique files :
												${rejectedFileList}</span></br>
										</div>
									</c:if>
								</div>
						</div>
					</div>
					<!-- File upload end -->
					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextArbAccept"
							class="sub_input memberMessageTextHandler"></input>
					</div>

				</div>

			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonarbitrationAccept" id="arbitrationAccept"
					onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

<!-- Create Arbitration Case Withdrawal -->
<div class="modal fade" id="arbitrationWithdrawal" tabindex="-1"
	role="dialog" aria-labelledby="arbitrationWithdrawal"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="arbitrationWithdrawalTitle" />
				
			</div>

			<div class="modal-body" id="data_modal">
				<div>
					<div class="input__container">
						<label for="amountTransaction"><spring:message
								code="txn.detail.lbl.amountTransaction" /></label> <input
							id="amountTransactionArbWithdrawal"
							class="sub_input amountTransactionHandler"
							value="${transactionDetailSummary.amtTran}" />
					</div>

					<div class="input__container">
						<label for="amountAdditional"> <spring:message
								code="txn.detail.lbl.amountsAdditional" />
						</label> <input id="amountAdditionalArbWithdrawal"
							class="sub_input amountAdditionalHandler"
							value="${transactionDetailSummary.amtAdd}" />

					</div>

					<div class="input__container">
						<label for="internalTrackingNumber"><spring:message
								code="txn.detail.lbl.internalTrackingNumber" /></label> <input
							id="internalTrackingNumberArbWithdrawal"
							class="sub_input internalTrackingNumberHandler"></input>
					</div>

					<div class="input__container">
						<label for="memberMessageText"><spring:message
								code="txn.detail.lbl.memberMessageText" /></label> <input
							id="memberMessageTextArbWithdrawal"
							class="sub_input memberMessageTextHandler"></input>
					</div>

				</div>

			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal"  onclick="clearData()">cancel</button>
				<button type="button" class="btn btn-secondary" data-dismiss="modal"  onclick="clearData()">clear</button>
				<button type="button" class="btn btn-primary submitButtonarbitrationWithdrawal"
					id="arbitrationWithdrawal" onclick="submitData(event);">Ok</button>
			</div>
		</div>
	</div>
</div>

</html>