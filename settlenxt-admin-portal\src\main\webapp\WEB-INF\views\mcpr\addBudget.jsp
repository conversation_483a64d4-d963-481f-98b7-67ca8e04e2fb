<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/addBudget.js" type="text/javascript"></script>

<script type="text/javascript">
	var budgetValidationMessages = {};
	budgetValidationMessages['budget'] = "<spring:message code='budget.budgetNumber.validation.msg' javaScriptEscape='true' />";
	budgetValidationMessages['vendor'] = "<spring:message code='budget.vendor.validation.msg' javaScriptEscape='true' />";
	budgetValidationMessages['year'] = "<spring:message code='budget.year.validation.msg' javaScriptEscape='true' />";
	budgetValidationMessages['budget1'] = "<spring:message code='budget.budgetNumber1.validation.msg' javaScriptEscape='true' />";
</script>

<div class="row">
	<div role="alert" style="display: none" id="jqueryError">
		<div id="errorStatus" class="alert alert-danger" role="alert">${errorStatus}</div>
	</div>
	<div role="alert" style="display: none" id="jquerySuccess">
		<div id="successStatus" class="alert alert-success" role="alert">${successStatus}</div>
	</div>
</div>
<div class="panel panel-default no_margin">
	<div class="panel-heading clearfix">
		<c:if test="${not empty addBudget}">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><label><spring:message
							code="budget.addscreen.title" /></label></span></strong>

		</c:if>
		<c:if test="${not empty editBudget}">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><label><spring:message
							code="budget.editscreen.title" /></label></span></strong>

		</c:if>

	</div>
	<c:if test="${not empty addBudget}">
		<c:url value="addBudgetConfig" var="submitBudgetConfig" />
	</c:if>
	<c:if test="${not empty editBudget}">
		<c:url value="updateBudgetConfig" var="submitBudgetConfig" />
	</c:if>


	<div class="panel-body">
		<form:form onsubmit="return encodeForm(this);" method="POST"
			id="addBudgetConfig" modelAttribute="budgetDTO"
			action="${submitBudgetConfig}" autocomplete="off">
			<br />
			<form:hidden path="budgetId" id="budgetId" name="budgetId"
				value="${budgetDTO.budgetId}" />
			<input id="hparentPage" type="hidden" value="${parentPage}" />
			<c:if test="${not empty showbutton}">
				<div class="row">
					<div class="col-sm-3">

						<div class="form-group">
							<label><spring:message code="budget.vendor" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addBudget}">

								<form:select path="vendorId" id="vendor" name="vendor"
									class="form-control">
									<form:option value="" label="SELECT" />
									<form:options items="${vendorList}" itemLabel="description"
										itemValue="code" />
								</form:select>
							</c:if>
							<c:if test="${not empty editBudget}">

								<form:input path="vendorId" id="vendor"
									value="${budgetDTO.vendor}" name="vendor" maxlength="50"
									cssClass="form-control medantory" readonly="true" />
							</c:if>
							<div id="errvendor">
								<span for="vendor" class="error"><form:errors
										path="vendor" /></span>
							</div>


						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="budget.year" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addBudget}">

								<form:select path="year" id="year" name="year" maxlength="10"
									value="${budgetDTO.year}" cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${finYear}" itemValue="code"
										itemLabel="description" />
								</form:select>

							</c:if>
							<c:if test="${not empty editBudget}">

								<form:input path="year" id="year"
									value="${budgetDTO.displayYear}" name="year" maxlength="50"
									cssClass="form-control medantory" readonly="true" />



							</c:if>
							<div id="erryear">
								<span for="year" class="error"><form:errors path="year" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="budget.budgetName" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addBudget}">
								<form:input path="budget" id="budget" name="budget"
									maxlength="50" cssClass="form-control medantory loginIDADD" />
							</c:if>
							<c:if test="${not empty editBudget}">
								<form:input path="budget" id="budget"
									value="${budgetDTO.budget}" name="budget" maxlength="50"
									cssClass="form-control medantory" />

							</c:if>
							<div id="errbudget">
								<span for="budget" class="error"><form:errors
										path="budget" /></span>
							</div>
						</div>
					</div>



				</div>
			</c:if>
			<c:if test="${empty showbutton}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default no_margin">
							<div class="panel-body">
								<table class="table table-striped infobold"
									style="font-size: 12px">
									<caption style="display: none;">Budget</caption>
									<thead style="display: none;">
										<th scope="col"></th>
									</thead>
									<tbody>									
										<tr>
											<td><label><spring:message
														code="budget.budgetId" /><span style="color: red"></span></label></td>
											<td>${budgetDTO.budgetId}</td>
											<td><label><spring:message code="budget.vendor" /><span
													style="color: red"></span></label></td>
											<td>${budgetDTO.vendor}</td>
											<td><label><spring:message
														code="budget.budgetName" /><span style="color: red"></span></label></td>
											<td>${budgetDTO.budget}</td>
											<td><label><spring:message code="budget.year" /><span
													style="color: red"></span></label></td>
											<td>${budgetDTO.displayYear}</td>

										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</c:if>


			<div class="col-sm-12 bottom_space">

				<div style="text-align: center">

					<c:if test="${not empty addBudget}">
						<c:if test="${not empty showbutton}">
							<button type="button" value="Submit" class="btn btn-success"
								onclick="resetAction();">
								<spring:message code="binexcl.reset" />
							</button>
						</c:if>
						<c:if test="${not empty showbutton}">
							<button type="button" value="Submit" id="bSubmit"
								class="btn btn-success" onclick="checkAlreadyPresent()">
								<spring:message code="msg.lbl.submit" />
							</button>
						</c:if>
					</c:if>

					<sec:authorize access="hasAuthority('Edit Budget')">
						<c:if test="${not empty editBudget}">
							<c:if test="${not empty showbutton}">
								<button type="button" value="Submit" id="bUpdate"
									class="btn btn-success" onclick="addOrUpdateBudget('E');">
									<spring:message code="msg.lbl.update" />
								</button>
							</c:if>
						</c:if>
						<c:if
							test="${budgetDTO.requestState eq 'R'and not empty showbutton}">
							<button type="button" class="btn btn-danger"
								onclick="postDiscardBudgetAction('/discardRejectedBudget');">
								<spring:message code="budget.discardBtn" />
							</button>

							<button type="button" class="btn btn-danger"
								onclick="userAction('N','/budgetPendingForApproval');">
								<spring:message code="binexcl.backBtn" />
							</button>
						</c:if>
					</sec:authorize>

					<c:if test="${budgetDTO.requestState  ne 'R'}">
						<c:if test="${parentPage  eq 'approvalTab'}">
							<button type="button" class="btn btn-danger"
								onclick="userAction('N','/budgetPendingForApproval');">
								<spring:message code="binexcl.backBtn" />
							</button>
						</c:if>
						<c:if test="${parentPage  ne 'approvalTab'}">
							<button type="button" class="btn btn-danger"
								onclick="userAction('N','/showBudget');">
								<spring:message code="binexcl.backBtn" />
							</button>
						</c:if>
					</c:if>

				</div>
			</div>
		</form:form>
	</div>
</div>
