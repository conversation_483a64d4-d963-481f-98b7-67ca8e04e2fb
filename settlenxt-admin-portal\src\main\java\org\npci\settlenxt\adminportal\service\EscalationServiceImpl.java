package org.npci.settlenxt.adminportal.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.portal.common.dto.BaseEscalationDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.repository.BaseEscalationRepository;
import org.npci.settlenxt.portal.common.service.BaseEscalationServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseFunctionalityEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EscalationServiceImpl extends BaseEscalationServiceImpl implements EscalationService {
	@Autowired
	BaseEscalationRepository baseEscalationRepo;

	@Override
	public String updateEscalation(BaseEscalationDTO escalationDTO) {

		if (escalationDTO.getName().split(",", -1).length == 0) {
			throw new SettleNxtException("Escalation Details are empty", "");
		}
		int n = escalationDTO.getName().split(",", -1).length;

		for (int i = 0; i < n; i++) {
			BaseEscalationDTO escalationSplitDTO = new BaseEscalationDTO();

			escalationSplitDTO.setMemberId(escalationDTO.getMemberId());
			escalationSplitDTO.setDepartmentId(escalationDTO.getDepartmentId().split(",", -1)[i]);
			escalationSplitDTO.setEscLevel(escalationDTO.getEscLevel().split(",", -1)[i]);

			escalationSplitDTO.setName(escalationDTO.getName().split(",", -1)[i]);
			escalationSplitDTO.setDesignation(escalationDTO.getDesignation().split(",", -1)[i]);
			escalationSplitDTO.setEmail(escalationDTO.getEmail().split(",", -1)[i]);
			escalationSplitDTO.setLandline(escalationDTO.getLandline().split(",", -1)[i]);
			escalationSplitDTO.setMobile(escalationDTO.getMobile().split(",", -1)[i]);
			escalationSplitDTO.setPinCode(escalationDTO.getPinCode().split(",", -1)[i]);

			if (escalationDTO.getState().split(",", -1)[i].equalsIgnoreCase("SELECT")) {
				escalationSplitDTO.setState("0");
			} else {
				escalationSplitDTO.setState(escalationDTO.getState().split(",", -1)[i]);
			}
			if (StringUtils.isNotBlank(escalationDTO.getAddress())) {
				escalationSplitDTO.setAddress(escalationDTO.getAddress().split(",", -1)[i]);

				escalationSplitDTO.setAddress(escalationSplitDTO.getAddress().replace("*", " "));
			}
			escalationSplitDTO.setCreatedBy(sessionDTO.getUserName());
			escalationSplitDTO.setCreatedOn(LocalDateTime.now());
			escalationSplitDTO.setLastUpdatedBy(null);
			escalationSplitDTO.setLastUpdatedOn(null);
			escalationSplitDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
			if (escalationDTO.getUserId() != null) {
				escalationSplitDTO.setUserId(escalationDTO.getUserId().split(",", -1)[i]);
			}
			updateEscalationCall(escalationSplitDTO, escalationDTO);
		}
		return CommonConstants.TRANSACT_SUCCESS;
	}

	private void updateEscalationCall(BaseEscalationDTO escalationSplitDTO, BaseEscalationDTO escalationDTO) {

		if (!StringUtils.isBlank(escalationSplitDTO.getName())
				|| !StringUtils.isBlank(escalationSplitDTO.getDesignation())
				|| !StringUtils.isBlank(escalationSplitDTO.getEmail())
				|| !StringUtils.isBlank(escalationSplitDTO.getLandline())
				|| !StringUtils.isBlank(escalationSplitDTO.getMobile())
				|| !StringUtils.isEmpty(escalationSplitDTO.getPinCode()) || !"0".equals(escalationSplitDTO.getState())
				|| !StringUtils.isBlank(escalationSplitDTO.getAddress())) {

			if (escalationDTO.getUserId() != null) {

				if (!StringUtils.isBlank(escalationSplitDTO.getUserId())) {
					
					escalationSplitDTO.setLastUpdatedBy(sessionDTO.getUserName());
					escalationSplitDTO.setLastUpdatedOn(LocalDateTime.now());
					escalationSplitDTO.setLastOperation(BaseFunctionalityEnum.EDIT_ESCALATION.name());
					
					encryptPersonalDetails(escalationSplitDTO);

					baseEscalationRepo.updateEscalationStg(escalationSplitDTO);
				} else {
					escalationSplitDTO.setLastOperation(BaseFunctionalityEnum.ADD_ESCALATION.name());
					
					encryptPersonalDetails(escalationSplitDTO);
					
					baseEscalationRepo.addEscalationStg(escalationSplitDTO);

				}
			} else {
				escalationSplitDTO.setLastOperation(BaseFunctionalityEnum.ADD_ESCALATION.name());
				
				encryptPersonalDetails(escalationSplitDTO);
				
				baseEscalationRepo.addEscalationStg(escalationSplitDTO);
			}
		}
	}





	@Override
	public String approveOrRejectEscalation(String memberId, String status, String remarks) {

		int status1 = 0;

		if (memberId == null) {
			throw new SettleNxtException("Member Id should not be empty", "");
		}

		BaseEscalationDTO escalationDTO = new BaseEscalationDTO();

		List<BaseEscalationDTO> escalationDtoList = baseEscalationRepo.getContactList(memberId);
		addOrUpdateEscalation(status, status1, escalationDTO, escalationDtoList);

		for (BaseEscalationDTO escalationDto : escalationDtoList) {
			escalationDto.setCheckerComments(remarks);
			escalationDto.setRequestState(status);
			
			escalationDto.setCreatedOn(LocalDateTime.now());
			escalationDto.setLastUpdatedBy(sessionDTO.getUserName());
			escalationDto.setLastUpdatedOn(LocalDateTime.now());
			if (status.equals(CommonConstants.REQUEST_STATE_APPROVED)) {
                escalationDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
            }else {
                escalationDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
            }
			
			baseEscalationRepo.updateEscalationStgState(escalationDto.getLastUpdatedBy(),
                    escalationDto.getLastUpdatedOn(), escalationDto.getRequestState(), escalationDto.getEscId(),
                     escalationDto.getCheckerComments(),escalationDto.getLastOperation());
			escalationDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
		}

		return escalationDTO.getStatusCode();
	}

	private void addOrUpdateEscalation(String status, int status1, BaseEscalationDTO escalationDTO,
			List<BaseEscalationDTO> escalationDtoList) {
		if (status.equals(CommonConstants.REQUEST_STATE_APPROVED)) {
			if (CollectionUtils.isNotEmpty(escalationDtoList)) {
				for (BaseEscalationDTO escalationDto : escalationDtoList) {
					escalationDto.setLastUpdatedBy(sessionDTO.getUserName());
					escalationDto.setLastUpdatedOn(LocalDateTime.now());
					status1 = baseEscalationRepo.updateEscalationMain(escalationDto);
					if (status1 == 0) {
						baseEscalationRepo.addEscalationMain(escalationDto);
						status1 = 1;
					}
				}

			} else {
				status1 = baseEscalationRepo.addContatctListMainTable(escalationDtoList);
			}

			if (status1 > 0) {
				escalationDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			}
		}
	}

	@Override
	public List<BaseEscalationDTO> getContactListPending() {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(CommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(CommonConstants.REQUEST_STATE_REJECTED);
		return baseEscalationRepo.getPendingForEscalationList(requestStateList);

	}

	@Override
	public List<BaseEscalationDTO> discardEscalationInfo(String memberId) {

		List<BaseEscalationDTO> escalationDto = baseEscalationRepo.getContactList(memberId);
		List<BaseEscalationDTO> escalationdtoMain = baseEscalationRepo.getContactListMain(memberId);
		if (!escalationdtoMain.isEmpty()) {

			for (BaseEscalationDTO escalationDtos : escalationdtoMain) {

				escalationDtos.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
				escalationDtos.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
				baseEscalationRepo.updateEscalation(escalationDtos);
			}

		} else {
			baseEscalationRepo.deleteDiscardedEntry(memberId);
		}
		
		decryptEscalationList(escalationDto);

		return escalationDto;
		

	}
	

	public String streetAddressValidation(String address) {
		String[] streeAddresArrs = null;
		if (StringUtils.isNotBlank(address)) {

			streeAddresArrs = address.trim().split("\\*");

		}
		if (streeAddresArrs != null) {
			StringBuilder streetAdd = new StringBuilder();
			for (String streetAddress:streeAddresArrs) {
				streetAdd.append(",");
				streetAdd.append(streetAddress);

			}
			streetAdd.replace(0, 1, "");
			return streetAdd.toString();
		}

		else {
			return null;

		}
	}

	@Override
	public BaseEscalationDTO updateApproveOrRejectBulk(String[] idArray, String status, String remarks) {

		BaseEscalationDTO escalation = new BaseEscalationDTO();

		List<String> memberIdList = Arrays.asList(idArray);
		List<BaseEscalationDTO> escalationToArr = baseEscalationRepo.fetchEscalationStgAppList(memberIdList);
		Map<String, List<BaseEscalationDTO>> escMap = escalationToArr.stream()
				.collect(Collectors.groupingBy(BaseEscalationDTO::getMemberId));
		for (String id:idArray) {

			try {
				List<BaseEscalationDTO> escalationDto = escMap.get(id);
				BaseEscalationDTO escalationEntryDto = escalationDto.get(0);
				if (escalationEntryDto == null) {
					throw new SettleNxtException("Exception occurred with memberIdList" + id, "");
				} else {
					String escalationEntryDto1 = approveOrRejectEscalation(
							String.valueOf(escalationEntryDto.getMemberId()), status, remarks);
					escalation.setStatusCode(escalationEntryDto1);
				}

			} catch (Exception ex) {

				throw new SettleNxtException("Exception for EscalationMatrixId" + id, "", ex);

			}
		}

		return escalation;
	}

	@Override
	public List<BaseEscalationDTO> getMemberListForReject() {
		return baseEscalationRepo.getMemberListForReject();
	}

	@Override
	public List<BaseEscalationDTO> getContactListStg(String pId) {
		
		List<BaseEscalationDTO> escalationList = baseEscalationRepo.getContactListStg(pId);
        decryptEscalationList(escalationList);
        return escalationList;
	}

}
