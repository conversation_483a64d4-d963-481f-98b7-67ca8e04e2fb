package org.npci.settlenxt.adminportal.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.ReasonCodeMasterRepository;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.FunctionCode;
import org.npci.settlenxt.portal.common.dto.ReasonCodeDto;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ReasonCodeServiceimpl implements ReasonCodeMasterService {

	private static final Logger log = LogManager.getLogger(ReasonCodeServiceimpl.class);
	@Autowired
	ReasonCodeMasterRepository reasonCodeMasterRepository;

	@Autowired
	SessionDTO sessionDTO;

	@Override
	public List<ReasonCodeDto> getReasonCode() {

		return reasonCodeMasterRepository.getReasonCodeList();
	}

	@Override
	public void addReasonCodeMaster(ReasonCodeDto reasonCodeDto) {

		reasonCodeDto.setCreatedOn(new Date());
		reasonCodeDto.setCreatedBy(sessionDTO.getUserName());
		reasonCodeDto.setLastUpdatedBy(sessionDTO.getUserName());
		reasonCodeDto.setLastUpdatedOn(new Date());
		reasonCodeDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		reasonCodeDto.setLastOperation(CommonConstants.ADD_REASON_CODE_LAST_OPERATION);

		reasonCodeMasterRepository.addReasonCode(reasonCodeDto);

	}

	@Override
	public ReasonCodeDto getReasonCodeDetail(String reasonCode) {

		return reasonCodeMasterRepository.getReasonCode(reasonCode);
	}

	@Override
	public ReasonCodeDto updateReasonCode(ReasonCodeDto reasonCodeDto) {

		reasonCodeDto.setLastUpdatedBy(sessionDTO.getUserName());
		reasonCodeDto.setLastUpdatedOn(new Date());
		reasonCodeDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		reasonCodeDto.setLastOperation(CommonConstants.EDIT_MCC_LAST_OPERATION);
		reasonCodeMasterRepository.updateReasonCodeDetail(reasonCodeDto);
		return reasonCodeDto;

	}

	@Override
	public List<ReasonCodeDto> getPendingReasonCode() {

		return reasonCodeMasterRepository.getPendingReasonCodeList();
	}

	@Override
	public ReasonCodeDto updateApproveOrRejectReasonCode(String reasonCode, String status, String remarks) {
		ReasonCodeDto reasonCodeDto = getReasonCodeDetail(reasonCode);

		reasonCodeDto.setRequestState(status);
		reasonCodeDto.setCheckerComments(remarks);

		if ("Approved".equals(status)) {
			reasonCodeDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			reasonCodeDto.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
		} else {
			reasonCodeDto.setRequestState(CommonConstants.REQUEST_STATE_REJECTED);
		}

		updateReasonCodeMasterStg(reasonCodeDto);

		if (reasonCodeDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {

			try {
				updateReasonCodeMaster(reasonCodeDto);

			} catch (Exception e) {

				throw new SettleNxtException("", "updateApproveOrRejectReasonCode", e);
			}
		}

		return reasonCodeDto;

	}

	private void updateReasonCodeMasterStg(ReasonCodeDto reasonCodeDto) {

		reasonCodeDto.setLastUpdatedOn(new Date());
		reasonCodeDto.setLastUpdatedBy(sessionDTO.getUserName());

		reasonCodeMasterRepository.updateReasonCodeStgState(reasonCodeDto);

	}

	private void updateReasonCodeMaster(ReasonCodeDto reasonCodeDto) {
		ReasonCodeDto reasonCodeDtoMain = reasonCodeMasterRepository
				.getApprovedReasonCode(reasonCodeDto.getReasonCode());
		if (ObjectUtils.isEmpty(reasonCodeDtoMain)) {
			reasonCodeDto.setCreatedOn(reasonCodeDto.getLastUpdatedOn());
			reasonCodeDto.setLastUpdatedBy(null);
			reasonCodeDto.setLastUpdatedOn(null);
			reasonCodeMasterRepository.saveReasonCode(reasonCodeDto);
		} else {
			reasonCodeDto.setLastUpdatedOn(reasonCodeDto.getLastUpdatedOn());
			reasonCodeDto.setLastUpdatedBy(sessionDTO.getUserName());
			reasonCodeMasterRepository.updateReasonCode(reasonCodeDto);
		}

		reasonCodeDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

	}

	@Override
	public ReasonCodeDto discardReasonCode(String reasonCode) {

		ReasonCodeDto reasonCodeDto = reasonCodeMasterRepository.getReasonCode(reasonCode);
		ReasonCodeDto reasonCodeDtoMain = reasonCodeMasterRepository.getApprovedReasonCode(reasonCode);

		if (reasonCodeDtoMain != null) {

			reasonCodeDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			reasonCodeDtoMain.setLastOperation("DISCARD");
			reasonCodeDtoMain.setLastUpdatedOn(new Date());
			reasonCodeMasterRepository.updateRequestStateReasonCodeStg(reasonCodeDtoMain);

		} else {
			reasonCodeMasterRepository.deleteDiscardedReasonCode(reasonCode);
		}

		return reasonCodeDto;

	}

	public List<FunctionCode> getFunctionCodeList() {

		return reasonCodeMasterRepository.getFunctionCodeList();
	}

	public List<ReasonCodeDto> getReasonCodeMasterList() {

		return reasonCodeMasterRepository.getReasonCodeMasterList();
	}

	@Override
	public List<ReasonCodeDto> showReasonCodeRules() {
		return reasonCodeMasterRepository.showReasonCodeRulesList(CommonConstants.REQUEST_STATE_APPROVED);
	}

	@Override
	public List<ReasonCodeDto> showPendingReasonCodeRules() {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(CommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(CommonConstants.REQUEST_STATE_REJECTED);
		return reasonCodeMasterRepository.showPendingReasonCodeRules(requestStateList);
	}

	@Override
	public List<CodeValueDTO> fetchActionCodeList() {
		return reasonCodeMasterRepository.fetchActionCodeList();
	}

	@Override
	public List<CodeValueDTO> fetchReasonCodeList() {
		return reasonCodeMasterRepository.fetchReasonCodeList();
	}

	@Override
	public void saveReasonCodeRules(ReasonCodeDto reasonCodeDto) {

		boolean result = checkDupReasonCodeRules(reasonCodeDto.getActionCode(), reasonCodeDto.getReasonCode(),
				reasonCodeDto.getFieldName(), reasonCodeDto.getRelationOperator());
		if (result) {
			throw new SettleNxtException("Reason Code Rules already configured",
					"Reason Code Rules already configured");
		}
		reasonCodeDto.setCreatedOn(new Date());
		reasonCodeDto.setCreatedBy(sessionDTO.getUserName());
		reasonCodeDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		reasonCodeDto.setLastOperation("A");
		reasonCodeDto.setStatus(CommonConstants.USER_ACTIVE_STATUS);
		if ("0".equalsIgnoreCase(reasonCodeDto.getLogicalReasonCode())) {
			reasonCodeDto.setLogicalReasonCode("");
		}
		reasonCodeMasterRepository.saveReasonCodeRulesStg(reasonCodeDto);
	}

	@Override
	public ReasonCodeDto updateReasonCodeRules(ReasonCodeDto reasonCodeDto) {
		reasonCodeDto.setLastUpdatedBy(sessionDTO.getUserName());
		reasonCodeDto.setLastUpdatedOn(new Date());
		reasonCodeDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		reasonCodeDto.setLastOperation("E");
		reasonCodeMasterRepository.updateReasonCodeRulesStg(reasonCodeDto);
		return reasonCodeDto;
	}

	@Override
	public ReasonCodeDto fetchReasonCodeRulesMain(String seqId) {
		return reasonCodeMasterRepository.fetchReasonCodeRulesMain(Integer.parseInt(seqId));
	}

	@Override
	public ReasonCodeDto fetchReasonCodeRulesStg(String seqId) {
		return reasonCodeMasterRepository.fetchReasonCodeRulesStg(Integer.parseInt(seqId));
	}

	@Override
	public ReasonCodeDto discardReasonCodeRules(String seqId) {

		ReasonCodeDto reasonCodeDto = fetchReasonCodeRulesMain(seqId);
		ReasonCodeDto reasonCodeDtoStg = fetchReasonCodeRulesStg(seqId);

		if (reasonCodeDto != null) {
			reasonCodeDto.setSeqIdInt(Integer.parseInt(seqId));
			reasonCodeDto.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			reasonCodeDto.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			reasonCodeDto.setLastUpdatedOn(new Date());
			reasonCodeMasterRepository.updateReasonCodeRulesStg(reasonCodeDto);
			return reasonCodeDto;
		} else {
			reasonCodeMasterRepository.deleteDiscardedReasonCodeRules(Integer.parseInt(seqId));
		}

		return reasonCodeDtoStg;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ReasonCodeDto approveReasonCodeRules(ReasonCodeDto reasonCodeDto, String status, String remarks) {
		reasonCodeDto.setSeqIdInt(Integer.parseInt(reasonCodeDto.getSeqId()));
		reasonCodeDto.setRequestState(status);
		reasonCodeDto.setCheckerComments(remarks);

		if ("A".equals(status)) {
			reasonCodeDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			reasonCodeDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			reasonCodeDto.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
		} else {
			reasonCodeDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
			reasonCodeDto.setRequestState(CommonConstants.REQUEST_STATE_REJECTED);
		}

		updateReasonCodeRulesStg(reasonCodeDto);

		if (reasonCodeDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
			// To update final table
			updateApprovedReasonCodeRules(reasonCodeDto);
			reasonCodeDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
		}

		return reasonCodeDto;
	}

	private void updateReasonCodeRulesStg(ReasonCodeDto reasonCodeDto) {
		reasonCodeDto.setLastUpdatedOn(new Date());
		reasonCodeDto.setLastUpdatedBy(sessionDTO.getUserName());

		reasonCodeMasterRepository.updateReasonCodeRulesState(reasonCodeDto);

	}

	private void updateApprovedReasonCodeRules(ReasonCodeDto reasonCodeDto) {
		ReasonCodeDto reasonCodeDb = reasonCodeMasterRepository
				.fetchReasonCodeRulesMain(Integer.parseInt(reasonCodeDto.getSeqId()));
		if (ObjectUtils.isEmpty(reasonCodeDb)) {
			reasonCodeDto.setCreatedOn(reasonCodeDto.getLastUpdatedOn());
			reasonCodeDto.setLastUpdatedBy(null);
			reasonCodeDto.setLastUpdatedOn(null);
			reasonCodeMasterRepository.saveReasonCodeRules(reasonCodeDto);
		} else {
			reasonCodeDto.setLastUpdatedOn(reasonCodeDto.getLastUpdatedOn());
			reasonCodeDto.setLastUpdatedBy(sessionDTO.getUserName());
			reasonCodeMasterRepository.updateReasonCodeRulesMain(reasonCodeDto);
		}

		reasonCodeDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
	}

	@Override
	public boolean checkDupReasonCodeRules(String actionCode, String reasonCode, String fieldName,
			String relationOperator) {
		StringBuilder fieldNameArr = new StringBuilder();
		String[] fieldArr = fieldName.split("\\|");
		for (String field:fieldArr) {
			fieldNameArr.append(",");
			fieldNameArr.append(field);

		}
		fieldNameArr = fieldNameArr.replace(0, 1, "");
		String x = fieldNameArr.toString();
		x = x.replace("|", "");

		if (!reasonCode.contains("_")) {
			reasonCode = reasonCode + "_" + "1";
		}

		int count = reasonCodeMasterRepository.fetchCountReasonCodeRules(actionCode, reasonCode, x, relationOperator);
		int countStg = reasonCodeMasterRepository.fetchCountReasonCodeRulesStg(actionCode, reasonCode, x,
				relationOperator);

		return count > 0 || countStg > 0;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public ReasonCodeDto updateApproveOrRejectBulkRcRules(String idList, String status, String remarks)
			throws SettleNxtException {
		String[] idArray = idList.split("\\|");
		ReasonCodeDto rcRulesData = new ReasonCodeDto();

		List<Integer> rcList = Arrays.stream(idArray).map(Integer::valueOf).collect(Collectors.toList());
		List<ReasonCodeDto> bulkDataToArr = reasonCodeMasterRepository.fetchPendingApprovalRcRules(rcList);
		Map<String, List<ReasonCodeDto>> rcMap = bulkDataToArr.stream()
				.collect(Collectors.groupingBy(ReasonCodeDto::getSeqId));

		for (String id:idArray) {

			try {
				List<ReasonCodeDto> rcRulesDto = rcMap.get(id);
				ReasonCodeDto rcRules = rcRulesDto.get(0);
				if (rcRules == null) {
					throw new SettleNxtException("Exception occurred with RecordId " + id, "");
				} else {
					approveReasonCodeRules(rcRules, status, remarks);
					rcRulesData.setStatusCode(rcRules.getStatusCode());
				}

			} catch (Exception ex) {
				throw new SettleNxtException("Exception for RecordId " + id, "", ex);

			}
		}

		return rcRulesData;
	}

	@Override
	public String updateBulkStgReasonCodeMaster(String reasonCodeList, String status) {

		try {
			String checkerComments = CommonConstants.RECORD_DESC_REJECTED;
			String reqState = CommonConstants.RECORD_REJECTED;
			if (StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
				checkerComments = CommonConstants.RECORD_DESC_APPROVED;
				reqState = "Approved";
			}
			String[] tranArray = reasonCodeList.split("\\|");

			for (String reasonCode : tranArray) {
				updateApproveOrRejectReasonCode(reasonCode, reqState, checkerComments);
			}
		} catch (Exception e) {
			return CommonConstants.RESULT_ERROR;
		}
		return CommonConstants.RESULT_SUCCESS;
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public String saveReasonCodeRulesList(List<ReasonCodeDto> reasonCodeList) {
		try {

			List<String> requestStateList = new ArrayList<>();
			requestStateList.add(CommonConstants.REQUEST_STATE_SUBMITTED);
			requestStateList.add(CommonConstants.REQUEST_STATE_APPROVED);
			for (ReasonCodeDto reasonCodeObj : reasonCodeList) {

				StringBuilder fieldName = new StringBuilder();
				String[] fieldArr = reasonCodeObj.getFieldName().split("\\|");
				for (String field:fieldArr) {
					fieldName.append(",");
					fieldName.append(field);

				}
				fieldName = fieldName.replace(0, 1, "");
				String x = fieldName.toString();
				x = x.replace("|", "");

				// Logtostate will have resoncode

				ReasonCodeDto fetchReasonCode = reasonCodeMasterRepository.getReasonCodeRulesId(requestStateList,
						reasonCodeObj.getLogToState(), reasonCodeObj.getActionCode(),
						reasonCodeObj.getRelationOperator(), reasonCodeObj.getFieldName());
				if (fetchReasonCode != null) {
					setReasonCodeState(reasonCodeObj, fetchReasonCode);
				} else {
					if (reasonCodeObj.getLogicalReasonCode().equals(CommonConstants.REASON_CODE_OP_AND)) {
						int index3 = 1;
						reasonCodeObj.setToState(
								reasonCodeObj.getReasonCode() + CommonConstants.REASON_CODE_KEY_SEPERATOR + index3);

					} else if (reasonCodeObj.getLogicalReasonCode().equals(CommonConstants.REASON_CODE_OP_OR)) {

						int index2 = 1;
						reasonCodeObj.setToState(
								reasonCodeObj.getReasonCode() + CommonConstants.REASON_CODE_KEY_SEPERATOR + index2);

					}

				}

				reasonCodeObj.setCreatedBy(sessionDTO.getUserName());
				reasonCodeObj.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
				reasonCodeObj.setCreatedOn(new Date());
				reasonCodeObj.setFieldName(x);
				reasonCodeObj.setLogicalReasonCode(reasonCodeObj.getReasonCode());
				reasonCodeObj.setReasonCode(reasonCodeObj.getToState());
				reasonCodeObj.setLastUpdatedBy(sessionDTO.getUserName());
				reasonCodeObj.setLastUpdatedOn(new Date());
				reasonCodeObj.setRequestState(BaseCommonConstants.REQUEST_STATE_SUBMITTED);

				reasonCodeMasterRepository.addReasonCodeRulesStg(reasonCodeObj);
			}
		} catch (Exception e) {
			log.error("Error while adding reason code rule: ", e);
			return CommonConstants.RESULT_ERROR;
		}
		return CommonConstants.RESULT_SUCCESS;
	}

	private void setReasonCodeState(ReasonCodeDto reasonCodeObj, ReasonCodeDto fetchReasonCode) {
		if (reasonCodeObj.getLogicalReasonCode().equals(CommonConstants.REASON_CODE_OP_AND)) {
			if (fetchReasonCode.getReasonCode().contains(CommonConstants.REASON_CODE_KEY_SEPERATOR)) {

				reasonCodeObj.setToState(fetchReasonCode.getReasonCode());
			}
		} else if (reasonCodeObj.getLogicalReasonCode().equals(CommonConstants.REASON_CODE_OP_OR) && fetchReasonCode.getReasonCode().contains(CommonConstants.REASON_CODE_KEY_SEPERATOR)) {
			
				String[] splitArr = StringUtils.split(fetchReasonCode.getReasonCode(),
						CommonConstants.REASON_CODE_KEY_SEPERATOR);
				int index = Integer.parseInt(splitArr[1]) + 1;
				reasonCodeObj.setToState(splitArr[0] + CommonConstants.REASON_CODE_KEY_SEPERATOR + index);
			
		}
	}

	@Override
	public String updateReasonCodeRulesList(List<ReasonCodeDto> reasonCodeDto) {

		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(CommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(CommonConstants.REQUEST_STATE_APPROVED);
		for (ReasonCodeDto reasonCodeObj : reasonCodeDto) {
			StringBuilder fieldName = new StringBuilder();
			String[] fieldArr = reasonCodeObj.getFieldName().split("\\|");
			for (String field:fieldArr) {
				fieldName.append(",");
				fieldName.append(field);

			}
			fieldName = fieldName.replace(0, 1, "");
			String x = fieldName.toString();
			x = x.replace("|", "");
			if ("Add".equals(reasonCodeObj.getType())) {

				// Logtostate will have resoncode

				if (reasonCodeObj.getLogicalReasonCode().equals(CommonConstants.REASON_CODE_OP_AND)) {

					reasonCodeObj.setToState(reasonCodeObj.getToState());

				}

				reasonCodeObj.setCreatedBy(sessionDTO.getUserName());
				reasonCodeObj.setLastOperation("Add Reason Code Rules");
				reasonCodeObj.setCreatedOn(new Date());
				reasonCodeObj.setFieldName(x);
				if (reasonCodeObj.getReasonCode().contains("_")) {
					reasonCodeObj.setLogicalReasonCode(reasonCodeObj.getReasonCode().split("_")[0]);
				} else {
					reasonCodeObj.setLogicalReasonCode(reasonCodeObj.getReasonCode());
				}
				reasonCodeObj.setReasonCode(reasonCodeObj.getToState());
				reasonCodeObj.setRequestState(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
				reasonCodeMasterRepository.addReasonCodeRulesStg(reasonCodeObj);
			}

			else if ("Edit".equals(reasonCodeObj.getType())) {
				reasonCodeObj.setFieldName(x);
				reasonCodeObj.setSeqIdInt(Integer.parseInt(reasonCodeObj.getSeqId()));
				reasonCodeObj.setLastUpdatedBy(sessionDTO.getUserName());
				reasonCodeObj.setLastUpdatedOn(new Date());
				reasonCodeObj.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
				reasonCodeObj.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
				reasonCodeMasterRepository.updateReasonCodeRulesStg(reasonCodeObj);

			}

		}
		return CommonConstants.RESULT_SUCCESS;
	}

	@Override
	public ReasonCodeDto fetchReasonCodeRulesStgBySeqId(String seqId) {

		return reasonCodeMasterRepository.fetchReasonCodeRulesStgBySeqId(Integer.parseInt(seqId));
	}

	@Override
	public List<ReasonCodeDto> getReasonCodeList() {
		List<ReasonCodeDto> reasoncodeList = new ArrayList<>();
		try {
			reasoncodeList = reasonCodeMasterRepository.getReasonCodeListstg();

		} catch (Exception e) {
			log.error("error while fetching all reason code form staging table {}" , e.getMessage());
		}
		return reasoncodeList;
	}

	@Override
	public List<ReasonCodeDto> getReasonCodewithReasonType() {

		return reasonCodeMasterRepository.getReasonCodewithReasonType();
	}
}
