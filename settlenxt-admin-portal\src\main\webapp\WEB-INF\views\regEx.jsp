<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>

<script>

	var NumberOnly= <spring:eval expression="@environment.getProperty('NumberOnly')" />;
    var DateFormat= <spring:eval expression="@environment.getProperty('DateFormat')" />;
	var Alphabet= <spring:eval expression="@environment.getProperty('Alphabet')" />;
	var streetaddress= <spring:eval expression="@environment.getProperty('streetaddress')"/>;
	var AlphabetWithSpace= <spring:eval expression="@environment.getProperty('AlphabetWithSpace')" />;
	var AlphaNumericNoSpace= <spring:eval expression="@environment.getProperty('AlphaNumericNoSpace')" />;
	var AlphaNumericWithSpace= <spring:eval expression="@environment.getProperty('AlphaNumericWithSpace')" />;
	var AlphaNumericWithSpaceDotHyphenAnd= <spring:eval expression="@environment.getProperty('AlphaNumericWithSpaceDotHyphenAnd')" />;
	var NumericwithPrecisionwith0= <spring:eval expression="@environment.getProperty('NumericwithPrecisionwith0')" />;
	var NumericwithPrecision= <spring:eval expression="@environment.getProperty('NumericwithPrecision')" />;
	var NumericwithPrecisionAmount= <spring:eval expression="@environment.getProperty('NumericwithPrecisionAmount')" />;
	var AlphaNumCommaHyphenUndSc= <spring:eval expression="@environment.getProperty('AlphaNumCommaHyphenUndSc')" />;
	var AlphaNumCommaHyphenUndScPoint= <spring:eval expression="@environment.getProperty('AlphaNumCommaHyphenUndScPoint')" />;
	var AlphaNumCommaHyphenUndScStar= <spring:eval expression="@environment.getProperty('AlphaNumCommaHyphenUndScStar')" />;
	var NumCommaStar= <spring:eval expression="@environment.getProperty('NumCommaStar')" />;
	var NumComma= <spring:eval expression="@environment.getProperty('NumComma')" />;
	var Gstin= <spring:eval expression="@environment.getProperty('Gstin')" />;
	var NumericsOnly= <spring:eval expression="@environment.getProperty('NumericsOnly')" />;
	var NumericsOnlyBtw10to12= <spring:eval expression="@environment.getProperty('NumericsOnlyBtw10to12')" />;
	var NumericsOnlyBtw10to15= <spring:eval expression="@environment.getProperty('NumericsOnlyBtw10to15')" />;
	var NumericsOnlyBtw8to12= <spring:eval expression="@environment.getProperty('NumericsOnlyBtw8to12')" />;
	var MailValidation= <spring:eval expression="@environment.getProperty('MailValidation')" />;
	var AlphaAndDot= <spring:eval expression="@environment.getProperty('AlphaAndDot')" />;
	var NumericBtwn8to20= <spring:eval expression="@environment.getProperty('NumericBtwn8to20')" />;
	var priorityVal= <spring:eval expression="@environment.getProperty('priorityVal')" />;
	var loginId= <spring:eval expression="@environment.getProperty('loginId')" />;
	var NumericwithPrecisionwith0From10to2= <spring:eval expression="@environment.getProperty('NumericwithPrecisionwith0From10to2')" />;
	var NumericwithPrecisionwith0From2to2= <spring:eval expression="@environment.getProperty('NumericwithPrecisionwith0From2to2')" />;
	var NumericwithPrecisionwith0From2to3= <spring:eval expression="@environment.getProperty('NumericwithPrecisionwith0From2to3')" />;
	var UserNameRegex= <spring:eval expression="@environment.getProperty('UserNameRegex')" />;
	var empIdRegex= <spring:eval expression="@environment.getProperty('empIdRegex')" />;
	var fieldValueRegex= <spring:eval expression="@environment.getProperty('fieldValueRegex')" />;
	var lkpTypeRegex= <spring:eval expression="@environment.getProperty('lkpTypeRegex')" />;
	var lkpValueRegex= <spring:eval expression="@environment.getProperty('lkpValueRegex')" />;
	var regex= <spring:eval expression="@environment.getProperty('regex')" />;
	var panLength= <spring:eval expression="@environment.getProperty('panLength')" />;
	var ZeroToHundredWithTwoDecimal=<spring:eval expression="@environment.getProperty('ZeroToHundredWithTwoDecimal')" />;
    var regexMap = {
			"NumberOnly":NumberOnly,
			"DateFormat":DateFormat,
			"Alphabet":Alphabet,
			"streetaddress":streetaddress,
			"AlphabetWithSpace":AlphabetWithSpace,
			"AlphaNumericNoSpace":AlphaNumericNoSpace,
			"AlphaNumericWithSpace":AlphaNumericWithSpace,
			"AlphaNumericWithSpaceDotHyphenAnd":AlphaNumericWithSpaceDotHyphenAnd,
			"NumericwithPrecisionwith0":NumericwithPrecisionwith0,
			"NumericwithPrecision":NumericwithPrecision,
			"NumericwithPrecisionAmount":NumericwithPrecisionAmount,
			"AlphaNumCommaHyphenUndSc":AlphaNumCommaHyphenUndSc,
			"AlphaNumCommaHyphenUndScPoint":AlphaNumCommaHyphenUndScPoint,
			"AlphaNumCommaHyphenUndScStar":AlphaNumCommaHyphenUndScStar,
			"NumCommaStar":NumCommaStar,
			"NumComma":NumComma,
			"Gstin":Gstin,
			"NumericsOnly":NumericsOnly,
			"NumericsOnlyBtw10to12":NumericsOnlyBtw10to12,
			"NumericsOnlyBtw10to15":NumericsOnlyBtw10to15,
			"NumericsOnlyBtw8to12":NumericsOnlyBtw8to12,
			"MailValidation":MailValidation,
			"AlphaAndDot":AlphaAndDot,
			"NumericBtwn8to20":NumericBtwn8to20,
			"priorityVal":priorityVal,
			"loginId":loginId,
			"NumericwithPrecisionwith0From10to2":NumericwithPrecisionwith0From10to2,
			"NumericwithPrecisionwith0From2to2":NumericwithPrecisionwith0From2to2,
			"NumericwithPrecisionwith0From2to3":NumericwithPrecisionwith0From2to3,
			"UserNameRegex":UserNameRegex,
			"empIdRegex":empIdRegex,
			"fieldValueRegex":fieldValueRegex,
			"lkpTypeRegex":lkpTypeRegex,
			"lkpValueRegex":lkpValueRegex,
			"regex":regex,
			"panLength":panLength,
			"ZeroToHundredWithTwoDecimal":ZeroToHundredWithTwoDecimal
	}
	



		
</script>