package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;

@Mapper
public interface RejectReasonCodeRuleRepository {

	List<RejectReasonCodeDTO> getRejectReasonCodeRule();

	void addRejectReasonCode(RejectReasonCodeDTO rejectReasonCodeDTO);

	RejectReasonCodeDTO getRejectReasonCode(int seqId);
	
	RejectReasonCodeDTO getRejectReasonCodeStg(int seqId);

	List<RejectReasonCodeDTO> getPendingRejectReasonCodeList();

	RejectReasonCodeDTO getPendingRejectReasonCode(int seqId);

	void updateRejectReasoncodeStg(RejectReasonCodeDTO rejectReasonCodeDTO);

	void saveRejectReasonCode(RejectReasonCodeDTO rejectReasonCodeDTO);

	void updateRejectReasonCode(RejectReasonCodeDTO rejectReasonCodeDTO);

	void deleteRejectReasonCode(int seqId);

	RejectReasonCodeDTO fetchSeqId();

	List<CodeValueDTO> fetchFuncCodeList();

	List<RejectReasonCodeDTO> getRejectReasonCodeRulemasterList();
	
}
