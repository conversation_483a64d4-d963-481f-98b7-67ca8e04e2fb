package org.npci.settlenxt.adminportal.dto;

import java.io.Serializable;

import org.npci.settlenxt.portal.common.dto.BaseDTO;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JGlobalMap
public class DisputeTransitionDTO extends BaseDTO implements Serializable {

	private static final long serialVersionUID = 1L;
	private int id;
	private String entity;
	private String currState;
	private String toState;
	private String logToState;
	private String fieldName;
	private String secFieldName;
	private String operator;
	private String relOp;
	private String fieldValue;
	private String fieldOperator;
	private String entityDesc;
	private String currStateDesc;
	private String toStateDesc;
	private String operatorDesc;
	private String fieldNameDesc;
	private String secFieldNameDesc;
	private String relOpDesc;
	private String fieldOperatorDesc;
	private String requestState;
	private String checkerComments;
	private String reqStateDesc;
}
