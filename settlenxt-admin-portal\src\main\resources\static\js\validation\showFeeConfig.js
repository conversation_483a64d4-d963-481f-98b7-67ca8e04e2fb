var showbuttonflag=0;



$(document).ready(function() {

	
	
	
	feeConfigMajorIds=[];
	feeConfigMinorIds=[];
	var majorMinor = $('#majorminor').val();

    
    $(document).ready(function () {
     	
    	
    $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                //If first column to be skipped to include the filter for the reasons line check box 
                if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   searchBoxFunc(colIdx, cell, title, api, majorMinor);
                   }
                });
            $('#tabnew_filter').hide();
           
        },
        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Fee Config',
                header: 'false',
                title: null,
                sheetName: 'Fee Config',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'Fee Config' ,
				header:'false', 
				title: null,
				sheetName:'Fee Config',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });
});

	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	
     $("#excelExport").on("click", function () {
    	        $(".buttons-excel").trigger("click");
    	    });
    	    
    	     $("#csvExport").on("click", function () {
    	        $(".buttons-csv").trigger("click");
    	    });
    	 
    	     $("#clearFilters").on("click", function () {
    	       $(".search-box").each(function() {
    				   $(this).val("");
    				     $(this).trigger("change");
    				});
    	    });
    	
   
 
	 showbuttonflag=0;
	
	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	
	

$("#selectAll").click(function(){
		
		$('#jqueryError4').hide();
        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
      
        var footerDataHeader = document.getElementById("detailsHeadersss");

        
        if(feeConfigMajorIds.length>0){
	footerDataHeader.innerHTML = feeConfigMajorIds.length+"     "+"records are selected";
	
	if( $('#selectAll').is(':checked') ){
 $("#toggleModal").modal('show');
        
}
else{
   $("#toggleModal").modal('hide');
        
}}else{
var i=0;
var feeConfigMajorIds2=[];
 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
	 feeConfigMajorIds2.push(this.value);
		                    i++;
		                });
if(feeConfigMajorIds2.length>0){


if(majorIdpending.length>0){
	footerDataHeader.innerHTML = majorIdpending.length+"     "+"records are selected";
	
	if( $('#selectAll').is(':checked') ){
 $("#toggleModal").modal('show');
        
}
else{
   $("#toggleModal").modal('hide');
        
}
}}}
	
});

	$("#selectAll1").click(function(){
		
		
		$('#jqueryError4').hide();
        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
      
        var footerDataHeader2 = document.getElementById("detailsHeaderss");

        
        if(feeConfigMinorIds.length>0){
	footerDataHeader2.innerHTML = feeConfigMinorIds.length+"     "+"records are selected";
	
	if( $('#selectAll1').is(':checked') ){
 $("#toggleModal1").modal('show');
        
}
else{
   $("#toggleModal1").modal('hide');
        
}}else{
var i=0;
var feeConfigMinorIds2=[];
 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
	 feeConfigMinorIds2.push(this.value);
		                    i++;
		                });
if(feeConfigMinorIds2.length>0){


if(minorIdpending.length>0){
	footerDataHeader2.innerHTML = minorIdpending.length+"     "+"records are selected";
	
	if( $('#selectAll1').is(':checked') ){
 $("#toggleModal1").modal('show');
        
}
else{
   $("#toggleModal1").modal('hide');
        
}
}}}
	
}
	);
	
	
	
});

function searchBoxFunc(colIdx, cell, title, api, majorMinor) {
	var cursorPosition = null;
    if (colIdx < actionColumnIndex) {
        var i = 0;
        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function(_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();

                ({ feeConfigMajorIds, i } = getSelectedMajorList(majorMinor, feeConfigMajorIds, i));
                ({ feeConfigMinorIds, i } = getSelectedMinorList(majorMinor, feeConfigMinorIds, i));


            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
    
}

function getSelectedMinorList(majorMinor, feeConfigMinorIds, i) {
    if (majorMinor === "minor") {
        feeConfigMinorIds = [];
        if (this.value != '') {

            $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
                feeConfigMinorIds.push(this.value);
                i++;
            });
        }
        else {
            feeConfigMinorIds = [];
        }
    }
    return { feeConfigMinorIds, i };
}

function getSelectedMajorList(majorMinor, feeConfigMajorIds, i) {
    if (majorMinor === "major") {
        feeConfigMajorIds = [];
        if (this.value != '') {

            $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
                feeConfigMajorIds.push(this.value);
                i++;
            });
        }
        else {
            feeConfigMajorIds = [];
        }
    }
    return { feeConfigMajorIds, i };
}

function getPendingMajorMinorList() {


	var url = '/feeConfigPendingForApproval';
	var data =  "significance,"
		+ $('#significance').val();
	postData(url, data);


}


function getMajorMinorList() {
	
	var url = "/showFeeConfig";
	var data = "significance," + $('#significance').val();
	postData(url, data);
}

function userAction(feeConfigId) {

	var url = "/showMinorConfig";
	var data = "feeConfigId," + feeConfigId ;
	postData(url, data);
}



function viewMajorMinorInfo(feeMajorId, type, requestId) {
	var url;
	if ($('#significance').val() == "M") {
		if (type == 'V')
			url = '/viewFeeMajor';
		else if (type == 'E')
			url = '/editFeeMajorMinor';
		else if (type == 'P')
			url = '/viewApproveFeeMajor';
		else if (type == 'R')
			url = '/viewRejFeeMajor';	
	} else if ($('#significance').val() == "N") {
		if (type == 'V')
			url = '/viewFeeMinor';
		else if (type == 'E')
			url = '/editFeeMajorMinor';
		else if (type == 'P')
			url = '/viewApproveFeeMinor';
		else if (type == 'R')
			url = '/viewRejFeeMinor';	
	}

	var data = "feeConfigId," + feeMajorId + ",significance," + $('#significance').val() + ",requestId," + requestId ;
	postData(url, data);
}





function submitForm(url) {
	var data =  "significance," + $('#significance').val();
	postData(url, data);
}




function ApproveorRejectBulk(type,action){
	
	var data;
	var url; 
	var sig= document.getElementById('significance').value;
	
	
	url = chooseUrl(sig, url);
	
	 var array = [];
		array = selectAllForApproval(action, array, sig);
	 
	var LoginIdList = "";
		for ( var arr of array) {
			LoginIdList = LoginIdList + arr + "|"
					;
		}
		
		if(array.length!=0){
	if(type=='A'){
		
		if(sig=='M'){
			
		data =  "status,"+"A"+",feeMajorIdList,"+LoginIdList+",remarks,"+"Approved";}
		else{
			data =  "status,"+"A"+",feeMinorIdList,"+LoginIdList+",remarks,"+"Approved";
		}

	}
	else if(type=='R'){
		
		if(sig=='M'){
			
		data =  "status,"+"R"+",feeMajorIdList,"+LoginIdList+",remarks,"+"Rejected";}
		else{
			data =  "status,"+"R"+",feeMinorIdList,"+LoginIdList+",remarks,"+"Rejected";
		}

	}
	

	postData(url, data);
		$('#errorStatus2').hide();
	$('#errorStatus2').html("");
	
	}else{
	
	$('#errorStatus2').html("Please select one or more records to bulk approve/reject records");
	$('#errorStatus2').show();
	}
	
}
function chooseUrl(sig, url) {
    if (sig == 'M') {
        url = '/approveFeeMajorBulkStatus';
    }
    else {
        url = '/approveFeeMinorBulkStatus';
    }
    return url;
}

function selectAllForApproval(action, array, sig) {
    if (action == 'no') {
        $("input:checkbox[name=type]:checked").each(function() {
            array.push($(this).val());
        });
    }
    else if (action == 'All') {



        if (sig == 'M') {
            if (feeConfigMajorIds.length > 0) {
                array = feeConfigMajorIds;
            }
            else {
                array = majorIdpending;
            }

        }
        else {
            if (feeConfigMinorIds.length > 0) {
                array = feeConfigMinorIds;
            }
            else {
                array = minorIdpending;
            }
        }
    }
    return array;
}

function deselectAll() {

	var e;
	var ele;
   $('#selectAll').prop('checked', false);
         ele=document.getElementsByName('type');  
   for(e of ele){  
       if(e.type=='checkbox')  
           e.checked=false;  
   }
   
   $('#selectAll1').prop('checked', false);
         ele=document.getElementsByName('type');  
   for(e of ele){  
       if(e.type=='checkbox')  
           e.checked=false;  
   }
   
}



