<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/addBinExclConfig.js"
	type="text/javascript"></script>

<script type="text/javascript">
	var binExclConfigValidationMessages = {};
	binExclConfigValidationMessages['participantName'] = "<spring:message code='binExcl.participant.validation.msg' javaScriptEscape='true' />";
	binExclConfigValidationMessages['binNumber'] = "<spring:message code='binExcl.bin.validation.msg' javaScriptEscape='true' />";
	binExclConfigValidationMessages['feeSelectionType'] = "<spring:message code='binExcl.feeType.validation.msg' javaScriptEscape='true' />";
	binExclConfigValidationMessages['fromMonth'] = "<spring:message code='binExcl.fromMonth.validation.msg' javaScriptEscape='true' />";
	binExclConfigValidationMessages['toMonth'] = "<spring:message code='binExcl.toMonth.validation.msg' javaScriptEscape='true' />";
	binExclConfigValidationMessages['fromYear'] = "<spring:message code='binExcl.fromYear.validation.msg' javaScriptEscape='true' />";
	binExclConfigValidationMessages['toYear'] = "<spring:message code='binExcl.toYear.validation.msg' javaScriptEscape='true' />";
	binExclConfigValidationMessages['pastMonthValidation'] = "<spring:message code='binExcl.pastMonthValidation.validation.msg' javaScriptEscape='true' />";
	binExclConfigValidationMessages['dateValidation'] = "<spring:message code='binExcl.dateValidation.validation.msg' javaScriptEscape='true' />";
</script>


<div class="panel panel-default no_margin">
	<div class="panel-heading clearfix">
		<c:if test="${not empty addNew}">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><label><spring:message
							code="binexcl.addscreen.title" /></label></span></strong>

		</c:if>
		<c:if test="${not empty editBin}">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><label><spring:message
							code="binexcl.editscreen.title" /></label></span></strong>

		</c:if>

	</div>
	<c:if test="${not empty addNew}">
		<c:url value="addBinExclConfig" var="submitBinExclConfigDetails" />
	</c:if>
	<c:if test="${not empty editBin}">
		<c:url value="updateBinExclConfig" var="submitExclBinConfigDetails" />
	</c:if>
	<div class="panel-body">
		<form:form onsubmit="return encodeForm(this);" method="POST"
			id="addEditBinExclConfig" modelAttribute="binExclDTO"
			action="${submitExclBinConfigDetails}" autocomplete="off">
			<br />
			<form:hidden path="excId" id="excId" name="excId"
				value="${binExclDTO.excId}" />
			<input type="hidden" id="binexclBin" name="binexclBin"
				value="${binExclDTO.bin}" />
			<input id="hparentPage" type="hidden" value="${parentPage}" />
			<c:if test="${not empty showbutton}">
				<div class="row">
					<div class="col-sm-3">

						<div class="form-group">
							<label><spring:message code="binexcl.participantName" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
								<form:select path="participantName" id="participantName"
									name="participantName" class="form-control"
									onChange="populateBinData()">
									<form:option value="" label="SELECT" />
									<form:options items="${participantList}"
										itemLabel="participantName" itemValue="participantId" />
								</form:select>
							</c:if>
							<c:if test="${not empty editBin}">
								<form:input path="participantName" id="participantName"
									value="${binExclDTO.bankName}" name="participantName"
									readonly="true" maxlength="50"
									cssClass="form-control medantory" />
							</c:if>
							<div id="errparticipantId">
								<span for="participantName" class="error"><form:errors
										path="participantName" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3 ">
						<div class="form-group">
							<label><spring:message code="binexcl.bin" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
								<form:select path="bin" id="binNumber" name="binNumber"
									class="form-control">
									<form:option value="" label="SELECT" />
									<form:options items="${binList}" itemLabel="binNumber"
										itemValue="binNumber" />

								</form:select>
							</c:if>
							<c:if test="${not empty editBin}">

								<form:input path="bin" id="binNumber" value="${binExclDTO.bin}"
									name="binNumber" readonly="true" maxlength="50"
									cssClass="form-control medantory" />
							</c:if>

							<div id="errbinId">
								<span for="binNumber" class="error"><form:errors
										path="binNumber" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3 ">
						<div class="form-group">
							<label><spring:message code="binexcl.baseorfeature" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
								<form:select id="feeSelectionType" name="feeSelectionName"
									path="feeSelectionType" class="form-control medantory">
									<form:option value="" label="SELECT" />
									<form:option lable="BASE" value="BASE" />
									<form:option lable="FEATURE " value="FEATURE" />
								</form:select>
							</c:if>
							<c:if test="${not empty editBin}">

								<form:select id="feeSelectionType" name="feeSelectionName"
									value="${binExclDTO.feeSelectionType}" path="feeSelectionType"
									class="form-control medantory">
									<form:option value="" label="SELECT" />
									<form:option lable="BASE" value="BASE" />
									<form:option lable="FEATURE " value="FEATURE" />
								</form:select>
							</c:if>
							<div id="errfeeSelectionType">
								<span for="feeSelectionType" class="error"><form:errors
										path="feeSelectionType" /></span>
							</div>
						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="binexcl.fromDate" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
								<form:select id="fromMonth" name="fromMonth" path="fromMonth"
									class="form-control medantory">
									<form:option value="" label="SELECT" />
									<form:options items="${beginningMonthList}"
										itemLabel="description" itemValue="code" />
								</form:select>
							</c:if>
							<c:if test="${not empty editBin}">

								<form:select path="fromMonth" id="fromMonth" name="fromMonth"
									maxlength="10" value="${binExclDTO.fromMonth}"
									cssClass="form-control medantory">
									<form:options items="${beginningMonthList}" itemValue="code"
										itemLabel="description" />
								</form:select>
							</c:if>
							<div id="errfromMonth">
								<span for="fromMonth" class="error"><form:errors
										path="fromMonth" /></span>
							</div>



						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="binexcl.year" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
								<form:select id="fromYear" name="fromYear" width="50%"
									path="fromYear" class="form-control medantory">
									<form:option value="" label="SELECT" />
									<form:options items="${yearList}" />
								</form:select>
							</c:if>
							<c:if test="${not empty editBin}">
								<form:select id="fromYear" name="fromYear" path="fromYear"
									class="form-control medantory">
									<form:option value="" label="SELECT" />
									<form:option value="${binExclDTO.fromYear}">
									</form:option>
									<form:options items="${yearList}" />
								</form:select>

							</c:if>

							<div id="errfromYear">
								<span for="fromYear" class="error" style="color: red;"><form:errors
										path="fromYear" /></span>
							</div>


						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="binexcl.toDate" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
								<form:select id="toMonth" name="toMonth" width="50%"
									path="toMonth" class="form-control medantory">
									<form:option value="" label="SELECT" />
									<form:options items="${endingMonthList}"
										itemLabel="description" itemValue="code" />
								</form:select>
							</c:if>
							<c:if test="${not empty editBin}">
								<form:select id="toMonth" name="toMonth" path="toMonth"
									class="form-control medantory" value="${binExclDTO.fromMonth}">
									<form:option value="" label="SELECT" />
									<form:options items="${endingMonthList}"
										itemLabel="description" itemValue="code" />
								</form:select>

							</c:if>
							<div id="errtoMonth">
								<span for="toMonth" class="error"><form:errors
										path="toMonth" /></span>
							</div>

						</div>
					</div>



					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="binexcl.year" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
								<form:select id="toYear" name="toYear" path="toYear"
									class="form-control medantory">
									<form:option value="" label="SELECT" />
									<form:options items="${yearList}" />
								</form:select>
							</c:if>
							<c:if test="${not empty editBin}">
								<form:select id="toYear" name="toYear" path="toYear"
									class="form-control medantory" value="${binExclDTO.toYear}">
									<form:option value="" label="SELECT" />
									<form:options items="${yearList}" />
								</form:select>

							</c:if>
							<div id="errtoYear">
								<span for="toYear" class="error"><form:errors
										path="toYear" /></span>
							</div>
						</div>
					</div>
				</div>
			</c:if>


			<c:if test="${empty showbutton}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default no_margin">
							<div class="panel-body">
								<table class="table table-striped" style="font-size: 12px">
									<caption style="display: none;">Bin Exclusion</caption>
									<thead style="display: none;">
										<th scope="col"></th>
									</thead>
									<tbody>
										<tr>
											<td><label><spring:message
														code="binexcl.participantName" /><span style="color: red"></span></label></td>
											<td>${binExclDTO.bankName}</td>
											<td><label><spring:message
														code="binexcl.baseorfeature" /><span style="color: red"></span></label></td>
											<td>${binExclDTO.feeSelectionType}</td>
											<td></td>
											<td></td>
										</tr>
										<tr>
											<td><label><spring:message code="binexcl.bin" /><span
													style="color: red"></span></label></td>
											<td>${binExclDTO.bin}</td>
											<td><label><spring:message
														code="binexcl.fromDate" /><span style="color: red"></span></label></td>
											<td>${binExclDTO.fromDate}</td>
											<td><label><spring:message code="binexcl.toDate" /><span
													style="color: red"></span></label></td>
											<td>${binExclDTO.toDate}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</c:if>


			<div class="col-sm-12 bottom_space">
				<hr />
				<div style="text-align: center">
					<c:if test="${not empty addNew}">
						<c:if test="${not empty addReset}">
							<button type="button" value="Submit" class="btn btn-success"
								onclick="resetAction();">
								<spring:message code="binexcl.reset" />
							</button>
						</c:if>

						<c:if test="${not empty showbutton}">
							<button type="button" id="BAddSubmit" value="Submit"
								class="btn btn-success" onclick="validateAddEditForm('A');">
								<spring:message code="msg.lbl.submit" />
							</button>
						</c:if>
					</c:if>

					<sec:authorize access="hasAuthority('Edit Bin Exclusion Config')">
						<c:if test="${not empty editBin}">
							<c:if test="${not empty showbutton}">
								<button type="button" id="BEditSubmit" value="Submit"
									class="btn btn-success" onclick="validateAddEditForm('E');">
									<spring:message code="msg.lbl.update" />
								</button>
							</c:if>
						</c:if>

						<c:if
							test="${binExclDTO.requestState eq 'R'and not empty showbutton}">
							<button type="button" class="btn btn-danger"
								onclick="postDiscardBinAction('/discardRejectedBinExclEntry');">
								<spring:message code="binexcl.discardBtn" />
							</button>

							<button type="button" class="btn btn-danger"
								onclick="userAction1('N','/binPendingForApproval');">
								<spring:message code="binexcl.backBtn" />
							</button>
						</c:if>
					</sec:authorize>

					<c:if test="${binExclDTO.requestState  ne 'R'}">
						<c:if test="${parentPage  eq 'approvalTab'}">
							<button type="button" class="btn btn-danger"
								onclick="userAction1('N','/binPendingForApproval');">
								<spring:message code="binexcl.backBtn" />
							</button>
						</c:if>
						<c:if test="${parentPage  ne 'approvalTab'}">
							<button type="button" class="btn btn-danger"
								onclick="userAction1('N','/showBinExclusion');">
								<spring:message code="binexcl.backBtn" />
							</button>
						</c:if>

					</c:if>
				</div>
			</div>
		</form:form>
	</div>
</div>

