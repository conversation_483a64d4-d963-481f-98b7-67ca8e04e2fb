package org.npci.settlenxt.adminportal.service;


import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.Year;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.cache.SysParams;
import org.npci.settlenxt.adminportal.config.kafka.utils.KafkaUtilService;
import org.npci.settlenxt.adminportal.dto.MoveReportDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.model.MoveReportModel;
import org.npci.settlenxt.adminportal.repository.MoveReportRepository;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import io.vertx.core.json.JsonObject;

@Service
public class MoveReportServiceImpl implements MoveReportService {

	private static final String MONTHLY_REPORTS_FOLDER_NAME = "MonthlyReports";

	private static final String SOURCE_BASE_FOLDER_FOR_MOVEMENT = "SOURCE_BASE_FOLDER_FOR_MOVEMENT";

	public static final Logger log = LogManager.getLogger(MoveReportServiceImpl.class);

	@Autowired
	MoveReportRepository moveReportRepository;

	@Autowired
	SessionDTO sessionDTO;

	@Autowired
	Environment environment;

	@Autowired
	KafkaUtilService kafkaUtilService;

	@Autowired
	SysParams sysParams;

	private String fileAccumulatorGenericFileCopyTopic;

	private static final String MONTHLY_REPORTS = "MonthlyReports";
	private static final String PULL_COMPLETED = "PULL_STARTED";

	private static final String COMPLETED = "COMPLETED";

	@PostConstruct
	void init() {
		fileAccumulatorGenericFileCopyTopic = environment.getProperty("kafka.topic.reportMovement.signing");
	}

	@Override
	public void saveMoveReportToDatabase(MoveReportDTO moveReportDTO, String reportNetwork, String year, String month,
			MoveReportModel moveReportModel) {

		String basePath = sysParams.getSystemKeyValue(SOURCE_BASE_FOLDER_FOR_MOVEMENT, reportNetwork);
		String destPath = moveReportRepository.getDestPath(MONTHLY_REPORTS);

		int yr = getYear(year);

		String yyyymm = yr + "-" + month;
		String path = basePath + yyyymm;

		unzip(basePath, path); // unzip all the files in source path
		deleteZipFile(basePath); // deletes the zip file after unzipping it in source path

		try {
			path=new File(path).getCanonicalPath();
		}catch(Exception e) {
			throw new SettleNxtException("Error in reading file", "",e);
		}
		
		
		try {
			File directory = new File(path);
			
			if (directory.exists()) {

				File[] files = directory.listFiles();
				if (files != null && files.length == 0) {
					throw new SettleNxtException("Directory exists but is empty", "");
				} else {
					
					String[] date = {year, month};
					moveReportRequest(moveReportDTO, reportNetwork, date, moveReportModel, files, path,
							destPath);
				}
			}

		} catch (Exception ex) {

			throw new SettleNxtException("Directory does not exists", "",ex);

		}

	}

	private void deleteZipFile(String basePath) {

		File directory = new File(basePath);
		File[] zipFiles = directory.listFiles((dir, name) -> name.toLowerCase(Locale.ROOT).endsWith(".zip"));
		for (File filepath : zipFiles) {
			try {
				Files.delete(filepath.toPath());
				log.info("File is Deleted ");
			} catch (IOException e) {
				log.info("fail to delete {}", e.getMessage());
			}

		}

	}

	public int getYear(String year) {
		int yr = 0;

		if ("Current Year".equalsIgnoreCase(year)) {
			yr = Year.now().getValue();
		} else {
			int currentYear = Year.now().getValue();
			yr = currentYear - 1;
		}
		return yr;
	}

	private void unzip(String zipPath, String destPath) {

		try {

			checkIfSrcIsEmpty(destPath); // check if src path is empty and delete if files are present

			File destDir = new File(destPath);

			File directory = new File(zipPath);
			File[] zipFiles = directory.listFiles((dir, name) -> name.toLowerCase(Locale.ROOT).endsWith(".zip"));
			zipPath = zipPath + zipFiles[0].getName();

			byte[] buffer = new byte[1024];
			try(ZipInputStream zis = new ZipInputStream(new FileInputStream(zipPath))) {

			ZipEntry zipEntry = zis.getNextEntry();

			while (zipEntry != null) {
				File newFile = newFile(destDir, zipEntry);
				if (zipEntry.isDirectory()) {
					errorInDirectoryCreation(newFile);
				} else {
					// fix for Windows-created archives
					File parent = newFile.getParentFile();
					errorInDirectoryCreation(parent);
					
					try(FileOutputStream fos = new FileOutputStream(newFile)){

					// write file content

					int len;
					while ((len = zis.read(buffer)) > 0) {
						fos.write(buffer, 0, len);
					}
					
				}
				zipEntry = zis.getNextEntry();
			}
			}

			zis.closeEntry();
			
		} }catch (IOException ex) {
			log.info("IOException : ", ex);
		}

	}

	private void errorInDirectoryCreation(File newFile) throws IOException {
		if (!newFile.isDirectory() && !newFile.mkdirs()) {
			throw new IOException("Failed to create directory " + newFile);
		}
	}

	public static File newFile(File destinationDir, ZipEntry zipEntry) throws IOException {
		File destFile = new File(destinationDir, zipEntry.getName());

		String destDirPath = destinationDir.getCanonicalPath();
		String destFilePath = destFile.getCanonicalPath();

		if (!destFilePath.startsWith(destDirPath + File.separator)) {
			throw new IOException("Entry is outside of the target dir: " + zipEntry.getName());
		}

		return destFile;
	}

	private void checkIfSrcIsEmpty(String basePath) {
		File folder = new File(basePath);

		if (!folder.exists()) {
			folder.mkdir();
		}
		try {
			if (folder.list().length != 0) {
				FileUtils.cleanDirectory(folder);
			}

		} catch (Exception ex) {
			log.info("IO Exception :", ex);
		}
	}

	@Override
	public void moveReportRequest(MoveReportDTO movereportDTO, String reportNetwork, String[] date,
			MoveReportModel moveReportModel, File[] files, String path, String baseDestinationPath) {
		
		String year = date[0];
		String month = date[1];

		int yr = getYear(year);

		DateTimeFormatter format = DateTimeFormatter.ofPattern("dd-MM-yyyy");
		String finaldate = month + "-" + yr;

		finaldate = "01" + "-" + finaldate;
		LocalDate lt = LocalDate.parse(finaldate, format).plusMonths(1);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ddMMyy");
		String formattedString = lt.format(formatter);

		moveReportModel.setCycleDate(formattedString);

		moveReportModel.setCycleNumber(getLastCycleForReportNetwork(reportNetwork));

		moveReportModel.setSettlementProductId(reportNetwork);
		moveReportModel.setReportName(MONTHLY_REPORTS);
		moveReportModel.setReportType(MONTHLY_REPORTS);

		moveReportModel.setFileType(MONTHLY_REPORTS);
		moveReportModel.setInstanceId(environment.getProperty("INSTANCE_ID"));
		moveReportModel.setSiteId(environment.getProperty("SITE_ID"));
		moveReportModel.setReportStatus(COMPLETED);
		moveReportModel.setSrcPath(path);
		moveReportModel.setStatus(PULL_COMPLETED);

		String yyyymm = yr + "-" + month;
		for (File fileToBeMoved : files) {
			String[] filename = fileToBeMoved.getName().split("_");
			String participantId = filename[0];
			moveReportModel.setGuid(UUID.randomUUID());
			String destinationPath = Utils.generateDelimiteredString(File.separator, baseDestinationPath, reportNetwork,
					MONTHLY_REPORTS_FOLDER_NAME, yyyymm, participantId);
			moveReportModel.setDestPath(destinationPath);
			moveReportModel.setParticipantId(participantId);
			moveReportModel.setFileName(fileToBeMoved.getName());
			moveReportRepository.insertProductReportStatus(moveReportModel);
			prepareMoveReportObj(moveReportModel);

		}
	}

	@Override
	public List<MoveReportDTO> getReportList() {
		return moveReportRepository.getReportList();
	}

	@Override
	public void addDefaultData(MoveReportDTO moveReportDTO, Date fromDateStr, Date toDateStr, String reportNetwork) {
		moveReportDTO.setFromDateStr(fromDateStr);
		moveReportDTO.setToDateStr(toDateStr);
		moveReportDTO.setReportNetwork(reportNetwork);
	}

	@Override
	public List<MoveReportDTO> searchReportList(MoveReportDTO movereportDTO) {
		boolean isRepoNet = true;
		if ("0".equals(movereportDTO.getReportNetwork()) || movereportDTO.getReportNetwork() == null)
			{
			isRepoNet = false;
			}
		movereportDTO.setToDateStr(DateUtils.addSeconds(movereportDTO.getToDateStr(), 86399));
		return moveReportRepository.searchReportList(movereportDTO, isRepoNet);
	}

	private String getLastCycleForReportNetwork(String reportNetwork) {				//network specific last cycle to be set
		List<String> allCycles = moveReportRepository.getAllCyclesByProductId(reportNetwork);
		int maxCycleNum = 0;
		String lastCycleName = "";
		try {
			if (!CollectionUtils.isEmpty(allCycles)) {
				for (String cycleName : allCycles) {
					int cycleNumber = NumberUtils.toInt(StringUtils.substring(cycleName, 1));
					if (cycleNumber > maxCycleNum) {
						lastCycleName = cycleName;
						maxCycleNum = cycleNumber;
					}
				}
			}

		} catch (Exception ex) {
			throw new SettleNxtException("No Cycles Available", "",ex);

		}

		return lastCycleName;
	}

	private void publishKafkaMessage(JsonObject moveReport) {

		log.info("move report published json {}", moveReport);

		kafkaUtilService.sendAsync(fileAccumulatorGenericFileCopyTopic, moveReport.toString());
	}

	private void prepareMoveReportObj(MoveReportModel moveReportModel) {
		JsonObject jObj = new JsonObject();
		jObj.put("guid", moveReportModel.getGuid().toString());
		jObj.put("cycleDate", moveReportModel.getCycleDate());
		jObj.put("cycleNumber", moveReportModel.getCycleNumber());
		jObj.put("productCode", moveReportModel.getSettlementProductId());
		jObj.put("reportName", moveReportModel.getReportName());
		jObj.put("reportType", moveReportModel.getReportType());
		jObj.put("participantId", moveReportModel.getParticipantId());
		jObj.put("fileType", moveReportModel.getFileType());
		jObj.put("instanceId", moveReportModel.getInstanceId());
		jObj.put("siteId", moveReportModel.getSiteId());
		jObj.put("fileName", moveReportModel.getFileName());
		jObj.put("reportStatus", moveReportModel.getReportStatus());
		jObj.put("srcPath", moveReportModel.getSrcPath());
		jObj.put("destPath", moveReportModel.getDestPath());
		jObj.put("auditEntryRequired", "Y");
		publishKafkaMessage(jObj);
	}

	@Override
	public List<MoveReportDTO> getMonthFromLookup() {
		return moveReportRepository.getMonthFromLookup();
	}

	@Override
	public List<CodeValueDTO> getMonths() {
		return moveReportRepository.getMonths();
	}

}
