package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class ActionCodeDTO {

	private int mti;
	private int actionCodeId;
	private int funcCode;
	private String actionCode;
	private String actionCodeDesc;
	private String funcCodeDesc;
	private String raisedBy;
	private String requestState;
	private String lastOperation;
	private String checkerComments;
	private String status;
	private String statusCode;
	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private String addEditFlag;
	private int tatPeriod;
	private String tatPeriodDayType;
	private String transitionActionCode;
	private String allowedActncdToRemove;
	private String capAmtCalReq;
	private String defaultReasonRejCode;
}