@charset "utf-8";
/* CSS Document */



hr {
    margin-top: 10px;
    margin-bottom: 10px;
    border: 0;
    border-top: 1px solid #eee;
}

#footer{color:black;}


body {
	background: #f4f3ef;
}

.bs-example {
    margin: 15px;
}


.alert {
   
    margin-bottom: 15px;padding: 10px;
   
}

#alert{ margin-bottom: 10px;}


 
    .panel-title .glyphicon{
        font-size: 14px; float:right;
    }


.nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover {
    color: #555;
    cursor: default;
    background-color: #fff;
    border: 1px solid #bbb;
    border-bottom-color: transparent;
}
.nav-tabs {
    border-bottom: 1px solid #bbb;
}

.tab-content {
    padding: 10px 6px 10px 6px;
    background: #fff;
    border: solid 1px #bbb;
    border-top: 0;
}

.panel-default>.panel-heading {
    color: #333;
    background-color: #ecf4ff;
    border-color: #c2d1e8;
}

.panel-default>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #c2d1e8;
}

.panel-group .panel-heading+.panel-collapse>.list-group, .panel-group .panel-heading+.panel-collapse>.panel-body {
    border-top: 1px solid #c2d1e8;
}

.panel-default {
    border-color: #c2d1e8;
}
.panel-title>.small, .panel-title>.small>a, .panel-title>a, .panel-title>small, .panel-title>small>a {
    color: inherit;
    font-weight: 600;
    font-size: 13px;
}

/**********************Breadcrum**************************/

.margin_bottom{margin-bottom: 10px;}

.red{color:red}

.page-title h4{
	    font-size: 24px;
}


.breadcrumb {
    padding: 8px 15px;
    /* margin-bottom: 20px; */
    list-style: none;
    /* background-color: #f5f5f5; */
    /* border-radius: 4px; */
}

.breadcrumbs {
    background-color: #fff;
    display: inline-block;
    margin-top: 0;
    padding: 0 5px;
    width: 100%;box-shadow: 0 2px 2px rgba(204, 197, 185, 0.5);
}

.page-header {
   /* min-height: 50px;*/
    margin: 0px;
    padding: 0px 15px;
    background: #ffffff;
    border-bottom: 0px;
}

.float-right {
    float: right!important;
}

.page-header .breadcrumb {
    margin: 0px;
    padding: 6.5px 0;
    background: #fff;
    text-transform: capitalize;
}

.text-right {
    text-align: right!important;
}

/************************Breadcrum Ends Here***************/




/************************navigation css********************/

.header .nav-tabs {
    border-bottom: 0px solid #ddd;
    float: right;
}

.navbar {
	border-radius: 0px;
}
.navbar-default {
	background-color: #1b327e;
	border-color: #1b2b55;
}
.navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:focus, .navbar-default .navbar-nav>.active>a:hover {
	color: #fff;
	background-color: #324b9e;
}
.navbar-default .navbar-nav>li>a {
	color: #a0acdc;
}
.navbar-default .navbar-nav>.open>a, .navbar-default .navbar-nav>.open>a:focus, .navbar-default .navbar-nav>.open>a:hover {
	color: #f5f7fb;
	background-color: #324b9e;
}
.navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
	color: #f5f7fb;
	background-color: transparent;
}
.navbar-nav>li>a {
	padding-top: 9px;
	padding-bottom: 9px;
}
.navbar {
	position: relative;
	min-height: 40px;
	margin-bottom: 10px;
	border: 1px solid transparent;
}

.text-right .nav-tabs>li>a:hover {
    border-color: #fff #fff #fff;
}
.text-right .nav>li>a:focus, .text-right .nav>li>a:hover {
    text-decoration: none;
    background-color: #fff;
}
.text-right .nav-tabs>li>a {
    margin-right: 2px;
    line-height: 1.42857143;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0;
    padding-left: 0px;
}

.text-right .nav .open>a, .text-right .nav .open>a:focus, .text-right .nav .open>a:hover {
    background-color: #fff;
    border-color: #ffffff;
}

.dropdown-menu>li>a {
    display: block;
    padding: 5px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
}


.body-content .nav-tabs>li>a {
    margin-right: 2px;
    line-height: 1.42857143;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0;
    background: #bbb;
    color: #403e3e;
}

.nav-tabs>li.active>a {
    color: #555;
    cursor: default;
    background-color: #fff;
    border: 1px solid #bbb;
    border-bottom-color: transparent;
    letter-spacing: 0.7px;
}


/**********************navigation css ends here************/


/************************Card Css*******************/

.card-white {
    border-radius: 2px;
    box-shadow: 0 2px 2px rgba(204, 197, 185, 0.5);
    background-color: #FFFFFF;
    color: #252422;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
    padding: 15px;
    border: solid 1px #bcd1f1;
}

.card {
   
    background-color: #fff;
    margin-bottom: 10px;
    border: 1px solid #bcd1f1;border-radius: 3px;
   /* box-shadow: 0 2px 2px rgba(204, 197, 185, 0.5);*/
}

.cardError {
   background-color: #fff;
   margin-bottom: 10px;
   /* border: 1px solid #bcd1f1; */
   border-radius: 3px;
   /* box-shadow: 0 2px 2px rgba(204, 197, 185, 0.5);*/
}


.card .card-header {
    padding: 6px 10px;
    background-color: #ecf4ff;
    border-bottom: 1px solid #bcd1f1 !important;
}

.card-header:first-child {
    border-radius: calc(.25rem - 1px) calc(.25rem - 1px) 0 0;
}

.card .card-title {
    margin: 0;
    color: #575962;
    font-size: 15px;
    font-weight: 600;
    /* line-height: 1.6; */
}

.card .card-body {
    padding: 6px 10px 5px 12px;
}

label {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: 700;
}


.form-group label, .form-check label {
    margin-bottom: .5rem !important;
    color: #000000 !important;
    font-weight: 400;
    font-size: 12.5px;

}
div.form-group.fee_list {
    display: flex;
    flex-direction: column;

}
div.form-group.fee_list > label {
    width: 30%;
    margin-right: 1rem;
    word-wrap: break-word;
}
div.form-group.fee_list > select, div.form-group.fee_list > input {
    width: 75%;
}
div.col-sm-12.form_container{
    display: flex;
    justify-content: space-around;
}


.form-inline .form-group label, .form-check label {
    margin-bottom: .5rem !important;
    color: #000000 !important;
    font-weight: 600;
    font-size: 12.5px;
	width:50%;
}
.form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
    width: 100%;
}

.form-inline .form-group .form-control {
    border: 0;
	box-shadow: none;}

.form-inline .form-group input {
   color: #676363;
}

.modal-footer {
    padding: 10px;
    text-align: center;
    border-top: 1px solid #e5e5e5;
}



.card .card-action {
    padding: 8px 15px 8px 15px;
    background-color: transparent;
    line-height: 30px;
    border-top: 1px solid #ebedf2 !important;
    font-size: 14px;
}

.form-control {
 border: 1px solid #c2d1e8;
}


.btn {
    padding: 5px 15px;
  }


.form-control {
    height: 32px;
    padding: 5px 10px;
    font-size: 12.5px;
    color: #000;
    border-radius: 3px;
}

/********************************************************/

/**********************************Content********************************/

.table>thead>tr>th {
    padding: 5px;
  font-size: 12.5px;
}

.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
    padding: 6px;
   font-size:13px;
}

table.dataTable td a {
    font-weight: 600;
    margin: 0px 4px 0px 4px;
}


.height-min{
min-height:650px;	
}


.page-title{margin-top:0px;}

.profile {
    width: 45px !important;
    padding: 8px;
}

.pagination>.active>a, .pagination>.active>a:focus, .pagination>.active>a:hover, .pagination>.active>span, .pagination>.active>span:focus, .pagination>.active>span:hover {
    z-index: 3;
    color: #fff;
    cursor: default;
    background-color: #1b327e;
    border-color: #1b327e;
}

.pagination>li>a, .pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 12px;
    margin-left: -1px;
    line-height: 1.42857143;
    color: #1b327e;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
}
    
.table>thead>tr>th {
    vertical-align: bottom;
    border-bottom: 2px solid #c2d1e8;
    /* color: #1b2b56 !important; */
    /* color: #9496a1; */
    color: #212529;
    font-weight: 600;
    background: #ecf4ff;
    /* background-color: rgba(0,0,0,.03); */
}

.btn-primary {
    color: #fff;
    background-color: #2986c9;
    border-color: #2986c9;
}
.btn-primary:hover {
    color: #fff;
    background-color: #58b3f4;
    border-color: #58b3f4;
}

.btn-primary.focus, .btn-primary:focus {
    color: #fff;
    background-color: #2986c8;
    border-color: #2986c8;
}

.btn-primary.active.focus, .btn-primary.active:focus, .btn-primary.active:hover, .btn-primary:active.focus, .btn-primary:active:focus, .btn-primary:active:hover, .open>.dropdown-toggle.btn-primary.focus, .open>.dropdown-toggle.btn-primary:focus, .open>.dropdown-toggle.btn-primary:hover {
    color: #fff;
    background-color: #2986c8;
    border-color: #2986c8;
}

.btn {
 border-radius: 2px;
 /* text-transform: uppercase; */
 font-size: 11.5px;
 letter-spacing: 0.8px;
 margin-right: 2px;
}


.form-group button{margin-top: 26px;}
.search_card{
	background: #ecf4ff;
	border:solid 1px #bcd1f1;
}

.welcome_txt {
    font-weight: 300;
   margin-right: 15px;
    text-align: right;
 
    position: relative;
    top: 0px;
}

.text-right .dropdown-menu {
    right: 0px;
    left: auto;
}

.header{background: #fff;padding:10px;}

.npci {
    width: 140px;
    margin-left: 0PX;
}

.upi {
    width: 120px;
    margin-left: 15px;
}

.body-content {
    padding: 5px 0px 10px 0px;
}

.modal-title {
    margin: 0;
    line-height: 1.42857143;
    font-weight: 600;
}

.modal-body {
    position: relative;
    padding: 10px;
    background: #f4f3ef;
}

/***********************Content css ends here****************************/

/*********************footer css***************************/

.footer {
	position: relative;
	bottom: 0;
	width: 100%;
	height: 60px;
	background-color: #CFCFCF;
}
.container .text-muted {
	margin: 20px 0;
}



/*********************footer css ends here***************************/

@media (max-width: 767px)
.navbar-default .navbar-nav .open .dropdown-menu>li>a {
    color: #c2d1e8;
}

@media (max-width: 767px)
.navbar-default .navbar-nav .open .dropdown-menu>li>a:focus, .navbar-default .navbar-nav .open .dropdown-menu>li>a:hover {
    color: #fff;
    background-color: transparent;
}


/*****************************************************************************************************************/









/***    left menu ****/

/***********************  TOP Bar ********************/
.sidebar{ width:220px;  background-color:#000;transition: all 0.5s  ease-in-out; }
.bg-defoult{background-color:#222;}
.sidebar ul{ list-style:none; margin:0px; padding:0px; }
.sidebar li a,.sidebar li a.collapsed.active{ display:block; padding:8px 12px; color:#fff;border-left:0px solid #dedede;  text-decoration:none}
.sidebar li a.active{background-color:#000;border-left:5px solid #dedede; transition: all 0.5s  ease-in-out}
.sidebar li a:hover{background-color:#000 !important;}
.sidebar li a i{ padding-right:5px;}
.sidebar ul li .sub-menu li a{ position:relative}
.sidebar ul li .sub-menu li a:before{
    font-family: FontAwesome;
    content: "\f105";
    display: inline-block;
    padding-left: 0px;
    padding-right: 10px;
    vertical-align: middle;
}
.sidebar ul li .sub-menu li a:hover:after {
    content: "";
    position: absolute;
    left: -5px;
    top: 0;
    width: 5px;
    background-color: #111;
    height: 100%;
}
.sidebar ul li .sub-menu li a:hover{ background-color:#222; padding-left:20px; transition: all 0.5s  ease-in-out}
.sub-menu{ border-left:5px solid #dedede;}
	.sidebar li a .nav-label,.sidebar li a .nav-label+span{ transition: all 0.5s  ease-in-out}
	

	.sidebar.fliph li a .nav-label,.sidebar.fliph li a .nav-label+span{ display:none;transition: all 0.5s  ease-in-out}
	.sidebar.fliph {
    width: 42px;transition: all 0.5s  ease-in-out;
   
}
	
.sidebar.fliph li{ position:relative}
.sidebar.fliph .sub-menu {
    position: absolute;
    left: 39px;
    top: 0;
    background-color: #222;
    width: 150px;
    z-index: 100;
}
	

	.user-panel {
    clear: left;
    display: block;
    float: left;
}
.user-panel>.image>img {
    width: 100%;
    max-width: 45px;
    height: auto;
}
.user-panel>.info,  .user-panel>.info>a {
    color: #fff;
}
.user-panel>.info>p {
    font-weight: 600;
    margin-bottom: 9px;
}
.user-panel {
    clear: left;
    display: block;
    float: left;
    width: 100%;
    margin-bottom: 15px;
    padding: 25px 15px;
    border-bottom: 1px solid;
}
.user-panel>.info {
    padding: 5px 5px 5px 15px;
    line-height: 1;
    position: absolute;
    left: 55px;
}

.fliph .user-panel{ display: none; }


/*************************Date And Time Picker CSS Starts here*************************************************/

.datetimepicker table tr td.active, .datetimepicker table tr td.active:hover, .datetimepicker table tr td.active.disabled, .datetimepicker table tr td.active.disabled:hover {
    background-color: #1b327e;
    background-image: -moz-linear-gradient(top,#1b327e,#1b327e);
    background-image: -ms-linear-gradient(top,#1b327e,#1b327e);
    background-image: -webkit-gradient(linear,0 0,0 100%,from(#1b327e),to(#1b327e));
    background-image: -webkit-linear-gradient(top,#1b327e,#1b327e);
    background-image: -o-linear-gradient(top,#1b327e,#1b327e);
    background-image: linear-gradient(to bottom,#1b327e,#1b327e);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1b327e',endColorstr='#1b327e',GradientType=0);
    border-color: #04c #04c #002a80;
    border-color: rgba(0,0,0,0.1) rgba(0,0,0,0.1) rgba(0,0,0,0.25);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0,0,0,0.25);
}



.datetimepicker table tr td span.active, .datetimepicker table tr td span.active:hover, .datetimepicker table tr td span.active.disabled, .datetimepicker table tr td span.active.disabled:hover {
    background-color: #1b327e;
    background-image: -moz-linear-gradient(top,#1b327e,#1b327e);
    background-image: -ms-linear-gradient(top,#1b327e,#1b327e);
    background-image: -webkit-gradient(linear,0 0,0 100%,from(#1b327e),to(#1b327e));
    background-image: -webkit-linear-gradient(top,#1b327e,#1b327e);
    background-image: -o-linear-gradient(top,#1b327e,#1b327e);
    background-image: linear-gradient(to bottom,#1b327e,#1b327e);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1b327e',endColorstr='#1b327e',GradientType=0);
    border-color: #04c #04c #002a80;
    border-color: rgba(0,0,0,0.1) rgba(0,0,0,0.1) rgba(0,0,0,0.25);
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0,0,0,0.25);
}


.input-group-addon {
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    color: #000;
    text-align: center;
    background-color: #ecf4ff;
    border: 1px solid #bcd1f1;
    border-radius: 4px;
}


/*************************Date And Time Picker CSS Ends here*******************************************************/




.card-white .panel {
    margin-bottom: 20px;
    background-color: #fff;
    border: 0px solid transparent;
    border-radius: 4px;
    -webkit-box-shadow: none;
    box-shadow: none;
}


input#uploadId, input.btn.btn-default.redirect {
    margin-top: 26px;
}


.panel {
    margin: 10px 0px 10px 0px;
   
}

.error{
	color: red;
}

/* edited by mayur kadu*/
.icon_bar {
    float: right;
    width: 250px;
    height: 18px;
    text-align: right;
}
.icon_bar img {
    width: 18px;
    margin-left: 20px;
    cursor: pointer;
}
.text-area textarea{
	width:85%;
}