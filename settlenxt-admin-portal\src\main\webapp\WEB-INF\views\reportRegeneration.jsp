<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript" src="./static/js/moment.min.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<link rel="stylesheet"
	href="./static/css/jquery-ui.css">
<script src="./static/js/jquery-ui.js"></script>

<script type="text/javascript" src=	"./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src=	"./static/js/jszip.min.js">
</script>
 
<script type="text/javascript" src=	"./static/js/buttons.html5.min.js">
</script>

 <script type="text/javascript"
	src="./static/js/validation/commonValidation.js"></script>
<script src="./static/js/validation/reportRegeneration.js"
	type="text/javascript"></script>
</head>
 
 <style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}

.control-label { 
    vertical-align: top; 
}



</style>
	
<script type="text/javascript">
var actionColumnIndex = 7;
var firstColumnToBeSkippedInFilterAndSort=false;

</script>

	

<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
	</ul>

	<div class="row">
		<div class="col-sm-12">
			<div class="panel panel-default">
				<div class="panel-heading">
					<strong>
						<span class="glyphicon glyphicon-th"></span> 
						<span data-i18n="Data">
							<spring:message code="reportRegenAndRecal.title" />
						</span>
					</strong>
				</div>
				<div class=" ">
					<div role="tabpanel" class="tab-pane active" id="home">
						<form:form onsubmit="return encodeForm(this);" method="POST" id="reportRegeneration" 
									modelAttribute="cycleManagementDTO" action="/SNxtAdminPortal/getReportRegenerationStatus" autocomplete="off">
							<br />
							<div class="row">
								<div class="col-sm-12">
									<div class="col-sm-2">
										<div class="form-group">
											<label for=""><spring:message code="reportProgressStatus.productId" /><span class="red">*</span></label>
											<form:select path="settlementProductId" id="select-settlementProductId"
                                                name="settlementProductId" class="form-control settleProductId" >
                                                <option value = "" disabled selected>Select product Id</option>
                                                <c:forEach var="settleProductName" items="${cycleManagementDTO.settleProduct}">
												<option value="${settleProductName.key}" data-product_id = "${settleProductName.value}">${settleProductName.key}</option>
												</c:forEach>
                                            </form:select>
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label for=""><spring:message code="reportProgressStatus.cycleDate" /><span class="red">*</span></label>
											<form:input path="cycleDate" id="cycleDate" data-date-format="yyyy-mm-dd"
											cssClass="form-control medantory" max="3000-01-01" 
											onfocus="this.max=new Date().toISOString().split('T')[0]"  />
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label>
												<spring:message code="reportProgressStatus.cycleNumber" /><span class="red">*</span>
											</label>
											<form:select path="cycleNumber" id="select-cycleNumber" 
												name="cycleNumber" class="form-control" >
												 <option value = "" disabled selected>Select Cycle Number</option>
											</form:select>
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label for="">ParticipantId <span class="red">*</span></label>
											<form:select path="participantId" id="select-participantId"
                                                name="participantId" class="form-control participant" >
                                                <option value = "" disabled selected>Select participant Id</option>
                                                <form:option value="*">ALL</form:option>
                                                <c:forEach var="participantId" items="${cycleManagementDTO.participantList}">
												<option value="${participantId}" data-participant_id = "${participantId}">${participantId}</option>
												</c:forEach>
                                            </form:select>
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label>
												<spring:message code="reportRegenAndRecal.reportType" /><span class="red">*</span>
											</label>
											<div id="mywrapper">
									
											<form:select path="reportType" id="select-reportType" placeholder="Pick a report Type..."
												name="reportType" class="form-control reportType" >
												 <option value = "" disabled selected>Select Report Type</option>
</form:select>
											</div>
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<button type="button" id="submit" class="btn btn-primary btn-sm" data-toggle="modal"
											 data-target="#myModal" style="margin-bottom:20px; border-radius: 6px; color:white;" disabled>Submit</button>
										 <button type="button" class="btn btn-primary btn-sm" id="refreshBtn"
								            value="Refresh" style="margin-bottom:20px; border-radius: 6px; color:white;"  onclick="getRegenerationStatus();">Refresh</button>
										 </div>
									</div>
								</div>
							</div>
						</form:form>
					</div>
				</div>
				<div id="afterSave">
					<div class=" ">
						<div class="row">
							<div class="col-sm-1"></div>
							<div class="col-sm-10">
								<div class="panel-body">
									<div class="table-responsive">
										<table id="dataTable" class="table table-bordered">
										<caption style = "display:none"></caption>
											<thead>
												<tr>
													<th scope = "col" class="text-center">Cycle Date</th>
													<th scope = "col" class="text-center">Request Date</th>
													<th scope = "col" class="text-center">Cycle Number</th>
													<th scope = "col" class="text-center">Report Type</th>
													<th scope = "col" class="text-center">Participant Id</th>
													<th scope = "col" class="text-center">Settlement Product Id</th>
													<th scope = "col" class="text-center">Regeneration Status</th>
												</tr>
												
											</thead>
											<tbody>
												<c:if test="${not empty cycleManagementDTO.cycleData}">
													<c:forEach var="cycleData" items="${cycleManagementDTO.cycleData}">
												       <tr data-regenerationStatus="${cycleData.regenerationStatus}">
													       <td>${cycleData.cycleDate}</td>
													       <td>${cycleData.requestDate}</td>
													       <td>${cycleData.cycleNumber}</td>
													       <td>${cycleData.reportType}</td>
															
															<c:choose>
																<c:when test="${cycleData.participantId ne '*'}">
																	<td>${cycleData.participantId}</td>
																</c:when>
																<c:otherwise>
																	<td>ALL</td>
																	</c:otherwise>
															</c:choose> 
															
													       <td>${cycleData.settlementProductId}</td>
													       <td>${cycleData.regenerationStatus}</td>
												       </tr>
													</c:forEach>
												</c:if>
												
											</tbody>
										</table>
									</div>
								</div>
							</div>
						<div class="col-sm-1"></div>
					</div>
				</div>
				<div class="container">
					<div class="modal fade" id="myModal" role="dialog">  
						    <div class="modal-dialog modal-md">  
						      <div class="modal-content">
							      <div class="modal-header">  
							          <div class="panel-heading">
									  </div>
					        	  </div> 
				        	  	  <div>
					        		<div class="modal-body"> 
					        			<form:form onsubmit="return encodeForm(this);" method="POST" commandeName="reportRegenAndRecal"
										id="addEditMember" modelAttribute="cycleManagementDTO"
										action="reportRegenAndRecal" autocomplete="off">
										<br /> 
										<div class="row">
										 	<div class="col-sm-12">
											  <div class="alert alert-dismissible hide" id="alertNotification" role="alert">
				                            	<strong><span id="alertMessageStrong"></span></strong><span id="alertMessage"></span>
				                         	</div>
											</div>
										</div>
									</form:form>
		        					</div>  
									<div class="modal-footer"> 		
				      					<div class="text-right"> 
				        				 	<button type="button" id="okAction" class="btn btn-primary" data-dismiss="modal" style="border-radius: 6px">Okay</button>
				        				</div> 
			        				</div>
			        		 	</div>  
					  		</div>  
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
</div>