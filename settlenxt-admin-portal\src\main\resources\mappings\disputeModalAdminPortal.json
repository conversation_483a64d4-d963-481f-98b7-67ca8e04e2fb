[{"name": "createPresentment", "title": "Create Presentment", "actionCode": "03", "funcCode": "200", "mti": "1240", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "primCompAttribute": "tipAmount", "compAttribute": "amountTransaction", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "cardAcceptorZipCode", "label": "Card Acceptor Zip Code", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "cardAcceptorZipCode", "destAttribute": "cardAcceptorZipCode", "validationExp": "", "validationMessage": "Invalid Card Acceptor Zip Code", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "merchantTelephoneNumber", "label": "Merchant Telephone Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "merchantTelephoneNumber", "destAttribute": "merchantTelephoneNumber", "validationExp": "", "validationMessage": "Merchant Telephone Number is invalid", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "cardHolderUID", "label": "Card Holder <PERSON>", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "cardHolderUID", "validationExp": "", "validationMessage": "Card Holder <PERSON> is invalid", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "cardHolderIncomeTaxPan", "label": "Card Holder Income Tax PAN", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "cardHolderIncomeTaxPan", "validationExp": "", "validationMessage": "Card Holder Income Tax PAN is invalid", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "cardAcceptorAdditionalAddress", "label": "Card Acceptor Additional Address", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "cardAcceptorAdditionalAddress", "destAttribute": "cardAcceptorAdditionalAddress", "validationExp": "", "validationMessage": "Card Acceptor Additional Address is invalid", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "merchantIndicator", "label": "Merchant Indicator", "type": "lookup", "lookupData": [{"code": "D", "value": "<PERSON><PERSON><PERSON>"}, {"code": "S", "value": "Small Merchant"}, {"code": "L", "value": "LIC Merchant"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "merchantIndicator", "validationExp": "", "validationMessage": "Merchant Indicator is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1}]}, {"name": "createArbRepresent", "title": "Re-presentment", "actionCode": "07", "funcCode": "205", "mti": "1240", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "documentIndicator", "label": "Document Indicator", "type": "lookup", "lookupData": [{"code": "Y", "value": "Yes"}, {"code": "N", "value": "No"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "documentIndicator", "validationExp": "", "onload": "showHideFileUpload", "onchange": "showHideFileUpload", "validationMessage": "Document Indicator is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "fullPartial", "label": "Full / Partial", "type": "lookup", "lookupData": [{"code": "F", "value": "Full"}, {"code": "P", "value": "Partial"}], "addtionalStyleClass": null, "onload": "enableDisableAmountTxn", "onchange": "enableDisableAmountTxn", "srcAttribute": "", "destAttribute": "fullPartial", "validationExp": "", "validationMessage": "Full or partial is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}]}, {"name": "offlinePresentment", "title": "Offline Presentment", "actionCode": "58", "funcCode": "260", "mti": "1240", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "cardAcceptorZipCode", "label": "Card Acceptor Zip Code", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "cardAcceptorZipCode", "destAttribute": "cardAcceptorZipCode", "validationExp": "", "validationMessage": "Invalid Card Acceptor Zip Code", "addtionalData": null, "maxlength": 20, "minlength": 1}, {"attributeName": "merchantTelephoneNumber", "label": "Merchant Telephone Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "merchantTelephoneNumber", "destAttribute": "merchantTelephoneNumber", "validationExp": "", "validationMessage": "Merchant Telephone Number is invalid", "addtionalData": null, "maxlength": 20, "minlength": 1}, {"attributeName": "cardAcceptorAdditionalAddress", "label": "Card Acceptor Additional Address", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "cardAcceptorAdditionalAddress", "destAttribute": "cardAcceptorAdditionalAddress", "validationExp": "", "validationMessage": "Card Acceptor Additional Address is invalid", "addtionalData": null, "maxlength": 30, "minlength": 1}, {"attributeName": "merchantIndicator", "label": "Merchant Indicator", "type": "lookup", "lookupData": [{"code": "D", "value": "<PERSON><PERSON><PERSON>"}, {"code": "S", "value": "Small Merchant"}, {"code": "L", "value": "LIC Merchant"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "merchantIndicator", "validationExp": "", "validationMessage": "Merchant Indicator is invalid", "addtionalData": null, "maxlength": 10, "minlength": 1}]}, {"name": "chargebackRepreAcc", "title": "Re-presentment accept", "actionCode": "08", "funcCode": "261", "mti": "1240", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amtApproval", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "disabled": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}]}, {"name": "raiseRefund", "title": "Raise Refund", "actionCode": "04", "funcCode": "262", "mti": "1240", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "amountTransaction", "sumationAttribute": "totalRefundedAmt", "primCompAttribute": "orgAmtPresentment", "compAttribute": "amountTransaction", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "messageReasonCode", "label": "Message Reason Code", "type": "lookup", "addtionalStyleClass": null, "onload": "setReasonCodesWithoutSubType", "srcAttribute": "", "destAttribute": "messageReasonCode", "validationExp": "", "validationMessage": "Invalid Message Reason Code", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}, {"attributeName": "fullPartial", "label": "Full / Partial", "type": "lookup", "lookupData": [{"code": "F", "value": "Full"}, {"code": "P", "value": "Partial"}], "addtionalStyleClass": null, "onchange": "enableDisableAmountTxn", "srcAttribute": "", "destAttribute": "fullPartial", "validationExp": "", "validationMessage": "Full or partial is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "internalTrackingNumber", "validationExp": "", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1}]}, {"name": "raiseRefund", "title": "Raise Refund", "actionCode": "04_2", "funcCode": "262", "mti": "1240", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "amountTransaction", "sumationAttribute": "totalRefundedAmt", "primCompAttribute": "orgAmtPresentment", "compAttribute": "amountTransaction", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "fullPartial", "label": "Full / Partial", "type": "lookup", "lookupData": [{"code": "F", "value": "Full"}, {"code": "P", "value": "Partial"}], "addtionalStyleClass": null, "onchange": "enableDisableAmountTxn", "srcAttribute": "", "destAttribute": "fullPartial", "validationExp": "", "validationMessage": "Full or partial is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "internalTrackingNumber", "validationExp": "", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1}]}, {"name": "createTipSurcharge", "title": "Create Surcharge Adjustment", "actionCode": "14", "funcCode": "265", "mti": "1240", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "tipAmount", "destAttribute": "amountTransaction", "compAttribute": "tipAmount", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}]}, {"name": "createPresentmentRev", "title": "Create Presentment Reversal", "actionCode": "55", "funcCode": "420", "mti": "1420", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "disabled": "Y", "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "messageReasonCode", "label": "Message Reason Code", "type": "lookup", "addtionalStyleClass": null, "onload": "setReasonCodesWithoutSubType", "srcAttribute": "", "destAttribute": "messageReasonCode", "validationExp": "", "validationMessage": "Invalid Message Reason Code", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}]}, {"name": "createPresentmentRev", "title": "Create Presentment Reversal", "actionCode": "55_2", "funcCode": "420", "mti": "1420", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "disabled": "Y", "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}]}, {"name": "chargeBackRefund", "title": "Create Chargeback Refund", "actionCode": "23", "funcCode": "264", "mti": "1442", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "compAttribute": "totalRefundedAmt", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "messageReasonCode", "label": "Message Reason Code", "type": "lookup", "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "messageReasonCode", "validationExp": "", "validationMessage": "Invalid Message Reason Code", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}, {"attributeName": "reasonSubtype", "label": "Reason Subtype", "type": "lookup-api", "lookupData": [{"url": "getReasonSubTypeDesc", "data": "availableReasonSubType", "type": "complex"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "reasonSubtype", "onchange": "setReasonCodeDropDown", "validationExp": "", "validationMessage": "Invalid Message Reason SubType", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}, {"attributeName": "fullPartial", "label": "Full / Partial", "type": "lookup", "lookupData": [{"code": "F", "value": "Full"}, {"code": "P", "value": "Partial"}], "addtionalStyleClass": null, "onload": "enableDisableAmountTxn", "onchange": "enableDisableAmountTxn", "srcAttribute": "", "destAttribute": "fullPartial", "validationExp": "", "validationMessage": "Full or partial is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "internalTrackingNumber", "validationExp": "", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "controlNo", "label": "Control Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "controlNo", "validationExp": "", "validationMessage": "Invalid control no", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "memberMessageText", "label": "Member Message Text", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "memberMessageText", "validationExp": "", "validationMessage": "Member Message Text is mandatory", "addtionalData": null, "maxlength": 100, "minlength": 1, "mandatory": "Y"}]}, {"name": "chargeBack", "title": "ChargeBack", "actionCode": "05", "funcCode": "450", "mti": "1442", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "amountTransaction", "sumationAttribute": "totalRefundedAmt", "primCompAttribute": "orgAmtPresentment", "compAttribute": "amountTransaction", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "internalTrackingNumber", "validationExp": "^[a-zA-Z0-9]*$", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "fullPartial", "label": "Full / Partial", "type": "lookup", "lookupData": [{"code": "F", "value": "Full"}, {"code": "P", "value": "Partial"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "fullPartial", "validationExp": "", "onload": "enableDisableAmountTxn", "onchange": "enableDisableAmountTxn", "validationMessage": "Full or partial is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "documentIndicator", "label": "Document Indicator", "type": "lookup", "lookupData": [{"code": "Y", "value": "Yes"}, {"code": "N", "value": "No"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "documentIndicator", "validationExp": "", "onload": "showHideFileUpload", "onchange": "showHideFileUpload", "validationMessage": "Document Indicator is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "memberMessageText", "label": "Member Message Text", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "memberMessageText", "validationExp": "", "validationMessage": "Member Message Text is mandatory", "addtionalData": null, "maxlength": 100, "minlength": 1, "mandatory": "Y"}, {"attributeName": "messageReasonCode", "label": "Message Reason Code", "type": "lookup", "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "messageReasonCode", "validationExp": "", "validationMessage": "Invalid Message Reason Code", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}, {"attributeName": "reasonSubtype", "label": "Reason Subtype", "type": "lookup-api", "lookupData": [{"url": "getReasonSubTypeDesc", "data": "availableReasonSubType", "type": "complex"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "reasonSubtype", "onchange": "setReasonCodeDropDown", "validationExp": "", "validationMessage": "Invalid Message Reason SubType", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}]}, {"name": "chargebackAcc", "title": "Chargeback - Accept", "actionCode": "06", "funcCode": "470", "mti": "1442", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amtApproval", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "disabled": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}]}, {"name": "chargebackInitFuncColl", "title": "Initiate Fund Collection", "actionCode": "41", "funcCode": "700", "mti": "1740", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "messageReasonCode", "label": "Message Reason Code", "type": "lookup", "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "messageReasonCode", "validationExp": "", "validationMessage": "Invalid Message Reason Code", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y", "hideOnload": "Y"}, {"attributeName": "reasonSubtype", "label": "Reason Subtype", "type": "lookup-api", "lookupData": [{"url": "getReasonSubTypeDesc", "data": "availableReasonSubType", "type": "complex"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "reasonSubtype", "onchange": "setReasonCodeDropDown", "validationExp": "", "validationMessage": "Invalid Message Reason SubType", "addtionalData": null, "maxlength": 20, "minlength": 1, "reasonCodeDefault": "Y", "hideOnload": "Y"}]}, {"name": "chargebackInitFunDisb", "title": "Initiate Fund Disbursement", "actionCode": "42", "funcCode": "701", "mti": "1740", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "messageReasonCode", "label": "Message Reason Code", "type": "lookup", "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "messageReasonCode", "validationExp": "", "validationMessage": "Invalid Message Reason Code", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y", "hideOnload": "Y"}, {"attributeName": "reasonSubtype", "label": "Reason Subtype", "type": "lookup-api", "lookupData": [{"url": "getReasonSubTypeDesc", "data": "availableReasonSubType", "type": "complex"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "reasonSubtype", "onchange": "setReasonCodeDropDown", "validationExp": "", "validationMessage": "Invalid Message Reason SubType", "addtionalData": null, "maxlength": 20, "minlength": 1, "reasonCodeDefault": "Y", "hideOnload": "Y"}]}, {"name": "creditAdjust", "title": "Create Refund - Issuer", "actionCode": "12", "funcCode": "401", "mti": "1240", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "messageReasonCode", "label": "Message Reason Code", "type": "lookup", "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "messageReasonCode", "validationExp": "", "validationMessage": "Invalid Message Reason Code", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}, {"attributeName": "reasonSubtype", "label": "Reason Subtype", "type": "lookup-api", "lookupData": [{"url": "getReasonSubTypeDesc", "data": "availableReasonSubType", "type": "complex"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "reasonSubtype", "onchange": "setReasonCodeDropDown", "validationExp": "", "validationMessage": "Invalid Message Reason SubType", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}, {"attributeName": "documentIndicator", "label": "Document Indicator", "type": "lookup", "lookupData": [{"code": "Y", "value": "Yes"}, {"code": "N", "value": "No"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "documentIndicator", "validationExp": "", "onload": "showHideFileUpload", "onchange": "showHideFileUpload", "validationMessage": "Document Indicator is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "fullPartial", "label": "Full / Partial", "type": "lookup", "lookupData": [{"code": "F", "value": "Full"}, {"code": "P", "value": "Partial"}], "addtionalStyleClass": null, "onload": "enableDisableAmountTxn", "onchange": "enableDisableAmountTxn", "srcAttribute": "", "destAttribute": "fullPartial", "validationExp": "", "validationMessage": "Full or partial is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "internalTrackingNumber", "validationExp": "", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "memberMessageText", "label": "Member Message Text", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "memberMessageText", "validationExp": "", "validationMessage": "Member Message Text is mandatory", "addtionalData": null, "maxlength": 100, "minlength": 1, "mandatory": "Y"}]}, {"name": "debitAdjust", "title": "Debit adjustment", "actionCode": "13", "funcCode": "763", "mti": "1740", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "primCompAttribute": "tipAmount", "destAttribute": "amountTransaction", "compAttribute": "amountTransaction", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "messageReasonCode", "label": "Message Reason Code", "type": "lookup", "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "messageReasonCode", "validationExp": "", "validationMessage": "Invalid Message Reason Code", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}, {"attributeName": "reasonSubtype", "label": "Reason Subtype", "type": "lookup-api", "lookupData": [{"url": "getReasonSubTypeDesc", "data": "availableReasonSubType", "type": "complex"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "reasonSubtype", "onchange": "setReasonCodeDropDown", "validationExp": "", "validationMessage": "Invalid Message Reason SubType", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}, {"attributeName": "documentIndicator", "label": "Document Indicator", "type": "lookup", "lookupData": [{"code": "Y", "value": "Yes"}, {"code": "N", "value": "No"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "documentIndicator", "validationExp": "", "onload": "showHideFileUpload", "onchange": "showHideFileUpload", "validationMessage": "Document Indicator is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}]}, {"name": "raiseRetrievalReq", "title": "Retrieval Request", "actionCode": "09", "funcCode": "603", "mti": "1644", "attributes": [{"attributeName": "messageReasonCode", "label": "Message Reason Code", "type": "lookup", "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "messageReasonCode", "validationExp": "", "validationMessage": "Invalid Message Reason Code", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}, {"attributeName": "reasonSubtype", "label": "Reason Subtype", "type": "lookup-api", "lookupData": [{"url": "getReasonSubTypeDesc", "data": "availableReasonSubType", "type": "complex"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "reasonSubtype", "onchange": "setReasonCodeDropDown", "validationExp": "", "validationMessage": "Invalid Message Reason SubType", "addtionalData": null, "maxlength": 20, "minlength": 1, "mandatory": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "internalTrackingNumber", "validationExp": "", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1}]}, {"name": "createVoidAcpAuthCancel", "title": "Create Void Request", "actionCode": "54", "funcCode": "266", "mti": "8144", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y", "disabled": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}]}, {"name": "createCashbackRev", "title": "Create Cashback Reversal", "actionCode": "64", "funcCode": "277", "mti": "1240", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "disabled": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "memberMessageText", "label": "Member Message Text", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "memberMessageText", "validationExp": "", "validationMessage": "Member Message Text is mandatory", "addtionalData": null, "maxlength": 100, "minlength": 1, "mandatory": "Y"}]}, {"name": "arbitratiotionVerdictIss", "title": "Arbitration Verdict- In favour of issuer", "actionCode": "51", "funcCode": "483", "mti": "8642", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "documentIndicator", "label": "Document Indicator", "type": "lookup", "lookupData": [{"code": "Y", "value": "Yes"}, {"code": "N", "value": "No"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "documentIndicator", "validationExp": "", "onload": "showHideFileUpload", "onchange": "showHideFileUpload", "validationMessage": "Document Indicator is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "internalTrackingNumber", "destAttribute": "internalTrackingNumber", "validationExp": "", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 20, "minlength": 1, "disabled": "Y"}, {"attributeName": "memberMessageText", "label": "Member Message Text", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "memberMessageText", "validationExp": "", "validationMessage": "Member Message Text is mandatory", "addtionalData": null, "maxlength": 100, "minlength": 1, "mandatory": "Y"}]}, {"name": "arbitratiotionVerdictAcq", "title": "Arbitration Verdict- In favour of acquirer", "actionCode": "52", "funcCode": "483", "mti": "8642", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "authAmount", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "documentIndicator", "label": "Document Indicator", "type": "lookup", "lookupData": [{"code": "Y", "value": "Yes"}, {"code": "N", "value": "No"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "documentIndicator", "validationExp": "", "onload": "showHideFileUpload", "onchange": "showHideFileUpload", "validationMessage": "Document Indicator is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "internalTrackingNumber", "destAttribute": "internalTrackingNumber", "validationExp": "", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 20, "minlength": 1, "disabled": "Y"}, {"attributeName": "memberMessageText", "label": "Member Message Text", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "memberMessageText", "validationExp": "", "validationMessage": "Member Message Text is mandatory", "addtionalData": null, "maxlength": 100, "minlength": 1, "mandatory": "Y"}]}, {"name": "arbitratiotionVerdictGoodFaithAcq", "title": "Good faith deny - Acquirer", "actionCode": "53", "funcCode": "682", "mti": "8744", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amtApproval", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "disabled": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "documentIndicator", "label": "Document Indicator", "type": "lookup", "lookupData": [{"code": "Y", "value": "Yes"}, {"code": "N", "value": "No"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "documentIndicator", "validationExp": "", "onload": "showHideFileUpload", "onchange": "showHideFileUpload", "validationMessage": "Document Indicator is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "internalTrackingNumber", "validationExp": "", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 20, "minlength": 1}, {"attributeName": "memberMessageText", "label": "Member Message Text", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "memberMessageText", "validationExp": "", "validationMessage": "Member Message Text is mandatory", "addtionalData": null, "maxlength": 100, "minlength": 1, "mandatory": "Y"}]}, {"name": "complianceVerdictAcq", "title": "Compliance Verdict - In favour of Initiator - Acquirer", "actionCode": "39", "funcCode": "679", "mti": "8644", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amtApproval", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "disabled": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "documentIndicator", "label": "Document Indicator", "type": "lookup", "lookupData": [{"code": "Y", "value": "Yes"}, {"code": "N", "value": "No"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "documentIndicator", "validationExp": "", "onload": "showHideFileUpload", "onchange": "showHideFileUpload", "validationMessage": "Document Indicator is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "internalTrackingNumber", "validationExp": "", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 20, "minlength": 1}, {"attributeName": "memberMessageText", "label": "Member Message Text", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "memberMessageText", "validationExp": "", "validationMessage": "Member Message Text is mandatory", "addtionalData": null, "maxlength": 100, "minlength": 1, "mandatory": "Y"}]}, {"name": "complianceVerdictIss", "title": "Compliance Verdict - In favour of Initiator - Issuer", "actionCode": "40", "funcCode": "679", "mti": "8644", "attributes": [{"attributeName": "amountTransaction", "label": "Amount, Transaction", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amtApproval", "destAttribute": "amountTransaction", "compAttribute": "amtApproval", "operator": "LT", "validationExp": "", "validationMessage": "<PERSON><PERSON><PERSON>", "addtionalData": null, "maxlength": 10, "minlength": 1, "disabled": "Y"}, {"attributeName": "amountAdditonal", "label": "<PERSON>ount, Additional", "type": "amount", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "amountAdditional", "destAttribute": "amountAdditional", "validationExp": "^(?!0*(\\.0{2})?$)(\\d+|\\d*\\.\\d{2})$", "validationMessage": "Invalid Adddtional Amount", "addtionalData": null}, {"attributeName": "documentIndicator", "label": "Document Indicator", "type": "lookup", "lookupData": [{"code": "Y", "value": "Yes"}, {"code": "N", "value": "No"}], "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "documentIndicator", "validationExp": "", "onload": "showHideFileUpload", "onchange": "showHideFileUpload", "validationMessage": "Document Indicator is mandatory", "addtionalData": null, "maxlength": 10, "minlength": 1, "mandatory": "Y"}, {"attributeName": "internalTrackingNumber", "label": "Internal Tracking Number", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "internalTrackingNumber", "validationExp": "", "validationMessage": "Internal Tracking Number is mandatory", "addtionalData": null, "maxlength": 20, "minlength": 1}, {"attributeName": "memberMessageText", "label": "Member Message Text", "type": "text", "lookupData": null, "addtionalStyleClass": null, "srcAttribute": "", "destAttribute": "memberMessageText", "validationExp": "", "validationMessage": "Member Message Text is mandatory", "addtionalData": null, "maxlength": 100, "minlength": 1, "mandatory": "Y"}]}]