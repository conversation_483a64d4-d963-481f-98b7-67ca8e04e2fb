{"transactions": {"declineDisputeCodes": ["07", "08", "28", "31", "40", "41", "50", "89"], "notallowedTypeStatusForAmountCapping": ["FFRCFWRD", "FFRQFWRD"], "notallowedFunctionForAmountCapping": ["764", "765", "766", "767", "800", "801", "802", "803", "804", "805", "806", "807", "808"], "disallowedProcCodesForActions": ["05"], "EmsCheckerWidgets": ["EMS_CHECKER", "CHB_CHECKER", "ARB_CHECKER", "PARB_CHECKER", "GFTH_CHECKER", "DOC_CHECKER", "COMP_CHECKER", "PCMP_CHECKER", "REP_CHECKER", "TRAN_CHECKER", "ADJ_CHECKER", "NW_CHECKER"], "participantNoGST": ["SBIN0020010", "SBIN0020011"]}, "admin": {"expiryMessageStartDays": "5", "defaultMaxUserCount": "25", "fileTypesForDisputes": ["JPEG", "JPG", "PNG", "TIF", "PDF", "TIFF", "BMP"], "fileTypesForOnboarding": ["JPEG", "JPG", "PNG", "TIF", "PDF", "TIFF", "BMP", "CSV", "TXT", "DOCX"], "fileTypesForCircularDocs": ["PGP", "DOC", "DOCX", "RAR", "ZIP", "PDF", "TIFF", "JPEG", "JPG"], "notAllowedfileTypesForDownloadZip": ["MPEG", "EXE"], "hiddenFoldersForDownload": ["TEMP", "TEST"], "fileTypesForMcpr": ["csv", "CSV"]}, "documentUploadTatByPhase": {"CHB1-SDRC": ["5"], "REP1-SDRC": ["5"]}, "documentUploadAllowedTransitions": {"AllowedList": ["ADJC-FWRD", "ADJC-SDRC", "ADJ-FWRD", "ADJ-SDRC", "PCMP-FWRD", "PCMP-SDRC", "COMP-FWRD", "COMP-SDRC", "GFTH-FWRD", "GFTH-SDRC", "REP1-FWRD", "REP1-SDRC", "ARB-FWRD", "ARB-SDRC", "PARB-FWRD", "PARB-SDRC", "ARB-ACPF", "ARB-ACPS", "ARB-CONF", "ARB-CONS", "COMP-CONF", "COMP-CONS", "ARB-RUSA", "ARB-RUSI", "COMP-RUSI", "COMP-RUSA", "ARB-RULI", "ARB-RULA", "COMP-RULI", "COMP-RULA", "CHB1-FWRD", "CHB1-SDRC", "CHB1-PNDR", "PCMP-ACPF", "PCMP-ACPS", "PCMP-DNYF", "PCMP-DNYS", "PARB-ACPF", "PARB-ACPS", "PARB-DNYF", "PARB-DNYS", "GFTH-ACPF", "GFTH-ACPS", "GFTH-DNYF", "GFTH-DNYS", "COMP-ACPF", "COMP-ACPS", "DOCN-FWRD", "DOCN-SDRC", "DOCF-SDRC", "DOCF-FWRD"]}, "documentUploadNotAllowedUserGroups": {"NotAllowedUserGroupList": ["11", "12"]}, "transitionFromStateDesc": {"INITTEMP_0200_200": "SMS Authorization", "INITTEMP_0100_100": "DMS Authorization", "INITTEMP_0400_400": "Authorization Reversal Transaction", "INITTEMP_1240_200": "Presentment (<PERSON> Auth)", "INITTEMP_1240_265": "SMS Tip and Surcharge Adjustment", "INITTEMP_1420_420": "Reversal of Presentment (<PERSON><PERSON>)"}, "authCylceStageCutoff": {"1": "230000", "2": "100000", "3": "150000", "4": "190000", "closedBusDayEndTime": "22595999", "currentBusDayStartTime": "00000000", "approveRejectButtonCutOff1": "100000", "approveRejectButtonCutOff2": "230000", "approveRejectButtonCutOff3": "150000", "approveRejectButtonCutOff4": "190000", "switchClockforapproveReject1": "10595999", "switchClockforapproveReject2": "22595999", "switchClockforapproveReject3": "15595999", "switchClockforapproveReject4": "19595999"}, "fileUploadFilter": {"1740-700-Tags": ["nARD", "nCaseNum"], "1740-700-ReasonCodes": ["2001", "2002", "2007", "2008"], "1740-701-Tags": ["nARD", "nCaseNum"], "1740-701-ReasonCodes": ["2021", "2022", "2027"]}, "PortalServiceUrl": {"tokenUrl": "http://***************:8070/NPCIcheckSumGenerationAndVerificationService/addrgcsappdetails", "destinationUrl": "http://***************:8070/fisMemberPortal/index.jsp"}, "rbipenalty": {"totalType": "3040"}, "settlementAlert": {"delay": "68", "progress": "69", "completed": "25", "delaytime": "15"}}