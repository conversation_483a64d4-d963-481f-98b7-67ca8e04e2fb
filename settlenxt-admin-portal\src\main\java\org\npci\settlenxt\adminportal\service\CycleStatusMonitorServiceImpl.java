package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.CycleStatusMonitorDTO;
import org.npci.settlenxt.adminportal.repository.CycleStatusMonitorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CycleStatusMonitorServiceImpl implements CycleStatusMonitorService {
	@Autowired
    CycleStatusMonitorRepository cycleStatusMonitorRepository;

 

    @Override
    public List<CycleStatusMonitorDTO> getCycleStatusDetails(CycleStatusMonitorDTO activityModuleDto) {
        return cycleStatusMonitorRepository.getCycleStatusDetails(activityModuleDto);
    }



	@Override
	public List<CycleStatusMonitorDTO> getCycleDetailsByGuid(int guid) {
		return cycleStatusMonitorRepository.getCycleDetailsByGuid(guid);
	}



	@Override
	public CycleStatusMonitorDTO getCycleStatusByGuid(int guid) {
		return cycleStatusMonitorRepository.getCycleStatusByGuid(guid);
		
	}
}
