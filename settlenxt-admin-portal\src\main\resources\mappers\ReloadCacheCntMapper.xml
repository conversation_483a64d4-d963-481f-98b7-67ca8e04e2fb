<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.ReloadCacheRepository">
<!-- 	<select id="getDetails" resultType="reloadCacheDTO">  
		SELECT 
			cache_key as cacheKey, 
			cache_value as cacheValue, 
			reload_inst_list as reloadInstList  
		FROM 
			reload_cache_counter 
		limit 1
	</select> -->
	
	<update id="updateReloadInstList">
	update reload_cache_counter set cache_value = cache_value + 1, reload_inst_list=#{reloadInstList},last_updated_date=now() ;
	</update>
</mapper>
