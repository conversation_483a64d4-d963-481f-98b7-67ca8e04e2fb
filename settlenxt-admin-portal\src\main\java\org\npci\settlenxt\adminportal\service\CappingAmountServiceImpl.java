package org.npci.settlenxt.adminportal.service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.CappingAmountDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.CappingAmountRepository;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional(rollbackFor = Throwable.class)
@Service
public class CappingAmountServiceImpl implements CappingAmountService {

	@Autowired
	CappingAmountRepository cappingAmountRepository;

	@Autowired
	SessionDTO sessionDTO;

	private static final String UTF = "UTF-8";

	@Override
	public List<CappingAmountDTO> getApprovedCappingAmount() {
		return cappingAmountRepository.getCappingAmountFromMainList();
	}

	@Override
	public List<CappingAmountDTO> getPendingForApprovalCappingAmountList() {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_REJECTED);
		return cappingAmountRepository.getPendingCappingAmountFromStg(requestStateList);
	}

	@Override
	public List<CappingAmountDTO> getFunctionCodeList() {
		return cappingAmountRepository.getFunctionCodeList();
	}

	@Override
	public CappingAmountDTO addEditCapping(CappingAmountDTO cappingAmountDto) {
		Date currentDate = new Date();

		cappingAmountDto.setCreatedBy(sessionDTO.getUserName());
		cappingAmountDto.setCreatedOn(currentDate);
		cappingAmountDto.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
		cappingAmountDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		cappingAmountDto.setStatus("A");
		cappingAmountDto.setLastUpdatedBy(null);
		cappingAmountDto.setLastUpdatedOn(null);

		prepareBinCardTypeAndValue(cappingAmountDto);
		cappingAmountRepository.insertCappingAmountintoStg(cappingAmountDto);
		return cappingAmountDto;
	}

	@Override
	public CappingAmountDTO getCappingAmount(String actionCode, String mccGroup, String binCardBrandId,
			String binCardTypeId, String fieldName, String relOperator, String fieldValue)
			throws UnsupportedEncodingException

	{

		String bincardbrandid = URLDecoder.decode(binCardBrandId, UTF);
		String bincardtypeid = URLDecoder.decode(binCardTypeId, UTF);

		return cappingAmountRepository.getCappingAmountfromStg(actionCode, mccGroup, bincardbrandid, bincardtypeid,
				fieldName, relOperator, fieldValue);
	}

	@Override
	public CappingAmountDTO approveOrRejectCappingAmount(String capId, String status, String remarks) {

		CappingAmountDTO cappingAmountDto = cappingAmountRepository.getCappingAmountfromStgCombined(capId);
		cappingAmountDto.setCheckerComments(remarks);
		cappingAmountDto.setStatus(status);

		if ("A".equals(status)) {
			cappingAmountDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			cappingAmountDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			cappingAmountDto.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
		} else {
			cappingAmountDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
			cappingAmountDto.setRequestState(CommonConstants.REQUEST_STATE_REJECTED);
		}

		updateCappingAmountStg(cappingAmountDto);

		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			cappingAmountDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

			CappingAmountDTO cappingAmountdtoMain = cappingAmountRepository.getCappingAmountfromMainCombined(capId);

			if (cappingAmountdtoMain != null) {

				cappingAmountDto.setStatus(status);
				cappingAmountDto.setLastUpdatedOn(cappingAmountDto.getLastUpdatedOn());
				cappingAmountDto.setLastUpdatedBy(sessionDTO.getUserName());
				cappingAmountRepository.updateCappingAmount(cappingAmountDto);
			} else {
				cappingAmountDto.setStatus(status);
				cappingAmountDto.setCreatedOn(cappingAmountDto.getLastUpdatedOn());
				cappingAmountDto.setLastUpdatedBy(null);
				cappingAmountDto.setLastUpdatedOn(null);
				cappingAmountRepository.saveCappingAmountMain(cappingAmountDto);
			}
		}

		return cappingAmountDto;
	}

	private void updateCappingAmountStg(CappingAmountDTO cappingAmountDto) {

		Date currentDate = new Date();

		cappingAmountDto.setLastUpdatedBy(sessionDTO.getUserName());
		cappingAmountDto.setLastUpdatedOn(currentDate);

		cappingAmountRepository.updateCappingAmountRequestState(cappingAmountDto);

	}



	@Override
	public int updateCappingAmount(CappingAmountDTO cappingAmountDto) {

		cappingAmountDto.setLastUpdatedBy(sessionDTO.getUserName());
		cappingAmountDto.setLastUpdatedOn(new Date());

		cappingAmountDto.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
		cappingAmountDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		cappingAmountDto.setStatus("A");
		prepareBinCardTypeAndValue(cappingAmountDto);
		return cappingAmountRepository.updateCappingAmountStg(cappingAmountDto);
	}

	private void prepareBinCardTypeAndValue(CappingAmountDTO cappingAmountDto) {
		String binCardVal = cappingAmountDto.getBinCardBrandId();
		if (binCardVal.contains("|")) {
			binCardVal = binCardVal.replace("|", ",");
		}

		String binCardTypeVal = cappingAmountDto.getBinCardTypeId();
		if (binCardTypeVal.contains("|")) {
			binCardTypeVal = binCardTypeVal.replace("|", ",");

		}

		binCardVal = binCardVal.substring(0, binCardVal.length() - 1);
		binCardTypeVal = binCardTypeVal.substring(0, binCardTypeVal.length() - 1);

		cappingAmountDto.setBinCardBrandId(binCardVal);

		cappingAmountDto.setBinCardTypeId(binCardTypeVal);

		if ("P".equals(cappingAmountDto.getAmountFlag())) {
			cappingAmountDto.setFlat(null);
		} else if ("F".equals(cappingAmountDto.getAmountFlag())) {
			cappingAmountDto.setAmountCapPercent(null);
		}
	}

	@Override
	public CappingAmountDTO getCappingInfoFromMainEdit(String cappingId) {
		return cappingAmountRepository.getCappingAmountMainEdit(Integer.parseInt(cappingId));
	}

	@Override
	public CappingAmountDTO discardCappingAmount(String actionCode, String mccGroup, String binCardBrandId,
			String binCardTypeId, String fieldName, String relOperator, String fieldValue)
			throws UnsupportedEncodingException {

		Date currentDate = new Date();

		String bincardbrandid = URLDecoder.decode(binCardBrandId, UTF);
		String bincardtypeid = URLDecoder.decode(binCardTypeId, UTF);
		CappingAmountDTO cappingAmountDto = getCappingAmount(actionCode, mccGroup, binCardBrandId, binCardTypeId,
				fieldName, relOperator, fieldValue);
		CappingAmountDTO cappingAmountDtoMain = cappingAmountRepository.getCappingAmountMain(actionCode, mccGroup,
				bincardbrandid, bincardtypeid, fieldName, relOperator, fieldValue);

		if (cappingAmountDtoMain != null) {
			cappingAmountDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			cappingAmountDtoMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			cappingAmountDtoMain.setLastUpdatedOn(currentDate);
			cappingAmountRepository.updateStgCappingAmount(cappingAmountDtoMain);
			return cappingAmountDtoMain;
		} else {
			cappingAmountRepository.deleteDiscardedEntry(actionCode, mccGroup, bincardbrandid, bincardtypeid, fieldName,
					relOperator, fieldValue);

		}

		return cappingAmountDto;
	}

	@Override
	public CappingAmountDTO updateApproveOrRejectBulk(String[] values, String status, String remarks) throws SettleNxtException {

		CappingAmountDTO cappingAmount = new CappingAmountDTO();
		List<String> cappingIdList = Arrays.asList(values);

		List<CappingAmountDTO> cappingAmountToArr = cappingAmountRepository.fetchCappingAmountStgAppList(cappingIdList);

		Map<String, List<CappingAmountDTO>> capMap = cappingAmountToArr.stream()
				.collect(Collectors.groupingBy(x -> x.getActionCode() + x.getMccGroup() + x.getBinCardBrandId()
						+ x.getBinCardTypeId() + x.getFieldName() + x.getRelOperator() + x.getFieldValue()));

		for (String val:values) {

			try {
				List<CappingAmountDTO> cappingDto = capMap.get(val);
				CappingAmountDTO cappingAmountEntryDto = cappingDto.get(0);
				if (cappingAmountEntryDto == null) {
					throw new SettleNxtException("Exception occurred with cappingAmountId" + val, "");
				} else {
					cappingAmountEntryDto = approveOrRejectCappingAmount(val, status, remarks);
					cappingAmount.setStatusCode(cappingAmountEntryDto.getStatusCode());
				}

			} catch (Exception ex) {

				throw new SettleNxtException("Exception for BulkDataEntryId" + val, "", ex);

			}
		}

		return cappingAmount;
	}

	@Override
	public List<CodeValueDTO> getActionCodeList() {
		return cappingAmountRepository.getActionCodeList();
	}

	@Override
	public List<CodeValueDTO> getMccGroupList() {
		return cappingAmountRepository.getMccGroupList();
	}

	@Override
	public CappingAmountDTO getCappingInfoFromStgEdit(String actionCode, String mccGroup, String binCardBrandId,
			String binCardTypeId, String fieldName, String relOperator, String fieldValue)
			throws UnsupportedEncodingException {

		String bincardbrandid = URLDecoder.decode(binCardBrandId, UTF);
		String bincardtypeid = URLDecoder.decode(binCardTypeId, UTF);

		return cappingAmountRepository.getCappingAmountFromStgEdit(actionCode, mccGroup, bincardbrandid, bincardtypeid,
				fieldName, relOperator, fieldValue);
	}

	@Override
	public boolean checkDuplicateRecords(String actionCode, String mccGroup, String binCardBrandId,
			String binCardTypeId, String fieldName, String fieldValue, String relOperator) {

		boolean check = false;

		int count = cappingAmountRepository.checkDuplicateRecords(actionCode, mccGroup, binCardBrandId, binCardTypeId,
				fieldName, fieldValue, relOperator);

		if (count > 0) {
			check = true;
		}

		return check;
	}

}