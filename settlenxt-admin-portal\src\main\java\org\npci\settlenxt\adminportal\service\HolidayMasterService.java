package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.HolidayMasterDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;

public interface HolidayMasterService {

	List<HolidayMasterDTO> getApprovedHolidayMasterList();

	List<HolidayMasterDTO> getPendingHolidayMasterList();

	HolidayMasterDTO getHoliday(String holidaySeqId);

	void updateHoliday(HolidayMasterDTO holidayMasterDTO);

	HolidayMasterDTO getHolidayFromMain(String holidaySeqId);

	List<CodeValueDTO> getProductList();

	HolidayMasterDTO addEditHolidayMaster(HolidayMasterDTO holidayMasterDto);

	 HolidayMasterDTO updateApproveOrRejectHolidayMasterBulk(String bulkApprovalholidayMasterList, String status,
			String remarks);

	 List<HolidayMasterDTO> getPendingForAppovalHolidayMasterList();

	 HolidayMasterDTO updateApproveOrRejectHolidayMaster(int holidayMasterSeqId, String status, String remarks)
			throws SettleNxtException;

	 HolidayMasterDTO discardHoliday(String holidaySeqId);

	void deleteReq(String hid, String reqst);
}
