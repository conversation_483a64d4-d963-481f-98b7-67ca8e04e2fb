package org.npci.settlenxt.adminportal.db.connection;

import javax.sql.DataSource;


import org.npci.base.commonlib.utils.security.AES;

import org.npci.settlenxt.portal.common.util.BaseCommonConstants;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class DBManager {
	@Bean
	public DataSource createEncConnection(Environment environ) {

		try {
			String creds = environ.getProperty(BaseCommonConstants.JDBC_ENC_PASSWORD);
			String secretKey = environ.getProperty(BaseCommonConstants.AES_CRYPT_KEY);
			
			String decPassword = AES.decrypt(creds, secretKey);
			HikariConfig dataSrcBuild = new HikariConfig();

			dataSrcBuild.setDriverClassName(environ.getProperty(BaseCommonConstants.JDBC_DRIVER));
			dataSrcBuild.setJdbcUrl(environ.getProperty(BaseCommonConstants.JDBC_URL));
			dataSrcBuild.setUsername(environ.getProperty(BaseCommonConstants.JDBC_USERNAME));
			dataSrcBuild.setPassword(decPassword);
			dataSrcBuild
					.setMaximumPoolSize(Integer.parseInt(environ.getProperty(BaseCommonConstants.JDBC_MAX_POOL_SIZE)));

			dataSrcBuild.setMinimumIdle(Integer.parseInt(environ.getProperty(BaseCommonConstants.JDBC_MIN_POOL_SIZE)));

			return new HikariDataSource(dataSrcBuild);
			

		} catch (Exception ex) {
			log.debug("Exception in DBManager() : {}  ", ex.getMessage(), ex);
		}
		return null;
	}
}
