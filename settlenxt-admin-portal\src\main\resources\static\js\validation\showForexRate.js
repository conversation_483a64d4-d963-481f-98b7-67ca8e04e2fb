$(document).ready(function () {
	var cursorPosition = null;
	$("#tabnew").DataTable({

		initComplete: function () {
			var api = this.api();

			// For each column
			api
				.columns()
				.eq(0)
				.each(function (colIdx) {
					//If first column to be skipped to include the filter for the reasons line check box
					if (!(colIdx == 0 && firstColumnToBeSkippedInFilterAndSort)) {
						// Set the header cell to contain the input element
						var cell = $('#tabnew thead tr th').eq(
							$(api.column(colIdx).header()).index()
						);
						var title = $(cell).text();


						if ((colIdx < actionColumnIndex)) {
							$(cell).html(title + '<br><input class="search-box"   type="text" />');

							// On every keypress in this input
							$(
								'input',
								$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
							)
								.off('keyup change')
								.on('change', function (_e) {
									// Get the search value
									$(this).attr('title', $(this).val());
									var regexr = '({search})';

									cursorPosition = this.selectionStart;
									// Search the column for that value
									api
										.column(colIdx)
										.search(
											this.value != ''
												? regexr.replace('{search}', '(((' + this.value + ')))')
												: '',
											this.value != '',
											this.value == ''
										)
										.draw();
								})
								.on('click', function (e) {
									e.stopPropagation();
								})
								.on('keyup', function (e) {
									e.stopPropagation();

									$(this).trigger('change');
									if (cursorPosition && cursorPosition != null) {
										$(this)
											.focus()[0]
											.setSelectionRange(cursorPosition, cursorPosition);
									}
								});
						} else {
							$(cell).html(title + '<br> &nbsp;');
						}
					}
				});
			$('#tabnew_filter').hide();

		},
		// Disabled ordering for first column in case
		columnDefs: [
			{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
		],
		order: [],
		dom: 'lBfrtip',
		buttons: [
			{
				extend: 'excelHtml5',
				text: 'Export',
				filename: 'ForexRate',
				header: 'false',
				title: null,
				sheetName: 'ForexRate',
				className: 'defaultexport',
				//exportOptions: {
				//columns: 'th:not(:last-child)'
				//}
			},
			{
				extend: 'csvHtml5',
				text: 'Export',
				filename: 'ForexRate',
				header: 'false',
				title: null,
				sheetName: 'ForexRate',
				className: 'defaultexport',
				//exportOptions: {
				//columns: 'th:not(:last-child)'
				//}
			}
		],

		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true
	});

	$("#excelExport").on("click", function () {
		$(".buttons-excel").trigger("click");
	});
	$("#csvExport").on("click", function () {
		$(".buttons-csv").trigger("click");
	});

	$("#clearFilters").on("click", function () {
		$(".search-box").each(function () {
			$(this).val("");
			$(this).trigger("change");
		});
	});
	$('#networkId').on('keyup keypress blur change', function () { searchForexRate('N'); })
	$('#settleDate').on('keyup keypress blur change', function () { searchForexRate('N'); })
	$("#selectAll").click(function () {
		$("input[type=checkbox]").prop('checked', $(this).prop('checked'));
		if ($('#selectAll').is(':checked')) {

			$("#toggleModalNews").modal('show');
		}
		else {
			$("#toggleModalNews").modal('hide');

			var ele = document.getElementsByName('type');
			for (var i of ele) {
				if (i.type == 'checkbox')
					i.checked = false;
			}
		}


		var referenceNoList = document.getElementById("newsIds");
		referenceNoList.innerHTML = "Total " + referenceNoListPendings.length + "     " + "records are selected";
		// Disabling SelectAll option diabling



	});
	if (referenceNoListPendings.length == 0) {
		if (typeof selectAll != "undefined") {
			document.getElementById("selectAll").disabled = true;
			document.getElementById("submitButtonA").disabled = true;
			document.getElementById("submitButtonR").disabled = true;
		}
	}
	$("#settleDate").datepicker({
		//dateFormat : "dd-mm-yy",
		dateFormat: "yy-mm-dd",
		changeMonth: true,
		changeYear: true,
		maxDate: 0,
		yearRange: "2021:2099",
		//minDate : 0,
		onClose: function () {
			$("#settleDate").datepicker("option", "maxDate", "0");
			//
		}
	});

});


function getPendingForexRate() {

	$('#userType').val($('#userType').val());
	var url = '/forexRateForApproval';
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "_vTransactToken," + tokenValue;
	postData(url, data);
}
function submitForm(url, userType) {
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "_vTransactToken," + tokenValue + ",userType," + userType;
	postData(url, data);
}
function viewForexRate(forexRateId, type) {
	if (type == 'V')
		var url = '/viewForexRate';
	else if (type == 'P')
		url = '/viewPendingForexRate';
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "forexRateId," + forexRateId + ",userType," + "P" + ",_vTransactToken," + tokenValue;
	postData(url, data);
}


function mySelect() {


	var array = [];

	$("input:checkbox[name=type]:checked").each(function () {
		array.push($(this).val());
	});


	var referenceNoList = document.getElementById("newsIds");

	if (array.length == referenceNoListPendings.length) {
		$('#selectAll').prop('checked', true);

		referenceNoList.innerHTML = "Total " + referenceNoListPendings.length + "     " + "records are selected";



		$("#toggleModalNews").modal('show');
	}
	else {
		$("#toggleModalNews").modal('hide');

	}

}

function ApproveOrRejectBulkForexRate(type, action) {

	var url = '/approveForexRateForBulk';


	var array = [];

	if (action == 'No') {
		$("input:checkbox[name=type]:checked").each(function () {
			array.push($(this).val());
		});
	}
	else if (action == 'All') {
		array = referenceNoListPendings;
	}



	var referenceIdIdList = "";
	for (var i of array) {
		referenceIdIdList = referenceIdIdList + i + "|"
			;
	}
	var data;
	if (type == 'A') {

		data = "status," + "A" + ",bulkApprovalReferenceNoList," + referenceIdIdList;
	}
	else if (type == 'R') {

		data = "status," + "R" + ",bulkApprovalReferenceNoList," + referenceIdIdList;
	}


	postData(url, data);

}


function deselectAll() {
	$('#selectAll').prop('checked', false);
	var ele = document.getElementsByName('type');
	let i = 0;
	for (i of ele) {
		if (i.type == 'checkbox')
			i.checked = false;
	}
}

function viewCardInfoRej(forexRateId) {
	var url = '/forexRateConfig';
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "forexRateId," + forexRateId + ",userType," + $('#userType').val() + ",_vTransactToken," + tokenValue;
	postData(url, data);
}


function Edit(forexRateId, url) {
	var tokenValue = "lIpLsRjLtLDPSzoS2xPf9WXiF/M=";
	var data = "forexRateId," + forexRateId + ",_vTransactToken," + tokenValue + ",status,"
		+ status;
	postData(url, data);
}

function clearValues() {
	$('#networkId').val('');
	$('#settleDate').val('');
	var data = "";
	var url = '/forexRates';
	postData(url, data);
}
function searchForexRate(type) {
	$("#err" + 'networkId').hide();
	$("#err" + 'settleDate').hide();
	var url = '/searchForexRate';
	if ($('#settleDate').val() == '' || $('#networkId').val() == '') {
		if ($('#settleDate').val() == '') {
			$("#err" + 'settleDate').find('.error').html("Please Select a Date");
			$("#err" + 'settleDate').show();
		}
		if ($('#networkId').val() == '') {
			$("#err" + 'networkId').find('.error').html("Please Select NetworkId");
			$("#err" + 'networkId').show();
		}

	}
	else if (type == 'Y') {
		var data = "networkId," + $('#networkId').val() + ",settleDate," + $('#settleDate').val();
		postData(url, data);
	}
}