package org.npci.settlenxt.adminportal.repository;


import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.adminportal.dto.ReportStatusDTO;
import org.npci.settlenxt.portal.common.dto.AuditBatchLogDTO;
import org.npci.settlenxt.portal.common.repository.BaseReportRepository;

@Mapper
public interface ReportRepository extends BaseReportRepository {

	 List<AuditBatchLogDTO> reportGeneratedList(@Param("auditBatchLogDTO") AuditBatchLogDTO auditBatchLogDTO,
			@Param("isFileType") Boolean isFileType, @Param("isMemberName") Boolean isMemberName,
			@Param("fileTypeList") List<String> fileTypeList);

	 List<AuditBatchLogDTO> generatedList(@Param("auditBatchLogDTO") AuditBatchLogDTO auditBatchLogDTO, @Param("isFileType") Boolean isFileType, @Param("isCycle") Boolean isCycle,@Param("isMemberName") Boolean isMemberName);

	 List<String> getProductId();

	 List<ReportStatusDTO> getReportStatusData(@Param("reportStatusDTO")ReportStatusDTO reportStatusDTO , @Param("isStatus") boolean isStatus,
			@Param("isReportType") boolean isReportType, @Param("isParticipant") boolean isParticipant);

	 List<ReportStatusDTO> getStatusCount(@Param("productCode") String productCode,
			@Param("cycleDate") String cycleDate,
			@Param("cycleNumber") String cycleNumber);

	 List<ReportStatusDTO> getStatusDataForProd(ReportStatusDTO reportStatusDTO);

	 List<String> getSettlementCycleListBasedOnProdCode(@Param("productCode") String productCode);

	 ReportStatusDTO getLatestCycleDN(ReportStatusDTO reportStatusDTO);
	 ReportStatusDTO getCycleInfo(String productCode);

}
