let escaltionIDs = [];
function mySelect() {


	var array;

	$("input:checkbox[name=type]:checked").each(function() {
		array.push($(this).val());
	});




}

function ApproveOrRejectEscalationBulk(type, action) {

	var url = '/approveOrRejectEscalationBulk';

	var array = [];
	var data = "";

	if (action == 'No') {
		$("input:checkbox[name=type]:checked").each(function() {
			array.push($(this).val());
		});
	}
	else if (action == 'All') {
		if (escaltionIDs.length > 0) {
			array = escaltionIDs;
		} else {
			array = memberIdListPendings;
		}
	}


	var memberIdList = "";
	for (var i of array) {
		memberIdList = memberIdList + i + "|"
			;
	}

	if (array.length != 0) {
		if (type == 'A') {

			data = "status," + "A" + ",bulkApprovalMemberIdList," + memberIdList;
		}
		else if (type == 'R') {

			data = "status," + "R" + ",bulkApprovalMemberIdList," + memberIdList;
		}

		$('#errorStatus2').hide();
		$('#errorStatus2').html("");
		postData(url, data);



	}
	else {
		$('#snxtSuccessMessage').hide();

		$('#errorStatus2').html("Please select one or more records to bulk approve/reject records");
		$('#errorStatus2').show();
		$('#jqueryError2').show();
	}


}

function deselectAll() {

	$('#selectAll').prop('checked', false);
	var ele = document.getElementsByName('type');

	for (let i of ele) {
		if (i.type == 'checkbox')
			i.checked = false;
	}

}

function submitForm(url) {
	var data = "";
	urlPostAction(data, url);

}



function validateFirstName(id, msgID) {

	var name = (document.getElementById(id).value);
	var errFirstName = document.getElementById(msgID);
	var regEx = /^[a-zA-Z ]*$/;

	if (name.trim().length == 0 && !(id.indexOf("Level_4") > 0 || id.indexOf("Level_5") > 0)) {
		errFirstName.className = 'error';
		errFirstName.innerHTML = "Mandatory field";

		return false;

	} else if (!regEx.test(name) && !(id.indexOf("Level_4") > 0 || id.indexOf("Level_5") > 0)) {

		errFirstName.className = 'error';

		errFirstName.innerHTML = "Enter alphabets only";

		return false;

	} else {

		errFirstName.className = 'error';
		errFirstName.innerHTML = "";

	}
	document.getElementById(id).value = (document.getElementById(id).value)
		.replace("/^\s*|\s*$/g", '');
	return true;
}

function validateAddress(id) {
	console.log("id is  " + document.getElementById(id));
	if (document.getElementById(id) != "" && document.getElementById(id) != null) {
		var name = (document.getElementById(id).value);


		if (name.includes(',')) {
			var address = name.split(",");
			var streetAddress = "";

			for (var i of address) {
				streetAddress = streetAddress + i + "*";

			}
			document.getElementById(id).value = streetAddress;
		}
		else {

			document.getElementById(id).value = name;
		}





	}
}

function validateEmailId(id, msgID) {

	var emailId = (document.getElementById(id).value).replace("/^\s*|\s*$/g", '');
	var errEmailId = document.getElementById(msgID);
	var regEx = /^[_A-Za-z0-9-]+(\.[_A-Za-z0-9-]+)*@[A-Za-z0-9]+(\.[A-Za-z0-9]+)*(\.[A-Za-z]{2,})$/;


	if (emailId.trim().length == 0 && !(id.indexOf("Level_4") > 0 || id.indexOf("Level_5") > 0)) {
		errEmailId.className = 'error';


		return false;

	} else if (!regEx.test(emailId) && !(id.indexOf("Level_4") > 0 || id.indexOf("Level_5") > 0)) {

		errEmailId.className = 'error';
		errEmailId.innerHTML = "Enter valid Email ID";
		return false;

	} else {

		errEmailId.className = 'error';
		errEmailId.innerHTML = "";

	}

	return true;
}

function validateMobileNo(id, msgID) {
	var mobileNo = (document.getElementById(id).value)
		.replace("/^\s*|\s*$/g", '');
	var errMobileNo = document.getElementById(msgID);
	var regEx = /^\d*$/gm;

	if (mobileNo.trim().length == 0 && !(id.indexOf("Level_4") > 0 || id.indexOf("Level_5") > 0)) {
		errMobileNo.className = 'error';
		errMobileNo.innerHTML = "Mandatory field";
		return false;
	} else if (!regEx.test(mobileNo) && !(id.indexOf("Level_4") > 0 || id.indexOf("Level_5") > 0)) {

		errMobileNo.className = 'error';
		errMobileNo.innerHTML = "Enter valid Mobile No";
		return false;

	} else if (mobileNo.trim().length < 10 && !(id.indexOf("Level_4") > 0 || id.indexOf("Level_5") > 0)) {
		errMobileNo.className = 'error';
		errMobileNo.innerHTML = "Please Enter valid Mobile No";
		return false;
	} else {
		errMobileNo.className = 'error';
		errMobileNo.innerHTML = "";
	}

	return true;
}

function validateContactNo(id, msgID) {

	var contactNo = (document.getElementById(id).value).replace("/^\s*|\s*$/g",
		'');
	var errContactNo = document.getElementById(msgID);
	var regEx = /^\d*$/gm;

	if (contactNo.trim().length == 0 && !(id.indexOf("Level_4") > 0 || id.indexOf("Level_5") > 0)) {
		errContactNo.className = 'error';
		errContactNo.innerHTML = "Mandatory field";
		return false;
	} else if (!regEx.test(contactNo) && !(id.indexOf("Level_4") > 0 || id.indexOf("Level_5") > 0)) {
		errContactNo.className = 'error';
		errContactNo.innerHTML = "Please Enter valid Phone No";
		return false;

	} else if (contactNo.trim().length < 10 && !(id.indexOf("Level_4") > 0 || id.indexOf("Level_5") > 0)) {

		errContactNo.className = 'error';
		errContactNo.innerHTML = "Enter valid Phone No";
		return false;

	} else {

		errContactNo.className = 'error';
		errContactNo.innerHTML = "";
	}

	return true;
}

function validateThreeLevels() {
	var check;
	for (let d = 1; d < 6; d++) {
		for (let l = 1; l < 6; l++) {

			check = validateFieldsFromCommonLib(check, d, l);



		}
	}

	$(".error").each(function() {
		if ($(this).text().trim().length > 0) {
			$(this).closest('.collapse').parents('.collapse').addClass('in');
			$(this).closest('.collapse').parents('.collapse').css('height', '');
			$(this).closest('.collapse').addClass('in');
			$(this).closest('.collapse').css('height', '');
		}
	});

	return check;
}


function validateFieldsFromCommonLib(check, d, l) {
	check = false;
	if (!validateFromCommonVal("name" + d + "Level_" + l, false, 'AlphabetWithSpace', 50, false)) {
		check = true;
	}
	if (!validateFromCommonVal("designation" + d + "Level_" + l, false, 'AlphabetWithSpace', 50, false)) {
		check = true;
	}
	if (!validateFromCommonVal("email" + d + "Level_" + l, false, 'MailValidation', 250, false)) {
		check = true;
	}
	if (!validateFromCommonVal("landline" + d + "Level_" + l, false, 'NumericsOnlyBtw10to12', 12, false)) {
		check = true;
	}
	if (!validateFromCommonVal("mobile" + d + "Level_" + l, false, 'Number', 10, true)) {
		check = true;
	}
	if (!validateFromCommonVal("address" + d + "Level_" + l, false, 'streetaddress', 50, false)) {
		check = true;
	}
	if (!validateFromCommonVal("pinCode" + d + "Level_" + l, false, 'Number', 6, true)) {
		check = true;
	}
	return check;
}

function getEscalationList() {
	let url = '/pendingEscalationForApproval';
	var data = "";
	urlPostAction(data, url);
}

function viewEscalationInfo(memberId, url) {

	var data = "memberId," + escape(memberId);
	postData(url, data);
}


function userAction(action) {

	var data = "memberId," + $('#memberId').val() + ",status,"
		+ status;
	postData(action, data);
}

function actionForm(url) {

	var data = "";
	urlPostAction(data, url);

}

function clearFilter() {
	$(".search-box").each(function() {
		$(this).val("");
		$(this).trigger("change");
	});


}


$(document).ready(function() {


	var cursorPosition = null;
	/* Initialization of datatables */
	$(document).ready(function() {

		$("#tabnew").DataTable({

			initComplete: function() {
				var api = this.api();

				// For each column
				api
					.columns()
					.eq(0)
					.each(function(colIdx) {
						//If first column to be skipped to include the filter for the reasons line check box 
						if (!(colIdx == 0 && firstColumnToBeSkippedInFilterAndSort)) {
							// Set the header cell to contain the input element
							var cell = $('#tabnew thead tr th').eq(
								$(api.column(colIdx).header()).index()
							);
							var title = $(cell).text();
							if (colIdx < actionColumnIndex) {

								$(cell).html(title + '<br><input class="search-box"   type="text" />');

								// On every keypress in this input
								$(
									'input',
									$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
								)
									.off('keyup change')
									.on('change', function(_e) {
										// Get the search value
										$(this).attr('title', $(this).val());
										var regexr = '({search})';

										cursorPosition = this.selectionStart;
										// Search the column for that value
										api
											.column(colIdx)
											.search(
												this.value != ''
													? regexr.replace('{search}', '(((' + this.value + ')))')
													: '',
												this.value != '',
												this.value == ''
											)
											.draw();
										escaltionIDs = [];
										if (this.value != '') {
											var i = 0;
											$("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
												escaltionIDs.push(this.value);
												i++;
											});
										}
										else {
											escaltionIDs = [];
										}
									})
									.on('click', function(e) {
										e.stopPropagation();
									})
									.on('keyup', function(e) {
										e.stopPropagation();

										$(this).trigger('change');
										if (cursorPosition && cursorPosition != null) {
											$(this)
												.focus()[0]
												.setSelectionRange(cursorPosition, cursorPosition);
										}
									});
							} else {
								$(cell).html(title + '<br> &nbsp;');
							}
						}
					});
				$('#tabnew_filter').hide();

			},
			// Disabled ordering for first column in case
			columnDefs: [
				{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
			],
			"order": [],
			dom: 'lBfrtip',
			buttons: [
				{
					extend: 'excelHtml5',
					text: 'Export',
					filename: 'Escalation Matrix',
					header: 'false',
					title: null,
					sheetName: 'Escalation Matrix',
					className: 'defaultexport',
					exportOptions: {
						columns: 'th:not(:last-child)'
					}
				},
				{
					extend: 'csvHtml5',
					text: 'Export',
					filename: 'Escalation Matrix',
					header: 'false',
					title: null,
					sheetName: 'Escalation Matrix',
					className: 'defaultexport',
					exportOptions: {
						columns: 'th:not(:last-child)'
					}
				}

			],

			searching: true,
			info: true,
			lengthChange: true,
			bLengthChange: true,
		});
	});

	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});

	$("#excelExport").on("click", function() {
		$(".buttons-excel").trigger("click");
	});

	$("#csvExport").on("click", function() {
		$(".buttons-csv").trigger("click");
	});

	$("#clearFilters").on("click", function() {
		clearFilter();
	});



	$('#memberId').selectize({
		sortField: 'text'
	});



	$('#addEditEscalation').on('keyup change paste', 'input, select, textarea', function() {
		$(this).closest('#addEditEscalation').data(
			'changed', true);
	});

	$('.jqueryError').text("");
	$('.jqueryError').hide();

	$('.submitBtn').prop('disabled', true);

	if (($(".alert-success").text().trim().length > 0)) {
		$('#addEditEscalation :input').prop('disabled', true);
		$('.ack').hide();
	}

	$('.submitBtn').click(function() {

		$(".validationFailed").text("");

		var check = validateThreeLevels();

		if (!check) {

			if ($('#addEditEscalation').data('changed')) {
				$('#errorStatus2').html('');
				$('#errorStatus2').hide();
				$('#jqueryError2').hide();
				if (($(".error").text().trim().length <= 0)) {
					$('button').prop('disabled', true);
					var tokenValue = document.getElementsByName("_TransactToken")[0].value;
					$('#addEditEscalation').attr('action', 'updateEscalation?_TransactToken=' + tokenValue);
					$('#addEditEscalation').submit();
				}
				else {
					$(".validationFailed").text("Form Validation Failed");
					$(".validationFailed").delay(2000).fadeOut();
				}
			} else {
				$('#errorStatus2').html('No data modified');
				$('#jqueryError2').show();

				$('#jqueryError').show();
				return false;
			}
		}
	});

	$('#ack').change(function() {
		if ($('#ack').is(':checked'))
			$('.submitBtn').prop('disabled', false);
		else
			$('.submitBtn').prop('disabled', true);

	});

	$('.toggleCollapse').click(function() {

		if ($(this).hasClass('glyphicon-collapse-down')) {
			$('.collapse').addClass('in');
			$('.collapse').css('height', '');
			$(this).removeClass('glyphicon-collapse-down');
			$(this).addClass('glyphicon-collapse-up');
			$(this).attr('title', 'Collapse All In');
		}
		else {
			$('.collapse').removeClass('in');
			$('.collapse').css('height', 'auto');
			$(this).removeClass('glyphicon-collapse-up');
			$(this).addClass('glyphicon-collapse-down');
			$(this).attr('title', 'Collapse All Out');
		}
	});

	$('#memberId').change(function() {
		if ((($('#memberId option:selected').val()).trim().length !== 0)) {

			var data = "memberId," + $('#memberId option:selected').val();
			postData('/addEscalation', data);
		}
	});





	$("#selectAll").click(function() {

		$("input[type=checkbox]").prop('checked', $(this).prop('checked'));
		if ($('#selectAll').is(':checked')) {
			//
			
		}
		else {


			var ele = document.getElementsByName('type');
			for (var val2 of ele) {
				if (val2.type == 'checkbox')
					val2.checked = false;
			}
		}


		var memberIdList = document.getElementById("memberIds");
		memberIdList.innerHTML = memberIdListPendings.length + "     " + "records are selected";


		if (escaltionIDs.length > 0) {
			memberIdList.innerHTML = escaltionIDs.length + "     " + "records are selected";

			if ($('#selectAll').is(':checked')) {
				$("#toggleModal").modal('show');

			}
			else {
				$("#toggleModal").modal('hide');

			}
		} else {
			var i = 0;
			var escaltionID2 = [];
			$("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
				escaltionID2.push(this.value);
				i++;
			});

			if (escaltionID2.length > 0) {


				if (memberIdListPendings.length > 0) {

					memberIdList.innerHTML = memberIdListPendings.length + "     " + "records are selected";

					if ($('#selectAll').is(':checked')) {
						$("#toggleModal").modal('show');

					}
					else {
						$("#toggleModal").modal('hide');

					}
				}
			}
		}


	});

});

