<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
        namespace="org.npci.settlenxt.adminportal.repository.NetworkFileRepository">

    <select id="getNetworkFileDetails" resultType="NetworkFileSummaryDTO">
        SELECT  COUNT(CASE WHEN status = 'Suspended' THEN 1 END) AS totalSuspended,
                COUNT(CASE WHEN status = 'Outgoing' THEN 1 END) AS totalOutGoing,
                COUNT(CASE WHEN status = 'Acknowledged' THEN 1 END) AS totalAcknowledged,
                COUNT(CASE WHEN status = 'Rejected' THEN 1 END) AS totalRejected,
                COUNT(*) AS totalTransactions
        FROM ${tableName}
        WHERE network=#{networkType} AND file_gen_date BETWEEN #{fromDate} AND #{toDate}
        <if test="status!=null"> AND status=#{status}</if>;
    </select>

    <select id="getAllTransactions" resultType="NetworkOutgoingModel">
        SELECT
            func_code AS funcCode, transaction_id AS tranId,
            batch_num AS batchNum, seq_num AS seqNum, encrypted_pan_num AS encryptedPanNum,
            charge_date AS chargeDate, rrn AS rrn, transaction_amt AS transactionAmt,
            status AS status, file_gen_date AS fileGenDate,
            ref_num AS refNum, net_charge_amt AS netChargeAmt, gross_settl_amt AS grossSettlAmt,
            net_settl_amt AS netSettlAmt, interchange_commission_settl_amt AS interchangeCommissionSettlAmt,
            network_ref_id AS networkRefId, mcc_code AS mccCode, field_name AS fieldName,
            field_value AS fieldValue, suspension_code AS suspensionCode, suspension_message AS suspensionMessage,
            rejection_code AS rejectionCode, rejection_message AS rejectionMessage,
            created_on AS createdOn, last_updated_on AS lastUpdatedOn,
            is_multileg_txn AS isMultilegTxn, outgoing_file_name AS outgoingFileName,
            network
        FROM ${tableName}
        WHERE network=#{networkType} AND file_gen_date BETWEEN #{fromDate} AND #{toDate}
        <if test="status!=null"> AND status=#{status}</if>;
    </select>

</mapper>
