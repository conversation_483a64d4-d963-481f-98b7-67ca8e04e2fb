<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript"
	src="./static/js/validation/viewSettlementCycle.js"></script>

<form:form onsubmit="removeSpace(this); encodeForm(this);" method="POST"
	id="viewApproveActionCode" modelAttribute="settlementCycleConfigDTO"
	action="/approveActionCode" autocomplete="off">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> 
                <span data-i18n="Data"><spring:message code="settlement.viewscreen.title" /></span></strong>
                <sec:authorize access="hasAuthority('Edit Settlement Cycle')">
                    <c:if test="${settlementCycleConfigDTO.requestState ne 'P'}">
                <div  class="icon_bar">
					<a data-toggle="tooltip" title="Edit"

						onclick="editSettlementCycleInfo('${settlementCycleConfigDTO.srNo}','/editSettlementCycle','${settlementCycleConfigDTO.requestState}')"

						href="#"><img src="./static/images/edit-grey.png" alt="edit"></a>
                </div>
                </c:if>
            </sec:authorize>
		</div>

		<div class="panel-body">
			<form:hidden path="srNo" value="${settlementCycleConfigDTO.srNo}" />

			<table class="table table-striped" style="font-size: 12px">
			
<caption style="display:none;">SETTLEMENT CONFIG</caption>

			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
						
						
						<td><label><spring:message code="settlement.srNo.msg" /><span
								style="color: red"></span></label></td>
						<td>${settlementCycleConfigDTO.srNo}</td>

						<td><label><spring:message code="settlement.cycleNumber.msg" /><span
								style="color: red"></span></label></td>
						<td>${settlementCycleConfigDTO.cycleNumber}</td>

						<td><label><spring:message code="settlement.startHour.msg" /><span
								style="color: red"></span></label></td>
						<td>${settlementCycleConfigDTO.startHour}</td>

						
						<td><label><spring:message code="settlement.endHour.msg" /><span
                            style="color: red"></span></label></td>
                    <td>${settlementCycleConfigDTO.endHour}</td>



					</tr>

                    <tr>
						
						<td><label>Active<span
                            style="color: red"></span></label></td>
                       
                        <td>${settlementCycleConfigDTO.isActiveValue =='t' ? 'Yes' : 'No'}</td>
                        <td><label><spring:message code="settlement.DateIncrement.msg" /><span
                            style="color: red"></span></label></td>
                        <td>${settlementCycleConfigDTO.dateIncrement}</td>

                        <td><label><spring:message code="settlement.ICNkeys.msg" /><span
								style="color: red"></span></label></td>
						<td>${settlementCycleConfigDTO.totalICNkeys}</td>
						
						 <td><label>Product ID<span
								style="color: red"></span></label></td>
						<td>${settlementCycleConfigDTO.productId}</td>

                        
					</tr>

                    <sec:authorize access="hasAuthority('Approve Settlement Cycle')">
                        <c:if test="${settlementCycleConfigDTO.requestState eq 'P'}">
                    <tr>
                        <td colspan="6"><div class="panel-heading-red  clearfix">
                                <strong><span class="glyphicon glyphicon-info-sign"></span> <span
                                    data-i18n="Data"><spring:message
                                            code="settlement.approvalPanel.title" /></span></strong>
                            </div></td> 
                    </tr>
                    <tr>
                        <td><label><spring:message
                                    code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
                        <td><select name="select" id="apprej">
                                <option value="N"><spring:message
                                        code="AM.lbl.select" /></option>
                                <option value="A" id="approve"><spring:message
                                        code="AM.lbl.approve" /></option>
                                <option value="R" id="reject"><spring:message
                                        code="AM.lbl.reject" /></option>
                        </select></td>
                        <td>
                            <div style="text-align:center" >
                                <label><spring:message code="AM.lbl.remarks" /><span
                                    style="color: red">*</span></label>
                            </div>
                        </td>
                        <td colspan="5"><textarea rows="4" cols="50"
                                maxlength="100" id="rejectReason"></textarea>
                            <div id="errorrejectReason" class="error"></div></td>
                    </tr>
                    </c:if>
                    </sec:authorize>

                </tbody>
            </table>
			
                <div style="text-align:center">
                    <sec:authorize access="hasAuthority('Approve Settlement Cycle')">
                        <c:if test="${settlementCycleConfigDTO.requestState eq 'P'}">
								<input name="button10" type="button" class="btn btn-success"
									id="approveRole"
									value="<spring:message
							code="ifsc.submitBtn" />"
									onclick="approve('/approveOrRejectSettlement')" />
                                    </c:if>
                                    </sec:authorize>
				<button type="button" class="btn btn-danger"
					onclick="submitForm('/settlementCycle');">
					<spring:message code="ifsc.backBtn" />
				</button>
			</div>
	</div>
</div>
</form:form>
