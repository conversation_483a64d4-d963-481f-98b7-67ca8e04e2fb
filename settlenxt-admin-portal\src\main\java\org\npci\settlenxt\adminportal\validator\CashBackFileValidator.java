package org.npci.settlenxt.adminportal.validator;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.repository.CashbackFileUploadRepository;
import org.npci.settlenxt.adminportal.validator.service.IValidationService;
import org.npci.settlenxt.portal.common.dto.CashBackFileUploadDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@EnableScheduling
@PropertySource("/application.properties")
public class CashBackFileValidator {
	
	private static final Logger log = LogManager.getLogger(CashBackFileValidator.class);
	
	@Autowired
	private Environment environment;
	
	@Autowired
	private IValidationService validationService;
	
	@Autowired
	private CashbackFileUploadRepository cashbackFileRepository;
	
	@Scheduled(cron = "${FILE_VALIDATION_BATCH_TIME_CRON}")
	private void fileValidator() {
		if (Boolean.TRUE.toString().equals(environment.getProperty("CashbackFileValidation"))) {
			Instant start = Instant.now();
			ExecutorService executorService = Executors.newFixedThreadPool(4);
			List<CashBackFileUploadDTO> cashbackFileList = cashbackFileRepository.getPendingCashBackFiles(
					environment.getProperty("SITE_ID"), environment.getProperty("INSTANCE_ID"));
			List<Future<String>> result = new ArrayList<>();
			try {
				for (CashBackFileUploadDTO fileUploadDTO : cashbackFileList) {
					result.add(executorService.submit(new AsyncFileValidator(validationService, null, fileUploadDTO)));
				}
			} finally {
				executorService.shutdown();
				if (executorService.isShutdown()) {
					Instant end = Instant.now();
					Duration timeElapsed = Duration.between(start, end);
					LocalTime t = LocalTime.MIDNIGHT.plus(timeElapsed);
					String diff = DateTimeFormatter.ofPattern("m:ss:A").format(t);
					log.info("Total Time taken: {}", diff);
				}
			}
		}
	}	
}
