package org.npci.settlenxt.adminportal.repository;

import java.time.LocalDate;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.adminportal.dto.FeeDTO;
import org.npci.settlenxt.portal.common.dto.ActionCodeDTO;
import org.npci.settlenxt.portal.common.dto.BinDTO;
import org.npci.settlenxt.portal.common.dto.DisputeDocumentDTO;
import org.npci.settlenxt.portal.common.dto.ParticipantDTO;
import org.npci.settlenxt.portal.common.dto.SettlementBinDTO;
import org.npci.settlenxt.portal.common.dto.TxnSettlementDTO;
import org.npci.settlenxt.portal.common.repository.BaseTransactionDetailRepository;

@Mapper
public interface TransactionDetailRepository extends BaseTransactionDetailRepository {

	 List<ParticipantDTO> getParticipantIdList();
	
	 List<TxnSettlementDTO> getTransactionDetails(@Param("originalTableName") String originalTableName,
			@Param("funcCode") String funcCode, @Param("tokenPan") String tokenPan, @Param("rrn") String rrn,
			@Param("partId") String partId);
	
	 List<TxnSettlementDTO> getPresentmentDetails(@Param("originalTableName") String originalTableName,
			@Param("funcCode") String funcCode, @Param("tokenPan") String tokenPan, @Param("rrn") String rrn,
			@Param("partId") String partId);
	
	 List<TxnSettlementDTO> getRevTranDetails(@Param("funcCode") String funcCode, @Param("tokenPan") String tokenPan, @Param("rrn") String rrn,
			@Param("partId") String partId,@Param("fromDate") LocalDate fromDate,@Param("toDate") LocalDate toDate);
	
	 int saveTxnActionDetail(@Param("model") TxnSettlementDTO transactionDetail);
	
	 List<TxnSettlementDTO> getTransactionDetails(TxnSettlementDTO txnSettlementDTO);
	
	 List<TxnSettlementDTO> getTransactionDetailsStage();
	
	 List<TxnSettlementDTO> getApprovedRejectedTxnDetails();

	 TxnSettlementDTO getTxnInfoStaging(String txnId, String status);
	 
	 List<TxnSettlementDTO> getPendingDisputeTxnList(@Param("txnId") String txnId,@Param("orgTranDate") LocalDate orgTranDate);
	
	 String getAcqSettlementBin(String acqBin);
	
	 String getIssSettlementBin(String issBin);
	
	 TxnSettlementDTO getDisputeTxnStatus(@Param("acqRefData") String acqRefData,@Param("partId") String partId,@Param("fromDate") LocalDate fromDate);
	
	 List<TxnSettlementDTO> getPendingDisputeNpciFundCashRevInfo(@Param("txnId") String txnId);
	
	 List<FeeDTO> getFeeNameList();

	 List<SettlementBinDTO> getSettlementBinList();
	
	 List<TxnSettlementDTO> getPendingDisputeBulkTxnInfo(@Param("txnId") String txnId,@Param("orgTranDate") LocalDate orgTranDate);

	 List<BinDTO> getAcquirerIdList();

	 List<BinDTO> getIssuerIdList();
	
	 ActionCodeDTO getMtiFuncCode(String actionCode);
	
	 int saveFundCollectDisburse(@Param("model") TxnSettlementDTO disputeTxnDto);
	
	 String getSettlementBinIss(String participantId,String entityType,String instId);
	
	 String getSettlementBinAcq(String participantId,String entityType,String instId);
	
	
	
	 List<TxnSettlementDTO> getDisputeTranDetailsStage(@Param("funcCode") String funcCode, @Param("tokenPan") String tokenPan, @Param("rrn") String rrn,
			@Param("fromDate") LocalDate fromDate,@Param("toDate") LocalDate toDate, @Param("status") String status);
	
	 int insertDocPath(@Param("docfilePathList") List<DisputeDocumentDTO> docfilePathList);

	 int insertDocPathStg(@Param("docfilePathList") List<DisputeDocumentDTO> docfilePathList);	
	
	 void updateDisputeDocFilePath(String txnId);
	
	 void updateDisputeDocFilePathStg(String txnId);

		List<TxnSettlementDTO> getIntrntlTransactionDetails(
				@Param("txnSettlementDTO") TxnSettlementDTO txnSettlementDTO, @Param("rrn") String rrn);

		List<TxnSettlementDTO> getIntrntlRevTranDetails(@Param("txnSettlementDTO") TxnSettlementDTO txnSettlementDTO,
				@Param("rrn") String rrn);

		List<TxnSettlementDTO> getIntrntlPresentmentDetails(
				@Param("txnSettlementDTO") TxnSettlementDTO txnSettlementDTO, @Param("rrn") String rrn);
	
}
