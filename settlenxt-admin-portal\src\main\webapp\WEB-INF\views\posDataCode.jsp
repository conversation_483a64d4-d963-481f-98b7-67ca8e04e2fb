<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<title></title>
<script>
function exportPosData(){
	
	   const dataMap = new Map([
		   ['',''],
		   ['Card Data Input Capability', '${transactionDetailSummary.posCrdDataInCap}' != '' ? '${transactionDetailSummary.posCrdDataInCap}' : "N/A"],
		   ['Cardholder Authentication Capability','${transactionDetailSummary.posCrdHldrAuthC}' != '' ? '${transactionDetailSummary.posCrdHldrAuthC}' : "N/A"],
		   ['Card Capture Capability','${transactionDetailSummary.posCardCaptCap}' != '' ? '${transactionDetailSummary.posCardCaptCap}' : "N/A"],
		   ['Terminal Operating Environment','${transactionDetailSummary.posOpenEnv}' != '' ? '${transactionDetailSummary.posOpenEnv}' : "N/A"],
		   ['Cardholder Present Data','${transactionDetailSummary.posCrdhldrPresent}' != '' ? '${transactionDetailSummary.posCrdhldrPresent}' : "N/A"],
		   ['Card Present Data','${transactionDetailSummary.posCardPres}' != '' ? '${transactionDetailSummary.posCardPres}' : "N/A"],
		   ['Card Data Input Mode','${transactionDetailSummary.posCrdDatInMod}' != '' ? '${transactionDetailSummary.posCrdDatInMod}' : "N/A"],
		   ['Cardholder Authentication Method','${transactionDetailSummary.posCrdHldrAuthMeth}' != '' ? '${transactionDetailSummary.posCrdHldrAuthMeth}' : "N/A"],
		   ['Cardholder Authentication Entity','${transactionDetailSummary.posCrdHldrAuth}' != '' ? '${transactionDetailSummary.posCrdHldrAuth}' : "N/A"],
		   ['Card Data Output Capability','${transactionDetailSummary.posCrdDataOtCap}' != '' ? '${transactionDetailSummary.posCrdDataOtCap}' : "N/A"],
		   ['Terminal Data Output Capability','${transactionDetailSummary.posTermOutCap}' != '' ? '${transactionDetailSummary.posTermOutCap}' : "N/A"],
		   ['PIN Capture Capability','${transactionDetailSummary.posPinCaptCap}' != '' ? '${transactionDetailSummary.posPinCaptCap}': "N/A"]
		   
		   
		 ]);

		 // Convert the Map data to an array of arrays
		 const dataArray = Array.from(dataMap, ([key, value]) => [key, value]);

		 // Create a new Excel workbook
		 const workbook = XLSX.utils.book_new();

		 // Convert the data array to a worksheet
		 const worksheet = XLSX.utils.aoa_to_sheet([['POS DATA', ''], ...dataArray]);

		 // Add the worksheet to the workbook
		 XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');

		 // Save the workbook to a file
		 XLSX.writeFile(workbook, 'PosData.xlsx');
}
</script>
</head>
<div class="panel panel-default">
															<div class="panel-heading">
																<h4 class="panel-title">
																	<a data-toggle="collapse" data-parent="#childAccordion"
																		href="#collapsePos${disTxnList.mti}${disTxnList.funcCode}${disTxnList.txnId}" class="collapsed" > POS Data Code
																		<span class="glyphicon glyphicon-plus accor-pos-icon"></span>	
																	</a>
																</h4>
															</div>
															<div id="collapsePos${disTxnList.mti}${disTxnList.funcCode}${disTxnList.txnId}" class="panel-collapse collapse">
																<div class="panel-body">
																	<div class="">
																		<div class="row">
																			<div class="col-md-12">
																				<div class="card">
																					<div class="card-body">
																						<table name="tabNew" class="table table-striped" style="font-size: 12px">
																							<caption style="display:none;">POS DATA CODE</caption>  
																							<tbody>
																							<th scope="col" style="display:none;"></th>
																								<tr>
																									<td><label><spring:message code="txn.detail.posData.lbl.cardDataInputCapability" /></label></td>
																									<td id="pan"><c:if test="${empty transactionDetailSummary.posCrdDataInCap}">N/A</c:if>${transactionDetailSummary.posCrdDataInCap }</td>
																									<td><label><spring:message code="txn.detail.posData.lbl.cardholderAuthenticationCapability" /></label></td>
																									<td id="tstampLocal"><c:if test="${empty transactionDetailSummary.posCrdHldrAuthC}">N/A</c:if>${transactionDetailSummary.posCrdHldrAuthC }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.posData.lbl.cardCaptureCapability" /></label></td>
																									<td id="rrn"><c:if test="${empty transactionDetailSummary.posCardCaptCap}">N/A</c:if>${transactionDetailSummary.posCardCaptCap }</td>
																									<td><label><spring:message code="txn.detail.posData.lbl.terminalOperatingEnvironment" /></label></td>
																									<td id="pan"><c:if test="${empty transactionDetailSummary.posOpenEnv}">N/A</c:if>${transactionDetailSummary.posOpenEnv }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.posData.lbl.cardholderPresentData" /></label></td>
																									<td id="tstampLocal"><c:if test="${empty transactionDetailSummary.posCrdhldrPresent}">N/A</c:if>${transactionDetailSummary.posCrdhldrPresent }</td>
																									<td><label><spring:message code="txn.detail.posData.lbl.cardPresentData" /></label></td>
																									<td id="rrn"><c:if test="${empty transactionDetailSummary.posCardPres}">N/A</c:if>${transactionDetailSummary.posCardPres }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.posData.lbl.cardDataInputMode" /></label></td>
																									<td id="pan"><c:if test="${empty transactionDetailSummary.posCrdDatInMod}">N/A</c:if>${transactionDetailSummary.posCrdDatInMod }</td>
																									<td><label><spring:message code="txn.detail.posData.lbl.cardholderAuthenticationMethod" /></label></td>
																									<td id="tstampLocal"><c:if test="${empty transactionDetailSummary.posCrdHldrAuthMeth}">N/A</c:if>${transactionDetailSummary.posCrdHldrAuthMeth }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.posData.lbl.cardholderAuthenticationEntity" /></label></td>
																									<td id="rrn"><c:if test="${empty transactionDetailSummary.posCrdHldrAuth}">N/A</c:if>${transactionDetailSummary.posCrdHldrAuth }</td>
																									<td><label><spring:message code="txn.detail.posData.lbl.cardDataOutputCapability" /></label></td>
																									<td id="pan"><c:if test="${empty transactionDetailSummary.posCrdDataOtCap}">N/A</c:if>${transactionDetailSummary.posCrdDataOtCap }</td>
																								</tr>
																								<tr>
																									<td><label><spring:message code="txn.detail.posData.lbl.terminalDataOutputCapability" /></label></td>
																									<td id="tstampLocal"><c:if test="${empty transactionDetailSummary.posTermOutCap}">N/A</c:if>${transactionDetailSummary.posTermOutCap }</td>
																									<td><label><spring:message code="txn.detail.posData.lbl.pinCaptureCapability" /></label></td>
																									<td id="rrn"><c:if test="${empty transactionDetailSummary.posPinCaptCap}">N/A</c:if>${transactionDetailSummary.posPinCaptCap }</td>
																								</tr>
																							</tbody>
																						</table>
																						</div>
																						<button style="margin-top: 10px"
																							class="btn btn-success pull-left btn_align"
																							onclick="exportPosData();">
																							<spring:message code="ifsc.exportBtn" />
																						</button>
																					</div>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>