<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.ProjectionEntryRepository">


<select id="getAllProjectionEntriesMain" resultType="projectionEntryDTO">
	
	
	SELECT r.card_projection_id as cardProjectionId,r.month as month,ct.description as cardTypeName,cv.description as cardVariantName
	,r.total_no_rupay_card_projected as totalCards,r.created_by as createdBy,r.created_on as createdOn,r.last_updated_by as lastUpdatedBy
	,r.last_updated_on as LastUpdatedOn,r.status as status,r.total_premium_payable_exc_tax as totalPremiumPayableExcTax,r.total_premium_payable_inc_tax as totalPremiumPayableIncTax,r.gst as gst
	,r.premium_percard_perannum as premiumPerCardPerAnnum,r.vendor as vendorId,r.bindata_total_premium_payable_exc_tax as bindataTotalPremiumPayableExcTax 
	,r.number_of_days as numberOfDays,stg.request_state requestState 
	FROM   MCPR_CARD_VARIANT_PROJECTION r 
	inner join MCPR_CARD_VARIANT_PROJECTION_stg stg on r.card_projection_id=stg.card_projection_id
	left join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE' 
	left join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT' 
	ORDER BY r.last_updated_on desc
		
</select>

<select id="getAllPendingProjectionEntries" resultType="projectionEntryDTO">
	SELECT r.card_projection_id as cardProjectionId,r.month as month,ct.description as cardTypeName,cv.description as cardVariantName,r.total_no_rupay_card_projected  as totalCards
	,r.created_by as createdBy,r.created_on as createdOn,r.last_updated_by as lastUpdatedBy
	,r.last_updated_on as LastUpdatedOn
	,r.checker_comments as checkerComments,r.request_state as requestState,r.status as status,r.last_operation as lastOperation
	FROM  MCPR_CARD_VARIANT_PROJECTION_STG r 
	left join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE' 
	left join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT' 			
	WHERE REQUEST_STATE IN ('R','P')
</select>


<select id="getCardTypeList" resultType="projectionEntryDTO">
     SELECT type as type, code as code, description as description from lookup where type='CARD_TYPE'
</select>

<select id="getCardVariantList" resultType="projectionEntryDTO">
	SELECT type as type, code as code, description as description from lookup where type='CARD_VARIANT'
</select>

<select id="fetchIdFromCardProjectionIdSequence" resultType="Integer">
	SELECT nextval('card_projectionid_seq')
</select>

<insert id="addProjectionEntry" >
	INSERT INTO  MCPR_CARD_VARIANT_PROJECTION_STG 
	(CARD_PROJECTION_ID,CARD_TYPE, CARD_VARIANT,TOTAL_NO_RUPAY_CARD_PROJECTED
	,MONTH,CREATED_BY,CREATED_ON, LAST_UPDATED_BY,LAST_UPDATED_ON
	, request_state, last_operation,checker_comments,status) VALUES(#{cardProjectionId},#{cardType},#{cardVariant}
	,#{totalCards},#{month},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}
	, #{requestState}, #{lastOperation},#{checkerComments},'A')
</insert>
<select id="getLookUpList" resultType="CodeValueDTO">
	SELECT type ,code, description from  lookup where type=#{type}
</select>
<select id="fetchApprovedEntriesFromDb" resultType="ProjectionEntryDTO">
	SELECT r.card_projection_id as cardProjectionId,r.month as month
	,r.total_no_rupay_card_projected as totalCards,r.created_by as createdBy,r.created_on as createdOn
	,r.last_updated_by as lastUpdatedBy
	,r.last_updated_on as LastUpdatedOn,r.status as status
	,r.checker_comments as checkerComments,r.request_state as requestState,r.last_operation as lastOperation
	from  MCPR_CARD_VARIANT_PROJECTION_STG r where REQUEST_STATE IN ('P','A') and MONTH =#{month} and card_type = #{cardType} and card_variant= #{cardVariant}
</select>
<select id="getPendingCardProjectionById" resultType="ProjectionEntryDTO">
	SELECT r.card_projection_id as cardProjectionId,r.month as month,r.card_type,ct.description as cardTypeName,r.card_variant,cv.description as cardVariantName
	,r.total_no_rupay_card_projected  as totalCards,r.created_by as createdBy,r.created_on as createdOn,r.last_updated_by as lastUpdatedBy
	,r.last_updated_on as LastUpdatedOn
	,r.checker_comments as checkerComments,r.request_state as requestState,r.status as status,r.last_operation as lastOperation
	FROM  MCPR_CARD_VARIANT_PROJECTION_STG r 
	left join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE' 
	left join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT' 			 
	WHERE CARD_PROJECTION_ID = #{cardProjectionId}
</select>
<select id="getCardProjectionByIdWithCalculation" resultType="ProjectionEntryDTO">
select r.card_projection_id as cardProjectionId,r.month as month,r.card_type as cardType,r.card_variant as cardVariant,r.total_no_rupay_card_projected as totalCards,r.created_by as createdBy ,r.created_on as createdOn
,r.last_updated_by as lastUpdatedBy,r.last_updated_on LastUpdatedOn,r.checker_comments as checkerComments,r.request_state as requestState,r.status as statusCode,r.last_operation as lastOperation 
,annual_premium_amt_per_card as premiumPerCardPerAnnum,vendor as vendorId,(number_of_days* annual_premium_amt_per_card *total_no_rupay_card_projected/total_year_days)::numeric(20,2) as totalPremiumPayableExcTax 
,((number_of_days*annual_premium_amt_per_card*total_no_rupay_card_projected/total_year_days)::numeric(20,2) *0.18)::numeric(20,2) as gst
,((number_of_days*annual_premium_amt_per_card*total_no_rupay_card_projected/total_year_days)::numeric(20,2) + ((number_of_days*annual_premium_amt_per_card*total_no_rupay_card_projected/total_year_days) *0.18)::numeric(20,2)) totalPremiumPayableIncTax 
,number_of_days as numberOfDays from (select cp.*,annual_premium_amt_per_card,ip.vendor 
,case when DATE_PART('month',to_date(concat('01-',cp.month),'dd-MON-yyyy')) in (1,2,3) then to_date(concat('31-03-', DATE_PART('year',to_date(concat('01-',cp.month),'dd-MON-yyyy'))::text),'dd-mm-yyyy') else to_date(concat('31-03-', (DATE_PART('year',to_date(concat('01-',cp.month),'dd-MON-yyyy'))+1)::text),'dd-mm-yyyy') end -to_date(concat('01-',cp.month),'dd-MON-yyyy') + 1 as number_of_days ,case when (case when DATE_PART('month',to_date(concat('01-',cp.month),'dd-MON-yyyy')) in (1,2,3) then DATE_PART('year',to_date(concat('01-',cp.month),'dd-MON-yyyy')) else DATE_PART('year',to_date(concat('01-',cp.month),'dd-MON-yyyy'))+1 end)::integer % 4=0 then 366 else 365 end total_year_days
from  MCPR_CARD_VARIANT_PROJECTION_stg cp 
left outer join  MCPR_INSURANCE_PREMIUM_CONFIG  ip on cp.card_type=ip.card_type and cp.card_variant=ip.card_variant and to_date(concat('01-',cp.month),'dd-MON-yyyy') between from_Date and to_Date 
	where CARD_PROJECTION_ID = #{cardProjectionId} )  r
</select>
<select id="getCardProjectionByIdMain" resultType="ProjectionEntryDTO">
	
			
			select r.card_projection_id as cardProjectionId, r.month as month, r.card_type as cardType,r.card_variant as cardVariant,r.total_no_rupay_card_projected as totalCards,r.created_by as createdBy 
			,r.created_on as createdOn,r.last_updated_by as lastUpdatedBy,r.last_updated_on LastUpdatedOn,r.status as statusCode 
			,r.premium_percard_perannum as premiumPerCardPerAnnum,r.vendor as vendorId,COALESCE(r.total_premium_payable_exc_tax,0)  as totalPremiumPayableExcTax 
			,r.gst as	gst,to_char(to_Date(concat('01-',left(r.MONTH,3),'-',right(r.MONTH,4)),'dd-MON-yyyy'),'Mon-YY') as monthData 
			,COALESCE(r.total_premium_payable_inc_tax,0) totalPremiumPayableIncTax, cv.description as cardVariantName,ct.description as cardTypeName,COALESCE(r.number_of_days,0) as numberOfDays 
			,COALESCE(r.no_rupay_card_actual,0) as cardActualCount,COALESCE(r.premium_payable_exc_tax,0) as premiumPayableExcTax,COALESCE(r.premium_payable_inc_tax,0) as premiumPayableIncTax ,
			COALESCE(r.bindata_total_premium_payable_exc_tax,0) as bindataTotalPremiumPayableExcTax,COALESCE(r.bindata_total_premium_payable_inc_tax,0) as bindataTotalPremiumPayableIncTax,stg.request_state requestState 
			from  MCPR_CARD_VARIANT_PROJECTION r 
			inner join MCPR_CARD_VARIANT_PROJECTION_stg stg on r.card_projection_id=stg.card_projection_id
			inner join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE' 
			inner join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT'
			where r.CARD_PROJECTION_ID = #{cardProjectionId}
			

</select>
<update id="updateCardProjectionFinalPaidAmount" >
	update MCPR_CARD_VARIANT_PROJECTION set total_premium_after_adjustment_exclTaxes =#{totalPremiumAfterAdjustmentExclTaxes} 
	,total_premium_after_adjustment_inclTaxes =#{totalPremiumAfterAdjustmentInclTaxes}
	where card_projection_id =#{cardProjectionId}
</update>

<select id="getCardProjectionByCardTypeCardVariantMain" resultType="ProjectionEntryDTO">
			select r.card_projection_id as cardProjectionId, r.month as month, r.card_type as cardType,r.card_variant as cardVariant,r.total_no_rupay_card_projected as totalCards,r.created_by as createdBy 
			,r.created_on as createdOn,r.last_updated_by as lastUpdatedBy,r.last_updated_on LastUpdatedOn,r.status as statusCode 
			,r.premium_percard_perannum as premiumPerCardPerAnnum,r.vendor as vendorId,COALESCE(r.total_premium_payable_exc_tax,0)  as totalPremiumPayableExcTax 
			,r.gst as	gst,to_char(to_Date(concat('01-',left(r.MONTH,3),'-',right(r.MONTH,4)),'dd-MON-yyyy'),'Mon-YY') as monthData 
			,COALESCE(r.total_premium_payable_inc_tax,0) totalPremiumPayableIncTax, cv.description as cardVariantName,ct.description as cardTypeName,COALESCE(r.number_of_days,0) as numberOfDays 
			,COALESCE(r.no_rupay_card_actual,0) as cardActualCount,COALESCE(r.premium_payable_exc_tax,0) as premiumPayableExcTax,COALESCE(r.premium_payable_inc_tax,0) as premiumPayableIncTax ,
			COALESCE(r.bindata_total_premium_payable_exc_tax,0) as bindataTotalPremiumPayableExcTax,COALESCE(r.bindata_total_premium_payable_inc_tax,0) as bindataTotalPremiumPayableIncTax,stg.request_state requestState 
			from  MCPR_CARD_VARIANT_PROJECTION r 
			inner join MCPR_CARD_VARIANT_PROJECTION_stg stg on r.card_projection_id=stg.card_projection_id
			inner join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE' 
			inner join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT'
			where r.MONTH =#{month} and r.card_type = #{cardType} and r.card_variant= #{cardVariant}  limit 1
			

</select>

<select id="getCardProjectionByMonthCTCVMain" resultType="ProjectionEntryDTO">
	select r.card_projection_id as cardProjectionId,r.month as month,r.card_type as cardType,r.card_variant as cardVariant,COALESCE(r.total_no_rupay_card_projected,0) as totalCards,r.created_by as createdBy 
			,r.created_on as createdOn,r.last_updated_by as lastUpdatedBy,r.last_updated_on LastUpdatedOn,r.status as statusCode 
			,COALESCE(premium_percard_perannum,'0') as premiumPerCardPerAnnum,vendor as vendorId,COALESCE(total_premium_payable_exc_tax,'0')  as totalPremiumPayableExcTax 
			,COALESCE(gst,'0') as	gst,to_char(to_Date(concat('01-',left(MONTH,3),'-',right(MONTH,4)),'dd-MON-yyyy'),'Mon-YY')	as monthData 
			,COALESCE(total_premium_payable_inc_tax,'0') totalPremiumPayableIncTax, cv.description as cardVariantName,ct.description as cardTypeName,COALESCE(r.number_of_days,0) as numberOfDays 
			,COALESCE(r.no_rupay_card_actual,0) as cardActualCount,COALESCE(premium_payable_exc_tax,0) as premiumPayableExcTax,COALESCE(premium_payable_inc_tax,0) as premiumPayableIncTax ,COALESCE(bindata_total_premium_payable_exc_tax,0) as bindataTotalPremiumPayableExcTax,COALESCE(bindata_total_premium_payable_inc_tax,0) as bindataTotalPremiumPayableIncTax 
			from  MCPR_CARD_VARIANT_PROJECTION r 
			inner join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE' inner join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT' 
			where MONTH =to_char((to_Date(concat('01-',#{month}),'dd-MON-YYYY') - interval '2 month'),'MON-YYYY') and card_type = #{cardType} and card_variant= #{cardVariant} 
</select>

<select id="getCardActualCount" resultType="Integer">
	select sum(COALESCE(phy_contact_card_increm,0)+COALESCE(phy_contact_less_card_increm,0)) from  MCPR_BIN_DATA_DETAILS a 
			 inner join  membin_details  b on a.bin_no=bin_number 
			 inner join  lookup cv on b.bin_card_variant=cv.code and cv.type='CARD_VARIANT' inner join  lookup ct on b.bin_card_type=ct.code and ct.type='CARD_TYPE' 
			 where  b.status!='D' and b.bin_number!='' and to_Date(concat('01',submitted_date),'ddmmyyyy') =(to_Date(concat('01-',#{month}),'dd-MON-YYYY') - interval '2 month') and b.bin_card_type = #{cardType} and b.bin_card_variant= #{cardVariant}  
</select>

<select id="getCardActualCountMarch" resultType="Integer">
	select sum(COALESCE(phy_contact_card_cumm_total,0) +COALESCE(phy_contact_less_card_total,0)) from  MCPR_BIN_DATA_DETAILS a 
			 inner join  membin_details  b on a.bin_no=bin_number 
			 inner join  lookup cv on b.bin_card_variant=cv.code and cv.type='CARD_VARIANT' inner join  lookup ct on b.bin_card_type=ct.code and ct.type='CARD_TYPE' 
			 where b.status!='D' and b.bin_number!='' and to_Date(concat('01',submitted_date),'ddmmyyyy') =(to_Date(concat('01-',#{month}),'dd-MON-YYYY') - interval '2 month') and b.bin_card_type = #{cardType} and b.bin_card_variant= #{cardVariant} 
</select>

<update id="updateCardProjectionStgState" >
	UPDATE  MCPR_CARD_VARIANT_PROJECTION_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, last_updated_on = #{lastUpdatedOn}, REQUEST_STATE = #{requestState},last_operation =#{lastOperation}, CHECKER_COMMENTS=#{checkerComments} WHERE CARD_PROJECTION_ID =  #{cardProjectionId}
</update>

<update id="updateCardActualMain" >
	update  MCPR_CARD_VARIANT_PROJECTION set premium_payable_inc_tax_for_march=#{premiumPayableIncTaxForMarch},premium_payable_exc_tax_for_march=#{premiumPayableExcTaxForMarch},premium_per_card_per_annum_for_march=#{premiumPerCardPerAnnumForMarch},total_no_card_projection_for_march=#{totalNoCardProjectionForMarch},total_no_card_issue_till_feb=#{totalNoCardIssuetillFeb}
	,no_rupay_card_actual=#{cardActualCount},premium_payable_exc_tax=#{premiumPayableExcTax},premium_payable_inc_tax=#{premiumPayableIncTax},bindata_total_premium_payable_exc_tax=#{bindataTotalPremiumPayableExcTax},bindata_total_premium_payable_inc_tax=#{bindataTotalPremiumPayableIncTax}  
	WHERE CARD_PROJECTION_ID = #{cardProjectionId}
</update>
<update id="updateCardProjectionMainMarchData" >
	update  MCPR_CARD_VARIANT_PROJECTION set premium_payable_inc_tax_for_march=#{premiumPayableIncTaxForMarch}
	,premium_payable_exc_tax_for_march=#{premiumPayableExcTaxForMarch},premium_per_card_per_annum_for_march=#{premiumPerCardPerAnnumForMarch}
	,total_no_card_projection_for_march=#{totalNoCardProjectionForMarch},total_no_card_issue_till_feb=#{totalNoCardIssuetillFeb}
	,no_rupay_card_actual_for_march=#{baseLineMarchActualCards}
	WHERE CARD_PROJECTION_ID = #{cardProjectionId}
</update>
<update id="updateCardActualMainMarchData" >
	update  MCPR_CARD_VARIANT_PROJECTION set no_rupay_card_actual_for_march=#{cardActualCount}
	,actual_premium_payable_exc_tax_for_march=#{actualPremiumPayableExcTaxForMarch},actual_premium_payable_inc_tax_for_march=#{actualPremiumPayableIncTaxForMarch}
	WHERE CARD_PROJECTION_ID = #{cardProjectionId}
</update>

<select id="checkInsurencePremiumData" resultType="String">
	select 'Y' as flag from  MCPR_INSURANCE_PREMIUM_CONFIG where card_type=#{cardType} and card_variant=#{cardVariant} and to_date(concat('01-',#{month}),'dd-MON-yyyy') between from_Date and to_Date
</select>

<select id="getApprovedCardProjectionById" resultType="ProjectionEntryDTO">
	SELECT r.card_projection_id as cardProjectionId,r.month as month,card_type as cardType,card_variant as cardVariant
	,r.total_no_rupay_card_projected  as totalCards
	,r.created_by as createdBy,r.created_on as createdOn,r.last_updated_by as lastUpdatedBy
	,r.last_updated_on as LastUpdatedOn
	,r.status as status
	 from  MCPR_CARD_VARIANT_PROJECTION r where CARD_PROJECTION_ID = #{cardProjectionId}
</select>

<insert id="saveCardProjection" >
	INSERT INTO  MCPR_CARD_VARIANT_PROJECTION(CARD_PROJECTION_ID,CARD_TYPE
	, CARD_VARIANT,TOTAL_NO_RUPAY_CARD_PROJECTED,MONTH,CREATED_BY,CREATED_ON, LAST_UPDATED_BY
	,LAST_UPDATED_ON,status,total_premium_payable_exc_tax,total_premium_payable_inc_tax,gst
	,premium_percard_perannum,vendor,bindata_total_premium_payable_exc_tax,number_of_days) 
	VALUES(#{cardProjectionId},#{cardType},#{cardVariant},#{totalCards},#{month},#{createdBy}
	,#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{statusCode},#{totalPremiumPayableExcTax}
	,#{totalPremiumPayableIncTax},#{gst},#{premiumPerCardPerAnnum},#{vendorId}
	,#{bindataTotalPremiumPayableExcTax},#{numberOfDays})
</insert>

<update id="updateCardProjection" >
	UPDATE  MCPR_CARD_VARIANT_PROJECTION_STG SET 
	TOTAL_NO_RUPAY_CARD_PROJECTED=#{totalCards},LAST_UPDATED_BY=#{lastUpdatedBy},LAST_UPDATED_ON= #{lastUpdatedOn},request_state= #{requestState}
	,last_operation =#{lastOperation},checker_comments = '' where CARD_PROJECTION_ID =  #{cardProjectionId}
</update>

<update id="updateCardProjectionMain" >
	UPDATE  MCPR_CARD_VARIANT_PROJECTION SET TOTAL_NO_RUPAY_CARD_PROJECTED=#{totalCards}
	,LAST_UPDATED_BY=#{lastUpdatedBy},LAST_UPDATED_ON= #{lastUpdatedOn},total_premium_payable_exc_tax=#{totalPremiumPayableExcTax}
	,total_premium_payable_inc_tax=#{totalPremiumPayableIncTax},gst=#{gst}
	,premium_percard_perannum=#{premiumPerCardPerAnnum},vendor=#{vendorId}
	,bindata_total_premium_payable_exc_tax=#{bindataTotalPremiumPayableExcTax},number_of_days=#{numberOfDays} 
	where CARD_PROJECTION_ID =  #{cardProjectionId}
</update>

<select id="getCardProjectionStgData" resultType="ProjectionEntryDTO">
	SELECT r.card_projection_id as cardProjectionId,r.month as month,r.card_type as cardType,r.card_variant as cardVariant
	,r.total_no_rupay_card_projected  as totalCards
	,r.created_by as createdBy,r.created_on as createdOn,r.last_updated_by as lastUpdatedBy
	,r.last_updated_on as LastUpdatedOn
	,r.checker_comments as checkerComments,r.request_state as requestState,r.status as status,r.last_operation as lastOperation
	from  MCPR_CARD_VARIANT_PROJECTION_STG r WHERE CARD_PROJECTION_ID = #{cardProjectionId}
</select>

<select id="getCardProjectionMainData" resultType="ProjectionEntryDTO">
	SELECT r.card_projection_id as cardProjectionId,r.month as month,card_type as cardType,card_variant as cardVariant
	,r.total_no_rupay_card_projected  as totalCards
	,r.created_by as createdBy,r.created_on as createdOn,r.last_updated_by as lastUpdatedBy
	,r.last_updated_on as LastUpdatedOn
	,r.status as status
	from  MCPR_CARD_VARIANT_PROJECTION r WHERE CARD_PROJECTION_ID = #{cardProjectionId}
</select>

<delete id="deleteDiscardedEntry" >
	DELETE FROM  MCPR_CARD_VARIANT_PROJECTION_STG WHERE CARD_PROJECTION_ID = #{cardProjectionId}
</delete>

<delete id="deleteCardProjectionFromMain" >
	DELETE FROM  MCPR_CARD_VARIANT_PROJECTION WHERE CARD_PROJECTION_ID = #{cardProjectionId}
</delete>

<delete id="deleteCardProjectionFromStg" >
	DELETE FROM  MCPR_CARD_VARIANT_PROJECTION_STG WHERE CARD_PROJECTION_ID = #{cardProjectionId}
</delete>



<select id="getTotalPremium" resultType="projectionEntryDTO">


	    
		select concat('Total Premium for ',ct.description ,' ',cv.description) as cardTypeName, coalesce(p.card_projection_id,0) as cardProjectionId
		,coalesce(p.total_premium_after_adjustment_inclTaxes,0)::numeric as totalPremium
		,coalesce(p.total_premium_after_adjustment_inclTaxes,0)::numeric as totalPremiumAfterAdjustmentInclTaxes
		,coalesce(p.total_premium_after_adjustment_excltaxes,0)::numeric as totalPremiumAfterAdjustmentExclTaxes
		,coalesce(p.month,'') as month
		from MCPR_INSURANCE_PREMIUM_CONFIG r
		left outer join MCPR_CARD_VARIANT_PROJECTION p on R.card_type=p.card_type and R.card_variant=p.card_variant and month=to_char(now() + interval ' 1 month','MON-yyyy')
		LEFT JOIN lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' left join lookup cv on R.card_variant=cv.code and cv.type='CARD_VARIANT' 
		 where r.VENDOR = #{vendorId} and now() + interval '1 month' between from_Date and to_Date

</select>

<select id="getTotalPremiumVendorWise" resultType="BigDecimal">

	    
		select sum(coalesce(p.total_premium_after_adjustment_inclTaxes,0)::numeric) as premiumPayableIncTax
		from MCPR_INSURANCE_PREMIUM_CONFIG r
		left outer join MCPR_CARD_VARIANT_PROJECTION p on R.card_type=p.card_type and R.card_variant=p.card_variant and month=to_char(now() + interval ' 1 month','MON-yyyy')
		LEFT JOIN lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' left join lookup cv on R.card_variant=cv.code and cv.type='CARD_VARIANT' 
		 where r.VENDOR = #{vendorId} and now() + interval '1 month' between from_Date and to_Date

</select>

<select id="getTotalPremiumVendorWiseExtGST" resultType="BigDecimal">
		select sum(coalesce(p.total_premium_after_adjustment_excltaxes,0)::numeric) as premiumPayableIncTax
		from MCPR_INSURANCE_PREMIUM_CONFIG r
		left outer join MCPR_CARD_VARIANT_PROJECTION p on R.card_type=p.card_type and R.card_variant=p.card_variant and month=to_char(now() + interval ' 1 month','MON-yyyy')
		LEFT JOIN lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' left join lookup cv on R.card_variant=cv.code and cv.type='CARD_VARIANT' 
		 where r.VENDOR = #{vendorId} and now() + interval '1 month' between from_Date and to_Date
</select>


<select id="getClosingPremiumVendorMonthWise" resultType="projectionEntryDTO">
		SELECT total_premium_paid as totalPremiumAfterAdjustmentInclTaxes,total_premium_paid_exc_gst as totalPremiumAfterAdjustmentExclTaxes
		FROM mcpr_vendor_month_premium  R 
		where R.MONTH = #{historyDate} and R.vendorid = #{vendorId}

</select>
<select id="getClosingPremiumVendorMonthWiseExtGST" resultType="BigDecimal">
		SELECT total_premium_paid_exc_gst as totalPremiumPaid 
		FROM mcpr_vendor_month_premium  R 
		where R.MONTH = #{historyDate} and R.vendorid = #{vendorId}

</select>

<select id="getMonths" resultType="projectionEntryDTO">
		SELECT TYPE as type,CODE as code,DESCRIPTION as description FROM  LOOKUP where type = 'Month' ORDER BY CODE
</select>

<select id="getTotalHistoricalPremiumByVariant" resultType="projectionEntryDTO">

		SELECT ct.description as cardTypeName,cv.description as cardVariantName, R.card_projection_id as cardProjectionId,R.total_no_rupay_card_projected as totalCards,R.month as month,R.last_updated_by as lastUpdatedBy,
		R.last_updated_on as lastUpdatedOn,R.created_by as createdBy,R.created_on as createdOn ,R.total_premium_payable_inc_tax as totalPremium
		FROM MCPR_CARD_VARIANT_PROJECTION R 
		LEFT JOIN lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' 
	    left join lookup cv on R.card_variant=cv.code and cv.type='CARD_VARIANT' 
		where R.MONTH = #{historyDate} and R.card_variant = #{cardVariant} and R.card_type = #{cardType}
</select>

<select id="getTotalHistoricalPremiumByVendor" resultType="projectionEntryDTO">
		

		select concat('Total Premium for ',ct.description ,' ',cv.description) as cardTypeName, coalesce(p.card_projection_id,0) as cardProjectionId
		,coalesce(p.total_premium_after_adjustment_inclTaxes,0)::numeric as totalPremium
		,coalesce(p.total_premium_after_adjustment_inclTaxes,0)::numeric as totalPremiumAfterAdjustmentInclTaxes
		,coalesce(p.total_premium_after_adjustment_excltaxes,0)::numeric as totalPremiumAfterAdjustmentExclTaxes
		,coalesce(p.month,'') as month
		from MCPR_INSURANCE_PREMIUM_CONFIG r
		left outer join MCPR_CARD_VARIANT_PROJECTION p on R.card_type=p.card_type and R.card_variant=p.card_variant and month=#{historyDate}
		LEFT JOIN lookup ct on R.card_type=ct.code and ct.type='CARD_TYPE' left join lookup cv on R.card_variant=cv.code and cv.type='CARD_VARIANT' 
		 where r.VENDOR = #{vendorId} and to_date(concat('01-',#{historyDate}),'dd-MON-YYYY')  between from_Date and to_Date

</select>

<select id="getMonthName" resultType="projectionEntryDTO">
		SELECT DESCRIPTION as description FROM  LOOKUP WHERE  type = 'Month' AND CODE = #{monthNumber}
</select>

<delete id="deleteClosingPremiumVendorMonthWise" >
		delete FROM  mcpr_vendor_month_premium  where MONTH = #{historyDate} and vendorid = #{vendorId}
</delete>
<insert id="insertClosingPremiumVendorMonthWise" >
		insert into mcpr_vendor_month_premium (MONTH,vendorid,total_premium_paid,total_premium_paid_exc_gst)	values(#{historyDate},#{vendorId},#{premiumAmount},#{premiumAmountExtGST})
</insert>
<select id="getTotalSumOfHistoricalPremiumByVendor" resultType="projectionEntryDTO">
		select sum(coalesce(total_premium_after_adjustment_inclTaxes,0)::numeric) as premiumPayableIncTax
		,sum(coalesce(total_premium_after_adjustment_inclTaxes,0)::numeric) as totalPremiumAfterAdjustmentInclTaxes
		,sum(coalesce(total_premium_after_adjustment_excltaxes,0)::numeric) as totalPremiumAfterAdjustmentExclTaxes
		FROM MCPR_CARD_VARIANT_PROJECTION R 
		where R.MONTH = #{historyDate} and R.vendor = #{vendorId}

</select>
<select id="getTotalPremiumVendorWiseMonthWiseExtGST" resultType="BigDecimal">
		select sum(coalesce(total_premium_after_adjustment_excltaxes,0)::numeric) as premiumPayableIncTax
		FROM MCPR_CARD_VARIANT_PROJECTION R 
		where R.MONTH = #{historyDate} and R.vendor = #{vendorId}

</select>
<select id="getProjectedCardCount"  resultType="Integer">
	select coalesce(total_no_rupay_card_projected,0) from MCPR_CARD_VARIANT_PROJECTION where month=#{month} and card_type=#{cardType} and card_variant=#{cardVariant}
</select>
<select id="getCardProjectionForBaseLine" resultType="ProjectionEntryDTO">
	select r.card_projection_id as cardProjectionId,r.month as month,r.card_type as cardType,r.card_variant as cardVariant
			,COALESCE(r.total_no_card_issue_till_feb,0) as totalNoCardIssuetillFeb
			,COALESCE(r.total_no_card_projection_for_march,0) as totalNoCardProjectionForMarch,COALESCE(r.premium_per_card_per_annum_for_march,0) as premiumPerCardPerAnnum
			,COALESCE(r.premium_payable_exc_tax_for_march,0) as premiumPayableExcTaxForMarch,COALESCE(r.premium_payable_inc_tax_for_march,0) as premiumPayableIncTaxForMarch
			from  MCPR_CARD_VARIANT_PROJECTION r 
			inner join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE' inner join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT' 
			where MONTH =to_char((to_Date(concat('01-',#{month}),'dd-MON-YYYY') - interval '1 month'),'MON-YYYY') 
			and card_type = #{cardType} and card_variant= #{cardVariant} 
</select>

</mapper>