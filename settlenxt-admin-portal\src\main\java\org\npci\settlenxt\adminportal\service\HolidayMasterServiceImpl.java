package org.npci.settlenxt.adminportal.service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.HolidayMasterDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.HolidayMasterRepository;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class HolidayMasterServiceImpl implements HolidayMasterService {
	
	@Autowired
	HolidayMasterRepository holidayMasterRepo;
	
	@Autowired
	SessionDTO sessionDTO;

	@Override
	public List<HolidayMasterDTO> getApprovedHolidayMasterList() {
		return holidayMasterRepo.getApprovedHolidayMasterList(CommonConstants.REQUEST_STATE_APPROVED);
	}

	@Override
	public List<HolidayMasterDTO> getPendingHolidayMasterList() {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(CommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(CommonConstants.REQUEST_STATE_REJECTED);
		return holidayMasterRepo.getPendingHolidayMasterList(requestStateList);
	}

	@Override
	public HolidayMasterDTO getHoliday(String holidaySeqId) {
		return holidayMasterRepo.getHolidayById(Integer.parseInt(holidaySeqId));
	}

	@Override
	public void updateHoliday(HolidayMasterDTO holidayMasterDTO) {
		holidayMasterDTO.setStatus(CommonConstants.USER_ACTIVE_STATUS);
		holidayMasterDTO.setLastUpdatedBy(sessionDTO.getUserName());
		holidayMasterDTO.setLastUpdatedOn(new Date());
		holidayMasterDTO.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
		holidayMasterDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		holidayMasterRepo.updateHolidayinStg(holidayMasterDTO);
	}

	@Override
	public HolidayMasterDTO getHolidayFromMain(String holidaySeqId) {

		return holidayMasterRepo.getHolidayFromMainById(Integer.parseInt(holidaySeqId));
	}

	@Override
	public List<CodeValueDTO> getProductList() {
		return holidayMasterRepo.getProductList();
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public HolidayMasterDTO addEditHolidayMaster(HolidayMasterDTO holidayMasterDTO) {

		Date currentDate = new Date();

		holidayMasterDTO.setCreatedBy(sessionDTO.getUserName());
		holidayMasterDTO.setCreatedOn(currentDate);
		holidayMasterDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
		holidayMasterDTO.setLastUpdatedBy(null);
		holidayMasterDTO.setLastUpdatedOn(null);
		if (holidayMasterDTO.getToDate() == null) {
			holidayMasterDTO.setStatus("P");

			holidayMasterDTO.setHolidayDate(holidayMasterDTO.getFromDate());
			int a = 0;
			HolidayMasterDTO existingHolidayMasterPublicHoliday = holidayMasterRepo
					.existingHolidayMasterPublicHoliday(holidayMasterDTO);
			if (existingHolidayMasterPublicHoliday != null) {
				throw new SettleNxtApplicationException("Holiday Master with same configuration already exists : "
						+ holidayMasterDTO.getHolidayDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
						"");
			}
			holidayMasterDTO.setHolidaySeqId(holidayMasterRepo.fetchIdFromSeq().toString());
			holidayMasterRepo.insertHolidayMasterWeeklyHoliday(holidayMasterDTO, a);
		} else {
			String weekTypeval = holidayMasterDTO.getWeekType();
			if (weekTypeval.contains("|")) {
				weekTypeval = weekTypeval.replace("|", ",");
			}

			weekTypeval = weekTypeval.substring(0, weekTypeval.length() - 1);
			holidayMasterDTO.setWeekType(weekTypeval);
			findAllDates(holidayMasterDTO);
		}
		return holidayMasterDTO;
	}


	


	private void findAllDates(HolidayMasterDTO holidayMasterDTO) {

		Date fromDate = null;
		Date toDate = null;
		int[] weekType = Arrays.stream(holidayMasterDTO.getWeekType().split(",")).mapToInt(Integer::parseInt).toArray();

		fromDate = holidayMasterDTO.getFromDate();
		setToDateTime(holidayMasterDTO);
		toDate = holidayMasterDTO.getToDate();
		
		LocalDate startDate = fromDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().withDayOfMonth(1);
		LocalDate startDateOrg = fromDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		LocalDate endDate = toDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		int insertCount = 0;

		while (!startDate.isAfter(endDate)) {
			for (int n : weekType) {
				long l = n;
				LocalDate nthWeekDay = startDate.with(TemporalAdjusters.firstInMonth(
						DayOfWeek.of(Integer.parseInt(holidayMasterDTO.getDayOfWeek()))))
						.plusWeeks(l - 1);
				if (!nthWeekDay.isBefore(startDateOrg) && !nthWeekDay.isAfter(endDate)) {

					holidayMasterDTO.setStatus("P");
					holidayMasterDTO
							.setHolidayDate(Date.from(nthWeekDay.atStartOfDay(ZoneId.systemDefault()).toInstant()));

					HolidayMasterDTO existingHolidayMasterWeeklyHoliday = holidayMasterRepo
							.existingHolidayMasterWeeklyHoliday(holidayMasterDTO, n);

					if (existingHolidayMasterWeeklyHoliday != null) {
						throw new SettleNxtApplicationException(
								"Holiday Master with same configuration already exists : " + holidayMasterDTO
										.getHolidayDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
								"");

					}
					holidayMasterDTO.setHolidaySeqId(holidayMasterRepo.fetchIdFromSeq().toString());
					holidayMasterRepo.insertHolidayMasterWeeklyHoliday(holidayMasterDTO, n);
					insertCount++;

				}

			}
			startDate = startDate.plusMonths(1);
		}

		if (insertCount == 0) {
			throw new SettleNxtApplicationException("ERR_NO_CONFIGURATION_EXISTS",
					"No Configuration Available for Holiday Master Date");

		}

	}

	private void setToDateTime(HolidayMasterDTO holidayMasterDTO) {		
		Calendar cal = Calendar.getInstance();
		cal.setTime(holidayMasterDTO.getToDate());
		cal.add(Calendar.DAY_OF_MONTH, 1);
		cal.add(Calendar.MILLISECOND, -1);
		holidayMasterDTO.setToDate(cal.getTime());
	}




	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public HolidayMasterDTO updateApproveOrRejectHolidayMaster(int holidayMasterSeqId, String status, String remarks)
			throws SettleNxtException {
		if (holidayMasterSeqId == 0) {
			throw new SettleNxtException("Holiday Master Seq Id should not be empty", "");
		}

		HolidayMasterDTO holidayMasterDto = getHolidayMasterStgInfo(holidayMasterSeqId);
		holidayMasterDto.setRequestState(status);
		holidayMasterDto.setCheckerComments(remarks);
		holidayMasterDto.setLastUpdatedOn(new Date());
		holidayMasterDto.setLastUpdatedBy(sessionDTO.getUserName());
		holidayMasterDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
		if (holidayMasterDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {

			updateApprovedHolidayMaster(holidayMasterDto);
			holidayMasterDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			holidayMasterDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
		}

		holidayMasterRepo.updateHolidayMasterStgState(holidayMasterDto.getLastUpdatedBy(),
				holidayMasterDto.getLastUpdatedOn(), holidayMasterDto.getRequestState(),
				Integer.parseInt(holidayMasterDto.getHolidaySeqId()), remarks, holidayMasterDto.getCheckerComments(),
				holidayMasterDto.getLastOperation());

		return holidayMasterDto;
	}

	private HolidayMasterDTO getHolidayMasterStgInfo(int holidayMasterSeqId) {
		HolidayMasterDTO holidayMasterDto = holidayMasterRepo.getHolidayMasterStgInfoBySeqId(holidayMasterSeqId);
		if (holidayMasterDto == null) {
			throw new SettleNxtException("No Data Exist", "");
		}

		return holidayMasterDto;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public HolidayMasterDTO updateApproveOrRejectHolidayMasterBulk(String holidayMasterList, String status,
			String remarks) {
		String[] holidayMasterSeqIdArray = holidayMasterList.split("\\|");
		HolidayMasterDTO holidayMaster = new HolidayMasterDTO();

		List<Integer> holidayMasterlist = Arrays.stream(holidayMasterSeqIdArray).map(Integer::valueOf)
				.collect(Collectors.toList());
		List<HolidayMasterDTO> holidayMasterDtoArr = holidayMasterRepo.getHolidayMasterStgInfoList(holidayMasterlist);
		Map<String, List<HolidayMasterDTO>> holidayMasterMap = holidayMasterDtoArr.stream()
				.collect(Collectors.groupingBy(HolidayMasterDTO::getHolidaySeqId));

		for (String holidayMasterSeqIdint :holidayMasterSeqIdArray) {

			try {
				List<HolidayMasterDTO> holidayMasterListDto = holidayMasterMap.get(holidayMasterSeqIdint);
				HolidayMasterDTO holidayMasterDto = holidayMasterListDto.get(0);
				if (holidayMasterDto == null) {
					throw new SettleNxtException(
							"Exception occurred with Holiday Master SeqId" + holidayMasterSeqIdint, "");
				} else {
					holidayMasterDto.setRequestState(status);
					holidayMasterDto.setCheckerComments(remarks);
					holidayMasterDto.setLastOperation(CommonConstants.REQUEST_STATE_REJECT_HOLIDAY);
					holidayMasterDto.setLastUpdatedOn(new Date());
					holidayMasterDto.setLastUpdatedBy(sessionDTO.getUserName());

					if (holidayMasterDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
						updateApprovedHolidayMaster(holidayMasterDto);
						holidayMasterDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
						holidayMaster.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
						holidayMasterDto.setLastOperation(CommonConstants.REQUEST_STATE_APPROVE_HOLIDAY);
					}

					holidayMasterRepo.updateHolidayMasterStgState(holidayMasterDto.getLastUpdatedBy(),
							holidayMasterDto.getLastUpdatedOn(), holidayMasterDto.getRequestState(),
							Integer.parseInt(holidayMasterDto.getHolidaySeqId()), remarks,
							holidayMasterDto.getCheckerComments(), holidayMasterDto.getLastOperation());
				}

			} catch (Exception ex) {

				throw new SettleNxtException("Exception for Holiday Master SeqID : " +holidayMasterSeqIdint , "",
						ex);

			}
		}

		return holidayMaster;
	}

	private void updateApprovedHolidayMaster(HolidayMasterDTO holidayMasterDto) {
		HolidayMasterDTO holidayMasterDtoDb = holidayMasterRepo
				.getHolidayMasterInfoByHolidaySeqId(Integer.parseInt(holidayMasterDto.getHolidaySeqId()));
		holidayMasterDto.setHseqId(Integer.parseInt(holidayMasterDto.getHolidaySeqId()));
		if (ObjectUtils.isEmpty(holidayMasterDtoDb)) {
			holidayMasterDto.setCreatedOn(new Date());
			holidayMasterRepo.saveHolidayMaster(holidayMasterDto);
		} else {

			holidayMasterRepo.updateHolidayMaster(holidayMasterDto);
		}

		holidayMasterDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
	}

	@Override
	public List<HolidayMasterDTO> getPendingForAppovalHolidayMasterList() {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(CommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(CommonConstants.REQUEST_STATE_REJECTED);
		return holidayMasterRepo.getPendingForApprovalHolidayMasterList(requestStateList);

	}

	@Override
	public HolidayMasterDTO discardHoliday(String holidaySeqId) {

		Date currentDate = new Date();

		HolidayMasterDTO holidayMasterDTO = holidayMasterRepo.getHolidayById(Integer.parseInt(holidaySeqId));
		HolidayMasterDTO holidayMasterDTOMain = holidayMasterRepo
				.getHolidayFromMainById(Integer.parseInt(holidaySeqId));

		if (holidayMasterDTOMain != null) {
			holidayMasterDTOMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			holidayMasterDTOMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			holidayMasterDTOMain.setLastUpdatedOn(currentDate);
			holidayMasterDTOMain.setLastUpdatedBy(sessionDTO.getUserName());
			holidayMasterRepo.updateHolidayinStg(holidayMasterDTOMain);
			return holidayMasterDTOMain;
		} else {
			holidayMasterRepo.deleteDiscardedEntry(Integer.parseInt(holidaySeqId));

		}

		return holidayMasterDTO;
	}

	@Override
	public void deleteReq(String hid, String reqst) {

		if ("A".equalsIgnoreCase(reqst) || "R".equalsIgnoreCase(reqst)) {
			holidayMasterRepo.deleteReq(hid, CommonConstants.REQUEST_STATE_DELETED, CommonConstants.USER_STATUS_DEACT,
					CommonConstants.LAST_OPERATION_DELETE);

		}
	}

}
