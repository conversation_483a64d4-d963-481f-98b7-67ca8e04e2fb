package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JGlobalMap
public class DisputeFeeRuleDTO {
	
	private static final long serialVersionUID = 1L;
	
	private int seqId;
	private String actionCode;
	private String feeType;
	private int priority;
	private String feeCode;
	private String logicalFeeCode;
	private String fieldName;
	private String fieldName1;
	private String fieldName2;
	private String relationalOperator;
	private String fieldValue;
	private String fieldOperator;
	private String status;
	private String statusCode;
	private String requestState;
	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private String lastOperation;
	private String checkerComments;
	private String logicalOperator;
}
