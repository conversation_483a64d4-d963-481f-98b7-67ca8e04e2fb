$(document).ready(
		function() {
			$("form :input").change(function() {
				$(this).closest('form').data('changed', true);
			});
			
			if($("#currencyId").val()>0){
				document.getElementById('currencyCode').readOnly = true;	
			}
			if($("#currencyDecimalPositionh").val() == 0){
				$("#currencyDecimalPosition").val('2');
				
			}
			
		    $("#errcurrencyCode").hide();
		    $("#errcurrencyDescription").hide();
		    
	$("#currencyCode").on('keyup keypress blur change', function () {
			        validateField('currencyCode', true, "Number",3,true,1,999,true);
			    });
	$("#currencyDescription").on('keyup keypress blur change', function () {
			        validateField('currencyDescription', true,"Any",100, false,0,0,false);
			    });
	$("#currencyAlpha").on('keyup keypress blur change', function () {
        validateField('currencyAlpha', true, "Alphabet",3,true,0,0,false);
    });
	$("#currencyDecimalPosition").on('keyup keypress blur change', function () {
        validateField('currencyDecimalPosition', true, "Number",1,true,0,0,false);
    });
		    
	disableSave();
	$("#currencyCode").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#currencyDescription").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#currencyAlpha").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#currencyDecimalPosition").on('keyup keypress blur change', function () {
        unableSave();
    });
		    
		});

function disableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = true;
}
}

function unableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = false;
}
}

window.history.forward();
function noBack() {
	window.history.forward();
}

function resetAction() {
$("#currencyCode").val("");
$("#currencyDescription").val("");
$("#currencyAlpha").val("");
$("#currencyDecimalPosition").val("");
	$("#snxtErrorMessage").hide();
	
	$("#errcurrencyCode").find('.error').html('');
    $("#errcurrencyDescription").find('.error').html('');
    $("#errcurrencyAlpha").find('.error').html('');
    $("#errcurrencyDecimalPosition").find('.error').html(''); 
	}

function viewCurrencyMasterAdd(url, type) {
	
	var isValid = true;
	
	
    if (!validateField('currencyCode', true, "Integer",3,true,1,999,true) && isValid) {
        isValid = false;
    }
    if (!validateField('currencyDescription', true,"Any",100, false,0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('currencyAlpha', true,"Alphabet",3, true,0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('currencyDecimalPosition', true,"Integer",1, true,0,0,false) && isValid) {
        isValid = false;
    }
    

var data ="";	
if (isValid ) {
    if(type=='E'){
		
	
	 data = "currencyId," + $("#currencyId").val() 
    +",currencyCode," + $("#currencyCode").val() 
	+",currencyDescription," + $("#currencyDescription").val()
	 +",currencyAlpha," + $("#currencyAlpha").val().toUpperCase()
	  +",currencyDecimalPosition," + $("#currencyDecimalPosition").val()
	  +",parentPage," + $("#hparentPage").val();
	}
	
	else if(type=='A'){
	 data = "currencyCode," + $("#currencyCode").val() 
    +",currencyDescription," + $("#currencyDescription").val()  
    +",currencyAlpha," + $("#currencyAlpha").val().toUpperCase()
	  +",currencyDecimalPosition," + $("#currencyDecimalPosition").val()
	  +",parentPage," + $("#hparentPage").val();
	}
	postData(url,data)

}
}




function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType!="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    isValid = checkNaN(fieldType, fieldValue, isValid);
    
        isValid = isInteger(fieldType, fieldValue, isValid);
    
        isValid = isAlphabet(fieldType, fieldValue, isValid);
     isValid = isAlphabetWithSpace(fieldType, fieldValue, isValid);
    isValid = isDecimal(fieldType, fieldValue, isValid);
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
      if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
      

    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
        if(currencyMasterValidationMessages[fieldId]){
            $("#err" + fieldId).find('.error').html(currencyMasterValidationMessages[fieldId]);
        }
        $("#err" + fieldId).show();
    }
    return isValid;
}


function isDecimal(fieldType, fieldValue, isValid) {
    if (fieldType == "Decimal") {
        let regEx = /^\d+\.?\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return isValid;
}

function isAlphabetWithSpace(fieldType, fieldValue, isValid) {
    if (fieldType == "AlphabetWithSpace") {
        let regEx = /^[A-Z ]+$/i;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return isValid;
}

function isAlphabet(fieldType, fieldValue, isValid) {
    if (fieldType == "Alphabet") {
        let regEx = /^[A-Z]+$/i;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return isValid;
}

function isInteger(fieldType, fieldValue, isValid) {
    if (fieldType == "Integer") {
        let regEx = /^\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return isValid;
}

function checkNaN(fieldType, fieldValue, isValid) {
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    return isValid;
}

function userAction(_type, action) {
	var data =  "status," + status;
	postData(action, data);
}

function postDiscardAction(action,_id) {
	var url = action;
	var currencyId = $("#currencyId").val();
	var data = "currencyId," + currencyId ;
	postData(url, data);
}



function discard(action, currencyId) {
	 
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "currencyId," + currencyId + ",_vTransactToken,"
			+ tokenValue;
	postData(action, data);
}






