$(document).ready(function() {
	var today = new Date();
	$("#cycleDate").datepicker({
		dateFormat: 'yyyy-MM-dd',
		todayHighlight: true,
		autoclose: true,
		changeMonth: true,
		changeYear: true,
		endDate: "today",
            maxDate: today
        }).on('changeDate', function (_ev) {
                $(this).datepicker('hide');
            });


        $('.datepicker').keyup(function () {
            if (this.value.match(/\D/g)) {
                this.value = this.value.replace(/[^0-9^-]/g, '');
            }
        });

	$("#select-settlementProductId").change(function() {
		var prodId = $('#select-settlementProductId option:selected').attr("data-product_id");
		var split_string = prodId.split(",");
		var sortedProductId = split_string.sort();
		var dropdown = $('#select-cycleNumber');
		$(dropdown).empty();
		$.each(sortedProductId, function(_key, item) {
			$("#select-cycleNumber").append(new Option(item, item));
		});
		console.log(prodId);

	});

	$('#select-settlementProductId').on('change', function() {
		enableSubmitBtn()
	});
	
	function enableSubmitBtn() {
		var shouldDisabled= false;
		var id = $("#dataTable").find(' tbody tr:first').attr('id');
		var settlementProductId = $('#select-settlementProductId').val();
		if (id == 'noDataAvailable') {
			$("#submit").removeAttr("disabled");
			return false;
		}
		$('.recalStatus').each(function() {
		    var recalStatus = $(this).attr('data-recalculationStatus');
		    var mergeStatus = $(this).attr('data-mergeStatus');
		    if(recalStatus === 'INPROGRESS' || (mergeStatus === 'INPROGRESS' || mergeStatus === null || mergeStatus === undefined)){
				 shouldDisabled= true;
				 return false;
			}else {
				shouldDisabled= false;
			}
		});
		if (settlementProductId == null || $(".mergeBtn").is(":enabled") || shouldDisabled) {
			$("#submit").attr("disabled", "disabled");
		} else {
			$("#submit").removeAttr("disabled");
		}
		
	}

	
	$("#submit").click(function() {
		var myObject = new Object();
		var url = getURL('/recalculationReportStatus');
		var date = $('#cycleDate').val();
		var cycleDate = moment(date).format('DDMMYY');
		var cycleNumber = $('#select-cycleNumber').val();
		var settlementProductId = $('#select-settlementProductId').val();
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		myObject.cycleDate = cycleDate;
		myObject.cycleNumber = cycleNumber;
		myObject.settlementProductId = settlementProductId;
		myObject.requestType = "recalculation";
		$.ajax({
			type: "POST",
			contentType: "application/json",
			url: url,
			data: JSON.stringify(myObject),
			dataType: 'json',
			"beforeSend": function(xhr) {
				xhr.setRequestHeader('_TransactToken', tokenValue);
			},
			success: function(response) {
				$("#alertNotification").removeClass("hide");
				if (response == null || response == '' || response == undefined) {
					$("#alertNotification").addClass("alert-danger");
					$("#alertMessageStrong").text("Error! ");
					$("#alertMessage").text("Failed to send message to recalculation the reports: " + "Internal server error");
				} else {
					if (response.Status == "FAILED" || response.Status == "503") {
						$("#alertNotification").addClass("alert-danger");
						$("#alertMessageStrong").text("Error! ");
						$("#alertMessage").text("Failed to send message to recalculation the reports : " + response.errorMessage);
					} else if (response.Status == "SUCCESS" || response.Status == "200") {
						$("#alertNotification").addClass("alert-success");
						$("#alertMessageStrong").text("Success! ");
						$("#alertMessage").text("Successfully send message to recalculation the reports.");
					} else {
						$("#alertNotification").addClass("alert-danger");
						$("#alertMessageStrong").text("Error! ");
						$("#alertMessage").text("Failed to send message to recalculation the reports: " + "Internal server error");
					}
				}
			},
			error: function(_e) {
				$("#btn-save").prop("disabled", false);
			}
		});
		$("#okAction").click(function() {
			window.setTimeout(function() { location.reload() }, 100)
		});

	});

	function getURL(url) {
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		
		return pathName + url;
	}

	$("#dataTable").DataTable({
		"fnRowCallback": function(nRow, _aData, _iDisplayIndex, _iDisplayIndexFull) {
			var status = $(nRow).attr("data-regenerationStatus");
			if (status == "MERGED") {
				$('td', nRow).addClass('bg-success');
			} else {
				$('td', nRow).addClass('bg-info');
			}
		},
		dom: 'lBfrtip',
		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true,
		order: [[1, 'desc']],
	});
	
	$(".mergeBtn").click(function() {
		var id = $(this).attr('data-id');
		$('#conf-merge-data-id').val(id);
		});
	
	$("#sendMergeTablesRequest").click(function() {
		var myObject = new Object();
		var url = getURL('/mergeTables');
		var mergeBtnId = $('#conf-merge-data-id').val();
		var cycleDate = $('#btn-merge' +mergeBtnId).attr("data-cycleDate");
		var cycleNumber = $('#btn-merge' +mergeBtnId).attr("data-cycleNumber");
		var settlementProductId = $('#btn-merge' +mergeBtnId).attr("data-productId");
		var uid = $('#btn-merge' +mergeBtnId).attr("data-uid");
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		myObject.cycleDate = cycleDate;
		myObject.cycleNumber = cycleNumber;
		myObject.settlementProductId = settlementProductId;
		myObject.uid = uid;
		$.ajax({
			type: "POST",
			contentType: "application/json",
			url: url,
			data: JSON.stringify(myObject),
			dataType: 'json',
			"beforeSend": function(xhr) {
				xhr.setRequestHeader('_TransactToken', tokenValue);
			},
			success: function(response) {
				$("#alertMsg").removeClass("hide");
				if (response == null || response == '' || response == undefined) {
					$("#alertMsg").addClass("alert-danger");
					$("#alertMessage").text("Error! ");
					$("#mergeTableMessage").text("Failed to send message to merge tables: " + "Internal server error");
				} else {
					if (response.Status == "FAILED" || response.Status == "503") {
						$("#alertMsg").addClass("alert-danger");
						$("#alertMessage").text("Error! ");
						$("#mergeTableMessage").text("Failed to send message to merge tables : " + response.errorMessage);
					} else if (response.Status == "SUCCESS" || response.Status == "200") {
						$("#alertMsg").addClass("alert-success");
						$("#alertMessage").text("Success! ");
						$("#mergeTableMessage").text("Successfully send message to merge tables.");
					} else {
						$("#alertMsg").addClass("alert-danger");
						$("#alertMessage").text("Error! ");
						$("#mergeTableMessage").text("Failed to send message to merge tables: " + "Internal server error");
					}
				}
			},
			error: function(_e) {
				$("#btn-save").prop("disabled", false);
			}
		});
		$("#okay").click(function() {
			window.setTimeout(function() { location.reload() }, 100)
		});

	});
	
	$(".cancelMerge").click(function() {
		var id = $(this).attr('data-id');
		$('#conf-merge-data-id').val(id);
		});
	
	$("#cancelMergeRequest").click(function() {
		var myObject = new Object();
		var url = getURL('/deleteRecalTxns');
		var cancelBtnId = $('#conf-merge-data-id').val();
		var cycleDate = $('#btn-mergeCancel' +cancelBtnId).attr("data-cycleDate");
		var cycleNumber = $('#btn-mergeCancel' +cancelBtnId).attr("data-cycleNumber");
		var settlementProductId = $('#btn-mergeCancel' +cancelBtnId).attr("data-productId");
		var uid = $('#btn-mergeCancel' +cancelBtnId).attr("data-uid");
		var requestDate = $('#btn-mergeCancel' +cancelBtnId).attr("data-requestDate");
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		myObject.cycleDate = cycleDate;
		myObject.cycleNumber = cycleNumber;
		myObject.settlementProductId = settlementProductId;
		myObject.uid = uid;
		myObject.requestDate = requestDate;
		$.ajax({
			type: "POST",
			contentType: "application/json",
			url: url,
			data: JSON.stringify(myObject),
			dataType: 'json',
			"beforeSend": function(xhr) {
				xhr.setRequestHeader('_TransactToken', tokenValue);
			},
			success: function(response) {
				$("#alertCancelMsg").removeClass("hide");
				if (response == null || response == '' || response == undefined) {
					$("#alertCancelMsg").addClass("alert-danger");
					$("#alertCancelMessage").text("Error! ");
					$("#ShowMessage").text("Failed to delete records of recal tables: " + "Internal server error");
				} else {
					if (response.Status == "FAILED" || response.Status == "503") {
						$("#alertCancelMsg").addClass("alert-danger");
						$("#alertCancelMessage").text("Error! ");
						$("#ShowMessage").text("Failed to delete records of recal tables : " + response.errorMessage);
					} else if (response.Status == "SUCCESS" || response.Status == "200") {
						$("#alertCancelMsg").addClass("alert-success");
						$("#alertCancelMessage").text("Success! ");
						$("#ShowMessage").text("Successfully delete records of recal tables.");
					} else {
						$("#alertCancelMsg").addClass("alert-danger");
						$("#alertCancelMessage").text("Error! ");
						$("#ShowMessage").text("Failed to delete records of recal tables: " + "Internal server error");
					}
				}
			},
			error: function(_e) {
				$("#btn-save").prop("disabled", false);
			}
		});
		$("#Okbtn").click(function() {
			window.setTimeout(function() { location.reload() }, 100)
		});
		
	});


});