$(document).ready(
	function() {
		$('form')
		.each(function() {
			$(this).data('serialized', $(this).serialize())
		})
		.on('change input', function() {
			$(this)
				.find('input:submit, button:submit')
				.prop('disabled', $(this).serialize() == $(this).data('serialized'))
				;
			$('#submitButton').prop('disabled', false);
			
			var sysValueUsed=$('#sysType').val()
			if ( sysValueUsed!= 'SELECT' && sysValueUsed == 'Others' ) {
			$('#sysParamTypeDiv').show();
			}else{
			$('#sysParamTypeDiv').hide();
			}
		})
		
		
	$('#submitButton').prop('disabled', true);
		$("form :input").change(function() {
		$(this).closest('form').data('changed', true)
	});
	
	$('#clearSysParam').click(function()
			{
        $('#sysType').val("SELECT");
        $('#sysParamTypeDiv').hide();
        $('#sysKey').val("")
        $('#sysType1').val("")
        $('#addProp').val("")
        $('#sysValue').val("")
		$('#desc').val("")
		$('#status').val("A")
		
		$("#errsysType").hide();
		$("#errsysType1").hide();
		$("#errsysKey").hide();
		$("#erraddProp").hide();
		$("#errsysValue").hide();
		$("#errdesc").hide();
		$("#errstatus").hide();
			});	
	
	$('#sysType').on('keyup keypress blur change',function () {
        validateFromCommonVal('sysType', true, "SelectionBox", "", false);
        checkDupSysParam(); 
    });

   $('#sysType1').on('keyup keypress blur change',function () {
        validateFromCommonVal('sysType1', false, "AlphaNumericWithSpace", 50, false); 
            
        if ($('#sysType').val() != 'SELECT' && $('#sysType').val() == 'Others' ) {
	         validateFromCommonVal('sysType1', true, "AlphaNumericWithSpace", 50, false);
	         checkDupSysParam();
        }
    });
    
     $('#sysKey').on('keyup keypress blur change',function () {
	        validateFromCommonVal('sysKey', true, "AlphaNumericWithSpace", 50, false); 
	        checkDupSysParam();
     });

	$('#sysValue').on('keyup keypress blur change',function () {
            validateFromCommonVal('sysValue', true, "AlphaNumericWithSpace", 50, false); 
            checkDupSysParam();
    });
    $('#addProp').on('keyup keypress blur change',function () {
            validateFromCommonVal('addProp', true, "AlphaNumericWithSpace", 50, false); 
            checkDupSysParam();
    });

	$('#desc').on('keyup keypress blur change',function () {
	        validateFromCommonVal('desc', true, "AlphaNumericWithSpace", 50, false); 
	        checkDupSysParam();
    });

   if (document.getElementById("onlyView").value == "Y") {
        document.getElementById("sysType").disabled = true;
        document.getElementById("sysKey").disabled = true;
        document.getElementById("sysValue").disabled = true;
        document.getElementById("addProp").disabled = true;
        document.getElementById("desc").disabled = true;
        document.getElementById("status").disabled = true;
    }	
});

function checkDupSysParam(){
	var validLookup=false;
	var valueoftype=$('#sysType').val();
	     if ($('#sysType').val() != 'SELECT' && $('#sysType').val() == 'Others' ) {
	       valueoftype=$('#sysType1').val()
        }
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	if ($('#flow').val()  == 'Y') {

		$.ajax({
			url: "checkDupSysDetails",
			type: "POST",
			dataType: "json",
			data: {

				"sysType": valueoftype,
				"sysKey": $('#sysKey').val(),
				"_TransactToken": tokenValue
			},
			success: function(response) {

				if (response.status == "BSUC_0001") {
					validLookup = true;

					$('#errorStatus2').show();

					$('#errorStatus2').html('SysParams Details Already Exists');

					callBack(true);




				} else {
					validLookup = false;
$('#errorStatus2').hide();
					$('#errorStatus2').html('');
					callBack(false);

				}
			},
			error: function(_request, _status, _error) {
					$('#errorStatus2').html('');
				callBack(false);
			}
		});

	} else {
		validLookup = false;
	}

	return validLookup;}


var ajaxValidLookUp;

function callBack(flag) {
	ajaxValidLookUp = flag;
}

function validateSysParam(url, flag){
	var check = false;
	if(!validateFromCommonVal('sysType', true, "SelectionBox", "", false)){
		check=true;
	}
	console.log($('#sysType').val() )
    if ($('#sysType').val() == 'Others' ) {
    if(!validateFromCommonVal('sysType1', true, "AlphaNumericWithSpace", 50, false)){
			check=true;
	}
	}

	if(!validateFromCommonVal('sysKey', true, "AlphaNumericWithSpace", 50, false)){
		check=true;
	}

	if(!validateFromCommonVal('sysValue', true, "AlphaNumericWithSpace", 50, false)){
		check=true;
	}
	
	if(!validateFromCommonVal('addProp', true, "AlphaNumericWithSpace", 50, false)){
		check=true;
	}
			
	if(!validateFromCommonVal('desc', true, "AlphaNumericWithSpace", 50, false)){
		check=true;
	}

    if (ajaxValidLookUp) {
       check= true;
	}
	if (!check) {

		addOrEditSysParam(url, flag)
	}else{
		return false;
	}
}

function addOrEditSysParam(url, flag){
	var data="";
	var sysTypeValue=$('#sysType').val();
	if (flag == 'A') {
		if (sysTypeValue != 'SELECT' && sysTypeValue == 'Others' ) {
		 sysTypeValue=$('#sysType1').val();
	    }
}
	data = "sysType," + sysTypeValue + ",sysKey,"
				+ $('#sysKey').val() + ",sysValue," + $('#sysValue').val()
				+ ",desc," + $('#desc').val() + ",status,"+ $('#status').val()+",addProp," + $('#addProp').val();

	postData(url, data);
}



function discardSysParam(url){
	var data = "sysType," + $('#sysType1').val()+",sysKey," + $('#sysKey1').val();
	postData(url, data);
}

function confirmationDiscardSysParam(type, key){
		$('#sysType1').val(type);
		$('#sysKey1').val(key);
		$("#toggleModalDiscardSysParam").modal('show'); 
}

function backPrevious(url){
	postData(url, "");
}
