package org.npci.settlenxt.adminportal.service;

import java.security.SecureRandom;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.cache.DataSecurityUtility;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.ErrorConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.MemberRepository;
import org.npci.settlenxt.common.cache.BaseLookupDTOCache;
import org.npci.settlenxt.common.cache.CacheReloaderConstants;
import org.npci.settlenxt.portal.common.dto.BinDTO;
import org.npci.settlenxt.portal.common.dto.BinDetailsCodeDescriptionDTO;
import org.npci.settlenxt.portal.common.dto.BinDetailsDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.FeatureFeeDTO;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;
import org.npci.settlenxt.portal.common.dto.LookUpDTO;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.MemberOnBoardingDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.npci.settlenxt.portal.common.dto.SettlementBinDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.service.BaseCacheReloaderService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Transactional(rollbackFor = Throwable.class)
@Service
public class MemberServiceImpl implements MemberService {
	@Autowired
	private MemberRepository memberRepository;

	@Autowired
	private MemberFileService memberFileService;

	@Autowired
	private BaseLookupDTOCache lookupDTOCache;

	@Autowired
	private MasterService masterSvc;

	@Autowired
	private DataSecurityUtility dataSecurityUtil;

	@Autowired
	private SessionDTO sessionDTO;

	@Autowired
	private BaseCacheReloaderService baseCacheReloaderSvc;
	
	@Autowired
	private Environment env;

	private static final String NO = "N";
	private static final String YES = "Y";
	private static final String IS_INTERNATIONAL_PARTICIPANT = "is.international.enabled";
	private boolean isInternational;
	
	@PostConstruct
	void init() {
		isInternational = "Y".equalsIgnoreCase(env.getProperty(IS_INTERNATIONAL_PARTICIPANT));
	}

	@Override
	public List<MemberDTO> getMembers(SearchCriteriaDTO searchCriteriaDTO) {
		String sSearch = null;
		if (searchCriteriaDTO.getSearchName() != null && !searchCriteriaDTO.getSearchName().equalsIgnoreCase("") ) {
		
				sSearch = "%" + searchCriteriaDTO.getSearchName().trim() + "%";

			
		}
		List<MemberDTO> membersData = memberRepository.getPendingMembers(searchCriteriaDTO.getStartVal(),
				searchCriteriaDTO.getEndVal(), sSearch);

		List<MemberDTO> membersDataList = new ArrayList<>();
		for (MemberDTO memberDto : membersData) {
			membersDataList.add(decryptParticipantSensitiveData(memberDto));

		}
		return membersDataList;
	}

	@Override
	public MemberDTO addMember(MemberDTO memberDTO) {

		SecureRandom secureRandom = new SecureRandom();
		int randomId = secureRandom.nextInt() * 10000;
		memberDTO.setMemberId(randomId);

		memberDTO.setRecordStatus("In Progress");
		memberDTO.setParticipantName(memberDTO.getBankName());
		memberDTO.setRequestState("Submitted");
		memberDTO.setMemberId(memberDTO.getMemberId());
		return memberDTO;
	}

	@Override
	public List<BinDetailsDTO> getBinDetails(String participantId, String binType, String flag) {

		if ("I".equals(binType) || "T".equals(binType)) {
			List<BinDetailsDTO> issBinList;
			if(YES.equalsIgnoreCase(flag)) {
				issBinList = memberRepository.getBinDetailsForDeleteBin(participantId, binType,
						BaseCommonConstants.ACTIVE_BIN_STATUS, isInternational);
			}
			else
			{
				issBinList = memberRepository.getBinDetails(participantId, binType,
						BaseCommonConstants.ACTIVE_BIN_STATUS, isInternational);
			}
			if (!CollectionUtils.isEmpty(issBinList)) {
				for (BinDetailsDTO issBin : issBinList) {
					setLookupDataInIssueBinList(issBin);
				}
			}

			return issBinList;
		} else {
			List<BinDetailsDTO> acqBinList ;
			if(YES.equalsIgnoreCase(flag)) {
				acqBinList = memberRepository.getAcqBinDetailsDeleteInsert(participantId, binType,
						BaseCommonConstants.ACTIVE_BIN_STATUS);
			}
			else
			{
				acqBinList = memberRepository.getAcqBinDetails(participantId, binType,
						BaseCommonConstants.ACTIVE_BIN_STATUS);
			}
			
			
			setAcqBinVals(acqBinList);

			return acqBinList;
		}

	}

	private void setLookupDataInIssueBinList(BinDetailsDTO issBin) {
		issBin.getBinDetailsCodeDescription().setBinCardType(
				lookupDTOCache.getDescription(BaseCommonConstants.BIN_CARD_TYPE, issBin.getBinCardType()));
		issBin.getBinDetailsCodeDescription().setBinProductType(lookupDTOCache
				.getDescription(BaseCommonConstants.BIN_PRODUCT_TYPE, issBin.getBinProductType()));
		issBin.getBinDetailsCodeDescription().setBinCardVariant(lookupDTOCache
				.getDescription(BaseCommonConstants.BIN_CARD_VARIANT, issBin.getBinCardVariant()));
		issBin.getBinDetailsCodeDescription().setBinCardBrand(
				lookupDTOCache.getDescription(BaseCommonConstants.BIN_CARD_BRAND, issBin.getBinCardBrand()));
		issBin.getBinDetailsCodeDescription().setIssBinType(
				lookupDTOCache.getDescription(BaseCommonConstants.CARD_ENTITY, issBin.getIssBinType()));
		issBin.getBinDetailsCodeDescription().setCardTechnology(
				lookupDTOCache.getDescription(BaseCommonConstants.CARD_TECHNOLOGY, issBin.getCardTechnology()));
		issBin.getBinDetailsCodeDescription().setAuthMechanism(
				lookupDTOCache.getDescription(BaseCommonConstants.AUTH_MECHANISM, issBin.getAuthMechanism()));
		issBin.getBinDetailsCodeDescription().setBankGroup(
				lookupDTOCache.getDescription(BaseCommonConstants.BANK_GROUP, issBin.getIssBankGroup()));
		issBin.getBinDetailsCodeDescription().setDomainUsage(
				lookupDTOCache.getDescription(BaseCommonConstants.DOMAIN_USAGE, issBin.getIssDomainUsage()));
		issBin.getBinDetailsCodeDescription().setMessageType(
				lookupDTOCache.getDescription(BaseCommonConstants.MESSAGE_TYPE, issBin.getMessageType()));
		issBin.getBinDetailsCodeDescription().setProductType(
				lookupDTOCache.getDescription(CommonConstants.PROD_TYPE, issBin.getIssProductType()));
		issBin.getBinDetailsCodeDescription().setSubScheme(
				lookupDTOCache.getDescription(BaseCommonConstants.SUB_SCHEME, issBin.getSubScheme()));
		issBin.getBinDetailsCodeDescription().setCardSubVariant(lookupDTOCache
				.getDescription(BaseCommonConstants.CARD_SUB_VARIANT, issBin.getCardSubVariant()));

		issBin.getBinDetailsCodeDescription().setProgramDetails(lookupDTOCache
				.getDescription(BaseCommonConstants.PROGRAM_DETAILS_LIST, issBin.getProgramDetails()));
		issBin.getBinDetailsCodeDescription().setFormFactor(
				lookupDTOCache.getDescription(BaseCommonConstants.FORM_FACTOR_LIST, issBin.getFormFactor()));
	}

	private void setAcqBinVals(List<BinDetailsDTO> acqBinList) {
		if (!CollectionUtils.isEmpty(acqBinList)) {
			for (BinDetailsDTO acqBin : acqBinList) {
				acqBin.setBinDetailsCodeDescription(new BinDetailsCodeDescriptionDTO());
				acqBin.getBinDetailsCodeDescription().setDomainUsage(
						lookupDTOCache.getDescription(BaseCommonConstants.DOMAIN_USAGE, acqBin.getAcqDomainUsage()));
				acqBin.getBinDetailsCodeDescription().setBankGroup(
						lookupDTOCache.getDescription(BaseCommonConstants.BANK_GROUP, acqBin.getAcqBankGroup()));
				acqBin.getBinDetailsCodeDescription().setProductType(
						lookupDTOCache.getDescription(BaseCommonConstants.PRODUCT_TYPE, acqBin.getAcqProductType()));
				acqBin.getBinDetailsCodeDescription().setBankGroup(
						lookupDTOCache.getDescription(BaseCommonConstants.BANK_GROUP, acqBin.getAcqBankGroup()));
			}
		}
	}

	@Override
	public List<BinDetailsDTO> getBinDetailsForPendingForApproval(String participantId, String binType) {

		if ("I".equals(binType) || "T".equals(binType)) {
			List<BinDetailsDTO> issBinList = memberRepository.getBinDetailsForPendingForApproval(participantId, binType,
					BaseCommonConstants.ACTIVE_BIN_STATUS, isInternational);
			if (!CollectionUtils.isEmpty(issBinList)) {
				for (BinDetailsDTO issBin : issBinList) {
					setLookupDataInIssueBinList(issBin);

				}
			}

			return issBinList;
		} else {
			List<BinDetailsDTO> acqBinList = memberRepository.getAcqBinDetailsForPendingForApproval(participantId,
					binType, BaseCommonConstants.ACTIVE_BIN_STATUS);
			setAcqBinVals(acqBinList);

			return acqBinList;
		}

	}

	private List<BinDetailsDTO> getAcqBinDetailsMain(String participantId, String binType) {

		List<BinDetailsDTO> acqBinList = memberRepository.getAcqBinDetailsMain(participantId, binType,
				BaseCommonConstants.ACTIVE_BIN_STATUS);
		setAcqBinVals(acqBinList);

		return acqBinList;

	}

	@Override
	public List<BinDetailsDTO> getIssuerAndTokenBinDetails(String participantId, String flag) {
		List<BinDetailsDTO> issBinList ;
		if(YES.equalsIgnoreCase(flag)) {
			issBinList = memberRepository.getIssuerAndTokenBinDetailsForDeleteBin(participantId, isInternational);
		}
		else
		{
			issBinList = memberRepository.getIssuerAndTokenBinDetails(participantId, isInternational);
		
		}
		
		 
		for (BinDetailsDTO issBin : issBinList) {
			issBin.setBinDetailsCodeDescription(new BinDetailsCodeDescriptionDTO());
			setLookupDataInIssueBinList(issBin);
			issBin.setNetworkSelection(memberRepository.getNetworkSelectionList(issBin.getBinNumber(),participantId));
		}
		return issBinList;
	}

	@Override
	public List<BinDetailsDTO> getIssuerAndTokenBinDetailsForPendingForApproval(String participantId) {
		List<BinDetailsDTO> issBinList = memberRepository
				.getIssuerAndTokenBinDetailsForPendingForApproval(participantId, isInternational);
		for (BinDetailsDTO issBin : issBinList) {
			issBin.setBinDetailsCodeDescription(new BinDetailsCodeDescriptionDTO());
			issBin.setNetworkSelection(memberRepository.getNetworkSelectionList(issBin.getBinNumber(),participantId));
			setLookupDataInIssueBinList(issBin);
		}
		return issBinList;
	}

	private List<BinDetailsDTO> getIssuerAndTokenBinDetailsMain(String participantId) {
		List<BinDetailsDTO> issBinList = memberRepository.getIssuerAndTokenBinDetailsMain(participantId,
				isInternational);
		for (BinDetailsDTO issBin : issBinList) {
			issBin.setBinDetailsCodeDescription(new BinDetailsCodeDescriptionDTO());
			issBin.setNetworkSelection(memberRepository.getNetworkSelectionList(issBin.getBinNumber(),participantId));
			setLookupDataInIssueBinList(issBin);
		}
		return issBinList;
	}

	@Override
	public void loadBinDetailsDTOs(MemberDTO memberDTO) {
		
		memberDTO.setIssBinList(getBinDetails(memberDTO.getParticipantId(), BaseCommonConstants.BIN_TYPE_ISSUER,NO));
		memberDTO.setAcqBinList(getBinDetails(memberDTO.getParticipantId(), BaseCommonConstants.BIN_TYPE_ACQUIRER,NO));
	}

	@Override
	public MemberDTO addMemberInfo(MemberOnBoardingDTO memberOnBoardingDTO) {

		MemberDTO memberDTO = setMemberData(encryptParticipantSensitiveData(memberOnBoardingDTO));
		memberDTO.setRecordStatus("I");
		memberDTO.setStatus("A");
		memberDTO.setRequestState("SAVE");
		memberDTO.setCreatedBy(sessionDTO.getUserName());
		memberDTO.setCreatedOn(new Date());
		memberOnBoardingDTO.setLastUpdatedBy(sessionDTO.getUserName());
		memberDTO.isInternational();
		memberDTO.setInternational(isInternational);
		memberRepository.addMember(memberDTO);
		return memberDTO;
	}

	private MemberDTO addMemberInfoMain(MemberOnBoardingDTO memberOnBoardingDTO) {

		MemberDTO memberDTO = setMemberData(memberOnBoardingDTO);
		memberDTO.setStatus(memberOnBoardingDTO.getStatus());

		memberRepository.addMemberMain(memberDTO);
		return memberDTO;
	}

	protected MemberDTO setMemberData(MemberOnBoardingDTO memberOnBoardingDTO) {
		MemberDTO memberDTO = new MemberDTO();
		memberOnBoardingDTO.setMemberId(fetchMemberIdSeq());
		memberDTO.setMemberId(memberOnBoardingDTO.getMemberId());
		memberDTO.setMemberType(memberOnBoardingDTO.getMemberType());
		memberDTO.setMemberName(memberOnBoardingDTO.getMemberName());
		memberDTO.setIfscCode(memberOnBoardingDTO.getIfscCode());
		memberDTO.setBankMasterCode(memberOnBoardingDTO.getBankMasterCode());
		memberDTO.setRtgsCode(memberOnBoardingDTO.getRtgsCode());
		memberDTO.setParticipantId(memberOnBoardingDTO.getParticipantId());
		memberDTO.setParticipantIdNFS(memberOnBoardingDTO.getParticipantIdNFS());
		memberDTO.setParticipantName(memberDTO.getMemberName());
		memberDTO.setSavingsAccNumber(memberOnBoardingDTO.getSavingsAccNumber());
		memberDTO.setCurrentAccNumber(memberOnBoardingDTO.getCurrentAccNumber());
		memberDTO.setBnkPhone(memberOnBoardingDTO.getBnkPhone());
		memberDTO.setBnkPhone2(memberOnBoardingDTO.getBnkPhone2());
		memberDTO.setBnkMobile(memberOnBoardingDTO.getBnkMobile());
		memberDTO.setBnkMobile2(memberOnBoardingDTO.getBnkMobile2());
		memberDTO.setBnkEmail(memberOnBoardingDTO.getBnkEmail());
		memberDTO.setBnkEmail2(memberOnBoardingDTO.getBnkEmail2());
		memberDTO.setBnkAdd(memberOnBoardingDTO.getBnkAdd());
		memberDTO.setBnkCountry(memberOnBoardingDTO.getBnkCountry());
		memberDTO.setBnkState(memberOnBoardingDTO.getBnkState());
		memberDTO.setBnkCity(memberOnBoardingDTO.getBnkCity());
		memberDTO.setBnkPincode(memberOnBoardingDTO.getBnkPincode());
		memberDTO.setGstIn(memberOnBoardingDTO.getGstIn());
		memberDTO.setGstAdd(memberOnBoardingDTO.getGstAdd());
		memberDTO.setGstCountry(memberOnBoardingDTO.getGstCountry());
		memberDTO.setGstState(memberOnBoardingDTO.getGstState());
		memberDTO.setGstCity(memberOnBoardingDTO.getGstCity());
		memberDTO.setGstPincode(memberOnBoardingDTO.getGstPincode());
		memberDTO.setBankSector(memberOnBoardingDTO.getBankSector());
		memberDTO.setParentParticipantId(memberOnBoardingDTO.getParentParticipantId());
		memberDTO.setWebSite(memberOnBoardingDTO.getWebSite());
		memberDTO.setSubNet(memberOnBoardingDTO.getSubNet());
		memberDTO.setUniqueBnk(memberOnBoardingDTO.getUniqueBnk());
		memberDTO.setMaxUser(memberOnBoardingDTO.getMaxUser());
		memberDTO.setCreatedBy(memberOnBoardingDTO.getCreatedBy());
		memberDTO.setCreatedOn(memberOnBoardingDTO.getCreatedOn());
		memberDTO.setCurrencyConversionBy(memberOnBoardingDTO.getCurrencyConversionBy());
		memberDTO.setCurrencyConversionType(memberOnBoardingDTO.getCurrencyConversionType());
		memberDTO.setIsType(memberOnBoardingDTO.getIsType());
		memberDTO.setInternational(isInternational);
		memberDTO.setForexId(memberOnBoardingDTO.getForexId());
		memberDTO.setDomesticFlag(memberOnBoardingDTO.getDomesticFlag());
		memberDTO.setDomesticCreatedDate(memberOnBoardingDTO.getDomesticCreatedDate());
		return memberDTO;
	}

	@Override
	public MemberDTO addMemberContactInfo(MemberOnBoardingDTO memberOnBoardingDTO) {
		MemberDTO memberDTO = setContatData(memberOnBoardingDTO);
		memberDTO.setCreatedBy(sessionDTO.getUserName());
		memberDTO.setStatus(CommonConstants.STATUS_APPROVE);
		memberDTO.setCreatedOn(new Date());
		memberRepository.addMemberContactInfo(memberDTO);
		return memberDTO;
	}

	private MemberDTO addMemberContactInfoMain(MemberOnBoardingDTO memberOnBoardingDTO) {
		MemberDTO memberDTO = setContatData2(memberOnBoardingDTO);
		memberDTO.setCreatedBy(memberOnBoardingDTO.getCreatedBy());
		memberDTO.setStatus(CommonConstants.STATUS_APPROVE);
		memberDTO.setLastUpdatedBy(memberOnBoardingDTO.getLastUpdatedBy());
		memberRepository.addMemberContactInfoMainTable(memberDTO);
		return memberDTO;
	}

	private MemberDTO setContatData2(MemberOnBoardingDTO memberOnBoardingDTO) {

		MemberDTO memberDTO = new MemberDTO();

		memberDTO.setParticipantContactId(memberOnBoardingDTO.getParticipantContactId());
		memberDTO.setMemberId(memberOnBoardingDTO.getMemberId());
		memberDTO.setParticipantId(memberOnBoardingDTO.getParticipantId());
		memberDTO.setAddressType(memberOnBoardingDTO.getAddressType());
		memberDTO.setCntChkrName(memberOnBoardingDTO.getCntChkrName());
		memberDTO.setCntPhone(memberOnBoardingDTO.getCntPhone());
		memberDTO.setCntMobile(memberOnBoardingDTO.getCntMobile());
		memberDTO.setCntFax(memberOnBoardingDTO.getCntFax());
		memberDTO.setCntAdd1(memberOnBoardingDTO.getCntAdd1());
		memberDTO.setCntCountry(memberOnBoardingDTO.getCntCountry());
		memberDTO.setCntState(memberOnBoardingDTO.getCntState());
		memberDTO.setCntCity(memberOnBoardingDTO.getCntCity());
		memberDTO.setCntPincode(memberOnBoardingDTO.getCntPincode());
		memberDTO.setCntEmail(memberOnBoardingDTO.getCntEmail());
		memberDTO.setCntDesignation(memberOnBoardingDTO.getCntDesignation());

		return memberDTO;
	}

	protected MemberDTO setContatData(MemberOnBoardingDTO memberOnBoardingDTO) {
		MemberDTO memberDTO = new MemberDTO();

		memberDTO.setMemberId(memberOnBoardingDTO.getMemberId());

		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntChkrName())) {
			memberDTO.setCntChkrName(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntChkrName()));
		}
		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntMobile())) {
			memberDTO.setCntMobile(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntMobile()));
		}
		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntDesignation())) {
			memberDTO.setCntDesignation(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntDesignation()));
		}
		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntEmail())) {
			memberDTO.setCntEmail(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntEmail()));
		}
		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntAdd1())) {
			memberDTO.setCntAdd1(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntAdd1()));
		}
		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntPhone())) {
            memberDTO.setCntPhone(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntPhone()));
        }
		memberDTO.setParticipantId(memberOnBoardingDTO.getParticipantId());
		memberDTO.setAddressType(memberOnBoardingDTO.getAddressType());
		memberDTO.setCntFax(memberOnBoardingDTO.getCntFax());
		memberDTO.setCntCountry(memberOnBoardingDTO.getCntCountry());
		memberDTO.setCntState(memberOnBoardingDTO.getCntState());
		memberDTO.setCntCity(memberOnBoardingDTO.getCntCity());
		memberDTO.setCntPincode(memberOnBoardingDTO.getCntPincode());

		return memberDTO;
	}

	@Override
	public Boolean checkMemberContactInfoSaved(String partcipantId) {
		return memberRepository.checkMemberContactInfoSaved(partcipantId) > 0;
	}

	@Override
	public List<SettlementBinDTO> getsettlementBinsList(String participantId, List<String> statusList) {
		return memberRepository.getsettlementBinsList(participantId, statusList);
	}

	@Override
	public List<MemberDTO> getFinalMemberList(SearchCriteriaDTO searchCriteriaDTO) {
		String sSearch = null;
		if (searchCriteriaDTO.getSearchName() != null  && !searchCriteriaDTO.getSearchName().equalsIgnoreCase("")) {
				sSearch = "%" + searchCriteriaDTO.getSearchName().trim() + "%";

			
		}
		List<MemberDTO> membersData = memberRepository.getFinalMemberList(searchCriteriaDTO.getStartVal(),
				searchCriteriaDTO.getEndVal(), sSearch);

		List<MemberDTO> membersDataList = new ArrayList<>();
		for (MemberDTO memberDto : membersData) {
			membersDataList.add(decryptParticipantSensitiveData(memberDto));

		}
		return membersDataList;

	}

	@Override
	public List<String> getParticipantActualFileNames(String participantId, String fileNames) {
		List<String> fileNameList = Arrays.asList(StringUtils.split(fileNames, "#"));
		return memberRepository.getFilesPaths(fileNameList, participantId);
	}

	@Override
	public void approveMember(MemberDTO memberDTO) {

		if (StringUtils.equals(memberDTO.getApprovalStatus(), CommonConstants.STATUS_APPROVE)) {

			MemberOnBoardingDTO memberOnBoardingDTO = getMemberDataForDeletedBin(memberDTO.getParticipantId());

			List<BinDTO> acqBinDTOList = memberRepository.getUserBinStgWithAcquireBin(memberDTO.getParticipantId());
			List<BinDTO> issBinDTOList = memberRepository.getUserBinStgWithIssuerBin(memberDTO.getParticipantId());
			List<BinDTO> acqBinDTOListMain = memberRepository
					.getUserBinStgWithAcquireBinMain(memberDTO.getParticipantId());

			List<BinDTO> issBinDTOListMain = memberRepository
					.getUserBinStgWithIssuerBinMain(memberDTO.getParticipantId());

			memberRepository.deleteUserBinMainWithParticipantID(memberDTO.getParticipantId());
			memberRepository.deleteUserBinStgWithParticipantID(memberDTO.getParticipantId());
			memberRepository.deleteMemberBin(memberDTO.getParticipantId());
			memberRepository.deleteSettlementBinFinal(memberDTO.getParticipantId());
			memberRepository.deleteDocumentMain(memberDTO.getParticipantId());
			memberOnBoardingDTO.setInternational(isInternational);
			updateMemberInfoMain(memberOnBoardingDTO);
			addUserInStg(memberOnBoardingDTO, acqBinDTOList);
			addUserInMain(memberOnBoardingDTO, acqBinDTOListMain);
			addBinNetworkMappingDetailsMain(memberOnBoardingDTO.getIssBinList(),memberOnBoardingDTO.getParticipantId());

			for (BinDetailsDTO binDetailsDTO : memberOnBoardingDTO.getIssBinList()) {
				List<BinDTO> binDTOListLocal = issBinDTOList.stream()
						.filter(binDTO -> StringUtils.equals(binDTO.getBinNumber(), binDetailsDTO.getBinNumber()))
						.toList();
				binDTOListLocal.forEach(userBin -> userBin.setBinId(binDetailsDTO.getBinId()));
				if (!CollectionUtils.isEmpty(binDTOListLocal)) {

					memberRepository.addUserBinStg(binDTOListLocal);
				}
			}
			for (BinDetailsDTO binDetailsDTO : memberOnBoardingDTO.getIssBinList()) {
				List<BinDTO> binDTOListLocal = issBinDTOListMain.stream()
						.filter(binDTO -> StringUtils.equals(binDTO.getBinNumber(), binDetailsDTO.getBinNumber()))
						.toList();
				binDTOListLocal.forEach(userBin -> userBin.setBinId(binDetailsDTO.getBinId()));
				if (!CollectionUtils.isEmpty(binDTOListLocal)) {
					memberRepository.addUserBinMain(binDTOListLocal);
				}
			}
		} else {
			memberDTO.setRejectedOn(new Date());
		}
		memberRepository.memberApproveStg(memberDTO.getParticipantId(), memberDTO.getRejectReason(), new Date(),
				sessionDTO.getUserName(), memberDTO.getApprovalStatus(), memberDTO.getRejectedOn());
		memberRepository.clearCurrentDeleteFlag(memberDTO.getParticipantId());

	}

	private void addBinNetworkMappingDetailsMain(List<BinDetailsDTO> issBinList, String participantId) {

		for(BinDetailsDTO issVal:issBinList) {

			memberRepository.deletePreviousBinMain(issVal.getBinNumber());

			List<BinDetailsDTO> listAvailableInDbStg = memberRepository
					.getNetworkSelectionListStg(issVal.getBinNumber(), participantId);
			List<String> updatedNetwork = listAvailableInDbStg.stream().filter(x -> x.getStatus().equals("P"))
					.map(BinDetailsDTO::getMappingNetwork).toList();

			for (String network : updatedNetwork)
				memberRepository.updateBinNetworkMappingDetails(network, issVal, participantId, "A");
			listAvailableInDbStg = memberRepository.getNetworkSelectionListStg(issVal.getBinNumber(), participantId);
			memberRepository.addBinNetworkMappingDetailsMain(listAvailableInDbStg, issVal, participantId);

		}
		
	}

	private void addUserInMain(MemberOnBoardingDTO memberOnBoardingDTO, List<BinDTO> acqBinDTOListMain) {
		for (BinDetailsDTO binDetailsDTO : memberOnBoardingDTO.getAcqBinList()) {
			List<BinDTO> binDTOListLocal = acqBinDTOListMain.stream()
					.filter(binDTO -> StringUtils.equals(binDTO.getAcquirerId(), binDetailsDTO.getAcquirerId()))
					.toList();
			binDTOListLocal.forEach(userBin -> userBin.setBinId(binDetailsDTO.getBinId()));
			if (!CollectionUtils.isEmpty(binDTOListLocal)) {
				memberRepository.addUserBinMain(binDTOListLocal);
			}
		}
	}

	private void addUserInStg(MemberOnBoardingDTO memberOnBoardingDTO, List<BinDTO> acqBinDTOList) {
		for (BinDetailsDTO binDetailsDTO : memberOnBoardingDTO.getAcqBinList()) {
			List<BinDTO> binDTOListLocal = acqBinDTOList.stream()
					.filter(binDTO -> StringUtils.equals(binDTO.getAcquirerId(), binDetailsDTO.getAcquirerId()))
					.toList();
			binDTOListLocal.forEach(userBin -> userBin.setBinId(binDetailsDTO.getBinId()));
			if (!CollectionUtils.isEmpty(binDTOListLocal)) {
				memberRepository.addUserBinStg(binDTOListLocal);
			}
		}
	}

	private void updateMemberInfoMain(MemberOnBoardingDTO memberOnBoardingDTO) {

		if (checkIfParticipantExists(memberOnBoardingDTO.getParticipantId())) {
			memberRepository.updateMemberInfoMain(memberOnBoardingDTO);
		} else {
			addMemberInfoMain(memberOnBoardingDTO);
		}
		updateSettlementBinDataMain(memberOnBoardingDTO);
		boolean counts = checkIfParticipantContactExistsMain(memberOnBoardingDTO.getParticipantId());
		if (counts) {
			updateMemberContactInfoMain(memberOnBoardingDTO);
		} else {
			addMemberContactInfoMain(memberOnBoardingDTO);
		}
		updateAcquirerBinDataMain(memberOnBoardingDTO);
		updateIssuerBinDataMain(memberOnBoardingDTO);
		memberOnBoardingDTO.setParticipantName(memberOnBoardingDTO.getMemberName());
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getDocuments())) {
			for (FileUploadDTO fileUpload : memberOnBoardingDTO.getDocuments()) {
				memberRepository.addFileDetailsMain(fileUpload);
			}
		}
		// updates reload cache value and inst list
		baseCacheReloaderSvc.updateReloadCacheCounter(new String[] { CacheReloaderConstants.PARTICIPANT,
				CacheReloaderConstants.PARTICIPANT_SETTLEMENT_BIN, CacheReloaderConstants.MEMBINDETAILS });

	}

	@Override
	public MemberOnBoardingDTO encryptParticipantSensitiveData(MemberOnBoardingDTO memBinDTO) {

		if (!StringUtils.isBlank(memBinDTO.getBnkPhone())) {
			memBinDTO.setBnkPhone(dataSecurityUtil.encrypt(memBinDTO.getBnkPhone()));
		}
		if (!StringUtils.isBlank(memBinDTO.getBnkPhone2())) {
			memBinDTO.setBnkPhone2(dataSecurityUtil.encrypt(memBinDTO.getBnkPhone2()));
		}
		if (!StringUtils.isBlank(memBinDTO.getBnkMobile())) {
			memBinDTO.setBnkMobile(dataSecurityUtil.encrypt(memBinDTO.getBnkMobile()));
		}

		if (!StringUtils.isBlank(memBinDTO.getBnkMobile2())) {
			memBinDTO.setBnkMobile2(dataSecurityUtil.encrypt(memBinDTO.getBnkMobile2()));
		}
		if (!StringUtils.isBlank(memBinDTO.getBnkEmail())) {
			memBinDTO.setBnkEmail(dataSecurityUtil.encrypt(memBinDTO.getBnkEmail()));
		}
		if (!StringUtils.isBlank(memBinDTO.getBnkEmail2())) {
			memBinDTO.setBnkEmail2(dataSecurityUtil.encrypt(memBinDTO.getBnkEmail2()));
		}

		if (!StringUtils.isBlank(memBinDTO.getBnkAdd())) {
			memBinDTO.setBnkAdd(dataSecurityUtil.encrypt(memBinDTO.getBnkAdd()));
		}
		if (!StringUtils.isBlank(memBinDTO.getGstAdd())) {
			memBinDTO.setGstAdd(dataSecurityUtil.encrypt(memBinDTO.getGstAdd()));
		}
		
		memBinDTO.setInternational(isInternational);
		return memBinDTO;
	}

	@Override
	public MemberOnBoardingDTO decryptParticipantSensitiveData(MemberOnBoardingDTO memOnBoardingDTO) {

		memOnBoardingDTO.setBnkPhone(dataSecurityUtil.decrypt(memOnBoardingDTO.getBnkPhone()));

		memOnBoardingDTO.setBnkPhone2(dataSecurityUtil.decrypt(memOnBoardingDTO.getBnkPhone2()));

		memOnBoardingDTO.setBnkMobile(dataSecurityUtil.decrypt(memOnBoardingDTO.getBnkMobile()));

		memOnBoardingDTO.setBnkMobile2(dataSecurityUtil.decrypt(memOnBoardingDTO.getBnkMobile2()));

		memOnBoardingDTO.setBnkEmail(dataSecurityUtil.decrypt(memOnBoardingDTO.getBnkEmail()));

		memOnBoardingDTO.setBnkEmail2(dataSecurityUtil.decrypt(memOnBoardingDTO.getBnkEmail2()));

		memOnBoardingDTO.setBnkAdd(dataSecurityUtil.decrypt(memOnBoardingDTO.getBnkAdd()));

		memOnBoardingDTO.setGstAdd(dataSecurityUtil.decrypt(memOnBoardingDTO.getGstAdd()));

		memOnBoardingDTO.setCntChkrName(dataSecurityUtil.decrypt(memOnBoardingDTO.getCntChkrName()));

		memOnBoardingDTO.setCntMobile(dataSecurityUtil.decrypt(memOnBoardingDTO.getCntMobile()));

		memOnBoardingDTO.setCntDesignation(dataSecurityUtil.decrypt(memOnBoardingDTO.getCntDesignation()));

		memOnBoardingDTO.setCntEmail(dataSecurityUtil.decrypt(memOnBoardingDTO.getCntEmail()));

		memOnBoardingDTO.setCntAdd1(dataSecurityUtil.decrypt(memOnBoardingDTO.getCntAdd1()));
		
		memOnBoardingDTO.setCntPhone(dataSecurityUtil.decrypt(memOnBoardingDTO.getCntPhone()));
		return memOnBoardingDTO;
	}

	@Override
	public MemberDTO decryptParticipantSensitiveData(MemberDTO memBinDTO) {

		memBinDTO.setBnkPhone(dataSecurityUtil.decrypt(memBinDTO.getBnkPhone()));

		memBinDTO.setBnkPhone2(dataSecurityUtil.decrypt(memBinDTO.getBnkPhone2()));

		memBinDTO.setBnkMobile(dataSecurityUtil.decrypt(memBinDTO.getBnkMobile()));

		memBinDTO.setBnkMobile2(dataSecurityUtil.decrypt(memBinDTO.getBnkMobile2()));

		memBinDTO.setBnkEmail(dataSecurityUtil.decrypt(memBinDTO.getBnkEmail()));

		memBinDTO.setBnkEmail2(dataSecurityUtil.decrypt(memBinDTO.getBnkEmail2()));

		memBinDTO.setBnkAdd(dataSecurityUtil.decrypt(memBinDTO.getBnkAdd()));

		memBinDTO.setGstAdd(dataSecurityUtil.decrypt(memBinDTO.getGstAdd()));

		return memBinDTO;
	}

	@Override
	public int updateMemberInfo(MemberOnBoardingDTO memberOnBoardingDTO, MultipartFile[] files) {
		updateSettlementBinData(memberOnBoardingDTO);
		boolean counts = checkIfParticipantContactExists(memberOnBoardingDTO.getParticipantId());
		if (counts) {
			updateMemberContactInfo(memberOnBoardingDTO);
		} else {
			if (!memberOnBoardingDTO.getAddressType().equalsIgnoreCase("0")) {
				addMemberContactInfo(memberOnBoardingDTO);
			}
		}
		if (checkIfParticipantExists(memberOnBoardingDTO.getParticipantId())) {
			memberOnBoardingDTO.setLastOperation("Edit Member");
		} else {
			memberOnBoardingDTO.setLastOperation("Add Member");
		}
		updateAcquirerBinData(memberOnBoardingDTO);
		updateIssuerBinData(memberOnBoardingDTO);
		memberOnBoardingDTO.setParticipantName(memberOnBoardingDTO.getMemberName());
		Map<String, String> uploadMap = new HashMap<>();

		if (files != null && files.length > 0) {
			List<FileUploadDTO> uploadedDocuments = memberFileService.saveDocuments(files,
					memberOnBoardingDTO.getBankMasterCode());
			for (FileUploadDTO document : uploadedDocuments) {
				uploadMap.put(document.getDocumentName(), document.getDocumentPath());

			}
		}
		memberOnBoardingDTO.setLastUpdatedOn(new Date());
		memberOnBoardingDTO.setLastUpdatedBy(sessionDTO.getUserName());
		updateDocumentData(memberOnBoardingDTO, uploadMap, memberOnBoardingDTO.getParticipantId());
		
		return memberRepository.updateMemberInfo(encryptParticipantSensitiveData(memberOnBoardingDTO));
	}

	protected void updateDocumentData(MemberOnBoardingDTO memberOnBoardingDTO, Map<String, String> uploadMap,
			String participantId) {
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getDocuments())) {

			List<String> existingDocumentList = memberRepository.getFileList(participantId).stream()
					.map(FileUploadDTO::getDocumentName).toList();

			for (FileUploadDTO fileUpload : memberOnBoardingDTO.getDocuments()) {
				if (fileUpload.getStatus().equalsIgnoreCase("D")) {
					addOrUpdateFileDetails(memberOnBoardingDTO, uploadMap, existingDocumentList, fileUpload);
				}
			}
			existingDocumentList = memberRepository.getFileList(participantId).stream()
					.map(FileUploadDTO::getDocumentName).toList();
			for (FileUploadDTO fileUpload : memberOnBoardingDTO.getDocuments()) {
				if (!fileUpload.getStatus().equalsIgnoreCase("D")) {
					addOrUpdateFileDetails(memberOnBoardingDTO, uploadMap, existingDocumentList, fileUpload);
				}
			}
		}
	}

	private void addOrUpdateFileDetails(MemberOnBoardingDTO memberOnBoardingDTO, Map<String, String> uploadMap,
			List<String> existingDocumentList, FileUploadDTO fileUpload) {
		if (!existingDocumentList.contains(fileUpload.getDocumentName())) {
			fileUpload.setDocumentPath(uploadMap.get(fileUpload.getDocumentName()));
			fileUpload.setParticipantId(memberOnBoardingDTO.getParticipantId());
			fileUpload.setIsActive(memberOnBoardingDTO.getIsActive());
			fileUpload.setCreatedBy(sessionDTO.getUserName());
			memberRepository.addFileDetails(fileUpload);
		} else {
			if (uploadMap.containsKey(fileUpload.getDocumentName())) {
				fileUpload.setDocumentPath(uploadMap.get(fileUpload.getDocumentName()));
			}
			fileUpload.setParticipantId(memberOnBoardingDTO.getParticipantId());
			fileUpload.setLastUpdatedBy(sessionDTO.getUserName());
			fileUpload.setLastUpdatedOn(new Date());
			memberRepository.updateFileDetails(fileUpload);
		}
	}

	private void updateSettlementBinDataMain(MemberOnBoardingDTO memberOnBoardingDTO) {
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getSettlementBinList())) {
			memberRepository.addSettlementBinFinal(memberOnBoardingDTO.getSettlementBinList());
		}
	}

	private void updateSettlementBinData(MemberOnBoardingDTO memberOnBoardingDTO) {
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getSettlementBinList())) {

			List<String> settlementBinNumberList = memberOnBoardingDTO.getSettlementBinList().stream()
					.map(SettlementBinDTO::getSettlementBinNumber).toList();
			List<SettlementBinDTO> existingSettlementBinNumberListForOtherParticipants = memberRepository
					.getSettlmentBinsForOtherParticipants(memberOnBoardingDTO.getParticipantId(),
							settlementBinNumberList);
			if (!CollectionUtils.isEmpty(existingSettlementBinNumberListForOtherParticipants)) {
				List<String> duplicateBins = existingSettlementBinNumberListForOtherParticipants.stream()
						.map(SettlementBinDTO::getSettlementBinNumber).toList();
				String errorMessage = ErrorConstants.SETTLEMENT_BIN_EXISTS_MESSAGE + ":  "
						+ StringUtils.join(duplicateBins, ",");
				throw new SettleNxtApplicationException(ErrorConstants.SETTLEMENT_BIN_EXISTS_CODE, errorMessage);
			}
			List<String> existingSettlementBinNumberList = memberRepository
					.getSettlmentBinsForParticipants(memberOnBoardingDTO.getParticipantId()).stream()
					.map(SettlementBinDTO::getSettlementBinNumber).toList();

			for (SettlementBinDTO settlementBinDTO : memberOnBoardingDTO.getSettlementBinList()) {
				if (existingSettlementBinNumberList.contains(settlementBinDTO.getSettlementBinNumber())) {
					if (settlementBinDTO.getActionType().equalsIgnoreCase("Edit")) {
						settlementBinDTO.setLastUpdatedBy(sessionDTO.getUserName());
						settlementBinDTO.setLastUpdatedOn(new Date());
						memberRepository.updateSettlementBin(settlementBinDTO);
					}
				} else {
					settlementBinDTO.setParticipantId(memberOnBoardingDTO.getParticipantId());
					settlementBinDTO.setSettlementBinId(String.valueOf(fetchBinIdSeq()));
					settlementBinDTO.setCreatedBy(sessionDTO.getUserName());
					settlementBinDTO.setCreatedOn(new Date());
					memberRepository.addSettlementBin(settlementBinDTO);
				}
			}
		}
	}

	private void updateAcquirerBinDataMain(MemberOnBoardingDTO memberOnBoardingDTO) {
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getAcqBinList())) {
			for (BinDetailsDTO binDetailsDTO : memberOnBoardingDTO.getAcqBinList()) {
				binDetailsDTO.setBinType(BaseCommonConstants.ACQUIRER_BIN_FLAG);
				binDetailsDTO.setAcqFrmDate(getDateFromInputDate(binDetailsDTO.getAcqFrmDateStr()));
				binDetailsDTO.setAcqToDate(getDateFromInputDate(binDetailsDTO.getAcqToDateStr()));
				binDetailsDTO.setParticipantId(memberOnBoardingDTO.getParticipantId());
				memberRepository.addAcqBinMain(binDetailsDTO);
			}
		}
	}

	private void updateAcquirerBinData(MemberOnBoardingDTO memberOnBoardingDTO) {
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getAcqBinList())) {
			List<String> acquireIdList = memberOnBoardingDTO.getAcqBinList().stream().map(BinDetailsDTO::getAcquirerId)
					.toList();
			List<BinDetailsDTO> existingAcquirerIdListForOtherParticipants = memberRepository
					.getAcquirerBinsForOtherParticipants(memberOnBoardingDTO.getParticipantId(), acquireIdList);
			if (!CollectionUtils.isEmpty(existingAcquirerIdListForOtherParticipants)) {
				List<String> duplicateBins = existingAcquirerIdListForOtherParticipants.stream()
						.map(BinDetailsDTO::getAcquirerId).toList();
				String errorMessage = ErrorConstants.ACQUIRER_ID_EXISTS_MESSAGE + ":  "
						+ StringUtils.join(duplicateBins, ",");
				throw new SettleNxtApplicationException(ErrorConstants.ACQUIRER_ID_EXISTS_CODE, errorMessage);
			}

			List<String> existingAcquirerIdList = memberRepository
					.getAcquirerBinsForParticipantsDuplicateCheck(memberOnBoardingDTO.getParticipantId()).stream()
					.map(BinDetailsDTO::getAcquirerId).toList();

			for (BinDetailsDTO binDetailsDTO : memberOnBoardingDTO.getAcqBinList()) {
				binDetailsDTO.setBinType(BaseCommonConstants.ACQUIRER_BIN_FLAG);
				binDetailsDTO.setAcqFrmDate(getDateFromInputDate(binDetailsDTO.getAcqFrmDateStr()));
				binDetailsDTO.setAcqToDate(getDateFromInputDate(binDetailsDTO.getAcqToDateStr()));
				addOrUpdateAcqBin(memberOnBoardingDTO, existingAcquirerIdList, binDetailsDTO);
			}
		}
	}

	private void addOrUpdateAcqBin(MemberOnBoardingDTO memberOnBoardingDTO, List<String> existingAcquirerIdList,
			BinDetailsDTO binDetailsDTO) {
		if (existingAcquirerIdList.contains(binDetailsDTO.getAcquirerId())) {
			if (binDetailsDTO.getActionType().equalsIgnoreCase("Edit")) {
				binDetailsDTO.setLastUpdatedBy(sessionDTO.getUserName());
				binDetailsDTO.setLastUpdatedOn(new Date());
				binDetailsDTO.setParticipantId(memberOnBoardingDTO.getParentParticipantId());
				if (binDetailsDTO.getStatus().equalsIgnoreCase(CommonConstants.DELETED_FLAG)) {
					binDetailsDTO.setCurrentDeleted(CommonConstants.YES_FLAG);
				}

				memberRepository.updateAcqBin(binDetailsDTO);
			}
		} else {
			binDetailsDTO.setParticipantId(memberOnBoardingDTO.getParticipantId());
			binDetailsDTO.setCreatedBy(sessionDTO.getUserName());
			binDetailsDTO.setCreatedOn(new Date());
			memberRepository.addAcqBin(binDetailsDTO);
		}
	}

	private void updateIssuerBinDataMain(MemberOnBoardingDTO memberOnBoardingDTO) {
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getIssBinList())) {
			for (BinDetailsDTO binDetailsDTO : memberOnBoardingDTO.getIssBinList()) {
				binDetailsDTO.setIssFrmDate(getDateFromInputDate(binDetailsDTO.getIssFrmDateStr()));
				binDetailsDTO.setIssToDate(getDateFromInputDate(binDetailsDTO.getIssToDateStr()));
				binDetailsDTO.setParticipantId(memberOnBoardingDTO.getParticipantId());

				memberRepository.addIssBinMain(binDetailsDTO);
			}
			List<BinDetailsDTO> activeBins=memberOnBoardingDTO.getIssBinList().stream()
					.filter(membin -> StringUtils.equals(membin.getStatus(), "A")).toList();
			List<BinDetailsDTO> deletedBins=memberOnBoardingDTO.getIssBinList().stream()
					.filter(membin -> StringUtils.equals(membin.getStatus(), "D")).toList();
			
			// Updating the active bin numbers as A-Allocated in bin_master
			if (CollectionUtils.isNotEmpty(activeBins) && !isInternational) {
			memberRepository.updateUnallocatedBin(activeBins,					
					"A", sessionDTO.getUserName());
			}
			// Updating the deleted bin numbers as U-UnAllocated in bin_master
			if (CollectionUtils.isNotEmpty(deletedBins) && !isInternational) {
			memberRepository.updateUnallocatedBin(deletedBins,
					"U", sessionDTO.getUserName());
			}
		}
	}

	
	private void updateIssuerBinData(MemberOnBoardingDTO memberOnBoardingDTO) {
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getIssBinList())) {
			List<String> issuerBinList = memberOnBoardingDTO.getIssBinList().stream().map(BinDetailsDTO::getBinNumber)
					.toList();
			List<BinDetailsDTO> existingIssuerIdListForOtherParticipants = memberRepository
					.getIssuerBinsForOtherParticipants(memberOnBoardingDTO.getParticipantId(), issuerBinList);
			if (!CollectionUtils.isEmpty(existingIssuerIdListForOtherParticipants)) {
				List<String> duplicateBins = existingIssuerIdListForOtherParticipants.stream()
						.map(BinDetailsDTO::getBinNumber).toList();
				String errorMessage = ErrorConstants.ISSUER_BIN_EXISTS_MESSAGE + ":  "
						+ StringUtils.join(duplicateBins, ",");
				throw new SettleNxtApplicationException(ErrorConstants.ISSUER_BIN_EXISTS_CODE, errorMessage);
			}
			List<String> existingIssuerBinList = memberRepository
					.getIssuerBinsForParticipantsDuplicateCheck(memberOnBoardingDTO.getParticipantId()).stream()
					.map(BinDetailsDTO::getBinNumber).toList();
			List<BinDetailsDTO> newIssuerBins = new ArrayList<>();
			for (BinDetailsDTO binDetailsDTO : memberOnBoardingDTO.getIssBinList()) {
				binDetailsDTO.setIssFrmDate(getDateFromInputDate(binDetailsDTO.getIssFrmDateStr()));
				binDetailsDTO.setIssToDate(getDateFromInputDate(binDetailsDTO.getIssToDateStr()));
				addOrUpdateMember(memberOnBoardingDTO, existingIssuerBinList, newIssuerBins, binDetailsDTO,
						memberOnBoardingDTO.getParticipantId());
			}
			if (!CollectionUtils.isEmpty(newIssuerBins) && !isInternational) {
				memberRepository.updateUnallocatedBin(newIssuerBins, "P", sessionDTO.getUserName());
			}
		}
	}

	private void addOrUpdateMember(MemberOnBoardingDTO memberOnBoardingDTO, List<String> existingIssuerBinList,
			List<BinDetailsDTO> newIssuerBins, BinDetailsDTO binDetailsDTO, String participantId) {
		if (existingIssuerBinList.contains(binDetailsDTO.getBinNumber())) {

			if ("Y".equalsIgnoreCase(env.getProperty(IS_INTERNATIONAL_PARTICIPANT))) {
				addDataForInternationalParticiapant(binDetailsDTO, participantId);

			}

			if (binDetailsDTO.getActionType().equalsIgnoreCase("Edit")) {
				binDetailsDTO.setLastUpdatedBy(sessionDTO.getUserName());
				binDetailsDTO.setLastUpdatedOn(new Date());
				binDetailsDTO.setParticipantId(memberOnBoardingDTO.getParentParticipantId());
				if (binDetailsDTO.getStatus().equalsIgnoreCase(CommonConstants.DELETED_FLAG)) {
					binDetailsDTO.setCurrentDeleted(CommonConstants.YES_FLAG);
				}
				binDetailsDTO.setInternational(isInternational);
				memberRepository.updateIssMemberBin(binDetailsDTO);
			}

		} else {
			prepareIssuerBinForAddAndEditAction(memberOnBoardingDTO, newIssuerBins, binDetailsDTO, participantId);
		}
	}

	private void addDataForInternationalParticiapant(BinDetailsDTO binDetailsDTO, String participantId) {
		List<String> dbBin = memberRepository.getNetworkSelectionListAll(binDetailsDTO.getBinNumber(),
				participantId);
		List<String> newNetworkAdded = new ArrayList<>();
		List<String> deletedNetwork = new ArrayList<>();
		List<String> oldChangedNetwork = new ArrayList<>();
		for (String networkValue : binDetailsDTO.getNetworkSelection()) {
			if (!dbBin.contains(networkValue)) {
				// if record coming from ui not in db, then new entry added
				newNetworkAdded.add(networkValue);
			}
			else {
				oldChangedNetwork.add(networkValue);
			}
			
		}
		if (!newNetworkAdded.isEmpty())
			memberRepository.addBinNetworkMappingDetails(newNetworkAdded, binDetailsDTO,
					participantId, "P");
		
		for (String networkValue : dbBin) {
			if (!binDetailsDTO.getNetworkSelection().contains(networkValue)) {
				// if record coming from ui not in db, then delete entry D
				deletedNetwork.add(networkValue);
			}


		}
		if (!deletedNetwork.isEmpty())
			for (String network : deletedNetwork)
				memberRepository.updateBinNetworkMappingDetails(network, binDetailsDTO,
						participantId, "D");

		if (!oldChangedNetwork.isEmpty())
			for (String network : oldChangedNetwork)
				memberRepository.updateBinNetworkMappingDetails(network, binDetailsDTO,
						participantId, "P");
	}
	
	
	
	private void prepareIssuerBinForAddAndEditAction(MemberOnBoardingDTO memberOnBoardingDTO,
		List<BinDetailsDTO> newIssuerBins, BinDetailsDTO binDetailsDTO, String participantId) {
			binDetailsDTO.setParticipantId(memberOnBoardingDTO.getParticipantId());
			binDetailsDTO.setCreatedBy(sessionDTO.getUserName());
			binDetailsDTO.setCreatedOn(new Date());
			binDetailsDTO.setInternational(isInternational);
			memberRepository.addIssBin(binDetailsDTO);
			if ("Y".equalsIgnoreCase(env.getProperty(IS_INTERNATIONAL_PARTICIPANT))) {
				memberRepository.addBinNetworkMappingDetails(binDetailsDTO.getNetworkSelection(), binDetailsDTO,
						participantId, "P");

				}
			newIssuerBins.add(binDetailsDTO);
	}
	@Override
	public int updateMemberContactInfo(MemberOnBoardingDTO memberOnBoardingDTO) {
		memberOnBoardingDTO.setLastUpdatedBy(sessionDTO.getUserName());
		memberOnBoardingDTO.setLastUpdatedOn(new Date());

		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntChkrName())) {
			memberOnBoardingDTO.setCntChkrName(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntChkrName()));
		}
		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntMobile())) {
			memberOnBoardingDTO.setCntMobile(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntMobile()));
		}
		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntDesignation())) {
			memberOnBoardingDTO.setCntDesignation(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntDesignation()));
		}
		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntEmail())) {
			memberOnBoardingDTO.setCntEmail(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntEmail()));
		}
		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntAdd1())) {
			memberOnBoardingDTO.setCntAdd1(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntAdd1()));
		}
		if (!StringUtils.isBlank(memberOnBoardingDTO.getCntPhone())) {
            memberOnBoardingDTO.setCntPhone(dataSecurityUtil.encrypt(memberOnBoardingDTO.getCntPhone()));
        }
		return memberRepository.updateMemberContactInfo(memberOnBoardingDTO);

	}

	private int updateMemberContactInfoMain(MemberOnBoardingDTO memberOnBoardingDTO) {
		return memberRepository.updateMemberContactInfoMain(memberOnBoardingDTO);

	}

	@Override
	public List<MemberDTO> getMembersSponsoBanks() {
		return memberRepository.getMembersSB();
	}

	@Override
	public int fetchParticipantIdSeq(String ifscCode) {
		return memberRepository.fetchParticipantIdSeq(ifscCode);
	}

	@Override
	public int fetchMemberIdSeq() {
		return memberRepository.fetchMemberIdSeq();

	}

	@Override
	public int fetchBinIdSeq() {
		return memberRepository.fetchBinIdSeq();

	}

	@Override
	public boolean checkIfParticipantExists(String participantId) {

		return memberRepository.checkIfParticipantExists(participantId) > 0;

	}

	@Override
	public boolean checkIfParticipantStgExists(String participantId) {
		return memberRepository.checkIfParticipantStgExists(participantId) > 0;
	}

	@Override
	public int discardMemberData(MemberOnBoardingDTO memberOnBoardingDTO) {
		unallocateIssuerBinOnDeletionOfParticipant(memberOnBoardingDTO);
		deleteMemberBinStgAll(memberOnBoardingDTO);
		deleteSettlementBinStgAll(memberOnBoardingDTO);
		// Unallocating the Issuer bin if participant data is deleted on discard
		memberRepository.deleteFileList(memberOnBoardingDTO.getParticipantId());
		memberRepository.deleteMemberContact(memberOnBoardingDTO.getParticipantId());          
		if (isInternational) {
			
			memberRepository.deletePreviousBinByParticipantId(memberOnBoardingDTO.getParticipantId());
		}

		return memberRepository.discardMemberData(memberOnBoardingDTO);
	}

	private void unallocateIssuerBinOnDeletionOfParticipant(MemberOnBoardingDTO memberOnBoardingDTO) {
		List<BinDetailsDTO> issuerBinData = getIssuerAndTokenBinDetails(memberOnBoardingDTO.getParticipantId(),NO);
		if (!CollectionUtils.isEmpty(issuerBinData) && !isInternational) {
			memberRepository.updateUnallocatedBin(issuerBinData, "U", sessionDTO.getUserName());
		}
	}

	@Override
	public MemberOnBoardingDTO getMemberFinalDiscard(String participantId) {
		return memberRepository.getMemberFinalDiscard(participantId);
	}

	@Override
	public boolean deleteMemberBinStgAll(MemberOnBoardingDTO memberOnBoardingDTO) {
		return memberRepository.deleteMemberBinStgAll(memberOnBoardingDTO) > 0;
	}

	@Override
	public boolean deleteSettlementBinStgAll(MemberOnBoardingDTO memberOnBoardingDTO) {
		return memberRepository.deleteSettlementBinStgAll(memberOnBoardingDTO) > 0;
	}

	@Override
	public Integer addBinListStg(List<BinDetailsDTO> binList) {
		return memberRepository.addBinListStg(binList);
	}

	@Override
	public Integer addAcqBinListStg(List<BinDetailsDTO> binList) {
		return memberRepository.addAcqBinListStg(binList);
	}

	@Override
	public Integer addIssBinListStg(List<BinDetailsDTO> binList) {
		return memberRepository.addIssBinListStg(binList, isInternational);
	}

	@Override
	public Integer addSettlementBinListStg(List<SettlementBinDTO> binList) {
		return memberRepository.addSettlementBinListStg(binList);
	}

	@Override
	public List<BinDetailsDTO> getBinDetailsFinal(String participantId, String binType) {
		return memberRepository.getBinDetailsFinal(participantId, binType, isInternational);
	}

	@Override
	public List<SettlementBinDTO> getsettlementBinsListAll(String participantId) {
		return memberRepository.getsettlementBinsListAll(participantId);

	}

	@Override
	public List<MemberDTO> getSavedMemberList(SearchCriteriaDTO searchCriteriaDTO) {
		String sSearch = null;
		if (searchCriteriaDTO.getSearchName() != null && !searchCriteriaDTO.getSearchName().equalsIgnoreCase("") ) {
			
				sSearch = "%" + searchCriteriaDTO.getSearchName().trim() + "%";

			
		}

		List<MemberDTO> membersData = memberRepository.getSavedMemberList(searchCriteriaDTO.getStartVal(),
				searchCriteriaDTO.getEndVal(), sSearch);

		List<MemberDTO> membersDataList = new ArrayList<>();
		for (MemberDTO memberDto : membersData) {
			membersDataList.add(decryptParticipantSensitiveData(memberDto));

		}
		return membersDataList;
	}

	@Override
	public Long getRowCount() {
		return memberRepository.getTotalMemberCount();
	}

	@Override
	public Long getPendingRowCount() {
		return memberRepository.getPendingMemberCount();
	}

	@Override
	public Long getSavedRowCount() {
		return memberRepository.getSavedMemberCount();
	}

	@Override
	public List<FileUploadDTO> getParticipantDocuments(String participantId) {
		return memberRepository.getFileList(participantId);

	}

	public String toUIDateFormat(Date date) {
		SimpleDateFormat fd = new SimpleDateFormat("dd-MM-yyyy");

		return fd.format(date);
	}

	private List<SettlementBinDTO> getSettlementBins(String participantId) {
		List<SettlementBinDTO> settlementBins = memberRepository.getSettlementBins(participantId);
		if (!CollectionUtils.isEmpty(settlementBins)) {
			settlementBins.forEach(settlementBin -> settlementBin.setSettlementCurrencyDescription(lookupDTOCache
					.getDescription(BaseCommonConstants.CURRENCY_CODE, settlementBin.getSettlementCurrency())));
		}
		return settlementBins;

	}

	private List<SettlementBinDTO> getSettlementBinsMain(String participantId) {
		List<SettlementBinDTO> settlementBins = memberRepository.getSettlementBinsMain(participantId, isInternational);
		if (!CollectionUtils.isEmpty(settlementBins)) {
			settlementBins.forEach(settlementBin -> settlementBin.setSettlementCurrencyDescription(lookupDTOCache
					.getDescription(BaseCommonConstants.CURRENCY_CODE, settlementBin.getSettlementCurrency())));
		}
		return settlementBins;

	}

	@Override
	public MemberOnBoardingDTO getMember(String participantId) {

		MemberOnBoardingDTO memberOnBoardingDTO = decryptParticipantSensitiveData(
				memberRepository.getMemberEdit(participantId, isInternational));
		List<FileUploadDTO> documents = memberRepository.getFileList(participantId);
		List<SettlementBinDTO> settlementBinsList = getSettlementBins(participantId);
		List<BinDetailsDTO> acqBinList = getBinDetails(participantId, "A",NO);
		List<BinDetailsDTO> issBinList = getIssuerAndTokenBinDetails(participantId,NO);

		processFetchMemberData(memberOnBoardingDTO, documents, settlementBinsList, acqBinList, issBinList);

		return memberOnBoardingDTO;
	}

	@Override
	public MemberOnBoardingDTO getMemberForPendingForApproval(String participantId) {

		MemberOnBoardingDTO memberOnBoardingDTO = decryptParticipantSensitiveData(
				memberRepository.getMemberEdit(participantId, isInternational));
		List<FileUploadDTO> documents = memberRepository.getFileList(participantId);
		List<SettlementBinDTO> settlementBinsList = getSettlementBins(participantId);
		List<BinDetailsDTO> acqBinList = getBinDetailsForPendingForApproval(participantId, "A");
		List<BinDetailsDTO> issBinList = getIssuerAndTokenBinDetailsForPendingForApproval(participantId);

		processFetchMemberData(memberOnBoardingDTO, documents, settlementBinsList, acqBinList, issBinList);

		return memberOnBoardingDTO;
	}

	@Override
	public MemberOnBoardingDTO getMemberData(String participantId) {
		String flag =NO;
		MemberOnBoardingDTO memberOnBoardingDTO = memberRepository.getMemberEdit(participantId, isInternational);
		List<FileUploadDTO> documents = memberRepository.getFileList(participantId);
		List<SettlementBinDTO> settlementBinsList = getSettlementBins(participantId);
		List<BinDetailsDTO> acqBinList = getBinDetails(participantId, "A", flag);
		List<BinDetailsDTO> issBinList = getIssuerAndTokenBinDetails(participantId, flag);

		processFetchMemberData(memberOnBoardingDTO, documents, settlementBinsList, acqBinList, issBinList);

		return memberOnBoardingDTO;
	}
	
	@Override
	public MemberOnBoardingDTO getMemberDataForDeletedBin(String participantId) {
 String flag =YES;
	MemberOnBoardingDTO memberOnBoardingDTO = memberRepository.getMemberEdit(participantId, isInternational);
		List<FileUploadDTO> documents = memberRepository.getFileList(participantId);
		List<SettlementBinDTO> settlementBinsList = getSettlementBins(participantId);
		List<BinDetailsDTO> acqBinList = getBinDetails(participantId, "A", flag);
		List<BinDetailsDTO> issBinList = getIssuerAndTokenBinDetails(participantId, flag);

		processFetchMemberData(memberOnBoardingDTO, documents, settlementBinsList, acqBinList, issBinList);

		return memberOnBoardingDTO;
	}

	protected void processFetchMemberData(MemberOnBoardingDTO memberOnBoardingDTO, List<FileUploadDTO> documents,
			List<SettlementBinDTO> settlementBinsList, List<BinDetailsDTO> acqBinList, List<BinDetailsDTO> issBinList) {
		memberOnBoardingDTO.setMemberTypeName(
				lookupDTOCache.getDescription(BaseCommonConstants.MEMBERTYPE, memberOnBoardingDTO.getMemberType()));
		memberOnBoardingDTO.setBankSectorName(
				lookupDTOCache.getDescription(BaseCommonConstants.BANK_SECTOR_LIST, memberOnBoardingDTO.getBankSector()));
		memberOnBoardingDTO.setAddressTypeName(
				lookupDTOCache.getDescription(BaseCommonConstants.ADDRESS_TYPE, memberOnBoardingDTO.getAddressType()));
		memberOnBoardingDTO.setSettlementBinList(settlementBinsList);

		memberOnBoardingDTO.setAcqBinList(acqBinList);

		if (!CollectionUtils.isEmpty(issBinList)) {
			issBinList.forEach(issBin -> issBin.setDomainUsage(
					lookupDTOCache.getDescription(BaseCommonConstants.DOMAIN_USAGE, issBin.getIssDomainUsage())));
			issBinList.forEach(issBin -> issBin.setProductTypeName(
					lookupDTOCache.getDescription(CommonConstants.CARD_PROD_TYPE, issBin.getIssProductType())));
			issBinList.forEach(issBin -> issBin.setSubSchemeName(
					lookupDTOCache.getDescription(BaseCommonConstants.SUB_SCHEME, issBin.getSubScheme())));
		}
		memberOnBoardingDTO.setIssBinList(issBinList);

		if (!CollectionUtils.isEmpty(documents)) {
			documents = documents.stream().filter(document -> !StringUtils.equals(document.getStatus(), "D"))
					.toList();
		}

		memberOnBoardingDTO.setDocuments(documents);
	}

	@Override
	public MemberOnBoardingDTO getMemberMain(String participantId) {
		MemberOnBoardingDTO memberOnBoardingDTO = memberRepository.getMemberMainView(participantId, isInternational);
		memberOnBoardingDTO = decryptParticipantSensitiveData(memberOnBoardingDTO);
		List<FileUploadDTO> documents = memberRepository.getFileListMain(participantId);
		List<SettlementBinDTO> settlementBinsList = getSettlementBinsMain(participantId);
		List<BinDetailsDTO> acqBinList = getAcqBinDetailsMain(participantId, "A");
		List<BinDetailsDTO> issBinList = getIssuerAndTokenBinDetailsMain(participantId);

		processFetchMemberData(memberOnBoardingDTO, documents, settlementBinsList, acqBinList, issBinList);

		return memberOnBoardingDTO;
	}

	@Override
	public Map<String, String> getFeatureMap() {
		List<FeatureFeeDTO> featureList = memberRepository.getFeatureListForAddandEditMember();
		Map<String, String> featureMap = new HashMap<>();

		for (FeatureFeeDTO featureFeeDTO : featureList) {
			if (!StringUtils.isEmpty(featureFeeDTO.getFeature())) {
				StringBuilder builder = new StringBuilder();
				builder.append(featureFeeDTO.getCardType());
				builder.append("#");
				builder.append(featureFeeDTO.getCardVariant());
				String key = builder.toString();

				if (featureMap.containsKey(key)) {
					featureMap.put(key, featureMap.get(key) + "," + featureFeeDTO.getFeature());
				} else {
					featureMap.put(key, featureFeeDTO.getFeature());
				}
			}
		}
		return featureMap;
	}

	@Override
	public List<Map<String, String>> prepareLookUpMap() {
		List<String> lookUpList = new ArrayList<>();
		lookUpList.add(BaseCommonConstants.BIN_CARD_TYPE);
		lookUpList.add(BaseCommonConstants.BIN_PRODUCT_TYPE);
		lookUpList.add(BaseCommonConstants.BIN_CARD_VARIANT);
		lookUpList.add(BaseCommonConstants.BIN_CARD_BRAND);
		lookUpList.add(BaseCommonConstants.DOMAIN_USAGE);
		lookUpList.add(BaseCommonConstants.CARD_TECHNOLOGY);
		lookUpList.add(BaseCommonConstants.PRODUCT_TYPE);
		lookUpList.add(BaseCommonConstants.MESSAGE_TYPE);
		lookUpList.add(BaseCommonConstants.AUTH_MECHANISM);

		List<LookUpDTO> list = masterSvc.getLookUpDataList(lookUpList);
		List<Map<String, String>> lookUpMapArr = new ArrayList<>();
		Map<String, List<LookUpDTO>> issMap = list.stream().collect(Collectors.groupingBy(LookUpDTO::getLkpType));

		for (String lookUpType : lookUpList) {
			Map<String, String> lookUpMap ;
			lookUpMap = issMap.get(lookUpType).stream()
					.collect(Collectors.toMap(LookUpDTO::getLkpValue, LookUpDTO::getLkpDesc));
			lookUpMapArr.add(lookUpMap);

		}
		return lookUpMapArr;
	}

	@Override
	public boolean checkIfParticipantContactExists(String participantId) {

		return memberRepository.checkIfParticipantContactExists(participantId) > 0;
	}

	private boolean checkIfParticipantContactExistsMain(String participantId) {
		return memberRepository.checkIfParticipantContactExistsMain(participantId) > 0;
	}

	@Override
	public String approveMemberBulk(String bulkIdList, MemberDTO memberDto) {
		String[] referenceNoArr = bulkIdList.split("\\|");
		for (String refNum:referenceNoArr) {
			if (!StringUtils.isEmpty(refNum)) {
				MemberDTO individualMember = new MemberDTO();
				individualMember.setApprovalStatus(memberDto.getApprovalStatus());
				individualMember.setRejectReason(memberDto.getApprovalRemarks());
				individualMember.setParticipantId(refNum);
				approveMember(individualMember);
			}
		}
		return CommonConstants.YES_FLAG;
	}

	private Date getDateFromInputDate(String inputeDate) {
		SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy");
		Date date = null;
		try {
			date = sdf.parse(inputeDate);
		} catch (ParseException e) {
			log.info("Unable to parse date {} ", e.getMessage(), e);

		}
		return date;
	}

	@Override
	public void discardMemberInfo(String participantId) {
		MemberOnBoardingDTO memberOnBoardingDTO = null;
		memberOnBoardingDTO = getMemberMain(participantId);
		if (isInternational) {
			discardDataForInternationalParticipant(participantId, memberOnBoardingDTO);
	}
		deleteMemberBinStgAll(memberOnBoardingDTO);
		deleteSettlementBinStgAll(memberOnBoardingDTO);
		memberRepository.deleteFileList(participantId);
		updateMemberContactInfo(memberOnBoardingDTO);
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getSettlementBinList())) {
			addSettlementBinListStg(memberOnBoardingDTO.getSettlementBinList());
		}
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getAcqBinList())) {
			addAcqBinListStg(memberOnBoardingDTO.getAcqBinList());
		}
		if (!CollectionUtils.isEmpty(memberOnBoardingDTO.getIssBinList())) {
			addIssBinListStg(memberOnBoardingDTO.getIssBinList());
		}
		for (FileUploadDTO fileUpload : memberOnBoardingDTO.getDocuments()) {
			memberRepository.addFileDetails(fileUpload);
		}
		memberOnBoardingDTO.setRecordStatus("A");
		memberOnBoardingDTO.setRequestState("SUBMIT");
		
		memberRepository.updateMemberInfo(encryptParticipantSensitiveData(memberOnBoardingDTO));
		memberRepository.clearCurrentDeleteFlag(participantId);

	}

	private void discardDataForInternationalParticipant(String participantId, MemberOnBoardingDTO memberOnBoardingDTO) {
		List<BinDetailsDTO> networkSelectionList= memberOnBoardingDTO.getIssBinList();
		if(networkSelectionList!=null) {
		for(BinDetailsDTO issVal:networkSelectionList) {
			List<BinDetailsDTO> dbBin = memberRepository.getNetworkSelectionListMainAll(issVal.getBinNumber(),
					participantId);
			List<BinDetailsDTO> dbBinStg = memberRepository.getNetworkSelectionListStg(issVal.getBinNumber(),
					participantId);
			Set<Integer> hashSetSeq = dbBinStg.stream().map(BinDetailsDTO::getSequenceId)
					.collect(Collectors.toSet());

			for (BinDetailsDTO network : dbBin) {
				hashSetSeq.remove(network.getSequenceId());
				memberRepository.updateBinNetworkMappingDetails(network.getMappingNetwork(), issVal, participantId,
						network.getStatus());
			}

			for (int seqId : hashSetSeq) {
				memberRepository.deleteStgDetailsBySeqId(seqId);
			}
		}
}
	}

	@Override
	public List<BinDetailsDTO> getUnallocatedIssBinData() {
		return memberRepository.getUnallocatedIssuerAndTokenBinDetails();
	}

	@Override
	public int duplicateSettlementBinCheck(String participantId, String settlementBinNumber) {
		return memberRepository.duplicateSettlementBinCheckSave(participantId, settlementBinNumber);
	}

	@Override
	public List<CodeValueDTO> getNetworkSelection() {
		return memberRepository.getNetworkSelection();
	}

	@Override
	public List<CodeValueDTO> getForexId() {
		return memberRepository.getForexId();
	}

	




}
