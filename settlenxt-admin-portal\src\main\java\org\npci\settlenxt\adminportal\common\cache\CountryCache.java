package org.npci.settlenxt.adminportal.common.cache;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.repository.CountryCacheRepository;
import org.npci.settlenxt.common.cache.BaseReloadableCache;
import org.npci.settlenxt.common.cache.CacheReloaderConstants;
import org.npci.settlenxt.portal.common.dto.CountryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CountryCache extends BaseReloadableCache<CountryDTO>{
	
	private static final Logger logger = LogManager.getLogger(CountryCache.class);
	private Map<String,CountryDTO> countryToMdlMap = new HashMap<>();
	
	@Autowired
	private CacheReloader cacheReloader;
	
	@Autowired
	private CountryCacheRepository countryCacheRepository;
	
	@PostConstruct
	void init() {
		init(cacheEntries);
		cacheReloader.registerCacheInst(CacheReloaderConstants.COUNTRY, this);		
	}
	
	@Override
	protected void init(Map<String, CountryDTO> newCacheEntries) {
		List<CountryDTO> countryList = countryCacheRepository.getAll();
		if (countryList == null || countryList.isEmpty()) {
			logger.error("Please configure country table....");
			return;
		}
		Map<String,CountryDTO> newCountryToMdlMap = new HashMap<>();
		for (CountryDTO countryModel : countryList) {
			if (countryModel != null) {
				newCacheEntries.put(countryModel.getCountryIso(), countryModel);
				newCountryToMdlMap.put(countryModel.getCountryAlpha(), countryModel);
				logger.info("added countryIso:- {}, countryAlpha:- {}", countryModel.getCountryAlpha(),
						countryModel.getCountryIso());
			} else {
				logger.error("countryAlpha or countryISO is null");
			}
		}
		Map<String,CountryDTO> oldCountryToMdlMap = countryToMdlMap;
		countryToMdlMap = newCountryToMdlMap;
		try {
			Thread.sleep(1000);
			oldCountryToMdlMap.clear();
		} catch (InterruptedException e) {
			logger.error(e.getMessage(),e);
			Thread.currentThread().interrupt();
		}
	}
	
	public String getCountryCodeByAlpha(String countryAlpha) {
		return countryToMdlMap.containsKey(countryAlpha) ? countryToMdlMap.get(countryAlpha).getCountryCode() : "";
		
	}
	
	public String getCountryCodeByISO(String countryISO) {
		return cacheEntries.containsKey(countryISO) ? cacheEntries.get(countryISO).getCountryCode() : "";
	}
}
