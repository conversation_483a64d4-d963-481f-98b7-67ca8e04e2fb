package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.FunctionCode;
import org.npci.settlenxt.portal.common.dto.ReasonCodeDto;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.stereotype.Service;

@Service
public interface ReasonCodeMasterService {

	List<ReasonCodeDto> getReasonCode();

	void addReasonCodeMaster(ReasonCodeDto reasonCodeDto);

	ReasonCodeDto getReasonCodeDetail(String reasonCode);

	ReasonCodeDto updateReasonCode(ReasonCodeDto reasonCodeDto);

	List<ReasonCodeDto> getPendingReasonCode();

	ReasonCodeDto updateApproveOrRejectReasonCode(String reasonCode, String status, String remarks);

	ReasonCodeDto discardReasonCode(String reasonCode);

	List<FunctionCode> getFunctionCodeList();

	List<ReasonCodeDto> getReasonCodeMasterList();

	List<ReasonCodeDto> showReasonCodeRules();

	List<ReasonCodeDto> showPendingReasonCodeRules();

	List<CodeValueDTO> fetchActionCodeList();

	List<CodeValueDTO> fetchReasonCodeList();

	void saveReasonCodeRules(ReasonCodeDto reasonCodeDto);

	 ReasonCodeDto updateReasonCodeRules(ReasonCodeDto reasonCodeDto);

	 ReasonCodeDto fetchReasonCodeRulesMain(String seqId);

	 ReasonCodeDto fetchReasonCodeRulesStg(String seqId);

	 ReasonCodeDto discardReasonCodeRules(String seqId);

	 ReasonCodeDto approveReasonCodeRules(ReasonCodeDto reasonCodeDto, String status, String remarks);

	 boolean checkDupReasonCodeRules(String actionCode, String reasonCode, String fieldName,
			String relationOperator);

	 ReasonCodeDto updateApproveOrRejectBulkRcRules(String idList, String status, String remarks)
			throws SettleNxtException;

	 String updateBulkStgReasonCodeMaster(String reasonCodeList, String status);

	String saveReasonCodeRulesList(List<ReasonCodeDto> reasonCodeList);

	String updateReasonCodeRulesList(List<ReasonCodeDto> reasonCodeDto);

	ReasonCodeDto fetchReasonCodeRulesStgBySeqId(String seqId);

	List<ReasonCodeDto> getReasonCodeList();

	List<ReasonCodeDto> getReasonCodewithReasonType();
}
