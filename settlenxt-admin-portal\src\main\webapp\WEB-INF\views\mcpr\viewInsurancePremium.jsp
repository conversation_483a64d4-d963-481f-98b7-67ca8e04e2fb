<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript">
var rebateValidationMessages = {};
rebateValidationMessages['vcardType'] = "<spring:message code="insurancePremium.cardType.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vcardVariant']= "<spring:message code="insurancePremium.cardVariant.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vannualPremiumAmtPerCard']= "<spring:message code="insurancePremium.insurenceAmtPerCardPerAnnum.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vvendor']= "<spring:message code="insurancePremium.vendor.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vfromMonth']= "<spring:message code="insurancePremium.fromMonth.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vfromYear']= "<spring:message code="insurancePremium.fromYear.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vtoMonth']= "<spring:message code="insurancePremium.toMonth.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vtoYear']= "<spring:message code="insurancePremium.toYear.validation.msg" javaScriptEscape='true' />";

</script>
<script type="text/javascript"
	src="./static/js/validation/mcpr/insurancePremiumToFuncRGCS.js"></script>
	
<div class="space_block">
	<div class="container-fluid height-min">

		<c:url value="approveUserStatus" var="approveUserStatus" />
		<form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewUser" modelAttribute="insurancePremiumInfoDto"
			action="${approveUserStatus}" autocomplete="off" >
		<div id="errvErrorInfo" class="error">
							<span for="ErrorInfo" class="error"><form:errors
									 /></span>
						</div>

			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="insurancePremium.insurancePremiumInformation" /></span></strong>
						</div>
						<div class="panel-body">
							<form:hidden path="insurancePremId" id="hinsurancePremId" />
							<form:hidden path="cardType" id="hcardType" />
							<form:hidden path="cardVariant" id="hcardVariant" />
							<form:hidden path="annualPremiumAmtPerCard" id="hannualPremiumAmtPerCard" />
							<form:hidden path="vendor" id="hvendor" />
							<form:hidden path="fromMonth" id="hfromMonth" />
							<form:hidden path="fromYear" id="hfromYear" />
							<form:hidden path="toYear" id="htoYear" />
							<form:hidden path="toMonth" id="htoMonth" />
							<form:hidden path="status" id="hstatus" />
							<input type="hidden" id="insurancePremiumNewRecord" value="${insurancePremiumNewRecord}"/>
							<input type="hidden" id="currDate" value="${insurancePremiumCurrentDate}"/>
							
							<c:if test="${insurancePremiumViewType ne 'afterSave' and insurancePremiumInfoDto.requestState ne 'P' and (viewTypeInsurancePremium ne 'V')}">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">Insurance Premium</caption>
							<thead style="display:none;"><th scope = "col"></th></thead>		
							<tbody>
								<tr>
									<td> 								
									<label><spring:message code="insurancePremium.cardType" /><span style="color: red">*</span></label></td>
									<td>		<form:select path="cardType" id="vcardType" name="vcardType"
											value="${insurancePremiumVO.cardType}"
											cssClass="form-control medantory">
											<form:option value="SELECT" label="SELECT" />
											<form:options items="${cardTypeList}"  itemValue="code" itemLabel="description"/>
											</form:select>
										<div id="errvcardType" class="error">
										<span for="vcardType" class="error"><form:errors
												id="vcardType" /></span>
										</div>

									</td>
									<td> 								
									<label><spring:message code="insurancePremium.cardVariant" /><span style="color: red">*</span></label></td>
									<td>	<form:select path="cardVariant" id="vcardVariant" name="vcardVariant"
										value="${insurancePremiumVO.cardVariant}" 
											cssClass="form-control medantory">
										<form:option value="SELECT" label="SELECT" />
											<form:options items="${cardVariantList}"  itemValue="code" itemLabel="description"/>
											</form:select>
											<div id="errvcardVariant" class="error">
											<span for="vcardVariant" class="error"><form:errors
												id="vcardVariant" /></span>
											</div>
									</td>
								</tr>
								<tr>
									<td> 								
									<label><spring:message code="insurancePremium.premiumPerCardPerAnnum" /><span style="color: red">*</span></label></td>
									<td>	<form:input path="annualPremiumAmtPerCard" id="vannualPremiumAmtPerCard" name="vannualPremiumAmtPerCard"
											maxlength="30" cssClass="form-control medantory" class="number-only" />
										<div id="errvannualPremiumAmtPerCard" class="error">
										<span for="vannualPremiumAmtPerCard" class="error"><form:errors
												id="vannualPremiumAmtPerCard" /></span>
									</div>
									</td>
									<td> 								
									<label><spring:message code="insurancePremium.vendor" /><span style="color: red">*</span></label></td>
									<td>	<form:select path="vendor" id="vvendor" name="vvendor"
										value="${insurancePremiumVO.vendor}" 
										cssClass="form-control medantory" onChange="disableToValue()">
										<form:option value="SELECT" label="SELECT" />
											<form:options items="${vendorList}"  itemValue="code" itemLabel="description"/>
											</form:select>
										<div id="errvvendor" class="error">
										<span for="vvendor" class="error"><form:errors
												id="vvendor" /></span>
									</div>
									</td>
								</tr>
								<tr>
									<td> 								
									<label><spring:message code="insurancePremium.fromMonthYear" /><span style="color: red">*</span></label></td>
									<td>	<form:select path="fromMonth" id="vfromMonth" name="vfromMonth"
										value="${insurancePremiumVO.fromMonth}" 
										cssClass="form-control medantory" onChange="disableToValue()">
										<form:option value="SELECT" label="SELECT" />
											<form:options items="${BeginingQtrMonths}"  itemValue="code" itemLabel="description"/>
											</form:select>
											<div id="errvfromMonth" class="error">
										<span for="vfromMonth" class="error"><form:errors
												id="vfromMonth" /></span>
									</div>
									</td>
									<td> 								
									<label><spring:message code="insurancePremium.Year" /><span style="color: red">*</span></label></td>
									<td>	<form:select path="fromYear" id="vfromYear" name="vfromYear"
										value="${insurancePremiumVO.fromYear}" 
										cssClass="form-control medantory" onChange="disableToValue()">
										<form:option value="SELECT" label="SELECT" />
											<form:options items="${BaseFeeYears}"  itemValue="code" itemLabel="description"/>
											</form:select>
											<div id="errvfromYear" class="error">
										<span for="vfromYear" class="error"><form:errors
												id="vfromYear" /></span></div>
									</td>
								</tr>
								<tr>
									<td> 								
									<label><spring:message code="insurancePremium.toMonthYear" /><span style="color: red">*</span></label></td>
									<td>	<form:select path="toMonth" id="vtoMonth" name="vtoMonth"
										value="${insurancePremiumVO.fromMonth}" 
										cssClass="form-control medantory" onChange="disableToValue()">
										<form:option value="SELECT" label="SELECT" />
											<form:options items="${EndingQtrMonths}"  itemValue="code" itemLabel="description"/>
											</form:select>
											<div id="errvtoMonth" class="error">
										<span for="vtoMonth" class="error"><form:errors
												id="vtoMonth" /></span>
									</div>
									</td>
									<td> 								
									<label><spring:message code="insurancePremium.Year" /><span style="color: red">*</span></label></td>
									<td>	<form:select path="toYear" id="vtoYear" name="vtoYear"
										value="${insurancePremiumVO.toYear}" 
										cssClass="form-control medantory" onChange="disableToValue()">
										<form:option value="SELECT" label="SELECT" />
											<form:options items="${BaseFeeYears}"  itemValue="code" itemLabel="description"/>
											</form:select>
											<div id="errvtoYear" class="error">
										<span for="vtoYear" class="error"><form:errors
												id="vtoYear" /></span></div>
									</td>
								</tr>
							</tbody>
							</table>
						</c:if>
						<c:if test="${insurancePremiumViewType eq 'afterSave' or insurancePremiumInfoDto.requestState eq 'P' or viewTypeInsurancePremium eq 'V'}">
						<table class="table table-striped" style="font-size: 12px">
						<caption style="display:none;">Insurance Premium</caption>
							<thead style="display:none;"><th scope = "col"></th></thead>
							<tbody>
								<tr>
									<td> 								
									<label><spring:message code="insurancePremium.cardType" /><span style="color: red"></span></label></td>
									<td>	${insurancePremiumInfoDto.cardTypeName }</td>
									<td> 								
									<label><spring:message code="insurancePremium.cardVariant" /><span style="color: red"></span></label></td>
									<td>	${insurancePremiumInfoDto.cardVariantName }</td>
								</tr>
								<tr>
									<td> 								
									<label><spring:message code="insurancePremium.premiumPerCardPerAnnum" /><span style="color: red"></span></label></td>
									<td>	${insurancePremiumInfoDto.annualPremiumAmtPerCard }</td>
									<td> 								
									<label><spring:message code="insurancePremium.vendor" /><span style="color: red"></span></label></td>
									<td>	${insurancePremiumInfoDto.vendorName }</td>
								</tr>
								<tr>
									<td> 								
									<label><spring:message code="insurancePremium.fromMonthYear" /><span style="color: red"></span></label></td>
									<td>	${insurancePremiumInfoDto.fromMonthName }</td>
									<td> 								
									<label><spring:message code="insurancePremium.Year" /><span style="color: red"></span></label></td>
									<td>	${insurancePremiumInfoDto.fromYear }</td>
								</tr>
								<tr>
									<td> 								
									<label><spring:message code="insurancePremium.toMonthYear" /><span style="color: red"></span></label></td>
									<td>	${insurancePremiumInfoDto.toMonthName }</td>
									<td> 								
									<label><spring:message code="insurancePremium.Year" /><span style="color: red"></span></label></td>
									<td>	${insurancePremiumInfoDto.toYear }</td>
								</tr>
							</tbody>
							</table>
						</c:if>
							
						</div>
					</div>
				</div>
			</div>				
		<div class="row">
			<div class="col-sm-12 bottom_space">
				<hr />
				<div style="text-align:center">

					<sec:authorize access="hasAuthority('Edit Insurance Premium')">
						<%-- <c:if test="${insurancePremiumInfoDto.requestState eq 'R' and viewTypeInsurancePremium ne 'V'}"> --%>
							<c:if test="${insurancePremiumInfoDto.requestState eq 'R' and viewTypeInsurancePremium eq 'V'}">
							<button type="button" id="discardInsurancePremium" class="btn btn-danger"
								onclick="postDiscardAction('/discardRejectedInsurancePremiumEntry','${originPage}');"><spring:message code="insurancePremium.discard" /></button>
						</c:if>
						
						<c:if test="${insurancePremiumInfoDto.requestState ne 'P'  and insurancePremiumViewType ne 'afterSave' and viewTypeInsurancePremium ne 'V'}">
						<c:if test="${insurancePremiumInfoDto.requestState eq 'A' or insurancePremiumInfoDto.requestState eq 'R'}">
							
							<button type="button" class="btn btn-success"  id="submitInsurancePremium"
								onclick="saveInsurancePremium('','','${originPage}')"><spring:message code="insurancePremium.submit" /></button>
						</c:if>
						</c:if>

					</sec:authorize>
					
					<c:if test="${viewTypeInsurancePremium eq 'V'}">
						<button type="button" class="btn btn-danger"
							onclick="homeInsurancePremium(	'V','/previewHoliday','${originPage}');"><spring:message code="insurancePremium.back" /></button>
					</c:if>
					<c:if test="${viewTypeInsurancePremium ne 'V'}">
						<button type="button" class="btn btn-danger"
							onclick="homeInsurancePremium(	'E','/previewHoliday','${originPage}');"><spring:message code="insurancePremium.back" /></button>
					</c:if>
						<c:if test="${insurancePremiumInfoDto.requestState ne 'P'  and insurancePremiumViewType ne 'afterSave' and viewTypeInsurancePremium eq 'V'}">
							<sec:authorize access="hasAuthority('Edit Insurance Premium')">
								<button type="button" class="btn btn-success"  id="bEdit"
									onclick="viewInsurancePremium('${insurancePremiumInfoDto.insurancePremId}','E','${originPage}')"><spring:message code="insurancePremium.edit" /></button>
							</sec:authorize>
						
						</c:if>


				</div>
			</div>
		</div>
		</form:form>

	</div>
</div>