package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.FunctionCode;
import org.npci.settlenxt.portal.common.dto.ReasonCodeDto;
import org.npci.settlenxt.portal.common.service.BaseReasonCodeMasterMapper;

@Mapper
public interface ReasonCodeMasterRepository extends BaseReasonCodeMasterMapper {

	List<ReasonCodeDto> getReasonCodeList();

	void addReasonCode(ReasonCodeDto reasonCodeDto);

	ReasonCodeDto getReasonCode(String reasonCode);

	void updateReasonCodeDetail(ReasonCodeDto reasonCodeDto);

	List<ReasonCodeDto> getPendingReasonCodeList();

	void saveReasonCode(ReasonCodeDto reasonCodeDto);

	void updateReasonCode(ReasonCodeDto reasonCodeDto);

	void updateReasonCodeStgState(ReasonCodeDto reasonCodeDto);

	ReasonCodeDto getApprovedReasonCode(String reasonCode);

	void deleteDiscardedReasonCode(String reasonCode);

	void updateRequestStateReasonCodeStg(ReasonCodeDto reasonCodeDtoMain);

	List<FunctionCode> getFunctionCodeList();

	List<ReasonCodeDto> getReasonCodeMasterList();

	List<ReasonCodeDto> showReasonCodeRulesList(@Param("requestState") String requestState);

	List<ReasonCodeDto> showPendingReasonCodeRules(@Param("requestStateList") List<String> requestStateList);

	 List<CodeValueDTO> fetchActionCodeList();

	 List<CodeValueDTO> fetchReasonCodeList();

	void saveReasonCodeRules(@Param("reasonCodeDto") ReasonCodeDto reasonCodeDto);

	void saveReasonCodeRulesStg(ReasonCodeDto reasonCodeDto);

	void updateReasonCodeRulesStg(ReasonCodeDto reasonCodeDto);

	 ReasonCodeDto fetchReasonCodeRulesMain(@Param("seqId") int seqId);

	 ReasonCodeDto fetchReasonCodeRulesStg(@Param("seqId") int seqId);

	void deleteDiscardedReasonCodeRules(@Param("seqId") int seqId);

	 void updateReasonCodeRulesState(ReasonCodeDto reasonCodeDto);

	void updateReasonCodeRulesMain(@Param("reasonCodeDto") ReasonCodeDto reasonCodeDto);

	 int fetchCountReasonCodeRulesStg(@Param("actionCode") String actionCode,
			@Param("reasonCode") String reasonCode, @Param("fieldName") String fieldName,
			@Param("relationOperator") String relationOperator);

	 int fetchCountReasonCodeRules(@Param("actionCode") String actionCode, @Param("reasonCode") String reasonCode,
			@Param("fieldName") String fieldName, @Param("relationOperator") String relationOperator);

	List<ReasonCodeDto> fetchPendingApprovalRcRules(@Param("idList") List<Integer> rcList);

	List<ReasonCodeDto> fetchPendingApprovalRcRulesList(@Param("idList") List<String> idList);

	void addReasonCodeRulesStg(ReasonCodeDto reasonCodeObj);

	ReasonCodeDto getReasonCodeRulesId(@Param("requestStateList") List<String> requestStateList,
			@Param("logToState") String logToState, @Param("actionCode") String actionCode,
			@Param("relationOperator") String relationOperator, @Param("fieldName") String fieldName);

	List<ReasonCodeDto> fetchReasonCodeRulesAppStg(@Param("actionCode") String actionCode,
			@Param("reasonCode") String reasonCode);

	ReasonCodeDto fetchReasonCodeRulesStgBySeqId(@Param("seqId") int seqId);

	List<ReasonCodeDto> getReasonCodeListstg();

	List<ReasonCodeDto> getReasonCodewithReasonType();
	
	List<ReasonCodeDto> getAll();
}
