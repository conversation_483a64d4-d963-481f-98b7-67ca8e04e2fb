
$(document).ready(
	function() {
		$("form :input").change(function() {
			$(this).closest('form').data('changed', true);
		});


		$("#errtipSurchargeType").hide();
		$("#erroperator").hide();
		$("#errsettlementAmount").hide();
		$("#erramountPercentFlag").hide();
		$("#errpercentage").hide();
		$("#erramount").hide();
		$("#errbinCardBrand").hide();
		$("#errbinCardType").hide();
		$("#errtipSurchargeName").hide();

		$("#labelpercentage").hide();
		$("#labelamount").hide();
		$("#percentage").hide();
		$("#amount").hide();

		showAllButton();
		loaddata();
		disableSave();

		$('#tipSurchargeType').on('keyup keypress blur change', function() {
			validateField('tipSurchargeType', true, "SelectionBox", 0, false, 1, 99999999999, true);
		});
		$('#operator').on('keyup keypress blur change', function() {
			validateField('operator', true, "SelectionBox", 0, false, 1, 99999999999, true);
		});
		$('#settlementAmount').on('keyup keypress blur change', function() {
			validateField('settlementAmount', true, "Decimal", 0, false, 0.0, 9999999999999999.99, true);
		});
		$('#binCardBrand').on('keyup keypress blur change', function() {
			validateField('binCardBrand', true, "SelectionBox", 0, false, 1, 99999999999, true);
		});
		$('#binCardType').on('keyup keypress blur change', function() {
			validateField('binCardType', true, "SelectionBox", 0, false, 1, 99999999999, true);
		});
		$('#amountPercentFlag').on('keyup keypress blur change', function() {
			validateField('amountPercentFlag', true, "SelectionBox", 0, false, 1, 99999999999, true);
		});
		$('#amount').on('keyup keypress blur change', function() {
			validateField('amount', true, "Decimal", 0, false, 0.0, 99999999.99, true);
		});
		$('#percentage').on('keyup keypress blur change', function() {
			validateField('percentage', true, "Decimal", 0, false, 0.0, 99.99, true);
		});
		$('#tipSurchargeName').on('keyup keypress blur change', function() {
			validateField('tipSurchargeName', true, "AlphabetWithSpace", 100, false, 0, 0, false);
		});


		$("#tipSurchargeType").on('keyup keypress blur change', function() {
			unableSave();
		});
		$("#operator").on('keyup keypress blur change', function() {
			unableSave();
		});
		$("#settlementAmount").on('keyup keypress blur change', function() {
			unableSave();
			validateField('settlementAmount', true, "Decimal", 0, false, 0.0, 9999999999999999.99, true);
		});
		$("#binCardBrand").on('keyup keypress blur change', function() {
			unableSave();
		});
		$("#binCardType").on('keyup keypress blur change', function() {
			unableSave();
		});
		$("#amountPercentFlag").on('keyup keypress blur change', function() {
			unableSave();
		});
		$("#amount").on('keyup keypress blur change', function() {
			unableSave();
			validateField('amount', true, "Decimal", 0, false, 0.0, 99999999.99, true);
		});
		$("#percentage").on('keyup keypress blur change', function() {
			unableSave();
			validateField('percentage', true, "Decimal", 0, false, 0.0, 99.99, true);
		});
		$("#tipSurchargeName").on('keyup keypress blur change', function() {
			unableSave();
			validateField('tipSurchargeName', true, "AlphabetWithSpace", 100, false, 0, 0, false);
		});

	});

function disableSave() {
	if (typeof bEdit != "undefined") {
		document.getElementById("bEdit").disabled = true;
	}
}

function unableSave() {
	if (typeof bEdit != "undefined") {
		document.getElementById("bEdit").disabled = false;
	}
}

window.history.forward();
function noBack() {
	window.history.forward();
}

function resetAction() {

	document.getElementById("addEditTipSurcharge").reset();
	$("#errtipSurchargeType").find('.error').html('');
	$("#erroperator").find('.error').html('');
	$("#errsettlementAmount").find('.error').html('');
	$("#erramountPercentFlag").find('.error').html('');
	$("#errpercentage").find('.error').html('');
	$("#erramount").find('.error').html('');
	$("#errbinCardBrand").find('.error').html('');
	$("#errbinCardType").find('.error').html('');
	$("#errtipSurchargeName").find('.error').html('');

}

function viewTipSurchargeAdd(_url, type) {
	$("#errtipSurchargeType").find('.error').html('');
	$("#erroperator").find('.error').html('');
	$("#errsettlementAmount").find('.error').html('');
	$("#erramountPercentFlag").find('.error').html('');
	$("#errpercentage").find('.error').html('');
	$("#erramount").find('.error').html('');
	$("#errbinCardBrand").find('.error').html('');
	$("#errbinCardType").find('.error').html('');
	$("#errtipSurchargeName").find('.error').html('');



	var isValid = true;
	var amountPercentFlag = $("#amountPercentFlag").val().trim();


	isValid = validateFields(isValid, amountPercentFlag);
	if (amountPercentFlag == 2&& !validateField('percentage', true, "Decimal", 0, false, 0.0, 99.99, true) && isValid) {
			isValid = false;
	}


	if (!validateField('tipSurchargeName', true, "AlphabetWithSpace", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}

	var percentageVariable = 0;

	var amountVariable = 0;

	if (amountPercentFlag == 1) {
		amountVariable = $("#amount").val();
	}
	else if (amountPercentFlag == 2) {
		percentageVariable = $("#percentage").val();
	}



	if (isValid) {
		checkDuplicateData(type, percentageVariable, amountVariable);

	}
}


function validateFields(isValid, amountPercentFlag) {
	if (!validateField('tipSurchargeType', true, "SelectionBox", 0, false, 1, 99999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('operator', true, "SelectionBox", 0, false, 1, 99999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('settlementAmount', true, "Decimal", 0, false, 0.0, 9999999999999999.99, true) && isValid) {
		isValid = false;
	}

	if (!validateField('binCardBrand', true, "SelectionBox", 0, false, 1, 99999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('binCardType', true, "SelectionBox", 0, false, 1, 99999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('amountPercentFlag', true, "SelectionBox", 0, false, 1, 99999999999, true) && isValid) {
		isValid = false;
	}
	if (amountPercentFlag == 1 && !validateField('amount', true, "Decimal", 0, false, 0.0, 99999999.99, true) && isValid) {
		isValid = false;
	}
	return isValid;
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber, isRange) {
	var fieldValue = $("#" + fieldId).val();
	var isValid = true;
	var isDataEmpty = false;
	if ((isMandatory && fieldValue.trim() == "" && (fieldType != "SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType == "SelectionBox")) {
		isValid = false;
		isDataEmpty = true;
	}
	isValid = checkNaN(fieldType, fieldValue, isValid);

	isValid = isAlphabetWithSpace(fieldType, fieldValue, isValid);



	isValid = isDecimal(fieldType, fieldValue, isValid);


	isValid = checkLength(isExactLength, fieldValue, length, isValid);
	isValid = checkRange(isRange, fieldValue, minNumber, maxNumber, isValid);


	if (isValid) {
		$("#err" + fieldId).hide();
	} else {
		if (fieldType != "SelectionBox") {
			if (isDataEmpty&&tipSurchargeValidationMessages[fieldId]) {
					$("#err" + fieldId).find('.error').html(tipSurchargeValidationMessages[fieldId]);
				
			}
			else if (tipSurchargeValidationMessages[fieldId + "1"]) {
					$("#err" + fieldId).find('.error').html(tipSurchargeValidationMessages[fieldId + "1"]);
				
			}

		}
		else if (tipSurchargeValidationMessages[fieldId]) {
				$("#err" + fieldId).find('.error').html(tipSurchargeValidationMessages[fieldId]);
			
		}

		$("#err" + fieldId).show();
	}
	return isValid;
}


function checkRange(isRange, fieldValue, minNumber, maxNumber, isValid) {
	if (isRange && !(Number(fieldValue) >= Number(minNumber) && Number(fieldValue) <= Number(maxNumber))) {
		isValid = false;
	}
	return isValid;
}

function checkLength(isExactLength, fieldValue, length, isValid) {
	if (isExactLength && fieldValue.length != length) {
		isValid = false;
	}
	return isValid;
}

function isDecimal(fieldType, fieldValue, isValid) {
	if (fieldType == "Decimal") {
		let regEx = /^\d*(\.\d{0,2})?$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

function isAlphabetWithSpace(fieldType, fieldValue, isValid) {
	if (fieldType == "AlphabetWithSpace") {
		let regEx = /^[A-Z ]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

function checkNaN(fieldType, fieldValue, isValid) {
	if (fieldType == "Number" && isNaN(fieldValue)) {
		isValid = false;

	}
	return isValid;
}

function userAction(_type, action) {
	var data = "status," + status;
	postData(action, data);
}

function discard(action, tipSurchargeId) {

	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "tipSurchargeId," + tipSurchargeId + ",_vTransactToken,"
		+ tokenValue;
	postData(action, data);
}



function postDiscardAction(action, _id) {
	var url = action;
	var tipSurchargeId = $("#tipSurchargeId").val();
	var data = "tipSurchargeId," + tipSurchargeId;
	postData(url, data);
}



function showAllButton() {
	var amountPercentFlag = $("#amountPercentFlag").val();

	if (amountPercentFlag == 1) {
		$("#amount").show();
		$("#labelamount").show();
		$("#labelpercentage").hide();
		$("#percentage").hide();
		$("#errpercentage").hide();
	}
	else if (amountPercentFlag == 2) {
		$("#percentage").show();
		$("#labelpercentage").show();
		$("#labelamount").hide();
		$("#amount").hide();
		$("#erramount").hide();

	}
	else {
		$("#percentage").hide();
		$("#amount").hide();
		$("#labelamount").hide();
		$("#labelpercentage").hide();
		$("#erramount").hide();
		$("#errpercentage").hide();
	}

}




function loaddata() {
	var hsettlementAmount = $("#hsettlementAmount").val();

	var hpercent = $("#hpercent").val();
	var hamount = $("#hamount").val();
	var htype = $("#htype").val();
	var htipSurchargeType = $("#tipSurchargeType").val();
	if (htype != "") {

		showControl(htype);
		var selectObj = document.getElementById("tipSurchargeType");
		for (let i of selectObj.options) {
			if (i.value == htipSurchargeType) {
				i.selected = true;
				break;
			}
		}
		selectObj = document.getElementById("operator");
		for (let i of selectObj.options) {
			if (i.value == hyear) {
				i.selected = true;
				break;
			}
		}
		selectObj = document.getElementById("amountPercentFlag");
		for (let i of selectObj.options) {
			if (i.value == hbankName) {
				i.selected = true;
				break;
			}
		}
		document.getElementById("settlementAmount").value = hsettlementAmount;
		document.getElementById("percent").value = hpercent;
		document.getElementById("amount").value = hamount;
		document.getElementById("tipSurchargeName").value = htipSurchargeName;

		$("#percentage").show();
		$("#amount").show();

	}
}

function checkDuplicateData(type, percentageVariable, amountVariable) {

	var data;
	var url;
	var tipSurchargeId = $("#tipSurchargeId").val();
	var tipSurchargeType = $("#tipSurchargeType").val();
	var operator = $("#operator").val();
	var amountPercentFlag = $("#amountPercentFlag").val();
	var settlementAmount = $("#settlementAmount").val();
	var tipSurchargeName = $("#tipSurchargeName").val();
	var binCardBrand = $("#binCardBrand").val();
	var binCardType = $("#binCardType").val();
	var parentPage = $("#hparentPage").val();

	if (type == 'E') {
		url = '/updateTipSurcharge';
		data = "tipSurchargeId," + tipSurchargeId + ",tipSurchargeType," + tipSurchargeType + ",operator," + operator + ",amountPercentFlag," + amountPercentFlag
			+ ",settlementAmount," + settlementAmount + ",percentage," + percentageVariable + ",amount," + amountVariable + ",tipSurchargeName," + tipSurchargeName + ",binCardBrand," + binCardBrand
			+ ",binCardType," + binCardType + ",parentPage," + parentPage;
	}
	else if (type == 'A') {
		url = '/addTipSurcharge';
		tipSurchargeId = "0";
		data = "tipSurchargeType," + tipSurchargeType + ",operator," + operator + ",amountPercentFlag," + amountPercentFlag
			+ ",settlementAmount," + settlementAmount + ",percentage," + percentageVariable + ",amount," + amountVariable + ",tipSurchargeName," + tipSurchargeName + ",binCardBrand," + binCardBrand
			+ ",binCardType," + binCardType + ",parentPage," + parentPage;
	}
	var msUrl = "validationCheckBinFeatureMapping";

	var tokenVal = document.getElementsByName("_TransactToken")[0].value;

	$.ajax({
		url: msUrl,
		type: "POST",
		dataType: "json",
		"beforeSend": function(xhr) { xhr.setRequestHeader('_TransactToken', tokenVal); },
		data: {
			"tipSurchargeId": tipSurchargeId,
			_TransactToken: tokenVal
		},
		success: function(response) {
			if (response.status == "BSUC_0001") {
				errvErrorInfo.className = 'error';
				errvErrorInfo.innerHTML = "";


				postData(url, data);
			}
			else {
				errvErrorInfo.className = 'error';
				errvErrorInfo.innerHTML = "Tip Surcharge Name Already Exists";
			}
		},
		error: function(_request, _status, _error) {
			errvErrorInfo.className = 'error';
			errvErrorInfo.innerHTML = "";
		}
	});

}
