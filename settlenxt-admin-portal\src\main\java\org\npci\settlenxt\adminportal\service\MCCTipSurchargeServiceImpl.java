package org.npci.settlenxt.adminportal.service;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.MCCTipSurchargeDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.MCCTipSurchargeRepository;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Throwable.class)
public class MCCTipSurchargeServiceImpl implements MCCTipSurchargeService {
	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	MCCTipSurchargeRepository mccTipSurchargeRepository;

	// show main tab
	public List<MCCTipSurchargeDTO> getTipSurchargeList() {
		return mccTipSurchargeRepository.getTipSurchargeListMain();
	}

	// show approval tab
	@Override
	@Transactional(readOnly = true)
	public List<MCCTipSurchargeDTO> getPendingTipSurcharge() {
		return mccTipSurchargeRepository.getTipSurchargePendingForApproval();
	}

	// view main tab info
	@Override
	@Transactional(readOnly = true)
	public MCCTipSurchargeDTO getMccTipSurchargeMainInfo(int mccTipSurchargeId) {
		MCCTipSurchargeDTO mccTipSurcharge = mccTipSurchargeRepository.getMccTipSurchargeProfileMain(mccTipSurchargeId);
		if (mccTipSurcharge == null) {
			throw new SettleNxtException("No Data Exist", "");
		}
		return mccTipSurcharge;
	}

	// view approval tab info
	@Override
	public MCCTipSurchargeDTO getMccTipSurchargeStgInfo(String mccTipSurchargeId) {
		MCCTipSurchargeDTO mccTipSurchargeStg = mccTipSurchargeRepository
				.getMccTipSurchargeStgInfoById(Integer.parseInt(mccTipSurchargeId));
		if (mccTipSurchargeStg == null) {
			throw new SettleNxtException("No Data Exist", "");
		}
		return mccTipSurchargeStg;
	}

	// add edit
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public MCCTipSurchargeDTO addEditMccTipSurcharge(MCCTipSurchargeDTO mccTipSurchargeDto) {
		int funcId = CommonConstants.TRANSACT_FUCTIONALITY_ADD_MCC_TIP_SURCHARGE_CONFIG;
		if (CommonConstants.EDIT_MCC_TIP_SURCHARGE.equalsIgnoreCase(mccTipSurchargeDto.getAddEditFlag())) {
			funcId = CommonConstants.TRANSACT_FUCTIONALITY_EDIT_MCC_TIP_SURCHARGE_CONFIG;
		}
		Date lt = new Date();
		mccTipSurchargeDto.setStatus("I");
		mccTipSurchargeDto.setRequestState("P");
		mccTipSurchargeDto.setCreatedOn(lt);
		mccTipSurchargeDto.setCreatedBy(sessionDTO.getUserName());
		mccTipSurchargeDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

		if (funcId == CommonConstants.TRANSACT_FUCTIONALITY_EDIT_MCC_TIP_SURCHARGE_CONFIG) {
			mccTipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
			mccTipSurchargeDto.setLastUpdatedOn(lt);
			mccTipSurchargeDto.setLastUpdatedBy(sessionDTO.getUserName());
			mccTipSurchargeRepository.updateMccTipSurcharge(mccTipSurchargeDto);
		}
		if (funcId == CommonConstants.TRANSACT_FUCTIONALITY_ADD_MCC_TIP_SURCHARGE_CONFIG) {
			int returnValue = checkDuplicateData(mccTipSurchargeDto);
			if (returnValue > 0) {
				throw new SettleNxtApplicationException("MCC Tip Surcharge Entry already exists", "Duplicate Record");
			}
			mccTipSurchargeDto.setMccTipSurchargeId(mccTipSurchargeRepository.fetchMccTipSurchargeIdSequence());
			mccTipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
			mccTipSurchargeRepository.insertMccTipSurchargeStg(mccTipSurchargeDto);
		}

		return mccTipSurchargeRepository.getMccTipSurchargeStg(mccTipSurchargeDto.getMccTipSurchargeId());
	}

	// edit
	@Override
	@Transactional(readOnly = true)
	public MCCTipSurchargeDTO getMccTipSurchargeForEdit(int mccTipSurchargeId) {
		return mccTipSurchargeRepository.getMccTipSurchargeStgInfoById(mccTipSurchargeId);
	}

	// for checker
	@Override
	public MCCTipSurchargeDTO approveOrRejectMccTipSurcharge(int mccTipSurchargeId, String status, String remarks) {
		MCCTipSurchargeDTO mccTipSurchargeDto = getMccTipSurchargeStg(mccTipSurchargeId);
		mccTipSurchargeDto.setRequestState(status);
		mccTipSurchargeDto.setCheckerComments(remarks);
		mccTipSurchargeDto.setStatus("A");
		
		Date lt = new Date();
		mccTipSurchargeDto.setLastUpdatedOn(lt);
		mccTipSurchargeDto.setLastUpdatedBy(sessionDTO.getUserName());
		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			mccTipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			MCCTipSurchargeDTO ifscdtoMain = mccTipSurchargeRepository.getMccTipSurchargeMain(mccTipSurchargeId);
			if (ifscdtoMain != null) {
				mccTipSurchargeRepository.updateMccTipSurchargeMain(mccTipSurchargeDto);
			} else {
				mccTipSurchargeRepository.insertMccTipSurchargeMain(mccTipSurchargeDto);
			}
		}
		if (CommonConstants.REQUEST_STATE_REJECTED.equals(status)) {
			mccTipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
			}
		mccTipSurchargeRepository.updateMccTipSurchargeRequestState(mccTipSurchargeDto);
		return mccTipSurchargeDto;
	}

	// for bulk checker
	@Override
	public String approveOrRejectMccTipSurchargeBulk(String bulkApprovalReferenceNoList, String status,
			String remarks) {
		String[] referenceNoArr = bulkApprovalReferenceNoList.split("\\|");
		int mccTipSurchargeId = 0;

		for (String refNum:referenceNoArr) {
			try {
				if (!StringUtils.isEmpty(refNum)) {
					mccTipSurchargeId = Integer.parseInt(refNum);
					MCCTipSurchargeDTO mccTipSurchargeDto = getMccTipSurchargeStg(mccTipSurchargeId);
					if (mccTipSurchargeDto == null) {

						throw new SettleNxtException("Exception occurred with Ref No" + refNum, "");
					}
					approveOrRejectBulk(status, remarks, mccTipSurchargeId, mccTipSurchargeDto);
				}
			} catch (Exception ex) {
				throw new SettleNxtException("", "Exception for Ref no" +refNum, ex);
			}
		}
		return CommonConstants.YES_FLAG;
	}

	private void approveOrRejectBulk(String status, String remarks, int mccTipSurchargeId,
			MCCTipSurchargeDTO mccTipSurchargeDto) {
		if (mccTipSurchargeDto != null) {
			mccTipSurchargeDto.setRequestState(status);
			mccTipSurchargeDto.setCheckerComments(remarks);
			if ("A".equals(status)) {
				mccTipSurchargeDto.setStatus("A");
			} else {
				mccTipSurchargeDto.setStatus("I");
			}
			
			Date lt = new Date();
			mccTipSurchargeDto.setLastUpdatedOn(lt);
			mccTipSurchargeDto.setLastUpdatedBy(sessionDTO.getUserName());
			if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
				mccTipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
				MCCTipSurchargeDTO mccTipSurchargeDtoMain = mccTipSurchargeRepository
						.getMccTipSurchargeMain(mccTipSurchargeId);
				if (mccTipSurchargeDtoMain != null) {
					mccTipSurchargeRepository.updateMccTipSurchargeMain(mccTipSurchargeDto);
				} else {
					mccTipSurchargeRepository.insertMccTipSurchargeMain(mccTipSurchargeDto);
				}
			}
			if (CommonConstants.REQUEST_STATE_REJECTED.equals(status)) {
				mccTipSurchargeDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
				}
			mccTipSurchargeRepository.updateMccTipSurchargeRequestState(mccTipSurchargeDto);
		}
	}

	// for discard
	@Override
	public MCCTipSurchargeDTO discardMccTipSurcharge(int mccTipSurchargeId) {
		MCCTipSurchargeDTO mccTipSurchargeDto = getMccTipSurchargeStg(mccTipSurchargeId);
		MCCTipSurchargeDTO mccTipSurchargeDtoMain = mccTipSurchargeRepository.getMccTipSurchargeMain(mccTipSurchargeId);
		if (mccTipSurchargeDtoMain != null) {
			mccTipSurchargeDtoMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			mccTipSurchargeDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			mccTipSurchargeRepository.updateMccTipSurchargeDiscard(mccTipSurchargeDtoMain);
		} else {
			mccTipSurchargeRepository.deleteDiscardedEntry(mccTipSurchargeDto);
		}
		return mccTipSurchargeDto;
	}

	@Override
	public MCCTipSurchargeDTO getMccTipSurchargeStg(int mccTipSurchargeId) {
		return mccTipSurchargeRepository.getMccTipSurchargeStg(mccTipSurchargeId);
	}

	@Override
	public int checkDuplicateData(MCCTipSurchargeDTO mccTipSurchargeDto) {

		return mccTipSurchargeRepository.validateDuplicateCheck(mccTipSurchargeDto);

	}
}
