$(document).ready(
		function() {

		var cursorPosition =null;
	/* Initialization of datatables */
	$(document).ready(function () {
    	
   var table =  $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                //If first column to be skipped to include the filter for the reasons line check box 
            dataTableFunc(colIdx,api);
   
                });
            $('#tabnew_filter').hide();
           
        },
        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'User',
                header: 'false',
                title: null,
                sheetName: 'User',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'User' ,
				header:'false', 
				title: null,
				sheetName:'User',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
		stateSave: true
    });
    
   
function dataTableFunc(colIdx,api)
	{
	             if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   cursorPosition = handleInput(colIdx, cell, title, api, cursorPosition);
                   }
	}
	
	  $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
	
	$('#fromDateStr').on('keyup keypress blur change',function () {
	
	validateFromDate('errFromDate');
	});
	
	$('#toDateStr').on('keyup keypress blur change',function () {
	
	validateToDate('errtoDate');
	});
		$("#fromDateStr").datepicker(
				{
					defaultDate : 0,
					dateFormat : "yy-mm-dd",
					changeMonth : true,
					changeYear : true,
					maxDate : 1,
					  onSelect: function () {
					  		 
					  		var y=""
					  		var someFormattedDate=""
				            var dt2 = $('#toDateStr');
				           var startDate = $(this).datepicker('getDate');
				      		    
				            startDate.setDate(startDate.getDate());
				            var minDate = $(this).datepicker('getDate');
				            
				            currentDate = new Date(Date.now()+(3600*24*1000));
				            dd = startDate.getDate();
							var mm = startDate.getMonth() + 1;
							 y = startDate.getFullYear();
							someFormattedDate = y + '-' + mm + '-'
									+ dd;
							$('#toDateStr').val(someFormattedDate);
							if (startDate > currentDate) {
								dd = currentDate.getDate();
								 mm = currentDate.getMonth() + 1;
								y = currentDate.getFullYear();
							someFormattedDate = y + '-' + mm
										+ '-' + dd;
								startDate.setDate(dd);
								$('#toDateStr').val(someFormattedDate);
							}
							
							
							
				            dt2.datepicker('option', 'maxDate', startDate);
				            dt2.datepicker('option', 'minDate', minDate);
				           
					  },
					  
	onClose :function() {
       validateFromDate('errFromDate');
         validateToDate('errtoDate');
     
       }
				});
		
		$("#selectAll").click(function() {
        let isChecked = $(this).prop('checked');
        table.rows().every(function() {
            let row = this.node();
            $(row).find('input[type="checkbox"]').prop('checked', isChecked);
        });
    });	
		
			var currentDate=""
			var dd=""
			var newDate=""	
		if ($('#fromDateStr').datepicker('getDate') != null) {
			newDate = $('#fromDateStr').datepicker('getDate');
			newDate.setDate(newDate.getDate());
			
			if (newDate == null) {
				newDate = 0;
			}
			currentDate = new Date(Date.now()+(3600*24*1000));
			if(newDate > currentDate){
				dd = currentDate.getDate();
				newDate.setDate(dd);
			}
		}
		$("#toDateStr").datepicker({
			defaultDate : 0,
			dateFormat : "yy-mm-dd",
			changeMonth : true,
			changeYear : true,
			minDate : $("#fromDateStr").datepicker('getDate'),
			maxDate : newDate,
         onClose :function() {
       validateToDate('errtoDate');
     
        validateFromDate('errFromDate');
       }
		});

	
	
					
			$("#searchBtn").click(
					function() {
								
					var check = handleCheckValue();						

	if(!check){
	var fromDateStr = $('#fromDateStr').val() ;
						var toDateStr = $('#toDateStr').val() ;
						var fileType = $('#fileType').val();
						var cycleNum = $('#cycleNum').val();
						var memberName = $('#memberName').val();
						
						var url = "/settleNxtfileSearchMembrStr";

						

						var data ="fromDateStr,"
								+ fromDateStr + ",toDateStr," + toDateStr + ",fileType,"
								+ fileType+",cycleNum,"
								+ cycleNum +",memberName,"
+ memberName ;
						postData(url, data);
	
	}else{
	return false;
	}
	

						
					});

			$(".bulkDtls1 clickFun").hover(function() {
				$('.bulkDtls1 clickFun').css('cursor', 'hand');

			});
			
			
	});

		});


function handleCheckValue() {
	var check = false;
	if (!validateFromDate('errFromDate')) {
		check = true;
	}
	if (!validateToDate('errtoDate')) {
		check = true;
	}
	if (!check) {
		if (!validateFromToDate('errFromDate')) {
			check = true;
		}
	}
	return check;
}

function handleInput(colIdx, cell, title, api, cursorPosition) {
	if (colIdx < actionColumnIndex) {

		$(cell).html(title + '<br><input class="search-box"   type="text" />');

		// On every keypress in this input
		$(
			'input',
			$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
		)
			.off('keyup change')
			.on('change', function () {
				// Get the search value
				$(this).attr('title', $(this).val());
				var regexr = '({search})';

				cursorPosition = this.selectionStart;
				// Search the column for that value
				api
					.column(colIdx)
					.search(
						this.value != ''
							? regexr.replace('{search}', '(((' + this.value + ')))')
							: '',
						this.value != '',
						this.value == ''
					)
					.draw();
			})
			.on('click', function (e) {
				e.stopPropagation();
			})
			.on('keyup', function (e) {
				e.stopPropagation();

				$(this).trigger('change');
				if (cursorPosition && cursorPosition != null) {
					$(this)
						.focus()[0]
						.setSelectionRange(cursorPosition, cursorPosition);
				}
			});
	} else {
		$(cell).html(title + '<br> &nbsp;');
	}
	return cursorPosition;
}

function resetAction() {
 	$('#errorStatus2').hide();
 	$('#memberName').val("0")	
 	$('#fromDateStr').val("");
 	$('#toDateStr').val("");
 	$('#fileType').val("0");
 	$('#cycleNum').val("0");
 	
 	 $('#errtoDate').hide();
 	  $('#errFromDate').hide();
 	  $('#afterReset').hide();
 	  $('#afterReset2').hide();
 	   $('#afterReset3').hide();
 	  	$('#errorStatus2').hide();
 	  	$('#snxtErrorMessage').hide();
 	
}
function getMonName(month) {
	switch (month) {
	case 1:
		return 'Jan';
	case 2:
		return 'Feb';
	case 3:
		return 'Mar';
	case 4:
		return 'Apr';
	case 5:
		return 'May';
	case 6:
		return 'Jun';
	case 7:
		return 'Jul';
	case 8:
		return 'Aug';
	case 9:
		return 'Sep';
	case 10:
		return 'Oct';
	case 11:
		return 'Nov';
	case 12:
		return 'Dec';
	}
	return 'Jan';

}
function getMon(month) {
	switch (month.toUpperCase()) {
	case 'JAN':
		return '0';
	case 'FEB':
		return '1';
	case 'MAR':
		return '2';
	case 'APR':
		return '3';
	case 'MAY':
		return '4';
	case 'JUN':
		return '5';
	case 'JUL':
		return '6';
	case 'AUG':
		return '7';
	case 'SEP':
		return '8';
	case 'OCT':
		return '9';
	case 'NOV':
		return '10';
	case 'DEC':
		return '11';
	}
	return '0';
}

function getRecordDetails(filePath) {
  	var tokenValue = document.getElementsByName("_TransactToken")[0].value;


     $.ajax({
    	 url : "settleNxtfileDownloadVerify",
        type: "POST",
		dataType : "json",
				data: {"filePath" : filePath,
					    "_TransactToken" : tokenValue},
       success: function(response) {
					var url="";
					var data="";
					if (response.status == "BSUC_0001") {
				url = "/settleNxtfileContentDownload";
				data = "filePath," + escape(filePath) ;
				
	postData(url, data);
			}else{
			url = "/settleNxtfileDownloadError";
			var fromDateStr = $('#fromDateStr').val() ;
						var toDateStr = $('#toDateStr').val() ;
						var fileType = $('#fileType').val();
						var cycleNum = $('#cycleNum').val();
												var memberName = $('#memberName').val();

				data =  "fromDateStr,"
								+ fromDateStr + ",toDateStr," + toDateStr + ",fileType,"
								+ fileType + ",cycleNum,"
								+ cycleNum  + ",memberName," + memberName;
	postData(url, data);
			}
}
})
}


function submitForm(url)
{
   
    var data = "";
    postData(url, data);
}

function validateFromDate(_msgID) {
var dateString = (document.getElementById("fromDateStr").value).replace('/^\s*|\s*$/g', '');
if (dateString == "") {
$('#errFromDate').show();
$("#errFromDate").find('.error').html("Please enter From Date");
					
					

return false;
} else{
$('#errFromDate').hide();
$("#errFromDate").find('.error').html(" ");
}
return true;
}
function validateToDate(_msgID) {
var dateString = (document.getElementById("toDateStr").value).replace('/^\s*|\s*$/g', '');
if (dateString == "") {

$('#errtoDate').show();
$("#errtoDate").find('.error').html("Please enter To Date");
return false;
} else{
$('#errtoDate').hide();
$("#errtoDate").find('.error').html(" ");
}
return true;
}
function validateFromToDate(_msgID) {
var fromDateStr = $('#fromDateStr').val() ;
var toDateStr = $('#toDateStr').val() ;
if(fromDateStr!="" && toDateStr!=""){
if (Date.parse(fromDateStr) > Date.parse(toDateStr)) {
$('#errFromDate').show();
$("#errFromDate").find('.error').html("From Date cannot be greater than To Date");

return false;
}
return true;
}
}

function allcheckedFileDownload(){
	
	 var url = '/SettlementReportContentDownload';
	
	 var array = [];

	$("input:checkbox[name=type]:checked").each(function(){
    array.push($(this).val());
});
	 
		var fileIds = "";
		for(let i of array) {
		fileIds = fileIds + i + "|";
		}
		
		var memberName = $('#memberName').val();
		var fileType = $('#fileType').val();
		var cycleNum = $('#cycleNum').val();
			var fromDateStr = $('#fromDateStr').val() ;
			
var data = "bulkdownloadIdList,"+fileIds+",memberName," + memberName + ",fileType,"
								+ fileType + ",cycleNum,"
								+ cycleNum + ",fromDateStr," + fromDateStr;
								
	if(array.length>0){
	$('#errorStatus2').hide();
	postData(url, data);}else{
	$('#errorStatus2').html("Please select one or more records to download");
$('#errorStatus2').show();
	}
	
	
	
}


function allFileDownload(){
	
	 var url = '/SettlementReportContentDownload';

		var fileIds = reportId[0];

		
	for ( let i = 1; i < reportId.length; i++) 
		{
		fileIds = fileIds+ "|" + reportId[i] ;
		}
		
	var memberName = $('#memberName').val();
	var fileType = $('#fileType').val();
	var cycleNum = $('#cycleNum').val();	
	var fromDateStr = $('#fromDateStr').val() ;
if(reportId.length>0)
	{
			
	var data = "bulkdownloadIdList,"+fileIds+",memberName," + memberName + ",fileType,"
								+ fileType + ",cycleNum,"
								+ cycleNum + ",fromDateStr," + fromDateStr;
	$('#errorStatus2').hide();
	
	postData(url, data);
	
	}
	else{
	$('#errorStatus2').html("Please select one or more records to download");
	$('#errorStatus2').show();
	}
	
}
function deselectAll() {

	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  

   
   let i=0;
   for(i of ele)
   {
   if(i.type=='checkbox')
   {
   i.checked=false;
   }
   }
   
}