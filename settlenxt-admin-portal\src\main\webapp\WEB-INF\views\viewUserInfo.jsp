<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>

<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ page import="java.time.format.DateTimeFormatter"%>
<script src="./static/js/validation/ViewUserInfo.js"
	type="text/javascript"></script>
	


<!-- Modal -->
<input:hidden id="refNum1" />
<div class="modal fade" id="toggleModalDeleteUser" tabindex="-1"
	role="dialog" aria-labelledby="toggleModalDeleteUser"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to delete this User ${userInfoDto.userName}?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">CANCEL</button>
				<button type="button" class="btn btn-primary"
					onclick="postUserAction('/deactivateUser');">YES</button>
			</div>
		</div>
	</div>
</div>
<!-- Modal Close-->

<div class="modal fade" id="toggleModalBlockUser" tabindex="-1"
	role="dialog" aria-labelledby="toggleModalBlockUser" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to block/unblock this User ${userInfoDto.userName}?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">CANCEL</button>
				<button type="button" class="btn btn-primary"
					onclick="postUserAction('/unlockUser');">YES</button>
			</div>
		</div>
	</div>
</div>
<!-- Modal Close-->

<div class="modal fade" id="toggleModalResetUser" tabindex="-1"
	role="dialog" aria-labelledby="toggleModalResetUser" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to reset password for this User ${userInfoDto.userName}?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">CANCEL</button>
				<button type="button" class="btn btn-primary"
					onclick="postUserAction('/resetPasswordsUser');">YES</button>
			</div>
		</div>
	</div>
</div>
<!-- Modal Close-->

<div class="space_block">
	<div class="container-fluid height-min">

		<c:url value="approveUserStatus" var="approveUserStatus" />
		<form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewUser" modelAttribute="userInfoDto"
			action="${approveUserStatus}" autocomplete="off">
			<input type="hidden" id="userType" value='${reqType}' />
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data">User Information </span></strong>
							<div class="icon_bar">
								<sec:authorize access="hasAuthority('Edit User') ">
									<c:if test="${empty Pending}">
										<a data-toggle="tooltip" title="Edit"
											onclick="userAction('N','/editUser');" href="#"><img
											src="./static/images/edit-grey.png" alt="edit"></a>
									</c:if>
								</sec:authorize>
								<sec:authorize access="hasAuthority('Deactivate User') ">
									<c:if test="${not empty deactivate}">
										<a data-toggle="modal" title="User Delete"
											data-target="#toggleModalDeleteUser"
											onclick="setRefnumUserId'${userInfoDto.userId}')" href="#"><img
											src="./static/images/user-deactivate-grey.png" alt="Delete"></a>
									</c:if>
								</sec:authorize>
								<sec:authorize access="hasAuthority('Lock User') ">
									<c:if test="${not empty lockuser}">
										<a data-toggle="modal" title="Block User"
											data-target="#toggleModalBlockUser"
											onclick="setRefnumUserId'${userInfoDto.userId}')" href="#"><img
											src="./static/images/usreblock-grey.png" alt="Block User"></a>
									</c:if>
								</sec:authorize>
								<sec:authorize access="hasAuthority('Unlock User') ">

									<c:if test="${not empty unlockuser}">
										<a data-toggle="modal" title="Unblock User"
											data-target="#toggleModalBlockUser"
											onclick="setRefnumUserId'${userInfoDto.userId}')" href="#"><img
											src="./static/images/usreblock-grey.png" alt="Unblock User"></a>
									</c:if>
								</sec:authorize>
								<sec:authorize access="hasAuthority('Reset Password') ">
									<c:if test="${empty Pending}">
										<c:if test="${not empty resetPassword}">
											<a data-toggle="modal" title="Reset Password"
												data-target="#toggleModalResetUser"
												onclick="setRefnumUserId'${userInfoDto.userId}')" href="#"><img
												src="./static/images/reset-password-gray.png"
												alt="Reset Password"></a>
										</c:if>
									</c:if>
								</sec:authorize>

							</div>
						</div>
						<div class="panel-body">
							<form:hidden path="userId" id="userId" />
							
							<table class="table table-striped" style="font-size: 12px">
								<caption style="display: none;">USER</caption>

								<thead style="display: none;">
									<th scope="col"></th>
									<th scope="col"></th>
								</thead>
								<tbody>




									<tr>

										<td><label><spring:message code="am.lbl.loginId" /></label></td>
										<td id="loginId">${userInfoDto.userName }</td>
										<td><label><spring:message code="sm.lbl.bankName" /></label></td>
										<td>${userInfoDto.bankName }</td>
										<c:if test="${not empty userInfoDto.accessLevel }">

											<td><label> <spring:message
														code="sm.lbl.accessLevel" /></label></td>
											<c:if test="${userInfoDto.accessLevel=='P' }">
												<td>Participant</td>
											</c:if>
											<c:if test="${userInfoDto.accessLevel=='B' }">
												<td>Settlement</td>
											</c:if>
										</c:if>
										<c:if test="${empty userInfoDto.accessLevel }">
											<td></td>

											<td></td>
										</c:if>
										<td></td>




									</tr>

									<tr>
								
										<c:if test="${userInfoDto.accessLevel=='B' }">
											<td><label><spring:message
														code="sm.lbl.binFlags" /></label></td>
											<td><select>
  												<c:forEach items="${userInfoDto.bins}" var="item">
    												<option value="${item}">${item}</option>
  												</c:forEach>
											</select>
											</td>
										</c:if>
									
									</tr>
									

									
									<tr>
										<td><label><spring:message code="am.lbl.empId" /></label></td>
										<td id="empId">${userInfoDto.empId}</td>
										<td><label><spring:message
													code="am.lbl.salutation" /></label></td>
										<td id="salutation">${userInfoDto.salutation }</td>
										<td><label><spring:message
													code="am.lbl.firstName" /></label></td>
										<td id="firstName">${userInfoDto.firstName }</td>

									</tr>

									<tr>
										<td><label><spring:message
													code="am.lbl.middleName" /></label></td>
										<td id="displayName">${userInfoDto.middleName }</td>
										<td><label><spring:message code="am.lbl.lastName" /></label></td>
										<td id="lastName">${userInfoDto.lastName }</td>



									</tr>
									<tr>
										<td><label><spring:message code="msg.lbl.emailId" /></label></td>
										<td id="emailId">${userInfoDto.emailId }</td>
										<td><label><spring:message code="am.lbl.mobileNo" /></label></td>
										<td id="mobileNo">${userInfoDto.mobileNo }</td>
										<td><label><spring:message
													code="msg.lbl.contactNo" /></label></td>
										<td id="contactNo">${userInfoDto.contactNo }</td>


									</tr>

									<%-- <tr>

										<td><label><spring:message code="sm.lbl.bankName" /></label></td>
										<td>${userInfoDto.bankName }</td>
										<c:if test="${not empty userInfoDto.accessLevel }">
										
										<td><label> <spring:message
													code="sm.lbl.accessLevel" /></label></td>
										<c:if test="${userInfoDto.accessLevel=='P' }">
											<td>Participant</td>
										</c:if>
										<c:if test="${userInfoDto.accessLevel=='B' }">
											<td>Settlement</td>
										</c:if>
										<c:if test="${userInfoDto.accessLevel=='B' }">
											<td><label><spring:message
														code="sm.lbl.binFlags" /></label></td>
											<td>${userInfoDto.bins }</td>
										</c:if>
</c:if>
<c:if test="${empty userInfoDto.accessLevel }">
<td></td>

										<td></td>
										</c:if>
										<td></td>

										<td></td>

										<td></td>


									</tr> --%>

									<tr>
										<td><label><spring:message
													code="cmn.lbl.streetAddress" /></label></td>
										<td id="streetAddress">${userInfoDto.streetAddress }</td>
										<td><label><spring:message
													code="cmn.lbl.cityName" /></label></td>
										<td id="city">${userInfoDto.cityName }</td>
										<td><label><spring:message code="msg.lbl.state" /></label></td>
										<td id="state">${userInfoDto.stateName }</td>


									</tr>


									<tr>
										<td><label><spring:message code="am.lbl.pinCode" /></label></td>
										<td id="pinNo">${userInfoDto.pincode}</td>
										<td><label><spring:message
													code="am.lbl.makChkFlag" /></label></td>
										<td id="makChk">${userInfoDto.makChkFlag}</td>


										<td></td>
										<td></td>

										<td></td>
										<td></td>
									</tr>

									<tr>


										<td><label><spring:message code="am.lbl.acStatus" /></label></td>
										<c:if test="${ not empty userInfoDto.status }">
											<c:choose>
												<c:when test="${userInfoDto.lockStatus eq 'L' }">
													<td style="color: red"><spring:message
															code="am.msg.locked" /></td>
												</c:when>
												<c:when test="${userInfoDto.status eq 'A'}">
													<td style="color: green"><spring:message
															code="common.msg.lbl.active" /></td>
												</c:when>
												<c:when test="${userInfoDto.status eq 'S'}">
													<td style="color: red">Suspended</td>
												</c:when>
												<c:when test="${userInfoDto.status eq 'Z'}">
													<td style="color: blue">Reset</td>
												</c:when>
												<c:when test="${userInfoDto.status eq 'R'}">
													<td style="color: blue">Reset</td>
												</c:when>
												<c:when test="${userInfoDto.status eq 'D'}">
													<td style="color: blue">Deleted</td>
												</c:when>
												<c:otherwise>
													<td>Null</td>
												</c:otherwise>
											</c:choose>
										</c:if>

										<td><label><spring:message
													code="am.lbl.lastUpdatedBy" /></label></td>
										<td>${userInfoDto.lastUpdatedBy}</td>
										<td><label><spring:message
													code="am.lbl.lastUpdatedOn" /></label></td>

										<td>${userInfoDto.lastUpdatedOn.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>
										<%-- 	<td>${userInfoDto.lastUpdatedOn}</td> --%>
									</tr>
								
								</tbody>
							</table>
							
							<input type="hidden" name="userType" id="userType"
								value="${userInfoDto.userType}"></input>
							<!-- User to role map -->
							<div class="panel-body">
							

								<%--div role="tabpanel" class="tab-pane active" id="home"--%>


								<form:form modelAttribute="userrole" method="POST"
									name="assignRoleToUser" action="${assignRoleToUser}"
									onsubmit="removeSpace(this);encodeForm(this);" id="userDTO"
									autocomplete="off">
									<div class="row">
										<form:input path="userId" id="userId" type="hidden"
											value="${userrole.userId}" />

										<input type="hidden" id="changeFlag" />
										<form:hidden path="loginId" id="loginId"
											value="${userrole.loginId}" />

									</div>
								</form:form>
							</div>
							<div role="tabpanel" class="tab-pane active" id="home">
								<div class="panel panel-default no_margin">
									<div class="panel-heading clearfix">
										<strong><span class="glyphicon glyphicon-th"></span>
											<span data-i18n="Data">User To Role Mapping</span></strong>
									</div>
								</div>
								<div class="panel-body">
									<div class="col-sm-3"></div>
									<div class="form-group clearfix" style="padding-top: 20px">
										<div class="col-xs-6">
											<div class="panel panel-default">
												<div class="panel-heading">
													<strong><span class="glyphicon glyphicon-th"></span>
														<span data-i18n="Data">Available Roles</span></strong>
												</div>

												<div class="table-responsive">
													<c:if test="${empty successList }">
														<table id="tabnew"
															class="table table-striped table-bordered"
															style="width: 100%;">
															<caption style="display: none;">USER</caption>
															<thead>
																<tr>
																	<th scope="col">Role Name</th>
																	<th scope="col">Add</th>
																</tr>
															</thead>
															<tbody id="optionList">
																<c:if test="${not empty roleOptionList}">
																	<c:forEach var="list" items="${roleOptionList}">
																		<tr class="selectedRoles" id="add${list.roleId}"
																			value="${list.roleId}">
																			<td>${list.roleName}</td>
																			<td><em
																				class="glyphicon glyphicon-circle-arrow-right"
																				onclick="addToAssignedList('${list.roleId}','${list.roleName}')">
																			</em></td>
																		</tr>
																	</c:forEach>
																</c:if>

															</tbody>
														</table>
													</c:if>
													<c:if test="${not empty successList}">
														<table id="tabnew"
															class="table table-striped table-bordered"
															style="width: 100%;">

															<caption style="display: none;">USER ROLE</caption>




															<thead>
																<tr>
																	<th scope="col">Role Name</th>
																</tr>
															</thead>
															<tbody id="optionList">
																<c:if test="${not empty roleOptionList}">

																	<c:forEach var="list" items="${roleOptionList}">
																		<tr class="selectedRoles" id="add${list.roleId}"
																			value="${list.roleId}">
																			<td>${list.roleName}</td>
																		</tr>
																	</c:forEach>

																</c:if>
															</tbody>
														</table>
													</c:if>


												</div>
											</div>
										</div>


										<div class="col-xs-6">

											<div class="panel panel-default">
												<div class="panel-heading">
													<strong><span class="glyphicon glyphicon-th"></span>
														<span data-i18n="Data">Selected Roles</span></strong>
												</div>

												<div class="table-responsive">
													<c:if test="${empty successList }">
														<table id="tabnew1"
															class="table table-striped table-bordered"
															style="width: 100%;">
															<caption style="display: none;">USER ROLE</caption>
															<thead>
																<tr>
																	<th scope="col">Role Name</th>
																	<th scope="col">Remove</th>
																</tr>
															</thead>
															<tbody id="assignedList">
																<c:if test="${not empty aRoleOptionList}">
																	<c:forEach var="list" items="${aRoleOptionList}">
																		<tr class="selectedRoles" id="remove${list.roleId}"
																			value="${list.roleName}">
																			<c:if test="${ list.status eq 'E'}">
																				<td style="color: blue">${list.roleName}</td>
																			</c:if>
																			<c:if test="${list.status ne 'E'}">
																				<td>${list.roleName}</td>
																			</c:if>
																			<c:if
																				test="${not empty successStatus or errorStatus eq  'User is already pending for approval.' }">
																				<td><em
																					class="glyphicon glyphicon-remove-circle"></em></td>
																			</c:if>
																			<sec:authorize access="hasAuthority('Edit User') ">
																				<c:if
																					test="${empty successStatus and  errorStatus ne  'User is already pending for approval.' }">
																					<td><em
																						class="glyphicon glyphicon-remove-circle"
																						onclick="removeTag('${list.roleId}','${list.roleName}')"></em>
																					</td>
																				</c:if>
																			</sec:authorize>
																		</tr>

																	</c:forEach>
																</c:if>
															</tbody>
														</table>
													</c:if>
													<c:if test="${not empty successList }">
														<table id="tabnew1"
															class="table table-striped table-bordered"
															style="width: 100%;">
															<caption style="display: none;">USER ROLE</caption>
															<thead>
																<tr>
																	<th scope="col">Role Name</th>
																</tr>
															</thead>
															<tbody id="assignedList">
																<c:if test="${not empty aRoleOptionList}">
																	<c:forEach var="list" items="${aRoleOptionList}">
																		<tr class="selectedRoles" id="remove${list.roleId}"
																			value="${list.roleName}">
																			<c:if test="${ list.status eq 'E'}">
																				<td style="color: blue">${list.roleName}</td>
																			</c:if>
																			<c:if test="${list.status ne 'E'}">
																				<td>${list.roleName}</td>
																			</c:if>

																		</tr>

																	</c:forEach>
																</c:if>
															</tbody>
														</table>



													</c:if>
												</div>
											</div>
										</div>

									</div>

								</div>
							</div>


						</div>
					</div>
		</form:form>
		<div class="row">
			<div class="col-sm-12 bottom_space ">
				<hr />
				<div style="text-align: center">


					<button type="button" class="btn btn-danger"
						onclick="userAction('2','/showUsers');">Back</button>


				</div>
			</div>
		</div>
	</div>

</div>
