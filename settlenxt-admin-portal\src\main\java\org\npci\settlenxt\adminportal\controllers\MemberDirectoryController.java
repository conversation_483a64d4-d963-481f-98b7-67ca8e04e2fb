package org.npci.settlenxt.adminportal.controllers;


import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.service.MasterService;
import org.npci.settlenxt.adminportal.service.ReportService;
import org.npci.settlenxt.adminportal.service.UserService;
import org.npci.settlenxt.portal.common.controllers.BaseMemberController;
import org.npci.settlenxt.portal.common.dto.BinDTO;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.MemberOnBoardingDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class MemberDirectoryController extends BaseMemberController {

	@Autowired
	MasterService masterService;

	@Autowired
	ReportService reportService;

	@Autowired
	UserService userService;

	private static final String SHOW_MEMBER_DIRECTORY = "showMemberDirectory";
	private static final String VIEW_MEM_DIRECTORY = "viewMemDirectory";
	private static final String TRUE="true";

	@PostMapping("/showMemberDirectory")
	@PreAuthorize("hasAuthority('View Member Directory')")
	public String showMemberDirectory(@RequestParam(value = "participantId", required = false) String participantId,
			@RequestParam(value = "setBin", required = false) String setBin,
			@RequestParam(value = "binNumber", required = false) String binNumber,
			@RequestParam(value = "isParticipant", required = false) String isParticipant,
			@RequestParam(value = "isBin", required = false) String isBin,
			@RequestParam(value = "isSetBin", required = false) String isSetBin, Model model) {

		MemberOnBoardingDTO memberOnBoardingDTO = new MemberOnBoardingDTO();
		MemberOnBoardingDTO member = new MemberOnBoardingDTO();

		if ((isParticipant != null || isSetBin != null || isBin != null) && (StringUtils.equalsIgnoreCase(isParticipant,TRUE) ||  StringUtils.equalsIgnoreCase(isSetBin,TRUE)
				|| TRUE.equalsIgnoreCase(isBin))) {
			
				setMemberOnboardingDto(participantId, setBin, binNumber, isParticipant, isBin, isSetBin, member);

				List<MemberDTO> memberList = reportService.getSearchMemberList(member);

				model.addAttribute(BaseCommonConstants.MEMBER_LIST, memberList);

				model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.YES_FLAG);

				if (StringUtils.equalsIgnoreCase(isParticipant,TRUE)) {
					List<BinDTO> settlementBinList = reportService.getSettlementBinList(participantId);
					model.addAttribute(BaseCommonConstants.SETTLEMENT_BIN_LIST, settlementBinList);
				}

				setMemberOnboardingDto(participantId, setBin, binNumber, isParticipant, isBin, isSetBin,
						memberOnBoardingDTO);

			
		}

		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.YES_FLAG);
		model.addAttribute(BaseCommonConstants.MEMBER_ONBOARD_DTO, memberOnBoardingDTO);
		model.addAttribute(BaseCommonConstants.PARTICIPANT_ID_LISTS, userService.getParticipantList());
		model.addAttribute(BaseCommonConstants.IS_PARTICIPANT, isParticipant);
		model.addAttribute(BaseCommonConstants.IS_SET_BIN, isSetBin);
		model.addAttribute(BaseCommonConstants.IS_BIN, isBin);
		return getView(model, SHOW_MEMBER_DIRECTORY);
	}

	private void setMemberOnboardingDto(String participantId, String setBin, String binNumber, String isParticipant,
			String isBin, String isSetBin, MemberOnBoardingDTO memberOnBoardingDTO) {
		if (StringUtils.equalsIgnoreCase(isParticipant, TRUE) ) {

			memberOnBoardingDTO.setParticipantId(participantId);
		}
		if (StringUtils.equalsIgnoreCase(isSetBin, TRUE)) {
			memberOnBoardingDTO.setSettlementBinNumber(setBin);
		}
		if (StringUtils.equalsIgnoreCase(isBin, TRUE)) {
			memberOnBoardingDTO.setBinNumber(binNumber);
		}
	}

	@PostMapping("/memberDirectoryFetchData")
	@PreAuthorize("hasAuthority('View Member Directory')")
	public String searchMemberDirectory(@RequestParam("settlementBinNumber") String settlementBinNumber,
			@RequestParam("participantId") String participantId, @RequestParam("binNumber") String binNumber,
			Model model) {

		searchMemberDirectoryCommon(settlementBinNumber, participantId, binNumber, model);
		model.addAttribute(CommonConstants.SEARCH, CommonConstants.YES_FLAG);
		
		return getView(model, SHOW_MEMBER_DIRECTORY);
	}

	@PostMapping("/getSettlementBinList")
	// @PreAuthorize("hasAuthority('View Member Directory')")
	public ResponseEntity<Object> getSettlementBinList(@RequestParam("participantId") String participantId) {
		List<BinDTO> settlementBinList;

		if (participantId.isEmpty()) {
			throw new SettleNxtException("Participant ID Empty", "");

		} else {
			settlementBinList = reportService.getSettlementBinList(participantId);
		}
		return new ResponseEntity<>(settlementBinList, HttpStatus.OK);
	}

	@PostMapping("/viewMember")
	@PreAuthorize("hasAuthority('View Member Directory')")
	public String viewMember(@RequestParam("participantId") String participantId,
			@RequestParam("binNumber") String binNumber,
			@RequestParam(value = "isParticipant", required = false) String isParticipant,
			@RequestParam(value = "isBin", required = false) String isBin,
			@RequestParam(value = "isSetBin", required = false) String isSetBin, Model model) {
		
		String portal=BaseCommonConstants.ADMIN_PORTAL_TYPE;
		
		viewMemberCommon(participantId, binNumber, isParticipant, isBin, isSetBin, model,portal);
		return getView(model, VIEW_MEM_DIRECTORY);
	}


}
