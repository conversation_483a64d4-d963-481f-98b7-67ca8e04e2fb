<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript">
var rebateValidationMessages = {};
rebateValidationMessages['vpenaltyForModifyUpload'] = "<spring:message code='penalty.modifyingData.validation.msg' javaScriptEscape='true' />";
rebateValidationMessages['vpenaltyForNonModifyUpload']= "<spring:message code="penalty.notUpdatingData.validation.msg" javaScriptEscape='true' />";
</script>
<script type="text/javascript"
	src="./static/js/validation/mcpr/penaltyToFuncRGCS.js"></script>
	<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${approvedlist eq 'Y' }">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" onclick="submitForm('/showPenaltyConfig');" role="tab"
			data-toggle="tab"> <span class="glyphicon glyphicon-credit-card">&nbsp;</span><spring:message code="penalty.tabname" />
		</a>

		<c:choose>
			<c:when test="${approvedlist eq 'N'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#profile" role="tab"  onclick="submitForm('/penaltyPendingForApproval');"
			data-toggle="tab"> <span class="glyphicon glyphicon-ok">&nbsp;</span><spring:message code="penalty.approval" />
		</a>


	</ul>
	<form:form id="penaltySearch" name="penaltySearch">
	</form:form>
	<c:if	test="${approvedlist eq 'Y'}">
										
		<div class="tab-content">
			<!-- tabpanel -->
			<div role="tabpanel" class="tab-pane active" id="home">
				<div class="row">
					<div class="space_block">
						<div class="container-fluid height-min">
							<c:url value="approveUserStatus" var="approveUserStatus" />
							<form:form onsubmit="removeSpace(this); encodeForm(this);"
								method="POST" id="viewPenalty" 
								action="${approveUserStatus}" autocomplete="off" >
							<div id="errvErrorInfo" class="error">
												<span for="ErrorInfo" class="error"><form:errors
														 /></span>
							</div>
							<div class="row">
								<div class="col-sm-12">
									<div class="panel panel-default no_margin">
										<div class="panel-heading clearfix">
											<strong><span class="glyphicon glyphicon-th"></span> <span
												data-i18n="Data"><spring:message code="penalty.penaltyInformation" /></span></strong>
										</div>
										<div class="panel-body">
											<input type="hidden" id="hpenaltyId" value="${penaltyData.penaltyId}"/>
											<table class="table table-striped" style="font-size: 12px">
											<caption style="display:none;">Show Penalty</caption>
												<tbody>
												<th scope="col"></th>
													<tr>
													<td></td>
													<td><label><spring:message code="penalty.amount" /><span style="color: red"></span></label></td>
													</tr>
													<tr>
													<td><label><spring:message code="penalty.modifyingData" /><span style="color: red"></span></label></td>
													<c:if test="${(pendingAppPenaltyData.requestState != 'P') && (pendingAppPenaltyData.requestState != 'R') }">
														<sec:authorize access="hasAuthority('Add MCPR Penalty')">
															<td><input id="vpenaltyForModifyUpload" value="${penaltyData.penaltyForModifyUpload}"/>
															<div id="errvpenaltyForModifyUpload" class="error">
															<span for="vpenaltyForModifyUpload" class="error"><form:errors id="vpenaltyForModifyUpload" /></span></div></td>
														</sec:authorize>
														<sec:authorize access="hasAuthority('Approve MCPR Penalty')">
															<td><label>${penaltyData.penaltyForModifyUpload}</label></td>
														</sec:authorize>
													</c:if>
													<c:if test="${(pendingAppPenaltyData.requestState == 'P') || (pendingAppPenaltyData.requestState == 'R') }">
														<td><label>${penaltyData.penaltyForModifyUpload}</label></td>
													</c:if>
													</tr>
													<tr>
													<td><label><spring:message code="penalty.notUpdatingData" /><span style="color: red"></span></label></td>
													<c:if test="${(pendingAppPenaltyData.requestState != 'P') && (pendingAppPenaltyData.requestState != 'R') }">
														<sec:authorize access="hasAuthority('Add MCPR Penalty')">
															<td><input id="vpenaltyForNonModifyUpload" value="${penaltyData.penaltyForNonModifyUpload}"/>
															<div id="errvpenaltyForNonModifyUpload" class="error">
															<span for="vpenaltyForNonModifyUpload" class="error"><form:errors id="vpenaltyForNonModifyUpload" /></span></div></td>
														</sec:authorize>
														<sec:authorize access="hasAuthority('Approve MCPR Penalty')">
															<td><label>${penaltyData.penaltyForNonModifyUpload}</label></td>
														</sec:authorize>
													</c:if>
													<c:if test="${(pendingAppPenaltyData.requestState == 'P') || (pendingAppPenaltyData.requestState == 'R') }">
														<td><label>${penaltyData.penaltyForNonModifyUpload}</label></td>
													</c:if>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>				

							<div class="row">
								<div class="col-sm-12 bottom_space">
									<hr />
									<div style="text-align:center">

										<sec:authorize access="hasAuthority('Add MCPR Penalty')">
											<c:if test="${(pendingAppPenaltyData.requestState != 'P') && (pendingAppPenaltyData.requestState != 'R') }">
												<button type="button" class="btn btn-success" id="bEdit"
													onclick="savePenalty('','V')"><spring:message code="penalty.submit" /></button>
											
											</c:if>


										</sec:authorize>
										<button type="button" class="btn btn-danger"
											onclick="homePenalty('N','/showPenaltyConfig');"><spring:message code="penalty.back" /></button>


									</div>
								</div>
							</div>
							</form:form>
						</div>
					</div>
				</div>	
			</div>
		</div>
	</c:if>
	<c:if	test="${approvedlist eq 'N'}">
		<c:if	test="${approveddata eq 'Y'}">
										
		<div class="tab-content">
			<!-- tabpanel -->
			<div role="tabpanel" class="tab-pane active" id="home">
				<div class="row">
					<div class="space_block">
						<div class="container-fluid height-min">
							<c:url value="approveUserStatus" var="approveUserStatus" />
							<form:form onsubmit="removeSpace(this); encodeForm(this);"
								method="POST" id="appPenalty" 
								action="${approveUserStatus}" autocomplete="off" >
							<div id="errvErrorInfo" class="error">
												<span for="ErrorInfo" class="error"><form:errors
														 /></span>
							</div>
							<div class="row">
								<div class="col-sm-12">
									<div class="panel panel-default no_margin">
										<div class="panel-heading clearfix">
											<strong><span class="glyphicon glyphicon-th"></span> <span
												data-i18n="Data"><spring:message code="penalty.penaltyInformation" /></span></strong>
										</div>
										
										<sec:authorize access="hasAuthority('Approve MCPR Penalty')">
											<div class="panel-body">
												<table class="table table-striped infobold" style="font-size: 12px">
												<caption style="display:none;">Show Penalty</caption>
												<tbody>
												<th scope="col"></th>
													<tr>
														<td colspan="6"><div class="panel-heading-red clearfix">
																<strong><span class="glyphicon glyphicon-info-sign"></span> <span
																	data-i18n="Data"><spring:message code="penalty.requestInformation" /></span></strong>
															</div></td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
													</tr>
													<tr>
														
														<td><label><spring:message code="penalty.requestType" /><span style="color: red"></span></label></td>
														<td>${pendingAppPenaltyData.lastOperation}</td>
														<td><label><spring:message code="penalty.requestDate" /><span style="color: red"></span></label></td>
														<td>${pendingAppPenaltyData.lastUpdatedOn}</td>
														<td><label><spring:message code="penalty.requestStatus" /><span style="color: red"></span></label></td>
														<%-- <td>${pendingAppPenaltyData.requestState}</td> --%>
														<td><c:if test="${pendingAppPenaltyData.requestState =='A' }"><spring:message	code="currencyMaster.requestState.approved.description" /></c:if>
															<c:if test="${pendingAppPenaltyData.requestState =='P' }"><spring:message code="currencyMaster.requestState.pendingApproval.description" /></c:if>
															<c:if test="${pendingAppPenaltyData.requestState =='R' }"><spring:message code="currencyMaster.requestState.rejected.description" /></c:if>
														</td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
														
													</tr>
													<tr>
														<td><label><spring:message code="penalty.requestBy" /><span style="color: red"></span></label></td>
														<td>${pendingAppPenaltyData.lastUpdatedBy}</td>
														
														<td><label><spring:message code="penalty.approverComments" /><span style="color: red"></span></label></td>
														<td colspan=2>${pendingAppPenaltyData.checkerComments}</td>									
														<td></td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
													</tr>
												</tbody>	
												</table>
											</div>
										</sec:authorize>
										
										<div class="panel-body">
											<input type="hidden" id="hpenaltyId" value="${pendingAppPenaltyData.penaltyId}"/>
											<table class="table table-striped" style="font-size: 12px">
											<caption style="display:none;">Show Penalty</caption>
												<tbody>
												<th scope="col"></th>
													<tr>
													<td></td>
													<td><label><spring:message code="penalty.amount" /><span style="color: red"></span></label></td>
													</tr>
													<tr>
													<td><label><spring:message code="penalty.modifyingData" /><span style="color: red"></span></label></td>
													<c:if test="${pendingAppPenaltyData.requestState == 'R' }">
														<sec:authorize access="hasAuthority('Add MCPR Penalty')">
															<td><input id="vpenaltyForModifyUpload" value="${pendingAppPenaltyData.penaltyForModifyUpload}"/>
															<div id="errvpenaltyForModifyUpload" class="error">
															<span for="vpenaltyForModifyUpload" class="error"><form:errors id="vpenaltyForModifyUpload" /></span></div></td>
														</sec:authorize>
														<sec:authorize access="hasAuthority('Approve MCPR TAT')">
															<td><label>${pendingAppPenaltyData.penaltyForModifyUpload}</label></td>
														</sec:authorize>
													</c:if>
													<c:if test="${(pendingAppPenaltyData.requestState == 'P') || (pendingAppPenaltyData.requestState == 'A') }">
													<td><label>${pendingAppPenaltyData.penaltyForModifyUpload}</label></td>
													</c:if>
													</tr>
													<tr>
													<td><label><spring:message code="penalty.notUpdatingData" /><span style="color: red"></span></label></td>
													<c:if test="${pendingAppPenaltyData.requestState == 'R' }">
														<sec:authorize access="hasAuthority('Add MCPR Penalty')">
															<td><input id="vpenaltyForNonModifyUpload" value="${pendingAppPenaltyData.penaltyForNonModifyUpload}"/>
															<div id="errvpenaltyForNonModifyUpload" class="error">
															<span for="vpenaltyForNonModifyUpload" class="error"><form:errors id="vpenaltyForNonModifyUpload" /></span></div></td>
														</sec:authorize>
														<sec:authorize access="hasAuthority('Approve MCPR TAT')">
															<td><label>${pendingAppPenaltyData.penaltyForNonModifyUpload}</label></td>
														</sec:authorize>
													</c:if>
													<c:if test="${(pendingAppPenaltyData.requestState == 'P') || (pendingAppPenaltyData.requestState == 'A') }">
													<td><label>${pendingAppPenaltyData.penaltyForNonModifyUpload}</label></td>
													</c:if>
													</tr>
												</tbody>
											</table>
										</div>
										<sec:authorize access="hasAuthority('Approve MCPR Penalty')">
											<c:if test="${pendingAppPenaltyData.requestState eq 'P'}">
												<div class="panel-body">
												<table class="table table-striped" style="font-size: 12px">
												<caption style="display:none;">Show Penalty</caption>
													<tbody>
													<th scope="col"></th>
													<tr>
														<td colspan="6"><div class="panel-heading-red  clearfix">
																<strong><span class="glyphicon glyphicon-info-sign"></span> <span
																	data-i18n="Data"><spring:message
																			code="AM.lbl.reqInfo" /></span></strong>
															</div></td>
													</tr>

													<tr>										<td><label><spring:message
																	code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
														<td><select name="select" id="apprej"
															onchange="display()">
																<option value="N"><spring:message
																		code="AM.lbl.select" /></option>
																<option value="A" id="approve"><spring:message
																		code="AM.lbl.approve" /></option>
																<option value="R" id="reject"><spring:message
																		code="AM.lbl.reject" /></option>
														</select></td>
														<td>
														<div style="text-align:center">
																<label><spring:message code="AM.lbl.remarks" /><span
																	style="color: red">*</span></label>
															</div></td>
														<!-- //Added by deepak on 31-03-2016 -->
														<td colspan="2"><textarea rows="4" cols="50"
																maxlength="100" id="rejectReason"></textarea>
															<div id="errorrejectReason" class="error"></div></td>
													</tr>
												</tbody>
												</table>					
											</c:if>
										</sec:authorize>
									</div>
								</div>
							</div>				

							<div class="row">
								<div class="col-sm-12 bottom_space">
									<hr />
									<div style="text-align:center">
										<sec:authorize access="hasAuthority('Approve MCPR Penalty')">
										<c:if test="${pendingAppPenaltyData.requestState eq 'P'}">
											<button type="button" class="btn btn-success" id="approveRole" 
												onclick="postAction('/approvePenaltyStatus');"><spring:message code="penalty.submit" /></button>
										</c:if>
										</sec:authorize>
										<sec:authorize access="hasAuthority('Add MCPR Penalty')">
										<c:if test="${pendingAppPenaltyData.requestState eq 'R'}">
											<button type="button" class="btn btn-success" id="bEdit" onclick="savePenalty('','A')"><spring:message code="penalty.submit" /></button>
											<button type="button" id="approveRole" class="btn btn-success"
											onclick="postDiscardAction('/discardRejectedPenaltyEntry');"><spring:message code="penalty.discard" /></button>
										</c:if>
										</sec:authorize>
										<button type="button" class="btn btn-danger"
											onclick="homePenalty('N','/showPenaltyConfig');"><spring:message code="penalty.back" /></button>
									</div>
								</div>
							</div>
							</form:form>
						</div>
					</div>
				</div>	
			</div>
		</div>
		</c:if>
		<c:if	test="${approveddata eq 'N'}">
		<div class="tab-content">
			<!-- tabpanel -->
			<div role="tabpanel" class="tab-pane active" id="home">
				<div class="row">
					<div class="space_block">
						<div class="container-fluid height-min">
							<div class="panel-heading clearfix">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message code="mcprTAT.noDataForApproval" /></span></strong>
							</div>
						</div>
					</div>
				</div>	
			</div>
		</div>
		</c:if>
	</c:if>
		
</div>
