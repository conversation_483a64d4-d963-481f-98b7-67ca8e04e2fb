<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />
<script type="text/javascript"
	src="./static/js/bootstrap-multiselect.js"></script>
<link rel="stylesheet" href="./static/css/bootstrap-multiselect.css"
	type="text/css" />
<style>
.form-control {
	background-color: white !important;
}
</style>
<script type="text/javascript">
 var validationMessages = {};      
validationMessages['product'] = "Product is Necessary";
validationMessages['holidayDesc'] = "Description is necessary";
validationMessages['periodType'] = "Period Type is necessary";
validationMessages['fromDateStr'] = "From Date is necessary";
validationMessages['toDateStr'] = "To Date is necessary";

validationMessages['dayOfWeek'] = "Day of Week is necessary";
validationMessages['weekType'] = "Week Type is necessary";
editFlowValue=false;
<c:if test="${editFlow eq 'Y'}">
editFlowValue=true;
</c:if>
</script>

<script type="text/javascript"
	src="./static/js/validation/commonValidation.js"></script>




<script type="text/javascript"
	src="./static/js/validation/addEditHolidayMaster.js"></script>
	
<div class="modal fade" id="toggleModalDiscardRole" tabindex="-1" role="dialog" aria-labelledby="toggleModalDiscardRole" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Are you sure you want to discard?</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">NO</button>
        <button type="button" class="btn btn-primary" onclick="userAction('/discardHoliday');" >YES</button>
      </div>
    </div>
  </div>
</div>
<!-- Modal Close-->		

<c:if test="${editFlow eq 'Y' or addFlow eq 'Y'}">
<input type='hidden' id="showMainTab" value="${showMainTab}"/>
<input type='hidden' id="editFlow" value="${editFlow}"/>
<input type='hidden' id="afterSave" value="${afterSave}"/>
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"> <c:if test="${editFlow eq 'Y'}">
						Edit Holiday
					</c:if> <c:if test="${addFlow eq 'Y'}">
						Add Holiday
					</c:if>
			</span></strong>
		</div>
		<form:form onsubmit="return encodeForm(this);" method="POST"
			id="addEditHolidayMaster" modelAttribute="holidayMasterDTO"
			action="/updateHolidayMaster" autocomplete="off">
			<br />
			<form:hidden path="holidaySeqId" id="holidaySeqId" />
			
			<input type='hidden' id="toHolidayDesc" value="${holidayMasterDTO.holidayDesc}"/>
			<input type='hidden' id="toWeekType" value="${holidayMasterDTO.weekType}"/>
			<input type='hidden' id="showbutton" value="${showButton}"/>
			<input type='hidden' id="toShowbutton" value="${showButton}"/>
			<input type='hidden' id="toPeriodType" value="${periodType}"/>
			<input type='hidden' id="toDayOfWeek" value="${dayOfWeek}"/>
			<input type='hidden' id="showbuttonCatch" value="${showbutton}"/>
			
			
			

			<div class="row">
				<div class="col-sm-12">
					<div class="col-sm-2">
						<div class="form-group">
							<label>
								Product<span
								style="color: red">*</span>
							</label>
							<c:if test="${addFlow eq 'Y'}">
								<form:select path="product" id="product" name="product"
									maxlength="50" cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${productList}" itemValue="code"
										itemLabel="description" />
								</form:select>
							</c:if>
							<c:if test="${editFlow eq 'Y'}">
								<form:select path="product" id="product" name="product"
									maxlength="50" cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${productList}" itemValue="code"
										itemLabel="description" />
								</form:select>
							</c:if>

							 	<div id="errproduct">
								<span for="product" class="error"><form:errors
										path="product" /> </span>
							</div>
						</div>
					</div>

					<div class="col-sm-2">
						<div class="form-group">
							<label>
								Period Type<span
								style="color: red">*</span>
							</label>
							<c:if test="${addFlow eq 'Y'}">
								<form:select path="periodType" id="periodType" name="periodType"
									maxlength="50" cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${periodTypeList}" itemValue="code"
										itemLabel="description" />
								</form:select>
							</c:if>
							<c:if test="${editFlow eq 'Y'}">
								<form:select path="periodType" id="periodType" name="periodType"
									maxlength="50" cssClass="form-control medantory" disabled= "true"
									>
									<form:option value="0" label="SELECT" />
									<form:options items="${periodTypeList}" itemValue="code"
										itemLabel="description" />
								</form:select>
							</c:if>

							<div id="errperiodType">
								<span for="periodType" class="error"><form:errors
										path="periodType" /> </span>
							</div>
						</div>
					</div>
					
					<div class="col-sm-3">
						<div class="form-group">
							<td><label> Leave Description<span
									style="color: red">*</span>
							</label></td>
							<td colspan="5"><textarea rows="2" cols="30" maxlength="100"
									id="holidayDesc" path="holidayDesc"></textarea>
								<div id="errholidayDesc">
									<span for="holidayDesc" class="error"><form:errors
											path="holidayDesc" />
								</div></td>
						</div>
					</div>
					<c:if test="${addFlow eq 'Y'}">
					<div id="divfromDateStr" class="col-md-3">
						<div class="form-group">
							<label id="frmdtLabel" for="">From Date
								<span class="red">*</span></label>
							<form:input path="fromDate" id="fromDateStr"
								name="fromDateStr" cssClass="form-control" readonly="true"
								
								data-date-format="yyyy-mm-dd" />

							<div id="errfromDateStr" class="error">
								<span for=fromDateStr class="error"><form:errors
										path="fromDate" /></span>
							</div>
						</div>
					</div>
					</c:if>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<c:if test="${addFlow eq 'Y'}">
				
					<div id="divtoDateStr" class="col-md-3">
					
						<div class="form-group">
							<label for="">To Date
								<span class="red">*</span></label>

							<form:input path="toDate" id="toDateStr"
								name="toDateStr" cssClass="form-control" readonly="true"
								
								data-date-format="yyyy-mm-dd" />

							<div id="errtoDateStr" class="error">
								<span for=toDateStr class="error"><form:errors
										path="toDate" /></span>
							</div>
						</div>
					</div>
					</c:if>



					<div id="divweekday" class="col-sm-3">
						<div class="form-group">
							<label>
								Day Of The Week<span
								style="color: red">*</span>
							</label>
							<c:if test="${addFlow eq 'Y'}">
								<form:select path="dayOfWeek" id="dayOfWeek" name="dayOfWeek" 
									maxlength="50" cssClass="form-control medantory">
									<form:option value="SELECT" label="SELECT" />
									<form:options items="${dayOfWeekList}" itemValue="code"
										itemLabel="description" />
								</form:select>
							</c:if>
							<c:if test="${editFlow eq 'Y'}">
								<form:select path="dayOfWeek" id="dayOfWeek" name="dayOfWeek" disabled= "true"
									maxlength="50" cssClass="form-control medantory"
									>
									<form:option value="0" label="SELECT" />
									<form:options items="${dayOfWeekList}" itemValue="code"
										itemLabel="description" />
								</form:select>
							</c:if>

								<div id="errdayOfWeek">
								<span for="dayOfWeek" class="error"><form:errors
										path="dayOfWeek" /> </span>
							</div> 
						</div>
					</div>

					<div id="divweekType" class="col-sm-3">
						<div class="col-sm-12">
							<label>Week Type<span style="color: red">*</span></label>
						</div>
						<div class="col-sm-12">
							<c:if test="${addFlow eq 'Y'}">
								<form:select path="weekType" id="weekType" name="weekType[]"
									maxlength="50" multiple="multiple" cssClass="form-control"
									style="display: none; margin-top: -25px padding-left: -100px ;">
									
									<form:options items="${weekTypeList}" itemValue="code"
										itemLabel="description" /> 
								</form:select> 
							</c:if>


							<c:if test="${editFlow eq 'Y'}">
								<form:select path="weekType" id="weekType" name="weekType" disabled= "true"
									maxlength="50" value="${holidayMasterDTO.weekType}"
									multiple="multiple" cssClass="form-control"
									style="margin-top: -25px padding-left: -100px ;"
									>

									<form:options items="${weekTypeList}" itemValue="code"
										itemLabel="description" />
								</form:select>
							</c:if>
							 
							<div id="errweekType">
								<span for="weekType" class="error"><form:errors path="weekType" />
								</span>
							</div> 
						</div>
					</div>
				</div>
			</div>


			<div class="row">
				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align: center">
						<c:if test="${editFlow eq 'Y'and showButton ne 'Y'}">
							<sec:authorize access="hasAuthority('Edit Holiday Master')">
								<button type="button" id = "updateHolidayBtn" class="btn btn-success" disabled = "true"
									onclick="saveDetails('/updateHoliday');">
									<spring:message code="ifsc.submitBtn" />
								</button>

							</sec:authorize>
						</c:if>
						<c:if test="${addFlow eq 'Y' and showButton ne 'Y'}">

							<sec:authorize access="hasAuthority('Add Holiday Master')">
								<input type="button" id = "saveHolidayBtn" class="btn btn-success"
									onclick="saveDetails('/saveHolidayMaster');"
									id="submitButton"
									value="<spring:message code="ifsc.submitBtn" />" />

							</sec:authorize>

						</c:if>
                        <c:if test="${showMainTab eq 'Y'}">
						<button type="button" class="btn btn-danger"
							onclick="submitForm('/getHolidayMasterList');">
							<spring:message code="ifsc.backBtn" />
						</button>
						</c:if>
						<c:if test="${showMainTab ne 'Y'}">
						<button type="button" class="btn btn-danger"
							onclick="submitForm('/getHolidayMasterPendingForApproval');">
							<spring:message code="ifsc.backBtn" />
						</button>
						</c:if>

						<sec:authorize access="hasAuthority('Add Holiday Master')">
						<c:if test="${showButton ne 'Y' and editFlow ne 'Y'}">
							<input type="button" id="clearHolidayMaster" value="Clear"
								class="btn btn-success"></input>
                            </c:if>
						</sec:authorize>


						<sec:authorize access="hasAuthority('Edit Holiday Master')">
							<c:if test="${holidayMasterDTO.requestState eq 'R' and showDiscard ne 'N'}">
								<input name="discardButton" type="button" href="#"  class="btn btn-danger"
									id="discardButton"
									value="<spring:message
							code="ifsc.discardBtn" />"
									 data-toggle="modal" data-target="#toggleModalDiscardRole"  />
							</c:if>
						</sec:authorize>

					</div>
				</div>
			</div>


		</form:form>
	</div>
</c:if>