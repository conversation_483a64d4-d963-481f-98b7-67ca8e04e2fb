package org.npci.settlenxt.adminportal.service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.npci.settlenxt.adminportal.repository.MasterRepository;
import org.npci.settlenxt.common.cache.BaseLookupDTOCache;
import org.npci.settlenxt.portal.common.dto.CityDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.CountryDTO;
import org.npci.settlenxt.portal.common.dto.LookUpDTO;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.TxnSettlementDTO;
import org.npci.settlenxt.portal.common.service.BaseMasterServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MasterServiceImpl extends BaseMasterServiceImpl implements MasterService {

	@Autowired
	private MasterRepository masterRepository;

	@Autowired
	private BaseLookupDTOCache lookupDTOCache;

	@Override
	public List<LookUpDTO> getCityMaster() {
		return masterRepository.getCityMaster();
	}

	@Override
	public List<LookUpDTO> getCityMasterListByStateId(int stateId) {
		return masterRepository.getCityMasterLkp(stateId, BaseCommonConstants.USER_STATUS_ACTIVE);
	}

	@Override
	public List<CodeValueDTO> getCachedLookUpData(String lookupType) {
		return lookupDTOCache.getLookupListOfType(lookupType);
	}

	@Override
	public List<CodeValueDTO> getCachedLookUpDataSortedByDescription(String lookupType) {
		List<CodeValueDTO> codeValueDTOs = lookupDTOCache.getLookupListOfType(lookupType);
		if (codeValueDTOs != null) {
			Collections.sort(codeValueDTOs, Comparator.comparing(CodeValueDTO::getDescription));
		}
		return codeValueDTOs;
	}

	@Override
	public List<LookUpDTO> getStateMaster() {
		return masterRepository.getStateMaster();
	}

	@Override
	public List<CityDTO> getCityMaster(int stateId) {
		return masterRepository.getStateCityMasterList(stateId);
	}

	@Override
	public List<CountryDTO> getCountryList() {
		return masterRepository.getCountryList();
	}

	@Override
	public String getStateCode(int stateId) {

		return masterRepository.getStateCode(stateId);
	}

	@Override
	public String getBinFeatures(String cardType) {
		return masterRepository.getBinFeatures(cardType);
	}

	@Override
	public String getBinCardVariant(String cardType) {
		return masterRepository.getBinCardVariant(cardType);
	}

	@Override
	public List<LookUpDTO> getIfscCodes() {
		return masterRepository.getIfscCodesList();
	}

	@Override
	public MemberDTO getUniqueBankName(String participantId) {
		return masterRepository.getUniqueBankName(participantId);
	}

	@Override
	public List<String> getListUniqueBankName(String participantId) {
		return masterRepository.getListUniqueBankName(participantId);
	}

	@Override
	public List<String> getFunctionCode() {
		List<TxnSettlementDTO> funcCodeList = masterRepository.getFunctionCode();
		return funcCodeList.stream().map(funcCode -> funcCode.getFuncCode() + " - " + funcCode.getTransactionCycle())
				.collect(Collectors.toList());
	}

	@Override
	public TxnSettlementDTO getFunctionCode(String funcCode) {
		return masterRepository.getFunctionTxnCycle(funcCode);
	}

}
