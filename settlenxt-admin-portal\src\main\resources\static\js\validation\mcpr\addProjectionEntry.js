$(document).ready(function () {

    var cursorPosition = null;
    $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    if (colIdx < actionColumnIndex) {
                        $(cell).html(title + '<br><input class="search-box"   type="text" />');

                        // On every keypress in this input
                        $(
                            'input',
                            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
                        )
                            .off('keyup change')
                            .on('change', function (_e) {
                                // Get the search value
                                $(this).attr('title', $(this).val());
                                var regexr = '({search})'; 

                                cursorPosition = this.selectionStart;
                                // Search the column for that value
                                api
                                    .column(colIdx)
                                    .search(
                                        this.value != ''
                                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                                            : '',
                                        this.value != '',
                                        this.value == ''
                                    )
                                    .draw();
                            })
                            .on('click', function (e) {
                                e.stopPropagation();
                            })
                            .on('keyup', function (e) {
                                e.stopPropagation();

                                $(this).trigger('change');
                                if (cursorPosition && cursorPosition != null) {
                                    $(this)
                                        .focus()[0]
                                        .setSelectionRange(cursorPosition, cursorPosition);
                                }
                            });
                    } else {
                        $(cell).html(title + '<br> &nbsp;');
                    }
                });
            $('#tabnew_filter').hide();
            
        },
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Insurance Primium Configuration',
                header: 'false',
                title: null,
                sheetName: 'Insurance Primium Configuration',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            }
 ,
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Insurance Primium Configuration' ,
						header:'false', 
						title: null,
						sheetName:'Insurance Primium Configuration',
						className:'defaultexport',
						exportOptions: {
				            columns: 'th:not(:last-child)'
				         }
                    }
        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true
    });
    
   $("#cardVariant").on('keyup keypress blur change', function () {
     validateField('cardVariant', true, "Alphabet", 100, false);
 });
 $("#cardType").on('keyup keypress blur change', function () {
     validateField('cardType', true, "Alphabet", 100, false);
 });
 $("#totalCards").on('keyup keypress blur change', function () {
     validateField('totalCards', true, "Integer", 100, false);
 }); 
    
 disableSave();
	$("#totalCards").on('keyup keypress blur change', function () {
     unableSave();
 });

    $("#excelExport").on("click", function () {
        $(".buttons-excel").trigger("click");
    });
 
 	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });
   
 
 

 
});
function disableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = true;
}
}

function unableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = false;
}
}



window.history.forward();
function noBack() {
    window.history.forward();
}

function display() {
    $(".appRejMust").hide();

}

function userAction(_type, action) {
	var url = action;
	var cardProjectionId = document.getElementById("cardProjectionId").value;
	var data = "cardProjectionId," + cardProjectionId + ",status,"
		+ status;
	postData(url, data);
} 

function addOrUpdateEntry(id) {
var url;
	if (id == 'A') {
		url = '/addCardProjection';

	} else if (id == 'E') {
		url = '/updateCardProjection';
	}
    var isValid = true;

    if (!validateField('cardVariant', true, "Alphabet", 100, false) && isValid) {
        isValid = false;
    }
    if (!validateField('cardType', true, "Alphabet", 100, false) && isValid) {
        isValid = false;
    }
    if (!validateField('totalCards', true, "Integer", 100, false) && isValid) {
        isValid = false;
    }
    
    
    if (isValid) {
    	var cardVariantName =$("#cardVariant option:selected").text();
    	var cardTypeName =$("#cardType option:selected").text();
    	
        var data = "totalCards," + $('#totalCards').val() + ",cardProjectionId," + $('#cardProjectionId').val()
        + ",cardVariant," + $('#cardVariant').val() + ",cardType," + $('#cardType').val()
        + ",cardVariantName," + cardVariantName + ",cardTypeName," + cardTypeName
        +",parentPage," + $("#hparentPage").val();
        
        postData(url, data);
    }
}

function validateField(fieldId, isMandatory, fieldType, _length, _isExactLength) {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if (isMandatory && fieldValue.trim() == "") {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    if (fieldValue == "SELECT") {
        isValid = false;
    }
    if (fieldType == "Integer") {
    let regEx =/^\d*$/;
    	if (!regEx.test(fieldValue)) {
    	isValid = false;
    }
    }
    
    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
    	if(cardProjectionValidationMessages[fieldId]){
    		$("#err" + fieldId).find('.error').html(cardProjectionValidationMessages[fieldId]);
    	}
        $("#err" + fieldId).show();
    }
    
    return isValid;
}


function postDiscardAction(action) {
	var url = action;
	var cardProjectionId = document.getElementById("cardProjectionId").value;
	var data = "cardProjectionId," + cardProjectionId ;
	postData(url, data);
	
}



function postDeleteAction(action) {
	var url = action;
	var cardProjectionId = document.getElementById("cardProjectionId").value;
	var data1 = "cardProjectionId," + cardProjectionId ;
	postData(url, data1);
	
}

function postCalculateAction(action) {
	var url1 = action;
	var cardProjectionId = document.getElementById("cardProjectionId").value;
	var data = "cardProjectionId," + cardProjectionId  ;
	postData(url1, data);
	
}
function resetAction(){ 
	document.getElementById("addCardProjection").reset();  
	$("#errcardType").find('.error').html('');
	$("#errcardVariant").find('.error').html('');
	$("#errtotalCards").find('.error').html('');
}






