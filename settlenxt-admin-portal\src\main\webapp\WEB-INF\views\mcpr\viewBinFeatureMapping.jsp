<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>


<script src="./static/js/validation/mcpr/viewBinFeatureMapping.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	
	<c:url value="getBinFeatureMapping" var="getBinFeatureMapping" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveBinFeatureMapping" modelAttribute="binFeatureMappingDTO"
		action="${getBinFeatureMapping}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"></span><spring:message code="binFeatureMapping.mainTab.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="binFeatureId" value="${binFeatureMappingDTO.binFeatureId}">

						<input type="hidden" id="crtuser"
							value="${binFeatureMappingDTO.createdBy}" />

						<table class="table table-striped infobold" style="font-size: 12px">
							<caption style="display:none;">Bin Feature Mapping</caption>
									<thead style="display:none;"><th scope = "col"></th></thead>
									<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message code="binFeatureMapping.viewscreen.title" /></span></strong>
										</div></td>
										<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									
								</tr>
								<tr>

								</tr>
								
								<tr>
									<td><label><spring:message code="binFeatureMapping.binFeatureId" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.binFeatureId}</td>
									<td><label><spring:message code="binFeatureMapping.participantName" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.bankName}</td>
									<td><label><spring:message code="binFeatureMapping.baseorfeature" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.featureName}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									
								</tr>
								<tr>
									
									
									<td><label><spring:message code="binFeatureMapping.bin" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.bin}</td>
									<td><label><spring:message code="binFeatureMapping.fromDate" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.fromDate}</td>
									<td><label><spring:message code="binFeatureMapping.toDate" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.toDate}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>

								
							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									
								

									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/showMemBinFeatureMapping');"><spring:message code="binFeatureMapping.backBtn" /></button>
										
						<c:if test="${binFeatureMappingDTO.requestState =='A' }">
								<sec:authorize access="hasAuthority('Edit Bin Feature Mapping')">
								<input name="deleteButton" type="button" class="btn btn-success"
								 id="Delate" value="Delete" 
								onclick="Edit('/deleteBinFeatureMapping','${binFeatureMappingDTO.binFeatureId}','mainTab');"/>
								<input name="editButton" type="button" class="btn btn-success"
								 id="approveRole" value="Edit" 
								onclick="Edit('/editBinFeatureMapping','${binFeatureMappingDTO.binFeatureId}','mainTab');"/>
								</sec:authorize>
							</c:if>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

