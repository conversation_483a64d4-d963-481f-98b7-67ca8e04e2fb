package org.npci.settlenxt.adminportal.controllers;

import java.time.LocalDate;
import java.util.List;

import org.npci.settlenxt.adminportal.common.cache.LookupDTOCache;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.CycleStatusMonitorDTO;
import org.npci.settlenxt.adminportal.dto.ReportStatusDTO;
import org.npci.settlenxt.adminportal.repository.CycleStatusMonitorRepository;
import org.npci.settlenxt.adminportal.service.CycleStatusMonitorService;
import org.npci.settlenxt.adminportal.service.ReportService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class CycleStatusMonitorController extends BaseController {

	private static final String CYCLE_STATUS_MONITOR_INFO = "cycleStatusMonitorInfo";
	private static final String CYCLE_DETAILS_INFO = "cyclyDetailsInfo";
	private static final String VIEW_CYCLE_STATUS_MONITOR_INFO = "viewCycleStatusMonitor";
	private static final String PROC_CODE = "prodCd";
	private static final String CYCLE_DATA = "cycleData";
	private static final String CYCLE_STATUS_MONITOR_DTO = "cycleStatusMonitorDto";
	private static final String CYCLE_STATUS = "CYCLE_STATUS";
	private static final String FLAG = "flag";
	private static final String CYCLE_NUMBER = "cycleNumbers";
	private static final String REFRESH_FLAG = "refreshFlag";
	private static final String GET_CYCLE_BY_GUI_ID = "getCycleByGuiId";
	private static final String DEFAULT_DATE = "defaultDate";
	private static final String RUNNING_CYCLE_INFO = "runningCycleInfo";

	@Autowired
	CycleStatusMonitorRepository cycleStatusMonitorRepository;
	@Autowired
	ReportService reportService;
	@Autowired
	CycleStatusMonitorService cycleStatusMonitorService;
	@Autowired
	LookupDTOCache lookupDtoCache;
	

	@PostMapping("/getCycleStatusDetails")
	@PreAuthorize("hasAuthority('View Cycle Status Details')")
	public String searchCycleStatusDetails(Model model) {

		LocalDate localDate = LocalDate.now();
		CycleStatusMonitorDTO cycleStatusMonitorDto = new CycleStatusMonitorDTO();
		
		ReportStatusDTO runningCycleInfo = reportService.getCycleInfo(env.getProperty("sub.network.default"));
		List<String> prodCdList = reportService.getProductId();
		

		model.addAttribute(DEFAULT_DATE, localDate);
		model.addAttribute(RUNNING_CYCLE_INFO, runningCycleInfo.getCycleNumber());
		model.addAttribute(PROC_CODE, prodCdList);
		model.addAttribute(CYCLE_STATUS_MONITOR_DTO, cycleStatusMonitorDto);

		return getView(model, VIEW_CYCLE_STATUS_MONITOR_INFO);

	}

	@PostMapping("/fetchCycleStatusDetails")
	@PreAuthorize("hasAuthority('View Cycle Status Details')")
	public String fetchCycleStatusDetails(@RequestParam("productCode") String productCode,
			@RequestParam("cycleNumber") String cycleNumber, @RequestParam("cycleDate") LocalDate cycleDate,
			@RequestParam("refresh") boolean refresh, Model model) {

		CycleStatusMonitorDTO cycleStatusMonitorDto = new CycleStatusMonitorDTO();
		cycleStatusMonitorDto.setProductCode(productCode);

		cycleStatusMonitorDto.setCycleDate(cycleDate);
		cycleStatusMonitorDto.setCycleNumber(cycleNumber);

		List<CycleStatusMonitorDTO> cycleDetails = cycleStatusMonitorService
				.getCycleStatusDetails(cycleStatusMonitorDto);
		for (CycleStatusMonitorDTO cycle : cycleDetails) {
			cycle.setActivityCode(lookupDtoCache.getDescription(CYCLE_STATUS, cycle.getActivityCode()));
		}

		List<String> prodCdList = reportService.getProductId();

		model.addAttribute(PROC_CODE, prodCdList);
		model.addAttribute(FLAG, CommonConstants.YES);

		if (refresh) {
			model.addAttribute(REFRESH_FLAG, CommonConstants.YES);
		}
		model.addAttribute(CYCLE_NUMBER, cycleNumber);
		model.addAttribute(CYCLE_DATA, cycleDetails);
		model.addAttribute(CYCLE_STATUS_MONITOR_DTO, cycleStatusMonitorDto);

		return getView(model, VIEW_CYCLE_STATUS_MONITOR_INFO);

	}

	@PostMapping("/getCycleData")
	public ResponseEntity<Object> getCycleNum(@RequestParam("productCode") String productCode, Model model) {
		List<String> settlementCycleNum = reportService.getSettlementCycleListBasedOnProdCode(productCode);
		return new ResponseEntity<>(settlementCycleNum, HttpStatus.OK);

	}

	@PostMapping("/getCycleByGuid")
	@PreAuthorize("hasAuthority('View Cycle Status Details')")
	public String getCycleByGuid(@RequestParam("guid") int guid, Model model) {

		List<CycleStatusMonitorDTO> cycleDetails = cycleStatusMonitorService.getCycleDetailsByGuid(guid);
		CycleStatusMonitorDTO cycleStatusMonitorDto = cycleStatusMonitorService.getCycleStatusByGuid(guid);

		model.addAttribute(CYCLE_STATUS_MONITOR_INFO, cycleStatusMonitorDto);
		model.addAttribute(CYCLE_DETAILS_INFO, cycleDetails);
		return getView(model, GET_CYCLE_BY_GUI_ID);
	}
}
