<?xml version="1.0" encoding="UTF-8"?>
<report xmlns="http://www.eclipse.org/birt/2005/design" version="3.2.23" id="1">
    <property name="createdBy">Eclipse BIRT Designer Version 4.6.0.v201606072122</property>
    <property name="units">in</property>
    <property name="iconFile">/templates/blank_report.gif</property>
    <property name="bidiLayoutOrientation">ltr</property>
    <property name="imageDPI">96</property>
    <parameters>
        <scalar-parameter name="fromDateStr" id="560">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
        <scalar-parameter name="toDateStr" id="561">
            <property name="valueType">static</property>
            <property name="dataType">string</property>
            <property name="distinct">true</property>
            <list-property name="selectionList"/>
            <property name="paramType">simple</property>
            <property name="controlType">text-box</property>
            <structure name="format">
                <property name="category">Unformatted</property>
            </structure>
        </scalar-parameter>
    </parameters>
    <data-sources>
        <oda-data-source extensionID="org.eclipse.birt.report.data.oda.jdbc" name="Data Source" id="6">
            <list-property name="privateDriverProperties">
                <ex-property>
                    <name>metadataBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
                <ex-property>
                    <name>disabledMetadataBidiFormatStr</name>
                </ex-property>
                <ex-property>
                    <name>contentBidiFormatStr</name>
                    <value>ILYNN</value>
                </ex-property>
                <ex-property>
                <name>disabledContentBidiFormatStr</name>
                </ex-property>
            </list-property>
       </oda-data-source>
    </data-sources>
    <data-sets>
        <oda-data-set extensionID="org.eclipse.birt.report.data.oda.jdbc.JdbcSelectDataSet" name="Data Set" id="7">
            <list-property name="columnHints">
                <structure>
                    <property name="columnName">reference_number</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">reference_number</text-property>
                    <text-property name="heading">reference_number</text-property>
                </structure>
                <structure>
                    <property name="columnName">news_alert_type</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">news_alert_type</text-property>
                    <text-property name="heading">news_alert_type</text-property>
                </structure>
                <structure>
                    <property name="columnName">created_on</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">created_on</text-property>
                    <text-property name="heading">created_on</text-property>
                </structure>
                <structure>
                    <property name="columnName">valid_from</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">valid_from</text-property>
                    <text-property name="heading">valid_from</text-property>
                </structure>
                <structure>
                    <property name="columnName">valid_to</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">valid_to</text-property>
                    <text-property name="heading">valid_to</text-property>
                </structure>
                <structure>
                    <property name="columnName">details</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">details</text-property>
                    <text-property name="heading">details</text-property>
                </structure>
                <structure>
                    <property name="columnName">status</property>
                    <property name="analysis">dimension</property>
                    <text-property name="displayName">status</text-property>
                    <text-property name="heading">status</text-property>
                </structure>
            </list-property>
            <list-property name="parameters">
                <structure>
                    <property name="name">param_1</property>
                    <property name="paramName">fromDateStr</property>
                    <property name="nativeName"></property>
                    <property name="dataType">dateTime</property>
                    <property name="nativeDataType">93</property>
                    <property name="position">1</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
                <structure>
                    <property name="name">param_2</property>
                    <property name="paramName">toDateStr</property>
                    <property name="nativeName"></property>
                    <property name="dataType">dateTime</property>
                    <property name="nativeDataType">93</property>
                    <property name="position">2</property>
                    <property name="isInput">true</property>
                    <property name="isOutput">false</property>
                </structure>
            </list-property>
            <structure name="cachedMetaData">
                <list-property name="resultSet">
                    <structure>
                        <property name="position">1</property>
                        <property name="name">reference_number</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">2</property>
                        <property name="name">news_alert_type</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">3</property>
                        <property name="name">created_on</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">4</property>
                        <property name="name">valid_from</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">5</property>
                        <property name="name">valid_to</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">6</property>
                        <property name="name">details</property>
                        <property name="dataType">string</property>
                    </structure>
                    <structure>
                        <property name="position">7</property>
                        <property name="name">status</property>
                        <property name="dataType">string</property>
                    </structure>
                </list-property>
            </structure>
            <property name="dataSource">Data Source</property>
            <list-property name="resultSet">
                <structure>
                    <property name="position">1</property>
                    <property name="name">reference_number</property>
                    <property name="nativeName">reference_number</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">2</property>
                    <property name="name">news_alert_type</property>
                    <property name="nativeName">news_alert_type</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">3</property>
                    <property name="name">created_on</property>
                    <property name="nativeName">created_on</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">4</property>
                    <property name="name">valid_from</property>
                    <property name="nativeName">valid_from</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">5</property>
                    <property name="name">valid_to</property>
                    <property name="nativeName">valid_to</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">93</property>
                </structure>
                <structure>
                    <property name="position">6</property>
                    <property name="name">details</property>
                    <property name="nativeName">details</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
                <structure>
                    <property name="position">7</property>
                    <property name="name">status</property>
                    <property name="nativeName">status</property>
                    <property name="dataType">string</property>
                    <property name="nativeDataType">12</property>
                </structure>
            </list-property>
            <xml-property name="queryText"><![CDATA[SELECT DISTINCT REFERENCE_NUMBER,NEWS_ALERT_TYPE,TO_CHAR(CREATED_ON,'DD/MM/YYYY HH24:MI:SS') as CREATED_ON,
TO_CHAR(VALID_FROM,'DD/MM/YYYY HH24:MI:SS') as VALID_FROM,TO_CHAR(VALID_TO,'DD/MM/YYYY HH24:MI:SS') as VALID_TO,DETAILS,(CASE REQUEST_STATE
        WHEN 'A' THEN 'Approved'
        WHEN 'P' THEN 'Submitted'
	 ELSE 'In-Progress' 
    END ) as status FROM NEWS_ALERTS_STG
 WHERE REQUEST_STATE!='D' AND (VALID_FROM >= TO_TIMESTAMP(?,'YYYY-MM-DD HH24:MI:SS') AND VALID_TO <= TO_TIMESTAMP(?,'YYYY-MM-DD HH24:MI:SS') ) ; ]]></xml-property>
        </oda-data-set>
    </data-sets>
    <page-setup>
        <simple-master-page name="Simple MasterPage" id="2">
            <property name="type">custom</property>
            <property name="height">11in</property>
            <property name="width">13in</property>
        </simple-master-page>
    </page-setup>
    <body>
        <table id="438">
            <property name="whiteSpace">nowrap</property>
            <property name="dataSet">Data Set</property>
            <list-property name="boundDataColumns">
                <structure>
                    <property name="name">reference_number</property>
                    <text-property name="displayName">reference_number</text-property>
                    <expression name="expression" type="javascript">dataSetRow["reference_number"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">news_alert_type</property>
                    <text-property name="displayName">news_alert_type</text-property>
                    <expression name="expression" type="javascript">dataSetRow["news_alert_type"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">created_on</property>
                    <text-property name="displayName">created_on</text-property>
                    <expression name="expression" type="javascript">dataSetRow["created_on"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">valid_from</property>
                    <text-property name="displayName">valid_from</text-property>
                    <expression name="expression" type="javascript">dataSetRow["valid_from"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">valid_to</property>
                    <text-property name="displayName">valid_to</text-property>
                    <expression name="expression" type="javascript">dataSetRow["valid_to"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">details</property>
                    <text-property name="displayName">details</text-property>
                    <expression name="expression" type="javascript">dataSetRow["details"]</expression>
                    <property name="dataType">string</property>
                </structure>
                <structure>
                    <property name="name">status</property>
                    <text-property name="displayName">status</text-property>
                    <expression name="expression" type="javascript">dataSetRow["status"]</expression>
                    <property name="dataType">string</property>
                </structure>
            </list-property>
            <column id="485">
                <property name="width">1.8in</property>
            </column>
            <column id="486">
                <property name="width">1.8in</property>
            </column>
            <column id="487">
                <property name="width">1.8in</property>
            </column>
            <column id="488">
                <property name="width">1.8in</property>
            </column>
            <column id="489">
                <property name="width">1.8in</property>
            </column>
            <column id="492">
                <property name="width">1.8in</property>
            </column>
            <column id="493">
                <property name="width">1.8in</property>
            </column>
            <header>
                <row id="439">
                    <property name="backgroundColor">#D3D3D3</property>
                    <property name="height">0.4166666666666667in</property>
                    <cell id="441">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="499">
                            <property name="backgroundColor">#FFFFFF</property>
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="fontWeight">bold</property>
                            <property name="textAlign">left</property>
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">Reference Number_D</text-property>
                        </label>
                    </cell>
                    <cell id="442">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="500">
                            <property name="backgroundColor">#FFFFFF</property>
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="fontWeight">bold</property>
                            <property name="paddingLeft">0.1in</property>
                            <property name="paddingRight">0in</property>
                            <property name="textAlign">left</property>
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">Type</text-property>
                        </label>
                    </cell>
                    <cell id="443">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="501">
                            <property name="backgroundColor">#FFFFFF</property>
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="fontWeight">bold</property>
                            <property name="paddingLeft">0.1in</property>
                            <property name="paddingRight">0in</property>
                            <property name="textAlign">left</property>
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">Date</text-property>
                        </label>
                    </cell>
                    <cell id="444">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="502">
                            <property name="backgroundColor">#FFFFFF</property>
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="fontWeight">bold</property>
                            <property name="paddingLeft">0.1in</property>
                            <property name="paddingRight">0in</property>
                            <property name="textAlign">left</property>
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">From Date</text-property>
                        </label>
                    </cell>
                    <cell id="445">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="503">
                            <property name="backgroundColor">#FFFFFF</property>
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="fontWeight">bold</property>
                            <property name="paddingLeft">0.1in</property>
                            <property name="paddingRight">0in</property>
                            <property name="textAlign">left</property>
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">To Date</text-property>
                        </label>
                    </cell>
                    <cell id="448">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="506">
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="fontWeight">bold</property>
                            <property name="paddingLeft">0.1in</property>
                            <property name="paddingRight">0in</property>
                            <property name="textAlign">left</property>
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">Details</text-property>
                        </label>
                    </cell>
                    <cell id="449">
                        <property name="backgroundColor">#FFFFFF</property>
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <label id="507">
                            <property name="backgroundColor">#FFFFFF</property>
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="fontWeight">bold</property>
                            <property name="paddingLeft">0.1in</property>
                            <property name="paddingRight">0in</property>
                            <property name="textAlign">left</property>
                            <property name="whiteSpace">nowrap</property>
                            <text-property name="text">Is Approved</text-property>
                        </label>
                    </cell>
                </row>
            </header>
            <detail>
                <row id="454">
                    <property name="height">0.28125in</property>
                    <cell id="456">
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="564">
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="resultSetColumn">reference_number</property>
                        </data>
                    </cell>
                    <cell id="457">
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="565">
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="resultSetColumn">news_alert_type</property>
                        </data>
                    </cell>
                    <cell id="458">
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="566">
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="resultSetColumn">created_on</property>
                        </data>
                    </cell>
                    <cell id="459">
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="567">
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="resultSetColumn">valid_from</property>
                        </data>
                    </cell>
                    <cell id="460">
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="568">
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="resultSetColumn">valid_to</property>
                        </data>
                    </cell>
                    <cell id="463">
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="569">
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="resultSetColumn">details</property>
                        </data>
                    </cell>
                    <cell id="464">
                        <property name="borderBottomStyle">solid</property>
                        <property name="borderBottomWidth">1px</property>
                        <property name="borderLeftStyle">solid</property>
                        <property name="borderLeftWidth">1px</property>
                        <property name="borderRightStyle">solid</property>
                        <property name="borderRightWidth">1px</property>
                        <property name="borderTopStyle">solid</property>
                        <property name="borderTopWidth">1px</property>
                        <data id="570">
                            <property name="fontFamily">"Arial"</property>
                            <property name="fontSize">9pt</property>
                            <property name="resultSetColumn">status</property>
                        </data>
                    </cell>
                </row>
            </detail>
            <footer>
                <row id="469">
                    <cell id="471"/>
                    <cell id="472"/>
                    <cell id="473"/>
                    <cell id="474"/>
                    <cell id="475"/>
                    <cell id="478"/>
                    <cell id="479"/>
                </row>
            </footer>
        </table>
    </body>
</report>
