var entry = {};
var allPages = 0;
$(document).ready(function() {
	$('#transitionErrorMsg').hide();
	$('#transitionErrorMsg1').hide();

	var cursorPosition = null;
	/* Initialization of datatables */
	$(document).ready(function() {

		var myTable = $("#tabnew").DataTable({

			initComplete: function() {
				var api = this.api();

				// For each column
				api
					.columns()
					.eq(0)
					.each(function(colIdx) {
						//If first column to be skipped to include the filter for the reasons line check box 
						if (!(colIdx == 0 && firstColumnToBeSkippedInFilterAndSort)) {
							// Set the header cell to contain the input element
							var cell = $('#tabnew thead tr th').eq(
								$(api.column(colIdx).header()).index()
							);
							var title = $(cell).text();
							cursorPosition = searchBoxFunc(colIdx, cell, title, api, cursorPosition);
						}
					});
				$('#tabnew_filter').hide();
			},

			// Disabled ordering for first column in case
			columnDefs: [
				{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
			],
			"order": [],
			dom: 'lBfrtip',
			buttons: [

				{
					extend: 'excelHtml5',
					text: 'Export',
					filename: 'DisputeFeeRule',
					header: 'false',
					title: null,
					sheetName: 'DisputeFeeRule',
					className: 'defaultexport',
					exportOptions: {
						columns: 'th:not(:last-child)'
					}
				},
				{
					extend: 'csvHtml5',
					text: 'Export',
					filename: 'DisputeFeeRule',
					header: 'false',
					title: null,
					sheetName: 'DisputeFeeRule',
					className: 'defaultexport',
					exportOptions: {
						columns: 'th:not(:last-child)'
					}
				}

			],

			searching: true,
			info: true,
			lengthChange: true,
			bLengthChange: true,
		});
		allPages = myTable.rows().nodes();
		
		 $("#excelExport").on("click", function () {
		        $(".buttons-excel").trigger("click");
		    });
		    
		     $("#csvExport").on("click", function () {
		        $(".buttons-csv").trigger("click");
		    });
		 
		     $("#clearFilters").on("click", function () {
		       $(".search-box").each(function() {
					   $(this).val("");
					     $(this).trigger("change");
					});
		    });
		
		$("#selectAll1").click(function() {
			if ($(this).hasClass('allChecked')) {
				$('.selectedId', allPages).prop('checked', false);
			} else {
				$('.selectedId', allPages).prop('checked', true);
			}

			$(this).toggleClass('allChecked');

			var footer = document.getElementById("ruless");
			
				var temp = document.getElementById("detailsHeaderss").value;
		    footer.innerHTML = $('<div>').text(temp).html() + "     " + "records are selected";
			
			
			showAndHideModel();

		});
	});
});


function searchBoxFunc(colIdx, cell, title, api, cursorPosition) {
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function() {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
    return cursorPosition;
}

function showAndHideModel() {
    if (document.getElementById("detailsHeaderss").value > 0) {
        if ($('#selectAll1').is(':checked')) {
            $("#toggleModal1").modal('show');
        }
        else {
            $("#toggleModal1").modal('hide');
        }
    }
    else {
        if ($('#selectAll').is(':checked')) {
            $('#transitionErrorMsg').show();
        }
        else {
            $('#transitionErrorMsg').hide();
        }
    }
}

function view(seqID, type) {
	var url;
	if (type == 'V')
		url = '/editDisputeFeeRule';
	else if (type == 'P')
		url = '/getDisputeFeeRule';
	else if (type == 'G')
		url = '/getPendingDisputeFeeRule';

	var data = "seqId," + seqID + ",viewType," + type;

	
	postData(url, data);
}

function viewDisputeInfo(action, data) {
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	var linkurl = pathName + action;
	var form = document.createElement("form");
	var dynInput;
	form.method = "POST";

	var parameters = data.split(",");
	for (var i = 0; i < 15; ++i) {
		if(i%2==0){
		dynInput = document.createElement("input");
		dynInput.setAttribute("type", "hidden");
		dynInput.setAttribute("id", parameters[i]);
		dynInput.setAttribute("name", parameters[i]);
		}
		else{
		dynInput.setAttribute("value", parameters[i]);

		form.appendChild(dynInput);
		}
	}
	dynInput = document.createElement("input");
	dynInput.setAttribute("type", "hidden");
	dynInput.setAttribute("id", parameters[16]);
	dynInput.setAttribute("name", parameters[16]);
	var fieldValue = parameters.slice(17).join();
	dynInput.setAttribute("value", fieldValue);
	form.appendChild(dynInput);

	document.body.appendChild(form); // added this for firefox Browser
	encodeForm(form); // Added by piyush for form encode

	form.action = linkurl;
	form.submit();
}

function submitForm(url) {
	var data = "userType," + $('#userType').val();
	postData(url, data);
}

function userAction(_type, action) {
	var url = action;
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";

	var data = "_vTransactToken," + tokenValue;
	postData(url, data);
}

function deselectAll() {
	$('#selectAll1').prop('checked', false);
	var ele = document.getElementsByName('type');
	for (let val of ele) {
		if (val.type == 'checkbox')
			val.checked = false
	}
}

function approveorRejectBulk(type) {
	var url = '/bulkApproveRejectDisputeFee';
	var LoginIdList = "";
	$('.selectedId', allPages).filter(":checked").each(function() {
		LoginIdList = LoginIdList + $(this).val() + "|";
	});
	if (LoginIdList === "") {
		$('#transitionErrorMsg1').show();
	} else {
		var data = "status," + type + ",disputeFeeList," + LoginIdList.slice(0, -1);
		postData(url, data);
	}
}

