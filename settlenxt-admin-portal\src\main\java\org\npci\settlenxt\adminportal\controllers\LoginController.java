package org.npci.settlenxt.adminportal.controllers;

import java.security.SecureRandom;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.service.UserService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import lombok.extern.slf4j.Slf4j;

@Controller
@Slf4j
public class LoginController extends BaseController {

	@Autowired
	private UserService userService;

	private static final String SPRING_SECURITY_LAST_EXCEPTION = "SPRING_SECURITY_LAST_EXCEPTION";
	private static final String LOGIN_ERR_MSG = "logingErroMessage";
	private static final String NEWS_ALERT_LIST = "newsAlertsList";
	private static final String TOKEN_ERROR = "tokenError";
	private static final String PORTAL = "portal";
	private static final String SETTLENXT_LOGIN_PAGE = "SettleNxtLogin";

	@GetMapping("/login")
	public String login(Model model, @RequestParam("logout") Optional<String> logout,
			@RequestParam("error") Optional<String> errorFlag, HttpSession session, HttpServletResponse response) {

		if (logout.isPresent()) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("login.logout"));
		}
		if (errorFlag.isPresent()) {
			model.addAttribute(CommonConstants.ERROR_STATUS, "");
			session.setAttribute(CommonConstants.ERROR_STATUS, "");
			model.addAttribute(CommonConstants.SUCCESS_STATUS, "");
			if (session.getAttribute(SPRING_SECURITY_LAST_EXCEPTION) != null) {
				Exception securityException = (Exception) session.getAttribute(SPRING_SECURITY_LAST_EXCEPTION);
				if (securityException != null) {
					model.addAttribute(LOGIN_ERR_MSG, securityException.getMessage());
					session.removeAttribute(SPRING_SECURITY_LAST_EXCEPTION);
				}
			} else {
				SecureRandom rand = new SecureRandom();
				String portal = env.getProperty(BaseCommonConstants.PORTAL);
				try {
					portal = portal + "/login?";
					response.sendRedirect(portal + "r=" + rand.nextInt());
				} catch (Exception e) {
					log.error(e.getMessage(), e);
				}
			}
		}

		model.addAttribute(NEWS_ALERT_LIST, userService.getNewsAlertsListPublic());
		String portal = env.getProperty(BaseCommonConstants.PORTAL);

		if (StringUtils.isBlank(portal)) {
			portal = CommonConstants.SETTLENXT_ADMIN_PORTAL;
		}
		model.addAttribute(PORTAL, portal);
		model.addAttribute(BaseCommonConstants.KEY, BaseCommonConstants.PUBLIC_KEY);
		return getLoginView(model, SETTLENXT_LOGIN_PAGE);
	}

	@GetMapping("/invalidSession")
	public String invalidSession(Model model) {
		model.addAttribute(CommonConstants.ERROR_STATUS, getMessageFromBundle("login.invalidSession"));
		String portal = env.getProperty(BaseCommonConstants.PORTAL);

		if (StringUtils.isBlank(portal)) {
			portal = CommonConstants.SETTLENXT_ADMIN_PORTAL;
		}
		model.addAttribute(PORTAL, portal);
		model.addAttribute(BaseCommonConstants.KEY, BaseCommonConstants.PUBLIC_KEY);
		return getLoginView(model, SETTLENXT_LOGIN_PAGE);
	}

	@GetMapping("/errorToken")
	public String tokenCheck(Model model, HttpServletRequest request, HttpSession session) {
		log.info("Error tokenCheck method");
		return getView(model, TOKEN_ERROR);
	}

	/**
	 * Error XSS Page Redirection From Post Request UI
	 * 
	 * @param model
	 * @param request
	 * @param session
	 * @return
	 */
	@PostMapping("/errorToken")
	public String tokenMethodCheck(Model model, HttpServletRequest request, HttpSession session) {
		return tokenCheck(model, request, session);
	}

}