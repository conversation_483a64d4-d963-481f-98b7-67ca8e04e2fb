function bindDateValidationsOnload() {

	$("#acqToDate").val("31-12-2099");
	$("#issToDate").val("31-12-2099");

	$("#acqToDate")
		.on(
			'blur',
			function () {
				var actDate = $("#acqFrmDate").val();
				var deactDate = $("#acqToDate").val();
				var fromarr = actDate.split('-');
				var toarr = deactDate.split('-');
				var from = new Date(fromarr[2], fromarr[1], fromarr[0]);
				var to = new Date(toarr[2], toarr[1], toarr[0]);
				if (isEmpty(deactDate)) {
					$('#erracqToDate').show();
				}
				else
					if (!isEmpty(actDate) && (from.getTime() > to.getTime())) {
						$('#erracqToDate').text('Deactivation Date should be greater than Activation date');
					} else {
						$('#erracqToDate').text('');
					}
			});
	$("#acqFrmDate")
		.on(
			'blur',
			function () {
				var actDate = $("#acqFrmDate").val();
				var deactDate = $("#acqToDate").val();
				var fromarr = actDate.split('-');
				var toarr = deactDate.split('-');
				var from = new Date(fromarr[2], fromarr[1], fromarr[0]);
				var to = new Date(toarr[2], toarr[1], toarr[0]);
				if (isEmpty(actDate)) {
					$('#erracqFrmDate').show();
					$('#erracqFrmDate').find('.error').html('Activation Date should be less than Deactivation date and mandatory');
				}
				else if (!isEmpty(deactDate) && (from.getTime() > to.getTime())) {
					$('#errissFrmDate').find('.error').html('Activation Date should be less than Deactivation date');
				} else {
					$('#errissFrmDate').find('.error').html('');
				}
			});
	$("#issToDate")
		.on(
			'blur',
			function () {

				var actDate = $("#issFrmDate").val();
				var deactDate = $("#issToDate").val();
				var fromarr = actDate.split('-');
				var toarr = deactDate.split('-');
				var from = new Date(fromarr[2], fromarr[1], fromarr[0]);
				var to = new Date(toarr[2], toarr[1], toarr[0]);
				if (isEmpty(deactDate)) {
					$('#errissToDate').show();
				}
				else if (!isEmpty(actDate) && (from.getTime() > to.getTime())) {
					$('#errissToDate').text('Deactivation Date should be greater than Activation date');
				} else {
					$('#errissToDate').text('');
				}
			});
	$("#issFrmDate")
		.on(
			'blur',
			function () {

				var actDate = $("#issFrmDate").val();
				var deactDate = $("#issToDate").val();
				var fromarr = actDate.split('-');
				var toarr = deactDate.split('-');
				var from = new Date(fromarr[2], fromarr[1], fromarr[0]);
				var to = new Date(toarr[2], toarr[1], toarr[0]);
				if (isEmpty(actDate)) {
					$('#errissFrmDate').show();
					$('#errissFrmDate').find('.error').html('Activation Date should be less than Deactivation date and mandatory');
				}
				else if (!isEmpty(deactDate) && (from.getTime() > to.getTime())) {
					$('#errissFrmDate').find('.error').html('Activation Date should be less than Deactivation date');
				} else {
					$('#errissFrmDate').find('.error').html('');

				}
			});

	$("#acqFrmDate").datepicker({
		dateFormat: "dd-mm-yy",
		changeMonth: true,
		changeYear: true,
		yearRange: "2022:2099",
		/*
		 * minDate : 1, maxDate : new Date(maxYear, month, day),
		 */
		minDate: 0,
		onClose: function (selectedDate) {
			$("#toDate").datepicker("option", "minDate", selectedDate);
		}
	});

	$("#acqToDate").datepicker({
		dateFormat: "dd-mm-yy",
		changeMonth: true,
		changeYear: true,
		yearRange: "2022:2099",
		/* maxDate: 0, */
		minDate: 0,
		onClose: function (selectedDate) {
			$("#toDate").datepicker("option", "minDate", selectedDate);
		}
	});

	$("#issFrmDate").datepicker({
		dateFormat: "dd-mm-yy",
		changeMonth: true,
		changeYear: true,
		yearRange: "2022:2099",
		/* maxDate: 0, */
		// minDate : 0,
		minDate: 0,
		onClose: function (selectedDate) {
			$("#toDate").datepicker("option", "minDate", selectedDate);
		}
	});

	$("#issToDate").datepicker({
		dateFormat: "dd-mm-yy",
		changeMonth: true,
		changeYear: true,
		yearRange: "2022:2099",
		/* maxDate: 0, */
		minDate: 0,
		onClose: function (selectedDate) {
			$("#toDate").datepicker("option", "minDate", selectedDate);
		}
	});
}