<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.McprTATRepository">
	<select id="getApprovedMcprTATData" resultType="McprTATDTO">
		SELECT R.tat_id as taTId, R.upload_W_TAT as uploadWTAT, R.edit_W_TAT as editWTAT,S.request_state as status FROM mcpr_tat_config R
		inner join mcpr_tat_config_stg S on R.tat_id=S.tat_id  
		order by R.tat_id limit 1
	</select>
	<select id="getPendingForAppovalMcprTATData" resultType="McprTATDTO">
		SELECT R.upload_W_TAT as uploadWTAT, R.edit_W_TAT as editWTAT,R.status, R.request_state as requestState,<PERSON>.checker_comments as checkerComments , tat_id as taTId, R.LAST_UPDATED_BY as lastUpdatedBy,R.LAST_UPDATED_ON as lastUpdatedOn, R.CREATED_ON as createdOn, R.CREATED_BY as createdBy, R.LAST_OPERATION as lastOperation FROM MCPR_TAT_CONFIG_STG R WHERE (R.REQUEST_STATE='S'  or R.REQUEST_STATE='P'  or R.REQUEST_STATE='R')  ORDER BY tat_id DESC limit 1
	</select>
	<select id="getMcprTATData" resultType="McprTATDTO">
		SELECT R.tat_id as taTId, R.upload_W_TAT as uploadWTAT, R.edit_W_TAT as editWTAT,R.status as status , REQUEST_STATE as requestState, CHECKER_COMMENTS checkerComments, CREATED_BY as createdBy, CREATED_ON as createdOn, LAST_UPDATED_BY as lastUpdatedBy, LAST_UPDATED_ON as lastUpdatedOn, LAST_OPERATION as lastOperation FROM MCPR_TAT_CONFIG_STG as R	WHERE tat_id= #{taTId}
	</select>
	<select id="getMcprTATMainData" resultType="McprTATDTO">
		SELECT R.tat_id as taTId, R.upload_W_TAT as uploadWTAT, R.edit_W_TAT as editWTAT,R.status as status  FROM MCPR_TAT_CONFIG as R	WHERE tat_id= #{taTId}
	</select>
	<update id="updateMcprTATStgMaker" >
		UPDATE MCPR_TAT_CONFIG_STG SET upload_W_TAT = #{uploadWTAT}, edit_W_TAT = #{editWTAT}, LAST_UPDATED_BY= #{lastUpdatedBy}, LAST_UPDATED_ON= #{lastUpdatedOn}, STATUS= #{requestState},REQUEST_STATE=#{requestState}, CHECKER_COMMENTS=#{checkerComments},LAST_OPERATION='Edit TAT' WHERE tat_id = #{taTId}
	</update>
	<insert id="saveMcprTATStg" >
		insert into MCPR_TAT_CONFIG_STG(tat_id,upload_W_TAT,edit_W_TAT,created_by,created_on,last_updated_by,status,LAST_UPDATED_ON,REQUEST_STATE, LAST_OPERATION)  VALUES 
			(#{taTId}, #{uploadWTAT}, #{editWTAT} , 
			#{createdBy},#{createdOn},#{lastUpdatedBy}, #{requestState},#{lastUpdatedOn}, 'P', 'Add TAT' )
	</insert>
	<update id="updateMcprTATMain" >
		UPDATE MCPR_TAT_CONFIG SET upload_W_TAT = #{uploadWTAT}, edit_W_TAT = #{editWTAT} WHERE tat_id in( select tat_id from MCPR_TAT_CONFIG where tat_id  = #{taTId})
	</update>
	<insert id="saveMcprTATMain" >
		insert into MCPR_TAT_CONFIG(tat_id,upload_W_TAT,edit_W_TAT,created_by,created_on,last_updated_by,status)  VALUES 
			(#{taTId}, #{uploadWTAT}, #{editWTAT}, 
			#{createdBy},#{createdOn},#{lastUpdatedBy}, #{requestState})
	</insert>
	<update id="updateMcprTATRequestState" >
		UPDATE MCPR_TAT_CONFIG_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} WHERE tat_id  = #{taTId}
	</update>
	<delete id="deleteDiscardedEntry" >
		DELETE FROM MCPR_TAT_CONFIG_STG 	WHERE tat_id= #{taTId}
	</delete>

</mapper>