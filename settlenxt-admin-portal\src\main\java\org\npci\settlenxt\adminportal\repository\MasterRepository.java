package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.portal.common.dto.CityDTO;
import org.npci.settlenxt.portal.common.dto.CountryDTO;
import org.npci.settlenxt.portal.common.dto.LookUpDTO;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.TxnSettlementDTO;
import org.npci.settlenxt.portal.common.repository.BaseMasterRepository;

@Mapper
public interface MasterRepository extends BaseMasterRepository {

	 List<LookUpDTO> getStateMaster();

	 List<LookUpDTO> getIfscCodesList();

	 List<LookUpDTO> getCityMaster();

	 List<CountryDTO> getCountryList();

	 List<CityDTO> getStateCityMasterList(int stateId);

	 String getStateCode(int stateId);

	 String getBinFeatures(String cardType);

	 String getBinCardVariant(String cardType);

	MemberDTO getUniqueBankName(@Param("participantId") String participantId);

	List<String> getListUniqueBankName(@Param("ifscCode") String ifscCode);

	 String getBankNFSCode(String ifscCode);

	 List<TxnSettlementDTO> getFunctionCode();

	 TxnSettlementDTO getFunctionTxnCycle(@Param("funcCode") String funcCode);

	 List<LookUpDTO> getLookUpData(String lookupType);

	 List<LookUpDTO> getCityMasterLkp(@Param("stateId") int stateId, @Param("status") String status);

}
