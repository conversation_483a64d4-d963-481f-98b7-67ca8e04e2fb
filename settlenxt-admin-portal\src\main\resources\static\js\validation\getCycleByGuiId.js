$(document).ready(function () {
	 var userIds=[];
    var cursorPosition = null;
     $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // If first column to be skipped to include the filter for
					// the reasons line check box
                    if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    ({ cursorPosition, userIds } = handleInput(colIdx, api, cursorPosition, userIds, cell, title));
                    }
                });
            $('#tabnew_filter').hide();
            
        },
     // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
        order: [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'IFSC',
                header: 'false',
                title: null,
                sheetName: 'IFSC',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'IFSC' ,
				header:'false', 
				title: null,
				sheetName:'IFSC',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }
        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true
    });

    $("#excelExport").on("click", function () {
        $(".buttons-excel").trigger("click");
    });
    $("#csvExport").on("click", function () {
        $(".buttons-csv").trigger("click");
    });
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });

     
     $("#selectAll").click(function(){
	 		
		 $('#jqueryError4').hide();
		 $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
		 
		 var referenceNoList = document.getElementById("newsIds");
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
        
		 if(userIds.length>0){
	referenceNoList.innerHTML = userIds.length+"     "+"records are selected";
	
     		if( $('#selectAll').is(':checked') )
		   {
			  
			  $("#toggleModalNews").modal('show');	
		  }
		 else
	     {
			 $("#toggleModalNews").modal('hide');
			 
			 }}else{
                var i=0;
                var userId2=[];
                 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
                                            userId2.push(this.value);
                                            i++;
                                        });
                if(userId2.length>0){
                
                
                if(referenceNoListPendings.length>0){
                    referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
                    
                    if( $('#selectAll').is(':checked') ){
                 $("#toggleModalNews").modal('show');
                        
                }
                else{
                   $("#toggleModalNews").modal('hide');
                        
                }
                }}}
            });
     
     
  // Disabling SelectAll option diabling
  	
 	});

function handleInput(colIdx, api, cursorPosition, userIds, cell, title) {
    if (colIdx < actionColumnIndex) {


        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function (_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
                userIds = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
                        userIds.push(this.value);
                        i++;
                    });
                }
                else {
                    userIds = [];
                }

            })
            .on('click', function (e) {
                e.stopPropagation();
            })
            .on('keyup', function (e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    } else {
        $(cell).html(title + '<br> &nbsp;');
    }
    return { cursorPosition, userIds };
}

function back(){
	var productCode = document.getElementById('productCode').innerText;
	var cycleNumber = document.getElementById('cycleNumber').innerText;
	var cycleDate = document.getElementById('cycleDate').innerText;
	
	var refresh = $('#autoReload').is(':checked'); 

	let url = "/fetchCycleStatusDetails";

	var data = "productCode," + productCode + ",cycleNumber," + cycleNumber
			+ ",cycleDate," + cycleDate + ",refresh,"+refresh;
	
	postData(url, data);

}
