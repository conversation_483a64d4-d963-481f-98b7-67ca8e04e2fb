var transitionList = [];
var entry = {};
var index = 0;
var relOperator = '';
var fieldOperator = '';
var name1 = '';
var name2 = '';
var value = '';

$(document).ready(function() {

	relOperator = document.getElementById("relOp").value;
	fieldOperator = document.getElementById("fieldOperator").value;
	name1 = document.getElementById("fieldName").value;
	name2 = document.getElementById("secFieldName").value;
	value = document.getElementById("fieldValue").value;

	setProp(relOperator);

	document.getElementById('transitionSuccessMsg').style.display = 'none';
	document.getElementById('transitionErrorMsg').style.display = 'none';
	document.getElementById('transitionInValidMsg').style.display = 'none';

	$('#entity').on('keyup keypress blur change', function() {
		validateFromCommonVal('entity', true, "SelectionBox", 3, false);
	});

	$('#currState').on('keyup keypress blur change', function() {
		validateFromCommonVal('currState', true, "SelectionBox", 3, false);
	});

	$('#toState').on('keyup keypress blur change', function() {
		validateFromCommonVal('toState', true, "SelectionBox", 6, false);
	});

	$('#operator').on('keyup keypress blur change', function() {
		validateFromCommonVal('operator', true, "Alphabet", 1, true);
	});

	$('#fieldName').on('keyup keypress blur change', function() {
		if (validateFromCommonVal('fieldName', true, "SelectionBox", 15, false)) {
			validateEditTrans();
		}
	});

	$('#secFieldName').on('keyup keypress blur change', function() {
		if (validateFromCommonVal('secFieldName', false, "SelectionBox", 15, false)) {
			validateEditTrans();
		}
	});

	$('#relOp').on('keyup keypress blur change', function() {
		if (validateFromCommonVal('relOp', true, "SelectionBox", 15, false)) {
			var rel = document.getElementById("relOp").value;
			setProp(rel);
			validateEditTrans();
		}
	});

	$('#fieldValue').on('keyup keypress blur change', function() {
		if ((document.getElementById("relOp").value).endsWith("_2")) {
			if (validateFromCommonVal('fieldValue', true, "AlphaNumCommaHyphenUndScStar", 20, false)) {
				validateEditTrans();
			}
		} else {
			if (validateFromCommonVal('fieldValue', true, "NumCommaStar", 20, false)) {
				validateEditTrans();
			}
		}
	});

	$('#fieldOperator').on('keyup keypress blur change', function() {
		if (validateFromCommonVal('fieldOperator', false, "SelectionBox", 15, false)) {
			validateEditTrans();
		}
	});

	$('#tabnew').hide();
});



function setProp(rel) {
	if (rel.endsWith("_1")) {
		document.getElementById('secFieldName').disabled = false;
		document.getElementById('fieldOperator').disabled = false;
	}
	else if (rel.endsWith("_2")) {
		document.getElementById('secFieldName').disabled = true;
		document.getElementById('secFieldName').value = "SELECT";
		document.getElementById('fieldOperator').disabled = false;
	}
	else {
		document.getElementById('secFieldName').disabled = true;
		document.getElementById('fieldOperator').disabled = true;
		document.getElementById('secFieldName').value = "SELECT";
		document.getElementById('fieldOperator').value = "SELECT";
	}
}

function validateEditTrans() {
	if (relOperator != document.getElementById("relOp").value || fieldOperator != document.getElementById("fieldOperator").value
		|| name1 != document.getElementById("fieldName").value || name2 != document.getElementById("secFieldName").value
		|| value != document.getElementById("fieldValue").value) {
		if ($("#editTransition")) {
			$("#editTransition").prop("disabled", false);
		}
	} else {
		if ($("#editTransition")) {
			$("#editTransition").prop("disabled", true);
		}
	}
}

function navigateTo(action) {
	postData(action, "");
}

function goBackTo(action) {
	var data = "id," + document.getElementById('seqid').value + ",reqState," + document.getElementById('reqS').value;
	postData(action, data);
}

function validateTransition() {
	var isValid = true;

	if (!validateFromCommonVal('entity', true, "SelectionBox", 3, false)) {
		isValid = false;
	}
	if (!validateFromCommonVal('currState', true, "SelectionBox", 3, false)) {
		isValid = false;
	}
	if (!validateFromCommonVal('toState', true, "SelectionBox", 6, false)) {
		isValid = false;
	}
	if (!validateFromCommonVal('operator', true, "Alphabet", 1, true)) {
		isValid = false;
	}
	if (!validateFromCommonVal('fieldName', true, "SelectionBox", 15, false)) {
		isValid = false;
	}
	if (!validateFromCommonVal('secFieldName', false, "SelectionBox", 15, false)) {
		isValid = false;
	}
	if (!validateFromCommonVal('relOp', true, "SelectionBox", 15, false)) {
		isValid = false;
	}
	if ((document.getElementById("relOp").value).endsWith("_2")) {
		if (!validateFromCommonVal('fieldValue', true, "AlphaNumCommaHyphenUndScStar", 20, false)) {
			isValid = false;
		}
	} else {
		if (!validateFromCommonVal('fieldValue', true, "NumCommaStar", 20, false)) {
			isValid = false;
		}
	}
	if (!validateFromCommonVal('fieldOperator', false, "SelectionBox", 15, false)) {
		isValid = false;
	}
	return isValid;
}

function saveTransition(type) {
	var isValid = validateTransition();
	if (isValid) {
		if (type === "Add") {
			entry['id'] = index;
		} else {
			entry['id'] = document.getElementById('seqid').value;
		}
		entry['entity'] = document.getElementById('entity').value;
		entry['entityDesc'] = getLookupDesc("entity", "#entity > option");
		entry['currState'] = document.getElementById('currState').value;
		entry['currStateDesc'] = getLookupDesc("currState", "#currState > option");
		entry['toState'] = document.getElementById('toState').value;
		entry['logToState'] = document.getElementById('toState').value;
		entry['toStateDesc'] = getLookupDesc("toState", "#toState > option");
		entry['operator'] = document.getElementById('operator').value;
		entry['operatorDesc'] = getLookupDesc("operator", "#operator > option");
		entry['fieldName'] = document.getElementById('fieldName').value;
		entry['fieldNameDesc'] = getLookupDesc("fieldName", "#fieldName > option");
		entry['secFieldName'] = document.getElementById('secFieldName').value;
		entry['secFieldNameDesc'] = getLookupDesc("secFieldName", "#secFieldName > option");
		entry['relOp'] = document.getElementById('relOp').value;
		entry['relOpDesc'] = getLookupDesc("relOp", "#relOp > option");
		var temp = document.getElementById('fieldValue').value;
		entry['fieldValue'] = $('<div>').text(temp).html();
		entry['fieldOperator'] = document.getElementById('fieldOperator').value;
		entry['fieldOperatorDesc'] = getLookupDesc("fieldOperator", "#fieldOperator > option");
		transitionList.push(entry);

		if (transitionList.length > 0) {
			var id = transitionList.map(e => e.id).indexOf(entry['id']);
			$('#tabnew').hide();
			$('#tabnew').append(`<tr id="tabnew_${id}"><td>` + entry['entityDesc'] + "</td><td>" + entry['currStateDesc'] + "</td><td>"
				+ entry['toStateDesc'] + "</td><td>" + entry['fieldNameDesc'] + "</td><td>"
				+ entry['secFieldNameDesc'] + "</td><td>" + entry['operatorDesc'] + "</td><td>" + entry['relOpDesc'] + "</td><td>" + entry['fieldValue'] + "</td><td>"
				+ entry['fieldOperatorDesc'] + "</td><td><input type='button' class='btn btn-danger remTransition' onclick=removeTransition(" + id + ") value='Remove' /></td></tr>");
			if (type === "Add") {
				$('#tabnew').show()
			}
			index++;
			$("#submitTransition").prop("disabled", false);
		} else {
			$('#tabnew').hide();
		}

		if (type === "Edit") {
			addTransitionRule('/editTransitionRules');
		}

		if (type === 'Add') {
			clearTransition();
		}
	} else {
		$('#transitionInValidMsg').text('Please provide all data properly');
		document.getElementById('transitionInValidMsg').style.display = 'block';
	}
}

function removeTransition(id) {
	if (transitionList.length > 1) {
		$(`#tabnew_${id}`).remove();
	} else {
		$(`#tabnew_${id}`).remove();
		$('#tabnew').hide();
	}
	transitionList.splice(id, 1);
}

function getLookupDesc(id, options) {
	const objectId = document.getElementById(id).value
	if (objectId === "SELECT") {
		return "";
	} else {
		const item = [...document.querySelectorAll(options)].map(element => ({ [element.value]: element.label })).filter(item1 => !("SELECT" in item1));
		return Object.values(item.filter(elem => elem[objectId]).pop())[0];
	}
}

function addTransitionRule(actionUrl) {
	if (transitionList.length > 0) {
		$('#tabnew').show();
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		var linkurl = pathName + actionUrl;
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		$.ajax({
			url: linkurl,
			type: "POST",
			dataType: "json",
			data: JSON.stringify(transitionList),
			"beforeSend": function(xhr) {
				xhr.setRequestHeader('_TransactToken', tokenValue);
			},
			contentType: "application/json; charset=utf-8",
			cache: false,
			success: function(response) {
				if (response.status == "BSUC_0001") {
					document.querySelectorAll('.remTransition').forEach(item => item.disabled = true);
					if (actionUrl.substring(1, 4) === "add") {
						document.getElementById('addTransition').disabled = true;
						document.getElementById('submitTransition').disabled = true;
						document.getElementById('clearTrans').disabled = true;
					} else {
						document.getElementById('editTransition').disabled = true;
					}
					[...document.querySelectorAll("#addEditTransition .row")].splice(0, 3).forEach(item => item.remove())
					document.getElementById('transitionSuccessMsg').style.display = 'block';
				} else {
					document.getElementById('transitionErrorMsg').style.display = 'block';
					$('.panel').hide();
				}
			},
			error: function() {
				document.getElementById('transitionErrorMsg').style.display = 'block';
				$('.panel').hide();
			}
		});
	} else {
		$('#transitionInValidMsg').text('Please add the transition rule first');
		document.getElementById('transitionInValidMsg').style.display = 'block';
	}
}

function clearTransition() {
	document.getElementById('transitionSuccessMsg').style.display = 'none';
	document.getElementById('transitionErrorMsg').style.display = 'none';
	document.getElementById('transitionInValidMsg').style.display = 'none';
	document.getElementById('entity').value = "SELECT";
	document.getElementById('currState').value = "SELECT";
	document.getElementById('toState').value = "SELECT";
	document.getElementById('fieldName').value = "SELECT";
	document.getElementById('secFieldName').value = "SELECT";
	document.getElementById('fieldOperator').value = "SELECT";
	document.getElementById('relOp').value = "SELECT";
	document.getElementById('operator').value = "N";
	document.getElementById('fieldValue').value = "";
	document.getElementById('secFieldName').disabled = true;
	document.getElementById('fieldOperator').disabled = true;
	$("#errentity").find(".error").html('');
	$("#errcurrState").find(".error").html('');
	$("#errtoState").find(".error").html('');
	$("#erroperator").find(".error").html('');
	$("#errfieldName").find(".error").html('');
	$("#errsecFieldName").find(".error").html('');
	$("#errrelOp").find(".error").html('');
	$("#errfieldOperator").find(".error").html('');
	$("#errfieldValue").find(".error").html('');
	entry = {};
}