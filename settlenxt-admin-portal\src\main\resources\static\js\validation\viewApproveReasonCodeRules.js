$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	}

function submitForm(action) {
	var url = action;
 	var data ="" ;
	postData(url, data);
}

function editRcRules(seqId, url) {
	
	var data =  "seqId,"+ escape(seqId);
	postData(url, data);
}

function postAction(seqId,action) {
	var remarks="";
	var data="";
	if(maxLengthTextArea('rejectReason')){
	if ($('#apprej option:selected').val() == "A") {
		if ($("#rejectReason").val() != "") {
			
			
			 remarks = $("#rejectReason").val();
	
			 data = "status," + "A"  + ",remarks,"
					+ remarks+ ",seqId,"+ escape(seqId);
			postData(action, data);
		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else if ($('#apprej option:selected').val() == "R") {
		if ($("#rejectReason").val() != "") {
				
				remarks = $("#rejectReason").val();
				
				data =  "status," + "R"  + ",remarks," + remarks + ",seqId,"+ escape(seqId);

				postData(action, data);
			

		} else {
			$(".remarkMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	} else {
		$(".appRejMust").show();
		$('html, body').animate({ scrollTop: 0 }, 'slow');
		return false;
	}
	}
}