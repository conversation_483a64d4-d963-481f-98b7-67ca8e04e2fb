package org.npci.settlenxt.adminportal.controllers;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import javax.servlet.http.HttpServletRequest;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.FunctionalityService;
import org.npci.settlenxt.adminportal.service.RoleProcessSvc;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.ApprovalDataDTO;
import org.npci.settlenxt.portal.common.dto.FunctionalityDTO;
import org.npci.settlenxt.portal.common.dto.MenuDTO;
import org.npci.settlenxt.portal.common.dto.RoleDTO;
import org.npci.settlenxt.portal.common.dto.RoleFunctionalityDTO;
import org.npci.settlenxt.portal.common.dto.RoleToFuncDTO;
import org.npci.settlenxt.portal.common.dto.UserInfoDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

/**
 * <AUTHOR>
 *
 */

@Controller
public class RoleController extends BaseController {

	@Autowired
	private RoleProcessSvc roleProcessSvc;

	@Autowired
	private MessageSource messageSource;

	@Autowired
	private SessionDTO sessionDto;

	@Autowired
	private FunctionalityService functionalityService;

	private static final String ADD_EDIT_ROLE = "addEditRole";
	private static final String EDIT_ROLE = "editRole";
	private static final String SHOW_ROLES = "showRoles";
	private static final String VIEW_ROLE_INFO = "viewRoleInfo";
	private static final String VIEW_APPROVE_ROLE_INFO = "viewApproveRole";
	private static final String SHOW_ROLE="showRole";
	private static final String STATUS="status";
	private static final String DEACTIVATE="deactivate";

	@PostMapping("/showRole")
	@PreAuthorize("hasAuthority('View Role')")
	public String showRole(Model model,
			@RequestParam(value = "userType", required = false, defaultValue = BaseCommonConstants.USER_TYPE_NPCI_ADMIN) String userType) {
		List<RoleDTO> roleDTOsList = roleProcessSvc.getRoleList(userType);
		model.addAttribute(BaseCommonConstants.SELECT_ROLE_TYPE, roleProcessSvc.getRoleHierarchyList());

		UserInfoDTO userInfoDTO = new UserInfoDTO();
		userInfoDTO.setUserType(userType);
		model.addAttribute(BaseCommonConstants.USER_INFO, userInfoDTO);
		model.addAttribute(BaseCommonConstants.ROLE_LIST, roleDTOsList);
		model.addAttribute(BaseCommonConstants.SHOW_ROLE, CommonConstants.YES);
		model.addAttribute(BaseCommonConstants.ADDROLE, CommonConstants.TRANSACT_YES);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
			model.addAttribute(BaseCommonConstants.SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(BaseCommonConstants.SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}
		return getView(model, SHOW_ROLES);

	}

	@PostMapping("/roleCreation")
	@PreAuthorize("hasAuthority('Add Role')")
	public String roleCreation(Model model) {
		RoleDTO roleDTO = new RoleDTO();
		model.addAttribute(BaseCommonConstants.SELECT_ROLE_TYPE, roleProcessSvc.getRoleHierarchyList());
		model.addAttribute(BaseCommonConstants.ADDROLE, BaseCommonConstants.ADD_ROLE);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, BaseCommonConstants.ADD_ROLE);
		model.addAttribute(BaseCommonConstants.ROLE_INFO, roleDTO);
		List<MenuDTO> moduleList = roleProcessSvc.getModuleList();
		model.addAttribute(BaseCommonConstants.MODULE_LIST, moduleList);
		return getView(model, ADD_EDIT_ROLE);
	}

	@PostMapping("/getFunctionalityList")
	@PreAuthorize("hasAuthority('View Role')")
	public ResponseEntity<Object> getFunctionlityList(@RequestParam("moduleId") Integer moduleId,
			@RequestParam("makChkFlag") String makChkFlag, @RequestParam("roleId") Integer roleId, Model model) {

		List<FunctionalityDTO> functionalityList = null;

		if (moduleId != null && roleId != null) {
			functionalityList = roleProcessSvc.getFunctionalityList(moduleId, roleId, makChkFlag);
		}
		return new ResponseEntity<>(functionalityList, HttpStatus.OK);
	}

	@PostMapping("/asignRoleToFunctionAdd")
	@PreAuthorize("hasAuthority('Add Role')")
	public String asignRoleToFunctionAdd(@ModelAttribute RoleFunctionalityDTO roleFunctionalityDTO,
			@RequestParam("userType") String userType, Model model,Locale locale) {

		try {
			roleFunctionalityDTO.setRoleType(userType);
			roleFunctionalityDTO = roleProcessSvc.addRole(roleFunctionalityDTO);
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_pendingRole", null, locale));
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_ROLE, ex);
		}

		RoleDTO roleDto = roleProcessSvc.getRoleStgInfo(String.valueOf(roleFunctionalityDTO.getRoleId()));
		List<FunctionalityDTO> roleFunctionality = functionalityService
				.getFunctionalityStg(String.valueOf(roleFunctionalityDTO.getRoleId()));
		List<MenuDTO> moduleList = roleProcessSvc.getModuleList();
		model.addAttribute(BaseCommonConstants.OPTION_FUNCTIONALITY, roleFunctionality);

		model.addAttribute(BaseCommonConstants.ROLE_INFO_DTO, roleDto);
		model.addAttribute(BaseCommonConstants.ADDROLE, BaseCommonConstants.ADD_ROLE);
		model.addAttribute(BaseCommonConstants.MODULE_LIST, moduleList);
		model.addAttribute(CommonConstants.REQ_TYPE, userType);
		model.addAttribute(BaseCommonConstants.ROLE_FUNCTIONALITY, roleDto);
		model.addAttribute(CommonConstants.ROLE_SUBMITTED, CommonConstants.YES_FLAG);

		return getView(model, EDIT_ROLE);

	}

	@PostMapping("/retrieveAllFunctionalityList")
	@PreAuthorize("hasAuthority('View Role')")
	public ResponseEntity<Object> retrieveAllFunctionalityList(Model model,
			@RequestParam("makChkFlag") String makChkFlag) {

		int count = roleProcessSvc.retrieveAllFunctionalityList(makChkFlag).size();
		JsonObject jsonResponse = new JsonObject();
		if (count > 0) {
			jsonResponse.addProperty(BaseCommonConstants.COUNT, count);

		} else {
			jsonResponse.addProperty(CommonConstants.STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	public void fetchRoleFunctionality(String rid, RoleDTO roleDTO, Model model) {

		if (null != rid) {
			List<MenuDTO> moduleList = roleProcessSvc.getModuleList();
			RoleToFuncDTO roleToFuncDTO = roleProcessSvc.fetchRoleFunctionality(rid, roleDTO);
			List<FunctionalityDTO> assignFunctionality = roleToFuncDTO.getFunctionalityDTOs();
			model.addAttribute(BaseCommonConstants.ROLE_INFO_DTO, roleDTO);
			model.addAttribute(BaseCommonConstants.ROLE_FUNCTIONALITY, roleDTO);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.TRANSACT_YES);
			model.addAttribute(BaseCommonConstants.OPTION_FUNCTIONALITY, assignFunctionality);
			model.addAttribute(BaseCommonConstants.MODULE_LIST, moduleList);
			model.addAttribute(BaseCommonConstants.ROLE_NAME, roleDTO.getRoleName());
			model.addAttribute(CommonConstants.REQ_TYPE, roleDTO.getUserType());
		}

	}

	@PostMapping("/editRole")
	@PreAuthorize("hasAuthority('Edit Role')")
	public String asignRoleToFunction(@ModelAttribute RoleFunctionalityDTO roleFunctionalityDTO,
			@RequestParam("requestState") String requestState, Model model,Locale locale) {

		RoleDTO roleDto = new RoleDTO();
		try {

			if (!requestState.equalsIgnoreCase(CommonConstants.REQUEST_STATE_REJECTED)) {
				model.addAttribute(CommonConstants.ROLE_SUBMITTED, CommonConstants.YES_FLAG);
			}

			roleFunctionalityDTO = roleProcessSvc.editRole(roleFunctionalityDTO);

			roleDto = roleProcessSvc.getApprovedRoleById(String.valueOf(roleFunctionalityDTO.getRoleId()));
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_pendingRole", null, locale));
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, EDIT_ROLE, ex);
		}
		List<MenuDTO> menuList = roleProcessSvc.getModuleList();
		model.addAttribute(BaseCommonConstants.ROLE_INFO_DTO, roleDto);
		model.addAttribute(BaseCommonConstants.ROLE_FUNCTIONALITY, roleFunctionalityDTO);
		model.addAttribute(BaseCommonConstants.OPTION_FUNCTIONALITY, roleFunctionalityDTO.getFunctionalityDTOs());
		model.addAttribute(BaseCommonConstants.MODULE_LIST, menuList);

		return getView(model, EDIT_ROLE);

	}

	@PostMapping("/rolePendingForApproval")
	@PreAuthorize("hasAuthority('View Role')")
	public String rolePendingForApproval(Model model,
			@RequestParam(name = "userType", required = false, defaultValue = BaseCommonConstants.USER_TYPE_NPCI_ADMIN) String userType) {

		return prepareRolePendingForApproval(model, userType);
	}

	private String prepareRolePendingForApproval(Model model, String userType) {
		try {
			model.addAttribute(BaseCommonConstants.SELECT_ROLE_TYPE, roleProcessSvc.getRoleHierarchyList());
			List<RoleDTO> pendingRoleList = roleProcessSvc.getPendingRoles(userType);
			model.addAttribute(BaseCommonConstants.ROLE_PENDING_LIST, pendingRoleList);
			model.addAttribute(BaseCommonConstants.ROLE_APP_PENDING, CommonConstants.YES);
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
				model.addAttribute(BaseCommonConstants.SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(BaseCommonConstants.SHOW_CHECKBOX, CommonConstants.YES_FLAG);
			}

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_ROLES, ex);
		}
		UserInfoDTO userInfoDto = new UserInfoDTO();
		userInfoDto.setUserType(userType);
		model.addAttribute(BaseCommonConstants.USER_INFO, userInfoDto);

		return getView(model, SHOW_ROLES);
	}

	@PostMapping("/getPendingRole")
	@PreAuthorize("hasAuthority('View Role')")
	public String getPendingRole(@RequestParam("rid") String roleId, Model model) {
		ApprovalDataDTO approvalDataDto = new ApprovalDataDTO();
		RoleDTO roleDto = new RoleDTO();
		List<FunctionalityDTO> functionalityList = new ArrayList<>();
		try {
			roleDto = roleProcessSvc.getRoleStgInfo(roleId);
			functionalityList = functionalityService.getFunctionalityStg(roleId);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_ROLE_INFO, ex);
		}
		model.addAttribute(BaseCommonConstants.REQUEST_INFO, approvalDataDto);
		model.addAttribute(BaseCommonConstants.ROLE_INFO_DTO, roleDto);
		model.addAttribute(BaseCommonConstants.FUNCTIONALITY_LIST, functionalityList);
		return getView(model, VIEW_APPROVE_ROLE_INFO);
	}

	@PostMapping("/getRole")
	@PreAuthorize("hasAuthority('View Role')")
	public String getRole(@RequestParam("rid") String rid, Model model) {
		RoleDTO roleDto = new RoleDTO();
		try {
			roleDto = roleProcessSvc.getApprovedRoleById(rid);
			roleProcessSvc.checkRoleStatusForDeactivate(roleDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, EDIT_ROLE, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
		}
		model.addAttribute(BaseCommonConstants.ROLE_INFO_DTO, roleDto);
		if (BaseCommonConstants.ROLE_STATUS_ACTIVE.equals(roleDto.getStatus())) {
			model.addAttribute(DEACTIVATE, DEACTIVATE);
		}
		fetchRoleFunctionality(rid, roleDto, model);
		return getView(model, EDIT_ROLE);
	}

	@PostMapping("/approveRoleStatus")
	@PreAuthorize("hasAuthority('Approve Role')")
	public String approveRoleStatus(@RequestParam("roleId") String roleId, @RequestParam("status") String status,
			@RequestParam("crtuser") String crtuser, @RequestParam("remarks") String remarks, Model model,
			HttpServletRequest request) {
		try {
			RoleDTO roleDTO = roleProcessSvc.updateApproveOrRejectRole(roleId, status, remarks);
			checkRoleApproveStatus(roleDTO, model, request);
			model.addAttribute(BaseCommonConstants.ROLE_INFO_DTO, roleDTO);
			fetchRoleFunctionality(String.valueOf(roleDTO.getRoleId()), roleDTO, model);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_ROLE_INFO, ex);
		}
		return getView(model, VIEW_APPROVE_ROLE_INFO);
	}

	@PostMapping("/discardRejectedRoleEntry")
	@PreAuthorize("hasAuthority('Edit Role')")
	public String discardRejectedRoleEntry(@RequestParam("roleId") String roleId, Model model,
			HttpServletRequest request) {
		try {
			RoleDTO roleDTO = roleProcessSvc.discardRejectedRoleEntry(roleId);

			roleDTO.setRequestState(CommonConstants.REQUEST_STATE_DISCARDED);
			model.addAttribute(BaseCommonConstants.ROLE_INFO_DTO, roleDTO);
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("am.msg.roleEntryDiscarded", null, request.getLocale()));
			fetchRoleFunctionality(String.valueOf(roleDTO.getRoleId()), roleDTO, model);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_ROLE_INFO, ex);
		}
		return getView(model, VIEW_APPROVE_ROLE_INFO);
	}

	private void checkRoleApproveStatus(RoleDTO roleDTO, Model model, HttpServletRequest request) {
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(roleDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("am.msg.roleApproved", null, request.getLocale()));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_roleRejected", null, request.getLocale()));
		}
	}

	@PostMapping("/checkDuplicateRoleName")
	@PreAuthorize("hasAuthority('Add Role')")
	public ResponseEntity<Object> checkDuplicateRoleName(Model model, @RequestParam("roleName") String roleName) {

		boolean result = roleProcessSvc.validateRoleName(roleName);

		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_SUCCESS);

		} else {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/deactivateRole")
	@PreAuthorize("hasAuthority('Edit Role')")
	public String deactivateRole(@RequestParam("rid") String rid, Model model, HttpServletRequest request) {

		RoleDTO roleDto = new RoleDTO();

		try {

			roleDto = roleProcessSvc.getApprovedRoleById(rid);
			roleProcessSvc.checkRoleAssignedToUser(roleDto);
			roleProcessSvc.deactivateRole(roleDto);
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_roleDeactivationPending", null, request.getLocale()));
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, EDIT_ROLE, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
		}

		model.addAttribute(BaseCommonConstants.ROLE_INFO_DTO, roleDto);
		fetchRoleFunctionality(rid, roleDto, model);
		return getView(model, EDIT_ROLE);
	}

	@PostMapping("/approveBulkRoleStatus")
	@PreAuthorize("hasAuthority('Approve Role')")
	public String approveBulkRoleStatus(@RequestParam("roleIdList") String roleIdList,
			@RequestParam("status") String status, @RequestParam("userType") String userType,
			@RequestParam("remarks") String remarks, Model model, HttpServletRequest request) {
		try {
			RoleDTO roleDTO = roleProcessSvc.updateApproveOrRejectBulkRole(roleIdList, status, remarks);
			checkRoleApproveBulkStatus(roleDTO, model, request);
			model.addAttribute(BaseCommonConstants.ROLE_INFO_DTO, roleDTO);
			
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_ROLE, ex);
		}
		return prepareRolePendingForApproval(model, userType);

	}

	private void checkRoleApproveBulkStatus(RoleDTO roleDTO, Model model, HttpServletRequest request) {
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(roleDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("am.msg.roleApproved", null, request.getLocale()));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_roleRejected", null, request.getLocale()));
		}
	}

	@PostMapping("/viewRole")
	@PreAuthorize("hasAuthority('View Role')")
	public String viewRole(@RequestParam("rid") String rid, Model model) {
		RoleDTO roleDto = new RoleDTO();
		List<FunctionalityDTO> functionalityList = new ArrayList<>();
		try {
			roleDto = roleProcessSvc.getRoleStgInfo(rid);
			functionalityList = functionalityService.getFunctionalityStg(rid);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_ROLE_INFO, ex);
		}
		model.addAttribute(BaseCommonConstants.ROLE_INFO_DTO, roleDto);
		model.addAttribute(BaseCommonConstants.FUNCTIONALITY_LIST, functionalityList);
		return getView(model, VIEW_ROLE_INFO);
	}

}
