package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class CurrencyMasterDTO {

	private int currencyId;
	private String currencyCode;
	private String currencyDescription;
	private String currencyAlpha;
	private int currencyDecimalPosition;
	private String checkerComments;
	private String lastOperation;
	private String requestState;
	private String addEditFlag;

	private String status;
	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private String userName;
}
