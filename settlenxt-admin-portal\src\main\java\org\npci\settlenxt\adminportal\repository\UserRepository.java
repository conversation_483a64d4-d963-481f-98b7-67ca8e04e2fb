/**
 * 
 */
package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.npci.settlenxt.portal.common.dto.UserDTO;
import org.npci.settlenxt.portal.common.repository.BaseUserRepository;

@Mapper
public interface UserRepository extends BaseUserRepository {

	 List<UserDTO> getUserList(SearchCriteriaDTO searchCriteriaDTO);

	 List<UserDTO> getUsersPendingForApproval(@Param("userType") String userType,
			@Param("userName") String userName, @Param("requestStateList") List<String> requestStateList);

	 String getUserAccessHierarchy(@Param("userType") String userType);

	 List<CodeValueDTO> getUserAccessHierarchyList(@Param("userTypeList") List<String> userTypeList);

	 List<UserDTO> getBulkUserInfoByUserIdList(@Param("list") List<Integer> list);

	 int checkIfDashBoardFuncExists(@Param("funcName") String funcName, @Param("userId") int userId);

}
