[{"messageFormat": "DMS", "messageType": "Presentment (<PERSON> Auth)", "direction": "OUT", "mti": "1240", "funcCode": "200", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd"], "optionalFields": ["nCrdHldrITPan", "nCrdAcpAddAdrs", "nCrdAcpZipCd", "nMerTelNum", "nAddData", "nUID", "nATD", "nApprvlCd"], "conditionalFields": ["nAmtAdd", "nICCData"]}}, {"messageFormat": "BOTH", "messageType": "Re-presentment (Full/Partial)", "direction": "OUT", "mti": "1240", "funcCode": "205", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nMemMsgTxt", "nDocInd", "nFulParInd"], "optionalFields": ["nIntrnTrackNum", "nApprvlCd"], "conditionalFields": ["nAmtAdd"]}}, {"messageFormat": "DMS", "messageType": "Offline Presentment (Without Auth)", "direction": "OUT", "mti": "1240", "funcCode": "260", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nProcCd", "nPosEntMode", "nPosCondCd", "nPosDataCd", "nCrdAcpBussCd", "nActnCd", "nCrdAcpIDCd", "nCrdAcpNm", "nCrdAcpLoc", "nCrdAcpCity", "nCrdAcpStNm", "nCrdAcpCtryCd", "nServCd"], "optionalFields": ["nApprvlCd", "nAddData", "nATD"], "conditionalFields": ["nICCData"]}}, {"messageFormat": "BOTH", "messageType": "Re-presentment Acceptance", "direction": "OUT", "mti": "1240", "funcCode": "261", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nTxnOrgInstCd"], "optionalFields": ["nIntrnTrackNum", "nApprvlCd"], "conditionalFields": []}}, {"messageFormat": "BOTH", "messageType": "Refund", "direction": "OUT", "mti": "1240", "funcCode": "262", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nFulParInd"], "optionalFields": ["nIntrnTrackNum", "nApprvlCd", "nMsgRsnCd"], "conditionalFields": ["nAmtAdd"]}}, {"messageFormat": "DMS", "messageType": "Offline Refund", "direction": "OUT", "mti": "1240", "funcCode": "263", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nCrdAcpZipCd", "nMerTelNum", "nProcCd", "nPosEntMode", "nPosCondCd", "nPosDataCd", "nCrdAcpBussCd", "nActnCd", "nCrdAcpIDCd", "nCrdAcpNm", "nCrdAcpLoc", "nCrdAcpCity", "nCrdAcpStNm", "nCrdAcpCtryCd"], "optionalFields": ["nApprvlCd", "nMemMsgTxt"], "conditionalFields": []}}, {"messageFormat": "SMS", "messageType": "SMS Tip and Surcharge Adjustment", "direction": "OUT", "mti": "1240", "funcCode": "265", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd"], "optionalFields": ["nAddData", "nApprvlCd"], "conditionalFields": []}}, {"messageFormat": "BOTH", "messageType": "ONUS Transaction", "direction": "OUT", "mti": "1240", "funcCode": "999", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nDtTmTrns", "nProcCd", "nPosEntMode", "nPosCondCd", "nPosDataCd", "nCrdAcpBussCd", "nCrdAcpIDCd", "nProdCd", "nCrdAcpNm", "nCrdAcpLoc", "nCrdAcpCity", "nCrdAcpStNm", "nCrdAcpCtryCd"], "optionalFields": ["nUID", "nCrdHldrITPan", "nCrdAcpAddAdrs", "nCrdAcpZipCd", "nMerTelNum", "nApprvlCd"], "conditionalFields": ["nAmtAdd"]}}, {"messageFormat": "DMS", "messageType": "Presentment Reversal", "direction": "OUT", "mti": "1420", "funcCode": "420", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nTxnOrgInstCd"], "optionalFields": ["nAddData", "nApprvlCd", "nMsgRsnCd"], "conditionalFields": []}}, {"messageFormat": "BOTH", "messageType": "Refund Chargeback (Full/Partial)", "direction": "OUT", "mti": "1442", "funcCode": "264", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nMsgRsnCd", "nFulParInd"], "optionalFields": ["nIntrnTrackNum", "nMemMsgTxt", "nDocInd", "nContNum", "nApprvlCd"], "conditionalFields": ["nAmtAdd"]}}, {"messageFormat": "BOTH", "messageType": "Chargeback (Full/Partial)", "direction": "OUT", "mti": "1442", "funcCode": "450", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nMsgRsnCd", "nMemMsgTxt", "nDocInd", "nFulParInd"], "optionalFields": ["nIntrnTrackNum", "nApprvlCd"], "conditionalFields": ["nAmtAdd"]}}, {"messageFormat": "BOTH", "messageType": "Chargeback Acceptance", "direction": "OUT", "mti": "1442", "funcCode": "470", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nTxnOrgInstCd"], "optionalFields": ["nIntrnTrackNum", "nApprvlCd"], "conditionalFields": []}}, {"messageFormat": "BOTH", "messageType": "Member Fund Collection", "direction": "OUT", "mti": "1740", "funcCode": "700", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nPAN", "nAcqInstCd", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nMsgRsnCd", "nCaseNum", "nARD"], "optionalFields": ["nIntrnTrackNum", "nMemMsgTxt"], "conditionalFields": ["nApprvlCd", "nCrdAcptTrmId"]}}, {"messageFormat": "SMS", "messageType": "Member Fund Disbursement", "direction": "OUT", "mti": "1740", "funcCode": "701", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nPAN", "nAcqInstCd", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nMsgRsnCd", "nCaseNum", "nARD"], "optionalFields": ["nIntrnTrackNum", "nMemMsgTxt"], "conditionalFields": ["nApprvlCd", "nCrdAcptTrmId"]}}, {"messageFormat": "SMS", "messageType": "Credit Adjustment", "direction": "OUT", "mti": "1740", "funcCode": "762", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nMsgRsnCd"], "optionalFields": ["nApprvlCd"], "conditionalFields": ["nAmtAdd"]}}, {"messageFormat": "SMS", "messageType": "Debit Adjustment", "direction": "OUT", "mti": "1740", "funcCode": "763", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nAmtTxn", "nCcyCdTxn", "nTxnOrgInstCd", "nMsgRsnCd", "nDocInd"], "optionalFields": ["nApprvlCd"], "conditionalFields": ["nAmtAdd"]}}, {"messageFormat": "SMS", "messageType": "Retrieval Request", "direction": "OUT", "mti": "1644", "funcCode": "603", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nARD", "nAcqInstCd", "nCrdAcptTrmId", "nTxnOrgInstCd", "nMsgRsnCd"], "optionalFields": ["nIntrnTrackNum", "nApprvlCd"], "conditionalFields": []}}, {"messageFormat": "BOTH", "messageType": "Header Message", "direction": "OUT", "mti": "1644", "funcCode": "670", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmFlGen", "nMemInstCd", "nUnFlNm", "nProdCd", "nSetBIN", "nFlCatg", "nVerNum"], "optionalFields": [], "conditionalFields": []}}, {"messageFormat": "BOTH", "messageType": "Trailer Message", "direction": "OUT", "mti": "1644", "funcCode": "671", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nUnFlNm", "nTxnCnt", "nRnTtlAmt"], "optionalFields": [], "conditionalFields": []}}, {"messageFormat": "DMS", "messageType": "Void", "direction": "OUT", "mti": "8144", "funcCode": "266", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nDtTmLcTxn", "nPAN", "nRRN", "nAcqInstCd", "nCrdAcptTrmId", "nTxnOrgInstCd"], "optionalFields": ["nApprvlCd"], "conditionalFields": []}}, {"messageFormat": "BOTH", "messageType": "NPCI Fee Collection", "direction": "OUT", "mti": "1740", "funcCode": "760", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nCcyCdTxn", "nTxnDesInstCd", "nFeeTpCd", "nAmtSet", "nFeeAmt", "nFeeDCInd", "nFeeCcy", "nCcyCdSet", "nMsgRsnCd", "nAmtTxn", "nPAN", "nAcqInstCd", "nMemMsgTxt"], "optionalFields": ["nTxnOrgInstCd"], "conditionalFields": []}}, {"messageFormat": "BOTH", "messageType": "NPCI Fee Disbursement", "direction": "OUT", "mti": "1740", "funcCode": "761", "fields": {"requiredFields": ["nMTI", "nFunCd", "nRecNum", "nCcyCdTxn", "nTxnDesInstCd", "nFeeTpCd", "nAmtSet", "nFeeAmt", "nFeeDCInd", "nFeeCcy", "nCcyCdSet", "nMsgRsnCd", "nAmtTxn", "nPAN", "nAcqInstCd", "nMemMsgTxt"], "optionalFields": ["nTxnOrgInstCd"], "conditionalFields": []}}]