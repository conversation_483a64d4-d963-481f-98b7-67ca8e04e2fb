$(document).ready(function() {
	if ($('#significance').val() == "M") {
	
	 $('#feeMajorId').on('keyup keypress blur change',function () {
	
			if (!validateFeeMajorID('errfeeMajorId')) {
				//empty
			} else {
				if($('#addFlow').val()=='Y')
	        	{
					checkDupMajororMinorId();}
	        	else if($('#editFlow').val()=='Y')
	        	{
	        		if($('#discardRejec').val()=='R'){
	        			var fee=$('#fee').val();
	        			var fee2=$('#feeMajorId').val();
	        	if(fee!=fee2)
	        	{
	        		checkDupMajororMinorId();}}}
			}
		});
		
		$('#cardType').change(function(){
		
			if($('#priority').val()!=0){
			
				//empty	
			}
		
		});


$('#priority').on('keyup keypress blur change',function () {
  
	
	
	if (!validatePriority('errPriority')) {
		//empty
	} else {
		if($('#addFlow').val()=='Y')
    	{
			checkPriorityMapwithMajorId();}
    	else if($('#editFlow').val()=='Y')
    	{
    		
    			var priority=$('#priorityCache').val();
    			var priority2=$('#priority').val();
    	if(priority!=priority2)
    	{
    		checkPriorityMapwithMajorId();}}
	}
	
	});

$('#schemeCode').on('keyup keypress blur change',function () {
        validateFromCommonVal('schemeCode',true,'SelectionBox',4,false);
    });
$('#productCode').on('keyup keypress blur change',function () {
        validateFromCommonVal('productCode',true,'SelectionBox',4,false);
    });
    
$('#cardType').on('keyup keypress blur change',function () {

        validateFromCommonVal('cardType',true,'SelectionBox',4,false);
    });
    
$('#cardBrand').on('keyup keypress blur change',function () {
        validateFromCommonVal('cardBrand',true,'SelectionBox',4,false);
    }); 
$('#funCd').on('keyup keypress blur change',function () {
        validateFromCommonVal('funCd',true,'SelectionBox',4,false);
    }); 

	}
	else {
$('#feeMinorId').on('keyup keypress blur change',function () {
		
			
			
			if (!validateFeeMinorID('errfeeMinorId')) {
					//empty
			} else {
				
				if($('#addFlow').val()=='Y')
	        	{
					checkDupMajororMinorId();}
	        	else if($('#editFlow').val()=='Y')
	        	{
	        		if($('#discardRejec').val()=='R'){
	        			var fee=$('#fee').val();
	        			var fee2=$('#feeMinorId').val();
	        	if(fee!=fee2)
	        	{
	        		checkDupMajororMinorId();}}}
				
			}
		});

	}
	
	
	

	$('#clearFeeMajorMinor').click(function()
			{
		
		if ($('#significance').val() == "M") {
		
		
		$(".error").each(function() {
		if($(this).text().trim().length > 0) 
		{
			
			$(this).empty();
		
		}
		}); 
  		 	$("#errConfigs").hide();
	 document.getElementById("schemeCode").value = "SELECT";
		 document.getElementById("feeMajorId").value = "";
	 document.getElementById("productCode").value = "SELECT";	
	 document.getElementById("cardType").value = "SELECT";
	 document.getElementById("cardBrand").value = "SELECT";
	 document.getElementById("funCd").value = "SELECT";
	 document.getElementById("priority").value = "0";
	}
		else  {
			$('#feeMinorId').val("")
			 $('#errfeeMinorId').html('');
			
		}
		
		
		
		  var selectedFields =[];
		selectedFields = document.getElementsByClassName("selectedFields");
		var fieldCodeList=[];
		
		for(var i of selectedFields){
		  var fieldCode=i.id;
		  fieldCode=fieldCode.substring(6);
		  fieldCodeList.push(fieldCode);
		  }
	 for(var j of fieldCodeList){
		  removeSelectedField(j)
		}
      
      
         
		
		
	
			});


$('form')
    .each(function(){
        $(this).data('serialized', $(this).serialize())
    })
    .on('change input', function(){
        $(this)             
            .find('input:submit, button:submit')
                .prop('disabled', $(this).serialize() == $(this).data('serialized'))
        ;

        $('#forms').prop('disabled', false);
        $('#submitButton').prop('disabled', false);
     })

     
     
     if (($('#showButton').val() != "YES")) {
     $('select').on('change', function() {
    	 $('#forms').prop('disabled', false);
    	   $('#submitButton').prop('disabled', false);
});
$('#tabnew').on('click', function(){
   $('#forms').prop('disabled', false);
   $('#submitButton').prop('disabled', false);
});

$('#tabnew1').on('click', function(){
   $('#forms').prop('disabled', false);
   $('#submitButton').prop('disabled', false);
});
}
   



if (($('#showButton').val() == "YES")) {


	$("input").prop('disabled', true);
	$("select").prop('disabled', true);
	
	}
	
	
	
	$("form").submit(function(event) {

    var feeConfigs = getValues();
    var flag4=validatefield2();

	if(feeConfigs.length ==0 || !flag4  ) { 
      event.preventDefault();
    }
});



$('#submitButton').prop('disabled', true);

$('#tabnew').on('click', function(){
    $('#submitButton').prop('disabled', false);
    });
    
    $('#removeall').on('click', function(){
    $('#submitButton').prop('disabled', false);
    });
$('#selectall').on('click', function(){
    $('#submitButton').prop('disabled', false);
    });  
	

});

function addOrUpdateFee(action) {

var data="";
var feeConfigs = getValues();
	
if (feeConfigs.length == 0) {
$("#errConfigs").show();
var error="Please insert  Fields/Values for FeeConfig Details";
document.getElementById("errConfigs").innerHTML=error.fontcolor("red");

return false;
}

var validation=validatefield2();

if(!validation){
return false;
}

	if ($('#significance').val() == "M") {
			 data = "feeMajorId," + $('#feeMajorId').val() +",oldFeeMajorId," + $('#oldFeeMajorId').val()+ ",feeConfigId," + $('#feeConfigId').val() + ",requestId," + $('#requestId').val()  + ",schemeCode," + $('#schemeCode').val() + ",productCode," + $('#productCode').val() +
			",cardType," + $('#cardType').val() + ",cardBrand," + $('#cardBrand').val() + ",priority," + $('#priority').val() +
			",funCd," + $('#funCd').val() + ",significance," +
			$('#significance').val() + ",status," + $('#status').val() + ",feeConfigs," + feeConfigs;
	}
	else {

	
		 data = "feeMinorId," + $('#feeMinorId').val() + ",feeConfigId," + $('#feeConfigId').val() + ",requestState," + $('#requestState').val() +  ",oldFeeMinorId," + $('#oldFeeMinorId').val() + ",significance," +
			$('#significance').val() + ",feeConfigs," + feeConfigs ;

		
	}
	
	postData(action, data);
}



function getValues() {
	var fieldArr = document.getElementsByClassName("selectedFieldName");
	var selectedAllFieldValues = "";
	var isFirstRow = true;
	var isValid = true;

	for (var i of fieldArr) {
		if (!isFirstRow) {
			selectedAllFieldValues += '#';
		}
		var fldName = i.id.substring(9);
	
		var validError =$('#fieldValue' + fldName).val();
		
		
		 var isempty = validError.trim();
		 
		if(isempty == ""){
		
		isValid= false;
		break;
		}
		
		selectedAllFieldValues += fldName + '|' +
			$('#relationalOperator' + fldName).val() + '|' + $('#status' + fldName).val() + '|' +escape($('#fieldValue' + fldName).val()) +'|';
		isFirstRow = false;
	}
	if(!isValid){


	selectedAllFieldValues="";
	}
	return selectedAllFieldValues;
}
function navigateTo(action,type) {

	
	var data =  "status,"
		+ status+ ",significance,"
		+ type;
	postData(action, data);
}





function userAction(action) {
	var data="";
	if ($('#significance').val() == "M") {
		 data = "feeMajorId," + $('#feeMajorId').val() + ",status,"
			+ status;
		postData(action, data);
	} else {
		 data = "feeMinorId," + $('#feeMinorId').val()  + ",status,"
			+ status;
		postData(action, data);
	}
}



function validateAddFee(action) {

	$('.jqueryError').text("");
	$('.jqueryError').hide();
	var check = false;



	if ($('#significance').val() == "M") {


		
		if (!validateFeeMajorID('errfeeMajorId')) {
			check = true;
		} else {
			if($('#addFlow').val()=='Y')
        	{
				checkDupMajororMinorId();
				if(callBackFeeMajorMinorId){
	            	
	            	check = true;	
	            }
        	}
        	else if($('#editFlow').val()=='Y')
        	{
        		if($('#discardRejec').val()=='R'){
        			var fee=$('#fee').val();
        			var fee2=$('#feeMajorId').val();
        	if(fee!=fee2)
        	{
        		checkDupMajororMinorId();
        		
        		if(callBackFeeMajorMinorId){
                	
                	check = true;	
                }
        	}}}
		}

		
		if (!validateFromCommonVal('schemeCode',true,'SelectionBox',4,false)) {
		check = true;
		}

		if (!validateFromCommonVal('productCode',true,'SelectionBox',4,false)) {
			check = true;
		}

		if (!validateFromCommonVal('cardType',true,'SelectionBox',4,false)) {
		check = true;
		}

		if (!validateFromCommonVal('cardBrand',true,'SelectionBox',4,false)) {
			check = true;
		}

		if (!validateFromCommonVal('funCd',true,'SelectionBox',4,false)) {
		check = true;
		}

		if (!validatePriority('errPriority')) {
			check = true;
		}
		else{
			checkPriorityMapwithMajorId();
if(callBackFeePriority){
            	
            	check = true;	
            }	
		}
		
		

	} else {
		
		if (!validateFeeMinorID('errfeeMinorId')) {
			check = true;
		} else {
			
			if($('#addFlow').val()=='Y')
        	{
				checkDupMajororMinorId();
if(callBackFeeMajorMinorId){
                	
                	check = true;	
                }	
        	
        	}
        	else if($('#editFlow').val()=='Y')
        	{
        		if($('#discardRejec').val()=='R'){
        			var fee3=$('#fee').val();
        			var fee4=$('#feeMinorId').val();
        		if(fee3!=fee4)
        		{
        				checkDupMajororMinorId();
        		
						if(callBackFeeMajorMinorId){
                	
                			check = true;	
                	}
        		}
        	
        	}
        	
        	}
			
		}
	
	}
	
	


	if (!check) {
		addOrUpdateFee(action);
	} else {
		return false;
	}
}


function validateFeeMajorID(msgID) {

	var feeMajorId = (document.getElementById("feeMajorId").value).replace(
		/(^\s*)|(\s*$)/g, '');
	var errfeeMajorId = document.getElementById(msgID);
	var regEx = /^[1-9]\d*$/;
	if (feeMajorId == "") {
		errfeeMajorId.className = 'error';
		errfeeMajorId.innerHTML = "Major Id is mandatory and should be numeric";
		return false;
	}
	else if (!regEx.test(feeMajorId)) {
		errfeeMajorId.className = 'error';
		errfeeMajorId.innerHTML = "Major Id is mandatory and should be numeric  ";
		return false;
	}
	else {
		errfeeMajorId.className = 'error';
		errfeeMajorId.innerHTML = "";
	}
	return true;

}
function validateFeeMinorID(msgID) {

	var feeMinorId = (document.getElementById("feeMinorId").value).replace(
		/(^\s*)|(\s*$)/g, '');
	var errfeeMinorId = document.getElementById(msgID);
	var regEx = /^[1-9]\d*$/;
	if (feeMinorId == "") {
		errfeeMinorId.className = 'error';
		errfeeMinorId.innerHTML = "Minor Id is mandatory and should be numeric";
		return false;
	}
	else if (!regEx.test(feeMinorId)) {
		errfeeMinorId.className = 'error';
		errfeeMinorId.innerHTML = "Minor Id is mandatory and should be numeric ";
		return false;
	}
	else {
		errfeeMinorId.className = 'error';
		errfeeMinorId.innerHTML = "";
	}
	return true;

}
function validatePriority(msgID) {
	var priority = (document.getElementById("priority").value).replace(
		/(^\s*)|(\s*$)/g, '');
	var errPriority = document.getElementById(msgID);
	var regEx = /^\d*$/i;

	if (priority == "") {
		errPriority.className = 'error';
		errPriority.innerHTML = "Priority is mandatory and should be numeric";
		return false;
	}
	else if (!regEx.test(priority)) {
		errPriority.className = 'error';
		errPriority.innerHTML = "Priority is mandatory and should be numeric ";
		return false;
	}
	else {
		errPriority.className = 'error';
		errPriority.innerHTML = "";
	}
	return true;



}


function checkDupMajororMinorId() {

    var validfeeConfigId = false;
    var significance=$('#significance').val();
  var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	if(significance=="M"){
		
		var feeConfigId=document.getElementById('feeMajorId').value;
	
        var errfeeConfigId = document.getElementById('errfeeMajorId');
     
        if (feeConfigId) {
  
            $.ajax({
                url : "checkDupFeeMajorMinorId",
                type : "POST",
                dataType : "json",
                data: {"feeConfigId" : feeConfigId,
                	"significance" :significance,
                        "_TransactToken" : tokenValue},
                success : function(response){

                    validfeeConfigId = afterSuccess(response, validfeeConfigId, errfeeConfigId);
                },
                error : function() {
                	errfeeConfigId.className = 'error';
                	errfeeConfigId.innerHTML = "";
                	callBackMajorMinorId(false);
                }
            });

        } else {
        	validfeeConfigId = false;
        }
	}
	else{
		var feeConfigId1=document.getElementById('feeMinorId').value;
		
        var errfeeConfigId1 = document.getElementById('errfeeMinorId');
        if (feeConfigId1 != "") {

            $.ajax({
                url : "checkDupFeeMajorMinorId",
                type : "POST",
                dataType : "json",
                data: {"feeConfigId" : feeConfigId1,
                	"significance" :significance,
                        "_TransactToken" : tokenValue},
                success : function(response){

                    if (response.status == "BSUC_0001") {
                    	validfeeConfigId = true;
                    	errfeeConfigId1.className = 'error';
                    	errfeeConfigId1.innerHTML = "FeeMinorId Already Exists";
                    	callBackMajorMinorId(true);

                    } else {
                    	validfeeConfigId = false;
                    	errfeeConfigId1.className = 'error';
                    	errfeeConfigId1.innerHTML = "";
                    	callBackMajorMinorId(false);
                    }
                },
                error : function() {
                	errfeeConfigId1.className = 'error';
                	errfeeConfigId1.innerHTML = "";
                	callBackMajorMinorId(false);
                }
            });

        } else {
        	validfeeConfigId = false;
        }
		
	}
     
      
        return validfeeConfigId;
    }
function afterSuccess(response, validfeeConfigId, errfeeConfigId) {
    if (response.status == "BSUC_0001") {
        validfeeConfigId = true;
        errfeeConfigId.className = 'error';
        errfeeConfigId.innerHTML = "FeeMajorId Already Exists";
        callBackMajorMinorId(true);

    }
    else {
        validfeeConfigId = false;
        errfeeConfigId.className = 'error';
        errfeeConfigId.innerHTML = "";
        callBackMajorMinorId(false);
    }
    return validfeeConfigId;
}

function validateStatus(msgID) {
	var status = (document.getElementById("status").value).replace(
		/(^\s*)|(\s*$)/g, '');
	var errStatus = document.getElementById(msgID);
	if (status == "0") {
		errStatus.className = 'error';
		errStatus.innerHTML = "Please select status";
		return false;
	}
	else {
		errStatus.className = 'error';
		errStatus.innerHTML = "";
	}
	return true;
}

function checkPriorityMapwithMajorId() {
	 
		var validPriority = false;
		var priority =  $('#priority').val();
		var cardType= $('#cardType').val();
		var feeMajorId =  $('#feeMajorId').val();
			var errPriority = document.getElementById('errPriority');
			var tokenValue = document.getElementsByName("_TransactToken")[0].value;
			if (priority != "") {

				$.ajax({
					url : "checkPriorityMapwithMajorId",
					type : "POST",
					dataType : "json",
					data: {"priority" : priority,"feeMajorId" : feeMajorId,"cardType" :cardType,
						    "_TransactToken" : tokenValue},
					success : function(response){

						if (response.status == "BSUC_0001") {
							
							validPriority = true;
							errPriority.className = 'error';
							
							errPriority.innerHTML = "Priority has already been set for MajorId : " +response.feeMajorId + " with this card type : " + document.getElementById('cardType')[document.getElementById('cardType').selectedIndex].innerHTML;
							callBackPriority(true);

						} else {
							validPriority = false;
							errPriority.className = 'error';
							errPriority.innerHTML = "";
							callBackPriority(false);
						}
					},
					error : function() {
						errPriority.className = 'error';
						errPriority.innerHTML = "";
						callBackPriority(false);
					}
				});

			} else {
				validPriority = false;
			}
			return validPriority;
		}
		
		
var callBackFeeMajorMinorId;

function callBackMajorMinorId(flag){
	callBackFeeMajorMinorId=flag;
}


var callBackFeePriority;

function callBackPriority(flag){
	callBackFeePriority=flag;
}

function validateField(id) {

var alphanumeric = /^[a-zA-Z0-9]+$/;
var numeric = /^\d+$/;
var alphanumeric2 = /[a-zA-Z0-9,]/;

	
	var fieldvalue =document.getElementById(id).value
	
	var name1= document.getElementById(id).name
	
	var name= name1.replace("fieldValue","")
	
	var errID= document.getElementById("errField" + name)
	
	var relationalvalue = document.getElementById("relationalOperator" + name).value

	
	if(relationalvalue == 'SW'){
	
	 if(!alphanumeric.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should alphanumeric and single value');
		$(errID).show();
		return false;
	 
	 } else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
		return true;
	}
	
	
	if(relationalvalue == 'GT'){
	
	 if(!numeric.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should Numeric and Single value');
		$(errID).show();
		return false;
	 
	 }
	 else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
 	return true;
	}
	
	if(relationalvalue == 'GTE'){
	
	 if(!numeric.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should Numeric and single value');
		$(errID).show();
		return false;
	 
	 }
	 else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
return true;
	}
	
	if(relationalvalue == 'LT'){
	
	 if(!numeric.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should Numeric and single value');
		$(errID).show();
		return false;
	 
	 }
	 else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
		return true;
	}
	
	if(relationalvalue == 'LTE'){
	
	 if(!numeric.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should Numeric and single value');
		$(errID).show();
		return false;
	 
	 }
	 else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
			return true;
	}
	
	if(relationalvalue == 'NOTCNTS'){
	
	 if(!alphanumeric2.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should be alphanumeric and can have comma separated values');
		$(errID).show();
		return false;
	 
	 }
	 else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
		return true;
	}
	
	if(relationalvalue == 'SW_IN'){
	
	 if(!alphanumeric2.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should be alphanumeric and can have comma separated values');
		$(errID).show();
		return false;
	 
	 }
	 else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
		return true;
	}
	if(relationalvalue == 'EQ'){
	
	 if(!alphanumeric.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should alphanumeric and single value');
		$(errID).show();
		return false;
	 
	 }
	 else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
		return true;
	}
	
	if(relationalvalue == 'IN'){
	
	 if(!alphanumeric2.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should be alphanumeric and can have comma separated values');
		$(errID).show();
		return false;
	 
	 }
	 else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
return true;	
	}
	
	if(relationalvalue == 'NEQ'){
	
	 if(!alphanumeric.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should alphanumeric and single value');
		$(errID).show();
		return false;
	 
	 }
	 else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
return true;
	}
	
	if(relationalvalue == 'CNTS')
	{
	
	 if(!alphanumeric2.test(fieldvalue))
	 {
	 $(errID).find('.error').html('Input should be alphanumeric and can have comma separated values ');
		$(errID).show();
		return false;
	 
	 }
	 else{
	 $(errID).find('.error').html('');
		$(errID).hide();
	 }
		return true;	
	}
	
	
	

    
    
}


