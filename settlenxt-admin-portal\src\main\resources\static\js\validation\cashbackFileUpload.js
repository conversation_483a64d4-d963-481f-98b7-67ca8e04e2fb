$(document).ready(function() {
	$('#errUniqueFile').hide();
	$('#cashbackSuccessMsg').hide();
	$('#cashbackErrorMsg').hide();

	 $("#tabnew").DataTable({
		initComplete: function() {
			var api = this.api();
			// For each column
			api
				.columns()
				.eq(0)
				.each(function(colIdx) {
					//If first column to be skipped to include the filter for the reasons line check box 
					if (!(colIdx == 0 && firstColumnToBeSkippedInFilterAndSort)) {
						// Set the header cell to contain the input element
						var cell = $('#tabnew thead tr th').eq(
							$(api.column(colIdx).header()).index()
						);
						var title = $(cell).text();
						handleInput(colIdx, cell, title, api);
					}
				});
			$('#tabnew_filter').hide();
		},

		// Disabled ordering for first column in case
		columnDefs: [
			{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
		],
		"order": [],
		dom: 'lBfrtip',
		buttons: [

			{
				extend: 'excelHtml5',
				text: 'Export',
				filename: 'CashBack',
				header: 'false',
				title: null,
				sheetName: 'CashBack',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			},
			{
				extend: 'csvHtml5',
				text: 'Export',
				filename: 'CashBack',
				header: 'false',
				title: null,
				sheetName: 'CashBack',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			}

		],

		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true,
	});

	$('#year').on('keyup keypress blur change', function() {
		validateFromCommonVal('year', true, "SelectionBox", 4, false);
	});
	$('#month').on('keyup keypress blur change', function() {
		validateFromCommonVal('month', true, "SelectionBox", 10, false);
	});

	$("#uploadId").click(function() {
		var validPage = validatePage();
		if (validPage) {
			var url = getURL("/fileUpload");
			var tokenValue = document.getElementsByName("_TransactToken")[0].value;
			var formData = new FormData()
			formData.append("year", document.getElementById("year").value)
			formData.append("month", document.getElementById("month").value)
			formData.append("document", document.getElementById("fileArr").files[0])
			formData.append("_TransactToken", tokenValue)
			$.ajax({
				method: 'POST',
				url: url,
				cache: false,
				processData: false,
				contentType: false,
				data: formData,
				success: async function(response) {
					if (response === "Success") {
						$("#cashbackErrorMsg").hide();
						$("#errUniqueFile").hide();
						$("#cashbackSuccessMsg").show();
						await new Promise(r => setTimeout(r, 3000));
						postData("/cashbackFileUpload", "");
					} else if (response === "RecMismatch") {
						$("#cashbackErrorMsg").hide();
						$("#cashbackSuccessMsg").hide();
						$("#errUniqueFile").show();
					} else {
						$("#errUniqueFile").hide();
						$("#cashbackSuccessMsg").hide();
						$("#cashbackErrorMsg").show();
					}
				},
				error: function() {
					$("#cashbackErrorMsg").show();
				}
			});
		}
	});

	$("#reset").click(function() {
		document.getElementById("year").value = "SELECT"
		document.getElementById("month").value = "SELECT"
		document.getElementById("fileArr").value = ""
		$('#errFile').text('');
		$('#errUniqueFile').hide();
		$('#cashbackSuccessMsg').hide();
		$('#cashbackErrorMsg').hide();
		$('#errmonth').hide();
		$('#erryear').hide();
	});
});

function handleInput(colIdx, cell, title, api) {
	var cursorPosition;
	if (colIdx < actionColumnIndex) {

		$(cell).html(title + '<br><input class="search-box"   type="text" />');

		// On every keypress in this input
		$(
			'input',
			$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
		)
			.off('keyup change')
			.on('change', function () {
				// Get the search value
				$(this).attr('title', $(this).val());
				var regexr = '({search})';

				cursorPosition = this.selectionStart;
				// Search the column for that value
				api
					.column(colIdx)
					.search(
						this.value != ''
							? regexr.replace('{search}', '(((' + this.value + ')))')
							: '',
						this.value != '',
						this.value == ''
					)
					.draw();
			})
			.on('click', function (e) {
				e.stopPropagation();
			})
			.on('keyup', function (e) {
				e.stopPropagation();

				$(this).trigger('change');
				if (cursorPosition && cursorPosition != null) {
					$(this)
						.focus()[0]
						.setSelectionRange(cursorPosition, cursorPosition);
				}
			});
	} else {
		$(cell).html(title + '<br> &nbsp;');
	}
}

function validatePage() {
	var flag = false;

	var specialChars = "#%&$*:<>?,\-+=@^`'()_;~[]/{|}";

	if (!validateFromCommonVal('year', true, "SelectionBox", 4, false)) {
		return flag;
	}
	if (!validateFromCommonVal('month', true, "SelectionBox", 10, false)) {
		return flag;
	}

	if ($('#fileArr').val().trim() == '') {
		$('#errFile').text('Please attach file');
	} else {
		var uplodedFile = $("#fileArr").get(0).files[0].name;
		var fileNameSplit = uplodedFile.split('.');

		for (var j of specialChars) {
			if (fileNameSplit[0].indexOf(j) > -1) {
				$('#errFile').text('Special Characters Not Allowed');
				return flag;
			}
		}

		var fileExtension = fileNameSplit[fileNameSplit.length - 1].toString().toLowerCase();
		if (fileExtension === 'csv' && fileNameSplit.length === 2) {
			flag = true;
			$('#errFile').text('');
		} else {
			$('#errFile').text('Please upload csv format file');
		}
	}
	return flag;
}

function refresh() {
	postData("/cashbackFileUpload", "");
}