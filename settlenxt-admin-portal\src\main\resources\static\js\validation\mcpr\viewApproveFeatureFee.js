	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});

function display() {
    $(".appRejMust").hide();

}

function userAction(action, cardId) {
	 
	var data = "cardId," + cardId ;
	postData(action, data);
}

function edit(action, cardId,parentPage) {
	 
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "cardId," + cardId + ",_vTransactToken,"
			+ tokenValue + ",parentPage," + parentPage;
	postData(action, data);
}

function backAction(_type, action) {
	var data =  "status,"
			+ status;
	postData(action, data);
}

 function postAction(_action) {
	var cardId;
	var url="";
	var remarks="";
	var data="";
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 cardId = $("#cardId").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveFeatureFee';
				 data = "cardId," + cardId + ",status," + "A" + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				 cardId = $("#cardId").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveFeatureFee';
				 data = "cardId," + cardId + ",status," + "R"  + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
	
			 
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		