	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	
	}
	function backAction(type, action) {
		var url = action;
	var data = "userType," + type ;
		postData(url, data);
	}
	
	function postAction(_action) {
	var cardProjectionId;
	var crtuser;
	var remarks;
	var url;
	var data;
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 cardProjectionId = $("#cardProjectionId").val();
				 crtuser = $("#crtuser").val();
				 remarks=$("#rejectReason").val();
		
				 url = '/approveCardProjection';
				 data = "cardProjectionId," + cardProjectionId + ",status," + "Approved" + ",crtuser,"
						+ crtuser + ",remarks,"
						+ remarks;
				
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				postRejectedData(cardProjectionId, remarks, crtuser, url, data);
	
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		
	
function postRejectedData(cardProjectionId, remarks, crtuser, url, data) {
    if ('${requestInfo.requestName}' == 'ROLE FUNCTIONALITY MAP') {
        cardProjectionId = $("#cardProjectionId").val();
        remarks = $("#rejectReason").val();
        crtuser = $("#crtuser").val();
        url = '/rejectCardProjection';

        data = "cardProjectionId," + cardProjectionId + ",status," + "Rejected"
            + ",crtuser," + crtuser + ",rejectReason," + remarks;

        postData(url, data);
    }
    else {
        cardProjectionId = $("#cardProjectionId").val();
        crtuser = $("#crtuser").val();
        remarks = $("#rejectReason").val();

        url = '/approveCardProjection';

        data = "cardProjectionId," + cardProjectionId + ",status," + "Rejected"
            + ",crtuser," + crtuser + ",remarks," + remarks;
        postData(url, data);
    }
  
}

	function edit(cardProjectionId, type,parentPage) {
	var url;
		if (type == 'V')
			url = '/editProjectionEntry';
		var data = "cardProjectionId," + cardProjectionId + ",viewType," + type + ",parentPage,"
			+ parentPage;
		postData(url, data);
	}	

	function postDiscardAction(action) {
		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		var url = action;
		var cardProjectionId = document.getElementById("cardProjectionId").value;
		var data = "cardProjectionId," + cardProjectionId + ",_vTransactToken," + tokenValue ;
		postData(url, data);
		
	}