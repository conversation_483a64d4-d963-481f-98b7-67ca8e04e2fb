

function backAction(_type, action) {
	var tokenValue = "lIpLsRjLtLDPSzoS2xPf9WXiF/M=";
	
	var mccId = $("#mccId").val();
	var data = "mccId," + mccId + ",_vTransactToken," + tokenValue + ",status,"
			+ status;
	postData(action, data);
}


function viewMcc(mccId, type,parentPage) {
var url="";
	if (type == 'V')
		url = '/editMcc';
	else if (type == 'P')
		url = '/getMcc';
	else if (type == 'G')
		url = '/getPendingMcc';
	
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "mccId," + mccId + ",viewType," + type + ",_vTransactToken,"
		+ tokenValue + ",parentPage," + parentPage;
	postData(url, data);
}