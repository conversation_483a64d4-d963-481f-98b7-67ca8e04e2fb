[{"xmlName": "nMTI", "type": "N4", "dataType": "N", "minLength": "4", "maxLength": "4", "regexPattern": "^\\d{4}$", "format": "F", "description": "Message Type Identifier", "reasonCodes": {"missing": 5034, "invalidLength": 5004, "invalidType": 5003}}, {"xmlName": "nFunCd", "type": "N3", "dataType": "N", "minLength": "3", "maxLength": "3", "regexPattern": "^\\d{3}$", "format": "F", "description": "Function Code", "reasonCodes": {"missing": 5211, "invalidLength": 5008, "invalidType": 5007}}, {"xmlName": "nRecNum", "type": "N8", "dataType": "N", "minLength": "8", "maxLength": "8", "regexPattern": "^\\d{8}$", "format": "F", "description": "Record Number", "reasonCodes": {"missing": 5035, "invalidLength": 5036, "invalidType": 5036}}, {"xmlName": "nDtTmLcTxn", "type": "N12", "dataType": "D", "minLength": "12", "maxLength": "12", "regexPattern": "^\\d{12}$", "format": "F", "dateFormat": "yyMMddHHmmss", "description": "Date Time/Local Transaction", "reasonCodes": {"missing": 5038, "invalidLength": 5037, "invalidType": 5037}}, {"xmlName": "nPAN", "type": "N12-19", "dataType": "N", "minLength": "12", "maxLength": "19", "regexPattern": "^\\d{12,19}$", "format": "V", "description": "Primary Account Number", "reasonCodes": {"missing": 5040, "invalidLength": 5039, "invalidType": 5041}}, {"xmlName": "nARD", "type": "AN23", "dataType": "AN", "minLength": "23", "maxLength": "23", "regexPattern": "^[0-9]{10}[a-zA-Z0-9]{12}[0-9]{1}$", "format": "F", "description": "Primary Account Number", "reasonCodes": {"missing": 5043, "invalidLength": 5042, "invalidType": 5042}}, {"xmlName": "nRRN", "type": "AN12", "dataType": "AN", "minLength": "12", "maxLength": "12", "regexPattern": "^[a-zA-Z0-9]{12}$", "format": "F", "description": "Retrieval Reference Number", "reasonCodes": {"missing": 5176, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "nAcqInstCd", "type": "N11", "dataType": "N", "minLength": "6", "maxLength": "11", "regexPattern": "^\\d{6,11}$", "format": "V", "description": "Acquirer Institution ID Code", "reasonCodes": {"missing": 5045, "invalidLength": 5044, "invalidType": 5044}}, {"xmlName": "nApprvlCd", "type": "AN6", "dataType": "AN", "minLength": "6", "maxLength": "6", "regexPattern": "^[a-zA-Z0-9]{6}$", "format": "F", "description": "Approval Code", "reasonCodes": {"missing": 5047, "invalidLength": 5046, "invalidType": 5046}}, {"xmlName": "nCrdAcptTrmId", "type": "ANS8", "dataType": "ANS", "minLength": "8", "maxLength": "8", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{8}$", "format": "F", "description": "Card Acceptor Terminal ID", "reasonCodes": {"missing": 5049, "invalidLength": 5048, "invalidType": 5048}}, {"xmlName": "nAmtTxn", "type": "N12", "dataType": "N", "minLength": "1", "maxLength": "12", "regexPattern": "^\\d{3,12}$", "format": "V", "description": "Transaction Amount", "reasonCodes": {"missing": 5050, "invalidLength": 5051, "invalidType": 5051}}, {"xmlName": "nCcyCdTxn", "type": "N3", "dataType": "N", "minLength": "3", "maxLength": "3", "regexPattern": "^(?i)(356|064|524)$", "format": "F", "description": "Transaction Currency Code", "reasonCodes": {"missing": 5053, "invalidLength": 5052, "invalidType": 5052}}, {"xmlName": "nAmtAdd", "type": "N12", "dataType": "N", "minLength": "3", "maxLength": "12", "regexPattern": "^\\d{3,12}$", "format": "V", "description": "Additional Amount", "constraints": [{"name": "nProcCd", "transform": "substring(0,2)", "operator": "=", "value": "09"}], "reasonCodes": {"missing": -1, "present": 5055, "invalidLength": 5056, "invalidType": 5054}}, {"xmlName": "nReplAmt", "type": "AN42", "dataType": "AN", "minLength": "42", "maxLength": "42", "regexPattern": "^[a-zA-Z0-9 ]{42}$", "format": "F", "description": "Replacement Amount", "reasonCodes": {"missing": -1, "invalidLength": 5267, "invalidType": 5267}}, {"xmlName": "nTxnOrgInstCd", "type": "AN11", "dataType": "AN", "minLength": "11", "maxLength": "11", "regexPattern": "^[a-zA-Z]{4}[0-9]{7}$", "format": "F", "description": "Transaction Originator Institution ID Code", "reasonCodes": {"missing": 5057, "invalidLength": 5058, "invalidType": 5058}}, {"xmlName": "nUID", "type": "N12", "dataType": "N", "minLength": "12", "maxLength": "12", "regexPattern": "^\\d{12}$", "format": "F", "description": "Card Holder <PERSON>", "reasonCodes": {"missing": -1, "invalidLength": 5059, "invalidType": 5059}}, {"xmlName": "nCrdHldrITPan", "type": "AN10", "dataType": "AN", "minLength": "10", "maxLength": "10", "regexPattern": "^[a-zA-Z0-9]{10}$", "format": "F", "description": "Card Holder Income Tax PAN", "reasonCodes": {"missing": -1, "invalidLength": 5060, "invalidType": 5060}}, {"xmlName": "nCrdAcpAddAdrs", "type": "ANS20", "dataType": "ANS", "minLength": "20", "maxLength": "20", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{20}$", "format": "F", "description": "Card Acceptor Additional Address", "reasonCodes": {"missing": -1, "invalidLength": 5061, "invalidType": 5061}}, {"xmlName": "nCrdAcpZipCd", "type": "ANS9", "dataType": "ANS", "minLength": "9", "maxLength": "9", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{9}$", "format": "F", "description": "Card Acceptor Zip Code", "reasonCodes": {"missing": 5293, "invalidLength": 5062, "invalidType": 5062}}, {"xmlName": "nMerTelNum", "type": "AN11", "dataType": "AN", "minLength": "11", "maxLength": "11", "regexPattern": "^[a-zA-Z0-9 ]{11}$", "format": "F", "description": "Merchant Telephone Number", "reasonCodes": {"missing": 5294, "invalidLength": 5063, "invalidType": 5063}}, {"xmlName": "nContNum", "type": "N15", "dataType": "N", "minLength": "15", "maxLength": "15", "regexPattern": "^\\d{15}$", "format": "F", "description": "Control Number", "reasonCodes": {"missing": 5064, "invalidLength": 5065, "invalidType": 5065}}, {"xmlName": "nDtTmTrns", "type": "N10", "dataType": "D", "minLength": "10", "maxLength": "10", "regexPattern": "^\\d{10}$", "format": "F", "dateFormat": "MMddHHmmss", "description": "Transmission Date Time", "reasonCodes": {"missing": 5169, "invalidLength": 5165, "invalidType": 5165}}, {"xmlName": "nProcCd", "type": "N6", "dataType": "N", "minLength": "2", "maxLength": "2", "regexPattern": "^\\d{6}$", "format": "F", "description": "Processing Code", "reasonCodes": {"missing": 5067, "present": 5068, "invalidLength": 5066, "invalidType": 5066}}, {"xmlName": "nPosEntMode", "type": "N3", "dataType": "N", "minLength": "3", "maxLength": "3", "regexPattern": "^\\d{3}$", "format": "F", "description": "PoS Entry Mode", "reasonCodes": {"missing": 5170, "invalidLength": 5069, "invalidType": 5069}}, {"xmlName": "nPosCondCd", "type": "N2", "dataType": "N", "minLength": "2", "maxLength": "2", "regexPattern": "^\\d{2}$", "format": "F", "description": "PoS Condition Code", "reasonCodes": {"missing": 5292, "invalidLength": 5070, "invalidType": 5070}}, {"xmlName": "nPosDataCd", "type": "ANS41", "dataType": "ANS", "minLength": "41", "maxLength": "41", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{41}$", "format": "F", "description": "PoS Data Code", "reasonCodes": {"missing": 5072, "invalidLength": 5071, "invalidType": 5071}}, {"xmlName": "nCrdAcpBussCd", "type": "N4", "dataType": "N", "minLength": "4", "maxLength": "4", "regexPattern": "^\\d{4}$", "format": "F", "description": "Card Acceptor Business Code", "reasonCodes": {"missing": 5074, "invalidLength": 5073, "invalidType": 5073}}, {"xmlName": "nActnCd", "type": "AN2", "dataType": "AN", "minLength": "2", "maxLength": "2", "regexPattern": "^[a-zA-Z0-9]{2}$", "format": "F", "description": "Action Code", "reasonCodes": {"missing": 5291, "present": 5076, "invalidLength": 7027, "invalidType": 7027}}, {"xmlName": "nProdCd", "type": "AN5", "dataType": "AN", "minLength": "5", "maxLength": "5", "regexPattern": "^[a-zA-Z0-9]{5}$", "format": "F", "description": "Product Code", "reasonCodes": {"missing": 5078, "invalidLength": 5077, "invalidType": 5077}}, {"xmlName": "nTxnDesInstCd", "type": "AN11", "dataType": "AN", "minLength": "11", "maxLength": "11", "regexPattern": "^[a-zA-Z0-9 ]{11}$", "format": "F", "description": "Transaction Destination Institution ID Code", "reasonCodes": {"missing": 5178, "invalidLength": 9204, "invalidType": 9204}}, {"xmlName": "nServCd", "type": "AN3", "dataType": "AN", "minLength": "3", "maxLength": "3", "regexPattern": "^[a-zA-Z0-9]{3}$", "format": "F", "description": "Service Code", "constraints": [{"name": "nPosCondCd", "operator": "=", "value": "00"}], "reasonCodes": {"missing": 5177, "present": 5088, "invalidLength": 5237, "invalidType": 5237}}, {"xmlName": "nCrdAcpIDCd", "type": "ANS15", "dataType": "ANS", "minLength": "15", "maxLength": "15", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{15}$", "format": "F", "description": "Card Acceptor ID Code", "reasonCodes": {"missing": 5166, "invalidLength": 5226, "invalidType": 5226}}, {"xmlName": "nCrdAcpNm", "type": "ANS23", "dataType": "ANS", "minLength": "23", "maxLength": "23", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{23}$", "format": "F", "description": "Card Acceptor Name", "reasonCodes": {"missing": 5109, "invalidLength": 5110, "invalidType": 5110}}, {"xmlName": "nCrdAcpLoc", "type": "ANS20", "dataType": "ANS", "minLength": "0", "maxLength": "20", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{0,20}$", "format": "V", "description": "Card Acceptor Location", "reasonCodes": {"missing": 5111, "invalidLength": 5167, "invalidType": 5167}}, {"xmlName": "nCrdAcpCity", "type": "ANS13", "dataType": "ANS", "minLength": "13", "maxLength": "13", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{13}$", "format": "F", "description": "Card Acceptor City", "reasonCodes": {"missing": -1, "invalidLength": 5112, "invalidType": 5112}}, {"xmlName": "nCrdAcpStNm", "type": "A2", "dataType": "A", "minLength": "2", "maxLength": "2", "regexPattern": "^[a-zA-Z]{2}$", "format": "F", "description": "Card Acceptor State", "reasonCodes": {"missing": 5113, "invalidLength": 5114, "invalidType": 5114}}, {"xmlName": "nCrdAcpCtryCd", "type": "A2", "dataType": "A", "minLength": "2", "maxLength": "2", "regexPattern": "^IN$", "format": "F", "description": "Card Acceptor Country Code", "reasonCodes": {"missing": 5115, "invalidLength": 5116, "invalidType": 5116}}, {"xmlName": "nMsgRsnCd", "type": "N4", "dataType": "N", "minLength": "4", "maxLength": "4", "regexPattern": "^\\d{4}$", "format": "F", "description": "Message Reason Code", "reasonCodes": {"missing": 5118, "present": 5119, "invalidLength": 5117, "invalidType": 5117}}, {"xmlName": "nIntrnTrackNum", "type": "AN20", "dataType": "AN", "minLength": "0", "maxLength": "20", "regexPattern": "^[a-zA-Z0-9 ]{0,20}$", "format": "V", "description": "Internal Tracking Number", "reasonCodes": {"missing": -1, "invalidLength": 5120, "invalidType": 5120}}, {"xmlName": "nMemMsgTxt", "type": "ANS100", "dataType": "ANS", "minLength": "0", "maxLength": "100", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{0,100}$", "format": "V", "description": "Member Message Text", "reasonCodes": {"missing": 5121, "present": 5122, "invalidLength": 5123, "invalidType": 5123}}, {"xmlName": "nDocInd", "type": "A1", "dataType": "A", "minLength": "1", "maxLength": "1", "regexPattern": "^(?i)(Y|N)$", "format": "F", "description": "Document Indicator", "reasonCodes": {"missing": 5125, "present": 5126, "invalidLength": 5124, "invalidType": 5124}}, {"xmlName": "nFulParInd", "type": "A1", "dataType": "A", "minLength": "1", "maxLength": "1", "regexPattern": "^(?i)(F|P)$", "format": "F", "description": "Full / Partial Indicator", "reasonCodes": {"missing": 5128, "present": 5129, "invalidLength": 5127, "invalidType": 5127}}, {"xmlName": "nCaseNum", "type": "AN14", "dataType": "AN", "minLength": "14", "maxLength": "14", "regexPattern": "^[a-zA-Z0-9 ]{14}$", "format": "F", "description": "Case Number", "constraints": [{"name": "", "operator": "", "value": ""}], "reasonCodes": {"missing": 5171, "invalidLength": 5221, "invalidType": 5221}}, {"xmlName": "nDtSet", "type": "N6", "dataType": "D", "minLength": "6", "maxLength": "6", "regexPattern": "^\\d{6}$", "format": "F", "dateFormat": "yyMMdd", "description": "Settlement Date", "reasonCodes": {"missing": -1, "present": 5130, "invalidLength": 5227, "invalidType": 5227}}, {"xmlName": "nSetDCInd", "type": "A1", "dataType": "A", "minLength": "1", "maxLength": "1", "regexPattern": "^(?i)(C|D)$", "format": "F", "description": "Settlement CR / DR Indicator", "reasonCodes": {"missing": -1, "present": 5131, "invalidLength": 5238, "invalidType": 5238}}, {"xmlName": "nAmtSet", "type": "N12", "dataType": "N", "minLength": "3", "maxLength": "12", "regexPattern": "^\\d{3,12}$", "format": "V", "description": "Settlement Amount", "reasonCodes": {"missing": -1, "present": 5132, "invalidLength": 5220, "invalidType": 5220}}, {"xmlName": "nCcyCdSet", "type": "N3", "dataType": "N", "minLength": "3", "maxLength": "3", "regexPattern": "^356$", "format": "F", "description": "Settlement Currency Code", "reasonCodes": {"missing": -1, "present": 5133, "invalidLength": 5223, "invalidType": 5223}}, {"xmlName": "nConvRtSet", "type": "N8", "dataType": "N", "minLength": "8", "maxLength": "8", "regexPattern": "^\\d{8}$", "format": "F", "description": "Settlement Conversion Rate", "reasonCodes": {"missing": -1, "present": 5134, "invalidLength": 5225, "invalidType": 5225}}, {"xmlName": "nAmtBil", "type": "N12", "dataType": "N", "minLength": "3", "maxLength": "12", "regexPattern": "^\\d{3,12}$", "format": "V", "description": "Billing Amount", "reasonCodes": {"missing": -1, "present": 5135, "invalidLength": 5219, "invalidType": 5219}}, {"xmlName": "nConvRtBil", "type": "N8", "dataType": "N", "minLength": "8", "maxLength": "8", "regexPattern": "^\\d{8}$", "format": "F", "description": "Billing Conversion Rate", "reasonCodes": {"missing": -1, "present": 5136, "invalidLength": 5224, "invalidType": 5224}}, {"xmlName": "nConvRtBil", "type": "N3", "dataType": "N", "minLength": "3", "maxLength": "3", "regexPattern": "^\\d{3}$", "format": "F", "description": "Billing Currency Code", "reasonCodes": {"missing": -1, "present": 5137, "invalidLength": 5222, "invalidType": 5222}}, {"xmlName": "nLtPrsntInd", "type": "A1", "dataType": "A", "minLength": "1", "maxLength": "1", "regexPattern": "^[a-zA-Z]{1}$", "format": "F", "description": "Late Presentment Indicator", "reasonCodes": {"missing": -1, "present": 5138, "invalidLength": 5234, "invalidType": 5234}}, {"xmlName": "nRGCSRcvdDt", "type": "N6", "dataType": "N", "minLength": "6", "maxLength": "6", "regexPattern": "^\\d{6}$", "format": "F", "description": "RGCS Received Date", "reasonCodes": {"missing": -1, "present": 5139, "invalidLength": 5264, "invalidType": 5264}}, {"xmlName": "nOrgSetDt", "type": "N6", "dataType": "N", "minLength": "6", "maxLength": "6", "regexPattern": "^\\d{6}$", "format": "F", "description": "Original Settlement Date", "reasonCodes": {"missing": -1, "present": 5140, "invalidLength": 5235, "invalidType": 5235}}, {"xmlName": "nFeeTpCd", "type": "N4", "dataType": "N", "minLength": "4", "maxLength": "4", "regexPattern": "^\\d{4}$", "format": "F", "description": "Fee Type Code", "reasonCodes": {"missing": 5174, "present": 5142, "invalidLength": 5231, "invalidType": 5231}}, {"xmlName": "nIntrchngCtg", "type": "N4", "dataType": "N", "minLength": "4", "maxLength": "4", "regexPattern": "^\\d{4}$", "format": "F", "description": "Interchange Category", "reasonCodes": {"missing": -1, "present": 5143, "invalidLength": 5223, "invalidType": 5223}}, {"xmlName": "nFeeAmt", "type": "N10", "dataType": "N", "minLength": "3", "maxLength": "10", "regexPattern": "^\\d{3,10}$", "format": "V", "description": "<PERSON><PERSON> Amount", "reasonCodes": {"missing": 5172, "present": 5144, "invalidLength": 5228, "invalidType": 5228}}, {"xmlName": "nFeeDCInd", "type": "A1", "dataType": "A", "minLength": "1", "maxLength": "1", "regexPattern": "^(?i)(C|D)$", "format": "F", "description": "Fee DR / CR Indicator", "reasonCodes": {"missing": 5175, "present": 5145, "invalidLength": 5230, "invalidType": 5230}}, {"xmlName": "nFeeCcy", "type": "N3", "dataType": "N", "minLength": "3", "maxLength": "3", "regexPattern": "^\\d{3}$", "format": "F", "description": "<PERSON><PERSON>", "reasonCodes": {"missing": 5173, "present": 5146, "invalidLength": 5229, "invalidType": 5229}}, {"xmlName": "nICCData", "type": "ANS255", "dataType": "ANS", "minLength": "0", "maxLength": "255", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{0,255}$", "format": "V", "description": "ICC System Related Data", "constraints": [{"name": "nPosEntMode", "transform": "substring(0,2)", "operator": "IN", "values": ["05", "95", "07", "91"]}], "reasonCodes": {"missing": 5149, "present": 5148, "invalidLength": 5147, "invalidType": 5147}}, {"xmlName": "nProcSts", "type": "A1", "dataType": "A", "minLength": "1", "maxLength": "1", "regexPattern": "^(?i)(S|F|E|H)$", "format": "F", "description": "Processing Status", "reasonCodes": {"missing": -1, "present": 5150, "invalidLength": 5236, "invalidType": 5236}}, {"xmlName": "nRGCSRejRsnCd", "type": "N4", "dataType": "N", "minLength": "4", "maxLength": "4", "regexPattern": "^\\d{4}$", "format": "F", "description": "RGCS Reject Reason Code", "reasonCodes": {"missing": -1, "present": 5151, "invalidLength": 5266, "invalidType": 5266}}, {"xmlName": "nAddData", "type": "ANS256", "dataType": "ANS", "minLength": "0", "maxLength": "256", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{0,256}$", "format": "V", "description": "Additional Data", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": 5269, "invalidType": 5269}}, {"xmlName": "nDtTmFlGen", "type": "N10", "dataType": "N", "minLength": "10", "maxLength": "10", "regexPattern": "^\\d{10}$", "format": "F", "description": "File Generation Date and Time", "reasonCodes": {"missing": 5012, "present": -1, "invalidLength": 5200, "invalidType": 5200}}, {"xmlName": "nMemInstCd", "type": "AN11", "dataType": "AN", "minLength": "11", "maxLength": "11", "regexPattern": "^[a-zA-Z0-9]{11}$", "format": "F", "description": "Member Institution ID Code", "reasonCodes": {"missing": 5014, "present": -1, "invalidLength": 5013, "invalidType": 5013}}, {"xmlName": "nUnFlNm", "type": "AN21", "dataType": "AN", "minLength": "21", "maxLength": "21", "regexPattern": "^[a-zA-Z0-9/\\.]{21}$", "format": "F", "description": "Unique File Name", "reasonCodes": {"missing": 5026, "present": -1, "invalidLength": 5017, "invalidType": 5017}}, {"xmlName": "nSetBIN", "type": "AN6", "dataType": "AN", "minLength": "6", "maxLength": "6", "regexPattern": "^[a-zA-Z0-9]{6}$", "format": "F", "description": "Settlement BIN", "reasonCodes": {"missing": 5019, "present": -1, "invalidLength": 5018, "invalidType": 5018}}, {"xmlName": "nFlCatg", "type": "A1", "dataType": "A", "minLength": "1", "maxLength": "1", "regexPattern": "^(?i)(T|P)$", "format": "F", "description": "File Category", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": 5020, "invalidType": 5020}}, {"xmlName": "nVerNum", "type": "ANS5", "dataType": "ANS", "minLength": "5", "maxLength": "5", "regexPattern": "^[a-zA-Z0-9 \\t\\!\"\\#\\$%\\'\\(\\)\\*\\+\\\\,-\\-\\.\\/\\:;=>?\\@\\^_`\\{\\|\\}\\~\\[\\]]{5}$", "format": "F", "description": "Version Number", "reasonCodes": {"missing": 5022, "present": -1, "invalidLength": 5021, "invalidType": 5021}}, {"xmlName": "nFlRejInd", "type": "A1", "dataType": "A", "minLength": "1", "maxLength": "1", "regexPattern": "^(?i)(Y|N)$", "format": "F", "description": "Entire File Reject Indicator", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": 5232, "invalidType": 5232}}, {"xmlName": "nFlRejRsnCd", "type": "N4", "dataType": "N", "minLength": "4", "maxLength": "4", "regexPattern": "^\\d{4}$", "format": "F", "description": "File Reject Reason Code", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "nTxnCnt", "type": "N8", "dataType": "N", "minLength": "8", "maxLength": "8", "regexPattern": "^\\d{8}$", "format": "F", "description": "Transaction Count", "reasonCodes": {"missing": 5028, "present": -1, "invalidLength": 5030, "invalidType": 5029}}, {"xmlName": "nRnTtlAmt", "type": "N15", "dataType": "N", "minLength": "1", "maxLength": "15", "regexPattern": "^\\d{1,15}$", "format": "F", "description": "Run Total Amount", "reasonCodes": {"missing": 5032, "present": -1, "invalidLength": 5201, "invalidType": 5033}}, {"xmlName": "nRecrPymtCd", "type": "N2", "dataType": "N", "minLength": "2", "maxLength": "2", "regexPattern": "^\\d{2}$", "format": "F", "description": "Payments Indicator", "reasonCodes": {"missing": -1, "present": 5079, "invalidLength": 5239, "invalidType": 5239}}, {"xmlName": "nArqcAuthID", "type": "N1", "dataType": "N", "minLength": "1", "maxLength": "1", "regexPattern": "^\\d{1}$", "format": "F", "description": "ARQC Authorization Indicator", "reasonCodes": {"missing": -1, "present": 5082, "invalidLength": 5242, "invalidType": 5242}}, {"xmlName": "nECIInd", "type": "N2", "dataType": "N", "minLength": "2", "maxLength": "2", "regexPattern": "^\\d{2}$", "format": "F", "description": "E-Commerce Indicator", "reasonCodes": {"missing": -1, "present": 5091, "invalidLength": 5248, "invalidType": 5248}}, {"xmlName": "nAdvRsnCd", "type": "AN7", "dataType": "AN", "minLength": "7", "maxLength": "7", "regexPattern": "^[a-zA-Z0-9 ]{7}$", "format": "F", "description": "Advice Reason Code", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F06", "type": "AN32", "dataType": "AN", "minLength": "10", "maxLength": "32", "regexPattern": "^[a-fA-F0-9 ]{0,32}$", "format": "F", "description": "9F06", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F37", "type": "AN8", "dataType": "AN", "minLength": "8", "maxLength": "8", "regexPattern": "^[a-fA-F0-9 ]{8}$", "format": "F", "description": "9f37", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F36", "type": "AN4", "dataType": "AN", "minLength": "4", "maxLength": "4", "regexPattern": "^[a-fA-F0-9 ]{4}$", "format": "F", "description": "9f36", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F35", "type": "N2", "dataType": "N", "minLength": "2", "maxLength": "2", "regexPattern": "^\\d{2}$", "format": "F", "description": "9f35", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F34", "type": "AN6", "dataType": "AN", "minLength": "6", "maxLength": "6", "regexPattern": "^[a-fA-F0-9 ]{6}$", "format": "F", "description": "9f34", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F33", "type": "AN6", "dataType": "AN", "minLength": "6", "maxLength": "6", "regexPattern": "^[a-fA-F0-9 ]{6}$", "format": "F", "description": "9f33", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F27", "type": "AN2", "dataType": "AN", "minLength": "2", "maxLength": "2", "regexPattern": "^[a-fA-F0-9 ]{2}$", "format": "F", "description": "9f27", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F26", "type": "AN16", "dataType": "AN", "minLength": "16", "maxLength": "16", "regexPattern": "^[a-fA-F0-9 ]{16}$", "format": "F", "description": "9f26", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F1E", "type": "AN16", "dataType": "AN", "minLength": "16", "maxLength": "16", "regexPattern": "^[a-zA-Z0-9 ]{16}$", "format": "F", "description": "9F1E", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F1A", "type": "N3", "dataType": "N", "minLength": "3", "maxLength": "3", "regexPattern": "^\\d{3}$", "format": "F", "description": "9F1A", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F10", "type": "AN64", "dataType": "AN", "minLength": "0", "maxLength": "64", "regexPattern": "^[a-fA-F0-9 ]{0,64}$", "format": "F", "description": "9F10", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F07", "type": "AN4", "dataType": "AN", "minLength": "4", "maxLength": "4", "regexPattern": "^[a-fA-F0-9 ]{4}$", "format": "F", "description": "9F07", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F03", "type": "N12", "dataType": "N", "minLength": "12", "maxLength": "12", "regexPattern": "^\\d{12}$", "format": "F", "description": "9F03", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F02", "type": "N12", "dataType": "N", "minLength": "12", "maxLength": "12", "regexPattern": "^\\d{12}$", "format": "F", "description": "9F02", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9C", "type": "N2", "dataType": "N", "minLength": "2", "maxLength": "2", "regexPattern": "^\\d{2}$", "format": "F", "description": "9C", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9A", "type": "N6", "dataType": "N", "minLength": "6", "maxLength": "6", "regexPattern": "^\\d{6}$", "format": "F", "description": "9A", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n95", "type": "AN10", "dataType": "AN", "minLength": "10", "maxLength": "10", "regexPattern": "^[a-fA-F0-9 ]{10}$", "format": "F", "description": "95", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n84", "type": "AN32", "dataType": "AN", "minLength": "10", "maxLength": "32", "regexPattern": "^[a-fA-F0-9 ]{0,32}$", "format": "F", "description": "84", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n82", "type": "AN4", "dataType": "AN", "minLength": "4", "maxLength": "4", "regexPattern": "^[a-fA-F0-9 ]{4}$", "format": "F", "description": "82", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n5F2A", "type": "N3", "dataType": "N", "minLength": "3", "maxLength": "3", "regexPattern": "^\\d{3}$", "format": "F", "description": "5F2A", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n5A", "type": "N19", "dataType": "N", "minLength": "16", "maxLength": "19", "regexPattern": "^[0-9]{0,19}$", "format": "F", "description": "5A", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n5F34", "type": "N2", "dataType": "N", "minLength": "2", "maxLength": "2", "regexPattern": "^[0-9]{2}$", "format": "F", "description": "5F34", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n91", "type": "AN32", "dataType": "AN", "minLength": "16", "maxLength": "32", "regexPattern": "^[a-fA-F0-9 ]{0,32}$", "format": "F", "description": "91", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F5B", "type": "AN50", "dataType": "AN", "minLength": "0", "maxLength": "50", "regexPattern": "^[a-fA-F0-9 ]{0,50}$", "format": "F", "description": "9F5B", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n71", "type": "AN254", "dataType": "AN", "minLength": "0", "maxLength": "254", "regexPattern": "^[a-fA-F0-9 ]{0,254}$", "format": "F", "description": "71", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n72", "type": "AN254", "dataType": "AN", "minLength": "0", "maxLength": "254", "regexPattern": "^[a-fA-F0-9 ]{0,254}$", "format": "F", "description": "72", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F09", "type": "AN4", "dataType": "AN", "minLength": "4", "maxLength": "4", "regexPattern": "^[a-fA-F0-9 ]{4}$", "format": "F", "description": "9F09", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F41", "type": "N8", "dataType": "N", "minLength": "4", "maxLength": "8", "regexPattern": "^[0-9]{0,8}$", "format": "F", "description": "9F41", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "n9F21", "type": "N6", "dataType": "N", "minLength": "6", "maxLength": "6", "regexPattern": "^[0-9]{6}$", "format": "F", "description": "9F21", "reasonCodes": {"missing": -1, "present": -1, "invalidLength": -1, "invalidType": -1}}, {"xmlName": "nATD", "type": "A1", "dataType": "A", "minLength": "1", "maxLength": "1", "regexPattern": "^[a-zA-Z]{1}$", "format": "F", "description": "Small Merchant", "reasonCodes": {"missing": 5163, "present": 5163, "invalidLength": 5163, "invalidType": 5163}}]