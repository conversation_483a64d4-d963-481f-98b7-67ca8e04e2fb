<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript">
var rebateValidationMessages = {};
rebateValidationMessages['vCardType'] = "<spring:message code='rebate.cardType.validation.msg' javaScriptEscape='true' />";
rebateValidationMessages['VFinancialYear']= "<spring:message code="rebate.financialYear.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['VFeatureOrBaseFee']= "<spring:message code="rebate.featureOrBaseFee.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['VOperatorIndi']= "<spring:message code="rebate.operatorIndi.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vNewCardCount1']= "<spring:message code="rebate.newCardCount1.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vNewCardCount2']= "<spring:message code="rebate.newCardCount2.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vRebatePercentage']= "<spring:message code="rebate.rebatePercentage.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['SelectedCard']= "<spring:message code="rebate.selectedCard.validation.msg" javaScriptEscape='true' />";

</script>
<script type="text/javascript"
	src="./static/js/validation/mcpr/rebateToFuncRGCS.js"></script>

<div class="space_block">
	<div class="container-fluid height-min">

		<c:url value="approveUserStatus" var="approveUserStatus" />
		<form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewUser" modelAttribute="rebateInfoDto"
			action="${approveUserStatus}" autocomplete="off" >
			
			<input type="hidden" id="rebateId" value="${rebateInfoDto.rebateID}" /> 
		<div id="errvErrorInfo" class="error">
		<span for="ErrorInfo" class="error"><form:errors /></span>
		</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="rebate.rebateInformation" /></span></strong>
							<form:hidden path="rebateID" id="hrebateID" />
							<form:hidden path="cardType" id="hcardType" />
							<form:hidden path="financialYear" id="hfinancialYear" />
							<form:hidden path="featureOrBaseFee" id="hfeatureOrBaseFee" />
							<form:hidden path="operatorIndi" id="hoperatorIndi" />
							<form:hidden path="status" id="hstatus" />
							<input type="hidden" id="rebateNewRecord" value="${rebateNewRecord}"/>
						</div>
						<div class="panel-body">
						<c:if test="${ viewTypeRebate eq 'V'}">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
										<td><label><spring:message code="rebate.cardType" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.cardTypeName }</td>
										<td><label><spring:message code="rebate.financialYear" /><span
												style="color: red"></span></label></td>
										<td>${rebateInfoDto.financialYear }</td>
										</tr>
										<tr>
										<td><label><spring:message code="rebate.featureOrBaseFee" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.featureOrBaseFee}</td>
										<td><label><spring:message code="rebate.operationalIndi" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.operatorIndi}</td>
										</tr>
										<tr>
										<td><label><spring:message code="rebate.newCardCount1" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.newCardCount1}</td>
										<td><label><spring:message code="rebate.newCardCount2" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.newCardCount2}</td>
										</tr>
										<tr>
										<td><label><spring:message code="rebate.rebatePercentage" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.rebatePercentage}</td>
										<td><label><spring:message code="rebate.rebateStatus" /><span style="color: red"></span></label></td>
										<td><c:if test="${rebateInfoDto.status=='A' }">Active</c:if></td>
									</tr>

								</tbody>
							</table>
						</c:if>
						
						
						<c:if test="${ (viewTypeRebate eq 'E') || (viewTypeRebate eq 'P')}">
							<c:if test="${ rebateInfoDto.requestState ne 'P'}">
								<table class="table table-striped" style="font-size: 12px">
								<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
									<tbody>
										<tr>
											<td><label><spring:message code="rebate.cardType" /><span style="color: red"></span></label></td>
											<td><form:select path="cardType" id="vCardType" name="VCardType"
										maxlength="10" cssClass="form-control medantory">
										<form:option value="SELECT" label="SELECT" />
										<form:options items="${cardTypeList}"  itemValue="code" itemLabel="description"/>
									</form:select>
									<div id="errvCardType" class="error">
									<span for="VCardType" class="error"><form:errors
										id="VCardType" /></span></div>
								</td>
								<td><label><spring:message code="rebate.financialYear" /><span
													style="color: red"></span></label></td>
								<td><form:select path="financialYear" id="VFinancialYear" name="VFinancialYear"
								maxlength="10"
								cssClass="form-control medantory">
								<form:option value="SELECT" lable="SELECT" />
								<form:option value="Present" lable="Present" />
								<form:option value="Previous" lable="Previous" />
								</form:select>
								<div id="errVFinancialYear" class="error">
								<span for="VFinancialYear" class="error"><form:errors
										id="VFinancialYear" /></span></div>
							</td>
							</tr>
							<tr>
							<td><label><spring:message code="rebate.featureOrBaseFee" />
							<span style="color: red"></span></label></td>
							<td><form:select path="featureOrBaseFee" id="VFeatureOrBaseFee" name="VFeatureOrBaseFee"
								maxlength="10" cssClass="form-control medantory">
								<form:option value="SELECT" lable="SELECT" />
								<form:option value="Feature Fee" lable="Feature Fee" />
								<form:option value="Base Fee" lable="Base Fee" />
								</form:select>
								<div id="errVFeatureOrBaseFee" class="error">
								<span for="VFeatureOrBaseFee" class="error"><form:errors
										id="VFeatureOrBaseFee" /></span></div>
							</td>			
							<td><label><spring:message code="rebate.operationalIndi" />
							<span style="color: red"></span></label></td>
								
							<td>	<form:select path="operatorIndi" id="VOperatorIndi" name="VOperatorIndi"
								maxlength="10"
								cssClass="form-control medantory"  onChange="disableToValue()">
								<form:option value="SELECT" lable="SELECT" />
								<form:option value="=" lable="=" />
								<form:option value=">" lable=">" />
								<form:option value="<" lable="<" />
								<form:option value=">=" lable=">=" />
								<form:option value="<=" lable="<=" />
								<form:option value="Between" lable="Between" />
								</form:select>
								<div id="errVOperatorIndi" class="error">
								<span for="VOperatorIndi" class="error"><form:errors
										id="VOperatorIndi" /></span></div>
							</td>
							</tr>
								<tr>
								<td><label><spring:message code="rebate.newCardCount1" />
								<span style="color: red"></span></label></td>
								<td><form:input path="newCardCount1" id="vNewCardCount1" />
								<div id="errvNewCardCount1" class="error">
								<span for="vNewCardCount1" class="error"><form:errors
										id="vNewCardCount1" /></span>
								</div>
								</td> 
								<td><label><spring:message code="rebate.newCardCount2" />
								<span style="color: red"></span></label></td>
								<td><form:input path="newCardCount2" id="vNewCardCount2" />
									<div id="errvNewCardCount2" class="error">
								<span for="vNewCardCount2" class="error"><form:errors
										id="vNewCardCount2" /></span></div>
							</td> 
								</tr>
								<tr>
								<td><label><spring:message code="rebate.rebatePercentage" />
								<span style="color: red"></span></label></td>
								<td><form:input path="rebatePercentage"  id="vRebatePercentage"  />
								<div id="errvRebatePercentage" class="error">
								<span for="vRebatePercentage" class="error"><form:errors
										id="vRebatePercentage" /></span></div>
								</td> 
								<td><label><spring:message code="rebate.rebateStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${rebateInfoDto.status=='A' }">Active</c:if></td>
										</tr>
	
									</tbody>
								</table>
							</c:if>
							<c:if test="${rebateInfoDto.requestState eq 'P'}">
								<table class="table table-striped" style="font-size: 12px">
								<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
									<tbody>
										<tr>
											<td><label><spring:message code="rebate.cardType" /><span style="color: red"></span></label></td>
											<td>${rebateInfoDto.cardTypeName }</td>
											<td><label><spring:message code="rebate.financialYear" /><span
													style="color: red"></span></label></td>
											<td>${rebateInfoDto.financialYear }</td>
											</tr>
											<tr>
											<td><label><spring:message code="rebate.featureOrBaseFee" /><span style="color: red"></span></label></td>
											<td>${rebateInfoDto.featureOrBaseFee}</td>
											<td><label><spring:message code="rebate.operationalIndi" /><span style="color: red"></span></label></td>
											<td>${rebateInfoDto.operatorIndi}</td>
											</tr>
											<tr>
											<td><label><spring:message code="rebate.newCardCount1" /><span style="color: red"></span></label></td>
											<td>${rebateInfoDto.newCardCount1}</td>
											<td><label><spring:message code="rebate.newCardCount2" /><span style="color: red"></span></label></td>
											<td>${rebateInfoDto.newCardCount2}</td>
											</tr>
											<tr>
											<td><label><spring:message code="rebate.rebatePercentage" /><span style="color: red"></span></label></td>
											<td>${rebateInfoDto.rebatePercentage}</td>
											<td><label><spring:message code="rebate.rebateStatus" /><span style="color: red"></span></label></td>
											<td><c:if test="${rebateInfoDto.status=='A' }">Active</c:if></td>
										</tr>
	
									</tbody>
								</table>
							</c:if>
						</c:if>
						</div>
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="rebate.cardVariantInformation" /></span></strong>
						</div>
						<c:if test="${ viewTypeRebate eq 'V'}">
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<c:if test="${not empty cardVariantList}">
									<c:forEach items="${cardVariantList}" var="cardVariant">
									<tr class="selectedRoles1">
									<td>${cardVariant.cardVariantName}</td>
									</tr>
									</c:forEach>
									</c:if>
								</tbody>
							</table>
						</div>
						</c:if>

						<c:if test="${ (viewTypeRebate eq 'E') || (viewTypeRebate eq 'P')}">
							<c:if test="${rebateNewRecord eq 'N' or rebateInfoDto.requestState eq 'P'}">
										
							<div class="panel-body">
								<table class="table table-striped" style="font-size: 12px">
								<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
									<tbody>
										<c:if test="${not empty cardVariantList}">
										<c:forEach items="${cardVariantList}" var="cardVariant">
										<tr class="selectedRoles1">
										<td>${cardVariant.cardVariantName}</td>
										</tr>
										</c:forEach>
										</c:if>
									</tbody>
								</table>
							</div>
							</c:if></c:if>
							<c:if test="${rebateNewRecord eq 'Y' and rebateInfoDto.requestState ne 'P'}">
							<div class="col-xs-6">
	
									<div class="panel panel-default">
										<div class="panel-heading">
											<strong><span class="glyphicon glyphicon-th"></span>
												<span data-i18n="Data"><spring:message code="rebate.availableCardVariant" /></span></strong>
										</div>
										<div >
	
											<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
											<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
													<tr>
														<th scope="col"><spring:message code="rebate.cardVariantName" /></th>
														<th scope="col"><spring:message code="rebate.add" /></th>
													</tr>
												<tbody id="optionList">
													<c:if test="${not empty cardVariantListStg}">
														<c:forEach items="${cardVariantListStg}"
															var="functionality">
																<tr  id="option${functionality.cardVariantId}"
																	>
																	<td>${functionality.cardVariantName}</td>
																	<td>
																	<em class="glyphicon glyphicon-circle-arrow-right" 
																	onclick="addToAssignedList('${functionality.cardVariantId}','${functionality.cardVariantName}')">
																	</em><input type="hidden" id="row' + id
								+ '" value="' + moduleId + '" ></td>
																</tr>
															
														</c:forEach>
													</c:if>
													<div id="errSelectedCard" class="error">
											<span for="SelectedCard" class="error"><form:errors
										id="SelectedCard" /></span>					
	
										</div>
												</tbody>
											</table>
	
	
	
											
	
										</div>
									</div>
	
								</div></c:if>
	
								<div class="col-xs-6">
	
									<div class="panel panel-default">
										<div class="panel-heading">
											<strong><span class="glyphicon glyphicon-th"></span>
												<span data-i18n="Data"><spring:message code="rebate.selectedCardVariant" /></span></strong>
										</div>
										<div class="table-responsive">
	
											<table id="tabnew1" class="table table-striped table-bordered" style="width:100%;">
											<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
													<tr>
														<th scope="col"><spring:message code="rebate.cardVariantName" /></th>
														<th scope="col"><spring:message code="rebate.remove" /></th>
													</tr>
												<tbody id="assignedList">
												<c:if test="${not empty cardVariantList}">
														<c:forEach items="${cardVariantList}"
															var="functionality">
																<tr   class="selectedRoles"  id="remove${functionality.cardVariantId}"
																	>
																	<td>${functionality.cardVariantName}</td>
																	<td>
																	<em class="glyphicon glyphicon-remove-circle" onclick="removeTag('${functionality.cardVariantId}','${functionality.cardVariantName}')">
																	</em></td>
																</tr>
															
														</c:forEach>
													</c:if>	
												</tbody>
											</table>
						
	
										</div>
									</div>
	
								</div>
							</div>						
							
							
						
					</div>
				</div>
							
		
		<div class="row">
			<div class="col-sm-12 bottom_space">
				<hr />
				<div style="text-align:center">

					<c:if test="${ (viewTypeRebate eq 'E') || (viewTypeRebate eq 'P')}">
						<%-- <c:if test="${rebateInfoDto.requestState =='A' }"> --%>
						
						<sec:authorize access="hasAuthority('Edit Rebate')">
							<c:if test="${rebateInfoDto.requestState eq 'R'}">
								<button type="button" id="approveRole" class="btn btn-danger"
									onclick="postDiscardAction('/discardRejectedRebateEntry','${originPage}');"><spring:message code="rebate.discard" /></button>
							</c:if>
						</sec:authorize>
						<%-- </c:if> --%>
						<sec:authorize access="hasAuthority('Add Rebate')">
	
							<c:if test="${rebateInfoDto.requestState ne 'P'}">
								<button type="button" class="btn btn-success" id="bSubmit"
									onclick="saveRebate('','','${originPage}')"><spring:message code="rebate.submit" /></button>
							</c:if>
						</sec:authorize>
					</c:if>
									
					<c:if test="${( viewTypeRebate eq 'V')    && (rebateInfoDto.requestState ne 'P')}">
					<sec:authorize access="hasAuthority('Edit Rebate')">
							<button type="button" class="btn btn-success" id="bEdit"
								onclick="viewRebate('${rebateInfoDto.rebateID}','E','${originPage}')"><spring:message code="rebate.edit" /></button>
					</sec:authorize>
					</c:if>
					
					<c:if test="${viewTypeRebate eq 'V'}">
					<button type="button" class="btn btn-danger"
						onclick="homeRebate('N','/showRebateList','${originPage}');"><spring:message code="rebate.back" /></button>
					</c:if>
					<c:if test="${(viewTypeRebate eq 'E') && (rebateInfoDto.requestState ne 'R')}">
					<button type="button" class="btn btn-danger"
						onclick="homeRebate('N','/showRebateList','${originPage}');"><spring:message code="rebate.back" /></button>
					</c:if>
					<c:if test="${(viewTypeRebate eq 'E') && (rebateInfoDto.requestState eq 'R')}">
					<button type="button" class="btn btn-danger"
						onclick="homeRebate('N','/rebatePendingForApproval','${originPage}');"><spring:message code="rebate.back" /></button> 
					</c:if>
					<c:if test="${viewTypeRebate ne 'V' and viewTypeRebate ne 'E'}">
					<button type="button" class="btn btn-danger"
						onclick="homeRebate('N','/rebatePendingForApproval','${originPage}');"><spring:message code="rebate.back" /></button>
					</c:if>

				</div>
			</div>
		</div>
		</form:form>

	</div>
</div>