function bindAcquirerBinOnloadFunctions() {
	$('#saveAcquirerBin')
		.click(
			function () {
				var check = false;

				if ($('#acqSettlementBin').val() == '0') {
					$('#erracqSettlementBin').find('.error').html('Please select Settlement Bin');
					$('#erracqSettlementBin').show();
					check = true;
				} else {
					$('#erracqSettlementBin').hide();
				}




				if ($('#acqProductType').val() == '0') {
					$('#erracqProductType').find('.error').html('Please select Product Type');
					$('#erracqProductType').show();
					check = true;
				} else {
					$('#erracqProductType').hide();
				}

				if ($('#acqBankGroup').val() == '0') {
					$('#erracqBankGroup').find('.error').html('Please select Bank Group');
					$('#erracqBankGroup').show();
					check = true;
				} else {
					$('#erracqBankGroup').hide();
				}

				if ($('#acqDomainUsage').val() == '0') {
					$('#erracqDomainUsage').find('.error').html('Please select Domain Usage');
					$('#erracqDomainUsage').show();
					check = true;
				} else {
					$('#erracqDomainUsage').hide();
				}

				if (!validateFromCommonVal('acquirerId',
					true, "NumericsOnly", 6, true)) {
					check = true;
				}

				if (!validateFromCommonVal('acqFrmDate',
					true, "DateFormat", 10, false)) {
					check = true;
				}

				if (!validateFromCommonVal('acqToDate',
					true, "DateFormat", 10, false)) {
					check = true;
				}

				if (!check) {
					saveAcquirerBin();
				}
				else {
					return false;
				}

			});

	$('#clearAcquirerBin').click(function () {
		clearAcquirerBinData();
	});
}
function clearAcquirerBinData(){


		$('#acqBankGroup').data('selectize').setValue(0);
		$('#acquirerId').val('');
		$('#oldAcquirerId').val('');
		$('#acquirerId').attr('disabled', false);
		$('#acqDomainUsage').val(0);
		$('#acqSettlementBin').val(0);
		$('#acqFrmDate').val('');
		$("#acqToDate").val('31-12-2099');
		$('#oldOrNewBinFlag').val('NewBin');
		$("#acqOfflineAllowed").val('N');
		$('#erracqBankGroup').hide();
		$('#erracquirerId').hide();
		$('#erracqDomainUsage').hide();
		$('#erracqSettlementBin').hide();
		$('#erracqFrmDate').hide();
		$("#erracqToDate").hide();
		$("#erracqOfflineAllowed").hide();

}
function editAcquirerBin(acquirerId) {
	clearAcquirerBinData();
	var acquirerDataIndex = acquirerBinData.findIndex(obj => obj.acquirerId == acquirerId);
	var acquirerModel = acquirerBinData[acquirerDataIndex];
	if (acquirerModel) {
		 acquirerId = acquirerModel.acquirerId;
		$('#oldAcquirerId').val(acquirerId);
		$('#acqBankGroup').data('selectize').setValue(acquirerModel.acqBankGroup);
		$('#acqBankGroup').val(acquirerModel.acqBankGroup);
		$('#acqDomainUsage').val(acquirerModel.acqDomainUsage);
		$('#acqFrmDate').val(acquirerModel.acqFrmDateStr);
		$('#acqToDate').val(acquirerModel.acqToDateStr)
		$('#acqProductType').val(acquirerModel.acqProductType);
		$('#acqSettlementBin').val(acquirerModel.acqSettlementBin);
		$("#acqOfflineAllowed").val(acquirerModel.offlineAllowed);
		$('#acquirerId').val(acquirerId);
		if (!acquirerModel.isNew) {
		$('#acquirerId').prop("disabled", true);
		}
	}

}

function viewAcquirerBin(acquirerId) {
	var acquirerDataIndex = acquirerBinData.findIndex(obj => obj.acquirerId == acquirerId);
	 acquirerId = $('#acquirerId').val();
	var acquirerModel = acquirerBinData[acquirerDataIndex];
	if (acquirerModel) {
		 acquirerId = acquirerModel.acquirerId;
		$('#acqBankGroupTD').html(acquirerModel.bankGroupName);
		$('#acqDomainUsageTD').html(acquirerModel.domainUsage);
		$('#acqFrmDateTD').html(acquirerModel.acqFrmDateStr);
		$('#acqToDateTD').html(acquirerModel.acqToDateStr)
		$('#acqProductTypeTD').html(acquirerModel.productTypeName);
		$('#acqSettlementBinTD').html(acquirerModel.acqSettlementBin);
		$('#isOfflineAllowedTD').html(acquirerModel.offlineAllowed=='Y'?'Yes':'No');
		$('#acquirerIdTD').html(acquirerId);
	}

}

function blockAcquirerBin(acquirerId) {
openConfirmDialog('Do you want to block the selected acquirer bin?', blockAcquirerBinAction, acquirerId);
}
function blockAcquirerBinAction(acquirerId) {
	var acquirerDataIndex = acquirerBinData.findIndex(obj => obj.acquirerId == acquirerId);
	var acquirerModel = acquirerBinData[acquirerDataIndex];
	if (acquirerModel) {
		acquirerModel.status = 'B';
		acquirerModel.actionType = 'Edit';
	}
	renderAcquirerBinTable();
	unableSave();
}
function unblockAcquirerBin(acquirerId) {
openConfirmDialog('Do you want to unblock the selected acquirer bin?', unblockAcquirerBinAction, acquirerId);
}
function unblockAcquirerBinAction(acquirerId) {
	var acquirerDataIndex = acquirerBinData.findIndex(obj => obj.acquirerId == acquirerId);
	var acquirerModel = acquirerBinData[acquirerDataIndex];
	if (acquirerModel) {
		acquirerModel.status = 'A';
		acquirerModel.actionType = 'Edit';
	}
	renderAcquirerBinTable();
	unableSave();
}

function deleteAcquirerBin(acquirerId) {
openConfirmDialog('Do you want to delete the selected acquirer bin?', deleteAcquirerBinAction, acquirerId);
}
function deleteAcquirerBinAction(acquirerId) {

	var acquirerDataIndex = acquirerBinData.findIndex(obj => obj.acquirerId == acquirerId);
	var acquirerModel = acquirerBinData[acquirerDataIndex];
	if (acquirerModel) {
		if (acquirerModel.isNew) {
			acquirerBinData = acquirerBinData.filter(obj => obj.acquirerId != acquirerId);
		} else {
			acquirerModel.status = 'D';
			acquirerModel.actionType = 'Edit';
		}
	}
	clearAcquirerBinData();
	renderAcquirerBinTable();
	unableSave();
}
function resetAcquirerBinData() {
	acquirerBinData.forEach(function (acquirerModel) {
		acquirerModel.isNew = false;
	});


	renderAcquirerBinTable();
}

function saveAcquirerBin() {

	var acquirerId = $('#acquirerId').val();
	var acquirerModel = {};
	var oldAcquirerId=$('#oldAcquirerId').val();
	if(oldAcquirerId==''){
	  oldAcquirerId=acquirerId;	  
	}
	var acquirerDataIndex = acquirerBinData.findIndex(obj => obj.acquirerId == oldAcquirerId);
	if (acquirerDataIndex < 0) {
		acquirerDataIndex = issuerBinData.findIndex(obj => obj.binNumber == oldAcquirerId);
		}

	if (acquirerDataIndex < 0) {
		acquirerModel = {};
		acquirerModel.acqBankGroup = $('#acqBankGroup').val();
		acquirerModel.acquirerId = acquirerId;
		acquirerModel.acqDomainUsage = $('#acqDomainUsage').val();
		acquirerModel.acqFrmDateStr = $('#acqFrmDate').val();
		acquirerModel.acqToDateStr = $('#acqToDate').val()
		acquirerModel.acqProductType = $('#acqProductType').val();
		acquirerModel.acqSettlementBin = $('#acqSettlementBin').val();
		acquirerModel.offlineAllowed = $("#acqOfflineAllowed").val();
		acquirerModel.status = 'A';
		acquirerModel.actionType = 'Add';
		acquirerModel.isNew = true;
		acquirerBinData.push(acquirerModel);
	}
	else {
	
		if($('#oldAcquirerId').val()!=acquirerId){
	   		var otheracquirerDataIndex = acquirerBinData.findIndex(obj => obj.acquirerId == acquirerId);
	   		var otherIssuerDataIndex = issuerBinData.findIndex(obj => obj.binNumber == acquirerId);
		    if(otheracquirerDataIndex!=-1 || otherIssuerDataIndex !=-1){
		    $("#erracquirerId").find('.error').html("Acquirer ID is existing already");
			$('#erracquirerId').show();
			return false;
			}
		}
		acquirerModel = acquirerBinData[acquirerDataIndex];
		if((acquirerModel.acqToDateStr != $('#acqToDate').val()) || (acquirerModel.acqProductType != $('#acqProductType').val()) || (acquirerModel.acqSettlementBin != $('#acqSettlementBin').val()) || (acquirerModel.offlineAllowed != $("#acqOfflineAllowed").val()) || (acquirerModel.acquirerId != acquirerId) || (acquirerModel.acqBankGroup != $('#acqBankGroup').val()) || (acquirerModel.acqDomainUsage != $('#acqDomainUsage').val()) || (acquirerModel.acqFrmDateStr != $('#acqFrmDate').val())  )
		{ 
			acquirerModel.acquirerId = acquirerId;
			acquirerModel.acqBankGroup = $('#acqBankGroup').val();
			acquirerModel.acqDomainUsage = $('#acqDomainUsage').val();
			acquirerModel.acqFrmDateStr = $('#acqFrmDate').val();
			acquirerModel.acqToDateStr = $('#acqToDate').val();
			acquirerModel.acqProductType = $('#acqProductType').val();
			acquirerModel.acqSettlementBin = $('#acqSettlementBin').val();
			acquirerModel.offlineAllowed = $("#acqOfflineAllowed").val();
			acquirerModel.actionType = 'Edit';
		}	
	}


	$('#acqBankGroup').data('selectize').setValue(0);
	$('#acquirerId').val('');
	$('#acqBinType').val(0);
	$('#acqProductType').val(0);
	$('#acqSettlementBin').val(0);
	$('#acqFrmDate').val('');
	$('#acqDomainUsage').val(0);
	$('#acqToDate').val('31-12-2099');
	$('#acquirerId').attr('disabled', false);
	$("#erracquirerId").find('.error').html('');
	$("#erracquirerId").hide();
	clearAcquirerBinData();
	renderAcquirerBinTable();
	unableSave();
}
function renderAcquirerBinTable() {
	var acquirerBinTableData = "";
	acquirerBinData.forEach(function (acquirerModel) {
		if (acquirerModel.status != 'D') {
			var statusDesc = acquirerModel.status == 'B' ? 'Blocked' : 'Active';
			var rowData = '<tr>' +
				'<td>' + getTrimmedString(acquirerModel.acquirerId) + '</td>' +
				'<td>' + getTrimmedString(domainUsageMapping[acquirerModel.acqDomainUsage]) + '</td>' +
				'<td>' + getTrimmedString(acquirerModel.acqSettlementBin) + '</td>' +
				'<td>' + getTrimmedString(productTypeMapping[acquirerModel.acqProductType]) + '</td>' +
				'<td>' + statusDesc + '</td>' +
				'<td>' +
				'<a href="javascript:editAcquirerBin(\'' + acquirerModel.acquirerId + '\')"><span class="glyphicon glyphicon-pencil my-tooltip" title="EDIT"></span></a>';

			if (acquirerModel.status == 'B') {
				rowData += ' &nbsp; &nbsp; <a href="javascript:unblockAcquirerBin(\'' + acquirerModel.acquirerId + '\')"><span class="glyphicon glyphicon-ok-circle my-tooltip" title="UNBLOCK"></span></a>';
			} else {
				rowData += ' &nbsp; &nbsp; <a href="javascript:blockAcquirerBin(\'' + acquirerModel.acquirerId + '\')"><span class="glyphicon glyphicon-ban-circle my-tooltip " title="BLOCK"></span></a>';
			}
			rowData += ' &nbsp; &nbsp; <a href="javascript:deleteAcquirerBin(\'' + acquirerModel.acquirerId + '\')"><span class="glyphicon glyphicon-trash my-tooltip" title="DELETE"></span></a></td>';
			rowData += '</tr> ';
			acquirerBinTableData += rowData;
		}
	});
	
	$('#acquirerBinsList').html(acquirerBinTableData);
}
function isSettlementAssociatedToAcquirerBin(settlementBin){
 	var acquirerDataIndex = acquirerBinData.findIndex(obj => obj.acqSettlementBin == settlementBin);
	return (acquirerDataIndex < 0);
	
}