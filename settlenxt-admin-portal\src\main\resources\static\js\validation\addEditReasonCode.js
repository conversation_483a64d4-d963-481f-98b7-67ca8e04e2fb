var reasonCodeMasterList = [];
var index = 0;
var datas = {};
var reasonCode;
var reasonCodeDesc;
var status;
var reasonCodeSubType;
var reasonCodeSubTypeDesc;
var reasonType;
var isDuplicate = false;
$(document).ready(function () {
	reasonCode = $('#reasonCode').val();
	reasonCodeDesc = $('#reasonCodeDesc').val();
	reasonCodeSubType = $('#reasonCodeSubType').val();
	reasonCodeSubTypeDesc = $('#reasonCodeSubTypeDesc').val();
	reasonType = $("#reasonType").val();
	status = $('#status').val();
    $("#errreasonCode").hide();
    $("#errreasonCodeDesc").hide();
    $("#errreasonType").hide();
    $('#reasonCode').on('keyup keypress blur change', function() {
    	validateFromCommonVal('reasonCode', true, "NumberOnly", 4, true);
    	validateEditReasonCode();
    	/*validateDuplicate($(reasonCode).val())*/
	});
    $('#reasonCodeDesc').on('keyup keypress blur change', function() {
    	validateFromCommonVal('reasonCodeDesc', true, "AlphaNumericWithSpace", 10, false);
    	validateEditReasonCode();
	});
    $('#status').on('keyup keypress blur change', function() {
    	validateEditReasonCode();
	});
    $('#reasonCodeSubType').on('keyup keypress blur change', function() {
    	validateFromCommonVal('reasonCodeSubType', false, "NumberOnly", 3, true);
    	validateEditReasonCode();
	});
    $('#reasonCodeSubTypeDesc').on('keyup keypress blur change', function() {
    	validateFromCommonVal('reasonCodeSubTypeDesc', false, "AlphabetWithSpace", 10, false);
    	validateEditReasonCode();
	});
    $('#reasonType').on('keyup keypress blur change', function() {
    	validateFromCommonVal('reasonType', true, "SelectionBox", 3, false);
    	validateEditReasonCode();
	});
    $('#bUpdate').prop('disabled', true);
    $('#bSubmit').prop('disabled', true);
    $('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
 
});


function resetAction() {
document.getElementById("addReasonCode").reset();
$("#errreasonCode").find('.error').html('');
$("#errreasonCodeDesc").find('.error').html('');
$("#errreasonCodeSubType").find('.error').html('');
$("#errreasonCodeSubTypeDesc").find('.error').html('');
$("#errreasonType").find('.error').html('');
$('#transitionSuccessMsg').hide();
$('#transitionErrorMsg').hide();

}

window.history.forward();
function noBack() {
    window.history.forward();
}

function display() {
    $(".appRejMust").hide();

}

function userAction(_type, action) {
	var url = action;
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var data = "reasonCode,"+$('#reasonCode').val()+",_TransactToken,"+tokenValue;
	postData(url, data);
} 

function addOrUpdateReasonCode(id) {
	if (id == 'A' && reasonCodeMasterList.length >= 1)
	 {
		addOrUpdateReasonCodeAll();
	}else if (id == 'E') {
		var url = '/updateReasonCode';
		    if (validateField()) {
		       var data = "reasonCode," + $('#reasonCode').val()  + ",reasonCodeDesc," + $('#reasonCodeDesc').val() + ",status," + $('#status').val()+",reasonCodeSubType,"+$('#reasonCodeSubType').val()+",reasonCodeSubTypeDesc,"+$('#reasonCodeSubTypeDesc').val()+",reasonType,"+$('#reasonType').val() ;

		        postData(url, data);
		    }
	}else{
		console.log("");
	}
   
}

function postDiscardReasonCodeAction(action) {
		var url = action;
	reasonCode = document.getElementById("reasonCode").value;
	var data = "reasonCode," + reasonCode  ;
	postData(url, data);
	
}

function saveReasonCode() {
	if(validateField()&& !isDuplicate){
	$('#bSubmit').prop('disabled', false);
	$('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
	datas['id'] = index; 
	datas['reasonCode'] = document.getElementById('reasonCode').value;
	datas['reasonCodeDesc'] = document.getElementById('reasonCodeDesc').value;
	datas['status'] = document.getElementById('status').value;
	datas['reasonCodeSubType'] = document.getElementById('reasonCodeSubType').value;
	datas['reasonCodeSubTypeDesc'] = document.getElementById('reasonCodeSubTypeDesc').value;
	datas['reasonType'] = document.getElementById('reasonType').value;
	reasonCodeMasterList.push(datas);
	
	
	
	var reasonCodeNode = document.createTextNode(datas['reasonCode']);
     var reasonCodeDescNode = document.createTextNode(datas['reasonCodeDesc']);
     var reasonCodeSubTypeNode = document.createTextNode(datas['reasonCodeSubType']);
     var reasonCodeSubTypeDescNode = document.createTextNode(datas['reasonCodeSubTypeDesc']);
     var reasonTypeNode = document.createTextNode(datas['reasonType']);

	if (reasonCodeMasterList.length > 0) {
		var id = reasonCodeMasterList.map(e => e.id).indexOf(datas['id']);
		$('#tabnew').hide();
			
	
	$('#tabnew').append(
                  $('<tr>').attr('id', 'tabnew_' + id).append(
                    $('<td>').append(reasonCodeNode),
                    $('<td>').append(reasonCodeDescNode),
                    $('<td>').append(reasonCodeSubTypeNode),
                    $('<td>').append(reasonCodeSubTypeDescNode),
                    $('<td>').append(reasonTypeNode),
                    $('<td>').append($('<input>').attr({
                      'type': 'button',
                      'class': 'btn btn-danger remReasonCode',
                      'onclick': 'removeReasonCode(' + id + ')',
                      'value': 'Remove',
                    }))
                  )
                );
	
	
	
	$('#tabnew').show();
		index++;
	} else {
		$('#tabnew').hide();
	}
	clear();
	}else if(isDuplicate){
		$("#errreasonCode").find('.error').html("Duplicate Reason Code");
		$('#errreasonCode').show();
	}
}


function addOrUpdateReasonCodeAll() {
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	if (reasonCodeMasterList.length > 0) {
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		var linkurl = pathName + "/addAllReasonCode";
		$.ajax({
			url: linkurl,
			type: "POST",
			dataType: "json",
			data: JSON.stringify(reasonCodeMasterList),
			"beforeSend": function (xhr) {
                xhr.setRequestHeader('_TransactToken', tokenValue);
            },
			contentType: "application/json; charset=utf-8",
			cache: false,
			success: function(response) {
				console.log(response)
				 if (response.status == "BSUC_0001") {
				document.querySelectorAll('.remReasonCode').forEach(item => item.disabled = true);
				document.getElementById('addmultiple').disabled = true;
				document.getElementById('bSubmit').disabled = true;
				document.getElementById('resetReasonCodeMaster').disabled = true;
				document.getElementById('addReasonCode').disabled = true;
				[...document.querySelectorAll("#addReasonCode .row")].splice(0, 2).forEach(item => item.remove())
				$('#transitionSuccessMsg').show();

				 }
				 else {
					 $('#transitionErrorMsg').show();
				 }
				 
			},
			 error: function(_request, _status, _error) {
	                document.getElementById('transitionErrorMsg').style.display = 'block';
	                $('.panel').hide();
	            }
		});
	} else {
	console.log("");
		
	}
}

function validateField() {
	var isValid = true;
	isDuplicate = false;
	if(!validateDuplicate($("#reasonCode").val())){
		isDuplicate = true;
	}
    if (!validateFromCommonVal('reasonCode', true, "Number", 4, true)) {
        isValid = false;
    }
    if (!validateFromCommonVal('reasonCodeDesc', true, "Alphanumeric", 10, false)) {
        isValid = false;
    }
    if (!validateFromCommonVal('reasonCodeSubType', false, "Number", 3, true)) {
        isValid = false;
    }
    if (!validateFromCommonVal('reasonCodeSubTypeDesc', false, "AlphabetWithSpace", 10, false)) {
        isValid = false;
    }
    if(!validateFromCommonVal('reasonType', true, "SelectionBox", 3, false)){
    	isValid = false;
    }
    return isValid;
}

function removeReasonCode(id) {
	if (reasonCodeMasterList.length > 0) {
		$(`#tabnew_${id}`).remove();
	} else {
		$(`#tabnew_${id}`).remove();
		$('#tabnew').hide();
	}
	reasonCodeMasterList.splice(id, 1);
}

function clear() {
	$('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
	document.getElementById('reasonCode').value = "";
	document.getElementById('reasonCodeDesc').value = "";
	document.getElementById('reasonCodeSubType').value = "";
	document.getElementById('reasonCodeSubTypeDesc').value = "";
	document.getElementById('reasonType').value = "";
	datas = {};
}

function validateDuplicate(reasonCode){
	var val;
	var flag = true;
	if(reasonCodeList.includes(parseInt(reasonCode))){
		return false;
	}
	reasonCodeMasterList.forEach(function(reasonCodeDto){if(reasonCodeDto.reasonCode == reasonCode){val =  "false"}});
	if(val == "false"){
		flag = false
	}
	return flag;
}




function validateEditReasonCode() {
	if (reasonCode != document.getElementById("reasonCode").value || reasonCodeDesc != document.getElementById("reasonCodeDesc").value || reasonCodeSubType != document.getElementById("reasonCodeSubType").value || reasonCodeSubTypeDesc != document.getElementById("reasonCodeSubTypeDesc").value || status != document.getElementById("status").value|| reasonType != document.getElementById("reasonType").value) {
		if ($("#bUpdate")) {
			$("#bUpdate").prop("disabled", false);
		}
	} else {
		if ($("#bUpdate")) {
			$("#bUpdate").prop("disabled", true);
		}
	}
}