<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.BudgetRepository">
	
<select id="getAllBudgetsListMain" resultType="budgetDTO">
		SELECT b.vendor as vendor, b.vendor_id as vendorId, b.budget as budget,b.fin_year as year, b.budget_id as budgetId,stg.request_state as requestState 
		FROM  BUDGET_CONFIG b 
		inner join BUDGET_CONFIG_STG stg on b.budget_id=stg.budget_id
		ORDER BY b.last_updated_on desc
	</select>
	
	<select id="getPendingBudgetConfigs" resultType="budgetDTO">
		SELECT vendor as vendor,vendor_id as vendorId,budget as budget,fin_year as year, budget_id as budgetId,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,created_by as createdBy,
		created_on as createdOn,checker_comments as checkerComments,last_operation as lastOperation,request_state as requestState FROM  BUDGET_CONFIG_STG WHERE REQUEST_STATE in ('P','R')
	</select>
	
	<select id="getVendorsforBudget" resultType="budgetDTO">
	      SELECT type as type,code as code, description as description from  lookup where type='MCPR_VENDOR'
	</select>
	
	<insert id="addBudgetConfig">
	   INSERT INTO  BUDGET_CONFIG_STG (BUDGET,fin_year, VENDOR,CREATED_BY,CREATED_ON, LAST_UPDATED_BY,LAST_UPDATED_ON, request_state, last_operation, status, vendor_id) VALUES
            (#{budget},#{year},#{vendor},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{requestState}, #{lastOperation}, #{status},#{vendorId})
              
	</insert>
	
	
	<select id="fetchIdFromBudgetIdSequence" resultType="int">
	  SELECT nextval('budgetid_seq')    
	</select>
	
	<select id="getApprovedBudgetConfigById" resultType="budgetDTO">
	     SELECT vendor as vendor,vendor_id as vendorId,budget as budget,fin_year as year, budget_id as budgetId,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,created_by as createdBy,
		created_on as createdOn from  BUDGET_CONFIG WHERE BUDGET_ID = #{budgetId}
	</select>
	
	<select id="getBudgetConfigById" resultType="budgetDTO">
	     SELECT vendor as vendor,vendor_id as vendorId,budget as budget,fin_year as year, budget_id as budgetId,
	     last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,created_by as createdBy,
		created_on as createdOn,checker_comments as checkerComments,last_operation as lastOperation,
		request_state as requestState from  BUDGET_CONFIG_STG WHERE BUDGET_ID = #{budgetId}
	</select>
	
	<select id="getBudgetConfigByIdMain" resultType="budgetDTO">
	    SELECT b.vendor as vendor,b.vendor_id as vendorId,b.budget as budget,b.fin_year as year, b.budget_id as budgetId,
	    stg.request_state as requestState
	    from  BUDGET_CONFIG b
	    inner join BUDGET_CONFIG_STG stg on b.budget_id=stg.budget_id
	    WHERE b.BUDGET_ID = #{budgetId}
	</select>
	
	<update id="updateBudgetConfig">
	     UPDATE  BUDGET_CONFIG_STG SET BUDGET =#{budget}, LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},REQUEST_STATE =#{requestState},
		last_operation= #{lastOperation},checker_comments = '' where BUDGET_ID = #{budgetId}
	</update>
	<update id="updateBudgetConfigMain">
	     UPDATE  BUDGET_CONFIG SET BUDGET =#{budget},fin_year = #{year}, LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},
		last_operation= #{lastOperation} where BUDGET_ID = #{budgetId}
	</update>
	
	
	<update id="updateBudgetStgState">
	     UPDATE  BUDGET_CONFIG_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, last_updated_on = #{lastUpdatedOn},  last_operation= #{lastOperation},
	     REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} WHERE BUDGET_ID =  #{budgetId}
	</update>
	
	<insert id="saveBudgetConfig" >
	    INSERT INTO  BUDGET_CONFIG(BUDGET_ID,VENDOR,BUDGET,fin_year,LAST_UPDATED_BY,last_updated_on,last_operation,created_on,created_by,vendor_id) values( #{budgetId},#{vendor}
			,#{budget},#{year},#{lastUpdatedBy},#{lastUpdatedOn},#{lastOperation},#{createdOn},#{createdBy},#{vendorId})
	</insert>
	
	<select id="getPendingBudgetConfigById" resultType="budgetDTO">
	    SELECT vendor as vendor,vendor_id as vendorId,budget as budget,fin_year as year, budget_id as budgetId,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,created_by as createdBy,
		created_on as createdOn,checker_comments as checkerComments,last_operation as lastOperation,request_state as requestState FROM  BUDGET_CONFIG_STG WHERE BUDGET_ID= #{budgetId}
	</select>
	
	<delete id="deleteDiscardedEntry">
	    DELETE FROM  BUDGET_CONFIG_STG WHERE BUDGET_ID = #{budgetId}
	</delete>
	
	<select id="getBudgetStgData" resultType="budgetDTO">
	   SELECT vendor as vendor,vendor_id as vendorId,budget as budget,fin_year as year, budget_id as budgetId,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,created_by as createdBy,
	   created_on as createdOn,checker_comments as checkerComments,last_operation as lastOperation,request_state as requestState FROM  BUDGET_CONFIG_STG where budget_id = #{budgetId}
	</select>
	
	
	
	<select id="getBudgetMainData" resultType="budgetDTO">
			SELECT vendor as vendor,budget as budget,fin_year as year, budget_id as budgetId,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,last_operation as lastOperation,created_by as createdBy,
			created_on as createdOn from  BUDGET_CONFIG where budget_id = #{budgetId}
	</select>
	
	<select id="getVendorById" resultType="String">
			SELECT description as description FROM  LOOKUP WHERE TYPE = 'MCPR_VENDOR' and CODE = #{vendorId}
	</select>
	
	
	<select id="getApprovedBudgetByVendorId" resultType="budgetDTO">
			SELECT vendor as vendor,vendor_id as vendorId,budget as budget, budget_id as budgetId,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,created_by as createdBy,
	   created_on as createdOn,last_operation as lastOperation,request_state as requestState FROM  BUDGET_CONFIG_STG WHERE VENDOR_ID = #{vendorId} and request_state IN ('A','P','R') and fin_year = #{year}
	</select>
		

</mapper>	