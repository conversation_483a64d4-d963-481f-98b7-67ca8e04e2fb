	<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
	<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
	<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">


														<div class="panel panel-default" id="settlementPanel">
															<div class="panel-heading">
																<h4 class="panel-title">
																	<a data-toggle="collapse" data-parent="#accordion"
																		href="#collapseLast" id="collapseLastLink">Settlement
																		Information<span
																		class="glyphicon glyphicon-plus areaShowHde  "></span>
																	</a>
																</h4>
															</div>
															<div id="collapseLast"
																class="panel-collapse collapse panelHideShow">
																<div class="panel-body">
																	<div class="">
																		<div class="row">
																			<div class="col-md-12">
																				<div class="card">
																					<div class="card-body">
																						<div class="row">
																							<div class="col-md-2">
																								<div class="form-group">
																									<label for="squareSelect"><spring:message code="member.currencyConversion"/><span class="red">*</span>
																									</label>
																									<form:select path="currencyConversionBy"
																										id="currencyConversionBy" class="form-control">
																										<form:option value="0">
																											<spring:message code="msg.lbl.select"></spring:message>
																										</form:option>
																										<form:options itemLabel="description"
																											itemValue="code" items="${currencyConvList}" />
																									</form:select>
																									<div id="errcurrencyConv">
																										<span for="currencyConversionBy" class="error"><form:errors
																												path="currencyConversionBy" /></span>
																									</div>
																								</div>
																							</div>
																							<div class="col-md-2">
																								<div class="form-group">
																									<label for="squareSelect"><strong><spring:message code="member.isNPCISettlement"/></strong> <span class="red">*</span> </label>
																									<div>
																										<form:select path="isType"
																										id="isType" class="form-control">
																										<form:option value="0">
																											<spring:message code="msg.lbl.select"></spring:message>
																										</form:option>
																										<form:option value="Y">
																											Yes
																										</form:option>
																										<form:option value="N">
																											No
																										</form:option>
																										</form:select>
																									</div>
																									<div id="errisType">
																										<span for="isType" class="error"></span>
																										<form:errors path="isType" />
																									</div>
																								</div>
																							</div>

																							<div class="col-md-2">
																								<div class="form-group">
																									<label for="squareSelect"><spring:message code="member.currencyConversionType"/><span class="red">*</span>
																									</label>
																									<form:select path="currencyConversionType"
																										id="currencyConversionType" class="form-control">
																										<form:option value="0">
																											<spring:message code="msg.lbl.select"></spring:message>
																										</form:option>
																										<form:options itemLabel="description"
																											itemValue="code"
																											items="${currencyConvTypeList}" />
																									</form:select>
																									<div id="errcurrencyConvType">
																										<span for="currencyConversionType" class="error"><form:errors
																												path="currencyConversionType" /></span>
																									</div>
																								</div>
																							</div>

																							<div class="col-md-2">
																								<div class="form-group">
																									<label for="squareSelect"><spring:message code="member.forexId"/><span
																										class="red">*</span></label>
																									<form:select path="forexId" id="forexId"
																										class="form-control">
																										<form:option value="0">
																											<spring:message code="msg.lbl.select"></spring:message>
																										</form:option>
																										<form:options itemLabel="description"
																											itemValue="code" items="${forexIdList}" />
																									</form:select>
																									<div id="errforexId">
																										<span for="forexId" class="error"><form:errors
																												path="forexId" /></span>
																									</div>
																								</div>
																							</div>

																						</div>
																					</div>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>
												

										
											
											</html>
											