package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class HolidayMasterDTO {
	private String holidaySeqId;
	private Date holidayDate;
	private Date fromDate;
	private Date toDate;
	private String holidayDesc;
	private String periodType;
	private String product;
	private String dayOfWeek;
	private String weekType;
	private String status;
	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private String requestState;
	private String checkerComments;
	private String lastOperation;
	private String statusCode;
	private int hseqId;

}
