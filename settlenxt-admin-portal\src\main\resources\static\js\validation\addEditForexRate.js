$(document).ready(
    function () {
        $("form :input").change(function () {
            $(this).closest('form').data('changed', true);
        });
        $('#networkId').on('keyup keypress blur change', function () {
            validateField('networkId', { isMandatory: true, fieldType: "SelectionBox", isExactLength: false, length: 0, isRange: false, minNumber: 1, maxNumber: 99999999999 });
            getConversionTypesBasedOnNetwork();
        })
        $('#rateConversion').on('keyup keypress blur change', function () {
            validateField('rateConversion', { isMandatory: true, fieldType: "Decimal", isExactLength: false, length: 1000000, isRange: true, minNumber: 0.000000, maxNumber: 999999.999999 });
        })
        $('#settleDate').on('keyup keypress blur change', function () {
            validateField('settleDate', { isMandatory: true, fieldType: "Date", isExactLength: false, length: 100, isRange: false, minNumber: 1, maxNumber: 9999 });
        })
        $('#currencyFrom').on('keyup keypress blur change', function () {
            validateField('currencyFrom', { isMandatory: true, fieldType: "SelectionBox", isExactLength: false, length: 0, isRange: false, minNumber: 1, maxNumber: 99999999999 });
        })
        $('#currencyTo').on('keyup keypress blur change', function () {
           validateField('currencyTo', { isMandatory: true, fieldType: "SelectionBox", isExactLength: false, length: 0, isRange: false, minNumber: 1, maxNumber: 99999999999 })
        })

        $("#errnetworkId").hide();
        $("#errrateConversion").hide();
        $("#errsettleDate").hide();
        $("#errcurrencyFrom").hide();
        $("#errcurrencyTo").hide();


        disableSave();
        $("#networkId").on("change", function () {
            enableSave();
        });
        $("#rateConversion").on("change keypress", function () {
            enableSave();
        });
        $("#settleDate").on("change", function () {
            enableSave();
        });
        $("#currencyFrom").on("change", function () {
            enableSave();
        });
        $("#currencyTo").on("change", function () {
            enableSave();
        });
        $("#settleDate").datepicker({
            //dateFormat : "dd-mm-yy",
            dateFormat: "yy-mm-dd",
            changeMonth: true,
            changeYear: true,
            maxDate: 0,
            yearRange: "2021:2099",
            //minDate : 0,
            onClose: function () {
                $("#settleDate").datepicker("option", "maxDate", "0");
                //
            }
        });
        if(document.getElementById("submitForexRate")){
         document.getElementById("submitForexRate").disabled = true;
         }

    });



function getConversionTypesBasedOnNetwork() {
	let networkId = $('#networkId').val();
	let tokenValue = document.getElementsByName("_TransactToken")[0].value;
	
	if(networkId==""){
		const tableBody = document.querySelector("#conversionTable tbody");
		tableBody.innerHTML = ''; // Clear existing rows
	}

	if (networkId != "") {
		$.ajax({
			url: "getConversionTypes",
			type: "POST",
			dataType: "json",
			data: {
				"networkId": networkId,
				"_TransactToken": tokenValue
			},
			success: function(response) {
				const tableBody = document.querySelector("#conversionTable tbody");
				tableBody.innerHTML = ''; // Clear existing rows
                document.getElementById("submitForexRate").disabled = false;
				response.forEach(item => {
					const row = document.createElement("tr");

					const conversionCell = document.createElement("td");
					conversionCell.textContent = `${item.currencyFrom} to ${item.currencyTo}`;
					row.appendChild(conversionCell);

					const rateCell = document.createElement("td");
					const rateInput = document.createElement("input");
					rateInput.type = "text";
					rateInput.value = item.rateConversion;
					rateInput.className = "rate-conversion";
					rateInput.dataset.networkId=item.networkId;
					rateInput.dataset.forexRateId = item.forexRateId;
       				rateInput.dataset.currencyFrom = item.currencyFrom;
        			rateInput.dataset.currencyTo = item.currencyTo;
					rateCell.appendChild(rateInput);
					
					const errorSpan = document.createElement("span");
					errorSpan.className = "error-message";
					errorSpan.style.color = "red";
					rateCell.appendChild(errorSpan);
					
					row.appendChild(rateCell);

					tableBody.appendChild(row);
				});
			}
		});
	}
}

function addForexRate(url) {
    let rateInputs = document.querySelectorAll('.rate-conversion');
    let isValid = true;
    
    if (!validateField('networkId', { isMandatory: true, fieldType: "SelectionBox", isExactLength: false, length: 0, isRange: false, minNumber: 1, maxNumber: 99999999999 }) && isValid) {
        return;
    }

    const updatedRates = Array.from(rateInputs).map(input => {
        const rateConversion = parseFloat(input.value);
        const errorSpan = input.nextElementSibling;
        let regEx =/^\d{0,6}(\.\d{0,6})?$/;
        if (isNaN(rateConversion) || rateConversion < 0 || !regEx.test(rateConversion)) {
            errorSpan.textContent = "Invalid rate conversion value";
            isValid = false;
        }else {
            errorSpan.textContent = "";
        }

        return {
            forexRateId: input.dataset.forexRateId,
            networkId:input.dataset.networkId,
            currencyFrom: input.dataset.currencyFrom,
            currencyTo: input.dataset.currencyTo,
            rateConversion: rateConversion
        };
    });

    if (!isValid) {
        return; 
    }
   addForexRateList(url, updatedRates);
}

function addForexRateList(url, updatedRates){
	let data="json,"+convertJsonToStringWithoutCommas(updatedRates);
	postData(url, data);
}

function convertJsonToStringWithoutCommas(jsonArray) {
    // Convert the JSON array to a string
    let jsonString = JSON.stringify(jsonArray);
    // Remove all commas from the string
    jsonString = jsonString.replace(/,/g, '');
    return jsonString;
}


function disableSave() {
    if (typeof bEdit != "undefined") {
        document.getElementById("bEdit").disabled = true;
    }
}

function enableSave() {
    if (typeof bEdit != "undefined") {
        document.getElementById("bEdit").disabled = false;
    }
}

window.history.forward();
function noBack() {
    window.history.forward();
}

function resetAction() {
    document.getElementById("addEditForexRate").reset();
    $("#errnetworkId").find('.error').html('');
    $("#errrateConversion").find('.error').html('');
    $("#errsettleDate").find('.error').html('');
    $("#errcurrencyFrom").find('.error').html('');
    $("#errcurrencyTo").find('.error').html('');

}

function viewForexRateAdd(url, type, parentPage) {

    var isValid = true;
    if (!validateField('networkId', { isMandatory: true, fieldType: "SelectionBox", isExactLength: false, length: 0, isRange: false, minNumber: 1, maxNumber: 99999999999 }) && isValid) {
        isValid = false;
    }
    if (!validateField('rateConversion', { isMandatory: true, fieldType: "Decimal", isExactLength: false, length: 1000000, isRange: true, minNumber: 0.000000, maxNumber: 999999.999999 }) && isValid) {
        isValid = false;
    }

    if (!validateField('settleDate', { isMandatory: true, fieldType: "Date", isExactLength: false, length: 100, isRange: false, minNumber: 1, maxNumber: 9999 }) && isValid) {
        isValid = false;
    }

    if (!validateField('currencyFrom', { isMandatory: true, fieldType: "SelectionBox", isExactLength: false, length: 0, isRange: false, minNumber: 1, maxNumber: 99999999999 }) && isValid) {
        isValid = false;
    }
    if (!validateField('currencyTo', { isMandatory: true, fieldType: "SelectionBox", isExactLength: false, length: 0, isRange: false, minNumber: 1, maxNumber: 99999999999 }) && isValid) {
        isValid = false;
    }
    if ($("#currencyFrom").val() != "" && $("#currencyFrom").val() == $("#currencyTo").val()) {
        $("#err" + 'currencyFrom').find('.error').html("Currency Conversion must be different");
        $("#errcurrencyFrom").show();
        $("#err" + 'currencyTo').find('.error').html("Currency Conversion must be different");
        $("#errcurrencyTo").show();
        isValid = false;
    }
    submitForexRate(isValid, url, type, parentPage);
}

function submitForexRate(isValid, url, type, parentPage) {
    if (isValid) {
        var data;
        var tokenValue;
        if (type == 'E') {
            tokenValue = "lIpLsRjLtLDPSzoS2xPf9WXiF/M=";
            data = "forexRateId," + $("#forexRateId").val() + ",networkId," + $("#networkId").val() + ",rateConversion," + $("#rateConversion").val() + ",settleDate," + $("#settleDate").val() + ",currencyFrom," + $("#currencyFrom").val() + ",currencyTo," + $("#currencyTo").val() + ",_vTransactToken," + tokenValue + ",parentPage," + parentPage;
        } else if (type == 'A') {
            data = "networkId," + $("#networkId").val() + ",rateConversion," + $("#rateConversion").val() + ",settleDate," + $("#settleDate").val() + ",currencyFrom," + $("#currencyFrom").val() + ",currencyTo," + $("#currencyTo").val() + ",_vTransactToken," + tokenValue;
        }
        postData(url, data)
    }
}


function validateField(fieldId, options) {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    var regEx;
    if (options.isMandatory && fieldValue.trimStart().trimEnd() === "" && options.fieldType == "SelectionBox") {
        isValid = false;
    }
    if (options.fieldType == "Decimal") {
        regEx =/^\d{0,6}(\.\d{0,6})?$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (options.fieldType == "Date") {
        if (fieldValue == "") {
            isValid = false;
        }
        regEx = /(\d{4}-\d{2}-\d{2})/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (options.isExactLength && fieldValue.length != options.length) {
        isValid = false;
    }
    if (options.isRange && !(Number(fieldValue) >= Number(options.minNumber) && Number(fieldValue) <= Number(options.maxNumber))) {
        isValid = false;
    }
    return createErrorMsg(isValid, fieldId, fieldValue);
}

function createErrorMsg(isValid, fieldId, fieldValue) {
    if (isValid) {
        $("#err" + fieldId).hide();
    } else {
        const errMsg = forexRateValidationMessages[fieldId];
        switch (fieldId) {
            case "rateConversion":
                if (fieldValue <= 0) {
                    $("#err" + fieldId).find('.error').html("Rate Conversion should be greater than 0");
                } else if (typeof fieldValue == "string") {
                    $("#err" + fieldId).find('.error').html("Rate Conversion should be numeric");
                } else if (errMsg) {
                    $("#err" + fieldId).find('.error').html(errMsg);
                }
                break;
            case "settleDate":
                if (fieldValue != "") {
                    $("#err" + fieldId).find('.error').html("Settle Date should be in yyyy-mm-dd");
                } else if (errMsg) {
                    $("#err" + fieldId).find('.error').html(errMsg);
                }
                break;
            default:
                if (errMsg) {
                    $("#err" + fieldId).find('.error').html(errMsg);
                }
                break;
        }
        $("#err" + fieldId).show();
    }
    return isValid;
}

function userAction(action) {
    var data = "status," + status;
    postData(action, data);
}


