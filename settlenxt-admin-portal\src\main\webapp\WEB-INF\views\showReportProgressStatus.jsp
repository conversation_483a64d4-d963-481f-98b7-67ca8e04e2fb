<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>

<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/selectize.bootstrap3.min.css" integrity="sha256-ze/OEYGcFbPRmvCnrSeKbRTtjG4vGLHXgOqsyLFTRjg=" crossorigin="anonymous" />
<link rel="stylesheet" href="./static/css/bootstrap-multiselect.css" type="text/css" />
<script src="./static/js/selectize.min.js" integrity="sha256-+C0A5Ilqmu4QcSPxrlGpaZxJ04VjsRjKu+G82kl5UJk=" crossorigin="anonymous"></script>
<script src="./static/js/jquery-3.6.0.js"></script>
<script src="./static/js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="./static/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="./static/js/dataTables.bootstrap.min.js"></script>
<script type="text/javascript" src="./static/js/dataTables.fixedHeader.min.js"></script>
<script type="text/javascript" src="./static/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="./static/js/responsive.bootstrap.min.js"></script>
<script type="text/javascript" src="./static/js/jszip.min.js"></script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js"></script>
<script type="text/javascript" src="./static/js/custom_js/vTransact.js"></script>
<script type="text/javascript" src="./static/js/bootstrap-multiselect.js"></script>
<script type="text/javascript" src="./static/js/validation/commonValidation.js"></script>
<script type="text/javascript"> var actionColumnIndex = 10; </script>
<script src="./static/js/validation/reportProgressStatus.js" type="text/javascript"></script>

<head>
<title></title>
	<style>
		.defaultexport {
			visibility: hidden;
		}
		
		table.dataTable thead {
			vertical-align: top;
		}
		
		table.dataTable thead .sorting {
			vertical-align: top;
			background: url('./static/images/sort_both.png') no-repeat center right;
		}
		
		table.dataTable thead .sorting_asc {
			vertical-align: top;
			background: url('./static/images/sort_asc.png') no-repeat center right;
		}
		
		table.dataTable thead .sorting_desc {
			vertical-align: top;
			background: url('./static/images/sort_desc.png') no-repeat center right;
		}
		
		table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
			{
			vertical-align: top;
			content: ""
		}
		
		table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
			{
			vertical-align: top;
			content: ""
		}
		
		.search-box {
			-webkit-box-sizing: border-box;
			-moz-box-sizing: border-box;
			box-sizing: border-box;
			background-color: transparent;
			width: 100%;
			border-width: 1px;
			border-style: inset;
		}
		
		.margin-top-0px{
			margin-top: 0px !important;
			text-align: left;
		}
	</style>
</head>
<script type="text/javascript" src="./static/js/bootstrap-datepicker.min.js"></script>
<link rel="stylesheet"
	href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.4.1/css/bootstrap-datepicker3.css" />
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
	</ul>

	<div class="row">
		<div class="col-sm-12">
			<div class="panel panel-default">
				<div class="panel-heading">
					<strong>
						<span class="glyphicon glyphicon-th"></span> 
						<span data-i18n="Data">
							<spring:message code="reportProgressStatus.title" />
						</span>
					</strong>
				</div>
				<div class=" ">
					<div role="tabpanel" class="tab-pane active" id="home">
						<form:form onsubmit="return encodeForm(this);" method="POST" id="reportProgressStatusForm" 
									modelAttribute="cycleManagementDTO" action="/SNxtAdminPortal/reportProgressStatus" autocomplete="off">
							<br />
							<div class="row">
								<div class="col-sm-12">
									<div class="col-sm-2">
										<div class="form-group">
											<label for=""><spring:message code="reportProgressStatus.productId" /><span class="red">*</span></label>
											<form:select path="settlementProductId" id="select-settlementProductId"
                                                name="settlementProductId" class="form-control" >
                                                <option value = "" disabled selected>Select product Id</option>
                                                <c:forEach var="settleProductName" items="${cycleManagementDTO.settleProduct}">
												<option value="${settleProductName.key}" data-product_id = "${settleProductName.value}">${settleProductName.key}</option>
												</c:forEach>
                                            </form:select>
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label for=""><spring:message code="reportProgressStatus.cycleDate" /><span class="red">*</span></label>
											<form:input path="cycleDate" id="cycleDate" data-date-format="yyyy-mm-dd"
											cssClass="form-control medantory" max="3000-01-01" 
											onfocus="this.max=new Date().toISOString().split('T')[0]"  />
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label>
												<spring:message code="reportProgressStatus.cycleNumber" />
											</label>
											<form:select path="cycleNumber" id="select-cycleNumber" 
												name="cycleNumber" class="form-control" >
												<form:option value="ALL">All</form:option>
											</form:select>
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label>
												<spring:message code="reportProgressStatus.bankType" />
											</label>
											<form:select path="bankType" id="select-bankType" placeholder="Pick a bank..."
												name="bankType" class="form-control" >
												<form:option value="NPCI">NPCI</form:option>
												<form:option value="PARTICIPANT">PARTICIPANT</form:option>
											</form:select>
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label>
												<spring:message code="reportProgressStatus.reportStatus" />
											</label>
											<form:select path="reportStatusArr" id="select-reportStatus" multiple="multiple"
												name="reportStatusArr" class="form-control multiselect" >
												<form:option value="INPROGRESS">Pending</form:option>
												<form:option value="COMPLETED">Completed</form:option>
												<form:option value="FAILED">Failed</form:option>
											</form:select>
										</div>
									</div>
									<div class="col-sm-2">
										<div class="text-right">
												<button type="button" class="btn btn-success" id="search">
													<spring:message code="am.lbl.search" />
												</button>
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class=" ">
								</div>
							</div>
						</form:form>
					</div>
				</div>
				<div id="afterSave">
					<div class=" ">
						<div class="row">
							<div class="col-sm-12">
								<div class=" ">
									<div class=" ">
									</div>
									<div class="panel-body">
										<div class="row">
											<div class="col-sm-12">
												<%-- <button class="btn  pull-right btn_align" id="clearFilters">
													<spring:message code="ifsc.clearFiltersBtn" />
												</button> 
												&nbsp;
												<a class="btn btn-success pull-right btn_align" href="#" id="excelExport">
													<spring:message code="ifsc.exportBtn" />
												</a> 
												&nbsp; 
												<a class="btn btn-success pull-right btn_align" href="#" id="csvExport">
													<spring:message code="ifsc.csvBtn" />
												</a> --%>
											</div>
										</div>
										<div class="table-responsive">
											<table id="tabnew" class="table table-bordered">
											<caption style="display:none;">Show Report Progress Statu</caption>
												<thead>
													<tr>
														<th scope= "col">Date</th>
														<th scope= "col">Bank Type</th>
														<th scope= "col">Name</th>
														<th scope= "col">Bank Id</th>
														<th scope= "col">Cycle</th>
														<th scope= "col">Status</th>
														<th scope= "col">RE-ID</th>
														<th scope= "col">Progress Date</th>
														<th scope= "col">Completed/Failed Date</th>
														<th scope= "col">Retry</th>
													</tr>
												</thead>
												<tbody>
												 	<c:if test="${empty cycleManagementDTO.cycleData}">
														<tr>
													       <td colspan="10">No data present for selected filter.</td>
													    </tr>
													</c:if>
													<c:if test="${not empty cycleManagementDTO.cycleData}">
														<c:forEach var="cycleData" items="${cycleManagementDTO.cycleData}">
														    <c:choose>
															    <c:when test="${cycleData.reportStatus eq 'COMPLETED'}">
															       <tr class="bg-success">
															    </c:when>
															    <c:when test="${cycleData.reportStatus eq 'PENDING'}">
															    	<tr class="bg-warning">
															    </c:when>
															    <c:when test="${cycleData.reportStatus eq 'FAILED'}">
															    	<tr class="bg-danger">
															    </c:when>
															    <c:otherwise>
															       <tr>
															    </c:otherwise>
															</c:choose>
														    
														    <%-- <c:if test="${cycleData.reportStatus eq 'COMPLETED'}">
														    	<tr class="bg-success">
														    </c:if>
														    <c:if test="${cycleData.reportStatus eq 'PENDING'}">
														    	<tr class="bg-warning">
														    </c:if>
														    <c:if test="${cycleData.reportStatus eq 'FAILED'}">
														    	<tr class="bg-danger">
														    </c:if> --%>
														       <td>${cycleData.cycleDate}</td>
														       <td>${cycleData.bankType}</td>
														       <td>${cycleData.reportName}</td>
														       <td>${cycleData.bankId}</td>
														       <td>${cycleData.cycleNumber}</td>
														       <td>${cycleData.reportStatus}</td>
														       <td>${cycleData.reId}</td>
														       <td>${cycleData.inProgressDateTime}</td>
														       <td>${cycleData.completedFailedDateTime}</td>
														       <td>${cycleData.retryCount}</td>
														    </tr>
														</c:forEach>
													</c:if>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>