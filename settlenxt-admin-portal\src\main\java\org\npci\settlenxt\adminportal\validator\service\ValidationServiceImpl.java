package org.npci.settlenxt.adminportal.validator.service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.npci.settlenxt.adminportal.common.cache.SettlementCycleCache;
import org.npci.settlenxt.adminportal.common.mapping.MessageFormatNameMapping;
import org.npci.settlenxt.adminportal.common.mapping.NameMappingContext;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.LoadRejectReasonCode;
import org.npci.settlenxt.adminportal.common.util.ValidationUtils;
import org.npci.settlenxt.adminportal.common.xml.XML;
import org.npci.settlenxt.adminportal.common.xml.XMLTokener;
import org.npci.settlenxt.adminportal.config.kafka.utils.KafkaUtilService;
import org.npci.settlenxt.adminportal.repository.CashbackFileUploadRepository;
import org.npci.settlenxt.adminportal.repository.FileUploadRepository;
import org.npci.settlenxt.adminportal.repository.UserRepository;
import org.npci.settlenxt.adminportal.service.FileRejectService;
import org.npci.settlenxt.adminportal.service.SettlementCycleConfigService;
import org.npci.settlenxt.adminportal.validator.check.BodyValidationCheck;
import org.npci.settlenxt.adminportal.validator.check.HeaderValidationCheck;
import org.npci.settlenxt.adminportal.validator.check.TrailerValidationCheck;
import org.npci.settlenxt.adminportal.validator.service.dto.CustomMappingUtils;
import org.npci.settlenxt.adminportal.validator.service.dto.FileDetail;
import org.npci.settlenxt.adminportal.validator.service.dto.FileError;
import org.npci.settlenxt.adminportal.validator.service.dto.HeaderRecord;
import org.npci.settlenxt.adminportal.validator.service.dto.Record;
import org.npci.settlenxt.adminportal.validator.service.dto.RecordError;
import org.npci.settlenxt.adminportal.validator.service.dto.TrailerRecord;
import org.npci.settlenxt.adminportal.validator.service.dto.TransactionRecord;
import org.npci.settlenxt.adminportal.validator.service.dto.ValidationResult;
import org.npci.settlenxt.portal.common.dto.BinDTO;
import org.npci.settlenxt.portal.common.dto.CashBackFileUploadDTO;
import org.npci.settlenxt.portal.common.dto.FileRejectDTO;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;
import org.npci.settlenxt.portal.common.dto.UserDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.model.CycleDetailsModel;
import org.npci.settlenxt.portal.common.util.FileNameUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.supercsv.cellprocessor.Optional;
import org.supercsv.cellprocessor.ift.CellProcessor;
import org.supercsv.io.CsvBeanReader;
import org.supercsv.io.CsvBeanWriter;
import org.supercsv.io.ICsvBeanReader;
import org.supercsv.io.ICsvBeanWriter;
import org.supercsv.prefs.CsvPreference;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;


/**
 * Process file validation
 * <AUTHOR>
 *
 */

@Service
public class ValidationServiceImpl implements IValidationService{
	
	private static final Logger log = LogManager.getLogger(ValidationServiceImpl.class);
	private DateTimeFormatter formatterYYMMDD = DateTimeFormatter.ofPattern("dd/MM/yyyy");


	@Autowired
	FileUploadRepository fileUploadRepository;
	
	@Autowired
	UserRepository userRepository;
	
	@Autowired
	FileRejectService fileRejectService;
	
	@Autowired
	private Environment environment;
	
	@Autowired
	LoadRejectReasonCode rejReasonCode;
	
	@Autowired
	private SettlementCycleConfigService settlementCycleConfigService;
	
	@Autowired
	private CashbackFileUploadRepository cashbackFileRepository;
	
	@Autowired
	private KafkaUtilService kafkaUtils;
	
	@Autowired
	SettlementCycleCache settlementCycleCache;

	
	
	private static final String ERROR = "ERROR";
	private static final String NATMTXN = "nAmtTxn";
	private static final String NFEEAMT = "nFeeAmt";
	private static final String NFUNCD = "nFunCd";
	
	
	@Override
	public ValidationResult validateFile(FileUploadDTO fileUploadDTO) {
		fileUploadDTO.setStatus("P");
		fileUploadDTO.setLastUpdatedOn(new Date());
		fileUploadRepository.updateFileUploadInfo(fileUploadDTO);
		return validate(fileUploadDTO);
	}

	private ValidationResult validate(FileUploadDTO fileUploadDTO) {
		List<String> processors = null;
		List<String> participantIds = null;
		String userParticipantId = null;
		LocalDateTime currTime = LocalDateTime.now();

		ValidationResult validationResult = new ValidationResult();
		validationResult.setFileId(fileUploadDTO.getDocumentId());
		validationResult.setFilePath(fileUploadDTO.getDocumentPath());
		validationResult.setFileName(fileUploadDTO.getDocumentName());
		validationResult.setFileStatus(fileUploadDTO.getStatus());
		validationResult.setInsertDayStr(fileUploadDTO.getInsertDay().toLocalDate().toString());
		validationResult.setFileGenDateTime(currTime.format(formatterYYMMDD));
		boolean process = true;
		try {
			UserDTO userDtos = userRepository.getUser(fileUploadDTO.getCreatedBy().toUpperCase(Locale.ROOT));
			userParticipantId = userDtos.getBankParticipantId();
			List<BinDTO> binDetailsList = userRepository.getBinDetailListByUserId(userDtos.getUserId());
			if(CollectionUtils.isEmpty(binDetailsList)) {
				throw new SettleNxtException("5153", "User and bin mapping not found");
			}
			List<Integer> binByParticipantId = binDetailsList.stream().map(BinDTO::getBinId).distinct().collect(Collectors.toList());
			String binId = binByParticipantId.stream().map(Object::toString).collect(Collectors.joining(","));
			List<BinDTO> binList = userRepository.getBinListByBinIds(binId);
			processors = binList.stream().map(BinDTO::getSettlementBin).distinct().collect(Collectors.toList());
			participantIds = new ArrayList<>();
			participantIds.add(userParticipantId);
		} catch (Exception e1) {
			log.error(e1.getMessage(),e1);
			FileError fileError = new FileError(5018, "User and bin mapping not found", "");
			validationResult.setFileErrors(new ArrayList<>());
			validationResult.getFileErrors().add(fileError);
			process = false;
		}
		if (Boolean.TRUE.equals(process)) {
			StringBuilder sB = new StringBuilder();
			String xmlAsString = null;
			try (FileReader fileReader = new FileReader(validationResult.getFilePath());
					BufferedReader bufferedReader = new BufferedReader(fileReader);) {
				printMemoryUsageStatistics("Before Reading the file");
				String textRead = bufferedReader.readLine();
				while (textRead != null) {
					sB.append(textRead);
					textRead = bufferedReader.readLine();
				}
				printMemoryUsageStatistics("After Reading the file");

				xmlAsString = sB.toString();
			} catch (FileNotFoundException fileNotFoundEx) {
				FileError fileError = new FileError(0, "FileNotFound", "");
				validationResult.setFileErrors(new ArrayList<>());
				validationResult.getFileErrors().add(fileError);
				process = false;
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				FileError fileError = new FileError(5153, "File parsing failed", "");
				validationResult.setFileErrors(new ArrayList<>());
				validationResult.getFileErrors().add(fileError);
				process = false;
			}

			npciValidator(processors, participantIds, userParticipantId, validationResult, process, sB,
					xmlAsString);
		}
		
		updateFileError(validationResult);
		
		try {
			List<FileRejectDTO> fileRejectLst = saveFileReject(validationResult);
			saveFileUpload(validationResult, fileRejectLst);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		}
		return validationResult;
		
	}

	private void npciValidator(List<String> processors, List<String> participantIds, String userParticipantId,
			ValidationResult validationResult, boolean process, StringBuilder stringBuilder, String xmlAsString) {
		if (Boolean.TRUE.equals(process)) {
			try {

				validationResult.setFileSize(StringUtils.isNotBlank(stringBuilder) ? stringBuilder.length() : 0);
				printMemoryUsageStatistics("NPCI VALIDATOR: BEFORE bufferedByteArrayOutputStream replace all");
				printMemoryUsageStatistics("NPCI VALIDATOR: After bufferedByteArrayOutputStream replace all");
				int xmlStart = xmlAsString.indexOf("<?");
				int xmlEnd = xmlAsString.indexOf("?>");
				String[] xmlArr = StringUtils.split(xmlAsString.substring(xmlStart, xmlEnd + 2), " ");
				handleXmlTagParsing(xmlArr);
				int headerStart = xmlAsString.indexOf("<Hdr>");
				int headerEnd = xmlAsString.indexOf("</Hdr>");
				printMemoryUsageStatistics("NPCI VALIDATOR: Before getting headerText ");
				String headerText = xmlAsString.substring(headerStart, headerEnd + 6);
				printMemoryUsageStatistics("NPCI VALIDATOR: After getting headerText ");
				validationResult.setXmlHeader(headerText);



				int txnStart = xmlAsString.indexOf("<Txn>");
				int txnEnd = xmlAsString.indexOf("</Txn>");

				List<String> xmlTransactions = new ArrayList<>();
				printMemoryUsageStatistics("NPCI VALIDATOR:  BEFORE while");
				while (txnStart > -1) {
					xmlTransactions.add(xmlAsString.substring(txnStart, txnEnd + 6));

					txnStart = xmlAsString.indexOf("<Txn>", txnEnd);
					txnEnd = xmlAsString.indexOf("</Txn>", txnStart);
				}
				printMemoryUsageStatistics("NPCI VALIDATOR:  After while");
				validationResult.setTotalRecordCount(xmlTransactions.size());


				int trlStart = xmlAsString.indexOf("<Trl>");

				validationResult.setXmlTrailer(xmlAsString.substring(trlStart));
				printMemoryUsageStatistics("NPCI VALIDATOR: Before toJSONObject");

				JSONObject xmlJSONObj = null;
				
				xmlJSONObj = xmlParsing(xmlAsString);

				printMemoryUsageStatistics("NPCI VALIDATOR: After toJSONObject");
				
				JSONObject headerData = xmlJSONObj.getJSONObject("File").getJSONObject("Hdr");
				printMemoryUsageStatistics("NPCI VALIDATOR: AFTER toJSONObject of Header");

				JSONObject trailerData = xmlJSONObj.getJSONObject("File").getJSONObject("Trl");
				printMemoryUsageStatistics("NPCI VALIDATOR: AFTER toJSONObject of Trl");
				
				Map<String, String> headerRecordMap = null;
				


				headerRecordMap = headerParsing(headerData);
				printMemoryUsageStatistics("NPCI VALIDATOR: AFTER getJsonAsMap of headerRecordMap");


				Map<String, String> trailerRecordMap = null;
				trailerRecordMap = trailerParse(trailerData);
				printMemoryUsageStatistics("NPCI VALIDATOR: AFTER getJsonAsMap of trailerRecordMap");

				
				printMemoryUsageStatistics("NPCI VALIDATOR: AFTER housekeeping1");

				FileDetail fileDetail = getFileDetails(validationResult.getFileName());
				String julianDate = fileDetail.getJulianDate();
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyDDD");
				LocalDate localdate = LocalDate.parse(julianDate, formatter);

				validationResult.setFileDate(localdate.toString());
				validationResult.setParticipantId(fileDetail.getParticipantId());

				HeaderRecord headerRecord = getHeader(headerRecordMap);

				printMemoryUsageStatistics("NPCI VALIDATOR: AFTER getHeader of headerRecordMap");
				

				List<Record> headerRecordsList = headerRecord.getRecords();
				
				String memInstCd = matchHeaderName(headerRecordsList);
				

				
				TrailerRecord trailerRecord = getTrailer(trailerRecordMap);

				printMemoryUsageStatistics("NPCI VALIDATOR: AFTER getTrailer(trailerRecordMap)");
				List<Record> trailerRecordsList = trailerRecord.getRecords();
				matchTrailerName(trailerRecordsList);

				

				printMemoryUsageStatistics("NPCI VALIDATOR: BEFORE HEADER");
				headerValidation(processors, participantIds, userParticipantId, validationResult, headerRecord, memInstCd);
				printMemoryUsageStatistics("NPCI VALIDATOR: AFTER HEADER");

				headerRecord.setTranCount(validationResult.getTotalRecordCount());

				trailerValidate(validationResult, headerRecord, trailerRecord);
				printMemoryUsageStatistics("NPCI VALIDATOR: AFTER TRAILER");

				printMemoryUsageStatistics("NPCI VALIDATOR: AFTER housekeeping2");
			} catch (SettleNxtException sNxte) {
				FileError fileError = new FileError(Integer.parseInt(sNxte.getErrorCode()), sNxte.getErrorMessage(), "");
				validationResult.setFileErrors(new ArrayList<>());
				validationResult.getFileErrors().add(fileError);
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				FileError fileError = new FileError(5153, "File parsing failed", "");
				validationResult.setFileErrors(new ArrayList<>());
				validationResult.getFileErrors().add(fileError);
			}
		}
	}

	private void handleXmlTagParsing(String[] xmlArr) {
		for (String string : xmlArr) {
			if ((Boolean.FALSE.equals(StringUtils.equals(string, "<?xml"))
					&& StringUtils.startsWith(string, "<?xml"))||(string.indexOf('=') != string.lastIndexOf('='))) {
				throw new SettleNxtException("5153", "Xml tag parsing failed");
			}
		}
	}

	private void matchTrailerName(List<Record> trailerRecordsList) {
		boolean nUnFlNmTrailerPresent = false;
		boolean nRecNumTrailerPresent = false;
		for (Record trailRec:trailerRecordsList) {
			Record trailerElement =  trailRec;
			String currentTrailerElementName = trailerElement.getName();
			if ("nUnFlNm".equalsIgnoreCase(currentTrailerElementName)) {
				nUnFlNmTrailerPresent = true;
			} else if ("nRecNum".equalsIgnoreCase(currentTrailerElementName)) {
				nRecNumTrailerPresent = true;
			}
		}
		printMemoryUsageStatistics(
				"NPCI VALIDATOR: AFTER for loop trailerRecordsList.size() " + trailerRecordsList.size());
		if (!nUnFlNmTrailerPresent || !nRecNumTrailerPresent) {
			throw new SettleNxtException("nUnFlNmTrailer or nRecNumTrailer not Present","");
		}
	}

	private String matchHeaderName(List<Record> headerRecordsList) {
		
		 String memInstCd = " ";
		boolean nFlCatgPresent = false;
		boolean nUnFlNmPresent = false;
		for (Record headRecord:headerRecordsList) {
			Record headerElement =  headRecord;
			String currentHeaderElementName = headerElement.getName();
			if ("nFlCatg".equalsIgnoreCase(currentHeaderElementName)) {
				nFlCatgPresent = true;
			} else if ("nUnFlNm".equalsIgnoreCase(currentHeaderElementName)) {
				nUnFlNmPresent = true;
			}  else if ("nMemInstCd".equalsIgnoreCase(currentHeaderElementName)) {
				memInstCd = headerElement.getValue();
			}
		}

		printMemoryUsageStatistics(
				"NPCI VALIDATOR: AFTER for loop headerRecordsList.size() " + headerRecordsList.size());
		if (!nFlCatgPresent || !nUnFlNmPresent) {
			throw new SettleNxtException("nFlCatg or nUnFlNm not Present","");
		}
		return memInstCd;
	}

	private void updateFileError(ValidationResult validationResult) {
		if (null != validationResult && CollectionUtils.isNotEmpty(validationResult.getFileErrors())) {
			List<FileError> updatedFileErrors = new ArrayList<>();
			List<Integer> uniqueErrorCode = new ArrayList<>();
			for (FileError currentFile : validationResult.getFileErrors()) {
				try {
					int currentFileErrorCode = currentFile.getCode();
					if (!uniqueErrorCode.contains(currentFileErrorCode)) {
						uniqueErrorCode.add(currentFileErrorCode);
						updatedFileErrors.add(currentFile);
					}
				} catch (Exception e) {
					log.error(e.getMessage(),e);
				}
			}
			validationResult.setFileErrors(updatedFileErrors);
		}
	}

	private Map<String, String> trailerParse(JSONObject trailerData) {
		Map<String, String> trailerRecordMap = null;
		ObjectMapper mapper;
		TypeReference<Map<String, String>> typeRef;
		try {
			mapper = new ObjectMapper();
			typeRef = new TypeReference<Map<String, String>>() {
			};
			trailerRecordMap = mapper.readValue(trailerData.toString(), typeRef);
		} catch (Exception e) {
			log.error("Error while parsing TRL: ", e);
			throw new SettleNxtException("5153", "Error while parsing TRL" + trailerData.toString(),e);
		}
		return trailerRecordMap;
	}

	private Map<String, String> headerParsing(JSONObject headerData) {
		Map<String, String> headerRecordMap = null;
		ObjectMapper mapper;
		TypeReference<Map<String, String>> typeRef;
		try {
			mapper = new ObjectMapper();
			typeRef = new TypeReference<Map<String, String>>() {
			};
			headerRecordMap = mapper.readValue(headerData.toString(), typeRef);
		} catch (Exception e) {
			log.error("Error while parsing HDR: ", e);
			throw new SettleNxtException("5153", "Error while parsing HDR" + headerData.toString(),e);
		}
		return headerRecordMap;
	}

	private void trailerValidate(ValidationResult validationResult, HeaderRecord headerRecord, TrailerRecord trailerRecord) {
		try {
			validateTrailer(validationResult, trailerRecord, headerRecord);
		} catch (Exception e) {
			log.error("Unable to process trailer: ", e);
			throw new SettleNxtException("5153", "Error while processing TRL",e);
		}
	}

	private void headerValidation(List<String> processors, List<String> participantIds, String userParticipantId,
			ValidationResult validationResult, HeaderRecord headerRecord, String memInstCd) {
		try {
			validateHeader(validationResult, headerRecord,  validationResult.getFileName(), memInstCd,
					processors, participantIds, userParticipantId);
		} catch (Exception e) {
			log.error("Unable to process header: ", e);
			throw new SettleNxtException("5153", "Error while processing HDR",e);
		}
	}

	private JSONObject xmlParsing(String xmlAsString) {
		XMLTokener x;
		JSONObject xmlJSONObj = new JSONObject();
		try {
			
			x = new XMLTokener(xmlAsString);
			while (x.more() && x.skipPast("<")) {
				XML.parse(x, xmlJSONObj, null);
			}
		} catch (Exception jsEx) {
			log.error("Error while parsing XML: ", jsEx);
			throw new SettleNxtException("5153", "Error while parsing XML",jsEx);
		} 
		return xmlJSONObj;
	}
	
	private List<FileRejectDTO> saveFileReject(ValidationResult validationResult) {
		int seqNo = 0;
		FileRejectDTO rejectFile;
		Set<FileRejectDTO> fileRejectLst = new HashSet<>();
		if (Boolean.FALSE.equals(validationResult.getFileErrors().isEmpty())) {
			printMemoryUsageStatistics("getFileErrors FILE_REJECT INSERTION STARTED");
			log.info("validationResult.getFileErrors().size() is  ==> {} ", validationResult.getFileErrors().size());
			for (FileError error : validationResult.getFileErrors()) {
				rejectFile = new FileRejectDTO();
				rejectFile.setFileId(validationResult.getFileId());
				rejectFile.setSeqNo(seqNo++);
				rejectFile.setTaskFormatted("NPEI01");
				int errorCode = error.getCode();
				String errorDescription = error.getDescription();

				if(errorCode<0 && errorDescription == null){
					errorCode = 5163;
				}

				rejectFile.setRecordNo(String.valueOf(-1));
				rejectFile.setFieldName(error.getFieldName());
				rejectFile.setRejectReason(errorDescription);
				rejectFile.setRejectCode(errorCode);
				rejectFile.setRejectXml(" ");
				rejectFile.setRejectDataType("Xml");
				rejectFile.setInsertDay(LocalDateTime.now());
				fileRejectService.insertFileReject(rejectFile);
				fileRejectLst.add(rejectFile);
			}
			printMemoryUsageStatistics("getFileErrors FILE_REJECT INSERTION COMPLETED");
		}
		

		int maxSize = fileRejectLst.size();
		return fileRejectLst.stream().limit(maxSize).collect(Collectors.toCollection(() -> new ArrayList<>(maxSize)));
	}

	private void saveFileUpload(ValidationResult validationResult, List<FileRejectDTO> fileRejectLst) {
		FileUploadDTO updateFile = new FileUploadDTO();
		updateFile.setDocumentId(validationResult.getFileId());
		updateFile.setDocumentName(validationResult.getFileName());
		updateFile.setDocumentPath(validationResult.getFilePath());
		updateFile.setFileRejects(fileRejectLst);
		updateFile.setFullFileRejected(Boolean.FALSE.equals(validationResult.getFileErrors().isEmpty()) ? "Y": "N");
		updateFile.setLastUpdatedOn(new Date());
		updateFile.setVersion(validationResult.getVersion());
	
		updateFile.setFileGenDateTime(validationResult.getFileGenDateTime());
		updateFile.setTotalAmount(validationResult.getTotalAmount() == null? BigDecimal.ZERO: validationResult.getTotalAmount());
		if (StringUtils.equals(updateFile.getFullFileRejected(), "Y")) {
			updateFile.setStatus("F");
			updateFile.setRejectedNoTrans(validationResult.getTotalRecordCount());

		} else {
			updateFile.setStatus(validationResult.getFileStatus());
			updateFile.setRejectedNoTrans(0);
		}
		
		
		updateFile.setValidNoTrans(0);
		updateFile.setTotalNoTrans(validationResult.getTotalRecordCount());
		fileUploadRepository.updateFileUploadInfo(updateFile);
		updateFile.setInsertDayStr(validationResult.getInsertDayStr());

		List<FileUploadDTO> fileUploadList = new ArrayList<>();
		fileUploadList.add(updateFile);
		if (!StringUtils.equals(updateFile.getStatus(), "F") && !StringUtils.endsWith(updateFile.getDocumentName(), CommonConstants.FILE_EXTENSION_CSV)) {
			log.info("Kafka Msg {}",fileUploadList);
			if(!fileUploadList.isEmpty()) {
				prepareAndSendMessageToKafka(fileUploadList);
			}
			
		}
	}

	private void prepareAndSendMessageToKafka(List<FileUploadDTO> fileUploadDTOList) {
		try{
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("files",  new JSONArray(fileUploadDTOList));
		String topic = environment.getProperty("kafka.topic.fileValidate");
		kafkaUtils.sendKafkaMessageAsync(topic , jsonObject.toString());
		log.debug("Published to Kafka");
		}
		catch(JSONException ex) {
			log.error("Error while puiblishing message to kafka");		
	}
	}
	
	

	public BigDecimal getTranTotalFromBody(Map<String, List<Map<String, Object>>> txnRecordsMap) {
		Map<String, Object> tempMap = null;
		List<Map<String, Object>> recordList = txnRecordsMap.get("Txn");
		BigDecimal tranTotal = BigDecimal.ZERO;
		for (Map<String,Object> rec:recordList) {
			long amtTxn = 0;
			tempMap = (HashMap<String, Object>) rec;
			String mti = tempMap.get("nMTI") instanceof String s ? s : null;
			mti = (mti == null && tempMap.get("nMTI") instanceof Integer) ? String.valueOf(tempMap.get("nMTI")) : null;
			String funcCd = tempMap.get(NFUNCD) instanceof String  s ? (String) s : null;
			funcCd = (funcCd == null && tempMap.get(NFUNCD) instanceof Integer)
					? String.valueOf(tempMap.get(NFUNCD))
					: null;

			amtTxn = getTxnAmount(tempMap, amtTxn, mti, funcCd);
			tranTotal = BigDecimal.valueOf(amtTxn).add(tranTotal);
		}

		return tranTotal;
	}

	private long getTxnAmount(Map<String, Object> tempMap, long amtTxn, String mti, String funcCd) {
		if (tempMap.get(NATMTXN) != null
				&& (tempMap.get(NATMTXN) instanceof String || tempMap.get(NATMTXN) instanceof Integer)) {
			if (tempMap.get(NATMTXN) instanceof Integer i) {
				amtTxn = i;
			} else if (tempMap.get(NATMTXN) instanceof String s) {
				amtTxn = Long.valueOf(s);
			}
		}
		if (mti != null && funcCd != null && StringUtils.equals(mti, "1740")
				&& (StringUtils.equals(funcCd, "760") || StringUtils.equals(funcCd, "761"))
				&& tempMap.get(NATMTXN) == null
				&& (tempMap.get(NFEEAMT) instanceof String || tempMap.get(NFEEAMT) instanceof Integer)) {
			if (tempMap.get(NFEEAMT) instanceof Integer i) {
				amtTxn = i;
			} else if (tempMap.get(NFEEAMT) instanceof String s) {
				amtTxn = Long.valueOf(s);
			}
		}
		return amtTxn;
	}

	protected void validateTrailer(ValidationResult validationResult, TrailerRecord trailer,
			HeaderRecord headerRecord) {
		new TrailerValidationCheck(trailer, headerRecord).validate(validationResult, rejReasonCode);
	}

	protected void validateBody(ValidationResult validationResult, List<TransactionRecord> txnRecords,
			int taskIdsSizeForSplit, String userParticipantId) {
		new BodyValidationCheck(userParticipantId).validate(validationResult, txnRecords, taskIdsSizeForSplit,
				rejReasonCode);
	}
	protected void validateHeader(ValidationResult validationResult, HeaderRecord header, String fileNameForXml, String memInstCd, List<String> processors, List<String> participantIds, String userParticipantId)
			 {
		new HeaderValidationCheck(header, fileNameForXml, participantIds, processors, userParticipantId,environment, memInstCd).validate(validationResult, rejReasonCode);
	}

	
	
	public HeaderRecord getHeader(Map<String, String> headerRecordMap) {
		HeaderRecord headerRecord = new HeaderRecord();
		List<Record> records = new ArrayList<>();
		Record recod = null;
		for (Map.Entry<String, String> entry : headerRecordMap.entrySet()) {
			recod = new Record();
			recod.setName(entry.getKey());
			recod.setValue(entry.getValue());
		    records.add(recod);
		}
		headerRecord.setRecords(records);
		return headerRecord;
	}

	public TrailerRecord getTrailer(Map<String, String> headerRecordMap) {
		TrailerRecord trailerRecord = new TrailerRecord();
		List<Record> records = new ArrayList<>();
		Record recod = null;
		for (Map.Entry<String, String> entry : headerRecordMap.entrySet()) {
			recod = new Record();
			recod.setName(entry.getKey());
			recod.setValue(entry.getValue());
		    records.add(recod);
		}
		trailerRecord.setRecords(records); 
		return trailerRecord;
	}
	
	public FileDetail getFileDetails(String fileName) {
		FileDetail fileDetailDTO = new FileDetail();
		String flName = "";
		String flExtn = "";
		
		if(StringUtils.isNotBlank(fileName)){
			int index = fileName.lastIndexOf('.');
			flExtn = fileName.substring(index+1);
			flName = fileName.substring(0,index);
			fileDetailDTO.setFileName(fileName);
			fileDetailDTO.setFileExtension(flExtn);
			fileDetailDTO.setId(StringUtils.substring(flName, 0, 2));
			fileDetailDTO.setClearingCycleInd(StringUtils.substring(flName, 2, 3));
			fileDetailDTO.setParticipantId(StringUtils.substring(flName, 3, 14));
			fileDetailDTO.setJulianDate(StringUtils.substring(flName, 14, 19));
			fileDetailDTO.setFileSequence(StringUtils.substring(flName, 19, 21));
		}
		
		return fileDetailDTO;
	}
	
	@SuppressWarnings("unchecked")
	public List<TransactionRecord> getBody(ValidationResult validationResult, Map<String,List<Map<String, Object>>> txnRecordsMap) {
		Map<String, String> recordsMap;
		List<TransactionRecord> txnRecords = new ArrayList<>();
		Map<String, Object> tempMap = null;
		List<Map<String, Object>> recordList = txnRecordsMap.get("Txn");
		Record recod = null;
		for(int i=0; i < recordList.size(); i++){
			TransactionRecord txnRecord = new TransactionRecord();
			tempMap = recordList.get(i);
			List<Record> records = new ArrayList<>();
			
			for (Entry<String, Object> fieldval : tempMap.entrySet()) {
				recod = new Record();
			    String fieldName = fieldval.getKey();
			    recod.setName(fieldName);
			    
			    if (fieldval.getValue() instanceof String) {
			    	recod.setValue((String) tempMap.get(fieldName));
			    } else if (tempMap.get(fieldName) instanceof List) {
			    	validateResult(validationResult, tempMap, i, fieldName);
			    	
		    		recod.setValue(String.valueOf(((List<Object>) tempMap.get(fieldName)).get(0)));
			    } else if (tempMap.get(fieldName) instanceof Number) {
			    	recod.setValue(String.valueOf(tempMap.get(fieldName)));
			    }  else if (tempMap.get(fieldName) instanceof LinkedHashMap) {
			    	recod.setValue(String.valueOf(tempMap.get(fieldName)));
			    }
			    else {
			    	log.info("Record Set Null Value {}", fieldName);
			    	log.info("Record Number {}", recod);
			    	recod.setValue(" ");
			    } 
			    
			    records.add(recod);
			}

			txnRecord.setRecordNumber(i);
			txnRecord.setRecords(records);
			
			recordsMap = ValidationUtils.listAsMap(records);
			
			String mti = recordsMap.get("nMTI");
			String funcCode = recordsMap.get(NFUNCD);
			
			String name = mti + funcCode;
			
			MessageFormatNameMapping mapping = null;
			
			mapping = CustomMappingUtils.getMessageFormatNameMapping(NameMappingContext.MESSAGEFORMAT, name);
			
			if (mapping != null) {
				try {
					txnRecord.setRequiredFields(mapping.getFields().getRequiredFields());
					txnRecord.setOptionalFields(mapping.getFields().getOptionalFields());
					txnRecord.setConditionalFields(mapping.getFields().getConditionalFields());
				} catch (Exception ex) {
					log.error(ex.getCause(),ex);
				}
			}
			
			txnRecords.add(txnRecord);
		}
		return txnRecords;
	}

	private void validateResult(ValidationResult validationResult, Map<String, Object> tempMap, int i, String fieldName) {
		if (((List<?>) tempMap.get(fieldName)).size() > 1) {
			String values = "";
			for (Object s : (List<String>) tempMap.get(fieldName)) {
				String listFieldDuplicate=  String.valueOf(s);
				values = listFieldDuplicate + ",";
			}
			values = values.substring(0, values.length() - 1);
			
			validationResult.addRecordError(Integer.valueOf((String) tempMap.get("nRecNum")), new RecordError(6024, "Duplicate tag found", fieldName, values, validationResult.getXmlTransactions().get(i)));
		}
	}
	
	public static void printMemoryUsageStatistics(String whatLocation) {
		int mb = 1024*1024;
		//Getting the runtime reference from system
		Runtime runtime = Runtime.getRuntime();
		log.debug("whatLocation is : {}", whatLocation);
		//Print used memory
		log.debug("Used Memory: {}", (runtime.totalMemory() - runtime.freeMemory()) / mb);
		//Print free memory
		String freeMemory = runtime.freeMemory() / mb + " out of "+runtime.maxMemory() / mb;
		log.debug("Free Memory: {}", freeMemory);
		//Print total available memory
		log.debug("Total Memory: {}", runtime.totalMemory() / mb);
		//Print Maximum available memory
		log.debug("Max Memory: {}" , runtime.maxMemory() / mb);
	}

	@Override
	public String parseAndSplitFile(CashBackFileUploadDTO cashbackFileUploadDTO) {
		List<CashBackFileUploadDTO> cashbackList = new ArrayList<>();
		if (StringUtils.isNotBlank(cashbackFileUploadDTO.getFileLocation())) {
			log.info("Parsing and splitting file : {} at : {}", cashbackFileUploadDTO.getFileName(), LocalDateTime.now());
			cashbackFileRepository.updateFile(cashbackFileUploadDTO.getFileId(), "S");
			String uploadFilePath = "";
			try {
				
				String file = cashbackFileUploadDTO.getFileLocation();
				Path p = Paths.get(file).normalize();
				uploadFilePath = new File(p.toString()).getCanonicalPath();
			} catch (Exception e1) {
				log.error("Error in getting path of file before reading: ", e1);
				cashbackFileRepository.updateFile(cashbackFileUploadDTO.getFileId(), "F");
				return ERROR;
			}
			
			printMemoryUsageStatistics("Before reading the file");
			log.debug(uploadFilePath);
			if(Boolean.FALSE.equals(handleFileUpdate(cashbackFileUploadDTO, cashbackList, uploadFilePath))) {
				return ERROR;
			}
			printMemoryUsageStatistics("After reading the file");
			
			try {
				List<CashBackFileUploadDTO> cashbackSortList = cashbackList.stream()
						.sorted((d1, d2) -> d2.getPID().compareTo(d1.getPID())).collect(Collectors.toList());
				
				Map<String, List<CashBackFileUploadDTO>> cashbackPIDMap = cashbackSortList.stream()
						.collect(Collectors.groupingBy(CashBackFileUploadDTO::getPID, Collectors.toList()));
				
				LocalDate currentDate = LocalDate.now();
				Map<String, CycleDetailsModel> map = settlementCycleCache.getProductWiseCycleObject(LocalDateTime.now());
				String productId = environment.getProperty("PRODUCT");
				CycleDetailsModel settlementCycleConfigDTO = Boolean.FALSE.equals(map.isEmpty()) ? map.get(productId)
						: null;
				String cycleNumber = settlementCycleConfigDTO != null ? settlementCycleConfigDTO.getCycleName() : null;
				String fileNamePrefix = String.valueOf(currentDate.getYear()) + "_"
						+ StringUtils.leftPad(String.valueOf(currentDate.getMonthValue()), 2, "0") + "_"
						+ StringUtils.leftPad(String.valueOf(currentDate.getDayOfMonth()), 2, "0");
				String fileNameSuffix = "CashbackReport_" + cycleNumber + ".csv";

				Set<String> participantId = new HashSet<>();

				printMemoryUsageStatistics("Before writing in the file");
				
				for (Map.Entry<String, List<CashBackFileUploadDTO>> entry : cashbackPIDMap.entrySet()) {
					participantId.add(entry.getKey());
					generatePIDFile(currentDate.toString(), cycleNumber, entry.getKey(), fileNamePrefix,
							fileNameSuffix, entry.getValue());
				}
				printMemoryUsageStatistics("After writing in the file");
				
				log.info("End Of Parsing and splitting file : {} at : {}", cashbackFileUploadDTO.getFileName(), LocalDateTime.now());
				cashbackFileRepository.updateFile(cashbackFileUploadDTO.getFileId(), "C");
				cashbackFileRepository.saveCashBackFile(cashbackFileUploadDTO);
				
				
			} catch (SettleNxtApplicationException e1) {
				cashbackFileRepository.updateFile(cashbackFileUploadDTO.getFileId(), "F");
				return ERROR;
			} catch (Exception e) {
				log.error("Error while writing/creating/parsing file: ", e);
				cashbackFileRepository.updateFile(cashbackFileUploadDTO.getFileId(), "F");
				return ERROR;
			}
		}
		return "SUCCESS";
	}

	private Boolean handleFileUpdate(CashBackFileUploadDTO cashbackFileUploadDTO, List<CashBackFileUploadDTO> cashbackList,
			String uploadFilePath) {
		boolean flag = true;
		try (ICsvBeanReader beanReader = new CsvBeanReader(new FileReader(uploadFilePath),
				CsvPreference.EXCEL_PREFERENCE)) {
			String[] fileHeaders = beanReader.getHeader(true);
			final String[] headers = getHeaders();
			List<String> headerList = Arrays.asList(headers);
			fileHeaders = validateFileHeaders(fileHeaders, headerList);
			final CellProcessor[] processor = getProcessors();
			CashBackFileUploadDTO cashbackDTO = null;
			while ((cashbackDTO = beanReader.read(CashBackFileUploadDTO.class, fileHeaders, processor)) != null) {
				cashbackList.add(cashbackDTO);
			}
			log.debug("Read {} lines from file", cashbackList.size());
		} catch (SettleNxtApplicationException e1) {
			log.error("Error in validating header: {}", e1.getErrorCode());
			cashbackFileRepository.updateFile(cashbackFileUploadDTO.getFileId(), "F");
			flag = false;
		} catch (Exception e) {
			log.error("Error in parse and split file: ", e);
			cashbackFileRepository.updateFile(cashbackFileUploadDTO.getFileId(), "F");
			flag = false;
		}
		return flag;
	}
	
	

	

	private String[] validateFileHeaders(String[] fileHeadrs, List<String> headersList) {
		for (int i = 0; i < fileHeadrs.length; i++) {
			if (!headersList.contains(fileHeadrs[i])) {
				throw new SettleNxtApplicationException("Invalid headers", "");
			}
			fileHeadrs[i] = StringUtils.remove(fileHeadrs[i], " ");
			fileHeadrs[i] = StringUtils.remove(fileHeadrs[i], ".");
		}
		return fileHeadrs;
	}
	
	
	
	
	private String createCashBackDirectory(String currentDate, String cycleNumber, String participantId) throws SettleNxtApplicationException {
		try {
			String basePath = environment.getProperty("CASHBACK_FILE_UPLOAD_PATH");
			FileNameUtil.createDirectory(basePath);
			FileNameUtil.createDirectory(basePath + currentDate);
			FileNameUtil.createDirectory(basePath + currentDate + File.separator + participantId);
			FileNameUtil.createDirectory(basePath + currentDate + File.separator + participantId + File.separator + cycleNumber);
			return basePath + currentDate + File.separator + participantId + File.separator + cycleNumber;
		} catch (Exception e) {
			log.error("Error while creating cashback directory: ", e);
			throw new SettleNxtApplicationException("ERR_CREATE_DIRECTORY", "Error while creating directory",e);
		}
	}
	
	private CellProcessor[] getProcessors() {
		return new CellProcessor[] { new Optional(), new Optional(), new Optional(),
				new Optional(), new Optional(), new Optional(), new Optional(), new Optional(), new Optional(),
				new Optional(), new Optional(), new Optional(), new Optional(), new Optional(), new Optional(),
				new Optional() };
	}
	
	private String[] getHeaders() {
		return new String[] { CommonConstants.SETTLEMENT_DATE, CommonConstants.LOCAL_DATE_TIME,
				CommonConstants.ISSUER_IIN, CommonConstants.PID, CommonConstants.PAN,
				CommonConstants.RRN, CommonConstants.CARD_ACC_TERM_ID, CommonConstants.MCC,
				CommonConstants.ISSUER_BANK, CommonConstants.STATUS, CommonConstants.APPROVAL_CODE,
				CommonConstants.TXN_AMOUNT, CommonConstants.AMOUNT_IN_RUPEES, CommonConstants.EA,
				CommonConstants.FINAL_AMOUNT, CommonConstants.REMARKS };
	}
	
	private void writeToCsv(String uploadFilePath, List<CashBackFileUploadDTO> cashbackList, String[] headers,
			CellProcessor[] processor, String[] beanHeader, boolean fileNotPresent)
			throws SettleNxtApplicationException {
		try (ICsvBeanWriter beanWriter = new CsvBeanWriter(new FileWriter(uploadFilePath, true),
				CsvPreference.EXCEL_PREFERENCE)) {
			if (fileNotPresent) {
				beanWriter.writeHeader(headers);
			}
			for (CashBackFileUploadDTO obj : cashbackList) {
				beanWriter.write(obj, beanHeader, processor);
			}
		} catch (Exception e) {
			log.error("Error in writing csv: ", e);
			throw new SettleNxtApplicationException("ERR_WRITE_TO_CSV", "Error while writing in csv",e);
		}
	}
	
	private boolean createFileIfNotExists(String docpath) throws IOException {
		Path p = Paths.get(docpath).normalize();
		String uploadFilePath = new File(p.toString()).getCanonicalPath();
		File uploadFile = new File(uploadFilePath);
		boolean newFile = false;
		if (!uploadFile.exists()) {
			newFile = uploadFile.createNewFile();
		}
		
		return newFile;
	}
	
	private void generatePIDFile(String currentDate, String cycleNumber, String pid, String fileNamePrefix,
			String fileNameSuffix, List<CashBackFileUploadDTO> pidList) {
		try {
			log.info("Processing PID {} of size: {}", pid, pidList.size());
			String[] headers = getHeaders();
			CellProcessor[] processor = getProcessors();
			String[] beanHeader = validateFileHeaders(headers, Arrays.asList(headers));
			String directory = createCashBackDirectory(currentDate, cycleNumber, pid);
			String fileName = directory + File.separator + fileNamePrefix + "_" + pid + "_" + fileNameSuffix;
			boolean fileNotPresent = createFileIfNotExists(fileName);
			Path p = Paths.get(fileName).normalize();
			String uploadFilePath = new File(p.toString()).getCanonicalPath();
			writeToCsv(uploadFilePath, pidList, headers, processor, beanHeader, fileNotPresent);
			log.info("Processing End PID {}", pid);
		} catch (Exception e) {
			log.error("ERRRORRRRRR: ", e);
			throw new SettleNxtApplicationException("ERR_WRITE_TO_CSV", "Error while writing in csv",e);
		}
	}
	
	
}
