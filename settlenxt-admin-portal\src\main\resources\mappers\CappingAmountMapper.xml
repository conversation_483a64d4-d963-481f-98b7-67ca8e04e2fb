<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.npci.settlenxt.adminportal.repository.CappingAmountRepository">

	<select id="getCappingAmountFromMainList" resultType="CappingAmountDTO">	
	SELECT ca.action_code as actionCode, ca.mcc_group as mccGroup, ca.bin_card_brand_id as binCardBrandId, 
	ca.bin_card_type_id as binCardTypeId, ca.cap_name as capName,
	ca.cap_type as capType, ca.field_name as fieldName, 
	ca.rel_operator as relOperator, ca.field_value as fieldValue, ca.amount_flag as amountFlag, 
	ca.flat as flat, ca.percentage as percentage, ca.min_amount as amountCapMin,
	ca.max_amount as amountCapMax, ca.status as status, 
	ca.created_by as createdBy, ca.created_on as createdOn, ca.last_updated_by as lastUpdatedBy, 
	ca.last_updated_on as lastUpdatedOn
    FROM cap_amount ca , cap_amount_stg cas 
    where 
    ca.action_code = cas.action_code 
    and ca.mcc_group = cas.mcc_group 
    and ca.bin_card_brand_id = cas.bin_card_brand_id 
    and ca.bin_card_type_id = cas.bin_card_type_id 
    and ca.field_name = cas.field_name 
    and ca.rel_operator = cas.rel_operator
    and ca.field_value = cas.field_value
    and cas.request_state = 'A';
    </select>


	<select id="getPendingCappingAmountFromStg" resultType="CappingAmountDTO">
	SELECT cas.action_code as actionCode, cas.mcc_group as mccGroup, cas.bin_card_brand_id as bincardBrandId, 
	cas.bin_card_type_id as bincardTypeId, cas.cap_name as capName,
	cas.cap_type as capType, cas.field_name as fieldName, 
	cas.rel_operator as relOperator, cas.field_value as fieldValue, cas.amount_flag as amountFlag, 
	cas.flat as flat, cas.percentage as percentage, cas.min_amount as amountcapMin,
	cas.max_amount as amountcapMax, cas.request_state as requestState, 
	cas.created_by as createdBy, cas.created_on as createdOn, cas.last_updated_by as lastUpdatedBy, 
	cas.last_updated_on as lastUpdatedOn, cas.checker_comments as checkerComments
	from cap_amount_stg cas 
	where request_state in <foreach item='item' index='index' collection='requestStateList' open='(' separator=',' close=')'>#{item}</foreach>;
	</select>	

	<select id="getFunctionCodeList" resultType="CappingAmountDTO">
	select CONCAT(fc.func_code, '-',fc.func_code_desc) as funcCodeDesc,fc.func_code as funcCode from  func_code fc
	</select>

<insert id="insertCappingAmountintoStg">
insert into cap_amount_stg(action_code,mcc_group, bin_card_brand_id, bin_card_type_id, cap_name, cap_type, field_name,rel_operator, field_value, amount_flag, flat, percentage, min_amount, max_amount, status,created_by, created_on, last_updated_by, last_updated_on,request_state, last_operation)
values(#{actionCode},#{mccGroup},#{binCardBrandId},#{binCardTypeId},#{capName},#{capType},#{fieldName},#{relOperator},#{fieldValue},#{amountFlag},#{flat},#{amountCapPercent},#{amountCapMin},#{amountCapMax},#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{requestState},#{lastOperation})
</insert>
<select id="getCappingAmountfromStg" resultType="CappingAmountDTO">
SELECT action_code as actionCode,mcc_group as mccGroup,bin_card_brand_id as binCardBrandId,bin_card_type_id as binCardTypeId,cap_name as capName,cap_type as capType,field_name as fieldName,rel_operator as relOperator,field_value as fieldValue,amount_flag as amountFlag,flat as flat,percentage as percentage,percentage as amountCapPercent ,min_amount as amountCapMin,max_amount as amountCapMax,status as status,created_by as createdBy,created_on as createdOn,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,request_state as requestState FROM cap_amount_stg where 
action_code =#{actionCode}and mcc_group = #{mccGroup}and bin_card_brand_id = #{binCardBrandId}and bin_card_type_id = #{binCardTypeId}and field_name = #{fieldName}and rel_operator = #{relOperator}and field_value = #{fieldValue}
</select>	

<select id="getCappingAmountMain" resultType="CappingAmountDTO">
SELECT action_code as actionCode,mcc_group as mccGroup,bin_card_brand_id as binCardBrandId,bin_card_type_id as binCardTypeId,cap_name as capName,cap_type as capType,field_name as fieldName,rel_operator as relOperator,field_value as fieldValue,amount_flag as amountFlag,flat as flat,percentage as amountCapPercent ,percentage as percentage ,min_amount as amountCapMin,max_amount as amountCapMax,status as status from cap_amount where 
action_code =#{actionCode}and mcc_group = #{mccGroup}and bin_card_brand_id = #{binCardBrandId}and bin_card_type_id = #{binCardTypeId}and field_name = #{fieldName}and rel_operator = #{relOperator}and field_value = #{fieldValue}
</select>		

<update id = "updateCappingAmount">
update cap_amount set action_code=#{actionCode}, mcc_group=#{mccGroup}, bin_card_brand_id=#{binCardBrandId}, bin_card_type_id= #{binCardTypeId},cap_name=#{capName},cap_type=#{capType},field_name=#{fieldName},rel_operator=#{relOperator}, field_value=#{fieldValue}, amount_flag=#{amountFlag}, flat=#{flatValue}, percentage=#{amountCapPercent}, min_amount=#{amountCapMin}, max_amount=#{amountCapMax}, status=#{status},created_by=#{createdBy}, created_on=#{createdOn}, last_updated_by=#{lastUpdatedBy}, last_updated_on=#{lastUpdatedOn}  where action_code = #{actionCode} and mcc_group = #{mccGroup} and bin_card_brand_id = #{binCardBrandId} and bin_card_type_id = #{binCardTypeId} and field_name = #{fieldName} and rel_operator = #{relOperator} and field_value = #{fieldValue}
</update>
	

<insert id="saveCappingAmountMain">
insert into cap_amount(
action_code,mcc_group,bin_card_brand_id,bin_card_type_id,cap_name,cap_type,field_name,rel_operator,field_value,amount_flag,flat,percentage,min_amount,max_amount,status,created_by,created_on,last_updated_by,last_updated_on
)
values(
#{actionCode},#{mccGroup},#{binCardBrandId},#{binCardTypeId},#{capName},#{capType},#{fieldName},#{relOperator},#{fieldValue},#{amountFlag},#{flatValue},#{percentageValue},#{amountCapMinValue},#{amountCapMaxValue},#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}
)
</insert>
	
<update id="updateCappingAmountRequestState">
update cap_amount_stg set request_state=#{requestState},last_operation=#{lastOperation},checker_comments=#{checkerComments},last_updated_by=#{lastUpdatedBy}, last_updated_on=#{lastUpdatedOn} 
where action_code = #{actionCode}and mcc_group = #{mccGroup}and bin_card_brand_id = #{binCardBrandId}and bin_card_type_id = #{binCardTypeId}and field_name = #{fieldName}and rel_operator = #{relOperator}and field_value = #{fieldValue}
</update>


<update id="updateCappingAmountStg">
update cap_amount_stg set action_code=#{actionCode}, mcc_group=#{mccGroup}, last_operation =#{lastOperation}, bin_card_brand_id=#{binCardBrandId}, bin_card_type_id= #{binCardTypeId},cap_name=#{capName},cap_type=#{capType},field_name=#{fieldName},rel_operator=#{relOperator}, field_value=#{fieldValue}, amount_flag=#{amountFlag}, flat=#{flat}, percentage=#{amountCapPercent}, min_amount=#{amountCapMin}, max_amount=#{amountCapMax}, status=#{status},last_updated_by=#{lastUpdatedBy}, last_updated_on=#{lastUpdatedOn},request_state=#{requestState}  where action_code = #{actionCode} and mcc_group = #{mccGroup} and bin_card_brand_id = #{binCardBrandId} and bin_card_type_id = #{binCardTypeId} and field_name = #{fieldName} and rel_operator = #{relOperator} and field_value = #{fieldValue}
</update>
	
<select id="getCappingAmountMainEdit" resultType="CappingAmountDTO">
	select capping_id as cappingId, func_code as funcCode,status as status,amt_cap_flat as amountCapFlat,amt_cap_percent as amountCapPercent,
	amt_max_cap as amountCapMax,created_by as createdBy,cap_amount_flag as capAmountFlag,created_on as createdOn,last_updated_by as lastUpdatedBy,
	last_updated_on as lastUpdatedOn,amt_cap_min as amountCapMin,priority as priority,mcc_id as mccId,request_state as requestState 
	from amount_capping_stg 
	where capping_id = #{cappingId}
</select>


<update id="updateStgCappingAmount">
update cap_amount_stg set action_code=#{actionCode}, mcc_group=#{mccGroup},
bin_card_brand_id=#{binCardBrandId}, bin_card_type_id= #{binCardTypeId},
cap_name=#{capName},cap_type=#{capType},field_name=#{fieldName},rel_operator=#{relOperator},
field_value=#{fieldValue}, amount_flag=#{amountFlag}, flat=#{flat}, percentage=#{amountCapPercent},
min_amount=#{amountCapMin}, max_amount=#{amountCapMax}, status=#{status},created_by=#{createdBy},
created_on=#{createdOn}, last_updated_by=#{lastUpdatedBy}, last_updated_on=#{lastUpdatedOn},request_state=#{requestState} 
where action_code = #{actionCode} and mcc_group = #{mccGroup} and bin_card_brand_id = #{binCardBrandId} and bin_card_type_id = #{binCardTypeId} 
and field_name = #{fieldName} and rel_operator = #{relOperator} and field_value = #{fieldValue}
</update>

<delete id="deleteDiscardedEntry">
DELETE FROM cap_amount_stg where action_code = #{actionCode} and mcc_group = #{mccGroup} and bin_card_brand_id = #{binCardBrandId} and bin_card_type_id = #{binCardTypeId} 
and field_name = #{fieldName} and rel_operator = #{relOperator} and field_value = #{fieldValue}
</delete>

<select id="getCappingId" resultType="Integer">
SELECT capping_id  as cappingId FROM amount_capping_stg WHERE priority = #{priority}  and request_state  in ('P','A')
</select>
	
<select id="fetchCappingAmountStgAppList" resultType="CappingAmountDTO">
select action_code as actionCode,mcc_group as mccGroup ,bin_card_brand_id as binCardBrandId, bin_card_type_id as
binCardTypeId, cap_name as capName , cap_type as capType ,field_name as fieldName, rel_operator as relOperator, field_value as fieldValue , amount_flag as amountFlag, flat as flat, flat as flatValue,percentage as percentage,min_amount as amountCapMin, max_amount as amountCapMax,status as status,created_by as createdBy,created_on as createdOn, last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn, request_state as requestState, last_operation as lastOperation, percentage as percentageValue,min_amount as amountCapMinValue,max_amount as amountCapMaxValue,checker_comments as checkerComments from cap_amount_stg
where action_code||mcc_group||bin_card_brand_id ||bin_card_type_id ||field_name|| rel_operator||field_value in<foreach item='item' index='index' collection='cappingIdList' open='(' separator=',' close=')'>#{item}</foreach>
</select>
	
	<select id="getActionCodeList" resultType="CodeValueDTO">
	select DISTINCT CONCAT(fc.action_code, '-',fc.action_code_description) as description,fc.action_code as code from  action_code fc
	</select>
	
	<select id="getMccGroupList" resultType="CodeValueDTO">
	select distinct mcc_group as code , mcc_group as description from mcc_config where mcc_group is not null order by mcc_group
	</select>



<select id="getCappingAmountFromStgEdit" resultType="CappingAmountDTO">
SELECT action_code as actionCode, mcc_group as mccGroup, bin_card_brand_id as binCardBrandId, bin_card_type_id as binCardTypeId,cap_name as capName,cap_type as capType,field_name as fieldName,rel_operator as relOperator, field_value as fieldValue, amount_flag as amountFlag, flat as flat, percentage as amountCapPercent, min_amount as amountCapMin, max_amount as amountCapMax, status as status,created_by as createdBy, created_on as createdOn, last_updated_by as lastUpdatedBy, last_updated_on as lastUpdatedOn FROM cap_amount_stg where action_code = #{actionCode} and mcc_group = #{mccGroup} and bin_card_brand_id = #{binCardBrandId} and bin_card_type_id = #{binCardTypeId} and field_name = #{fieldName} and rel_operator = #{relOperator} and field_value = #{fieldValue}
</select>

<select id="getCappingAmountfromStgCombined" resultType="CappingAmountDTO">
select action_code as actionCode,mcc_group as mccGroup ,bin_card_brand_id as binCardBrandId,
bin_card_type_id as binCardTypeId, cap_name as capName ,cap_type as capType ,field_name as fieldName,
rel_operator as relOperator, field_value as fieldValue ,amount_flag as amountFlag, flat as flat,flat as flatValue,
percentage as percentage,min_amount as amountCapMin, max_amount as amountCapMax,status as status,created_by as createdBy,
created_on as createdOn, last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn, request_state as requestState,
last_operation as lastOperation, percentage as percentageValue,percentage as amountCapPercent,min_amount as amountCapMinValue,max_amount as amountCapMaxValue,
checker_comments as checkerComments from cap_amount_stg where action_code||mcc_group||bin_card_brand_id ||bin_card_type_id ||field_name|| rel_operator||field_value=#{capId}
</select>

<select id="getCappingAmountfromMainCombined" resultType="CappingAmountDTO">
select action_code as actionCode,mcc_group as mccGroup ,bin_card_brand_id as binCardBrandId,
bin_card_type_id as binCardTypeId, cap_name as capName , cap_type as capType ,field_name as fieldName,
rel_operator as relOperator, field_value as fieldValue , amount_flag as amountFlag, flat as flat,
flat as flatValue,percentage as percentage,percentage as amountCapPercent, min_amount as amountCapMin, max_amount as amountCapMax,status as status,
created_by as createdBy,created_on as createdOn, last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn,
percentage as percentageValue,min_amount as amountCapMinValue,max_amount as amountCapMaxValue from cap_amount 
where action_code||mcc_group||bin_card_brand_id ||bin_card_type_id ||field_name|| rel_operator||field_value=#{capId}
</select>

<select id="checkDuplicateRecords" resultType="int">
select count(action_code) from cap_amount_stg where action_code = #{actionCode} and mcc_group = #{mccGroup} and bin_card_brand_id = #{binCardBrandId} and bin_card_type_id = #{binCardTypeId} 
and field_name = #{fieldName} and rel_operator = #{relOperator} and field_value = #{fieldValue}
</select>
	
</mapper>