$(document).ready(function () {
	userIds=[];
    
     $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                	//If first column to be skipped to include the filter for the reasons line check box 
                    if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    handleInput(colIdx, cell, title, api);
                    }
                });
            $('#tabnew_filter').hide();
            
        },
     // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
        order: [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Insurance Primium Configuration',
                header: 'false',
                title: null,
                sheetName: 'Insurance Primium Configuration',
                className: 'defaultexport'
                /*,
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }*/
            }
 ,
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Insurance Primium Configuration' ,
						header:'false', 
						title: null,
						sheetName:'Insurance Primium Configuration',
						className:'defaultexport'
						/*,
						exportOptions: {
				            columns: 'th:not(:last-child)'
				         }*/
                    }
        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true
    });

    $("#excelExport").on("click", function () {
        $(".buttons-excel").trigger("click");
    });
 
 	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });
     
  
     
     $("#selectAll").click(function(){
	 		
		 $('#jqueryError4').hide();
		 $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
		 
		 var referenceNoList = document.getElementById("newsIds");
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
        
		 if(userIds.length>0){
	referenceNoList.innerHTML = userIds.length+"     "+"records are selected";
	
     		if( $('#selectAll').is(':checked') )
		   {
			  
			  $("#toggleModalNews").modal('show');	
		  }
		 else
	     {
			 $("#toggleModalNews").modal('hide');
			 
			 }}else{
                handleModalShow(referenceNoList);}
            });
     
  // Disabling SelectAll option diabling
   	if(referenceNoListPendings.length==0)
   	{
  		if (typeof selectAll != "undefined") {
  			document.getElementById("selectAll").disabled = true;
  			document.getElementById("submitButtonA").disabled = true;
 			document.getElementById("submitButtonR").disabled = true;
  		}
   	}
  		    
		    
});


function handleModalShow(referenceNoList) {
	var i = 0;
	var userId2 = [];
	$("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
		userId2.push(this.value);
		i++;
	});
	if (userId2.length > 0) {


		if (referenceNoListPendings.length > 0) {
			referenceNoList.innerHTML = referenceNoListPendings.length + "     " + "records are selected";

			if ($('#selectAll').is(':checked')) {
				$("#toggleModalNews").modal('show');

			}
			else {
				$("#toggleModalNews").modal('hide');

			}
		}
	}
}

function handleInput(colIdx, cell, title, api) {
	var cursorPosition = null;
	if (colIdx < actionColumnIndex) {
		$(cell).html(title + '<br><input class="search-box"   type="text" />');

		// On every keypress in this input
		$(
			'input',
			$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
		)
			.off('keyup change')
			.on('change', function (_e) {
				// Get the search value
				$(this).attr('title', $(this).val());
				var regexr = '({search})';

				cursorPosition = this.selectionStart;
				// Search the column for that value
				api
					.column(colIdx)
					.search(
						this.value != ''
							? regexr.replace('{search}', '(((' + this.value + ')))')
							: '',
						this.value != '',
						this.value == ''
					)
					.draw();
				userIds = [];
				if (this.value != '') {
					var i = 0;
					$("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
						userIds.push(this.value);
						i++;
					});
				}
				else {
					userIds = [];
				}
			})
			.on('click', function (e) {
				e.stopPropagation();
			})
			.on('keyup', function (e) {
				e.stopPropagation();

				$(this).trigger('change');
				if (cursorPosition && cursorPosition != null) {
					$(this)
						.focus()[0]
						.setSelectionRange(cursorPosition, cursorPosition);
				}
			});
	} else {
		$(cell).html(title + '<br> &nbsp;');
	}
	
}

function viewInsurancePremium(insurancePremId, type,originPage) {
var url;
	if (type == 'V')
		url = '/getInsurancePremium';
	else if (type == 'E')
		url = '/getInsurancePremium';
	else if (type == 'P')
		url = '/getPendingInsurancePremium';
		
	 var data = "insurancePremId," + insurancePremId + ",viewType," + type +  ",originPage," + originPage ;
	postData(url, data);
}

function viewInsurancePremiumGrid(insurancePremId, type,approvedlist,originPage) {
	var url;
	if (approvedlist == 'Y')
		url = '/getInsurancePremium';
	else if (approvedlist == 'N')
	{
		if (type == 'R')
			url = '/getInsurancePremium';
		else	
			url = '/getPendingInsurancePremium';
		
	}	
	var data = "insurancePremId," + insurancePremId + ",viewType,V"
			+ ",originPage," + originPage ;
	postData(url, data);
}


function resetAction() {
$("#vcardType").val("SELECT");
$("#vcardVariant").val("SELECT");
$("#vannualPremiumAmtPerCard").val("");
$("#vvendor").val("SELECT");
$("#vfromMonth").val("SELECT");
$("#vfromYear").val("SELECT");
$("#vtoMonth").val("SELECT");
$("#vtoYear").val("SELECT");
$("#errvErrorInfo").hide();

$("#errvcardType").find('.error').html('');
$("#errvcardVariant").find('.error').html('');
$("#errvannualPremiumAmtPerCard").find('.error').html('');
$("#errvvendor").find('.error').html('');
$("#errvfromMonth").find('.error').html('');
$("#errvfromYear").find('.error').html('');
$("#errvtoMonth").find('.error').html('');
$("#errvtoYear").find('.error').html('');
}


function disableToValue()
{
if (typeof vNewCardCount2 != "undefined") {
		$('#vNewCardCount2').attr('disabled', true);
		if (document.getElementById("VOperatorIndi").value == "Between") {
		$('#vNewCardCount2').attr('disabled', false);
			}
	}
}

function viewInsurancePremiumAdd(_userID, _type,originPage) {
	
	var isValid = true;
    isValid = handleFieldCard(isValid);
    if (!validateField('vannualPremiumAmtPerCard', true, "Decimal", 100, false,0.0,99999999.99,true) && isValid) {
        isValid = false;
    }
    if (!validateField('vvendor', true, "SelectionBox", 100, false,0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('vfromMonth', true, "SelectionBox", 100, false,0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('vfromYear', true, "SelectionBox", 100, false,0,0,false) && isValid) {
    	        isValid = false;
    }
    if (!validateField('vtoMonth', true, "SelectionBox", 100, false,0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('vtoYear', true, "SelectionBox", 100, false,0,0,false) && isValid) {
        isValid = false;
    }

	if (!isValid) {
		return false;
	}

	
	var url = '/asignInsurancePremiumToFunctionAdd';
	
	checkDuplicateData(url,originPage);
	
}
function handleFieldCard(isValid) {
	if (!validateField('vcardType', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}
	if (!validateField('vcardVariant', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}
	return isValid;
}

function homeInsurancePremium(_userID, _type,originPage) {
	var url = '/showMcprInsurancePremiumConfig';
	var data = "originPage," + originPage ;
	postData(url, data);
}


function submitForm(url,_userType,originPage) {
	 var data = "userType," + "P"  + ",originPage," + originPage ;
	postData(url, data);
}

function checkDuplicateData(url,originPage) {

	var cardType=document.getElementById("vcardType").value;
	var cardVariant=document.getElementById("vcardVariant").value;
	var annualPremiumAmtPerCard=document.getElementById("vannualPremiumAmtPerCard").value;
	var vendor=document.getElementById("vvendor").value;
	var fromMonth=document.getElementById("vfromMonth").value;
	var fromYear=document.getElementById("vfromYear").value;
	var toMonth=document.getElementById("vtoMonth").value;
	var toYear=document.getElementById("vtoYear").value;
	var insurancePremId = 0;
	
	var validvRoleName=false;
	var msUrl = "checkDuplicateDataInsurancePremium";

	var tokenVal = document.getElementsByName("_TransactToken")[0].value;
		$.ajax({
			url: msUrl,
			type: "POST",
			dataType: "json",
			"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenVal);},
			data: {
				"cardType": cardType,
				"cardVariant": cardVariant,
				"fromMonth": fromMonth,
				"fromYear": fromYear,
				"toMonth": toMonth,
				"toYear": toYear,
				"insurancePremId": insurancePremId,
				_TransactToken: tokenVal
			},
			success: function(response) {
				if (response.status == "BSUC_0001") {
					validvRoleName = true;
					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "";
					var data = "insurancePremId,0,cardType," + cardType + 
					",cardVariant," + cardVariant + ",annualPremiumAmtPerCard," + annualPremiumAmtPerCard + 
					",vendor," + vendor + 
					",fromMonth," + fromMonth + ",fromYear," + fromYear + ",toMonth," + toMonth + ",toYear," + toYear + 
					 ",originPage," + originPage ;

					postData(url, data);					
				} else if(response.status == "Duplicate") {
					validvRoleName = false;
 					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "Insurance Premium Configuration Already Exists";
					
				}else {
					validvRoleName = false;
 					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "Discontinuousion in Insurance Premium Configuration. Last Configuration is done for " + response.status + " month.";
					
				}
			},
			error: function(_request, _status, _error) {
				errvErrorInfo.className = 'error';
				errvErrorInfo.innerHTML = "";
			}
		});

	return validvRoleName;
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    
	var fieldValue = $("#" + fieldId).val();
	var curdateinfo = document.getElementById("currDate").value;
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType !="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    isValid = handleFieldTypeIntAlpha(fieldType, fieldValue, isValid);
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
  	if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
      var fieldValue1;
      var fieldValue2;
      
      isValid = validateFieldTypeYear(fieldId, isValid, fieldValue1, fieldValue2, curdateinfo);

    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
    	if(rebateValidationMessages[fieldId]){
    		$("#err" + fieldId).find('.error').html(rebateValidationMessages[fieldId]);
    	}
        $("#err" + fieldId).show();
    }
    return isValid;
}


function validateFieldTypeYear(fieldId, isValid, fieldValue1, fieldValue2, curdateinfo) {
	if (fieldId == "vfromYear" && isValid) {

		fieldValue1 = $("#vfromYear").val() + $("#vfromMonth").val();
		fieldValue2 = curdateinfo;

		if (parseInt(fieldValue2) - parseInt(fieldValue1) > 2) {
			isValid = false;
		}
	}
	if (fieldId == "vtoYear" && isValid) {

		fieldValue1 = $("#vtoYear").val() + $("#vtoMonth").val();
		fieldValue2 = curdateinfo;

		if (Number(fieldValue1) < Number(fieldValue2)) {
			isValid = false;
		}
	}
	if (fieldId == "vtoYear" && isValid) {

		fieldValue1 = $("#vfromYear").val() + $("#vfromMonth").val();
		fieldValue2 = $("#vtoYear").val() + $("#vtoMonth").val();

		if (Number(fieldValue1) >= Number(fieldValue2)) {
			isValid = false;
		}
	}
	return isValid;
}

function handleFieldTypeIntAlpha(fieldType, fieldValue, isValid) {
	var regEx;
	if (fieldType == "Alphabet") {
		regEx = /^[A-Z]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphabetWithSpace") {
		regEx = /^[A-Z ]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphanumericNoSpace") {
		regEx = /^[A-Za-z0-9]+$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "Integer") {
		regEx = /^\d*$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}

	if (fieldType == "Decimal") {
		regEx = /^\d*(\.\d{0,2})?$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

function mySelect(){
 $('#jqueryError4').hide();
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	 
	  var referenceNoList = document.getElementById("newsIds");
   
	 if(array.length==referenceNoListPendings.length){
		 $('#selectAll').prop('checked', true);
		
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
			 $("#toggleModalNews").modal('show');
	 }
	 else{
		 $("#toggleModalNews").modal('hide');
		 
	 }
	
}

function ApproveorRejectBulkInsurancePremium(type,action){
	
	 var url = '/approveInsurancePremiumBulk';
	 var i=0;
	 var array = [];

	 if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(action=='All'){
		 array=referenceNoListPendings; 
	 }
	   if(userIds.length>0){
			  
		  $('#jqueryError4').hide();
		 let referenceIdIdList = "";
		
		for ( i of userIds) {
			referenceIdIdList = referenceIdIdList + i + "|";
		}
		 let data;
	if(type=='A'){
			
		 data =  "status,"+"A"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	else if(type=='R'){
		
		 data = "status,"+"R"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	
	postData(url, data);
	}
	   else if(array.length>0){
			  $('#jqueryError4').hide();
			 let referenceIdIdList = "";
			 
			for(i of array){  
		        referenceIdIdList = referenceIdIdList + i + "|" ;  
		     } 
			 let data = "";
			if(type=='A'){
			 data =  "status,"+"A"+",bulkApprovalReferenceNoList,"+referenceIdIdList;
			}
			else if(type=='R'){
			 data = "status,"+"R"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
			}
		postData(url, data);
		}
	else{
			  
			  $('#errorStatus4').html('Please Select  Atleast One Record');
				$('#jqueryError4').show();
		  }
}


function deselectAll() {
 $('#jqueryError4').hide();
	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  
	var i =0;
		for ( i of ele) {
			if(i.type=='checkbox')  
         		i.checked=false; 
		}
 
}


 