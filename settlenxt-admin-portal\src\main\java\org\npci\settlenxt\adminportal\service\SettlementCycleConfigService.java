package org.npci.settlenxt.adminportal.service;

import java.text.ParseException;
import java.util.List;

import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.SettlementCycleConfigDTO;

public interface SettlementCycleConfigService {

	SettlementCycleConfigDTO getSrNoInfoFromStg(int i);

	List<SettlementCycleConfigDTO> getApprovedSettlementFromMain();

	List<SettlementCycleConfigDTO> getPendingSettlementFromStg();

	SettlementCycleConfigDTO updateApproveOrRejectBulkSettlementCycle(String settlementCycleNoList, String status,
			String remarks);

	SettlementCycleConfigDTO getSettlementDetails(String srNo);

	SettlementCycleConfigDTO approveOrRejectSettlement(String srNo, String status, String remarks);

	void deleteSettlement(String srNo);

	SettlementCycleConfigDTO getSrNoInfoFromMain(int i);

	 List<CodeValueDTO> getLookupDataSorted(String string);

	SettlementCycleConfigDTO addSettlementCycle(SettlementCycleConfigDTO settlementCycleConfigDTO);

	 SettlementCycleConfigDTO updateSettlementCycle(SettlementCycleConfigDTO settlementCycleConfigDTO);

	 boolean validateSettlementTime(String flow, String startHr, String endHr, String cycleNum, String prodId);

	 boolean validateOverLapSettlementTime(String prodId, String startHr, String endHr, String cycleNum)
			throws ParseException;

}
