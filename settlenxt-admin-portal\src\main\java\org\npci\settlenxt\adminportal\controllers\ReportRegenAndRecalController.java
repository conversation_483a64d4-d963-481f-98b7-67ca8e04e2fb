package org.npci.settlenxt.adminportal.controllers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.cache.LookupDTOCache;
import org.npci.settlenxt.adminportal.common.cache.SysParams;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.DateUtils;
import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.service.IReportProgressStatusService;
import org.npci.settlenxt.adminportal.service.IReportRegenAndRecalService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonPrimitive;

import io.vertx.core.json.JsonObject;

/**
 * This controller class is used to regenerate and recalculate the reports
 * 
 * <AUTHOR>
 *
 */
@Controller
public class ReportRegenAndRecalController extends BaseController {

	@Autowired
	IReportRegenAndRecalService reportRegenAndRecalService;

	@Autowired
	IReportProgressStatusService reportProgressStatusService;
	
	@Autowired
	LookupDTOCache lookupDTOCache;

	@Autowired
	SysParams sysparams;

	public static final String REPORT_RECAL="reportRecalculation";
	public static final String REPORT_REGENERATION="reportRegeneration";
	private static final String REGEN_REPORT_TYPES_NPCI = "REGEN_REPORT_TYPES_NPCI";
	private static final String REGEN_REPORT_TYPES = "REGEN_REPORT_TYPES_";
	private static final String PARTICIPANT_NPCI = "Participant_Id";

	/**
	 * This method is used to get regeneration of report status
	 * 
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/regenerationReportStatus")
	@ResponseBody
	public String getReportRegenStatus(@RequestBody CycleManagementDTO cycleManagementDTO, Model model)
			throws SettleNxtException {
		cycleManagementDTO.setRequestType(CommonConstants.REGENERATION);
		return reportRegenAndRecalService.sendDataToReportRegeneration(cycleManagementDTO);
	}

	/**
	 * <li>This method is used to fetch product id with respective cycle number,
	 * fetch the participantId and regeneration status of reports</li>
	 * 
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/reportRegeneration")
	public String fetchProductIdDetails(
			@ModelAttribute(CommonConstants.CYCLE_MANAGEMENT_DTO) CycleManagementDTO cycleManagementDTO, Model model)
			throws SettleNxtException {
		prepareReportRegeneration(cycleManagementDTO);
		cycleManagementDTO.setRequestType(CommonConstants.REGENERATION);
		cycleManagementDTO = reportRegenAndRecalService.getRegenerationReportsStatus(cycleManagementDTO);
		model.addAttribute(CommonConstants.CYCLE_MANAGEMENT_DTO, cycleManagementDTO);
		return getView(model, REPORT_REGENERATION);
	}

	private void prepareReportRegeneration(CycleManagementDTO cycleManagementDTO) {
		String response = reportProgressStatusService.fetchProductIdDetails(cycleManagementDTO);
		JsonObject data = new JsonObject(response);
		@SuppressWarnings("unchecked")
		Map<String, String> productData = data.mapTo(Map.class);
		cycleManagementDTO.setSettleProduct(productData);
		if (StringUtils.isBlank(cycleManagementDTO.getCycleDate())) {
			String formattedRequestDate = DateUtils.getTodayLocalDate(DateUtils.YYYY_MM_DD);
			cycleManagementDTO.setCycleDate(formattedRequestDate);
		}
		List<String> paricipants = getParticipantList();
		cycleManagementDTO.setParticipantList(paricipants);
	}

	/**
	 * This method is used to get recalculation of report status
	 * 
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/recalculationReportStatus")
	@ResponseBody
	public String getRecalculationStatus(@RequestBody CycleManagementDTO cycleManagementDTO, Model model)
			throws SettleNxtException {
		cycleManagementDTO.setRequestType(CommonConstants.RECALCULATION);
		return reportRegenAndRecalService.sendDataToReportRegeneration(cycleManagementDTO);
	}

	/**
	 * <li>This method is used to fetch product id with respective cycle number and
	 * send data to recalculation of reports</li>
	 * 
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/reportRecalculation")
	public String sendDataToReportRecalculation(
			@ModelAttribute(CommonConstants.CYCLE_MANAGEMENT_DTO) CycleManagementDTO cycleManagementDTO, Model model)
			throws SettleNxtException {
		prepareReportRegeneration(cycleManagementDTO);
		cycleManagementDTO.setRequestType(CommonConstants.RECALCULATION);
		cycleManagementDTO = reportRegenAndRecalService.getRegenerationReportsStatus(cycleManagementDTO);
		model.addAttribute(CommonConstants.CYCLE_MANAGEMENT_DTO, cycleManagementDTO);
		return getView(model, REPORT_RECAL);
	}

	private List<String> getParticipantList() {
		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
		int iDisplayStart = 0;
		int iDisplayLength = 0;
		String sSearch = "";
		List<String> paricipants = new ArrayList<>();
		int startval = iDisplayStart + 1;
		int endval = iDisplayStart + iDisplayLength;
		searchCriteriaDTO.setStartVal(startval);
		searchCriteriaDTO.setEndVal(endval);
		searchCriteriaDTO.setSearchName(sSearch);
		List<MemberDTO> paricipantList = reportRegenAndRecalService.getParticipantList(searchCriteriaDTO);
		if (paricipantList != null && !paricipantList.isEmpty()) {
			JsonArray row = new JsonArray();
			for (MemberDTO c : paricipantList) {
				row.add(new JsonPrimitive(c.getParticipantId() == null ? "" : c.getParticipantId() + ""));
			}
			paricipants = new Gson().fromJson(row, ArrayList.class);
		}
		return paricipants;
	}

	/**
	 * This method is used to merge tables
	 * 
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/mergeTables")
	@ResponseBody
	public String getMergeTables(@RequestBody CycleManagementDTO cycleManagementDTO, Model model) throws SettleNxtException {
		cycleManagementDTO.setRequestType(CommonConstants.MERGE_TABLES);
		return reportRegenAndRecalService.sendDataToReportRegeneration(cycleManagementDTO);
	}

	/**
	 * This method is used to delete recal transactions
	 * 
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/deleteRecalTxns")
	@ResponseBody
	public String deleteRecalTxns(@RequestBody CycleManagementDTO cycleManagementDTO, Model model) throws SettleNxtException {
		return reportRegenAndRecalService.deleteRecalTxns(cycleManagementDTO);
	}

	@GetMapping("/getReportTypes")
	public ResponseEntity<List<CodeValueDTO>> getReportTypes(@RequestParam("participantId") String participantId,
			@RequestParam("prodId") String prodId) {
		List<CodeValueDTO> reportTypes;
		if (sysparams.getSystemKeyValue(CommonConstants.NPCI_MEMBER, PARTICIPANT_NPCI)
				.equalsIgnoreCase(participantId)) {
			reportTypes = lookupDTOCache.getLookupListOfType(REGEN_REPORT_TYPES_NPCI);
		} else {
			reportTypes = lookupDTOCache.getLookupListOfType(REGEN_REPORT_TYPES + prodId);
		}
		return ResponseEntity.ok(reportTypes);
	}

}
