<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>

<script type="text/javascript"
	src="./static/js/validation/viewSysParam.js"></script>

<form:form onsubmit="removeSpace(this); encodeForm(this);" method="POST"
	id="viewApprovalSysParams" modelAttribute="sysParamsDTO"
	autocomplete="off">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message
						code="sysParams.viewscreen.title" /></span></strong>
			<div class="icon_bar">
				<sec:authorize access="hasAuthority('Edit Sys Params') ">
					<a data-toggle="tooltip" title="Edit"
						onclick="editSysParamsInfo('${sysParamsDTO.sysType}','${sysParamsDTO.sysKey}','/editSysParam')"
						href="#"><img src="./static/images/edit-grey.png" alt="edit"></a>

				</sec:authorize>
			</div>
		</div>

		<div class="panel-body">
			<form:hidden path="sysType" value="${sysParamsDTO.sysType}" />
			<form:hidden path="sysKey" value="${sysParamsDTO.sysKey}" />

			<table class="table table-striped" style="font-size: 12px">
			<caption style="display:none;">View SysParam</caption> 
			<thead style="display:none;"><th scope="col"></th></thead>

				<caption style="display: none;">
					<spring:message code="sysParam.main.title" />
				</caption>
				
				<tbody>
					<tr>


						<td><label><spring:message code="sm.lbl.sysType" /><span
								style="color: red"></span></label></td>
						<td>${sysParamsDTO.sysType}</td>

						<td><label><spring:message code="sm.lbl.sysKey" /><span
								style="color: red"></span></label></td>
						<td>${sysParamsDTO.sysKey}</td>

						<td><label><spring:message code="sm.lbl.sysValue" /><span
								style="color: red"></span></label></td>
						<td>${sysParamsDTO.sysValue}</td>
						<td><label><spring:message code="sm.lbl.desc" /><span
								style="color: red"></span></label></td>
						<td>${sysParamsDTO.desc}</td>
					</tr>
					<tr>
						<td><label><spring:message code="sm.lbl.addProp" /><span
								style="color: red"></span></label></td>
						<td>${sysParamsDTO.addProp}</td>

						<td><label><spring:message code="sm.lbl.status" /><span
								style="color: red"></span></label></td>
						<c:choose>
							<c:when test="${sysParamsDTO.status=='A' }">
								<td>Active</td>
							</c:when>
							<c:otherwise>
								<td>InActive</td>
							</c:otherwise>
						</c:choose>


						<td><label><spring:message code="sm.lbl.createdBy" /><span
								style="color: red"></span></label></td>
						<td>${sysParamsDTO.createdBy}</td>
					</tr>

				</tbody>
			</table>

			<div style="text-align: center">
				<button type="button" class="btn btn-danger"
					onclick="submitForm('/showSysParamsMain');">
					<spring:message code="ifsc.backBtn" />
				</button>
			</div>
		</div>
	</div>
</form:form>
