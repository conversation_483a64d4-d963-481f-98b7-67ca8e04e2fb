<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/viewFeatureFee.js"
	type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">
		 <form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewFeatureFee" modelAttribute="featureFeeDto"
			action="${approveFeatureFeeStatus}" autocomplete="off"> 
			<input type="hidden" id="cardId" value="${featureFeeDto.cardConfigId}" />
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="featureFee.viewscreen.title" /></span></strong>
						</div>
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">Feature Fee</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
										<td><label><spring:message code="featureFee.cardConfigId" /></label></td>
										<td id="cardConfigId">${featureFeeDto.cardConfigId }</td>
										<td><label><spring:message code="featureFee.cardType" /></label></td>
										<td id="cardType">${featureFeeDto.cardTypeName }</td>
										<td><label><spring:message code="featureFee.cardVariant" /></label></td>
										<td id="cardVariant">${featureFeeDto.cardVariantName }</td>
									</tr>
									<tr>
										<td><label><spring:message code="featureFee.featureFee" /></label></td>
										<td id="featureFee">${featureFeeDto.featureFee }</td>
										<td><label><spring:message code="featureFee.feature" /></label></td>
										<td id="feature">${featureFeeDto.feature }</td>
										<td><label><spring:message code="featureFee.details" /></label></td>
										<td id="details">${featureFeeDto.details }</td>
									</tr>
									<tr>
										<td><label><spring:message code="featureFee.fromDate" /></label></td>
										<td id="fromDate">${featureFeeDto.fromDate }</td>
										<td><label><spring:message code="featureFee.toDate" /></label></td>
										<td id="toDate">${featureFeeDto.toDate }</td>
										<td></td>
										<td></td>
									</tr>
								</tbody>
							</table>
							
							
							<div style="text-align:center;">
				<hr />
				<div style="text-align:center">
					<button type="button" class="btn btn-danger"
						onclick="userAction('N','/featureFeeConfiguration');">
						<spring:message code="featureFee.backBtn" /></button>
						
					<c:if test="${featureFeeDto.requestState =='A' }">
						<sec:authorize access="hasAuthority('Edit Feature Fee')">
							<input name="editButton" type="button" class="btn btn-success"
							 id="approveRole" value="Edit" 
							onclick="EditFeatureFee('/editFeatureFee','${featureFeeDto.cardConfigId}','mainTab');"/>
						</sec:authorize>
					</c:if>

															
				</div>
		</div>
							
						</div>
					</div>
				</div>
			</div>
		</form:form>
		
	</div>

</div>
