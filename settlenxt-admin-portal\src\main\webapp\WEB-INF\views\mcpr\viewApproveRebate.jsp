<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/viewApproveRebate.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">Please
		Select Approve/Reject action.</div>
	<div class="alert alert-danger remarkMust" role="alert">Please
		Enter Remarks.</div>
	<c:url value="approveRebateStatus" var="approveRebateStatus" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveRebate" modelAttribute="rebateInfoDto"
		action="${approveRebateStatus}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"> <spring:message code="rebate.pendingRebate" /> </span></strong>
					</div>

					<div class="panel-body">
					 
						<input type="hidden" id="rebateId" value="${rebateInfoDto.rebateID}" /> 

						<input type="hidden" id="crtuser" value="${rebateInfoDto.lastUpdatedBy}" />

						<table class="table table-striped infobold" style="font-size: 12px">
							<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
									<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message code="rebate.requestInformation" /></span></strong>
										</div></td>
 										<td></td>
										<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									
									<td><label><spring:message code="rebate.requestType" /><span style="color: red"></span></label></td>
									<td>${rebateInfoDto.lastOperation}</td>
									<td><label><spring:message code="rebate.requestDate" /><span style="color: red"></span></label></td>
									<td>${rebateInfoDto.lastUpdatedOn}</td>
									<td><label><spring:message code="rebate.requestStatus" /><span style="color: red"></span></label></td>
									<td>${rebateInfoDto.requestStateDiscription}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									
								</tr>
								<tr>
									<td><label><spring:message code="rebate.requestBy" /><span style="color: red"></span></label></td>
									<td>${rebateInfoDto.lastUpdatedBy}</td>
									
									<td><label><spring:message code="rebate.approverComments" /><span style="color: red"></span></label></td>
									<td colspan=2>${rebateInfoDto.checkerComments}</td>									
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>

								  <tr>
								<td colspan="6">
								<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="rebate.rebateInformation" /></span></strong>
						</div>
						<div class="panel-body">
							<input type="hidden" id="cardType" value="${rebateInfoDto.cardType}"/>
							<input type="hidden" id="financialYear" value="${rebateInfoDto.financialYear}"/>
							<input type="hidden" id="featureOrBaseFee" value="${rebateInfoDto.featureOrBaseFee}"/>
							<input type="hidden" id="operatorIndi" value="${rebateInfoDto.operatorIndi}"/>
							<input type="hidden" id="status" value="${rebateInfoDto.status}"/>
							<table class="table table-striped" style="font-size: 12px">
								<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
									<tbody>
									<tr>
										<td><label><spring:message code="rebate.cardType" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.cardTypeName }</td>
										<td><label><spring:message code="rebate.financialYear" /><span
												style="color: red"></span></label></td>
										<td>${rebateInfoDto.financialYear }</td>
										</tr>
										<tr>
										<td><label><spring:message code="rebate.featureOrBaseFee" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.featureOrBaseFee}</td>
										<td><label><spring:message code="rebate.operationalIndi" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.operatorIndi}</td>
										</tr>
										<tr>
										<td><label><spring:message code="rebate.newCardCount1" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.newCardCount1}</td>
										<td><label><spring:message code="rebate.newCardCount2" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.newCardCount2}</td>
										</tr>
										<tr>
										<td><label><spring:message code="rebate.rebatePercentage" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.rebatePercentage}</td>
										<td><label><spring:message code="rebate.rebateStatus" /><span style="color: red"></span></label></td>
										<td>${rebateInfoDto.requestStateDiscription}</td>
									</tr>

								</tbody>
							</table>
						</div>
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message code="rebate.cardVariantInformation" /></span></strong>
						</div>
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
									
								<tbody>
									<c:if test="${not empty cardVariantList}">
									<c:forEach items="${cardVariantList}" var="cardVariant">
									<tr class="selectedRoles">
									<td>${cardVariant.cardVariantName}</td>
									</tr>
									</c:forEach>
									</c:if>
								</tbody>
							</table>
						</div>
						<sec:authorize access="hasAuthority('Approve Rebate')">
							<c:if test="${ (viewTypeRebate eq 'E') || (viewTypeRebate eq 'P')}">
								<c:if test="${rebateInfoDto.requestState eq 'S' or rebateInfoDto.requestState eq 'P' or  rebateInfoDto.requestState eq 'Pending For Approval'}" >
									<tr>
										<td colspan="6"><div class="panel-heading-red  clearfix">
												<strong><span class="glyphicon glyphicon-info-sign"></span> <span
													data-i18n="Data"><spring:message
															code="AM.lbl.reqInfo" /></span></strong>
											</div></td>
									</tr>

									<tr>										<td><label><spring:message
													code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
										<td><select name="select" id="apprej"
											onchange="display()">
												<option value="N"><spring:message
														code="AM.lbl.select" /></option>
												<option value="A" id="approve"><spring:message
														code="AM.lbl.approve" /></option>
												<option value="R" id="reject"><spring:message
														code="AM.lbl.reject" /></option>
										</select></td>
										<td>
										<div style="text-align:center">
												<label><spring:message code="AM.lbl.remarks" /><span
													style="color: red">*</span></label>
											</div></td>
										<!-- //Added by deepak on 31-03-2016 -->
										<td colspan="2"><textarea rows="4" cols="50"
												maxlength="100" id="rejectReason"></textarea>
											<div id="errorrejectReason" class="error"></div></td>
									</tr>
								</c:if>
							</c:if>
						</sec:authorize>

							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
										<sec:authorize access="hasAuthority('Approve Rebate')">
										<c:if test="${ (viewTypeRebate eq 'E') || (viewTypeRebate eq 'P')}">
											<c:if test="${rebateInfoDto.requestState eq 'S' or rebateInfoDto.requestState eq 'P'  or  rebateInfoDto.requestState eq 'Pending For Approval'}">
												
												<button type="button" class="btn btn-success" id="approveRole" 
													onclick="postAction('/approveRebateStatus','${originPage}');"><spring:message code="rebate.submit" /></button>
											</c:if>
										</c:if>
										</sec:authorize>
									
										<button type="button" class="btn btn-danger"
											onclick="backAction('P','/rebatePendingForApproval','${originPage}');"><spring:message code="rebate.back" /></button>
								
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

