$(document).ready(function () {    

    if ($("#apprej").length) {

        $('.appRejMust').hide();
        $('.remarkMust').hide();

        $('#apprej').change(function () {
            if ($("#apprej").val() != "N") {
                $(".appRejMust").hide();
            } else {
                $(".appRejMust").show();
                $(".remarkMust").hide();
            }
        });

    }

});



window.history.forward();
function noBack() {
    window.history.forward();
}

function display() {
    $(".appRejMust").hide();

}
function userAction(action) {
	var ifscCode = $("#ifscCode").val(); 
	var data = "ifscCode," + ifscCode ;
	postData(action, data);
}


 function postAction(_action) {
	var data="";
	var ifscCode;
	var url="";
	var remarks="";
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 ifscCode = $("#ifscCode").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveIFSC';
				 data = "ifscCode," + ifscCode + ",status," + "A" + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				 ifscCode = $("#ifscCode").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveIFSC';
				 data = "ifscCode," + ifscCode + ",status," + "R"  + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
	
			 
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		