package org.npci.settlenxt.adminportal.validator.service.dto;

import java.math.BigDecimal;
import java.util.List;

public class HeaderRecord {
	private List<Record> records;
	
	private BigDecimal tranTotal;
	private int tranCount;

	public List<Record> getRecords() {
		return records;
	}

	public void setRecords(List<Record> records) {
		this.records = records;
	}

	public String getHeaderDataAsString() {
		StringBuilder allRecord = new StringBuilder();
		Record recod = null;
		for(int i=0; i<records.size(); i++){
			recod = records.get(i);
			allRecord.append(recod.getName()+"="+recod.getValue());
			if(i < (records.size()-1))
				{
				allRecord.append(",");
				}
		}
		return allRecord.toString();
	}
	
	public String getHeaderDataAsXMLString() {
		StringBuilder allRecord = new StringBuilder();
		Record recod = null;
		for(Record rec:records){
			recod = rec;
			allRecord.append("<" + recod.getName() + ">" + recod.getValue() + "</" + recod.getName() + ">");
		}
		
		return allRecord.toString();
	}

	
	public BigDecimal getTranTotal() {
		return tranTotal;
	}

	public void setTranTotal(BigDecimal tranTotal) {
		this.tranTotal = tranTotal;
	}

	public int getTranCount() {
		return tranCount;
	}

	public void setTranCount(int tranCount) {
		this.tranCount = tranCount;
	}
}
