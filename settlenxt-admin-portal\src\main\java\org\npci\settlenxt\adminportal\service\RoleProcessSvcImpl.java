/**
 * 
 */
package org.npci.settlenxt.adminportal.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.FunctionalityRepository;
import org.npci.settlenxt.adminportal.repository.RoleRepository;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.FunctionalityDTO;
import org.npci.settlenxt.portal.common.dto.RoleDTO;
import org.npci.settlenxt.portal.common.dto.RoleFunctionalityDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseRoleProcessSvcImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 *
 */

@Service
@SuppressWarnings("unused")
public class RoleProcessSvcImpl extends BaseRoleProcessSvcImpl implements RoleProcessSvc {
	
	private static final Logger logger = LogManager.getLogger(RoleProcessSvcImpl.class);

	@Autowired
	private RoleRepository roleRepository;

	@Autowired
	private FunctionalityRepository functionalityRepository;

	@Autowired
	private SessionDTO sessionDTO;

	private static final String ROLE_FUNC_ERROR = "AM_MSG_roleTofunctionalityError";

	@Transactional(propagation = Propagation.REQUIRED)
	@Override
	public RoleFunctionalityDTO addRole(RoleFunctionalityDTO roleFunctionalityDTO) {

		roleFunctionalityDTO.setStatus(BaseCommonConstants.USER_STATUS);
		roleFunctionalityDTO.setRoleId(roleRepository.fetchIdFromRoleIdSequence());
		roleFunctionalityDTO.setRoleName(roleFunctionalityDTO.getRoleName());
		roleFunctionalityDTO.setRoleDesc(roleFunctionalityDTO.getRoleDesc());
		roleFunctionalityDTO.setCreatedBy(sessionDTO.getUserName());
		roleFunctionalityDTO.setCreatedDate(new Date());

		roleFunctionalityDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		roleFunctionalityDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
		generateAndAssignFunctionlistDTO(roleFunctionalityDTO);
		roleFunctionalityDTO.getFunctionalityDTOs().forEach(funcDto -> {
			funcDto.setCreatedBy(roleFunctionalityDTO.getCreatedBy());
			funcDto.setCreatedDate(roleFunctionalityDTO.getCreatedDate());
			funcDto.setLastUpdatedBy(null);
			funcDto.setLastUpdatedOn(null);
		});
		try {
			int status = roleRepository.insertRoleStg(roleFunctionalityDTO);

			if (status == 1) {

				int roleFuncResult = functionalityRepository.insertBatchRoletoFunctionalities(roleFunctionalityDTO);
				if (roleFuncResult > 0) {
					roleFunctionalityDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
				} else {
					throw new SettleNxtException(ROLE_FUNC_ERROR, "");
				}

			} else {
				throw new SettleNxtException(ROLE_FUNC_ERROR, "");
			}

			roleFunctionalityDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
		} catch (Exception ex) {

			throw new SettleNxtException(ROLE_FUNC_ERROR, "",ex);

		}

		return roleFunctionalityDTO;
	}

	private void generateAndAssignFunctionlistDTO(RoleFunctionalityDTO roleFunctionalityDTO) {
		FunctionalityDTO funcDTO;
		List<FunctionalityDTO> funcList = new ArrayList<>();
		String[] fuctionality = roleFunctionalityDTO.getFunctionalityIds().split("\\|");
		for (int i = 0; i < fuctionality.length; i+=2) {
			funcDTO = new FunctionalityDTO();
			funcDTO.setRoleId(roleFunctionalityDTO.getRoleId());
			if (fuctionality[i].matches("[0-9]+")) {

				funcDTO.setFuncId(Integer.parseInt(fuctionality[i]));
				if ((i + 1) < fuctionality.length) {
					funcDTO.setFuncName(fuctionality[i + 1]);
				}
				funcDTO.setStatus("E");
				
			} 
			funcList.add(funcDTO);
		}
		roleFunctionalityDTO.setFunctionalityDTOs(funcList);
	}

	@Transactional
	@Override
	public RoleFunctionalityDTO editRole(RoleFunctionalityDTO roleFunctionalityDTO)  {

		roleFunctionalityDTO.setLastUpdatedBy(sessionDTO.getUserName());
		roleFunctionalityDTO.setLastUpdatedOn(new Date());
		roleFunctionalityDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		roleFunctionalityDTO.setStatus(BaseCommonConstants.USER_STATUS);
		roleFunctionalityDTO.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
		roleRepository.updateRoleStgOperation(roleFunctionalityDTO.getLastUpdatedBy(),
				roleFunctionalityDTO.getLastUpdatedOn(), roleFunctionalityDTO.getRequestState(),
				roleFunctionalityDTO.getRoleId(), "", roleFunctionalityDTO.getLastOperation());
		generateAndAssignFunctionlistDTO(roleFunctionalityDTO);
		roleFunctionalityDTO.getFunctionalityDTOs().forEach(funcDto -> {
			funcDto.setWorkFlowId(funcDto.getWorkFlowId());
			funcDto.setRoleId(roleFunctionalityDTO.getRoleId());
		});
		List<RoleFunctionalityDTO> mappedRole = roleRepository.getRoleFuncStgByRoleId(roleFunctionalityDTO.getRoleId());

		Map<Long, List<RoleFunctionalityDTO>> map = mappedRole.stream()
				.collect(Collectors.groupingBy(RoleFunctionalityDTO::getFuncId));

		roleFunctionalityDTO.getFunctionalityDTOs().forEach(funcDto -> {

			funcDto.setLastUpdatedBy(null);
			funcDto.setLastUpdatedOn(null);
			if (map != null) {

				if (map.containsKey(funcDto.getFuncId())) {

					funcDto.setCreatedDate(map.get(funcDto.getFuncId()).get(0).getCreatedOn());
					funcDto.setCreatedBy(map.get(funcDto.getFuncId()).get(0).getCreatedBy());

				} else {
					funcDto.setCreatedDate(new Date());
					funcDto.setCreatedBy(sessionDTO.getUserName());

				}
			} else {

				funcDto.setCreatedDate(new Date());
				funcDto.setCreatedBy(sessionDTO.getUserName());
			}
		});
		functionalityRepository.deleteRoleFunctionalityStg(roleFunctionalityDTO.getRoleId());
		functionalityRepository.insertBatchRoletoFunctionalities(roleFunctionalityDTO);
		roleFunctionalityDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

		return roleFunctionalityDTO;

	}

	@Override
	@Transactional
	public RoleDTO updateApproveOrRejectRole(String roleId, String status, String remarks)  {

		if (StringUtils.isBlank(roleId)) {
			throw new SettleNxtException("AM_MSG_wfid_Empty", "");
		}

		RoleDTO roleDTO = getRoleStgInfo(roleId);
		roleDTO.setRequestState(status);
		roleDTO.setCheckerComments(remarks);
		roleDTO.setLastUpdatedOn(new Date());
		roleDTO.setLastUpdatedBy(sessionDTO.getUserName());

		// Update work flow tables here
		if ("Approved".equals(status)) {
			roleDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			roleDTO.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			roleDTO.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
		} else {
			roleDTO.setRequestState(CommonConstants.REQUEST_STATE_REJECTED);
			roleDTO.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
		}

		if (roleDTO.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
			// To update final table

			try {
				updateApprovedRole(roleDTO);
			} catch (Exception e) {
				//  Auto-generated catch block
				logger.info("Exception in Role Approval {}",e.getMessage(),e);
			}
		}
		roleRepository.updateRoleStgState(roleDTO.getLastUpdatedBy(), roleDTO.getLastUpdatedOn(),
				roleDTO.getLastOperation(), roleDTO.getRequestState(), roleDTO.getRoleId(), remarks);

		return roleDTO;
	}

	@Override
	@Transactional
	public RoleDTO discardRejectedRoleEntry(String roleId)  {

		if (StringUtils.isBlank(roleId)) {
			throw new SettleNxtException("AM_MSG_wfid_Empty", "");
		}

		RoleDTO roleDTO = getRoleStgInfo(roleId);

		RoleDTO roleDtoDB = roleRepository.getRoleInfoByRoleId(roleDTO.getRoleId());
		if (roleDtoDB == null) {
			roleDTO.setRequestState(CommonConstants.REQUEST_STATE_DISCARDED);
			roleRepository.deleteRoleStgDiscard(roleDTO.getRoleId());

		} else {

			roleRepository.updateRoleStgState(sessionDTO.getUserName(), new Date(),
					CommonConstants.LAST_OPERATION_DISCARD, CommonConstants.REQUEST_STATE_APPROVED, roleDTO.getRoleId(),
					"");
			functionalityRepository.deleteRoleFunctionalityStg(roleDTO.getRoleId());
			createRoleFunctionalityFromRoleMain(roleDTO.getRoleId());
		}

		return roleDTO;
	}

	private void updateApprovedRole(RoleDTO roleDTO) throws SettleNxtException {
		RoleDTO roleDtoDB = roleRepository.getRoleInfoByRoleId(roleDTO.getRoleId());
		if (ObjectUtils.isEmpty(roleDtoDB)) {
			roleDTO.setCreatedOn(roleDTO.getLastUpdatedOn());
			roleDTO.setCreatedBy(roleDTO.getCreatedBy());
			long roleId = roleRepository.saveRole(roleDTO);
		} else {
			roleRepository.updateRole(roleDTO);
		}

		List<RoleFunctionalityDTO> mappedRole = roleRepository.getRoleFuncByRoleId(roleDTO.getRoleId());

		Map<Long, List<RoleFunctionalityDTO>> map = mappedRole.stream()
				.collect(Collectors.groupingBy(RoleFunctionalityDTO::getFuncId));

		functionalityRepository.deleteRoleFunctionality(roleDTO.getRoleId());

		createRoleFunctionality(roleDTO.getRoleId(), map);
		roleDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
	}

	public void createRoleFunctionality(int roleId, Map<Long, List<RoleFunctionalityDTO>> map) {
		List<FunctionalityDTO> functionalityStgDtoList = functionalityRepository
				.getFunctionalityStgMappingByRoleId(roleId);

		functionalityStgDtoList.forEach(funcDto -> {
			funcDto.setStatus("A");
			if (map != null) {

				if (map.containsKey(funcDto.getFuncId())) {

					funcDto.setCreatedOn(funcDto.getCreatedOn());
					funcDto.setCreatedBy(funcDto.getCreatedBy());
					funcDto.setLastUpdatedBy(null);
					funcDto.setLastUpdatedOn(null);

				} else {
					funcDto.setCreatedOn(new Date());
					funcDto.setCreatedBy(funcDto.getCreatedBy());
					funcDto.setLastUpdatedBy(null);
					funcDto.setLastUpdatedOn(null);

				}
			} else {
				funcDto.setCreatedOn(new Date());
				funcDto.setCreatedBy(funcDto.getCreatedBy());
				funcDto.setLastUpdatedBy(null);
				funcDto.setLastUpdatedOn(null);

			}
		});
		functionalityStgDtoList
				.forEach(functionalityStgDto -> functionalityRepository.saveFunctionality(functionalityStgDto));
	}

	public void createRoleFunctionalityFromRoleMain(int roleId) {
		RoleFunctionalityDTO roleFunctionalityDTO = new RoleFunctionalityDTO();
		roleFunctionalityDTO.setStatus(BaseCommonConstants.USER_STATUS);
		roleFunctionalityDTO.setCreatedBy(sessionDTO.getUserName());
		roleFunctionalityDTO.setCreatedDate(new Date());
		roleFunctionalityDTO.setLastUpdatedBy(sessionDTO.getUserName());
		roleFunctionalityDTO.setLastUpdatedOn(new Date());
		roleFunctionalityDTO.setRoleId(roleId);
		List<FunctionalityDTO> functionalityStgDtoList = functionalityRepository
				.getFunctionalityRoleMappingByRoleId(roleId);
		functionalityStgDtoList.forEach(functionalityStgDTO -> {
			functionalityStgDTO.setStatus("A");
			functionalityStgDTO.setLastUpdatedBy(sessionDTO.getUserName());
			functionalityStgDTO.setLastUpdatedOn(new Date());

		});
		roleFunctionalityDTO.setFunctionalityDTOs(functionalityStgDtoList);
		functionalityRepository.insertBatchRoletoFunctionalities(roleFunctionalityDTO);
	}

	@Override
	@Transactional(readOnly = true)
	public void checkRoleStatusForDeactivate(RoleDTO roleDto) {
		if (BaseCommonConstants.ROLE_STATUS_DEACT.equalsIgnoreCase(roleDto.getStatus())) {
			throw new SettleNxtException("AM_MSG_roleDeleteEdit", "");
		}
	}

	@Override
	@Transactional(readOnly = true)
	public void checkRoleAssignedToUser(RoleDTO roleDto) {

		int count = roleRepository.checkInUserRoleMapping(roleDto);
		if (count > 0) {
			throw new SettleNxtException("AM_MSG_roleAssigned", "");
		}
	}

	@Override
	public void deactivateRole(RoleDTO roleDto) throws SettleNxtException {
		roleDto.setLastOperation(CommonConstants.LAST_OPERATION_DELETE);
		roleDto.setRequestState(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
		roleDto.setLastUpdatedBy(sessionDTO.getUserName());
		roleDto.setLastUpdatedOn(new Date());
		roleDto.setStatus("I");
		roleRepository.updateRoleStgDeactivate(roleDto);
	}

	@Override
	public List<CodeValueDTO> getRoleHierarchyList() {
		return roleRepository.getRoleHierarchyList(BaseCommonConstants.USER_TYPE_SUPER_ADMIN);
	}

	@Override
	public RoleDTO updateApproveOrRejectBulkRole(String roleIdList, String status, String remarks) throws SettleNxtException {

		String[] roleIdArr = roleIdList.split("\\|");
		RoleDTO roleDto = new RoleDTO();

		int[] values = Arrays.stream(roleIdArr).mapToInt(Integer::parseInt).toArray();
		List<Integer> list = Arrays.stream(values).boxed().collect(Collectors.toList());

		List<RoleDTO> roleIdarr = getBulkRoleStgInfo(list);

		Map<Integer, List<RoleDTO>> roleMap = roleIdarr.stream().collect(Collectors.groupingBy(RoleDTO::getRoleId));

		for (String roleId:roleIdArr) {

			try {

				List<RoleDTO> roledto = roleMap.get(Integer.parseInt(roleId));
				RoleDTO roleDTO = roledto.get(0);

				if (roleDTO == null) {
					throw new SettleNxtException("RoleId should not be empty", "");
				}
				if (roleDTO != null) {
					roleDTO.setRequestState(status);
					roleDTO.setCheckerComments(remarks);
					roleDTO.setLastUpdatedOn(new Date());
					roleDTO.setLastUpdatedBy(sessionDTO.getUserName());

					// Update work flow tables here
					if ("A".equals(status)) {
						roleDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
						roleDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
						roleDTO.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
						roleDTO.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
					} else {
						roleDTO.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
						roleDTO.setRequestState(CommonConstants.REQUEST_STATE_REJECTED);
					}

					if (roleDTO.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
						// To update final table
						updateApprovedRole(roleDTO);
					}
					roleRepository.updateRoleStgState(roleDTO.getLastUpdatedBy(), roleDTO.getLastUpdatedOn(),
							roleDTO.getLastOperation(), roleDTO.getRequestState(), roleDTO.getRoleId(), remarks);

				}

			} catch (Exception ex) {

				throw new SettleNxtException("Exception for RoleId" + roleId, "",ex);


			}
		}

		return roleDto;
	}

	private List<RoleDTO> getBulkRoleStgInfo(List<Integer> list) {

		return roleRepository.getBulkRoleStgInfoByRoleId(list);
	}

}
