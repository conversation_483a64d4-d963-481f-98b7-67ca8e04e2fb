2028=Cap amount exceeded
2029=Cap amount exceeded for additional component
2030=Service Fee For ATM Cash Advance
2031=Service Fee For Cash Advances
2032=Service Fee For Cheque Cashing
2033=Service Fee For Travellers Cheques
3201=Out of applicable Time Frame
3202=Invalid dispute reason code for given function code
3203=Action not allowed on account of business rule(s) violation
3204=Invalid life cycle for function code & originator
3205=Function code - reason code pair not exists for specified version
3206=No TAT defined for function code-reason code pair in specified version
3207=The TAT for the Dispute Cycle/Adjustment you trying to raise has expired
3208=Adjustment is not allowed for given MCC
3401=Action not allowed on account of business rule(s) violation
3402=Action not allowed on account of case already exists
3403=Action not allowed on account of compliance not met
3404=Invalid Data Input
3405=Re-login on account of Web Session Expired / Time out
3510=Unauthorized Signature
3511=Split Transaction
3512=Cardholder Letter Required for Legal Purposes
3513=Illegible fulfilment - Unable to provide legible Transaction supporting documents (TSD) copy
3514=Wrong Merchant establishment Category Code (MCC)
3515=Copy of Transaction supporting documents (TSD) for Legal Proceeding or Law Enforcement Investigation
3516=Improper return and submission of a Fees
4001=Time period for processing of authorization cancellation request is expired.
4002=Presentment is already processed
4003=Void is already processed
4004=Duplicate Transaction, Authorization cancellation request is already raised
4005=Reversal for successful auth is already processed.
4006=Auth Cancellation Request Can only be processed for successful auth
4051=Time period for processing of authorization cancellation acceptance is expired.
4052=Presentment is already processed
4053=Void is already processed
4054=Invalid transaction, No auth cancellation request against this message
4055=Duplicate Transaction, Authorization cancellation acceptance is already raised
4101=Presentment is already processed. Can't be raised for presented transactions
4102=Auth Cancellation acceptance is already processed.
4103=Can't be raised against unsuccessful authorization
4104=Time period for processing of void is expired
4105=Reversal for successful auth is already processed.
4106=No authorization present against presented void
4151=Void is already processed
4152=Auth Cancellation acceptance is already processed.
4153=Can't be raised against unsuccessful authorization
4154=Reversal for successful auth is already processed.
4155=No authorization present against given presentment
4156=Already presented. Duplicate presentment
4157=Cash Back amount exceeds the transaction amount - Valid for Cash Back Transactions
4201=TAT for processing the Offline Refund
4251=Reversal is already processed
4252=Chargeback is already processed
4253=Refund is already processed
4254=No presentment found for the given presentment reversal
4301=Full Chargeback is already processed
4302=Presentment reversal is already processed
4303=No presentment found for the given refund
4304=Duplicate transaction. Refund is already present with same ARD and Control Number
4351=Full Chargeback is already processed
4352=Presentment reversal is already processed
4353=No presentment found for the given refund
4354=Total Refund processed exceeds the original transaction amount
4355=Duplicate transaction. Refund is already present with same ARD and Control Number
4356=Refund amount exceeds the Original Amount - Chargeback processed amount
4401=TAT for processing the Offline Refund
4402=Duplicate Transaction. Transactions with same details are already present.
4451=Can only be processed against successful authorization and Debit adjustment
4452=Chargeback is already processed and Credit addjustment amount exceeds the (Auth settlement amount/Debit adj settlement amount - Chargeback Amount)
4453=Authorization reversal is present against the given Credit adjustment.
4457=Adjustment amount exceeds the settled auth record For MCC's other than Petrol, Railways & Hotels.
4459=Adjustment amount exceeds the settled debit adjustment record.
4504=No successful Auth record present for given adjustment.
5001=Invalid MTI
5002=MTI must be present in Header Message
5003=MTI must be numeric
5004=MTI must be of 4 digit
5005=Invalid Function Code
5006=Function code must be present in Header Message
5007=Function code must be numeric
5008=Function code must be of 3 digit
5009=Record Number not present in Header Message
5010=Record Number Must be 00000001
5011=Invalid format Date & Time, file generated.
5012=Date & Time , file generated must be present in Header Message
5013=Invalid Member Institution Id (PID).
5014=Member Institution ID must be present in Header Message
5015=Invalid product code in Header Message
5016=Product Code missing in the Header Message
5017=Incorrect Unique File Name
5018=Settlement BIN not Valid
5019=Settlement BIN must be present in Header message
5020=Invalid File Category.
5021=Version Number not Valid
5022=Version Number must be present in Header Message
5023=MTI must be present in Trailer Message
5024=Function code must be present in Trailer Message
5025=Incorrect Record Number
5026=File name not matched with Header message
5027=Transaction Count Not-Matched
5028=Transaction Count must be present
5029=Transaction Count must be numeric
5030=Transaction count must be of 8 digit
5031=Incorrect Run Total Amount
5032=Run total amount must be present
5033=Run total amount should be numeric
5034=MTI must be present in Transaction Message
5035=Record number must be present in Transaction Message
5036=Invalid Record Number
5037=Invalid format Date & Time, Local Transaction
5038=Value of "Date & Time, Local Transaction" must be present
5039=Invalid Primary Account Number
5040=Primary account Number must be present
5041=Primary account Number must be of numeric
5042=Invalid Acquirer Reference Data
5043=Acquirer Reference Data must be present
5044=Invalid Acquiring Institution ID Code
5045=Acquiring Institution ID Code must be present
5046=Invalid Approval Code
5047=Approval code must be present
5048=Invalid format Card Acceptor Terminal ID
5049=Card Acceptor Terminal ID must be present
5050=Amount, Transaction must be present
5051=Invalid format Amount, Transaction
5052=Invalid Currency Code, Transaction
5053=Currency Code, Transaction must be present
5054=Amounts, Additional must be numeric
5055=Amounts, Additional should not be present
5056=Invalid Amounts, Additional
5057=Transaction Originator Institution ID Code must be present
5058=Invalid Transaction Originator Institution ID Code
5059=Invalid Card Holder UID
5060=Invalid Card Holder Income Tax PAN
5061=Invalid Card Acceptor Additional Address
5062=Invalid Card Acceptor Zip Code
5063=Invalid Merchant Telephone number
5064=Control Number must be present
5065=Control Number not valid
5066=Processing Code not valid
5067=Processing Code must be present
5068=Processing Code should not be present
5069=Point of Service Entry Mode not valid
5070=Point of Service Condition Code not valid
5071=Invalid Point of Service Data Code
5072=Point of Service Data Code must be present
5073=Invalid Card Acceptor Business Code
5074=Card Acceptor Business Code must be present
5075=Invalid Action Code
5076=Action Code should not be present
5077=Invalid Product Code
5078=Product Code must be present
5079=Recurring Payment Indicator should not be present
5080=STIP indicator should not be present
5081=Loyalty Points should not be present
5082=ARQC Authorization Indicator (only for EMV STIP) should not be present
5083=ICS2 result code should not be present
5084=ICS CPS, CPS/ATM VC, CPS ATM ACI should not be present
5085=ICS transaction identifier should not be present
5086=Processing Code - From account should not be present
5087=Processing Code - To account should not be present
5088=Service Code (should not be part of any report) should not be present
5089=CVD2 match result should not be present
5090=CVD/iCVD match result code should not be present
5091=E-Commerce indicator should not be present
5092=ICS1 result code should not be present
5093=Transaction ID should not be present
5094=FEE , Amount (On-Line) should not be present
5095=Forwarding Institution Code should not be present
5096=Cross Border Flag (International Impact) should not be present
5097=Fraud Score should not be present
5098=International Authorization Network Identifier should not be present
5099=Network Data should not be present
5100=Auth Amount (International Impact) should not be present
5101=Auth Currency Code (International Impact) should not be present
5102=Auth Original Amount (International Impact) should not be present
5103=Auth Settlement Amount (International Impact) should not be present
5104=Auth Settlement Currency Code (International Impact) should not be present
5105=Auth Settlement Conversion Rate (International Impact) should not be present
5106=Auth Billing Amount (International Impact) should not be present
5107=Auth Billing Currency (International Impact) should not be present
5108=Auth Billing Conversion Rate (International Impact) should not be present
5109=Card Acceptor Name should be present
5110=Invalid Card Acceptor Name
5111=Card Acceptor Location/address must be present
5112=Invalid Card Acceptor City
5113=Card Acceptor State Name must be present
5114=Card Acceptor State Name should be proper format
5115=Card Acceptor Country Code must be present
5116=Card Acceptor Country Code not valid
5117=Invalid Message Reason Code
5118=Message Reason Code must be present
5119=Message Reason Code should not be present
5120=Invalid Internal Tracking Number
5121=Member Message Text must be present
5122=Member Message Text should not be present
5123=Invalid Member Message Text.
5124=Document Indicator not valid
5125=Document Indicator must be present
5126=Document Indicator should not be present
5127=Invalid Full/Partial Indicator
5128=Full/Partial Indicator must be present
5129=Full/Partial Indicator should not be present
5130=Date, Settlement format should not be present
5131=Settlement DR/CR Indicator should not be present
5132=Amount, Settlement should not be present
5133=Currency Code, Settlement should not be present
5134=Conversion Rate, Settlement should not be present
5135=Amount, Billing should not be present
5136=Conversion Rate, billing should not be present
5137=Currency Code, Billing should not be present
5138=Late Presentment Indicator should not be present
5139=RGCS Received date should not be present
5140=Original Settlement Date should not be present
5141=Verdict Favour/Against should not be present
5142=Fee Type Code should not be present
5143=Interchange Category should not be present
5144=Fee amount should not be present
5145=Fee DR/CR Indicator should not be present
5146=Fee Currency should not be present
5147=Invalid Format ICC system related data
5148=ICC system related data should not be present
5149=ICC system related data must be present
5150=Processing Status should not be present
5151=RGCS Record Reject Reason Code should not be present
5153=Invalid Actual File format
5162=Participant not valid for present Acq ID or PAN Bin
5163=Invalid XML Tag name or XML Tag name not configured
5164=Error in inserting data into system
5165=Invalid Date and Time, Transmission
5166=Card Acceptor ID Code must be present
5167=Invalid Card Acceptor Location/address
5168=Invalid Card Acceptor City
5169=Date and Time, Transmission must be present
5170=Point of Service Entry Mode must be present
5171=Case Number must be present
5172=FEE Amount must be present
5173=FEE Currency must be present
5174=FEE Type code must be present
5175=FEE DR/CR Indicator must be present
5176=Retrieval reference number must be present
5177=Service Code must be present
5178=Transaction Destination Code must be present
5200=Invalid date & Time, File generated
5201=Invalid Run Total, Amount
5202=Invalid Unique File Name
5203=Invalid Version No
5204=Invalid function code for the mentioned MTI
5205=Transaction Amount is not matching with previous lifecycle transaction amount
5206=Additional Amount is not matching with previous lifecycle additional amount
5207=Transaction Amount Partial is not matching with previous lifecycle transaction amount
5208=Additional Cash Amount disputed cannot be greater than the Total Additional Cash Amount
5209=Previous life cycle transaction is missing for given function code
5210=Duplicate transaction for current lifecycle
5211=Base function code is not present for current transaction
5212=Insufficient fund with acquirer for current acquirer initiated transaction
5213=Insufficient fund with issuer for current acquirer initiated transaction
5214=Insufficient fund with issuer for current issuer initiated transaction
5215=Insufficient fund with acquirer for current issuer initiated transaction
5216=Adjustment amount is greater than cap amount
5217=Purchase amount disputed cannot be greater than Total Purchase Amount
5218=Amount cannot be less than or equal to Zero
5219=Invalid Amount, Billing
5220=Invalid Amount, Settlement
5221=Invalid Case Number
5222=Invalid Currency Code, Billing
5223=Invalid Currency Code, Settlement
5224=Invalid Conversion Rate, billing
5225=Invalid Conversion Rate, Settlement
5226=Invalid Card Acceptor ID code
5227=Invalid Date, Settlement
5228=Invalid Fee Amount
5229=Invalid Fee Currency
5230=Invalid Fee DR/CR Indicator
5231=Invalid Fee Type Code
5232=Invalid Entire File Reject Indicator
5233=Invalid Interchange Category
5234=Invalid Late Presentment Indicator
5235=Invalid Original Settlement Date
5236=Invalid Processing Status
5237=Invalid Service Code
5238=Invalid Settlement DR/CR Indicator
5239=Invalid Recurring Payment Indicator
5240=Invalid STIP Flag
5241=Invalid Loyalty Points
5242=Invalid Authorization Indicator
5243=Invalid ICS2(UCAF) Result code
5244=Invalid ICS CPS,CPS/ATM VC,CPS/ATM ACI
5245=Invalid ICS Transaction Identifier
5246=Invalid CVV2 Match Result
5247=Invalid CVV/iCVV Match Result Code
5248=Invalid ECI Indicator
5249=Invalid ICS1 (CAVV) Result Code
5250=Invalid Forwarding Institution Code
5251=Invalid Cross Border Flag
5252=Invalid Fraud Score
5253=Invalid International Authorization Network Data
5254=Invalid Network Data
5255=Invalid Auth Amount
5256=Invalid Auth Currency code
5257=Invalid Auth Original Amount
5258=Invalid Auth Settlement Amount
5259=Invalid Auth Settlement currency code
5260=Invalid Auth Settlement Conversion Rate
5261=Invalid Auth Billing Amount
5262=Invalid Auth Billing Currency
5263=Invalid Auth Billing Conversion Rate
5264=Invalid RGCS Received date
5265=Invalid Verdict Favour/Against
5266=Invalid RGCS Record Reject Reason Code
5267=Invalid Replacement Amount
5268=Invalid Card Sequence Number
5269=Invalid Additional Response Data
5270=Invalid Image Code
5271=Invalid Personal Phrase
5272=Invalid Original Data element
5273=Invalid Account Identification
5274=Invalid Private Field
5275=Transaction Not matched.
5276=Settlement Bin In Header not matched with Transaction Settlement Bin
5277=Action not allowed on Un-Successful(Action Code Other than 00, 07, 08, 28, 31, 40, 41, 50, 89) transaction.
5278=Number of occurrence exceeds for the given cycle.
5279=Unable to find match using control number
5280=Duplicate refund chargeback for a given control number
5281=Amount cannot be exceeded than auth amount for Cash at POS
5282=Additional amount cannot be exceeded than auth cash amount for PWCB
5283=Transaction with case number not found.
5284=Verdict transaction for given case number not found.
5285=Verdict Transaction for given Case Number found with no favour.
5286=Reason code not supported for issuing favour verdict
5287=Reason code not supported for acquiring favour verdict
5288=Additional amount is allowed only for PWCB transactions
5289=Amount does not match with previous lifecycle transaction amount or allowable amount.
5290=Invalid Product code for the Record
5291=Action Code Must Be Present
5292=Point of Service Condition code must be present
5293=Card Acceptor Zip Code Must be present
5294=Merchant Telephone number must be present
5295=Invalid Fee Amount, limit exceeded
6023=Onus transaction not allowed
6024=Error in file format. Duplicate tag found.
6025=Unexpected tag found
6026=Duplicate record found
6027=No Match found for HOLD Transaction
6028=Not a onus transaction
6029=Compliance reject
6100=Invalid ARD / Acquirer Institution ID code
6101=Reserved For Future Use
6102=Reserved For Future Use
6103=Reserved For Future Use
6104=Reserved For Future Use
6105=Reserved For Future Use
6106=Reserved For Future Use
6107=Reserved For Future Use
6108=Reserved For Future Use
6109=Reserved For Future Use
6110=Reserved For Future Use
6111=Reserved For Future Use
6112=Reserved For Future Use
6113=Reserved For Future Use
6114=Reserved For Future Use
6115=Reserved For Future Use
6116=Reserved For Future Use
6117=Reserved For Future Use
6118=Reserved For Future Use
6119=Reserved For Future Use
6120=Reserved For Future Use
6121=Reserved For Future Use
6122=Reserved For Future Use
6123=Reserved For Future Use
6124=Reserved For Future Use
6125=Reserved For Future Use
6126=Reserved For Future Use
6127=Reserved For Future Use
6128=Reserved For Future Use
6129=Reserved For Future Use
6130=Reserved For Future Use
6131=Reserved For Future Use
6132=Reserved For Future Use
6133=Reserved For Future Use
6134=Reserved For Future Use
6135=Reserved For Future Use
6136=Reserved For Future Use
6137=Reserved For Future Use
6138=Reserved For Future Use
6139=Reserved For Future Use
6140=Reserved For Future Use
6141=Reserved For Future Use
6142=Reserved For Future Use
6143=Reserved For Future Use
6144=Reserved For Future Use
6145=Reserved For Future Use
6146=Reserved For Future Use
6147=Reserved For Future Use
6148=Reserved For Future Use
6149=Reserved For Future Use
6150=Reserved For Future Use
7001=Invalid MTI
7002=Invalid Function Code
7003=BIN not allowed for Offline Txn
7004=Invalid BIN
7005=Invalid Acquirer Id
7006=Invalid currency code
7007=Invalid Originator Inst Code
7008=Invalid Processing code
7009=Invalid POS Entry Mode -SF1
7010=Invalid POS Entry Mode -SF2
7011=Invalid PoS Condition Code
7012=Invalid POS Data Code -SF1
7013=Invalid POS Data Code -SF2
7014=Invalid POS Data Code -SF3
7015=Invalid POS Data Code -SF4
7016=Invalid POS Data Code -SF5
7017=Invalid POS Data Code -SF6
7018=Invalid POS Data Code -SF7
7019=Invalid POS Data Code -SF8
7020=Invalid POS Data Code -SF9
7021=Invalid POS Data Code -SF10
7022=Invalid POS Data Code -SF11
7023=Invalid POS Data Code -SF12
7024=Invalid Zip Code
7025=Invalid Card Acceptor Business Code
7026=Invalid Service Code
7027=Invalid Action Code
7028=Invalid State Name
7029=Invalid Country Code
7030=EMV Tag must be Present
7031=Compliance Rejected due to Transaction Amount
7032=Compliance Rejected due to Cash Amount
7033=Compliance Rejected due to ECI Indicator
7034=Compliance Rejected due to Origination ID
7035=Compliance Rejected due to STIP Indicator
7036=Compliance Rejected due to Bin Card Brand
7037=Compliance Rejected due to Bin Message System Id
7038=Compliance Rejected due to ServiceCode1ID
7039=Compliance Rejected due to ServiceCode2ID
7040=Compliance Rejected due to ServiceCode3ID
7041=Compliance Rejected due to Bin Deletion
7042=Invalid Product-Message System-Function Code-POS Entry Mode Combination
7043=Compliance Rejected due to Additional Amount
9202=Invalid Header
9203=Invalid Footer
9204=Invalid Transaction Destination Code
9205=Duplicate Entry for Refund Matching
9999=Invalid Transaction Format