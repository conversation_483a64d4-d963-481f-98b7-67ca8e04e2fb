package org.npci.settlenxt.adminportal.dto;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import org.npci.settlenxt.portal.common.dto.BaseDTO;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@SuppressWarnings("unused")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JGlobalMap
public class BatchUploadDTO extends BaseDTO implements Serializable {
	private int batchId;
	private int batchRunId;
	private int memberId;
	private String fileName;
	private LocalDateTime reqDate;
	private LocalDateTime procStartDate;
	private LocalDateTime procEndDate;
	private int totalRecs;
	private int successRecs;
	private int failedRecs;
	private String fileUplThrough;
	private String ackStatus;
	private String ackBatchrunId;
	private String statusCode;
	private String status;
	private String statusDesc;
	private String threadName;
	private String batchProcessType;
	private String outputFileName;
	private String clearBatchId;
	private Date fromDate;
	private Date toDate;
	private String pageType;

}
