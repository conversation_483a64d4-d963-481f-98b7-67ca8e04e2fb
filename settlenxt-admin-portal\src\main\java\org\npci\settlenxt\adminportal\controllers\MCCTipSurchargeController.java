package org.npci.settlenxt.adminportal.controllers;

import java.util.List;
import java.util.Locale;

import javax.servlet.http.HttpServletRequest;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.MCCTipSurchargeDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.CodeValueService;
import org.npci.settlenxt.adminportal.service.MCCTipSurchargeService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

//@Slf4j
@Controller
public class MCCTipSurchargeController extends BaseController {

	private static final String SHOW_MCC_TIP_SURCHARGE = "showMccTipSurcharge";
	private static final String VIEW_MCC_TIP_SURCHARGE = "viewMccTipSurcharge";
	private static final String VIEW_APPROVE_MCC_TIP_SURCHARGE = "viewApproveMccTipSurcharge";
	private static final String ADD_EDIT_MCC_TIP_SURCHARGE = "addEditMccTipSurcharge";
	private static final String SHOW_CHECK_BOX = "showCheckBox";
	private static final String TIP_SURCHARGE_NAME_LIST = "tipSurchargeNameList";
	private static final String MCC_LIST = "mccList";
	// @SuppressWarnings({ "unchecked", "unused" })
	@Autowired
	private MessageSource messageSource;
	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	private MCCTipSurchargeService mccTipSurchargeService;
	@Autowired
	private CodeValueService codeValueService;

	// show Main tab of TipSurcharge
	@PostMapping("/mccTipSurchargeMain")
	@PreAuthorize("hasAuthority('View MCC Tip Surcharge')")
	public String fetchApprovedMccTipSurchargeList(Model model) {
		try {
			List<MCCTipSurchargeDTO> mccTipSurchargeList = mccTipSurchargeService.getTipSurchargeList();
			model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.TRANSACT_YES);//
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_LIST, mccTipSurchargeList);
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.ADD_MCC_TIP_SURCHARGE, CommonConstants.TRANSACT_YES);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, SHOW_MCC_TIP_SURCHARGE, ex);
		}
		return getView(model, SHOW_MCC_TIP_SURCHARGE);
	}

	// show Approval TAB of TipSurcharge
	@PostMapping("/mccTipSurchargeForApproval")
	@PreAuthorize("hasAuthority('View MCC Tip Surcharge')")
	public String mccTipSurchargeForApproval(Model model) {
		try {
			List<MCCTipSurchargeDTO> pendingMccTipSurchargeList = mccTipSurchargeService.getPendingTipSurcharge();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_PENDING_LIST, pendingMccTipSurchargeList);
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_APP_PENDING, CommonConstants.TRANSACT_YES);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_MCC_TIP_SURCHARGE, ex);
		}
		return getView(model, SHOW_MCC_TIP_SURCHARGE);
	}

	// view TipSurcharge in Main tab
	@PostMapping("/getMccTipSurcharge")
	@PreAuthorize("hasAuthority('View MCC Tip Surcharge')")
	public String getMccTipSurcharge(@RequestParam("mccTipSurchargeId") String mccTipSurchargeId, Model model) {
		MCCTipSurchargeDTO mccTipSurchargeDto = new MCCTipSurchargeDTO();
		try {
			mccTipSurchargeDto = mccTipSurchargeService.getMccTipSurchargeMainInfo(Integer.valueOf(mccTipSurchargeId));
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mccTipSurchargeDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_MCC_TIP_SURCHARGE, ex);
		}
		return getView(model, VIEW_MCC_TIP_SURCHARGE);
	}

	// view TipSurcharge in Approval tab
	@PostMapping("/getPendingMccTipSurcharge")
	@PreAuthorize("hasAuthority('View MCC Tip Surcharge')")
	public String getPendingMccTipSurcharge(@RequestParam("mccTipSurchargeId") String mccTipSurchargeId, Model model,
			HttpServletRequest request) {
		MCCTipSurchargeDTO mccTipSurchargeDto = new MCCTipSurchargeDTO();
		try {
			mccTipSurchargeDto = mccTipSurchargeService.getMccTipSurchargeStgInfo(mccTipSurchargeId);
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mccTipSurchargeDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_MCC_TIP_SURCHARGE, ex);
		}
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.DISCARD_MCC_TIP_SURCHARGE);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_MCC_TIP_SURCHARGE);
		return getView(model, VIEW_APPROVE_MCC_TIP_SURCHARGE);
	}

	// add TipSurcharge
	@PostMapping("/mccTipSurchargeCreation")
	@PreAuthorize("hasAuthority('Add MCC Tip Surcharge')")
	public String mccTipSurchargeCreation(Model model) {
		MCCTipSurchargeDTO mccTipSurchargeDto = new MCCTipSurchargeDTO();
		List<CodeValueDTO> tipSurchargeNameList = codeValueService.getTipSurchargeNameList();
		List<CodeValueDTO> mccList = codeValueService.getMccNameList();
		model.addAttribute(TIP_SURCHARGE_NAME_LIST, tipSurchargeNameList);
		model.addAttribute(MCC_LIST, mccList);
		model.addAttribute(CommonConstants.ADD_MCC_TIP_SURCHARGE, CommonConstants.ADD_MCC_TIP_SURCHARGE);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_MCC_TIP_SURCHARGE);
		model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mccTipSurchargeDto);
		return getView(model, ADD_EDIT_MCC_TIP_SURCHARGE);
	}

	// To save given input
	@PostMapping("/addMccTipSurcharge")
	@PreAuthorize("hasAuthority('Add MCC Tip Surcharge')")
	public String addMccTipSurcharge(
			@ModelAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO) MCCTipSurchargeDTO mccTipSurchargeDto, Model model) {
		mccTipSurchargeDto.setAddEditFlag(CommonConstants.ADD_MCC_TIP_SURCHARGE);
		try {
			List<CodeValueDTO> tipSurchargeNameList = codeValueService.getTipSurchargeNameList();
			List<CodeValueDTO> mccList = codeValueService.getMccNameList();
			model.addAttribute(TIP_SURCHARGE_NAME_LIST, tipSurchargeNameList);
			model.addAttribute(MCC_LIST, mccList);
			mccTipSurchargeDto = mccTipSurchargeService.addEditMccTipSurcharge(mccTipSurchargeDto);
			model.addAttribute(CommonConstants.ADD_MCC_TIP_SURCHARGE, CommonConstants.ADD_MCC_TIP_SURCHARGE);
		} catch (Exception ex) {
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mccTipSurchargeDto);
			model.addAttribute(CommonConstants.ADD_MCC_TIP_SURCHARGE, CommonConstants.ADD_MCC_TIP_SURCHARGE);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_MCC_TIP_SURCHARGE);
			return handleErrorCodeAndForward(model, ADD_EDIT_MCC_TIP_SURCHARGE, ex);
		}
		model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mccTipSurchargeDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mccTipSurcharge.addSuccess.msg"));
		return getView(model, ADD_EDIT_MCC_TIP_SURCHARGE);
	}

	// To Edit TipSurcharge
	@PostMapping("/editMccTipSurcharge")
	@PreAuthorize("hasAuthority('Edit MCC Tip Surcharge')")
	public String editMccTipSurcharge(@RequestParam("mccTipSurchargeId") String mccTipSurchargeId, Model model,
			@RequestParam("parentPage") String parentPage) {
		MCCTipSurchargeDTO mccTipSurchargeDto = new MCCTipSurchargeDTO();

		try {
			List<CodeValueDTO> tipSurchargeNameList = codeValueService.getTipSurchargeNameList();
			List<CodeValueDTO> mccList = codeValueService.getMccNameList();
			model.addAttribute(TIP_SURCHARGE_NAME_LIST, tipSurchargeNameList);
			model.addAttribute(MCC_LIST, mccList);
			mccTipSurchargeDto = mccTipSurchargeService.getMccTipSurchargeForEdit(Integer.valueOf(mccTipSurchargeId));
			mccTipSurchargeDto.setAddEditFlag(CommonConstants.EDIT_MCC_TIP_SURCHARGE);
			model.addAttribute(CommonConstants.EDIT_MCC_TIP_SURCHARGE, CommonConstants.EDIT_MCC_TIP_SURCHARGE);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_MCC_TIP_SURCHARGE);
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mccTipSurchargeDto);
			model.addAttribute(CommonConstants.PARENT_PAGE, parentPage);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_MCC_TIP_SURCHARGE, ex);
		}

		model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mccTipSurchargeDto);
		return getView(model, ADD_EDIT_MCC_TIP_SURCHARGE);
	}

	// To save edited input
	@PostMapping("/updateMccTipSurcharge")
	@PreAuthorize("hasAuthority('Edit MCC Tip Surcharge')")
	public String updateMccTipSurcharge(@ModelAttribute("mccTipSurchargeDto") MCCTipSurchargeDTO mccTipSurchargeDto,
			BindingResult result, Model model, HttpServletRequest request, Locale locale,
			@RequestParam("parentPage") String parentPage) {
		MCCTipSurchargeDTO mcclocal;
		try {
			List<CodeValueDTO> tipSurchargeNameList = codeValueService.getTipSurchargeNameList();
			List<CodeValueDTO> mccList = codeValueService.getMccNameList();
			model.addAttribute(TIP_SURCHARGE_NAME_LIST, tipSurchargeNameList);
			model.addAttribute(MCC_LIST, mccList);
			mccTipSurchargeDto.setAddEditFlag(CommonConstants.EDIT_MCC_TIP_SURCHARGE);
			mccTipSurchargeService.addEditMccTipSurcharge(mccTipSurchargeDto);
			mcclocal = mccTipSurchargeService.getMccTipSurchargeStg(mccTipSurchargeDto.getMccTipSurchargeId());

			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("mccTipSurcharge.updateSuccess.msg", null, locale));
		} catch (Exception ex) {
			mcclocal = mccTipSurchargeDto;
			handleErrorCodeAndForward(model, ADD_EDIT_MCC_TIP_SURCHARGE, ex);
		}
		model.addAttribute(CommonConstants.EDIT_MCC_TIP_SURCHARGE, CommonConstants.EDIT_MCC_TIP_SURCHARGE);
		model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mcclocal);
		model.addAttribute(CommonConstants.PARENT_PAGE, parentPage);
		return getView(model, ADD_EDIT_MCC_TIP_SURCHARGE);
	}

	// For Checker Approval
	@PostMapping("/approveMccTipSurcharge")
	@PreAuthorize("hasAuthority('Approve MCC Tip Surcharge')")
	public String approveMccTipSurcharge(@RequestParam("mccTipSurchargeId") String mccTipSurchargeId,
			@RequestParam("status") String status, @RequestParam("remarks") String remarks, Model model) {
		MCCTipSurchargeDTO mccTipSurchargeDto = new MCCTipSurchargeDTO();
		try {
			mccTipSurchargeDto = mccTipSurchargeService
					.approveOrRejectMccTipSurcharge(Integer.valueOf(mccTipSurchargeId), status, remarks);
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mccTipSurchargeDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_MCC_TIP_SURCHARGE, ex);
		}
		model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mccTipSurchargeDto);
		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {

			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("mccTipSurcharge.approvalSuccess.msg"));
		} else if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("mccTipSurcharge.rejectionSuccess.msg"));
		}

		return getView(model, VIEW_APPROVE_MCC_TIP_SURCHARGE);
	}

	// For Checker Approval
	@PostMapping("/approveMccTipSurchargeBulk")
	@PreAuthorize("hasAuthority('Approve MCC Tip Surcharge')")
	public String approveMccTipSurchargeBulk(
			@RequestParam("bulkApprovalReferenceNoList") String bulkApprovalReferenceNoList,
			@RequestParam("status") String status, Model model) {
		String successStatus = "";
		try {
			String remarks = "";
			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}
			successStatus = mccTipSurchargeService.approveOrRejectMccTipSurchargeBulk(bulkApprovalReferenceNoList,
					status, remarks);
			List<MCCTipSurchargeDTO> pendingMccTipSurchargeList = mccTipSurchargeService.getPendingTipSurcharge();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_PENDING_LIST, pendingMccTipSurchargeList);
			model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_APP_PENDING, CommonConstants.TRANSACT_YES);
			if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && "A".equalsIgnoreCase(status)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mccTipSurcharge.approvalSuccess.msg"));
			} else if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && "R".equalsIgnoreCase(status)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mccTipSurcharge.rejectionSuccess.msg"));
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_MCC_TIP_SURCHARGE, ex);
		}
		return getView(model, SHOW_MCC_TIP_SURCHARGE);
	}

	// For Discard
	@PostMapping("/discardMccTipSurcharge")
	@PreAuthorize("hasAuthority('Edit MCC Tip Surcharge')")
	public String discardMccTipSurcharge(@RequestParam("mccTipSurchargeId") String mccTipSurchargeId, Model model) {
		MCCTipSurchargeDTO mccTipSurchargeDto = new MCCTipSurchargeDTO();
		try {
			mccTipSurchargeDto = mccTipSurchargeService.discardMccTipSurcharge(Integer.valueOf(mccTipSurchargeId));
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_MCC_TIP_SURCHARGE, ex);
		}
		model.addAttribute(CommonConstants.MCC_TIP_SURCHARGE_DTO, mccTipSurchargeDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mccTipSurcharge.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_MCC_TIP_SURCHARGE);
	}

}
