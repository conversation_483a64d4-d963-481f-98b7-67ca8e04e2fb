<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />

<script type="text/javascript"
	src="./static/js/bootstrap-datepicker.min.js"></script>		
<link rel="stylesheet" href="./static/css/bootstrap-datepicker3.css"
	type="text/css" />

<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>

<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<script type="text/javascript"
	src="./static/js/validation/moveReportMainPage.js"></script>

	<script type="text/javascript"> 
		var actionColumnIndex=5;	
		</script>

<style>
	.defaultexport {
		visibility: hidden;
	}
	
	table.dataTable thead {
		vertical-align: top;
	}
	
	table.dataTable thead .sorting {
		vertical-align: top;
		background: url('./static/images/sort_both.png') no-repeat center right;
	}
	
	table.dataTable thead .sorting_asc {
		vertical-align: top;
		background: url('./static/images/sort_asc.png') no-repeat center right;
	}
	
	table.dataTable thead .sorting_desc {
		vertical-align: top;
		background: url('./static/images/sort_desc.png') no-repeat center right;
	}
	
	table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
		{
		vertical-align: top;
		content: ""
	}
	
	table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
		{
		vertical-align: top;
		content: ""
	}
	
	.search-box {
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		background-color: transparent;
		width: 100%;
		border-width: 1px;
		border-style: inset;
	}
	</style>
	

<div class="container-fluid height-min">
	<div class="row">
		<div class="body-content">

			<div class="card">
				<div class="card-header">
					<div class="card-title">
						Report Search
						<sec:authorize access="hasAuthority('Add Reports Movement and Signing')">
						<a class="btn btn-success pull-right btn_align"
							href="#" onclick="addReport();" style="margin-top: -10px;"><em
							class="glyphicon-plus"></em> <spring:message
								code="moveReport.addBtn" /> </a>
								</sec:authorize>
					</div>
				</div>
				<div class="card-body">
					<form:form onsubmit="encodeForm(this);"
						enctype="multipart/form-data" modelAttribute="MoveReportDto"
						method="POST" autocomplete="off">
						<div class="row">
							<div class="col-md-3">
								<div class="form-group">
									<label for=""><spring:message
											code="moveReport.fromDate" /> <span class="red">*</span></label>

									<form:input path="fromDateStr" id="fromDateStr"
										name="fromDateStr" cssClass="form-control" readonly="true"
										onkeyup="validateFromDate('errFromDate')"
										data-date-format="yyyy-mm-dd" />

									<div id="errFromDate" class="error">
										<span for=fromDateStr class="error"><form:errors
												path="fromDateStr" /></span>
									</div>
								</div>
							</div>
							<div class="col-md-3">
								<div class="form-group">
									<label for=""><spring:message code="moveReport.toDate" />
										<span class="red">*</span></label>
									<form:input path="toDateStr" id="toDateStr" name="toDateStr"
										onkeyup="validateToDate('errtoDate')" cssClass="form-control"
										readonly="true" data-date-format="yyyy-mm-dd" />
									<div id="errtoDate" class="error">
										<span for=toDateStr class="error"><form:errors
												path="toDateStr" /></span>
									</div>
								</div>
							</div>
							<div id="divnetwork" class="col-md-3">

								<div class="form-group">
									<label for=""><strong><spring:message
												code="moveReport.reportNetwork"></spring:message></strong> </label>
									<form:select path="reportNetwork" id="reportNetwork"
										class="form-control">
										<form:option value="0">
											<spring:message code="msg.lbl.select"></spring:message>
										</form:option>
										<form:options itemLabel="description" itemValue="code"
											items="${reportNetwork}" />
									</form:select>
									 <div id="errreportNetwork">
									<span for="reportNetwork" class="error"><form:errors
											path="reportNetwork" /> </span>
								</div>
								</div>
							</div>
						</div>
					</form:form>
				</div>


				<div class="card-action">
					<button type="button" class="btn btn-primary" id="searchBtn">
						<spring:message code="moveReport.searchBtn" />
					</button>
					<input type="button"
			onclick="clearFields()" class="btn btn-success" id="clearButton"
			value="<spring:message code="moveReport.clearBtn" />" />
					
				</div>



			</div>


			<div class="col-sm-12">
				<div class="card-white">

					<div class="panel panel-default">
						<div class="table-responsive">
							<table id="tabnew"
								class="table table-striped dataTable table-bordered order"
								>
								 <caption style="display:none;">Move Report MainPage</caption>
								<thead>
									<tr>
										<th scope="col"><spring:message code="moveReport.requestId" /></th>
										<th scope="col"><spring:message code="moveReport.reportType" /></th>
										<th scope="col"><spring:message code="moveReport.reportNetwork" /></th>
										<th scope="col"><spring:message code="moveReport.requestTime" /></th>
										<th scope="col"><spring:message code="moveReport.status"/></th>
									</tr>
								</thead>
								<tbody>
									<c:forEach var="report" items="${reportList}">
										<tr>
											<td>${report.requestId}</td>
											<td>${report.reportType}</td>
											<td>${report.reportNetwork}</td>
											<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
												value="${report.requestTime}" /></td>
											<td><c:if test="${report.status eq 'A' }">ACTIVE</c:if>
											<c:if test="${report.status eq 'C'}">COMPLETED</c:if></td>
										</tr>
									</c:forEach>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>