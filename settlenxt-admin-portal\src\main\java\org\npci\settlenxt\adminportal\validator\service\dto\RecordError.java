package org.npci.settlenxt.adminportal.validator.service.dto;

import lombok.Data;

@Data
public class RecordError {
	private Integer errorNo;
	private String errorDescription;
	private String name;
	private String value;
	private String xml;
	

	public RecordError(Integer errorNo, String errorDescription, String name,
			String value, String xml) {
		super();
		this.errorNo = errorNo;
		this.errorDescription = errorDescription;
		this.name = name;
		this.value = value;
		this.xml = xml.replace("&amp;", "&");
	}
}
