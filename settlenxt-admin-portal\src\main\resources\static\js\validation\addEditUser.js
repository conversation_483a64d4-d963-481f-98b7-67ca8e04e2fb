$(document).ready(function() {




    $('#accessLevel').on('keyup keypress blur change',function () {
        validateFromCommonVal('accessLevel', true, "SelectionBox", "", false); 
    });
    
    $('#salutation').on('keyup keypress blur change',function () {
        validateFromCommonVal('salutation', true, "SelectionBox", "", false); 
    });
    
    $('#firstName').on('keyup keypress blur change',function () {
        validateFromCommonVal('firstName', true, "UserNameRegex", 50, false); 
    });
    
    $('#middleName').on('keyup keypress blur change',function () {
        validateFromCommonVal('middleName', false, "UserNameRegex", 50, false); 
    });  
    
    $('#lastName').on('keyup keypress blur change',function () {
        validateFromCommonVal('lastName', false, "UserNameRegex", 50, false); 
    });
    
    $('#emailId').on('keyup keypress blur change',function () {
        validateFromCommonVal('emailId', true, "MailValidation", 100, false); 
    }); 
    

     $('#stateId').on('keyup keypress blur change',function () {
        validateFromCommonVal('stateId', false, "SelectionBox", "", false); 
    }); 
    
     $('#cityId').on('keyup keypress blur change',function () {
        validateFromCommonVal('cityId', false, "SelectionBox", "", false); 
    });   
    
    
    $('#pincode').on('keyup keypress blur change',function () {
        validateFromCommonVal('pincode', false, "NumericsOnly", 6 , true); 
    });
    
    $('#status').on('keyup keypress blur change',function () {
        validateFromCommonVal('status', false, "SelectionBox", "" , false); 
    }); 
    
    $('#makerChecker').on('keyup keypress blur change',function () {
        validateFromCommonVal('makerChecker', true, "SelectionBox", "" , false); 
    }); 
    
     $('#mobileNo').on('keyup keypress blur change',function () {
       validateMobileNo('errmobileNo');
    }); 
    
     $('#contactNo').on('keyup keypress blur change',function () {
         validateContactNo('errcontactNo');
    }); 
      
     $('#empId').on('keyup keypress blur change',function () {
     validateFromCommonVal('empId', false, "empIdRegex", 10, false);
   });

     

$("#clear").click(function() {


  $(".error").each(function() {
		if($(this).text().trim().length > 0) 
		{
			
			$(this).empty();
		
		}
		}); 


	document.getElementById("loginId").value = "";
	if(!( document.getElementById("userType").value)==4){
     document.getElementById("participantName").value ="SELECT";}

     document.getElementById("empId").value = "";
     document.getElementById("salutation").value = "SELECT";
     document.getElementById("firstName").value = "";
     document.getElementById("middleName").value = "";
     document.getElementById("lastName").value = "";
     
     document.getElementById("emailId").value = "";
     document.getElementById("mobileNo").value = "";
     document.getElementById("streetAddress").value = "";
     document.getElementById("contactNo").value = "";
     document.getElementById("stateId").value = "0";
     document.getElementById("cityId").value = "0";
     document.getElementById("pincode").value = "";
     document.getElementById("makerChecker").value="SELECT";
    
     $("#binListClass").empty();
 	$("#binListClass").multiselect("rebuild");
 	$("#binListClass").multiselect("refresh");
    
 if (document.getElementById("userType").value=='3'){
     document.getElementById("accessLevel").value="0";
     document.getElementById("prefix").value="";
     }
 
  
    
    

	 var selectedArray = document.getElementsByClassName("selectedRoles");
	
if (selectedArray.length > 0) {
	for(let i of selectedArray){
			var id =i.id.replace(
				"option", '').replace("remove", "");
			var roleNameData = i.innerText;
			roleNameData = $('<div>').text(roleNameData).html();
			var roleName = "'" + roleNameData + "'";
			roleName = $('<div>').text(roleName).html();

			$('#optionList')
				.append(
					'<tr class="optionRoles" id="option'
					+ id
					+ '"><td>'
					+ roleNameData
					+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right" onclick="addToAssignedList('
					+ id + ',' + roleName + ')"></td></tr>');
		}
		$('#assignedList').empty();
		  $("#binListClass").next().hide();
			$("#mywrapper").hide();
			$("#binListClasss").empty();
			  $("#binListClasss").next().hide();
	}

	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');
	
	$('#optionList').empty();
     

         
	document.getElementById("participantName").value ="SELECT";
     
	$("#mywrapperBinLable").hide();

 
});

	 $('#binFlagss').hide();
	 if($('#accessLevel').val()=='P'){
		 $("#binListClass").next().hide();
			$("#binListClasss").next().hide();
	 	$("#mywrappers").hide();
	 	$("#mywrapperBinLable").hide();}
	 else if($('#accessLevel').val()=='B'){
		 $("#mywrapperBinLable").show();
	 }

$('form')
    .each(function(){
        $(this).data('serialized', $(this).serialize())
    })
    .on('change input', function(){
        $(this)             
            .find('input:submit, button:submit')
                .prop('disabled', $(this).serialize() == $(this).data('serialized'))
        ;
        $('#forms').prop('disabled', false);
       
     })
$('#forms').prop('disabled', true);
$('#tabnew').on('click', function(){
   $('#forms').prop('disabled', false);
});

$('#tabnew1').on('click', function(){
   $('#forms').prop('disabled', false);
});

	if ($('#loginId').val() !== "" && $('#loginId').val() !== null && $('#error').val() == null) {
		if (!document.getElementById("loginId").readOnly) {
			$("input").prop('disabled', true);
			$("select").prop('disabled', true);
			
				$('#prefix').hide();
		
		} 

		}

	
	if ($('#success').val() == 'successStatus') {
		$("input").prop('disabled', true);
		$("#mywrappers").hide();
		$("select").prop('disabled', true);

	}
	if (($('#showButton').val()!="yes")) {
		$("#mywrappers").hide();
		$("input").prop('disabled', true);
		$("select").prop('disabled', true);
		$('#firstName').attr('readonly', true);
		$("#binListClass").next().hide();
		$("#binListClasss").next().hide();
		}
	
	$('#addSingle').click(function() {
		return !$('#source option:selected').remove().appendTo('#destination');
}	);

	$("form :input").change(function() {

		$(this).closest('form').data('changed', true);

	});
	
	
	var allSelected=true;
      $('#binListClass').multiselect({
		buttonWidth : '160px',
		includeSelectAllOption: true,
		nonSelectedText:'Select Bins',
		

		  onSelectAll: function(){
			  if(allSelected){
			     allSelected = false;
			  
			     $("#errbinId").hide();
					$("#errbinId").find('.error').html('');
			  
			  }
			  
			  else{
				  
				  document.getElementById("binFlagss").value = "";
				  allSelected = true;
				  
					
			  }
			  },

	
	
		onChange : function(selected) {
			
			if (selected) {
		
				
				$("#errbinId").hide();
				$("#errbinId").find('.error').html('');

								 
			} 
		},

	}); 
	
    

	 $('#binListClasss').multiselect({
		buttonWidth : '160px',
		includeSelectAllOption: true,
		nonSelectedText:'Select Bins',
		  onSelectAll: function(){
			  if(allSelected){
			     allSelected = false;
			     $("#errbinId").hide();
					$("#errbinId").find('.error').html('');
			  
			  }
			  
			  else{
			
				  document.getElementById("binFlagss").value = "";
				  allSelected = true;
				  $("#errbinId").hide();
					$("#errbinId").find('.error').html('');
					
			  }
			  },

	
	
		

	}); 
	


	$("#errLvType").hide();

	$('#map').hide();
	
	 
		 
		 $('#accessLevel')
			.change(
				function() {
					 $("#mywrapperBinLable").show();
					 if($('#accessLevel').val()=='P'){
						 $("#binListClass").next().hide();
							$("#binListClasss").next().hide();
					 	$("#mywrappers").hide();
					 	$("#mywrapperBinLable").hide();}
					 else if($('#accessLevel').val()=='B'){
						 changeFunc("1");
						 $("#mywrapperBinLable").show();
					 }
					
					
					
					
				});

	$('#vEntityType').on('change', function() {
		$("#errLvType").text('');
		$("#errLvType").hide();
		var array = $('#assignedList').find('tr');
let i=0;
		for (i of array) {
		var td = i;
			td.children[1].children[0].click();
		}
	

	});
	

   $('#loginId').on('keyup keypress blur change',function () {
 if (!validateLoginId('errLoginId'))
{
 console.log("");
}else{
checkDuplicateLoginId();  }
    
});
    
$('#participantName').blur(function() {  

 if (!validateParticipantName('errParticipant')){
 console.log("");
 }	 
  else{
checkMaxUserBasedParticipant();}
		
});

function checkMaxUserBasedParticipant() {
	var validParticipantName = false;
	var participantName =  $('#participantName').val();
	
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		if (participantName != "") {

			$.ajax({
				url : "checkMaxUserIdForParticipant",
				type : "POST",
				dataType : "json",
				data: {"participantName" : escape(participantName),
					    "_TransactToken" : tokenValue},
				success : function(response){

					if (response.status == "BSUC_0001") {
						validParticipantName = true;
					
							$('#errParticipant2').find('.error').html('User creation Limit for selected Participant Id has exceeded the Limit');
					 $('#errParticipant2').show();
						
						$('#participantName').focus();
						

					} else {
						validParticipantName = false;
				
					$('#errParticipant2').find('.error').html('');
					 $('#errParticipant2').hide();
					}
				},
				error : function() {
			
					$('#errParticipant2').find('.error').html('');
					 $('#errParticipant2').hide();
				}
			});

		} else {
			validParticipantName = false;
			
			
		}
		return validParticipantName;
	}



	



	$('#stateId')
		.change(
			function() {

				if ($('#stateId').val() != '0') {
					var stateId = $("#stateId").val();
					var tokenValue = document.getElementsByName("_TransactToken")[0].value;
					$
						.ajax({
							url: "getUserCityMaster",
							type: "POST",
							data: {
								stateId: stateId,
								"_TransactToken": tokenValue
							},
							dataType: "json",
							success: function(data) {
								$("#cityId").empty();
								var len = data.length;
								$("#cityId")
									.append(
										'<option value="0">--Select--</option>');
								for (var i = 0; i < len; i++) {
									var id = data[i].lkpValue;
									var name = data[i].lkpDesc;

									$("#cityId").append("<option value='" + id + "'>" + name + "</option>");

								}
							}
						});

				} else {
					$("#cityId").empty();
					$("#cityId")
					.append(
							'<option value="0">--Select--</option>');
					
				}

			});
			
								$('#participantName').change(
							function(){
								
								$("#mywrapperBinLable").hide();
								$("#binListClass").next().hide();
								var y = document.getElementById("accessLevel");
							    y.value = '0';
							    
							    	var i = document.getElementById("participantName").value.substring(0,4);
								$('#prefix').val(i);
								
								
							});
			

});

window.history.forward();
function noBack() {
	window.history.forward();
}
function userAction(_type, action) {

	var uid = document.getElementById("userId").value;
	var data = "uid," + uid  + ",status,"
		+ status + ",userType," + $('#userType').val();
	postData(action, data);
}


function checkDuplicateLoginId() {
	var loginId = $('#prefix').val() + $('#loginId').val();
	var validloginId = false;
	
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	
	if (loginId != "") {

		$.ajax({
			url: "checkDuplicateLoginId",
			type: "POST",
			dataType: "json",
			data: {
				"loginId": loginId,
				"_TransactToken": tokenValue
			},
			success: function(response) {

				if (response.status == "BSUC_0001") {
					validloginId = true;
					
				$('#errloginId2').find('.error').html('Login Id already Exists');
				 $('#errloginId2').show();
				 callBackDupLogin(true);


				} else {
					validloginId = false;
					$('#errloginId2').find('.error').html('');
					 $('#errloginId2').hide();
					 callBackDupLogin(false);
				}
			},
			error: function() {
				$('#errloginId2').find('.error').html('');
				 $('#errloginId2').hide();
				 callBackDupLogin(false);
			}
		});

	} else {
		validloginId = false;
	}
	return validloginId;
}

function validateLoginId(_msgID) {

		if (parseInt($('#userId').val()) > 0) {
			return true;
		}
		var loginId = (document.getElementById("loginId").value);
	
		var flag = true;
	
	var regEx= /^[a-z0-9]*$/i;
		
		var minLn = 5;
		var maxLn = 50;
	
		if (loginId == "") {
			
			$('#errloginId').find('.error').html('Please Enter Login Id');
			$('#errloginId2').find('.error').html('');
			return false;
	
		} else if (!regEx.test(loginId)) {
					
				$('#errloginId').find('.error').html('Login Id Should be Alphanumeric');
				$('#errloginId2').find('.error').html('');
				return false;
	}
		if (loginId.length > maxLn) {
			
			$('#errloginId').find('.error').html('Login Id Should be Max 50 Characters');
			$('#errloginId2').find('.error').html('');
			return false;
	
		}
		if (loginId.length < minLn) {
			
		
			$('#errloginId').find('.error').html('Login Id Should be Minimum 5 Characters');
			$('#errloginId2').find('.error').html('');
			
			return false;
	
		}
		if (flag) {
			
	
			$('#errloginId').find('.error').html('');
			$('#errloginId2').find('.error').html('');
			
		}
	
		return true;
	}

function validateMobileNo(_msgID) {
		
		
		var mobileNo = (document.getElementById("mobileNo").value);
		
		var flag = true;
	var regEx = /^\d*$/i;
		
		
		var minLn = 10;
		var maxLn = 15;
	
		if (mobileNo == "") {
			
			$('#errmobileNo').find('.error').html('Mobile No should be from 10 to 15 numerics');
			return false;
	
		} else if (!regEx.test(mobileNo)) {
			
				$('#errmobileNo').find('.error').html('Mobile no should be numerics');
				return false;
	}
		if (mobileNo.length > maxLn) {
			
			$('#errmobileNo').find('.error').html('Mobile no  should be max 15 numerics');
			return false;
	
		}
		if (mobileNo.length < minLn) {
			
		
			$('#errmobileNo').find('.error').html('Mobile no should be minimum 10 numerics');
			
			return false;
	
		}
		if (flag) {
			
	
			$('#errmobileNo').find('.error').html('');
			
		}
	
		return true;
	}
	

function validateContactNo(_msgID) {
		
		var contactNo = (document.getElementById("contactNo").value);
		
		var flag = true;
	var regEx = /^\d*$/i;
		
		
		var minLn = 8;
		var maxLn = 12;
	
		if (contactNo == "") {
			
			$('#errcontactNo').find('.error').html('Contact No should be from 8 to 12 numerics');
			return false;
	
		} else if (!regEx.test(contactNo)) {
					
				$('#errcontactNo').find('.error').html('Contact no should be numerics');
				return false;
	}
		if (contactNo.length > maxLn) {
			
			$('#errcontactNo').find('.error').html('Contact no  should be max 12 numerics');
			return false;
	
		}
		if (contactNo.length < minLn) {
			
		
			$('#errcontactNo').find('.error').html('Contact no should be minimum 8 numerics');
			
			return false;
	
		}
		if (flag) {
			
	
			$('#errcontactNo').find('.error').html('');
			
		}
	
		return true;
	}
	


function addToAssignedList(id, arg1) {
arg1 = $('<div>').text(arg1).html();
	var moduleId = document.getElementById("makerChecker").value;
	moduleId = $('<div>').text(moduleId).html();
	var roleName = "'" + arg1 + "'";
	roleName = $('<div>').text(roleName).html();

	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}
	$('#assignedList')
		.append(
			'<tr class="selectedRoles" value="'
			+ arg1
			+ '" id="remove'
			+ id
			+ '"><td >'
			+ arg1
			+ '</td><td><i class="glyphicon glyphicon-remove-circle" style="color: blue" onclick="removeTag('
			+ id + ',' + roleName
			+ ')" ></i><input type="hidden" id="row' + id
			+ '" value="' + moduleId + '" ></td></tr>');
	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');

	$('#option' + id).remove();
	$("#errmakerChecker2").find('.error').html('');
}

function removeTag(roleId, arg1) {





var id = roleId;
var roleNameData = arg1;
var roleName = "'" + roleNameData + "'";
$('#optionList')
.append(
'<tr class="optionRoles" id="option'
+ id
+ '"><td>'
+ roleNameData
+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right" onclick="addToAssignedList('
+ id + ',' + roleName + ')"></td></tr>');
$('.dataTables_empty').remove();
$('#remove' + id).remove();
$('#changeFlag').val('true');
$("#errmakerChecker2").find('.error').html('');
}




var ajaxValidLoginId;

function callBackDupLogin(flag){
	ajaxValidLoginId=flag;
}



function validateAddEditForm(id) {
	$('.jqueryError').text("");
	$('.jqueryError').hide();
	var check = false;
	var userType=$('#userType').val();
    var requestState=$('#requestState').val();

    
if(!validateFromCommonVal('makerChecker', true, "SelectionBox", "" , false)){
     check = true;
     }
	if (!validateFromCommonVal('salutation', 
	     true, "SelectionBox", "", false) ) {
	  check = true;
     }

	
	if (id == 'A') {
    if (!validateLoginId('errLoginId'))
    {
    	 check = true;
    }else{
    checkDuplicateLoginId(); 
    
  
    
    if(ajaxValidLoginId){
    	
    	check = true;	
    }
	
    
    }}

 if (id == 'E') {
	 
	 if($('#newUserRejected').val()!='Yes'){
			
    if (!validateFromCommonVal('status', 
	  false, "SelectionBox", "", false) ) {
	  check = true;
     }}
}
	
   if (!validateLoginId('errloginId')) {
	
			check = true;
		}
		
		if (!validateMobileNo('errmobileNo')) {
	
			check = true;
		}
		if (!validateContactNo('errcontactNo')) {
	
			check = true;
		}
	
    if (!validateFromCommonVal('firstName', 
	  true, "UserNameRegex", 50, false) ) {
	  check = true;
     }
     	
    if (!validateFromCommonVal('lastName', false, "UserNameRegex", 50, false)) {
    	  check = true;
     }

    if (!validateFromCommonVal('middleName',false, "UserNameRegex", 50, false)) {
	  check = true;
     }
	
    if (!validateFromCommonVal('emailId', 
	  true, "MailValidation", 100, false) ) {
	  check = true;
     }	

    

   if (!validateFromCommonVal('cityId', 
	  false, "SelectionBox", "", false)) {
	  check = true;
     }
     
   if (!validateFromCommonVal('stateId', 
	  false, "SelectionBox", "", false) ) {
	  check = true;
     }
     
   if (!validateFromCommonVal('pincode', 
	  false, "NumericsOnly", 6 , false) ) {
	  check = true;
     }
	
   

	  if (!validateFromCommonVal('empId', 
	  false, "empIdRegex", 10, false) ) {
	  check = true;
     }
	
	if('3'==userType && requestState!='R' ){
	 if (!validateParticipantName('errParticipant')) {

		check = true;
	}
	
	  if (!validateFromCommonVal('accessLevel', 
	     true, "SelectionBox", "", false)) {
	  check = true;
     }
     

	}
	
	var arr = document.getElementsByClassName("selectedRoles");
	var roles = "";
let i=0;
for(i of arr) {
		roles = roles + i.id.replace('remove', '') + "|"
			+ $('#' + i.id).attr('value') + "|";}
	roles = roles.substring(0, roles.length - 1);

	if (roles.length == 0) {
		$("#errmakerChecker2").show();
		  $("#errmakerChecker2").find('.error').html('Please select roles to assign to  user');
		
		check = true;	
	}
	else{
		$("#errmakerChecker2").hide();
		$("#errmakerChecker2").find('.error').html('');	
	}
	
	
	 var text2 ="";
	var accessLevel = "";
	 var nameArr3 ="";
	if('3'==userType && requestState!='R' ){

	console.log(document.getElementById("requestState"));	
	accessLevel=document.getElementById("accessLevel").value;
	}
	
	
	if('3'==userType && requestState=='R' ){
		
			
			accessLevel=document.getElementById("accessLevel").value;
			}
	
	if(id=='A' && accessLevel=='B' ){

			 text2 = $('#binListClass option:selected').toArray().map(item => item.value).join();
				 console.log(text2);
				nameArr3 = text2.split(',');
			    if( text2==null || nameArr3.length==0 || text2==""){
					  
			    	$("#errbinId").show();
					$("#errbinId").find('.error').html("Please select bins");
					check = true;	
						}
				 else{
					 
						$("#errbinId").hide();
						$("#errbinId").find('.error').html('');
				 }
				
				}
			else if (id=='E' && accessLevel=='B'){
				 var text3 = $('#binListClasss option:selected').toArray().map(item => item.value).join();
				 console.log(text2);
				 nameArr3 = text3.split(',');
				 if(  text3==null || nameArr3.length==0 || text3==""){
					  
					 $("#errbinId").show();
						$("#errbinId").find('.error').html("Please select bins");
						check = true;	
						}
				 else{
					 
						
						$("#errbinId").hide();
						$("#errbinId").find('.error').html('');
				 }
					
				
			}

	if (!check) {



		if ($('#addEditUser').data('changed') || $('#changeFlag').val() == 'true') {
		    document.querySelector(".button").disabled = true;
			addEdit(id,userType);

		} else {
			$('#jqueryError').text('No data modified');
			$('#jqueryError').show();
			return false;
		}

	} else {
		return false;
	}

}

function addEdit(id,userType) {
var url;
			    var text="";
var nameArr1="";
var nameArr2="";
var binIdList="";
	var status = "";
	var requestState=$('#requestState').val();
	({ url, status } = setUrlNStatus(id));
	

	if (document.getElementById("entityType").value != "A") {
		var entityType = document.getElementById("entityType").value;
		var firstName = document.getElementById("firstName").value;
		var lastName = document.getElementById("lastName").value;
		var middleName = document.getElementById("middleName").value;
		var emailId = document.getElementById("emailId").value;
		var mobileNo = document.getElementById("mobileNo").value;
		var loginId = document.getElementById("loginId").value;
		var userId = document.getElementById("userId").value;
		var makerChecker = document.getElementById("makerChecker").value;
		var streetAddress1 = document.getElementById("streetAddress").value;
		var streeArr = streetAddress1.split(",");
		var salutation = document.getElementById("salutation").value;
		var streetAddress = "";
		streetAddress = streetAddressArray(streeArr);
		

		var cityId = document.getElementById("cityId").value;
		var stateId = document.getElementById("stateId").value;
		var pincode = document.getElementById("pincode").value;
		var empId = document.getElementById("empId").value;
		var contactNo = document.getElementById("contactNo").value;
		var arr = document.getElementsByClassName("selectedRoles");
		var participantName = "";
		var accessLevel = "";
		if('3'==userType && requestState!='R' ){
	participantName=	document.getElementById("participantName").value;
		console.log(document.getElementById("requestState"));	
		accessLevel=document.getElementById("accessLevel").value;
		}

		var roles = getRoleListStr(arr);

		roles = roles.substring(0, roles.length - 1);

		if (roles.length == 0) {
			$("#errvMakChk").show();
			$("#errvMakChk").text("Please select roles to assign to  user");
			return false;
		}
	

		if('3'==userType && requestState=='R' ){
			participantName=	document.getElementById("participantName").value;
				
				accessLevel=document.getElementById("accessLevel").value;
				}
		
				if(id=='A' && accessLevel=='B' ){
		  id = $('#binListClass option:selected').toArray().map(item => item.value).join();
		   nameArr1  = id.split(',');
	
			$('#binFlags').val(arr);
	
	 text= $('#binListClass option:selected').toArray().map(item => item.text).join();
			  document.getElementById("binFlagss").value = text;
			  
	
			nameArr2 = text.split(',');

					binIdList = getBinIdList(nameArr2, nameArr1);
			
			
			}
		else if (id=='E' && accessLevel=='B'){
			 id = $('#binListClasss option:selected').toArray().map(item => item.value).join();
			     nameArr1 = id.split(',');
				console.log(nameArr1);
				$('#binFlags').val(arr);
		
			    text = $('#binListClasss option:selected').toArray().map(item => item.text).join();
				  document.getElementById("binFlagss").value = text;
				  
		
			 nameArr2 = text.split(',');
				
			
				
				
				 binIdList = setBinIdList(nameArr2, nameArr1);

				
				
			
		}
		
		


		var data = "roleIds," + roles + ",entityType," + entityType +",binIds," + binIdList
			+ ",firstName," + firstName + ",lastName," + lastName
			+ ",middleName," + middleName + ",emailId," + emailId
			+ ",loginId," + loginId + ",mobileNo," + mobileNo
			+ ",streetAddress," + streetAddress + ",cityId," + cityId
			+ ",stateId," + stateId + ",pincode," + pincode + ",contactNo,"
			+ contactNo + ",empId," + empId +",accessLevel," + accessLevel + ",userId," 
			+ userId + ",makerChecker," + makerChecker+ ",salutation," + salutation+ ",status," + status
			 + ",userType," + $('#userType').val()+ ",requestState," + $('#requestState').val()+",participantName," + escape(participantName);

		
	

		postData(url, data);

	}
}



function setUrlNStatus(id) {
var url,status;
	if (id == 'A') {
		url = '/addUser';
		status = 'Active';

	}
	else if (id == 'E') {
		url = '/updateUser';
		status = document.getElementById("status").value;
	}
	return { url, status };
}

function getRoleListStr(arr) {
	var roles = "";
	for (let j of arr) {
		roles = roles + j.id.replace('remove', '') + "|"
			+ $('#' + j.id).attr('value') + "|";

	}
	return roles;
}

function getBinIdList(nameArr2, nameArr1) {
var binIdList = "";
	for (let i = 0; i < nameArr2.length; i++) {
		binIdList = binIdList + nameArr1[i] + "|"
			+ nameArr2[i] + "|";
	}
	return binIdList;
}

function streetAddressArray(streeArr) {
var streetAddress="";
	for (let i of streeArr) {
		streetAddress = streetAddress + i + "*";
	}
	return streetAddress;
}

function setBinIdList(nameArr2, nameArr1) {
var binIdList = "";
	for (var p = 0; p < nameArr2.length; p++) {
		binIdList = binIdList + nameArr1[p] + "|"
			+ nameArr2[p] + "|";
	}
	return binIdList;
}

function tester() {
	$("td.ticked i").remove();

}
function test(id) {
	var fun = document.getElementsByClassName("glyphicon glyphicon-ok-sign");
	if (fun.length == 1) {
		$("td.ticked i").remove();
		$("td.tick" + id).addClass('ticked').append(
			'<i class="glyphicon glyphicon-ok-sign"></i>');
	}
}



function validateMakerChecker(msgID) {
var makerChecker = document.getElementById("makerChecker").value;
var errvMakChk = document.getElementById(msgID);
if (makerChecker == "SELECT") {
errvMakChk.className = 'error';
errvMakChk.innerHTML = "Please Select Role Type";
return false;
}
else {
errvMakChk.className = 'error';
errvMakChk.innerHTML = "";
}

return true;
}



function validateParticipantName(_msgID) {



	var participantName = (document.getElementById("participantName").value);
		

	

	if (participantName == "SELECT") {



		$('#errParticipant').find('.error').html('Please select Participant Name');
			
		return false;

	} 	else {
	
     $('#errParticipant').find('.error').html('');
     
    }	
	
	return true;
}






function populateEmailId() {

	var emailId = document.getElementById("loginId").value;
	var regEx = /^[_A-Za-z0-9-]+(\.[_A-Za-z0-9-]+)*@[A-Za-z0-9]+(\.[A-Za-z0-9]+)*(\.[A-Za-z]{2,})$/;

	if (!regEx.test(emailId)) {
	 console.log("");
	} else {

		document.getElementById("emailId").value = emailId;

	}

	return true;
}
function validateEntityType() {
	if ($("#entityType option:selected").val() == "A") {
		$("#errEntityType").text("Please select the Entity type");
		return false;
	} else
		$("#errEntityType").text("");
	return true;
}


function validateBranchCode(msgID) {

	var branchCode = document.getElementById("branchCode").value;
	var errBranchCode = document.getElementById(msgID);
var regEx = /^[A-Z0-9 ]+$/i;

	if (branchCode == "") {
		errBranchCode.className = 'error';
		errBranchCode.innerHTML = "";
		return true;

	} else if (!regEx.test(branchCode)) {

		errBranchCode.className = 'error';
		errBranchCode.innerHTML = "Branch Code must be alphanumeric only";
		return false;

	} else {

		errBranchCode.className = 'error';
		errBranchCode.innerHTML = "";
	}

	return true;
}

function getCities() {

	if ($('#stateId').val() != '0') {
		var stateId = $("#stateId").val();
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		
		$.ajax({
			url: "getUserCityMaster",
			type: "POST",
			data: {
				stateId: stateId,
				_TransactToken: tokenValue
			},
			dataType: "json",
			success: function(data) {
				$("#cityName").empty();
				$("#cityName").append('<option value="0">--Select--</option>');
				$.each(data, function(option) {
					$("#cityName").append(
						'<option value="' + option.value + '">'
						+ option.label + '</option>');
				});
			}
		});

	} else {
		$("#cityName").empty();
	}
}


function getFunctionalityAddList(reqType) {

	$("#errMsg").hide();
	var roleId = "0";
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/') + 1);
	var rtype = reqType;
	var makerChecker = $("#makerChecker").val();

	
	var url = pathName + "getRoleListBasedOnUserType?rtype=" + rtype + "&makerChecker=" + makerChecker;
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	
	$.ajax({
		type: "POST",
		url: url,
		data: {
			roleId: roleId,
			_TransactToken: tokenValue
		},
		success: function(data) {
		
		
			
			$('#optionList').empty();
			
			
			
	var id="";
	let i=0;
for (i of data) {
				 id= i.roleId;
				var name = i.roleName;
				var name1 = "'" + name + "'";
				$('#optionList')
					.append(
						'<tr class="optionRoles"  id="option'
						+ id
						+ '"><td>'
						+ name
						+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right"  onclick="addToAssignedList('
						+ id + ',' + name1
						+ ')"></td></tr>');}
			
			
			var selectedArray = document.getElementsByClassName("selectedRoles");
			var counter = 0;
			if (selectedArray.length > 0) {
			let j=0;
			 for ( j of selectedArray) {
			

					id = j.id.replace(
						"selectedRoles", '').replace("remove", "");
					$('table#tabnew tr#option' + id).remove();

				}
			}
			

			 for (let b of data) {
			 for (let n of selectedArray) {
				
					id = n.id.replace("selectedRoles", '');
					if ((b.roleId) == id) {
						counter++;
					}
				}
			}
		},
		error: function() {
			console.log("error");
		}
	});
}

function restFunsTab() {
	var Table = document.getElementById("assignedList");
	Table.innerHTML = "<tr class='odd'><td valign='top' colspan='2' class='dataTables_empty'></td></tr>";
}

function removeAllFunctionalities() {




	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}

	 

	var selectedArray = document.getElementsByClassName("selectedRoles");
	

	if (selectedArray.length > 0) {
	
	  for (let j of selectedArray) {
       	var id = j.id.replace(
				"option", '').replace("remove", "");
			var roleNameData = j.innerText;
			roleNameData = $('<div>').text(roleNameData).html();
			var roleName = "'" + roleNameData + "'";
			roleName = $('<div>').text(roleName).html();

			$('#optionList')
				.append(
					'<tr class="optionRoles" id="option'
					+ id
					+ '"><td>'
					+ roleNameData
					+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right" onclick="addToAssignedList('
					+ id + ',' + roleName + ')"></td></tr>');
	
   
}
	
		
		$('#assignedList').empty();

	}

	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');
	 $('#forms').prop('disabled', false);
}


function selectAllFunctionlities() {

	var moduleId = document.getElementById("makerChecker").value;
moduleId = $('<div>').text(moduleId).html();

	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}

	
	 var optionsArray = document.getElementsByClassName("optionRoles");

	
	
let i=0;
	if (optionsArray.length > 0) {
for (i of optionsArray) {
		
			var id = i.id.replace(
				"option", '').replace("remove", "");
				id = $('<div>').text(id).html();
			var roleNameData = i.innerText;
			roleNameData = $('<div>').text(roleNameData).html();
			var roleName = "'" + roleNameData + "'";
			roleName = $('<div>').text(roleName).html();
			id = $('<div>').text(id).html();

			$('#assignedList')
				.append(
					'<tr class="selectedRoles" value="'
					+ roleNameData
					+ '" id="remove'
					+ id
					+ '"><td >'
					+ roleNameData
					+ '</td><td><i class="glyphicon glyphicon-remove-circle" style="color: blue" onclick="removeTag('
					+ id + ',' + roleName
					+ ')" ></i><input type="hidden" id="row' + id
					+ '" value="' + moduleId + '" ></td></tr>');

		}
		$('#optionList').empty();
	}
	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');
	
	 $('#forms').prop('disabled', false);


}


function postDiscardAction(action, _id) {

	document.querySelector(".button").disabled = true;
	var url = action;
	var userId = $("#userId").val();
	var data =  "userId," + userId ;
	postData(url, data);
}


function setRefnum(reqId){
	document.getElementById("refNum").value = reqId;

}


function changeFunc(_value) {
    $("#mywrapperBinLable").show();
    $("#binListClass").empty();
    $("#binListClass").multiselect("destroy"); // Destroy any existing multiselect instances
 
    if ($('#participantName').val() != '0') {
        if ($('#accessLevel').val() == 'P') {
            $("#binListClass").next().hide();
            $("#mywrapper").hide();
            $("#binListClasss").next().hide();
        } else if ($('#accessLevel').val() == 'B') {
            var participantName = $("#participantName").val();
            var arrs = participantName.split('-');
            var participantId = arrs[0];
            var tokenValue = document.getElementsByName("_TransactToken")[0].value;
 
            $.ajax({
                url: "getBinDetail",
                type: "POST",
                data: {
                    participantId: participantId,
                    "_TransactToken": tokenValue
                },
                dataType: "json",
                success: function(data) {
                    $("#binListClass").empty();
                    $("#binListClasss").empty();
                    $("#mywrapper").show();
                    $("#binListClass").next().show();
                    $("#mywrappers").show();
                    $("#binListClasss").next().show();
                    
                    let options = [];
                    data.forEach(item => {
                        let titleToool = '';
 
                        if (item.binType == "Issuer") {
                            titleToool = `BinNumber: ${item.binNumber}\nSettlementBin: ${item.settlementBin}\nLowBin: ${item.lowBin}\nHighBin: ${item.highBin}\nProduct: ${item.productType}\nBinType: ${item.binType}\nBinProductType: ${item.binProductType}\nBinCardType: ${item.binCardType}\nBinCardVariant: ${item.binCardvariant}`;
                        } else if (item.binType == "Acquirer") {
                            titleToool = `BinNumber: ${item.binNumber}\nSettlementBin: ${item.settlementBin}\nProduct: ${item.productType}\nBinType: ${item.binType}`;
                        }
 
                        options.push(`<option class='tags' title='${titleToool}' value='${item.binId}' id='${item.binId}'>${item.binNumber}</option>`);
                    });
 
                    $("#binListClass").html(options.join(''));
                    $('#binListClass').multiselect({
                        enableFiltering: true,
                        includeSelectAllOption: true,
                        enableCaseInsensitiveFiltering: true,
                        maxHeight: 200,
                        buttonWidth: '100%'
                    });
 
                    // Initialize tooltips efficiently using delegation
                    $('.multiselect-container').tooltip({
                        items: 'li:not(.filter, .group, .multiselect-all)',
                        content: function() {
                            return $(this).attr('title');
                        },
                        placement: 'right',
                        container: 'body',
                        tooltipClass: "mytooltip"
                    });
 
                }
            });
        } else {
            $("#mywrapper").show();
            $("#binListClass").next().hide();
            $("#binListClass").empty();
            $("#binListClasss").next().hide();
        }
    }
}
 