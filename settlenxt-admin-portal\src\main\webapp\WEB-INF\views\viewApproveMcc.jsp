<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script src="./static/js/validation/viewApproveMcc.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">
		<spring:message code="budget.apprejecterrormsg" />
	</div>
	<div class="alert alert-danger remarkMust" role="alert">
		<spring:message code="budget.remarkserror" />
	</div>
	<c:url value="approveMccConfig" var="approveMccConfig" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveMccConfig" modelAttribute="mccDTO"
		action="${approveMcc}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="mcc.approvalPendingViewScreen.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="mccId" value="${mccDTO.mccId}"> <input
							type="hidden" id="crtuser" value="${mccDTO.lastUpdatedBy}" />

						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">MCC</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td colspan="8">
										<div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> 
											<span data-i18n="Data"> <spring:message code="budget.requestInfo" /></span></strong>
										</div>
									</td>
								</tr>
								<tr>
									<td><label><spring:message code="currencyMaster.requestType" /></label></td>
									<td>${mccDTO.lastOperation}</td>
									<td><label><spring:message code="currencyMaster.requestDate" /></label></td>
									<c:choose>
											<c:when test="${mccDTO.lastUpdatedOn == null}">
												<td>${mccDTO.createdOn}</td>
											</c:when>
											<c:otherwise>
												<td>${mccDTO.lastUpdatedOn}</td>
												</c:otherwise>
										</c:choose>
									<td><label><spring:message code="currencyMaster.requestStatus" /></label></td>
									<td><c:if test="${mccDTO.requestState =='A' }">
											<spring:message code="currencyMaster.requestState.approved.description" />
										</c:if> <c:if test="${mccDTO.requestState =='P' }">
											<spring:message code="currencyMaster.requestState.pendingApproval.description" />
										</c:if> <c:if test="${mccDTO.requestState =='R' }">
											<spring:message code="currencyMaster.requestState.rejected.description" />
										</c:if> <c:if test="${mccDTO.requestState =='D' }">
											<spring:message code="currencyMaster.requestState.discared.description" />
										</c:if></td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="currencyMaster.requestBy" /></label></td>
									<c:choose>
											<c:when test="${mccDTO.lastUpdatedBy == null}">
												<td>${mccDTO.createdBy}</td>
											</c:when>
											<c:otherwise>
												<td>${mccDTO.lastUpdatedBy}</td>
												</c:otherwise>
										</c:choose>
									
									<td><label><spring:message code="currencyMaster.approverComments" /></label></td>
									<td colspan=2>${mccDTO.checkerComments}</td>
									<td></td>
									<td></td>
									<td></td>

								</tr>
								<td colspan="8"><div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-credit-card"></span> <span
											data-i18n="Data"> <spring:message code="mcc.viewscreen.title" /></span></strong>
									</div></td>
								<tr>
									<td><label><spring:message code="mcc.mccGroup" /><span style="color: red"></span></label></td>
									<td>${mccDTO.mccGroupName}</td>
									<td><label><spring:message code="mcc.mccCode" /><span style="color: red"></span></label></td>
									<td>${mccDTO.mccCode}</td>
									<td><label><spring:message code="mcc.mccDesc" /><span style="color: red"></span></label></td>
									<td>${mccDTO.mccDesc}</td>
									<td><label><spring:message code="mcc.status" /><span style="color: red"></span></label></td>
									<td><c:if test="${mccDTO.status =='A' }">ENABLE</c:if> <c:if
											test="${mccDTO.status =='I' }">DISABLE</c:if></td>

								</tr>

								<sec:authorize access="hasAuthority('Approve Mcc Config')">
									<c:if test="${mccDTO.requestState eq 'P'}">
										<tr>
											<td colspan="8"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"> <spring:message code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
										</tr>
										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej" onchange="display()">
													<option value="N"><spring:message code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message code="AM.lbl.reject" /></option>
											</select>
											</td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div>
											</td>
										<td></td>
										<td></td>
										<td></td>
										
										</tr>
									</c:if>
								</sec:authorize>
							</tbody>
						</table>


						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Mcc Config')">
										<c:if test="${mccDTO.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approveRole" value="Submit"
												onclick="postAction('/approveMcc');" />
										</c:if>
									</sec:authorize>
									
									<sec:authorize access="hasAuthority('Edit Mcc Config')">				
									<c:if test="${mccDTO.requestState  eq 'R' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="userAction('/discardRejectedMcc','${mccDTO.mccId}');" />
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="edit('/editMcc','${mccDTO.mccId}','pendingApprove');"/>
									</c:if>
									</sec:authorize>

									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/mccPendingForApproval');">
										<spring:message code="budget.backBtn" />
									</button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

