[{"id": "00", "description": "Member generated outgoing file"}, {"id": "01", "description": "Network generated incoming file"}, {"id": "02", "description": "Network generated acknowledgement"}, {"id": "03", "description": "Network generated web acknowledgement"}, {"id": "04", "description": "Network Generated acknowledgement for ON Hold transactions"}, {"id": "71", "description": "Negative List Request File"}, {"id": "72", "description": "Negative List Response File"}, {"id": "73", "description": "VIP Card Management Request File"}, {"id": "74", "description": "VIP Card Management Response File"}, {"id": "75", "description": "PIN Offset File Upload Request File"}, {"id": "76", "description": "PIN Offset File Upload Response File"}, {"id": "80", "description": "Onus Transaction File"}, {"id": "81", "description": "BIN Management File"}, {"id": "82", "description": "Acquirer ID Management File"}, {"id": "83", "description": "SMS response file to Acquirer"}, {"id": "84", "description": "SMS Incoming file to Issuer"}, {"id": "85", "description": "RAW Data to Acquirer"}, {"id": "86", "description": "Raw Data to Issuer"}, {"id": "87", "description": "DMS response file to Acquirer"}, {"id": "88", "description": "DMS incoming file to Issuer"}]