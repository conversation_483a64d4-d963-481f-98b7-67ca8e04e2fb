$(document).ready(function () {
    $("#errvendor").hide();
    $("#errbudget").hide();
    $("#erryear").hide();

    
    $('#vendor').on('keyup keypress blur change', function () {
        validateField('vendor', true, "Alphanumeric", 100, 0,0,false);
    });
    $('#budget').on('keyup keypress blur change', function () {
        validateField('budget', true, "Decimal", 100, 0.0,9999999999999999.99,true);
    });
     $('#year').on('keyup keypress blur change', function () {
        validateField('year', true, "SelectionBox",0,1,99999999999,true);
    });
 	disableSave();
 	
 	  $("#vendor").on('keyup keypress blur change', function () {
 	  validateField('vendor', true, "Alphanumeric", 100, 0,0,false);
        unableSave();
    });
 	  $("#budget").on('keyup keypress blur change', function () {
 	  validateField('budget', true, "Decimal", 100, 0.0,9999999999999999.99,true);
        unableSave();
    });
    $("#year").on('keyup keypress blur change', function () {
    validateField('year', true, "SelectionBox",0,1,99999999999,true);
        unableSave();
    });
    
});

function disableSave()
{
if (typeof bSubmit != "undefined") {
	document.getElementById("bSubmit").disabled = true;
}
if (typeof bUpdate != "undefined") {
	document.getElementById("bUpdate").disabled = true;
}
}

function unableSave()
{
if (typeof bSubmit != "undefined") {
	document.getElementById("bSubmit").disabled = false;
}
if (typeof bUpdate != "undefined") {
	document.getElementById("bUpdate").disabled = false;
}
}

function resetAction() {
$("#errorStatus").hide();
document.getElementById("addBudgetConfig").reset();
$("#errbudget").find('.error').html('');
$("#errvendor").find('.error').html('');
$("#erryear").find('.error').html('');
}

window.history.forward();
function noBack() {
    window.history.forward();
}

function display() {
    $(".appRejMust").hide();

}

function userAction(_type, action) {
	var url = action;
	
	var budgetId = document.getElementById("budgetId").value;
	var data = "budgetId," + budgetId + ",status,"
		+ status;
	postData(url, data);
} 

function addOrUpdateBudget(id) {
var url;
	if (id == 'A') {
		url = '/addBudget';

	} else if (id == 'E') {
		url = '/updateBudget';
	}
    var isValid = true;

    if (!validateField('vendor', true, "Alphanumeric", 100, 0,0,false) && isValid) {
        isValid = false;
    }
    if (!validateField('budget', true, "Decimal", 100, 0.0,9999999999999999.99,true) && isValid) {
        isValid = false;
    }
    if (id == 'A') {
     if (!validateField('year', true, "SelectionBox",0,1,99999999999,true) && isValid) {
        isValid = false;
    }}
    
    
    
    if (isValid) {
    	
        
        var data = "budgetId," + $('#budgetId').val()+ ",budget," + $('#budget').val() 
       + ",vendorId," + $('#vendor').val()+ ",year," + $('#year').val()
        +",parentPage," + $("#hparentPage").val();
       
        postData(url, data);
    }
}

function validateField(fieldId, isMandatory, fieldType, _length,  minNumber, maxNumber ,isRange) {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
     var isDataEmpty = false;
     
     if ((isMandatory && fieldValue.trim() == "" && (fieldType!="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
        isDataEmpty = true;
    }
    isValid = checkNAN(fieldType, fieldValue, isValid);
    isValid  = isAlphabet(fieldType, fieldValue, isValid);
    isValid  = isAlphabetWithSpace(fieldType, fieldValue, isValid);
    isValid  = isAlphanumericNoSpace(fieldType, fieldValue, isValid);
    isValid  = isInteger(fieldType, fieldValue, isValid);
    isValid = isDecimal(fieldType, fieldValue, isValid);
    
    isValid = checkRange(isRange, fieldValue, minNumber, maxNumber, isValid);
    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
    	if (fieldType != "SelectionBox"){
    		if (isDataEmpty && budgetValidationMessages[fieldId]){
                    $("#err" + fieldId).find('.error').html(budgetValidationMessages[fieldId]);
                
    		}
    		else if(budgetValidationMessages[fieldId+"1"]){
                    $("#err" + fieldId).find('.error').html(budgetValidationMessages[fieldId+"1"]);
                
    		}
    		
    	}
    	else if(budgetValidationMessages[fieldId]){
                $("#err" + fieldId).find('.error').html(budgetValidationMessages[fieldId]);
            
    	}
    		
        $("#err" + fieldId).show();
    }
    return isValid;
}


function checkRange(isRange, fieldValue, minNumber, maxNumber, isValid) {
    if (isRange && !(Number(fieldValue) >= Number(minNumber) && Number(fieldValue) <= Number(maxNumber))) {
        isValid = false;
    }
    return isValid;
}

function isDecimal(fieldType, fieldValue, isValid) {
    if (fieldType == "Decimal") {
        var regEx = /^\d*(\.\d{0,2})?$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return isValid;
}

function isInteger(fieldType, fieldValue, isValid) {
    if (fieldType == "Integer") {
        var regEx = /^\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return  isValid;
}

function isAlphanumericNoSpace(fieldType, fieldValue, isValid) {
    if (fieldType == "AlphanumericNoSpace") {
        var regEx = /^[A-Za-z0-9]+$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return  isValid ;
}

function isAlphabetWithSpace(fieldType, fieldValue, isValid) {
    if (fieldType == "AlphabetWithSpace") {
        var regEx = /^[\dA-Z ]+$/i;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return  isValid ;
}

function isAlphabet(fieldType, fieldValue, isValid) {
    if (fieldType == "Alphabet") {
        var regEx = /^[\dA-Z]+$/i;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return  isValid ;
}

function checkNAN(fieldType, fieldValue, isValid) {
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;

    }
    return isValid;
}

function postDiscardBudgetAction(action) {
		
	var url = action;
	var budgetId = document.getElementById("budgetId").value;
	var data = "budgetId," + budgetId ;
	postData(url, data);
	
}

function checkAlreadyPresent()
{
	var vendor = $("#vendor").val();
	var year = $("#year").val();
	var budget = $("#budget").val();
	
	if(vendor !="0" && year!="0" && budget !=0)
	{
	
	var validvRoleName=false;
	var msUrl = "checkAlreadyPresent";
	
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	$.ajax({
			url: msUrl,
			type: "POST",
			dataType: "json",
			headers: {
			'_TransactToken': tokenValue
			},
			
			
			data: {
				"vendor": vendor,
				"year": year
			},
			success: function(response) {
			console.log(response);
				if (response.status == "BSUC_0001") {
					validvRoleName = true;
					var id ='A';
					addOrUpdateBudget(id);
							
				} else {
					validvRoleName = false;
					$('#errorStatus').html('Budget entry already present');
					$('#jqueryError').show();
					$('#jquerySuccess').hide();
				}
			},
			
		});

	return validvRoleName;}	
}
