#Configuration Module Labels


#IFSC Configuration screen labels
ifsc.editscreen.title=Edit IFSC Information
ifsc.addscreen.title=Add New IFSC Information
ifsc.viewscreen.title=IFSC Information
ifsc.listscreen.title=IFSC List
ifsc.approvalPanel.title=IFSC Approval
ifsc.mainTab.title=IFSC
ifsc.approvalTab.title=Approval

ifsc.ifscCode=IFSC Code
ifsc.bankName=Bank Name
ifsc.bankCode=Bank Code
ifsc.nfsCode=NFS Code
ifsc.savingsAccId=Saving Account Id
ifsc.currAccId=Current Account Id
ifsc.rtgsAccId=RTGS Code
ifsc.status=Status
ifsc.submitBtn=Submit
ifsc.backBtn=Back
ifsc.editBtn=Edit
ifsc.viewBtn=View
ifsc.clearBtn=Clear
ifsc.exportBtn=Excel
ifsc.csvBtn=CSV
ifsc.clearFiltersBtn=Clear Filters
ifsc.discardBtn=Discard
ifsc.addIFSCBtn=Add IFSC
ifsc.actionTitle=Action
ifsc.approvalStatus=Approval Status
ifsc.activeStatus=Active
ifsc.inactiveStatus=Inactive
ifsc.requestState.approved.description=Approved
ifsc.requestState.pendingApproval.description=Pending for Approval
ifsc.requestState.rejected.description=Rejected
ifsc.requestState.discared.description=Discarded
ifsc.requestInformation=Request Information
ifsc.requestType=Request Type
ifsc.requestDate=Request Date
ifsc.requestStatus=Request Status
ifsc.requestBy=Request By
ifsc.approverComments=Approver Comments
ifsc.checkerComments=Checker Comments
ifsc.createdBy=Created By


#Fee Rate screen labels
feeRate.editscreen.title=Edit FeeRate Information
feeRate.addscreen.title=Add New FeeRate Information
feeRate.viewscreen.title=FeeRate Information
feeRate.listscreen.title=FeeRate List
feeRate.approvalPanel.title=FeeRate Approval
feeRate.mainTab.title=FeeRate
feeRate.approvalTab.title=Approval
feeRate.feeId= Fee Id
feeRate.feeType= Fee Type
feeRate.feeCode= Fee Code
feeRate.feeDesc= Fee Description
feeRate.txnCurrency= Txn Currency
feeRate.validFromDt= Valid From
feeRate.validToDt= Valid To
feeRate.gstCode = GST Code
feeRate.cashFeeFlat=CashFees Flat
feeRate.cashFeePercent=CashFees Percent
feeRate.cashFeeMin=CashFees Min
feeRate.cashFeeMax=CashFees Max
feeRate.reverseCashFee= Reverse Cash Fee
feeRate.purFeeFlat= Purchase Fee Flat
feeRate.purFeePercent= Purchase Fee Percent
feeRate.purFeeMin= Purchase Fee Min
feeRate.purFeeMax= Purchase Fee Max
feeRate.ifscCode=IFSC Code
feeRate.bankName=Bank Name
feeRate.bankCode=Bank Code
feeRate.nfsCode=NFS Code
feeRate.savingsAccId=Saving Account Id
feeRate.currAccId=Current Account Id
feeRate.rtgsAccId=RTGS Code
feeRate.status=Status
feeRate.creditTo = Credit To
feeRate.debitTo = Debit To
feeRate.submitBtn=Submit
feeRate.backBtn=Back
feeRate.editBtn=Edit
feeRate.viewBtn=View
feeRate.discardBtn=Discard
feeRate.addFeeBtn=Add Fee
feeRate.actionTitle=Action
feeRate.approvalStatus=Approval Status
feeRate.activeStatus=Active
feeRate.inactiveStatus=Inactive
feeRate.requestState.approved.description=Approved
feeRate.requestState.pendingApproval.description=Pending for Approval
feeRate.requestState.rejected.description=Rejected
feeRate.requestState.discared.description=Discarded
feeRate.requestInformation=Request Information
feeRate.requestType=Request Type
feeRate.requestDate=Request Date
feeRate.requestStatus=Request Status
feeRate.requestBy=Request By
feeRate.createdBy = Created By
feeRate.createdOn=Created On
feeRate.lastUpdatedBy=Last Updated By
feeRate.lastUpdatedOn=Last Updated On
feeRate.approverComments=Approver Comments
feeRate.remarks = Remarks
feeRate.approveReject = Approve/Reject :
feeRate.select = Select
feeRate.approve = Approve
feeRate.reject = Reject
feeRate.Approve = Approve
feeRate.Reject = Reject
feeRate.feeTypeCode=Fee Type
feeRate.multiplier=Multiplier
feeRate.gstcode=GST Code
feeRate.reversCashFee=Reverse Cash Fee
feeRate.discardBtn = Discard
feesUpload.fees= Fees Master File Upload 
feesUpload.fileUplMonitoring= File Upload Monitoring
feesUpload.selectFile= Select File
feesUpload.uploadFile= Upload File
feesUpload.reset= Reset
feesUpload.download=Download
feesUpload.fromDate= From Date
feesUpload.toDate= To Date
feesUpload.fileName= File Name
feesUpload.samplefileName=Sample File Name
feesUpload.search= Search
feesUpload.status= Status
feesUpload.batchRunId = Batch Run ID
feesUpload.fileSize = File Size
feesUpload.invalideCount = Invalid Count
feesUpload.validCount = Valid Count
feesUpload.totalCount = Total Count
feesUpload.fileProcStatus = File Processing Status
feesUpload.reqDate = Request Date
feesUpload.selectRecord = Please select at least 1 record
feesUpload.pageType=Page Type
feesUpload.searchFile=Search File
feesUpload.invalidCount=Invalid Count
am.lbl.feeConfig=FeeConfig
am.lbl.major=Major
am.lbl.minor=Minor
am.lbl.majorId=Major Id
am.lbl.minorId=Minor Id
am.lbl.schemeCode=Scheme Code
am.lbl.productCode=Product Code
am.lbl.cardType=Card Type
am.lbl.cardBrand=Card Brand
am.lbl.functionCode=Function Code
am.lbl.priority=Priority
am.lbl.feeConfigs=Fee Configs
am.lbl.feeId=Fee Id
am.lbl.fieldName=Field Name
am.lbl.relationalOperator=Relational Operator
am.lbl.fieldValue=Field Value
am.lbl.subFieldName=SubField Name
am.lbl.significance=Significance
am.lbl.status=Status
sm.lbl.view=View
sm.lbl.edit=Edit
am.lbl.feeConfigId=Fee Config Id
am.lbl.feeMinorId=Fee Minor Id
feeMajor.viewscreen.title=FeeMajor Information
feeMajor.editscreen.title=Edit FeeMajor Information
feeMajor.addscreen.title= Add New FeeMajor Information
feeMinor.editscreen.title=Edit FeeMinor Information
feeMinor.addscreen.title= Add New FeeMinor Information
feeRate.columnValue = Column Value
feeRate.fieldName = Column Name
msg.lbl.addFeeMajorMinor=Add Fee Major/Minor
fees.addFeeConfig.title = Add Fee Config
fees.editFeeConfig.title = Edit Fee Config
feeConfig.viewscreen.title=FeeConfig Information
feeRate.acqProcFeeId=Acq processing fee id
feeRate.acqAssFeeId=Acq assessment fee id
feeRate.issProcFeeId=Iss processing fee id
feeRate.issAssFeeId=Iss assessment fee id
feeRate.acqAuthFeeId=Acq auth fee id
feeRate.issAuthFeeId=Iss auth fee id
feeRate.requestId=Request Id
feeRate.lastUpdatedBy=Last Updated By
feeRate.lastUpdatedOn=Last Updated On
feeRate.createdOn=Created On
feeMinor.viewscreen.title=FeeMinor Information
sm.lbl.save=Save
feeRate.interchangeFees=Interchange Fees
feeRate.issassessFees=Issuer Assessment Fee
feeRate.acqassessFees=Acquirer Assessment Fee
feeRate.issprocessFees=Issuer Processing Fee
feeRate.acqprocessFees=Acquirer Processing Fee
feeRate.issAuthFees=Issuer Authorization Fee
feeRate.acqAuthFees=Acquirer  Authorization Fee
feeRate.clearBtn=Clear
sm.lbl.cancel=Cancel
ERR_BIN_EXCL_EXISTS=Bin Exclusion with same date range already exists


#Rebate screen labels
rebate.listscreen.title=Add Rebate
rebate.rebateList=Rebate List
rebate.rebateId=Rebate Id
rebate.cardType=Card Type
rebate.financialYear=Financial Year
rebate.featureOrBaseFee=Feature or Base Fee
rebate.operationalIndi=Operational Indi
rebate.newCardCount1=New Card Count 1
rebate.newCardCount2=New Card Count 2
rebate.rebatePercentage=Rebate Percentage
rebate.status=Status
rebate.action=Action
rebate.edit=Edit
rebate.view=View
rebate.submit=Submit
rebate.back=Back
rebate.availableCardVariant=Available Card Variant
rebate.selectedCardVariant=Selected Card Variant
rebate.cardVariantName=Card Variant Name
rebate.add=Add
rebate.remove=Remove
rebate.discard=Discard
rebate.cardVariantInformation=Card Variant Information
rebate.rebateStatus=Rebate Status
rebate.rebateInformation=Rebate Information
rebate.pendingRebate=Pending Rebate
rebate.requestInformation=Request Information
rebate.requestType=Request Type
rebate.requestDate=Request Date
rebate.requestStatus=Request Status
rebate.requestBy=Request By
rebate.approverComments=Approver Comments

#Penalty screen labels
penalty.listscreen.title=Penalty Configuration
penalty.status=Status
penalty.action=Action
penalty.edit=Edit
penalty.view=View
penalty.submit=Submit
penalty.back=Back
penalty.add=Add
penalty.remove=Remove
penalty.approval=Approval
penalty.tabname=Penalty
penalty.discard=Discard
penalty.rebateStatus=Penalty Status
penalty.penaltyInformation=Penalty Configuration Information
penalty.pendingRebate=Pending Penalty Configuration 
penalty.requestInformation=Penalty Configuration  Information
penalty.requestType=Request Type
penalty.requestDate=Request Date
penalty.requestStatus=Request Status
penalty.requestBy=Request By
penalty.approverComments=Approver Comments
penalty.amount=Penalty Amount
penalty.modifyingData=Penalty for modifying uploaded data
penalty.notUpdatingData=Penalty for not updating data for a bin
penalty.noDataForApproval=No Data Available for Approval

#McprTAT screen labels
mcprTAT.listscreen.title=MCPR TAT Configuration
mcprTAT.status=Status
mcprTAT.action=Action
mcprTAT.edit=Edit
mcprTAT.view=View
mcprTAT.submit=Submit
mcprTAT.back=Back
mcprTAT.add=Add
mcprTAT.remove=Remove
mcprTAT.approval=Approval
mcprTAT.tabname=MCPR TAT
mcprTAT.discard=Discard
mcprTAT.rebateStatus=MCPR TAT Status
mcprTAT.mcprTATInformation=MCPR TAT Configuration Information
mcprTAT.mcprTATConfiguration=Pending MCPR TAT Configuration 
mcprTAT.requestInformation=MCPR TAT Configuration  Information
mcprTAT.requestType=Request Type
mcprTAT.requestDate=Request Date
mcprTAT.requestStatus=Request Status
mcprTAT.requestBy=Request By
mcprTAT.approverComments=Approver Comments
mcprTAT.dataUploadWindow=MCPR TAT for MCPR Data upload Window
mcprTAT.dataEditWindow=MCPR TAT for MCPR Data eidt Window
mcprTAT.TAT=MCPR TAT in Number
mcprTAT.noDataForApproval=No Data Available for Approval

#InsurancePremium screen labels
insurancePremium.listscreen.title=Insurance Premium Configuration
insurancePremium.status=Status
insurancePremium.action=Action
insurancePremium.edit=Edit
insurancePremium.view=View
insurancePremium.submit=Submit
insurancePremium.back=Back
insurancePremium.add=Add
insurancePremium.remove=Remove
insurancePremium.approval=Approval
insurancePremium.tabname=InsurancePremium
insurancePremium.discard=Discard
insurancePremium.rebateStatus=Insurance Premium Status
insurancePremium.insurancePremiumInformation=Insurance Premium Configuration Information
insurancePremium.insurancePremiumPendingConfiguration=Pending Insurance Premium  Configuration 
insurancePremium.requestInformation=Insurance Premium Configuration  Information
insurancePremium.requestType=Request Type
insurancePremium.requestDate=Request Date
insurancePremium.requestStatus=Request Status
insurancePremium.requestBy=Request By
insurancePremium.approverComments=Approver Comments
insurancePremium.cardType=Card Type
insurancePremium.cardVariant=Card Variant
insurancePremium.premiumPerCardPerAnnum=Premium Per Card per Annum
insurancePremium.vendor=Vendor
insurancePremium.id=ID
insurancePremium.fromMonthYear=From Date
insurancePremium.toMonthYear=To Date
insurancePremium.Year=Year


#Card Configuration screen labels
cardConfig.editscreen.title=Edit Base Fee Information
cardConfig.addscreen.title=Add New Base Fee Information
cardConfig.viewscreen.title=Base Fee Information
cardConfig.listscreen.title=Base Fee List
cardConfig.approvalPanel.title=Base Fee Approval

cardConfig.mainTab.title=Base Fee
cardConfig.approvalTab.title=Approval

cardConfig.cardConfigId=Card Config ID
cardConfig.cardType=Card Type
cardConfig.cardVariant=Card Variant
cardConfig.baseFee=Base Fee
cardConfig.fromDate=From Date
cardConfig.toDate=To Date
cardConfig.requestState=Request State
cardConfig.status=Status
cardConfig.checkerComents=CheckersComments
cardConfig.year=Year
cardConfig.submitBtn=Submit
cardConfig.backBtn=Back
cardConfig.editBtn=Edit
cardConfig.viewBtn=View
cardConfig.discardBtn=Discard
cardConfig.addCardConfigBtn=Add Base Fee
cardConfig.actionTitle=Action
cardConfig.exportBtn=Excel
cardConfig.approvalStatus=Approval Status
cardConfig.requestState.approved.description=Approved
cardConfig.requestState.pendingApproval.description=Pending for Approval
cardConfig.requestState.rejected.description=Rejected
cardConfig.requestState.discared.description=Discarded
cardConfig.requestInformation=Request Information
cardConfig.requestType=Request Type
cardConfig.requestDate=Request Date
cardConfig.requestStatus=Request Status
cardConfig.requestBy=Request By
cardConfig.approverComments=Approver Comments



#Feature fee screen labels
featureFee.editscreen.title=Edit Feature Fee Information
featureFee.addscreen.title=Add New Feature Fee Information
featureFee.viewscreen.title=Feature Fee Information
featureFee.listscreen.title=Feature Fee List
featureFee.approvalPanel.title=Feature Fee Approval

featureFee.mainTab.title=Feature Fee
featureFee.approvalTab.title=Approval

featureFee.cardConfigId=Card Config ID
featureFee.cardType=Card Type
featureFee.cardVariant=Card Variant
featureFee.featureFee=Feature Fee
featureFee.feature=Feature
featureFee.details=Details
featureFee.fromDate=From Date
featureFee.toDate=To Date
featureFee.requestState=Request State
featureFee.status=Status
featureFee.checkerComents=CheckersComments
featureFee.year=Year
featureFee.submitBtn=Submit
featureFee.backBtn=Back
featureFee.editBtn=Edit
featureFee.viewBtn=View
featureFee.exportBtn=Excel
featureFee.discardBtn=Discard
featureFee.addFeatureFeeBtn=Add Feature Fee
featureFee.actionTitle=Action
featureFee.approvalStatus=Approval Status
featureFee.requestState.approved.description=Approved
featureFee.requestState.pendingApproval.description=Pending for Approval
featureFee.requestState.rejected.description=Rejected
featureFee.requestState.discared.description=Discarded
featureFee.requestInformation=Request Information
featureFee.requestType=Request Type
featureFee.requestDate=Request Date
featureFee.requestStatus=Request Status
featureFee.requestBy=Request By
featureFee.approverComments=Approver Comments


#product feature fee details
productFeature.binProduct=View Bin Specific Base/Product Fee
productFeature.bankProduct=View Bank Specific Base/Product Fee
productFeature.binBankFeature=View Feature Fee
productFeature.quater=Quater
productFeature.year=Year
productFeature.binNo=Bin Number
productFeature.bankName=Bank Name
productFeature.productFee=Product Fee
productFeature.featureFee=Feature Fee
productFeature.featureName=Feature Name
productFeature.totalFee=Total Fee
productFeature.details=Details
productFeature.fee=Fee
productFeature.view=View
productFeature.reset=Reset
productFeature.productFeatureFeeList=Product and Feature Fee Data List
productFeature.productFeatureFeeViewScreen=Product and Feature Fee View Screen
productFeature.clearFiltersBtn=Clear Filters
productFeature.exportBtn=Excel
productFeature.csvBtn=CSV

# mcpr view/edit/delete data
mcprBinDetails.editscreen.title=Edit MCPR Bin Details
mcprBinDetails.deletescreen.title=Delete MCPR Bin Details
mcprBinDetails.viewscreen.title=MCPR Bin Details Information
mcprBinDetails.listscreen.title=MCPR Bin Details
mcprBinDetails.listscreenList.title=MCPR Bin Details List
#mcprBinDetails.approvalPanel.title=Feature Fee Approval
mcprBinDetails.approvalTab.title=Approval

#mcprBinDetails.mainTab.title=Feature Fee

mcprBinDetails.mcprBinDetailsId=MCPR Bin Details ID
mcprBinDetails.monthEnding=Month Ending
mcprBinDetails.binNumber=Bin Number
mcprBinDetails.bankName=Bank Name
mcprBinDetails.month=Month
mcprBinDetails.year=Year

mcprBinDetails.physicalContactCardCumulativeRpay=Physical - Contact Card - Cumulative RuPay cards live in the system and issued till Month ending
mcprBinDetails.physicalContactCardIncremental=Physical - Contact Card - Incremental Cards issued in the month
mcprBinDetails.physicalContactlessCardCumulativeRpay=Physical - Contactless Card - Cumulative RuPay cards live in the system and issued till Month ending
mcprBinDetails.physicalContactlessCardIncremental=Physical - Contactless Card - Incremental Cards issued in the month
mcprBinDetails.virtualCardCumulativeRpay=Virtual Card - Cumulative RuPay cards live in the system and issued till Month ending
mcprBinDetails.virtualCardIncremental=Virtual Card - Incremental Cards issued in the month
mcprBinDetails.ecomTxnOnusCount=e-Comm Transactions (On Us) - Transaction count
mcprBinDetails.ecomTxnOnusAmt=e-Comm Transactions (On Us) - Transaction amount
mcprBinDetails.posContactDomesticTxnCount=PoS (Contact - Card Present) - Domestic Transactions (On Us) - Transaction count
mcprBinDetails.posContactDomesticTxnAmt=PoS (Contact - Card Present) - Domestic Transactions (On Us) - Transaction amount
mcprBinDetails.posContactlessOnlineRetailsDomesticTxnCount=PoS (Contactless - Online - Retails) - Domestic Transactions (OnUs) - Transaction count
mcprBinDetails.posContactlessOnlineRetailsDomesticTxnAmt=PoS (Contactless - Online - Retails) - Domestic Transactions (OnUs) - Transaction amount
mcprBinDetails.posContactlessOnlineTransitDomesticTxnCount=PoS (Contactless - Online - Transit) - Domestic Transactions (OnUs) - Transaction count
mcprBinDetails.posContactlessOnlineTransitDomesticTxnAmt=PoS (Contactless - Online - Transit) - Domestic Transactions (OnUs) - Transaction amount
mcprBinDetails.posContactlessOfflineRetailsDomesticTxnCount=PoS (Contactless - Offline - Retails) - Domestic Transactions (OnUs) - Transaction count
mcprBinDetails.posContactlessOfflineRetailsDomesticTxnAmt=PoS (Contactless - Offline - Retails) - Domestic Transactions (OnUs) - Transaction amount
mcprBinDetails.posContactlessOfflineTransitDomesticTxnCount=PoS (Contactless - Offline - Transit) - Domestic Transactions (OnUs) - Transaction count
mcprBinDetails.posContactlessOfflineTransitDomesticTxnAmt=PoS (Contactless - Offline - Transit) - Domestic Transactions (OnUs) - Transaction amount
mcprBinDetails.atmTxnCount=ATM (Card Present) - Domestic Transactions (On Us) - Transaction count
mcprBinDetails.atmTxnAmt=ATM (Card Present) - Domestic Transactions (On Us) - Transaction amount
mcprBinDetails.totalCumulativeCards=Total Cumulative Cards
mcprBinDetails.totalIncrementalCards=Total Incremental Cards

mcprBinDetails.submitBtn=Submit
mcprBinDetails.searchBtn=Search
mcprBinDetails.backBtn=Back
mcprBinDetails.editBtn=Edit
mcprBinDetails.viewBtn=View 
mcprBinDetails.status=Status
mcprBinDetails.exportBtn=Excel
mcprBinDetails.csvBtn=CSV
mcprBinDetails.clearFiltersBtn=Clear Filters
mcprBinDetails.discardBtn=Discard
#mcprBinDetails.addFeatureFeeBtn=Add Feature Fee
mcprBinDetails.actionTitle=Action
mcprBinDetails.approvalStatus=Approval Status
mcprBinDetails.requestState.approved.description=Approved
mcprBinDetails.requestState.pendingApproval.description=Pending for Approval
mcprBinDetails.requestState.rejected.description=Rejected
mcprBinDetails.requestState.deleted.description=Deleted
mcprBinDetails.requestState.discared.description=Discarded
mcprBinDetails.requestInformation=Request Information
mcprBinDetails.requestType=Request Type
mcprBinDetails.requestDate=Request Date
mcprBinDetails.requestStatus=Request Status
mcprBinDetails.requestBy=Request By
mcprBinDetails.approverComments=Approver Comments
mcprBinDetails.apprejecterrormsg=Please Select Approve/Reject action.
mcprBinDetails.remarkserror=Please Enter Remarks

#Tip Surcharge screen labels
tipSurcharge.editscreen.title=Edit Tip Surcharge Information
tipSurcharge.addscreen.title=Add New Tip Surcharge Information
tipSurcharge.viewscreen.title=Tip Surcharge Information
tipSurcharge.listscreen.title=Tip Surcharge List
tipSurcharge.approvalPanel.title=Tip Surcharge Approval

tipSurcharge.mainTab.title=Tip Surcharge
tipSurcharge.approvalTab.title=Approval

tipSurcharge.tipSurchargeId=Tip Surcharge ID
tipSurcharge.tipSurchargeType=Type
tipSurcharge.operator=Operator
tipSurcharge.settlementAmount=Settlement Amount
tipSurcharge.capFlat=Cap Flat
tipSurcharge.capPercentage=Cap Percentage
tipSurcharge.binCardBrand=Bin Card Brand
tipSurcharge.binCardType=Bin Card Type
tipSurcharge.amountPercentFlag=Amount/Percent
tipSurcharge.percentage=Percent Value (Inclusive GST)
tipSurcharge.amount=Amount Value (Inclusive GST)
tipSurcharge.tipSurchargeName=Tip Surcharge Name
tipSurcharge.requestState=Request State
tipSurcharge.status=Status
tipSurcharge.checkerComents=CheckersComments
tipSurcharge.submitBtn=Submit
tipSurcharge.backBtn=Back
tipSurcharge.editBtn=Edit
tipSurcharge.resetBtn=Reset
tipSurcharge.viewBtn=View
tipSurcharge.clearBtn=Clear
tipSurcharge.exportBtn=Excel
tipSurcharge.csvBtn=CSV
tipSurcharge.discardBtn=Discard
tipSurcharge.addTipSurchargeBtn=Add Tip Surcharge
tipSurcharge.actionTitle=Action
tipSurcharge.approvalStatus=Approval Status
tipSurcharge.requestState.approved.description=Approved
tipSurcharge.requestState.pendingApproval.description=Pending for Approval
tipSurcharge.requestState.rejected.description=Rejected
tipSurcharge.requestState.discared.description=Discarded
tipSurcharge.requestInformation=Request Information
tipSurcharge.requestType=Request Type
tipSurcharge.requestDate=Request Date
tipSurcharge.requestStatus=Request Status
tipSurcharge.requestBy=Request By
tipSurcharge.approverComments=Approver Comments

#MCC Tip Surcharge screen labels
mccTipSurcharge.editscreen.title=Edit MCC Tip Surcharge Information
mccTipSurcharge.addscreen.title=Add New MCC Tip Surcharge Information
mccTipSurcharge.viewscreen.title=MCC Tip Surcharge Information
mccTipSurcharge.listscreen.title=MCC Tip Surcharge List
mccTipSurcharge.approvalPanel.title=MCC Tip Surcharge Approval

mccTipSurcharge.mainTab.title=MCC Tip Surcharge
mccTipSurcharge.approvalTab.title=Approval

mccTipSurcharge.mccTipSurchargeId=MCC Tip Surcharge ID
mccTipSurcharge.tipSurchargeId=Tip/Surcharge Name
mccTipSurcharge.mccId=MCC Name
mccTipSurcharge.requestState=Request State
mccTipSurcharge.status=Status
mccTipSurcharge.checkerComents=CheckersComments
mccTipSurcharge.submitBtn=Submit
mccTipSurcharge.backBtn=Back
mccTipSurcharge.editBtn=Edit
mccTipSurcharge.resetBtn=Reset
mccTipSurcharge.viewBtn=View
mccTipSurcharge.clearBtn=Clear
mccTipSurcharge.exportBtn=Excel
mccTipSurcharge.csvBtn=CSV
mccTipSurcharge.discardBtn=Discard
mccTipSurcharge.addMccTipSurchargeBtn=Add MCC Tip Surcharge
mccTipSurcharge.actionTitle=Action
mccTipSurcharge.approvalStatus=Approval Status
mccTipSurcharge.requestState.approved.description=Approved
mccTipSurcharge.requestState.pendingApproval.description=Pending for Approval
mccTipSurcharge.requestState.rejected.description=Rejected
mccTipSurcharge.requestState.discared.description=Discarded
mccTipSurcharge.requestInformation=Request Information
mccTipSurcharge.requestType=Request Type
mccTipSurcharge.requestDate=Request Date
mccTipSurcharge.requestStatus=Request Status
mccTipSurcharge.requestBy=Request By
mccTipSurcharge.approverComments=Approver Comments

#Currency Master screen labels
currencyMaster.editscreen.title=Edit Currency Master Information
currencyMaster.addscreen.title=Add New Currency Master Information
currencyMaster.viewscreen.title=Currency Master Information
currencyMaster.listscreen.title=Currency Master List
currencyMaster.approvalPanel.title=Currency Master Approval

currencyMaster.mainTab.title=Currency Master
currencyMaster.approvalTab.title=Approval

currencyMaster.currencyId=Currency Master ID
currencyMaster.currencyMasterCode=Currency Master Code
currencyMaster.currencyMasterDescription=Currency Master Description
currencyMaster.currencyAlpha=Currency Alpha
currencyMaster.currencyDecimalPosition=Currency Decimal Position
currencyMaster.requestState=Request State
currencyMaster.status=Status
currencyMaster.checkerComents=CheckersComments
currencyMaster.submitBtn=Submit
currencyMaster.backBtn=Back
currencyMaster.editBtn=Edit
currencyMaster.resetBtn=Reset
currencyMaster.viewBtn=View
currencyMaster.clearBtn=Clear
currencyMaster.exportBtn=Excel
currencyMaster.csvBtn=CSV
currencyMaster.discardBtn=Discard
currencyMaster.addCurrencyMasterBtn=Add Currency Master
currencyMaster.actionTitle=Action
currencyMaster.approvalStatus=Approval Status
currencyMaster.requestState.approved.description=Approved
currencyMaster.requestState.pendingApproval.description=Pending for Approval
currencyMaster.requestState.rejected.description=Rejected
currencyMaster.requestState.discared.description=Discarded
currencyMaster.requestInformation=Request Information
currencyMaster.requestType=Request Type
currencyMaster.requestDate=Request Date
currencyMaster.requestStatus=Request Status
currencyMaster.requestBy=Request By
currencyMaster.approverComments=Approver Comments

#Function Code screen labels
functionCode.editscreen.title=Edit Function Code Information
functionCode.addscreen.title=Add New Function Code Information
functionCode.viewscreen.title=Function Code Information
functionCode.listscreen.title=Function Code List
functionCode.approvalPanel.title=Function Code Approval

functionCode.mainTab.title=Function Code
functionCode.approvalTab.title=Approval

functionCode.functionCodeId=Function Code ID
functionCode.mti=mti
functionCode.procCode=Proc Code
functionCode.funcCode=Func Code
functionCode.funcCodeDesc=Func Code Desc
functionCode.feeType=Fee Type
functionCode.fundMovement=Fund Movement
functionCode.fundMovementSide=Fund Movement Side
functionCode.recalculate=Recalculate
functionCode.transactionType=Transaction Type
functionCode.networkTxnType=Network Txn Type

functionCode.requestState=Request State
functionCode.status=Status
functionCode.checkerComents=CheckersComments
functionCode.submitBtn=Submit
functionCode.backBtn=Back
functionCode.editBtn=Edit
functionCode.resetBtn=Reset
functionCode.viewBtn=View
functionCode.clearBtn=Clear
functionCode.exportBtn=Excel
functionCode.csvBtn=CSV
functionCode.discardBtn=Discard
functionCode.addFunctionCodeBtn=Add Function Code
functionCode.actionTitle=Action
functionCode.approvalStatus=Approval Status
functionCode.requestState.approved.description=Approved
functionCode.requestState.pendingApproval.description=Pending for Approval
functionCode.requestState.rejected.description=Rejected
functionCode.requestState.discared.description=Discarded
functionCode.requestInformation=Request Information
functionCode.requestType=Request Type
functionCode.requestDate=Request Date
functionCode.requestStatus=Request Status
functionCode.requestBy=Request By
functionCode.approverComments=Approver Comments

#BinExclusion Configuration screen labels
binexcl.editscreen.title=Edit Bin Exclusion Information
binexcl.addscreen.title=Add New Bin Exclusion Information
binexcl.viewscreen.title=Bin Exclusion Information
binexcl.listscreen.title=Bin Exclusion List
binexcl.approvalPanel.title=Approval
binexcl.approvalPendingViewScreen.title=Pending Bin Exclusion 
binexcl.requestInfo=Request Information
binexcl.mainTab.title=Bin Exclusion
binexcl.approvalTab.title=Approval
binexcl.baseorfeature=Base/Feature
binexcl.participantName=Participant Name
binexcl.bin=Bin
binexcl.fromDate=From Date
binexcl.toDate=To Date
binexcl.year=Year
binexcl.excId=Exclusion Id
binexcl.apprejecterrormsg=Please Select Approve/Reject action.
binexcl.remarkserror=Please Enter Remarks
binexcl.status=Status
binexcl.submitBtn=Submit
binexcl.backBtn=Back
binexcl.editBtn=Edit
binexcl.viewBtn=View
binexcl.discardBtn=Discard
binexcl.addBinExclusionBtn=Add Bin Exclusion
binexcl.actionTitle=Action
binexcl.approvalStatus=Approval Status
binexcl.activeStatus=Active
binexcl.inactiveStatus=Inactive
binexcl.requestState.approved.description=Approved
binexcl.requestState.pendingApproval.description=Pending for Approval
binexcl.requestState.rejected.description=Rejected
binexcl.requestState.discared.description=Discarded
binexcl.requestType=Request Type
binexcl.requestDate=Request Date
binexcl.requestStatus=Request Status
binexcl.requestBy=Request By
binexcl.approverComments=Approver Comments
binexcl.checkerComments=Checker Comments


#budget 
budget.requestState.pendingApproval.description=Pending for Approval
budget.requestState.rejected.description=Rejected
budget.vendor=Vendor
budget.budgetName=Budget
budget.year=Financial Year
budget.budgetId=Budget Id
budget.addscreen.title=Add New Budget Information
budget.editscreen.title=Edit New Budget Information
budget.viewscreen.title=Budget Information
budget.submitBtn=Submit
budget.backBtn=Back
budget.editBtn=Edit
budget.viewBtn=View
budget.discardBtn=Discard
budget.requestType=Request Type
budget.requestDate=Request Date
budget.requestStatus=Request Status
budget.requestBy=Request By
budget.approverComments=Approver Comments
budget.requestInfo=Request Information
budget.apprejecterrormsg=Please Select Approve/Reject action.
budget.remarkserror=Please Enter Remarks
budget.approvalPendingViewScreen.title=Pending Budget
budget.actionTitle=Action



am.lbl.participantId =Participant Name
am.lbl.accessLevel = Access Level
addEditUser.participantName.required =	Participant Name is  required
addEditUser.accessLevel.required =Access Level is  required
sm.lbl.accessLevel= Access Level
sm.lbl.binFlags=Bin Details
msg.lbl.addfeeConfig= Add FeeMap
feeMajor.config.screen=Fee Config Details	
feeRate.issAuthFees=Iss Auth Fees
feeRate.acqAuthFees=Acq Auth Fees
am.lbl.feeMinorList=Fee Minor List
am.lbl.reset=Reset
am.lbl.feeMajorMapping= FeeMajor Mapping
am.lbl.feeMajorMinorMapping= Minor Priority Mapping Details

#Projection Entry and Premium Calculation Card Variant wise

projectionEntry.editscreen.title=Add New Card Projection Information
projectionEntry.addscreen.title=Edit New Card Projection Information
projectionEntry.viewscreen.title=Projection Entry Information
projectionEntry.approvalPendingViewScreen.title= Pending Projection Entry
projectionEntry.mainTab.title=Projection Entry
projectionEntry.approvalTab.title=Approval
projectionEntry.addProjectionEntryBtn=Add Card Projection 
projectionEntry.listscreen.title= Projection Entry List
projectionEntry.cardProjectionId=Card Projection Id
projectionEntry.cardType=Card Type
projectionEntry.month=Month
projectionEntry.cardVariant=Card Variant
projectionEntry.totalCards=Total No. Of Rupay Cards Projected
projectionEntry.actionTitle=Action
projectionEntry.submitBtn=Submit
projectionEntry.backBtn=Back
projectionEntry.editBtn=Edit
projectionEntry.viewBtn=View
projectionEntry.requestState.approved.description=Approved
projectionEntry.requestState.pendingApproval.description=Pending for Approval
projectionEntry=Rejected
projectionEntry.requestState=Request State
projectionEntry.requestStatus=Request Status
projectionEntry.requestState.discared.description=Discarded
projectionEntry.requestType=Request Type
projectionEntry.status=Status
projectionEntry.approverComments=Approver Comments
projectionEntry.requestInfo=Request Information
projectionEntry.requestDate=Request Date
projectionEntry.requestBy=Request By
projectionEntry.requestState.rejected.description=Rejected
projectionEntry.requestState.discarded.description=Discarded
projectionEntry.discardBtn=Discard
binexcl.reset=Reset


projectionEntry.totalPremium=Total Premium
projectionEntry.currencyCode=INR
premiumCal.premiumTitle=Net Premium Payable/Receivable
premiumCal.premiumTitleList=Net Premium Payable/Receivable List
premiumCal.year=Year
premiumCal.month=Month
projectionEntry.totalPremiumExclGST=Total Premium Excl GST

historical.premiumCal.variant=View Variant Specific Insurance Payment
historical.premiumCal.vendor=View Net Insurance Payment
historical.premiumCal.adjust=View Net Adjust Payment

am.lbl.search=Search
member.listscreen.title= Member Directory
am.lbl.participantId=Participant ID
am.lbl.settlementBin=Settlement Bin
am.lbl.binId=BIN / ACQUIRER ID
am.lbl.memberBankName=Member Bank Name
am.lbl.memberType=Member Type
am.lbl.memberName= Participant Name
am.lbl.ifscCode= Ifsc Code
 am.lbl.bankSector=Bank Sector
 am.lbl.bankMasterCode= Master Code
 am.lbl.rtgsCode= Rtgs Code
 am.lbl.participantIdCode=Participant Code
 am.lbl.savingsAccNumber= Savings Account
 am.lbl.currentAccNumber= Current Account
 am.lbl.uniqueBnkName= Unique Bank
 am.lbl.participantIdNFS= NFS ParticipantId
 am.lbl.bnkPhone=Phone Number1
 am.lbl.bnkPhone2=Phone Number2
 am.lbl.bnkMobile= Mobile Number
 am.lbl.bnkMobile2= Mobile Number2
 am.lbl.bnkEmail=Email1
 am.lbl.bnkEmail2=Email Address2
 am.lbl.bnkAdd= Legal Address
 am.lbl.bankCountry=Country
 am.lbl.bankState=State
 am.lbl.bankCity=City	
 am.lbl.bnkPincode=Zip Code
 am.lbl.gstIn=GSTIN 
 am.lbl.gstAdd=Gst Address
 am.lbl.gstCntry=Gst Country
 am.lbl.gstSt=Gst State
 am.lbl.gstCty=Gst City
 am.lbl.gstPincode=Gst pincode
 am.lbl.webSite=Website
 am.lbl.addressType=Address Type
 am.lbl.contactname=Contact Person Name
 am.lbl.cntPhone=Phone Number
 am.lbl.cntMobile=Mobile Number
 am.lbl.cntFax=Fax
 am.lbl.cntAdd1= Legal Address
 am.lbl.contactCountry=Country Name
 am.lbl.contactState= State Name
 am.lbl.contactCity=City Name
 am.lbl.cntPincode=Zip Code
 am.lbl.cntEmail=Email
 am.lbl.cntDesignation=Authorized Officer Designation


mcc.mccGroup=MCC Group
mcc.mccCode= Business Code
mcc.mccDesc=MCC Description
mcc.status=Status
mcc.mccId=MCC Id
mcc.createdBy=Created By User


mcc.addscreen.title=Add New MCC Information
mcc.editscreen.title=Edit New MCC Information
mcc.viewscreen.title=MCC Information
mcc.approvalPendingViewScreen.title=Pending MCC
mcc.mainTab.title=MCC
mcc.listscreen.title=MCC List
mcc.status.enable.description=Enable
mcc.status.disable.description=Disable
mcc.approvalTab.title=MCC List

feeRate.revCashFeeMax= Net Max
feeRate.revCashFeeMin= Net Min
feeRate.netMax= Net Fee Max
feeRate.netMin= Net Fee Min 
feeRate.feeTypCode= Fee Type Code


am.lbl.state=State
am.lbl.name=Name
am.lbl.designation=Designation
am.lbl.email=Email ID
am.lbl.mobile=Mobile No
am.lbl.landline=Landline Number
am.lbl.address=Legal Address
am.lbl.zipcode=Zip Code


escalation.mainTab.title= Add Edit Escalation
am.lbl.escalation=Escalation List
am.lbl.memberId= Member Id
histpremiumCal.premiumTitle = Historical Premium Calculation


reasonCode.addscreen.title=Add New Reason Code
reasonCode.editscreen.title=Edit New Reason Code Information
reasonCode.viewscreen.title=Reason Code Information
reasonCode.approvalPendingViewScreen.title=Pending Reason Code
reasonCode.mainTab.title=Reason Code
reasonCode.mccCode=Code
reasonCode.mccDesc=Description
reasonCode.status=Status
reasonCode.status.enable.description=Enable
reasonCode.status.disable.description=Disable
reasonCode.approvalTab.title=Reason Code




jsonValidator.addscreen.title=Add New Json Validator
jsonValidator.editscreen.title=Edit New Json Validator Information
jsonValidator.viewscreen.title=Json Validator Information
jsonValidator.approvalPendingViewScreen.title=Pending Json Validator
jsonValidator.mainTab.title=Json Validator
jsonValidator.mccCode=Code
jsonValidator.mccDesc=Description
jsonValidator.status=Status
jsonValidator.status.enable.description=Enable
jsonValidator.status.disable.description=Disable
jsonValidator.approvalTab.title=Json Validator


am.lbl.rejectedDate = Rejected Date

reportProgressStatus.title = Report Progress Status
reportProgressStatus.cycleDate = Cycle Date
reportProgressStatus.cycleNumber = Cycle Number
reportProgressStatus.bankType = Bank Type
reportProgressStatus.reportStatus = Status
reportCycleStatus.title = Report Cycle Status
am.lbl.refresh=Refresh
reportInternalCycleStatus.title = Internal Cycle Status
am.lbl.cycleNumber = Cycle Number
am.lbl.status = Status
am.lbl.submit = Submit
cycleStatusUpdate.title = Update Cycle Status
am.lbl.confirm = Confirm

reprocessIntCyc.hdr.confirmation = Confirmation
reprocessIntCyc.btn.submit = Submit
reprocessIntCyc.btn.close = Close


#action code
actioncode.MainTab.title= Action Code
msg.lbl.actionCodeList= Action Code List
action.addActionCodeBtn= Add Action Code
am.lbl.mti= MTI
am.lbl.actionCode= Action Code
am.lbl.actionCodeDesc= Action Code Desc
am.lbl.funcCodeDesc= Function Code Desc
am.lbl.raisedBy= Raised By
action.acq= Acquirer
action.iss= Issuer
am.lbl.tatPeriod=TAT PERIOD
am.lbl.tatPeriodDayType=TAT Period DayType
am.lbl.transitionActionCode=Transition Action Code
am.lbl.allowedActncdToRemove=Allowed Action Code To Remove
st.lbl.capAmtCalReq=CAP AMT CAL
am.lbl.defaultReasonRejCode=Default Reason RejCode

disputeFeeRule.mainTab.title = Dispute Fee Rule
disputeFeeRule.viewscreen.title= Dispute Fee Rule Information
disputeFeeRules.actionCode = Action Code
disputeFeeRules.feeType = Fee Type
disputeFeeRules.feeCode = Fee Code
disputeFeeRules.fieldName = Field Name
disputeFeeRules.fieldName1 = Field Name 1
disputeFeeRules.fieldName2 = Field Name 2
disputeFeeRules.relationalOperator = Relational Operator
disputeFeeRules.status = Status
disputeFeeRules.action=Action
disputeFeeRules.priority = Priority
disputeFeeRules.logicalFeeCode = Logical Fee Code
disputeFeeRules.fieldValue = Field Value
disputeFeeRules.fieldOperator = Field Operator
disputeFeeRules.createdBy = Created By
disputeFeeRules.createdOn = Created On
disputeFeeRules.checkerComments = Checker Comments
disputeFeeRule.addscreen.title=Add Dispute Fee Rule
disputeFeeRule.editscreen.title=Edit Dispute Fee Rule Information
disputeFeeRule.approvalPendingViewScreen.title=Pending Dispute Fee Rule
disputeFeeRule.lessThan = LESS THAN
disputeFeeRule.lessThanEqual = LESS THAN EQUAL TO
disputeFeeRule.greaterThanEqual = GREATER THAN
disputeFeeRule.greaterThanEqual = GREATER THAN EQUAL
disputeFeeRule.equal = EQUAL
disputeFeeRule.noOperator = NO OPERATOR
disputeFeeRule.dateDifference = DATE DIFFERENCE
dispute.transition.addBtn = Add 
#Reason code Rules
reasonCodeRules.addBtn = Add
reasonCodeRules.addscreen.title=Add New Reason Code Rules
reasonCodeRules.editscreen.title=Edit New Reason Code Rules Information
reasonCodeRules.viewscreen.title=Reason Code Rules Information
reasonCodeRules.approvalPendingViewScreen.title=Pending Reason Code Rules
reasonCodeRules.mainTab.title=Reason Code Rules
reasonCode.actionCode=Action Code
reasonCode.reasonCode=Reason Code
reasonCode.fieldName=Field Name
reasonCode.relationOperator=Relational Operator
reasonCode.fieldOperator=Field Operator
reasonCode.fieldValue=Field Value
reasonCode.logicalReasonCode=Logical Reason Code	
	
fileUpload.selectFile= Select File
fileUpload.uploadFile= Upload File
fileUpload.reset= Reset
fileUpload.samplefileName=Sample File Name
fileUpload.download=Download
fileUpload.searchFile=Search File
fileUpload.fielUploadDetails=File Upload Details
fileUpload.exportBtn=Export
fileUpload.csvBtn=Csv
fileUpload.refresh=Refresh	

fileUpload.view.documentName=Document Name
fileUpload.view.participantId=Participant Id
fileUpload.view.totalNoTrans=Transaction Count
fileUpload.view.totalAmount=Total Amount
fileUpload.view.rejectedNoTrans=Rejected Transaction Count
fileUpload.view.fullFileRejected=Full File Rejected
fileUpload.view.status=Status
fileUpload.view.insertDate=Insert Date
fileUpload.view.seqNo=SNo.
fileUpload.view.recordNo=Record Number
fileUpload.view.fieldName=Field Name
fileUpload.view.rejectReason=Reject Reason
fileUpload.view.rejectCode=Reject Code
fileUpload.view.rejectXml=Reject Xml
fileUpload.view.stageSuccess=File staged successfully. Please refresh the page to check status.
fileUpload.view.DiscardSuccess=File Discarded successfully. Please refresh the page to check status.

#Bin Feature Mapping screen labels
binFeatureMapping.editscreen.title=Edit Bin Feature Mapping Information
binFeatureMapping.addscreen.title=Add New Bin Feature Mapping Information
binFeatureMapping.viewscreen.title=Bin Feature Mapping Information
binFeatureMapping.listscreen.title=Bin Feature Mapping List
binFeatureMapping.approvalPanel.title=Approval
binFeatureMapping.approvalPendingViewScreen.title=Pending Bin Feature Mapping 
binFeatureMapping.requestInfo=Request Information
binFeatureMapping.mainTab.title=Bin Feature Mapping
binFeatureMapping.approvalTab.title=Approval
binFeatureMapping.baseorfeature=Feature
binFeatureMapping.participantName=Participant Name
binFeatureMapping.bin=Bin
binFeatureMapping.fromDate=From Date
binFeatureMapping.toDate=To Date
binFeatureMapping.year=Year
binFeatureMapping.binFeatureId=Bin Feature Id
binFeatureMapping.apprejecterrormsg=Please Select Approve/Reject action.
binFeatureMapping.remarkserror=Please Enter Remarks
binFeatureMapping.status=Status
binFeatureMapping.submitBtn=Submit
binFeatureMapping.backBtn=Back
binFeatureMapping.editBtn=Edit
binFeatureMapping.viewBtn=View
binFeatureMapping.discardBtn=Discard
binFeatureMapping.addBinExclusionBtn=Add Bin Feature Mapping
binFeatureMapping.actionTitle=Action
binFeatureMapping.approvalStatus=Approval Status
binFeatureMapping.activeStatus=Active
binFeatureMapping.inactiveStatus=Inactive
binFeatureMapping.requestState.approved.description=Approved
binFeatureMapping.requestState.pendingApproval.description=Pending for Approval
binFeatureMapping.requestState.rejected.description=Rejected
binFeatureMapping.requestState.discared.description=Discarded
binFeatureMapping.requestType=Request Type
binFeatureMapping.requestDate=Request Date
binFeatureMapping.requestStatus=Request Status
binFeatureMapping.requestBy=Request By
binFeatureMapping.approverComments=Approver Comments
binFeatureMapping.checkerComments=Checker Comments
binFeatureMapping.reset=Reset

#NFC/NFD
npciFundCollectDisburse.addscreen.title=NPCI Fee Collection and Disbursement
npciFundCollectDisburse.select.action = Select Action
npciFundCollectDisburse.FundCollection = NPCI Fund Collection
npciFundCollectDisburse.FundDisburse = NPCI Fund Disbursement
npciFundCollectDisburse.TxnOrgInstIdCd = Tran Originator Inst ID Code
npciFundCollectDisburse.TxnDestId = Transaction Destination ID
npciFundCollectDisburse.npciMsgTxt = NPCI Message Text
npciFundCollectDisburse.binType = Bin Type
npciFundCollectDisburse.feeCurr = Fee Currency
npciFundCollectDisburse.defaultIssID = Default Issuing ID
npciFundCollectDisburse.amount = Amount
npciFundCollectDisburse.defaultAcqID = Default Acquiring ID
npciFundCollectDisburse.feeCategoryCd = Fee Category Code
npciFundCollectDisburse.settlementBin =  Settlement Bin
npciFundCollectDisburse.feeName = Fee Names
npciFundCollectDisburse.issuer = Issuer
npciFundCollectDisburse.acquirer = Acquirer
npciFundCollectDisburse.feeCurr = Indian Rupee

disputeFeeRules.logicalOperator = Logical Operator
disputeFeeRule.approvalTab.title=Dispute Fee Rule

rejectReasonCodeRules.addscreen.title=Add New Reject Reason Code Rules
rejectReasonCodeRules.editscreen.title=Edit New Reject Reason Code Rules Information
rejectReasonCodeRules.viewscreen.title=Reject Reason Code Rules Information
rejectReasonCodeRules.approvalPendingViewScreen.title=Pending Reject Reason Code Rules
rejectReasonCodeRules.mainTab.title=Reject Reason Code Rules
rejectReasonCodeRules.mccCode=Code
rejectReasonCodeRules.mccDesc=Description
rejectReasonCodeRules.status=Status
rejectReasonCodeRules.status.enable.description=Enable
rejectReasonCodeRules.status.disable.description=Disable
rejectReasonCodeRules.approvalTab.title=Reject Reason Code

dispute.transition.mainTab.title=Transition Rule
dispute.transition.approvalTab.title=Approval
dispute.transition.add=Add Transition Rule
dispute.transition.list=Dispute Transition Rule List
dispute.transition.edit=Edit Transition Rule
dispute.transition.view=View Transition Rule
dispute.transition.approve=Approve/Reject Transition Rule

dispute.transition.entity=Entity Type
dispute.transition.currState=Current State
dispute.transition.toState=To State
dispute.transition.fieldName= Field Name 1
dispute.transition.secFieldName= Field Name 2
dispute.transition.relationalOp=Relational Operator
dispute.transition.fieldVal= Value
dispute.transition.fieldOp= Operator
dispute.transition.operator= Logical Operator
dispute.transition.submitBtn = Submit
dispute.transition.addBtn = Add
dispute.transition.clearBtn = Reset
dispute.transition.action=Action
dispute.transition.editBtn = Edit
dispute.transition.discardBtn = Discard
dispute.transition.label.approve = Approve
dispute.transition.label.reject = Reject
dispute.transition.label.select = Select
dispute.transition.remarks = Remarks
dispute.transition.createdBy=Created By
dispute.transition.createdOn=Created On
dispute.transition.lastUpdatedBy=Last Updated By
dispute.transition.lastUpdatedOn=Last Updated On
dispute.transition.checkerComment=Remarks
dispute.transition.state = Status
dispute.transition.apprejecterrormsg=Please Select Approve/Reject action.
dispute.transition.remarkserror=Please Enter Remarks
reportProgressStatus.productId = Product Id

cashback.fileUpload.title = Upload CashBack File
cashback.fileUpload.year = Year
cashback.fileUpload.month = Month
cashback.fileUpload.searchTitle = Uploaded Files
cashback.fileUpload.refresh = Refresh
reportRegenAndRecal.title = Reports Regeneration
reportRegenAndRecal.reportType = Report Type
reportRegenAndRecal.requestType = Request Type
reportRegenerationStatus.title = Reports Regeneration Status
am.lbl.regenerationStatus= Regeneration Status
reportRegenerationStatus.requestDate = Request Date

txnSearch.pendingTab.title=Pending
txnSearch.approveRejectedIP.title=Approve/Rejected/InProgress
txnSearch.Approve=Approve
txnSearch.Reject=Reject
txnSearch.Refresh=Refresh
am.lbl.reportType = Report Type
reportstatus.listscreen.title = Report Status
am.lbl.cycleDate= Cycle Date
reportType.validation.msg = Please select Report Type
productCode.validation.msg = Please select Product Code
cycleNumber.validation.msg= Please select Cycle Number
cycleDate.validation.msg= Please select Cycle Date
reportRecalculation.title = Reports Recalculation


#holiday master screen labels
holidayMaster.mainTab.title=Holiday Master
holidayMaster.approvalTab.title=Approval
holidayMaster.addHolidayMasterBtn=Add Holiday Master
holidayMaster.listscreen.title=Holiday Master List
holidayMaster.approve=Approve
holidayMaster.reject = Reject
holidayMaster.holidaySeqId=Holiday Seq Id
holidayMaster.holidayDate=Holiday Date
holidayMaster.holidayDesc=Holiday Description
holidayMaster.periodType=Period Type
holidayMaster.product=Product
holidayMaster.dayOfTheWeek=Day of the week
holidayMaster.weekType=Week Type	
holidayMaster.createdDate=Request Date
holidayMaster.status=Status
holidayMaster.createdBy=Created By
holidayMaster.createdOn=Created On
holidayMaster.lastUpdatedBy=lastUpdatedBy
holidayMaster.lastUpdatedOn=lastUpdatedOn
holidayMaster.requestState=requestState
holidayMaster.checkerComments=Checker Comments
holidayMaster.lastOperation=Last Operation
holidayMaster.approvalStatus=Approval Status
holidayMaster.status=Status
holidayMaster.requestState.approved.description=Approved
holidayMaster.requestState.pendingApproval.description=Pending for Approval
holidayMaster.requestState.rejected.description=Rejected
holidayMaster.requestState.discared.description=Discarded
holidayMaster.activeStatus=Active
holidayMaster.inactiveStatus=Inactive
reportRecalculation.title = Reports Recalculation
outgoingFileForceClose.title = Outgoing Files Force Close
reasonCode.reasonCodeSubType=Reason Code SubType
reasonCode.reasonCodeSubTypeDesc=Reason Code SubType Description
reasonCode.reasonType = Reason Type
crossSiteICStatus.title = Cross Site IC status



##MoveReport

moveReport.search=REPORT SEARCH
moveReport.toDate=To Date
moveReport.fromDate=From Date
moveReport.title=MOVE REPORTS
moveReport.reportType=Report Type
moveReport.requestId=Request Id
moveReport.requestTime=Request Time
moveReport.year=Year
moveReport.month=Month
moveReport.reportNetwork=Report Network
moveReport.submitBtn=Submit
moveReport.clearBtn=Clear
moveReport.backBtn=Back
moveReport.addBtn=Upload Report and Signment
moveReport.status=Status
moveReport.searchBtn=Report Search


##SysParam
sysParam.main.title=Sys Param
sysParam.viewscreen.title=Sys Param Information
sysParam.requestInformation=Request Information
sysParam.requestType=Request Type
sysParam.status=Status
sysParam.requestDate=Request Date

sysParam.requestState.approved.description = Approved 
sysParam.requestState.pendingApproval.description=Pending for Approval
sysParam.requestState.rejected.description=Rejected
sysParam.requestState.discared.description=Discarded
sysParam.requestBy=Request By
sysParam.approverComments= Approver Comments
sysParam.approvalPanel.title=Sys Param Approval
sysParam.resetBtn=Reset
sysParam.submitBtn=Save
sysParam.backBtn=Back

sm.lbl.sysType=Type
sm.lbl.sysKey = Key
sm.lbl.sysValue = Value
sm.lbl.desc = Description
sm.lbl.addProp = Property
sm.lbl.createdBy = Created By
sm.lbl.lastUpdatedBy = Last Updated By
sm.lbl.createdOn = Created On
sm.lbl.lastUpdatedOn = Last Updated On
sm.lbl.checkersComment = Checker's Comment
sm.lbl.requestState = Req. State
sm.lbl.status = Status
sm.lbl.lastOperation = Last Operation
sm.lbl.sysParamMain=Sys Param Config
sm.lbl.addSysParam = Add System Parameter
sm.lbl.sysParamsList = System Parameter Information
sysParams.viewscreen.title = System Parameter View Screen
sm.lbl.requestDate= Date
sm.lbl.requestType= Request Type
sm.lbl.requestBy= Request By
sm.lbl.remarks= Remarks
sm.lbl.approveReject= Approve/Reject
sm.lbl.approve= Approve
sm.lbl.select= Select

