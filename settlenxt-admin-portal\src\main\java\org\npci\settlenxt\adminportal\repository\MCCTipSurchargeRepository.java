package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.adminportal.dto.MCCTipSurchargeDTO;

@Mapper
public interface MCCTipSurchargeRepository {

	 List<MCCTipSurchargeDTO> getTipSurchargeListMain();

	 List<MCCTipSurchargeDTO> getTipSurchargePendingForApproval();

	 MCCTipSurchargeDTO getMccTipSurchargeProfileMain(int mccTipSurchargeId);

	 MCCTipSurchargeDTO getMccTipSurchargeStgInfoById(int mccTipSurchargeId);

	 MCCTipSurchargeDTO getMccTipSurchargeMain(int mccTipSurchargeId);

	 MCCTipSurchargeDTO getMccTipSurchargeStg(int mccTipSurchargeId);

	 int fetchMccTipSurchargeIdSequence();

	 void insertMccTipSurchargeStg(MCCTipSurchargeDTO mccTipSurchargeDto);

	 void insertMccTipSurchargeMain(MCCTipSurchargeDTO mccTipSurchargeDto);

	 void updateMccTipSurchargeMain(MCCTipSurchargeDTO mccTipSurchargeDto);

	 void updateMccTipSurcharge(MCCTipSurchargeDTO mccTipSurchargeDto);

	 void updateMccTipSurchargeDiscard(MCCTipSurchargeDTO mccTipSurchargeDto);

	 void updateMccTipSurchargeRequestState(MCCTipSurchargeDTO mccTipSurchargeDto);

	 void deleteDiscardedEntry(MCCTipSurchargeDTO mccTipSurchargeDto);

	 int validateDuplicateCheck(MCCTipSurchargeDTO mccTipSurchargeDto);
}
