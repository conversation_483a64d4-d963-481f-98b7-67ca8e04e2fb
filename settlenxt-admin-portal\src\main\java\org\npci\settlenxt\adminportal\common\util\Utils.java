package org.npci.settlenxt.adminportal.common.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.model.NetworkOutgoingModel;

import static org.npci.settlenxt.adminportal.common.util.CommonConstants.YYYY_MM_DD_HH_MM_SS_PATTERN;
import static org.npci.settlenxt.adminportal.common.util.CommonConstants.YYYY_MM_DD_PATTERN;


public final class Utils {
	
	private static final Logger log = LogManager.getLogger(Utils.class);
	private static final String REGEX_NUMBER = "[0-9-]*";
    private static final String REGEX_NUMBER_DOT="[0-9-.]*";
	private Utils () {
		log.info("Inside Utils private constructor");
	}
	
	public static int parseInt(String s) {
		if (StringUtils.isBlank(s)) {
			return 0;
		}
		return s.matches(REGEX_NUMBER)? Integer.parseInt(s) : 0;
	}

	public static boolean isNumeric(String s) {
		if (StringUtils.isBlank(s)) {
			return false;
		}
		return s.matches(REGEX_NUMBER_DOT);
	}

	public static String[] getAttributeNamesForNetworkOutGoingModel() {
		return new String[]{
				"Function Code", "Transaction ID", "Batch Number", "Sequence Number",
				"Card Number", "Charge Date", "RRN", "Transaction Amount", "Status",
				"File Generation Date", "Reference Number", "Net Charge Amount",
				"Gross Settlement Amount", "Net Settlement Amount", "Interchange Commission Settlement Amount",
				"Network Reference ID", "MCC Code", "Field Name", "Field Value",
				"Suspension Code", "Suspension Message", "Rejection Code", "Rejection Message",
				"Is Multi-leg transaction", "Outgoing File Name", "Network",
				"Created On", "Last Updated On"
		};
	}

	public static String[] getAttributeValues(NetworkOutgoingModel networkOutgoingModel) {
		return new String[]{
				networkOutgoingModel.getFuncCode(), "'" + networkOutgoingModel.getTranId(),
				networkOutgoingModel.getBatchNum(), networkOutgoingModel.getSeqNum(),
				networkOutgoingModel.getEncryptedPanNum(), networkOutgoingModel.getChargeDate().toString(),
				networkOutgoingModel.getRrn(), String.valueOf(networkOutgoingModel.getTransactionAmt()),
				networkOutgoingModel.getStatus(), networkOutgoingModel.getFileGenDate().format(DateTimeFormatter.ofPattern(YYYY_MM_DD_PATTERN)),
				networkOutgoingModel.getRefNum(), String.valueOf(networkOutgoingModel.getNetChargeAmt()),
				String.valueOf(networkOutgoingModel.getGrossSettlAmt()), String.valueOf(networkOutgoingModel.getNetSettlAmt()),
				String.valueOf(networkOutgoingModel.getInterchangeCommissionSettlAmt()), networkOutgoingModel.getNetworkRefId(),
				networkOutgoingModel.getMccCode(), networkOutgoingModel.getFieldName(),
				networkOutgoingModel.getFieldValue(), networkOutgoingModel.getSuspensionCode(),
				networkOutgoingModel.getSuspensionMessage(), networkOutgoingModel.getRejectionCode(),
				networkOutgoingModel.getRejectionMessage(), networkOutgoingModel.getIsMultilegTxn(),
				networkOutgoingModel.getOutgoingFileName(), networkOutgoingModel.getNetwork(),
				String.valueOf(networkOutgoingModel.getCreatedOn()), String.valueOf(networkOutgoingModel.getLastUpdatedOn())
		};
	}

	public static List<String> generateTableNamesInRange(String tableName, LocalDate fromDate, LocalDate toDate) {
		List<String> tableNames = new ArrayList<>();
		LocalDate current = fromDate.withDayOfMonth(1);
		while (current.isBefore(toDate.plusMonths(1).withDayOfMonth(1))) {
			tableNames.add(String.format("%s_%02d%02d", tableName, current.getMonthValue(), current.getYear() % 100));
			current = current.plusMonths(1);
		}
		return tableNames;
	}



	public static String generateFileName(String fromDate, String toDate, String networkType, String status){
		String fileName = String.join(CommonConstants.NETWORK_FILE_NAME_SEPARATOR, networkType, fromDate, toDate);
		if (status != null) {
			fileName += CommonConstants.NETWORK_FILE_NAME_SEPARATOR + status;
		}
		return fileName;
	}

	public static LocalDateTime convertFromDateToLocalDateTime(String fromDate){
		return LocalDateTime.parse(fromDate + " 00:00:00", DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS_PATTERN));
	}

	public static LocalDateTime convertToDateToLocalDateTime(String toDate){
		return LocalDateTime.parse(toDate + " 23:59:59", DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS_PATTERN));
	}
}