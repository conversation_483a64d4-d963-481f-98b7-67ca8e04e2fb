<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.MccRepository">

	<select id="getMccList" resultType="mccDTO">
		SELECT  m.mcc_id as mccId,m.mcc_group as mccGroup, m.mcc_code as mccCode, m.mcc_desc as mccDesc,m.status as status, m.created_by as createdBy,  
		mg.description as mccGroupName
		from  mcc_config_stg m  
		left join  lookup mg on mcc_group=mg.code and mg.type='MCCGroupList'
		WHERE m.request_state ='A' ORDER BY m.last_updated_on desc
	</select>
	<select id="getMccListMain" resultType="mccDTO">
		SELECT  m.mcc_id as mccId,m.mcc_group as mccGroup, m.mcc_code as mccCode, m.mcc_desc as mccDesc,m.status as status, m.created_by as createdBy,  
		mg.description as mccGroupName, stg.request_state as requestState
		from  mcc_config m  
		inner join mcc_config_stg stg on m.mcc_id=stg.mcc_id
		left join  lookup mg on m.mcc_group=mg.code and mg.type='MCCGroupList'
		 ORDER BY m.last_updated_on desc
	</select>
	

	<select id="getPendingMccConfigs" resultType="mccDTO">
		SELECT m.mcc_id as mccId,m.mcc_group as mccGroup, m.mcc_code as mccCode,m.mcc_desc as mccDesc,m.status as status,
		m.created_by as createdBy, m.checker_comments as checkerComments,m.last_operation as lastOperation,m.request_state as requestState,
		mg.description as mccGroupName
		FROM mcc_config_stg m 
		left join  lookup mg on mcc_group=mg.code and mg.type='MCCGroupList'
		WHERE REQUEST_STATE IN ('P', 'R')
	</select>

	<insert id="addMccConfig">
	INSERT INTO MCC_CONFIG_STG (mcc_id,mcc_code,mcc_group,mcc_desc,status,created_by,created_on,request_state,last_operation)
		VALUES(#{mccId}, #{mccCode},#{mccGroup},#{mccDesc},#{status},#{createdBy},#{createdOn},#{requestState}, #{lastOperation})
	</insert>

	<select id="fetchIdFromMccIdSequence" resultType="int">
		SELECT nextval('mcc_id_seq')
	</select>

	<select id="getMccConfigById" resultType="mccDTO">
		SELECT m.mcc_id as mccId, m.mcc_group as mccGroup, m.mcc_code as mccCode, m.mcc_desc as mccDesc, m.status as status,m.last_updated_by as lastUpdatedBy,
		m.last_updated_on as lastUpdatedOn, m.created_by as createdBy, m.created_on as createdOn, m.checker_comments as checkerComments,
		m.last_operation as lastOperation, m.request_state as requestState, 
		mg.description as mccGroupName from mcc_config_stg m
		left join  lookup mg on mcc_group=mg.code and mg.type='MCCGroupList'
		WHERE mcc_id = #{mccId}
	</select>
	
	<select id="getMccConfigByIdMain" resultType="mccDTO">
		SELECT m.mcc_id as mccId, m.mcc_group as mccGroup, m.mcc_code as mccCode, m.mcc_desc as mccDesc, m.status as status,m.last_updated_by as lastUpdatedBy,
		m.last_updated_on as lastUpdatedOn, m.created_by as createdBy, m.created_on as createdOn,
		stg.last_operation as lastOperation, stg.request_state as requestState, 
		mg.description as mccGroupName 
		from mcc_config m
		inner join mcc_config_stg stg on m.mcc_id=stg.mcc_id
		left join  lookup mg on m.mcc_group=mg.code and mg.type='MCCGroupList'
		WHERE m.mcc_id = #{mccId}
	</select>
<update id="updateMccConfig">
		UPDATE mcc_config_stg SET status =#{status}, last_updated_by = #{lastUpdatedBy}, last_updated_on = #{lastUpdatedOn},
		last_operation= #{lastOperation},created_by=#{createdBy},created_on=#{createdOn}, request_state =#{requestState},CHECKER_COMMENTS= #{checkerComments} where MCC_ID = #{mccId}
	</update>

	<update id="updateMccConfigDiscard">
		UPDATE mcc_config_stg SET mcc_id=#{mccId},mcc_code=#{mccCode},mcc_group=#{mccGroup},mcc_desc=#{mccDesc},status =#{status}, last_updated_by = #{lastUpdatedBy}, last_updated_on = #{lastUpdatedOn},
		last_operation= #{lastOperation},created_by=#{createdBy},created_on=#{createdOn}, request_state =#{requestState},CHECKER_COMMENTS= #{checkerComments} where MCC_ID = #{mccId}
	</update>
	
	<update id="updateMccMain">
		UPDATE mcc_config SET mcc_id=#{mccId},mcc_code=#{mccCode},mcc_group=#{mccGroup},mcc_desc=#{mccDesc},status =#{status}, last_updated_by = #{lastUpdatedBy}, last_updated_on = #{lastUpdatedOn},
		last_operation= #{lastOperation} where MCC_ID = #{mccId} 
	</update>
	
	<update id="updateMccConfigEdit">
		UPDATE mcc_config_stg SET mcc_code=#{mccCode},mcc_group=#{mccGroup},mcc_desc=#{mccDesc},status =#{status}, last_updated_by = #{lastUpdatedBy}, last_updated_on = #{lastUpdatedOn},
		last_operation= #{lastOperation}, request_state =#{requestState},CHECKER_COMMENTS= #{checkerComments} where MCC_ID = #{mccId}
	</update>

	<select id="getPendingMccConfigById" resultType="mccDTO">
		SELECT m.mcc_id as mccId, m.mcc_group as mccGroup, m.mcc_code as mccCode,m.mcc_desc as mccDesc,m.status as status, m.last_updated_by as lastUpdatedBy,
		m.last_updated_on as lastUpdatedOn, m.created_by as createdBy, m.created_on as createdOn,m.checker_comments as checkerComments,
		m.last_operation as lastOperation, m.request_state as requestState, 
		mg.description as mccGroupName from mcc_config_stg m 
		left join  lookup mg on mcc_group=mg.code and mg.type='MCCGroupList'
		WHERE mcc_id = #{mccId}
	</select>

	<update id="updateMccStgState">
		update mcc_config_stg SET last_updated_by =#{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},request_state =#{requestState} ,last_operation= #{lastOperation},
		checker_comments =#{checkerComments} where mcc_id = #{mccId}
	</update>

	<select id="getApprovedMccConfigById" resultType="mccDTO">
		SELECT m.mcc_id as mccId, m.mcc_group as mccGroup, m.mcc_code as mccCode,m.mcc_desc as mccDesc,m.status as status, m.last_updated_by as lastUpdatedBy,
		m.last_updated_on as lastUpdatedOn, m.created_by as createdBy, m.created_on as createdOn,
		mg.description as mccGroupName from mcc_config m 
		left join  lookup mg on mcc_group=mg.code and mg.type='MCCGroupList'
		WHERE mcc_id = #{mccId}
	</select>

	<insert id="saveMccConfig">
		insert into mcc_config
		(mcc_id,mcc_group, mcc_code,mcc_desc,status,last_updated_by,last_updated_on,created_on,created_by,last_operation,charge_type)
		values(
		#{mccId},#{mccGroup},#{mccCode},#{mccDesc},#{status},#{lastUpdatedBy},#{lastUpdatedOn},#{createdOn},#{createdBy},#{lastOperation},#{chargeType})
	</insert>

	<select id="getMccStgData" resultType="mccDTO">
		SELECT m.mcc_id as mccId, m.mcc_group as mccGroup, m.mcc_code as mccCode,m.mcc_desc as mccDesc,m.status as status,m.last_updated_by as lastUpdatedBy,
		m.last_updated_on as lastUpdatedOn,m.created_by as createdBy, m.created_on as createdOn,m.checker_comments as checkerComments,
		m.last_operation as lastOperation,m.request_state as requestState,
		mg.description as mccGroupName FROM mcc_config_stg m 
		left join  lookup mg on mcc_group=mg.code and mg.type='MCCGroupList'
		where mcc_id = #{mccId}
	</select>


	<select id="getMccMainData" resultType="mccDTO">
		SELECT m.mcc_id as mccId, m.mcc_group as mccGroup, m.mcc_code as mccCode,m.mcc_desc as mccDesc,m.status as status,m.last_updated_by as lastUpdatedBy,
		m.last_updated_on as lastUpdatedOn,m.created_by as createdBy, m.created_on as createdOn, 
		mg.description as mccGroupName from mcc_config m 
		left join  lookup mg on mcc_group=mg.code and mg.type='MCCGroupList'
		where mcc_id = #{mccId}

	</select>

	<delete id="deleteDiscardedMccEntry">
		delete from mcc_config_stg WHERE mcc_id = #{mccId}
	</delete>

<select id="validateDuplicateCheck" resultType="int">
		SELECT count(*) from  mcc_config_stg  where mcc_code =#{mccCode}
	</select>
	
	<select id="getByMccCode" resultType="mccDTO">
		 SELECT m.mcc_id as mccId, m.mcc_group as mccGroup, m.mcc_code as mccCode,m.mcc_desc as mccDesc,m.status as status, m.last_updated_by as lastUpdatedBy,
		m.last_updated_on as lastUpdatedOn, m.created_by as createdBy, m.created_on as createdOn
		 from mcc_config m where m.mcc_code = #{mccCode}
	</select>

	<update id="updateMccConfigForChargeType">
		update mcc_config set charge_type=#{chargeType},last_operation= #{lastOperation},last_updated_by =#{lastUpdatedBy},last_updated_on = #{lastUpdatedOn} where mcc_code=#{mccCode}
	</update>

</mapper>	