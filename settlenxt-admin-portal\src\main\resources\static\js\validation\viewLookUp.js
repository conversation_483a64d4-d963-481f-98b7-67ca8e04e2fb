 $(document).ready(function () {
 
 

	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});
 
 
 
 
 
 })
 
  function display() {
		$(".appRejMust").hide();
	}
 
 
 function submitForm(url) {
    var data = "" ;
    postData(url, data);
}

function viewLookUpInfo(lookupId,action) {
     var data = "lookupId," + lookupId ;
     console.log("lookup:" + lookupId);
    postData(action, data);
    }
 


function approve(url){
	
var lookupId = $("#lookupId").val();
var remarks=$("#rejectReason").val();

if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 
				
				var data = "lookupId," + lookupId + ",status," + "A" + ",remarks," + remarks;
				postData(url, data);
			} 
			else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} 
		
		
		else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				
				var dataRej = "lookupId," + lookupId + ",status," + "R"  + ",remarks," + remarks;
				postData(url, dataRej);
			}
			else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		 } else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
			
}