<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript"> 
var actionColumnIndex=8;

<c:if test="${approvedlist eq 'N'}">
	actionColumnIndex=9;
</c:if>
</script>

<script src="./static/js/validation/mcpr/addProjectionEntry.js" type="text/javascript"></script>
<script type="text/javascript" src=	"./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src=	"./static/js/jszip.min.js">
</script>
 
<script type="text/javascript" src=	"./static/js/buttons.html5.min.js">
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
	 
	.defaultexport {
  visibility: hidden;
}

table.dataTable thead  { vertical-align: top;}
table.dataTable thead .sorting { vertical-align: top; background: url('./static/images/sort_both.png') no-repeat center right; }
table.dataTable thead .sorting_asc { vertical-align: top;background: url('./static/images/sort_asc.png') no-repeat center right; }
table.dataTable thead .sorting_desc { vertical-align: top;background: url('./static/images/sort_desc.png') no-repeat center right; }
table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before{ vertical-align: top;content:""}
table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after{ vertical-align: top;content:""}
.search-box  {	
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;	
	background-color: transparent;
    width: 100%;
    border-width:1px;
	border-style:inset;
    }
</style>

<script type="text/javascript">
var cardProjectionValidationMessages={};
cardProjectionValidationMessages['cardVariant']="<spring:message code='cardProjection.cardVariant.validation.msg' javaScriptEscape='true' />";
cardProjectionValidationMessages['cardType']="<spring:message code='cardProjection.cardType.validation.msg' javaScriptEscape='true' />";
cardProjectionValidationMessages['totalCards']="<spring:message code='cardProjection.totalCards.validation.msg' javaScriptEscape='true' />";

</script>


<div class="row">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
		<c:if test="${not empty addEntry}">
		<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><label><spring:message code="cardprojection.addscreen.title" /></label></span></strong>
			
		</c:if>
		<c:if test="${not empty editEntry}">
		<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><label><spring:message code="cardprojection.editscreen.title" /></label></span></strong>
			
		</c:if>
			
		</div>
		<c:if test="${not empty addEntry}">
			<c:url value="addCardProjection" var="submitCardProjection" />
		</c:if>
		<c:if test="${not empty editEntry}">
			<c:url value="updateCardProjection" var="submitCardProjection" />
		</c:if>
		<div class="panel-body">
			<form:form onsubmit="return encodeForm(this);" method="POST"
				id="addCardProjection" modelAttribute="projectionEntryDTO"
				action="${submitCardProjection}" autocomplete="off">
				<br />
				<form:hidden path="cardProjectionId" id="cardProjectionId" name="cardProjectionId" value="${projectionEntryDTO.cardProjectionId}"/>
				<form:hidden path="cardVariantName" id="cardVariantName" name="cardVariantName"
					value="${projectionEntryDTO.cardVariantName}" />
				<form:hidden path="cardTypeName" id="cardTypeName" name="cardTypeName"
					value="${projectionEntryDTO.cardTypeName}" />
				<input id="hparentPage" type="hidden"
				value="${parentPage}" />
				<c:if test="${not empty showbutton}">
				<div class="row">
				<div class="col-sm-3">
                    <div class="form-group">
                        <label>Month<span style="color: red">*</span></label>
                        <form:input path="month" id="month" name="month" readonly="true"
                            cssClass="form-control medantory" value="${projectionEntryDTO.month}" />
                    </div>
                    </div>
				
				
				
				
				
				<div class="col-sm-3">
			
						<div class="form-group">
							<label><spring:message code="projectionEntry.cardType" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addEntry}">
							
								<form:select path="cardType" id="cardType" name="cardType"
									class="form-control">
									<form:option value="" label="SELECT" />
									<form:options 
										items="${cardTypeList}" itemValue="code" itemLabel="description"/>
								</form:select>
							</c:if>
							<c:if test="${not empty editEntry}">
								
								<form:input path="cardType" id="cardType"
									value="${projectionEntryDTO.cardTypeName}" name="cardType"
									maxlength="50" cssClass="form-control medantory" readonly="true" />
							</c:if>
							<div id="errcardType">
								<span for="cardType" class="error"><form:errors
										path="cardType" /></span>
							</div>
						</div>
					</div>
					<div class="col-sm-3">
			
						<div class="form-group">
							<label><spring:message code="projectionEntry.cardVariant" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addEntry}">
							
								<form:select path="cardVariant" id="cardVariant" name="cardVariant"
									class="form-control">
									<form:option value="" label="SELECT" />
									<form:options 
										items="${cardVariantList}"  itemValue="code" itemLabel="description"/>
								</form:select>
							</c:if>
							<c:if test="${not empty editEntry}">
								<form:input path="cardVariant" id="cardVariant"
									value="${projectionEntryDTO.cardVariantName}" name="cardVariant"
									maxlength="50" cssClass="form-control medantory" readonly="true" />
							</c:if>
							<div id="errcardVariant">
								<span for="cardVariant" class="error"><form:errors
										path="cardVariant" /></span>
							</div>

							
						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="projectionEntry.totalCards" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addEntry}">
								<form:input path="totalCards" id="totalCards" name="totalCards"
									maxlength="50" cssClass="form-control medantory loginIDADD"
									 />
							</c:if>
							<c:if test="${not empty editEntry}">
								<form:input path="totalCards" id="totalCards"
									value="${ProjectionEntryDTO.totalCards}" name="totalCards" 
									maxlength="50" cssClass="form-control medantory" />

							</c:if>
							<div id="errtotalCards">
								<span for="totalCards" class="error"><form:errors
										path="totalCards" /></span>
							</div>
						</div>
					</div>
					
					

						
				</div>
				</c:if>
				<c:if test="${empty showbutton}">
				<div class="row">
						<div class="col-sm-12">
							<table class="table table-striped infobold" style="font-size: 12px">
							<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
						<tbody>
							<tr>
									<%-- <td><label><spring:message code="projectionEntry.cardProjectionId" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.cardProjectionId}</td> --%>
									<td><label><spring:message code="projectionEntry.cardType" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.cardTypeName}</td>
									
									<td><label><spring:message code="projectionEntry.cardVariant" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.cardVariantName}</td>
									<td><label><span
											style="color: red"></span></label></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="projectionEntry.totalCards" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.totalCards}</td>
									<td><label><spring:message code="projectionEntry.month" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.month}</td>
									<td><label><span
											style="color: red"></span></label></td>
									
									<td></td>
									<td></td>
									<td></td>
								</tr>
						</tbody>

						</table>
						</div>
						</div>
				</c:if>

				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center">
						<c:if test="${not empty addEntry}">
						<c:if test="${not empty addReset}">
						<button type="button" value="Submit" class="btn btn-success"
									onclick="resetAction();">
									<spring:message code="binexcl.reset" />
								</button>
					</c:if>
					</c:if>
						
						
						
						<c:if test="${not empty addEntry}">
							<c:if test="${not empty showbutton}">
								<button type="button" value="Submit" class="btn btn-success"
									onclick="addOrUpdateEntry('A');">
									<spring:message code="msg.lbl.submit" />
								</button>
							</c:if>
						</c:if>
						
						
						<sec:authorize access="hasAuthority('Edit Card Projection')">
						<c:if test="${not empty editEntry}">
							<c:if test="${not empty showbutton}">
								<button type="button" value="Submit" class="btn btn-success" id="bEdit"
									onclick="addOrUpdateEntry('E');">
									<spring:message code="msg.lbl.update" />
								</button>
								
								<button type="button" value="Submit" class="btn btn-success"
									onclick="postDeleteAction('/deleteEntry');">
									Delete
								</button>
								
							</c:if>
							
							
						</c:if>
							
						<c:if test="${projectionEntryDTO.requestState eq 'R'and not empty showbutton}">
							<button type="button" class="btn btn-danger"
							onclick="postDiscardAction('/discardRejectedEntry');">
							<spring:message code="projectionEntry.discardBtn" /></button>
						
						<button type="button" class="btn btn-danger"
							onclick="userAction('N','/projectionEntryPendingForApproval');">
							<spring:message code="binexcl.backBtn" /></button>
						</c:if>
						</sec:authorize>
						
						
						<c:if test="${projectionEntryDTO.requestState ne 'R'}">
						<c:if test="${parentPage  ne 'approvalTab'}">
							<button type="button" class="btn btn-danger"
									onclick="userAction('N','/showProjectionEntry');">
									<spring:message code="budget.backBtn" /></button>
						</c:if> 
						<c:if test="${parentPage  eq 'approvalTab'}">
						<button type="button" class="btn btn-danger"
									onclick="userAction('N','/projectionEntryPendingForApproval');">
									<spring:message code="budget.backBtn" /></button>
						</c:if>
						</c:if>
						
						
						
					</div>

				</div>
				<c:if test="${not empty premiumCalculationDTO}">
				<div class="panel-body">
					<div class="row">
						
						<div class="row">
									<div class="col-sm-12">
											<button class="btn  pull-right btn_align" 
											id="clearFilters"><spring:message code="currencyMaster.clearBtn" /></button>
											&nbsp; <a class="btn btn-success pull-right btn_align" href="#" id="csvExport">
											<spring:message code="currencyMaster.csvBtn" /> </a>
											<a class="btn btn-success pull-right btn_align" href="#"
												id="excelExport"  ><spring:message code="currencyMaster.exportBtn" />
											</a>

									</div>
								</div>
						
						
						<!-- exportdatainfo -->
						<div id="exportdatainfo" style="display:none;">
							<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
							<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
								<thead>
									<tr>
										<th scope="col">${premiumCalculationDTO.nextProjectedCardsLabel}</th>
										<th scope="col">${premiumCalculationDTO.nextPremiumPerCardPerAnnumLabel}</th>
										<th scope="col">${premiumCalculationDTO.nextTotalPremiumPayableExclTaxesLabel}</th>
										<th scope="col">${premiumCalculationDTO.nextGSTLabel}</th>
										<th scope="col">Total Premium Payable (Inclusive of all taxes)</th>
										<th scope="col">${premiumCalculationDTO.previousMonthProjectedLabel}</th>
										<th scope="col">Summary</th>
										<th scope="col">Summary</th>
										<th scope="col">Total No. of Rupay cards projected</th>
										<th scope="col">Total No. of Rupay cards issued</th>
										<th scope="col">Premium Per Card Per Annum (Excl. of GST)</th>
										<th scope="col">Premium Per Card Per Annum (Excl. of GST)</th>
										<th scope="col">${premiumCalculationDTO.previousProjectedTotalPremiumPayableExclTaxesLebal}</th>
										<th scope="col">Total Premium Paid (Excl. of GST)</th>
										<th scope="col">${premiumCalculationDTO.premiumDiffLebal}</th>
										<th scope="col">${premiumCalculationDTO.totalNoCardIssuetillFebLebal}</th>
										<th scope="col">${premiumCalculationDTO.totalNoCardProjectionForMarchLebal}</th>
										<th scope="col">Total Card Base</th>
										<th scope="col">Premium Per Card Per Annum</th>
										<th scope="col">Total Premium payable (Exclusive of all taxes) for 365 days</th>
										<th scope="col">${premiumCalculationDTO.premiumPayableGSTTaxForMarchLebal}</th>
										<th scope="col">Total Premium payable for existing card base (inclusive of all taxes)</th>

										<th scope="col">${premiumCalculationDTO.previousProjectedTotalPremiumPayableInclTaxesLebal}</th>
										<th scope="col">${premiumCalculationDTO.previousActualTotalPremiumPayableInclTaxesLebal}</th>
										<th scope="col">Total Premium payable for existing card base (Excl of taxes)</th>
										<th scope="col">${premiumCalculationDTO.totalPremiumAfterAdjustmentExclTaxesLebal}</th>
										<th scope="col">${premiumCalculationDTO.totalPremiumAfterAdjustmentInclTaxesLebal}</th>

									</tr>
								</thead>
								<tbody>
										<tr>
										<td>${premiumCalculationDTO.nextProjectedCards}</td>
										<td>${premiumCalculationDTO.nextPremiumPerCardPerAnnumDisplay}</td>
										<td>${premiumCalculationDTO.nextTotalPremiumPayableExclTaxesDisplay}</td>
										<td>${premiumCalculationDTO.nextGSTAmountDisplay}</td>
										<td>${premiumCalculationDTO.nextTotalPremiumPayableInclTaxesDisplay}</td>
										<td>${premiumCalculationDTO.previousMonthActualLabel}</td>
										<td>${premiumCalculationDTO.previousProjectedLabel}</td>
										<td>${premiumCalculationDTO.previousActualLabel}</td>
										<td>${premiumCalculationDTO.previousProjectedCards}</td>
										<td>${premiumCalculationDTO.previousActualCards}</td>
										<td>${premiumCalculationDTO.previousProjectedPremiumPerCardPerAnnumDisplay}</td>
										<td>${premiumCalculationDTO.previousActualPremiumPerCardPerAnnumDisplay}</td>
										<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableExclTaxesDisplay}</td>
										<td>${premiumCalculationDTO.previousActualTotalPremiumPayableExclTaxesDisplay}</td>
										<td>${premiumCalculationDTO.premiumDiffDisplay}</td>
										<td>${premiumCalculationDTO.previousMonthActualLabel}</td>
										<td>${premiumCalculationDTO.totalNoCardProjectionForMarch}</td>
										<td>${premiumCalculationDTO.totalCardBase}</td>
										<td>${premiumCalculationDTO.premiumPerCardPerAnnumForMarchDisplay}</td>
										<td>${premiumCalculationDTO.premiumPayableExcTaxForMarchDisplay}</td>
										<td>${premiumCalculationDTO.premiumPayableGSTTaxForMarchDisplay}</td>
										<td>${premiumCalculationDTO.premiumPayableIncTaxForMarchDisplay}</td>

										<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableInclTaxesDisplay}</td>
										<td>${premiumCalculationDTO.previousActualTotalPremiumPayableInclTaxesDisplay}</td>
										<td>${premiumCalculationDTO.premiumPayableExcTaxForMarchDisplay}</td>
										<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentExclTaxesDisplay}</td>
										<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentInclTaxesDisplay}</td>
										</tr>
								</tbody>
							</table>
						</div>

						
						
						
						<div class="table-responsive">
						<table id="tabnewMain" class="table table-striped table-bordered" style="width:100%;">	
						<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
						<tr>
							<td colspan=4><strong>Projection Calculation</strong></td>
						</tr>
						<tr>
							<td>Summary</td>
							<td>${premiumCalculationDTO.cardInfo}</td> 
						</tr>
						<tr>
							<td>${premiumCalculationDTO.nextProjectedCardsLabel}</td>
							<td>${premiumCalculationDTO.nextProjectedCards}</td>
						</tr>
						<tr>
							<td>${premiumCalculationDTO.nextPremiumPerCardPerAnnumLabel}</td>
							<td>${premiumCalculationDTO.nextPremiumPerCardPerAnnumDisplay}</td>
						</tr>
						<tr>
							<td>${premiumCalculationDTO.nextTotalPremiumPayableExclTaxesLabel}</td>
							<td>${premiumCalculationDTO.nextTotalPremiumPayableExclTaxesDisplay}</td>
						</tr>
						<tr>
							<td>${premiumCalculationDTO.nextGSTLabel}</td>
							<td>${premiumCalculationDTO.nextGSTAmountDisplay}</td>
						</tr>
						<tr>
							<td>Total Premium Payable (Inclusive of all taxes)</td>
							<td>${premiumCalculationDTO.nextTotalPremiumPayableInclTaxesDisplay}</td>
						</tr>
						</table>
						</div>						
						<Br>
						<Br>	
						    <div class="form-group">
							<label>Adjustment</label>
						    </div>
						
						<div class="table-responsive">
						<table id="tabnewMain" class="table table-striped table-bordered" style="width:100%;">
						<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope="col"></th></thead>	
						<tr>
							<td colspan=4><strong>Table II</strong></td>
						</tr>
						<tr>
							<td colspan=2>${premiumCalculationDTO.previousMonthProjectedLabel}</td>
							<td colspan=2>${premiumCalculationDTO.previousMonthActualLabel} </td>
						</tr>
						<tr>
							<td>Summary</td>
							<td>${premiumCalculationDTO.previousProjectedLabel}</td>
							<td>Summary</td>
							<td>${premiumCalculationDTO.previousActualLabel}</td>
						</tr>
						<tr>
							<td>Total No. of Rupay cards projected</td>
							<td>${premiumCalculationDTO.previousProjectedCards}</td>
							<td>	Total No. of Rupay cards issued</td>
							<td>${premiumCalculationDTO.previousActualCards}</td>
						</tr>
						<tr>
							<td>Premium Per Card Per Annum (Excl. of GST)</td>
							<td>${premiumCalculationDTO.previousProjectedPremiumPerCardPerAnnumDisplay}</td>
							<td>Premium Per Card Per Annum (Excl. of GST)</td>
							<td>${premiumCalculationDTO.previousActualPremiumPerCardPerAnnumDisplay}</td>
						</tr>
						<tr>
							<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableExclTaxesLebal}</td>
							<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableExclTaxesDisplay}</td>
							<td>	Total Premium Paid (Excl. of GST)</td>
							<td>${premiumCalculationDTO.previousActualTotalPremiumPayableExclTaxesDisplay}</td>
						</tr>
						<tr>
							<td>${premiumCalculationDTO.premiumDiffLebal}</td>
							<td colspan=3>${premiumCalculationDTO.premiumDiffDisplay}</td>
						</tr></table>
						</div>						
						<Br>
						<Br>	

						<c:if test="${IsAprFlag eq 'Y'}">
						    <div class="form-group">
							<label><strong>Insurence for Existing Card Base</strong></label>
						    </div>
						
						<div class="table-responsive">
						<table id="tabnewMain" class="table table-striped table-bordered" style="width:100%;">	
						<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
						<tr>
							<td >${premiumCalculationDTO.totalNoCardIssuetillFebLebal}</td>
							<td >${premiumCalculationDTO.previousMonthActualLabel} </td>
						</tr>
						<tr>
							<td>${premiumCalculationDTO.totalNoCardProjectionForMarchLebal}</td>
							<td>${premiumCalculationDTO.totalNoCardProjectionForMarch}</td>
						</tr>
						<tr>
							<td>Total Card Base</td>
							<td>${premiumCalculationDTO.totalCardBase}</td>
						</tr>
						<tr>
							<td>Premium Per Card Per Annum</td>
							<td>${premiumCalculationDTO.premiumPerCardPerAnnumForMarchDisplay}</td>
						</tr>
						<tr>
							<td>Total Premium payable (Exclusive of all taxes) for 365 days</td>
							<td>${premiumCalculationDTO.premiumPayableExcTaxForMarchDisplay}</td>
						</tr>
						<tr>
							<td>${premiumCalculationDTO.premiumPayableGSTTaxForMarchLebal}</td>
							<td>${premiumCalculationDTO.premiumPayableGSTTaxForMarchDisplay}</td>
						</tr>
						<tr>
							<td>Total Premium payable for existing card base (inclusive of all taxes)</td>
							<td >${premiumCalculationDTO.premiumPayableIncTaxForMarchDisplay}</td>
						</tr></table>
						</div>						
						<Br>
						<Br>	
						</c:if>

						<div class="table-responsive">
						<table id="tabnewMain" class="table table-striped table-bordered" style="width:100%;">	
						<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
						<tr>
							<td><strong>Particulars</strong></td>
							<td><strong>Amount</strong></td>
						</tr>
						<tr>
							<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableInclTaxesLebal}</td>
							<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableInclTaxesDisplay}</td>
						</tr>
						<tr>
							<td>${premiumCalculationDTO.previousActualTotalPremiumPayableInclTaxesLebal}</td>
							<td>${premiumCalculationDTO.previousActualTotalPremiumPayableInclTaxesDisplay}</td>
						</tr>
						<c:if test="${IsAprFlag eq 'Y'}">
						<tr>
							<td>Total Premium payable for existing card base (Excl of taxes)</td>
							<td>${premiumCalculationDTO.premiumPayableExcTaxForMarchDisplay}</td>
						</tr>
						</c:if>
						<tr>
							<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentExclTaxesLebal}</td>
							<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentExclTaxesDisplay}</td>
						</tr>
						<tr>
							<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentInclTaxesLebal}</td>
							<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentInclTaxesDisplay}</td>
						</tr>
						
						</table>


					</div>						
				</div>	
				</div>					
				</c:if>
			</form:form>
		</div>
	</div>
</div>
