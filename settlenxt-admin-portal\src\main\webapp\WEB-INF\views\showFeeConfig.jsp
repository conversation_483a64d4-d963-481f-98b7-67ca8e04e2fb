<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ page import="java.time.format.DateTimeFormatter" %>
<script type="text/javascript">

<c:if test="${showApprovalTab eq 'YES' and major eq  'YES'}">
		<c:if test="${showCheckBox eq 'Y'}">
	var actionColumnIndex = 10;
	var firstColumnToBeSkippedInFilterAndSort=true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	var actionColumnIndex = 9;
	var firstColumnToBeSkippedInFilterAndSort=false;
	</c:if>
</c:if>
	<c:if test="${showApprovalTab eq 'YES' and minor eq  'YES'}">
	<c:if test="${showCheckBox eq 'Y'}">
var actionColumnIndex = 9;
var firstColumnToBeSkippedInFilterAndSort=true;
</c:if>
<c:if test="${showCheckBox eq 'N'}">
var actionColumnIndex = 8;
var firstColumnToBeSkippedInFilterAndSort=false;
</c:if>	
</c:if>
	<c:if test="${showFeeConfig eq 'YES' and major eq  'YES'}">
	var actionColumnIndex = 8;
	var firstColumnToBeSkippedInFilterAndSort=false;
	</c:if>
	<c:if test="${showFeeConfig eq 'YES' and minor eq  'YES'}">
	var actionColumnIndex = 5;
	var firstColumnToBeSkippedInFilterAndSort=false;
	</c:if>
	
</script>
	
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>

<script>
var majorIdpending=[];
var minorIdpending=[];

<c:if test="${not empty pendingFeeConfigList}">
<c:forEach items="${pendingFeeConfigList}" var="operator">
<c:if test="${operator.requestState eq 'P' and major eq 'YES'}">

majorIdpending.push(${operator.feeMajorId});

</c:if>
<c:if test="${operator.requestState eq 'P' and minor eq 'YES'}">

minorIdpending.push(${operator.feeConfigId});

</c:if>

</c:forEach>
</c:if>






</script>

<div id="errorStatus2" class="alert alert-danger" role="alert"
	style="display: none"></div>
<div id="errorStatus4" class="alert alert-danger" role="alert"
	style="display: none"></div>

<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<thead style="display:none;"><th scope="col"></th></thead>
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>


</head>
<!-- Modal -->

<div class="modal fade" id="toggleModal" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalDeleteNews" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject all records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>

				<p id="detailsHeadersss" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulk('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulk('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Modal Close-->

<!-- Modal -->

<div class="modal fade" id="toggleModal1" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalDeleteNews" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">FeeMinor IDs</label>
				<p id="detailsHeaderss" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulk('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulk('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Modal Close-->






<script src="./static/js/validation/showFeeConfig.js"
	type="text/javascript"></script>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showFeeConfig eq 'YES'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/showFeeConfig');" role="tab"
			data-toggle="tab"><span class="glyphicon glyphicon-user">
				<spring:message code="am.lbl.feeConfig" />
		</span> </a>

		<c:choose>
			<c:when test="${showApprovalTab eq 'YES'}">
				<li role="presentation" class="active">
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" role="tab" onclick="getPendingMajorMinorList();"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> <spring:message
					code="sm.lbl.approval" />
		</span></a>


	</ul>


	<div class="tab-content">

		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
				<div class="col-sm-12">
					<form:form onsubmit="removeSpace(this); encodeForm(this);"
						method="POST" id="addEditFeeM" modelAttribute="feeRateDto"
						action="" autocomplete="off">
						
						<input type="hidden" name="majorminor" id="majorminor" value="${majorminor}"/> 
						<c:choose>
							<c:when test="${showApprovalTab eq 'YES'}">
								<form:select path="significance" id="significance"
									class="form-control" style="width: 135px;"
									onchange="getPendingMajorMinorList();">
									<form:option value="M">
										<span data-i18n="Data"> <spring:message
												code="am.lbl.major" /></span>
									</form:option>
									<form:option value="N">
										<span data-i18n="Data"> <spring:message
												code="am.lbl.minor" /></span>
									</form:option>
								</form:select>
							</c:when>
							<c:when test="${showFeeConfig eq 'YES'}">
								<form:select path="significance" id="significance"
									class="form-control" style="width: 135px;"
									onchange="getMajorMinorList();">
									<form:option value="M">
										<span data-i18n="Data"> <spring:message
												code="am.lbl.major" /></span>
									</form:option>
									<form:option value="N">
										<span data-i18n="Data"> <spring:message
												code="am.lbl.minor" /></span>
									</form:option>
								</form:select>
							</c:when>
						</c:choose>
					</form:form>

					<sec:authorize access="hasAuthority('Add Fees')">
						<c:if test="${addFeeConfig eq 'YES'}">
						<c:if test="${significance eq 'M'}">
<div id="feeMajorAdd">
<a class="btn btn-success pull-right btn_align" href="#"
								onclick="submitForm('/createFeeMajorMinor','M');"
								style="margin-top: -30px;"><em class="glyphicon-plus"></em> Add FeeMajor</a>
</div>
</c:if>
	<c:if test="${significance eq 'N'}">
<div id="feeMinorAdd">
<a class="btn btn-success pull-right btn_align" href="#"
								onclick="submitForm('/createFeeMajorMinor','M');"
								style="margin-top: -30px;"><em class="glyphicon-plus"></em>Add FeeMinor</a>
								
</div>
</c:if>
							

						</c:if>
					</sec:authorize>


				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="ifsc.clearFiltersBtn" />
					</button>


					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="excelExport"><spring:message code="ifsc.exportBtn" /> </a>
					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="csvExport"><spring:message code="ifsc.csvBtn" /> </a>


				</div>
			</div>


			<c:if test="${not empty feeConfig and major eq  'YES' }">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message code="am.lbl.feeConfig" /></span></strong>
							</div>
							<div class="panel-body">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width:100%;">
										<caption style="display:none;">FEE</caption>
										<thead>
											<tr>
												<th scope = "col"><spring:message code="am.lbl.majorId" /></th>
												<th scope = "col"><spring:message code="am.lbl.schemeCode" /></th>
												<th scope = "col"><spring:message code="am.lbl.productCode" /></th>
												<th scope = "col"><spring:message code="am.lbl.cardType" /></th>
												<th scope = "col"><spring:message code="am.lbl.cardBrand" /></th>
												<th scope = "col"><spring:message code="am.lbl.functionCode" /></th>
												<th scope = "col"><spring:message code="am.lbl.priority" /></th>
												<th scope = "col"><spring:message code="am.lbl.status" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="feeMajor" items="${feeConfig}">

												<tr
													onclick="javascript:viewMajorMinorInfo('${feeMajor.feeMajorId}','V','${feeMajor.requestId}')">
													<td>${feeMajor.feeMajorId}</td>
													
<td>${schemeTypeMap[feeMajor.schemeCode]}</td>
													<td>${binProdTypeMap[feeMajor.productCode]}</td>

													<td>${binCardTypeMap[feeMajor.cardType]}</td>

													<td>${binCardBrandMap[feeMajor.cardBrand]}</td>

													<td>${FuncCodeMap[feeMajor.funCd]}</td>
													<td>${feeMajor.priority}</td>

													<c:choose>
														<c:when test="${feeMajor.status eq 'A'}">
															<td style="color: blue"><spring:message
																	code="common.msg.lbl.active" /></td>
														</c:when>
														<c:when test="${feeMajor.status eq 'I'}">
															<td style="color: red"><spring:message
																	code="common.msg.lbl.inactive" /></td>
														</c:when>
														<c:otherwise>
															<td><spring:message code="common.msg.lbl.null" /></td>
														</c:otherwise>
													</c:choose>
												</tr>
											</c:forEach>

										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>

			</c:if>


			<c:if test="${not empty feeConfig and minor eq 'YES'}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message code="am.lbl.feeConfig" /></span></strong>
							</div>
							<div class="panel-body">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width:100%;">
										<caption style="display:none;">FEE</caption>
										<thead>
											<tr>
												<th scope = "col"><spring:message code="am.lbl.feeMinorId" /></th>
												<th scope = "col"><spring:message code="feeRate.createdBy" /></th>
												<th scope = "col"><spring:message code="feeRate.createdOn" /></th>
												<th scope = "col"><spring:message code="feeRate.lastUpdatedBy" /></th>
												<th scope = "col"><spring:message code="feeRate.lastUpdatedOn" /></th>

												
											</tr>
										</thead>
										<tbody>
											<c:forEach var="feeConfig" items="${feeConfig}">

												<tr
													onclick="javascript:viewMajorMinorInfo('${feeConfig.feeConfigId}','V','${feeConfig.requestId}')">
													<td>${feeConfig.feeConfigId}</td>
													<td>${feeConfig.createdBy}</td>
													<td>${feeConfig.createdOn.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>
													<td>${feeConfig.lastUpdatedBy}</td>
													<td>${feeConfig.lastUpdatedOn.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>
													
												</tr>


											</c:forEach>


										</tbody>
									</table>

								</div>
							</div>

						</div>
			</c:if>


			<c:if test="${empty feeConfig}">
				<div class="row">
					<div class="col-md-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<sec:authorize access="hasAuthority('Approve User')">
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveorRejectBulk('A','no')" id="submitButton"
										value="<spring:message code="feeRate.Approve" />" />
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveorRejectBulk('R','no')" id="submitButton"
										value="<spring:message code="feeRate.Reject" />" />
								</sec:authorize>
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message code="am.lbl.feeConfig" /></span></strong>
							</div>
							<div class="panel-body">
								<c:choose>
									<c:when test="${not empty showApprovalTab and major eq 'YES' }">
										<div class="table-responsive">
											<table id="tabnew" class="table table-striped table-bordered"
												style="width:100%;">
												<caption style="display:none;">FEE</caption>
												<thead>
													<tr>
														<sec:authorize access="hasAuthority('Approve Fees')">
															<th scope = "col"><input type=checkbox name='selectAllCheck'
																id="selectAll" data-toggle="modal"
																data-target="toggleModal" value=""></input></th>
														</sec:authorize>
														<th scope = "col"><spring:message code="am.lbl.majorId" /></th>
														<th scope = "col"><spring:message code="am.lbl.schemeCode" /></th>
														<th scope = "col"><spring:message code="am.lbl.productCode" /></th>
														<th scope = "col"><spring:message code="am.lbl.cardType" /></th>
														<th scope = "col"><spring:message code="am.lbl.cardBrand" /></th>
														<th scope = "col"><spring:message code="am.lbl.functionCode" /></th>
														<th scope = "col"><spring:message code="am.lbl.priority" /></th>
														<th scope = "col"><spring:message code="am.lbl.status" /></th>
														<th scope = "col"><spring:message code="sm.lbl.checkerComments" /></th>

													</tr>
												</thead>
												<tbody>
													<c:forEach var="feeMajor" items="${pendingFeeConfigList}">

														
															<c:if test="${feeMajor.requestState eq 'P' }">
															<tr
															onclick="javascript:viewMajorMinorInfo('${feeMajor.feeMajorId}','P','${feeMajor.requestId}')">
																<sec:authorize access="hasAuthority('Approve Fees')">
																	<td onclick=event.stopPropagation()><input
																		type=checkbox name='type'
																		value='${feeMajor.feeMajorId}'></input></td>
																</sec:authorize>
															</c:if>

															<c:if test="${feeMajor.requestState eq 'R' }">
															<tr
															onclick="javascript:viewMajorMinorInfo('${feeMajor.feeMajorId}','R','${feeMajor.requestId}')">
																<sec:authorize access="hasAuthority('Approve Fees')">
																	<td onclick=event.stopPropagation()><input
																		type=checkbox name='types' style="display: none"
																		value='${feeMajor.feeMajorId}'></input></td>
																</sec:authorize>
															</c:if>

															<td>${feeMajor.feeMajorId}</td>
															<td>${schemeTypeMap[feeMajor.schemeCode]}</td>
															
															<td>${binProdTypeMap[feeMajor.productCode]}</td>

															<td>${binCardTypeMap[feeMajor.cardType]}</td>

															<td>${binCardBrandMap[feeMajor.cardBrand]}</td>

															<td>${FuncCodeMap[feeMajor.funCd]}</td>
															<td>${feeMajor.priority}</td>
															<td>${feeMajor.requestState =='P' ? 'Pending for Approval' : 'Rejected'}</td>
															<td>${feeMajor.checkerComments}</td>
															
														</tr>
													</c:forEach>

												</tbody>
											</table>
										</div>
									</c:when>

									<c:when test="${not empty showApprovalTab and minor eq 'YES' }">
										<div class="table-responsive">
											<table id="tabnew" class="table table-striped table-bordered"
												style="width:100%;">
												<caption style="display:none;">FEE</caption>
												<thead>
													<tr>
														<sec:authorize access="hasAuthority('Approve Fees')">
															<th scope = "col"><input type=checkbox name='selectAllCheck'
																id="selectAll1" data-toggle="modal"
																data-target="toggleModal1" value=""></input></th>
														</sec:authorize>
														<th scope = "col"><spring:message code="am.lbl.feeMinorId" /></th>
														<th scope = "col"><spring:message code="feeRate.createdBy" /></th>
														<th scope = "col"><spring:message code="feeRate.createdOn" /></th>
														<th scope = "col"><spring:message code="feeRate.lastUpdatedBy" /></th>
														<th scope = "col"><spring:message code="feeRate.lastUpdatedOn" /></th>
														<th scope = "col"><spring:message code="am.lbl.requestType" /></th>
														<th scope = "col"><spring:message code="am.lbl.status" /></th>
														<th scope = "col"><spring:message code="sm.lbl.checkerComments" /></th>
													</tr>
												</thead>
												<tbody>
													<c:forEach var="feeConfig" items="${pendingFeeConfigList}">

															<c:if test="${feeConfig.requestState eq 'P' }">
															<tr
															onclick="javascript:viewMajorMinorInfo('${feeConfig.feeConfigId}','P','${feeConfig.requestId}')">
														
																<sec:authorize access="hasAuthority('Approve Fees')">
																	<td onclick=event.stopPropagation()><input
																		type=checkbox name='type'
																		value='${feeConfig.feeConfigId}'></input></td>
																</sec:authorize>
															</c:if>
															<c:if test="${feeConfig.requestState eq 'R' }">
															<tr
															onclick="javascript:viewMajorMinorInfo('${feeConfig.feeConfigId}','R','${feeConfig.requestId}')">
														
																<sec:authorize access="hasAuthority('Approve Fees')">
																	<td onclick=event.stopPropagation()><input
																		type=checkbox name='types' style="display: none"
																		value='${feeConfig.feeConfigId}'></input></td>
																</sec:authorize>
															</c:if>
															<td>${feeConfig.feeConfigId}</td>
															<td>${feeConfig.createdBy}</td>
															<td>${feeConfig.createdOn.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>
															<td>${feeConfig.lastUpdatedBy}</td>
															<td>${feeConfig.lastUpdatedOn.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>
															<td>${feeConfig.lastOperation}</td>
															<td>${feeConfig.requestState =='P' ? 'Pending for Approval' : 'Rejected'}</td>
															<td>${feeConfig.checkerComments}</td>
														</tr>
													</c:forEach>

												</tbody>
											</table>
										</div>
									</c:when>

								</c:choose>



							</div>
						</div>
					</div>
				</div>
			</c:if>

		</div>

	</div>





</div>