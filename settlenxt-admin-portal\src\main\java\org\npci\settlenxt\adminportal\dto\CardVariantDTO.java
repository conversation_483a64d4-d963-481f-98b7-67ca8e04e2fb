
package org.npci.settlenxt.adminportal.dto;

import java.io.Serializable;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JGlobalMap
public class CardVariantDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	private long workFlowId;
	private long rebateId;
	private long cardVariantId;
	private String cardVariantName;

}
