$(document)
		.ready(
				function() {
					var btn;
					var btnName;

					$("#transactionDate").datepicker({
						dateFormat : "yy-mm-dd",
						changeMonth : true,
						changeYear : true
					});

					// Add minus icon for collapse element which is open by
					// default
					$(".collapse.in").each(
							function() {
								$(this).siblings(".panel-heading").find(
										".glyphicon").addClass(
										"glyphicon-minus").removeClass(
										"glyphicon-plus");
							});

					// Toggle plus minus icon on show hide of collapse element
					$(".collapse").on(
							'show.bs.collapse',
							function() {
								$(this).parent().find(".glyphicon")
										.removeClass("glyphicon-plus")
										.addClass("glyphicon-minus");
							}).on(
							'hide.bs.collapse',
							function() {
								$(this).parent().find(".glyphicon")
										.removeClass("glyphicon-minus")
										.addClass("glyphicon-plus");
							});

					if ($('#actionTypes').val() != ""
							&& $('#showBtns').val() == "") {
						var actions = $('#actionTypes').val();
						var buttonArray;

						if (actions.indexOf(",") != -1) {
							buttonArray = actions.split(",");

							for (var buttonVal of buttonArray) {
								if (buttonVal.indexOf("|") != -1) {
									btn = buttonVal.split("|");
									btnName = btn[0].replace('#', "-");

									/*
									 * $('<button type="button" class="btn
									 * btn-primary callAction" id="actionBtn"
									 * data-value="'+btn[1]+'" >'+btn[0]+'</button>').appendTo('.btnDiv')
									 */

									$(
											'<button data-toggle="modal" type="button" class="launch-modal btn btn-primary callAction" data-target="#exampleModalLong"  id="actionBtn"  data-value="'
													+ btn[1]
													+ '" >'
													+ btnName
													+ '</button>').appendTo(
											'.btnDiv')

								}
							}
							$(
									'<a href="#" id="backButton" class="btn btn-default">Back</a>')
									.appendTo('.btnDiv')
						} else {
							btn = actions.split("|");
							btnName = btn[0].replace('#', "-");
							/*
							 * $( '<button type="button" class="btn btn-primary
							 * callAction" data-value="'+btn[1]+'" >'+btn[0]+'</button>').appendTo('.btnDiv')
							 */

							$(
									'<button data-toggle="modal" type="button" class="launch-modal btn btn-primary callAction" data-target="#exampleModalLong"   data-value="'
											+ btn[1]
											+ '" >'
											+ btnName
											+ '</button>').appendTo('.btnDiv')

							$(
									'<a href="#" id="backButton" class="btn btn-default">Back</a>')
									.appendTo('.btnDiv')
						}
					} else {
						$(
								'<a href="#" id="backButton" class="btn btn-default">Back</a>')
								.appendTo('.btnDiv')
					}

					$('.callAction')
							.on(
									'click',
									function() {
										var arr;

										arr = $(this).attr('data-value')
												.split("-");
										$('#adjRefNo').val(arr[5]);

										if (arr[6] == '0' || arr[6] == "") {
											$('#orgAdjRefNoDiv').hide();
										} else {
											$('#orgAdjRefNo').val(arr[6]);
											$('#orgAdjRefNoDiv').show();
										}

										if (arr[2] == 'F') {
											
											$('#adjAmount').val(arr[4]);
											$('#radiobtn').hide();
											$('#strTxnAmountval').val(arr[4]);
										} else {
											
											$('#adjAmount').val(arr[4]);
											$('#strTxnAmountval').val(arr[4]);
											
										}

										$('.submitAdjReq').attr('data-value',
												$(this).attr('data-value'));

										arr = $(this).attr('data-value')
												.split('-');

										if (arr[1] != '114' && arr[1] != '117'
												&& arr[1] != '142'
												&& arr[1] != '120'
												&& arr[1] != '152'
												&& arr[1] != '145'
												&& arr[1] != '148'
												&& arr[1] != '141'
												&& arr[1] != '128'
												&& arr[1] != '126') {
											$('#evidanceDiv').hide();
										} else {
											$('#evidanceDiv').show();
										}

										if (!(arr[1] == '112'
												|| arr[1] == '102'
												|| arr[1] == '131' || arr[1] == '132')) {

											$('#radioDiv').hide();
											$('#adjAmount').attr('readonly',
													true);
										} else {
											if (arr[7] != 'U2') {
												$('#radioDiv').hide();
												$('#adjAmount').attr(
														'readonly', true);
											} else {

												$('#radioDiv').show();
												$('#adjAmount').attr(
														'readonly', false);
												if ($(
														"input[name='paytype']:checked")
														.val() == 'Y') {
													$('#adjAmount').attr(
															'readonly', false);
												} else {
													$('#adjAmount').attr(
															'readonly', true);
												}
												$('#isPartial')
														.val(
																$(
																		"input[name='paytype']:checked")
																		.val());
											}
										}
										var tokenValue = document
												.getElementsByName("_TransactToken")[0].value;
										$
												.ajax({

													url : "getReasoncodes",
													type : "POST",
													data : {
														actionId : arr[1],
														participantId : arr[3],
														txnSubType : arr[7],
														flag : arr[0],
														respCode : arr[8],
														_TransactToken : tokenValue,
													},
													dataType : "json",
													success : function(data) {
														$("#reasonCode")
																.empty();
														$
																.each(
																		data,
																		function(
																				_index,
																				option) {
																			$(
																					"#reasonCode")
																					.append(
																							'<option value="'
																									+ option.value
																									+ '">'
																									+ option.label
																									+ '</option>');
																		});
													}
												});

										$('#exampleModalLongTitle').html(
												$(this).text().toUpperCase())

									});

					$('.submitAdjReq')
							.on(
									'click',
									function() {
										var tokenValue = document
												.getElementsByName("_TransactToken")[0].value;
										if (validateForm($(this).attr(
												'data-value').split('-'))) {
											$('#actionType').val(
													$(this).attr('data-value'));
											$('#remarks').val(
													$('#remrks').val());
											$('#txnId').val($('#txnId1').val());
											$('#custRefNo')
													.val($('#rrn').val());

											$("#raiseAdjReq").attr(
													'action',
													'raiseActionReq?_xyrftp='+sha256(tokenValue));
											$("#raiseAdjReq").submit();
										}
									});

					$('#example')
							.on(
									'click',
									'.tabRowId',
									function() {

										var txnId = $(this).attr('data-value');
										var url = "/getTransaction";
										var data =  "txnId,"
												+ txnId;
										postData(url, data);
									});

					$("#chargeBack")
							.click(
									function() {

										var txnId = $('#txnId').val();
										var chargeFlag = "C";
										var tokenValue = document
												.getElementsByName("_TransactToken")[0].value;

										$.ajax({
											url : "getchargeBackData",
											type : "POST",
											dataType : "json",
											data : {
												"_TransactToken" : tokenValue,
												"txnId" : txnId,
												"chargeFlag" : chargeFlag
											},
											success : function(response) {
												console.log("response "
														+ response);
											},
											error : function(_request, _status,
													_error) {
												console.log("error");
											}
										});

									});

					$('#backButton')
							.click(
									function() {

										var data = "";
										postData('/viewTransactionSearch', data);
									});

					

					$('.dwnldEvidence')
							.click(
									function() {

										
										var data = "fileName,"
												+ $(this).attr('data-value');
										postData('/downloadEvidenceFile', data);
									});
					
					$('.dwnldRgcsEvidence')
					.click(
							function() {
								
								var path = $(this).attr('data-value').split("ClearingDocuments")[1];
								path = path.replace(/\\/g, "/");
								var data =  "fileName,"
										+ path;
								postData('/downloadRgcsEvidenceFile', data);
							});

					function validateForm(arr) {

						if ($('#adjAmount').val() != "") {

							if (parseFloat($('#adjAmount').val()) > parseFloat($(
									'#strTxnAmountval').val())) {
								$('#adjAmountErr').html(
										"Ajustment amount should not be greater than "
												+ $('#strTxnAmountval').val());
								return false;
							}
							if ($('#radioDiv').is(':visible')) {
								var selected = $("#radioDiv input[type='radio']:checked");
								if (selected.val() == 'Y') {
									var x = parseFloat($('#adjAmount').val());
									var y = parseFloat($('#strTxnAmountval')
											.val());
									if (x.toString() == y.toString()) {
										$('#adjAmountErr').html(
												"Enter Only partial amount ");
										return false;
									}
									if ($('#adjAmount').val() == 0) {
										$('#adjAmountErr').html(
												"Please Enter amount ");
										return false;
									}
									if ($('#adjAmount').val() > 0) {
										$('#adjAmountErr').text("");
									} else {
										$('#adjAmountErr').html(
												"Negative amount not allowed ");
										return false;
									}
									if (decimalCheck()) {
										return false;
									}
								}

							}

						} else {

							$('#adjAmountErr').html(
									"Please enter adjustment amount");
							return false;
						}

						if ($('#bankAdjRefno').val() != "") {

							var regEx = /^[0-9A-Za-z/[\\\]:;_s-]*$/;
							if ($('#bankAdjRefno').val() != "") {
								if (!regEx.test($('#bankAdjRefno').val())) {
									$('#bankAdjRefnoErr')
											.text(
													'Please enter alpha numeric Bank Adjustment RefNo');
									return false;
								} else if (parseInt($('#bankAdjRefno').val()) == 0) {
									$('#bankAdjRefnoErr')
											.text(
													'Please enter valid Bank Adjustment RefNo');
									return false;
								} else {
									$('#bankAdjRefnoErr').text('');

								}
							}
						} else {
							$('#bankAdjRefnoErr').html(
									"Please enter Bank Adjustment Number");
							return false;
						}

						if ($('#remrks').val() != "") {

							$('#remrksErr').text("");
						} else {

							$('#remrksErr').html("Please enter remarks");
							return false;
						}

						/*
						 * if($('#file').val().trim() == ''){
						 * $('#errFile').text('Please attach file'); result =
						 * false; }else{ result=true; $('#errFile').text(''); }
						 */

						if ($('#reasonCode').val() != "0") {
							$('#reasonCodeErr').text("");
						} else {
							$('#reasonCodeErr')
									.html("Please enter Reason Code");
							return false;
						}

						
						debugger;
						if (arr[1] == '114' || arr[1] == '142'
								|| arr[1] == '117' || arr[1] == '120'
								|| arr[1] == '141' || arr[1] == '128'
								|| arr[1] == '126') {

							if ($("#file").val() == '') {
								$("#errFile").text("Please select file");

								return false;
							}
						}

						

						return true;

						/*
						 * alert(result +"1") if(result=='undefined'){
						 * alert(result +"2") return true; }
						 * 
						 * return result;
						 */
					}

					$('input[name="paytype"]').on('click change', function(_e) {
						if ($("input[name='paytype']:checked").val() == 'Y') {// full
							$('#adjAmount').attr('readonly', false);
							$('#isPartial').val('Y');
						} else {
							$('#adjAmount').attr('readonly', true);
							$('#adjAmount').val($('#strTxnAmountval').val());
							$('#isPartial').val('N');

						}
					});

					
					$('#bankAdjRefno')
							.keyup(
									function() {
										
										var regEx = /^[0-9A-Za-z/[\\\]:;_s-]*$/;
										if ($('#bankAdjRefno').val() != "") {
											if (!regEx.test($('#bankAdjRefno')
													.val())) {
												$('#bankAdjRefnoErr')
														.text(
																'Please enter alpha numeric Bank Adjustment RefNo');
												return false;
											} else if (parseInt($(
													'#bankAdjRefno').val()) == 0) {
												$('#bankAdjRefnoErr')
														.text(
																'Please enter valid Bank Adjustment RefNo');
												return false;
											} else {
												$('#bankAdjRefnoErr').text('');

											}
										}
									});

				});

function decimalCheck() {
	var dec = document.getElementById('adjAmount').value;
	if (dec.indexOf(".") > 1) {
		var res = dec.substring(dec.indexOf(".") + 1);
		var kl = res.split("");
		if (kl.length > 2) {
			$('#adjAmountErr').html("Only two decimal places is allowed ");
			return true;
		} else {
			$('#adjAmountErr').html("");
		}
	}
}
function validate(file) {
	var ext = file.split(".");
	ext = ext[ext.length - 1].toLowerCase();
	var arrayExtensions = [ "doc", "docx", "pdf", "jpg", "xls", "xlsx", "xps", "png", "zip", "txt", "jpeg", "rtf", "bmp ", "rar",
		"tif" ];
	if ($.inArray(ext, arrayExtensions) == -1) {
		$('#errFile').html("Only doc,docx,pdf,jpg,jpeg,xls,xlsx,xps,png,zip,txt,rtf,bmp,rar,tif format allowed ");
		
	} else {
		$('#errFile').html("");
	}
}

function setAccordian() {

	$("#next1").click(function() {

		$("#collapseTwo").addClass("panel-collapse collapse in");
		

	});

	$("#next2").click(function() {

		$("#collapseThree").addClass("panel-collapse collapse in");
		

	});

	$("#next3").click(function() {

		$("#collapseFour").addClass("panel-collapse collapse in");
		

	});

	$("#approve").click(function() {
		$(".success").show();
		$(".bs-example").css("margin-top", "60px");
	});

	// Add minus icon for collapse element which is open by default
	$(".collapse.in").each(
			function() {
				$(this).siblings(".panel-heading").find(".glyphicon").addClass(
						"glyphicon-minus").removeClass("glyphicon-plus");
			});

	// Toggle plus minus icon on show hide of collapse element
	$(".collapse").on(
			'show.bs.collapse',
			function() {
				$(this).parent().find(".glyphicon").removeClass(
						"glyphicon-plus").addClass("glyphicon-minus");
			}).on(
			'hide.bs.collapse',
			function() {
				$(this).parent().find(".glyphicon").removeClass(
						"glyphicon-minus").addClass("glyphicon-plus");
			});

	var table = $('#example').DataTable({
		responsive : true
	});

	new $.fn.dataTable.FixedHeader(table);

}
