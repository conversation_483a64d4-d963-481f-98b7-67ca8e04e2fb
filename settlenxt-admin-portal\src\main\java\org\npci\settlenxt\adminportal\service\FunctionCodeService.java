package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.FunctionCodeDTO;
import org.springframework.stereotype.Service;

@Service
public interface FunctionCodeService {

	 List<FunctionCodeDTO> getFunctionCodeList();

	 List<FunctionCodeDTO> getPendingFunctionCode();

	 FunctionCodeDTO getFunctionCodeMainInfo(int functionCodeId);

	 FunctionCodeDTO getFunctionCodeStgInfo(String functionCodeId);

	 FunctionCodeDTO addEditFunctionCode(FunctionCodeDTO functionCodeDto);

	 FunctionCodeDTO getFunctionCodeForEdit(int functionCodeId);

	 FunctionCodeDTO approveOrRejectFunctionCode(int functionCodeId, String status, String remarks);

	 FunctionCodeDTO discardFunctionCode(int functionCodeId);

	 FunctionCodeDTO getFunctionCodeStg(int functionCodeId);

	 String approveOrRejectFunctionCodeBulk(String bulkApprovalReferenceNoList, String status, String remarks);

	 int checkDuplicateData(FunctionCodeDTO functionCodeDto);

}
