function bindSettlementBinOnloadFunctions() {
	$('#settlementBin').change(function() {
		if ($('#settlementBin').val() != "0") {
			$('#errsettlementBin').hide();
		}
		if ($('#settlementBin').val() == 1) {
			$('#oldOrNewBinFlag').val("NewBin");
			$('#settlementBinId').val("");
			$('#settlementBinIdDiv').show();
		} else {
			$('#oldOrNewBinFlag').val("OldBin");

			$('#settlementBinIdDiv').hide();
		}
	}
	);
	$('#saveSettlementBin')
		.click(
			function() {
				var check = false;



				if (!validateSettBinIdForSaving('settlementBinId',
					'errsettlementBinId')) {
					check = true;
				}


				if ($('#currencyCode').val() == '0') {

					$("#errcurrencyCode").find('.error').html("Please select Currency Code");

					$('#errcurrencyCode').show();
					check = true;
				} else {
					$('errcurrencyCode').hide();
				}
				if ($('#clearingAgencyType').val() == '') {

					$("#errclearingAgencyType").find('.error').html("Please select Clearing Agency");

					$('#errclearingAgencyType').show();
					check = true;
				} else {
					$('errclearingAgencyType').hide();
				}




				if ($('#isDefaultBin').prop("checked")) {
					$('#isDefaultBin').val("Y");
				}
				else {
					$('#isDefaultBin').val("N");
				}

				if (!check) {

					duplicateSettlementBinCheckSaving();
				}
				else {
					return false;
				}

			});
	$('#clearSettlementBin').click(function() {

	

		$('#errcurrencyCode').hide();
		$('#errsettlementBin').hide();
		$('#errsettlementBinId').hide();
		$('#errisDefaultBin').hide();
		$('#currencyCode').val(0);
		$('#settlementBinId').val('');
		$('#settlementBinId').prop("disabled", false);
		$('#settlementOldBinNumber').val('');
		$('#isDefaultBin').prop("checked", false);
	});
	clearSettlementBinData();

}
function clearSettlementBinData() {
	
	$('#errcurrencyCode').hide();
	$('#errsettlementBinId').hide();
	$('#errisDefaultBin').hide();

	$('#currencyCode').val(0);
	$('#settlementBinId').val('');
	$('#settlementBinId').prop("disabled", false);
	$('#isDefaultBin').prop("checked", true);
}
function editSettlementBin(settlementBinNumber) {
	clearSettlementBinData();
	var settlementDataIndex = settlementBinData.findIndex(obj => obj.settlementBinNumber == settlementBinNumber);
	var settlementBinId = $('#settlementBinId').val();
	var settlementModel = settlementBinData[settlementDataIndex];
	if (settlementModel) {
		 settlementBinId = settlementModel.settlementBinId;
		$('#settlementBinId').val(settlementBinId);
		if (!settlementModel.isNew) {
			$('#settlementBinId').prop("disabled", true);
		}
		$('#currencyCode').val(settlementModel.settlementCurrency);
		$('#clearingAgencyType').val(settlementModel.clearingAgencyType);
		$('#settlementOldBinNumber').val(settlementBinNumber);

		if (settlementModel.isDefault == 'Y') {
			$('#isDefaultBin').val('Y');
			$('#isDefaultBin').prop('checked', true);
		} else {
			$('#isDefaultBin').prop('checked', false);
			$('#isDefaultBin').val('N');
		}

	}
}
function blockSettlementBin(settlementBinNumber) {
	openConfirmDialog('Do you want to block the selected settlement bin?', blockSettlementBinAction, settlementBinNumber);
}
function blockSettlementBinAction(settlementBinNumber) {
	var settlementDataIndex = settlementBinData.findIndex(obj => obj.settlementBinNumber == settlementBinNumber);
	var settlementModel = settlementBinData[settlementDataIndex];
	if (settlementModel) {
		settlementModel.status = 'B';
		settlementModel.actionType = 'Edit';
	}
	renderSettlementBinTable();
	refereshSettlementBinCombos();
	unableSave();
}
function unblockSettlementBin(settlementBinNumber) {
	openConfirmDialog('Do you want to unblock the selected settlement bin?', unblockSettlementBinAction, settlementBinNumber);
}
function unblockSettlementBinAction(settlementBinNumber) {
	var settlementDataIndex = settlementBinData.findIndex(obj => obj.settlementBinNumber == settlementBinNumber);
	var settlementModel = settlementBinData[settlementDataIndex];
	if (settlementModel) {
		settlementModel.status = 'A';
		settlementModel.actionType = 'Edit';
	}
	renderSettlementBinTable();
	unableSave();
}
function deleteSettlementBin(settlementBinNumber) {
	openConfirmDialog('Do you want to delete the selected settlement bin?', deleteSettlementBinAction, settlementBinNumber);
}
function deleteSettlementBinAction(settlementBinNumber) {
	$("#errsettlementBin").hide();
	if(isSettlementAssociatedToIssuerBin(settlementBinNumber) || isSettlementAssociatedToAcquirerBin(settlementBinNumber)){
	 		$("#errsettlementBin").find('.error').html("Settlement bin can not be deleted as it is associated with one or more acquirer/issuer/token bins");
	 		$("#errsettlementBin").show();
			return ;	
	  } 
	var settlementDataIndex = settlementBinData.findIndex(obj => obj.settlementBinNumber == settlementBinNumber);
	var settlementModel = settlementBinData[settlementDataIndex];
	if (settlementModel) {
	if(settlementModel.isDefault=='Y'){
	 		$("#errsettlementBin").find('.error').html("Settlement bin can not be deleted as it is marked as default settlement bin");
	 		$("#errsettlementBin").show();
			return ;	
	  } 
		settlementBinData = settlementBinData.filter(obj => obj.settlementBinNumber != settlementBinNumber);
	}
	
	clearSettlementBinData();

	renderSettlementBinTable();
	refereshSettlementBinCombos();
	unableSave();
}
function resetSettlementBinData() {
	settlementBinData.forEach(function(settlementModel) {
		settlementModel.isNew = false;
	});
	refereshSettlementBinCombos();
}
function refereshSettlementBinCombos() {
	$("#acqSettlementBin").empty();
	$("#acqSettlementBin").append('<option value="0">--Select--</option>');
	settlementBinData.forEach(function(settlementModel) {
	
	if(settlementModel.status=='A'){
		$("#acqSettlementBin").append(
			'<option value="' + settlementModel.settlementBinNumber + '">'
			+ settlementModel.settlementBinNumber + '</option>');
	}});
	$("#issSettlementBin").empty();
	$("#issSettlementBin").append('<option value="0">--Select--</option>');
	settlementBinData.forEach(function(settlementModel) {
		if(settlementModel.status=='A'){
		$("#issSettlementBin").append(
			'<option value="' + settlementModel.settlementBinNumber + '">'
			+ settlementModel.settlementBinNumber + '</option>');
	}});

	
}

function saveSettlementBin() {
	var settlementBinId = $('#settlementBinId').val();
	var settlementBinNumber = $('#ifscCode').val() + settlementBinId;
	$("#errsettlementBin").hide();
	var settlementModel = {};
	var settlementOldBinNumber = $('#settlementOldBinNumber').val();
	if(settlementBinData.length==0 && $('#isDefaultBin').val()=='N'){
 		$("#errsettlementBin").find('.error').html("Kindly mark this fisrt settlement bin as default bin");
 		$("#errsettlementBin").show();
 		console.log("Delete Validation")
		return ;	
  	}
	if(settlementOldBinNumber==''){
	  settlementOldBinNumber=settlementBinNumber;
	  
	}
	else if($('#settlementOldBinNumber').val()!=settlementBinNumber) {	
	  if(isSettlementAssociatedToIssuerBin($('#settlementOldBinNumber').val()) || isSettlementAssociatedToAcquirerBin($('#settlementOldBinNumber').val())){
	 		$("#errsettlementBin").find('.error').html("Settlement bin id can not be edited as it is associated with one or more acquirer/issuer/token bins");
	 		$("#errsettlementBin").show();
	 		console.log("Delete Validation")
			return ;	
	  } 
	}
	var settlementDataIndex = settlementBinData.findIndex(obj => obj.settlementBinNumber == settlementOldBinNumber);
	
    
	if (settlementDataIndex < 0) {
		settlementModel = {};
		settlementModel.settlementBinId = settlementBinId;
		settlementModel.settlementBinNumber = settlementBinNumber;
		settlementModel.isDefault = $('#isDefaultBin').val();
		settlementModel.settlementCurrency = $('#currencyCode').val();
		settlementModel.clearingAgencyType = $('#clearingAgencyType').val();

		settlementModel.status = 'A';
		settlementModel.isNew = true;
		settlementBinData.push(settlementModel);
		settlementModel.actionType = 'Add';
		refereshSettlementBinCombos();
	}
	else {
		if($('#settlementOldBinNumber').val()!=settlementBinNumber){
	   		var otherSettlementDataIndex = settlementBinData.findIndex(obj => obj.settlementBinNumber == settlementBinNumber);
		    if(otherSettlementDataIndex!=-1 ){
		    	$("#errsettlementBinId").find('.error').html("Settlement Bin ID is existing already");
		    	return;
		    }
	    } 
		let settlementBinDataNew = settlementBinData.filter(obj => obj.settlementBinNumber != $('#settlementOldBinNumber').val());
		settlementBinDataNew = settlementBinDataNew.filter(obj => obj.isDefault == 'Y');
		if(settlementBinDataNew.length==0 && $('#isDefaultBin').val()=='N'){
	 		$("#errsettlementBin").find('.error').html("Participant should have at least one default Settlement Bin");
	 		$("#errsettlementBin").show();
	 		console.log("Delete Validation")
			return ;	
	  	}
	  	
		settlementModel = settlementBinData[settlementDataIndex];
		setModelValues(settlementModel, settlementBinNumber, settlementBinId);	
	}
	checkSettlementBinNo(settlementBinNumber);

	refereshSettlementBinCombos();
	renderSettlementBinTable();

	$('#settlementBinId').val("");
	$('#currencyCode').val("0");
	$('#isDefaultBin').prop('checked', true);
	$('#settlementBinId').prop("disabled", false);
	unableSave();
}
function setModelValues(settlementModel, settlementBinNumber, settlementBinId) {
	if ((settlementModel.settlementBinNumber != settlementBinNumber) || (settlementModel.isDefault != $('#isDefaultBin').val()) || (settlementModel.settlementCurrency != $('#currencyCode').val()) || (settlementModel.clearingAgencyType != $('#clearingAgencyType').val())) {
		settlementModel.settlementBinId = settlementBinId;
		settlementModel.settlementBinNumber = settlementBinNumber;
		settlementModel.isDefault = $('#isDefaultBin').val();
		settlementModel.settlementCurrency = $('#currencyCode').val();
		settlementModel.clearingAgencyType = $('#clearingAgencyType').val();
		settlementModel.actionType = 'Edit';
	}
}

function checkSettlementBinNo(settlementBinNumber) {
	settlementBinData.forEach(function (settlementModel) {
		if ($('#isDefaultBin').val() == 'Y') {
			if (settlementModel.settlementBinNumber != settlementBinNumber) {
				if (settlementModel.isDefault == 'Y') {
					settlementModel.actionType = 'Edit';
				}
				settlementModel.isDefault = 'N';
			}
		}
	});
}

function renderSettlementBinTable() {
	var settlmentTableData = "";
	var participantId = $('#participantId').val();
	settlementBinData.forEach(function(settlementModel) {
		var statusDesc = settlementModel.status == 'B' ? 'Blocked' : 'Active';
		var rowData = '<tr>' +
			'<td>' + getTrimmedString(participantId) + '</td>' +
			'<td>' + getTrimmedString(settlementModel.settlementBinNumber) + '</td>' +
			'<td>' + getTrimmedString(currencyCodeMapping[settlementModel.settlementCurrency]) + '</td>' +
			'<td>' + getTrimmedString(settlementModel.clearingAgencyType) + '</td>' +
			'<td>' + getTrimmedString(settlementModel.isDefault) + '</td>' +
			'<td>' + statusDesc + '</td>' +
			'<td>' +
			'<a href="javascript:editSettlementBin(\'' + settlementModel.settlementBinNumber + '\')"><span class="glyphicon glyphicon-pencil my-tooltip" title="EDIT"></span></a>';
		if (settlementModel.isNew) {
			rowData += ' &nbsp; &nbsp; <a href="javascript:deleteSettlementBin(\'' + settlementModel.settlementBinNumber + '\')"><span class="glyphicon glyphicon-trash my-tooltip" title="DELETE"></span></a>';
		}
		if (settlementModel.status == 'B') {
			rowData += ' &nbsp; &nbsp; <a href="javascript:unblockSettlementBin(\'' + settlementModel.settlementBinNumber + '\')"><span class="glyphicon glyphicon-ok-circle my-tooltip" title="UNBLOCK"></span></a>';
		} else {
			rowData += ' &nbsp; &nbsp; <a href="javascript:blockSettlementBin(\'' + settlementModel.settlementBinNumber + '\')"><span class="glyphicon glyphicon-ban-circle my-tooltip " title="BLOCK"></span></a></td>';
		}
		rowData += '</tr> ';
		settlmentTableData += rowData;
	});
	$('#settlementBinsList').html(settlmentTableData);
}

function validateSettBinId(id, _msgID) {
	var settlementBinId = $('#settlementBinId').val();
	var settlementBinNumber = $('#ifscCode').val() + settlementBinId;
	$("#errsettlementBin").hide();
	
	var settlementOldBinNumber = $('#settlementOldBinNumber').val();

	var binId = (document.getElementById(id).value)
		.replace(/^\s*|\s*$/g, '');
	
	var regEx = /^[A-Za-z0-9]+$/;

	if (binId == "") {
		$("#errsettlementBinId").find('.error').html("Please Enter Settlement Bin ID");
	$('#errsettlementBinId').show();

		return false;

	}
	else if (binId.length!=2 || (binId != "" && !regEx.test(binId))) {
	$("#errsettlementBinId").find('.error').html("Settlement Bin Id must be 2 alphaNumerics");
	$('#errsettlementBinId').show();

		return false;
	}

	 else {
		$("#errsettlementBinId").find('.error').html(" ");

	}
	let settlementBinDataNew = settlementBinData.filter(obj => obj.settlementBinNumber != settlementOldBinNumber);
	settlementBinDataNew = settlementBinDataNew.filter(obj => obj.settlementBinNumber == settlementBinNumber);
	if(settlementBinDataNew.length>0 ){
		$("#errsettlementBinId").find('.error').html("Settlement Bin ID is existing already");
		$('#errsettlementBinId').show();
		return false;
  	}

	duplicateSettlementBinCheck();
	
}
function validateSettBinIdForSaving(id, _msgID) {
	var settlementBinId = $('#settlementBinId').val();
	var settlementBinNumber = $('#ifscCode').val() + settlementBinId;
	$("#errsettlementBin").hide();
	
	var settlementOldBinNumber = $('#settlementOldBinNumber').val();

	var binId = (document.getElementById(id).value)
		.replace(/^\s*|\s*$/g, '');
	
	var regEx = /^[A-Za-z0-9]+$/;

	if (binId == "") {
		$("#errsettlementBinId").find('.error').html("Please Enter Settlement Bin ID");
	$('#errsettlementBinId').show();

		return false;

	}
	else if (binId.length!=2 || (binId != "" && !regEx.test(binId))) {
	$("#errsettlementBinId").find('.error').html("Settlement Bin Id must be 2 alphaNumerics");
	$('#errsettlementBinId').show();

		return false;
	}

	 else {
		$("#errsettlementBinId").find('.error').html(" ");

	}
	let settlementBinDataNew = settlementBinData.filter(obj => obj.settlementBinNumber != settlementOldBinNumber);
	settlementBinDataNew = settlementBinDataNew.filter(obj => obj.settlementBinNumber == settlementBinNumber);
	if(settlementBinDataNew.length>0 ){
		$("#errsettlementBinId").find('.error').html("Settlement Bin ID is existing already");
		$('#errsettlementBinId').show();
		return false;
  	}
	return true;

	
}
