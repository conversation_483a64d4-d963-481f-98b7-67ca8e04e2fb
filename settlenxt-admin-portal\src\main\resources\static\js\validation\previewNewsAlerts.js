function viewNews(reqId) {
	let url = '/getNewsAlerts';
	var data = "reqType," + "V" + ",refNumber," + reqId + ",screenName," + $('#screenName').val();

	postData(url, data);
	clickAndDisable(this);
}




$(document).ready(function () {
	let newsAndAlertIds = [];
	var cursorPosition = null;
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	$("#tabnew").dataTable({
		"bServerSide": false,
		"bProcessing": true,
		paging: true,
		pageLength: 10,
		"sAjaxSource": "searchNewsAlerts",
		"bJQueryUI": true,
		"sServerMethod": "POST",
		"bDestroy": true,
		"language": {
			"search": "Search : "
		},

		"fnRowCallback": function (nRow, aData, _iDisplayIndex) {


			$('td:eq(0)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(1)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(2)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(3)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(4)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(5)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(6)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(7)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(8)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(9)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });

			return nRow;
		},
		"fnServerParams": function (aoData) {
			aoData.push({ "name": "_TransactToken", "value": tokenValue }

			);
		},

		"fnServerData": function (sSource, aoData, fnCallback) {
			handleServerData(sSource, aoData, fnCallback);
		},
		initComplete: function () {
			var api = this.api();


			$('#IsLastLevel').val(NaN);

			// For each column
			api
				.columns()
				.eq(0)
				.each(function (colIdx) {
					// Set the header cell to contain the input element
					var cell = $('#tabnew thead tr th').eq(
						$(api.column(colIdx).header()).index()
					);
					var title = $(cell).text();


					if (colIdx < actionColumnIndex) {
						$(cell).html(title + '<br><input class="search-box"   type="text" />');

						// On every keypress in this input
						$(
							'input',
							$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
						)
							.off('keyup change')
							.on('change', function (_e) {
								// Get the search value
								$(this).attr('title', $(this).val());
								var regexr = '({search})';


								// Search the column for that value
								api
									.column(colIdx)
									.search(
										this.value != ''
											? regexr.replace('{search}', '(((' + this.value + ')))')
											: '',
										this.value != '',
										this.value == ''
									)
									.draw();
							})
							.on('click', function (e) {
								e.stopPropagation();
							})
							.on('keyup', function (e) {
								handleKeyUp(e);
							});
					} else {
						$(cell).html(title + '<br> &nbsp;');
					}
				});
			$('#tabnew_filter').hide();
			$('.dt-buttons').hide();
		},

		columnDefs: [
			{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
		],

		"order": [],

		dom: 'lBfrtip',

		buttons: [
			{
				extend: 'excelHtml5',
				text: 'Export',
				filename: 'News and Alerts',
				header: 'false',
				title: null,
				sheetName: 'News and Alerts',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			},
			{
				extend: 'csvHtml5',
				text: 'Export',
				filename: 'News and Alerts',
				header: 'false',
				title: null,
				sheetName: 'News and Alerts',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			}
		],

		searching: true,
		info: true
	});

	$('#tabnew').on('click', '.viewNews', function () {

		let url = '/getNewsAlerts';
		var refNumber = $(this).data("value").split('-')[0];
		var data = "reqId," + $(this).data("value") + ",reqType," + "V" + ",refNumber," + refNumber;
		postData(url, data);
	});



	$("#tabnewPending").dataTable({
		"bServerSide": false,
		paging: true,
		pageLength: 10,
		"sAjaxSource": "searchPendingNewsAlerts",
		"bProcessing": true,
		"bJQueryUI": true,
		"sServerMethod": "POST",
		"bDestroy": true,
		"language": {
			"search": "search : "
		},


		"fnRowCallback": function (nRow, aData, iDisplayIndex) {
			if (iDisplayIndex == 0) {
				newsAndAlertIds = [];
			}
			if (aData[13] == 'Checker') {
				console.log("In checker");
				if (aData[8] == 'Rejected') {
					$('td:eq(0)', nRow).html('<input type=checkbox style="display:none;" name="type" value=\'' + aData[1] + '\'></input>');
				}
				if (aData[8] == 'Pending') {
					newsAndAlertIds.push(aData[1]);
					$('td:eq(0)', nRow).html('<input type=checkbox name="type" id="selectSingle" onclick="mySelect();" value=\'' + aData[1] + '\'></input>');
				}

				if (aData[8] == 'Rejected') {
					$('td:eq(1)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(2)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(3)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(4)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(5)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(6)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(7)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(8)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(9)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(10)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(11)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
					$('td:eq(12)', nRow).addClass('alignment').click(function () { viewNews(aData[1]); });
				}
				if (aData[8] == 'Pending') {
					$('td:eq(1)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(2)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(3)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(4)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(5)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(6)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(7)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(8)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(9)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(10)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(11)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
					$('td:eq(12)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[1]); });
				}


				return nRow;
			}
			else {
				console.log("In maker");
				if (aData[7] == 'Rejected') {
					console.log("In rej");
					$('td:eq(0)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(1)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(2)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(3)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(4)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(5)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(6)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(7)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(8)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(9)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(10)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
					$('td:eq(11)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
				}
				if (aData[7] == 'Pending') {
					console.log("In pen");
					$('td:eq(0)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(1)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(2)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(3)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(4)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(5)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(6)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(7)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(8)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(9)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(10)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
					$('td:eq(11)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });

				}
			}
			return nRow;
		},

		"fnServerParams": function (aoData) {
			console.log(aoData);
			aoData.push({ "name": "_TransactToken", "value": tokenValue }

			);
		},

		"fnServerData": function (sSource, aoData, fnCallback) {
			console.log("sSource");
			$.ajax({
				"dataType": 'json',
				"type": "POST",
				"url": sSource,
				"data": aoData,
				"success": function (json) {
					fnCallback(json);

				}
			});
		},
		initComplete: function () {
			var api = this.api();

			// For each column
			api
				.columns()
				.eq(0)
				.each(function (colIdx) {
					//If first column to be skipped to include the filter for the reasons line check box 
					if (!(colIdx == 0 && firstColumnToBeSkippedInFilterAndSort)) {
						// Set the header cell to contain the input element
						var cell = $('#tabnewPending thead tr th').eq(
							$(api.column(colIdx).header()).index()
						);
						var title = $(cell).text();
						newsAndAlertIds = handleGestures(colIdx, cell, title, api, newsAndAlertIds, cursorPosition);
					}
				});
			$('#tabnewPending_filter').hide();

		},
		// Disabled ordering for first column in case

		columnDefs: [
			{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
		],

		"order": [],
		dom: 'lBfrtip',
		buttons: [
			{
				extend: 'excelHtml5',
				text: 'Export',
				filename: 'News&Alerts',
				header: 'false',
				title: null,
				sheetName: 'News&Alerts',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			},
			{
				extend: 'csvHtml5',
				text: 'Export',
				filename: 'News&Alerts',
				header: 'false',
				title: null,
				sheetName: 'News&Alerts',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			}

		],

		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true,
	});



	$('#tabnewPending').on('click', '.viewPendingNews', function () {

		let url = '/getNewsAlerts';
		var refNumber = $(this).data("value").split('-')[0];
		var data = "refNumber," + refNumber + ",reqType," + "P";
		postData(url, data);
	});

	$("#tabnewSavedNews").dataTable({
		"bServerSide": false,
		paging: true,
		pageLength: 10,
		"sAjaxSource": "searchSavedNewsAlerts",
		"bProcessing": true,
		"bJQueryUI": true,
		"sServerMethod": "POST",
		"bDestroy": true,
		"fnRowCallback": function (nRow, aData, _iDisplayIndex) {
			console.log("j");
			$('td:eq(0)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(1)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(2)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(3)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(4)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(5)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(6)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(7)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(8)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			$('td:eq(9)', nRow).addClass('alignment').click(function () { viewNews(aData[0]); });
			return nRow;
		},

		"fnServerParams": function (aoData) {
			console.log("aoData");
			aoData.push({ "name": "_TransactToken", "value": tokenValue }

			);
		},

		"fnServerData": function (sSource, aoData, fnCallback) {
			console.log("i");
			$.ajax({
				"dataType": 'json',
				"type": "POST",
				"url": sSource,
				"data": aoData,
				"success": function (json) {
					fnCallback(json);

				}
			});
		},
		initComplete: function () {
			var api = this.api();
			console.log("api " + api);

			$('#IsLastLevel').val(NaN);

			// For each column
			api
				.columns()
				.eq(0)
				.each(function (colIdx) {
					// Set the header cell to contain the input element
					var cell = $('#tabnewSavedNews thead tr th').eq(
						$(api.column(colIdx).header()).index()
					);
					var title = $(cell).text();

					console.log("title " + title);
					if (colIdx < actionColumnIndex) {
						$(cell).html(title + '<br><input class="search-box"   type="text" />');

						// On every keypress in this input
						$(
							'input',
							$('#tabnewSavedNews thead tr th').eq($(api.column(colIdx).header()).index())
						)
							.off('keyup change')
							.on('change', function (_e) {
								// Get the search value
								console.log("");
								$(this).attr('title', $(this).val());
								var regexr = '({search})';


								// Search the column for that value
								api
									.column(colIdx)
									.search(
										this.value != ''
											? regexr.replace('{search}', '(((' + this.value + ')))')
											: '',
										this.value != '',
										this.value == ''
									)
									.draw();
							})
							.on('click', function (e) {
								e.stopPropagation();
							})
							.on('keyup', function (e) {
								handleKeyUp(e);
							});
					} else {
						$(cell).html(title + '<br> &nbsp;');
					}
				});
			$('#tabnewSavedNews_filter').hide();
			$('.dt-buttons').hide();
		},

		columnDefs: [
			{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
		],

		"order": [],
		dom: 'lBfrtip',

		buttons: [
			{
				extend: 'excelHtml5',
				text: 'Export',
				filename: 'News&Alert',
				header: 'false',
				title: null,
				sheetName: 'News&Alert',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			},
			{
				extend: 'csvHtml5',
				text: 'Export',
				filename: 'News&Alert',
				header: 'false',
				title: null,
				sheetName: 'News&Alert',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			}
		],

		searching: true,
		info: true


	});


	$("#tabnewDeletedNews").dataTable({
		"bServerSide": false,
		"paging": true,
		"pageLength": 10,
		"sAjaxSource": "searchDeletedNewsAlerts",
		"bProcessing": true,
		"bJQueryUI": true,
		"sServerMethod": "POST",
		"bDestroy": true,

		"fnRowCallback": function (nRow, aData, _iDisplayIndex) {
			$('td:eq(0)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
			$('td:eq(1)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
			$('td:eq(2)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
			$('td:eq(3)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
			$('td:eq(4)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
			$('td:eq(5)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
			$('td:eq(6)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
			$('td:eq(7)', nRow).html('Deleted');
			$('td:eq(8)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });
			$('td:eq(9)', nRow).addClass('alignment').click(function () { viewPendingNews(aData[0]); });

			return nRow;
		},

		"fnServerParams": function (aoData) {
			console.log("io");
			aoData.push({ "name": "_TransactToken", "value": tokenValue }

			);
		},

		"fnServerData": function (sSource, aoData, fnCallback) {
			handleServerData(sSource, aoData, fnCallback);
		},
		initComplete: function () {
			var api = this.api();


			$('#IsLastLevel').val(NaN);

			// For each column
			api
				.columns()
				.eq(0)
				.each(function (colIdx) {
					// Set the header cell to contain the input element
					var cell = $('#tabnewDeletedNews thead tr th').eq(
						$(api.column(colIdx).header()).index()
					);
					var title = $(cell).text();

					console.log("title");
					if (colIdx < actionColumnIndex) {
						$(cell).html(title + '<br><input class="search-box"   type="text" />');

						// On every keypress in this input
						$(
							'input',
							$('#tabnewDeletedNews thead tr th').eq($(api.column(colIdx).header()).index())
						)
							.off('keyup change')
							.on('change', function (_e) {
								console.log();
								// Get the search value
								$(this).attr('title', $(this).val());
								var regexr = '({search})';


								// Search the column for that value
								api
									.column(colIdx)
									.search(
										this.value != ''
											? regexr.replace('{search}', '(((' + this.value + ')))')
											: '',
										this.value != '',
										this.value == ''
									)
									.draw();
							})
							.on('click', function (e) {
								e.stopPropagation();
							})
							.on('keyup', function (e) {
								console.trace("");
								e.stopPropagation();

								$(this).trigger('change');
								if (cursorPosition && cursorPosition != null) {
									$(this)
										.focus()[0]
										.setSelectionRange(cursorPosition, cursorPosition);
								}
							});
					} else {
						$(cell).html(title + '<br> &nbsp;');
					}
				});
			$('#tabnewDeletedNews_filter').hide();
			$('.dt-buttons').hide();
		},

		columnDefs: [
			{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
		],

		"order": [],
		dom: 'lBfrtip',

		buttons: [
			{
				extend: 'excelHtml5',
				text: 'Export',
				filename: 'News&Alert',
				header: 'false',
				title: null,
				sheetName: 'News&Alert',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			},
			{
				extend: 'csvHtml5',
				text: 'Export',
				filename: 'News&Alert',
				header: 'false',
				title: null,
				sheetName: 'News&Alert',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			}
		],

		searching: true,
		info: true


	});

	$("#excelExport").on("click", function () {
		$(".buttons-excel").trigger("click");
	});

	$("#csvExport").on("click", function () {
		$(".buttons-csv").trigger("click");
	});

	$("#clearFilters").on("click", function () {
		$(".search-box").each(function () {
			$(this).val("");
			$(this).trigger("change");
		});
	});

	$("#selectAll").click(function () {



		$('#jqueryError4').hide();
		$("input[type=checkbox]").prop('checked', $(this).prop('checked'));

		var footerDataHeader = document.getElementById("newsIds");

		var i = 0;
		var newsAndAlertIds2 = [];
		$("#tabnewPending tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
			newsAndAlertIds2.push(this.value);
			i++;
		});
		if (newsAndAlertIds.length > 0) {
			handleModalShow(newsAndAlertIds2, footerDataHeader, newsAndAlertIds);
		} else {

			if (newsAndAlertIds2.length > 0) {


				if (referenceNoListPendings.length > 0) {
					footerDataHeader.innerHTML = referenceNoListPendings.length + "     " + "records are selected";

					if ($('#selectAll').is(':checked')) {
						$("#toggleModalNews").modal('show');

					}
					else {
						$("#toggleModalNews").modal('hide');

					}
				}
			}
		}



	});


});

function handleKeyUp(e) {
	e.stopPropagation();

	$(this).trigger('change');
	if (cursorPosition && cursorPosition != null) {
		$(this)
			.focus()[0]
			.setSelectionRange(cursorPosition, cursorPosition);
	}
}


function handleServerData(sSource, aoData, fnCallback) {
	$.ajax({
		"dataType": 'json',
		"type": "POST",
		"url": sSource,
		"data": aoData,
		"success": function (json) {
			fnCallback(json);

		}
	});
}

function handleGestures(colIdx, cell, title, api, newsAndAlertIds, cursorPosition) {
	if (colIdx < actionColumnIndex) {

		$(cell).html(title + '<br><input class="search-box"   type="text" />');

		// On every keypress in this input
		$(
			'input',
			$('#tabnewPending thead tr th').eq($(api.column(colIdx).header()).index())
		)
			.off('keyup change')
			.on('change', function (_e) {
				console.table("");
				// Get the search value
				$(this).attr('title', $(this).val());
				var regexr = '({search})';


				// Search the column for that value
				api
					.column(colIdx)
					.search(
						this.value != ''
							? regexr.replace('{search}', '(((' + this.value + ')))')
							: '',
						this.value != '',
						this.value == ''
					)
					.draw();
				if (this.value == '') {
					newsAndAlertIds = [];
				}
			})
			.on('click', function (e) {
				e.stopPropagation();
			})
			.on('keyup', function (e) {
				console.log("");
				e.stopPropagation();

				$(this).trigger('change');
				if (cursorPosition && cursorPosition != null) {
					$(this)
						.focus()[0]
						.setSelectionRange(cursorPosition, cursorPosition);
				}
			});
	} else {
		$(cell).html(title + '<br> &nbsp;');
	}
	return newsAndAlertIds;
}

function handleModalShow(newsAndAlertIds2, footerDataHeader, newsAndAlertIds) {
	if (newsAndAlertIds2.length > 0) {
		footerDataHeader.innerHTML = newsAndAlertIds.length + "     " + "records are selected";

		if ($('#selectAll').is(':checked')) {
			$("#toggleModalNews").modal('show');

		}
		else {
			$("#toggleModalNews").modal('hide');

		}
	}
}

function mySelect() {


	var array = [];

	$("input:checkbox[name=type]:checked").each(function () {
		array.push($(this).val());
	});


	var referenceNoList = document.getElementById("newsIds");
	if (array.length > 0) {
		if (array.length == referenceNoListPendings.length) {
			$('#selectAll').prop('checked', true);

			referenceNoList.innerHTML = referenceNoListPendings.length + "     " + "records are selected";



			$("#toggleModalNews").modal('show');
		}
		else {
			$("#toggleModalNews").modal('hide');

		}
	}

}

function ApproveorRejectBulkNews(type, action) {

	var url = '/approveNewsAlertsBulk';

	var array = [];
	var data = "";

	if (action == 'No') {
		$("input:checkbox[name=type]:checked").each(function () {
			array.push($(this).val());
		});
	}
	else if (action == 'All') {

		if (newsAndAlertIds.length > 0) {
			array = newsAndAlertIds;
		} else {
			array = referenceNoListPendings;
		}

	}



	var referenceIdIdList = "";

	for (let i of array) {
		referenceIdIdList = referenceIdIdList + i + "|";
	}




	if (array.length > 0) {

		$('#jqueryError4').hide()

		if (type == 'A') {

			data = "status," + "A" + ",bulkApprovalReferenceNoList," + referenceIdIdList;
		}
		else if (type == 'R') {

			data = "status," + "R" + ",bulkApprovalReferenceNoList," + referenceIdIdList;
		}


		postData(url, data);
	} else {
		$('#errorStatus4').html('Please Select  Atleast One Record');
		$('#jqueryError4').show();
	}

}


function deselectAll() {

	$('#selectAll').prop('checked', false);
	var ele = document.getElementsByName('type');


	for (let i of ele) {
		if (i.type == 'checkbox')
			i.checked = false;
	}


}

function clickAndDisable(link) {
	// disable subsequent clicks
	link.onclick = function (event) {
		event.preventDefault();
	}
}

function submitForm(url) {
	var data = "";
	postData(url, data);
}

function viewPendingNews(reqId) {

	let url = '/getNewsAlerts';
	var data = "reqType," + "P" + ",refNumber," + reqId;
	postData(url, data);
	clickAndDisable(this);
}

