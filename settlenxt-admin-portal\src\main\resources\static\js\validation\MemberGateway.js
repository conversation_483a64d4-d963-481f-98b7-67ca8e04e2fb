function getCity(stateFieldId, cityFieldId) {
    var tokenValue = document.getElementsByName("_TransactToken")[0].value;
    if ($('#' + stateFieldId).val() != '0') {
        $('#err' + stateFieldId).hide();
        var stateId = $('#' + stateFieldId).val();
        $.ajax({
            url: "getCityMaster",
            type: "POST",
            data: {
                stateId: stateId,
                _TransactToken: tokenValue
            },
            dataType: "json",
		"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenValue);},
            success: function (data) {
                $("#memberBody").removeClass("loadingdata");
                $('#' + cityFieldId).empty();
                $('#' + cityFieldId)
                    .append('<option value="0">--Select--</option>');
                $.each(data,
                    function (_index, option) {
                        $(
                            '#' + cityFieldId)
                            .append(
                                '<option value="'
                                + option.cityId
                                + '">'
                                + option.cityName
                                + '</option>');
                    });



            }
        });

    } else {
        $('#' + cityFieldId).empty();
    }
}
function getIFSCDetails() {
    var tokenValue = document.getElementsByName("_TransactToken")[0].value;
    if ($('#ifscCode').val() != '0') {
        var ifscCode = $("#ifscCode").val();
        $("#memberBody").addClass("loadingdata");
        $.ajax({
            url: "getIFSCDetails",
            type: "POST",
            data: {
                ifscCode: ifscCode,
                _TransactToken: tokenValue
            },
            dataType: "json",
  		"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenValue);},
            success: function (data) {
                $("#memberBody").removeClass("loadingdata");
                $('#bankMasterCode').val(data.bankMasterCode);
                $('#participantId').val(data.participantId);
                $('#rtgsCode').val(data.rtgsCode);
                $('#savingsAccNumber').val(data.savingsAccNumber);
                $('#currentAccNumber').val(data.currentAccNo);
                $('#participantIdNFS').val(data.participantIdNFS);

                $('#errifscCode').find('.error').html("");
                $('#errbankMasterCode').find('.error').html("");
                $('#errrtgsCode').find('.error').html("");
                $('#errparticipantId').find('.error').html("");
                $('#errsavingsAccNumber').find('.error').html("");
                $('#errcurrentAccNumber').find('.error').html("");
                $('#errparticipantIdNFS').find('.error').html("");
                $("#uniqueBnkName").empty();
                $("#uniqueBnkName").append('<option value="1">--Others--</option>');
                $.each(data.uniqueBanks, function (_index, uniqueBankName) {
                    if (uniqueBankName != null) {
                        $("#uniqueBnkName").append(
                            '<option value="' + uniqueBankName + '">'
                            + uniqueBankName + '</option>');
                    }
                });

            },
             error: function (_error) {
            
            $("#memberBody").removeClass("loadingdata");
              $('#errifscCode').find('.error').html("Unable to fetch ifsc details");
               $('#errifscCode').show();
        }

        });



    } else {
        $('#bankMasterCode').val("");
        $('#participantId').val("");
        $('#rtgsCode').val("");
        $('#savingsAccNumber').val("");
        $('#currentAccNumber').val("");
        $('#participantIdNFS').val("");
    }
}
function saveMemberDetails() {
    var tokenValue = document.getElementsByName("_TransactToken")[0].value;
    tokenValue = $('<div>').text(tokenValue).html();
    var hasValidationErrors = validateMemberDataOnSave();
    if (!hasValidationErrors) {
        var model = createModelForSave();
        const formData = new FormData();
        formData.append('model', JSON.stringify(model));
        
        for (var i of documentData) {
        
            formData.append('document', i.fileContent);
        }
        formData.append('_TransactToken', tokenValue);
        console.log("calling loading");
        $("#memberBody").addClass("loadingdata");
        $.ajax({
            url: "addMember",
            type: "POST",
 		"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenValue);},
            enctype: 'multipart/form-data',
            data: formData,
            processData: false,
            contentType: false,
            dataType: "json",
            cache: false,
            success: function (data) {
    			if (data.success && data.success.length > 0)
    			{
    				openInformationDialog(data.success,"Member Data Save Status");
    			}
		        if (data.error && data.error.length > 0) {
    				openInformationDialog(data.error,"Member Data Save Status");
		    	}

                handleSaveSuccessAjaxResponse(data);
                $("#memberBody").removeClass("loadingdata");
            },
            error: function (_error) {
                
                $("#memberBody").removeClass("loadingdata");
            }

        });


    }
}
function submitForApproval() {
    var tokenValue = document.getElementsByName("_TransactToken")[0].value;
   tokenValue = $('<div>').text(tokenValue).html();
            
    var hasValidationErrors = validateMemberDataOnSubmit();
    if (!hasValidationErrors) {
            

    var model = createModelForSave();
    const formData = new FormData();
    formData.append('model', JSON.stringify(model));
    for (var i of documentData) {
        formData.append('document', i);
    }
    formData.append(_TransactToken, tokenValue);
    $("#memberBody").addClass("loadingdata");
    $.ajax({
        url: "submitMemberData",
        type: "POST",
 		"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenValue);},
        data: formData,
        processData: false,
        contentType: false,
        dataType: "json",
        cache: false,
        success: function (data) {
            $("#memberBody").removeClass("loadingdata");


            handleDataSuccess(data);
            if (data.error && data.error.length > 0) {
                $('#errorStatus2').html(data.error);
                $('#jqueryError2').show();
                $('#jquerySuccess').hide();
    				openInformationDialog(data.error,"Member Data Save Status");
            }

        },
        error: function (_error) {
            
            $("#memberBody").removeClass("loadingdata");
        }

    });
	}
}

function handleDataSuccess(data) {
    if (data.success && data.success.length > 0) {
        $('.btn-success').addClass('disabled');
        $('#successStatus').html(data.success);
        $("input").prop('disabled', true);
        $("select").prop('disabled', true);
        dataSubmittedApproval = true;
        $("#acqBankGroup")[0].selectize.disable();
        $("#issBankGroup")[0].selectize.disable();           
        $(".error").each(function() {
        if($(this).text().trim().length > 0) 
        {
            $(this).hide();
        }
         });
    $('#jqueryError2').hide();
    $('#jquerySuccess').show();

    if (typeof downloadFiles != "undefined") {
        document.getElementById("downloadFiles").disabled = true;
    }
    if (typeof deleteAllFiles != "undefined") {
        document.getElementById("deleteAllFiles").disabled = true;
    }
    openInformationDialog(data.success, "Member Data Save Status");
}
}

function discardMemberData() {
    var tokenValue = document.getElementsByName("_TransactToken")[0].value;
    var model = {
        "participantId": $('#participantId').val(),
        "saveMemberResult": $('#saveMemberResult').val(),
        "reqType": $('#reqType').val(),
        "parentParticipantId": $('#parentParticipantId').val(),
        "requestState": $('#requestState').val(),
        "recordStatus": $('#recStatus').val(),
        _TransactToken: tokenValue,
    }
    $("#memberBody").addClass("loadingdata");
    $.ajax({
        url: "discardMemberData",
        type: "POST",
 		"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenValue);},
        data: JSON.stringify(model),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (data) {
            $("#memberBody").removeClass("loadingdata");


            if (data.success && data.success.length > 0) {
                $('.btn-success').addClass('disabled');
                $('#successStatus').html(data.success);
                $('#jqueryError2').hide();
                $('#jquerySuccess').show();
                
            }
            if (data.error && data.error.length > 0) {
                $('#errorStatus2').html(data.error);
                $('#jqueryError2').show();
                $('#jquerySuccess').hide();
                
            }

        },
        error: function (_error) {
            $("#memberBody").removeClass("loadingdata");
        }
    });

}
function createModelForSave() {
    return {
        "memberId": $('#memberId').val(),
        "memberType": $('#memberType').val(),
        "memberName": $('#memberName').val(),
        "ifscCode": $('#ifscCode').val(),
        "bankMasterCode": $('#bankMasterCode').val(),
        "rtgsCode": $('#rtgsCode').val(),
        "participantId": $('#participantId').val(),
        "savingsAccNumber": $('#savingsAccNumber').val(),
        "currentAccNumber": $('#currentAccNumber').val(),
        "participantIdNFS": $('#participantIdNFS').val(),
        "bnkPhone": $('#bnkPhone').val(),
        "bnkPhone2": $('#bnkPhone2').val(),
        "bnkMobile": $('#bnkMobile').val(),
        "bnkMobile2": $('#bnkMobile2').val(),
        "bnkEmail": $('#bnkEmail').val(),
        "bnkEmail2": $('#bnkEmail2').val(),
        "bnkAdd": $('#bnkAdd').val(),
        "bnkCountry": $('#bnkCountry').val(),
        "bnkState": $('#bnkState').val(),
        "bnkCity": $('#bnkCity').val(),
        "bnkPincode": $('#bnkPincode').val(),
        "gstIn": $('#gstn').val(),
        "gstAdd": $('#gstAdd').val(),
        "gstCountry": $('#gstCountry').val(),
        "gstState": $('#gstState').val(),
        "gstCity": $('#gstCity').val(),
        "gstPincode": $('#gstPincode').val(),
        // "bnkPincode": $('#bnkPincode').val(),
        "addressType": $('#addressType').val(),
        "cntChkrName": $('#cntChkrName').val(),
        "bankSector": $('#bankSector').val(),
        "cntPhone": $('#cntPhone').val(),
        "cntMobile": $('#cntMobile').val(),
        "cntFax": $('#cntFax').val(),
        "cntAdd1": $('#cntAdd1').val(),
        "cntCountry": $('#cntCountry').val(),
        "cntState": $('#cntState').val(),
        "cntCity": $('#cntCity').val(),
        "cntPincode": $('#cntPincode').val(),
        "issBinType": $('#issBinType').val(),
        "issBankGroup": $('#issBankGroup').val(),
        "issProductType": $('#issProductType').val(),
        "issSettlementBin": $('#issSettlementBin').val(),
        "binNumber": $('#binNumber').val(),
        "binType": $('#binType').val(),
        "lowBin": $('#lowBin').val(),
        "highBin": $('#highBin').val(),
        "panLength": $('#panLength').val(),
        "binCardType": $('#binCardType').val(),
        "binProductType": $('#binProductType').val(),
        "binCardVariant": $('#binCardVariant').val(),
        "binCardBrand": $('#binCardBrand').val(),
        "issDomainUsage": $('#issDomainUsage').val(),
        "messageType": $('#messageType').val(),
        "cardTechnology": $('#cardTechnology').val(),
        "authMechanism": $('#authMechanism').val(),
        "subScheme": $('#subScheme').val(),
        "cardSubVariant": $('#cardSubVariant').val(),
        "programDetails": $('#programDetails').val(),
        "formFactor": $('#formFactor').val(),
        "acquirerId": $('#acquirerId').val(),
        "acqBankGroup": $('#acqBankGroup').val(),
        "acqProductType": $('#acqProductType').val(),
        "acqSettlementBin": $('#acqSettlementBin').val(),
        "acqDomainUsage": $('#acqDomainUsage').val(),
        "isOfflineAllowed": $("input[name=isOfflineAllowed]:checked").val(),
        // "isOfflineAllowed":
        // $('#isOfflineAllowed').val(),
        "reqType": $('#reqType').val(),
        "saveMemberResult": $('#saveMemberResult').val(),
        "parentParticipantId": $('#parentParticipantId').val(),
        "buttonType": $('#saveMemberData').attr('name'),
        "uniqueBnkName": $('#uniqueBnkName').val(),
        "uniqueBnk": $('#uniqueBnk').val(),
        "requestState": $('#requestState').val(),
        "webSite": $('#webSite').val(),
        "cntEmail": $('#cntEmail').val(),
        "cntDesignation": $('#cntDesignation').val(),
        "isIssOfflineAllowed": $("input[name=isIssOfflineAllowed]:checked").val(),
        "subNet": $('#subNet').val(),
        "settlementBinList": settlementBinData,
        "acqBinList": acquirerBinData,
        "issBinList": issuerBinData,
        "documents": documentData,
        "maxUser": $('#maxUser').val(),
        "markUp" :$('#markUp').val(),
        "networkLicenseId":$("networkLicenseId").val(),
        "networkIssId":$("networkIssId").val(),
        "networkSelection":$('#networkSelection').val(),
        "currencyConversionBy":$('#currencyConversionBy').val(),
          "forexId":$('#forexId').val(),
            "currencyConversionType":$('#currencyConversionType').val(),
            "isType":$('#isType').val()
        
        

    };
}
function handleSaveSuccessAjaxResponse(data) {
    if ($('#requestState').val() != "") {
        $('#memberType').attr('disabled', true);
        $('#parentParticipantId').attr('disabled', true);
        $('#ifscCode').attr('disabled', true);
    }
    $('#memberType').attr('disabled', true);
    $('#parentParticipantId').attr('disabled', true);
    $('#ifscCode').attr('disabled', true);
    
    $('#rtgsCode').attr('disabled', true);
    $('#participantId').attr('disabled', true);
    
    $('#savingsAccNumber').attr('disabled', true);
    $('#currentAccNumber').attr('disabled', true);
    	

    $('#errmemberType').hide();
    $('#errparentParticipantId').hide();
    $('#errifscCode').hide();
    $('#errbankSector').hide();
    $('#erraddressType').hide();
    $('#erruniqueBnkName').hide();
    $('#errbnkState').hide();
    $('#errgstState').hide();
    $('#errbnkCity').hide();
    $('#errgstCity').hide();
    $('#errmaxUser').hide();


    $('#saveMemberResult').val(data.saveMemberResult);
    $('#requestState').val(data.requestState);
    if($('#requestState').val()!='')
    {
   	requestStateFlag=$('#requestState').val();
   	}
    $('#memberId').val(data.memberId);
    if (data.success && data.success.length > 0) {
        $('#successStatus').html(data.success);
        $('#jqueryError2').hide();
        $('#jquerySuccess').show();
        $('#afterSave').show();
        $('#submitMemberData').attr('disabled', false);
        resetSettlementBinData();
        resetDocumentData();
    }
    if (data.error && data.error.length > 0) {
        $('#errorStatus2').html(data.error);
        $('#jqueryError2').show();
        $('#jquerySuccess').hide();
    }
}


function editMember(participantId, landingParam) {
    var url = '/editMember';
    var data = "participantId," + participantId + ",reqType," + "E" + ",editFlag," + "E"  + ",landingParam," + landingParam;
    postData(url, data);

}
function postActionMember(_action) {
    var tokenValue = document.getElementsByName("_TransactToken")[0].value;
   var rejectReason1 = $('#rejectReason').val();

var reason = "";
    if ($("#apprej").val() != "N") {
        $(".appRejMust").hide();
    } else {
        $(".appRejMust").show();
        return false;
    }

    if ($('#rejectReason').val() == "") {
        $(".remarkMust").show();
        return false;
    } else {
        document.querySelector(".button").disabled = true;
        $(".remarkMust").hide();
        reason = ifMaxLenRejectReason(tokenValue, reason, rejectReason1);
    }
}
function ifMaxLenRejectReason(tokenValue, reason, rejectReason1) {
    if (maxLengthTextArea('rejectReason')) {
        $('button').prop('disabled', true);

        var model = {
            "participantId": $('#participantId').val(),
            "approvalStatus": $('#apprej option:selected').val(),
            "rejectReason": $('#rejectReason').val(),
            _TransactToken: tokenValue
        };

        $("#memberBody").addClass("loadingdata");
        $.ajax({
            url: "approveMember",
            type: "POST",
            "beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenValue); },
            data: JSON.stringify(model),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            cache: false,
            success: function (data) {
                if (data.success && data.success.length > 0) {
                    $('#successStatus').html(data.success);
                    $('#jqueryError2').hide();
                    $('#jquerySuccess').show();
                    $('#afterSave').show();
                    $('#approvepanel').hide();
                    $('#approveMember').hide();
                    $('#approveMember').attr('disabled', true);
                    $("#apprej").attr('disabled', true);
                    $('#rejectReason').attr('disabled', true);



                    if ($('#apprej option:selected').val() == "A") {
                        reason = "Approved";
                    }
                    else {
                        reason = "Reject";
                    }
                    var requeststr = document.getElementById("request");
                    requeststr.innerHTML = reason;


                    var checkerValue = document.getElementById("checker");

                    checkerValue.innerHTML = rejectReason1;
                }
                if (data.error && data.error.length > 0) {
                    $('#errorStatus2').html(data.error);
                    $('#jqueryError2').show();
                    $('#jquerySuccess').hide();
                    $('#approvepanel').show();
                    $('#approveMember').show();
                    $('#approveMember').attr('disabled', false);
                }
                handleSaveSuccessAjaxResponse(data);
                $("#memberBody").removeClass("loadingdata");
            },
            error: function (_error) {
                $("#memberBody").removeClass("loadingdata");
            }
        });

    }
    return reason;
}

function downloadMemberDocumentBatch() {
    var url = '/downloadMemberFilesBatch';
    var participantId = $('#participantId').val();
    var checkBoxElements = document.getElementsByName("selectMemberDocument");
    var documentNames = "";
    var firstData = true;
    for (var i of checkBoxElements) {
        if (i.checked) {
            if (!firstData) {
                documentNames += "#";
            }
            documentNames += i.id;
            firstData = false;
        }
    }
    if (documentNames != "") {
        var data = "participantId," + participantId  + ",documentNames," + escape(documentNames);
        postData(url, data);
    }

}
function downloadMemberDocument(fileName) {
    var url = '/downloadMemberFile';
   var participantId = $('#participantId').val();
    var data = "participantId," + participantId  + ",documentName," + escape(fileName);
    postData(url, data);

}
function duplicateSettlementBinCheck() {
var settlementBinId = $('#settlementBinId').val();
	var settlementBinNumber = $('#ifscCode').val() + settlementBinId;
    var tokenValue = document.getElementsByName("_TransactToken")[0].value;
    
    $("#memberBody").addClass("loadingdata");
    $.ajax({
        url: "duplicateSettlementBinCheck",
        type: "POST",
            data: {
                participantId: $('#participantId').val(),
                settlementBinNumber: settlementBinNumber,
				_TransactToken: tokenValue
            },
            dataType: "json",
			"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenValue);},
			success: function (data) {
            $("#memberBody").removeClass("loadingdata");
            if (data.status =='BSUC_0001') {
               
				return true;
            }
            if (data.status =='BERR_0002') {
            
                $("#errsettlementBinId").find('.error').html(settlementBinNumber + " Settlement Bin ID already present with this participant");
				$('#errsettlementBinId').show();
				
				
				return false;
            }
        },
        error: function (_error) {
            $("#memberBody").removeClass("loadingdata");
        }
    });

}

function duplicateSettlementBinCheckSaving() {
var settlementBinId = $('#settlementBinId').val();
	var settlementBinNumber = $('#ifscCode').val() + settlementBinId;
    var tokenValue = document.getElementsByName("_TransactToken")[0].value;
    
    $("#memberBody").addClass("loadingdata");
    $.ajax({
        url: "duplicateSettlementBinCheck",
        type: "POST",
            data: {
                participantId: $('#participantId').val(),
                settlementBinNumber: settlementBinNumber,
				_TransactToken: tokenValue
            },
            dataType: "json",
			"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenValue);},
			success: function (data) {
            $("#memberBody").removeClass("loadingdata");
            if (data.status =='BSUC_0001') {
               
				saveSettlementBin();
            }
            if (data.status =='BERR_0002') {
            
                $("#errsettlementBinId").find('.error').html(settlementBinNumber + " Settlement Bin ID already present with this participant");
				$('#errsettlementBinId').show();
				
				
				return false;
            }
        },
        error: function (_error) {
            $("#memberBody").removeClass("loadingdata");
        }
    });

}