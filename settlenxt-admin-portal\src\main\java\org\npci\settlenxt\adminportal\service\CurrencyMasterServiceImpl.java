package org.npci.settlenxt.adminportal.service;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.CurrencyMasterDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.CurrencyMasterRepository;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Throwable.class)
public class CurrencyMasterServiceImpl implements CurrencyMasterService {

	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	CurrencyMasterRepository currencyMasterRepository;

	// show main tab
	public List<CurrencyMasterDTO> getCurrencyMasterList() {
		return currencyMasterRepository.getCurrencyMasterListMain();
	}

	// show approval tab
	@Override
	@Transactional(readOnly = true)
	public List<CurrencyMasterDTO> getPendingCurrencyMaster() {
		return currencyMasterRepository.getCurrencyMasterPendingForApproval();
	}

	// view main tab info
	@Override
	@Transactional(readOnly = true)
	public CurrencyMasterDTO getCurrencyMasterMainInfo(int currencyId) {
		CurrencyMasterDTO currencyMaster = currencyMasterRepository.getCurrencyMasterProfileMain(currencyId);
		if (currencyMaster == null) {
			throw new SettleNxtException("No Data Exist", "");
		}
		return currencyMaster;
	}

	// view approval tab info
	@Override
	public CurrencyMasterDTO getCurrencyMasterStgInfo(String currencyId) {
		CurrencyMasterDTO currencyMasterStg = currencyMasterRepository
				.getCurrencyMasterStgInfoById(Integer.parseInt(currencyId));
		if (currencyMasterStg == null) {
			throw new SettleNxtException("No Data Exist", "");
		}
		return currencyMasterStg;
	}

	// add edit
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public CurrencyMasterDTO addEditCurrencyMaster(CurrencyMasterDTO currencyMasterDto) {
		int funcId = CommonConstants.TRANSACT_FUCTIONALITY_ADD_CURRENCY_MASTER_CONFIG;
		if (CommonConstants.EDIT_CURRENCY_MASTER.equalsIgnoreCase(currencyMasterDto.getAddEditFlag())) {
			funcId = CommonConstants.TRANSACT_FUCTIONALITY_EDIT_CURRENCY_MASTER_CONFIG;
		}
		Date lt = new Date();
		currencyMasterDto.setStatus("A");
		currencyMasterDto.setLastUpdatedOn(lt);
		currencyMasterDto.setLastUpdatedBy(sessionDTO.getUserName());
		currencyMasterDto.setCreatedOn(lt);
		currencyMasterDto.setCreatedBy(sessionDTO.getUserName());
		currencyMasterDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

		if (funcId == CommonConstants.TRANSACT_FUCTIONALITY_EDIT_CURRENCY_MASTER_CONFIG) {
			currencyMasterDto.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
			currencyMasterRepository.updateCurrencyMaster(currencyMasterDto);
		}
		if (funcId == CommonConstants.TRANSACT_FUCTIONALITY_ADD_CURRENCY_MASTER_CONFIG) {
			int returnValue = checkDuplicateData(currencyMasterDto);
			if (returnValue > 0) {
				throw new SettleNxtApplicationException("ERR_CURRENCY_MASTER_EXISTS1", "Duplicate Record");
			}
			currencyMasterDto.setCurrencyId(currencyMasterRepository.fetchCurrencyMasterIdSequence());
			currencyMasterDto.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
			currencyMasterRepository.insertCurrencyMasterStg(currencyMasterDto);
		}

		return currencyMasterRepository.getCurrencyMasterStg(currencyMasterDto.getCurrencyId());
	}

	// edit
	@Override
	@Transactional(readOnly = true)
	public CurrencyMasterDTO getCurrencyMasterForEdit(int currencyId) {
		return currencyMasterRepository.getCurrencyMasterStgInfoById(currencyId);
	}

	// for checker
	@Override
	public CurrencyMasterDTO approveOrRejectCurrencyMaster(int currencyId, String status, String remarks) {
		CurrencyMasterDTO currencyMasterDto = getCurrencyMasterStg(currencyId);
		currencyMasterDto.setRequestState(status);
		currencyMasterDto.setCheckerComments(remarks);
		prepareCurrencyMasterForApproval(status, remarks, currencyId, currencyMasterDto);
		return currencyMasterDto;
	}

	// for bulk checker
	@Override
	public String approveOrRejectCurrencyMasterBulk(String bulkApprovalReferenceNoList, String status, String remarks) {
		String[] referenceNoArr = bulkApprovalReferenceNoList.split("\\|");
		int currencyId = 0;

		for (String refNum:referenceNoArr) {
			try {
				if (!StringUtils.isEmpty(refNum)) {
					currencyId = Integer.parseInt(refNum);
					CurrencyMasterDTO currencyMasterDto = getCurrencyMasterStg(currencyId);
					if (currencyMasterDto == null) {

						throw new SettleNxtException("Exception occurred with Ref No" + refNum, "");
					}
					approveOrRejectBulk(status, remarks, currencyId, currencyMasterDto);
				}
			} catch (Exception ex) {
				throw new SettleNxtException("", "Exception for Ref no" + refNum, ex);
			}
		}
		return CommonConstants.YES_FLAG;
	}

	private void approveOrRejectBulk(String status, String remarks, int currencyId,
			CurrencyMasterDTO currencyMasterDto) {
		if (currencyMasterDto != null) {
			currencyMasterDto.setRequestState(status);
			currencyMasterDto.setCheckerComments(remarks);
			if ("A".equals(status)) {
				currencyMasterDto.setStatus("A");
			} else {
				currencyMasterDto.setStatus("I");
			}
			prepareCurrencyMasterForApproval(status, remarks, currencyId, currencyMasterDto);
		}
	}

	private void prepareCurrencyMasterForApproval(String status, String remarks, int currencyId,
			CurrencyMasterDTO currencyMasterDto) {
		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			currencyMasterDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			CurrencyMasterDTO currencyMasterMain = currencyMasterRepository.getCurrencyMasterMain(currencyId);

			if (currencyMasterMain != null) {
				currencyMasterRepository.updateCurrencyMasterMain(currencyMasterDto);
			} else {
				currencyMasterRepository.insertCurrencyMasterMain(currencyMasterDto);
			}
		}
		if (CommonConstants.REQUEST_STATE_REJECTED.equals(status)) {
			currencyMasterDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
			}
		currencyMasterDto.setCheckerComments(remarks);
		
		Date lt = new Date();
		currencyMasterDto.setLastUpdatedOn(lt);
		currencyMasterDto.setLastUpdatedBy(sessionDTO.getUserName());
		currencyMasterRepository.updateCurrencyMasterRequestState(currencyMasterDto);
	}

	// for discard
	@Override
	public CurrencyMasterDTO discardCurrencyMaster(int currencyId) {
		CurrencyMasterDTO currencyMasterDto = getCurrencyMasterStg(currencyId);
		CurrencyMasterDTO currencyMasterDtoMain = currencyMasterRepository.getCurrencyMasterMain(currencyId);
		if (currencyMasterDtoMain != null) {
			currencyMasterDtoMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			currencyMasterDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			currencyMasterRepository.updateCurrencyMaster(currencyMasterDtoMain);
		} else {
			currencyMasterRepository.deleteDiscardedEntry(currencyMasterDto);
		}
		return currencyMasterDto;
	}

	@Override
	public CurrencyMasterDTO getCurrencyMasterStg(int currencyId) {
		return currencyMasterRepository.getCurrencyMasterStg(currencyId);
	}

	@Override
	public int checkDuplicateData(CurrencyMasterDTO currencyMasterDto) {

		return currencyMasterRepository.validateDuplicateCheck(currencyMasterDto);

	}
}
