
$(document).ready(function () {

    var cursorPosition = null;
   $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    if (colIdx < actionColumnIndex) {
                        $(cell).html(title + '<br><input class="search-box"   type="text" />');

                        // On every keypress in this input
                        $(
                            'input',
                            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
                        )
                            .off('keyup change')
                            .on('change', function (_e) {
                                // Get the search value
                                $(this).attr('title', $(this).val());
                                var regexr = '({search})'; 

                                cursorPosition = this.selectionStart;
                                // Search the column for that value
                                api
                                    .column(colIdx)
                                    .search(
                                        this.value != ''
                                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                                            : '',
                                        this.value != '',
                                        this.value == ''
                                    )
                                    .draw();
                            })
                            .on('click', function (e) {
                                e.stopPropagation();
                            })
                            .on('keyup', function (e) {
                                e.stopPropagation();

                                $(this).trigger('change');
                                if (cursorPosition && cursorPosition != null) {
                                    $(this)
                                        .focus()[0]
                                        .setSelectionRange(cursorPosition, cursorPosition);
                                }
                            });
                    } else {
                        $(cell).html(title + '<br> &nbsp;');
                    }
                });
            $('#tabnew_filter').hide();
            
        },
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Net Premium',
                header: 'false',
                title: null,
                sheetName: 'Net Premium',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:first-child)'
                }
            },
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Net Premium' ,
						header:'false', 
						title: null,
						sheetName:'Net Premium',
						className:'defaultexport',
						exportOptions: {
				            columns: 'th:not(:first-child)'
				         }
                    }
        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true
    });

    $("#excelExport").on("click", function () {
        $(".buttons-excel").trigger("click");
    });
 	     $("#csvExport").on("click", function () {	
	        $(".buttons-csv").trigger("click");
	    });
 
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });
     
     $("#errvendorId").hide();
   		
  var datainfo = document.getElementById('hidenDiv'); 
 datainfo.style.display = 'none';
});


function resetAction() {

	document.getElementById("vendorId").options[0].selected = true;
	$("#errvendorId").find('.error').html('');
	
	}



function viewNetPremium(action) {
	var isValid = true;
	var fieldValue = $("#vendorId").val();
	if (fieldValue == "SELECT") {
        isValid = false;
    }
	
	if(isValid){
	var url = action;
	var vendorId = document.getElementById("vendorId").value;
	
	var data = "vendorId," + vendorId  + ",status,"
		+ status;
	postData(url, data);
	}
	else{
		if(netPremiumValidationMessages['vendorId']){
    		$("#errvendorId").find('.error').html(netPremiumValidationMessages['vendorId']);
    	}
		 $("#errvendorId").show();
	}

	} 


