/**
 * 
 */
package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.RoleDTO;
import org.npci.settlenxt.portal.common.dto.RoleFunctionalityDTO;
import org.npci.settlenxt.portal.common.repository.BaseRoleRepository;

@Mapper
public interface RoleRepository extends BaseRoleRepository {

	 List<CodeValueDTO> getRoleHierarchyList(@Param("fieldValue") String fieldValue);

	 List<RoleDTO> getBulkRoleStgInfoByRoleId(@Param("list") List<Integer> list);

	 List<RoleFunctionalityDTO> getRoleFuncStgByRoleId(@Param("roleId") int roleId);

	 List<RoleFunctionalityDTO> getRoleFuncByRoleId(@Param("roleId") int roleId);

}
