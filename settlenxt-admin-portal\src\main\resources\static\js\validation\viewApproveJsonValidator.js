	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	
	}
	function backAction(type, action) {
		var url = action;
		var data = "userType," + type ;
		postData(url, data);
	}
	
function postAction(_action) {
		var remarks;
		var crtuser;
		var seqId;
		var url;
		var data;

		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		if (maxLengthTextArea('rejectReason')) {
			if ($('#apprej option:selected').val() == "A") {
				if ($("#rejectReason").val() != "") {
					remarks = $("#rejectReason").val();
					crtuser = $("#crtuser").val();
					seqId = $('#seqId').val();

					url = '/approveJsonValidator';
					data = "seqId," + seqId + ",status," + "Approved"
							+ ",crtuser," + crtuser + ",_vTransactToken,"
							+ tokenValue + ",remarks," + remarks;
					postData(url, data);
				} else {
					$(".remarkMust").show();
					$('html, body').animate({
						scrollTop : 0
					}, 'slow');
					return false;
				}
			} else if ($('#apprej option:selected').val() == "R") {
				if ($("#rejectReason").val() != "") {
					
						remarks = $("#rejectReason").val();
						crtuser = $("#crtuser").val();
						url = '/approveJsonValidator';
						seqId = $('#seqId').val();
						
						data = "seqId," + seqId + ",status," + "Rejected"
						+ ",crtuser," + crtuser + ",_vTransactToken,"
						+ tokenValue + ",remarks," + remarks ;
						postData(url, data);

				} else {
					$(".remarkMust").show();
					$('html, body').animate({
						scrollTop : 0
					}, 'slow');
					return false;
				}
			} else {
				$(".appRejMust").show();
				$('html, body').animate({
					scrollTop : 0
				}, 'slow');
				return false;
			}
		}
	}
		
	
	

