package org.npci.settlenxt.adminportal.service;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class NetworkBinUploadFactory{
	@Autowired
	JcbNetworkBinUploadImpl jcbImpl;
	@Autowired
	CupNetworkBinUploadImpl cupImpl;
	@Autowired
	DciNetworkBinUploadImpl dciImpl;
	@Autowired
	DefaultNetwokBinImpl defaultImpl;
	@Autowired
	MemberBinFileUpload rupayMemImp;
	
	
	public NetWorkBinUploadInterface getInterface(String fileType) {
		if(StringUtils.equals(fileType, "JCB"))
			{
			return jcbImpl;
			}
		else if(StringUtils.equals(fileType, "CUP"))
			{
			return cupImpl;
			}
		else if(StringUtils.equals(fileType, "DCI"))
			{
			return dciImpl;
			}
		else if(StringUtils.equals(fileType, "RUPAY"))
			{
			return rupayMemImp;
			}
		else
			{
			return defaultImpl;
			}
	}
}
