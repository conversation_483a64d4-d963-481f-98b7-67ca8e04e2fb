	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});

function display() {
    $(".appRejMust").hide();

}

function userAction(action, mccTipSurchargeId) {
	 
	var data = "mccTipSurchargeId," + mccTipSurchargeId ;
	postData(action, data);
}

function edit(action, mccTipSurchargeId,parentPage) {
	 
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "mccTipSurchargeId," + mccTipSurchargeId + ",_vTransactToken,"
			+ tokenValue +",parentPage," + parentPage;
	postData(action, data);
}


function backAction(_type, action) {
	var data =  "status,"
			+ status;
	postData(action, data);
}

 function postAction(_action) {
	var data="";
	var url="";
	var mccTipSurchargeId;
	var remarks="";
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 mccTipSurchargeId = $("#mccTipSurchargeId").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveMccTipSurcharge';
				 data = "mccTipSurchargeId," + mccTipSurchargeId + ",status," + "A"  + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				 mccTipSurchargeId = $("#mccTipSurchargeId").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveMccTipSurcharge';
				 data = "mccTipSurchargeId," + mccTipSurchargeId + ",status," + "R"  + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
	
			 
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		