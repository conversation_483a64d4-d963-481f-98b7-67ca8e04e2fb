$(document).ready(
		function() {
			var maxLength = 70;
			$('#vSENDER').keyup(function() {
				var length = $(this).val().length;
				var length = maxLength - length;
				$('#SenderChars').text(length);
			});
			$('#vMGSSRLTXT').keyup(function() {
				var length = $(this).val().length;
				var length = maxLength - length;
				$('#ScrollChars').text(length);
			});
			$('#vMGSSUB').keyup(function() {
				var length = $(this).val().length;
				var length = maxLength - length;
				$('#SubjectChars').text(length);
			});
			$('#vMGSTYPE').change(function() {
				if ($('#vMGSTYPE').val() === 'Others') {
					$('#vMGSTYPEO').show();
				} else {
					$('#vMGSTYPEO').hide();
				}
			});
			$('#vMGSDEPT').change(function() {
				if ($('#vMGSDEPT').val() === 'Others') {
					$('#vMGSDEPTO').show();
				} else {
					$('#vMGSDEPTO').hide();
				}
			});
			$("#eVLDFRM").datepicker({
				dateFormat : "d-M-yy",
				changeMonth : true,
				changeYear : true,
				// maxDate : 0,
				minDate : 0,
				onClose : function(selectedDate) {
					$("#eVLDFRM").datepicker("option", selectedDate);
				}
			});
			// $("#eVLDFRM").datepicker('setDate', new Date());
			$("#evEXPDATE").datepicker(
					{
						defaultDate : 0,
						dateFormat : "d-M-yy",
						changeMonth : true,
						changeYear : true,
						// maxDate : 0,
						minDate : 1,
						onClose : function(selectedDate) {
							$("#evEXPDATE").datepicker("option", "minDate",
									selectedDate);
						}
					});

			$("#eVLDFRM").change(function() {

				var date2 = $('#eVLDFRM').datepicker('getDate', '+1d');
				date2.setDate(date2.getDate() + 5);
				$('#evEXPDATE').datepicker('setDate', date2);
			});
			$('#broadCastForm').click(
					function() {
						var f = validateAddBroadCastForm();
						var tokenValue = document.getElementsByName("_TransactToken")[0].value;
						if (f) {
							$('button').prop('disabled', true);
							var url = '/submitBroadCastMessage';
							var loc = window.location;
							var pathName = loc.pathname.substring(0,
									loc.pathname.lastIndexOf('/'));
							linkurl = pathName + url;
							$('#addEditBroadCast').attr('action', 'submitBroadCastMessage?_xyrftp='+sha256(tokenValue));
							$('#addEditBroadCast').submit();
						} /*
							 * else { $('#errTaxPer').text(''); return false; }
							 */
						return false;
					});
			function validateAddBroadCastForm() {
				var validFlag = true;
				valiFlag = validateTillDate();
				if ($('#vSENDER').val() == "") {
					$('#errBroadCastSnd').text(
							'Please enter Sender of The Message.');
					validFlag = false;
				} else {
					$('#errBroadCastSnd').text('');
				}
				if ($('#vMGSSUB').val() == "") {
					$('#errBroadCastSub').text(
							'Please enter Subject of The Message.');
					validFlag = false;
				} else {
					$('#errBroadCastSub').text('');
				}
				if ($('#vMGSSRLTXT').val() == "") {
					$('#errBroadCastScrlTxt').text(
							'Please enter Scroll Text of The Message.');
					validFlag = false;
				} else {
					$('#errBroadCastScrlTxt').text('');
				}
				if ($('#vMGSBDY').val() == "") {
					$('#errBroadCastBdy').text(
							'Please enter Body of The Message.');
					validFlag = false;
				} else {
					$('#errBroadCastBdy').text('');
				}
				if ($('#vMGSTYPE').val() === 'Others') {
					if ($('#vMGSTYPEO').val() == "") {
						$('#errBroadCastType').text(
								'Please enter Type of The Message.');
						validFlag = false;
					} else {
						$('#errBroadCastType').text('');
					}
				} else {
					$('#errBroadCastType').text('');
				}
				if ($('#vMGSDEPT').val() === 'Others') {
					if ($('#vMGSDEPTO').val() == "") {
						$('#errBroadCastDept').text(
								'Please enter Dept of The Message.');
						validFlag = false;
					} else {
						$('#errBroadCastDept').text('');
					}
				} else {
					$('#errBroadCastDept').text('');
				}
				if ($('#vMGSSRLTXT').val().length > 70) {
					$('#errBroadCastScrlTxt').text(
							'Only 70 Chareacters Allowed!.');

					return false;
				} else {
					$('#errBroadCastScrlTxt').text('');
				}
				if ($('#vMGSSUB').val().length > 70) {
					$('#errBroadCastSub')
							.text('Only 70 Chareacters Allowed!.');

					return false;
				} else {
					$('#errBroadCastSub').text('');
				}
				if ($('#vSENDER').val().length > 70) {
					$('#errBroadCastSnd')
							.text('Only 70 Chareacters Allowed!.');

					return false;
				} else {
					$('#errBroadCastSnd').text('');
				}
				if ($('#eVLDFRM').val() == '') {
					$('#errEffFromDate').text('Please Select A Date!.');

					return false;
				} else {
					$('#errBroadCastSnd').text('');
				}

				return validFlag;
			}
			function validateTillDate() {
				var valiFlag = true;
				if ($('#evEXPDATE').val() <= $('eVLDFRM').val()) {
					$('#errEffTillDate').text('To Date must Be Greater!');
					valiFlag = false;
				}
				return valiFlag;
			}
		});

function userAction(type, action) {
	$('button').prop('disabled', true);
	var uid = document.getElementById("vMGSID").value;
	var data = "uid," + uid  + ",status,"
			+ status;
	postData(action, data);
}
window.history.forward();
function noBack() {
	window.history.forward();
}