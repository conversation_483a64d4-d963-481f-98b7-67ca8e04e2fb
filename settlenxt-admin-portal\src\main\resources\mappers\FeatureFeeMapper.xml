<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.FeatureFeeRepository">
	<select id="getFeatureFeeList" resultType="FeatureFeeDTO">
		SELECT f.card_config_id as cardConfigId, f.card_type as cardType, f.card_variant as cardVariant,f.feature,f.details, f.feature_fee as featureFee, f.from_date as fromDate, f.to_date as toDate, f.last_updated_by as lastUpdatedBy, f.last_updated_on as lastUpdatedOn, ct.description as cardTypeName,cv.description as cardVariantName,f.from_Date_full as fromDateComplate,f.to_Date_full as toDateComplate, f.last_operation as lastOperation FROM mcpr_feature_fee_config_stg f left join lookup ct on card_type=ct.code and ct.type='CARD_TYPE' left join lookup cv on card_variant=cv.code and cv.type='CARD_VARIANT' WHERE f.request_state =#{requestType} ORDER BY f.last_updated_on DESC
	</select>
	<select id="getFeatureFeeListForMain" resultType="FeatureFeeDTO">
		SELECT f.card_config_id as cardConfigId, f.card_type as cardType, f.card_variant as cardVariant,f.feature,f.details, f.feature_fee as featureFee, f.from_date as fromDate, f.to_date as toDate, f.last_updated_by as lastUpdatedBy
		, f.last_updated_on as lastUpdatedOn, ct.description as cardTypeName,cv.description as cardVariantName,f.from_Date_full as fromDateComplate,f.to_Date_full as toDateComplate, f.last_operation as lastOperation 
		,stg.request_state requestState 
		FROM mcpr_feature_fee_config f 
		inner join mcpr_feature_fee_config_stg stg on f.card_config_id=stg.card_config_id
		left join lookup ct on f.card_type=ct.code and ct.type='CARD_TYPE' 
		left join lookup cv on f.card_variant=cv.code and cv.type='CARD_VARIANT' 
		 ORDER BY f.last_updated_on DESC
	</select>
	<select id="getFeatureFeePendingForApproval" resultType="FeatureFeeDTO">
		SELECT f.card_config_id as cardConfigId, f.card_type as cardType, f.card_variant as cardVariant,f.feature,f.details, f.feature_fee as featureFee, f.from_date as fromDate, f.to_date as toDate, f.last_updated_by as lastUpdatedBy, f.last_updated_on as lastUpdatedOn, f.last_operation as lastOperation, f.request_state as requestState, f.checker_comments as checkerComments, ct.description as cardTypeName,cv.description as cardVariantName,f.from_Date_full as fromDateComplate,f.to_Date_full as toDateComplate FROM mcpr_feature_fee_config_stg f left join lookup ct on card_type=ct.code and ct.type='CARD_TYPE' left join  lookup cv on card_variant=cv.code and cv.type='CARD_VARIANT' WHERE f.request_state in ('P','R')
	</select>
	<select id="getFeatureFeeProfile" resultType="FeatureFeeDTO">
		SELECT f.card_config_id as cardConfigId, f.card_type as cardType, f.card_variant as cardVariant,f.feature,f.details, f.feature_fee as featureFee, f.from_date as fromDate, f.to_date as toDate, f.last_updated_by as lastUpdatedBy, f.last_updated_on as lastUpdatedOn, f.request_state as requestState, ct.description as cardTypeName,cv.description as cardVariantName,f.from_Date_full as fromDateComplate,f.to_Date_full as toDateComplate, f.last_operation as lastOperation from mcpr_feature_fee_config_stg f left join lookup ct on card_type=ct.code and ct.type='CARD_TYPE' left join  lookup cv on card_variant=cv.code and cv.type='CARD_VARIANT' where f.card_config_id = #{cardid} and (f.request_state = 'A')
	</select>
	<select id="getFeatureFeeProfileMain" resultType="FeatureFeeDTO">
		SELECT f.card_config_id as cardConfigId, f.card_type as cardType, f.card_variant as cardVariant,f.feature,f.details, f.feature_fee as featureFee, f.from_date as fromDate, f.to_date as toDate, f.last_updated_by as lastUpdatedBy
		, f.last_updated_on as lastUpdatedOn, stg.request_state as requestState, ct.description as cardTypeName,cv.description as cardVariantName,f.from_Date_full as fromDateComplate,f.to_Date_full as toDateComplate
		, f.last_operation as lastOperation
		FROM mcpr_feature_fee_config f 
		inner join mcpr_feature_fee_config_stg stg on f.card_config_id=stg.card_config_id
		left join lookup ct on f.card_type=ct.code and ct.type='CARD_TYPE' 
		left join  lookup cv on f.card_variant=cv.code and cv.type='CARD_VARIANT' 
		where f.card_config_id = #{cardid} 
	</select>
	<select id="getFeatureFeeStgInfoByCardId" resultType="FeatureFeeDTO">
		SELECT f.card_config_id as cardConfigId, f.card_type as cardType, f.card_variant as cardVariant,f.feature,f.details, f.feature_fee as featureFee, f.from_date as fromDate, f.to_date as toDate, f.last_updated_by as lastUpdatedBy, f.last_updated_on as lastUpdatedOn,f.created_on as createdOn, f.created_by as createdBy, f.request_state as requestState,f.status, f.last_operation as lastOperation, f.checker_comments as checkerComments, ct.description as cardTypeName,cv.description as cardVariantName,f.from_Date_full as fromDateComplate,f.to_Date_full as toDateComplate FROM  mcpr_feature_fee_config_stg f left join lookup ct on card_type=ct.code and ct.type='CARD_TYPE' left join  lookup cv on card_variant=cv.code and cv.type='CARD_VARIANT' WHERE f.card_config_id =#{f.card_config_id}
	</select>
	<select id="getFeatureFeeMain" resultType="FeatureFeeDTO">
	 	SELECT f.card_config_id as cardConfigId, f.card_type as cardType, f.card_variant as cardVariant,f.feature,f.details, f.feature_fee as featureFee, f.from_date as fromDate, f.to_date as toDate, f.last_updated_by as lastUpdatedBy, f.last_updated_on as lastUpdatedOn,f.created_on as createdOn, f.created_by as createdBy, f.status, ct.description as cardTypeName,cv.description as cardVariantName,f.from_Date_full as fromDateComplate,f.to_Date_full as toDateComplate  FROM  mcpr_feature_fee_config f left join lookup ct on card_type=ct.code and ct.type='CARD_TYPE' left join  lookup cv on card_variant=cv.code and cv.type='CARD_VARIANT' where f.card_config_id = #{f.card_config_id}
	</select>
	<select id="getFeatureFeeStg" resultType="FeatureFeeDTO">
		SELECT f.card_config_id as cardConfigId, f.card_type as cardType, f.card_variant as cardVariant,f.feature,f.details, f.feature_fee as featureFee, f.from_date as fromDate, f.to_date as toDate, f.last_updated_by as lastUpdatedBy, f.last_updated_on as lastUpdatedOn,f.created_on as createdOn, f.created_by as createdBy, f.request_state as requestState, f.status, f.last_operation as lastOperation, ct.description as cardTypeName,cv.description as cardVariantName,f.from_Date_full as fromDateComplate,f.to_Date_full as toDateComplate FROM mcpr_feature_fee_config_stg f left join lookup ct on card_type=ct.code and ct.type='CARD_TYPE' left join lookup cv on card_variant=cv.code and cv.type='CARD_VARIANT' where f.card_config_id = #{f.card_config_id}
	</select>
	<select id="fetchIdFromFeatureFeeSequence" resultType="int">	
		SELECT nextval('feature_card_config_id_seq ')
	</select>
	<insert id="insertFeatureFeeStg" >
		insert into mcpr_feature_fee_config_stg (card_config_id, card_type, card_variant, feature_fee, feature, details, from_date, to_date, created_by,created_on, last_updated_by,last_updated_on, request_state, last_operation, status,from_Date_full,to_Date_full) VALUES
		(#{cardConfigId}, #{cardType}, #{cardVariant}, #{featureFee},#{feature},#{details}, #{fromDate}, #{toDate},
		#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{requestState}, #{lastOperation}, #{status}, 
		to_date(concat('01',#{fromMonth},#{fromYear}),'ddmmyyyy')  ,to_date(concat(case  when #{toMonth} in ('03','12') then '31' else '30' end,#{toMonth},#{toYear}),'ddmmyyyy') )
	</insert>
	<insert id="insertFeatureFeeMain">
		insert into mcpr_feature_fee_config (card_config_id, card_type, card_variant, feature_fee, feature, details, from_date, to_date, created_by, created_on, last_updated_by, last_updated_on, status,from_Date_full,to_Date_full) VALUES
		(#{cardConfigId},#{cardType}, #{cardVariant}, #{featureFee},#{feature},#{details}, #{fromDate}, #{toDate}, #{createdBy}, #{createdOn}
		, #{lastUpdatedBy}, #{lastUpdatedOn},#{status}
		,#{fromDateComplate},#{toDateComplate}	)
	</insert>
	<update id="updateFeatureFeeMain">
		update mcpr_feature_fee_config set CARD_CONFIG_ID=#{cardConfigId}, CARD_TYPE=#{cardType}, CARD_VARIANT=#{cardVariant},feature_fee=#{featureFee}, feature=#{feature}, details=#{details}, FROM_DATE=#{fromDate}, TO_DATE=#{toDate}, CREATED_BY =#{createdBy},CREATED_ON =#{createdOn}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status},from_Date_full=#{fromDateComplate},to_Date_full=#{toDateComplate} WHERE CARD_CONFIG_ID = #{cardConfigId}
	</update>
	<update id="updateFeatureFee">
		update  mcpr_feature_fee_config_stg set CARD_TYPE=#{cardType}, CARD_VARIANT=#{cardVariant},feature_fee=#{featureFee}, feature=#{feature}, details=#{details}, FROM_DATE=#{fromDate}, TO_DATE=#{toDate}, CREATED_BY =#{createdBy},CREATED_ON =#{createdOn}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, STATUS=#{status}, request_state =#{requestState}, last_operation= #{lastOperation}, CHECKER_COMMENTS='',from_Date_full=to_date(concat('01',#{fromMonth},#{fromYear}),'ddmmyyyy') ,to_Date_full=to_date(concat(case  when #{toMonth} in ('03','12') then '31' else '30' end,#{toMonth},#{toYear}),'ddmmyyyy')	WHERE card_config_id= #{cardConfigId}
	</update>
	<update id="updateFeatureFeediscard">
		update  mcpr_feature_fee_config_stg set CARD_TYPE=#{cardType}, CARD_VARIANT=#{cardVariant},feature_fee=#{featureFee}, feature=#{feature}, details=#{details}, FROM_DATE=#{fromDate}, TO_DATE=#{toDate}, CREATED_BY =#{createdBy},CREATED_ON =#{createdOn}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, STATUS=#{status}, request_state =#{requestState}, last_operation= #{lastOperation}, CHECKER_COMMENTS='',from_Date_full=#{fromDateComplate} ,to_Date_full=#{toDateComplate}	WHERE card_config_id= #{cardConfigId}
	</update>
	<update id="updateFeatureFeeRequestState">
		update mcpr_feature_fee_config_stg set REQUEST_STATE=#{requestState}, CHECKER_COMMENTS=#{checkerComments}, LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, last_operation= #{lastOperation}	WHERE card_config_id= #{cardConfigId}
	</update>
	<delete id="deleteDiscardedEntry" >
		delete from mcpr_feature_fee_config_stg WHERE card_config_id= #{cardConfigId}
	</delete>
	<select id="validateDuplicateCheckList" resultType="FeatureFeeDTO">
		select R.card_config_id as cardConfigId, R.card_type	as cardType, R.card_variant	as cardVariant  FROM mcpr_feature_fee_config_stg as R	
		WHERE CARD_CONFIG_ID!= #{cardConfigId} and R.CARD_TYPE=#{cardType} and R.CARD_VARIANT=#{cardVariant}
		and feature=#{feature}
		and ((from_date_full=to_date(#{fromYearMonth},'ddmmyyyy') and to_date_full=to_date(#{toYearMonth},'ddmmyyyy')) 
		or (to_date(#{fromYearMonth},'ddmmyyyy') between from_date_full and to_Date_full ) 
		or (to_date(#{toYearMonth},'ddmmyyyy') between from_date_full and to_Date_full ) 
		or (from_date_full between to_date(#{fromYearMonth},'ddmmyyyy') and to_date(#{toYearMonth},'ddmmyyyy') ) 
		or (to_Date_full between to_date(#{fromYearMonth},'ddmmyyyy') and to_date(#{toYearMonth},'ddmmyyyy')  ) )
	</select>
	<select id="validateFromDateList" resultType="FeatureFeeDTO">	
		select R.card_config_id as cardConfigId, R.card_type	as cardType, R.card_variant	as cardVariant  FROM mcpr_feature_fee_config_stg as R	WHERE CARD_CONFIG_ID!= #{cardConfigId} and R.CARD_TYPE=#{cardType} and R.CARD_VARIANT=#{cardVariant}
		and to_Date_full= to_date(#{fromYearMonth},'ddmmyyyy')  -interval '1 day'
	</select> 
	<select id="validateCombination" resultType="FeatureFeeDTO">	
		select R.card_config_id as cardConfigId, R.card_type	as cardType, R.card_variant	as cardVariant  FROM mcpr_feature_fee_config_stg as R	WHERE CARD_CONFIG_ID!= #{cardConfigId} and R.CARD_TYPE=#{cardType} and R.CARD_VARIANT=#{cardVariant}
	</select>
	<select id="getLastTodate" resultType="String">    
        select to_char(max(to_Date_full),'MONTH-YYYY') as lastToMonth from mcpr_feature_fee_config_stg as R where card_config_id!= #{cardConfigId} and R.card_type=#{cardType} and R.card_variant=#{cardVariant}
        and to_Date_full  &lt; to_date(#{fromYearMonth},'ddmmyyyy')  
    </select>
	
</mapper>

	