var i;
$(document).ready(function () { 


$('#clearFeeMajorMinor').on('click', function(){
$("#errConfigs").hide();
});




   



});



function validatefield2(){


var arr =[]
var flag = true

console.log("here in validate2")

			$("#assignedList tr").each(function() {
                                          arr.push(this.id);
                                        });
                                                        
                              
                                        
                                        for(let s in arr){
                                     
                                        	if(!validateField("fieldValue" + arr[s].replace("remove",""))){
                                        	
                                        		flag = false;
                                        	}
                                        }

		return flag;

}
 
 function validateRelOperator(msgID) {

	var relOperator = (document.getElementById("relationalOperator").value).replace(
		/(^\s*)|(\s*$)/g, '');
	var errRelOperator = document.getElementById(msgID);

	if (relOperator == "0") {
		errRelOperator.className = 'error';
		errRelOperator.innerHTML = "Please select operator";
		return false;
	}
	else {
		errRelOperator.className = 'error';
		errRelOperator.innerHTML = "";
	}
	return true;

}

function validateFieldValue() {

	var fieldValue = (document.getElementById(msgID).value).replace(
		/(^\s*)|(\s*$)/g, '');
	var errfieldValue = document.getElementById(msgID);

	if (fieldValue == "") {
		errfieldValue.className = 'error';
		errfieldValue.innerHTML = "Please enter Field Value";
		return false;
	}
	else {
		errfieldValue.className = 'error';
		errfieldValue.innerHTML = "";
	}
	return true;

}
function validateFieldName(msgID) {

	var fieldName = (document.getElementById("fieldName").value).replace(
		/(^\s*)|(\s*$)/g, '');
	var errfieldName = document.getElementById(msgID);

	if (fieldName == "0") {
		errfieldName.className = 'error';
		errfieldName.innerHTML = "Please Select Field Name";
		return false;
	}
	else {
		errfieldName.className = 'error';
		errfieldName.innerHTML = "";
	}
	return true;

}


 
 
function selectAllFields() {
	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}
	$('#errConfigs').hide();
		var availableFields;
		availableFields = document.getElementsByClassName("availableFields");
		var fieldCodeList=[];
		
		for( i of availableFields){
		  var fieldCode= i.id;
		  fieldCode=fieldCode.substring(3);
		  fieldCodeList.push(fieldCode);
		  }
		 for(i of fieldCodeList){
		  addFeeConfigToSelectedList(i, i)
		}
		
}		

function removeAllSelectedFields() {
 
 
 $('#submitButton').prop('disabled', false);
		var selectedFields ;
		selectedFields = document.getElementsByClassName("selectedFields");
		var fieldCodeList=[];
		
		for(i of selectedFields){
		  var fieldCode=i.id;
		  fieldCode=fieldCode.substring(6);
		  fieldCodeList.push(fieldCode);
		  }
		 for(i of fieldCodeList){
		  removeSelectedField(i)
		}
}		

function addFeeConfigToSelectedList(fieldCode, _fieldDescription){

	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}
	var optionsHTMLStr="";
	for(i of operatorList){
		optionsHTMLStr+='<option value="'+i.value+'">'+i.label+'</option>';
	}
	
	$('#errConfigs').hide();
	
	$('#assignedList')
			.append(
					'<tr class="selectedFields" value="'
							+ fieldCode
							+ '" id="remove'
							+ fieldCode
							+ '"><td id="fieldName'+ fieldCode+'" class="selectedFieldName" >'
							+ fieldCode
							+ '</td><td >'
							+'<select id="relationalOperator'+ fieldCode+'" name="relationalOperator'+ fieldCode+'">'
							+optionsHTMLStr
							+'</select>'
							+ '</td><td >'
							+'<input type="text" size=10 onkeyup="validateField('
							+ '\'fieldValue'+fieldCode+ '\''
							+ ');" name="fieldValue'+ fieldCode+'"  id="fieldValue'+ fieldCode+'"></input>'
							+ '<div id = "errField'+ fieldCode+'">'
							+  '<span for="fieldName" class="error"><form:errors path="fieldName" />'
							+	'</span>'
							+ '</div >'
							+ '</td>'
							+'<td >'
							+'<select id="status'+ fieldCode+'" name="status'+ fieldCode+'">'
							+ '<option value="A">Active</option>'
							+ '<option value="I">Inactive</option>'
							+'</select>'
							+ '</td><td >'
							+'<i class="glyphicon glyphicon-remove-circle" style="color: blue" onclick="removeSelectedField('
							+ '\''+fieldCode+ '\''
							+ ')" ></i></td></tr>');
							
							
						
							
		$('#add'+fieldCode).remove();							 
}



			


 function removeSelectedField(fieldCode){
 $('#optionList')
.append('<tr class="availableFields" id="add'+fieldCode+'" value="'+fieldCode+'">'
		+'	<td>'+fieldCode+'</td>'
		+'	<td><i class="glyphicon glyphicon-circle-arrow-right" id="addBtn" onclick="addFeeConfigToSelectedList(\''+fieldCode+'\',\''+fieldCode+'\')">'
		+'	</i></td> </tr>');
	$('#remove' + fieldCode).remove();
	
	$('#submitButton').prop('disabled', false);
  
 }
 function validateKeypress() {
 var alpha = /[ A-Za-z]/;

 
    var keyChar = String.fromCharCode(event.which || event.keyCode);
    var result= alpha.test(keyChar) ? keyChar : false;
    if(!result){
    console.log("Enter valid input")
    }
    
    
}





