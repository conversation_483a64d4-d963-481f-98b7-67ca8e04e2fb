package org.npci.settlenxt.adminportal.common.mapping;

public enum NameMappingContext {
	
	MESSAGEFORMAT("messageformats"), MESSAGEFORMAT_NPCIMAKER("messageformatsNPCIMaker"), DATAELEMENTS("dataelements"), FILETYPES("filetypes"), TAGGED("tags_transactions");
		
	private String context;
	
	NameMappingContext(String context) {
		this.context = context;
	}
	
	public String getContext() {
		return context;
	}
	
    public static NameMappingContext fromString(String parameterName) {
        if (parameterName != null) {
            for (NameMappingContext objType : values()) {
                if (parameterName.equalsIgnoreCase(objType.context)) {
                    return objType;
                }
            }
        }
        return null;
    }
}
