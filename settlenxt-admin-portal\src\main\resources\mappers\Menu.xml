<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.MenuRepository">
	<select id="getUserMenuList" resultType="MenuDTO">
		WITH CTE_PMNU AS (SELECT MNU.MENU_ID AS MENUID,MNU.MENU_ORDER ,
		MNU.MENU_NAME AS MENUNAME, MNU.MENU_URL AS MENUURL, MNU.PARENT_MENU_ID
		AS PARENTMENUID,
		MNU.MODULE_ID AS MODULEID FROM USER_ROLE_MAPPING UR INNER JOIN ROLE_FUNC_MAPPING RF
		ON UR.ROLE_ID = RF.ROLE_ID INNER JOIN MENU_FUNC_MAPPING MF ON
		RF.FUNC_ID = MF.FUNC_ID INNER JOIN MENU MNU ON MF.MENU_ID =MNU.MENU_ID
		WHERE UR.USER_ID = #{userId} AND UR.STATUS =#{status} AND RF.STATUS
		=#{status} AND MF.STATUS =#{status} AND MNU.STATUS =#{status} ORDER BY
		MNU.MENU_ORDER asc )
		select
		PMNU.MENU_ID as menuId,
		PMNU.MENU_NAME as menuName,
		PMNU.MENU_URL as menuUrl,
		PMNU.PARENT_MENU_ID as parentMenuId,
		PMNU.MODULE_ID as moduleId,
		PMNU.menu_order
		from
		MENU PMNU
		inner join CTE_PMNU CTE on
		CTE.PARENTMENUID = PMNU.MENU_ID
		group by
		PMNU.MENU_ID,
		PMNU.MENU_NAME,
		PMNU.MENU_URL,
		PMNU.PARENT_MENU_ID,
		PMNU.MODULE_ID,
		PMNU.MENU_ORDER
		union
		select
		CTE.MENUID ,
		CTE.MENUNAME ,
		CTE.MENUURL ,
		CTE.PARENTMENUID ,
		CTE.MODULEID,
		CTE.MENU_ORDER
		from
		CTE_PMNU CTE
		group by
		CTE.MENUID,
		CTE.MENUNAME,
		CTE.MENUURL,
		CTE.PARENTMENUID,
		CTE.MODULEID,
		CTE.MENU_ORDER
		order by
		moduleId,
		parentMenuId,
		MENU_ORDER asc;

	</select>

</mapper>