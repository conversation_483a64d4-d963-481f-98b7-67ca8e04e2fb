var showbuttonflag=0;

$(document).ready(function() {

 	holidayIdsArr=[];
    
    
    
    
    $(document).ready(function () {
     $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
        //If first column to be skipped to include the filter for the reasons line check box 
                if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   searchBoxFunc(colIdx, cell, title, api);
                   }
                });
            $('#tabnew_filter').hide();
           
        },
        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Holiday Master',
                header: 'false',
                title: null,
                sheetName: 'Holiday Master',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'Holiday Master' ,
				header:'false', 
				title: null,
				sheetName:'Holiday Master',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });
});	

    


	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	
     $("#excelExport").on("click", function () {
    	        $(".buttons-excel").trigger("click");
    	    });
    	    
    	     $("#csvExport").on("click", function () {
    	        $(".buttons-csv").trigger("click");
    	    });
    	 
    	     $("#clearFilters").on("click", function () {
    	       $(".search-box").each(function() {
    				   $(this).val("");
    				     $(this).trigger("change");
    				});
    	    });
    	
   
 
	 showbuttonflag=0;

	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	
	

	$("#selectAll").click(function(){

				 $('#errorStatus4').hide();
				 	        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
				 	      
							 var holidayMasterSeqIdList = document.getElementById("holidayMasterSeqIds");
							 holidayMasterSeqIdList.innerHTML=holidayMasterListPendings.length+" "+"records are selected";

				 	if(holidayIdsArr.length>0){
				 		holidayMasterSeqIdList.innerHTML = holidayIdsArr.length+"     "+"records are selected";
				 		
				 		if( $('#selectAll').is(':checked') ){
				 	 $("#toggleModal").modal('show');
				 	        
				 	}
				 	else{
				 	   $("#toggleModal").modal('hide');
				 	        
				 	}}else{
				 		var i=0;
				 		var holidayIdsArr2=[];
				 		 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
				 			holidayIdsArr2.push(this.value);
				 				                    i++;
				 				                });
				 		 
				 		 if(holidayIdsArr2.length>0){


				 		if(holidayMasterListPendings.length>0){
				 		
				 			
				 			holidayMasterSeqIdList.innerHTML = holidayMasterListPendings.length+"     "+"records are selected";
				 			
				 			if( $('#selectAll').is(':checked') ){
				 		 $("#toggleModal").modal('show');
				 		        
				 		}
				 		else{
				 		   $("#toggleModal").modal('hide');
				 		        
				 		}
				 		 }}}
		});
	});
    
    

function searchBoxFunc(colIdx, cell, title, api) {
	var cursorPosition = null;
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function(_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
                holidayIdsArr = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
                        holidayIdsArr.push(this.value);
                        i++;
                    });
                }
                else {
                    holidayIdsArr = [];
                }
            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
   
}

function submitForm(url) {
	var data = "";
	postData(url, data);
}

function getPendingHolidayMasterList(){
	var url = '/getHolidayMasterPendingForApproval';
	var data = "";
	postData(url, data);
}
function ApproveorRejectBulkHolidayMaster(type,action){
	 var data="";
	 var url = '/approveOrRejectBulkHolidayMaster';
	let i=0;
	 var array = [];

	 if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(action=='All'){
	if(holidayIdsArr.length>0){
	 array= holidayIdsArr;
	  }
		 else{
			  array= holidayMasterListPendings;}
	 }
	   
	   
		var holidayMasterList = "";
		
		for(i of array){
			holidayMasterList = holidayMasterList + i + "|"
					;
		}
		
		
		if(array.length!=0){
	if(type=='A'){
	holidayMasterList=btoa(holidayMasterList);
			
	 data =  "status,"+"A"+",bulkApprovalholidayMasterList,"+holidayMasterList;
	}
	else if(type=='R'){
		
		holidayMasterList=btoa(holidayMasterList);
		data = "status,"+"R"+",bulkApprovalholidayMasterList,"+holidayMasterList;
	}
	

	postData(url, data);
	$('#errorStatus2').hide();
	$('#errorStatus2').html("");
	
	
	}
	else{
	
	$('#errorStatus2').html("Please select one or more records to bulk approve/reject records");
	$('#errorStatus2').show();
	}
		
}



function mySelect(){
	
	
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	    
	 
	 var holidayMasterSeqIdList = document.getElementById("holidayMasterSeqIds");
   
	 if(array.length==holidayMasterListPendings.length){
		 $('#selectAll').prop('checked', true);
		 
		 holidayMasterSeqIdList.innerHTML = holidayMasterListPendings.length+"     "+"records are selected";
			 $("#toggleModal").modal('show');
	 }
	 else{
		 $("#toggleModal").modal('hide');
		 
	 }
	
}

function deselectAll() {

	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  
		let i=0; 
		 for(i of ele){
   
      if(i.type=='checkbox')  
          i.checked=false;  
  } 
  
}
function viewHolidayMasterInfo(holidaySeqId,showMainTab, url) {

	holidaySeqId=btoa(holidaySeqId);
	var data = "holidaySeqId," + holidaySeqId+",showMainTab,"+showMainTab  ;
	console.log(data);
	postData(url, data);
}
    