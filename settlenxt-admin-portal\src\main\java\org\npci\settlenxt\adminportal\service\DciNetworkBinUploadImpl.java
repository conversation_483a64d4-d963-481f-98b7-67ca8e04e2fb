package org.npci.settlenxt.adminportal.service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.cache.CountryCache;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.MccRepository;
import org.npci.settlenxt.adminportal.repository.MemberRepository;
import org.npci.settlenxt.common.cache.BaseLookupDTOCache;
import org.npci.settlenxt.common.cache.CacheReloaderConstants;
import org.npci.settlenxt.portal.common.dto.BinDetailsDTO;
import org.npci.settlenxt.portal.common.dto.MccDTO;
import org.npci.settlenxt.portal.common.service.BaseCacheReloaderService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class DciNetworkBinUploadImpl implements NetWorkBinUploadInterface {

	private static final String NETWORK_DCI = "NETWORK_DCI";
	public static final String HTTP_XML_ORG_SAX_FEATURES_EXTERNAL_PARAMETER_ENTITIES = "http://xml.org/sax/features/external-parameter-entities";
	public static final String HTTP_XML_ORG_SAX_FEATURES_EXTERNAL_GENERAL_ENTITIES = "http://xml.org/sax/features/external-general-entities";
	public static final String HTTP_APACHE_ORG_XML_FEATURES_DISALLOW_DOCTYPE_DECL = "http://apache.org/xml/features/disallow-doctype-decl";
	public static final String FEATURE_SECURE_PROCESSING = "http://javax.xml.XMLConstants/feature/secure-processing";

	@Autowired
	SessionDTO sessionDTO;

	@Autowired
	MemberRepository memberRepository;

	@Autowired
	CountryCache countryCache;

	@Autowired
	MccRepository mccRepository;

	@Autowired
	private BaseCacheReloaderService baseCacheReloaderSvc;

	@Autowired
	private BaseLookupDTOCache lookupCache;

	private static final String UNIQUE_CONSTRAINT_ERROR = "duplicate key value violates unique constraint";

	@Override
	public String processNetworkBinFile(List<MultipartFile> files) {
		try {
			for (MultipartFile file : files) {
				DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
				dbf.setFeature(HTTP_APACHE_ORG_XML_FEATURES_DISALLOW_DOCTYPE_DECL, true);
				dbf.setFeature(HTTP_XML_ORG_SAX_FEATURES_EXTERNAL_GENERAL_ENTITIES, false);
				dbf.setFeature(HTTP_XML_ORG_SAX_FEATURES_EXTERNAL_PARAMETER_ENTITIES, false);
				String content = new String(file.getBytes(), StandardCharsets.UTF_8);
				String replacedContent = content.replace("&", "&amp;");
				dbf.setFeature(FEATURE_SECURE_PROCESSING, true);
				DocumentBuilder db = dbf.newDocumentBuilder();
				InputStream is = new ByteArrayInputStream(replacedContent.getBytes(StandardCharsets.UTF_8));
				Document doc = db.parse(is);
				doc.getDocumentElement().normalize();
				NodeList list = doc.getElementsByTagName("cycleRange");

				for (int temp = 0; temp < list.getLength(); temp++) {
					Node node = list.item(temp);
					if (node.getNodeType() == Node.ELEMENT_NODE) {
						processDciNodes(node);
					}
				}
				
				NodeList mccList = doc.getElementsByTagName("mcc");
				for(int temp=0;temp < mccList.getLength();temp++) {
					Node node = mccList.item(temp);
					if (node.getNodeType() == Node.ELEMENT_NODE) {
						processDciNodesForMcc(node);
					}
					
				}

				NodeList iicList = doc.getElementsByTagName("iic");
				for (int temp = 0; temp < iicList.getLength(); temp++) {
					Node node = iicList.item(temp);
					if (node.getNodeType() == Node.ELEMENT_NODE) {
						processDciNodesForIic(node);
					}

				}

			}
			baseCacheReloaderSvc.updateReloadCacheCounter(new String[] { CacheReloaderConstants.PARTICIPANT,
					CacheReloaderConstants.PARTICIPANT_SETTLEMENT_BIN, CacheReloaderConstants.MEMBINDETAILS,CacheReloaderConstants.MCC_CONFIG });
		}catch (Exception e) {
			log.error("Network Bin File Upload Exception " + e.getMessage() + " - {}", e);
			return BaseCommonConstants.FAIL_STATUS;
		}
		return BaseCommonConstants.PROCESS_STATUS_SUCCESS;
	}

	private void processDciNodesForMcc(Node node) {
		Element element = (Element) node;
		if (element.getParentNode().getNodeName().equals("mccs")) {
			MccDTO mccDto = new MccDTO();
			mccDto.setMccId(mccRepository.fetchIdFromMccIdSequence());
			mccDto.setMccCode(element.getElementsByTagName("mcc").item(0).getTextContent());
			mccDto.setMccDesc(element.getElementsByTagName("mccDescription").item(0).getTextContent());
			mccDto.setChargeType(element.getElementsByTagName("dxsChargeType").item(0).getTextContent());
			Optional<MccDTO> mccDetail = mccRepository.getByMccCode(mccDto.getMccCode());
			if (mccDetail.isPresent()) {
				mccDto.setLastOperation("update Charge_type");
				mccDto.setLastUpdatedBy(sessionDTO.getUserName());
				mccDto.setLastUpdatedOn(new Date());
				mccRepository.updateMccConfigForChargeType(mccDto);
			} else {
				try {
					mccDto.setCreatedOn(new Date());
					mccDto.setCreatedBy(sessionDTO.getUserName());
					mccDto.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
					mccDto.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
					mccRepository.saveMccConfig(mccDto);
				} catch (Exception ex) {
					handleGenericException(ex);
				}
			}
		}
	}

	private void processDciNodesForIic(Node node) throws ParseException {
		Element element = (Element) node;
		BinDetailsDTO binDetailsDto = new BinDetailsDTO();
		if (element.getParentNode().getNodeName().equals("iics")) {
			binDetailsDto
					.setAcquirerId(element.getElementsByTagName("isoIIC").item(0).getTextContent().substring(5, 11));
			Optional<BinDetailsDTO> binDetailsDTO1 = memberRepository.getNetBinDetailsByAcqId(binDetailsDto.getAcquirerId());
			if (!binDetailsDTO1.isPresent()) {
				populateDciAcqBinDetails(binDetailsDto);
				memberRepository.insertNetwBinRange(binDetailsDto);
			}else {
				populateDciAcqBinDetails(binDetailsDto);
				memberRepository.updateNetBinDetailsForAcqId(binDetailsDto);

			}

		}

	}

	private void processDciNodes(Node node) throws ParseException {
		Element element = (Element) node;
		String fromCycleRange = element.getElementsByTagName("fromCycleRange").item(0).getTextContent();
		String toCycleRange = element.getElementsByTagName("toCycleRange").item(0).getTextContent();
		String rangeStatus = element.getElementsByTagName("rangeStatus").item(0).getTextContent();
		String status = ("International".equalsIgnoreCase(rangeStatus) || "Local".equalsIgnoreCase(rangeStatus)) ? "A"
				: "D";
		String bin = null;
		int length = (fromCycleRange.length() > toCycleRange.length()) ? toCycleRange.length()
				: fromCycleRange.length();
		for (int index = 0; index < length; index++) {
			if (fromCycleRange.charAt(index) != toCycleRange.charAt(index)) {
				bin = fromCycleRange.substring(0, index);
				break;
			}
		}
		if (bin == null) {
			bin = fromCycleRange;
		}
		String panLength = element.getElementsByTagName("panLength").item(0).getTextContent();
		String effectiveDate = element.getElementsByTagName("effectiveDate").item(0).getTextContent();
		String isoNumericCode = element.getElementsByTagName("isoNumericCode").item(0).getTextContent();
		String isoCurrency = element.getElementsByTagName("isoCurrency").item(0).getTextContent();
		String lowBin = StringUtils.rightPad(fromCycleRange, 9, "0");
		String highBin = StringUtils.rightPad(toCycleRange, 9, "9");
		String dxsCode = element.getElementsByTagName("dxs").item(0).getTextContent();

		Optional<BinDetailsDTO> binDetailsDTO1 = memberRepository.getNetBinDetail(bin);
		if (binDetailsDTO1.isPresent()) {
			memberRepository.updateNetBinRange(bin, status,dxsCode, null);
		} else {
			BinDetailsDTO binDetailsDto = new BinDetailsDTO();
			binDetailsDto.setBinNumber(bin);
			binDetailsDto.setDxsCode(dxsCode);
			populateDciBinDetails(panLength, effectiveDate, isoNumericCode, isoCurrency, lowBin, highBin, binDetailsDto);
			try {
				memberRepository.insertNetwBinRange(binDetailsDto);
			}catch(Exception ex) {
				handleGenericException(ex);
			}
		}
	}

	private void handleGenericException(Exception ex) {
		String errorDesc;
		if (ex.getCause() != null) {
			errorDesc = ex.getCause().toString();
		} else {
			errorDesc = ex.getMessage();
		}
		if (StringUtils.contains(errorDesc,UNIQUE_CONSTRAINT_ERROR)) {
			log.info("Duplicate Data Entry - {}",ex.getMessage());
		}else {
			log.info("Error - {}:",ex.getMessage());
		}
	}
	
	private void populateDciBinDetails(String panLength, String effectiveDate, String isoNumericCode,
			String isoCurrency, String lowBin, String highBin, BinDetailsDTO binDetailsDto) throws ParseException {
		String participantId = lookupCache.getDescription(NETWORK_DCI, "DEF_ISS_PARTICIPANT");
		String bankGroup=lookupCache.getDescription(NETWORK_DCI, "DEF_ISS_BANKGRP");
		String binProductType = lookupCache.getDescription(NETWORK_DCI, "BIN_PROD_TYPE");
		String binCardVarient = lookupCache.getDescription(NETWORK_DCI, "BIN_CARD_VARIENT");
		String binCardTechnology = lookupCache.getDescription(NETWORK_DCI, "BIN_CARD_TECHNOLOGY");
		String binCardBrand = lookupCache.getDescription(NETWORK_DCI, "BIN_CARD_BRAND");
		String productType = lookupCache.getDescription(NETWORK_DCI, "PROD_TYPE");
		String settlementBin = lookupCache.getDescription(NETWORK_DCI, "SETTLEMENT_BIN");
		String binCardType = lookupCache.getDescription(NETWORK_DCI, "BIN_CARD_TYPE");

		binDetailsDto.setBinId(fetchBinIdSeq());
		binDetailsDto.setLowBin(lowBin);
		binDetailsDto.setHighBin(highBin);
		binDetailsDto.setAcqBinActivationDate(new SimpleDateFormat("yyyy/MM/dd").parse(effectiveDate));
		binDetailsDto.setDectivationDate(new SimpleDateFormat("yyyy-MM-dd").parse("2099-12-31"));
		binDetailsDto.setBinType("I");
		binDetailsDto.setCreatedBy(sessionDTO.getUserName());
		binDetailsDto.setCreatedOn(new Date());
		binDetailsDto.setLastUpdatedBy(sessionDTO.getUserName());
		binDetailsDto.setLastUpdatedOn(new Date());
		if(participantId != null) {
			binDetailsDto.setParticipantId(participantId);
		}else {
		binDetailsDto.setParticipantId("NDCI5000001");
		}
		binDetailsDto.setDomainUsage("2");
		binDetailsDto.setBankGroup(bankGroup);
		binDetailsDto.setOfflineAllowed("N");
		binDetailsDto.setPanLength(Integer.parseInt(panLength));
		binDetailsDto.setBinCardType(binCardType);
		binDetailsDto.setBinProductType(binProductType);
		binDetailsDto.setBinCardVariant(binCardVarient);
		binDetailsDto.setCardTechnology(binCardTechnology);
		binDetailsDto.setBinCardBrand(binCardBrand);
		binDetailsDto.setMessageType("D");
		binDetailsDto.setProductType(productType);
		binDetailsDto.setSettlementBin(settlementBin);
		binDetailsDto.setStatus("A");
		String countryCode = countryCache.getCountryCodeByISO(isoNumericCode);
		binDetailsDto.setCountryCode(StringUtils.isNotBlank(countryCode) ? countryCode : "");
		binDetailsDto.setCurrencyCode(isoCurrency);
	}

	private void populateDciAcqBinDetails(BinDetailsDTO binDetailsDto) throws ParseException {
		binDetailsDto.setBinId(fetchBinIdSeq());
		String acqProductType = lookupCache.getDescription(NETWORK_DCI, "ACQ_PROD_TYPE");
		String binCardType = lookupCache.getDescription(NETWORK_DCI, "BIN_CARD_TYPE");

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		String dateAsString = formatter.format(new Date());
		binDetailsDto.setAcqBinActivationDate(formatter.parse(dateAsString));
		binDetailsDto.setDectivationDate(new SimpleDateFormat("yyyy-MM-dd").parse("2099-12-31"));
		binDetailsDto.setBinType("A");
		binDetailsDto.setCreatedBy(sessionDTO.getUserName());
		binDetailsDto.setCreatedOn(new Date());
		binDetailsDto.setLastUpdatedBy(sessionDTO.getUserName());
		binDetailsDto.setLastUpdatedOn(new Date());
		binDetailsDto.setBinCardType(binCardType);
		String participantId = lookupCache.getDescription(NETWORK_DCI, "DEF_ACQ_PARTICIPANT");
		if (participantId != null) {
			binDetailsDto.setParticipantId(participantId);
		} else {
			binDetailsDto.setParticipantId("NDCI5000002");
		}
		binDetailsDto.setBankGroup(lookupCache.getDescription(NETWORK_DCI, "DEF_ACQ_BANKGRP"));
		binDetailsDto.setOfflineAllowed("N");
		binDetailsDto.setMessageType("D");
		binDetailsDto.setProductType(acqProductType);
		binDetailsDto.setSettlementBin(lookupCache.getDescription(NETWORK_DCI, "DEF_ACQ_SETBIN"));
		binDetailsDto.setStatus("A");
	}

	private int fetchBinIdSeq() {
		return memberRepository.fetchBinIdSeq();
	}
}
