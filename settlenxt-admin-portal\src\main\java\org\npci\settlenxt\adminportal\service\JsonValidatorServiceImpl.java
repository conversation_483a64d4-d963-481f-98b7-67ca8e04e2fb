package org.npci.settlenxt.adminportal.service;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.JsonValidatorRepository;
import org.npci.settlenxt.portal.common.dto.JsonValidatorDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Service
public class JsonValidatorServiceImpl implements JsonValidatorService {
	
	@Autowired
	JsonValidatorRepository jsonValidatorRepository;
	
	@Autowired
	SessionDTO sessionDTO;

	@Override
	public List<JsonValidatorDTO> getJsonValidatorDetails() {

		return jsonValidatorRepository.getJsonValidatorList();
	}

	@Override
	public void addJsonValidatorStg(JsonValidatorDTO jsonValidatorDto) {

		JsonValidatorDTO jsonValidatorSeqId = jsonValidatorRepository.fetchSeqId();
		jsonValidatorDto.setSeqId(jsonValidatorSeqId.getSeqId());
		jsonValidatorDto.setLastUpdatedOn(new Date());
		jsonValidatorDto.setLastUpdatedBy(sessionDTO.getUserName());
		jsonValidatorDto.setCreatedOn(new Date());
		jsonValidatorDto.setCreatedBy(sessionDTO.getUserName());
		jsonValidatorDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		jsonValidatorDto.setLastOperation(CommonConstants.ADD_REASON_CODE_LAST_OPERATION);
		jsonValidatorRepository.addJsonValidatorStg(jsonValidatorDto);
		
	}

	@Override
	public JsonValidatorDTO getJsonValidator(int seqId) {

		return jsonValidatorRepository.getJsonValidatorStgDetail(seqId);
	}

	@Override
	public List<JsonValidatorDTO> getPendingReasonCode() {

		return jsonValidatorRepository.getJsonValidatorPendingList();
	}

	@Override
	public JsonValidatorDTO getJsonValidatorStgDetail(int seqId) {

		return jsonValidatorRepository.getJsonValidatorStgDetail(seqId);
	}

	@Override
	public JsonValidatorDTO updateJsonValidatorStg(JsonValidatorDTO jsonValidatorDto) {

		jsonValidatorDto.setLastUpdatedBy(sessionDTO.getUserName());
		jsonValidatorDto.setLastUpdatedOn(new Date());
		jsonValidatorDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		jsonValidatorDto.setLastOperation(CommonConstants.EDIT_MCC_LAST_OPERATION);
		jsonValidatorRepository.updateJsonValidatorStg(jsonValidatorDto);
		return jsonValidatorDto;
	}

	@Override
	public JsonValidatorDTO updateApproveOrRejectJsonValidator(int seqId, String status, String remarks) {

		JsonValidatorDTO jsonValidatorDTO = getJsonValidatorStgDetatil(seqId);
		jsonValidatorDTO.setRequestState(status);
		jsonValidatorDTO.setCheckerComments(remarks);
		jsonValidatorDTO.setLastUpdatedOn(new Date());
		jsonValidatorDTO.setLastUpdatedBy(sessionDTO.getUserName());
		
		
		if ("Approved".equals(status)) {
			jsonValidatorDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			jsonValidatorDTO.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
		} else {
			
			jsonValidatorDTO.setRequestState(CommonConstants.REQUEST_STATE_REJECTED);
		}

		

		if (jsonValidatorDTO.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
			
			try {
				updateJsonValidator(jsonValidatorDTO);
				
			} catch (Exception e) {
				
				log.error("Error while Updating in main Json Validator table");
			}
		}
		jsonValidatorDTO.setLastUpdatedBy(jsonValidatorDTO.getLastUpdatedBy());
		jsonValidatorDTO.setLastUpdatedOn(jsonValidatorDTO.getLastUpdatedOn());
		jsonValidatorDTO.setCheckerComments(remarks);
		jsonValidatorRepository.updateJsonValidatorStg(jsonValidatorDTO);
		
		
		return jsonValidatorDTO;
	}
	
	public JsonValidatorDTO getJsonValidatorStgDetatil(int seqId) {

		return jsonValidatorRepository.getJsonValidatorStgDetail(seqId);
	}
	
	private void updateJsonValidator(JsonValidatorDTO jsonValidatorDto) {
		JsonValidatorDTO jsonValidatorDtoMain = jsonValidatorRepository.getJsonValidator(jsonValidatorDto.getSeqId());
			if (ObjectUtils.isEmpty(jsonValidatorDtoMain)) {
				jsonValidatorRepository.saveJsonValidator(jsonValidatorDto);
			} else {
				
				jsonValidatorRepository.updateJsonValidator(jsonValidatorDto);
			}

			jsonValidatorDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			
		}

	@Override
	public JsonValidatorDTO discardJsonValidator(int seqId) {

		JsonValidatorDTO jsonValidatorDto = jsonValidatorRepository.getJsonValidatorStgDetail(seqId);
		JsonValidatorDTO jsonValidatorDtoMain = jsonValidatorRepository.getJsonValidator(seqId);

		if (jsonValidatorDtoMain != null) {
			jsonValidatorDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			jsonValidatorDtoMain.setLastOperation("Discarded");
			jsonValidatorRepository.updateJsonValidatorStg(jsonValidatorDtoMain);
		} else {
			jsonValidatorRepository.deleteDiscardedJsonValidator(seqId);
		}

		return jsonValidatorDto;
	}

	@Override
	public String updateBulkStgJsonValidator(String jsonValidatorList, String status) {

		try {
			String checkerComments = CommonConstants.RECORD_DESC_REJECTED;
			String reqState = CommonConstants.RECORD_REJECTED;
			if (StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
				checkerComments = CommonConstants.RECORD_DESC_APPROVED;
				reqState = "Approved";
			}
			String[] tranArray = jsonValidatorList.split("\\|");
			int id;
			for(String seqId:tranArray) {
				id = Integer.parseInt(seqId);
				updateApproveOrRejectJsonValidator(id,reqState,checkerComments);
			}
		} catch (Exception e) {
			log.error("Error while approving/rejecting Json Validator");
			return CommonConstants.RESULT_ERROR;
		}
		return CommonConstants.RESULT_SUCCESS;
	}

	@Override
	public List<String> getPcodeList() throws SettleNxtException {

		return jsonValidatorRepository.getPcodeList();
	}

	@Override
	public List<JsonValidatorDTO> getJsonValidatorMainDetails() {

		return jsonValidatorRepository.getJsonValidatorMainList();
	}

}
