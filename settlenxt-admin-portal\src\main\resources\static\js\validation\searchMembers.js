var landingParamflag="main";
var data;
function viewMember(reqId) {
	
	url = '/getMember';
	data = "reqType,"+"V"+",participantId," +reqId+ ",landingParam," + landingParamflag; 
	postData(url, data);
	clickAndDisable(this); 
}

function viewMemberMain(reqId) {
	
	url = '/getMemberMain';
	data = "reqType,"+"V"+",participantId," +reqId + ",landingParam," + landingParamflag; 
	postData(url, data);
	clickAndDisable(this); 
}

function viewMemberPending(memberId,participantId,bankMasterCode) {
	
		url = '/getMember';
	
		data = "memberId,"+memberId +",participantId," +participantId + ",bankMasterCode," +bankMasterCode + ",reqType,"+"P"+ ",landingParam," + landingParamflag; 
		postData(url, data);
}



$(document).ready(function() {


	var tokenValue = document.getElementsByName("_TransactToken")[0].value;						
	
	$("#tabnew").dataTable({
		"bServerSide": false,
		"bProcessing": true,
		 paging: true,
         pageLength: 10,
		"sAjaxSource": "searchFinalMembers",
		
		"bJQueryUI": true,
		"sServerMethod": "POST",
		"bDestroy": true,
		"language": {
		    "search": "Search : "
		  },
		
		"fnRowCallback": function( nRow, aData, _iDisplayIndex ) {
			$('td:eq(0)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(1)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(2)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(3)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(4)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(5)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(6)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(7)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(8)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(9)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			
			return nRow;
		},
		"fnServerParams": function ( aoData ) {
			aoData.push({ "name": "_TransactToken", "value": tokenValue}
					
			);
		},
	
		"fnServerData": function ( sSource, aoData, fnCallback ) {
			$.ajax({
				"dataType": 'json',
				"type": "POST",
				"url": sSource,
				"data": aoData,
				"beforeSend": function (xhr) {
					xhr.setRequestHeader('_TransactToken', tokenValue);
				},
				"success": function(json){
					
					fnCallback(json);
					
				}
			});               
		},
		   initComplete: function () {
            var api = this.api();
            console.log("api " + api);
            
            $('#IsLastLevel').val(NaN);
 
            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    
                    console.log("title " + title);
                    if(colIdx<actionColumnIndex){
                    $(cell).html(title+'<br><input class="search-box"   type="text" />');
 
                    // On every keypress in this input
                    $(
                        'input',
                        $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
                    )
                        .off('keyup change')
                        .on('change', function (_e) {
                            // Get the search value
                            $(this).attr('title', $(this).val());
                            var regexr = '({search})'; 
 
                            cursorPosition = this.selectionStart;
                            // Search the column for that value
                            api
                                .column(colIdx)
                                .search(
                                    this.value != ''
                                        ? regexr.replace('{search}', '(((' + this.value + ')))')
                                        : '',
                                    this.value != '',
                                    this.value == ''
                                )
                                .draw();
                        })
						.on('click', function (e) {
                            e.stopPropagation();})
                        .on('keyup', function (e) {
                            e.stopPropagation();
 
                            $(this).trigger('change');
							if(cursorPosition && cursorPosition!=null){
                            $(this)
                                .focus()[0]
                                .setSelectionRange(cursorPosition, cursorPosition);
								}
                        });
                        }else{
                         $(cell).html(title+'<br> &nbsp;');}
                });
                $('#tabnew_filter').hide();
                $('.dt-buttons').hide();
        },
        dom: 'lBfrtip',
		
        buttons: [
            {
                        extend: 'excelHtml5',
                        text: 'Export',
                        filename: 'Member' ,
						header:'false', 
						title: null,
						sheetName:'Member',
						className:'defaultexport',
						exportOptions: {
							 columns: 'th:not(:last-child)'
				         }
                    },
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Member' ,
						header:'false', 
						title: null,
						sheetName:'Member',
						className:'defaultexport',
						exportOptions: {
				            columns: 'th:not(:last-child)'
				         }
                    }
        ],    
		 
		searching: true,
		info: true
		
		
		});
      
	$('#tabnew').on('click','.viewMemberMain',function() {
		
		url = '/getMemberMain';
		var participantId = $(this).data("value").split('-')[2];
		data = "reqId,"+$(this).data("value")+",reqType,"+"V"+",participantId," +participantId; 
		postData(url, data);
	});
		
	
	$('#tabnew').on('click','.deactiveStr',function() {
		
		
		url = '/activateDeactivateStr';
		data = "storeId,"+$(this).data("value")+",flag,"+"D"; 
		postData(url, data);
	});
	
	$("#tabnewchkr").dataTable({
		"bServerSide": false,
		"bProcessing": true,
		 paging: true,
         pageLength: 10,
		"sAjaxSource": "searchFinalMembers",
		
		"bJQueryUI": true,
		"sServerMethod": "POST",
		"bDestroy": true,
		"language": {
		    "search": "Search : "
		  },
		
		"fnRowCallback": function( nRow, aData, _iDisplayIndex ) {
			$('td:eq(0)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(1)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(2)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(3)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(4)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(5)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(6)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(7)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(8)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			$('td:eq(9)', nRow).addClass('alignment').click(function(){viewMemberMain(aData[0]);});
			
			landingParamflag="main";	
			
			return nRow;
		},
		"fnServerParams": function ( aoData ) {
			aoData.push({ "name": "_TransactToken", "value": tokenValue}
					
			);
		},
	
		"fnServerData": function ( sSource, aoData, fnCallback ) {
			$.ajax({
				"dataType": 'json',
				"type": "POST",
				"url": sSource,
				"data": aoData,
				"beforeSend": function (xhr) {
					xhr.setRequestHeader('_TransactToken', tokenValue);
				},
				"success": function(json){
					
					fnCallback(json);
					
				}
			});               
		},
		   initComplete: function () {
            var api = this.api();
            console.log("api " + api);
            
            $('#IsLastLevel').val(NaN);
 
            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnewchkr thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    
                    console.log("title " + title);
                    if(colIdx<actionColumnIndex){
                    $(cell).html(title+'<br><input class="search-box"   type="text" />');
 
                    // On every keypress in this input
                    $(
                        'input',
                        $('#tabnewchkr thead tr th').eq($(api.column(colIdx).header()).index())
                    )
                        .off('keyup change')
                        .on('change', function (_e) {
                            // Get the search value
                            $(this).attr('title', $(this).val());
                            var regexr = '({search})'; 
 
                            cursorPosition = this.selectionStart;
                            // Search the column for that value
                            api
                                .column(colIdx)
                                .search(
                                    this.value != ''
                                        ? regexr.replace('{search}', '(((' + this.value + ')))')
                                        : '',
                                    this.value != '',
                                    this.value == ''
                                )
                                .draw();
                        })
						.on('click', function (e) {
                            e.stopPropagation();})
                        .on('keyup', function (e) {
                            e.stopPropagation();
 
                            $(this).trigger('change');
							if(cursorPosition && cursorPosition!=null){
                            $(this)
                                .focus()[0]
                                .setSelectionRange(cursorPosition, cursorPosition);
								}
                        });
                        }else{
                         $(cell).html(title+'<br> &nbsp;');}
                });
                $('#tabnewchkr_filter').hide();
                $('.dt-buttons').hide();
        },
        dom: 'lBfrtip',
		
        buttons: [
            {
                        extend: 'excelHtml5',
                        text: 'Export',
                        filename: 'Member' ,
						header:'false', 
						title: null,
						sheetName:'Member',
						className:'defaultexport',
						exportOptions: {
							 columns: 'th:not(:last-child)'
				         }
                    },
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Member' ,
						header:'false', 
						title: null,
						sheetName:'Member',
						className:'defaultexport',
						exportOptions: {
				            columns: 'th:not(:last-child)'
				         }
                    }
        ],    
		 
		searching: true,
		info: true
		});
	
	$('#tabnewchkr').on('click','.viewMemberMain',function() {
		
		url = '/getMemberMain';
		var participantId = $(this).data("value").split('-')[2];
		data = "reqId,"+$(this).data("value")+",reqType,"+"V"+",participantId," +participantId; 
		postData(url, data);
	});
	
	$("#tabnewPending").dataTable({
"bServerSide": false,
"bProcessing": true,
paging: true,
pageLength: 10,
		"sAjaxSource": "searchPendingMembers",
		
		"bJQueryUI": true,
		"sServerMethod": "POST",
		"bDestroy": true,
		"language": {
		    "search": "Member Name : "
		  },
		"fnRowCallback": function( nRow, aData, _iDisplayIndex ) {
			
			if(aData[10] == 'Rejected'){
			$('td:eq(0)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(1)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(2)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(3)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(4)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(5)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(6)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(7)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(8)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(9)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(10)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(11)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(12)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
				
				}else{
				
				$('td:eq(0)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(1)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(2)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(3)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(4)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(5)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(6)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(7)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(8)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(9)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(10)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(11)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			$('td:eq(12)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[14],aData[0],aData[3]);});
			
			}
			landingParamflag="approve";	
			return nRow;
		},
		
		"fnServerParams": function ( aoData ) {
			aoData.push({ "name": "_TransactToken", "value": tokenValue}
					
			);
		},
		
		"fnServerData": function ( sSource, aoData, fnCallback ) {
			$.ajax({
				"dataType": 'json',
				"type": "POST",
				"url": sSource,
				"data": aoData,
				"beforeSend": function (xhr) {
					xhr.setRequestHeader('_TransactToken', tokenValue);
				},
				"success": function(json){
					
					fnCallback(json);
					
				}
			});               
		},
		   initComplete: function () {
            var api = this.api();
            console.log("api " + api);
            
            $('#IsLastLevel').val(NaN);
 
            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnewPending thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    
                    console.log("title " + title);
                    if(colIdx<actionColumnIndex){
                    $(cell).html(title+'<br><input class="search-box"   type="text" />');
 
                    // On every keypress in this input
                    $(
                        'input',
                        $('#tabnewPending thead tr th').eq($(api.column(colIdx).header()).index())
                    )
                        .off('keyup change')
                        .on('change', function (_e) {
                            // Get the search value
                            $(this).attr('title', $(this).val());
                            var regexr = '({search})'; 
 
                            cursorPosition = this.selectionStart;
                            // Search the column for that value
                            api
                                .column(colIdx)
                                .search(
                                    this.value != ''
                                        ? regexr.replace('{search}', '(((' + this.value + ')))')
                                        : '',
                                    this.value != '',
                                    this.value == ''
                                )
                                .draw();
                        })
						.on('click', function (e) {
                            e.stopPropagation();})
                        .on('keyup', function (e) {
                            e.stopPropagation();
 
                            $(this).trigger('change');
							if(cursorPosition && cursorPosition!=null){
                            $(this)
                                .focus()[0]
                                .setSelectionRange(cursorPosition, cursorPosition);
								}
                        });
                        }else{
                         $(cell).html(title+'<br> &nbsp;');}
                });
                $('#tabnewPending_filter').hide();
                $('.dt-buttons').hide();
        },
        dom: 'lBfrtip',
		
        buttons: [
            {
                        extend: 'excelHtml5',
                        text: 'Export',
                        filename: 'Member' ,
						header:'false', 
						title: null,
						sheetName:'Member',
						className:'defaultexport',
						exportOptions: {
							 columns: 'th:not(:last-child)'
				         }
                    },
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Member' ,
						header:'false', 
						title: null,
						sheetName:'Member',
						className:'defaultexport',
						exportOptions: {
				            columns: 'th:not(:last-child)'
				         }
                    }
        ],    
		 
		searching: true,
		info: true
		
		
		});
      

	$("#tabnewPendingchkr").dataTable({
		"bServerSide": false,
		"bProcessing": true,
		 paging: true,
         pageLength: 10,
		"sAjaxSource": "searchPendingMembers",
		
		"bJQueryUI": true,
		"sServerMethod": "POST",
		"bDestroy": true,
		"language": {
		    "search": "Member Name : "
		  },
		"fnRowCallback": function( nRow, aData, iDisplayIndex ) {
		if(iDisplayIndex==0)
		{
		memIds=[];
		}
			 if(aData[11] == 'Rejected'){
				 	$('td:eq(0)', nRow).html('<input type=checkbox style="display:none;" name="type" value=\''+aData[1]+'\'></input>');
				 } 
				 if(aData[11] == 'Pending'){
	
				 	memIds.push(aData[1]);
				 	
				 	
				 	
				 	$('td:eq(0)', nRow).html('<input type=checkbox name="type" id="selectSingle" onclick="mySelect();" value=\''+aData[1]+'\'></input>');
				 }
			$('td:eq(1)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(2)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(3)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(4)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(5)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(6)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});	
			$('td:eq(7)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(8)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(9)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(10)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(11)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(12)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			$('td:eq(13)', nRow).addClass('alignment').click(function(){viewMemberPending(aData[15],aData[1],aData[4]);});
			landingParamflag="approve";	
			
			return nRow;
		},
		
		"fnServerParams": function ( aoData ) {
			aoData.push({ "name": "_TransactToken", "value": tokenValue}
					
			);
		},
		
		"fnServerData": function ( sSource, aoData, fnCallback ) {
			$.ajax({
				"dataType": 'json',
				"type": "POST",
				"url": sSource,
				"data": aoData,
				"beforeSend": function (xhr) {
					xhr.setRequestHeader('_TransactToken', tokenValue);
				},
				"success": function(json){
					
					fnCallback(json);
					
				}
			});               
		},
		    initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                //If first column to be skipped to include the filter for the reasons line check box 
                if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnewPendingchkr thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   if (colIdx < actionColumnIndex) {
                       
                        $(cell).html(title + '<br><input class="search-box"   type="text" />');

                        // On every keypress in this input
                        $(
                            'input',
                            $('#tabnewPendingchkr thead tr th').eq($(api.column(colIdx).header()).index())
                        )
                            .off('keyup change')
                            .on('change', function (_e) {
                                // Get the search value
                                $(this).attr('title', $(this).val());
                                  var regexr = '({search})'; 

                                cursorPosition = this.selectionStart;
                                // Search the column for that value
                                api
                                    .column(colIdx)
                                    .search(
                                        this.value != ''
                                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                                            : '',
                                        this.value != '',
                                        this.value == ''
                                    )
                                    .draw();
                            })
                            .on('click', function (e) {
                                e.stopPropagation();
                            })
                            .on('keyup', function (e) {
                                e.stopPropagation();

                                $(this).trigger('change');
                                if (cursorPosition && cursorPosition != null) {
                                    $(this)
                                        .focus()[0]
                                        .setSelectionRange(cursorPosition, cursorPosition);
                                }
                            });
                    } else {
                        $(cell).html(title + '<br> &nbsp;');
                    }
                   }
                });
            $('#tabnewPendingchkr_filter').hide();
           
        },
        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Member',
                header: 'false',
                title: null,
                sheetName: 'Member',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'Member' ,
				header:'false', 
				title: null,
				sheetName:'Member',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });
	
	$('#tabnewPendingchkr').on('click','.viewPendingMember',function() {
		
		url = '/getMember';
			var memberId = $(this).data("value").split('-')[0];
		var participantId = $(this).data("value").split('-')[1];
		var bankMasterCode = $(this).data("value").split('-')[2];
			data = "memberId,"+memberId +",participantId," +participantId + ",bankMasterCode," +bankMasterCode + ",reqType,"+"P"; 
		postData(url, data);
	});
	
 if (networkUsed== 'Y') {
        orderOption = [[1, 'desc']]; // Sort by the 12th column in descending order
    } else {
        orderOption = [[0, 'asc']]; // Default sorting (e.g., by the first column in ascending order)
    }	
	$("#tabnewSavedMem").dataTable({
"bServerSide": false,
"bProcessing": true,
paging: true,
pageLength: 10,
		"sAjaxSource": "searchSavedMembers",
		
		"bJQueryUI": true,
		"sServerMethod": "POST",
		"bDestroy": true,
		"language": {
		    "search": "Member Name : "
		  },
		   "order":orderOption,
		"fnRowCallback": function( nRow, aData, _iDisplayIndex ) {
			$('td:eq(0)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(1)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(2)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(3)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(4)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(5)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(6)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(7)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(8)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(9)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			$('td:eq(10)', nRow).addClass('alignment').click(function(){viewMember(aData[0]);});
			if(networkUsed=='Y'){
			$('td:eq(11)', nRow).addClass('alignment').click(function() { viewMember(aData[0]); });}
			landingParamflag="saveData";	
			
			return nRow;
		},
		
		"fnServerParams": function ( aoData ) {
			aoData.push({ "name": "_TransactToken", "value": tokenValue}
					
			);
		},
		
		"fnServerData": function ( sSource, aoData, fnCallback ) {
			$.ajax({
				"dataType": 'json',
				"type": "POST",
				"url": sSource,
				"data": aoData,
				"beforeSend": function (xhr) {
					xhr.setRequestHeader('_TransactToken',tokenValue);
				},
				"success": function(json){
					
					fnCallback(json);
					
				}
			});               
		},
		   initComplete: function () {
            var api = this.api();
             
            $('#IsLastLevel').val(NaN);
 
            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnewSavedMem thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   
                    if(colIdx<actionColumnIndex){
                    $(cell).html(title+'<br><input class="search-box"   type="text" />');
 
                    // On every keypress in this input
                    $(
                        'input',
                        $('#tabnewSavedMem thead tr th').eq($(api.column(colIdx).header()).index())
                    )
                        .off('keyup change')
                        .on('change', function (_e) {
                            // Get the search value
                            $(this).attr('title', $(this).val());
                            var regexr = '({search})'; 
 
                            cursorPosition = this.selectionStart;
                            // Search the column for that value
                            api
                                .column(colIdx)
                                .search(
                                    this.value != ''
                                        ? regexr.replace('{search}', '(((' + this.value + ')))')
                                        : '',
                                    this.value != '',
                                    this.value == ''
                                )
                                .draw();
                        })
						.on('click', function (e) {
                            e.stopPropagation();})
                        .on('keyup', function (e) {
                            e.stopPropagation();
 
                            $(this).trigger('change');
							if(cursorPosition && cursorPosition!=null){
                            $(this)
                                .focus()[0]
                                .setSelectionRange(cursorPosition, cursorPosition);
								}
                        });
                        }else{
                         $(cell).html(title+'<br> &nbsp;');}
                });
                $('#tabnewSavedMem_filter').hide();
                $('.dt-buttons').hide();
        },
        dom: 'lBfrtip',
		
        buttons: [
            {
                        extend: 'excelHtml5',
                        text: 'Export',
                        filename: 'Member' ,
						header:'false', 
						title: null,
						sheetName:'Member',
						className:'defaultexport',
						exportOptions: {
							 columns: 'th:not(:last-child)'
				         }
                    },
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Member' ,
						header:'false', 
						title: null,
						sheetName:'Member',
						className:'defaultexport',
						exportOptions: {
				            columns: 'th:not(:last-child)'
				         }
                    }
        ],    
		 
		searching: true,
		info: true
		
		
		});
      

	
	$('#tabnew').on('click', '.actDeActBtn', function() {
		
		var value = $(this).attr('data-value').split(',');
		
		 $('#memId').val($(this).closest('tr').find('td:eq(0)').text());
		 $('#memType').val($(this).closest('tr').find('td:eq(3)').text());
		 $('#memName').val($(this).closest('tr').find('td:eq(1)').text());
		 $('#partId').val($(this).closest('tr').find('td:eq(2)').text())
		
		 
		if(value[1] == 'A')
		   $('.displayActDeactBtn').text('Activate');
		else
		   $('.displayActDeactBtn').text('Deactivate');
		 
		var value = $('#memId').val() + ',' +  value[1] ;
		
		$('.displayActDeactBtn').attr('data-value',value);
		
		
		
		
	});
	
$('.displayActDeactBtn').click(function() {
		
	    $('#remrks').val('');
		var value = $(this).attr('data-value').split(',');
		var url = '/actDeactMember';	
		data = "memberId," + value[0] + ",reqType," + value[1] +",remarks,"+$('#remrks').val();
		postData(url, data);

		
	});
	
 $("#excelExport").on("click", function () {
	        $(".buttons-excel").trigger("click");
	    });
	    
	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
	 
	     $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
	    $("#selectAll").click(function(){
        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
      
  
	
	if( $('#selectAll').is(':checked') ){

	if(memIds.length>0) {
 $("#toggleModalMember").modal('show');
        }
}
else{
   $("#toggleModalMember").modal('hide');
			 var ele=document.getElementsByName('type');  
	     for(var i of ele){  
	         if(i.type=='checkbox')  
	             i.checked=false;  
	     } 
        
}
		   var referenceNoList = document.getElementById("memIds");
		   referenceNoList.innerHTML = memIds.length+"     "+"records are selected";
	
});
	
});



function clickAndDisable(link) {
    // disable subsequent clicks
    link.onclick = function(event) {
       event.preventDefault();
    }
  } 






function editMember(reqId) {
	
	url = '/editMember';
	data = "participantId,"+reqId+",reqType,"+"E"+",editFlag,"+"E"; 
	postData(url, data);
	clickAndDisable(this); 
}

function editSavedMember(reqId) {
	
	url = '/getSavedMember';
	data = "participantId,"+reqId+",reqType,"+"E"+",editFlag,"+"E"; 
	postData(url, data);
	clickAndDisable(this); 
}

function submitForm(url)
{
	data = "";
	postData(url, data);
}
function viewFeeWaiver(reqId) {
	
	url = '/createFeeWaiver';
	data = "reqId,"+reqId; 
	postData(url, data);
	clickAndDisable(this); 
}

function memberDeactivatedAlert()
{
	alert("Member not in active status can not edit");
}

function approveorRejectBulkMem(type,action){
	
	 var url = '/approveorRejectBulkMem';
	
	 var array = [];

	 if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(action=='All'){
		 array=memIds; 
	 }
	   
	
		
		
		
		if(array.length>0){
		var memIdsList = "";
		for ( var i of array) {
			memIdsList = memIdsList + i + "|" ;
		}
		
		 console.log(memIdsList);
	if(type=='A'){
			
		data =  "status,"+"A"+",bulkApprovalIdList,"+memIdsList;
	}
	else if(type=='R'){
		
		data = "status,"+"R"+",bulkApprovalIdList,"+memIdsList;
	}
	

	postData(url, data);
	}
	else{
	  
			  $('#errorStatus4').html('Please Select  Atleast One Record');
				$('#jqueryError4').show();
	
	}
	
}


function deselectAll() {

	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  
   for(var i of ele){  
       if(i.type=='checkbox')  
           i.checked=false;  
   } 
   
}
function mySelect(){
	
	
	$('#jqueryError4').hide();
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	    
	
	  var referenceNoList = document.getElementById("memIds");
    
    
    if(array.length>0){
	 if(array.length==memIds.length){
	
	 
		 $('#selectAll').prop('checked', true);
		
		   referenceNoList.innerHTML = memIds.length+"     "+"records are selected";
		    
		 
			
			 $("#toggleModalMember").modal('show');
	 }
	 else{
		 $("#toggleModalMember").modal('hide');
		 
	 }}
	
}