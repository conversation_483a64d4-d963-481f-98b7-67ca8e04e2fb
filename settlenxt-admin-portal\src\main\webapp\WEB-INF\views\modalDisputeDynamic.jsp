<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<style>
.input__container {
	display: flex;
	justify-content: space-between;
	margin: 5pt;
}

.sub_select {
	width: fit-content;
	min-width: 25rem;
	max-width: 25rem;
}

.sub_input {
	width: fit-content;
	min-width: 25rem;
}

.file_upload {
	margin-left: 6px;
}

.file_select {
	margin-left: 32rem;
}
</style>
<script type="text/javascript"
	src="./static/js/validation/modalDisputeDynamic.js"></script>

<input type="hidden" id="mti" name="mti" />
<input type="hidden" id="funcCode" name="funcCode" />
<input type="hidden" id="fullPartial" name="fullPartial" />
<input type="hidden" id="reasonSubtype" style="max-width: 10rem;" name="reasonSubtype" />
<input type="hidden" id="messageReasonCode" style="max-width: 10rem;" name="messageReasonCode" />
<input type="hidden" id="memberMessageText" name="memberMessageText" />
<input type="hidden" id="documentIndicator" name="documentIndicator" />
<input type="hidden" id="actionCode" name="actionCode" />
<input type="hidden" id="cardHolderUID" name="cardHolderUID" />
<input type="hidden" id="cardHolderIncomeTaxPan" name="cardHolderIncomeTaxPan" />
<input type="hidden" id="merchantIndicator" name="merchantIndicator" />
<input type="file" id="fileArr" name="fileArr" style="display:none;" />
<input type="hidden" id="controlNo" name="controlNo" />
<input type="hidden" id="amountTransaction" name="amountTransaction" />
<input type="hidden" id="modelName" />
<input type="hidden" id="amountAdditional" name="amountAdditional" />



<div class="modal fade" id="disputeModal" tabindex="-1"
	role="dialog" aria-labelledby="disputeModal" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="disputeModalTitle" />				
			</div>
			<div class="modal-body" id="data_modal">
				<div id="inputControls">
				</div>
				<div class="input__container">
				  <span id="modalerrorMessage" class="error"></span>
				</div>
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="hideData()">Cancel</button>
				<button type="button" class="btn btn-secondary" onclick="resetDisputeData()">Clear</button>
				<button type="button" class="btn btn-primary submitButtonchargeBackRefund" id="submitDispute"
					onclick="submitDynData()">Submit</button>
			</div>
		</div>
	</div>
</div>

