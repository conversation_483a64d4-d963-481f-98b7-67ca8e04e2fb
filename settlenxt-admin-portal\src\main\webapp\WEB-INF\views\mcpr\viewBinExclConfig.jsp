<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script src="./static/js/validation/mcpr/viewApproveBinExclConfig.js"
	type="text/javascript"></script>
<script src="./static/js/validation/mcpr/viewBinExclRGCS.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	
	<c:url value="getBinExclConfig" var="getBinExclConfig" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveBinExclConfig" modelAttribute="binExclDTO"
		action="${getBinExclConfig}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"></span><spring:message code="binexcl.mainTab.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="excId" value="${binExclDTO.excId}">

						<input type="hidden" id="crtuser"
							value="${binExclDTO.createdBy}" />

						<table class="table table-striped infobold" style="font-size: 12px">
							<caption style="display:none;">Bin Exclusion</caption>
									<thead style="display:none;"><th scope = "col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"></span><spring:message code="binexcl.viewscreen.title" /></span></strong>
										</div></td>
										<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									
								</tr>
								
								
								<tr>
									<td><label><spring:message code="binexcl.excId" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.excId}</td>
									<td><label><spring:message code="binexcl.participantName" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.bankName}</td>
									<td><label><spring:message code="binexcl.baseorfeature" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.feeSelectionType}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									
								</tr>
								<tr>
									
									
									<td><label><spring:message code="binexcl.bin" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.bin}</td>
									<td><label><spring:message code="binexcl.fromDate" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.fromDate}</td>
									<td><label><spring:message code="binexcl.toDate" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.toDate}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>

								
							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									
								

									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/showBinExclusion');"><spring:message code="binexcl.backBtn" /></button>
										
						<c:if test="${binExclDTO.requestState =='A' }">
								<sec:authorize access="hasAuthority('Edit Bin Exclusion Config')">
								<input name="editButton" type="button" class="btn btn-success"
								 id="approveRole" value="Edit" 
								onclick="Edit('/editBinExclConfig','${binExclDTO.excId}','mainTab');"/>
								</sec:authorize>
							</c:if>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

