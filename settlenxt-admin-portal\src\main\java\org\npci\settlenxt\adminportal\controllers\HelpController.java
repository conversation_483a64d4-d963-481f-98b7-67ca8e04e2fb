package org.npci.settlenxt.adminportal.controllers;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.npci.settlenxt.adminportal.service.HelpService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
public class HelpController extends BaseController {

	@Autowired
	HelpService helpService;

	private static final String SYS_TYPE_ADMIN_HELP_DOC = "AdminHelpDocuments";
	private static final String VIEW_PAGE = "viewHelpDocument";

	private static final String ADMIN_HELP_DOC_SYSKEY_LIST = "adminHelpDocKeyList";

	@PostMapping("/downloadHelpFile")
	public void downloadMemberFile(@RequestParam("documentName") String documentName, HttpServletResponse response,
			Model model) {

		response.setContentType("application/octet-stream");
		response.setHeader("Content-Disposition", "attachment;filename=" + documentName);
		response.setStatus(HttpServletResponse.SC_OK);
		String documentPath = helpService.getDocumentPath(documentName, SYS_TYPE_ADMIN_HELP_DOC);
		try {

			Path file = Paths.get(documentPath);
			response.setHeader("Content-Disposition", "attachment;filename=" + file.getFileName());
			Files.copy(file, response.getOutputStream());

		} catch (Exception e) {
			log.error("Unable to download the help document file :{} ", documentName, e);
			handleErrorCodeAndForward(model, VIEW_PAGE, e);

		}
	}

	@PostMapping("/fetchHelpDocument")
	public String fetchHelpDocuments(Model model) {

		List<String> bankHelpSyskeyList = helpService.getSysKeyFromCache(SYS_TYPE_ADMIN_HELP_DOC);
		model.addAttribute(ADMIN_HELP_DOC_SYSKEY_LIST, bankHelpSyskeyList);
		return getView(model, VIEW_PAGE);
	}

}
