package org.npci.settlenxt.adminportal.controllers;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.ReportStatusDTO;
import org.npci.settlenxt.adminportal.service.ReportService;
import org.npci.settlenxt.adminportal.service.UserService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class ReportStatusController extends BaseController {

	@Autowired
	BaseLookupService lookupService;

	@Autowired
	UserService userService;

	@Autowired
	ReportService reportService;

	private static final String SHOW_REPORT_STATUS = "showReportStatus";
	private static final String PROC_CODE = "prodCd";
	private static final String REPORT_TYPE = "reportType";
	private static final String STATUS = "status";
	private static final String STATUS_DATA = "statusData";

	private static final String FLAG = "flag";

	private static final String CYCLE_NUMBERS = "cycleNumbers";

	@PostMapping("/showReportStatus")
	@PreAuthorize("hasAuthority('View Report Status')")
	public String searchReportStatus(Model model) {

		ReportStatusDTO reportStatusDto = new ReportStatusDTO();

		List<String> prodCdList = reportService.getProductId();
		List<String> reportType1 = reportService.getSettlementFileType();
		List<CodeValueDTO> status = lookupService.getLookupData(CommonConstants.REPORTSTATUS);

		model.addAttribute(PROC_CODE, prodCdList);
		model.addAttribute(REPORT_TYPE, reportType1);
		model.addAttribute(STATUS, status);

		model.addAttribute(BaseCommonConstants.PARTICIPANT_ID_LISTS, userService.getParticipantList());

		model.addAttribute(CommonConstants.REPORT_STATUS_DTO, reportStatusDto);
		return getView(model, SHOW_REPORT_STATUS);
	}

	@PostMapping("/reportStatusFetchData")
	@PreAuthorize("hasAuthority('View Report Status')")
	public String searchReportStatus(@RequestParam("productCode") String productCode,
			@RequestParam("status") String status, @RequestParam("participantId") String participantId,
			@RequestParam("reportType") String reportType, @RequestParam("cycleNumber") String cycleNumber,
			@RequestParam("cycleDate") String cycleDate, Model model) {
		try {
			ReportStatusDTO reportStatusDto = new ReportStatusDTO();
			ReportStatusDTO reportStatusDto1 = new ReportStatusDTO();
			int configureTime = 15;

			reportStatusDto.setProductCode(productCode);
			reportStatusDto.setStatus(status);
			reportStatusDto.setParticipantId(participantId);
			reportStatusDto.setReportType(reportType);
			reportStatusDto.setCycleNumber(cycleNumber);

			String[] myStr1 = cycleDate.split("-");
			String year = myStr1[2];
			year = year.substring(2);

			String date = myStr1[0] + myStr1[1] + year;
			reportStatusDto.setCycleDate(date);

			reportStatusDto1.setProductCode(productCode);
			reportStatusDto1.setStatus(status);
			reportStatusDto1.setParticipantId(participantId);
			reportStatusDto1.setReportType(reportType);
			reportStatusDto1.setCycleDate(cycleDate);
			reportStatusDto1.setCycleNumber(cycleNumber);
			List<ReportStatusDTO> statusData = reportService.getReportStatusData(reportStatusDto);

			statusData = reportService.udpatestatus(statusData, configureTime);

			model.addAttribute(STATUS_DATA, statusData);
			List<String> prodCdList = reportService.getProductId();
			List<String> reportType1 = reportService.getSettlementFileType();
			List<CodeValueDTO> status1 = lookupService.getLookupData(CommonConstants.REPORTSTATUS);

			model.addAttribute(PROC_CODE, prodCdList);
			model.addAttribute(REPORT_TYPE, reportType1);
			model.addAttribute(STATUS, status1);
			model.addAttribute(FLAG, CommonConstants.YES);
			model.addAttribute(CYCLE_NUMBERS, cycleNumber);

			model.addAttribute(BaseCommonConstants.PARTICIPANT_ID_LISTS, userService.getParticipantList());

			model.addAttribute(CommonConstants.REPORT_STATUS_DTO, reportStatusDto1);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_REPORT_STATUS, ex);
		}
		return getView(model, SHOW_REPORT_STATUS);

	}

	@PostMapping("/getCycleNum")
	public ResponseEntity<Object> getCycleNum(@RequestParam("productCode") String productCode, Model model) {

		List<String> settlementCycleNum = new ArrayList<>();

		if (StringUtils.isNotBlank(productCode)) {
			settlementCycleNum = reportService.getSettlementCycleListBasedOnProdCode(productCode);
			model.addAttribute(CommonConstants.SETTLEMENT_CYCLE_LIST_NO, settlementCycleNum);
		}

		return new ResponseEntity<>(settlementCycleNum, HttpStatus.OK);

	}
}