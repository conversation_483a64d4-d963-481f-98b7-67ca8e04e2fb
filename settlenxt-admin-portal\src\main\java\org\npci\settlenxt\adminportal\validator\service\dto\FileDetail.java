package org.npci.settlenxt.adminportal.validator.service.dto;

import java.util.Date;

import org.npci.settlenxt.adminportal.common.mapping.FileTypeNameMapping;
import org.npci.settlenxt.adminportal.common.mapping.NameMappingContext;
import org.npci.settlenxt.adminportal.common.util.DateUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FileDetail {
	
	private String id;
	private String clearingCycleInd;
	private String participantId;
	private String julianDate;
	private String fileSequence;
	private String fileName;
	private String fileExtension;
	private String description;
	
	FileTypeNameMapping mapping;
	
	public FileDetail() {
		super();
		mapping = CustomMappingUtils.getFileTypeNameMapping(NameMappingContext.FILETYPES, id);
	}
	

	
	public String getClearingCycleInd() {
		return clearingCycleInd;
	}
	public void setClearingCycleInd(String clearingCycleInd) {
		this.clearingCycleInd = clearingCycleInd;
	}
	public String getParticipantId() {
		return participantId;
	}
	public void setParticipantId(String participantId) {
		this.participantId = participantId;
	}
	public String getJulianDate() {
		return julianDate;
	}
	public void setJulianDate(String julianDate) {
		this.julianDate = julianDate;
	}
	public String getFileSequence() {
		return fileSequence;
	}
	public void setFileSequence(String fileSequence) {
		this.fileSequence = fileSequence;
	}
	public String getFileExtension() {
		return fileExtension;
	}
	public void setFileExtension(String fileExtension) {
		this.fileExtension = fileExtension;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	
	public boolean isValidFile(){
		log.info("Start - validateFileName()");
		
		boolean isValidFile = false;
		mapping = CustomMappingUtils.getFileTypeNameMapping(NameMappingContext.FILETYPES, id);
		if(mapping != null){
			isValidFile = isValidFileType() && isValidFileDate();
		}
		
		log.info("End - validateFileName()");
		return isValidFile;
	}
	
	public boolean isValidFileType(){
		return true;
	}
	
	public boolean isValidFileDate(){
		boolean isValidFileDate = false;

		Date fileDate = DateUtils.convertJulianDate(this.julianDate, "YYDDD");
		Date currentDate = new Date();
		
		if(currentDate.compareTo(fileDate) > 0)
			{
			isValidFileDate = true;
			}

		return isValidFileDate;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}
	
}
