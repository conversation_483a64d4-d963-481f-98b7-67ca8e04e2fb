package org.npci.settlenxt.adminportal.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.eclipse.birt.core.exception.BirtException;
import org.npci.settlenxt.portal.common.dto.NewsAlertsDTO;
import org.npci.settlenxt.portal.common.dto.NewsDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.ui.Model;

public interface NewsAlertsService {

	Long getRowCount();

	Long getRowCountApproval();

	Long getSavedRowCount();

	Long getDeleteRowCount();

	int fetchNewsIdSeq();

	int fetchReferenceNoSeq();

	void addDefaultListData(Model model);

	NewsDTO addNewsInfo(NewsAlertsDTO newsAlertsDTO) throws SettleNxtException;

	int updateNewsInfo(NewsAlertsDTO newsAlertsDTO) throws SettleNxtException;

	int updateSchedulerInfo(NewsAlertsDTO newsAlertsDTO) throws SettleNxtException;

	Boolean checkNewsInfoSaved(String title);

	NewsDTO getNewsAlerts(String refNumber);

	void approveNewsAlerts(NewsDTO newsDTO, Model model) throws SettleNxtException;

	 NewsDTO createAndUpdateFinalNewsAlerts(NewsDTO newsDTO) throws SettleNxtException;

	Map<Integer, Object> getNewsAlerts(SearchCriteriaDTO searchCriteriaDTO);

	List<NewsAlertsDTO> getFinalNewsAlertsList(SearchCriteriaDTO searchCriteriaDTO);

	List<NewsAlertsDTO> getTempNewsAlertsList(SearchCriteriaDTO searchCriteriaDTO);

	List<NewsAlertsDTO> getSavedNewsAlertsList(SearchCriteriaDTO searchCriteriaDTO);

	List<NewsAlertsDTO> getDeletedNewsAlertsList(SearchCriteriaDTO searchCriteriaDTO);

	 NewsAlertsDTO discardNewsAndAlerts(String referenceNumber) throws SettleNxtException;

	int addDistributionDetails(NewsAlertsDTO newsAlertsDTO) throws SettleNxtException;

	 void deleteNewsAlert(NewsDTO newsDto) throws SettleNxtException;

	 NewsAlertsDTO newsAlertBirtReportGeneration(String fromDate, String toDate) throws SettleNxtException,BirtException,IOException;

	 boolean fetchCountNewsAlerts(String fromDate, String toDate);

	 List<NewsAlertsDTO> fetchDistributionDetails(int newsId);

	String approveNewsAlertsBulk(String bulkApprovalReferenceNoList, NewsDTO newsDTO, Model model) throws SettleNxtException;

	 long checkIfExistsInStg(int newsId);

}
