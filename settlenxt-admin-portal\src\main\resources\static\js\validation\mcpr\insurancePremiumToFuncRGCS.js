var isFromDateDisable=0;
var isToDateDisable=0;
var insurancePremiumNewRecordLocal ='N'; 

$(document).ready(function() {
	insurancePremiumNewRecordLocal = document.getElementById("insurancePremiumNewRecord").value;
	var insurancePremiumNewRecord = document.getElementById("insurancePremiumNewRecord").value;
	if(insurancePremiumNewRecord=='N')
	{
		$('#vcardType').attr('disabled', true);
		$('#vcardVariant').attr('disabled', true);
	}
	
	    var fromDate = $("#vfromYear").val()+$("#vfromMonth").val();
	    var toDate = $("#vtoYear").val()+$("#vtoMonth").val();
	    var currdate = document.getElementById("currDate").value;
	    
	    if((Number(currdate) >= Number(fromDate)) && insurancePremiumNewRecord=='N' )
	    {
			$('#vfromYear').attr('disabled', true);
			$('#vfromMonth').attr('disabled', true);
			$('#vvendor').attr('disabled', true);
			document.getElementById('vannualPremiumAmtPerCard').readOnly = true;
			isFromDateDisable=1;
    	}
	    if(Number(currdate) >= Number(toDate))
	    {
			$('#vtoYear').attr('disabled', true);
			$('#vtoMonth').attr('disabled', true);
			$('#vvendor').attr('disabled', true);
			document.getElementById('vannualPremiumAmtPerCard').readOnly = true;
			isToDateDisable=1;
    	}
    	if(isFromDateDisable==1 && isToDateDisable==1)
    	{
			$('#submitInsurancePremium').attr('disabled', true);
    	}
    	
    	
	disableSave();
	$("#errMsg").hide();
	
	$("#vcardType").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#vcardVariant").on('keyup keypress blur change', function () {
        unableSave();
    });
	$("#vannualPremiumAmtPerCard").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#vvendor").on('keyup keypress blur change', function () {
        unableSave();
    });	$("#vfromMonth").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#vfromYear").on('keyup keypress blur change', function () {
        unableSave();
    });	$("#vtoMonth").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#vtoYear").on('keyup keypress blur change', function () {
        unableSave();
    });
   });

function disableSave()
{
	if (typeof submitInsurancePremium != "undefined") {
		document.getElementById("submitInsurancePremium").disabled = true;
	}
}

function unableSave()
{
	
    	if(isFromDateDisable==1 && isToDateDisable==1)
    	{
			$('#submitInsurancePremium').attr('disabled', true);
    	}
		else
		{
			if (typeof submitInsurancePremium != "undefined") {
				document.getElementById("submitInsurancePremium").disabled = false;
			}
		}	
}
function disableToValue()
{
if (typeof vNewCardCount2 != "undefined") {
		$('#vNewCardCount2').attr('disabled', true);
		if (document.getElementById("VOperatorIndi").value == "Between") {
		$('#vNewCardCount2').attr('disabled', false);
			}
}
}

function saveInsurancePremium(_userID, _type,originPage ) {
	
	var insurancePremiumNewRecord = document.getElementById("insurancePremiumNewRecord").value;
	
	var isValid = true;

	isValid = handleNewRecord(insurancePremiumNewRecord, isValid);
	    if (!validateField('vannualPremiumAmtPerCard', true, "Decimal", 100, false,0,1000000,false) && isValid) {
		isValid = false;
	    }
	    if (!validateField('vvendor', true, "SelectionBox", 100, false,0,0,false) && isValid) {
		isValid = false;
	    }
	    if (!validateField('vfromMonth', true, "SelectionBox", 100, false,0,0,false) && isValid) {
		isValid = false;
	    }
	    if (!validateField('vfromYear', true, "SelectionBox", 100, false,0,0,false) && isValid) {
			isValid = false;
	    }
	    if (!validateField('vtoMonth', true, "SelectionBox", 100, false,0,0,false) && isValid) {
		isValid = false;
	    }
	    if (!validateField('vtoYear', true, "SelectionBox", 100, false,0,0,false) && isValid) {
		isValid = false;
	    }
	    if(!isValid)
	    {
		return false;
	    }

	var url = '/editInsurancePremiumToFunction';
	
	checkDuplicateData(url,originPage);

	    
	
}



function handleNewRecord(insurancePremiumNewRecord, isValid) {
	if (insurancePremiumNewRecord == 'Y') {
		if (!validateField('vcardType', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
			isValid = false;
		}
		if (!validateField('vcardVariant', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
			isValid = false;
		}
	}
	return isValid;
}

function urlPostAction(_type, action,originPage) {
	var data = "originPage," + originPage ;
	postData(action, data);
}
function homeInsurancePremium(_userID, _type,originPage) {
	var url;
	if(originPage=='mainPage')
	{
	url = '/showMcprInsurancePremiumConfig';
	}
	else
	{
	url = '/insurancePremiumPendingForApproval';
	}
		
	
	var data = "originPage," + originPage ;
	postData(url, data);
}


var optionFunctionalityList = new Array();


function postDiscardAction(action,originPage) {
		var url = action;
		var insurancePremId = document.getElementById("hinsurancePremId").value;
		var data = "insurancePremId," + insurancePremId  + ",originPage," + originPage ;
		postData(url, data);
 }
	

function checkDuplicateData(url,originPage) {

	var cardType=document.getElementById("vcardType").value;
	var cardVariant=document.getElementById("vcardVariant").value;
	var annualPremiumAmtPerCard=document.getElementById("vannualPremiumAmtPerCard").value;
	var vendor=document.getElementById("vvendor").value;
	var fromMonth=document.getElementById("vfromMonth").value;
	var fromYear=document.getElementById("vfromYear").value;
	var toMonth=document.getElementById("vtoMonth").value;
	var toYear=document.getElementById("vtoYear").value;
	var insurancePremId = document.getElementById("hinsurancePremId").value;
	
	var validvRoleName=false;
	var msUrl = "checkDuplicateDataInsurancePremium";

var tokenVal = document.getElementsByName("_TransactToken")[0].value;
		$.ajax({
			url: msUrl,
			type: "POST",
			dataType: "json",
			"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenVal);},
			data: {
				"cardType": cardType,
				"cardVariant": cardVariant,
				"fromMonth": fromMonth,
				"fromYear": fromYear,
				"toMonth": toMonth,
				"toYear": toYear,
				"insurancePremId": insurancePremId
			},
			success: function(response) {
				if (response.status == "BSUC_0001") {
					validvRoleName = true;
					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "";
					 var data = "insurancePremId," + insurancePremId + ",cardType," + cardType + 
					",cardVariant," + cardVariant + ",annualPremiumAmtPerCard," + annualPremiumAmtPerCard + 
					",vendor," + vendor +  ",originPage," + originPage +
					",fromMonth," + fromMonth + ",fromYear," + fromYear + ",toMonth," + toMonth + ",toYear," + toYear ;

					postData(url, data);					
				} else if(response.status == "Duplicate") {
					validvRoleName = false;
 					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "Insurance Premium Configuration Already Exists";
					
				}else {
					validvRoleName = false;
 					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "Discontinuousion in Insurance Premium Configuration. Last Configuration is done for " + response.status + " month.";
					
				}
			},
			error: function(_request, _status, _error) {
				errvErrorInfo.className = 'error';
				errvErrorInfo.innerHTML = "";
			}
		});

	return validvRoleName;
}
function viewInsurancePremium(insurancePremId, type,originPage) {
	var url;
	if (type == 'V')
		url = '/getInsurancePremium';
	else if (type == 'E')
		url = '/getInsurancePremium';
	else if (type == 'P')
		url = '/getPendingInsurancePremium';
		
	var data = "insurancePremId," + insurancePremId + ",viewType," + type + ",originPage," + originPage ;
	
	postData(url, data);
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
	var curdateinfo = document.getElementById("currDate").value;
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType!="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    isValid = validateFieldTypeIntAlpha(fieldType, fieldValue, isValid);
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
  	if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
    
    var fieldValue1;
    var fieldValue2;
      ({ isValid, fieldValue1, fieldValue2 } = validateYear(fieldId, isValid, fieldValue1, fieldValue2, curdateinfo));
     if(fieldId=="vtoYear"  && isValid)
    {
    	
	     fieldValue1 = $("#vfromYear").val()+$("#vfromMonth").val();
	     fieldValue2 = $("#vtoYear").val()+$("#vtoMonth").val();
	    
	    
	    if(Number(fieldValue1) >= Number(fieldValue2))
	    {
        	isValid = false;
    	}
    }
    showErrorMsg(isValid, fieldId);
    return isValid;
}
function validateYear(fieldId, isValid, fieldValue1, fieldValue2, curdateinfo) {
	if (fieldId == "vfromYear" && isValid && isFromDateDisable == 0) {

		fieldValue1 = $("#vfromYear").val() + $("#vfromMonth").val();
		fieldValue2 = curdateinfo;
		if (insurancePremiumNewRecordLocal == 'N') {
			if (Number(fieldValue1) < Number(fieldValue2)) {
				isValid = false;
			}
		}

		else {
			if (parseInt(fieldValue2) - parseInt(fieldValue1) > 2) {
				isValid = false;
			}
		}

	}
	if (fieldId == "vtoYear" && isValid && isToDateDisable == 0) {

		fieldValue1 = $("#vtoYear").val() + $("#vtoMonth").val();
		fieldValue2 = curdateinfo;

		if (Number(fieldValue1) < Number(fieldValue2)) {
			isValid = false;
		}
	}
	return { isValid, fieldValue1, fieldValue2 };
}

function showErrorMsg(isValid, fieldId) {
	if (isValid) {
		$("#err" + fieldId).hide();
	} else {
		if (rebateValidationMessages[fieldId]) {
			$("#err" + fieldId).find('.error').html(rebateValidationMessages[fieldId]);
		}
		$("#err" + fieldId).show();
	}
}

function validateFieldTypeIntAlpha(fieldType, fieldValue, isValid) {
	var regEx;
	if (fieldType == "Alphabet") {
		regEx = /^[a-zA-Z]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphabetWithSpace") {
		regEx = /^[a-zA-Z ]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphanumericNoSpace") {
		regEx = /^[A-Za-z0-9]+$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "Integer") {
		regEx = /^\d*$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "Decimal") {
		regEx = /^\d+\.?\d*$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

