package org.npci.settlenxt.adminportal.validator.service;

import org.npci.settlenxt.adminportal.validator.service.dto.ValidationResult;
import org.npci.settlenxt.portal.common.dto.CashBackFileUploadDTO;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;

/**
 * <AUTHOR>
 *
 */
public interface IValidationService {

	
	/**
	 * Validate provided file
	 * @param fileUploadDTO
	 * @return ValidationResult
	 */
	 ValidationResult validateFile(FileUploadDTO fileUploadDTO);
	
	 String parseAndSplitFile(CashBackFileUploadDTO cashbackFileUploadDTO);
}
