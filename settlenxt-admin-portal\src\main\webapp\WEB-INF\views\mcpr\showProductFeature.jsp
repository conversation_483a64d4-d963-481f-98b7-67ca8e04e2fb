<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript"> 
var actionColumnIndex=6;
</script>

<script src="./static/js/validation/mcpr/showProductFeature.js"
	type="text/javascript"></script>
	
<script type="text/javascript">
var productFeatureValidationMessages={};
productFeatureValidationMessages['quater']="<spring:message code='productFeature.quater.validation.msg' javaScriptEscape='true' />";
productFeatureValidationMessages['year']="<spring:message code='productFeature.year.validation.msg' javaScriptEscape='true' />";
productFeatureValidationMessages['binNo']="<spring:message code='productFeature.binNo.validation.msg' javaScriptEscape='true' />";
productFeatureValidationMessages['bankName']="<spring:message code='productFeature.bankName.validation.msg' javaScriptEscape='true' />";

</script>
<div class="row">
<div role="alert" style="display: none" id="jqueryError">
<div id="errorStatus" class="alert alert-danger" role="alert">${errorStatus}</div>
</div>
<div role="alert" style="display: none" id="jquerySuccess">
<div id="successStatus" class="alert alert-success" role="alert">${successStatus}</div>
</div>
</div>
<script type="text/javascript" src=	"./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src=	"./static/js/jszip.min.js">
</script>
<script type="text/javascript" src=	"./static/js/buttons.html5.min.js">
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
	 
	.defaultexport {
  visibility: hidden;
}

table.dataTable thead  { vertical-align: top;}
table.dataTable thead .sorting { vertical-align: top; background: url('./static/images/sort_both.png') no-repeat center right; }
table.dataTable thead .sorting_asc { vertical-align: top;background: url('./static/images/sort_asc.png') no-repeat center right; }
table.dataTable thead .sorting_desc { vertical-align: top;background: url('./static/images/sort_desc.png') no-repeat center right; }
table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before{ vertical-align: top;content:""}
table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after{ vertical-align: top;content:""}
.search-box  {	
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;	
	background-color: transparent;
    width: 100%;
    border-width:1px;
	border-style:inset;
    }
</style>

<div class="space_block">

<div class="row">
<div class="col-sm-12">
<div class="panel panel-default"> 
	<div class="panel-heading">
	<strong><span class="glyphicon glyphicon-th"></span> 
		<span data-i18n="Data"><spring:message code="productFeature.productFeatureFeeViewScreen" /></span></strong>
	</div>
	<div class="tab-content">
		<div>
			<input id="showControl" type="hidden"/>
			<input id="hquater" type="hidden" value="${quater}"/>
			<input id="hyear" type="hidden" value="${year}"/>
			<input id="hbinNo" type="hidden" value="${binNo}"/>
			<input id="hbankName" type="hidden" value="${bankName}"/>
			<input id="htype" type="hidden" value="${type}"/>
		</div>
		
		<div role="tabpanel" class="tab-pane active" id="home">
			<table class="table table-striped" style="font-size: 12px">
			<caption style="display:none;">Product Feature Fee</caption>
  		   <thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
					<td style="width:20%;"></td>
						<td style="width:5%;"> 								
							<label><spring:message code="productFeature.quater" /><span style="color: red">*</span></label></td>
						<td style="width:20%;"><form:select path="quater" id="quater" name="quater"
							maxlength="10"  value="${quater}"
							cssClass="form-control medantory" onChange="showAllButton()">
								<form:option value="SELECT" label="SELECT" />
								<form:options items="${monthList}"  itemValue="code" itemLabel="description"/>
							</form:select>
							<div id="errquater">
						<span for="quater" class="error">
						<form:errors path="quater" /> </span>
					</div>

					</td>
					<td style="width:5%;"></td>
					<td style="width:5%;"> 								
						<label><spring:message code="productFeature.year" /><span style="color: red">*</span></label></td>
					<td style="width:20%;"><form:select path="year" id="year" name="year"
						maxlength="10" value="${year}" 
						cssClass="form-control medantory" onChange="showAllButton()">
						<form:option value="SELECT" label="SELECT" />
							<form:options items="${yearList}"  itemValue="description" itemLabel="description"/>
							</form:select>
						<div id="erryear" class="error">
							<span for="year" class="error">
							<form:errors path="year" /></span>
						</div>
						</td>
						<td style="width:20%;"></td>
						</tr>
					</tbody>
				</table>
			
		<div class="col-sm-12 bottom_space">
			<hr />
				<div style="text-align:center">
						<button type="button" class="btn btn-success" id="viewBinProductFee"
						onclick="showControl('vbpf');"><spring:message code="productFeature.binProduct" /></button>
						<button type="button" class="btn btn-success" id="viewBankProductFee"
						onclick="showControl('vbkpf');"><spring:message code="productFeature.bankProduct" /></button>					
						<button type="button" class="btn btn-success" id="viewFeatureFee"
						onclick="showControl('vff');"><spring:message code="productFeature.binBankFeature" /></button>
				</div>
		</div>
		
		<table class="table table-striped" style="font-size: 12px">
		<caption style="display:none;">Product Feature Fee</caption>
  		<thead style="display:none;"><th scope="col"></th></thead>
			<tbody>
				<tr>
					<td style="width:20%;"></td>
					<td style="width:10%;">
						<label id="labelBinNo" ><spring:message code="productFeature.binNo" /><span style="color: red">*</span></label></td>
					<td style="width:20%;"><form:input path="binNo" id="binNo" name="binNo" 
							cssClass="form-control medantory" value="${binNo}" />
						<div id="errbinNo">
						<span for="binNo" class="error">
						<form:errors path="binNo" /> </span>
						</div>
					</td>
					<td style="width:5%;"></td>
					<td style="width:10%;">
						<label id="labelbankName" ><spring:message code="productFeature.bankName" /><span style="color: red">*</span></label></td>
					<td style="width:20%;"><form:select path="bankName" id="bankName" name="bankName"
							maxlength="10" value="${bankName}" 
							cssClass="form-control medantory">
							<form:option value="SELECT" label="SELECT" />
							<form:options items="${bankNameList}"  itemValue="code" itemLabel="description"/>
							</form:select>
						<div id="errbankName" class="error">
							<span for="bankName" class="error">
							<form:errors path="bankName" /></span>
						</div>
					</td>
					<td style="width:20%;"></td>
					</tr>	
			</tbody>
			</table>
			
			<div class="col-sm-12 bottom_space">
			<hr />
				<div style="text-align:center">
					<button type="button" class="btn btn-success"
					onclick="checkAlreadyPresent();"><spring:message code="productFeature.view" /></button>
					<%-- onclick="viewData('/productFeatureFeeViewSearch');"><spring:message code="productFeature.view" /></button> --%>
					
					<button type="button" value="Submit" class="btn btn-success" 
					onclick="resetAction();"><spring:message code="productFeature.reset" /></button>
					
				</div>
			</div>
			
			
				<div class="col-sm-12" id ='hideTable'>	
					<div class="panel panel-default">
				
					<c:if test="${View eq 'Yes'}">
						<div class="panel-heading">
							<strong><span class="glyphicon glyphicon-th"></span> 
							<span data-i18n="Data"> <spring:message code="productFeature.productFeatureFeeList" /> </span>
							</strong>
						</div>
								<div class="panel-body">
									<div class="row">
										<div class="col-sm-12">
											<button class="btn  pull-right btn_align" id="clearFilters">
												<spring:message code="productFeature.clearFiltersBtn" />
											</button>
											&nbsp; <a class="btn btn-success pull-right btn_align" href="#" id="csvExport">
											<spring:message code="productFeature.csvBtn" /> </a>
											<a class="btn btn-success pull-right btn_align" href="#" id="excelExport">
											<spring:message code="productFeature.exportBtn" /></a>
											
										</div>
									</div>

										<c:if test="${type eq 'vbpf' or type eq 'vbkpf'}">
											<div class="table-responsive">
												<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
													<caption style="display:none;">Product Feature Fee</caption>
													<thead>
														<tr>
															<th scope="col"><label><spring:message code="productFeature.binNo" /></label></th>
															<th scope="col"><label><spring:message code="productFeature.productFee" /></label></th>
															<th scope="col"><label><spring:message code="productFeature.featureFee" /></label></th>
															<th scope="col"><label><spring:message code="productFeature.totalFee" /></label></th>
														</tr>
													</thead>
													<tbody>
														<c:if test="${not empty productFeatureDataList}">
															<c:forEach var="base" items="${productFeatureDataList}">
																<tr>
																	<td>${base.binNo}</td>
																	<td>${base.productFee}</td>
																	<td>${base.featureFee}</td>
																	<td>${base.totalFee}</td>

																</tr>
															</c:forEach>
														</c:if>
													</tbody>
												</table>
											</div>
										</c:if>
										<c:if test="${type eq 'vff'}">
											<div class="table-responsive">
												<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
												<caption style="display:none;">Product Feature Fee</caption>
												<thead>
														<tr>
															<th scope="col"><label><spring:message code="productFeature.featureName" /></label></th>
															<th scope="col"><label><spring:message code="productFeature.details" /></label></th>
															<th scope="col"><label><spring:message code="productFeature.fee" /></label></th>
															<th scope="col"><label><spring:message code="productFeature.totalFee" /></label></th>
														</tr>
													</thead>
													<tbody>
														<c:if test="${not empty productFeatureDataList}">
															<c:forEach var="feature"
																items="${productFeatureDataList}">
																<tr>
																	<td>${feature.featureName}</td>
																	<td>${feature.details}</td>
																	<td>${feature.fee}</td>
																	<td>${feature.totalFee}</td>
																</tr>
															</c:forEach>
														</c:if>
													</tbody>
												</table>
											</div>
										</c:if>
									</div>
								</c:if>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	</div>
</div>
