package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.CurrencyMasterDTO;
import org.springframework.stereotype.Service;

@Service
public interface CurrencyMasterService {

	 List<CurrencyMasterDTO> getCurrencyMasterList();

	 List<CurrencyMasterDTO> getPendingCurrencyMaster();

	 CurrencyMasterDTO getCurrencyMasterMainInfo(int currencyId);

	 CurrencyMasterDTO getCurrencyMasterStgInfo(String currencyId);

	 CurrencyMasterDTO addEditCurrencyMaster(CurrencyMasterDTO currencyMasterDto);

	 CurrencyMasterDTO getCurrencyMasterForEdit(int currencyId);

	 CurrencyMasterDTO approveOrRejectCurrencyMaster(int currencyId, String status, String remarks);

	 CurrencyMasterDTO discardCurrencyMaster(int currencyId);

	 CurrencyMasterDTO getCurrencyMasterStg(int currencyId);

	 int checkDuplicateData(CurrencyMasterDTO currencyMasterDto);

	 String approveOrRejectCurrencyMasterBulk(String bulkApprovalReferenceNoList, String status, String remarks);

}
