<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.ReasonCodeMasterRepository">

	<select id="getReasonCodeList"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">

		SELECT reason_code_master.reason_code as
		reasonCode,reason_code_master.reason_code_desc as
		reasonCodeDesc,reason_code_master.status as status,
		reason_code_master.reason_code_subtype as reasonCodeSubType,
		reason_code_master.reason_code_subtype_desc as reasonCodeSubTypeDesc,reason_code_master.reason_type as reasonType from
		reason_code_master join reason_code_master_stg
		on reason_code_master.reason_code = reason_code_master_stg.reason_code
		and reason_code_master_stg.request_state='A';

	</select>
	
	<select id="getReasonCodewithReasonType"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">

		SELECT reason_code_master.reason_code as
		reasonCode,reason_code_master.reason_code_desc as
		reasonCodeDesc,reason_code_master.status as status,
		reason_code_master.reason_code_subtype as reasonCodeSubType,
		reason_code_master.reason_code_subtype_desc as reasonCodeSubTypeDesc,reason_code_master.reason_type as reasonType from
		reason_code_master join reason_code_master_stg
		on reason_code_master.reason_code = reason_code_master_stg.reason_code
		and reason_code_master_stg.request_state='A' and reason_code_master.reason_type = 'rejected';

	</select>
	
	<select id="getReasonCodeListstg"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">

		SELECT reason_code as
		reasonCode,reason_code_desc as
		reasonCodeDesc,status as status,
		reason_code_subtype as reasonCodeSubType,
		reason_code_subtype_desc as reasonCodeSubTypeDesc,reason_type as reasonType from
		reason_code_master_stg;
		

	</select>

	<select id="getApprovedReasonCode"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">

		SELECT reason_code as reasonCode,reason_code_desc as reasonCodeDesc,status
		as status,reason_code_subtype as reasonCodeSubType,reason_type as reasonType,
		reason_code_subtype_desc as reasonCodeSubTypeDesc from reason_code_master where reason_code=#{reasonCode};

	</select>

	<insert id="addReasonCode">
		INSERT INTO REASON_CODE_MASTER_STG (REASON_CODE,REASON_TYPE,
		REASON_CODE_DESC,STATUS,reason_code_subtype,reason_code_subtype_desc,CREATED_BY,CREATED_ON,LAST_UPDATED_BY,LAST_UPDATED_ON,request_state,last_operation,checker_comments)VALUES(#{reasonCode},
		#{reasonType},#{reasonCodeDesc},
		#{status},#{reasonCodeSubType},#{reasonCodeSubTypeDesc},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},
		#{requestState}, #{lastOperation},#{checkerComments})
	</insert>

	<select id="getReasonCode"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		SELECT reason_code as reasonCode,reason_code_desc as reasonCodeDesc,status
		as status,last_updated_by as lastUpdatedBy,last_updated_on as
		lastUpdatedOn,created_by as createdBy,
		created_on as
		createdOn,checker_comments as checkerComments,last_operation as
		lastOperation,request_state as requestState,reason_code_subtype as reasonCodeSubType,
		reason_code_subtype_desc as reasonCodeSubTypeDesc,reason_type as reasonType from
		REASON_CODE_MASTER_STG WHERE REASON_CODE = #{reasonCode};
	</select>

	<update id="updateReasonCodeDetail">
		UPDATE REASON_CODE_MASTER_STG SET REASON_CODE_DESC =
		#{reasonCodeDesc},STATUS =#{status},LAST_UPDATED_BY =
		#{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},REQUEST_STATE
		=#{requestState} ,LAST_OPERATION=#{lastOperation},
		reason_code_subtype = #{reasonCodeSubType},
		reason_code_subtype_desc = #{reasonCodeSubTypeDesc},reason_type=#{reasonType},
		checker_comments = '' where REASON_CODE = #{reasonCode}
	</update>

	<select id="getPendingReasonCodeList"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		SELECT reason_code as reasonCode,reason_code_desc as
		reasonCodeDesc,status as status,last_updated_by as
		lastUpdatedBy,last_updated_on as lastUpdatedOn,created_by as
		createdBy,reason_code_subtype as reasonCodeSubType,
		reason_code_subtype_desc as reasonCodeSubTypeDesc,
		created_on as createdOn,checker_comments as
		checkerComments,last_operation as lastOperation,request_state as
		requestState,reason_type as reasonType FROM REASON_CODE_MASTER_STG WHERE REQUEST_STATE IN ('P',
		'R')
	</select>

	<insert id="saveReasonCode">
		INSERT INTO REASON_CODE_MASTER(REASON_CODE,REASON_CODE_DESC,STATUS,REASON_CODE_SUBTYPE,REASON_CODE_SUBTYPE_DESC,REASON_TYPE) values(
		#{reasonCode},#{reasonCodeDesc},#{status},#{reasonCodeSubType},#{reasonCodeSubTypeDesc},#{reasonType})
	</insert>

	<update id="updateReasonCode">
		UPDATE REASON_CODE_MASTER SET STATUS
		=#{status},REASON_CODE=#{reasonCode},REASON_CODE_DESC=#{reasonCodeDesc},reason_code_subtype = #{reasonCodeSubType},
		reason_code_subtype_desc = #{reasonCodeSubTypeDesc},reason_type=#{reasonType}
		where reason_code = #{reasonCode};
	</update>

	<update id="updateRequestStateReasonCodeStg">
		UPDATE REASON_CODE_MASTER_STG SET REQUEST_STATE
		=#{requestState}, STATUS = #{status} , REASON_CODE_DESC =
		#{reasonCodeDesc},reason_code_subtype = #{reasonCodeSubType},
		reason_code_subtype_desc = #{reasonCodeSubTypeDesc},reason_type=#{reasonType} where reason_code = #{reasonCode};
	</update>

	<update id="updateReasonCodeStgState">
		UPDATE REASON_CODE_MASTER_STG SET STATUS
		=#{status},LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on =
		#{lastUpdatedOn},REQUEST_STATE =#{requestState} ,
		checker_comments =
		#{checkerComments},REASON_CODE=#{reasonCode},REASON_CODE_DESC=#{reasonCodeDesc},reason_code_subtype = #{reasonCodeSubType},
		reason_code_subtype_desc = #{reasonCodeSubTypeDesc},reason_type=#{reasonType}
		where reason_code = #{reasonCode};
	</update>

	<delete id="deleteDiscardedReasonCode">
		DELETE FROM REASON_CODE_MASTER_STG WHERE REASON_CODE
		= #{reasonCode};
	</delete>

	<select id="getFunctionCodeList"
		resultType="org.npci.settlenxt.portal.common.dto.ActionCodeDTO">
		SELECT DISTINCT(function_code) as functionCode,CONCAT(function_code,' -
		',function_code_description) as functionCodeDescription from
		action_code;
	</select>

	<select id="getReasonCodeMasterList"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		SELECT reason_code as reasonCode,CONCAT(reason_code,' - ',reason_code_desc)
		as reasonCodeDesc from reason_code_master;
	</select>


	<select id="showReasonCodeRulesList"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		SELECT distinct rr.reason_code as reasonCode,rr.seq_id as seqId,rr.action_code
		as
		actionCode,rr.status as status,
		rr.field_name as
		fieldName,rr.relational_operator as relationOperator
		,rr.field_value as
		fieldValue,rr.field_operator as
		fieldOperator,rr.logical_reason_code as
		logicalReasonCode,
		rr.created_by as createdBy,rr.created_on as
		createdOn
		from reasoncode_rules rr inner join reasoncode_rules_stg rs
		on rr.seq_id = rs.seq_id
		and
		rs.request_state=#{requestState};

	</select>
	
	<select id="showPendingReasonCodeRules"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		select distinct reason_code as reasonCode, seq_id as seqId,action_code as
		actionCode,request_state as requestState,
		field_name as
		fieldName,relational_operator as relationOperator,field_value as
		fieldValue,field_operator as fieldOperator,logical_reason_code as
		logicalReasonCode,
		created_by as createdBy,created_on as
		createdOn,checker_comments as
		checkerComments from reasoncode_rules_stg
		where request_state in
		<foreach item="item" index="index" collection="requestStateList" open="("
			separator="," close=")">
			 (#{item})
		</foreach>
	</select>

	
	<select id="fetchActionCodeList"
		resultType="org.npci.settlenxt.portal.common.dto.CodeValueDTO">

		select action_code as code,CONCAT (action_code,'-',
		action_code_description) as description from action_code

	</select>

	<select id="fetchReasonCodeList"
		resultType="org.npci.settlenxt.portal.common.dto.CodeValueDTO">

		select reason_code as code,CONCAT (reason_code,'-',
		reason_code_desc) as description from reason_code_master

	</select>

	<insert id="saveReasonCodeRules">
		INSERT INTO reasoncode_rules
		(seq_id,action_code,
		reason_code, relational_operator, field_value,
		field_operator, logical_reason_code, created_on, created_by, status,field_name)
		VALUES(#{reasonCodeDto.seqIdInt},#{reasonCodeDto.actionCode},#{reasonCodeDto.reasonCode},#{reasonCodeDto.relationOperator},#{reasonCodeDto.fieldValue},
		#{reasonCodeDto.fieldOperator},#{reasonCodeDto.logicalReasonCode},#{reasonCodeDto.createdOn},#{reasonCodeDto.createdBy},#{reasonCodeDto.status},#{reasonCodeDto.fieldName})
	</insert>
	<insert id="saveReasonCodeRulesStg">
		INSERT INTO reasoncode_rules_stg
		(action_code,
		reason_code, field_name, relational_operator, field_value,
		field_operator, logical_reason_code, status, created_by, created_on,
		request_state, last_operation,
		checker_comments)
		VALUES(#{actionCode},#{reasonCode},#{fieldName},#{relationOperator},#{fieldValue},
		#{fieldOperator},#{logicalReasonCode},#{status},#{createdBy},#{createdOn},
		#{requestState}, #{lastOperation},#{checkerComments})
	</insert>

	<update id="updateReasonCodeRulesStg">
		UPDATE reasoncode_rules_stg SET relational_operator=#{relationOperator}, field_value =
		#{fieldValue},STATUS =#{status},LAST_UPDATED_BY =
		#{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},REQUEST_STATE
		=#{requestState} ,LAST_OPERATION=#{lastOperation},
		checker_comments
		=#{checkerComments},field_name=#{fieldName} where
		seq_id = #{seqIdInt}
	</update>

	<select id="fetchReasonCodeRulesStg"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		select distinct reason_code as reasonCode,  seq_id as seqId,action_code as
		actionCode,request_state as requestState,
		relational_operator as relationOperator,field_value as
		fieldValue,field_operator as fieldOperator,logical_reason_code as
		logicalReasonCode,status as status,
		created_by as createdBy,created_on
		as
		createdOn,checker_comments as
		checkerComments,field_name
		as fieldName from
		reasoncode_rules_stg
		where seq_id = #{seqId}
	</select>
	
	<select id="fetchReasonCodeRulesStgBySeqId"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		select distinct reason_code as reasonCode,  seq_id as seqId,action_code as
		actionCode,request_state as requestState,
		relational_operator as relationOperator,field_value as
		fieldValue,field_operator as fieldOperator,logical_reason_code as
		logicalReasonCode,status as status,
		created_by as createdBy,created_on
		as
		createdOn,checker_comments as
		checkerComments, field_name
		as fieldName from
		reasoncode_rules_stg
		where seq_id=#{seqId}
	</select>
	
	
	<select id="fetchReasonCodeRulesStgList"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		select distinct reason_code as reasonCode,  seq_id as seqId,action_code as
		actionCode,request_state as requestState,
		field_name
		as fieldName,relational_operator as relationOperator,field_value as
		fieldValue,field_operator as fieldOperator,logical_reason_code as
		logicalReasonCode,status as status,
		created_by as createdBy,created_on
		as
		createdOn,checker_comments as
		checkerComments from
		reasoncode_rules_stg
		where REASON_CODE = #{reasonCode} and
		action_code=#{actionCode}
		
	</select>

	
	
		<select id="fetchReasonCodeRulesMain"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		select distinct r.reason_code as reasonCode, r.seq_id as seqId,r.action_code as
		actionCode,r.status as status,
		r.relational_operator as relationOperator,r.field_value as
		fieldValue,r.field_operator as fieldOperator,r.logical_reason_code as
		logicalReasonCode,
		r.created_by as createdBy,r.created_on as
		createdOn,rs.request_state as requestState,r.field_name as
		fieldName from
		reasoncode_rules r join reasoncode_rules_stg rs on rs.seq_id=r.seq_id
		where r.seq_id=#{seqId}
	</select>

	<delete id="deleteDiscardedReasonCodeRules">
		DELETE FROM reasoncode_rules_stg where seq_id=#{seqId}
	</delete>

	<update id="updateReasonCodeRulesState">
		UPDATE reasoncode_rules_stg SET LAST_UPDATED_BY =
		#{lastUpdatedBy},last_operation =#{lastOperation},
		LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE =
		#{requestState},
		CHECKER_COMMENTS=#{checkerComments}
		where seq_id =
		#{seqIdInt}
		
	</update>

	<update id="updateReasonCodeRulesMain">
		UPDATE reasoncode_rules SET field_value =
		#{reasonCodeDto.fieldValue},STATUS =#{reasonCodeDto.status},LAST_UPDATED_BY =
		#{reasonCodeDto.lastUpdatedBy},last_updated_on = #{reasonCodeDto.lastUpdatedOn},
		logical_reason_code=#{reasonCodeDto.logicalReasonCode} where
		seq_id =
		#{reasonCodeDto.seqIdInt}
	</update>
<select id="fetchCountReasonCodeRulesStg" resultType="int">
		SELECT count(*) from reasoncode_rules_stg  where REASON_CODE =
		#{reasonCode} and action_code=#{actionCode}
		and
		field_name=#{fieldName}
		and relational_operator=#{relationOperator}
	</select>
	
	<select id="fetchCountReasonCodeRules" resultType="int">
		SELECT count(*) from reasoncode_rules  where REASON_CODE =
		#{reasonCode} and action_code=#{actionCode}
		and
		field_name=#{fieldName}
		and relational_operator=#{relationOperator}
	</select>
	
	<insert id="addReasonCodeRulesStg">
		INSERT INTO reasoncode_rules_stg
		(action_code,
		reason_code, field_name, relational_operator, field_value,
		field_operator, logical_reason_code, status, created_by, created_on,
		request_state, last_operation)
		VALUES(#{actionCode},#{reasonCode},#{fieldName},#{relationOperator},#{fieldValue},
		#{fieldOperator},#{logicalReasonCode},#{status},#{createdBy},#{createdOn},
		#{requestState}, #{lastOperation})
	</insert>
	
	
	
	<select id="fetchPendingApprovalRcRules"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		select distinct reason_code as reasonCode,seq_id as seqId,action_code as
		actionCode,request_state as requestState,
		field_name as
		fieldName,relational_operator as relationOperator,field_value as
		fieldValue,field_operator as fieldOperator,logical_reason_code as
		logicalReasonCode,
		created_by as createdBy,created_on as
		createdOn,checker_comments as
		checkerComments from reasoncode_rules_stg
		where seq_id in
		<foreach item="item" index="index" collection="idList" open="("
			separator="," close=")">
			 (#{item})
		</foreach>
	</select>
	
		
	<select id="fetchPendingApprovalRcRulesList"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		select distinct reason_code as reasonCode,seq_id as seqId,action_code as
		actionCode,request_state as requestState,
		field_name as
		fieldName,relational_operator as relationOperator,field_value as
		fieldValue,field_operator as fieldOperator,logical_reason_code as
		logicalReasonCode,
		created_by as createdBy,created_on as
		createdOn,checker_comments as
		checkerComments from reasoncode_rules_stg
		where action_code||reason_code
		 in
		<foreach item="item" index="index" collection="idList" open="("
			separator="," close=")">
			 (#{item})
		</foreach>
	</select>
	
	
		<select id="getReasonCodeRulesId"
		resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
		select reason_code as reasonCode from reasoncode_rules_stg where logical_reason_code=#{logToState} 
		and action_code=#{actionCode}
		and request_state in
		<foreach item="item" index="index" collection="requestStateList" open="("
			separator="," close=")">
			 (#{item})
		</foreach> 
		order by reason_code desc limit 1
	</select>
	<!-- 
	and field_name=#{fieldName} and relational_operator=#{relationOperator}
	 -->
	
	<select id="getAll" resultType="org.npci.settlenxt.portal.common.dto.ReasonCodeDto">
			select reason_code as reasonCode,reason_code_desc as
			reasonCodeDesc,reason_code_subtype as reasonCodeSubType,
			reason_code_subtype_desc as reasonCodeSubTypeDesc,status from
			reason_code_master where status='A';
	</select>
</mapper>