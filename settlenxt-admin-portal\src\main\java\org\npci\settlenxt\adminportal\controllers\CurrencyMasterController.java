package org.npci.settlenxt.adminportal.controllers;

import java.util.List;
import java.util.Locale;

import javax.servlet.http.HttpServletRequest;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.CurrencyMasterDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.CurrencyMasterService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

//@Slf4j
@Controller
public class CurrencyMasterController extends BaseController {

	private static final String SHOW_CURRENCY_MASTER = "showCurrencyMaster";
	private static final String VIEW_CURRENCY_MASTER = "viewCurrencyMaster";
	private static final String VIEW_APPROVE_CURRENCY_MASTER = "viewApproveCurrencyMaster";
	private static final String ADD_EDIT_CURRENCY_MASTER = "addEditCurrencyMaster";
	private static final String SHOW_CHECK_BOX = "showCheckBox";
	// @SuppressWarnings({ "unchecked", "unused" })
	@Autowired
	private MessageSource messageSource;
	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	private CurrencyMasterService currencyMasterService;

	// show Main tab of Currency Master
	@PostMapping("/currencyMasterMain")
	@PreAuthorize("hasAuthority('View Currency Master')")
	public String fetchApprovedCurrencyMasterList(Model model) {
		try {
			List<CurrencyMasterDTO> currencyMasterList = currencyMasterService.getCurrencyMasterList();
			model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.TRANSACT_YES);//
			model.addAttribute(CommonConstants.CURRENCY_MASTER_LIST, currencyMasterList);
			model.addAttribute(CommonConstants.CURRENCY_MASTER, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.ADD_CURRENCY_MASTER, CommonConstants.TRANSACT_YES);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, SHOW_CURRENCY_MASTER, ex);
		}
		return getView(model, SHOW_CURRENCY_MASTER);
	}

	// show Approval tab
	@PostMapping("/currencyMasterForApproval")
	@PreAuthorize("hasAuthority('View Currency Master')")
	public String currencyMasterForApproval(Model model) {
		try {
			List<CurrencyMasterDTO> pendingCurrencyMasterList = currencyMasterService.getPendingCurrencyMaster();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.CURRENCY_MASTER_PENDING_LIST, pendingCurrencyMasterList);
			model.addAttribute(CommonConstants.CURRENCY_MASTER_APP_PENDING, CommonConstants.TRANSACT_YES);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_CURRENCY_MASTER, ex);
		}
		return getView(model, SHOW_CURRENCY_MASTER);
	}

	// view in Main tab
	@PostMapping("/getCurrencyMaster")
	@PreAuthorize("hasAuthority('View Currency Master')")
	public String getCurrencyMaster(@RequestParam("currencyId") String currencyId, Model model) {
		CurrencyMasterDTO currencyMasterDto = new CurrencyMasterDTO();
		try {
			currencyMasterDto = currencyMasterService.getCurrencyMasterMainInfo(Integer.valueOf(currencyId));
			model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, currencyMasterDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_CURRENCY_MASTER, ex);
		}
		return getView(model, VIEW_CURRENCY_MASTER);
	}

	// view in Approval tab
	@PostMapping("/getPendingCurrencyMaster")
	@PreAuthorize("hasAuthority('View Currency Master')")
	public String getPendingCurrencyMaster(@RequestParam("currencyId") String currencyId, Model model,
			HttpServletRequest request) {
		CurrencyMasterDTO currencyMasterDto = new CurrencyMasterDTO();
		try {
			currencyMasterDto = currencyMasterService.getCurrencyMasterStgInfo(currencyId);
			model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, currencyMasterDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_CURRENCY_MASTER, ex);
		}
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.DISCARD_CURRENCY_MASTER);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_CURRENCY_MASTER);
		return getView(model, VIEW_APPROVE_CURRENCY_MASTER);
	}

	// add
	@PostMapping("/currencyMasterCreation")
	@PreAuthorize("hasAuthority('Add Currency Master')")
	public String currencyMasterCreation(Model model) {
		CurrencyMasterDTO currencyMasterDto = new CurrencyMasterDTO();
		model.addAttribute(CommonConstants.ADD_CURRENCY_MASTER, CommonConstants.ADD_CURRENCY_MASTER);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_CURRENCY_MASTER);
		model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, currencyMasterDto);
		return getView(model, ADD_EDIT_CURRENCY_MASTER);
	}

	// To save given input
	@PostMapping("/addCurrencyMaster")
	@PreAuthorize("hasAuthority('Add Currency Master')")
	public String addCurrencyMaster(
			@ModelAttribute(CommonConstants.CURRENCY_MASTER_DTO) CurrencyMasterDTO currencyMasterDto, Model model) {
		currencyMasterDto.setAddEditFlag(CommonConstants.ADD_CURRENCY_MASTER);
		try {
			currencyMasterDto = currencyMasterService.addEditCurrencyMaster(currencyMasterDto);
			model.addAttribute(CommonConstants.ADD_CURRENCY_MASTER, CommonConstants.ADD_CURRENCY_MASTER);
		} catch (Exception ex) {
			model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, currencyMasterDto);
			model.addAttribute(CommonConstants.ADD_CURRENCY_MASTER, CommonConstants.ADD_CURRENCY_MASTER);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_CURRENCY_MASTER);
			return handleErrorCodeAndForward(model, ADD_EDIT_CURRENCY_MASTER, ex);
		}
		model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, currencyMasterDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("currencyMaster.addSuccess.msg"));
		return getView(model, ADD_EDIT_CURRENCY_MASTER);
	}

	// To Edit TipSurcharge
	@PostMapping("/editCurrencyMaster")
	@PreAuthorize("hasAuthority('Edit Currency Master')")
	public String editCurrencyMaster(@RequestParam("currencyId") String currencyId, Model model,
			@RequestParam("parentPage") String parentPage) {
		CurrencyMasterDTO currencyMasterDto = new CurrencyMasterDTO();

		try {
			currencyMasterDto = currencyMasterService.getCurrencyMasterForEdit(Integer.valueOf(currencyId));
			currencyMasterDto.setAddEditFlag(CommonConstants.EDIT_CURRENCY_MASTER);
			model.addAttribute(CommonConstants.EDIT_CURRENCY_MASTER, CommonConstants.EDIT_CURRENCY_MASTER);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_CURRENCY_MASTER);
			model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, currencyMasterDto);
			model.addAttribute(CommonConstants.PARENT_PAGE, parentPage);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_CURRENCY_MASTER, ex);
		}

		model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, currencyMasterDto);
		return getView(model, ADD_EDIT_CURRENCY_MASTER);
	}

	// To save edited input
	@PostMapping("/updateCurrencyMaster")
	@PreAuthorize("hasAuthority('Edit Currency Master')")
	public String updateCurrencyMaster(@ModelAttribute("currencyMasterDto") CurrencyMasterDTO currencyMasterDto,
			BindingResult result, Model model, HttpServletRequest request, Locale locale,
			@RequestParam("parentPage") String parentPage) {
		CurrencyMasterDTO mcclocal;
		try {
			currencyMasterDto.setAddEditFlag(CommonConstants.EDIT_CURRENCY_MASTER);
			currencyMasterService.addEditCurrencyMaster(currencyMasterDto);
			mcclocal = currencyMasterService.getCurrencyMasterStg(currencyMasterDto.getCurrencyId());

			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("currencyMaster.updateSuccess.msg", null, locale));
		} catch (Exception ex) {
			mcclocal = currencyMasterDto;
			handleErrorCodeAndForward(model, ADD_EDIT_CURRENCY_MASTER, ex);
		}
		model.addAttribute(CommonConstants.EDIT_CURRENCY_MASTER, CommonConstants.EDIT_CURRENCY_MASTER);
		model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, mcclocal);
		model.addAttribute(CommonConstants.PARENT_PAGE, parentPage);

		return getView(model, ADD_EDIT_CURRENCY_MASTER);
	}

	// For Checker Approval
	@PostMapping("/approveCurrencyMaster")
	@PreAuthorize("hasAuthority('Approve Currency Master')")
	public String approveCurrencyMaster(@RequestParam("currencyId") String currencyId,
			@RequestParam("status") String status, @RequestParam("remarks") String remarks, Model model) {
		CurrencyMasterDTO currencyMasterDto = new CurrencyMasterDTO();
		try {
			currencyMasterDto = currencyMasterService.approveOrRejectCurrencyMaster(Integer.valueOf(currencyId), status,
					remarks);
			model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, currencyMasterDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_CURRENCY_MASTER, ex);
		}
		model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, currencyMasterDto);
		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {

			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("currencyMaster.approvalSuccess.msg"));
		} else if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("currencyMaster.rejectionSuccess.msg"));
		}

		return getView(model, VIEW_APPROVE_CURRENCY_MASTER);
	}

	// For Checker Approval
	@PostMapping("/approveCurrencyMasterBulk")
	@PreAuthorize("hasAuthority('Approve Currency Master')")
	public String approveCurrencyMasterBulk(
			@RequestParam("bulkApprovalReferenceNoList") String bulkApprovalReferenceNoList,
			@RequestParam("status") String status, Model model) {
		String successStatus = "";
		try {
			String remarks = "";
			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}
			successStatus = currencyMasterService.approveOrRejectCurrencyMasterBulk(bulkApprovalReferenceNoList, status,
					remarks);
			List<CurrencyMasterDTO> pendingCurrencyMasterList = currencyMasterService.getPendingCurrencyMaster();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.CURRENCY_MASTER_PENDING_LIST, pendingCurrencyMasterList);
			model.addAttribute(CommonConstants.CURRENCY_MASTER_APP_PENDING, CommonConstants.TRANSACT_YES);
			if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_APPROVED)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("currencyMaster.approvalSuccess.msg"));
			} else if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_REJECTED)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("currencyMaster.rejectionSuccess.msg"));
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_CURRENCY_MASTER, ex);
		}
		return getView(model, SHOW_CURRENCY_MASTER);
	}

	// For Discard
	@PostMapping("/discardCurrencyMaster")
	@PreAuthorize("hasAuthority('Edit Currency Master')")
	public String discardCurrencyMaster(@RequestParam("currencyId") String currencyId, Model model) {
		CurrencyMasterDTO currencyMasterDto = new CurrencyMasterDTO();
		try {
			currencyMasterDto = currencyMasterService.discardCurrencyMaster(Integer.valueOf(currencyId));
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_CURRENCY_MASTER, ex);
		}
		model.addAttribute(CommonConstants.CURRENCY_MASTER_DTO, currencyMasterDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("currencyMaster.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_CURRENCY_MASTER);
	}

}
