package org.npci.settlenxt.adminportal.validator.service.dto;

import java.util.ArrayList;
import java.util.List;

public class TransactionRecord {
	private Integer recordNumber;
	private List<Record> records;
	private int totalRecordsCount;
	
	private List<String> conditionalFields;
	private List<String> optionalFields;
	private List<String> requiredFields;
	
	public TransactionRecord() {
		super();
	}


	public List<Record> getRecords() {
		return records;
	}

	public Integer getRecordNumber() {
		return recordNumber;
	}

	public void setRecordNumber(Integer recordNumber) {
		this.recordNumber = recordNumber;
	}

	public void setRecords(List<Record> records) {
		this.records = records;
	}

	public List<String> getRowDataAsString() {
		List<String>rowDataAsString = new ArrayList<>();
		StringBuilder allRecord = null;
		Record recod = null;
		if(records != null){
			allRecord = new StringBuilder();
			for(int i=0; i<records.size(); i++){
				recod = records.get(i);
				allRecord.append(recod.getName()+"="+recod.getValue());
				if(i < (records.size()-1))
					{
					allRecord.append(",");
					}
			}
			rowDataAsString.add(allRecord.toString());
		}
		
		return rowDataAsString;
	}
	
	public List<String> getRowDataAsXMLString() {
		List<String> rowDataAsXMLString = new ArrayList<>();
		StringBuilder allRecod = null;
		Record recod = null;
		if(records != null){
			allRecod = new StringBuilder();
			for(Record recordData:records){
				recod = recordData;
				allRecod.append("<" + recod.getName() + ">" + recod.getValue() + "</" + recod.getName() + ">");
			}
			rowDataAsXMLString.add(allRecod.toString());
		}
		
		return rowDataAsXMLString;
	}

	

	public int getTotalRecordsCount() {
		return totalRecordsCount;
	}

	public void setTotalRecordsCount(int totalRecordsCount) {
		this.totalRecordsCount = totalRecordsCount;
	}

	public List<String> getConditionalFields() {
		return conditionalFields;
	}

	public void setConditionalFields(List<String> conditionalFields) {
		this.conditionalFields = conditionalFields;
	}

	public List<String> getOptionalFields() {
		return optionalFields;
	}

	public void setOptionalFields(List<String> optionalFields) {
		this.optionalFields = optionalFields;
	}

	public List<String> getRequiredFields() {
		return requiredFields;
	}

	public void setRequiredFields(List<String> requiredFields) {
		this.requiredFields = requiredFields;
	}

	public List<String> getValidFields() {
		List<String> validFields = new ArrayList<>();
		
		if (requiredFields != null) {
			validFields.addAll(requiredFields);
		}
		
		if (conditionalFields != null) {
			validFields.addAll(conditionalFields);
		}
		
		if (optionalFields != null) {
			validFields.addAll(optionalFields);
		}
		
		return validFields;
	}
}
