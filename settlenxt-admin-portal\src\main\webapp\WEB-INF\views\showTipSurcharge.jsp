<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript">
	var actionColumnIndex = 10;
	var firstColumnToBeSkippedInFilterAndSort = false;
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 14;
	firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 13;
	firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
</script>

<script>
	var referenceNoListPendings = [];
	<c:if test="${not empty pendingTipSurchargeList}">
	<c:forEach items="${pendingTipSurchargeList}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	referenceNoListPendings.push('${operator.tipSurchargeId}');
	</c:if>
	</c:forEach>
	</c:if>
</script>

<script src="./static/js/validation/showTipSurcharge.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="./static/js/jszip.min.js"></script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js"></script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: bottom;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>
<!-- Model -->
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalNews" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Tip Surcharge
					Approval/Rejection</label>
				<p id="newsIds" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulkTipSurcharge('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulkTipSurcharge('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Model -->

<input:hidden id="refNum" />
<div class="row">


	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/tipSurchargeMain');" role="tab"
			data-toggle="tab"><span class="glyphicon glyphicon-credit-card">
		</span> <spring:message code="tipSurcharge.mainTab.title" /></a>
		<c:choose>
			<c:when test="${pendingAppTipSurcharge eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" role="tab" onclick="getPendingTipSurchargeList();"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> </span> <spring:message
				code="tipSurcharge.approvalTab.title" /></a>
	</ul>


	<div class="tab-content">
		<div role="tabpanel" class="tab-pane active" id="home">
			<c:if test="${showMainTab eq 'Yes'}">

				<div class="row">
					<div class="col-sm-12">
						<sec:authorize access="hasAuthority('Add Tip Surcharge')">
							<c:if test="${addTipSurcharge eq 'Yes'}">
								<a class="btn btn-success pull-right btn_align" href="#"
									onclick="submitForm('/tipSurchargeCreation','P');"
									style="margin: -5px 0px 2px 0px;"><em
									class="glyphicon-plus"></em> <spring:message
										code="tipSurcharge.addTipSurchargeBtn" /></a>
							</c:if>
						</sec:authorize>
						<div class="row">
									<div class="col-sm-12">
										<button class="btn  pull-right btn_align" id="clearFilters">
											<spring:message code="tipSurcharge.clearBtn" />
										</button>
										&nbsp; <a class="btn btn-success pull-right btn_align"
											href="#" id="csvExport"> <spring:message
												code="tipSurcharge.csvBtn" />
										</a> <a class="btn btn-success pull-right btn_align" href="#"
											id="excelExport"><spring:message
												code="tipSurcharge.exportBtn" /> </a>
									</div>
								</div>
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="tipSurcharge.listscreen.title" /></span></strong>
							</div>
							<div class="panel-body">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Tip Surcharge</caption>
										<thead>
											<tr>
												<th scope="col"><label><spring:message
															code="tipSurcharge.tipSurchargeId" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.tipSurchargeName" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.tipSurchargeType" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.operator" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.settlementAmount" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.capFlat" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.capPercentage" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.binCardBrand" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.binCardType" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.amountPercentFlag" /></label></th>
											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty tipSurchargeList}">
												<c:forEach var="tipSurcharge" items="${tipSurchargeList}">
													<tr>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharge.tipSurchargeId}','V')">${tipSurcharge.tipSurchargeId}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharge.tipSurchargeId}','V')">${tipSurcharge.tipSurchargeName}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharge.tipSurchargeId}','V')">${tipSurcharge.tipSurType}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharge.tipSurchargeId}','V')">${tipSurcharge.operatorName}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharge.tipSurchargeId}','V')">${tipSurcharge.settlementAmount}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharge.tipSurchargeId}','V')">${tipSurcharge.amount}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharge.tipSurchargeId}','V')">${tipSurcharge.percentage}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharge.tipSurchargeId}','V')">${tipSurcharge.cardBrand}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharge.tipSurchargeId}','V')">${tipSurcharge.cardType}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharge.tipSurchargeId}','V')">${tipSurcharge.amtFlag}</td>
													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>
			<c:if test="${showApprovalTab eq 'Yes'}">
				<div class="row">
				<div class="col-sm-12">
										<button class="btn  pull-right btn_align" id="clearFilters">
											<spring:message code="tipSurcharge.clearBtn" />
										</button>
										&nbsp; <a class="btn btn-success pull-right btn_align"
											href="#" id="csvExport"> <spring:message
												code="tipSurcharge.csvBtn" />
										</a> <a class="btn btn-success pull-right btn_align" href="#"
											id="excelExport"><spring:message
												code="tipSurcharge.exportBtn" /> </a>

									</div>
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="tipSurcharge.listscreen.title" /></span></strong>
								<c:if test="${not empty pendingTipSurchargeList}">
									<sec:authorize access="hasAuthority('Approve Tip Surcharge')">
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkTipSurcharge('A','No')"
											id="submitButtonA"
											value="<spring:message code="feeRate.Approve" />" />
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkTipSurcharge('R','No')"
											id="submitButtonR"
											value="<spring:message code="feeRate.Reject" />" />
									</sec:authorize>
								</c:if>
							</div>
							<div class="panel-body">
								
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Tip Surcharge</caption>
										<thead>
											<tr>
												<sec:authorize
													access="hasAuthority('Approve Tip Surcharge')">
													<th scope="col"><input type=checkbox name='selectAllCheck'
														id="selectAll" data-target="toggleModalNews" value="All"></input></th>
												</sec:authorize>
												<th scope="col"><label><spring:message
															code="tipSurcharge.tipSurchargeId" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.tipSurchargeName" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.tipSurchargeType" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.operator" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.settlementAmount" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.capFlat" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.capPercentage" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.binCardBrand" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.binCardType" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.amountPercentFlag" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.requestType" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.status" /></label></th>
												<th scope="col"><label><spring:message
															code="tipSurcharge.checkerComents" /></label></th>
											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty pendingTipSurchargeList}">

												<c:forEach var="tipSurcharges"
													items="${pendingTipSurchargeList}">
													<tr>
														<sec:authorize
															access="hasAuthority('Approve Tip Surcharge')">
															<c:if test="${tipSurcharges.requestState =='P' }">
																<td><input type=checkbox name='type'
																	id="selectSingle" onclick="mySelect();"
																	value="${tipSurcharges.tipSurchargeId}"></input></td>
															</c:if>
															<c:if test="${tipSurcharges.requestState !='P' }">
																<td></td>
															</c:if>
														</sec:authorize>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.tipSurchargeId}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.tipSurchargeName}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.tipSurType}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.operatorName}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.settlementAmount}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.amount}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.percentage}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.cardBrand}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.cardType}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.amtFlag}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.lastOperation}</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">
															<c:if test="${tipSurcharges.requestState =='A' }">
																<spring:message
																	code="tipSurcharge.requestState.approved.description" />
															</c:if> <c:if test="${tipSurcharges.requestState =='P' }">
																<spring:message
																	code="tipSurcharge.requestState.pendingApproval.description" />
															</c:if> <c:if test="${tipSurcharges.requestState =='R' }">
																<spring:message
																	code="tipSurcharge.requestState.rejected.description" />
															</c:if> <c:if test="${tipSurcharges.requestState =='D' }">
																<spring:message
																	code="tipSurcharge.requestState.discared.description" />
															</c:if>
														</td>
														<td
															onclick="javascript:viewTipSurcharge('${tipSurcharges.tipSurchargeId}','P')">${tipSurcharges.checkerComments}</td>
													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>
		</div>
	</div>
</div>
