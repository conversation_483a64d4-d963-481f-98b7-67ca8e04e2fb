$(document).ready(
		function() {
			$("form :input").change(function() {
				$(this).closest('form').data('changed', true);
			});
			
			 $('#totalCumulativeCards').attr('disabled', true);
               $('#totalIncrementalCards').attr('disabled', true);
               
			 $("#errbinNumber").hide();
			 $("#errphyContactCardCummRuPayCard").hide();
			 $("#errphyContactCardIncrementalCard").hide();
			 $("#errphyContactlessCummRuPayCard").hide();
			 $("#errphyContactlessIncrementalCard").hide();
			 $("#errvirtualCardCummRuPayCard").hide();
			 $("#errvirtualCardIncrementalCard").hide();
			 $("#errecommTxnOnusCount").hide();
			 $("#errecommTxnOnusAmt").hide();
			 $("#errposConcactCardPresentDomTxnOnusCount").hide();
			 $("#errposConcactCardPresentDomTxnOnusAmt").hide();
			 $("#errposContactlessOnlRetailsDomTxnOnusCount").hide();
			 $("#errposContactlessOnlRetailsDomTxnOnusAmt").hide();
			 $("#errposContactlessOnlTransitDomTxnOnusCount").hide();
			 $("#errposContactlessOnlTransitDomTxnOnusAmt").hide();
			 $("#errposContactlessOffRetailsDomTxnOnusCount").hide();
			 $("#errposContactlessOffRetailsDomTxnOnusAmt").hide();
			 $("#errposContactlessOffTransitDomTxnOnusCount").hide();
			 $("#errposContactlessOffTransitDomTxnOnusAmt").hide();
			 $("#erratmCardPresentDomTxnOnusCount").hide();
			 $("#erratmCardPresentDomTxnOnusAmt").hide();
			 
			
			 $('#phyContactCardCummRuPayCard').blur(function () {
			        validateField ('phyContactCardCummRuPayCard', true, "Number",0,false,0,99999999999999,true);
			        sumValue();
			    });
			 $('#phyContactlessCummRuPayCard').blur(function () {
			        validateField ('phyContactlessCummRuPayCard', true, "Number",0,false,0,99999999999999,true);
			        sumValue();
			    });
			 $('#virtualCardCummRuPayCard').blur(function () {
			        validateField ('virtualCardCummRuPayCard', true, "Number",0,false,0,99999999999999,true);
			        sumValue();
			    });
			 $('#phyContactCardIncrementalCard').blur(function () {
			        validateField ('phyContactCardIncrementalCard', true, "Number",0,false,0,99999999999999,true);
			        sumValue();
			    });
			 $('#phyContactlessIncrementalCard').blur(function () {
			        validateField ('phyContactlessIncrementalCard', true, "Number",0,false,0,99999999999999,true);
			        sumValue();
			    });
			 $('#virtualCardIncrementalCard').blur(function () {
			        validateField ('virtualCardIncrementalCard', true, "Number",0,false,0,99999999999999,true);
			        sumValue();
			    });
			    
		
			 disableSave();
				
			 $("#phyContactCardCummRuPayCard").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('phyContactCardCummRuPayCard', true, "Number",0,false,0,99999999999999,true);
			    });
				$("#phyContactCardIncrementalCard").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('phyContactCardIncrementalCard', true, "Number",0,false,0,99999999999999,true);
			    });
			 	$("#phyContactlessCummRuPayCard").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('phyContactlessCummRuPayCard', true, "Number",0,false,0,99999999999999,true);
			    });	
			 	$("#phyContactlessIncrementalCard").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('phyContactlessIncrementalCard', true, "Number",0,false,0,99999999999999,true);
			    });	
			 	$("#virtualCardCummRuPayCard").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('virtualCardCummRuPayCard', true, "Number",0,false,0,99999999999999,true);
			    });
			 	$("#virtualCardIncrementalCard").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('virtualCardIncrementalCard', true, "Number",0,false,0,99999999999999,true);
			    });	
			 	$("#ecommTxnOnusCount").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('ecommTxnOnusCount', true, "Number",0,false,0,99999999999999,false);
			    });

				$("#ecommTxnOnusAmt").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('ecommTxnOnusAmt', true, "Decimal", 0, false,0.0,99999999999999.99,false)
			    });
			 	$("#posConcactCardPresentDomTxnOnusCount").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('posConcactCardPresentDomTxnOnusCount', true, "Number",0,false,0,99999999999999,true);
			    });
				$("#posConcactCardPresentDomTxnOnusAmt").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('posConcactCardPresentDomTxnOnusAmt', true, "Decimal", 0, false,0.0,99999999999999.99,true);
			    });
			 	$("#posContactlessOnlRetailsDomTxnOnusCount").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('posContactlessOnlRetailsDomTxnOnusCount', true, "Number",0,false,0,99999999999999,true);
			    });	
			 	$("#posContactlessOnlRetailsDomTxnOnusAmt").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('posContactlessOnlRetailsDomTxnOnusAmt', true, "Decimal", 0, false,0.0,99999999999999.99,true); 
			    });
			 	$("#posContactlessOnlTransitDomTxnOnusCount").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('posContactlessOnlTransitDomTxnOnusCount', true, "Number",0,false,0,99999999999999,true); 
			    });	
			 	$("#posContactlessOnlTransitDomTxnOnusAmt").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('posContactlessOnlTransitDomTxnOnusAmt', true, "Decimal", 0, false,0.0,99999999999999.99,true);
			    });
			 	$("#posContactlessOffRetailsDomTxnOnusCount").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('posContactlessOffRetailsDomTxnOnusCount', true, "Number",0,false,0,	99999999999999,true);
			    });	
			 	$("#posContactlessOffRetailsDomTxnOnusAmt").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('posContactlessOffRetailsDomTxnOnusAmt', true, "Decimal", 0, false,0.0,99999999999999.99,true);
			    });
				$("#posContactlessOffTransitDomTxnOnusCount").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('posContactlessOffTransitDomTxnOnusCount', true, "Number",0,false,0,99999999999999,true);
			    });	
				$("#posContactlessOffTransitDomTxnOnusAmt").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('posContactlessOffTransitDomTxnOnusAmt', true, "Decimal", 0, false,0.0,99999999999999.99,true);
			    });
			 	$("#atmCardPresentDomTxnOnusCount").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('atmCardPresentDomTxnOnusCount', true, "Number",0,false,0,99999999999999,true);
			    });
			 	$("#atmCardPresentDomTxnOnusAmt").on('keyup keypress blur change', function () {
			        unableSave();
			        validateField('atmCardPresentDomTxnOnusAmt', true, "Decimal", 0, false,0.0,99999999999999.99,true);
			    }); 
			 
		});

function disableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = true;
}
}

function unableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = false;
}
}


window.history.forward();
function noBack() {
	window.history.forward();
}

function sumValue(){
	var cumm = parseInt(parseInt($("#phyContactCardCummRuPayCard").val()) + parseInt($("#phyContactlessCummRuPayCard").val()) + parseInt($("#virtualCardCummRuPayCard").val()));
	var incre = parseInt(parseInt($("#phyContactCardIncrementalCard").val()) + parseInt($("#phyContactlessIncrementalCard").val()) + parseInt($("#virtualCardIncrementalCard").val()));
	document.getElementById("totalCumulativeCards").value=cumm;
	document.getElementById("totalIncrementalCards").value=incre;
	
}



function viewMcprBinDetailsEdit(url, type) {
	
	var isValid = true;

    isValid = handleFieldTypePhyContactCard(isValid);
    if (!validateField('virtualCardCummRuPayCard', true, "Number",0,false,0,99999999999999,true) && isValid) {
        isValid = false;
    }
    if (!validateField('virtualCardIncrementalCard', true, "Number",0,false,0,99999999999999,true) && isValid) {
        isValid = false;
    }
    if (!validateField('ecommTxnOnusCount', true, "Number",0,false,0,99999999999999,true) && isValid) {
        isValid = false;
    }
    if (!validateField('ecommTxnOnusAmt', true, "Decimal", 0, false,0.0,99999999999999.99,true) && isValid) {
        isValid = false;
    }
    isValid = handlePostConcatCard(isValid);
    isValid = validatePosCardAndAtmCardField(isValid);
    

	
if (isValid ) {
	
	var cumm = parseInt(parseInt($("#phyContactCardCummRuPayCard").val()) + parseInt($("#phyContactlessCummRuPayCard").val()) + parseInt($("#virtualCardCummRuPayCard").val()));
	var incre = parseInt(parseInt($("#phyContactCardIncrementalCard").val()) + parseInt($("#phyContactlessIncrementalCard").val()) + parseInt($("#virtualCardIncrementalCard").val()));
	
	var data;
	if(type=='E'){
		 data = "mcprBinDataDetailsId," + $("#mcprBinDataDetailsId").val() + ",binNumber," +  $("#binNumber").val()
		+ ",participantId," + $("#participantId").val() + ",submittedDate," +  $("#submittedDate").val()
		+ ",monthEnding," + $("#monthEnding").val() + ",phyContactCardCummRuPayCard," +  $("#phyContactCardCummRuPayCard").val()
		+ ",phyContactCardIncrementalCard," + $("#phyContactCardIncrementalCard").val() + ",phyContactlessCummRuPayCard," +  $("#phyContactlessCummRuPayCard").val()
		+ ",phyContactlessIncrementalCard," + $("#phyContactlessIncrementalCard").val() + ",virtualCardCummRuPayCard," +  $("#virtualCardCummRuPayCard").val()
		+ ",virtualCardIncrementalCard," + $("#virtualCardIncrementalCard").val() + ",ecommTxnOnusCount," +  $("#ecommTxnOnusCount").val()
		+ ",ecommTxnOnusAmt," + $("#ecommTxnOnusAmt").val() + ",posConcactCardPresentDomTxnOnusCount," +  $("#posConcactCardPresentDomTxnOnusCount").val()
		+ ",posConcactCardPresentDomTxnOnusAmt," + $("#posConcactCardPresentDomTxnOnusAmt").val() + ",posContactlessOnlRetailsDomTxnOnusCount," +  $("#posContactlessOnlRetailsDomTxnOnusCount").val()
		+ ",posContactlessOnlRetailsDomTxnOnusAmt," + $("#posContactlessOnlRetailsDomTxnOnusAmt").val() + ",posContactlessOnlTransitDomTxnOnusCount," +  $("#posContactlessOnlTransitDomTxnOnusCount").val()
		+ ",posContactlessOnlTransitDomTxnOnusAmt," + $("#posContactlessOnlTransitDomTxnOnusAmt").val() + ",posContactlessOffRetailsDomTxnOnusCount," +  $("#posContactlessOffRetailsDomTxnOnusCount").val()
		+ ",posContactlessOffRetailsDomTxnOnusAmt," + $("#posContactlessOffRetailsDomTxnOnusAmt").val() + ",posContactlessOffTransitDomTxnOnusCount," +  $("#posContactlessOffTransitDomTxnOnusCount").val()
		+ ",posContactlessOffTransitDomTxnOnusAmt," + $("#posContactlessOffTransitDomTxnOnusAmt").val() + ",atmCardPresentDomTxnOnusCount," +  $("#atmCardPresentDomTxnOnusCount").val()
		+ ",atmCardPresentDomTxnOnusAmt," + $("#atmCardPresentDomTxnOnusAmt").val() + ",totalCumulativeCards," +  cumm
		+ ",totalIncrementalCards," + incre ;
	}
		
	else if(type=='A'){
		
		
		 data = "binNumber," +  $("#binNumber").val()
		+ ",participantId," + $("#participantId").val() + ",submittedDate," +  $("#submittedDate").val()
		+ ",monthEnding," + $("#monthEnding").val() + ",phyContactCardCummRuPayCard," +  $("#phyContactCardCummRuPayCard").val()
		+ ",phyContactCardIncrementalCard," + $("#phyContactCardIncrementalCard").val() + ",phyContactlessCummRuPayCard," +  $("#phyContactlessCummRuPayCard").val()
		+ ",phyContactlessIncrementalCard," + $("#phyContactlessIncrementalCard").val() + ",virtualCardCummRuPayCard," +  $("#virtualCardCummRuPayCard").val()
		+ ",virtualCardIncrementalCard," + $("#virtualCardIncrementalCard").val() + ",ecommTxnOnusCount," +  $("#ecommTxnOnusCount").val()
		+ ",ecommTxnOnusAmt," + $("#ecommTxnOnusAmt").val() + ",posConcactCardPresentDomTxnOnusCount," +  $("#posConcactCardPresentDomTxnOnusCount").val()
		+ ",posConcactCardPresentDomTxnOnusAmt," + $("#posConcactCardPresentDomTxnOnusAmt").val() + ",posContactlessOnlRetailsDomTxnOnusCount," +  $("#posContactlessOnlRetailsDomTxnOnusCount").val()
		+ ",posContactlessOnlRetailsDomTxnOnusAmt," + $("#posContactlessOnlRetailsDomTxnOnusAmt").val() + ",posContactlessOnlTransitDomTxnOnusCount," +  $("#posContactlessOnlTransitDomTxnOnusCount").val()
		+ ",posContactlessOnlTransitDomTxnOnusAmt," + $("#posContactlessOnlTransitDomTxnOnusAmt").val() + ",posContactlessOffRetailsDomTxnOnusCount," +  $("#posContactlessOffRetailsDomTxnOnusCount").val()
		+ ",posContactlessOffRetailsDomTxnOnusAmt," + $("#posContactlessOffRetailsDomTxnOnusAmt").val() + ",posContactlessOffTransitDomTxnOnusCount," +  $("#posContactlessOffTransitDomTxnOnusCount").val()
		+ ",posContactlessOffTransitDomTxnOnusAmt," + $("#posContactlessOffTransitDomTxnOnusAmt").val() + ",atmCardPresentDomTxnOnusCount," +  $("#atmCardPresentDomTxnOnusCount").val()
		+ ",atmCardPresentDomTxnOnusAmt," + $("#atmCardPresentDomTxnOnusAmt").val() + ",totalCumulativeCards," +  cumm
		+ ",totalIncrementalCards," + incre ;
	}
	if(type=='F'){
		 data = "mcprBinDataDetailsId," + $("#mcprBinDataDetailsId").val() + ",binNumber," +  $("#binNumber").val()
		+ ",participantId," + $("#participantId").val() + ",submittedDate," +  $("#submittedDate").val()
		+ ",monthEnding," + $("#monthEnding").val() + ",phyContactCardCummRuPayCard," +  $("#phyContactCardCummRuPayCard").val()
		+ ",phyContactCardIncrementalCard," + $("#phyContactCardIncrementalCard").val() + ",phyContactlessCummRuPayCard," +  $("#phyContactlessCummRuPayCard").val()
		+ ",phyContactlessIncrementalCard," + $("#phyContactlessIncrementalCard").val() + ",virtualCardCummRuPayCard," +  $("#virtualCardCummRuPayCard").val()
		+ ",virtualCardIncrementalCard," + $("#virtualCardIncrementalCard").val() + ",ecommTxnOnusCount," +  $("#ecommTxnOnusCount").val()
		+ ",ecommTxnOnusAmt," + $("#ecommTxnOnusAmt").val() + ",posConcactCardPresentDomTxnOnusCount," +  $("#posConcactCardPresentDomTxnOnusCount").val()
		+ ",posConcactCardPresentDomTxnOnusAmt," + $("#posConcactCardPresentDomTxnOnusAmt").val() + ",posContactlessOnlRetailsDomTxnOnusCount," +  $("#posContactlessOnlRetailsDomTxnOnusCount").val()
		+ ",posContactlessOnlRetailsDomTxnOnusAmt," + $("#posContactlessOnlRetailsDomTxnOnusAmt").val() + ",posContactlessOnlTransitDomTxnOnusCount," +  $("#posContactlessOnlTransitDomTxnOnusCount").val()
		+ ",posContactlessOnlTransitDomTxnOnusAmt," + $("#posContactlessOnlTransitDomTxnOnusAmt").val() + ",posContactlessOffRetailsDomTxnOnusCount," +  $("#posContactlessOffRetailsDomTxnOnusCount").val()
		+ ",posContactlessOffRetailsDomTxnOnusAmt," + $("#posContactlessOffRetailsDomTxnOnusAmt").val() + ",posContactlessOffTransitDomTxnOnusCount," +  $("#posContactlessOffTransitDomTxnOnusCount").val()
		+ ",posContactlessOffTransitDomTxnOnusAmt," + $("#posContactlessOffTransitDomTxnOnusAmt").val() + ",atmCardPresentDomTxnOnusCount," +  $("#atmCardPresentDomTxnOnusCount").val()
		+ ",atmCardPresentDomTxnOnusAmt," + $("#atmCardPresentDomTxnOnusAmt").val() + ",totalCumulativeCards," +  cumm
		+ ",totalIncrementalCards," + incre ;
	}
	
	
	postData(url,data)

}
}




function validatePosCardAndAtmCardField(isValid) {
	if (!validateField('posContactlessOffRetailsDomTxnOnusAmt', true, "Decimal", 0, false, 0.0, 99999999999999.99, true) && isValid) {
		isValid = false;
	}
	if (!validateField('posContactlessOffTransitDomTxnOnusCount', true, "Number", 0, false, 0, 99999999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('posContactlessOffTransitDomTxnOnusAmt', true, "Decimal", 0, false, 0.0, 99999999999999.99, true) && isValid) {
		isValid = false;
	}
	if (!validateField('atmCardPresentDomTxnOnusCount', true, "Number", 0, false, 0, 99999999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('atmCardPresentDomTxnOnusAmt', true, "Decimal", 0, false, 0.0, 99999999999999.99, true) && isValid) {
		isValid = false;
	}
	return isValid;
}

function handleFieldTypePhyContactCard(isValid) {
	if (!validateField('binNumber', true, "Number",0,false,1,99999999999,true) && isValid) {
        isValid = false;
    }
	if (!validateField('phyContactCardCummRuPayCard', true, "Number", 0, false, 0, 99999999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('phyContactCardIncrementalCard', true, "Number", 0, false, 0, 99999999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('phyContactlessCummRuPayCard', true, "Number", 0, false, 0, 99999999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('phyContactlessIncrementalCard', true, "Number", 0, false, 0, 99999999999999, true) && isValid) {
		isValid = false;
	}
	return isValid;
}

function handlePostConcatCard(isValid) {
	if (!validateField('posConcactCardPresentDomTxnOnusCount', true, "Number", 0, false, 0, 99999999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('posConcactCardPresentDomTxnOnusAmt', true, "Decimal", 0, false, 0.0, 99999999999999.99, true) && isValid) {
		isValid = false;
	}
	if (!validateField('posContactlessOnlRetailsDomTxnOnusCount', true, "Number", 0, false, 0, 99999999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('posContactlessOnlRetailsDomTxnOnusAmt', true, "Decimal", 0, false, 0.0, 99999999999999.99, true) && isValid) {
		isValid = false;
	}
	if (!validateField('posContactlessOnlTransitDomTxnOnusCount', true, "Number", 0, false, 0, 99999999999999, true) && isValid) {
		isValid = false;
	}
	if (!validateField('posContactlessOnlTransitDomTxnOnusAmt', true, "Decimal", 0, false, 0.0, 99999999999999.99, true) && isValid) {
		isValid = false;
	}
	if (!validateField('posContactlessOffRetailsDomTxnOnusCount', true, "Number", 0, false, 0, 99999999999999, true) && isValid) {
		isValid = false;
	}
	return isValid;
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType!="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    isValid = validateFieldTypeIntAndAlpha(fieldType, fieldValue, isValid);
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
      if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
      

    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
        if(mcprValidationMessages[fieldId]){
            $("#err" + fieldId).find('.error').html(mcprValidationMessages[fieldId]);
        }
        $("#err" + fieldId).show();
    }
    return isValid;
}



function validateFieldTypeIntAndAlpha(fieldType, fieldValue, isValid) {
	var regEx;
	if (fieldType == "Alphabet") {
		regEx = /^[A-Z]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphabetWithSpace") {
		regEx = /^[A-Z ]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphanumericNoSpace") {
		regEx = /^[A-Za-z0-9]+$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "Integer") {
		regEx = /^\d*$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "Decimal") {
		regEx = /^\d+\.?\d*$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

function userAction(_type, action) {
	var data =  "status," + status;
	postData(action, data);
}

function postDiscardAction(action,_id) {
	var url = action;
	var mcprId = $("#mcprBinDataDetailsId").val();
	var data = "mcprId," + mcprId ;
	postData(url, data);
}

function checkAlreadyPresent()
{
	var binNo = $("#binNumber").val();
	var monthEnd = $("#monthEnding").val();
					
	var validvRoleName=false;
	var msUrl = "checkAlreadyPresent";

	var tokenVal = document.getElementsByName("_TransactToken")[0].value;
		$.ajax({
			url: msUrl,
			type: "POST",
			dataType: "json",
			"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenVal);},
			
			data: {
				"binNumber": binNo,
				"monthEnding": monthEnd,
				_TransactToken: tokenVal
			},
			success: function(response) {
				if (response.status == "BSUC_0001") {
					validvRoleName = true;
					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "";
										
				} else {
					validvRoleName = false;
					
					var url="/getMcprBinDetailsByBinAndMonth"
					var data = "binNumber," + binNo +",monthEnding," + monthEnd ;
					postData(url, data);
				}
			},
			error: function(_request, _status, _error) {
				errvErrorInfo.className = 'error';
				errvErrorInfo.innerHTML = "";
			}
		});

	return validvRoleName;

	
}