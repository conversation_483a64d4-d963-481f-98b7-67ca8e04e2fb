package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.TipSurchargeDTO;
import org.springframework.stereotype.Service;

@Service
public interface TipSurchargeService {

	 List<TipSurchargeDTO> getTipSurchargeList();

	 List<TipSurchargeDTO> getPendingTipSurcharge();

	 TipSurchargeDTO getTipSurchargeMainInfo(int tipSurchargeId);

	 TipSurchargeDTO getTipSurchargeStgInfo(String tipSurchargeId);

	 TipSurchargeDTO addEditTipSurcharge(TipSurchargeDTO tipSurchargeDto);

	 TipSurchargeDTO getTipSurchargeForEdit(int tipSurchargeId);

	 TipSurchargeDTO approveOrRejectTipSurcharge(int tipSurchargeId, String status, String remarks);

	 TipSurchargeDTO discardTipSurcharge(int tipSurchargeId);

	 TipSurchargeDTO getTipSurchargeStg(int tipSurchargeId);

	 String approveOrRejectTipSurchargeBulk(String bulkApprovalReferenceNoList, String status, String remarks);
	
	 List<TipSurchargeDTO> checkDuplicateDataForTipSurcharge(TipSurchargeDTO tipSurchargeDto);

}
