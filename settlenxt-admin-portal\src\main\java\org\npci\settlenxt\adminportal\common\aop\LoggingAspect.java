package org.npci.settlenxt.adminportal.common.aop;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Aspect
@Component

public class LoggingAspect {
	@Around("execution(* org.npci.settlenxt.adminportal.controllers..*(..)))")
	public Object logControllerMethodCalls(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
		return logMethodInfo(proceedingJoinPoint);
	}

	@Around("execution(* org.npci.settlenxt.adminportal.service..*(..)))")
	public Object logCServiceMethodCalls(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
		return logMethodInfo(proceedingJoinPoint);
	}

	@Around("execution(* org.npci.settlenxt.adminportal.repository..*(..)))")
	public Object logSecurityvMethodCalls(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
		return logMethodInfo(proceedingJoinPoint);
	}

	private Object logMethodInfo(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
		MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();

		// Get intercepted method details
		String className = methodSignature.getDeclaringType().getSimpleName();
		String methodName = methodSignature.getName();
		log.debug("Entering {}.{}", className, methodName);

		final StopWatch stopWatchObj = new StopWatch();

		// Measure method execution time
		stopWatchObj.start();
		Object result = proceedingJoinPoint.proceed();
		stopWatchObj.stop();

		log.debug("Leaving  {}.{} -  : {} ms ", className, methodName, stopWatchObj.getTotalTimeMillis());
		return result;
	}
}
