<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
	<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<script type="text/javascript"
	src="./static/js/validation/viewLookUp.js"></script>
	

<form:form onsubmit="removeSpace(this); encodeForm(this);" method="POST"
	id="viewApprovalLookUp" modelAttribute="lookUpDTO"
	action="/approveLookUp" autocomplete="off">
	<div class="alert alert-danger appRejMust" role="alert">Please
		Select Approve/Reject action.</div>
    <div class="alert alert-danger remarkMust" role="alert">Please
		Enter Remarks.</div>		
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="lookUp.viewscreen.title" /></span></strong>
			
		</div>

		<div class="panel-body">
			<form:hidden path="lookupId" value="${lookUpDTO.lookupId}" />

			<table class="table table-striped" style="font-size: 12px">
			<caption style="display:none;">View Approval Lookup</caption> 
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
				
				<tr>
						<td colspan="6"><div class="panel-heading-red clearfix">
								<strong><span class="glyphicon glyphicon-info-sign"></span> <span
									data-i18n="Data"><spring:message
											code="ifsc.requestInformation" /></span></strong>
							</div></td>
						<td></td>
					</tr>
					<tr>
						
						<td><label><spring:message code="sm.lbl.lkpType" /><span
								style="color: red"></span></label></td>
						<td>${lookUpDTO.lkpType}</td>

						<td><label><spring:message code="sm.lbl.lkpValue" /><span
								style="color: red"></span></label></td>
						<td>${lookUpDTO.lkpValue}</td>

						<td><label><spring:message code="sm.lbl.lkpDescp" /><span
								style="color: red"></span></label></td>
						<td>${lookUpDTO.lkpDesc}</td>

					</tr>

					<tr>

								<td><label><spring:message code="sm.lbl.lkpStatus" /><span
								style="color: red"></span></label></td>
								<c:choose>
												<c:when test="${lookUpDTO.status=='A' }">
													<td>Active</td>
												</c:when>
												<c:otherwise>
												<td>InActive</td>
												</c:otherwise>
											</c:choose>
						
						
						<td><label><spring:message code="sm.lbl.lkpCreatedBy" /><span
								style="color: red"></span></label></td>
						<td>${lookUpDTO.createdBy}</td>
						<td><label><spring:message
												code="sm.lbl.requestStatus" /></label></td>
									<c:if test="${lookUpDTO.requestState=='A' }">
										<td>Approved</td>
									</c:if>
									<c:if test="${lookUpDTO.requestState=='P' }">
										<td>Pending for Approval</td>
									</c:if>
									<c:if test="${lookUpDTO.requestState=='R' }">
										<td>Rejected</td>
									</c:if>
									<c:if test="${lookUpDTO.requestState=='D' }">
										<td>Discarded</td>
									</c:if>
			


					</tr>
					
					<tr>
					<td><label><spring:message code="ifsc.requestBy" /><span
								style="color: red"></span></label></td>
								<c:choose>
												<c:when test="${ empty lookUpDTO.lastUpdatedBy}">
													<td>${lookUpDTO.createdBy}</td>
												</c:when>
										
												<c:otherwise>
												<td>${lookUpDTO.lastUpdatedBy}</td>
												</c:otherwise>
											</c:choose>
						
						
<td><label><spring:message
												code="sm.lbl.requestDate" /></label></td>
													<c:choose>
												<c:when test="${ empty lookUpDTO.lastUpdatedOn}">
												 <td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
															value="${lookUpDTO.createdOn}" /></td>
													        
												
												</c:when>
										
												<c:otherwise>
												 <td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
															value="${lookUpDTO.lastUpdatedOn}" /></td>
													        
												</c:otherwise>
											</c:choose>
												    
						<td><label><spring:message
									code="ifsc.approverComments" /><span style="color: red"></span></label></td>
						<td>${lookUpDTO.checkerComments}</td>
						
						<td><label><spring:message code="msg.lbl.requestType" /><span
								style="color: red"></span></label> </td>
						<td>${lookUpDTO.lastOperation}</td>

					</tr>
					
					
			<c:if test="${lookUpDTO.requestState eq 'P'}">
				 
				<sec:authorize access="hasAuthority('Approve LookUp')">
					
							<tr>
								<td colspan="6"><div class="panel-heading-red  clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> <span
											data-i18n="Data"><spring:message
													code="cap.approvalPanel.title" /></span></strong>
									</div></td>
							</tr>
							<tr>
								<td><label><spring:message
											code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
								<td><select name="select" id="apprej">
										<option value="N"><spring:message
												code="AM.lbl.select" /></option>
										<option value="A" id="approve"><spring:message
												code="AM.lbl.approve" /></option>
										<option value="R" id="reject"><spring:message
												code="AM.lbl.reject" /></option>
								</select></td>
								<td>
									<div style="text-align:center">
										<label><spring:message code="AM.lbl.remarks" /><span
											style="color: red">*</span></label>
									</div>
								</td>
								<td colspan="5"><textarea rows="4" cols="50"
										maxlength="100" id="rejectReason"></textarea>
									<div id="errorrejectReason" class="error"></div></td>
							</tr>
							
							</sec:authorize>
						</c:if>


				</tbody>
			</table>
			
			<div class="row">
				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center">
					
						<sec:authorize access="hasAuthority('Approve LookUp')">
							<c:if test="${lookUpDTO.requestState eq 'P'}">
								<input name="button10" type="button" class="btn btn-success"
									id="approveRole"
									value="<spring:message
							code="ifsc.submitBtn" />"
									onclick="approve('/approveLookUp');" />
							</c:if>
						</sec:authorize>
						
						<button type="button" class="btn btn-danger"
							onclick="submitForm('/getPendingLookUpList');">
							<spring:message code="ifsc.backBtn" />
						</button>
					</div>
				</div>
			</div>

				</div>
	</div>
</form:form>
