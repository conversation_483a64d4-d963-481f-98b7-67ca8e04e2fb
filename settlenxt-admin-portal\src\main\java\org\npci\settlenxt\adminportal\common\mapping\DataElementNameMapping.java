package org.npci.settlenxt.adminportal.common.mapping;

import java.util.List;

public class DataElementNameMapping {
	private String xmlName;
	private String type;
	private String dataType;
	private String minLength;
	private String maxLength;
	private String regexPattern;
	private String format;
	private String description;
	private String dateFormat;
	private ReasonCodes reasonCodes;
	private List<FieldConstraints> constraints;
	
	private NameMappingContext context;
	
	public String getXmlName() {
		return xmlName;
	}
	public void setXmlName(String xmlName) {
		this.xmlName = xmlName;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getRegexPattern() {
		return regexPattern;
	}
	public void setRegexPattern(String regexPattern) {
		this.regexPattern = regexPattern;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public List<FieldConstraints> getConstraints() {
		return constraints;
	}
	public void setConstraints(List<FieldConstraints> constraints) {
		this.constraints = constraints;
	}
	
	public String getDataType() {
		return dataType;
	}
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
	public String getMinLength() {
		return minLength;
	}
	public void setMinLength(String minLength) {
		this.minLength = minLength;
	}
	public String getMaxLength() {
		return maxLength;
	}
	public void setMaxLength(String maxLength) {
		this.maxLength = maxLength;
	}
	public NameMappingContext getContext() {
		return context;
	}
	public void setContext(NameMappingContext context) {
		this.context = context;
	}
	public String getFormat() {
		return format;
	}
	public void setFormat(String format) {
		this.format = format;
	}
	public ReasonCodes getReasonCodes() {
		return reasonCodes;
	}
	public void setReasonCodes(ReasonCodes reasonCodes) {
		this.reasonCodes = reasonCodes;
	}
	public String getDateFormat() {
		return dateFormat;
	}
	public void setDateFormat(String dateFormat) {
		this.dateFormat = dateFormat;
	}
}
