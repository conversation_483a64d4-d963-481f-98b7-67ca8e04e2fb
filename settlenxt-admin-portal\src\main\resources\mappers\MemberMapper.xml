<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.MemberRepository">
		<insert id="addMember">
		<if test="isInternational">
		INSERT INTO participant_stg(member_id, bank_code,bank_type,bank_name,unique_bank_name,ifsc_code, bank_sector, rtgs_code,participant_id,
			savings_acct_id,current_acct_id,phone_number1,phone_number2,mobile_number1,mobile_number2,email_id1,email_id2,legal_address,
			country_id,state_id,zip_code,city_id,gst_in,gst_address,gst_city,gst_country,gst_state,gst_code,
			created_by,created_on,status,request_state,record_status,parent_participant_id,website,nfs_id,sub_net, last_operation,MAX_USER_COUNT,currency_conversion_by,conversion_master,forex_id,is_npci_sett)
			VALUES (#{memberId},#{bankMasterCode}, #{memberType}, #{memberName}, #{uniqueBnk},#{ifscCode},#{bankSector}, 
			#{rtgsCode},#{participantId},
			#{savingsAccNumber},#{currentAccNumber},#{bnkPhone}, #{bnkPhone2}, #{bnkMobile}, #{bnkMobile2}, #{bnkEmail},
			#{bnkEmail2}, #{bnkAdd},#{bnkCountry}, #{bnkState}, #{bnkPincode}, #{bnkCity},
			#{gstIn}, #{gstAdd}, #{gstCity},
			#{gstCountry}, #{gstState}, #{gstPincode},#{createdBy},now(),#{status},#{requestState}, #{recordStatus}, #{parentParticipantId},#{webSite},#{participantIdNFS},#{subNet},'Add Member', #{maxUser},#{currencyConversionBy},#{currencyConversionType},#{forexId},#{isType})
	</if>
		<if test="!isInternational">
			INSERT INTO participant_stg(member_id, bank_code,bank_type,bank_name,unique_bank_name,ifsc_code, bank_sector, rtgs_code,participant_id,
			savings_acct_id,current_acct_id,phone_number1,phone_number2,mobile_number1,mobile_number2,email_id1,email_id2,legal_address,
			country_id,state_id,zip_code,city_id,gst_in,gst_address,gst_city,gst_country,gst_state,gst_code,
			created_by,created_on,status,request_state,record_status,parent_participant_id,website,nfs_id,sub_net, last_operation,MAX_USER_COUNT)
			VALUES (#{memberId},#{bankMasterCode}, #{memberType}, #{memberName}, #{uniqueBnk},#{ifscCode},#{bankSector}, 
			#{rtgsCode},#{participantId},
			#{savingsAccNumber},#{currentAccNumber},#{bnkPhone}, #{bnkPhone2}, #{bnkMobile}, #{bnkMobile2}, #{bnkEmail},
			#{bnkEmail2}, #{bnkAdd},#{bnkCountry}, #{bnkState}, #{bnkPincode}, #{bnkCity},
			#{gstIn}, #{gstAdd}, #{gstCity},
			#{gstCountry}, #{gstState}, #{gstPincode},#{createdBy},now(),#{status},#{requestState}, #{recordStatus}, #{parentParticipantId},#{webSite},#{participantIdNFS},#{subNet},'Add Member', #{maxUser})
		
		</if>
	</insert>
	<insert id="addMemberMain">
	<if test = "#{isInternational}">
		INSERT INTO participant(member_id, bank_code,bank_type,bank_name,unique_bank_name,ifsc_code, bank_sector, rtgs_code,participant_id,
			savings_acct_id,current_acct_id,phone_number1,phone_number2,mobile_number1,mobile_number2,email_id1,email_id2,legal_address,
			country_id,state_id,zip_code,city_id,gst_in,gst_address,gst_city,gst_country,gst_state,gst_code,
			created_by,created_on,last_updated_on,last_updated_by,status,parent_participant_id,website,nfs_id,sub_net,MAX_USER_COUNT,
			currency_conversion_by,conversion_master,forex_id,is_npci_sett,domestic_flag,domestic_created_date)
			VALUES (#{memberId},#{bankMasterCode}, #{memberType}, #{memberName}, #{uniqueBnk},#{ifscCode},#{bankSector}, 
			#{rtgsCode},#{participantId},
			#{savingsAccNumber},#{currentAccNumber},#{bnkPhone}, #{bnkPhone2}, #{bnkMobile}, #{bnkMobile2}, #{bnkEmail},
			#{bnkEmail2}, #{bnkAdd},#{bnkCountry}, #{bnkState}, #{bnkPincode}, #{bnkCity},
			#{gstIn}, #{gstAdd}, #{gstCity},
			#{gstCountry}, #{gstState}, #{gstPincode},#{createdBy},#{createdOn},#{lastUpdatedOn}, #{lastUpdatedBy} ,#{status}, #{parentParticipantId},#{webSite},#{participantIdNFS},#{subNet}, #{maxUser},
			#{currencyConversionBy},#{currencyConversionType},#{forexId},#{isType},#{domesticFlag},#{domesticCreatedDate})
	</if>
	<if test = "!#{isInternational}">
		INSERT INTO participant(member_id, bank_code,bank_type,bank_name,unique_bank_name,ifsc_code, bank_sector, rtgs_code,participant_id,
			savings_acct_id,current_acct_id,phone_number1,phone_number2,mobile_number1,mobile_number2,email_id1,email_id2,legal_address,
			country_id,state_id,zip_code,city_id,gst_in,gst_address,gst_city,gst_country,gst_state,gst_code,
			created_by,created_on,last_updated_on,last_updated_by,status,parent_participant_id,website,nfs_id,sub_net,MAX_USER_COUNT)
			VALUES (#{memberId},#{bankMasterCode}, #{memberType}, #{memberName}, #{uniqueBnk},#{ifscCode},#{bankSector}, 
			#{rtgsCode},#{participantId},
			#{savingsAccNumber},#{currentAccNumber},#{bnkPhone}, #{bnkPhone2}, #{bnkMobile}, #{bnkMobile2}, #{bnkEmail},
			#{bnkEmail2}, #{bnkAdd},#{bnkCountry}, #{bnkState}, #{bnkPincode}, #{bnkCity},
			#{gstIn}, #{gstAdd}, #{gstCity},
			#{gstCountry}, #{gstState}, #{gstPincode},#{createdBy},#{createdOn},#{lastUpdatedOn}, #{lastUpdatedBy} ,#{status}, #{parentParticipantId},#{webSite},#{participantIdNFS},#{subNet}, #{maxUser})
		</if>	
	</insert>
	<insert id="insertNetwBinRange">
		INSERT INTO network_bin_details(bin_id, participant_id, activation_date, domain_usage, deactivation_date, bank_group, offline_allowed, bin_type,
		 bin_number, low_bin, high_bin, pan_length, bin_card_type, bin_product_type, country_code, currency_code, bin_card_variant, bin_card_brand, 
		 message_type, card_technology, authentication_mechanism, sub_scheme, card_sub_variant, program_details, form_factor, features, created_by, 
		 created_on, last_updated_by, last_updated_on, status, product_type, settlement_bin, acquirer_id, member_id,bin_length,dxs_code,license_id,iss_id)
		 VALUES (#{binId}, #{participantId}, #{acqBinActivationDate},#{domainUsage}, #{dectivationDate}, #{bankGroup}, #{offlineAllowed},#{binType},
		 #{binNumber}, #{lowBin}, #{highBin}, #{panLength}, #{binCardType}, #{binProductType},#{countryCode}, #{currencyCode}, #{binCardVariant},
		 #{binCardBrand},#{messageType},#{cardTechnology}, #{authMechanism}, #{subScheme}, #{cardSubVariant}, #{programDetails}, #{formFactor},
	 	 #{featureIssBin}, #{createdBy}, #{createdOn}, #{lastUpdatedBy}, #{lastUpdatedOn}, #{status}, #{productType}, #{settlementBin}, #{acquirerId},
	 	 #{memberId},9,#{dxsCode},#{licenseId},#{issId});
	</insert>
	<select id="checkIfParticipantStgExists" resultType="long">
		select count(*) from participant_stg where participant_id = #{participantId}
	</select>
	<select id="checkIfParticipantExists" resultType="long">
		select count(*) from participant where participant_id = #{participantId}
	</select>
	<select id="checkIfParticipantContactExists" resultType="long">
		select count(*) from participant_contact_stg where participant_id = #{participantId}
	</select>
	<select id="checkIfParticipantContactExistsMain" resultType="long">
	select count(*) from participant_contact where participant_id = #{participantId}
	</select>
	<insert id="addMemberMainTable">
		INSERT INTO participant(ifsc_code,unique_bank_name,member_id, bank_code,bank_type,bank_name,bank_sector, rtgs_code,participant_id,
			phone_number1,phone_number2,mobile_number1,mobile_number2,email_id1,email_id2,legal_address,
			country_id,state_id,zip_code,city_id,gst_in,gst_address,gst_city,gst_country,gst_state,
			created_by,created_on,status,request_state,record_status,remarks,gst_code,nfs_id,savings_acct_id,current_acct_id,website,parent_participant_id,sub_net,MAX_USER_COUNT)
			VALUES (#{ifscCode},#{uniqueBnk},#{memberId},#{bankMasterCode}, #{memberType}, #{memberName},#{bankSector}, #{rtgsCode},
			#{participantId},#{bnkPhone}, #{bnkPhone2}, #{bnkMobile}, #{bnkMobile2}, #{bnkEmail},
			#{bnkEmail2}, #{bnkAdd},#{bnkCountry}, #{bnkState}, #{bnkPincode}, #{bnkCity},
			#{gstIn}, #{gstAdd}, #{gstCity},#{gstCountry}, #{gstState},
			#{createdBy},#{createdOn},'A',#{requestState}, #{recordStatus}, #{rejectReason},#{gstPincode},#{participantIdNFS},#{savingsAccNumber},#{currentAccNumber},#{webSite},#{parentParticipantId},#{subNet}, #{maxUser})
	</insert>
	<insert id="addMemberContactInfo">
		INSERT INTO participant_contact_stg(member_id,participant_id, legal_address,address_type,name,
			phone_number,mobile_number,fax_number,country_id,state_id,city_id,zip_code,
			created_by,created_on,status,email_id,auth_officer_desg)
			VALUES (#{memberId}, #{participantId}, #{cntAdd1}, #{addressType}, #{cntChkrName}, #{cntPhone}, #{cntMobile}, #{cntFax},
			#{cntCountry}, #{cntState},#{cntCity},#{cntPincode},#{createdBy},now(), #{status},#{cntEmail},#{cntDesignation})
	</insert>
	<insert id="addMemberContactInfoMainTable">
		INSERT INTO participant_contact(particpant_contact_id,member_id,participant_id, legal_address,address_type,name,
			phone_number,mobile_number,fax_number,country_id,state_id,city_id,
			created_by,created_on,status,zip_code,email_id,auth_officer_desg)
			VALUES (#{participantContactId},#{memberId}, #{participantId}, #{cntAdd1}, #{addressType}, #{cntChkrName}, #{cntPhone}, #{cntMobile}, #{cntFax},
			#{cntCountry}, #{cntState},#{cntCity},#{createdBy},#{createdOn}, #{status},#{cntPincode},#{cntEmail},#{cntDesignation})
	</insert>
	<insert id="addSettlementBin">
		INSERT INTO participant_settlementbin_stg(settlement_bin_id, participant_id, settlement_bin_no,
			is_default, is_complete,is_active,created_by,status,settlement_currency, clearing_agency_type, created_on)
			VALUES (#{settlementBinId}::numeric, #{participantId}, #{settlementBinNumber}, #{isDefault}, 'A', 'A',#{createdBy}, #{status},#{settlementCurrency},#{clearingAgencyType},#{createdOn})
	</insert>
	<insert id="addSettlementBinMainTable">
		INSERT INTO participant_settlementbin_stg(settlement_bin_id, participant_id, settlement_bin_no,
			is_default, is_complete,is_active,created_by,status,settlement_currency, clearing_agency_type, created_on)
			VALUES (#{settlementBinId}::numeric, #{participantId}, #{settlementBinNumber}, #{isDefault}, 'A', 'A',#{createdBy}, #{status},#{settlementCurrency},#{clearingAgencyType}#{createdOn})
	</insert>
	<insert id="updateDefaultSettlementBin">
		update participant_settlementbin_stg set is_default ='N' where participant_id = #{participantId}
	</insert>
	<insert id="addAcqBin">
		INSERT INTO membin_details_stg(
		  acquirer_id, bank_group, bin_type, 
		  domain_usage, participant_id, created_by, 
		  settlement_bin, product_type, status, 
		  offline_allowed, activation_date, deactivation_date, created_on
		) 
		VALUES 
		  (
		    #{acquirerId},#{acqBankGroup}, #{binType}
		    ,#{acqDomainUsage},#{participantId}, #{createdBy}, 
		    #{acqSettlementBin}, #{acqProductType},  #{status}, 
		    #{offlineAllowed}, #{acqFrmDate}, #{acqToDate},#{createdOn}
		   )
	</insert>
	
	<insert id="addIssBin">
	<if test="isInternational">
		INSERT INTO membin_details_stg(markup,network_license_id,network_issuer_id,bank_group,bin_type,bin_number,bin_length,additional_params,
			low_bin,high_bin,pan_length,bin_card_type,bin_product_type,bin_card_variant,bin_card_brand,
			message_type,card_technology,authentication_mechanism,participant_id,
			domain_usage,created_by,settlement_bin,product_type,
			sub_scheme,card_sub_variant,program_details,form_factor,status,activation_date,deactivation_date,offline_allowed,features, created_on)
			VALUES (#{markUp},#{networkLicenseId},#{networkIssId},#{issBankGroup}, #{issBinType}, #{binNumber},#{binLength},#{additionalParams}, #{lowBin}, #{highBin}, #{panLength}, #{binCardType},
			#{binProductType}, #{binCardVariant},#{binCardBrand}, #{messageType}, #{cardTechnology}, #{authMechanism}, #{participantId},
			#{issDomainUsage},#{createdBy}, #{issSettlementBin}, #{issProductType},
			#{subScheme},#{cardSubVariant},#{programDetails},#{formFactor},#{status},#{issFrmDate}, #{issToDate},#{offlineAllowed},#{featureIssBin},#{createdOn})
</if>
<if test="!isInternational">
	INSERT INTO membin_details_stg(bank_group,bin_type,bin_number,bin_length,additional_params,
			low_bin,high_bin,pan_length,bin_card_type,bin_product_type,bin_card_variant,bin_card_brand,
			message_type,card_technology,authentication_mechanism,participant_id,
			domain_usage,created_by,settlement_bin,product_type,
			sub_scheme,card_sub_variant,program_details,form_factor,status,activation_date,deactivation_date,offline_allowed,features, created_on)
			VALUES (#{issBankGroup}, #{issBinType}, #{binNumber},#{binLength},#{additionalParams}, #{lowBin}, #{highBin}, #{panLength}, #{binCardType},
			#{binProductType}, #{binCardVariant},#{binCardBrand}, #{messageType}, #{cardTechnology}, #{authMechanism}, #{participantId},
			#{issDomainUsage},#{createdBy}, #{issSettlementBin}, #{issProductType},
			#{subScheme},#{cardSubVariant},#{programDetails},#{formFactor},#{status},#{issFrmDate}, #{issToDate},#{offlineAllowed},#{featureIssBin},#{createdOn})
	
</if>
	</insert>
		<insert id="addAcqBinMain">
		INSERT INTO membin_details(
		  bin_id,acquirer_id, bank_group, bin_type, 
		  domain_usage, participant_id, created_by, 
		  settlement_bin, product_type, status, 
		  offline_allowed, activation_date, deactivation_date, created_on,last_updated_by,last_updated_on,
		  message_type
		) 
		VALUES 
		  (
		  #{binId}, #{acquirerId},#{acqBankGroup}, #{binType}
		    ,#{acqDomainUsage},#{participantId}, #{createdBy}, 
		    #{acqSettlementBin}, #{acqProductType},  #{status}, 
		    #{offlineAllowed}, #{acqFrmDate}, #{acqToDate},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{messageType}
		   )
	</insert>
	
	<insert id="addIssBinMain">
		INSERT INTO membin_details(markup,network_license_id,network_issuer_id,bin_id, bank_group,bin_type,bin_number,bin_length,additional_params,
			low_bin,high_bin,pan_length,bin_card_type,bin_product_type,bin_card_variant,bin_card_brand,
			message_type,card_technology,authentication_mechanism,participant_id,
			domain_usage,created_by,settlement_bin,product_type,
			sub_scheme,card_sub_variant,program_details,form_factor,status,activation_date,deactivation_date,offline_allowed,features, created_on,  last_updated_by,last_updated_on)
			VALUES (#{markUp},#{networkLicenseId},#{networkIssId},#{binId},#{issBankGroup}, #{issBinType}, #{binNumber},#{binLength},#{additionalParams}, #{lowBin}, #{highBin}, #{panLength}, #{binCardType},
			#{binProductType}, #{binCardVariant},#{binCardBrand}, #{messageType}, #{cardTechnology}, #{authMechanism}, #{participantId},
			#{issDomainUsage},#{createdBy}, #{issSettlementBin}, #{issProductType},
			#{subScheme},#{cardSubVariant},#{programDetails},#{formFactor},#{status},#{issFrmDate}, #{issToDate},#{offlineAllowed},#{featureIssBin},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn})
			
	</insert>
		<select id="getSettlementBins" resultType="SettlementBinDTO">
		select  settlement_bin_id as settlementBinId,is_complete as isComplete,is_active as isActive,participant_id as participantId
		, settlement_bin_no as settlementBinNumber,settlement_currency as settlementCurrency, is_default as isDefault, status
		,created_by as createdBy,created_on  as createdOn  , clearing_agency_type as clearingAgencyType
		,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn
		FROM participant_settlementbin_stg  WHERE participant_id = #{participantId}
	</select>
		<select id="getSettlementBinsMain" resultType="SettlementBinDTO">
		select  settlement_bin_id as settlementBinId,is_complete as isComplete,is_active as isActive,participant_id as participantId
		, settlement_bin_no as settlementBinNumber,settlement_currency as settlementCurrency, is_default as isDefault, status
		,created_by as createdBy,created_on  as createdOn, clearing_agency_type as clearingAgencyType
		,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn 
		FROM participant_settlementbin  WHERE participant_id = #{participantId}
	</select>
	<insert id="addFileDetails">
		INSERT INTO participant_documents_stg(document_name, document_path,is_active,participant_id,
			created_by,status,created_on)VALUES (#{documentName},  #{documentPath}, 'A', #{participantId},
			#{createdBy},'A', now())
			
	</insert>
	<insert id="addFileDetailsMain">
		INSERT INTO participant_documents(document_id,document_name, document_path,is_active,participant_id,
			created_by,status,created_on, last_updated_by, last_updated_on)VALUES (#{documentId},#{documentName},  #{documentPath}, #{isActive}, #{participantId},
			#{createdBy},#{status},#{createdOn}, #{lastUpdatedBy},#{lastUpdatedOn})
			
	</insert>
	<update id="updateFileDetails">
		update participant_documents_stg set document_path= #{documentPath}, status=#{status} , last_updated_by = #{lastUpdatedBy}, last_updated_on=now() where document_id=#{documentId} and participant_id=#{participantId}
	</update>
	<update id="finalMemberSubmit">
		UPDATE participant_stg SET status='N',request_state = 'Submit', record_status = 'P' WHERE member_id = #{memberId}
			
	</update>
	<update id="memberApproveStg">
		UPDATE participant_stg SET record_status = #{recordStatus} , checker_comments=#{rejectReason}, last_updated_on=#{lastUpdatedOn}
		, last_updated_by=#{userName},rejected_on=#{rejectedOn}
		 WHERE participant_id = #{participantId}
			
	</update>
	<update id="finalMemberReject">
		UPDATE participant SET status='N', record_status = 'R' WHERE participant_id = #{participantId}
			
	</update>
	<update id="stgMemberReject">
		UPDATE participant_stg SET  record_status = 'R',remarks=#{rejectReason},rejected_on=now() WHERE participant_id = #{participantId}
			
	</update>
 
	<select id="getsettlementBinsList" resultType="SettlementBinDTO">
		SELECT participant_id as participantId,settlement_bin_no as settlementBinNumber,is_default as isDefault,status as status,settlement_bin_id as settlementBinId,is_complete as isComplete
		,is_active	as isActive,created_by as createdBy,created_on as createdOn,last_updated_by as lastUpdatedBy, clearing_agency_type as clearingAgencyType
		,last_updated_on as lastUpdatedOn
		 FROM participant_settlementbin_stg WHERE participant_id= #{participantId} 
		and status in <foreach item='item' index='index' collection='statusList' open='(' separator=',' close=')'>#{item}</foreach> order by created_on
	</select>
	<select id="getNetBinDetail" resultType="BinDetailsDTO">
	SELECT bin_id as binId, participant_id as participantId,
	activation_date as acqBinActivationDate, domain_usage as domainUsage,
	deactivation_date as dectivationDate, bank_group as bankGroup,
	offline_allowed as offlineAllowed, bin_type as binType, bin_number as
	binNumber, low_bin as lowBin, high_bin as highBin, pan_length as
	panLength, bin_card_type as binCardType, bin_product_type as
	binProductType , country_code as countryCode, currency_code as
	currencyCode, bin_card_variant as binCardVariant , bin_card_brand as
	binCardBrand, message_type as messageType, card_technology as
	cardTechnology, authentication_mechanism as authMechanism, sub_scheme
	as subScheme, card_sub_variant as cardSubVariant, program_details as
	programDetails, form_factor as formFactor, features as featureIssBin,
	created_by as createdBy, created_on as createdOn, last_updated_by as
	lastUpdatedBy , last_updated_on as lastUpdatedOn, status as status,
	product_type as productType, settlement_bin as settlementBin ,
	acquirer_id as acquirerId, member_id as memberId
	FROM
	network_bin_details where bin_number = #{bin} order by bin_id desc limit 1;
	</select>
	<select id="getAcqBinDetails" resultType="BinDetailsDTO">
		 SELECT 
			  TO_CHAR(activation_date, 'DD-MM-YYYY') as acqFrmDateStr, 
			  TO_CHAR(deactivation_date, 'DD-MM-YYYY') as acqToDateStr, 
			  bin_id as binId, 
			  domain_usage as acqDomainUsage, 
			  bank_group as acqBankGroup, 
			  offline_allowed as offlineAllowed, 
			  bin_type as binType,   
			  created_by as createdBy, 
			  created_on as createdOn, 
			  last_updated_by as lastUpdatedBy, 
			  last_updated_on as lastUpdatedOn, 
			  status, 
			  product_type as acqProductType, 
			  settlement_bin as acqSettlementBin, 
			  acquirer_id as acquirerId
			  ,participant_id as participantId
			  , activation_date as acqFrmDate, deactivation_date as acqToDate
			FROM 
			  membin_details_stg 
			WHERE  participant_id = #{participantId} and bin_type = #{binType} and status!='D' order by created_on
	</select>
	
	
	<select id="getAcqBinDetailsDeleteInsert" resultType="BinDetailsDTO">
		 SELECT 
			  TO_CHAR(activation_date, 'DD-MM-YYYY') as acqFrmDateStr, 
			  TO_CHAR(deactivation_date, 'DD-MM-YYYY') as acqToDateStr, 
			  bin_id as binId, 
			  domain_usage as acqDomainUsage, 
			  bank_group as acqBankGroup, 
			  offline_allowed as offlineAllowed, 
			  bin_type as binType,   
			  created_by as createdBy, 
			  created_on as createdOn, 
			  last_updated_by as lastUpdatedBy, 
			  last_updated_on as lastUpdatedOn, 
			  status, 
			  product_type as acqProductType, 
			  settlement_bin as acqSettlementBin, 
			  acquirer_id as acquirerId
			  ,participant_id as participantId
			  , activation_date as acqFrmDate, deactivation_date as acqToDate
			FROM 
			  membin_details_stg 
			WHERE participant_id = #{participantId} and bin_type = #{binType} order by created_on
			  <!-- participant_id = #{participantId} and bin_type = #{binType} and status!='D' order by created_on -->
	</select>

	<select id="getAcqBinDetailsForPendingForApproval" resultType="BinDetailsDTO">
		 SELECT 
			  TO_CHAR(activation_date, 'DD-MM-YYYY') as acqFrmDateStr, 
			  TO_CHAR(deactivation_date, 'DD-MM-YYYY') as acqToDateStr, 
			  bin_id as binId, 
			  domain_usage as acqDomainUsage, 
			  bank_group as acqBankGroup, 
			  offline_allowed as offlineAllowed, 
			  bin_type as binType,   
			  created_by as createdBy, 
			  created_on as createdOn, 
			  last_updated_by as lastUpdatedBy, 
			  last_updated_on as lastUpdatedOn, 
			  status, 
			  product_type as acqProductType, 
			  settlement_bin as acqSettlementBin, 
			  acquirer_id as acquirerId
			  ,participant_id as participantId
			  , activation_date as acqFrmDate, deactivation_date as acqToDate
			FROM 
			  membin_details_stg 
			WHERE 
			  participant_id = #{participantId} and bin_type = #{binType} and (status!='D' or (status='D' and   current_deleted='Y')) order by created_on
	</select>
	<select id="getAcqBinDetailsMain" resultType="BinDetailsDTO">
		 SELECT bin_id as binId, member_id as memberId,bin_type as acqBinType,
			  TO_CHAR(activation_date, 'DD-MM-YYYY') as acqFrmDateStr, 
			  TO_CHAR(deactivation_date, 'DD-MM-YYYY') as acqToDateStr, 
			  bin_id as binId, 
			  domain_usage as acqDomainUsage, 
			  bank_group as acqBankGroup, 
			  offline_allowed as offlineAllowed, 
			  bin_type as binType,   
			  created_by as createdBy, 
			  created_on as createdOn, 
			  last_updated_by as lastUpdatedBy, 
			  last_updated_on as lastUpdatedOn, 
			  status, 
			  product_type as acqProductType, 
			  settlement_bin as acqSettlementBin, 
			  acquirer_id as acquirerId
			  ,participant_id as participantId
			  , activation_date as acqFrmDate, deactivation_date as acqToDate
			FROM 
			  membin_details
			WHERE 
			  participant_id = #{participantId} and bin_type = #{binType} and status!='D' order by created_on
	</select>
	<select id="getIssuerAndTokenBinDetails" resultType="BinDetailsDTO">
			SELECT 
			  TO_CHAR(activation_date, 'DD-MM-YYYY') as issFrmDateStr, 
			  TO_CHAR(deactivation_date, 'DD-MM-YYYY') as issToDateStr, 
			  bin_id as binId, 
			  participant_id as participantId, 
			  domain_usage as issDomainUsage, 
			   bank_group as issBankGroup, 
			  offline_allowed as offlineAllowed, 
			  bin_type as issBinType, 
			  bin_number as binNumber, 
			  bin_length as binLength,
			  <if test="isInternational">
			  markup as markUp,network_license_id as networkLicenseId,network_issuer_id as networkIssId,domestic_flag as domesticFlag,
			  </if>
			  additional_params as additionalParams,
			  low_bin as lowBin, 
			  high_bin as highBin, 
			  pan_length as panLength, 
			  bin_card_type as binCardType, 
			  bin_product_type as binProductType, 
			  bin_card_variant as binCardVariant, 
			  bin_card_brand as binCardBrand, 
			  message_type as messageType, 
			  card_technology as cardTechnology, 
			  authentication_mechanism as authMechanism, 
			  sub_scheme as subScheme, 
			  card_sub_variant as cardSubVariant, 
			  program_details as programDetails, 
			  form_factor as formFactor, 
			  features as featureIssBin, 
			  created_by as createdBy, 
			  created_on as createdOn, 
			  last_updated_by as lastUpdatedBy, 
			  last_updated_on as lastUpdatedOn, 
			  status, 
			  product_type as issProductType, 
			  settlement_bin as issSettlementBin,  
			  features as featureIssBin 
			  , activation_date as issFrmDate, deactivation_date as issToDate
			FROM  membin_details_stg 
			WHERE   participant_id = #{participantId} and bin_type IN ('I', 'T')  and status!='D' order by created_on 
	</select>
	<select id="getIssuerAndTokenBinDetailsForDeleteBin" resultType="BinDetailsDTO">
			SELECT 
			  TO_CHAR(activation_date, 'DD-MM-YYYY') as issFrmDateStr, 
			  TO_CHAR(deactivation_date, 'DD-MM-YYYY') as issToDateStr, 
			  bin_id as binId, 
			  participant_id as participantId, 
			  domain_usage as issDomainUsage, 
			   bank_group as issBankGroup, 
			  offline_allowed as offlineAllowed, 
			  bin_type as issBinType, 
			  bin_number as binNumber, 
			  bin_length as binLength,
			  additional_params as additionalParams,
			  low_bin as lowBin, 
			  high_bin as highBin, 
			  pan_length as panLength, 
			  bin_card_type as binCardType, 
			  bin_product_type as binProductType, 
			  bin_card_variant as binCardVariant, 
			  bin_card_brand as binCardBrand, 
			  message_type as messageType, 
			  card_technology as cardTechnology, 
			  authentication_mechanism as authMechanism, 
			  sub_scheme as subScheme, 
			  card_sub_variant as cardSubVariant, 
			  program_details as programDetails, 
			  form_factor as formFactor, 
			  features as featureIssBin, 
			  created_by as createdBy, 
			  created_on as createdOn, 
			  last_updated_by as lastUpdatedBy, 
			  last_updated_on as lastUpdatedOn, 
			  status, 
			  product_type as issProductType, 
			  settlement_bin as issSettlementBin,  
			  features as featureIssBin 
			  , activation_date as issFrmDate, deactivation_date as issToDate<if test = "isInternational">,markUp as markUp ,network_license_id as networkLicenseId,network_issuer_id as networkIssId,domestic_flag as domesticFlag</if>
			FROM  membin_details_stg 
			WHERE  participant_id = #{participantId} and bin_type IN ('I', 'T')  order by created_on
			 <!--  participant_id = #{participantId} and bin_type IN ('I', 'T')  and status!='D' order by created_on -->
	</select>
	
	<select id="getNetworkSelection" resultType="CodeValueDTO">
	select  participant_id as code , concat(participant_id,' - ',bank_name)  as description from participant where bank_type='N'
	</select>
	<select id="getForexId" resultType="CodeValueDTO">
	select forex_id as code  , description as description from forex_master
	</select>
	<select id="getIssuerAndTokenBinDetailsForPendingForApproval" resultType="BinDetailsDTO">
			SELECT 
			  TO_CHAR(activation_date, 'DD-MM-YYYY') as issFrmDateStr, 
			  TO_CHAR(deactivation_date, 'DD-MM-YYYY') as issToDateStr, 
			  bin_id as binId, 
			  participant_id as participantId, 
			  domain_usage as issDomainUsage, 
			   bank_group as issBankGroup, 
			  offline_allowed as offlineAllowed, 
			  bin_type as issBinType, 
			  bin_number as binNumber, 
			  bin_length as binLength,
			  additional_params as additionalParams,
			  markup as markUp,
			  network_license_id as networkLicenseId,network_issuer_id as networkIssId,
			  low_bin as lowBin, 
			  high_bin as highBin, 
			  pan_length as panLength, 
			  bin_card_type as binCardType, 
			  bin_product_type as binProductType, 
			  bin_card_variant as binCardVariant, 
			  bin_card_brand as binCardBrand, 
			  message_type as messageType, 
			  card_technology as cardTechnology, 
			  authentication_mechanism as authMechanism, 
			  sub_scheme as subScheme, 
			  card_sub_variant as cardSubVariant, 
			  program_details as programDetails, 
			  form_factor as formFactor, 
			  features as featureIssBin, 
			  created_by as createdBy, 
			  created_on as createdOn, 
			  last_updated_by as lastUpdatedBy, 
			  last_updated_on as lastUpdatedOn, 
			  status, 
			  product_type as issProductType, 
			  settlement_bin as issSettlementBin,  
			  features as featureIssBin 
			  , activation_date as issFrmDate, deactivation_date as issToDate
			  ,current_deleted as currentDeleted 	<if test = "isInternational">,markUp as markUp ,network_license_id as networkLicenseId,network_issuer_id as networkIssId,domestic_flag as domesticFlag</if>
			FROM  membin_details_stg 
			WHERE 
			  participant_id = #{participantId} and bin_type IN ('I', 'T')  and (status!='D' or (status='D' and current_deleted='Y')) order by created_on
	</select>
		<select id="getUnallocatedIssuerAndTokenBinDetails" resultType="BinDetailsDTO">
			SELECT 
			  domain_usage as issDomainUsage, 
			  offline_allowed as offlineAllowed, 
			  bin_type as issBinType, 
			  bin_number as binNumber, 
			  low_bin as lowBin, 
			  high_bin as highBin, 
			  pan_length as panLength, 
			  bin_card_type as binCardType, 
			  bin_product_type as binProductType, 
			  bin_card_variant as binCardVariant, 
			  bin_card_brand as binCardBrand, 
			  message_type as messageType, 
			  card_technology as cardTechnology,  
			  status, 
			  product_type as issProductType, 
			  activation_date as issFrmDate, deactivation_date as issToDate
			FROM  bin_master 
			WHERE  status='U' order by bin_number
	</select>
	<update id="updateUnallocatedBin">
		update bin_master set status =#{status}, last_updated_on=now(), last_updated_by=#{userId} where status !=#{status} and  bin_number IN
		<foreach item='item' index='index' collection='binList' open='(' separator=',' close=')'>#{item.binNumber}</foreach>
	</update>
	<select id="getIssuerAndTokenBinDetailsMain" resultType="BinDetailsDTO">
			SELECT member_id as memberId, 
			  TO_CHAR(activation_date, 'DD-MM-YYYY') as issFrmDateStr, 
			  TO_CHAR(deactivation_date, 'DD-MM-YYYY') as issToDateStr, 
			  bin_id as binId, 
			  participant_id as participantId, 
			  domain_usage as issDomainUsage, 
			   bank_group as issBankGroup, 
			  offline_allowed as offlineAllowed, 
			  bin_type as issBinType, bin_type as binType, 
			  bin_number as binNumber,
			  bin_length as binLength,
			  additional_params as additionalParams,
			  low_bin as lowBin, 
			  high_bin as highBin, 
			  pan_length as panLength, 
			  bin_card_type as binCardType, 
			  bin_product_type as binProductType, 
			  bin_card_variant as binCardVariant, 
			  bin_card_brand as binCardBrand, 
			  message_type as messageType, 
			  card_technology as cardTechnology, 
			  authentication_mechanism as authMechanism, 
			  sub_scheme as subScheme, 
			  card_sub_variant as cardSubVariant, 
			  program_details as programDetails, 
			  form_factor as formFactor, 
			  features as featureIssBin, 
			  created_by as createdBy, 
			  created_on as createdOn, 
			  last_updated_by as lastUpdatedBy, 
			  last_updated_on as lastUpdatedOn, 
			  status, 
			  product_type as issProductType, 
			  settlement_bin as issSettlementBin,  
			  features as featureIssBin 
			  , activation_date as issFrmDate, deactivation_date as issToDate<if test = "isInternational">,markUp as markUp ,network_license_id as networkLicenseId,network_issuer_id as networkIssId</if>
			FROM  membin_details 
			WHERE 
			  participant_id = #{participantId} and bin_type IN ('I', 'T')  and status!='D' order by created_on
	</select>
	<select id="getBinDetails" resultType="BinDetailsDTO">
		  SELECT TO_CHAR(activation_date,'DD-MM-YYYY') as issFrmDateStr,TO_CHAR(deactivation_date,'DD-MM-YYYY') as issToDateStr,bin_id as binId,participant_id as participantId,domain_usage as domainUsage ,bank_group as bankGroup,offline_allowed as offlineAllowed,bin_type as binType,bin_number as binNumber,bin_length as binLength, additional_params as additionalParams,low_bin as lowBin,high_bin as highBin ,pan_length as panLength,bin_card_type as binCardType,bin_product_type as binProductType,bin_card_variant as binCardVariant,bin_card_brand as binCardBrand,message_type as messageType,card_technology  as cardTechnology,authentication_mechanism as authMechanism,sub_scheme as subScheme,card_sub_variant as cardSubVariant,program_details as programDetails,form_factor as formFactor,features as featureIssBin,created_by,created_on as createdOn,
		  last_updated_by,last_updated_on,status, member_id as memberId,
		  product_type as issProductType,settlement_bin as settlementBin,acquirer_id,stip,features as featureIssBin<if test = "isInternational">,markup as markUp ,network_license_id as networkLicenseId,network_issuer_id as networkIssId,domestic_flag as domesticFlag</if>
		  FROM membin_details_stg 
		  WHERE participant_id = #{participantId} and bin_type = #{binType} and status!='D' order by created_on
	</select>
	<select id="getBinDetailsForDeleteBin" resultType="BinDetailsDTO">
		  SELECT TO_CHAR(activation_date,'DD-MM-YYYY') as issFrmDateStr,TO_CHAR(deactivation_date,'DD-MM-YYYY') as issToDateStr,bin_id as binId,participant_id as participantId,domain_usage as domainUsage ,bank_group as bankGroup,offline_allowed as offlineAllowed,bin_type as binType,bin_number as binNumber,bin_length as binLength, additional_params as additionalParams,low_bin as lowBin,high_bin as highBin ,pan_length as panLength,bin_card_type as binCardType,bin_product_type as binProductType,bin_card_variant as binCardVariant,bin_card_brand as binCardBrand,message_type as messageType,card_technology  as cardTechnology,authentication_mechanism as authMechanism,sub_scheme as subScheme,card_sub_variant as cardSubVariant,program_details as programDetails,form_factor as formFactor,features as featureIssBin,created_by,created_on as createdOn,
		  last_updated_by,last_updated_on,status, member_id as memberId,
		  product_type as issProductType,settlement_bin as settlementBin,acquirer_id,stip,features as featureIssBin <if test = "isInternational">,markup as markUp ,network_license_id as networkLicenseId,network_issuer_id as networkIssId,domestic_flag as domesticFlag</if>
		  FROM membin_details_stg 
		  WHERE participant_id = #{participantId} and bin_type = #{binType}  order by created_on
	</select>
	<select id="getBinDetailsForPendingForApproval" resultType="BinDetailsDTO">
		  SELECT TO_CHAR(activation_date,'DD-MM-YYYY') as issFrmDateStr,TO_CHAR(deactivation_date,'DD-MM-YYYY') as issToDateStr,bin_id as binId,participant_id as participantId,domain_usage as domainUsage ,bank_group as bankGroup,offline_allowed as offlineAllowed,bin_type as binType,bin_number as binNumber,bin_length as binLength, additional_params as additionalParams,low_bin as lowBin,high_bin as highBin ,pan_length as panLength,bin_card_type as binCardType,bin_product_type as binProductType,bin_card_variant as binCardVariant,bin_card_brand as binCardBrand,message_type as messageType,card_technology  as cardTechnology,authentication_mechanism as authMechanism,sub_scheme as subScheme,card_sub_variant as cardSubVariant,program_details as programDetails,form_factor as formFactor,features as featureIssBin,created_by,created_on as createdOn,last_updated_by,last_updated_on,status, member_id as memberId,product_type as issProductType,settlement_bin as settlementBin,acquirer_id,stip,features as featureIssBin 
		  ,current_deleted as currentDeleted<if test = "isInternational">,markup as markUp,network_license_id as networkLicenseId,network_issuer_id as networkIssId,domestic_flag as domesticFlag </if>
		  FROM membin_details_stg WHERE participant_id = #{participantId} and bin_type = #{binType} and ( status!='D' or (status='D' and current_deleted='Y') order by created_on
	</select>
	<select id="getIssBinDetailsFetch" resultType="BinDetailsDTO">
 			 SELECT TO_CHAR(activation_date,'DD-MM-YYYY') as issFrmDateStr,TO_CHAR(deactivation_date,'DD-MM-YYYY') as issToDateStr,bin_id as binId,participant_id as participantId,domain_usage as domainUsage,bank_group as bankGroup,offline_allowed as offlineAllowed,bin_type as binType,bin_number as binNumber,low_bin as lowBin,high_bin as highBin,pan_length as panLength,bin_card_type as binCardType,bin_product_type as binProductType,bin_card_variant as binCardVariant,bin_card_brand as binCardBrand,message_type as messageType,card_technology as cardTechnology,authentication_mechanism as authMechanism,sub_scheme as subScheme,card_sub_variant as cardSubVariant,program_details as programDetails,form_factor as formFactor,features as featureIssBin,created_by,created_on as createdOn,last_updated_by,last_updated_on,status, member_id as memberId,product_type as issProductType,settlement_bin as settlementBin,acquirer_id,stip,features as featureIssBin FROM membin_details_stg WHERE participant_id = #{participantId} and bin_type in ('I','T') and status!='D' order by created_on
	</select>
	<select id="getAcqBinList" resultType="BinDetailsDTO">
		  	SELECT bin_id as binId,bank_group as acqBankGroup,acquirer_id as acquirerId,offline_allowed as offlineAllowed,domain_usage as acqDomainUsage
		  	,product_type as acqProductType,settlement_bin as acqSettlementBin,participant_id as participantId,bin_type as binType
		  	,activation_date as acqFrmDate,deactivation_date as acqToDate,created_by as createdBy,created_on as createdOn,status 
		  	FROM membin_details_stg  where  participant_id = #{participantId} and bin_type = 'A' 
		  		and status in <foreach item='item' index='index' collection='statusList' open='(' separator=',' close=')'>#{item}</foreach> order by created_on 
	</select>
	<select id="getAcqBinListMain" resultType="BinDetailsDTO">
		  	SELECT bin_id as binId,bank_group as acqBankGroup,acquirer_id as acquirerId,domain_usage as acqDomainUsage,product_type as acqProductType
		  	,settlement_bin as acqSettlementBin,participant_id as participantId,bin_type as binType,activation_date as acqFrmDate,deactivation_date as acqToDate
		  	,created_by as createdBy,created_on as createdOn,status 
		  	FROM membin_details where  participant_id = #{participantId} and bin_type = 'A' and status!='D'  
	</select>
	<select id="getIssBinList" resultType="BinDetailsDTO">
		  	SELECT bin_type as binType,bin_id as binId,activation_date as issFrmDate,deactivation_date as issToDate,participant_id as participantId,bin_type as binType,bank_group as issBankGroup,bin_number as binNumber,bin_length as binLength, additional_params as additionalParams,low_bin as lowBin,high_bin as highBin,pan_length as panLength,bin_card_type as binCardType,bin_product_type as binProductType,bin_card_variant as binCardVariant,bin_card_brand as binCardBrand,
			domain_usage as issDomainUsage,message_type as messageType,card_technology as cardTechnology,authentication_mechanism as authMechanism,product_type as issProductType,settlement_bin as issSettlementBin,sub_scheme as subScheme,
			card_sub_variant as cardSubVariant,program_details as programDetails,status ,form_factor as formFactor,offline_allowed as offlineAllowed,created_by as createdBy,created_on as createdOn,features as featureIssBin<if test = "isInternational">,markup as markUp ,network_license_id as networkLicenseId,network_issuer_id as networkIssId,domestic_flag as domesticFlag</if> from membin_details_stg
			where  participant_id = #{participantId} 
				and bin_type in ('I','T') and status in <foreach item='item' index='index' collection='statusList' open='(' separator=',' close=')'>#{item}</foreach>  
	</select>
	<select id="getIssBinListMain" resultType="BinDetailsDTO">
		  	SELECT bin_type as binType,bin_id as binId,activation_date as issFrmDate,deactivation_date as issToDate,participant_id as participantId,bin_type as binType,bank_group as issBankGroup,bin_number as binNumber,bin_length as binLength, additional_params as additionalParams,low_bin as lowBin,high_bin as highBin,pan_length as panLength,bin_card_type as binCardType,bin_product_type as binProductType,bin_card_variant as binCardVariant,bin_card_brand as binCardBrand,
			domain_usage as issDomainUsage,message_type as messageType,card_technology as cardTechnology,authentication_mechanism as authMechanism,product_type as issProductType,settlement_bin as issSettlementBin,sub_scheme as subScheme,
			card_sub_variant as cardSubVariant,program_details as programDetails,status,form_factor as formFactor,offline_allowed as offlineAllowed,created_by as createdBy,created_on as createdOn,features as featureIssBin  from membin_details
			where  participant_id = #{participantId} and bin_type in ('I','T') and status !='D'  
	</select>
	<select id="getFilesList" resultType="FileUploadDTO">
			SELECT document_id as documentId,document_path as documentPath 
			FROM participant_documents_stg WHERE participant_id = #{participantId} 
			and document_id in <foreach item='item' index='index' collection='fileIDs' open='(' separator=',' close=')'>#{item}</foreach>
	</select>
	<select id="getFilesPaths" resultType="String">
			SELECT document_path as documentPath 
			FROM participant_documents_stg WHERE participant_id = #{participantId} 
			and document_name in <foreach item='item' index='index' collection='fileNames' open='(' separator=',' close=')'>#{item}</foreach>
	</select>
	<select id="checkMemberContactInfoSaved" resultType="Integer">
		  	SELECT COUNT(*) from participant_contact_stg WHERE participant_id = #{participantId}
	</select>
	<select id="fetchParticipantIdSeq" resultType="int">
		  	select case when ( to_number(max(participantId),'FM9999')+1) is null then '1' else  to_number(max(participantId),'FM9999')+1  end as TOTAL from (SELECT substring(participant_id,8)  as participantId FROM participant_stg  where ifsc_code =#{ifscCode} ) A
	</select>
	<select id="getMember" resultType="MemberDTO">
		  	select unique_bank_name as uniqueBnk,member_id as memberid,bank_type as memberType,bank_name as participantName,bank_name as memberName,ifsc_code as ifscCode, bank_sector as bankSector, bank_code as bankMasterCode,
			rtgs_code as rtgsCode,savings_acct_id as savingsAccNumber,current_acct_id as currentAccNumber,participant_id as participantId,bank_name as participantName,parent_participant_id as parentParticipantId,
			phone_number1 as bnkPhone,phone_number2 as bnkPhone2,mobile_number1 as bnkMobile,mobile_number2 as bnkMobile2,email_id1 as bnkEmail,email_id2 as bnkEmail2,legal_address as bnkAdd,
			country_id as bnkCountry,state_id as bnkState,zip_code as bnkPincode,city_id as bnkCity,gst_in as gstIn,gst_address as gstAdd,gst_city as gstCity,gst_country as gstCountry,
			gst_state as gstState,gst_code as gstPincode,request_state as requestState,record_status as recordStatus,website as webSite,created_by as createdBy, last_operation as lastOperation, checker_comments as checkerComments, nfs_id as participantIdNFS, sub_net as subNet from participant_stg where participant_id = #{participantId}
	</select>
	<select id="getMemberMain" resultType="MemberDTO">
		  select unique_bank_name as uniqueBnk,member_id as memberid,bank_type as memberType,bank_name as memberName,ifsc_code as ifscCode, bank_sector as bankSector, bank_code as bankMasterCode,
			rtgs_code as rtgsCode,savings_acct_id as savingsAccNumber,current_acct_id as currentAccNumber,participant_id as participantId,bank_name as participantName,parent_participant_id as parentParticipantId,
			phone_number1 as bnkPhone,phone_number2 as bnkPhone2,mobile_number1 as bnkMobile,mobile_number2 as bnkMobile2,email_id1 as bnkEmail ,email_id2 as bnkEmail2,legal_address as bnkAdd,
			country_id as bnkCountry,state_id as bnkState,zip_code as bnkPincode,city_id as bnkCity,gst_in as gstIn,gst_address as gstAdd,gst_city as gstCity,gst_country as gstCountry,
			gst_state as gstState,gst_code as gstPincode,request_state as requestState,nfs_id as participantIdNFS,record_status as recordStatus,website as webSite,created_by as createdBy, sub_net as subNet from participant where participant_id = #{participantId}
	</select>
	<select id="getMemberContactInfo" resultType="MemberContactInfoDTO">
		 select address_type as addressType,name as cntChkrName,phone_number as cntPhone,mobile_number as cntMobile,fax_number as cntFax,legal_address as cntAdd1,
			country_id as cntCountry,state_id as cntState,city_id as cntCity,zip_code as cntPincode,email_id as cntEmail,auth_officer_desg as cntDesignation from participant_contact_stg where participant_id = #{participantId}
	</select>
	<select id="getMemberContactInfoMain" resultType="MemberContactInfoDTO">
		select address_type as addressType,name as cntChkrName,phone_number as cntPhone,mobile_number as cntMobile,fax_number as cntFax,legal_address as cntAdd1,
			country_id as cntCountry,state_id as cntState,city_id as cntCity,zip_code as cntPincode,email_id as cntEmail,auth_officer_desg as cntDesignation from participant_contact where participant_id = #{participantId}
	</select>
	<select id="getMembers" resultType="MemberDTO">
		SELECT a.participant_id as participantId,a.memberid as memberId,a.bank_name as memberName,a.bank_type as memberType,a.bank_code as bankMasterCode
 		FROM(select  row_number() OVER (ORDER BY member_id DESC) line_number,participant_id,member_id as memberid,bank_name,bank_type,bank_code,record_status,remarks
 		,last_updated_on last_updated_on,legal_address,phone_number1,email_id1,status,created_on as created_on ,legal_address,phone_number1,email_id1,status
 		,rejected_onas rejected_on from participant_stg s where s.record_status in ('P','R')  AND ( (( s.participant_id) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_name) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_type) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.record_status) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_code) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.legal_address) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.phone_number1) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.email_id1) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) ))A
	</select>
	<select id="getPendingMembers" resultType="MemberDTO">
		SELECT a.participant_id as participantId,a.memberid as memberId,a.bank_name as memberName,a.bank_type as memberType,a.bank_code as bankMasterCode
		,a.record_status as recordStatus,a.record_status  as rejectReason,a.last_updated_on as lastUpdatedOn,a.status as status,a.legal_address as bnkAdd
		,a.email_id1 as bnkEmail,a.phone_number1 as bnkPhone,a.created_on as createdOn,a.rejected_on as rejectedOn  
		FROM(
		select  row_number() OVER (ORDER BY member_id DESC) line_number,participant_id,member_id as memberid
		,bank_name,bank_type,bank_code,record_status,remarks
		,last_updated_on last_updated_on,legal_address,phone_number1,email_id1,status
		,created_on as created_on
		,rejected_on as rejected_on from participant_stg s where s.record_status in ('P','R')  AND ( (( s.participant_id) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_name) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_type) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.record_status) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_code) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.legal_address) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.phone_number1) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.email_id1) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) ))A  order by line_number
	</select>
	<select id="getStateNameList" resultType="StateDTO">
		select state.state_id as stateId,state.state_name as stateName from state inner join participant_stg on 
			participant_stg.state_id = state.state_id  where participant_stg.participant_id = #{participantId}			
	</select>
	<select id="getCityNameList" resultType="CityDTO">
		select city.city_id as cityId,city.city_name as cityName from city inner join participant_stg 
			on participant_stg.city_id = city.city_id  where participant_stg.participant_id = #{participantId}
	</select>
	<select id="getCountNameList" resultType="CountryDTO">
		select country.country_id as countryId,country.country_name as countryName from country inner join participant_stg
			on participant_stg.country_id = country.country_id
			where participant_stg.participant_id = #{participantId}
	</select>
	<select id="getGstCountryNameList" resultType="CountryDTO">
		select country.country_id as countryId,country.country_name as countryName from country inner join participant_stg
			on participant_stg.gst_country = country.country_id
			where participant_stg.participant_id = #{participantId}
	</select>
	<select id="getGstStateNameList" resultType="StateDTO">
		select state.state_id as stateId,state.state_name as stateName from state inner join participant_stg on
			participant_stg.gst_state = state.state_id where participant_stg.participant_id = #{participantId}
	</select>
	<select id="getGstCityNameList" resultType="CityDTO">
		select city.city_id as cityId,city.city_name as cityName from city inner join participant_stg
			on participant_stg.gst_city = city.city_id where participant_stg.participant_id = #{participantId}
	</select>
	<select id="getMemberCount" resultType="Integer">
		SELECT COUNT(*) from participant_stg WHERE  participant_id = #{participantId}
	</select>
	<select id="fetchMemberIdSeq" resultType="int">
		SELECT nextval('member_id_seq')
	</select>
	<insert id="addAcquirerBinListMainTable">
			insert into membin_details (bin_id,member_id,bank_group,acquirer_id,domain_usage,product_type,settlement_bin,
					participant_id,status,created_by,created_on,bin_type,activation_date,deactivation_date,offline_allowed,last_updated_by,last_updated_on)
			values <foreach  collection='acqBinList' item='bin' separator=','>
			(#{bin.binId}, #{bin.memberId},#{bin.acqBankGroup}, #{bin.acquirerId},
					#{bin.acqDomainUsage} , #{bin.acqProductType} , #{bin.acqSettlementBin},
					#{bin.participantId}, #{bin.status},#{bin.createdBy},#{bin.createdOn},'A',#{bin.acqFrmDate},#{bin.acqToDate},#{bin.offlineAllowed},#{bin.lastUpdatedBy},#{bin.lastUpdatedOn})
					</foreach>
			
	</insert>
	<insert id="addIssBinListMainTable">
	insert into membin_details (bin_id,member_id,bank_group,bin_type,bin_number,
			low_bin,high_bin,pan_length,bin_card_type,bin_product_type,bin_card_variant,bin_card_brand,
			message_type,card_technology,authentication_mechanism,participant_id,
			domain_usage,created_by,created_on,settlement_bin,product_type,
			sub_scheme,card_sub_variant,program_details,status,form_factor,activation_date,deactivation_date,offline_allowed,features,last_updated_by,last_updated_on)
			values <foreach  collection='issBinList' item='issBin' separator=','>
			(#{issBin.binId},#{issBin.memberId},#{issBin.issBankGroup}, #{issBin.binType},
					#{issBin.binNumber},#{issBin.lowBin},#{issBin.highBin},
					#{issBin.panLength},#{issBin.binCardType}, #{issBin.binProductType},
					#{issBin.binCardVariant},#{issBin.binCardBrand}, #{issBin.messageType},
					#{issBin.cardTechnology},#{issBin.authMechanism}, #{issBin.participantId},
					#{issBin.issDomainUsage} ,#{issBin.createdBy} ,#{issBin.createdOn},
					#{issBin.issSettlementBin}, #{issBin.issProductType}, #{issBin.subScheme},
					#{issBin.cardSubVariant},#{issBin.programDetails},#{issBin.status},#{issBin.formFactor},#{issBin.issFrmDate},#{issBin.issToDate},#{issBin.offlineAllowed},#{issBin.featureIssBin},#{issBin.lastUpdatedBy},#{issBin.lastUpdatedOn})
			</foreach>
			
	</insert>
	<select id="getFinalMemberList" resultType="MemberDTO">
		select a.memberid as memberId,a.participant_id as participantId,a.bank_name as participantName,a.bank_type as bankType,a.bank_code as bankCode
		,a.created_on as createdOn,a.last_updated_on as lastUpdatedOn,a.status as status,a.legal_address as bnkAdd
		,a.email_id1 as bnkEmail,a.phone_number1 as bnkPhone
		 from(select  ROW_NUMBER() OVER (ORDER BY s.member_id DESC) LINE_NUMBER,s.bank_code,s.bank_type,s.member_id as memberid,
		 s.participant_id,s.bank_name,s.created_on as created_on,s.request_state,s.status,s.legal_address,s.phone_number1,s.email_id1,
		 s.last_updated_on as last_updated_on from participant s  where  s.status = 'A' AND
		  ( (( s.participant_id) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_name) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_type) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_code) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.legal_address) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.phone_number1) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.email_id1) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) ) )A 
	</select>
	<select id="getTempMemberList" resultType="MemberDTO">
		select bank_code as bankCode,bank_type as bankType, member_id as memberId,participant_id as participantId,bank_name as participantName,created_on as createdOn,request_state as requestState,record_status from participant_stg where record_status in ('P','R')
	</select>
	<update id="updateSettlementBin">
		update participant_settlementbin_stg set is_default =#{isDefault}, settlement_currency=#{settlementCurrency}, status=#{status},clearing_agency_type=#{clearingAgencyType},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn} where settlement_bin_no = #{settlementBinNumber}
	</update>
	<update id="updateAcqBin">
	 update membin_details_stg set 
		bank_group = #{acqBankGroup},settlement_bin=#{acqSettlementBin},
	  	product_type=#{acqProductType},domain_usage=#{acqDomainUsage},
	 	offline_allowed=#{offlineAllowed},activation_date=#{acqFrmDate},
	  	deactivation_date=#{acqToDate},
	 	last_updated_by=#{lastUpdatedBy}, status=#{status},last_updated_on=#{lastUpdatedOn}
	 	,current_deleted=#{currentDeleted} 
 	  where acquirer_id=#{acquirerId}
 </update>
	<update id="updateMemberBin">
		update membin_details_stg set bank_group=#{acqBankGroup},settlement_bin=#{settlementBin},product_type=#{productType},
		domain_usage=#{domainUsage},acquirer_id= #{acquirerId},offline_allowed=#{offlineAllowed},activation_date=#{acqFrmDate},
		deactivation_date=#{acqToDate},last_updated_on=now(),last_updated_by=#{lastUpdatedBy} where acquirer_id=#{oldAcquirerId}
	</update>
	<update id="updateIssMemberBin">
		update membin_details_stg set features=#{featureIssBin}, bank_group=#{issBankGroup},bin_number=#{binNumber},bin_length=#{binLength},
		additional_params=#{additionalParams}, low_bin=#{lowBin},high_bin=#{highBin},pan_length=#{panLength},bin_card_type=#{binCardType},
		bin_product_type=#{binProductType},bin_card_variant=#{binCardVariant},bin_card_brand=#{binCardBrand},
		domain_usage=#{issDomainUsage},message_type=#{messageType},card_technology=#{cardTechnology},
		authentication_mechanism=#{authMechanism},product_type=#{issProductType},sub_scheme=#{subScheme},
		card_sub_variant=#{cardSubVariant},program_details=#{programDetails},form_factor=#{formFactor},
		settlement_bin=#{issSettlementBin},activation_date=#{issFrmDate},deactivation_date=#{issToDate},
		last_updated_on=#{lastUpdatedOn},last_updated_by=#{lastUpdatedBy},offline_allowed=#{offlineAllowed}, status=#{status} 
		,current_deleted=#{currentDeleted},bin_type=#{issBinType}<if test = "isInternational">,markup =#{markUp},network_license_id =#{ networkLicenseId},network_issuer_id =#{ networkIssId}</if>
		where bin_number=#{binNumber}  and status!='D';
	</update>
	<select id="getMemberFinal" resultType="MemberDTO">
		SELECT member_id as memberId, bank_name as memberName, bank_type as memberType, PARTICIPANT_id as participantId, bank_code as bankCode, created_on FROM participant_stg V
	</select>
	<select id="getMemberEdit" resultType="MemberOnBoardingDTO">
	SELECT 
	  ParticipantStg.status as isActive, 
	  ParticipantStg.status as status, 
	  ParticipantStg.ifsc_code as ifscCode, 
	  ParticipantStg.nfs_id as participantIdNFS, 
	  ParticipantStg.current_acct_id as currentAccNumber, 
	  ParticipantStg.savings_acct_id as savingsAccNumber,
	  <if test = "isInternational">
	  ParticipantStg.currency_conversion_by as currencyConversionBy,
	  ParticipantStg.conversion_master as currencyConversionType,
	  ParticipantStg.forex_id as forexId,
	  ParticipantStg.is_npci_sett as isType,
	</if>
	  ParticipantStg.sub_net as subNet,  
	  ContactStg.particpant_contact_id as participantContactId, 
	  ContactStg.zip_code as cntPinCode, 
	  ContactStg.legal_address as cntAdd1, 
	  ContactStg.address_type as addressType, 
	  ContactStg.name as cntChkrName, 
	  ContactStg.phone_number as cntPhone, 
	  ContactStg.mobile_number as cntMobile, 
	  ContactStg.fax_number as cntFax, 
	  ContactStg.country_id as cntCountry, 
	  ContactStg.state_id as cntState, 
	  ContactStg.city_id as cntCity, 
	  ContactStg.created_by as cntCreatedBy, 
	  upper(ParticipantStg.gst_in) as gstIn, 
	  ContactStg.email_id as cntEmail, 
	  ContactStg.auth_officer_desg as cntDesignation, 
	  ParticipantStg.gst_address as gstAdd, 
	  ParticipantStg.gst_country as gstCountry, 
	  ParticipantStg.gst_state as gstState, 
	  ParticipantStg.gst_city as gstCity, 
	  ParticipantStg.gst_code as gstPincode, 
	  ParticipantStg.bank_code as currentAccNumber, 
	  ParticipantStg.member_id as memberId, 
	  ParticipantStg.bank_code as bankMasterCode, 
	  ParticipantStg.bank_type as memberType, 
	  ParticipantStg.bank_name as memberName, 
	  ParticipantStg.bank_sector as bankSector, 
	  ParticipantStg.rtgs_code as rtgsCode, 
	  ParticipantStg.participant_id as participantId, 
	  ParticipantStg.phone_number1 as bnkPhone, 
	  ParticipantStg.phone_number2 as bnkPhone2, 
	  ParticipantStg.mobile_number1 as bnkMobile, 
	  ParticipantStg.mobile_number2 as bnkMobile2, 
	  ParticipantStg.email_id1 as bnkEmail, 
	  ParticipantStg.email_id2 as bnkEmail2, 
	  ParticipantStg.legal_address as bnkAdd, 
	  ParticipantStg.country_id as bnkCountry, 
	  ParticipantStg.state_id as bnkState, 
	  ParticipantStg.zip_code as bnkPincode, 
	  ParticipantStg.city_id as bnkCity, 
	  ParticipantStg.request_state as requestState, 
	  ParticipantStg.record_status as recordStatus, 
	  ParticipantStg.unique_bank_name as uniqueBnk, 
	  ParticipantStg.parent_participant_id as parentParticipantId, 
	  ParticipantStg.website as webSite, 
	  ParticipantStg.created_by as createdBy, 
	  ParticipantStg.last_updated_by as lastUpdatedBy,
	  ParticipantStg.last_updated_on as lastUpdatedOn,  
	  ParticipantStg.last_operation as lastOperation,  
	  ParticipantStg.created_on createdOn ,
	  ParticipantStg.checker_comments as checkerComments,
	  participantStg.domestic_created_date as domesticCreatedDate,
	  participantStg.domestic_flag as domesticFlag
	  ,bnkcnt.country_name as bnkCountryName
	  ,gntcnt.country_name as gstCountryName
	  ,cttcnt.country_name as cntCountryName
	  ,bnkstt.state_name as bnkStateName
	  ,gststt.state_name as gstStateName
	  ,cttstt.state_name as cntStateName
	  ,bnkct.city_name as bnkCityName
	  ,gstct.city_name as gstCityName
	  ,cttct.city_name as cntCityName,
	  parentParticipant.bank_name as parentParticipantName,participantStg.MAX_USER_COUNT as maxUser
	FROM 
	  participant_stg participantStg 
	  left join participant_contact_stg contactStg on ContactStg.participant_id = ParticipantStg.participant_id 
	  left join country bnkcnt on bnkcnt.country_id=ParticipantStg.country_id
	  left join country gntcnt on gntcnt.country_id=ParticipantStg.gst_country
	  left join country cttcnt on cttcnt.country_id=ContactStg.country_id
	  left join state bnkstt on bnkstt.state_id=ParticipantStg.state_id
	  left join state gststt on gststt.state_id=ParticipantStg.gst_state
	  left join state cttstt on cttstt.state_id=ContactStg.state_id
	  left join city bnkct on bnkct.city_id=ParticipantStg.city_id
	  left join city gstct on gstct.city_id=ParticipantStg.gst_city
	  left join city cttct on cttct.city_id=ContactStg.city_id
	  left join ifsc Ifsc on 
	    ParticipantStg.ifsc_code = Ifsc.ifsc_code 
	  left join participant parentParticipant on  participantStg.parent_participant_id = parentParticipant.participant_id 
	WHERE 
	  ParticipantStg.participant_id  = #{participantId}
	</select>
	<select id="getMemberMainView" resultType="MemberOnBoardingDTO">
	SELECT 
	  participant.status as isActive, 
	   participant.status as status, 
	  participant.ifsc_code as ifscCode, 
	  participant.nfs_id as participantIdNFS, 
	  participant.current_acct_id as currentAccNumber, 
	  participant.savings_acct_id as savingsAccNumber,
	  participant.sub_net as subNet,  
	  contact.particpant_contact_id as participantContactId, 
	  contact.zip_code as cntPinCode, 
	  contact.legal_address as cntAdd1, 
	  contact.address_type as addressType, 
	  contact.name as cntChkrName, 
	  contact.phone_number as cntPhone, 
	  contact.mobile_number as cntMobile, 
	  contact.fax_number as cntFax, 
	  contact.country_id as cntCountry, 
	  contact.state_id as cntState, 
	  contact.city_id as cntCity, 
	  contact.created_by as cntCreatedBy, 
	  upper(participant.gst_in) as gstIn, 
	  contact.email_id as cntEmail, 
	  contact.auth_officer_desg as cntDesignation, 
	  participant.gst_address as gstAdd, 
	  participant.gst_country as gstCountry, 
	  participant.gst_state as gstState, 
	  participant.gst_city as gstCity, 
	  participant.gst_code as gstPincode, 
	  participant.bank_code as currentAccNumber, 
	  participant.member_id as memberId, 
	  participant.bank_code as bankMasterCode, 
	  participant.bank_type as memberType, 
	  participant.bank_name as memberName, 
	  participant.bank_sector as bankSector, 
	  participant.rtgs_code as rtgsCode, 
	  participant.participant_id as participantId, 
	  participant.phone_number1 as bnkPhone, 
	  participant.phone_number2 as bnkPhone2, 
	  participant.mobile_number1 as bnkMobile, 
	  participant.mobile_number2 as bnkMobile2, 
	  participant.email_id1 as bnkEmail, 
	  participant.email_id2 as bnkEmail2, 
	  participant.legal_address as bnkAdd, 
	  participant.country_id as bnkCountry, 
	  participant.state_id as bnkState, 
	  participant.zip_code as bnkPincode, 
	  participant.city_id as bnkCity, 
	  participant.unique_bank_name as uniqueBnk, 
	  participant.parent_participant_id as parentParticipantId, 
	  participant.website as webSite, 
	  participant.created_by as createdBy, 
	  participant.last_updated_by as lastUpdatedBy, 
	  participant.created_on createdOn 
	  ,bnkcnt.country_name as bnkCountryName
	  ,gntcnt.country_name as gstCountryName
	  ,cttcnt.country_name as cntCountryName
	  ,bnkstt.state_name as bnkStateName
	  ,gststt.state_name as gstStateName
	  ,cttstt.state_name as cntStateName
	  ,bnkct.city_name as bnkCityName
	  ,gstct.city_name as gstCityName
	  ,cttct.city_name as cntCityName,
	  participantStg.record_status as recordStatus,
	  <if test = "isInternational">participant.currency_conversion_by as currencyConversionBy,participant.conversion_master as currencyConversionType,
	  participant.forex_id as forexId,participant.is_npci_sett as isType , </if>
	  parentParticipant.bank_name as parentParticipantName,participant.MAX_USER_COUNT as maxUser
	FROM 
	  participant participant
	  inner join participant_stg participantStg on participant.participant_id =  participantStg.participant_id
	  left join participant_contact contact on contact.participant_id = participant.participant_id 
	  left join country bnkcnt on bnkcnt.country_id=participant.country_id
	  left join country gntcnt on gntcnt.country_id=participant.gst_country
	  left join country cttcnt on cttcnt.country_id=contact.country_id
	  left join state bnkstt on bnkstt.state_id=participant.state_id
	  left join state gststt on gststt.state_id=participant.gst_state
	  left join state cttstt on cttstt.state_id=contact.state_id
	  left join city bnkct on bnkct.city_id=participant.city_id
	  left join city gstct on gstct.city_id=participant.gst_city
	  left join city cttct on cttct.city_id=contact.city_id
	  left join ifsc Ifsc on  participant.ifsc_code = Ifsc.ifsc_code 
	  left join participant parentParticipant on  participant.parent_participant_id = parentParticipant.participant_id 
	WHERE 
	  participant.participant_id = #{participantId}
	</select>
	<update id="updateMemberInfo">
		update participant_stg set  <if test = "isInternational">currency_conversion_by=#{currencyConversionBy},conversion_master=#{currencyConversionType},forex_id=#{forexId},is_npci_sett = #{isType} , </if> bank_type=#{memberType},unique_bank_name=#{uniqueBnk},bank_name=#{memberName},ifsc_code=#{ifscCode},bank_sector=#{bankSector},phone_number1=#{bnkPhone},phone_number2=#{bnkPhone2},mobile_number1=#{bnkMobile},mobile_number2=#{bnkMobile2},email_id1=#{bnkEmail},email_id2=#{bnkEmail2},legal_address=#{bnkAdd},country_id=#{bnkCountry},state_id=#{bnkState},city_id=#{bnkCity},zip_code=#{bnkPincode},gst_in=#{gstIn},gst_address=#{gstAdd},gst_city=#{gstCity},gst_country=#{gstCountry},gst_state=#{gstState},gst_code=#{gstPincode},request_state=#{requestState},record_status=#{recordStatus},parent_participant_id=#{parentParticipantId},last_updated_on=#{lastUpdatedOn},last_updated_by=#{lastUpdatedBy},website=#{webSite}, nfs_id=#{participantIdNFS},sub_net=#{subNet}, last_operation=#{lastOperation}, checker_comments ='',  MAX_USER_COUNT=#{maxUser} where participant_id = #{participantId}
	</update>
	<update id="updateMemberInfoMain">
		update participant set  bank_type=#{memberType},unique_bank_name=#{uniqueBnk},bank_name=#{memberName},ifsc_code=#{ifscCode},bank_sector=#{bankSector},phone_number1=#{bnkPhone},phone_number2=#{bnkPhone2},mobile_number1=#{bnkMobile},mobile_number2=#{bnkMobile2},email_id1=#{bnkEmail},email_id2=#{bnkEmail2},legal_address=#{bnkAdd},country_id=#{bnkCountry},state_id=#{bnkState},city_id=#{bnkCity},zip_code=#{bnkPincode},gst_in=#{gstIn},gst_address=#{gstAdd},gst_city=#{gstCity},gst_country=#{gstCountry},gst_state=#{gstState},gst_code=#{gstPincode},parent_participant_id=#{parentParticipantId},last_updated_on=#{lastUpdatedOn},last_updated_by=#{lastUpdatedBy},website=#{webSite}, nfs_id=#{participantIdNFS},sub_net=#{subNet},MAX_USER_COUNT=#{maxUser} <if test = "isInternational">,currency_conversion_by=#{currencyConversionBy},conversion_master=#{currencyConversionType},forex_id=#{forexId},is_npci_sett=#{isType}</if> where participant_id = #{participantId}
	</update>
	<update id="updateMemberContactInfo">
		update participant_contact_stg set legal_address=#{cntAdd1},address_type= #{addressType},name=#{cntChkrName},phone_number=#{cntPhone},mobile_number=#{cntMobile},fax_number=#{cntFax},country_id=#{cntCountry},state_id=#{cntState},city_id=#{cntCity},zip_code=#{cntPincode},email_id=#{cntEmail},auth_officer_desg=#{cntDesignation} where participant_id = #{participantId}
	</update>
	<update id="updateMemberContactInfoMain">
		update participant_contact set legal_address=#{cntAdd1},address_type= #{addressType},name=#{cntChkrName},phone_number=#{cntPhone},mobile_number=#{cntMobile},fax_number=#{cntFax},country_id=#{cntCountry},state_id=#{cntState},city_id=#{cntCity},zip_code=#{cntPincode},email_id=#{cntEmail},auth_officer_desg=#{cntDesignation} where participant_id = #{participantId}
	</update>
	<update id="updateMemberStgInfo">
		update participant_stg set record_status = #{recordStatus}, request_state = #{requestState}, last_updated_on=now(),last_updated_by=#{lastUpdatedBy} where participant_id = #{participantId}	
	</update>
	<select id="getMembersSB" resultType="MemberDTO">
			SELECT participant_id as participantId,ifsc_code as ifscCode, bank_name as participantName,ROW_NUMBER() OVER (ORDER BY bank_name ) LINE_NUMBER FROM participant where bank_type='S'				
	</select>
	<select id="fetchBinIdSeq" resultType="int">
			SELECT nextval('bin_id_seq')
	</select>
	<update id="updateMemberMainTable">
		update participant set  website=#{webSite}, unique_bank_name=#{uniqueBnk},member_id=#{memberId}, bank_code=#{bankMasterCode},bank_type=#{memberType},bank_name=#{memberName},bank_sector=#{bankSector}, rtgs_code=#{rtgsCode},participant_id=#{participantId},phone_number1=#{bnkPhone},phone_number2=#{bnkPhone2},mobile_number1=#{bnkMobile},mobile_number2=#{bnkMobile2},email_id1=#{bnkEmail},email_id2=#{bnkEmail2},legal_address=#{bnkAdd},country_id=#{bnkCountry},state_id=#{bnkState},zip_code= #{bnkPincode},city_id= #{bnkCity},gst_in= #{gstIn},gst_address=#{gstAdd},gst_city=#{gstCity},gst_country=#{gstCountry},gst_state=#{gstState},request_state=#{requestState},parent_participant_id=#{parentParticipantId},last_updated_on=now(),last_updated_by=#{lastUpdatedBy},remarks= #{rejectReason}, nfs_id=#{participantIdNFS},sub_net=#{subNet},MAX_USER_COUNT=#{maxUser}  where participant_id = #{participantId}
	</update>
	<update id="updateMemberContactFinal">
		update participant_contact set auth_officer_desg=#{cntDesignation} , legal_address=#{cntAdd1},address_type= #{addressType},name=#{cntChkrName},phone_number=#{cntPhone},mobile_number=#{cntMobile},fax_number=#{cntFax},country_id=#{cntCountry},state_id=#{cntState},city_id=#{cntCity},zip_code=#{cntPincode} where participant_id = #{participantId}
	</update>
	<update id="deleteMemberContact">
		delete from  participant_contact_stg where participant_id = #{participantId}
		</update>
	<delete id="deleteMemberBin">
		delete from membin_details where participant_id = #{participantId}
	</delete>
	<delete id="deleteUserBinMain">
		Delete from user_bin where participant_id=#{participantId} and bin_id=#{binId}
	</delete>
	<delete id="deleteUserBinStg">
		Delete from user_bin_stg where participant_id=#{participantId} and bin_id=#{binId}
	</delete>
	<delete id="deleteUserBinMainWithParticipantID">
		Delete from user_bin where participant_id=#{participantId} 
	</delete>
	<delete id="deleteUserBinStgWithParticipantID">
		Delete from user_bin_stg where participant_id=#{participantId} 
	</delete>
	<select id="getSettlmentBinsForOtherParticipants" resultType="SettlementBinDTO">
			select participant_id as participantId, settlement_bin_no as settlementBinNumber FROM participant_settlementbin_stg WHERE  participant_id != #{participantId} and settlement_bin_no  in <foreach item='item' index='index' collection='settlementBinNumberList' open='(' separator=',' close=')'>#{item}</foreach> AND status!='D'
	</select>
	<select id="getSettlmentBinsForParticipants" resultType="SettlementBinDTO">
			select participant_id as participantId, settlement_bin_no as settlementBinNumber FROM participant_settlementbin_stg WHERE  participant_id = #{participantId}  AND status!='D'
	</select>
	<select id="getAcquirerBinsForOtherParticipants" resultType="BinDetailsDTO">
			select participant_id as participantId, acquirer_id as acquirerId FROM membin_details_stg 
			WHERE  participant_id != #{participantId} 
			<!--  and  bin_type IN ('A')--> 
			and acquirer_id  in <foreach item='item' index='index' collection='acqBinList' open='(' separator=',' close=')'>#{item}</foreach> 
			AND status!='D'
	</select>
	<select id="getAcquirerBinsForParticipants" resultType="BinDetailsDTO">
			select participant_id as participantId, acquirer_id as acquirerId FROM membin_details_stg WHERE  participant_id = #{participantId}  AND status!='D'
	</select>
	<select id="getAcquirerBinsForParticipantsDuplicateCheck" resultType="BinDetailsDTO">
			select participant_id as participantId, acquirer_id as acquirerId FROM membin_details_stg WHERE  participant_id = #{participantId}  
	</select>
	<select id="getIssuerBinsForOtherParticipants" resultType="BinDetailsDTO">
			select participant_id as participantId, bin_number as binNumber FROM membin_details_stg WHERE  participant_id != #{participantId} 
			<!--  and   bin_type IN ('I', 'T')--> 
			and bin_number  in <foreach item='item' index='index' collection='issBinList' open='(' separator=',' close=')'>#{item}</foreach> 
			AND status!='D'
	</select>
	<select id="getIssuerBinsForParticipants" resultType="BinDetailsDTO">
			select participant_id as participantId, bin_number as binNumber FROM membin_details_stg WHERE  participant_id = #{participantId}  AND status!='D'
	</select>
	<select id="getIssuerBinsForParticipantsDuplicateCheck" resultType="BinDetailsDTO">
			select participant_id as participantId, bin_number as binNumber FROM membin_details_stg WHERE  participant_id = #{participantId}  AND status!='D'
	</select>
	<select id="duplicateSettlementBinCheck" resultType="int">
			select count(*) REC_COUNT FROM participant_settlementbin_stg WHERE settlement_bin_no = #{settleBinNo} AND status!='D'
	</select>
	<select id="duplicateAcqBinCheck" resultType="int">
			select sum(REC_COUNT) REC_COUNT from ((SELECT count(*) REC_COUNT from membin_details WHERE participant_id != #{participantId} AND status!='D'  AND (acquirer_id= #{acquirerId} or bin_number= #{acquirerId}) ) UNION ALL ( SELECT count(*) REC_COUNT FROM membin_details_stg WHERE status!='D' AND  (acquirer_id= #{acquirerId} or bin_number= #{acquirerId}))) A
	</select>
	<select id="duplicateIssBinCheck" resultType="int">
			select sum(REC_COUNT) REC_COUNT from ((SELECT count(*) REC_COUNT from membin_details WHERE participant_id != #{participantId} AND status!='D'  AND (bin_number= #{binNo} or acquirer_id= #{binNo}))  UNION ALL  (SELECT count(*) REC_COUNT FROM membin_details_stg WHERE  status!='D' and ( bin_number= #{binNo} or acquirer_id= #{binNo}))) A		
	</select>
	<update id="updateNetBinRange">
		UPDATE network_bin_details SET status=#{status} , dxs_code=#{dxsCode},license_id=#{licenseId} WHERE bin_number=#{binNumber};	
	</update>
	<update id="deleteIssBinStg">
		update membin_details_stg set status=#{status} where bin_number=#{binNo} and participant_id = #{participantId}	
	</update>
	<update id="deleteAcqBinStg">
		update membin_details_stg set status=#{status} where acquirer_id=#{acqId} and participant_id = #{participantId}	
	</update>
	<delete id="deleteIssBinFinal">
		delete from membin_details where bin_number=#{binNo} and participant_id = #{participantId}
	</delete>
	<delete id="deleteAcqBinFinal">
		delete from membin_details where acquirer_id=#{acqId} and participant_id = #{participantId}
	</delete>
	<update id="deleteSettlementBinStg">
		update membin_details_stg set status=#{status}  where settlement_bin_no=#{settleBinNo} and participant_id = #{participantId}
	</update>
	<delete id="deleteDocumentMain">
		delete from participant_documents where participant_id = #{participantId}
	</delete>
	<delete id="deleteSettlementBinFinal">
		delete from participant_settlementbin where participant_id = #{participantId}
	</delete>
	<update id="blockUnblockIssBinStg">
			update membin_details_stg set status=#{status} where bin_number=#{binNo} and participant_id = #{participantId}	
	</update>
	<update id="blockUnblockAcqBinStg">
			update membin_details_stg set status=#{status} where acquirer_id=#{acqId} and participant_id = #{participantId}
	</update>
	<update id="blockUnblockSettlementBinStg">
			update participant_settlementbin_stg set status=#{status} where settlement_bin_no=#{settleBinNo} and participant_id = #{participantId}
	</update>
	<delete id="discardMemberData">
		delete from participant_stg where participant_id = #{participantId}	
	</delete>
	<update id="deleteMemberStg">
			delete from participant_stg where participant_id = #{participantId}	
	</update>
	<update id="updateMemberInfoStg">
			update participant_stg set unique_bank_name=#{uniqueBnk},member_id=#{memberId}, bank_code=#{bankMasterCode},bank_type=#{memberType},bank_name=#{memberName},bank_sector=#{bankSector}, rtgs_code=#{rtgsCode},participant_id=#{participantId},phone_number1=#{bnkPhone},phone_number2=#{bnkPhone2},mobile_number1=#{bnkMobile},mobile_number2=#{bnkMobile2},email_id1=#{bnkEmail},email_id2=#{bnkEmail2},legal_address=#{bnkAdd},country_id=#{bnkCountry},state_id=#{bnkState},zip_code= #{bnkPincode},city_id= #{bnkCity},gst_in= #{gstIn},gst_address=#{gstAdd},gst_city=#{gstCity},gst_country=#{gstCountry},gst_state=#{gstState},status=#{status},request_state=#{requestState},record_status=#{recordStatus},parent_participant_id=#{parentParticipantId},last_updated_on=now(),last_updated_by=#{lastUpdatedBy}, nfs_id=#{participantIdNFS}, sub_net=#{subNet} where participant_id = #{participantId}
	</update>
	<update id="updateMemberContactInfoStg">
			update participant_contact_stg set legal_address=#{cntAdd1},address_type= #{addressType},name=#{cntChkrName},phone_number=#{cntPhone},mobile_number=#{cntMobile},fax_number=#{cntFax},country_id=#{cntCountry},state_id=#{cntState},city_id=#{cntCity},zip_code=#{cntPincode} where participant_id = #{participantId}
	</update>
	<select id="getMemberFinalDiscard" resultType="MemberOnBoardingDTO">
			
				select 	  participant.status as isActive, 	  participant.ifsc_code as ifscCode, 	  participant.nfs_id as participantIdNFS,
 	  participant.current_acct_id as currentAccNumber, 	  participant.savings_acct_id as savingsAccNumber,
	  participant.sub_net as subNet,  	  contact.zip_code as cntPinCode, 	  contact.legal_address as cntAdd1, 	  contact.address_type as addressType
	  , 	  contact.name as cntChkrName, 	  contact.phone_number as cntPhone, 	  contact.mobile_number as cntMobile, 	  contact.fax_number as cntFax
	  , 	  contact.country_id as cntCountry, 	  contact.state_id as cntState, 	  contact.city_id as cntCity, 	  contact.created_by as cntCreatedBy, 
	  upper(participant.gst_in) as gstIn, 	  contact.email_id as cntEmail, 	  contact.auth_officer_desg as cntDesignation, 	  participant.gst_address as gstAdd,
 	  participant.gst_country as gstCountry, 	  participant.gst_state as gstState, 	  participant.gst_city as gstCity, 	  participant.gst_code as gstPincode,
 	  participant.bank_code as currentAccNumber, 	  participant.member_id as memberId, 	  participant.bank_code as bankMasterCode,
 	  participant.bank_type as memberType, 	  participant.bank_name as memberName, 	  participant.bank_sector as bankSector, 	  participant.rtgs_code as rtgsCode, 
	  participant.participant_id as participantId, 	  participant.phone_number1 as bnkPhone, 	  participant.phone_number2 as bnkPhone2, 
	  participant.mobile_number1 as bnkMobile, 	  participant.mobile_number2 as bnkMobile2, 
	  participant.email_id1 as bnkEmail, 	  participant.email_id2 as bnkEmail2, 	  participant.legal_address as bnkAdd, 	 
	  participant.country_id as bnkCountry, 	  participant.state_id as bnkState, 
	  participant.zip_code as bnkPincode, 	  participant.city_id as bnkCity, 	  participant.unique_bank_name as uniqueBnk, 
	  participant.parent_participant_id as parentParticipantId, 	  participant.website as webSite, 
	  participant.created_by as createdBy, 	  participant.last_updated_by as lastUpdatedBy, 	  participant.created_on createdOn
	  , 	  participant.MAX_USER_COUNT maxUser 
	  ,	  contact.particpant_contact_id as participantContactId, 
FROM participant  left  join participant_contact contact on participant.participant_id=contact.participant_id 
		where participant.participant_id=#{participantId}				
	</select>
	<update id="deleteMemberBinStgAll">
			delete from membin_details_stg where participant_id = #{participantId}	
	</update>
	<update id="deleteSettlementBinStgAll">
			delete from participant_settlementbin_stg where participant_id = #{participantId}	
	</update>
	<select id="getsettlementBinsListAll" resultType="SettlementBinDTO">
			SELECT participant_id as participantId,settlement_bin_no as settlementBinNumber,is_default as isDefault,status as status,settlement_bin_id as settlementBinId,is_complete as isComplete
				,is_active	as isActive,created_by as createdBy,created_on as createdOn,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn
			 FROM participant_settlementbin WHERE participant_id = #{participantId}
	</select>
	<select id="getBinDetailsFinal" resultType="BinDetailsDTO">
			SELECT activation_date as issFrmDate,deactivation_date as issToDate,bin_id as binId,participant_id as participantId,
			domain_usage as domainUsage,bank_group as bankGroup,offline_allowed as offlineAllowed,bin_type as binType,bin_number as binNumber,
			low_bin as lowBin,high_bin as highBin,pan_length as panLength,bin_card_type as binCardType,bin_product_type as binProductType,
			bin_card_variant as binCardVariant,bin_card_brand as binCardBrand,message_type as messageType,card_technology as cardTechnology,
			authentication_mechanism as authMechanism,sub_scheme as subScheme,card_sub_variant as cardSubVariant,program_details as programDetails,
			form_factor as formFactor,features as featureIssBin,created_by,created_on,last_updated_by ,last_updated_on,status, member_id as memberid,
			product_type as issProductType,settlement_bin as settlementBin,acquirer_id as acquirerId,offline_allowed as offlineAllowed 
			FROM membin_details WHERE participant_id = #{participantId} and bin_type = #{binType} and status!='D'
	</select>
	<select id="Final" resultType="BinDetailsDTO">
				SELECT TO_CHAR(activation_date,'DD-MM-YYYY') as acqFrmDateStr,TO_CHAR(deactivation_date,'DD-MM-YYYY') as acqToDateStr,bin_id as binId,participant_id as participantId,domain_usage as domainUsage,bank_group as bankGroup,offline_allowed,bin_type,bin_number,bin_length,additional_params,low_bin,high_bin,pan_length,bin_card_type,bin_product_type as acqProductType,bin_card_variant as binCardVariant,bin_card_brand as binCardBrand,message_type as messageType,card_technology as cardTechnology,authentication_mechanism as authMechanism,sub_scheme as subScheme,card_sub_variant as cardSubVariant,program_details as programDetails,form_factor as formFactor,features,created_by,created_on,last_updated_by,last_updated_on,status, member_id as memberid,product_type as acqProductType,settlement_bin as settlementBin,acquirer_id as acquirerId,stip,features FROM membin_details_stg WHERE participant_id = #{participantId} and bin_type = #{binType} and status!='D' order by created_on
	</select>
	<insert id="addBinListStg">
	 insert into membin_details_stg (member_id,bank_group,bin_type,bin_number,
			low_bin,high_bin,pan_length,bin_card_type,bin_product_type,bin_card_variant,bin_card_brand,
			message_type,card_technology,authentication_mechanism,participant_id,
			domain_usage,created_by,created_on,settlement_bin,product_type,
			sub_scheme,card_sub_variant,program_details,status,form_factor,activation_date,deactivation_date,acquirer_id,offline_allowed)
			values<foreach  collection='binList' item='bin' separator=','>
			(#{bin.memberId},#{bin.issBankGroup}, #{bin.binType},#{bin.binNumber},#{bin.lowBin},#{bin.highBin},
					#{bin.panLength},#{bin.binCardType}, #{bin.binProductType},
					#{bin.binCardVariant},#{bin.binCardBrand}, #{bin.messageType},
					#{bin.cardTechnology},#{bin.authMechanism}, #{bin.participantId},
					#{bin.domainUsage} ,#{bin.createdBy} , #{bin.createdOn},
					#{bin.settlementBin}, #{bin.productType}, #{bin.subScheme},
					#{bin.cardSubVariant},#{bin.programDetails}, #{bin.status},#{bin.formFactor},#{bin.issFrmDate},#{bin.issToDate}, #{bin.acquirerId}, #{bin.isOfflineAllowed})
			</foreach>
	</insert>
	<insert id="addIssBinListStg">
<if test="isInternational">	 insert into membin_details_stg (member_id,bank_group,bin_type,bin_number,bin_length,additional_params,
			low_bin,high_bin,pan_length,bin_card_type,bin_product_type,bin_card_variant,bin_card_brand,
			message_type,card_technology,authentication_mechanism,participant_id,
			domain_usage,created_by,created_on,settlement_bin,product_type,
			sub_scheme,card_sub_variant,program_details,status,form_factor,activation_date,deactivation_date,acquirer_id,offline_allowed, features,markup,network_issuer_id,network_license_id)
			values<foreach  collection='binList' item='bin' separator=','>
			(#{bin.memberId},#{bin.issBankGroup}, #{bin.binType},#{bin.binNumber},#{bin.binLength},#{bin.additionalParams},#{bin.lowBin},#{bin.highBin},
					#{bin.panLength},#{bin.binCardType}, #{bin.binProductType},
					#{bin.binCardVariant},#{bin.binCardBrand}, #{bin.messageType},
					#{bin.cardTechnology},#{bin.authMechanism}, #{bin.participantId},
					#{bin.issDomainUsage} ,#{bin.createdBy} , #{bin.createdOn},
					#{bin.issSettlementBin}, #{bin.issProductType}, #{bin.subScheme},
					#{bin.cardSubVariant},#{bin.programDetails}, #{bin.status},#{bin.formFactor},#{bin.issFrmDate},#{bin.issToDate}, #{bin.acquirerId}, #{bin.offlineAllowed},#{bin.featureIssBin},#{bin.markUp},#{bin.networkIssId},#{bin.networkLicenseId})
			</foreach>
	</if>
	<if test="!isInternational">
	 insert into membin_details_stg (member_id,bank_group,bin_type,bin_number,bin_length,additional_params,
			low_bin,high_bin,pan_length,bin_card_type,bin_product_type,bin_card_variant,bin_card_brand,
			message_type,card_technology,authentication_mechanism,participant_id,
			domain_usage,created_by,created_on,settlement_bin,product_type,
			sub_scheme,card_sub_variant,program_details,status,form_factor,activation_date,deactivation_date,acquirer_id,offline_allowed, features)
			values<foreach  collection='binList' item='bin' separator=','>
			(#{bin.memberId},#{bin.issBankGroup}, #{bin.binType},#{bin.binNumber},#{bin.binLength},#{bin.additionalParams},#{bin.lowBin},#{bin.highBin},
					#{bin.panLength},#{bin.binCardType}, #{bin.binProductType},
					#{bin.binCardVariant},#{bin.binCardBrand}, #{bin.messageType},
					#{bin.cardTechnology},#{bin.authMechanism}, #{bin.participantId},
					#{bin.issDomainUsage} ,#{bin.createdBy} , #{bin.createdOn},
					#{bin.issSettlementBin}, #{bin.issProductType}, #{bin.subScheme},
					#{bin.cardSubVariant},#{bin.programDetails}, #{bin.status},#{bin.formFactor},#{bin.issFrmDate},#{bin.issToDate}, #{bin.acquirerId}, #{bin.offlineAllowed},#{bin.featureIssBin})
			</foreach>
	
	</if>
	</insert>
	<insert id="addAcqBinListStg">
		INSERT INTO membin_details_stg(member_id,
		  acquirer_id, bank_group, bin_type, 
		  domain_usage, participant_id, created_by, 
		  settlement_bin, product_type, status, 
		  offline_allowed, activation_date, deactivation_date, created_on
		) 
		VALUES<foreach  collection='binList' item='bin' separator=','>
			(#{bin.memberId},
		    #{bin.acquirerId},#{bin.acqBankGroup}, #{bin.binType}
		    ,#{bin.acqDomainUsage},#{bin.participantId}, #{bin.createdBy}, 
		    #{bin.acqSettlementBin}, #{bin.acqProductType},  #{bin.status}, 
		    #{bin.offlineAllowed}, #{bin.acqFrmDate}, #{bin.acqToDate}, #{bin.createdOn})
			</foreach>
		   
<!--  	 insert into membin_details_stg (member_id,bank_group,bin_type,bin_number,
			low_bin,high_bin,pan_length,bin_card_type,bin_product_type,bin_card_variant,bin_card_brand,
			message_type,card_technology,authentication_mechanism,participant_id,
			domain_usage,created_by,created_on,settlement_bin,product_type,
			sub_scheme,card_sub_variant,program_details,status,form_factor,activation_date,deactivation_date,acquirer_id,offline_allowed)
			values<foreach  collection='binList' item='bin' separator=','>
			(#{bin.memberId},#{bin.issBankGroup}, #{bin.binType},#{bin.binNumber},#{bin.lowBin},#{bin.highBin},
					#{bin.panLength},#{bin.binCardType}, #{bin.binProductType},
					#{bin.binCardVariant},#{bin.binCardBrand}, #{bin.messageType},
					#{bin.cardTechnology},#{bin.authMechanism}, #{bin.participantId},
					#{bin.domainUsage} ,#{bin.createdBy} , now(),
					#{bin.settlementBin}, #{bin.productType}, #{bin.subScheme},
					#{bin.cardSubVariant},#{bin.programDetails}, #{bin.status},#{bin.formFactor},#{bin.issFrmDate},#{bin.issToDate}, #{bin.acquirerId}, #{bin.isOfflineAllowed})
			</foreach>-->
	</insert>
	
	<insert id="addSettlementBinListStg">
			insert into participant_settlementbin_stg (settlement_bin_id, participant_id, settlement_bin_no,
					is_default, is_complete,is_active,created_by,created_on,status,settlement_currency )
			values  <foreach  collection='binList' item='bin' separator=','>
			( #{bin.settlementBinId}::numeric,#{bin.participantId}, #{bin.settlementBinNumber},
					#{bin.isDefault},#{bin.isComplete},#{bin.isActive},#{bin.createdBy},#{bin.createdOn}, #{bin.status},#{bin.settlementCurrency})
			</foreach>
	</insert>
	<select id="getSavedMemberList" resultType="MemberDTO">
			select a.memberid as memberId,a.participant_id as participantId,a.bank_name as memberName,
			a.bank_type as memberType,a.bank_code as bankMasterCode,a.record_status as recordStatus,a.created_on as createdOn
			,a.last_updated_on as lastUpdatedOn,a.status as status,a.legal_address as bnkAdd
			,a.email_id1 as bnkEmail,a.phone_number1 as bnkPhone,domestic_created_date as domesticCreatedDate
			 from(select  row_number() OVER (ORDER BY member_id DESC) line_number,participant_id,member_id as memberid,bank_name,bank_type,bank_code,record_status,legal_address,phone_number1,email_id1
			 ,last_updated_on as last_updated_on,status,created_on as created_on, domestic_created_date from participant_stg s where s.record_status='I'  AND ( (( s.participant_id) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_name) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_type) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.record_status) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.bank_code) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.legal_address) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.phone_number1) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) OR (( s.email_id1) LIKE COALESCE ( #{sSearch},'0') OR '0' LIKE COALESCE ( #{sSearch},'0')) ) )A 
			
	</select>
	<select id="getTotalMemberCount" resultType="long">
			select count(*) from participant_stg where record_status  in ('A')
	</select>
	<select id="getPendingMemberCount" resultType="long">
			 select count(*) from participant_stg where record_status in ('P','R')
	</select>
	<select id="getSavedMemberCount" resultType="long">
			 select count(*) from participant_stg where record_status='I'
	</select>
	<insert id="addSettlementBinFinal">
			insert into participant_settlementbin (settlement_bin_id, participant_id, settlement_bin_no, settlement_currency,
					is_default, is_complete,is_active,created_by,last_updated_by,status,created_on,last_updated_on)
			values <foreach  collection='binList' item='bin' separator=','>
			( #{bin.settlementBinId}::numeric,#{bin.participantId}, #{bin.settlementBinNumber},#{bin.settlementCurrency},
					#{bin.isDefault},#{bin.isComplete},#{bin.isActive},
					#{bin.createdBy},#{bin.lastUpdatedBy}, #{bin.status},#{bin.createdOn},#{bin.lastUpdatedOn})
			</foreach>
			
	</insert>
	<select id="checkSettlementBinReference" resultType="int">
			 select count(*) REC_COUNT FROM membin_details_stg WHERE settlement_bin = #{settleBinNo} and participant_id = #{participantId}
			 
	</select>
	<select id="getFileList" resultType="FileUploadDTO">
			SELECT document_id as documentId, document_path as documentPath, document_name as documentName, is_active as isActive, participant_id as participantId, created_by as createdBy, created_on as createdOn, last_updated_by as lastUpdatedBy, last_updated_on as lastUpdatedOn, status as status  FROM participant_documents_stg WHERE participant_id = #{participantId} and status != 'D'  
	</select>
		<select id="getFileListMain" resultType="FileUploadDTO">
			SELECT document_id as documentId, document_path as documentPath, document_name as documentName, is_active as isActive, participant_id as participantId, created_by as createdBy, created_on as createdOn, last_updated_by as lastUpdatedBy, last_updated_on as lastUpdatedOn, status as status FROM participant_documents WHERE participant_id = #{participantId} 
	</select>
	<delete id="deleteFileList">
		delete from participant_documents_stg where participant_id = #{participantId}	
	</delete>
	<delete id="deleteSettlementBin">
		delete from participant_settlementbin where participant_id = #{participantId}	
	</delete>
	<select id="getFeatureListForAddandEditMember" resultType="featureFeeDTO">
			select distinct feature as feature, card_type as cardType, card_variant as cardVariant from mcpr_feature_fee_config where status='A'
	</select>
	<select id="duplicateDataForSettlementBinCheck" resultType="int">
			select count(*) REC_COUNT FROM participant_settlementbin_stg WHERE settlement_bin_no = #{settleBinNo} and is_default=#{isDefault}
	</select>
	<select id="getUserBinStg" resultType="BinDTO">
			SELECT user_id as userId,bin_id as binId,participant_id as participantId,created_by as createdBy,created_on as createdOn,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn from user_bin_stg  where participant_id=#{participantId}
	</select>
	<select id="getUserBinMain" resultType="BinDTO">
			SELECT user_id as userId,bin_id as binId,participant_id as participantId,created_by as createdBy,created_on as createdOn,last_updated_by as lastUpdatedBy,last_updated_on as lastUpdatedOn from user_bin where participant_id=#{participantId}
	</select>
	<select id="getUserBinStgWithSettlementBin" resultType="BinDTO">
			SELECT a.user_id as userId,a.bin_id as binId,a.participant_id as participantId,a.created_by as createdBy,a.created_on as createdOn,a.last_updated_by as lastUpdatedBy,a.last_updated_on as lastUpdatedOn
			,b.settlement_bin_no as settlementBin 
			 from user_bin_stg  a 
			inner join participant_settlementbin b on a.bin_id =b.settlement_bin_id  
			where a.participant_id=#{participantId}
	</select>
	<select id="getUserBinStgWithAcquireBin" resultType="BinDTO">
			SELECT a.user_id as userId,a.bin_id as binId,a.participant_id as participantId,a.created_by as createdBy,a.created_on as createdOn,a.last_updated_by as lastUpdatedBy,a.last_updated_on as lastUpdatedOn
			,b.acquirer_id as acquirerId 
			 from user_bin_stg  a 
			inner join membin_details b on a.bin_id =b.bin_id  and bin_type='A' and b.status !='D'
			where a.participant_id=#{participantId}
	</select>
	<select id="getUserBinStgWithIssuerBin" resultType="BinDTO">
			SELECT a.user_id as userId,a.bin_id as binId,a.participant_id as participantId,a.created_by as createdBy,a.created_on as createdOn,a.last_updated_by as lastUpdatedBy,a.last_updated_on as lastUpdatedOn
			,b.bin_number as binNumber 
			 from user_bin_stg  a 
			inner join membin_details b on a.bin_id =b.bin_id  and bin_type='I' and b.status !='D'
			where a.participant_id=#{participantId}
	</select>
	<select id="getUserBinStgWithAcquireBinMain" resultType="BinDTO">
			SELECT a.user_id as userId,a.bin_id as binId,a.participant_id as participantId,a.created_by as createdBy,a.created_on as createdOn,a.last_updated_by as lastUpdatedBy,a.last_updated_on as lastUpdatedOn
			,b.acquirer_id as acquirerId 
			 from user_bin  a 
			inner join membin_details b on a.bin_id =b.bin_id  and bin_type='A' and b.status !='D'
			where a.participant_id=#{participantId}
	</select>
	<select id="getUserBinStgWithIssuerBinMain" resultType="BinDTO">
			SELECT a.user_id as userId,a.bin_id as binId,a.participant_id as participantId,a.created_by as createdBy,a.created_on as createdOn,a.last_updated_by as lastUpdatedBy,a.last_updated_on as lastUpdatedOn
			,b.bin_number as binNumber 
			 from user_bin  a 
			inner join membin_details b on a.bin_id =b.bin_id  and bin_type='I' and b.status !='D'
			where a.participant_id=#{participantId}
	</select>
	<insert id="addUserBinMain">
			insert into user_bin (user_id, participant_id, bin_id,
					created_by,created_on,last_updated_by,last_updated_on)
			values <foreach  collection='userbinMain' item='bin' separator=','>
			( #{bin.userId},#{bin.participantId}, #{bin.binId},

					#{bin.createdBy},#{bin.createdOn},#{bin.lastUpdatedBy}, now())
			</foreach>
			
	</insert>
	<insert id="addUserBinStg">
			insert into user_bin_stg (user_id, participant_id, bin_id,
					created_by,created_on)
			values <foreach  collection='userbin' item='bin' separator=','>
			( #{bin.userId},#{bin.participantId}, #{bin.binId},
					#{bin.createdBy},#{bin.createdOn})
			</foreach>
	</insert>
	<select id="getAcqBinListBulk" resultType="BinDetailsDTO">
		SELECT bin_id as binId,bank_group as acqBankGroup,acquirer_id as acquirerId,domain_usage as acqDomainUsage,product_type as acqProductType,settlement_bin as acqSettlementBin,participant_id as participantId,bin_type as binType,activation_date as acqFrmDate,deactivation_date as acqToDate,created_by as createdBy,created_on as createdOn,status FROM membin_details_stg  where  participant_id
			in <foreach item='item' index='index' collection='participantIdList' open='(' separator=',' close=')'>#{item}</foreach>    and bin_type = 'A' and status in <foreach item='item' index='index' collection='statusList' open='(' separator=',' close=')'>#{item}</foreach>  
	</select>
	<select id="getIssBinListBulk" resultType="BinDetailsDTO">
			SELECT bin_id as binId,activation_date as issFrmDate,deactivation_date as issToDate,participant_id as participantId,bin_type as binType,bank_group as issBankGroup,bin_number as binNumber,low_bin as lowBin,high_bin as highBin,pan_length as panLength,bin_card_type as binCardType,bin_product_type as binProductType,bin_card_variant as binCardVariant,bin_card_brand as binCardBrand,
			domain_usage as issDomainUsage,message_type as messageType,card_technology as cardTechnology,authentication_mechanism as authMechanism,product_type as issProductType,settlement_bin as issSettlementBin,sub_scheme as subScheme,
			card_sub_variant as cardSubVariant,program_details as programDetails,status,form_factor as formFactor,offline_allowed as isIssOfflineAllowed,created_by as createdBy,created_on as createdOn,features as featureIssBin  from membin_details_stg
			where  participant_id in <foreach item='item' index='index' collection='participantIdList' open='(' separator=',' close=')'>#{item}</foreach>  and bin_type in ('I','T') and status in <foreach item='item' index='index' collection='statusList' open='(' separator=',' close=')'>#{item}</foreach>  
	</select>
	<select id="getMemberBulk" resultType="MemberDTO">
			select unique_bank_name as uniqueBnkName,member_id as memberid,bank_type as memberType,bank_name as memberName,ifsc_code as ifscCode, bank_sector as bankSector, bank_code as bankMasterCode,
			rtgs_code as rtgsCode,savings_acct_id as savingsAccNumber,current_acct_id as currentAccNumber,participant_id as participantId,bank_name as memberName,parent_participant_id as parentParticipantId,
			phone_number1 as bnkPhone,phone_number2 as bnkPhone2,mobile_number1 as bnkMobile,mobile_number2 as bnkMobile2,email_id1 as bnkEmail,email_id2 as bnkEmail2,legal_address as bnkAdd,
			country_id as bnkCountry,state_id as bnkState,zip_code as bnkPincode,city_id as bnkCity,gst_in as gstIn,gst_address as gstAdd,gst_city as gstCity,gst_country as gstCountry,
			gst_state as gstState,gst_code as gstPincode,request_state as requestState,record_status as recordStatus,website as webSite,created_by as createdBy,nfs_id as participantIdNFS
			from participant_stg where participant_id in <foreach item='item' index='index' collection='participantIdList' open='(' separator=',' close=')'>#{item}</foreach>    
	</select>
	<select id="getMemberContactInfoBulk" resultType="MemberContactInfoDTO">
			select address_type as addressType,name as cntChkrName,phone_number as cntPhone,mobile_number as cntMobile,fax_number as cntFax,legal_address as cntAdd1,
			country_id as cntCountry,state_id as cntState,city_id as cntCity,zip_code as cntPincode,email_id as cntEmail,auth_officer_desg as cntDesignation,participant_id as participantId from participant_contact_stg where participant_id in <foreach item='item' index='index' collection='participantIdList' open='(' separator=',' close=')'>#{item}</foreach> 
	</select>
	<delete id="deleteIssBinStgForNonActiveMember">
		delete from membin_details_stg where bin_number=#{binNo} and participant_id = #{participantId}
	</delete>
	<delete id="deleteAcqBinStgForNonActiveMember">
		delete from membin_details_stg where acquirer_id=#{acqId} and participant_id = #{participantId}
	</delete>
	<delete id="deleteSettlementBinStgForNonActiveMember">
		delete  membin_details_stg   where settlement_bin_no=#{settleBinNo} and participant_id = #{participantId}
	</delete>
	<!-- <select id="getBinCount" resultType="int">
			select count(*) REC_COUNT FROM membin_details WHERE participant_id = #{participantId}  
	</select> -->
	<select id="getparticipantContactCount" resultType="int">
			select count(*) REC_COUNT FROM participant_contact WHERE participant_id = #{participantId}
	</select>
	<select id="getparticipantSettlementBinCount" resultType="int">
			select count(*) REC_COUNT FROM participant_settlementbin WHERE participant_id = #{participantId}
	</select>
	<update id="clearCurrentDeleteFlag">
		update membin_details_stg set current_deleted=null where participant_id = #{participantId}	
	</update>
	<select id="duplicateSettlementBinCheckSave" resultType="int">
		select count(*) REC_COUNT FROM participant_settlementbin_stg WHERE participant_id != #{participantId} and settlement_bin_no = #{settlementBinNumber} 
	</select>
	<insert id="addBinNetworkMappingDetails">
			INSERT INTO bin_network_mapping_stg (bin_number,participant_id,mapping_network,status) VALUES
	<foreach  collection='networkList' item='networkList' separator=','>
	(  #{issBinList.binNumber,jdbcType=VARCHAR} ,  #{participantId,jdbcType=VARCHAR}, #{networkList,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR}
	
	)	</foreach>
			
	</insert>
		<insert id="addBinNetworkMappingDetailsMain">
			INSERT INTO bin_network_mapping (sequence_id,bin_number,participant_id,mapping_network,status) VALUES
	<foreach  collection='networkList' item='networkList' separator=','>
	( #{networkList.sequenceId,jdbcType=INTEGER},#{issBinList.binNumber,jdbcType=VARCHAR},  #{participantId,jdbcType=VARCHAR}, #{networkList.mappingNetwork,jdbcType=VARCHAR},#{networkList.status,jdbcType=VARCHAR}
	
	)	</foreach>
			
	</insert>
	
	
	<delete id="deletePreviousBin">
	delete from bin_network_mapping_stg where bin_number = #{binNumber}
	</delete>
	
	<delete id="deletePreviousBinMain">
	delete from bin_network_mapping where bin_number = #{binNumber}
	</delete>
	
	<select id="getNetworkSelectionList" resultType="String">
	select mapping_network from bin_network_mapping_stg where bin_number=#{binNumber} and participant_id=#{participantId} and status not in ('D')
	</select>
	
	<select id="getNetworkSelectionListMain" resultType="String">
	select mapping_network from bin_network_mapping where bin_number=#{binNumber} and participant_id=#{participantId} and status not in ('D')
	</select>
	
	<select id="getAll" resultType="participantDTO"> 
		SELECT
		bank_code as bankCode,
		bank_name as bankName,
		participant_id as participantId,
		bank_name as participantName,
		bank_type as bankType,
		rtgs_code as rtgsCode,
		bank_sector as bankSector,
		country_id as countryId,
		ifsc_code as ifscCode,
		legal_address as legalAddress,
		gst_address as gstAddress,
		gst_in as gstIn,
		gst_state as gstState,
		nfs_id as nfsId,
		parent_participant_id as parentParticipantId,
		participant_public_key as participantPublicKey,
		savings_acct_id as accountId,
		sub_net as subNet
		FROM
		participant
		where 
		status = 'A'
	</select>
	<select id="getNetworkSelectionListAll" resultType="String">
	select mapping_network from bin_network_mapping_stg where bin_number=#{binNumber} and participant_id=#{participantId} 
	</select>
	
	<select id="getNetworkSelectionListMainAll" resultType="BinDetailsDTO">
	select mapping_network as mappingNetwork , sequence_id as sequenceId,status as status from bin_network_mapping where bin_number=#{binNumber} and participant_id=#{participantId} 
	</select>
	<delete id="deletePreviousBinByParticipantId">
	delete from bin_network_mapping_stg where participant_id = #{participantId}
	</delete>
	
	<select id="getNetworkSelectionListStg" resultType="BinDetailsDTO">
	select mapping_network as mappingNetwork,sequence_id as sequenceId,status as status from bin_network_mapping_stg where bin_number=#{binNumber} and participant_id=#{participantId} 
	</select>
	
	<update id="updateBinNetworkMappingDetails">
	Update bin_network_mapping_stg set status=#{status}
	where bin_number= #{binDetailsDTO.binNumber} and participant_id=  #{participantId} and mapping_network=#{network}

	</update>
	<delete id="deleteStgDetailsBySeqId">
	delete from bin_network_mapping_stg where sequence_id=#{seqId} 
	</delete>
	
	
	<select id="getMemberBinDetailsByAcqId" resultType="BinDetailsDTO">
	select acquirer_id as acquirerId from membin_details where acquirer_id = #{acquirerId} limit 1
	</select>
	<select id="getAllParticipantSettlementBin" resultType="participantSettlementBinDTO"> 
		SELECT
	settlement_bin_id as settlementBinId,
	participant_id as participantId,
	settlement_bin_no as settlementBinNo,
	is_default as isDefault,
	created_by as createdBy,
	created_on as createdOn,
	last_updated_by as lastUpdatedBy,
	last_updated_on as lastUpdatedOn,
	settlement_currency as settlementCurrency,
	clearing_agency_type as clearingAgencyType
	FROM
	participant_settlementbin;
	</select>
	<select id="getNetBinDetailsByAcqId" resultType="BinDetailsDTO">
		select acquirer_id as acquirerId from network_bin_details where acquirer_id
		= #{acquirerId} limit 1
	</select>
	<update id="updateNetBinDetailsForAcqId">
		UPDATE network_bin_details set bin_id = #{binId} ,
		participant_id = #{participantId} ,
		activation_date = #{acqBinActivationDate} ,
		domain_usage = #{domainUsage},
		deactivation_date = #{dectivationDate} ,
		bank_group = #{bankGroup} ,
		offline_allowed = #{offlineAllowed} ,
		bin_number = #{binNumber} ,
		message_type = #{messageType} ,
		created_by = #{createdBy} ,
		created_on = #{createdOn} ,
		last_updated_by = #{lastUpdatedBy} ,
		last_updated_on = #{lastUpdatedOn} ,
		status = #{status} ,
		product_type = #{productType} ,
		settlement_bin = #{settlementBin} ,
		member_id = #{memberId} , bin_length = 9
		where acquirer_id =#{acquirerId}

	</update>

</mapper>
	