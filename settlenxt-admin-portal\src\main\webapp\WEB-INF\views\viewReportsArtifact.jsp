<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<title><spring:message code="am.lbl.title" /></title>

<script type="text/javascript">

	var actionColumnIndex = 1;
	var firstColumnToBeSkippedInFilterAndSort=false;
</script>
<script type="text/javascript"
	src="./static/js/validation/viewReportsArtifact.js"></script>



</head>
<body onload="noBack();">


<div class="space_block">
	<div class="container-fluid height-min">
		<ul class="nav nav-tabs" role="tablist" id="myTab">
		<li role="presentation" class="active" />
		
		
		</ul>
		
		<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span><span data-i18n="Data"> Reports and Artifacts</strong>
							</div>
							
		<form:form id="reportFiles" name="reportFiles">

			<div class="tab-content">
				<!-- tabpanel -->
				<div role="tabpanel" class="tab-pane active" id="home">
					<div class="row">
						<div class="col-sm-12"></div>
					</div>

					<div class="row">
						<div class="col-xs-12">
							<div class="panel panel-default">
								<div class="panel-body">
									<div class="table-responsive">
										<table id="tabnew" class="table table-striped table-bordered"
											style="width:100%;">
											<caption style="display:none;">View Report Artifact</caption> 
											<thead>
												<tr>
													<th scope="col">Content</th>
												</tr>
											</thead>
											<tbody>
												<c:if test="${not empty folders}">
													<c:forEach var="folder" items="${folders}">
														<tr>
															<td><a href="#" onclick="javascript:openFolder('${folderpath}/${folder}')"><span class="glyphicon glyphicon-folder-open">&nbsp;</span>${folder}</a></td>
														</tr>
													</c:forEach>
												</c:if>
												<c:if test="${not empty files}">
													<c:forEach var="file" items="${files}">
														<tr>
															<td><a href="#" onclick="javascript:downloadReportFile('${folderpath}/${file}')"><span class="glyphicon glyphicon-file">&nbsp;</span>${file}</a></td>
														</tr>
													</c:forEach>
												</c:if>
												<c:if test="${empty files && empty folders}">
													<tr>
														<td><span>No folders or files found</span></td>
													</tr>
												</c:if>
											</tbody>
										</table>
									</div>
								</div>
								<div class="modal-footer text-center">
									<c:if test="${back}">
										<button type="button" class="btn btn-primary" onclick="javascript:navBack('${folderpath}')" data-dismiss="modal">Back</button>
									</c:if>
								</div>
							</div>
						</div>
					</div> 
				</div>
			</div>
		</form:form>
	</div>
</div>
</div>
</div>
	</div>
</body>
</html>