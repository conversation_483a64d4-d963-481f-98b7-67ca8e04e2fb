$(document).ready(function() {
 userIds=[];
	var cursorPosition =null;
	/* Initialization of datatables */
	$(document).ready(function () {
    	
     $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                //If first column to be skipped to include the filter for the reasons line check box 
            dataTableFunc(colIdx,api);
   
                });
            $('#tabnew_filter').hide();
           
        },
        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'User',
                header: 'false',
                title: null,
                sheetName: 'User',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'User' ,
				header:'false', 
				title: null,
				sheetName:'User',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });
});	
   
function dataTableFunc(colIdx,api)
	{
	             if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   ({ cursorPosition} = handleInput(colIdx, cell, title, api, cursorPosition));
                   }
	}
	 
	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	
	
	 $("#excelExport").on("click", function () {
	        $(".buttons-excel").trigger("click");
	    });
	    
	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
	 
	     $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
	
	
	     
	     
	
	$("#selectAll").click(function(){
		
		$('#jqueryError4').hide();
        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
      
        var footerDataHeader = document.getElementById("detailsHeadersss");

        
        if(userIds.length>0){
	footerDataHeader.innerHTML = userIds.length+"     "+"records are selected";
	
	if( $('#selectAll').is(':checked') ){
 $("#toggleModal").modal('show');
        
}
else{
   $("#toggleModal").modal('hide');
        
}}else{
var i=0;
var userId2=[];
 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
		                    userId2.push(this.value);
		                    i++;
		                });
if(userId2.length>0){


if(userIdpending.length>0){
	footerDataHeader.innerHTML = userIdpending.length+"     "+"records are selected";
	
	if( $('#selectAll').is(':checked') ){
 $("#toggleModal").modal('show');
        
}
else{
   $("#toggleModal").modal('hide');
        
}
}}}
	
});
	
	
	
	
	
		
});


function handleInput(colIdx, cell, title, api, cursorPosition) {
	if (colIdx < actionColumnIndex) {

		$(cell).html(title + '<br><input class="search-box"   type="text" />');

		// On every keypress in this input
		$(
			'input',
			$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
		)
			.off('keyup change')
			.on('change', function () {
				// Get the search value
				$(this).attr('title', $(this).val());
				var regexr = '({search})';

				cursorPosition = this.selectionStart;
				// Search the column for that value
				api
					.column(colIdx)
					.search(
						this.value != ''
							? regexr.replace('{search}', '(((' + this.value + ')))')
							: '',
						this.value != '',
						this.value == ''
					)
					.draw();
				userIds = [];
				if (this.value != '') {
					var i = 0;
					$("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
						userIds.push(this.value);
						i++;
					});
				}
				else {
					userIds = [];
				}



			})
			.on('click', function (e) {
				e.stopPropagation();
			})
			.on('keyup', function (e) {
				e.stopPropagation();

				$(this).trigger('change');
				if (cursorPosition && cursorPosition != null) {
					$(this)
						.focus()[0]
						.setSelectionRange(cursorPosition, cursorPosition);
				}
			});
	} else {
		$(cell).html(title + '<br> &nbsp;');
	}
	return { cursorPosition};
}

function mySelect(){
	
	$('#jqueryError4').hide();
	
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	    
	 
	  var footerDataHeader = document.getElementById("detailsHeadersss");
	  if(array.length>0){
	 if(array.length==userIds.length){
		 $('#selectAll').prop('checked', true);
		 
			footerDataHeader.innerHTML = userIds.length+"     "+"records are selected";
			 $("#toggleModal").modal('show');
	 }
	 else{
		 $("#toggleModal").modal('hide');
		 
	 }
	  }
}

function getPendingUserList(){

	$('#userType').val($('#userType').val());
	var url = '/userPendingForApproval';
	var data =  "userType,"
	+ $('#userType').val();
	postData(url, data);
		

}

function getPendingIFSCList(){
	$('#userType').val($('#userType').val());
	var url = '/ifscPendingForApproval';
	var data =  "userType,"
	+ $('#userType').val();
	postData(url, data);
}

function getUserList(){
	$('#userType').val($('#userType').val());
	var url = "/showUsers";	
	var data = "userType," + $('#userType').val();
	postData(url,data);
}
function viewUserInfo(userId, type) {
var url="";
	if (type == 'V')
		url = '/getUser';
	else if (type == 'P')
		url = '/getPendingUser';
	var data = "uid," + userId + ",userType,"  + $('#userType').val() ;
	postData(url, data);
}


function viewUserInfoRej(userId,type) {
	
	var url="";
	if (type == 'V')
		url = '/getPendingUser';
	else if (type == 'P')
		url = '/editRejectedUser';
	
	var data = "uid," +  userId  + ",userType,"  + $('#userType').val() ;
	postData(url, data);
}


function submitForm(url) {
	var data = "userType," + $('#userType').val();
	postData(url, data);
}


function deselectAll() {

	$('#jqueryError4').hide();
var ele="";
   $('#selectAll').prop('checked', false);
         ele=document.getElementsByName('type');  
     let i=0;  
let j=0;  
         for (i of ele) {
         if(i.type=='checkbox')  
           i.checked=false; 
   
}
  
   
   $('#selectAll1').prop('checked', false);
         ele=document.getElementsByName('type');  
               for (j of ele) {
         if(j.type=='checkbox')  
           j.checked=false; 
   
}

   
}




function ApproveorRejectBulk(type,action){
	
let i=0;
	var url = '/approveBulkUserStatus';
	
	
	 var array = [];
		if(action=='no'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	   
	   

	   }
	   else if(action=='All'){
	   if(userIds.length>0){
	   array= userIds;
	   		}else{
	   		array= userIdpending;}
	   }
		
		
		  if(array.length>0){
			  
			  
			  
			  $('#jqueryError4').hide();
			  
		  
	 
	var LoginIdList = "";
	
	 for (i of array) {
         
        LoginIdList = LoginIdList + i + "|"
					; 
   
}
	var data="";	
	if(type=='A'){
		
		 data =  "status,"+"A"+",userIdList,"+LoginIdList+",remarks,"+"Approved"+",userType," + $('#userType').val();
	}
	 if(type=='R'){
		
		 data =  "status,"+"R"+",userIdList,"+LoginIdList+",remarks,"+"Rejected"+",userType," + $('#userType').val();
		
	}
	
		postData(url, data);
		
		
		  }
		  else{
			  
			  $('#errorStatus4').html('Please Select  Atleast One Record');
				$('#jqueryError4').show();
		  }
	}






