package org.npci.settlenxt.adminportal.service;

import com.opencsv.CSVWriter;
import lombok.RequiredArgsConstructor;
import org.npci.settlenxt.adminportal.common.cache.DataSecurityUtility;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.Utils;
import org.npci.settlenxt.adminportal.dto.NetworkFileSummaryDTO;
import org.npci.settlenxt.adminportal.model.NetworkOutgoingModel;
import org.npci.settlenxt.adminportal.repository.NetworkFileRepository;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.npci.settlenxt.portal.common.util.BaseCommonUtil;

@Service
@RequiredArgsConstructor
public class NetworkFileServiceImpl implements NetworkFileService {

    private final NetworkFileRepository networkFileRepository;
    private final DataSecurityUtility dataSecurityUtil;
    private static final String TABLE_NAME = "network_outgoing_matching_params";



    @Override
    public List<NetworkFileSummaryDTO> getNetworkFileDetails(String fromDate, String toDate, String networkType, String status) {
        List<String> tableNames = Utils.generateTableNamesInRange(TABLE_NAME, LocalDate.parse(fromDate, DateTimeFormatter.ofPattern(CommonConstants.YYYY_MM_DD_PATTERN)),
                LocalDate.parse(toDate, DateTimeFormatter.ofPattern(CommonConstants.YYYY_MM_DD_PATTERN)));
        String fileName = Utils.generateFileName(fromDate, toDate, networkType, status);
        NetworkFileSummaryDTO result = NetworkFileSummaryDTO.builder().fileName(fileName).build();

        for (String tableName : tableNames) {
            NetworkFileSummaryDTO networkFileSummaryDTO = networkFileRepository.getNetworkFileDetails(tableName, Utils.convertFromDateToLocalDateTime(fromDate), Utils.convertToDateToLocalDateTime(toDate), status, networkType);
            result.setTotalSuspended(result.getTotalSuspended() + networkFileSummaryDTO.getTotalSuspended());
            result.setTotalOutGoing(result.getTotalOutGoing() + networkFileSummaryDTO.getTotalOutGoing());
            result.setTotalAcknowledged(result.getTotalAcknowledged() + networkFileSummaryDTO.getTotalAcknowledged());
            result.setTotalRejected(result.getTotalRejected() + networkFileSummaryDTO.getTotalRejected());
            result.setTotalTransactions(result.getTotalTransactions() + networkFileSummaryDTO.getTotalTransactions());
        }
        return result.getTotalTransactions() > 0 ? List.of(result) : List.of();
    }

    @Override
    public InputStream downloadCsv(String fromDate, String toDate, String networkType, String status) throws IOException {
        List<String> tableNames = Utils.generateTableNamesInRange(TABLE_NAME, LocalDate.parse(fromDate, DateTimeFormatter.ofPattern(CommonConstants.YYYY_MM_DD_PATTERN)),
                LocalDate.parse(toDate, DateTimeFormatter.ofPattern(CommonConstants.YYYY_MM_DD_PATTERN)));
        List<NetworkOutgoingModel> transactions = new ArrayList<>();
        for (String tableName : tableNames) {
            transactions.addAll(networkFileRepository.getAllTransactions(tableName, Utils.convertFromDateToLocalDateTime(fromDate), Utils.convertToDateToLocalDateTime(toDate), status, networkType));
        }
        return csvFileWriter(transactions);
    }

    public ByteArrayInputStream csvFileWriter(List<NetworkOutgoingModel> transactions) throws IOException {
		for (NetworkOutgoingModel networkOutgoingModel : transactions) {
			networkOutgoingModel.setEncryptedPanNum(
					BaseCommonUtil.maskCardNumber(dataSecurityUtil.decrypt(networkOutgoingModel.getEncryptedPanNum())));
		}
        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             CSVWriter csvPrinter = new CSVWriter(new PrintWriter(out))) {
            csvPrinter.writeNext(Utils.getAttributeNamesForNetworkOutGoingModel());
            for (NetworkOutgoingModel transaction : transactions) {
                csvPrinter.writeNext(Utils.getAttributeValues(transaction));
            }
            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

}
