var _0x7939 = [
		"",
		"\x74\x6F\x4C\x6F\x77\x65\x72\x43\x61\x73\x65",
		"\x61\x62\x63",
		"\x61\x39\x39\x39\x33\x65\x33\x36\x34\x37\x30\x36\x38\x31\x36\x61\x62\x61\x33\x65\x32\x35\x37\x31\x37\x38\x35\x30\x63\x32\x36\x63\x39\x63\x64\x30\x64\x38\x39\x64",
		"\x6C\x65\x6E\x67\x74\x68", "\x63\x6F\x6E\x63\x61\x74",
		"\x30\x31\x32\x33\x34\x35\x36\x37\x38\x39\x41\x42\x43\x44\x45\x46",
		"\x30\x31\x32\x33\x34\x35\x36\x37\x38\x39\x61\x62\x63\x64\x65\x66",
		"\x63\x68\x61\x72\x43\x6F\x64\x65\x41\x74", "\x63\x68\x61\x72\x41\x74",
		"\x66\x72\x6F\x6D\x43\x68\x61\x72\x43\x6F\x64\x65" ];
var hexcase = 0;
var b64pad = _0x7939[0];
function getmyVal(_0xa234x4) {
	return rstr2hex(rstr_sha1(str2rstr_utf8(_0xa234x4)));
};
function hex_hmac_sha1(_0xa234x4, _0xa234x6) {
	return rstr2hex(rstr_hmac_sha1(str2rstr_utf8(_0xa234x4),
			str2rstr_utf8(_0xa234x6)));
};
function sha1_vm_test() {
	return hex_sha1(_0x7939[2])[_0x7939[1]]() == _0x7939[3];
};
function rstr_sha1(_0xa234x4) {
	return binb2rstr(binb_sha1(rstr2binb(_0xa234x4), _0xa234x4[_0x7939[4]] * 8));
};
function rstr_hmac_sha1(_0xa234xa, _0xa234xb) {
	var _0xa234xc = rstr2binb(_0xa234xa);
	if (_0xa234xc[_0x7939[4]] > 16) {
		_0xa234xc = binb_sha1(_0xa234xc, _0xa234xa[_0x7939[4]] * 8);
	}
	;
	var _0xa234x4 = Array(16), _0xa234xd = Array(16);
	for (var _0xa234x6 = 0; _0xa234x6 < 16; _0xa234x6++) {
		_0xa234x4[_0xa234x6] = _0xa234xc[_0xa234x6] ^ 909522486;
		_0xa234xd[_0xa234x6] = _0xa234xc[_0xa234x6] ^ 1549556828;
	}
	;
	var _0xa234xe = binb_sha1(_0xa234x4[_0x7939[5]](rstr2binb(_0xa234xb)),
			512 + _0xa234xb[_0x7939[4]] * 8);
	return binb2rstr(binb_sha1(_0xa234xd[_0x7939[5]](_0xa234xe), 512 + 160));
};
function rstr2hex(_0xa234xa) {
	try {
		hexcase;
	} catch (g) {
		hexcase = 0;
	}
	;
	var _0xa234xb = hexcase ? _0x7939[6] : _0x7939[7];
	var _0xa234x6 = _0x7939[0];
	var _0xa234x4;
	for (var _0xa234xd = 0; _0xa234xd < _0xa234xa[_0x7939[4]]; _0xa234xd++) {
		_0xa234x4 = _0xa234xa[_0x7939[8]](_0xa234xd);
		_0xa234x6 += _0xa234xb[_0x7939[9]]((_0xa234x4 >>> 4) & 15)
				+ _0xa234xb[_0x7939[9]](_0xa234x4 & 15);
	}
	;
	return _0xa234x6;
};
function str2rstr_utf8(_0xa234xa) {
	var _0xa234x6 = _0x7939[0];
	var _0xa234xd = -1;
	var _0xa234x4, _0xa234xc;
	while (++_0xa234xd < _0xa234xa[_0x7939[4]]) {
		_0xa234x4 = _0xa234xa[_0x7939[8]](_0xa234xd);
		_0xa234xc = _0xa234xd + 1 < _0xa234xa[_0x7939[4]] ? _0xa234xa[_0x7939[8]]
				(_0xa234xd + 1)
				: 0;
		if (55296 <= _0xa234x4 && _0xa234x4 <= 56319 && 56320 <= _0xa234xc
				&& _0xa234xc <= 57343) {
			_0xa234x4 = 65536 + ((_0xa234x4 & 1023) << 10) + (_0xa234xc & 1023);
			_0xa234xd++;
		}
		;
		if (_0xa234x4 <= 127) {
			_0xa234x6 += String[_0x7939[10]](_0xa234x4);
		} else {
			if (_0xa234x4 <= 2047) {
				_0xa234x6 += String[_0x7939[10]](
						192 | ((_0xa234x4 >>> 6) & 31), 128 | (_0xa234x4 & 63));
			} else {
				if (_0xa234x4 <= 65535) {
					_0xa234x6 += String[_0x7939[10]](
							224 | ((_0xa234x4 >>> 12) & 15),
							128 | ((_0xa234x4 >>> 6) & 63),
							128 | (_0xa234x4 & 63));
				} else {
					if (_0xa234x4 <= 2097151) {
						_0xa234x6 += String[_0x7939[10]](
								240 | ((_0xa234x4 >>> 18) & 7),
								128 | ((_0xa234x4 >>> 12) & 63),
								128 | ((_0xa234x4 >>> 6) & 63),
								128 | (_0xa234x4 & 63));
					}
					;
				}
				;
			}
			;
		}
		;
	}
	;
	return _0xa234x6;
};
function rstr2binb(_0xa234x6) {
	var _0xa234x4 = Array(_0xa234x6[_0x7939[4]] >> 2);
	for (var _0xa234xa = 0; _0xa234xa < _0xa234x4[_0x7939[4]]; _0xa234xa++) {
		_0xa234x4[_0xa234xa] = 0;
	}
	;
	for (var _0xa234xa = 0; _0xa234xa < _0xa234x6[_0x7939[4]] * 8; _0xa234xa += 8) {
		_0xa234x4[_0xa234xa >> 5] |= (_0xa234x6[_0x7939[8]](_0xa234xa / 8) & 255) << (24 - _0xa234xa % 32);
	}
	;
	return _0xa234x4;
};
function binb2rstr(_0xa234x6) {
	var _0xa234x4 = _0x7939[0];
	for (var _0xa234xa = 0; _0xa234xa < _0xa234x6[_0x7939[4]] * 32; _0xa234xa += 8) {
		_0xa234x4 += String[_0x7939[10]]
				((_0xa234x6[_0xa234xa >> 5] >>> (24 - _0xa234xa % 32)) & 255);
	}
	;
	return _0xa234x4;
};
function binb_sha1(_0xa234x14, _0xa234x15) {
	_0xa234x14[_0xa234x15 >> 5] |= 128 << (24 - _0xa234x15 % 32);
	_0xa234x14[((_0xa234x15 + 64 >> 9) << 4) + 15] = _0xa234x15;
	var _0xa234x16 = Array(80);
	var _0xa234x17 = 1732584193;
	var _0xa234x18 = -271733879;
	var _0xa234x19 = -1732584194;
	var _0xa234x1a = 271733878;
	var _0xa234x1b = -1009589776;
	for (var _0xa234x1c = 0; _0xa234x1c < _0xa234x14[_0x7939[4]]; _0xa234x1c += 16) {
		var _0xa234x1d = _0xa234x17;
		var _0xa234x1e = _0xa234x18;
		var _0xa234x1f = _0xa234x19;
		var _0xa234x20 = _0xa234x1a;
		var _0xa234xb = _0xa234x1b;
		for (var _0xa234xe = 0; _0xa234xe < 80; _0xa234xe++) {
			if (_0xa234xe < 16) {
				_0xa234x16[_0xa234xe] = _0xa234x14[_0xa234x1c + _0xa234xe];
			} else {
				_0xa234x16[_0xa234xe] = bit_rol(_0xa234x16[_0xa234xe - 3]
						^ _0xa234x16[_0xa234xe - 8]
						^ _0xa234x16[_0xa234xe - 14]
						^ _0xa234x16[_0xa234xe - 16], 1);
			}
			;
			var _0xa234x21 = safe_add(safe_add(bit_rol(_0xa234x17, 5), sha1_ft(
					_0xa234xe, _0xa234x18, _0xa234x19, _0xa234x1a)), safe_add(
					safe_add(_0xa234x1b, _0xa234x16[_0xa234xe]),
					sha1_kt(_0xa234xe)));
			_0xa234x1b = _0xa234x1a;
			_0xa234x1a = _0xa234x19;
			_0xa234x19 = bit_rol(_0xa234x18, 30);
			_0xa234x18 = _0xa234x17;
			_0xa234x17 = _0xa234x21;
		}
		;
		_0xa234x17 = safe_add(_0xa234x17, _0xa234x1d);
		_0xa234x18 = safe_add(_0xa234x18, _0xa234x1e);
		_0xa234x19 = safe_add(_0xa234x19, _0xa234x1f);
		_0xa234x1a = safe_add(_0xa234x1a, _0xa234x20);
		_0xa234x1b = safe_add(_0xa234x1b, _0xa234xb);
	}
	;
	return Array(_0xa234x17, _0xa234x18, _0xa234x19, _0xa234x1a, _0xa234x1b);
};
function sha1_ft(_0xa234xc, _0xa234x4, _0xa234xe, _0xa234xb) {
	if (_0xa234xc < 20) {
		return (_0xa234x4 & _0xa234xe) | ((~_0xa234x4) & _0xa234xb);
	}
	;
	if (_0xa234xc < 40) {
		return _0xa234x4 ^ _0xa234xe ^ _0xa234xb;
	}
	;
	if (_0xa234xc < 60) {
		return (_0xa234x4 & _0xa234xe) | (_0xa234x4 & _0xa234xb)
				| (_0xa234xe & _0xa234xb);
	}
	;
	return _0xa234x4 ^ _0xa234xe ^ _0xa234xb;
};
function sha1_kt(_0xa234x4) {
	return (_0xa234x4 < 20) ? 1518500249 : (_0xa234x4 < 40) ? 1859775393
			: (_0xa234x4 < 60) ? -1894007588 : -899497514;
};
function safe_add(_0xa234x4, _0xa234xd) {
	var _0xa234xa = (_0xa234x4 & 65535) + (_0xa234xd & 65535);
	var _0xa234x6 = (_0xa234x4 >> 16) + (_0xa234xd >> 16) + (_0xa234xa >> 16);
	return (_0xa234x6 << 16) | (_0xa234xa & 65535);
};
function bit_rol(_0xa234x4, _0xa234x6) {
	return (_0xa234x4 << _0xa234x6) | (_0xa234x4 >>> (32 - _0xa234x6));
};