<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.npci.settlenxt.adminportal.repository.HolidayMasterRepository">

	<select id="getApprovedHolidayMasterList" resultType="HolidayMasterDTO">
		select hm.id as holidaySeqId, hm.holiday_date holidayDate, hm.product as product, hm.holiday_desc as holidayDesc, hm.period_type as periodType,hms.request_state as requestState,hms.checker_comments as checkerComments,
		hm.day_of_week as dayOfWeek, hm.week_type as weekType, hm.status as status, hm.created_by as createdBy,hm.created_on as createdOn,
		hm.last_updated_by as lastUpdatedBy,hm.last_updated_on as lastUpdatedOn from holiday_master hm left join holiday_master_stg hms on hm.id=hms.id where hms.request_state=#{requestState} order by hms.last_updated_on desc;
	</select>
	
	
	<select id="getPendingHolidayMasterList" resultType="HolidayMasterDTO">
		select hms.id as holidaySeqId, hms.holiday_date holidayDate, hms.product as product, hms.holiday_desc as holidayDesc, hms.period_type as periodType,hms.request_state as requestState,hms.checker_comments as checkerComments,
		hms.day_of_week as dayOfWeek, hms.week_type as weekType, hms.status as status, hms.created_by as createdBy,hms.created_on as createdOn,
		hms.last_updated_by as lastUpdatedBy,hms.last_updated_on as lastUpdatedOn from holiday_master_stg hms where hms.request_state in <foreach item='item' index='index' collection='requestStateList' open='(' separator=',' close=')'>#{item} </foreach> order by hms.last_updated_on desc
	</select>
<select id="getHolidayById" resultType="HolidayMasterDTO">
		SELECT id as holidaySeqId,
		holiday_date as holidayDate, product as product,
		holiday_desc as holidayDesc,
		period_type as periodType, day_of_week as dayOfWeek,
		week_type as weekType, status as status, 
		created_by as createdBy, created_on as createdOn,
		last_updated_by as lastUpdatedBy,
		last_updated_on as lastUpdatedOn,
		request_state as requestState, checker_comments as checkerComments,
		last_operation as lastOperation
		FROM holiday_master_stg
		WHERE id=#{holidaySeqId};

	</select>
	
	
	<select id="getHolidayFromMainById" resultType="HolidayMasterDTO">
		SELECT hm.id as holidaySeqId,
		hm.holiday_date as holidayDate, hm.product as product,
		hm.holiday_desc as holidayDesc,
		hm.period_type as periodType, hm.day_of_week as dayOfWeek,
		hm.week_type as weekType, hm.status as status, 
		hm.created_by as createdBy, hm.created_on as createdOn,
		hm.last_updated_by as lastUpdatedBy,
		hm.last_updated_on as lastUpdatedOn,
		hms.request_state as requestState, hms.checker_comments as checkerComments,
		hms.last_operation as lastOperation
		FROM holiday_master hm left join holiday_master_stg hms on hm.id = hms.id
		WHERE hm.id=#{holidaySeqId};

	</select>

	<update id="updateHolidayinStg">
		UPDATE holiday_master_stg set
		product = #{product},status = #{status},
		holiday_desc = #{holidayDesc},
		request_state = #{requestState},checker_comments ='',
		last_updated_by = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},
		last_operation = #{lastOperation}
		where id=cast(#{holidaySeqId} as integer)
	</update>


	<select id="getProductList" resultType="CodeValueDTO">
		select distinct result as
		code,result as description from rule_master;
	</select>
	<insert id="insertHolidayMasterWeeklyHoliday">
		insert into
		holiday_master_stg(id,holiday_date,holiday_desc,period_type,product,day_of_week,week_type,created_by,created_on,last_updated_by,last_updated_on,request_state,status,last_operation)
		values(cast(#{holidayMasterDTO.holidaySeqId} as integer),#{holidayMasterDTO.holidayDate},#{holidayMasterDTO.holidayDesc},
		#{holidayMasterDTO.periodType},#{holidayMasterDTO.product},#{holidayMasterDTO.dayOfWeek},#{weekNumber},#{holidayMasterDTO.createdBy},
		#{holidayMasterDTO.createdOn},#{holidayMasterDTO.lastUpdatedBy},#{holidayMasterDTO.lastUpdatedOn},#{holidayMasterDTO.status},'A',#{holidayMasterDTO.lastOperation})
	</insert>

	<select id="fetchIdFromSeq" resultType="Integer">
		select
		nextval('holiday_master_id_seq');
	</select>
	
	<select id="getHolidayMasterStgInfoList" resultType="HolidayMasterDTO">
		select hms.id as holidaySeqId, hms.holiday_date holidayDate,
		hms.product as product, hms.holiday_desc as holidayDesc,
		hms.period_type as periodType,hms.request_state as
		requestState,hms.checker_comments as checkerComments,
		hms.day_of_week
		as dayOfWeek, hms.week_type as weekType, hms.status as status,
		hms.created_by as createdBy,hms.created_on as createdOn,
		hms.last_updated_by as lastUpdatedBy,hms.last_updated_on as
		lastUpdatedOn from holiday_master_stg hms where hms.id in
		<foreach item='item' index='index' collection='holidayMasterlist'
			open='(' separator=',' close=')'>#{item} </foreach>
		order by hms.last_updated_on desc
	</select>
    <update id="updateHolidayMasterStgState">
		UPDATE HOLIDAY_MASTER_STG SET LAST_UPDATED_BY =
		#{lastUpdatedBy},
		LAST_UPDATED_ON = #{lastUpdatedOn},
		REQUEST_STATE=#{requestState},
		CHECKER_COMMENTS=#{checkerComments},LAST_OPERATION=#{lastOperation}
		WHERE ID=#{holidaySeqId}
	</update>
    <select id="getHolidayMasterInfoByHolidaySeqId" resultType="HolidayMasterDTO">
		select hm.id as holidaySeqId, hm.holiday_date holidayDate,
		hm.product
		as product, hm.holiday_desc as holidayDesc,
		hm.period_type as
		periodType, hm.day_of_week
		as dayOfWeek, hm.week_type as weekType,
		hm.status as status,
		hm.created_by as createdBy,hm.created_on as
		createdOn,
		hm.last_updated_by as lastUpdatedBy,hm.last_updated_on as
		lastUpdatedOn from holiday_master hm where hm.id = #{holidaySeqId}
		order by hm.last_updated_on desc
	</select>
	<select id="getPendingForApprovalHolidayMasterList"
		resultType="HolidayMasterDTO">
		select hms.id as holidaySeqId, hms.holiday_date holidayDate,
		hms.product as product, hms.holiday_desc as holidayDesc,
		hms.period_type as periodType,hms.request_state as
		requestState,hms.checker_comments as checkerComments,
		hms.day_of_week
		as dayOfWeek, hms.week_type as weekType, hms.status as status,
		hms.created_by as createdBy,hms.created_on as createdOn,
		hms.last_updated_by as lastUpdatedBy,hms.last_updated_on as
		lastUpdatedOn from holiday_master_stg hms where hms.request_state in
		<foreach item='item' index='index'
			collection='requestStateList' open='(' separator=',' close=')'>#{item}
		</foreach>
		order by hms.last_updated_on desc

	</select>
	<select id="getHolidayMasterStgInfoBySeqId"
		resultType="HolidayMasterDTO">
		select hms.id as holidaySeqId, hms.holiday_date holidayDate,
		hms.product as product,
		hms.holiday_desc as holidayDesc, hms.period_type
		as periodType,hms.request_state as requestState,
		hms.checker_comments as
		checkerComments,hms.day_of_week as dayOfWeek,hms.week_type as weekType,
		hms.status as status, hms.created_by as createdBy,hms.created_on as
		createdOn, hms.last_updated_by as lastUpdatedBy,
		hms.last_updated_on as
		lastUpdatedOn from holiday_master_stg hms where hms.id=#{holidaySeqId}
		order by hms.last_updated_on desc;
	</select>
	<insert id="saveHolidayMaster">
		Insert Into holiday_master
		(id,holiday_date,product,holiday_desc,period_type,day_of_week,week_type,status,created_by,created_on)
		values
		(#{hseqId},#{holidayDate},#{product},#{holidayDesc},#{periodType},#{dayOfWeek},cast(#{weekType}
		as
		integer),#{status},#{createdBy},#{createdOn})
	</insert>


	<update id="updateHolidayMaster">
		UPDATE HOLIDAY_MASTER SET holiday_date =
		#{holidayDate},product =#{product},holiday_desc = #{holidayDesc},
		period_type = #{periodType},day_of_week = #{dayOfWeek},week_type =
		cast(#{weekType} as integer),status = #{status},
		created_by = #{createdBy},created_on =
		#{createdOn},last_updated_by = #{lastUpdatedBy},
		last_updated_on =
		#{lastUpdatedOn} where id = #{hseqId}
	</update>
	<update id="deleteReq">
		UPDATE holiday_master_stg set
		request_state=#{state},status=#{status},last_operation=#{lastOpDelete}
		where id=cast(#{hid} as integer)
	</update>

	<delete id="deleteDiscardedEntry">
		DELETE FROM holiday_master_stg where id = #{holidaySeqId}
	</delete>

<select id="existingHolidayMasterPublicHoliday" resultType="HolidayMasterDTO">
		SELECT id as hseqId,
		holiday_date as holidayDate, product as product,
		holiday_desc as holidayDesc,
		period_type as periodType, day_of_week as dayOfWeek,
		week_type as weekType, status as status, 
		created_by as createdBy, created_on as createdOn,
		last_updated_by as lastUpdatedBy,
		last_updated_on as lastUpdatedOn,
		request_state as requestState, checker_comments as checkerComments,
		last_operation as lastOperation
		FROM holiday_master_stg
		WHERE product = #{holidayMasterDTO.product} and holiday_date = #{holidayMasterDTO.holidayDate}

	</select>
	
	<select id="existingHolidayMasterWeeklyHoliday" resultType="HolidayMasterDTO">
		SELECT id as hseqId,
		holiday_date as holidayDate, product as product,
		holiday_desc as holidayDesc,
		period_type as periodType, day_of_week as dayOfWeek,
		week_type as weekType, status as status, 
		created_by as createdBy, created_on as createdOn,
		last_updated_by as lastUpdatedBy,
		last_updated_on as lastUpdatedOn,
		request_state as requestState, checker_comments as checkerComments,
		last_operation as lastOperation
		FROM holiday_master_stg
		WHERE product = #{holidayMasterDTO.product} and holiday_date = #{holidayMasterDTO.holidayDate}

	</select>
</mapper>