package org.npci.settlenxt.adminportal.service;

import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.repository.CashbackFileUploadRepository;
import org.npci.settlenxt.adminportal.repository.FileUploadRepository;
import org.npci.settlenxt.adminportal.validator.AsyncFileValidator;
import org.npci.settlenxt.adminportal.validator.service.IValidationService;
import org.npci.settlenxt.portal.common.dto.CashBackFileUploadDTO;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;
import org.npci.settlenxt.portal.common.service.BaseFileUploadServiceImpl;
import org.npci.settlenxt.portal.common.util.FileNameUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service Class used for
 * <li>Bulk file upload</li>
 * <li>Search file from file upload table</li>
 * <li>Stage uploaded file</li>
 * <li>Get Current Cycle details</li>
 * <li>Get File upload details by documentId</li>
 * 
 * <AUTHOR>
 *
 */
@Service
public class FileUploadServiceImpl extends BaseFileUploadServiceImpl implements IFileUploadService {
	
	private static final Logger log = LogManager.getLogger(FileUploadServiceImpl.class);

	@Autowired
	private FileUploadRepository fileUploadRepository;

	@Autowired
	private CashbackFileUploadRepository cashbackFileRepository;
	
	@Autowired
	IValidationService validationService;

	private static ExecutorService executorService = Executors.newFixedThreadPool(4);
	private static final String ADMIN_PORTAL = "Admin";
	private static final String ERROR = "ERROR";	

	@Override
	protected String getProcessEncryptedFile() {
		return environment.getProperty(CommonConstants.PROCESS_ENCRYPTED_FILE);
	}

	@Override
	protected List<FileUploadDTO> getFilesByPortals() {
		return fileUploadRepository.getFilesByPortal(ADMIN_PORTAL);
	}

	@Override
	protected String getFileUploadsPath() {
		return environment.getProperty("DISPUTE_FILE_UPLOAD_PATH");
	}

	@Override
	protected void setPortal(FileUploadDTO fileUploadDTO) {
		fileUploadDTO.setPortal(ADMIN_PORTAL);
	}

	@Override
	protected void submitAsyncFileValidator(FileUploadDTO newDTO) {
		executorService.submit(new AsyncFileValidator(validationService, newDTO, null));
	}

	@Override
	protected String getStagePath() {
		return environment.getProperty("DISPUTE_FILE_UPLOAD_STAGE_PATH");
	}

	@Override
	public String cashBackFileUpload(CashBackFileUploadDTO cashbackDTO) throws NullPointerException{
		String filename = cashbackDTO.getFileArr().getOriginalFilename();
		String splitedFilename = "";
		if(filename != null) {
			splitedFilename = filename.split("\\.")[1];
		}
		try {
			String filepath = environment.getProperty("CASHBACK_FILE_UPLOAD_PATH");
			if (!isDigit(cashbackDTO.getYear()) || !isDigit(cashbackDTO.getMonth())) {
				log.error("Validation failed for year or month");
				return ERROR;
			}
			if (cashbackDTO.getFileArr() != null && StringUtils.isNotBlank(cashbackDTO.getFileArr().getOriginalFilename())
					&& Boolean.FALSE.equals(StringUtils.equals("csv",splitedFilename))) {
				log.error("Validation failed for uploaded file");
				return ERROR;
			}
			List<CashBackFileUploadDTO> fileUploadList = cashbackFileRepository
					.getFileUploadByYearMonth(cashbackDTO.getYear(), cashbackDTO.getMonth());
			if (Boolean.FALSE.equals(fileUploadList.isEmpty())) {
				log.error("Record found for this year and month");
				return "RECMISMATCH";
			}
			if (cashbackDTO.getFileArr() != null) {
				double sizeInKiloBytes = cashbackDTO.getFileArr().getSize() * 0.001;
				cashbackDTO.setFileName(cashbackDTO.getFileArr().getOriginalFilename());
				cashbackDTO.setFileLocation(filepath + cashbackDTO.getFileName());
				FileNameUtil.createDirectory(filepath);
				File file = FileNameUtil.createFile(cashbackDTO.getFileLocation());
				cashbackDTO.getFileArr().transferTo(file);
				cashbackDTO.setCreatedBy(sessionDTO.getUserName());
				cashbackDTO.setStatus("P");
				cashbackDTO.setSiteId(environment.getProperty("SITE_ID"));
				cashbackDTO.setInstanceId(environment.getProperty("INSTANCE_ID"));
				cashbackDTO.setFileSize(String.valueOf(sizeInKiloBytes));
				cashbackFileRepository.saveFile(cashbackDTO);
			} else {
				log.error("File content is invalid");
				return ERROR;
			}

		} catch (Exception e) {
			log.error("Error while cashback file upload: ", e);
			return ERROR;
		}
		return "SUCCESS";
	}

	@Override
	public List<CashBackFileUploadDTO> searchCashBackFiles() {
		try {
			List<CashBackFileUploadDTO> cashbackFileList = cashbackFileRepository.getAllCashBackFiles();
			if (cashbackFileList.isEmpty()) {
				return Collections.emptyList();
			} else {
				for (CashBackFileUploadDTO cashBackFileUploadDTO : cashbackFileList) {
					if (StringUtils.equals(cashBackFileUploadDTO.getStatus(), "C")) {
						cashBackFileUploadDTO.setStatusDesc("Complete");
					} else if (StringUtils.equals(cashBackFileUploadDTO.getStatus(), "S")) {
						cashBackFileUploadDTO.setStatusDesc("Splitting");
					} else if (StringUtils.equals(cashBackFileUploadDTO.getStatus(), "P")) {
						cashBackFileUploadDTO.setStatusDesc("Pending");
					} else if (StringUtils.equals(cashBackFileUploadDTO.getStatus(), "F")) {
						cashBackFileUploadDTO.setStatusDesc("Failed");
					} else {
						cashBackFileUploadDTO.setStatusDesc("Error");
					}
					cashBackFileUploadDTO.setFileSize(cashBackFileUploadDTO.getFileSize() + " Kb");
				}
				return cashbackFileList;
			}
		} catch (Exception e) {
			log.error("Error while searching cashback files: ", e);
			return Collections.emptyList();
		}
	}

	private boolean isDigit(String strNum) {
		Pattern pattern = Pattern.compile("\\d+");
		Matcher matcher = pattern.matcher(strNum);
		return matcher.matches();
	}
}
