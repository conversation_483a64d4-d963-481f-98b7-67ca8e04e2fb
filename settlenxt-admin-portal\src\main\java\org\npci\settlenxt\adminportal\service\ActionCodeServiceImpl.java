package org.npci.settlenxt.adminportal.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.ActionCodeDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.ActionCodeRepository;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional(rollbackFor = Throwable.class)
@Service
public class ActionCodeServiceImpl implements ActionCodeService {

	@Autowired
	SessionDTO sessionDTO;

	@Autowired
	ActionCodeRepository actionCodeRepository;

	@Override
	public List<ActionCodeDTO> getApprovedActionCodeFromMain() {
		return actionCodeRepository.getApprovedActionCodeFromMain();
	}

	@Override
	public List<ActionCodeDTO> getPendingForApprovalActionCode() {
		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_REJECTED);
		return actionCodeRepository.getPendingForApprovalActionCodeFromStg(requestStateList);
	}

	@Override
	public List<CodeValueDTO> getFuncCodeList() {
		return actionCodeRepository.getFuncCodeList();
	}

	@Override
	public ActionCodeDTO addEditActionCode(ActionCodeDTO actionCodeDTO) {
		Date currentDate = new Date();
		actionCodeDTO.setActionCodeId(actionCodeRepository.getActionCodeSeqId());
		actionCodeDTO.setCreatedBy(sessionDTO.getUserName());
		actionCodeDTO.setCreatedOn(currentDate);
		actionCodeDTO.setLastUpdatedBy(null);
		actionCodeDTO.setLastUpdatedOn(null);
		actionCodeDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
		actionCodeDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

		actionCodeRepository.insertActionCodeIntoStg(actionCodeDTO);
		return actionCodeDTO;

	}

	@Override
	public ActionCodeDTO getActionCodeInfoFromMain(int actionCodeId) {
		return actionCodeRepository.getActionCodeInfoFromMain(actionCodeId);
	}

	@Override
	public ActionCodeDTO getActionCode(int actionCodeId) {
		return actionCodeRepository.getActionCodeInfoFromStg(actionCodeId);
	}

	@Override
	public ActionCodeDTO approveOrRejectActionCode(int actionCodeId, String status, String remarks) {

		ActionCodeDTO actionCodeDTO = getActionCode(actionCodeId);
		actionCodeDTO.setLastUpdatedOn(new Date());
		actionCodeDTO.setLastUpdatedBy(sessionDTO.getUserName());
		actionCodeDTO.setRequestState(status);
		actionCodeDTO.setCheckerComments(remarks);

		if (CommonConstants.REQUEST_STATE_REJECTED.equals(status)) {
			actionCodeDTO.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);

		}
		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			actionCodeDTO.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			ActionCodeDTO actionCodeDTOmain = actionCodeRepository.getActionCodeInfoFromMain(actionCodeId);
			actionCodeDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

			if (actionCodeDTOmain != null) {

				actionCodeRepository.updateActionCode(actionCodeDTO);
			} else {

				actionCodeDTO.setCreatedOn(actionCodeDTO.getLastUpdatedOn());
				actionCodeDTO.setLastUpdatedOn(null);
				actionCodeDTO.setLastUpdatedBy(null);
				actionCodeRepository.saveActionCodeMain(actionCodeDTO);
			}
		}
		actionCodeDTO.setLastUpdatedOn(new Date());
		actionCodeDTO.setLastUpdatedBy(sessionDTO.getUserName());

		actionCodeRepository.updateActionCodeRequestState(actionCodeDTO);

		return actionCodeDTO;
	}

	@Override
	public ActionCodeDTO getActionCodeFromMainEdit(int actionCodeId) {
		return actionCodeRepository.getActionCodeFromMainEdit(actionCodeId);
	}

	@Override
	public int updateActionCode(ActionCodeDTO actionCodeDTO) {
		actionCodeDTO.setLastUpdatedBy(sessionDTO.getUserName());
		actionCodeDTO.setLastUpdatedOn(new Date());

		actionCodeDTO.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
		actionCodeDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

		return actionCodeRepository.updateActionCodeStg(actionCodeDTO);

	}

	@Override
	public ActionCodeDTO discardActionCode(int actionCodeId) {
		ActionCodeDTO actionCodeDTO = getActionCode(actionCodeId);
		ActionCodeDTO actionCodeDTOmain = actionCodeRepository.getActionCodeInfoFromMain(actionCodeId);

		if (actionCodeDTOmain != null) {
			actionCodeDTOmain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			actionCodeDTOmain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);

			actionCodeDTOmain.setLastUpdatedBy(sessionDTO.getUserName());
			actionCodeDTOmain.setLastUpdatedOn(new Date());

			actionCodeRepository.updateStgActionCode(actionCodeDTOmain);

			return actionCodeDTOmain;
		} else {

			actionCodeRepository.deleteDiscardedEntry(actionCodeId);

		}

		return actionCodeDTO;
	}

	@Override
	public ActionCodeDTO updateApproveOrRejectBulkActionCode(String bulkActionCodeIdList, String status,
			String remarks) {

		String[] idArray = bulkActionCodeIdList.split("\\|");
		ActionCodeDTO actionCode = new ActionCodeDTO();

		int[] values = Arrays.stream(idArray).mapToInt(Integer::parseInt).toArray();
		List<Integer> actionCodeIdList = Arrays.stream(values).boxed().collect(Collectors.toList());

		List<ActionCodeDTO> actionCodeToArr = actionCodeRepository.fetchCappingAmountStgAppList(actionCodeIdList);

		Map<Integer, List<ActionCodeDTO>> cappingMap = actionCodeToArr.stream()
				.collect(Collectors.groupingBy(ActionCodeDTO::getActionCodeId));

		for (String id:idArray) {

			try {
				List<ActionCodeDTO> actiondto = cappingMap.get(Integer.parseInt(id));
				ActionCodeDTO actionCodeEntryDto = actiondto.get(0);
				if (actionCodeEntryDto == null) {
					throw new SettleNxtException("Exception occurred with cappingAmountId" + id, "");
				} else {
					actionCodeEntryDto = approveOrRejectActionCode(actionCodeEntryDto.getActionCodeId(), status,
							remarks);
					actionCode.setStatusCode(actionCodeEntryDto.getStatusCode());
				}

			} catch (Exception ex) {
				throw new SettleNxtException("Exception for BulkDataEntryId" + id, "", ex);

			}
		}

		return actionCode;

	}

	@Override
	public List<CodeValueDTO> getMccFromFuncCode() {

		return actionCodeRepository.getMCCList();

	}

	@Override
	public boolean checkDistinctActioncode(String actionCode) {
		ActionCodeDTO actDto = new ActionCodeDTO();
		actDto.setActionCode(actionCode);
		actDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
		int i = actionCodeRepository.checkDistinctActionCode(actDto);
		int j = actionCodeRepository.checkDistinctActionCodeStg(actDto);

		return i > 0 || j > 0;

	}

	@Override
	public List<CodeValueDTO> getActionCodeList() {
		/* CommonConstants.REQUEST_STATE_APPROVED */
		return actionCodeRepository.getActionCodeList();
	}

	@Override
	public List<CodeValueDTO> getReasonCodeList() {
		return actionCodeRepository.getReasonCodeList();

	}

}
