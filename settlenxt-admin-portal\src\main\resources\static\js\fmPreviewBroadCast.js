$(document).ready(function() {

	var table = $('#tabnew').DataTable({
		"bSort":false,
		"order" : [ [ 1, "desc" ] ]
	});

	$('#tabnew tbody').on('click', 'tr', function() {

		if ($(this).hasClass('selected')) {
			$(this).removeClass('selected');
		} else {
			table.$('tr.selected').removeClass('selected');
			$(this).addClass('selected');
		}
	});

	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	$('.viewBroadCast').click(function() {
		var a = $(this).data("value");
		var b = a.split(",");
		var url = b[0];
		var data = a;
		if (b[1] == 'V') {
			url = '/getBroadCastMessageList';
			data = "taxId," + b[0] ;
		} else if (b[1] == 'P') {
			url = '/getPendingBroadCastMessageList';
			data = "";
		}
		postData(url, data);
	});
	$('#tabnew').on('click', '.goTax', function() {
		// $(".goTax").click(function(){
		var dataValues = $(this).data("value");
		// var values=dataValues.split(",");
		var messageId = dataValues;
		// var lvId=values[1];
		var url = '/getBroadCastMessages';
		var data = "taxCode," + taxCode;
		postData(url, data);
	});
	$('.getBroadCast').click(function() {

		var a = $(this).data("value");
		var b = a.split(",");
		url = '/getBroadCastMessages';
		data = "mgsid," + b[0] ;
		postData(url, data);
	});
});