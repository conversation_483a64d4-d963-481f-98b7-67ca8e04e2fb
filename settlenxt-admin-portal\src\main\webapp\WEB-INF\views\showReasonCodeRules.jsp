<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ page import="java.time.format.DateTimeFormatter"%>
<script type="text/javascript">
	<c:if test="${showReasonCode eq 'YES'}">
	var actionColumnIndex = 8;
	var firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
	<c:if test="${pendingReasonCode eq 'YES'}">
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 11;
	var firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 11;
	var firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
	</c:if>
</script>



<script src="./static/js/validation/showReasonCodeRules.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script>
var recordIdpending=[];

<c:if test="${not empty reasonCodePendingList}">
<c:forEach items="${reasonCodePendingList}" var="operator">
<c:if test="${operator.requestState eq 'P'}">


recordIdpending.push(${operator.seqId});


</c:if>
</c:forEach>
</c:if>
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>

<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: bottom;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>

<div class="row">


	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>

<!-- Modal -->

<div class="modal fade" id="toggleModal" tabindex="-1" role="dialog"
	aria-labelledby="toggleModal" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Record IDs</label>
				<p id="detailsHeadersss" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulk('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulk('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Modal Close-->
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showReasonCode eq 'YES' }">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" onclick="submitForm('/showReasonCodeRules');"
			role="tab" data-toggle="tab"> <span
			class="glyphicon glyphicon-credit-card">&nbsp;</span> <spring:message
				code="reasonCodeRules.mainTab.title" />
		</a>

		<c:choose>
			<c:when test="${pendingReasonCode eq 'YES'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#profile" role="tab"
			onclick="submitForm('/showPendingReasonCodeRules');"
			data-toggle="tab"> <span class="glyphicon glyphicon-ok">&nbsp;</span>
			<spring:message
				code="reasonCodeRules.approvalPendingViewScreen.title" />
		</a>



	</ul>

	

	<div class="tab-content">
	<div>
	<sec:authorize access="hasAuthority('Add Reason Code Rules')">
		<c:if test="${addReasonCodeRules eq 'Yes'}">
			<a class="btn btn-success pull-right btn_align" href="#"
				onclick="submitForm('/addReasonCodeRules');"
				style="margin: -10px 15px 1px 0px;"><em class="glyphicon-plus"></em>
				Add Reason Code</a>
		</c:if>
	</sec:authorize>
	</div>
	
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
			<c:if test="${showReasonCode eq 'YES'}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="reasonCodeRules.mainTab.title" /></span></strong>
							</div>

							
							<div class="panel-body">
								<div class="row">
									         
										<div class="col-sm-12">
		
										
			<button class="btn  pull-right btn_align" id="clearFilters">
				<spring:message code="ifsc.clearFiltersBtn" />
			</button>
			&nbsp; <a class="btn btn-success pull-right btn_align"
				href="#" id="csvExport"> <spring:message
					code="ifsc.csvBtn" />
			</a> <a class="btn btn-success pull-right btn_align" href="#"
				id="excelExport">Excel</a>         
			
		</div>
									        
								</div>
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;"></caption>
										
										<thead>
											<tr>
												<th scope="col"><spring:message code="reasonCode.actionCode" /></th>
												<th scope="col"><spring:message code="reasonCode.reasonCode" /></th>
												<th scope="col"><spring:message code="reasonCode.fieldName" /></th>
												<th scope="col"><spring:message code="reasonCode.relationOperator" /></th>
												<th scope="col"><spring:message code="reasonCode.fieldValue" /></th>
												<th scope="col"><spring:message code="reasonCode.logicalReasonCode" /></th>
												<th scope="col"><spring:message code="am.lbl.createdBy" /></th>
												<th scope="col"><spring:message code="am.lbl.createdDate" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="reasonCode" items="${reasonCodeMasterlist}">
												<tr
													onclick="javascript:viewAction('${reasonCode.seqId}','V')">
													<td>${actionCodeMap[reasonCode.actionCode]}</td>
													<td>${reasonCode.reasonCode}</td>
													<td>${reasonCode.fieldName}</td>
													<td>${relOpMap[reasonCode.relationOperator]}</td>
													<td>${reasonCode.fieldValue}</td>
													<td>${reasonCodeMap[reasonCode.logicalReasonCode]}</td>
													
													<td>${reasonCode.createdBy}</td>
													<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
															value="${reasonCode.createdOn}" /></td>
												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>


			<input type="hidden" id="_TransactToken" name="_TransactToken"
				value="${sessionDTO.transactToken}" />
			<c:if test="${pendingReasonCode eq 'YES'}">

				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<sec:authorize
									access="hasAuthority('Approve Reason Code Rules')">
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveorRejectBulk('A','no')" id="submitButton"
										value="<spring:message code="feeRate.Approve" />" />
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveorRejectBulk('R','no')" id="submitButton"
										value="<spring:message code="feeRate.Reject" />" />
								</sec:authorize>
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="reasonCode.approvalTab.title" /></span></strong>
							</div>
							<div class="panel-body">
								<div class="row">
									         
									<div class="col-sm-12">
										 
										<button class="btn  pull-right btn_align" id="clearFilters">
											<spring:message code="ifsc.clearFiltersBtn" />
										</button>
										&nbsp; <a class="btn btn-success pull-right btn_align"
											href="#" id="csvExport"> <spring:message
												code="ifsc.csvBtn" />
										</a> <a class="btn btn-success pull-right btn_align" href="#"
											id="excelExport">Excel</a>         
										
									</div>
									        
								</div>
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display:none;">REASON CODE RULES</caption>
										<thead>
											<tr>

												<sec:authorize
													access="hasAuthority('Approve Reason Code Rules')">
													<th scope="col"><input type=checkbox name='selectAllCheck'
														id="selectAll" data-toggle="modal"
														data-target="toggleModal" value=""></input></th>
												</sec:authorize>
												<th scope="col"><spring:message code="reasonCode.actionCode" /></th>
												<th scope="col"><spring:message code="reasonCode.reasonCode" /></th>
												<th scope="col"><spring:message code="reasonCode.fieldName" /></th>
												<th scope="col"><spring:message code="reasonCode.relationOperator" /></th>
												<th scope="col"><spring:message code="reasonCode.fieldValue" /></th>
												<th scope="col"><spring:message code="reasonCode.logicalReasonCode" /></th>
												<th scope="col"><spring:message code="reasonCode.status" /></th>
												<th scope="col">Request By</th>
												<th scope="col">Request Date</th>
												<th scope="col"><spring:message code="sm.lbl.checkerComments" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="pendingReasonCode"
												items="${reasonCodePendingList}">
												<c:if test="${pendingReasonCode.requestState eq 'P' }">
													<tr
														onclick="javascript:viewAction('${pendingReasonCode.seqId}','P')">
														<sec:authorize
															access="hasAuthority('Approve Reason Code Rules')">
															<td onclick=event.stopPropagation()><input
																type=checkbox name='type' id="selectSingle"
																onclick="mySelect();"
																value='${pendingReasonCode.seqId}'></input></td>
														</sec:authorize>
												</c:if>

												<c:if test="${pendingReasonCode.requestState eq 'R' }">
													<tr
														onclick="javascript:viewAction('${pendingReasonCode.seqId}','R')">
														<sec:authorize
															access="hasAuthority('Approve Reason Code Rules')">
															<td onclick=event.stopPropagation()><input
																type=checkbox name='types' style="display: none"></input></td>
														</sec:authorize>
												</c:if>


	<td>${actionCodeMap[pendingReasonCode.actionCode]}</td>
	<td>${pendingReasonCode.reasonCode}</td>
													
													<td>${pendingReasonCode.fieldName}</td>
													<td>${relOpMap[pendingReasonCode.relationOperator]}</td>
													<td>${pendingReasonCode.fieldValue}</td>
														<td>${reasonCodeMap[pendingReasonCode.logicalReasonCode]}</td>
													
													
											
												<td>${pendingReasonCode.requestState =='P' ? 'Pending for Approval' : 'Rejected'}</td>
												<td>${pendingReasonCode.createdBy}</td>
												<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
														value="${pendingReasonCode.createdOn}" /></td>
												<td>${pendingReasonCode.checkerComments}</td>
												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>

		</div>

	</div>



</div>
