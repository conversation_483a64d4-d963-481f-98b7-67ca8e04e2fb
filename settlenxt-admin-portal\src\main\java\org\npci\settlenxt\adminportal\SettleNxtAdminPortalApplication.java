package org.npci.settlenxt.adminportal;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.view.InternalResourceViewResolver;
import org.springframework.web.servlet.view.JstlView;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootApplication(exclude = KafkaAutoConfiguration.class)
public class SettleNxtAdminPortalApplication {

	public static void main(String[] args) {
		try {
			log.info("Starting application");
			SpringApplication.run(SettleNxtAdminPortalApplication.class, args);

		} catch (Exception e) {
			log.error(" Exception Occured:: ", e);
		}

	}

	@Bean
	public ViewResolver internalResourceViewResolver() {
		InternalResourceViewResolver bean = new InternalResourceViewResolver();
		bean.setViewClass(JstlView.class);
		bean.setPrefix("/WEB-INF/views/");
		bean.setSuffix(".jsp");
		bean.setExposeContextBeansAsAttributes(true);
		return bean;
	}

}
