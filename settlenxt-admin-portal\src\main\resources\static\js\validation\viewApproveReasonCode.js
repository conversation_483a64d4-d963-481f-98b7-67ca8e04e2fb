	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	
	}
	function backAction(type, action) {
		var url = action;
		var data = "userType," + type ;
		postData(url, data);
	}
	
	function postAction(_action) {
		var reasonCode;
		var remarks;
		var url;
		var data;
	if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				reasonCode = $("#reasonCode").val();
				
				remarks=$("#rejectReason").val();
		
				url = '/approveReasonCode';
				data = "reasonCode," + reasonCode + ",status," + "Approved" + ",crtuser,"
						+ crtuser  + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
						reasonCode = $("#reasonCode").val();
					let crtuser= $("#crtuser").val();
					remarks = $("#rejectReason").val();
					
					url = '/approveReasonCode';
	
					data = "reasonCode," + reasonCode + ",status," + "Rejected"
							+ ",crtuser," + crtuser + ",remarks," + remarks;
					postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		
	
	

