window.history.forward();

function noBack() {
	window.history.forward();
}

function urlPostAction(type, action) {
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var data = "_TransactToken,"+tokenValue;
	postData(action, data);
}

function urlPostActionWithData(action, additionalData) {
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var data = "_TransactToken,"+tokenValue;
	if (additionalData != undefined && additionalData != null) {
		data = data + "," + additionalData;
	}
	postData(action, data);
}

function postData(action, data) {
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	linkurl = pathName + action;
	var form = document.createElement("form");
	form.method = "POST";
	
	
	var tokenValue=null
	if(document.getElementsByName("_TransactToken")[0])
		tokenValue= document.getElementsByName("_TransactToken")[0].value;
    if(!data || data.trim()==""){
     	data="_TransactToken,"+tokenValue;
     }else{
		data = data+",_TransactToken,"+tokenValue;
	}
	var parameters = data.split(",");

	for (var i = 0; i < parameters.length; ++i) {
	var dynInput = document.createElement("input");
		parameters[i]= $('<div>').text(parameters[i]).html();
		var x="hidden";
			x= $('<div>').text(x).html();
		dynInput.setAttribute("type", x);
		dynInput.setAttribute("id", parameters[i]);
		dynInput.setAttribute("name", parameters[i]);
		++i;
		dynInput.setAttribute("value", parameters[i]);

		form.appendChild(dynInput);
	}
	document.body.appendChild(form); // added this	for firefox Browser
	encodeForm(form);	//Added by piyush for form encode

	form.action = linkurl;
	form.submit();
}


$(document).ready(function () {
	$('[name=download]')
		.click(
			function () {
				url = '/download';
				fileName = $(this).attr("fileName");
				filePath = $(this).attr("filePath");

				var tokenValue = document
					.getElementsByName("_TransactToken")[0].value;
				var data = "_TransactToken,"
					+ tokenValue + ",fileName,"
					+ fileName + ",filePath,"
					+ filePath;
				postData(url, data);
			});
	$('.redirect').click(
		function () {
			var url = $(this).attr("url");
			var tokenValue = $("[name=_TransactToken]")
				.get(0).value;
			var data = "_TransactToken," + tokenValue;
			postData(url, data);
		});
});

//Added by prashant on 1.04.2016

function maxLengthTextArea(id) {
	var length = $('#' + id).attr("maxlength");
	if ($('#' + id).val().length > length) {
		$('#error' + id).text('Maximum ' + length + ' character allowed.');
		return false;
	} else {
		$('#error' + id).text('');
		return true;
	}
}

function clickAndDisable(link) {
	// disable subsequent clicks
	link.onclick = function (event) {
		event.preventDefault();
	}
}

function urlGetAction(action) {
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	linkurl = pathName + action;
	window.location.href = linkurl;
}

function getURL(url) {
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	linkurl = pathName + url;
	return linkurl;
}

async function postDataFromFetch(action, data) {



	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	linkurl = pathName + action;
	var form = document.createElement("form");
	form.id = "__hidden_FORM";
	form.method = "POST";

	var parameters = data.split(",");

	for (var i = 0; i < parameters.length; ++i) {
		var dynInput = document.createElement("input");
		dynInput.setAttribute("type", "hidden");
		dynInput.setAttribute("id", parameters[i]);
		dynInput.setAttribute("name", parameters[i]);
		++i;
		dynInput.setAttribute("value", parameters[i]);

		form.appendChild(dynInput);
	}
	document.body.appendChild(form); // added this	for firefox Browser
	encodeForm(form);	//Added by piyush for form encode

	form.action = linkurl;
	const dataObject = new FormData();
	for (const element of document.querySelectorAll(`#${form.id} input`)) {

		dataObject.append(element.name,element.value);

	}
	const options = {
			method: "POST",
			body:dataObject,
            mode: "no-cors",
            cache: "no-cache",
            credentials: "same-origin",
			headers:{
				"Content-Type": "form-data"
			}
		};
	const response = await fetch(linkurl,options);
	
	return response;
}
