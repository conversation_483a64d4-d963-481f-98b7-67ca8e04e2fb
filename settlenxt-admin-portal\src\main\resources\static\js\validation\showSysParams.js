$(document).ready(function() {
	 sysParamIds = [];
	
	/* Initialization of datatables */
	$(document).ready(function() {

		 $("#tabnew").DataTable({

			initComplete: function() {
				var api = this.api();

				// For each column
				api
					.columns()
					.eq(0)
					.each(function(colIdx) {
						//If first column to be skipped to include the filter for the reasons line check box 
						if (!(colIdx == 0 && firstColumnToBeSkippedInFilterAndSort)) {
							// Set the header cell to contain the input element
							var cell = $('#tabnew thead tr th').eq(
								$(api.column(colIdx).header()).index()
							);
							var title = $(cell).text();
							searchBoxFunc(colIdx, cell, title, api);
						}
					});
				$('#tabnew_filter').hide();
				
			},
			// Disabled ordering for first column in case
			columnDefs: [
				{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
			],
			"order": [],
			dom: 'lBfrtip',
			buttons: [
				{
					extend: 'excelHtml5',
					text: 'Export',
					filename: 'Sys Param',
					header: 'false',
					title: null,
					sheetName: 'Sys Param',
					className: 'defaultexport',
					exportOptions: {
						//columns: 'th:not(:last-child)'
					}
				},
				{
					extend: 'csvHtml5',
					text: 'Export',
					filename: 'Sys Param',
					header: 'false',
					title: null,
					sheetName: 'Sys Param',
					className: 'defaultexport',
					exportOptions: {
						//columns: 'th:not(:last-child)'
					}
				}

			],

			searching: true,
			info: true,
			lengthChange: true,
			bLengthChange: true,
		});
	});

	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});


	$("#excelExport").on("click", function() {
		$(".buttons-excel").trigger("click");
	});

	$("#csvExport").on("click", function() {
		$(".buttons-csv").trigger("click");
	});

	$("#clearFilters").on("click", function() {
		$(".search-box").each(function() {
			$(this).val("");
			$(this).trigger("change");
		});
	});




	$("#selectAll").click(function() {
		$('#errorStatus4').hide();
		$("input[type=checkbox]").prop('checked', $(this).prop('checked'));

		var sysParamData = document.getElementById("sysParam");
		sysParamData.innerHTML = sysParamIds.length + "     " + "records are selected";


		if (sysParamIds.length > 0) {

			if ($('#selectAll').is(':checked')) {
				$("#toggleModal").modal('show');
			}
			else {
				$("#toggleModal").modal('hide');
			}
		}

		else {
			var i = 0;
			var sysParamIds2 = [];
			$("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
				sysParamIds2.push(this.value);
				i++;
			});
			if (sysParamIds2.length > 0) {


				if (sysParamListPendings.length > 0) {
					sysParamData.innerHTML = sysParamListPendings.length + "     " + "records are selected";

					if ($('#selectAll').is(':checked')) {
						$("#toggleModal").modal('show');

					}
					else {
						$("#toggleModal").modal('hide');

					}
				}
			}
		}

	});

});

function searchBoxFunc(colIdx, cell, title, api) {
	var cursorPosition = null;
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function() {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
                sysParamIds = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
                        sysParamIds.push(this.value);
                        i++;
                    });
                }
                else {
                    sysParamIds = [];
                }

            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
    
}

function mySelect(){
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	    
	 var sysParamData = document.getElementById("sysParam");
     console.log(array);
	 if(array.length==sysParamListPendings.length){
		 $('#selectAll').prop('checked', true);
		 
		 sysParamData.innerHTML = sysParamListPendings.length+"     "+"records are selected";
			 $("#toggleModal").modal('show');
	 }
	 else{
		 $("#toggleModal").modal('hide');
		 
	 }
	
}

function ApproveOrRejectBulk(type, action) {

	var url = '/approveOrRejectBulkSysParam';
	var data;

	var array = [];
	if (action == 'No') {
		$("input:checkbox[name=type]:checked").each(function() {
			array.push($(this).val());
		});
	}
	else if (action == 'All') {

		if (sysParamIds.length > 0) {
			array = sysParamIds;
		} else {
			array = sysParamListPendings;
		}

	}

	var bulkSysParamList = "";
	for (var i of array) {
		bulkSysParamList = bulkSysParamList + i + "|";
	}


	console.log(array)

	if (array.length != 0) {
		if (type == 'A') {

			data = "status," + "A" + ",bulkSysParamList," + bulkSysParamList + ",remarks," + "Approved";
		}

		else if (type == 'R') {

			data = "status," + "R" + ",bulkSysParamList," + bulkSysParamList + ",remarks," + "Rejected";
		}

		postData(url, data);
		$('#errorStatus2').hide();
		$('#errorStatus2').html("");

	} else {

		$('#errorStatus2').html("Please select one or more records to bulk approve/reject records");
		$('#errorStatus2').show();
	}

}


function submitForm(url) {
	var data = "";
	postData(url, data);
}

function viewSysParamsInfo(sysType,sysKey, action) {
	var data = "sysType," + sysType+",sysKey," + sysKey;
	postData(action, data);
}




function deselectAll() {

	$('#selectAll').prop('checked', false);
	var ele = document.getElementsByName('type');
	for (var i of ele) {
		if (i.type == 'checkbox')
			i.checked = false;
	}

}
