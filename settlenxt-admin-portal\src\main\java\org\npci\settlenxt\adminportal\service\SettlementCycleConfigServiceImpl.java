package org.npci.settlenxt.adminportal.service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.SettlementCycleConfigRepository;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.SettlementCycleConfigDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SettlementCycleConfigServiceImpl implements SettlementCycleConfigService {

	@Autowired
	SessionDTO sessionDTO;

	@Autowired
	SettlementCycleConfigRepository settlementCycleConfigRepository;

	static int getHour(String dateTime) {

		return Integer.parseInt(dateTime.substring(0, dateTime.indexOf(':')));
	}

	static int getMinute(String dateTime) {

		return Integer.parseInt(dateTime.substring(dateTime.indexOf(':') + 1));
	}

	static int getSeconds(String dateTime) {

		return Integer.parseInt(dateTime.substring(dateTime.indexOf(':') + 4, dateTime.indexOf(':') + 6));
	}

	@Override
	public List<SettlementCycleConfigDTO> getApprovedSettlementFromMain() {

		return settlementCycleConfigRepository.getApprovedDataFromMain();
	}

	@Override
	public List<SettlementCycleConfigDTO> getPendingSettlementFromStg() {

		return settlementCycleConfigRepository.getPendingSettlementFromStg();
	}

	@Override
	public SettlementCycleConfigDTO updateApproveOrRejectBulkSettlementCycle(String settlementCycleNoList,
			String status, String remarks) {

		String[] idArray = settlementCycleNoList.split("\\|");

		SettlementCycleConfigDTO settlementSNo = new SettlementCycleConfigDTO();
		int[] values = Arrays.stream(idArray).mapToInt(Integer::parseInt).toArray();
		List<Integer> srNoList = Arrays.stream(values).boxed().collect(Collectors.toList());

		List<SettlementCycleConfigDTO> settlementConfigInfo = settlementCycleConfigRepository.fetchSrNoList(srNoList);

		Map<Integer, List<SettlementCycleConfigDTO>> settlementConfigMap = settlementConfigInfo.stream()
				.collect(Collectors.groupingBy(SettlementCycleConfigDTO::getSrNo));

		for (String id:idArray) {

			try {
				List<SettlementCycleConfigDTO> settementConfDto = settlementConfigMap.get(Integer.parseInt(id));
				SettlementCycleConfigDTO settlementCycleDto = settementConfDto.get(0);
				if (settlementCycleDto == null) {
					throw new SettleNxtException("Exception occurred with Settlement Config Id " + id, "");
				} else {
					settlementCycleDto = approveOrRejectSettlement(String.valueOf(settlementCycleDto.getSrNo()), status,
							remarks);
					settlementSNo.setStatusCode(settlementCycleDto.getStatusCode());
				}

			} catch (Exception ex) {

				throw new SettleNxtException("Exception for Settlement Config Id" + id, "", ex);

			}
		}

		return settlementSNo;

	}

	@Override
	public SettlementCycleConfigDTO getSettlementDetails(String srNo) {

		return settlementCycleConfigRepository.getSettlementDetails(Integer.parseInt(srNo));
	}

	@Override
	public SettlementCycleConfigDTO approveOrRejectSettlement(String srNo, String status, String remarks) {

		SettlementCycleConfigDTO settlementCycleConfigDTO = settlementCycleConfigRepository
				.getSettlementDetails(Integer.parseInt(srNo));

		settlementCycleConfigDTO.setRequestState(status);
		settlementCycleConfigDTO.setCheckerComments(remarks);

		if (status.equals(CommonConstants.REQUEST_STATE_APPROVED)) {
			settlementCycleConfigDTO.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			if (settlementCycleConfigRepository.searchSrNoInMain(Integer.parseInt(srNo)) != null) {
				settlementCycleConfigRepository.updateSrNoInMain(settlementCycleConfigDTO);
			} else {
				settlementCycleConfigRepository.insertSrNoInMain(settlementCycleConfigDTO);
			}
			settlementCycleConfigDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
		} else if (status.equals(CommonConstants.REQUEST_STATE_REJECTED)) {
			settlementCycleConfigDTO.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
		}

		settlementCycleConfigRepository.updateRequestStateInStg(settlementCycleConfigDTO);

		return settlementCycleConfigDTO;
	}

	@Override
	public SettlementCycleConfigDTO getSrNoInfoFromStg(int i) {
		return settlementCycleConfigRepository.getSrNoInfoFromStg(i);
	}

	@Override
	public void deleteSettlement(String srNo) {
		SettlementCycleConfigDTO settlementCycleConfigDTO = settlementCycleConfigRepository
				.searchSrNoInMain(Integer.parseInt(srNo));
		if (settlementCycleConfigDTO != null) {
			settlementCycleConfigDTO.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			settlementCycleConfigDTO.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			settlementCycleConfigRepository.editSettlement(settlementCycleConfigDTO);
		} else
			{
			settlementCycleConfigRepository.deleteSettlement(Integer.parseInt(srNo));
			}
	}

	@Override
	public SettlementCycleConfigDTO getSrNoInfoFromMain(int i) {
		return settlementCycleConfigRepository.getSrNoInfoFromMain(i);

	}

	@Override
	public List<CodeValueDTO> getLookupDataSorted(String data) {

		return settlementCycleConfigRepository.getLookupDataSorted(data);
	}

	@Override
	public SettlementCycleConfigDTO addSettlementCycle(SettlementCycleConfigDTO settlementCycleConfigDTO) {
		try {
			settlementCycleConfigDTO.setSrNo(settlementCycleConfigRepository.getSettlementCycleConfigSeqId());
			settlementCycleConfigDTO.setCreatedBy(sessionDTO.getUserName());
			settlementCycleConfigDTO.setCreatedOn(LocalDateTime.now());
			settlementCycleConfigDTO.setLastUpdatedBy(null);
			settlementCycleConfigDTO.setLastUpdatedOn(null);
			settlementCycleConfigDTO.setActive("true".equals(settlementCycleConfigDTO.getIsActiveValue()));
			SimpleDateFormat format = new SimpleDateFormat("HH:mm");
			settlementCycleConfigDTO.setStartHour(
					settlementCycleConfigDTO.getStartHour() + ":" + settlementCycleConfigDTO.getStartMin());
			settlementCycleConfigDTO
					.setEndHour(settlementCycleConfigDTO.getEndHour() + ":" + settlementCycleConfigDTO.getEndMin());
			Date date1 = format.parse(settlementCycleConfigDTO.getStartHour());
			Date date2 = format.parse(settlementCycleConfigDTO.getEndHour());
			long difference = date2.getTime() - date1.getTime();
			long diffMinutes = difference / (60 * 1000) % 60;
			settlementCycleConfigDTO.setTotalICNkeys((int) (diffMinutes / 15));
			settlementCycleConfigDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
			settlementCycleConfigDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

			settlementCycleConfigRepository.insertSettlementCycleIntoStg(settlementCycleConfigDTO);
		} catch (ParseException e) {
			throw new SettleNxtException("Date Parse Exception in addSettlementCycle ", "", e);
		}
		return settlementCycleConfigDTO;

	}

	@Override
	public SettlementCycleConfigDTO updateSettlementCycle(SettlementCycleConfigDTO settlementCycleConfigDTO) {
		try {
			settlementCycleConfigDTO.setActive("true".equals(settlementCycleConfigDTO.getIsActiveValue()));
			settlementCycleConfigDTO.setLastUpdatedBy(sessionDTO.getUserName());
			settlementCycleConfigDTO.setLastUpdatedOn(LocalDateTime.now());
			settlementCycleConfigDTO.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
			settlementCycleConfigDTO.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

			SimpleDateFormat format = new SimpleDateFormat("HH:mm");
			settlementCycleConfigDTO.setStartHour(
					settlementCycleConfigDTO.getStartHour() + ":" + settlementCycleConfigDTO.getStartMin());
			settlementCycleConfigDTO
					.setEndHour(settlementCycleConfigDTO.getEndHour() + ":" + settlementCycleConfigDTO.getEndMin());
			Date date2 = format.parse(settlementCycleConfigDTO.getEndHour());
			Date date1 = format.parse(settlementCycleConfigDTO.getStartHour());
			long difference = date2.getTime() - date1.getTime();
			long diffMinutes = difference / (60 * 1000) % 60;
			settlementCycleConfigDTO.setTotalICNkeys((int) (diffMinutes / 15));
			settlementCycleConfigRepository.updateSettlementCycleStg(settlementCycleConfigDTO);
		} catch (Exception ex) {
			throw new SettleNxtException("Error in Updating Settlement Config", "", ex);
		}
		return settlementCycleConfigDTO;

	}

	@Override
	public boolean validateSettlementTime(String flow, String startHr, String endHr, String cycleNum, String prodId) {

		SettlementCycleConfigDTO settlementCycleConfigDTO = new SettlementCycleConfigDTO();
		settlementCycleConfigDTO.setStartHour(startHr);
		settlementCycleConfigDTO.setEndHour(endHr);
		settlementCycleConfigDTO.setCycleNumber(cycleNum);
		settlementCycleConfigDTO.setProductId(prodId);

		int i = 0;

		if ("editFlow".equals(flow)) {
			i = settlementCycleConfigRepository.validateSettlementTimeEdit(settlementCycleConfigDTO);
		} else {
			i = settlementCycleConfigRepository.validateSettlementTime(settlementCycleConfigDTO);
		}

		return i > 0;

	}

	@Override
	public boolean validateOverLapSettlementTime(String prodId, String startHr, String endHr, String cycleNum)
			throws ParseException {

		List<SettlementCycleConfigDTO> settlementTimeList = settlementCycleConfigRepository.getAllTimeIntervals(prodId,
				cycleNum);
		boolean res = false;
		int size = settlementTimeList.size();
		if (size > 0) {
			for (int i = 0; i < size; i++) {
				res = hasOverlap(settlementTimeList.get(i).getStartHour(), startHr,
						settlementTimeList.get(i).getEndHour(), endHr);
				if (res) {
					break;
				}
			}
		}
		return res;
	}

	private boolean hasOverlap(String startHour, String startHr, String endHour, String endHr) {

		boolean flag = true;
		int start1Minutes = Integer.parseInt(startHour.split(":")[0]) * 60 + Integer.parseInt(startHour.split(":")[1]);
		int start2Minutes = Integer.parseInt(startHr.split(":")[0]) * 60 + Integer.parseInt(startHr.split(":")[1]);
		int end1Minutes = Integer.parseInt(endHour.split(":")[0]) * 60 + Integer.parseInt(endHour.split(":")[1]);
		int end2Minutes = Integer.parseInt(endHr.split(":")[0]) * 60 + Integer.parseInt(endHr.split(":")[1]);

		if (start1Minutes == end2Minutes) {

			return checkcase1(start1Minutes, start2Minutes, end1Minutes);

		}
		if (end1Minutes == start2Minutes) {

			flag = checkcase2(start1Minutes, end1Minutes, end2Minutes);

		} else {
			flag = start1Minutes <= end2Minutes && end1Minutes >= start2Minutes;
		}
		return flag;

	}

	private boolean checkcase2(int start1Minutes, int end1Minutes, int end2Minutes) {

		boolean flag = true;
		if (start1Minutes < end2Minutes && end1Minutes < end2Minutes) {
			flag = false;
		}

		return flag;
	}

	private boolean checkcase1(int start1Minutes, int start2Minutes, int end1Minutes) {

		boolean flag = true;
		if (start2Minutes < start1Minutes && start2Minutes < end1Minutes) {
			flag = false;
		}

		return flag;
	}

}
