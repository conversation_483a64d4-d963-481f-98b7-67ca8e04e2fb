package org.npci.settlenxt.adminportal.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;


import org.springframework.stereotype.Component;

import lombok.Data;

@Component
@Data
public class CycleStatusMonitorDTO {
	
    private int guid;
    private String productCode;
    private LocalDate cycleDate;
    private String cycleNumber;
    private String activityCode;
    private String status;
    private String childFlag;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String key;
    private String value;

}
