package org.npci.settlenxt.adminportal.dto;

import java.time.LocalDateTime;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JGlobalMap
public class KafkaTransactionDetailDTO {

	private String nAcqInstCd;
	private String nActnCd;
	private double nAmtTxn;
	private String nARD;
      /* nCcyCdTxn*/
	private String nCrdAcpBussCd;
	private String nCrdAcpCity;
	private String nCrdAcpCtryCd;
	private String nCrdAcpIDCd;
	
	private String nCrdAcpStNm;
	
	private String nCrdAcptTrmId;
	private LocalDateTime nDtTmLcTxn;
	private String nFunCd;
	private String nMTI;
	private String nPAN;
	private String nPosCondCd;
	private String nPosDataCd;
	private String nPosEntMode;
	private double nProcCd;
	private String nRecNum;
	private String nServCd;
	private String nTxnOrgInstCd;
	
}
