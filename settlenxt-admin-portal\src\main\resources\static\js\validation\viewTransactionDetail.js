var finalFileList = [];
var fileUploadList = [];
var inValidFileList = new Set();
let allowedFileNameExtensions = [];
var monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
$(document).ready(function() {
		$('#fileUploadSuccessMsg').hide();
		$('#fileUploadErrorMsg').hide();
			$(".card-body-document").hide();
			$(".collapse.in").each(
			function() {
				$(this).siblings(".panel-heading").find(
					".glyphicon:first").addClass(
					"glyphicon-minus").removeClass(
					"glyphicon-plus");
			});

			$(".collapse").on('show.bs.collapse',
			function() {
				$('a[href="#'+$(this).attr("id")+'"] span')
				.removeClass("glyphicon-plus")
				.addClass("glyphicon-minus");
			}).on('hide.bs.collapse',
			function() {
				$('a[href="#'+$(this).attr("id")+'"] span')
				.removeClass("glyphicon-minus")
				.addClass("glyphicon-plus");
			});
			
			document.querySelectorAll(".tStampLocal").forEach(item=>{
			if(item.innerText !== "N/A" && item.innerText !== ""){
			    const newDate = new Date(item.innerText); 
			    var day = addBelow10Zero(newDate.getDate());
			    var month = addBelow10Zero(newDate.getMonth() + 1);
			    var year =  newDate.getFullYear();
			    var hours = addBelow10Zero(`${newDate.getHours()}`);
			    var minutes = addBelow10Zero(`${newDate.getMinutes()}`);
			    var seconds = addBelow10Zero(`${newDate.getSeconds()}`);
			    item.innerText = `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
			   }
			})
			document.querySelectorAll(".rgcsSettlementDate,.deadlineDate").forEach(item=>{
			if(item.innerText !== "N/A" && item.innerText !== ""){
			    const newDate = new Date(item.innerText); 
			    var day = addBelow10Zero(newDate.getDate());
			    var month = monthNames[(newDate.getMonth())];
			    var year =  newDate.getFullYear();
			    item.innerText = `${day} ${month} ${year}`;
			   }
			})
			
			document.querySelectorAll(".raiseDateAndTime").forEach(item=>{
			if(item.innerText !== "N/A" && item.innerText !== ""){
			    const newDate = new Date(item.innerText.replace("IST","")); 
			    var day = addBelow10Zero(newDate.getDate());
			    var month = monthNames[(newDate.getMonth())];
			    var year =  newDate.getFullYear();
			    var hours = addBelow10Zero(`${newDate.getHours()}`);
			    var minutes = addBelow10Zero(`${newDate.getMinutes()}`);
			    var seconds = addBelow10Zero(`${newDate.getSeconds()}`);
			    item.innerText = `${day} ${month} ${year} ${hours}:${minutes}:${seconds}`;
			   }
			})
			
			document.querySelectorAll(".disputeFee").forEach(item=>{
			if(item.innerText !== "N/A" && item.innerText !== ""){
			    let fee = parseFloat(item.innerText); 
			    item.innerText = fee.toFixed(2);
			   }
			})
			
			$("#files").change(function(_e){
				saveDispDoc();
			});
			
			$('.appRejMust').hide();
	   		$('.remarkMust').hide();
	    
	    	hideAndShowRemarkReason();
			
		
			var tableElement = document.querySelectorAll('table[id="tabId"]');
			
			for(var te of tableElement){
			    var tdElements = te.getElementsByTagName("td");
			    for(var tde of tdElements){
			        tde.className = "col-md-3";
			    }
			}
			
			
			var filExtensionList = document.getElementById('fileExtensionList').value;
			allowedFileNameExtensions = filExtensionList.split(',');
		});
function hideAndShowRemarkReason() {
    $('#apprej').change(function() {
        if ($("#apprej").val() != "N") {
            $(".appRejMust").hide();
        }
        else {
            $(".appRejMust").show();
            $(".remarkMust").hide();
        }
    });
    $('#rejectReason').change(function() {
        if ($("#rejectReason").val().trim() != "") {
            $(".remarkMust").hide();
        }
        else {
            $(".remarkMust").show();
        }
    });
}

function backAction(type, action) {
	var url = action;
	
	var searchedFromDate = document.getElementById("searchedFromDate").value;
	var searchedToDate = document.getElementById("searchedToDate").value;
	var searchedRrn = document.getElementById("searchedRrn").value;
	var searchedTokenPan = document.getElementById("searchedTokenPan").value;
	var searchedActionCode = document.getElementById("searchedActionCode").value;
	var searchedPartId = document.getElementById("searchedPartId").value; 
	var data = "searchType," + encodeURI(type) + ",fromDate," + encodeURI(searchedFromDate) + ",toDate," + encodeURI(searchedToDate) + ",rrn," + encodeURI(searchedRrn) + ",tokenPan," + encodeURI(searchedTokenPan) + ",searchedActionCode," + encodeURI(searchedActionCode) + ",partId," + encodeURI(searchedPartId);
	postData(url, data);
}

function openCustCompInfo(){
	$("#custComplaintModal").modal("show");	
}

function backViewTxnAction(type, action) {
	var url = action;
	var searchedFromDate = document.getElementById("searchedFromDate").value;
	var searchedToDate = document.getElementById("searchedToDate").value;
	var searchedRrn = document.getElementById("searchedRrn").value;
	var searchedTokenPan = document.getElementById("searchedTokenPan").value;
	var searchedActionCode = document.getElementById("searchedActionCode").value;
	var searchedPartId = document.getElementById("searchedPartId").value;
	var searchedCrn = document.getElementById("searchedCrn").value;
	var searchedSchemeCodeBank = document.getElementById("searchedSchemeCodeBank").value;
	var data = "userType," + type + ",fromDate," + searchedFromDate + ",toDate," + searchedToDate + ",rrn," + searchedRrn + ",tokenPan," + searchedTokenPan + ",searchedActionCode," + searchedActionCode + ",partId," + searchedPartId + ",complaintNumber," + searchedCrn + ",schemeCodeBank," + searchedSchemeCodeBank;
	postData(url, data);
}

function backViewIncomingTxnAction(type, action) {
	url = action;
	var searchedFromDate = document.getElementById("searchedFromDate").value;
	var searchedToDate = document.getElementById("searchedToDate").value;
	var searchedSchemeCodeBank = document.getElementById("schemeCode").value;
	var searchedFuncCodeDescription = document.getElementById("searchedFuncCodeDescription").value;
	var searchedMessageDirection = document.getElementById("searchedMessageDirection").value;
	var data = "userType," + type + ",fromDate," + searchedFromDate + ",toDate," + searchedToDate + ",schemeCode," + searchedSchemeCodeBank + ",funcCodeDescription," + searchedFuncCodeDescription + ",messageDirection," + searchedMessageDirection  ;
	postData(url, data);
}

function openDynamicModal() {
	var action = $('#actions').val();
	var jsonData = $('#jsonData').val();
	if (action !== 'SELECT') {
		openDisputeModal(JSON.parse(jsonData), action);
	}
}

function submitData(event=null) {
	const modalId = event ? event.target.id: "createPresentment";
	$('.submitButton' + modalId).prop("disabled",true);
	$('#'+ modalId).modal('hide');
	var url = "/saveMemberActionData";
	
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var mti = document.getElementById("mti").value;
	var funcCode = document.getElementById("funcCode").value;
	var orgFuncCode = document.getElementById("orgFuncCode").value;
	var actionCode = document.getElementById("actionCode").value;
	var amtTran = document.getElementById("amountTransaction").value;
	var cardAcceptorZipCode = document.getElementById("cardAcceptorZipCode").value;
	var merchantTelephoneNumber = document.getElementById("merchantTelephoneNumber").value;
	var cardHolderUID = document.getElementById("cardHolderUID").value;
	var cardHolderIncomeTaxPan = document.getElementById("cardHolderIncomeTaxPan").value;
	var cardAcptAdnlAddress = document.getElementById("cardAcceptorAdditionalAddress").value;
	var merchantIndicator = document.getElementById("merchantIndicator").value;
	
	var partialInd = document.getElementById("fullPartial").value;
	var messageReasonCode = document.getElementById("messageReasonCode").value;
	var internalTrackNo = document.getElementById("internalTrackingNumber").value;
	var docInd = document.getElementById("documentIndicator").value;
	var memMsgTxt = document.getElementById("memberMessageText").value;
	var amtAdd = document.getElementById("amountAdditional").value;
	var ctrlNo = document.getElementById("controlNo").value;
	var txnId = document.getElementById("txnId").value;
	var rrn = document.getElementById("rrnval").value;
	var originalTableName = document.getElementById("orgTabName").value;
	
	
	const data = {"mti":mti,"funcCode":funcCode,"toState":actionCode,"amtTran":amtTran,"cardAcceptorZipCode":cardAcceptorZipCode,
				  "merchantTelephoneNumber":merchantTelephoneNumber,"cardHldrUID":cardHolderUID,"cardHldrInTaxPan":cardHolderIncomeTaxPan,
				  "cardAcptAdnlAddress":cardAcptAdnlAddress,"merchantCatInd":merchantIndicator,"reasonCode":messageReasonCode,"partialInd":partialInd,
	              "docInd":docInd,"memMsgTxt":memMsgTxt,"ctrlNo":ctrlNo,"amtAdd":amtAdd,"internalTrackNo":internalTrackNo,"txnId":txnId,"rrn":rrn,
	              "originalTableName":originalTableName,"orgFuncCode":orgFuncCode };
	
	var formData = new FormData();
	if(docInd !== 'N'){
		var filesLength = document.getElementById('fileArr').files.length;
		for (var x = 0; x < filesLength; x++) {
			formData.append("document", document.getElementById("fileArr").files[x]);
		}
	}
	formData.append("objectData", JSON.stringify(data)); 

	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	var linkurl = pathName + url;
	$.ajax({
						url : linkurl,
						type : "POST",
						cache: false,
						processData: false,  // tell jQuery not to process the data
   						contentType: false,  // tell jQuery not to set contentType
						dataType : "json",
						data: formData,
						"beforeSend": function (xhr) {
                   			 xhr.setRequestHeader('_TransactToken', tokenValue);
               			 },
						
						success : function(response) {
							console.log("RESPONSE: " + response.status);
							if (response.status === "BSUC_0001") {
								$('#actions').hide();
								$('#transitionSuccessMsg').show();
							 } 
							 else {
								document.getElementById("navbar").scrollIntoView({behavior:"smooth"});
								$('#transitionErrorMsg').show();
					 		} 
						},
				 		
			});

}

function showSuccessMessage(msg) {
    const bodyElement = document.querySelector(".body-content");
    const successMessage = document.createElement("h5");
    successMessage.style.margin = "auto";
    successMessage.style.maxWidth = "100%";
    successMessage.style.backgroundColor = "#dff0d8";
    successMessage.style.border = "1pt solid #d6e9c6";
    successMessage.style.padding = "5pt";
    successMessage.innerText = msg;
    successMessage.style.color = "#3c763d";
    successMessage.style.fontWeight = "600";

   bodyElement.insertAdjacentElement("afterbegin", successMessage);

  
}

function populateData(data,resultValue) {
	$("select#messageReasonCode").empty();
	console.log(resultValue);
	
    var options = '<option value="0">--Select--</option>';
    for (var i = 0; i <data.length; i++) {
      options += '<option value="' + resultValue[i] + '">' + resultValue[i] + ' - ' + data[i] + '</option>';
   
    $("select#messageReasonCode").html(options);
  }
}

function submitResponse() {
	
	if ($('#apprej option:selected').val() != "N"){
		if ($("#rejectReason").val().trim() != "") {	
			$("#memberBody").addClass("loadingdata");	
			var tokenValue = document.getElementsByName("_TransactToken")[0].value;
			var txnId = document.getElementById("txnId").value;
			var rrn = document.getElementById("rrnval").value;
			var status = document.getElementById("apprej").value;
			var approve_comment = document.getElementById("rejectReason").value;
			var data = "_TransactToken," + tokenValue +",txnId," +txnId +",rrn,"+rrn  + ",status,"+ status+",approve_comment,"+approve_comment + ",_TransactToken," + tokenValue;
			
			document.querySelector(".approveReject").style.display = 'none';
			document.getElementById("submitButton").disabled = true;
			postDataFromFetch("/approveDispute", data).then(async (response) => {
				console.log(response.status);
				document.getElementById("submitButton").style.visibility = 'hidden';
				document.getElementById("navbar").scrollIntoView({behavior:"smooth"});
				var msg = (await response.text());
				if (response.status === 200 && !(msg === null || msg.trim() === "")) {
					$("#memberBody").removeClass("loadingdata");
					showSuccessMessage(msg);
				}else{
					$("#memberBody").removeClass("loadingdata");
					$('#transitionErrorMsg').show();
				}
			})
			}
			else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
	}
	else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
	}
	
	
}	

function refreshScreen(){
	$("#memberBody").addClass("loadingdata");
	var postUrl = "/transactionDetail";	
	var mti = document.getElementById("orgMti").value;
	var funcCode = document.getElementById("orgFuncCode").value;
	var orgFuncCode = document.getElementById("orgFuncCode").value;
	var txnId = document.getElementById("orgTxnId").value;
	
	var originalTableName = document.getElementById("orgTabName").value;
	
	var acqRefData = document.getElementById("acqRefData").value;
	var caseNo = document.getElementById("caseNo").value;
	var searchedFromDate = document.getElementById("searchedFromDate").value;
	var searchedToDate = document.getElementById("searchedToDate").value;
	var searchedRrn = document.getElementById("searchedRrn").value;
	var searchedTokenPan = document.getElementById("searchedTokenPan").value;
	var searchedActionCode = document.getElementById("searchedActionCode").value;
	var searchedPartId = document.getElementById("searchedPartId").value;
	var searchedCrn = document.getElementById("searchedCrn").value;
	var searchedSchemeCodeBank = document.getElementById("searchedSchemeCodeBank").value;
	var txnStatus = document.getElementById("txnStatus").value;
	var dataInfo = "txnId,"+txnId + ",funcCode,"+funcCode +",mti,"+mti + ",originalTableName," +originalTableName + ",acquirerReferenceData," + acqRefData + ",caseNumber," + caseNo + ",orgFuncCode," + orgFuncCode + ",fromDate," + searchedFromDate + ",toDate," + searchedToDate + ",rrn," + searchedRrn + ",tokenPan," + searchedTokenPan + ",actionCode," + searchedActionCode + ",partId," + searchedPartId  + ",complaintNumber," + searchedCrn +",txnStatus," + txnStatus +",schemeCodeBank," + searchedSchemeCodeBank;
	postData(postUrl, dataInfo);							
}

function addBelow10Zero(val){
    if (val < 10) {
        return "0"+(val);
    }
    else return (val);
}

function clearData(){
	document.getElementById("documentIndicator").value = '';
	document.getElementById("mti").value = '';
	document.getElementById("funcCode").value = '';
	document.getElementById("orgFuncCode").value = '';
	document.getElementById("actionCode").value = '';
	document.getElementById("amountTransaction").value = '';
	document.getElementById("cardAcceptorZipCode").value = '';
	document.getElementById("merchantTelephoneNumber").value = '';
	document.getElementById("cardHolderUID").value = '';
	document.getElementById("cardHolderIncomeTaxPan").value = '';
	document.getElementById("cardAcceptorAdditionalAddress").value = '';
	document.getElementById("merchantIndicator").value = '';
	document.getElementById("reasonSubtype").value = '';
	document.getElementById("fullPartial").value = '';
	document.getElementById("messageReasonCode").value = '';
	document.getElementById("internalTrackingNumber").value = '';
	document.getElementById("documentIndicator").value = '';
	document.getElementById("memberMessageText").value = '';
	document.getElementById("amountAdditional").value = '';
	document.getElementById("controlNo").value = '';
}

function downloadDoc(docPath,_docName){
	$('#documentErrorMsg').hide();
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	$.ajax({
    	url : "docDownloadVerify",
        type: "POST",
		dataType : "json",
				data: {"docPath" : docPath,
					    "_TransactToken" : tokenValue},
       			success: function(response) {
					var url="";
					var data="";
						if (response.status == "BSUC_0001") {
							url = "/downloadDoc";
							data = "docPath," + docPath ;
							postData(url, data);
						}else{
							$('#documentErrorMsg').show();
							document.getElementById("navbar").scrollIntoView({behavior:"smooth"});
						}
				}
		})
}

function downloadCustComplaintDoc(docPath,_docName){
	$('#documentCustCompErrorMsg').hide();
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	$.ajax({
    	url : "docDownloadVerify",
        type: "POST",
		dataType : "json",
				data: {"docPath" : docPath,
					    "_TransactToken" : tokenValue},
       			success: function(response) {
					var url="";
					var data="";
						if (response.status == "BSUC_0001") {
							url = "/downloadDoc";
							data = "docPath," + docPath ;
							postData(url, data);
						}else{
							$('#documentCustCompErrorMsg').show();
							document.getElementById("navbar").scrollIntoView({behavior:"smooth"});
						}
				}
		})
}

function openUploadFile() {
	document.getElementById("fileUploadTitle").innerHTML = "Upload File";
	$("#fileUploadModule").modal("show");
	$(".card-body-document").show();
}

function resetData() {
	$("#errFiles").text('');
	$("#files").val('');
	$('#fileUploadSuccessMsg').hide();
	$('#fileUploadErrorMsg').hide();
	$("#fileUploadSubmit").show();
	$("#resetfileupload").show();
	var tableId = document.getElementById('documentUploadTabId');
	while(tableId.rows.length > 1){
    	tableId.deleteRow(1);
	}
	fileUploadList.splice(0,fileUploadList.length);
	finalFileList.splice(0,finalFileList.length);
	inValidFileList.clear();
	document.getElementById("fileUploadSubmit").disabled = false;
	document.getElementById("resetfileupload").disabled = false;
	
}

function validateFileUpload(){
	$("#errFiles").text("");
	var isXML = false;
	var flag = false;
	

	
	var isDup = false;
	var dupFileNames = "";
	var invalidFileExtensions=[];
	var invalidFileNames=[];
	var filesLength = document.getElementById('files').files.length;
	
	if(filesLength == 0){
			$("#errFiles").text("Please select file");  
			return false;
	}
	else{
		({ flag,  isDup, dupFileNames } = fileValidation(invalidFileNames, invalidFileExtensions, dupFileNames));
		if(invalidFileExtensions.length>0){
			isXML = true;
		 }
		if (flag && !isXML) {
			$("#errFiles").text("File Name is having special characters or file name is too long " + invalidFileNames.map(item => item).join());
			return false;
		} else if(isXML){
			$("#errFiles").text('Invalid file type for '+invalidFileExtensions.map(item => item).join());	
			return false;
		} else if(isDup){
			$("#errFiles").text(dupFileNames.substr(0,dupFileNames.length-1) + " already uploaded");
		}
	}
	return true;
}

function fileValidation(invalidFileNames, invalidFileExtensions,  dupFileNames) {
	var filesLength = document.getElementById('files').files.length;
	var docNamesSet = new Set(docNameList);
	var fileNameRegex = /^[a-zA-Z0-9()_,-., ]+$/;
	let extnIndex="";
	var flag = false;
	var isDup = false;
	
    for (var x = 0; x < filesLength; x++) {
        var uplodedFile = $("#files").get(0).files[x].name;
        if (!docNamesSet.has($("#files").get(0).files[x].name)) {
            var fileName = uplodedFile.substring(0, uplodedFile.lastIndexOf('.'));
            if (fileName.length > 100 || !fileNameRegex.test(fileName)) {
                flag = true;
                invalidFileNames.push(fileName);
                inValidFileList.add(uplodedFile);
            }
            extnIndex = allowedFileNameExtensions.findIndex(extn => uplodedFile.toUpperCase().endsWith(extn.toUpperCase()));
            console.log(extnIndex);
            if (extnIndex == -1) {
                invalidFileExtensions.push(uplodedFile);
                inValidFileList.add(uplodedFile);
            }
        }
        else {
            isDup = true;
            dupFileNames += uplodedFile;
            dupFileNames += ",";
        }
    }
    return { flag, extnIndex, isDup, dupFileNames };
}

function removeSelectedField(docName){
	var row = document.getElementById("remove" + docName);
	row.parentNode.removeChild(row);
	for(var idx = 0;idx < finalFileList.length ; idx++){
		if(finalFileList[idx].name == docName){
			finalFileList.splice(idx,1);
		}
	}
	for(var uidx = 0;uidx < fileUploadList.length ; uidx++){
		if(fileUploadList[uidx] == docName){
			fileUploadList.splice(uidx,1);
		}
	}
	var tableId = document.getElementById('documentUploadTabId');
	var tBody = tableId.getElementsByTagName('tbody')[0];
	var tableRow = tBody.getElementsByTagName('tr');
	if(tableRow.length == 0) {
		$("#errFiles").text('');
		$("#files").val('');
	}
}

function saveDispDoc(){
	validateFileUpload();
	var optionsHTMLStr = '<option value="0">Select</option>';
	for(var dtl of docTypeList){
		optionsHTMLStr+='<option value="'+dtl.value+'">'+dtl.label+'</option>';
	}
	var filesLength = document.getElementById('files').files.length;
	var docNamesSet = new Set(docNameList);
	var docUploadSet = new Set(fileUploadList);
	for (var x = 0; x < filesLength; x++) {
			var fileInfo = document.getElementById("files").files[x];
			var docName = fileInfo.name;
			if(!docNamesSet.has(docName)  && !docUploadSet.has(docName) && !inValidFileList.has(docName)){
				finalFileList.push(fileInfo);
				fileUploadList.push(fileInfo.name);
				$('#docUploadList').append(
						'<tr class="selectedFields" value="'
								+ docName
								+ '" id="remove'
								+ docName
								+ '">'
								+ '<td id="fieldName' + docName+'" class="selectedFieldName" >'
								+ docName
								+ '</td><td>'
								+ '<select onchange="validateUploadedFile()" id="docType'+ docName+'" name="docType'+ docName+'">'
								+ optionsHTMLStr
								+ '</select>'
								+ '<div class="error" id="err' + docName + '"></div>'
								+ '</td><td>'
								+ '<i class="glyphicon glyphicon-remove-circle" id="removeIcon'
								+ docName
								+ '" style="color: blue" onclick="removeSelectedField('
								+ '\''+docName+ '\''
								+ ')" ></i></td></tr>');
				}
			}
}

function validateUploadedFile(){
	var valid = true;
	if(finalFileList.length > 0){
		for(var ffl of finalFileList){
			var fileId = ffl.name;
			var docType = document.getElementById("docType" + fileId).value;
			if(docType == "0"){
				valid = false;
				document.getElementById("err" + fileId ).innerHTML = "Please select the document Type";
			}else{
				document.getElementById("err" + fileId ).innerHTML = "";
			}
		}
	}else{
		valid = false;
		$("#errFiles").text("Please select file"); 
	}
	return valid;
}

function uploadFile(status) {
	var url = "/uploadTxnFile";
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var formData = new FormData();
	var tstampLocal = document.getElementById("tstampLocal").value;
	var txnId = document.getElementById("latestTxnId").value;
	var docFilePath = document.getElementById("docFilePath").value;
	var selectedDocType = [];
	if(validateUploadedFile()){
		for(var ffl of finalFileList){
			var fileId = ffl.name;
			formData.append("document", ffl);
			selectedDocType.push(document.getElementById("docType" + fileId).value);
			document.getElementById("docType" + fileId).disabled = true;
	        document.getElementById("removeIcon" + fileId).style.display = 'none';
		}
		formData.append("txnId",txnId);
		formData.append("tstampLocal",tstampLocal);
		formData.append("status",status);
		formData.append("docFilePath",docFilePath);
		formData.append("docTypeList",selectedDocType);
		formData.append("_TransactToken", tokenValue);
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		var linkurl = pathName + url;
		document.getElementById("fileUploadSubmit").disabled = true;
		document.getElementById("resetfileupload").disabled = true;
		$("#errFiles").text(""); 
		$.ajax({
			method: 'POST',
			url: linkurl,
			cache: false,
			processData: false,
			contentType: false,
			data: formData,
			success: function(response) {
				 if (response == "Success") {
					 	$('#fileUploadSuccessMsg').show();
					 	$("#fileUploadSubmit").hide();
						$("#resetfileupload").hide() ;
						console.log("success");

						 }
						 else {
							$('#fileUploadErrorMsg').show();
							$("#fileUploadSubmit").hide();
							$("#resetfileupload").hide() ;
						 } 
					},
					 error: function(_request, _status, _error) {
						$("#fileUploadSubmit").hide();
						$("#resetfileupload").hide() ;
			         }
		});
		}
}

function hideDate() {
	resetData();
	$("#fileUploadModule").modal("hide");
}