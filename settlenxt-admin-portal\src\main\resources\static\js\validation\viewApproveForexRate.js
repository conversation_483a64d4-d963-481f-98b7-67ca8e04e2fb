$(document).ready(function () {
	$('.appRejMust').hide();
	$('.remarkMust').hide();

	$('#apprej').change(function () {
		if ($("#apprej").val() != "N") {
			$(".appRejMust").hide();
		} else {
			$(".appRejMust").show();
			$(".remarkMust").hide();
		}
	});
	
	$('#rejectReason').on('keyup keypress blur change', function() {
		if ($("#rejectReason").val() != "") {
			$(".remarkMust").hide();
		}else {
			$(".remarkMust").show();
		}
	});

});

function display() {
	$(".appRejMust").hide();
}

function userAction(action, forexRateId) {
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "forexRateId," + forexRateId + ",_vTransactToken,"
		+ tokenValue;
	postData(action, data);
}
function EditForexRate(action, forexRateId, parentPage) {
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "forexRateId," + forexRateId + ",_vTransactToken,"
		+ tokenValue + ",parentPage," + parentPage;
	postData(action, data);
}

function backAction(action) {
	var data = "status," + status;
	postData(action, data);
}

function postAction(action) {
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data;
	var forexRateId;
	var remarks;
	if (maxLengthTextArea('rejectReason')) {
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				forexRateId = $("#forexRateId").val();
				remarks = $("#rejectReason").val();
				data = "forexRateId," + forexRateId + ",status," + "A" + ",_vTransactToken," + tokenValue + ",remarks,"
					+ remarks;
				postData(action, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				forexRateId = $("#forexRateId").val();
				remarks = $("#rejectReason").val();
				data = "forexRateId," + forexRateId + ",status," + "R" + ",_vTransactToken," + tokenValue + ",remarks,"
					+ remarks;
				postData(action, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
	}
}
