@charset "utf-8";
/* CSS Document */

body {
  
    padding-bottom: 40px;
    background-color: #fff;
}




.form-signin-text{
	font-size: 21px;
    color: #9a9797;
}

.footer_login {
    position: absolute;
    bottom: 0;
    width: 100%;
    /*height: 60px;*/
    color:#ccc;
    background-color: #2b2b2b;
}

.form-signin {
  max-width: 330px;
  padding: 15px;
  margin: 0 auto;
}
.form-signin .form-signin-heading,
.form-signin .checkbox {
  margin-bottom: 10px;
}
.form-signin .checkbox {
  font-weight: normal;
}
.form-signin .form-control {
    position: relative;
    height: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 7px;
    font-size: 14px;
    border-radius: 4px;margin-bottom: 10px;
}
.form-signin .form-control:focus {
  z-index: 2;
}
.form-signin input[type="email"] {
  margin-bottom: -1px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.form-signin input[type="password"] {
  margin-bottom: 10px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
/*Edited by Mayur Kadu*/

.new-footer{
	position:absolute;
}


