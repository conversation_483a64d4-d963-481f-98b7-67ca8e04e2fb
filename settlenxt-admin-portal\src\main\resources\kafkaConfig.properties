kafka.bootstrap.servers = 10.40.56.132:9092
kafka.client.id = settlenxtIntegrityChecker
kafka.topic.fileUpload = fileUpload
kafka.topic.disputeTxn = disputeTxn
kafka.topic.dispute_acknowledge = dispute_acknowledge
kafka.publisher.compression.type = none
kafka.topic.currencyFileUpload = CurrencyRateFileTopic

kafka.consumer.poll.interval = 1000
kafka.max.poll.records = 10
kafka.consumer.count = 2
kafka.session.timeout.ms = 15000
kafka.key.deserializer = org.apache.kafka.common.serialization.StringDeserializer
kafka.value.deserializer = org.apache.kafka.common.serialization.StringDeserializer
kafka.auto.commit = false
kafka.auto.commit.inteval.ms = 1000
