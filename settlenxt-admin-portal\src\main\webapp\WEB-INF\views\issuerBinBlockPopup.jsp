<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>

<div class="modal fade" id="toggleIssBinBlock" tabindex="-1" role="dialog" aria-labelledby="toggleIssBinBlock" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Are you sure you want to Block/Unblock the Issuer/Token Bin?</h5>
        <button type="button" class="close" data-dismiss="modal"  aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
     <div>
          <label style="color:blue;font-weight:bold;" >Issuer/Token Bin Number</label>
          <p id="issBinBlockUnblock"/>
          </div>

<input type="hidden" name="modalbinNumber" id="modalbinNumber" value="">
<input type="hidden" name="modalparticipantId" id="modalparticipantId" value="">
<input type="hidden" name="modalbinType" id="modalbinType" value="">
<input type="hidden" name="modalbuttonId" id="modalbuttonId" value="">



     <div class="modal-footer">
        
        <button type="button" class="btn btn-primary" onclick="blockBin(document.getElementById('modalbinNumber').value,document.getElementById('modalparticipantId').value,document.getElementById('modalbinType').value,document.getElementById('modalbuttonId').value);">Approve</button>
      <button type="button" class="btn btn-primary" data-dismiss="modal"  aria-label="Close" >Reject</button>
      </div>
    </div>
  </div>
</div>
