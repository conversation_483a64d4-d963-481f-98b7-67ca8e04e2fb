package org.npci.settlenxt.adminportal.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.DateUtils;
import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.gateway.RestGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

/**
 * <li>This service gives response to api called from ui screen. It is used for
 * getting all cross site ic status details</li>
 * 
 * <AUTHOR>
 *
 */
@Service
public class CrossSiteIcStatusServiceImpl implements ICrossSiteIcStatusService {

	private static final Logger logger = LogManager.getLogger(CrossSiteIcStatusServiceImpl.class);

	@Autowired
	private Environment environment;

	@Autowired
	RestGateway restGateway;

	/**
	 * <li>This method is used to prepare request and send request to the
	 * integrityChecker service to get cross site ic status details.</li>
	 */
	@Override
	public CycleManagementDTO getCrossSiteIcStatusDetails(CycleManagementDTO cycleManagementDTO) {
		if (StringUtils.isBlank(cycleManagementDTO.getSystemDate())) {
			String formattedRequestDate = DateUtils.getTodayLocalDate(DateUtils.YYYY_MM_DD);
			cycleManagementDTO.setSystemDate(formattedRequestDate);
		}
		try {
			JsonObject requestBody = new JsonObject();
			requestBody.addProperty(CommonConstants.SYSTEM_DATE, cycleManagementDTO.getSystemDate());
			String url = environment.getProperty(CommonConstants.INTEGRITY_CHECKER_DOMAIN)
					+ CommonConstants.SETTLENXT_FETCH_CROSS_SITE_IC_DETAILS;
			List<Map<String, String>> cycleDetails = new ArrayList<>();
			String result = restGateway.postAPIForJsonObject(url, requestBody);
			logger.info("Response of cross site status details is {}", result);
			if (StringUtils.isBlank(result)) {
				cycleManagementDTO.setCycleData(cycleDetails);
				return cycleManagementDTO;
			}
			JsonObject respBody = (JsonObject) JsonParser.parseString(result);
			JsonArray cycleData = respBody.getAsJsonArray(CommonConstants.CROSS_SITE_IC_DETAILS);
			cycleDetails = new Gson().fromJson(cycleData, ArrayList.class);
			logger.info("Cycle details {}", cycleDetails);
			cycleManagementDTO.setCycleData(cycleDetails);
		} catch (Exception e1) {
			logger.error("Error occured while getting cross site status details{}", e1.getMessage(), e1);
		}
		return cycleManagementDTO;
	}

	/**
	 * This method is used for retry the internal cycle or outgoing files
	 */
	@Override
	public String retryIcnOrOutgoingFiles(CycleManagementDTO cycleManagementDTO) {
		logger.info("CrossSiteIcStatusServiceImpl: retryIcnOrOutgoingFiles() : in");
		String result = "";
		if (StringUtils.isBlank(cycleManagementDTO.getForceOperation())) {
			cycleManagementDTO.setForceOperation(CommonConstants.RETRY);
		}
		try {
			cycleManagementDTO.setIcStatus(CommonConstants.SUSPECTED);
			String[] icnArray = cycleManagementDTO.getInternalCycleNumber().split(",");
			JsonArray icnNumber = new Gson().toJsonTree(icnArray).getAsJsonArray();
			JsonObject requestBody = new JsonObject();
			requestBody.addProperty(CommonConstants.SYSTEM_DATE, cycleManagementDTO.getSystemDate());
			requestBody.add(CommonConstants.INTERNAL_CYCLE_NUMBER, icnNumber);
			requestBody.addProperty(CommonConstants.FORCE_OPERATION, cycleManagementDTO.getForceOperation());
			requestBody.addProperty(CommonConstants.IC_STATUS, cycleManagementDTO.getIcStatus());
			String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
					+ CommonConstants.SETTLENXT_UPDATE_CYCLE_STATUS;
			
			result = restGateway.postAPIForJsonObject(url, requestBody);
			logger.info("RestGateway result : {}", result);
		} catch (Exception e1) {
			logger.error("Error occured while getting report cycle status {}", e1.getMessage(), e1);
		}
		logger.info("CrossSiteIcStatusServiceImpl: retryIcnOrOutgoingFiles() : out");
		return result;
	}
}
