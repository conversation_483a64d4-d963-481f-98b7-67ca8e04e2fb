package org.npci.settlenxt.adminportal.controllers;

import java.util.List;

import org.npci.settlenxt.portal.common.controllers.BaseCurrencyFileUploadController;
import org.npci.settlenxt.portal.common.dto.CurrencyFileUploadDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

@Controller
public class CurrencyFileUploadController extends BaseCurrencyFileUploadController{

	private static final String CURRENCY_FILE_UPLOAD_VIEW = "currencyFilesUploadView";
	private static final String CURRENCY_FILE_REJECT_VIEW = "currencyFileRejectView";
	private static final String ADMIN_PORTAL = "Admin";
	@PostMapping("/currencyFilesUploadView")
	@PreAuthorize("hasAuthority('Currency File Upload')")
	public String currencyFileBulkUploadList(Model model,
			@RequestParam(value = "isDBCallRequired", defaultValue = "false") boolean isDBCallRequired) {
		String portal=ADMIN_PORTAL;
		currencyFileUploadListCommon(model, isDBCallRequired,portal);
		return getView(model, CURRENCY_FILE_UPLOAD_VIEW);
	}

	@PostMapping("/searchCurrencyFileUploadList")
	@PreAuthorize("hasAuthority('Currency File Upload')")
	public String searchFileUploadList(Model model,
			@ModelAttribute("currencyFileUploadDTO") CurrencyFileUploadDTO currencyFileUploadDTO) {
		String portal=ADMIN_PORTAL;
		searchFileUploadListCommon(model, currencyFileUploadDTO,portal);
		return getView(model, CURRENCY_FILE_UPLOAD_VIEW);
	}

	@PostMapping("/bulkFilesUploadCurrency")
	@PreAuthorize("hasAuthority('Currency File Upload')")
	public ResponseEntity<Object> currencyFileBulkUpload(@RequestParam("document") List<MultipartFile> files, @ModelAttribute("currencyFileUploadDTO") CurrencyFileUploadDTO currencyFileUploadDTO) {
		currencyFileUploadDTO.setPortal(ADMIN_PORTAL);
		return currencyFileBulkUploadCommon(files, currencyFileUploadDTO);
	}

	@PostMapping("/stageUploadedFilesCurrency")
	@PreAuthorize("hasAuthority('Currency File Upload')")
	public String stageUploadedFiles(Model model, @ModelAttribute("currencyFileUploadDTO") CurrencyFileUploadDTO currencyFileUploadDTO) {
		stageUploadedFilesCommon(model, currencyFileUploadDTO);
		return getView(model, CURRENCY_FILE_UPLOAD_VIEW);
	}

	@PostMapping("/currencyFileRejectView")
	@PreAuthorize("hasAuthority('Currency File Upload')")
	public String currencyFileRejectView(Model model, @RequestParam("fileId") String fileId) {
		currencyFileRejectViewCommon(model, fileId);
		return getView(model, CURRENCY_FILE_REJECT_VIEW);
	}
	
	@PostMapping("/currencyFileDiscard")
	@PreAuthorize("hasAuthority('Currency File Upload')")
	public String discardCurrencyFile(Model model, @RequestParam("fileId") String fileId,@RequestParam("fileName") String fileName) {
		discardCurrencyFileCommon(model, fileId, fileName);
		return getView(model, CURRENCY_FILE_UPLOAD_VIEW);
	}


}
