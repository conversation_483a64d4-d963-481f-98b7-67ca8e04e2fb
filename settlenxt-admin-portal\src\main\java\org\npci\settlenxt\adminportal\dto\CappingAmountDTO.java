package org.npci.settlenxt.adminportal.dto;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class CappingAmountDTO {
	private String funcCode;
	private String funcCodeDesc;
	private BigDecimal amountCapFlat;
	private BigDecimal amountCapPercent;
	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private int cappingId;
	private String mccId;
	private String lastOperation;
	private String checkerComments;
	private String statusCode;
	private String editMccId;
	private String actionCode;
	private String mccGroup;
	private String binCardBrandId;
	private String binCardTypeId;
	private String capName;
	private String capType;
	private String fieldName;
	private String relOperator;
	private String fieldValue;
	private String amountFlag;
	private BigDecimal flat;
	private String percentage;
	private BigDecimal amountCapMax;
	private BigDecimal amountCapMin;
	private String status;
	private String requestState;
	private String addEditFlag;
	private String capAmountFlag;
	private BigDecimal flatValue;
	private BigDecimal percentageValue;
	private BigDecimal amountCapMaxValue;
	private BigDecimal amountCapMinValue;

}
