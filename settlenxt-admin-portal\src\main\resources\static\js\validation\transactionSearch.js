$(document).ready(function() {
	
	
	
	
	

	
	$("#transactionDate").datepicker({
		dateFormat : "yy-mm-dd",
		changeMonth : true,
		changeYear : true,
		maxDate: 0,
	//	minDate : 0,
		onClose : function(selectedDate) {
			$("#toDate").datepicker("option", "minDate", selectedDate);
		}
	});
	
	$("#transactionDate").focus(function(){
		$('#errtransactionDate').html("");
	});
	
	
	



$('#searchButon').click(function() {
	var flag = validateForm();
	if (flag){
		
		
			$("#example").dataTable({
				"bServerSide": true,
				"bSort":false,
				"bFilter":false,
				"processing": true,
				"serverSide": true,
				"searching": true,
				"sAjaxSource": "searchTransactionData",
				"bProcessing": true,
				/*"responsive": true,
				"bAutoWidth": true,	*/
				
				"sPaginationType": "full_numbers",
				"bJQueryUI": true,
				"sServerMethod": "POST",
				"bDestroy": true,
				"bAutoWidth":false,
				"sScrollX" : true,
				
				"fnRowCallback": function( nRow, aData, _iDisplayIndex ) {
					$('td:eq(11)', nRow).html('<a data-toggle="modal" class="launch-modal tabRowId" data-value='+aData[1]+' style="cursor: pointer;">View</a>')
					
					//$('td:eq()', nRow).html('<a class="viewDetails" data-value='+aData[2]+' style="cursor:pointer;"> view </a>')
					return nRow;
				},
				"fnServerParams": function ( aoData ) {
					aoData.push( { "name": "transactionDate", "value": $('#transactionDate').val() == "" ? "" : $('#transactionDate').val() },
							{ "name": "rrnNo", "value": $('#rrnNo').val() == "" ? "" : $('#rrnNo').val()},
							{ "name": "txntype", "value": $('#txntype').val() == "" ? "" : $('#txntype').val()},
							{ "name": "txnId", "value": $('#txnId').val() == "" ? "" : $('#txnId').val()},
					);
				},
				"fnServerData": function ( sSource, aoData, fnCallback ) {
					$.ajax({
						"dataType": 'json',
						"type": "POST",
						"url": sSource,
						"data": aoData,
						"success": function(json){
							
							var errorMessage=json.errorMessage;
							
							fnCallback(json);
							hideAppr(errorMessage);
							
							
						}
					});               
				}
			});
 }
});


function validateForm()
{
	var result = true;
	
	
	if($('#transactionDate').val() == "" ){		
		$('#errtransactionDate').html("Please select transcation date");
		return false;
		
	}else{
		$('#errtransactionDate').html("");
	}
	
	
	var regEx = /^\d+$/i;
	var value=$('#rrnNo').val();
	if(value != ""){
		if (!regEx.test(value.trim())) {
			$('#errrrnNo').html('Please enter only numbers');
			return false;
		}
		else {
			$('#errrrnNo').html("");
		
		}
	}
	
	
	
	return result;
}



	
$('#example').on('click', '.tabRowId', function() {
	   
	   
	   var txnId=$(this).attr('data-value');
	
	   var trnDateStr= $('#transactionDate').val();
	   var txntype=$('#txntype').val();
	   var url = "/getTransaction";
	   var data = "txnId,"+ txnId+",trnDateStr,"+trnDateStr+",txntype,"+txntype;
		postData(url, data);
	   
	   
	   

	});
	   
	   $("#chargeBack").click(function (){
			
		   var txnId = $('#txnId').val();
		   var chargeFlag= "C";
			var tokenValue = document.getElementsByName("_TransactToken")[0].value;
			
			 $.ajax({
                 url : "getchargeBackData",
                 type : "POST",
                 dataType : "json",
                 data : {
                     "_TransactToken" : tokenValue,
                     "txnId" : txnId,
                     "chargeFlag" : chargeFlag
                 },
                 success : function(response) {
                	 console.log("response "+response);
                 },
                 error : function(_request, _status, _error) {
                  console.log("error");
                 }
             });
	
			});
	   
	   $('#backButton').click(function(){
			
			var data = "";
			postData('/viewTransactionSearch',data);
		});
	   
	   $('#resetBtn').click(function(){
			$('#transactionDate').val('');
			$('#rrnNo').val('');
			$('#txnId').val('');
		});
	   
	
});






function hideAppr(errorMessage) {
    if (errorMessage != "") {

        $('.appr').hide();
    }
}
