<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
				<c:forEach var="bin"	items="${settlementBinsList}" varStatus="status">
																								<tr id="${status.index}">																								
																								<td>${bin.participantId}</td>
																								<td>${bin.settlementBinNumber}</td>
																							<td>${bin.isDefault}</td>
				<td>
				<c:if test="${(reqType eq 'E'  and requestState ne 'SAVE' and requestState ne '')}">

				<c:choose>
						<c:when test="${(bin.status eq 'B')}">					
							<input type="button" id="blockSettleBin${status.index}" value="UNBLOCK" class="btn btn-success" data-toggle="modal" data-target="#togglesettlebin" onclick="setblockSettlementBin('${bin.settlementBinNumber}','${bin.participantId}',this.id)">
						</c:when>
						<c:otherwise>
							<input type="button" id="blockSettleBin${status.index}" value="BLOCK"  class="btn btn-success" data-toggle="modal" data-target="#togglesettlebin" onclick="setblockSettlementBin('${bin.settlementBinNumber}','${bin.participantId}',this.id)">									
						</c:otherwise>																																	
				</c:choose>		
						
				</c:if>			
			</td>																																														
			</tr>
			</c:forEach>
		