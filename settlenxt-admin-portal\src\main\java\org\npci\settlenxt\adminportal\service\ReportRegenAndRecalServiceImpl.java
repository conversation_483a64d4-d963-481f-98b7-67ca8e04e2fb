package org.npci.settlenxt.adminportal.service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.DateUtils;
import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.gateway.RestGateway;
import org.npci.settlenxt.adminportal.repository.MemberRepository;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

/**
 * <li>This service gives response to api called from ui screen. It is used to
 * regenerate the reports</li>
 * 
 * <AUTHOR>
 *
 */
@Service
@PropertySource("/application-dev.properties")
public class ReportRegenAndRecalServiceImpl implements IReportRegenAndRecalService {

	private static final Logger logger = LogManager.getLogger(ReportRegenAndRecalServiceImpl.class);

	@Autowired
	private Environment environment;

	@Autowired
	private MemberRepository memberRepository;

	@Autowired
	RestGateway restGateway;

	/**
	 * <li>This method is received data from UI screen and prepare request and send
	 * request to the reportOrchestation service to regenerate or recalculate the
	 * reports</li>
	 */
	@Override
	public String sendDataToReportRegeneration(CycleManagementDTO cycleManagementDTO) {

		String result = "";
		JsonObject requestBody = getReportRegenerationJsonRequest(cycleManagementDTO);
		try {
			result = postAPIForJsonObjectFetchRegenRecal(requestBody);

		} catch (Exception e1) {
			logger.error(" Error occured while getting regenerated report status {}", e1.getMessage(), e1);
		}
		return result;
	}

	public String postAPIForJsonObjectFetchRegenRecal(JsonObject requestBody) {
		String result = "";
		try {
			String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
					+ CommonConstants.SETTLENXT_FETCH_REGEN_AND_RECAL;
			result = restGateway.postAPIForJsonObject(url, requestBody);
			logger.info("Response of regeneration of reports is {}", result);
		} catch (Exception e1) {
			logger.error("Error occured while getting regenerated report status {} ", e1.getMessage(), e1);
		}
		return result;
	}

	/**
	 * This method is used to fetch the all participant Id
	 */
	@Override
	public List<MemberDTO> getParticipantList(SearchCriteriaDTO searchCriteriaDTO) {
		String sSearch = null;
		List<MemberDTO> membersData = memberRepository.getFinalMemberList(searchCriteriaDTO.getStartVal(),
				searchCriteriaDTO.getEndVal(), sSearch);

		List<MemberDTO> membersDataList = new ArrayList<>();
		for (MemberDTO memberDto : membersData) {
			membersDataList.add(memberDto);
		}

		membersDataList.sort(Comparator.comparing(MemberDTO::getParticipantId));
		return membersDataList;
	}

	/**
	 * This method is used to get report regeneration or recalculation status
	 */
	@Override
	public CycleManagementDTO getRegenerationReportsStatus(CycleManagementDTO cycleManagementDTO) {
		JsonObject requestBody = getReportRegenerationJsonRequest(cycleManagementDTO);
		List<Map<String, String>> cycleDetails = new ArrayList<>();
		try {
			String result = "";

			result = postAPIForJsonObjectReportRegenRecal(requestBody);

			if (StringUtils.isBlank(result)) {
				cycleManagementDTO.setCycleData(cycleDetails);
				return cycleManagementDTO;
			}
			JsonObject respBody = (JsonObject) JsonParser.parseString(result);
			JsonArray cycleData = null;
			switch (cycleManagementDTO.getRequestType()) {
			case CommonConstants.REGENERATION:
				cycleData = respBody.getAsJsonArray(CommonConstants.REGENERATION_STATUS);
				break;
			case CommonConstants.RECALCULATION:
				cycleData = respBody.getAsJsonArray(CommonConstants.RECALCULATION_STATUS);
				break;
			default:
				break;
			}
			cycleDetails = new Gson().fromJson(cycleData, ArrayList.class);
			cycleManagementDTO.setCycleData(cycleDetails);
		} catch (Exception e1) {
			logger.error("Error occured while getting regenerated report status {}", e1.getMessage(), e1);
		}
		return cycleManagementDTO;
	}

	public String postAPIForJsonObjectReportRegenRecal(JsonObject requestBody) {

		String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
				+ CommonConstants.SETTLENXT_REPORT_REGEN_AND_RECAL_STATUS;
		String result = restGateway.postAPIForJsonObject(url, requestBody);
		logger.info("Response of regeneration of reports is {}", result);
		return result;
	}

	/**
	 * <li>This method is received data from UI screen and prepare request and send
	 * request to the reportOrchestation service to delete all recal txns</li>
	 */
	@Override
	public String deleteRecalTxns(CycleManagementDTO cycleManagementDTO) {
		String result = "";
		JsonObject requestBody = new JsonObject();
		requestBody.addProperty(CommonConstants.CYCLE_DATE, cycleManagementDTO.getCycleDate());
		requestBody.addProperty(CommonConstants.UID, cycleManagementDTO.getUid());
		requestBody.addProperty(CommonConstants.REQUEST_TYPE, CommonConstants.RECALCULATION);
		requestBody.addProperty(CommonConstants.CYCLE_NUMBER, cycleManagementDTO.getCycleNumber());
		requestBody.addProperty(CommonConstants.REQUEST_DATE, cycleManagementDTO.getRequestDate());
		requestBody.addProperty(CommonConstants.SETTLEMENT_PRODUCT_ID, cycleManagementDTO.getSettlementProductId());
		try {
			String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
					+ CommonConstants.SETTLENXT_DELETE_RECAL_TABLES_TXN;
			result = restGateway.postAPIForJsonObject(url, requestBody);
			logger.info("Response of deleting recal txns{}", result);
		} catch (Exception e1) {
			logger.error("Error occured while deleting recal txns{}", e1.getMessage(), e1);
		}
		return result;
	}

	private JsonObject getReportRegenerationJsonRequest(CycleManagementDTO cycleManagementDTO) {
		JsonObject requestBody = new JsonObject();
		if (StringUtils.isBlank(cycleManagementDTO.getCycleDate())) {
			String formattedRequestDate = DateUtils.getTodayLocalDate(DateUtils.DDMMYY);
			cycleManagementDTO.setCycleDate(formattedRequestDate);
		}
		if (StringUtils.isBlank(cycleManagementDTO.getRequestDate())) {
			String formattedRequestDate = null;
			if (StringUtils.equals(cycleManagementDTO.getRequestType(), CommonConstants.REGENERATION)) {
				formattedRequestDate = DateUtils.getDatewithTimeStamp(DateUtils.YYYY_MM_DD_HH_MM_SS);

			} else {
				formattedRequestDate = DateUtils.getTodayLocalDate(DateUtils.YYYY_MM_DD);
			}
			cycleManagementDTO.setRequestDate(formattedRequestDate);
		}
		if (StringUtils.isBlank(cycleManagementDTO.getCycleNumber())) {
			cycleManagementDTO.setCycleNumber(CommonConstants.CYCLE_NUMBER_C1);
		}
		if (StringUtils.isBlank(cycleManagementDTO.getSettlementProductId())) {
			cycleManagementDTO.setSettlementProductId(CommonConstants.PRODUCT_ID_RPY);
		}

		if (StringUtils.equals(cycleManagementDTO.getRequestType(), CommonConstants.REGENERATION)) {
			if (StringUtils.isBlank(cycleManagementDTO.getParticipantId())) {
				cycleManagementDTO.setParticipantId(CommonConstants.PARTICIPANT_TYPE_ALL);
			}
			if (StringUtils.isBlank(cycleManagementDTO.getReportType())) {
			
				cycleManagementDTO.setReportType(CommonConstants.RAW_AUTH);
			}

			requestBody.addProperty(CommonConstants.REPORT_TYPE, cycleManagementDTO.getReportType());
			requestBody.addProperty(CommonConstants.REQUEST_TYPE, CommonConstants.REGENERATION);
			requestBody.addProperty(CommonConstants.PARTICIPANT_ID, cycleManagementDTO.getParticipantId());
		} else if (StringUtils.equals(cycleManagementDTO.getRequestType(), CommonConstants.RECALCULATION)) {
			requestBody.addProperty(CommonConstants.REQUEST_TYPE, CommonConstants.RECALCULATION);
		} else {
			requestBody.addProperty(CommonConstants.REQUEST_TYPE, CommonConstants.MERGE_TABLES);
			requestBody.addProperty(CommonConstants.UID, cycleManagementDTO.getUid());
		}
		requestBody.addProperty(CommonConstants.REQUEST_DATE, cycleManagementDTO.getRequestDate());
		requestBody.addProperty(CommonConstants.CYCLE_DATE, cycleManagementDTO.getCycleDate());
		requestBody.addProperty(CommonConstants.CYCLE_NUMBER, cycleManagementDTO.getCycleNumber());
		requestBody.addProperty(CommonConstants.SETTLEMENT_PRODUCT_ID, cycleManagementDTO.getSettlementProductId());

		return requestBody;
	}

}
