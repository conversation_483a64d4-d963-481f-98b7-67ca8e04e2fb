<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

	<script src="./static/js/validation/mcpr/viewApproveBinExclConfig.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert"><spring:message code="binexcl.apprejecterrormsg" /></div>
	<div class="alert alert-danger remarkMust" role="alert"><spring:message code="binexcl.remarkserror" /></div>
	<c:url value="approveBinExclConfig" var="approveBinExclConfig" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveBinExclConfig" modelAttribute="binExclDTO"
		action="${approveBinExclConfig}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="binexcl.approvalPendingViewScreen.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="excId" value="${binExclDTO.excId}">

						<input type="hidden" id="crtuser"
							value="${binExclDTO.lastUpdatedBy}" />

						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">Bin Exclusion</caption>
									<thead style="display:none;"><th scope = "col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message code="binexcl.requestInfo" /></span></strong>
										</div></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>
								<tr>


									<td><label><spring:message code="binexcl.requestType" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.lastOperation}</td>
									<td><label><spring:message code="binexcl.requestDate" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.lastUpdatedOn}</td>
									<td><label><spring:message code="binexcl.requestStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${binExclDTO.requestState=='A' }">Approved</c:if></td>
									<td><c:if test="${binExclDTO.requestState=='P' }">Pending for Approval</c:if></td>
									<td><c:if test="${binExclDTO.requestState=='R' }">Rejected</c:if></td>
									<td><c:if test="${binExclDTO.requestState=='D' }">Discarded</c:if></td>
									<td></td>
									<td></td>
									<td></td> 
									<td></td>

								</tr>
								<tr>
									<td><label><spring:message code="binexcl.requestBy" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.lastUpdatedBy}</td>

									<td><label><spring:message code="binexcl.approverComments" /><span
											style="color: red"></span></label></td>
									<td colspan=2>${binExclDTO.checkerComments}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>

								<tr>
									<td colspan="6">
										<div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-user"></span> <span
												data-i18n="Data"><spring:message code="binexcl.viewscreen.title" /></span></strong>
										</div>
									</td>

									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>


								<tr>
									<td><label><spring:message code="binexcl.excId" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.excId}</td>
									<td><label><spring:message code="binexcl.participantName" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.bankName}</td>
									
									<td><label><spring:message code="binexcl.bin" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.bin}</td>
									<td><label><span
											style="color: red"></span></label></td>
									<td><label><spring:message code="binexcl.fromDate" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.fromDate}</td>
									<td><label><spring:message code="binexcl.toDate" /><span style="color: red"></span></label></td>
									<td>${binExclDTO.toDate}</td>
									<td></td>
									<td></td>
									<td></td>
								</tr>

								
								
								<sec:authorize access="hasAuthority('Approve User')">
									<c:if test="${binExclDTO.requestState eq 'P'}">
										<tr>
											<td colspan="6"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"><spring:message
																code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
										</tr>
										

										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="AM.lbl.reject" /></option>
											</select></td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											<!-- //Added by deepak on 31-03-2016 -->
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										</tr>
									</c:if>
							</sec:authorize>	

							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
								<sec:authorize access="hasAuthority('Approve Bin Exclusion Config')">
									<c:if test="${binExclDTO.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approveRole" value="Submit"
												onclick="postAction('/approveBinExclConfig');" />
										</c:if>
									</sec:authorize>
													
									<sec:authorize access="hasAuthority('Edit Bin Exclusion Config')">				
									<c:if test="${binExclDTO.requestState  eq 'R' and not empty showbutton}">
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="javascript:viewBinExclConfig('${binExclDTO.excId}','V','approvalTab');"/>						
									
									<button type="button" class="btn btn-danger"
										onclick="postDiscardBinAction('/discardRejectedBinExclEntry');">
										<spring:message code="binexcl.discardBtn" /></button>
									</c:if>
									</sec:authorize>
									
									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/binPendingForApproval');">
										<spring:message code="binexcl.backBtn" /></button>
								
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

