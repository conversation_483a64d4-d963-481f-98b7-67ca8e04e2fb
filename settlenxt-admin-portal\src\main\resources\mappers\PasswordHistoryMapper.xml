<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.PasswordHistoryRepository">
	
<select id="getPasswordList" resultType="PasswordHistoryDTO">
SELECT USER_ID as userId, PASSWORD as password FROM PASSWORD_HISTORY WHERE USER_ID= #{userId} ORDER BY CREATED_ON  DESC LIMIT #{lastPasswordsNo}
</select>

<insert id="insertPasswordHistory">
		INSERT INTO PASSWORD_HISTORY (USER_ID, PASSWORD, CREATED_ON, CREATED_BY, DIGEST) VALUES(#{userId}, #{password}, #{createdOn}, #{createdBy}, #{digest})
</insert>

</mapper>	
