$(document).ready(function () {

    var cursorPosition = null;
    $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    if (colIdx < actionColumnIndex) {
                        $(cell).html(title + '<br><input class="search-box"   type="text" />');

                        // On every keypress in this input
                        $(
                            'input',
                            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
                        )
                            .off('keyup change')
                            .on('change', function (_e) {
                                // Get the search value
                                $(this).attr('title', $(this).val());
                                var regexr = '({search})'; 

                                cursorPosition = this.selectionStart;
                                // Search the column for that value
                                api
                                    .column(colIdx)
                                    .search(
                                        this.value != ''
                                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                                            : '',
                                        this.value != '',
                                        this.value == ''
                                    )
                                    .draw();
                            })
                            .on('click', function (e) {
                                e.stopPropagation();
                            })
                            .on('keyup', function (e) {
                                e.stopPropagation();

                                $(this).trigger('change');
                                if (cursorPosition && cursorPosition != null) {
                                    $(this)
                                        .focus()[0]
                                        .setSelectionRange(cursorPosition, cursorPosition);
                                }
                            });
                    } else {
                        $(cell).html(title + '<br> &nbsp;');
                    }
                });
            $('#tabnew_filter').hide();
        },
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Insurance Primium Configuration',
                header: 'false',
                title: null,
                sheetName: 'Insurance Primium Configuration',
                className: 'defaultexport'
            }
 ,
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Insurance Primium Configuration' ,
						header:'false', 
						title: null,
						sheetName:'Insurance Primium Configuration',
						className:'defaultexport'
                    }
        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true
    });

    $("#excelExport").on("click", function () {
       console.log(" Export called", $(".buttons-excel") );
        $(".buttons-excel").trigger("click");
    });
 
 	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });
   

 

 
});



function viewCardProjection(cardProjectionId, type) {
var url;
	if (type == 'V')
		url = '/editProjectionEntry';
	else if (type == 'P')
		url = '/getProjectionEntry';
	else if (type == 'G')
		url = '/getPendingProjectionEntry';
	
	var data = "cardProjectionId," + cardProjectionId + ",viewType," + type ;
	postData(url, data);
}
function edit(cardProjectionId, type,parentPage) {
var url;
	if (type == 'V')
		url = '/editProjectionEntry';
	else if (type == 'P')
		url = '/getProjectionEntry';
	else if (type == 'G')
		url = '/getPendingProjectionEntry';
	
	var data = "cardProjectionId," + cardProjectionId + ",viewType," + type
	+ ",parentPage," + parentPage;
	postData(url, data);
}
function postCalculateAction(action) {
	var url = action;
	var cardProjectionId = document.getElementById("excId").value;
	var data = "cardProjectionId," + cardProjectionId  ;
	postData(url, data);
	
}


function submitForm(url) {
	
	var data = "userType," + $('#userType').val();
	postData(url, data);
}


function mySelect(){
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	 
	  var referenceNoList = document.getElementById("newsIds");
 
	 if(array.length==referenceNoListPendings.length){
		 $('#selectAll').prop('checked', true);
		
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
			 $("#toggleModalNews").modal('show');
	 }
	 else{
		 $("#toggleModalNews").modal('hide');
		 
	 }
	
}

function ApproveorRejectBulkProjectionEntry(type,action){
	
	 var url = '/approveCardProjectionBulk';
	
	 var array = [];

	 if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(action=='All'){
		 array=referenceNoListPendings; 
	 }
	   
		var referenceIdIdList = "";
		var i = 0;
		for (i of array) {
			referenceIdIdList = referenceIdIdList + i + "|";
		}
		var data;
	if(type=='A'){
			
		 data =  "status,"+"A"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	else if(type=='R'){
		
		 data = "status,"+"R"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	
	postData(url, data);
	
}


function deselectAll() {

	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  
		 var i=0;
	for(i of ele){  
   	if(i.type=='checkbox')  
      	 i.checked=false;  
	} 

}

function backAction(type, action) {
	var url = action;
	var data = "userType," + type ;
	postData(url, data);
}