package org.npci.settlenxt.adminportal.validator.check;

import java.util.List;

import org.npci.settlenxt.adminportal.common.util.LoadRejectReasonCode;
import org.npci.settlenxt.adminportal.validator.service.dto.TransactionRecord;
import org.npci.settlenxt.adminportal.validator.service.dto.ValidationResult;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;

public interface IValidationCheck {
	 void validate(ValidationResult validationResult, List<TransactionRecord> txnRecords) throws SettleNxtException;
	
	 void validate(ValidationResult validationResult, LoadRejectReasonCode rejReasonCode) throws SettleNxtException;
	
	 void validate(ValidationResult validationResult, List<TransactionRecord> txnRecords, int taskIdsSizeForSplit, LoadRejectReasonCode rejReasonCode) throws SettleNxtException;
}
