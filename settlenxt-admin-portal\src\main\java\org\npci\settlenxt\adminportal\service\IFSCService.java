package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.IFSCDTO;

public interface IFSCService {

	/**
	 * This is method is used for retrieving approved IFSC List
	 * 
	 * @return List of Approved IFSCDTO
	 */
	 List<IFSCDTO> getApprovedIFSCList();

	/**
	 * This is method is used for retrieving IFSC List that are pending for approval
	 * 
	 * @return List of IFSCDTO that are pending for approval
	 */
	 List<IFSCDTO> getPendingForAppovalIFSCList();

	/**
	 * This method is used to retrieve the IFSC details for the given IFSC code
	 * 
	 * @param ifscCode IFSC Code
	 * @return IFSCDTO for given IFSC code
	 */
	IFSCDTO getIFSC(String ifscCode);

	IFSCDTO getIFSCStg(String ifscCode);

	/**
	 * This method is used to update the IFSC details
	 * 
	 * @param ifscdto IFSC Details to be updated
	 * @return updated IFSCDTO
	 */
	int updateIFSC(IFSCDTO ifscdto);

	/**
	 * This method is used to save the IFSC details
	 * 
	 * @param ifscdto IFSC Details to be saved
	 * @return saved IFSCDTO
	 */
	long saveIFSC(IFSCDTO ifscdto);

	/**
	 * This method is used to updated the request status and move the data from IFSC
	 * staging table to IFSC main table if it is approved
	 * 
	 * @param ifscCode IFSC Code
	 * @param status   Request status (A - Approved. R - Rejected)
	 * @param remarks  Approver comments
	 * @return Approved/Rejected IFSC Data
	 */
	IFSCDTO approveOrRejectIFSC(String ifscCode, String status, String remarks);

	/**
	 * This method is used to discard the added/edited IFSC entry by maker
	 * 
	 * @param ifscCode IFSC Code
	 * @return discard IFSC data
	 */
	IFSCDTO discardIFSC(String ifscCode);

	IFSCDTO getApprovedIFSC(String ifscCode);

	 String approveOrRejectIFSCForBulk(String bulkApprovalReferenceNoList, String status, String remarks);

}
