$(document).ready(function() {
	$("#systemDate").datepicker({
		dateFormat: 'yyyy-MM-dd',
		todayHighlight: true,
		autoclose: true,
		changeMonth: true,
		changeYear: true
	});
	
	setTimeout(autoRefresh,60000);
	$("#refresh").click(function() {
		refreshStatus();
	});

	var oTable = $("#tabnew").DataTable({
		"fnRowCallback": function(nRow, _aData, _iDisplayIndex, _iDisplayIndexFull) {
			var status = $(nRow).attr("data-statusId");
			if (status == "IC_DONE") {
				$('td', nRow).addClass('bg-success');
			} else if (status == "FAILED" || status == "IC_FAILED") {
				$('td', nRow).addClass('bg-danger');
			} else {
				$('td', nRow).addClass('bg-info');
			}
		},
		dom: 'lBfrtip',
		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true,
		stateSave: true
	});

	var cycleNumbers = [];
	$("#submit").click(function() {
		var myObject = new Object();
		var url = getURL('/updateInternalCycle');
		var forceOperation = $('#reprocess').find(":selected").val();
		var rowcollection = oTable.$(".reprocess-checkbox:checked", { "page": "all" });
		rowcollection.each(function(_index, elem) {
			var checkbox_value = $(elem).val();
			cycleNumbers.push(checkbox_value);
		});
		var systemDate = $('#systemDate').val();
		var internalCycleNumber = cycleNumbers.toString();
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		myObject.forceOperation = forceOperation;
		myObject.systemDate = systemDate;
		myObject.internalCycleNumber = internalCycleNumber;
		$.ajax({
			type: "POST",
			contentType: "application/json",
			url: url,
			data: JSON.stringify(myObject),
			dataType: 'json',
			"beforeSend": function(xhr) {
				xhr.setRequestHeader('_TransactToken', tokenValue);
			},
			success: function(response) {
				$("#alertNotification").removeClass("hide");
				$("#modalRetry .close").click()
				$("#myModal .close").click()
				if (response == null || response == '' || response == undefined) {
					$("#alertNotification").addClass("alert-danger");
					$("#alertMessageStrong").text("Error! ");
					$("#alertMessage").text("Failed to update cycle status: " + "Internal server error");
				} else {
					if (response.Status == "FAILED" || response.Status == "503") {
						$("#alertNotification").addClass("alert-danger");
						$("#alertMessageStrong").text("Error! ");
						$("#alertMessage").text("Failed to update cycle status: " + response.errorMessage);
					} else if (response.Status == "SUCCESS" || response.Status == "200") {
						$("#alertNotification").addClass("alert-success");
						$("#alertMessageStrong").text("Success! ");
						$("#alertMessage").text("Successfully updated cycle status.");
					} else {
						$("#alertNotification").addClass("alert-danger");
						$("#alertMessageStrong").text("Error! ");
						$("#alertMessage").text("Failed to update cycle status: " + "Internal server error");
					}
				}
			},
			error: function(_e) {
				$("#btn-save").prop("disabled", false);
			}
		});
		$("#okAction").click(function() {
			window.setTimeout(function() { location.reload() }, 100)
		});
	});

	function refreshStatus(){
	 var systemDate = $('#systemDate').val();
		var reportStatus = $('#select-reportStatus').val();
		var url = "/reportInternalCycleStatus";
		var data = "systemDate," + systemDate
			+ ",reportStatus," + reportStatus;
		postData(url, data);
	}
	function autoRefresh(){
		if($('#autoReload').prop("checked")){
	     refreshStatus();
		 }
		 setTimeout(autoRefresh,60000);
	}
	function getURL(url) {
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		
		return pathName + url;
	}

	$('#tabnew').on('click', '#checked', function() {
		if (this.checked) {
			oTable.$("input[name='checkedBox']").prop('checked', true);
		} else {
			oTable.$("input[name='checkedBox']").prop('checked', false);
		}
	});

	$('#tabnew').on('click', '.reprocess-checkbox, .hCheckbox', function() {
		if ($('.reprocess-checkbox:checked').length > 0) {
			$("#btn").removeAttr("disabled");
		} else {
			$("#btn").attr("disabled", "disabled");
		}
		if ($('.reprocess-checkbox:checked').length == $('.reprocess-checkbox').length) {
			$('#checked').prop('checked', true);
		} else {
			$('#checked').prop('checked', false);
		}
	});

});