$(document).ready(function() {
	
	
	$("#uploadButon").click(function(){
		$("#uploadButon").attr("disabled", true);
		var check = validatepage();
		if(check) {
		
			if ($("#memberId").val() != "0") {
				$("#errMemberIdId").text("");
	
				if ($("#file").val() != '') {
					
					var ext = $('#file').val().split('.')[1];
					var tokenValue = document.getElementsByName("_TransactToken")[0].value;
					if ((ext=='cer') || (ext=='CER') || (ext=='crt') || (ext=='CRT')) {
						$('#errFile').text('');
						$("#certUploadForm").attr('action','certFileUpload?_TransactToken='+tokenValue);
						$("#certUploadForm").submit();
					} else {
						$('#errFile').text('File format not supported. Upload only .cer or .crt extension file');
					}
				} else {
					$("#errFile").text("Please select file");
				}
			} else {
				$("#errMemberIdId").text("Please select a bank");
			}
		}
		$("#uploadButon").attr("disabled", false);
	});

	
	function validatepage() {

		var flag = true ;

		if($('#file').val().trim() == ''){
			$('#errFile').text('Please attach file');
			flag = false;
		}else{
			$('#errFile').text('');
		}
		

		return flag ;
	}
});

/*
 * function viewFunByTxnId(txId){ alert(txnId); }
 */

