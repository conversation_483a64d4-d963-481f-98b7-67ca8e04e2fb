<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.InsurancePremiumRepository">
	<select id="getApprovedInsurancePremiumList" resultType="InsurancePremiumDTO">
		SELECT R.insurance_prem_id as insurancePremId, R.card_type	as cardType, R.card_variant	as cardVariant,R.vendor	as vendor,R.annual_premium_amt_per_card as annualPremiumAmtPerCard,R.from_date as fromDate,R.to_date as toDate,'Active' status 
			,v.description as vendorName,ct.description as cardTypeName,cv.description as cardVariantName,R.from_yearmonth as fromYearMonth,R.to_yearmonth as toYearMonth
			,left(R.from_yearmonth,4) as fromYear,right(<PERSON>.from_yearmonth,2) as from<PERSON><PERSON>h,left(<PERSON><PERSON>to_yearmonth,4) as toYear,right(R.to_yearmonth,2) as toMonth 
			,fm.description as fromMonthName,tm.description as toMonthName
			 FROM MCPR_INSURANCE_PREMIUM_CONFIG R inner join MCPR_INSURANCE_PREMIUM_CONFIG_STG rs on r.insurance_prem_id=rs.insurance_prem_id 
			 left join  lookup v on r.vendor=v.code and v.type='MCPR_VENDOR'
			 left join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE'
			 left join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT'
			 left join  lookup fm on right(R.from_yearmonth,2)=fm.code and fm.type='BeginingQtrMonths'
			 left join  lookup tm on right(R.to_yearmonth,2)=tm.code and tm.type='EndingQtrMonths'
			 where Rs.REQUEST_STATE='A' ORDER BY r.insurance_prem_id DESC
	</select>
	
	<select id="getApprovedInsurancePremiumListMain" resultType="InsurancePremiumDTO">
		SELECT R.insurance_prem_id as insurancePremId, R.card_type	as cardType, R.card_variant	as cardVariant,R.vendor	as vendor,R.annual_premium_amt_per_card as annualPremiumAmtPerCard,R.from_date as fromDate,R.to_date as toDate,'Active' status 
			,v.description as vendorName,ct.description as cardTypeName,cv.description as cardVariantName,R.from_yearmonth as fromYearMonth,R.to_yearmonth as toYearMonth
			,left(R.from_yearmonth,4) as fromYear,right(R.from_yearmonth,2) as fromMonth,left(R.to_yearmonth,4) as toYear,right(R.to_yearmonth,2) as toMonth 
			,fm.description as fromMonthName,tm.description as toMonthName
			 FROM MCPR_INSURANCE_PREMIUM_CONFIG R 
			 inner join MCPR_INSURANCE_PREMIUM_CONFIG_STG rs on r.insurance_prem_id=rs.insurance_prem_id 
			 left join  lookup v on r.vendor=v.code and v.type='MCPR_VENDOR'
			 left join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE'
			 left join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT'
			 left join  lookup fm on right(R.from_yearmonth,2)=fm.code and fm.type='BeginingQtrMonths'
			 left join  lookup tm on right(R.to_yearmonth,2)=tm.code and tm.type='EndingQtrMonths'
			 ORDER BY r.insurance_prem_id DESC
	</select>
	
	
	<select id="getPendingForAppovalInsurancePremiumList" resultType="InsurancePremiumDTO">
		SELECT R.insurance_prem_id as insurancePremId, R.card_type	as cardType, R.card_variant	as cardVariant,R.vendor	as vendor,R.annual_premium_amt_per_card as annualPremiumAmtPerCard,R.from_date as fromDate,R.to_date as toDate,case when R.REQUEST_STATE in ('P','S') then 'Pending For Approval' when R.REQUEST_STATE='R' then 'Rejected' when R.REQUEST_STATE='A' then 'Active'  end status, R.request_state as requestState,R.checker_comments as checkerComments , R.LAST_UPDATED_BY as lastUpdatedBy,R.LAST_UPDATED_ON as lastUpdatedOn, R.CREATED_ON as createdOn, R.CREATED_BY as createdBy, R.LAST_OPERATION as lastOperation,COALESCE(R.CHECKER_COMMENTS,'')  as checkerComments 
				,v.description as vendorName,ct.description as cardTypeName,cv.description as cardVariantName,R.from_yearmonth as fromYearMonth,R.to_yearmonth as toYearMonth 
				,fm.description as fromMonthName,tm.description as toMonthName
				,left(R.from_yearmonth,4) as fromYear,right(R.from_yearmonth,2) as fromMonth,left(R.to_yearmonth,4) as toYear,right(R.to_yearmonth,2) as toMonth 
				 FROM MCPR_INSURANCE_PREMIUM_CONFIG_STG R 
				 left join  lookup v on r.vendor=v.code and v.type='MCPR_VENDOR'
				 left join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE'
				 left join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT'
				 left join  lookup fm on right(R.from_yearmonth,2)=fm.code and fm.type='BeginingQtrMonths'
				 left join  lookup tm on right(R.to_yearmonth,2)=tm.code and tm.type='EndingQtrMonths'
				 WHERE (R.REQUEST_STATE='S'  or R.REQUEST_STATE='P'  or R.REQUEST_STATE='R')  ORDER BY insurance_prem_id DESC
	</select>
	<select id="getInsurancePremium" resultType="InsurancePremiumDTO">
		SELECT R.insurance_prem_id as insurancePremId, R.card_type	as cardType, R.card_variant	as cardVariant,R.vendor	as vendor,R.annual_premium_amt_per_card as annualPremiumAmtPerCard,R.from_date as fromDate,R.to_date as toDate,R.status, R.request_state as requestState,R.checker_comments as checkerComments , R.LAST_UPDATED_BY as lastUpdatedBy,R.LAST_UPDATED_ON as lastUpdatedOn, R.CREATED_ON as createdOn, R.CREATED_BY as createdBy, R.LAST_OPERATION as lastOperation 
			,v.description as vendorName,ct.description as cardTypeName,cv.description as cardVariantName,R.from_yearmonth as fromYearMonth,R.to_yearmonth as toYearMonth 
			,fm.description as fromMonthName,tm.description as toMonthName,case when R.REQUEST_STATE in ('P','S') then 'Pending For Approval' when R.REQUEST_STATE='R' then 'Rejected' when R.REQUEST_STATE='A' then 'Active'  end requestStateDiscription,R.CHECKER_COMMENTS checkerComments 
			,left(R.from_yearmonth,4) as fromYear,right(R.from_yearmonth,2) as fromMonth,left(R.to_yearmonth,4) as toYear,right(R.to_yearmonth,2) as toMonth 
			FROM MCPR_INSURANCE_PREMIUM_CONFIG_STG R 
			 left join  lookup v on r.vendor=v.code and v.type='MCPR_VENDOR'
			 left join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE'
			 left join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT'
			 left join  lookup fm on right(R.from_yearmonth,2)=fm.code and fm.type='BeginingQtrMonths'
			 left join  lookup tm on right(R.to_yearmonth,2)=tm.code and tm.type='EndingQtrMonths'
			  WHERE insurance_prem_id= #{insurancePremId} 
	</select>
	<select id="getInsurancePremiumMain" resultType="InsurancePremiumDTO">
		SELECT R.insurance_prem_id as insurancePremId, R.card_type	as cardType, R.card_variant	as cardVariant,R.vendor	as vendor,R.annual_premium_amt_per_card as annualPremiumAmtPerCard
		,left(R.from_yearmonth,4) as fromYear,right(R.from_yearmonth,2) as fromMonth,left(R.to_yearmonth,4) as toYear,right(R.to_yearmonth,2) as toMonth 
		,fm.description as fromMonthName,tm.description as toMonthName,R.from_date as fromDate,R.to_date as toDate,R.status as status
		,left(R.from_yearmonth,4) as fromYear,right(R.from_yearmonth,2) as fromMonth
		,v.description as vendorName,ct.description as cardTypeName,cv.description as cardVariantName,R.from_yearmonth as fromYearMonth,R.to_yearmonth as toYearMonth 
		  FROM MCPR_INSURANCE_PREMIUM_CONFIG as R 
		inner join MCPR_INSURANCE_PREMIUM_CONFIG_STG rs on r.insurance_prem_id=rs.insurance_prem_id 
			 left join  lookup v on r.vendor=v.code and v.type='MCPR_VENDOR'
			 left join  lookup ct on r.card_type=ct.code and ct.type='CARD_TYPE'
			 left join  lookup cv on r.card_variant=cv.code and cv.type='CARD_VARIANT'
			 left join  lookup fm on right(R.from_yearmonth,2)=fm.code and fm.type='BeginingQtrMonths'
			 left join  lookup tm on right(R.to_yearmonth,2)=tm.code and tm.type='EndingQtrMonths'
			WHERE R.insurance_prem_id= #{insurancePremId}
	</select>
	<update id="updateInsurancePremiumMaker" >
		UPDATE MCPR_INSURANCE_PREMIUM_CONFIG_STG SET vendor = #{vendor}, annual_premium_amt_per_card = #{annualPremiumAmtPerCard}, LAST_UPDATED_BY= #{lastUpdatedBy}, LAST_UPDATED_ON= #{lastUpdatedOn}, STATUS=#{requestState}
		,REQUEST_STATE=#{requestState},from_date=to_date(concat('01',#{fromMonth},#{fromYear}),'ddmmyyyy')
		,to_date=to_date(concat(case  when #{toMonth} in ('03','12') then '31' else '30' end,#{toMonth},#{toYear}),'ddmmyyyy')
		,LAST_OPERATION=#{lastOperation}, CHECKER_COMMENTS=#{checkerComments}  
		,from_yearmonth=#{fromYearMonth},to_yearmonth=#{toYearMonth}
		WHERE insurance_prem_id = #{insurancePremId}
	</update>
	<insert id="saveInsurancePremium" >
		insert into MCPR_INSURANCE_PREMIUM_CONFIG_STG(insurance_prem_id,card_type,card_variant
		,vendor,annual_premium_amt_per_card,created_by,created_on,last_updated_by
		,status,LAST_UPDATED_ON,REQUEST_STATE, LAST_OPERATION,from_date,to_date,from_yearmonth,to_yearmonth)  VALUES 
			(#{insurancePremId}, #{cardType}, #{cardVariant} ,#{vendor}, 
			#{annualPremiumAmtPerCard},	#{createdBy},#{createdOn},#{lastUpdatedBy}, 'P',#{lastUpdatedOn}
			, 'P', #{lastOperation} 
			,to_date(concat('01',#{fromMonth},#{fromYear}),'ddmmyyyy')  ,to_date(concat(case  when #{toMonth} in ('03','12') then '31' else '30' end,#{toMonth},#{toYear}),'ddmmyyyy') 
			,#{fromYearMonth},#{toYearMonth}  )
	</insert>
	<update id="updateInsurancePremiumMain" >
		UPDATE MCPR_INSURANCE_PREMIUM_CONFIG SET vendor = #{vendor}, annual_premium_amt_per_card = #{annualPremiumAmtPerCard}
		,from_yearmonth=#{fromYearMonth},to_yearmonth=#{toYearMonth}
		,to_date = #{toDate}, from_date = #{fromDate} 
		WHERE insurance_prem_id  = #{insurancePremId}
	</update>
	<insert id="saveInsurancePremiumMain" >
		insert into MCPR_INSURANCE_PREMIUM_CONFIG(insurance_prem_id,card_type,card_variant,vendor,annual_premium_amt_per_card,from_date
		,to_date,created_by,created_on,last_updated_by,status,from_yearmonth,to_yearmonth)  VALUES 
			(#{insurancePremId}, #{cardType}, #{cardVariant},#{vendor}, 
			#{annualPremiumAmtPerCard}, #{fromDate} , #{toDate} , 
			#{createdBy},#{createdOn},#{lastUpdatedBy}, #{requestState}
			,#{fromYearMonth},#{toYearMonth})
	</insert>
	<update id="updateInsurancePremiumRequestState" >
		UPDATE MCPR_INSURANCE_PREMIUM_CONFIG_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState},LAST_OPERATION=#{lastOperation}, CHECKER_COMMENTS=#{checkerComments} WHERE insurance_prem_id  = #{insurancePremId}
	</update>
	<delete id="deleteDiscardedEntry" >
		DELETE FROM MCPR_INSURANCE_PREMIUM_CONFIG_STG 	WHERE insurance_prem_id= #{insurancePremId}
	</delete>
	<select id="getLookUpList" resultType="CodeValueDTO">
		SELECT type ,code, description from  lookup where type=#{type}
	</select>
	<select id="fetchIdFrominsurancePremIdSequence" resultType="int">
		SELECT nextval('insurancePremId_seq')
	</select>
	<select id="validateDuplicateCheckList" resultType="InsurancePremiumDTO">
		SELECT R.insurance_prem_id as insurancePremId, R.card_type	as cardType, R.card_variant	as cardVariant  
		FROM MCPR_INSURANCE_PREMIUM_CONFIG_STG as R	WHERE insurance_prem_id!= #{insurancePremId} and R.card_type=#{cardType} and R.card_variant=#{cardVariant}  
		and ((from_date=to_date(#{fromYearMonth},'ddmmyyyy') and to_date=to_date(#{toYearMonth},'ddmmyyyy')) 
		or (to_date(#{fromYearMonth},'ddmmyyyy') between from_date and to_Date ) 
		or (to_date(#{toYearMonth},'ddmmyyyy') between from_date and to_Date ) 
		or (from_date between to_date(#{fromYearMonth},'ddmmyyyy') and to_date(#{toYearMonth},'ddmmyyyy') ) 
		or (to_Date between to_date(#{fromYearMonth},'ddmmyyyy') and to_date(#{toYearMonth},'ddmmyyyy')  ) )
	</select>
	<select id="validateFromDateList" resultType="InsurancePremiumDTO">
		SELECT R.insurance_prem_id as insurancePremId, R.card_type	as cardType, R.card_variant	as cardVariant  FROM MCPR_INSURANCE_PREMIUM_CONFIG_STG as R	WHERE insurance_prem_id!= #{insurancePremId} and R.card_type=#{cardType} and R.card_variant=#{cardVariant}   
			 and to_Date= to_date(#{fromYearMonth},'ddmmyyyy')  -interval '1 day'
	</select>
	<select id="getLastTodate" resultType="String">
		SELECT to_char(max(to_Date),'MONTH-YYYY') lastToMonth  FROM MCPR_INSURANCE_PREMIUM_CONFIG_STG as R	
		WHERE insurance_prem_id!= #{insurancePremId} and R.card_type=#{cardType} and R.card_variant=#{cardVariant}   
			 and to_Date &lt; to_date(#{fromYearMonth},'ddmmyyyy')  
	</select>
	<select id="validateCombination" resultType="InsurancePremiumDTO">
		SELECT R.insurance_prem_id as insurancePremId, R.card_type	as cardType, R.card_variant	as cardVariant  FROM MCPR_INSURANCE_PREMIUM_CONFIG_STG as R	WHERE insurance_prem_id!= #{insurancePremId} and R.card_type=#{cardType} and R.card_variant=#{cardVariant}  
	</select>

</mapper>