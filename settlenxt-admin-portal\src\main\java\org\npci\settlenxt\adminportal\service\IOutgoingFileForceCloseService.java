package org.npci.settlenxt.adminportal.service;

import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;


/**
 * Service Interface used for 
 * <li> get list of failed/inprogress/forceCloseSuccess/forceCloseFailed outgoing files</li>
 * <li> force close outgoing files</li>
 * <AUTHOR>
 *
 */
public interface IOutgoingFileForceCloseService {

	CycleManagementDTO getInprogressFailedOutgoingFileList(CycleManagementDTO cycleManagementDTO);

	String forceCloseOutgoingFiles(CycleManagementDTO cycleManagementDTO);

}
