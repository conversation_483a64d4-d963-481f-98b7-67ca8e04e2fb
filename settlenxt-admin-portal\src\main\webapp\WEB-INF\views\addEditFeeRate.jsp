<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>

<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script src="./static/js/jquery-3.6.0.js"></script>
<script src="./static/js/jquery-ui.js"></script>

<script type="text/javascript"
	src="./static/js/bootstrap-multiselect.js"></script>
<link rel="stylesheet" href="./static/css/bootstrap-multiselect.css"
	type="text/css" />
<link rel="stylesheet" href="./static/css/jquery-ui.css" />
<script type="text/javascript">
	var validationMessages = {};
	validationMessages['daystobewaived'] = "<spring:message code='feeRate.daystobewaived.validation.msg' javaScriptEscape='true' />";
	validationMessages['feeType'] = "<spring:message code='feeRate.feeType.validation.msg' javaScriptEscape='true' />";
	validationMessages['feeTypCode'] = "<spring:message code='feeRate.feeTypCode.validation.msg' javaScriptEscape='true' />";
	validationMessages['feeCode'] = "<spring:message code='feeRate.feeCode.validation.msg' javaScriptEscape='true' />";
	validationMessages['feeDesc'] = "<spring:message code='feeRate.feeDesc.validation.msg' javaScriptEscape='true' />";
	validationMessages['txnCurrency'] = "<spring:message code='feeRate.txnCurrency.validation.msg' javaScriptEscape='true' />";
	validationMessages['cashFeeFlat'] = "<spring:message code='feeRate.cashFeeFlat.validation.msg' javaScriptEscape='true' />";
	validationMessages['cashFeePercent'] = "<spring:message code='feeRate.cashFeePercent.validation.msg' javaScriptEscape='true' />";
	validationMessages['cashFeeMin'] = "<spring:message code='feeRate.cashFeeMin.validation.msg' javaScriptEscape='true' />";
	validationMessages['cashFeeMax'] = "<spring:message code='feeRate.cashFeeMax.validation.msg' javaScriptEscape='true' />";
	validationMessages['purFeeFlat'] = "<spring:message code='feeRate.purFeeFlat.validation.msg' javaScriptEscape='true' />";
	validationMessages['purFeePercent'] = "<spring:message code='feeRate.purFeePercent.validation.msg' javaScriptEscape='true' />";
	validationMessages['purFeeMin'] = "<spring:message code='feeRate.purFeeMin.validation.msg' javaScriptEscape='true' />";
	validationMessages['purFeeMax'] = "<spring:message code='feeRate.purFeeMax.validation.msg' javaScriptEscape='true' />";
	validationMessages['status'] = "<spring:message code='feeRate.status.validation.msg' javaScriptEscape='true' />";
	validationMessages['validToDt'] = "<spring:message code='feeRate.validToDt.validation.msg' javaScriptEscape='true' />";
	validationMessages['validFromDt'] = "<spring:message code='feeRate.validFromDt.validation.msg' javaScriptEscape='true' />";
	validationMessages['gstCode'] = "<spring:message code='feeRate.gstCode.validation.msg' javaScriptEscape='true' />";
	validationMessages['creditTo'] = "<spring:message code='feeRate.creditTo.validation.msg' javaScriptEscape='true' />";
	validationMessages['debitTo'] = "<spring:message code='feeRate.debitTo.validation.msg' javaScriptEscape='true' />";
	validationMessages['netMin'] = "<spring:message code='feeRate.netMin.validation.msg' javaScriptEscape='true' />";
	validationMessages['netMax'] = "<spring:message code='feeRate.netMax.validation.msg' javaScriptEscape='true' />";
</script>
<script type="text/javascript"
	src="./static/js/validation/commonValidation.js"></script>
<script type="text/javascript"
	src="./static/js/validation/addEditFeeRate.js"></script>
<input type="hidden" id="showButton" name="showButton"
	value="${showbutton}" />
<input type="hidden" id="addFlow" name="addFlow" value="${addFlow}" />
<input type="hidden" id="editFlow" name="editFlow" value="${editFlow}" />
<input type="hidden" id="showButton" name="showButton"
	value="${showbutton}" />
<!-- Modal -->

<div class="modal fade" id="toggleModalDiscardFeeRate" tabindex="-1"
	role="dialog" aria-labelledby="toggleModalDiscardFeeRate"
	aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to discard?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">NO</button>
				<button type="button" class="btn btn-primary"
					onclick="userAction('/discardFeeRate');">YES</button>
			</div>
		</div>
	</div>
</div>
<!-- Modal Close-->
<c:if test="${editFlow eq 'Y' or addFlow eq 'Y'}">
	<div class="panel panel-default no_margin"
		style="padding-bottom: 15px;">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"> <c:if test="${editFlow eq 'Y'}">
						<spring:message code="feeRate.editscreen.title" />
					</c:if> <c:if test="${addFlow eq 'Y'}">
						<spring:message code="feeRate.addscreen.title" />
					</c:if>
			</span></strong>
		</div>
		<form:form onsubmit="return encodeForm(this);" method="POST"
			id="addEditFee" modelAttribute="feeRateDto" action="/updateFee"
			autocomplete="off">
			<br />
			<div class="row">
				<div class="col-sm-12">
					<form:hidden path="feeId" id="feeId" name="feeId" />
					<input type='hidden' id="hdateAction"
						value="${feeRateDto.dateAction}" />

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.feeTypeCode" /><span
								style="color: red">*</span></label>
							<form:select path="feeType" id="feeType" name="feeType"
								maxlength="4" cssClass="form-control medantory">

								<form:option value="SELECT">
									<spring:message code="msg.lbl.select"></spring:message>
								</form:option>
								<form:options itemLabel="feeTypes" itemValue="feeTypCode"
									items="${feecode}" />
							</form:select>


							<input type="hidden" id="fee" name="fee"
								value="${feeRateDto.feeCode}" /> <input type="hidden"
								id="discardRejec" name="discardRejec"
								value="${feeRateDto.requestState}" />

							<div id="errfeeType">
								<span for="feeType" class="error"><form:errors
										path="feeType" /> </span>
							</div>
						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.feeTypCode" /><span
								style="color: red">*</span></label>
							<form:input path="feeTypCode" id="feeTypCode" name="feeTypCode"
								maxlength="4" cssClass="form-control medantory" />

							<div id="errfeeTypCode">
								<span for="feeTypCode" class="error"><form:errors
										path="feeTypCode" /></span>
							</div>
						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.feeCode" /><span
								style="color: red">*</span> </label>
							<c:if test="${not empty addFlow}">
								<form:input path="feeCode" id="feeCode" name="feeCode"
									maxlength="4" cssClass="form-control medantory" />

							</c:if>


							<c:if test="${not empty editFlow}">
								<c:choose>
									<c:when
										test="${feeRateDto.requestState  eq 'R' and editFirstRejectFeeRate eq 'Y'}">
										<form:input path="feeCode" id="feeCode" name="feeCode"
											maxlength="4" cssClass="form-control medantory" />

									</c:when>
									<c:otherwise>
										<form:input path="feeCode" id="feeCode" name="feeCode"
											maxlength="4" cssClass="form-control medantory"
											readonly="true" />
									</c:otherwise>
								</c:choose>
							</c:if>
							<div id="errfeeCode">
								<span for="feeCode" class="error"><form:errors
										path="feeCode" /></span>
							</div>
						</div>
					</div>
					<input type="hidden" id="revCashes" name="revCashes"
						value="${feeRateDto.reversCashFee}" />

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.feeDesc" /><span
								style="color: red">*</span></label>
							<form:input path="feeDesc" id="feeDesc" name="feeDesc"
								maxlength="100" cssClass="form-control medantory" />

							<div id="errfeeDesc">
								<span for="feeDesc" class="error"><form:errors
										path="feeDesc" /></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.txnCurrency" /><span
								style="color: red">*</span></label>

							<form:select path="txnCurrency" id="txnCurrency"
								name="txnCurrency" maxlength="4"
								cssClass="form-control medantory">

								<form:option value="SELECT">
									<spring:message code="msg.lbl.select"></spring:message>
								</form:option>
								<form:options itemLabel="txnCurrency" itemValue="txnCode"
									items="${txnCurrency}" />
							</form:select>

							<div id="errtxnCurrency">
								<span for="txnCurrency" class="error"><form:errors
										path="txnCurrency" /> </span>
							</div>
						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.cashFeeFlat" /><span
								style="color: red">*</span> </label>

							<form:input path="cashFeeFlat" id="cashFeeFlat"
								name="cashFeeFlat" maxlength="10"
								cssClass="form-control medantory numeric" />

							<div id="errcashFeeFlat">
								<span for="cashFeeFlat" class="error"><form:errors
										path="cashFeeFlat" /> </span>
							</div>
						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.cashFeePercent" /><span
								style="color: red">*</span></label>

							<form:input path="cashFeePercent" id="cashFeePercent"
								name="cashFeePercent" maxlength="6"
								cssClass="form-control medantory numeric" />


							<div id="errcashFeePercent">
								<span for="cashFeePercent" class="error"><form:errors
										path="cashFeePercent" /></span>
							</div>
						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.cashFeeMin" /><span
								style="color: red">*</span></label>
							<form:input path="cashFeeMin" id="cashFeeMin" name="cashFeeMin"
								maxlength="10" cssClass="form-control medantory numeric" />
							<div id="errcashFeeMin">
								<span for="cashFeeMin" class="error"><form:errors
										path="cashFeeMin" /></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.cashFeeMax" /><span
								style="color: red">*</span></label>

							<form:input path="cashFeeMax" id="cashFeeMax" name="cashFeeMax"
								maxlength="10" cssClass="form-control medantory numeric" />

							<div id="errcashFeeMax">
								<span for="cashFeeMax" class="error"><form:errors
										path="cashFeeMax" /></span>
							</div>
						</div>
					</div>




					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.purFeeFlat" /><span
								style="color: red">*</span> </label>

							<form:input path="purFeeFlat" id="purFeeFlat" name="purFeeFlat"
								maxlength="10" cssClass="form-control medantory numeric" />


							<div id="errpurFeeFlat">
								<span for="purFeeFlat" class="error"><form:errors
										path="purFeeFlat" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.purFeePercent" /><span
								style="color: red">*</span></label>

							<form:input path="purFeePercent" id="purFeePercent"
								name="purFeePercent" maxlength="6"
								cssClass="form-control medantory numeric" />


							<div id="errpurFeePercent">
								<span for="purFeePercent" class="error"><form:errors
										path="purFeePercent" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.purFeeMin" /><span
								style="color: red">*</span></label>
							<form:input path="purFeeMin" id="purFeeMin" name="purFeeMin"
								maxlength="10" cssClass="form-control medantory numeric" />
							<div id="errpurFeeMin">
								<span for="purFeeMin" class="error"><form:errors
										path="purFeeMin" /></span>
							</div>
						</div>
					</div>

				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.purFeeMax" /><span
								style="color: red">*</span></label>

							<form:input path="purFeeMax" id="purFeeMax" name="purFeeMax"
								maxlength="10" cssClass="form-control medantory numeric" />

							<div id="errpurFeeMax">
								<span for="purFeeMax" class="error"><form:errors
										path="purFeeMax" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.netMin" /><span
								style="color: red;">*</span> </label>

							<form:input path="netMin" id="netMin" name="netMin"
								maxlength="10" cssClass="form-control medantory numeric" />


							<div id="errnetMin">
								<span for="netMin" class="error"><form:errors
										path="netMin" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.netMax" /><span
								style="color: red;">*</span> </label>

							<form:input path="netMax" id="netMax" name="netMax"
								maxlength="10" cssClass="form-control medantory numeric" />


							<div id="errnetMax">
								<span for="netMax" class="error"><form:errors
										path="netMax" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.multiplier" /><span
								style="color: red;" class="require">*</span> </label>

							<form:input path="multiplier" id="multiplier" name="multiplier"
								maxlength="7" cssClass="form-control medantory"
								onkeydown="validateMultiplier('errmultiplier')" />

							<div id="errmultiplier">
								<span for="multiplier" class="error"><form:errors
										path="multiplier" /></span>
							</div>
						</div>
					</div>

				</div>
			</div>

			<div class="row">
				<div class="col-sm-12">


					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.gstcode" /></label>
							<form:select path="gstCode" id="gstCode" name="gstCode"
								maxlength="4" cssClass="form-control medantory">
								<form:option value="0">
									<spring:message code="msg.lbl.select"></spring:message>
								</form:option>
								<form:options itemLabel="gstCode" itemValue="gstCode"
									items="${gstCode}" />
							</form:select>


							<div id="errgstCode">
								<span for="gstCode" class="error"><form:errors
										path="gstCode" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.creditTo" /><span
								style="color: red">*</span></label>

							<form:select path="creditTo" id="creditTo" name="creditTo"
								maxlength="4" cssClass="form-control medantory">

								<form:option value="SELECT">
									<spring:message code="msg.lbl.select"></spring:message>
								</form:option>
								<form:options itemLabel="description" itemValue="creditTo"
									items="${partyType}" />
							</form:select>

							<div id="errcreditTo">
								<span for="creditTo" class="error"><form:errors
										path="creditTo" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.debitTo" /><span
								style="color: red">*</span> </label>

							<form:select path="debitTo" id="debitTo" name="debitTo"
								maxlength="4" cssClass="form-control medantory">

								<form:option value="SELECT">
									<spring:message code="msg.lbl.select"></spring:message>
								</form:option>
								<form:options itemLabel="description" itemValue="creditTo"
									items="${partyType}" />
							</form:select>
							<div id="errdebitTo">
								<span for="debitTo" class="error"><form:errors
										path="debitTo" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.validFromDt" /><span
								style="color: red">*</span> </label>
							<c:if test="${not empty addFlow}">
								<form:input path="validFromDt" id="validFromDt"
									pattern="yyyy-MM-dd" name="validFromDt"
									cssClass="form-control input-square viewOnly" readOnly="true" />
							</c:if>
							<c:if test="${not empty editFlow}">
								<fmt:formatDate value="${feeRateDto.validFromDt}"
									pattern="dd/MM/yyyy" var="myDate" />
								<form:input path="validFromDt" id="validFromDt"
									pattern="yyyy-MM-dd" name="validFromDt"
									cssClass="form-control input-square viewOnly" readOnly="true"
									value="${myDate}" />
							</c:if>
							<div id="errvalidFromDt">
								<span for="validFromDt" class="error"><form:errors
										path="validFromDt" /></span>
							</div>
						</div>
					</div>

				</div>
			</div>


			<div class="row">
				<div class="col-sm-12">

					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.validToDt" /><span
								style="color: red">*</span> </label>
							<c:if test="${not empty addFlow}">
								<form:input path="validToDt" id="validToDt" pattern="yyyy-MM-dd"
									name="validToDt" cssClass="form-control input-square viewOnly"
									readOnly="true" />
							</c:if>
							<c:if test="${not empty editFlow}">
								<fmt:formatDate value="${feeRateDto.validToDt}"
									pattern="dd/MM/yyyy" var="myDate" />
								<form:input path="validToDt" id="validToDt" pattern="yyyy-MM-dd"
									name="validToDt" readOnly="true"
									cssClass="form-control input-square viewOnly" value="${myDate}" />
							</c:if>
							<div id="errvalidToDt">
								<span for="validToDt" class="error"><form:errors
										path="validToDt" /></span>
							</div>
						</div>
					</div>
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.reversCashFee" /></label> <input
								type="checkbox" name="reversCashFee" id="revCash">

							<div id="errreversCashFee">
								<span for="reversCashFee" class="error"><form:errors
										path="reversCashFee" /></span>
							</div>
						</div>
					</div>


					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="feeRate.status" /><span
								style="color: red">*</span></label>
							<form:select id="status" name="status" path="status"
								class="form-control medantory">

								<form:option value="A">Active</form:option>
								<form:option value="I">InActive</form:option>
							</form:select>

							<div id="errstatus">
								<span for="status" class="error"><form:errors
										path="status" /></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="col-sm-3" id='daystobewaivedId' style="display: none">
						<div class="form-group">
							<label><spring:message code="feeRate.daystobewaived" /></label>
							<form:input path="daystobewaived" id="daystobewaived"
								name="daystobewaived" maxlength="4"
								cssClass="form-control medantory numeric" />
							<div id="errdaystobewaived">
								<span for="daystobewaived" class="error"><form:errors
										path="daystobewaived" /></span>
							</div>
						</div>
					</div>
					<div class="col-sm-3" id='waiverDayTypeId' style="display: none">
						<div class="form-group">
							<label><spring:message code="feeRate.waiverDayType" /></label>
							<form:select path="waiverDayType" id="waiverDayType"
								name="waiverDayType" maxlength="4"
								cssClass="form-control medantory">
								<form:option value="">
									<spring:message code="msg.lbl.select"></spring:message>
								</form:option>
								<form:options itemLabel="description" itemValue="code"
									items="${waiverDayType}" />
							</form:select>
							<div id="errwaiverDayType">
								<span for="waiverDayType" class="error"><form:errors
										path="waiverDayType" /></span>
							</div>
						</div>
					</div>
					<div class="col-sm-3" id='penaltyDayTypeId' style="display: none">
						<div class="form-group">
							<label><spring:message code="feeRate.penaltyDayType" /></label>
							<form:select path="penaltyDayType" id="penaltyDayType"
								name="penaltyDayType" maxlength="4"
								cssClass="form-control medantory">
								<form:option value="">
									<spring:message code="msg.lbl.select"></spring:message>
								</form:option>
								<form:options itemLabel="description" itemValue="code"
									items="${penaltyDayType}" />
							</form:select>
							<div id="errpenaltyDayType">
								<span for="penaltyDayType" class="error"><form:errors
										path="penaltyDayType" /></span>
							</div>
						</div>
					</div>
					<div class="col-sm-3" id='compoundFeeId' style="display: none">
						<div class="form-group">
							<label><spring:message code="feeRate.compoundFee" /></label>
							<form:select path="compoundFee" id="compoundFee"
								name="compoundFee" maxlength="4"
								cssClass="form-control medantory">
								<form:option value="">
									<spring:message code="msg.lbl.select"></spring:message>
								</form:option>
								<form:options itemLabel="description" itemValue="code"
									items="${compoundFee}" />
							</form:select>
							<div id="errcompoundFee">
								<span for="compoundFee" class="error"><form:errors
										path="compoundFee" /></span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-12">
					<div class="col-sm-3" id='dailyFeeId' style="display: none">
						<div class="form-group">
							<label><spring:message code="feeRate.dailyFee" /></label>
							<form:select path="dailyFee" id="dailyFee" name="dailyFee"
								maxlength="4" cssClass="form-control medantory">
								<form:option value="">
									<spring:message code="msg.lbl.select"></spring:message>
								</form:option>
								<form:options itemLabel="description" itemValue="code"
									items="${dailyFee}" />
							</form:select>
							<div id="errdailyFee">
								<span for="dailyFee" class="error"><form:errors
										path="dailyFee" /></span>
							</div>
						</div>
					</div>
					<div class="col-sm-3" id='impactToId' style="display: none">
						<div class="form-group">
							<label><spring:message code="feeRate.impactTo" /></label>
							<form:select path="impactTo" id="impactTo" name="impactTo"
								maxlength="4" cssClass="form-control medantory">
								<form:option value="">
									<spring:message code="msg.lbl.select"></spring:message>
								</form:option>
								<form:options itemLabel="description" itemValue="code"
									items="${impactTo}" />
							</form:select>
							<div id="errimpactTo">
								<span for="impactTo" class="error"><form:errors
										path="impactTo" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3" id="multiDateAction" style="display: none">
						<div class="col-sm-12">
							<label><spring:message code="feeRate.dateAction" /></label>
						</div>
						<div class="col-sm-12">
							<form:select path="dateAction" id="dateAction" name="dateAction"
								maxlength="50" multiple="multiple" cssClass="form-control"
								style="display: none; margin-top: -25px padding-left: -100px ;">

								<form:options items="${dateAction}" itemValue="code"
									itemLabel="description" />
							</form:select>

							<div id="errdateAction">
								<span for="dateAction" class="error"><form:errors
										path="dateAction" /> </span>
							</div>
						</div>
					</div>
				</div>
			</div>





			<div class="row">
				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center">


						<c:if test="${editFlow eq 'Y' and showbutton ne 'YES'}">
							<sec:authorize access="hasAuthority('Edit Fees')">
								<input type="button" class="btn btn-success"
									onclick="validateAddFees('/updateFeeRate')" id="submitButton"
									value="<spring:message code="ifsc.submitBtn" />" />
							</sec:authorize>
						</c:if>


						<c:if test="${feeRateDto.requestState eq 'R'}">
							<sec:authorize access="hasAuthority('Edit Fees')">

								<input name="discardButton" type="button" class="btn btn-danger"
									href="#" id="approveRole" value="Discard" data-toggle="modal"
									data-target="#toggleModalDiscardFeeRate" />
							</sec:authorize>
						</c:if>



						<c:if test="${addFlow eq 'Y'}">
							<sec:authorize access="hasAuthority('Add Fees')">
								<input type="button" id="clearFeeRate" value="Clear"
									class="btn btn-success"></input>
								<input type="button" class="btn btn-success"
									onclick="validateAddFees('/addFeeRate')" id="submitButton"
									value="<spring:message code="feeRate.submitBtn" />" />
							</sec:authorize>
						</c:if>
						<c:choose>
							<c:when test="${feeRateDto.requestState eq 'R'}">
								<button type="button" class="btn btn-danger"
									onclick="navigateTo('/getFeePendingForApproval');">
									<spring:message code="feeRate.backBtn" />
								</button>
							</c:when>
							<c:otherwise>
								<button type="button" class="btn btn-danger"
									onclick="navigateTo('/showFeeRates');">
									<spring:message code="feeRate.backBtn" />
								</button>
							</c:otherwise>
						</c:choose>





					</div>
				</div>
			</div>


		</form:form>
	</div>
</c:if>