<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<script type="text/javascript"> var actionColumnIndex = 8; </script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<script type="text/javascript" src="./static/js/jszip.min.js"></script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js"></script>
<script type="text/javascript" src="./static/js/jquery.3.6.0.min.js"></script>
<script src="./static/js/selectize.min.js" integrity="sha256-+C0A5Ilqmu4QcSPxrlGpaZxJ04VjsRjKu+G82kl5UJk=" crossorigin="anonymous"></script>
<link rel="stylesheet" href="./static/css/selectize.bootstrap3.min.css" integrity="sha256-ze/OEYGcFbPRmvCnrSeKbRTtjG4vGLHXgOqsyLFTRjg=" crossorigin="anonymous" />
<link rel="stylesheet" href="./static/css/jquery-ui.css">
<script src="./static/js/jquery-3.6.0.js"></script>
<script src="./static/js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/vTransact.js"></script>
<script type="text/javascript" src="./static/js/bootstrap-datepicker.min.js"></script>
<link rel="stylesheet" href="./static/css/bootstrap-datepicker3.css" />
<script type="text/javascript" src="./static/js/moment.min.js"></script>
<script type="text/javascript" src="./static/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="./static/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="./static/js/dataTables.bootstrap.min.js"></script>
<script type="text/javascript" src="./static/js/dataTables.fixedHeader.min.js"></script>
<script type="text/javascript" src="./static/js/dataTables.responsive.min.js"></script>
<head>
<title></title>
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>
</head>
<script type="text/javascript"
	src="./static/js/validation/commonValidation.js"></script>
<script src="./static/js/validation/reportRecalculation.js"
	type="text/javascript"></script>

<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
	</ul>

	<div class="row">
		<div class="col-sm-12">
			<div class="panel panel-default">
				<div class="panel-heading">
					<strong>
						<span class="glyphicon glyphicon-th"></span> 
						<span data-i18n="Data">
							<spring:message code="reportRecalculation.title" />
						</span>
					</strong>
				</div>
				<div class=" ">
					<div role="tabpanel" class="tab-pane active" id="home">
						<form:form onsubmit="return encodeForm(this);" method="POST" id="reportRecalculation" 
									modelAttribute="cycleManagementDTO" action="/SNxtAdminPortal/reportRecalculation" autocomplete="off">
							<br />
							<div class="row">
								<div class="col-sm-12">
									<div class="col-sm-2">
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label for=""><spring:message code="reportProgressStatus.productId" /><span class="red">*</span></label>
											<form:select path="settlementProductId" id="select-settlementProductId"
                                                name="settlementProductId" class="form-control settleProductId" >
                                                <option value = "" disabled selected>Select product Id</option>
                                                <c:forEach var="settleProductName" items="${cycleManagementDTO.settleProduct}">
												<option value="${settleProductName.key}" data-product_id = "${settleProductName.value}">${settleProductName.key}</option>
												</c:forEach>
                                            </form:select>
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label for=""><spring:message code="reportProgressStatus.cycleDate" /><span class="red">*</span></label>
											<form:input path="cycleDate" id="cycleDate" data-date-format="yyyy-mm-dd"
											cssClass="form-control medantory" max="3000-01-01" 
											onfocus="this.max=new Date().toISOString().split('T')[0]"  />
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<label>
												<spring:message code="reportProgressStatus.cycleNumber" /><span class="red">*</span>
											</label>
											<form:select path="cycleNumber" id="select-cycleNumber" 
												name="cycleNumber" class="form-control" >
											</form:select>
										</div>
									</div>
									<div class="col-sm-2">
										<div class="form-group">
											<button type="button" id="submit" class="btn btn-primary btn-sm" data-toggle="modal"
											 data-target="#myModal" style="margin-bottom:20px; border-radius: 6px; color:white;" disabled>Submit</button>
										 </div>
									</div>
									<div class="col-sm-2">
									</div>
								</div>
							</div>
						</form:form>
					</div>
				</div>
				<div id="afterSave">
					<div class=" ">
						<div class="row">
							<div class="col-sm-1"></div>
							<div class="col-sm-10">
								<div class="panel-body">
									<div class="table-responsive">
										<table id="dataTable" class="table table-bordered">
										<caption style = "display:none"></caption>
											<thead>
												<tr>
													<th scope = "col" class="text-center">Cycle Date</th>
													<th scope = "col" class="text-center">Request Date</th>
													<th scope = "col" class="text-center">Cycle Number</th>
													<th scope = "col" class="text-center">Settlement Product Id</th>
													<th scope = "col" class="text-center">Recalculation Status</th>
													<th scope = "col" class="text-center">Merging Status</th>
													<th scope = "col" class="text-center">Merge Tables</th>
													<th scope = "col" class="text-center">Cancel Recalculation Request</th>
												</tr>
												
											</thead>
											<tbody>
												<c:if test="${not empty cycleManagementDTO.cycleData}">
													<c:forEach var="cycleData" items="${cycleManagementDTO.cycleData}" varStatus="status">
												       <tr data-recalStatus="${cycleData.recalculationStatus}">
													       <td>${cycleData.cycleDate}</td>
													       <td>${cycleData.requestDate}</td>
													       <td>${cycleData.cycleNumber}</td>
													       <td>${cycleData.settlementProductId}</td>
													       <td class="recalStatus" data-recalculationStatus= "${cycleData.recalculationStatus}" data-mergeStatus= "${cycleData.mergingStatus}">${cycleData.recalculationStatus}</td>
													       <td class="mergeStatus" >${cycleData.mergingStatus}</td>
															<td>
																<c:if
																	test="${cycleData.recalculationStatus eq 'DONE' && (empty cycleData.mergingStatus)}">
																	<button type="button" id="btn-merge${status.index}" data-id = "${status.index}"
																		class="btn btn-success btn-sm mergeBtn" data-toggle="modal"
																		data-target="#confirmation-modal" data-uid = "${cycleData.uid}" data-cycleNumber = "${cycleData.cycleNumber}"
																		data-cycleDate = "${cycleData.cycleDate}" data-productId = "${cycleData.settlementProductId}"
																		style="border-radius: 6px;">Merge</button>
																</c:if>
																<c:if
																	test="${cycleData.recalculationStatus ne 'DONE' || (cycleData.recalculationStatus eq 'DONE' && cycleData.mergingStatus eq 'DONE')
																	|| (empty cycleData.recalculationStatus && empty cycleData.mergingStatus) || cycleData.recalculationStatus eq 'USER_CANCELLED'
																	|| (cycleData.recalculationStatus eq 'DONE' && cycleData.mergingStatus eq 'FAILED')
																	|| (cycleData.recalculationStatus eq 'DONE' && cycleData.mergingStatus eq 'INPROGRESS')}">
																	<button type="button" class="btn btn-success btn-sm mergeBtn"
																		style="border-radius: 6px;" disabled>Merge</button>
																</c:if>
															</td>
															<td>
																<c:if
																	test="${cycleData.recalculationStatus eq 'DONE' && (empty cycleData.mergingStatus)}">
																	<button type="button"id="btn-mergeCancel${status.index}" data-id = "${status.index}"
																		class="btn btn-danger btn-sm cancelMerge" data-toggle="modal"
																		data-target="#cancelConfir-modal" data-cycleNumber = "${cycleData.cycleNumber}" data-uid = "${cycleData.uid}"
																		data-cycleDate = "${cycleData.cycleDate}" data-productId = "${cycleData.settlementProductId}" data-requestDate = "${cycleData.requestDate}"
																		style="border-radius: 6px;">Cancel</button>
																</c:if>
																<c:if
																	test="${cycleData.recalculationStatus ne 'DONE' || (cycleData.recalculationStatus eq 'DONE' && cycleData.mergingStatus eq 'DONE')
																	|| (empty cycleData.recalculationStatus && empty cycleData.mergingStatus) || cycleData.recalculationStatus eq 'USER_CANCELLED'
																	|| (cycleData.recalculationStatus eq 'DONE' && cycleData.mergingStatus eq 'FAILED')
																	|| (cycleData.recalculationStatus eq 'DONE' && cycleData.mergingStatus eq 'INPROGRESS')}">
																	<button type="button"
																		class="btn btn-danger btn-sm" data-toggle="modal"
																		data-target="#cancelConfir-modal"
																		style="border-radius: 6px;" disabled>Cancel</button>
																</c:if>
															</td>
														</tr>
													</c:forEach>
												</c:if>
												<c:if test="${empty cycleManagementDTO.cycleData}">
													<tr id="noDataAvailable">
												       <td colspan="5">No data present for selected request date and report type</td>
												    </tr>
												</c:if>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						<div class="col-sm-1"></div>
					</div>
				</div>
				<div class="container">
					<div class="modal fade" id="myModal" role="dialog">  
						    <div class="modal-dialog modal-md">  
						      <div class="modal-content">
							      <div class="modal-header">  
							          <div class="panel-heading">
									  </div>
					        	  </div> 
				        	  	  <div>
					        		<div class="modal-body"> 
					        			<form:form onsubmit="return encodeForm(this);" method="POST" commandeName="reportRegenAndRecal"
										id="addEditMember" modelAttribute="cycleManagementDTO"
										action="reportRegenAndRecal" autocomplete="off">
										<br /> 
										<div class="row">
										 	<div class="col-sm-12">
											  <div class="alert alert-dismissible hide" id="alertNotification" role="alert">
				                            	<strong><span id="alertMessageStrong"></span></strong><span id="alertMessage"></span>
				                         	</div>
											</div>
										</div>
									</form:form>
		        					</div>  
									<div class="modal-footer"> 		
				      					<div class="text-right"> 
				        				 	<button type="button" id="okAction" class="btn btn-primary" data-dismiss="modal" style="border-radius: 6px">Okay</button>
				        				</div> 
			        				</div>
			        		 	</div>  
					  		</div>  
						</div>
					</div>
					<div class="modal fade" id="confirmation-modal" role="dialog">  
						    <div class="modal-dialog modal-md">  
						      <div class="modal-content">
							      <div class="modal-header">  
							          <div class="panel-heading">
									  </div>
					        	  </div> 
				        	  	  <div>
					        		<div class="modal-body">  
										<div class="row">
										 	<div class="col-sm-12">
											  <label>Are you sure you want to continue?</label>
											</div>
										</div>
		        					</div>  
									<div class="modal-footer"> 		
				      					<div class="text-right"> 
				      						<input type = "hidden" id= "conf-merge-data-id"/>
				        				 	<button type="button" class="btn btn-success" id="sendMergeTablesRequest"
												 data-target="#mergeTables" data-toggle="modal" style="border-radius: 6px">
												<spring:message code="am.lbl.confirm" />
											</button>
				        				 	<button type="button" class="btn btn-danger" data-dismiss="modal" style="border-radius: 6px">Close</button> 
				        				</div> 
			        				</div>
			        		 	</div>  
					  		</div>  
						</div>
					</div>
					<div class="modal fade" id="mergeTables" role="dialog">  
						    <div class="modal-dialog modal-md">  
						      <div class="modal-content">
							      <div class="modal-header">  
							          <div class="panel-heading">
									  </div>
					        	  </div> 
				        	  	  <div>
					        		<div class="modal-body">  
										<div class="row">
										 	<div class="col-sm-12">
											  <div class="alert alert-dismissible hide" id="alertMsg" role="alert">
				                            	<strong><span id="alertMessage"></span></strong><span id="mergeTableMessage"></span>
				                         	</div>
											</div>
										</div>
		        					</div>  
									<div class="modal-footer"> 		
				      					<div class="text-right"> 
				        				 	<button type="button" id="okay" class="btn btn-primary" data-dismiss="modal" style="border-radius: 6px">Okay</button> 
				        				</div> 
			        				</div>
			        		 	</div>  
					  		</div>  
						</div>
					</div>
						<div class="modal fade" id="cancelConfir-modal" role="dialog">  
						    <div class="modal-dialog modal-md">  
						      <div class="modal-content">
							      <div class="modal-header">  
							          <div class="panel-heading">
									  </div>
					        	  </div> 
				        	  	  <div>
					        		<div class="modal-body">  
										<div class="row">
										 	<div class="col-sm-12">
											  <label>Are you sure you want to cancel request?</label>
											</div>
										</div>
		        					</div>  
									<div class="modal-footer"> 		
				      					<div class="text-right"> 
				        				 	<button type="button" class="btn btn-success" id="cancelMergeRequest"
												 data-target="#showResp" data-toggle="modal" style="border-radius: 6px">
												<spring:message code="am.lbl.confirm" />
											</button>
				        				 	<button type="button" class="btn btn-danger" data-dismiss="modal" style="border-radius: 6px">Close</button> 
				        				</div> 
			        				</div>
			        		 	</div>  
					  		</div>  
						</div>
					</div>
					<div class="modal fade" id="showResp" role="dialog">  
						    <div class="modal-dialog modal-md">  
						      <div class="modal-content">
							      <div class="modal-header">  
							          <div class="panel-heading">
									  </div>
					        	  </div> 
				        	  	  <div>
					        		<div class="modal-body">  
										<div class="row">
										 	<div class="col-sm-12">
											  <div class="alert alert-dismissible hide" id="alertCancelMsg" role="alert">
				                            	<strong><span id="alertCancelMessage"></span></strong><span id="ShowMessage"></span>
				                         	</div>
											</div>
										</div>
		        					</div>  
									<div class="modal-footer"> 		
				      					<div class="text-right"> 
				        				 	<button type="button" id="Okbtn" class="btn btn-primary" data-dismiss="modal" style="border-radius: 6px">Okay</button> 
				        				</div> 
			        				</div>
			        		 	</div>  
					  		</div>  
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
</div>