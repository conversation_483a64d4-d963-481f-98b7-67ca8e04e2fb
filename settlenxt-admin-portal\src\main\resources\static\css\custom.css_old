@charset "utf-8";
/* CSS Document */

body {
	background: #f4f3ef;
}

#content{min-height:503px;}


/**********************Breadcrum**************************/

.page-title h4{
	    font-size: 24px;
}


.breadcrumb {
    padding: 8px 15px;
    /* margin-bottom: 20px; */
    list-style: none;
    /* background-color: #f5f5f5; */
    /* border-radius: 4px; */
}

.breadcrumbs {
    background-color: #fff;
    display: inline-block;
    margin-top: 0;
    padding: 0 5px;
    width: 100%;box-shadow: 0 2px 2px rgba(204, 197, 185, 0.5);
}

.page-header {
   /* min-height: 50px;*/
    margin: 0px;
    padding: 0px 15px;
    background: #ffffff;
    border-bottom: 0px;
}

.float-right {
    float: right!important;
}

.page-header .breadcrumb {
    margin: 0px;
    padding: 13.5px 0;
    background: #fff;
    text-transform: capitalize;
}

.text-right {
    text-align: right!important;
}

/************************Breadcrum Ends Here***************/




/************************navigation css********************/

.header .nav-tabs {
    border-bottom: 0px solid #ddd;
    float: right;
}

.navbar {
	border-radius: 0px;
}
.navbar-default {
	background-color: #253c84;
	border-color: #253c84;
}
.navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:focus, .navbar-default .navbar-nav>.active>a:hover {
	color: #fff;
	background-color: #324b9e;
}
.navbar-default .navbar-nav>li>a {
	color: #ffffff;
}
.navbar-default .navbar-nav>.open>a, .navbar-default .navbar-nav>.open>a:focus, .navbar-default .navbar-nav>.open>a:hover {
	color: #f5f7fb;
	background-color: #324b9e;
}
.navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
	color: #f5f7fb;
	background-color: transparent;
}
.navbar-nav>li>a {
	padding-top: 9px;
	padding-bottom: 9px;
}
.navbar {
	position: relative;
	min-height: 40px;
	margin-bottom: 10px;
	border: 1px solid transparent;
}

.text-right .nav-tabs>li>a:hover {
    border-color: #fff #fff #fff;
}
.text-right .nav>li>a:focus, .text-right .nav>li>a:hover {
    text-decoration: none;
    background-color: #fff;
}
.text-right .nav-tabs>li>a {
    margin-right: 2px;
    line-height: 1.42857143;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0;
    padding-left: 0px;
}

.text-right .nav .open>a, .text-right .nav .open>a:focus, .text-right .nav .open>a:hover {
    background-color: #fff;
    border-color: #ffffff;
}

.dropdown-menu>li>a {
    display: block;
    padding: 5px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
}


/**********************navigation css ends here************/


/************************Card Css*******************/

.card-white {
    border-radius: 2px;
  box-shadow: 0 2px 2px rgba(204, 197, 185, 0.5);
    background-color: #FFFFFF;
    color: #252422;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
    padding: 15px;
}

.card {
    border-radius: 0px;
    background-color: #fff;
    margin-bottom: 30px;
   /* -webkit-box-shadow: 0px 1px 15px 1px rgba(69, 65, 78, 0.08);
    -moz-box-shadow: 0px 1px 15px 1px rgba(69, 65, 78, 0.08);
    box-shadow: 0px 1px 15px 1px rgba(69, 65, 78, 0.08);*/
    border: 1px solid #bcd1f1;box-shadow: 0 2px 2px rgba(204, 197, 185, 0.5);
}

.card .card-header {
    padding: 10px 10px;
    background-color: transparent;
    border-bottom: 1px solid #bcd1f1 !important;
}

.card-header:first-child {
    border-radius: calc(.25rem - 1px) calc(.25rem - 1px) 0 0;
}

.card .card-title {
    margin: 0;
    color: #575962;
    font-size: 18px;
    font-weight: 400;
    line-height: 1.6;
}

.card .card-body {
    padding: 15px 15px 10px 15px;
}

.form-group label, .form-check label {
    margin-bottom: .5rem !important;
    color: #1b2b56 !important;
    font-weight: 400;
    font-size: 1.4rem;
}

.card .card-action {
    padding: 15px 15px 15px 15px;
    background-color: transparent;
    line-height: 30px;
    border-top: 1px solid #ebedf2 !important;
    font-size: 14px;
}

.form-control {
 border: 1px solid #c2d1e8;border-radius: 2px;
}

/********************************************************/

/**********************************Content********************************/


.bottom_space{padding-bottom: 20px;}

.no_margin{margin:0px !important;}

#content-body .nav>li>a {
    position: relative;
    display: block;
    padding: 10px 15px;
    background: #fafafa;
}


.height-min{
min-height:550px;	
}


.page-title{margin-top:0px;}

.profile {
    width: 45px !important;
    padding: 8px;
}

.pagination>.active>a, .pagination>.active>a:focus, .pagination>.active>a:hover, .pagination>.active>span, .pagination>.active>span:focus, .pagination>.active>span:hover {
    z-index: 3;
    color: #fff;
    cursor: default;
    background-color: #1b327e;
    border-color: #1b327e;
}

.pagination>li>a, .pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 12px;
    margin-left: -1px;
    line-height: 1.42857143;
    color: #1b327e;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
}
    
.table>thead>tr>th {
    vertical-align: bottom;
    border-bottom: 2px solid #c2d1e8;
    /* color: #1b2b56 !important; */
    /* color: #9496a1; */
    color: #212529;
    font-weight: 600;
    background: #ecf4ff;
    /* background-color: rgba(0,0,0,.03); */
}

.btn-primary {
    color: #fff;
    background-color: #2986c9;
    border-color: #2986c9;
}
.btn-primary:hover {
    color: #fff;
    background-color: #58b3f4;
    border-color: #58b3f4;
}

.btn-primary.focus, .btn-primary:focus {
    color: #fff;
    background-color: #2986c8;
    border-color: #2986c8;
}

.btn-primary.active.focus, .btn-primary.active:focus, .btn-primary.active:hover, .btn-primary:active.focus, .btn-primary:active:focus, .btn-primary:active:hover, .open>.dropdown-toggle.btn-primary.focus, .open>.dropdown-toggle.btn-primary:focus, .open>.dropdown-toggle.btn-primary:hover {
    color: #fff;
    background-color: #2986c8;
    border-color: #2986c8;
}

.btn {
 border-radius: 2px;
 text-transform: uppercase;
 font-size: 12px;
 letter-spacing: 0.8px;
 margin-right: 2px;
}


/*.form-group button{margin-top: 26px;}*/
.search_card{
	background: #ecf4ff;
	border:solid 1px #bcd1f1;
}

.welcome_txt {
    font-weight: 300;
  /* margin-right: 15px;*/
    text-align: right;
 
    position: relative;
    top: 0px;
}

.text-right .dropdown-menu {
    right: 0px;
    left: auto;
}

.header{background: #fff;padding:10px;}

.npci {
    width: 120px;
    margin-left: 0PX;margin-top: 5px;
}

.upi {
    width: 96px;
    margin-left: 15px;margin-top: 5px;
}

.paneldev{
	    padding: 10px;
    padding-left: 0px;
}

.newPanel{
	    border-radius: 0;
    box-shadow: NONE;
    BORDER: 0;
}

.icon_bar{float:right;width:250px;height:18px;text-align: right;}
.icon_bar img {width:18px;margin-left: 20px;cursor:pointer;}

.body-content {
	padding: 12px;
}

.table-responsive {
    min-height: .01%;
    overflow-x: auto;
    padding: 7px;
}


.panel>.table-bordered, .panel>.table-responsive>.table-bordered {
    border: 1 !important;
}


.panel {
   /* margin-bottom: 20px;*/
   margin: 8px;
}

.table-bordered{border: solid 1px #c2d1e8 !important;}

.tab-content>.active {
    display: block;
    background: #fff;
    border: solid 1px #ddd;
    border-top: 0;
    border-radius: 0px 0px 4px 4px;
}

.nav>li>a:focus, .nav>li>a:hover {
    text-decoration: none;
    background-color: #ffffff;
    /* color: #fff; */
    /* border: solid 1px #ddd; */
}
.nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover {
    color: #1a327d;
    cursor: default;
    background-color: #fff;
    border: 1px solid #ddd;
    border-bottom-color: transparent;
}

a {
    color: #343535;
    text-decoration: none;
}

a:focus, a:hover {
    color: #464748;
    text-decoration: underline;
}

/***********************Content css ends here****************************/


/*************************Button CSS Starts here**********************************/

.btn_align{margin: 10PX 10PX 2PX 0PX;}
.btn-success {
    color: #253c84;
    background-color: #ffffff;
    border-color: #253c84;
}

.btn-success:hover {
    color: #fff;
    background-color: #253c84;
    border-color: #253c84;
}

.btn-success.focus, .btn-success:focus {
    color: #fff;
    background-color: #253c84;
    border-color: #253c84;
}
.btn-success.active.focus, .btn-success.active:focus, .btn-success.active:hover, .btn-success:active.focus, .btn-success:active:focus, .btn-success:active:hover, .open>.dropdown-toggle.btn-success.focus, .open>.dropdown-toggle.btn-success:focus, .open>.dropdown-toggle.btn-success:hover {
    color: #fff;
    background-color: #253c84;
    border-color: #253c84;
}


/**************************Button CSS Ends here********************************/


/*********************footer css***************************/

.footer {
	position: relative;
	bottom: 0;
	width: 100%;
	height: auto;color:#ccc;
	background-color: #2b2b2b;
}
.container .text-muted {
	margin: 20px 0;
}


.error
    {
    	color: red !important;
    }


/*********************footer css ends here***************************/

@media (max-width: 767px)
.navbar-default .navbar-nav .open .dropdown-menu>li>a {
    color: #c2d1e8;
}

@media (max-width: 767px)
.navbar-default .navbar-nav .open .dropdown-menu>li>a:focus, .navbar-default .navbar-nav .open .dropdown-menu>li>a:hover {
    color: #fff;
    background-color: transparent;
}
