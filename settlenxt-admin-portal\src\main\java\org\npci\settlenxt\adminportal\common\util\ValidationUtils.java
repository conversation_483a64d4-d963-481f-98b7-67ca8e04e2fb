package org.npci.settlenxt.adminportal.common.util;

import java.io.File;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.mapping.DataElementNameMapping;
import org.npci.settlenxt.adminportal.common.mapping.MessageFormatNameMapping;
import org.npci.settlenxt.adminportal.common.mapping.NameMappingContext;
import org.npci.settlenxt.adminportal.common.mapping.NameMappingLoader;
import org.npci.settlenxt.adminportal.validator.service.dto.CustomMappingUtils;
import org.npci.settlenxt.adminportal.validator.service.dto.Record;
import org.npci.settlenxt.adminportal.validator.service.dto.RecordError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ValidationUtils {
	
	@Autowired
	private static Environment environment;
	
	private static String configFile;
	
	private static String nFunCd="nFunCd";
	private static String fileLocation="/npci/guiserver/portal/adminportal/";
	@PostConstruct
	public static void init() {
		String mainLocPath;
		if (environment == null) {
			log.info("environment is empty");
			mainLocPath = fileLocation;
		} else {
			mainLocPath = environment.getProperty("FILE_CLASSPATH");
		}
		configFile = mainLocPath;
	}
	
	public static Map<String, String> listAsMap(List<Record> records) {
		Map<String, String> recordsMap = new HashMap<>();
		
		for (Record rec : records) {
			recordsMap.put(rec.getName(), rec.getValue());
		}
		
		return recordsMap;
	}
	
	public Map<String, Record> listAsRecordMap(List<Record> records) {
		Map<String, Record> recordsMap = new HashMap<>();
		
		for (Record rec : records) {
			recordsMap.put(rec.getName(), rec);
		}
		
		return recordsMap;
	}


	public static List<RecordError> validateRequiredFields(ValidationType type, Map<String, String> recordsMap, String xml,  LoadRejectReasonCode rejReason){
		List<RecordError> errorList = new ArrayList<>();
		MessageFormatNameMapping mapping;
		String mti = recordsMap.get("nMTI");
		String funcCode = recordsMap.get(nFunCd);
		if(StringUtils.isBlank(mti) || StringUtils.isBlank(funcCode) || StringUtils.isNotBlank(funcCode)){
			handleValidationType(type, errorList, mti, funcCode);
		}
		
		if(StringUtils.isNotBlank(mti) && StringUtils.isNotBlank(funcCode)) {
			String name = mti + funcCode;
			mapping = CustomMappingUtils.getMessageFormatNameMapping(NameMappingContext.MESSAGEFORMAT, name);
			if(mapping != null){
				List<String> requiredFieldList = mapping.getFields().getRequiredFields();
				if(requiredFieldList != null && Boolean.FALSE.equals(requiredFieldList.isEmpty())){
					handleChanges(recordsMap, xml, rejReason, errorList, mti, funcCode, requiredFieldList);
				}
				
			}
		}
		
		return errorList;
	}

	private static void handleChanges(Map<String, String> recordsMap, String xml, LoadRejectReasonCode rejReason,
			List<RecordError> errorList, String mti, String funcCode, List<String> requiredFieldList) {
		String fieldName = "";
		for(String requireField :requiredFieldList){
			fieldName = requireField;
			if(StringUtils.isNotBlank(fieldName) && !recordsMap.containsKey(fieldName)){
				
				try {
					String nMsgRsnCd = recordsMap.get("nMsgRsnCd");
					
					String variant = System.getProperty("variant", "");
					if(StringUtils.isBlank(variant)) {
						variant = "NPCI";
					}
					String docpath=configFile + "mappings/NPCI/config" + variant + ".json";
					Path p = Paths.get(docpath).normalize();
					String uploadFilePath = new File(p.toString()).getCanonicalPath();
					URL url = NameMappingLoader
							.getFilePathUsingCP(uploadFilePath);
					ObjectMapper mapper = new ObjectMapper();
					Map<String, Map<String, Object>> configData = mapper.readValue(new File(url.toURI()),
							new TypeReference<Map<String, Map<String, Object>>>() {
							});
					
					String optionalTagsName = mti + "-" + funcCode + "-Tags";
					String optionalReasonCodesName = mti + "-" + funcCode + "-ReasonCodes";
					@SuppressWarnings("unchecked")
					List<String> optionalTags = (ArrayList<String>) configData.get("fileUploadFilter")
							.get(optionalTagsName);
					@SuppressWarnings("unchecked")
					List<String> optionalReasonCodes = (ArrayList<String>) configData.get("fileUploadFilter")
							.get(optionalReasonCodesName);
					if (CollectionUtils.isNotEmpty(optionalTags)
							&& CollectionUtils.isNotEmpty(optionalReasonCodes)
							&& StringUtils.isNotBlank(nMsgRsnCd) && optionalTags.contains(fieldName)
							&& optionalReasonCodes.contains(nMsgRsnCd)) {
							continue;
					}
				}catch(Exception e) {
					log.info("Error while checking for required fields ", e);
				}
				//MFD and MFC changes - End
				
				DataElementNameMapping dataMapping = CustomMappingUtils.getDataElementNameMapping(NameMappingContext.DATAELEMENTS, fieldName);							
				
				int missingCode = generateMissingCode(dataMapping);
				log.debug("Required Field Name : [{}] not found [RecordSeq={}]", fieldName , recordsMap.get("nRecNum"));
				errorList.add(new RecordError(missingCode, rejReason.getRejectReasonDesc(String.valueOf(missingCode)), fieldName, null, xml));
			}
		}
	}

	private static int generateMissingCode(DataElementNameMapping dataMapping) {
		int missingCode = -1;
		if (dataMapping != null && dataMapping.getReasonCodes() != null) {
			missingCode = dataMapping.getReasonCodes().getMissing();
		}
		return missingCode;
	}

	private static void handleValidationType(ValidationType type, List<RecordError> errorList, String mti,
			String funcCode) {
		if(type.equals(ValidationType.HEADER)){
			if (StringUtils.isBlank(mti)) {
				errorList.add(new RecordError(5002,
								"MTI must be present in Header Message.", "nMTI", mti, ""));
			}			
			if (StringUtils.isBlank(funcCode)) {
				errorList.add(new RecordError(5006,
								"Function code must be present in Header Message.", nFunCd, funcCode, ""));
			}
			if (StringUtils.isNotBlank(funcCode) && !"670".equals(funcCode)) {
				errorList.add(new RecordError(5005,
								"Function Code in Header should be 670 for all disputes", nFunCd, funcCode, ""));
			}
		}else if(type.equals(ValidationType.TRAILER)){
			if (StringUtils.isBlank(mti)) {
				errorList.add(new RecordError(5023,
								"MTI must be present in Trailer Message.", "nMTI", mti, ""));
			}
			if (StringUtils.isBlank(funcCode)) {
				errorList.add(new RecordError(5024,
								"Function code must be present in Trailer Message.", nFunCd, funcCode, ""));
			}
			handleForFuncCode671(errorList, funcCode);
			
		}
	}

	private static void handleForFuncCode671(List<RecordError> errorList, String funcCode) {
		if (StringUtils.isNotBlank(funcCode) && !"671".equals(funcCode)) {
			errorList.add(new RecordError(5005,
							"Function Code in Trailer should be 671 for all disputes", nFunCd, funcCode, ""));
		}
	}

	// convenience method that will handle converting the list to map for us
	public static List<RecordError> validateRequiredFields(ValidationType type, List<Record> recordsMap, String xml, LoadRejectReasonCode rejReasonCode){
		return validateRequiredFields(type, listAsMap(recordsMap), xml, rejReasonCode);
	}
}
