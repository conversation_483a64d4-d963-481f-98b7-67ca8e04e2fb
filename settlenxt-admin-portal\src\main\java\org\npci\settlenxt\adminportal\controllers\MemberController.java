package org.npci.settlenxt.adminportal.controllers;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.ErrorConstants;
import org.npci.settlenxt.adminportal.dto.IFSCDTO;
import org.npci.settlenxt.adminportal.service.IFSCService;
import org.npci.settlenxt.adminportal.service.MasterService;
import org.npci.settlenxt.adminportal.service.MemberService;
import org.npci.settlenxt.adminportal.service.NetWorkBinUploadInterface;
import org.npci.settlenxt.adminportal.service.NetworkBinUploadFactory;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.BinDetailsDTO;
import org.npci.settlenxt.portal.common.dto.CityDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.MemberOnBoardingDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.exception.SettleNxtSystemException;
import org.npci.settlenxt.portal.common.service.BaseLookupServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.introspect.VisibilityChecker;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;


@Controller
public class MemberController extends BaseController {
	private static final Logger log = LogManager.getLogger(MemberController.class);
	private static final String LANDING_MAIN = "main";
	private static final String LANDING_PARAM = "landing";
	private static final String ERROR_RESPONSE_KEY = "error";
	private static final String SUCCESS_RESPONSE_KEY = "success";
	private static final String RECORD_STATUS_VALUE_PENDING = "P";
	private static final String REQUEST_STATE_VALUE_SUBMIT = "SUBMIT";
	private static final String SAVE_MEMBER_FAILURE_RESPONSE = "Unable to save the member details";
	private static final String SAVE_MEMBER_SUCCESS_RESPONSE = "Member details have been saved sucessfully";
	private static final String REQUEST_STATE_ATTRIBUTE = "requestState";
	private static final String SAVE_MEMBER_RESULT_VALUE_SAVE = "SAVE";
	private static final String REQUEST_STATE_VALUE_SAVE = "SAVE";
	private static final String SAVE_MEMBER_RESULT_ATTRIBUTE = "saveMemberResult";
	private static final String MEMBER_ON_BOARDING_DTO_ATTRIBUTE_NAME = "memberOnBoardingDTO";
	private static final String VIEW_ADD_EDIT_MEMBER = "MemberAdd";
	private static final String VIEW_MEMBER = "MemberView";
	private static final String ATTRIBUTE_NAME_SPONSOR_BANK_LIST = "sponsorBankList";
	private static final String FILE_DOWNLOAD_REQD_VALUE_Y = "Y";
	private static final String ATTRIBUTE_FILE_DOWNLOAD_REQD = "fileDownloadReqd";
	private static final String ATTRIBUTE_NAME_CITY_LIST = "cityList";
	private static final String REQ_TYPE_ADD = "ADD";
	private static final String REQ_TYPE = "reqType";
	private static final String SCREEN_NAME = "screenName";
	private static final String SUB_NET_DEFAULT_VALUE = "subNetDefaultValue";
	private static final String IFSC_CODES_LIST = "ifscCodesList";
	private static final String STATE_LIST = "stateList";
	private static final String COUNTRY_LIST = "countryList";
	private static final String MEMBER_TYPE_LIST = "memberTypeList";
	private static final String BIN_PROD_TYPE_LIST = "binProdTypeList";
	private static final String BIN_CARD_TYPE_LIST = "binCardTypeList";
	private static final String BIN_CARD_VARIANT_LIST = "binCardVariantList";
	private static final String BIN_CARD_BRAND_LIST = "binCardBrandList";
	private static final String DOMAIN_IN_USAGE_LIST = "domainUsageList";
	private static final String MESSAGE_TYPE_LIST = "messageTypeList";
	private static final String BANK_GROUP_LIST = "bankGroupList";
	private static final String BIN_TYPE_LIST = "binTypeList";
	private static final String CARD_TECH_LIST = "cardTechList";
	private static final String AUTH_MECH_LIST = "authMechList";
	private static final String ADDRESS_TYPE_LIST = "addressTypeList";
	private static final String PRODUCT_TYPE_LIST = "productTypeList";
	private static final String SUB_SCHEME_LIST = "subSchemeList";
	private static final String CARD_SUB_VARIANT_LIST = "cardSubVariantList";
	private static final String PROGRAM_DETAILS_LIST = "programDetailsList";
	private static final String FORM_FACTOR_LIST = "formFactorList";
	private static final String BANK_SECTOR_LIST = "bankSectorList";
	private static final String CURRENCY_CODE_LIST = "currencyCodeList";
	private static final String CLEARING_AGENCY_TYPE_LIST = "clearingAgencyTypeList";
	private static final String SUBNET_LIST = "subNetList";
	private static final String FEATURE_MAP = "featureMap";
	private static final String UNALLOCATED_ISS_BIBDATA = "unallocatedIssBinData";
	private static final String BANK_MASTER_CODE = "bankMasterCode";
	private static final String PARTICIPANT_ID = "participantId";
	private static final String RTGS_CODE = "rtgsCode";
	private static final String SAVING_ACC_NUMBER = "savingsAccNumber";
	private static final String CURRENT_ACC_NO = "currentAccNo";
	private static final String PARTICIPANT_ID_NFS = "participantIdNFS";
	private static final String UNIQUE_BANKS = "uniqueBanks";
	private static final String I = "I";
	private static final String ZERO = "0";
	private static final String RECORD_STATUS_VALUE_APPROVE = "A";
	private static final String RECORD_STATUS_VALUE_DISCARD = "D";
	private static final String FETURES_ISSUER_BIN = "feturesIssuerbin";
	private static final String BIN_CARD_VARIANT = "binCardVariant";
	private static final String PLEASE_PROVIDE_MEMBER_CONTACT_DETAILS = "Please provide member contact details ";
	private static final String PARTICIPANT_ID_AND_TYPE_ARE_MANDATORY = "Participant ID and Bank Type are mandatory";
	private static final String PLEASE_PROVIDE_BIN_DETAILS = "Please provide bin details.";
	private static final String PLEASE_UPLOSD_DOCUMENTS = "Please upload Documents.";
	private static final String MEMBER_DETAILS_ARE_UPDATED_ADN_SENT_FOR_APPROVAL = "Member details are updated and sent for approval";
	private static final String UNABLE_TO_SUBMIT_THE_MEMBER_DETAILS_FOR_APPROVAL = "Unable to submit the memdber details for approval";
	private static final String DISCARD_MEMBER_SUCCESS = "Discard Member Success";
	private static final String DISCARD_MEMBER_FAIELD = "Discard member Failed";
	private static final String FILE_UPLOAD_PATH = "FILE_UPLOAD_PATH";
	private static final String REGEX = "[^a-zA-Z0-9.-]";
	private static final String SEEPERATOR = "_";
	private static final String UTF_8 = "UTF-8";
	private static final String MEMBER_DETAILS_HAS_BEEN_APPROVED = "Member details has been approved";
	private static final String MEMBER_DETAILS_HAS_BEEN_REJECTED = "Member details has been rejected";
	private static final String BIN_DETAILS_DTO = "binDetailsDto";
	private static final String NFS_FILE_UPLOAD = "nfsFileUpload";
	private static final String INTERNATIONAL_PARTICIPANT = "InternationalParticipant";
	private static final String NETWORK_CODE = "networkCode";


	private static final String ADDITIONAL_PARAMS = "ADDITIONAL_PARAMS";

	private static final String BIN_TYPE_LENGTH = "BIN_TYPE_LENGTH";

	private String isInternational;
	private String networkCode;

	@Autowired
	BaseLookupServiceImpl lookUpService;

	@Autowired
	private MemberService memberService;

	@Autowired
	private IFSCService ifscService;

	@Autowired
	private MasterService masterService;
	
	@Autowired
	private NetworkBinUploadFactory netWorkBinFactroy;
	
	
	@PostConstruct
	void init() {
		isInternational = env.getProperty("is.international.enabled");
		networkCode = env.getProperty("bankType.network.code", "P");
	}

	@PostMapping("/memberCreation")
	@PreAuthorize("hasAuthority('Add Member')")
	public String memberCreation(Model model) {
		MemberOnBoardingDTO memberOnBoardingDTO = new MemberOnBoardingDTO();
		addDefaultListData(model);
		model.addAttribute(SCREEN_NAME, BaseCommonConstants.ADDMEMBER);
		model.addAttribute(ATTRIBUTE_NAME_CITY_LIST, null);
		model.addAttribute(REQ_TYPE, REQ_TYPE_ADD);
		model.addAttribute(ATTRIBUTE_FILE_DOWNLOAD_REQD, FILE_DOWNLOAD_REQD_VALUE_Y);
		model.addAttribute(MEMBER_ON_BOARDING_DTO_ATTRIBUTE_NAME, memberOnBoardingDTO);
		List<MemberDTO> memberList = memberService.getMembersSponsoBanks();
		model.addAttribute(ATTRIBUTE_NAME_SPONSOR_BANK_LIST, memberList);
		model.addAttribute(INTERNATIONAL_PARTICIPANT, isInternational);
		model.addAttribute(NETWORK_CODE, networkCode);
		return getView(model, VIEW_ADD_EDIT_MEMBER);
	}

	private void addDefaultListData(Model model) {
		String subNetDefaultValue = env.getProperty("sub.network.default");

		model.addAttribute(SUB_NET_DEFAULT_VALUE, subNetDefaultValue);
		model.addAttribute(IFSC_CODES_LIST, masterService.getIfscCodes());
		model.addAttribute(STATE_LIST, masterService.getStateMaster());
		model.addAttribute(ATTRIBUTE_NAME_CITY_LIST, masterService.getCityMaster());
		model.addAttribute(COUNTRY_LIST, masterService.getCountryList());
		model.addAttribute(MEMBER_TYPE_LIST, masterService.getCachedLookUpData(BaseCommonConstants.MEMBERTYPE));
		model.addAttribute(BIN_PROD_TYPE_LIST, masterService.getCachedLookUpData(BaseCommonConstants.BIN_PRODUCT_TYPE));
		model.addAttribute(BIN_CARD_TYPE_LIST, masterService.getCachedLookUpData(BaseCommonConstants.BIN_CARD_TYPE));
		model.addAttribute(BIN_CARD_VARIANT_LIST, masterService.getCachedLookUpData(BaseCommonConstants.BIN_CARD_VARIANT));
		model.addAttribute(BIN_CARD_BRAND_LIST, masterService.getCachedLookUpData(BaseCommonConstants.BIN_CARD_BRAND));
		model.addAttribute(DOMAIN_IN_USAGE_LIST, masterService.getCachedLookUpData(BaseCommonConstants.DOMAIN_USAGE));
		model.addAttribute(MESSAGE_TYPE_LIST, masterService.getCachedLookUpData(BaseCommonConstants.MESSAGE_TYPE));
		model.addAttribute(BANK_GROUP_LIST,
				masterService.getCachedLookUpDataSortedByDescription(BaseCommonConstants.BANK_GROUP));
		model.addAttribute(BIN_TYPE_LIST, masterService.getCachedLookUpData(CommonConstants.BIN_TYPE));
		model.addAttribute(CARD_TECH_LIST, masterService.getCachedLookUpData(BaseCommonConstants.CARD_TECHNOLOGY));
		model.addAttribute(AUTH_MECH_LIST, masterService.getCachedLookUpData(BaseCommonConstants.AUTH_MECHANISM));
		model.addAttribute(ADDRESS_TYPE_LIST, masterService.getCachedLookUpData(BaseCommonConstants.ADDRESS_TYPE));
		model.addAttribute(PRODUCT_TYPE_LIST, masterService.getCachedLookUpData(BaseCommonConstants.PRODUCT_TYPE));
		model.addAttribute(SUB_SCHEME_LIST, masterService.getCachedLookUpData(BaseCommonConstants.SUB_SCHEME));
		model.addAttribute(CARD_SUB_VARIANT_LIST, masterService.getCachedLookUpData(BaseCommonConstants.CARD_SUB_VARIANT));
		model.addAttribute(PROGRAM_DETAILS_LIST,
				masterService.getCachedLookUpData(BaseCommonConstants.PROGRAM_DETAILS_LIST));
		model.addAttribute(FORM_FACTOR_LIST, masterService.getCachedLookUpData(BaseCommonConstants.FORM_FACTOR_LIST));
		model.addAttribute(BANK_SECTOR_LIST, masterService.getCachedLookUpData(BaseCommonConstants.BANK_SECTOR_LIST));
		model.addAttribute(CURRENCY_CODE_LIST, masterService.getCachedLookUpData(BaseCommonConstants.CURRENCY_CODE));
		model.addAttribute(CLEARING_AGENCY_TYPE_LIST,
				masterService.getCachedLookUpData(BaseCommonConstants.NPCI_CLR_AGENCY));
		model.addAttribute(SUBNET_LIST, masterService.getCachedLookUpData(BaseCommonConstants.SUB_NET));
		model.addAttribute(FEATURE_MAP, memberService.getFeatureMap());
		model.addAttribute(UNALLOCATED_ISS_BIBDATA, memberService.getUnallocatedIssBinData());
		List<CodeValueDTO> binTypeLength = masterService.getCachedLookUpData(BIN_TYPE_LENGTH);
		if ("Y".equalsIgnoreCase(isInternational)) {
			List <CodeValueDTO> networkSelectionList=memberService.getNetworkSelection();
			List <CodeValueDTO> currencyConvList=masterService.getCachedLookUpDataSortedByDescription("CURRENCY_CONVERSION");
			List <CodeValueDTO> forexIdList=memberService.getForexId();
			List <CodeValueDTO> currencyConvTypeList=masterService.getCachedLookUpDataSortedByDescription("CURRENCY_CONVERSION_TYPE");
		model.addAttribute("networkSelectionList",networkSelectionList);
		model.addAttribute("currencyConvList",currencyConvList);
		
		model.addAttribute("forexIdList",forexIdList);
		model.addAttribute("currencyConvTypeList",currencyConvTypeList);}
		
		
		if (!ObjectUtils.isEmpty(binTypeLength)) {
			model.addAttribute("binTypeLength", binTypeLength);
		}

		List<CodeValueDTO> additionalParamsList = masterService.getCachedLookUpData(ADDITIONAL_PARAMS);
		if (!ObjectUtils.isEmpty(additionalParamsList)) {
			Collections.sort(additionalParamsList,
					Comparator.comparing(codeValueDTO -> Integer.parseInt(codeValueDTO.getCode())));
			model.addAttribute("additionalParamsList", additionalParamsList);
		}


	}

	@PreAuthorize("hasAnyAuthority('Add Member','Edit Member')")
	@PostMapping("/getCityMaster")
	public ResponseEntity<Object> getCityMaster(@RequestParam("stateId") Integer stateId) {
		List<CityDTO> list = null;
		list = masterService.getCityMaster(stateId);
		return new ResponseEntity<>(list, HttpStatus.OK);
	}

	@PreAuthorize("hasAnyAuthority('Add Member','Edit Member')")
	@PostMapping("/getIFSCDetails")
	public ResponseEntity<String> getIFSCDetails(@RequestParam("ifscCode") String ifscCode) throws SettleNxtException {
		JsonObject jsonResponse = new JsonObject();
		Integer participantSeq = memberService.fetchParticipantIdSeq(ifscCode);
		List<String> bankNameList = masterService.getListUniqueBankName(ifscCode);
		JsonElement bankNamesElement = new Gson().toJsonTree(bankNameList);
		IFSCDTO ifscdto = ifscService.getApprovedIFSC(ifscCode);
		if (ifscdto != null) {
			String participantId = ifscCode + ifscdto.getBankCode() + String.format("%04d", participantSeq);
			jsonResponse.addProperty(BANK_MASTER_CODE, ifscdto.getBankCode());
			jsonResponse.addProperty(PARTICIPANT_ID, participantId);
			jsonResponse.addProperty(RTGS_CODE, ifscdto.getRtgsAccId());
			jsonResponse.addProperty(SAVING_ACC_NUMBER, ifscdto.getSavingsAccId());
			jsonResponse.addProperty(CURRENT_ACC_NO, ifscdto.getCurrAccId());
			jsonResponse.addProperty(PARTICIPANT_ID_NFS, ifscdto.getNfsCode());
			jsonResponse.add(UNIQUE_BANKS, bankNamesElement);
		}
		refreshSession();
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PreAuthorize("hasAnyAuthority('Add Member','Edit Member')")
	@PostMapping("/getFeatures")
	public ResponseEntity<String> getBinFeatures(@RequestParam("cardType") String cardType) {
		JsonObject jsonResponse = new JsonObject();
		String issuerBinFeatures = masterService.getBinFeatures(cardType);
		jsonResponse.addProperty(FETURES_ISSUER_BIN, issuerBinFeatures);

		String binCardVariant = masterService.getBinCardVariant(cardType);
		jsonResponse.addProperty(BIN_CARD_VARIANT, binCardVariant);
		refreshSession();
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PostMapping("/addMember")
	@PreAuthorize("hasAnyAuthority('Add Member','Edit Member')")
	@ResponseBody
	public ResponseEntity<String> saveMember(@RequestParam("model") String model,
			@RequestParam(name = "document", required = false) MultipartFile[] uploadfiles) {
		JsonObject jsonResponse = new JsonObject();
		try {
			MemberOnBoardingDTO memberOnBoardingDTO = createModel(model);

			refreshSession();
			if (StringUtils.isNotEmpty(memberOnBoardingDTO.getParticipantId())
					&& !ZERO.equals(memberOnBoardingDTO.getMemberType())) {
				if (!memberService.checkIfParticipantStgExists(memberOnBoardingDTO.getParticipantId())) {
					memberService.addMemberInfo(memberOnBoardingDTO);
				} else {
					memberOnBoardingDTO.setRecordStatus(I);
					memberService.updateMemberInfo(memberOnBoardingDTO, uploadfiles);
				}

				jsonResponse.addProperty(SAVE_MEMBER_RESULT_ATTRIBUTE, SAVE_MEMBER_RESULT_VALUE_SAVE);
				jsonResponse.addProperty(REQUEST_STATE_ATTRIBUTE, REQUEST_STATE_VALUE_SAVE);
				jsonResponse.addProperty(SUCCESS_RESPONSE_KEY, SAVE_MEMBER_SUCCESS_RESPONSE);
				return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
			} else {
				jsonResponse.addProperty(ERROR_RESPONSE_KEY, PARTICIPANT_ID_AND_TYPE_ARE_MANDATORY);
				return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
			}
		} catch (SettleNxtException e) {
			log.error("Save Member failed", e);
			jsonResponse.addProperty(SAVE_MEMBER_RESULT_ATTRIBUTE, "");
			jsonResponse.addProperty(ERROR_RESPONSE_KEY, e.getErrorMessage());
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		} catch (Exception e) {
			log.error("Save Member failed", e);
			jsonResponse.addProperty(SAVE_MEMBER_RESULT_ATTRIBUTE, "");
			jsonResponse.addProperty(ERROR_RESPONSE_KEY, SAVE_MEMBER_FAILURE_RESPONSE);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}

	}

	protected MemberOnBoardingDTO createModel(String model) throws JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
		mapper.setVisibility(
				VisibilityChecker.Std.defaultInstance().withFieldVisibility(JsonAutoDetect.Visibility.ANY));
		return mapper.readValue(model, MemberOnBoardingDTO.class);
	}

	@PostMapping("/submitMemberData")
	@PreAuthorize("hasAnyAuthority('Add Member','Edit Member')")
	@ResponseBody
	public ResponseEntity<String> submitMemberData(@RequestParam("model") String model,
			@RequestParam(name = "document", required = false) MultipartFile[] uploadfiles) {
		JsonObject jsonResponse = new JsonObject();
		try {
			refreshSession();
			MemberOnBoardingDTO memberOnBoardingDTO = createModel(model);
			if (memberOnBoardingDTO.getAddressType().equalsIgnoreCase(ZERO)) {
				jsonResponse.addProperty(ERROR_RESPONSE_KEY, PLEASE_PROVIDE_MEMBER_CONTACT_DETAILS);
				return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
			}
			if (CollectionUtils.isEmpty(memberOnBoardingDTO.getAcqBinList())
					&& CollectionUtils.isEmpty(memberOnBoardingDTO.getIssBinList())) {
				jsonResponse.addProperty(ERROR_RESPONSE_KEY, PLEASE_PROVIDE_BIN_DETAILS);
				return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
			}
			if ("Y".equalsIgnoreCase(isInternational)
					&& !CollectionUtils.isEmpty(memberOnBoardingDTO.getIssBinList())) {
				List<String> binList = memberOnBoardingDTO.getIssBinList().stream()
						.filter(x -> ((x.getMarkUp() == 0) || x.getNetworkSelection() == null))
						.map(BinDetailsDTO::getBinNumber).toList();
				if (!binList.isEmpty()) {
					jsonResponse.addProperty(ERROR_RESPONSE_KEY, "Data not entered for bin :" + binList.toString());
					return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
				}
			}
			if (!memberService.checkIfParticipantExists(memberOnBoardingDTO.getParticipantId()) && memberOnBoardingDTO.getAcqBinList().stream()
					.filter(acqBin -> RECORD_STATUS_VALUE_APPROVE.equalsIgnoreCase(acqBin.getStatus())).count() == 0
					&& memberOnBoardingDTO.getIssBinList().stream()
							.filter(issBin -> RECORD_STATUS_VALUE_APPROVE.equalsIgnoreCase(issBin.getStatus()))
							.count() == 0)  {
				
					jsonResponse.addProperty(ERROR_RESPONSE_KEY, PLEASE_PROVIDE_BIN_DETAILS);
					return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
				
			}

			if (memberOnBoardingDTO.getDocuments().stream()
					.filter(doc -> RECORD_STATUS_VALUE_APPROVE.equalsIgnoreCase(doc.getStatus())).count() == 0) {
				jsonResponse.addProperty(ERROR_RESPONSE_KEY, PLEASE_UPLOSD_DOCUMENTS);
				return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
			}
			memberOnBoardingDTO.setRequestState(REQUEST_STATE_VALUE_SUBMIT);
			memberOnBoardingDTO.setRecordStatus(RECORD_STATUS_VALUE_PENDING);

			memberService.updateMemberInfo(memberOnBoardingDTO, uploadfiles);
			jsonResponse.addProperty(SUCCESS_RESPONSE_KEY, MEMBER_DETAILS_ARE_UPDATED_ADN_SENT_FOR_APPROVAL);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		} catch (SettleNxtException e) {
			log.error("Unable to submit the memd", e);
			jsonResponse.addProperty(ERROR_RESPONSE_KEY, e.getErrorMessage());
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		} catch (Exception e) {
			log.error("Submit Member failed", e);
			jsonResponse.addProperty(ERROR_RESPONSE_KEY, UNABLE_TO_SUBMIT_THE_MEMBER_DETAILS_FOR_APPROVAL);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}

	}

	@PostMapping("/discardMemberData")
	@PreAuthorize("hasAnyAuthority('Add Member','Edit Member')")
	@ResponseBody
	public ResponseEntity<String> discardMemberData(@RequestBody MemberOnBoardingDTO memberOnBoardingDTO,
			HttpServletRequest request, RedirectAttributes redirectAttributes) {
		refreshSession();
		JsonObject jsonResponse = new JsonObject();
		try {
			if (memberService.checkIfParticipantExists(memberOnBoardingDTO.getParticipantId())) {
				memberService.discardMemberInfo(memberOnBoardingDTO.getParticipantId());
			} else {
				memberOnBoardingDTO.setRecordStatus(RECORD_STATUS_VALUE_DISCARD);
				memberService.discardMemberData(memberOnBoardingDTO);
			}
			jsonResponse.addProperty(SUCCESS_RESPONSE_KEY, DISCARD_MEMBER_SUCCESS);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		} catch (Exception e) {
			log.error("Discard Member Data Failed", e);
			jsonResponse.addProperty(ERROR_RESPONSE_KEY, DISCARD_MEMBER_FAIELD);
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}

	}

	@PostMapping("/downloadMemberFilesBatch")
	@PreAuthorize("hasAnyAuthority('View-Edit Member','Edit Member','View Member pending for approval')")
	public void downloadMemberFilesBatch(@RequestParam("participantId") String participantId,
			@RequestParam("documentNames") String documentNames, HttpServletResponse response) throws UnsupportedEncodingException {
		
		documentNames=URLDecoder.decode(documentNames,UTF_8);
		String baseDirectory = env.getProperty(FILE_UPLOAD_PATH);
		response.setContentType("application/octet-stream");
		String sanitizedParticipantId = participantId.replaceAll(REGEX, SEEPERATOR);
		response.setHeader("Content-Disposition",
				"attachment;filename=" + URLEncoder.encode(sanitizedParticipantId, UTF_8) + "-documents.zip");
		response.setStatus(HttpServletResponse.SC_OK);
		List<String> fileNames = memberService.getParticipantActualFileNames(participantId, documentNames);
		try (ZipOutputStream zippedOut = new ZipOutputStream(response.getOutputStream())) {
			for (String file : fileNames) {
				FileSystemResource resource = new FileSystemResource(baseDirectory + file);
				ZipEntry e = new ZipEntry(resource.getFilename());
				e.setSize(resource.contentLength());
				e.setTime(System.currentTimeMillis());
				zippedOut.putNextEntry(e);
				StreamUtils.copy(resource.getInputStream(), zippedOut);
				zippedOut.closeEntry();
			}
			zippedOut.finish();
		} catch (Exception e) {
			throw new SettleNxtSystemException(ErrorConstants.GENERIC_ERROR_CODE, ErrorConstants.GENERIC_ERROR_MESSAGE,
					e);
		}
	}

	@PostMapping("/downloadMemberFile")
	@PreAuthorize("hasAnyAuthority('View-Edit Member','Edit Member','View Member pending for approval')")
	public void downloadMemberFile(@RequestParam("participantId") String participantId,
			@RequestParam("documentName") String documentName, HttpServletResponse response)
			throws UnsupportedEncodingException {
		String baseDirectory = env.getProperty(FILE_UPLOAD_PATH);
		response.setContentType("application/octet-stream");
		String sanitizedDocumentName = documentName.replaceAll(REGEX, SEEPERATOR);
		response.setHeader("Content-Disposition",
				"attachment;filename=" + URLEncoder.encode(sanitizedDocumentName, UTF_8));
		response.setStatus(HttpServletResponse.SC_OK);
		List<String> fileNames = memberService.getParticipantActualFileNames(participantId, documentName);
		if (CollectionUtils.isEmpty(fileNames)) {
			log.error("Document is not present in the server : {} ", documentName);
			throw new SettleNxtSystemException(ErrorConstants.MEMBER_DOCUMENT_NOT_FOUND_ERROR_CODE,
					ErrorConstants.MEMBER_DOCUMENT_NOT_FOUND_ERROR_MESSAGE);
		}
		try {
			if (fileNames.get(0).lastIndexOf('.') <= fileNames.get(0).lastIndexOf('/')) {
				throw new SettleNxtException("Error in File Path", "");
			}

			Path file = Paths.get(baseDirectory, fileNames.get(0));
			if (file.startsWith(Paths.get(baseDirectory))) {
				Files.copy(file, response.getOutputStream());
			} else {
				throw new SettleNxtException("Download Exception", "");
			}

		} catch (Exception e) {

			throw new SettleNxtSystemException(ErrorConstants.GENERIC_ERROR_CODE, ErrorConstants.GENERIC_ERROR_MESSAGE,
					e);
		}
	}

	@PostMapping("/editMember")
	@PreAuthorize("hasAnyAuthority('View-Edit Member','Edit Member','View Member pending for approval')")
	public String editMember(@RequestParam("participantId") String participantId,
			@RequestParam(REQ_TYPE) String reqType, Model model) {

		getParticipantStagingData(participantId, reqType, model);
		return getView(model, VIEW_ADD_EDIT_MEMBER);

	}

	protected void getParticipantStagingData(String participantId, String reqType, Model model) {
		MemberOnBoardingDTO memberOnBoardingDTO = null;
		if (RECORD_STATUS_VALUE_PENDING.equalsIgnoreCase(reqType)) {
			memberOnBoardingDTO = memberService.getMemberForPendingForApproval(participantId);
		} else {
			memberOnBoardingDTO = memberService.getMember(participantId);
		}
		model.addAttribute("NEWMEMREJECTED", memberOnBoardingDTO.getNewMemberRejectedFlag());
		model.addAttribute(ATTRIBUTE_FILE_DOWNLOAD_REQD, FILE_DOWNLOAD_REQD_VALUE_Y);
		if (memberService.checkIfParticipantExists(memberOnBoardingDTO.getParticipantId())) {
			model.addAttribute("editFinalMember", "Yes");
		}
		model.addAttribute(REQ_TYPE, reqType);
		model.addAttribute("recStatus", memberOnBoardingDTO.getRecordStatus());
		model.addAttribute(MEMBER_ON_BOARDING_DTO_ATTRIBUTE_NAME, memberOnBoardingDTO);
		if (memberOnBoardingDTO.getCntAdd1() != null) {
			model.addAttribute("cityLists",
					masterService.getCityMasterListByStateId(memberOnBoardingDTO.getCntState()));
		}
		model.addAttribute("bankNameList", masterService.getListUniqueBankName(memberOnBoardingDTO.getIfscCode()));
		model.addAttribute(ATTRIBUTE_NAME_CITY_LIST,
				masterService.getCityMasterListByStateId(memberOnBoardingDTO.getBnkState()));
		model.addAttribute("cityListGST", masterService.getCityMasterListByStateId(memberOnBoardingDTO.getGstState()));

		addDefaultListData(model);
		List<MemberDTO> memberList = memberService.getMembersSponsoBanks();
		model.addAttribute(INTERNATIONAL_PARTICIPANT, isInternational);
		model.addAttribute(NETWORK_CODE, networkCode);
		model.addAttribute(ATTRIBUTE_NAME_SPONSOR_BANK_LIST, memberList);
	}

	protected void getParticipant(String participantId, Model model) {
		MemberOnBoardingDTO memberOnBoardingDTO = null;
		memberOnBoardingDTO = memberService.getMemberMain(participantId);

		model.addAttribute(MEMBER_ON_BOARDING_DTO_ATTRIBUTE_NAME, memberOnBoardingDTO);
		addDefaultListData(model);
	}

	@PostMapping("/getMemberMain")
	@PreAuthorize("hasAnyAuthority('View-Edit Member','Edit Member','View Member pending for approval')")
	public String viewMemberMain(@RequestParam("participantId") String participantId,
			@RequestParam(REQ_TYPE) String reqType, Model model,
			@RequestParam(value = "landingParam", required = false, defaultValue = LANDING_MAIN) String landingParam) {
		getParticipant(participantId, model);
		model.addAttribute(LANDING_PARAM, landingParam);
		model.addAttribute(INTERNATIONAL_PARTICIPANT, isInternational);
		model.addAttribute(NETWORK_CODE, networkCode);
		return getView(model, VIEW_MEMBER);

	}

	@PostMapping("/getMember")
	@PreAuthorize("hasAnyAuthority('View-Edit Member','Edit Member','View Member pending for approval')")
	public String viewMemberStg(@RequestParam("participantId") String participantId,
			@RequestParam(REQ_TYPE) String reqType, Model model,
			@RequestParam(value = "landingParam", required = false, defaultValue = LANDING_MAIN) String landingParam) {
		getParticipantStagingData(participantId, reqType, model);
		model.addAttribute(INTERNATIONAL_PARTICIPANT, isInternational);
		model.addAttribute(NETWORK_CODE, networkCode);
		model.addAttribute(LANDING_PARAM, landingParam);
		return getView(model, VIEW_MEMBER);

	}

	@PostMapping("/approveMember")
	@PreAuthorize("hasAuthority('Approve Member')")
	@ResponseBody
	public ResponseEntity<String> approveMember(@RequestBody MemberDTO memberDTO) {
		JsonObject jsonResponse = new JsonObject();
		try {
			
			memberService.approveMember(memberDTO);
			if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(memberDTO.getApprovalStatus())) {
				jsonResponse.addProperty(SUCCESS_RESPONSE_KEY, MEMBER_DETAILS_HAS_BEEN_APPROVED);
			} else {
				jsonResponse.addProperty(SUCCESS_RESPONSE_KEY, MEMBER_DETAILS_HAS_BEEN_REJECTED);
			}
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		} catch (SettleNxtException e) {
			log.error("Unable to submit the memd", e);
			jsonResponse.addProperty(ERROR_RESPONSE_KEY, e.getErrorMessage());
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		} catch (Exception e) {
			log.error("Approve member details failed", e);
			jsonResponse.addProperty(ERROR_RESPONSE_KEY, "Unable to approve the member details");
			return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
		}
	}

	@PostMapping("/netBinFileUpload")
	public String nfsFileUpload(Model model) {
		BinDetailsDTO binDetailsDto = new BinDetailsDTO();
		model.addAttribute(BIN_DETAILS_DTO, binDetailsDto);
		model.addAttribute("netBinfiletypeList", masterService.getCachedLookUpData(BaseCommonConstants.NET_BIN_FILE_TYPE));
		return getView(model, NFS_FILE_UPLOAD);
	}

	@PostMapping("/bulkNetBinFileUpload")
	public ResponseEntity<String> bulkNFSFilesUpload(Model model, @RequestParam("document") List<MultipartFile> files,
			@RequestParam("fileType") String fileType) {

		NetWorkBinUploadInterface networkBinFileProc = netWorkBinFactroy.getInterface(fileType);
		String result = networkBinFileProc.processNetworkBinFile(files);
		return new ResponseEntity<>(result, HttpStatus.OK);
	}

	// checkAlreadyPresent
	@PostMapping("/duplicateSettlementBinCheck")
	@PreAuthorize("hasAnyAuthority('Add Member','Edit Member')")
	public ResponseEntity<Object> duplicateSettlementBinCheck(@RequestParam("participantId") String participantId,
			@RequestParam("settlementBinNumber") String settlementBinNumber, Model model) {

		int result = memberService.duplicateSettlementBinCheck(participantId, settlementBinNumber);

		JsonObject jsonResponse = new JsonObject();
		if (result > 0) {
			jsonResponse.addProperty(CommonConstants.STATUS, CommonConstants.TRANSACT_FAIL);

		} else {
			jsonResponse.addProperty(CommonConstants.STATUS, CommonConstants.TRANSACT_SUCCESS);
		}
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

}
