<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/addEditMcc.js"
	type="text/javascript"></script>

<script type="text/javascript">
	var mccValidationMessages = {};
	mccValidationMessages['mccGroup'] = "<spring:message code='mcc.mccGroup.validation.msg' javaScriptEscape='true' />";
	mccValidationMessages['mccCode'] = "<spring:message code='mcc.mccCode.validation.msg' javaScriptEscape='true' />";
	mccValidationMessages['mccDesc'] = "<spring:message code='mcc.mccDesc.validation.msg' javaScriptEscape='true' />";
</script>


<div class="row">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<c:if test="${not empty addMcc}">
				<strong><span class="glyphicon glyphicon-th"></span> <span
					data-i18n="Data"><label><spring:message
								code="mcc.addscreen.title" /></label></span></strong>

			</c:if>
			<c:if test="${not empty editMcc}">
				<strong><span class="glyphicon glyphicon-th"></span> <span
					data-i18n="Data"><label><spring:message
								code="mcc.editscreen.title" /></label></span></strong>

			</c:if>
		</div>

		<div class="panel-body">
			<form:form onsubmit="return encodeForm(this);" method="POST"
				id="addMccConfig" modelAttribute="mccDTO"
				action="${submitMccConfig}" autocomplete="off">
				<br />
				<form:hidden path="mccId" id="mccId" name="mccId"
					value="${mccDTO.mccId}" />
					<form:hidden path="mccGroupName" id="mccGroupName" name="mccGroupName"
					value="${mccDTO.mccGroupName}" />
				<input id="hparentPage" type="hidden"
				value="${parentPage}" />
				<c:if test="${not empty showbutton}">
					<div class="row">
					<div class="col-sm-12">
					
					<div class="col-sm-3">
					<div class="form-group">
						<label><spring:message code="mcc.mccGroup" /><span style="color: red">*</span></label>
							<form:select path="mccGroup" id="mccGroup" name="mccGroup"
								maxlength="10" value="${mccDTO.mccGroup}"
								cssClass="form-control medantory" >
								<form:option value="" label="SELECT" />
								<form:options items="${mccGroupList}"  itemValue="code" itemLabel="description"/>
								</form:select>
								<div id="errmccGroup">
							<span for="mccGroup" class="error"><form:errors
							path="mccGroup" /> </span>
						</div>
					</div>
				</div>	


						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="mcc.mccCode" /><span
									style="color: red">*</span></label>
								<c:if test="${not empty addMcc}">
									<form:input path="mccCode" id="mccCode" name="mccCode"
										maxlength="50" cssClass="form-control medantory loginIDADD" />
								</c:if>
								<c:if test="${newMcc eq 'Yes'}">
								<c:if test="${not empty editMcc}">
									<form:input path="mccCode" id="mccCode"
										value="${mccDTO.mccCode}" name="mccCode" maxlength="50"
										cssClass="form-control medantory"/>

								</c:if>
								</c:if>
								<c:if test="${not empty editMcc and newMcc ne 'Yes'}">
									<form:input path="mccCode" id="mccCode"
										value="${mccDTO.mccCode}" name="mccCode" maxlength="50"
										cssClass="form-control medantory" readonly="true" />

								</c:if>
								<div id="errmccCode">
									<span for="mccCode" class="error"><form:errors
											path="mccCode" /></span>
								</div>
							</div>
						</div>

						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="mcc.mccDesc" /><span
									style="color: red">*</span></label>
								<c:if test="${not empty addMcc}">
									<form:input path="mccDesc" id="mccDesc" name="mccDesc"
										maxlength="50" cssClass="form-control medantory loginIDADD" />
								</c:if>
								<c:if test="${not empty editMcc}">
									<form:input path="mccDesc" id="mccDesc"
										value="${mccDTO.mccDesc}" name="mccCode" maxlength="50"
										cssClass="form-control medantory" />

								</c:if>
								<div id="errmccDesc">
									<span for="mccDesc" class="error"><form:errors
											path="mccDesc" /></span>
								</div>
							</div>
						</div>

						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="mcc.status" /><span
									style="color: red">*</span></label>
								<c:if test="${not empty addMcc}">
									<form:select path="status" id="status" name="status"
										class="form-control">
										<form:option value="A" label="ENABLE" />
										<form:option value="I" label="DISABLE" />

									</form:select>
								</c:if>
								<c:if test="${not empty editMcc}">
									<form:select path="status" id="status" name="status"
										class="form-control">
										<c:if test="${mccDTO.status =='A' }">
											<form:option value="A" label="ENABLE" />
											<form:option value="I" label="DISABLE" />
										</c:if>
										<c:if test="${mccDTO.status =='I' }">
											<form:option value="I" label="DISABLE" />
											<form:option value="A" label="ENABLE" />
										</c:if>


									</form:select>

								</c:if>
								<div id="errstatus">
									<span for="mccStatus" class="error"><form:errors
											path="status" /></span>
								</div>
							</div>
						</div>
						</div>
					</div>
				</c:if>
				<c:if test="${empty showbutton}">
					<div class="row">
						<div class="col-sm-12">
							<div class="panel panel-default no_margin">
								<div class="panel-body">
									<table class="table table-striped" style="font-size: 12px">
										<caption style="display:none;">MCC</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
											<tr>
												<td><label><spring:message code="mcc.mccGroup" /><span
														style="color: red"></span></label></td>
												<td>${mccDTO.mccGroupName}</td>
												<td><label><spring:message code="mcc.mccCode" /><span
														style="color: red"></span></label></td>
												<td>${mccDTO.mccCode}</td>
												<td><label><spring:message code="mcc.mccDesc" /><span
														style="color: red"></span></label></td>
												<td>${mccDTO.mccDesc}</td>
												<td><label><spring:message code="mcc.status" /><span
														style="color: red"></span></label></td>
												<td><c:if test="${mccDTO.status =='A' }">ENABLE</c:if>
													<c:if test="${mccDTO.status =='I' }">DISABLE</c:if></td>

											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>

				</c:if>

				<div class="col-sm-12 bottom_space">
					<hr />
					
					<div style="text-align:center">
						<c:if test="${not empty addMcc}">
						<c:if test="${not empty showbutton}">
							<button type="button" value="Submit" class="btn btn-success"
							onclick="resetAction();">
							<spring:message code="am.lbl.reset" />
							</button>
						</c:if>			    
						<c:if test="${not empty showbutton}">
							<button type="button" value="Submit" id="bSubmit"
							class="btn btn-success" onclick="addOrUpdateMcc('A');">
							<spring:message code="msg.lbl.submit" />
							</button>
						</c:if>
						</c:if>
						<sec:authorize access="hasAuthority('Edit Mcc Config')">
							<c:if test="${not empty editMcc and not empty showbutton}">
								<button type="button" value="Submit" id="bUpdate"
									class="btn btn-success" onclick="addOrUpdateMcc('E');">
									<spring:message code="msg.lbl.update" />
								</button>
								
								<%-- <c:if test="${back eq 'Yes'  and not empty showbutton}">
								<button type="button" class="btn btn-danger"
								onclick="userAction('P','/mccPendingForApproval');">
								<spring:message code="budget.backBtn" />
								</button>
							</c:if> --%>
							</c:if>
							
							<c:if test="${mccDTO.requestState  eq 'R' and not empty showbutton}">	
								<button type="button" class="btn btn-danger"
								onclick="postDiscardMccAction('/discardRejectedMcc');">
								<spring:message code="budget.discardBtn" />
								</button>
								<c:if test="${mccDTO.requestState  eq 'R'}">
								<button type="button" class="btn btn-danger"
								onclick="userAction('P','/mccPendingForApproval');">
								<spring:message code="budget.backBtn" />
								</button>
							</c:if>
							</c:if>
							
						</sec:authorize>
						
						<c:if test="${mccDTO.requestState  ne 'R'}">
						<c:if test="${parentPage  eq 'pendingApprove'}">
						<button type="button" class="btn btn-danger"
							onclick="userAction('N','/mccPendingForApproval');">
							<spring:message code="budget.backBtn" />
							</button>
						</c:if>
						<c:if test="${parentPage  ne 'pendingApprove'}">
							<button type="button" class="btn btn-danger"
							onclick="userAction('N','/showMcc');">
							<spring:message code="budget.backBtn" />
							</button>
						</c:if>
						</c:if>
					</div>
					
					
				</div>
			</form:form>
		</div>
	</div>
</div>
