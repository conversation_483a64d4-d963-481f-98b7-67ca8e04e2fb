<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript"
	src="./static/js/validation/ApproveCappingAmount.js"></script>


<form:form onsubmit="removeSpace(this); encodeForm(this);" method="POST"
	id="viewApproveCappingAmount" modelAttribute="cappingAmountDto" action="/approveCappingAmount"
	autocomplete="off">
	<div class="alert alert-danger appRejMust" role="alert">Please
		Select Approve/Reject action.</div>
		<div class="alert alert-danger remarkMust" role="alert">Please
		Enter Remarks.</div>
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="cap.viewscreen.title" /></span></strong>
		</div>
		<div class="panel-body">
		<input type="hidden" id="actionCode" value="${cappingAmountDto.actionCode}">
		<input type="hidden" id="mccGroup" value="${cappingAmountDto.mccGroup}">
		<input type="hidden" id="binCardBrandId" value="${cappingAmountDto.binCardBrandId}">
		<input type="hidden" id="binCardTypeId" value="${cappingAmountDto.binCardTypeId}">
		<input type="hidden" id="fieldName" value="${cappingAmountDto.fieldName}">
		<input type="hidden" id="relOperator" value="${cappingAmountDto.relOperator}" />
		<input type="hidden" id="fieldValue" value="${cappingAmountDto.fieldValue}" />
		
			
			<table class="table table-striped infobold" style="font-size: 12px">
			<caption style="display:none;">viewapprovecappingamount</caption>
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
						<td colspan="6"><div class="panel-heading-red clearfix">
								<strong><span class="glyphicon glyphicon-info-sign"></span> <span
									data-i18n="Data"><spring:message
											code="ifsc.requestInformation" /></span></strong>
							</div></td>
						<td></td>
					</tr>
					<tr>
						<td><label><spring:message code="am.lbl.actionCode" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.actionCode}</td>
					
						<td><label><spring:message code="am.lbl.mccGroup" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.mccGroup}</td>
						
						<td><label><spring:message code="am.lbl.binCardBrandId" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.binCardBrandId}</td>
						
						<td><label><spring:message code="am.lbl.binCardTypeId" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.binCardTypeId}</td>
						
						<td></td>
						<td></td>
						</tr>
						
				<tr>
						
						<td><label><spring:message code="am.lbl.capName" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.capName}</td>
						
						<td><label><spring:message code="am.lbl.capType" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.capType}</td>
						
						<td><label><spring:message code="am.lbl.fieldName" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.fieldName}</td>
						
						<td><label><spring:message code="am.lbl.relOperator" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.relOperator}</td>
					
						<td></td>
						<td></td>
					
						
						</tr>
						
						<tr>
						
						<td><label><spring:message code="am.lbl.fieldValue" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.fieldValue}</td>
						
						<td><label><spring:message code="am.lbl.amountFlag" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.amountFlag}</td>
						
						<td><label><spring:message code="am.lbl.flat" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.flat}</td>
						
						<td><label><spring:message code="am.lbl.percentage" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.percentage}</td>
					
						<td></td>
						<td></td>
					
						
						</tr>
						<tr>
						<td><label><spring:message code="am.lbl.amountCapMax" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.amountCapMax}</td>
						
						<td><label><spring:message code="am.lbl.amountCapMin" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.amountCapMin}</td>
						
						<td><label><spring:message code="ifsc.status" /><span
								style="color: red"></span></label></td>
						<td><c:if test="${cappingAmountDto.status=='A' }">
								<spring:message code="ifsc.activeStatus" />
							</c:if> <c:if test="${cappingAmountDto.status=='I' }">
								<spring:message code="ifsc.inactiveStatus" />
							</c:if></td>
						<td></td>
					</tr>
					

					<tr>
					
						<td><label><spring:message code="sm.lbl.requestStatus" /><span
								style="color: red"></span></label></td>
						<td><c:if test="${cappingAmountDto.requestState=='A' }">
								<spring:message code="ifsc.requestState.approved.description" />
							</c:if> <c:if test="${cappingAmountDto.requestState=='P' }">
								<spring:message
									code="ifsc.requestState.pendingApproval.description" />
							</c:if> <c:if test="${cappingAmountDto.requestState=='R' }">
								<spring:message code="ifsc.requestState.rejected.description" />
							</c:if> <c:if test="${cappingAmountDto.requestState=='D' }">
								<spring:message code="ifsc.requestState.discarded.description" />
							</c:if></td>
							
						<td><label><spring:message code="ifsc.requestBy" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.lastUpdatedBy}</td>

						<td><label><spring:message
									code="ifsc.approverComments" /><span style="color: red"></span></label></td>
						<td>${cappingAmountDto.checkerComments}</td>
						
						<td><label><spring:message code="msg.lbl.requestType" /><span
								style="color: red"></span></label> </td>
						<td>${cappingAmountDto.lastOperation}</td>
						

						
						<td></td>
					</tr>
				
			

					
			
			<c:if test="${cappingAmountDto.requestState eq 'P'}">
				 <sec:authorize access="hasAuthority('Approve Capping Amount')">
				
					
							<tr>
								<td colspan="6"><div class="panel-heading-red  clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> <span
											data-i18n="Data"><spring:message
													code="cap.approvalPanel.title" /></span></strong>
									</div></td>
							</tr>
							<tr>
								<td><label><spring:message
											code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
								<td><select name="select" id="apprej">
										<option value="N"><spring:message
												code="AM.lbl.select" /></option>
										<option value="A" id="approve"><spring:message
												code="AM.lbl.approve" /></option>
										<option value="R" id="reject"><spring:message
												code="AM.lbl.reject" /></option>
								</select></td>
								<td>
									<div style="float:center;">
										<label><spring:message code="AM.lbl.remarks" /><span
											style="color: red">*</span></label>
									</div>
								</td>
								<td colspan="5"><textarea rows="4" cols="50"
										maxlength="100" id="rejectReason"></textarea>
									<div id="errorrejectReason" class="error"></div></td>
							</tr>
							</sec:authorize> 
							
							 
							
							
							</c:if>
			
						</tbody>
					</table>
				

			<div class="row">
				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center" >
					
						<sec:authorize access="hasAuthority('Approve Capping Amount')">
							<c:if test="${cappingAmountDto.requestState eq 'P'}">
								<input name="button10" type="button" class="btn btn-success"
									id="approveRole"
									value="<spring:message
							code="ifsc.submitBtn" />"
									onclick="approve('/approveCappingAmount');" />
							</c:if>
						</sec:authorize>
						
						<button type="button" class="btn btn-danger"
							onclick="submitForm('/cappingAmountPendingForApproval');">
							<spring:message code="ifsc.backBtn" />
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</form:form>
