<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<sec:authorize access="hasAuthority('Approve Reason Code')"
	var="hasApproveAuthority"></sec:authorize>
<script type="text/javascript"> 
var actionColumnIndex=7;
var firstColumnToBeSkippedInFilterAndSort = false;
<c:if test="${showPendingReasonCode eq 'Yes'}">
	
	<c:if test="${hasApproveAuthority}">
	actionColumnIndex=8;
	var firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
</c:if>
</script>


<script src="./static/js/validation/showReasonCodeMaster.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
</script>

<script type="text/javascript" src="./static/js/buttons.html5.min.js">
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: bottom;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>

<div id="body-content">
	<div class="space_block">
		<div class="alert alert-danger" role="alert" id="transitionErrorMsg">
			<spring:message code="dispute.transition.records.msg" />
		</div>
		<div role="alert" style="display: none" id="jqueryError4">
			<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
		</div>
		<div class="alert alert-danger" role="alert" id="transitionErrorMsg1">
			<spring:message code="dispute.transition.recordsSelect.msg" />
		</div>
	</div>
</div>


<div class="modal fade" id="toggleModal" tabindex="-1" role="dialog"
	aria-labelledby="toggleModal" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Record IDs</label>
				<p id="detailsHeadersss" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulk('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulk('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>

<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showReasonCode eq 'YES' }">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" onclick="submitForm('/showReasonCodeMaster');"
			role="tab" data-toggle="tab"> <span
			class="glyphicon glyphicon-credit-card">&nbsp;</span>
		<spring:message code="reasonCode.mainTab.title" />
		</a>

		<c:choose>
			<c:when test="${pendingReasonCode eq 'YES'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#profile" role="tab"
			onclick="submitForm('/reasonCodePendingForApproval');"
			data-toggle="tab"> <span class="glyphicon glyphicon-ok">&nbsp;</span>
		<spring:message code="binexcl.approvalPanel.title" />
		</a>



	</ul>



	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
				<div class="col-sm-12">
				<sec:authorize access="hasAuthority('Add Reason Code')">
									<c:if test="${addReasonCodeMaster eq 'Yes'}">
										<a class="btn btn-success pull-right btn_align" href="#"
											onclick="submitForm('/createReasonCode');"
											style="margin: -5px 3px 1px 0px;"><em
											class="glyphicon-plus"></em> Add Reason Code</a>
									</c:if>
								</sec:authorize>

								<div class="row">
									<div class="col-sm-12">
										<button class="btn  pull-right btn_align" id="clearFilters">
											<spring:message code="ifsc.clearFiltersBtn" />
										</button>
										&nbsp; <a class="btn btn-success pull-right btn_align"
											href="#" id="csvExport"> <spring:message
												code="ifsc.csvBtn" />
										</a> <a class="btn btn-success pull-right btn_align" href="#"
											id="excelExport">Excel</a>
									</div>
								</div>
				</div>
			</div>

			<c:if test="${showReasonCode eq 'YES'}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="reasonCode.viewscreen.title" /></span></strong>
							</div>
							<div class="panel-body">
								
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">USER ROLE</caption>
										<thead>
											<tr>
												<th scope="col"><spring:message code="reasonCode.reasonCode" /></th>
												<th scope="col"><spring:message code="reasonCode.reasonCodeDesc" /></th>
												<th scope="col"><spring:message code="reasonCode.reasonCodeSubType" /></th>
												<th scope="col"><spring:message
														code="reasonCode.reasonCodeSubTypeDesc" /></th>
												<th scope="col"><spring:message code="reasonCode.reasonType" /></th>
												<th scope="col"><spring:message code="reasonCode.status" /></th>
												<th scope="col"><spring:message code="budget.actionTitle" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="reasonCode" items="${reasonCodeMasterlist}">

												<tr>
													<td
														onclick="javascript:viewMcc('${reasonCode.reasonCode}','P')">${reasonCode.reasonCode}</td>
													<td
														onclick="javascript:viewMcc('${reasonCode.reasonCode}','P')">${reasonCode.reasonCodeDesc}</td>
													<td
														onclick="javascript:viewMcc('${reasonCode.reasonCode}','P')">${reasonCode.reasonCodeSubType}</td>
													<td
														onclick="javascript:viewMcc('${reasonCode.reasonCode}','P')">${reasonCode.reasonCodeSubTypeDesc}</td>
													<td
														onclick="javascript:viewMcc('${reasonCode.reasonCode}','P')">${reasonCode.reasonType}</td>
													<td
														onclick="javascript:viewMcc('${reasonCode.reasonCode}','P')">
														<c:if test="${reasonCode.status =='A' }">
															<spring:message
																code="reasonCode.status.enable.description" />
														</c:if> <c:if test="${reasonCode.status =='I' }">
															<spring:message
																code="reasonCode.status.disable.description" />
														</c:if>
													</td>


													<td><c:if test="${showReasonCode eq 'YES'}">
															<%-- <sec:authorize access="hasAuthority('Edit Reason Code')">
													
															<a
																href="javascript:viewMcc('${reasonCode.reasonCode}','V')"
																data-i18n="Data"><spring:message code="sm.lbl.edit" /></span></strong></a>
															</sec:authorize> &nbsp;  --%>

															<a
																href="javascript:viewMcc('${reasonCode.reasonCode}','P')"
																onclick="clickAndDisable(this);"><strong><span
																	data-i18n="Data"><spring:message
																			code="sm.lbl.view" /></span></strong></a>

														</c:if> <c:if test="${showPendingReasonCodeMaster eq 'Yes'}">
															<a
																href="javascript:viewMcc('${reasonCode.reasonCode}','G')"
																onclick="clickAndDisable(this);"><strong><span
																	data-i18n="Data"><spring:message
																			code="sm.lbl.view" /></span></strong></a>
														</c:if></td>

												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>



			<c:if test="${pendingReasonCode eq 'YES'}">
				<input type="hidden" id="allPendingSize" name="headr"
					value="${tranSize}" />
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<sec:authorize access="hasAuthority('Approve Reason Code')">
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveorRejectBulk('A','no')" id="submitButton"
										value="<spring:message code="dispute.transition.label.approve" />" />
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveorRejectBulk('R','no')" id="submitButton"
										value="<spring:message code="dispute.transition.label.reject" />" />
								</sec:authorize>
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="reasonCode.approvalTab.title" /></span></strong>
							</div>
							<div class="panel-body">
								
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">USER ROLE</caption>
										<thead>
											<tr>
												<sec:authorize access="hasAuthority('Approve Reason Code')">

													<th id="selectalldisp"><input type=checkbox
														name='selectAllCheck' id="selectAll" data-toggle="modal"
														data-target="toggleModal1" value=""></input></th>
												</sec:authorize>
												<th scope="col"><spring:message code="reasonCode.reasonCode" /></th>
												<th scope="col"><spring:message code="reasonCode.reasonCodeDesc" /></th>
												<th scope="col"><spring:message code="reasonCode.reasonCodeSubType" /></th>
												<th scope="col"><spring:message
														code="reasonCode.reasonCodeSubTypeDesc" /></th>
												<th scope="col"><spring:message code="reasonCode.reasonType" /></th>
												<th scope="col"><spring:message code="reasonCode.status" /></th>
												<th scope="col"><spring:message code="budget.actionTitle" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="pendingReasonCode"
												items="${reasonCodePendingList}">
												<tr>
													<sec:authorize access="hasAuthority('Approve Reason Code')">
														<td onclick=event.stopPropagation()><c:if
																test="${pendingReasonCode.requestState  eq 'P'}">
																<input type=checkbox name='type' id="selectSingle"
																	class="selectedId"
																	value='${pendingReasonCode.reasonCode}'></input>
															</c:if> <c:if test="${pendingReasonCode.requestState  eq 'R'}">
																<input type=checkbox name='types' style="display: none"></input>
															</c:if></td>
													</sec:authorize>
													<td
														onclick="javascript:viewMcc('${pendingReasonCode.reasonCode}','G')">${pendingReasonCode.reasonCode}</td>
													<td
														onclick="javascript:viewMcc('${pendingReasonCode.reasonCode}','G')">${pendingReasonCode.reasonCodeDesc}</td>
													<td
														onclick="javascript:viewMcc('${pendingReasonCode.reasonCode}','G')">${pendingReasonCode.reasonCodeSubType}</td>
													<td
														onclick="javascript:viewMcc('${pendingReasonCode.reasonCode}','G')">${pendingReasonCode.reasonCodeSubTypeDesc}</td>
													<td
														onclick="javascript:viewMcc('${pendingReasonCode.reasonCode}','G')">${pendingReasonCode.reasonType}</td>
													<td><c:if test="${pendingReasonCode.status =='A' }">
															<spring:message code="mcc.status.enable.description" />
														</c:if> <c:if test="${pendingReasonCode.status =='I' }">
															<spring:message code="mcc.status.disable.description" />
														</c:if></td>


													<sec:authorize access="hasAuthority('Edit Reason Code')">
														<c:choose>
															<c:when test="${pendingReasonCode.requestState  eq 'R'}">

																<td><a
																	href="javascript:viewMcc('${pendingReasonCode.reasonCode}','V')"
																	onclick="clickAndDisable(this);"><strong><span
																			data-i18n="Data"><spring:message
																					code="sm.lbl.edit" /></span></strong></a></td>
															</c:when>
															<c:otherwise>

																<td><a
																	href="javascript:viewMcc('${pendingReasonCode.reasonCode}','G')"
																	onclick="clickAndDisable(this);"><strong><span
																			data-i18n="Data"><spring:message
																					code="sm.lbl.view" /></span></strong></a></td>
															</c:otherwise>
														</c:choose>
													</sec:authorize>





													<sec:authorize access="hasAuthority('Approve Reason Code')">
														<td><a
															href="javascript:viewMcc('${pendingReasonCode.reasonCode}','G')"
															onclick="clickAndDisable(this);"><strong><span
																	data-i18n="Data"><spring:message
																			code="sm.lbl.view" /></span></strong></a>
													</sec:authorize>

													</td>
												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>

		</div>

	</div>



</div>
