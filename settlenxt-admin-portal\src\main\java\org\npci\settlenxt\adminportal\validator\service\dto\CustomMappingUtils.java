package org.npci.settlenxt.adminportal.validator.service.dto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import org.npci.settlenxt.adminportal.common.mapping.CustomMappingLoader;
import org.npci.settlenxt.adminportal.common.mapping.DataElementNameMapping;
import org.npci.settlenxt.adminportal.common.mapping.FileTypeNameMapping;
import org.npci.settlenxt.adminportal.common.mapping.MessageFormatNameMapping;
import org.npci.settlenxt.adminportal.common.mapping.NameMappingContext;

import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for managing various mappings in the settlement system.
 * This class provides thread-safe access to different types of mappings
 * and their relationships.
 */
@Slf4j
public final class CustomMappingUtils {
    
    // Thread-safe maps using ConcurrentHashMap
    private static final Map<NameMappingContext, Map<String, DataElementNameMapping>> xmlNameToDataElementMap = 
        new ConcurrentHashMap<>();
    private static final Map<NameMappingContext, Map<String, MessageFormatNameMapping>> fileTypeToMessageFormatMap = 
        new ConcurrentHashMap<>();
    private static final Map<NameMappingContext, Map<String, FileTypeNameMapping>> fileTypeMap = 
        new ConcurrentHashMap<>();
    
    // Thread-safe lists
    private static final List<String> mtiList = Collections.synchronizedList(new ArrayList<>());
    private static final List<String> functionCodeList = Collections.synchronizedList(new ArrayList<>());

    private CustomMappingUtils() {
        // Private constructor to prevent instantiation
    }

    /**
     * Retrieves DataElementNameMapping for given context and XML name
     * @throws IllegalArgumentException if context or xmlName is null
     */
    public static DataElementNameMapping getDataElementNameMapping(NameMappingContext context, String xmlName) {
        validateParams(context, xmlName);
        return getXMLNameToDataElementMap(context).get(xmlName);
    }

    /**
     * Gets all DataElementMappings for a given context
     */
    public static List<DataElementNameMapping> getDataElementMappings(NameMappingContext context) {
        Objects.requireNonNull(context, "Context cannot be null");
        return CustomMappingLoader.getDataElementNameMappings(context);
    }

    /**
     * Gets FileTypeNameMapping for given context and fileTypeId
     */
    public static FileTypeNameMapping getFileTypeNameMapping(NameMappingContext context, String fileTypeId) {
        validateParams(context, fileTypeId);
        return getFileTypeMap(context).get(fileTypeId);
    }

    /**
     * Gets MessageFormatNameMapping for given context and fileType
     */
    public static MessageFormatNameMapping getMessageFormatNameMapping(NameMappingContext context, String fileType) {
        validateParams(context, fileType);
        return getFileTypeToMessageFormatMap(context).get(fileType);
    }

    /**
     * Gets the mapping between file types and message formats for a given context
     */
    public static Map<String, MessageFormatNameMapping> getFileTypeToMessageFormatMap(NameMappingContext context) {
        Objects.requireNonNull(context, "Context cannot be null");
        return fileTypeToMessageFormatMap.computeIfAbsent(context, k -> {
            Map<String, MessageFormatNameMapping> innerMap = new HashMap<>();
            for (MessageFormatNameMapping mapping : getMessageFormatMappings(k)) {
                if (mapping.getMti() != null && mapping.getFuncCode() != null) {
                    String key = mapping.getMti() + mapping.getFuncCode();
                    innerMap.put(key, mapping);
                    synchronized (functionCodeList) {
                        functionCodeList.add(mapping.getFuncCode());
                        mtiList.add(mapping.getMti());
                    }
                }
            }
            log.info("MTI list in mappings : {}", mtiList);
            return Collections.unmodifiableMap(innerMap);
        });
    }

    /**
     * Gets the file type mappings for a given context
     */
    public static Map<String, FileTypeNameMapping> getFileTypeMap(NameMappingContext context) {
        Objects.requireNonNull(context, "Context cannot be null");
        return fileTypeMap.computeIfAbsent(context, k -> {
            Map<String, FileTypeNameMapping> innerMap = new HashMap<>();
            for (FileTypeNameMapping mapping : getFileTypeMappings(k)) {
                if (mapping.getId() != null) {
                    innerMap.put(mapping.getId(), mapping);
                }
            }
            return Collections.unmodifiableMap(innerMap);
        });
    }

    /**
     * Gets XML name to data element mappings for a given context
     */
    public static Map<String, DataElementNameMapping> getXMLNameToDataElementMap(NameMappingContext context) {
        Objects.requireNonNull(context, "Context cannot be null");
        return xmlNameToDataElementMap.computeIfAbsent(context, k -> {
            Map<String, DataElementNameMapping> innerMap = new HashMap<>();
            for (DataElementNameMapping mapping : getDataElementMappings(k)) {
                if (mapping.getXmlName() != null) {
                    innerMap.put(mapping.getXmlName(), mapping);
                }
            }
            return Collections.unmodifiableMap(innerMap);
        });
    }

    /**
     * Returns an unmodifiable view of the MTI list
     */
    public static List<String> getMtiList() {
        return Collections.unmodifiableList(mtiList);
    }

    /**
     * Returns an unmodifiable view of the function code list
     */
    public static List<String> getFunctionCodeList() {
        return Collections.unmodifiableList(functionCodeList);
    }

    /**
     * Clears all cached mappings
     */
    public static void clearCaches() {
        xmlNameToDataElementMap.clear();
        fileTypeToMessageFormatMap.clear();
        fileTypeMap.clear();
        synchronized (functionCodeList) {
            mtiList.clear();
            functionCodeList.clear();
        }
        log.info("All mapping caches cleared");
    }

    private static void validateParams(NameMappingContext context, String param) {
        Objects.requireNonNull(context, "Context cannot be null");
        Objects.requireNonNull(param, "Parameter cannot be null");
    }

    private static List<MessageFormatNameMapping> getMessageFormatMappings(NameMappingContext context) {
        return CustomMappingLoader.getMessageFormatMappings(context);
    }

    private static List<FileTypeNameMapping> getFileTypeMappings(NameMappingContext context) {
        return CustomMappingLoader.getFileTypeMappings(context);
    }
}
