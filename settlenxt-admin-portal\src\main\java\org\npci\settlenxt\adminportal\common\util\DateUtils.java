package org.npci.settlenxt.adminportal.common.util;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;

/**
 * This class is used for formatting date format
 * 
 * <AUTHOR>
 *
 */
public final class DateUtils {
	private DateUtils(){
		
	}
	private static final Logger logger = LogManager.getLogger(DateUtils.class);

	public static final String DDMMYY = "ddMMyy";
	public static final String YYMMDD = "yyMMdd";
	public static final String YYYY_MM_DD = "yyyy-MM-dd";
	public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
	public static final String DD_MM_YYYY = "dd-MM-yyyy";
	public static final String YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss.SSS";
	/**
	 * This method is used to format local date
	 * 
	 * @param dateFormat
	 * @return
	 */
	public static String getTodayLocalDate(String dateFormat) {
		String date = null;
		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
			date = formatter.format(LocalDate.now());
		} catch (Exception e) {
			logger.error("Error while getting Local Date  {} {} ", e.getMessage(), e);
		}
		return date;
	}

	

	/**
	 * Translate the Given Date From One Format to Another Format
	 * 
	 * @param fromDate
	 * @param fromDateFormat
	 * @param toDateFormat
	 * @return FormattedDate
	 */
	public static String getChangeDateFormat(String fromDate, String fromDateFormat, String toDateFormat) {
		String toDate = null;
		try {
			SimpleDateFormat fromFormat = new SimpleDateFormat(fromDateFormat);
			SimpleDateFormat toFormat = new SimpleDateFormat(toDateFormat);
			Date parsedDate = fromFormat.parse(fromDate);
			toDate = toFormat.format(parsedDate);
		} catch (ParseException pe) {
			logger.error("Error while parsing Date {} {} ", pe.getMessage(), pe);
		}
		return toDate;

	}

	public static String formatyyyyMMddTHHmmssSSS(Date date) {
		SimpleDateFormat outputFormat = new SimpleDateFormat(YYYY_MM_DD_T_HH_MM_SS);
		return outputFormat.format(date);
	}

	/**
	 * Gets the Date object.
	 * 
	 * @param formattedDate
	 * @param format
	 * @return
	 * @throws ParseException
	 */
	public static Date getDateObject(String formattedDate, String format) throws SettleNxtException {
		if (StringUtils.isAllEmpty(formattedDate)) {
			return new Date();
		} else {
			return Date.from(LocalDate.parse(formattedDate, DateTimeFormatter.ofPattern(format))
					.atStartOfDay(ZoneId.systemDefault()).toInstant());

		}
	}
	/**
	 * Format the date in given format
	 * 
	 * @param date
	 * @param format
	 * @return Date
	 */
	public static String getFormattedDate(Date date, String format) {
		String formattedDate = null;
		if (date == null) {
			date = new Date();
		}
		SimpleDateFormat dateFormat = null;
		try {
			dateFormat = new SimpleDateFormat(format);
			formattedDate = dateFormat.format(date);
		} catch (Exception e) {
			logger.error("Error while getting Local Date {} {} ", e.getMessage(), e);
		}
		return formattedDate;
	}
	public static Date convertJulianDate(String julianDate, String julianFormat) {

		SimpleDateFormat sdfJulian = new SimpleDateFormat(julianFormat);
		
		Date dt = null;
		try {
			dt = sdfJulian.parse(julianDate);

		} catch (ParseException e) {
			logger.error("Error while parsing julian Date {} {} ", e.getMessage(), e);
		}
		return dt;
	}

	public static String getDatewithTimeStamp(String format) {
		String timeDate = null;
		try {
			Date date = new Date();
			Timestamp ts = new Timestamp(date.getTime());
			SimpleDateFormat formatter = new SimpleDateFormat(format);
			timeDate = formatter.format(ts);
		} catch (Exception e) {
			logger.error("Error while getting Local Date {} {} ", e.getMessage(), e);
		}
		return timeDate;
	}
}
