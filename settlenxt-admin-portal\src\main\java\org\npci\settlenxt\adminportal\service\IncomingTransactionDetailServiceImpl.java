package org.npci.settlenxt.adminportal.service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.repository.IncomingTransactionDetailRepo;
import org.npci.settlenxt.portal.common.dto.DisputeTxnModel;
import org.npci.settlenxt.portal.common.dto.IncomingTransactionDetailDTO;
import org.npci.settlenxt.portal.common.service.BaseIncomingTransactionDetailServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class IncomingTransactionDetailServiceImpl extends BaseIncomingTransactionDetailServiceImpl
		implements IncomingTransactionDetailService {
	@Autowired
	IncomingTransactionDetailRepo incomingTransactionDetail;

	private static final Logger logger = LogManager.getLogger(IncomingTransactionDetailServiceImpl.class);

	private static final String FAILED = "FAILED";

	@Override
	public List<DisputeTxnModel> getDisputesDetails(IncomingTransactionDetailDTO incomingTransactionDetailDTO) {

		boolean isRrn = false;
		boolean isSchemeCode = false;
		boolean isFuncCode = false;
		boolean isPan = false;
		incomingTransactionDetailDTO.setDateSearch(LocalDate
				.parse(incomingTransactionDetailDTO.getOutgoingCreatedDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"))
				.format(DateTimeFormatter.ofPattern("yyMMdd")));
		if (!"0".equals(incomingTransactionDetailDTO.getSchemeCode())) {
			isSchemeCode = true;
		}
		if (!"0".equals(incomingTransactionDetailDTO.getFuncCodeDescription())) {
			isFuncCode = true;
			incomingTransactionDetailDTO
					.setFuncCodeValue(incomingTransactionDetailDTO.getFuncCodeDescription().split("-")[0]);
		}
		if (!StringUtils.isAllBlank(incomingTransactionDetailDTO.getPan())) {
			isPan = true;

		}
		if (!StringUtils.isAllBlank((incomingTransactionDetailDTO.getRrn()))) {
			isRrn = true;
		}
		return incomingTransactionDetail.getDisputesDetailsAdmin(incomingTransactionDetailDTO, isSchemeCode, isFuncCode,
				isPan, isRrn);
	}

	@Override
	public List<IncomingTransactionDetailDTO> parseJsonToTransactionList(List<IncomingTransactionDetailDTO> disputeList,
			String jsonString) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			List<IncomingTransactionDetailDTO> transactions = objectMapper.readValue(jsonString,
					new TypeReference<List<IncomingTransactionDetailDTO>>() {
					});

			for (IncomingTransactionDetailDTO transaction : transactions) {
				String status = getStatus(transaction.getStatus());
				transaction.setStatus(status);
				String rejectReason = getRejectReason(transaction.getRejectReason(), status);
				transaction.setRejectReason(rejectReason);
				String input1 = getConditionalField(transaction.getAmount(), status);
				transaction.setAmount(input1);

				incomingTransactionDetail.approveRejectMaker(transaction);
				disputeList.add(transaction);
			}
		} catch (Exception e) {
			logger.error("Unable to parse Json {} {}", e, e.getMessage());
		}
		return disputeList;
	}

	@Override
	public List<IncomingTransactionDetailDTO> parseJsonToTransactionListChecker(
			List<IncomingTransactionDetailDTO> disputeList, String jsonString) {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			List<IncomingTransactionDetailDTO> transactions = objectMapper.readValue(jsonString,
					new TypeReference<List<IncomingTransactionDetailDTO>>() {
					});

			for (IncomingTransactionDetailDTO transaction : transactions) {
				String status = getStatusForChecker(transaction.getStatus());
				transaction.setStatus(status);
				String rejectReason = getRejectReason(transaction.getRejectReason(), status);
				transaction.setRejectReason(rejectReason);

				transaction.setPage("C");
				incomingTransactionDetail.approveRejectChecker(transaction);
				disputeList.add(transaction);
			}
		} catch (Exception e) {
			logger.error("Unable to parse Json {} {}", e, e.getMessage());
		}
		return disputeList;
	}

	private String getStatus(String status) {
		return "R".equals(status) ? FAILED : "RPA";
	}

	private String getStatusForChecker(String status) {
		return "R".equals(status) ? FAILED : "IP";
	}

	private String getConditionalField(String field, String status) {
		return !FAILED.equals(status) ? field : "";
	}

	private String getRejectReason(String rejectReason, String status) {
		return FAILED.equals(status) ? rejectReason : "";
	}

}
