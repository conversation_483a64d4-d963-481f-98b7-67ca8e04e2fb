package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
public class DefaultNetwokBinImpl implements NetWorkBinUploadInterface{
	
	@Override
	public String processNetworkBinFile(List<MultipartFile> files) {
		return BaseCommonConstants.FAIL_STATUS;
	}

}
