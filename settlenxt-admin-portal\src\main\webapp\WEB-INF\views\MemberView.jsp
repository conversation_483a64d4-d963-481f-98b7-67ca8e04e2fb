<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<script type="text/javascript"
	src="./static/js/bootstrap-multiselect.js"></script>
<link rel="stylesheet" href="./static/css/bootstrap-multiselect.css"
	type="text/css" />
<head>
<title></title>
<script type="text/javascript"
	src="./static/js/validation/commonValidation.js"></script>
<jsp:include page="addEditMemberValMsg.jsp" />

<script type="text/javascript" src="./static/js/validation/MemberAdd.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberSettlementBin.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberAcquirerBin.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberDocument.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberIssuerBin.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberValidation.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberDateFunctions.js"></script>
<script type="text/javascript"
	src="./static/js/validation/MemberGateway.js"></script>
	
<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />
<style>
.overlay {
	display: none;
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 999;
	background: rgba(255, 255, 255, 0.8) url("./static/images/spinner.gif")
		center no-repeat;
}

.loadingdata {
	overflow: hidden;
}

.loadingdata .overlay {
	display: block;
}

input.largerChcekbox {
	width: 20px;
	height: 20px
}

button.multiselect {
	margin-top: -5px;
}

.form-control {
	background-color: white !important;
}
</style>
</head>

<script>
 var bankIFSCMapping={};
 var currencyCodeMapping={};
 var productTypeMapping={};
 var domainUsageMapping={};
 var binCardTypeMapping={};
 var binCardProductTypeMapping={};
 var binCardVariantMapping={};
 var binCardBrandMapping={};
 var binTypeMapping={};
 var submemberBankType='<spring:eval expression="@environment.getProperty('submember.bank.type')" />';
 var sponsorBankType='<spring:eval expression="@environment.getProperty('sponsor.bank.type')" />' ;
 var unallocatedIssBins=[];
 <c:forEach var="codeValue" items="${currencyCodeList}" >
 currencyCodeMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${productTypeList}" >
 productTypeMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${domainUsageList}" >
 domainUsageMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${binCardTypeList}" >
 binCardTypeMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${binProdTypeList}" >
 binCardProductTypeMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${binCardVariantList}" >
 binCardVariantMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${binCardBrandList}" >
 binCardBrandMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="codeValue" items="${binTypeList}" >
 binTypeMapping["${codeValue.code}"]="${codeValue.description}"; </c:forEach>
 <c:forEach var="sponsorBank" items="${sponsorBankList}" >
 bankIFSCMapping["${sponsorBank.participantId}"]="${sponsorBank.ifscCode}"; </c:forEach>
 var settlementBinData=[]; 
 <c:if test="${not empty memberOnBoardingDTO.settlementBinList}">
  <c:forEach var="settlementBin" items="${memberOnBoardingDTO.settlementBinList}" varStatus="status">
  	settlementBinData.push({settlementBinId:"${settlementBin.settlementBinNumber}".replace("${memberOnBoardingDTO.ifscCode}",""), status: "${settlementBin.status}", settlementBinNumber:"${settlementBin.settlementBinNumber}",isDefault :"${settlementBin.isDefault}",settlementCurrency:"${settlementBin.settlementCurrency}",isNew:false}); </c:forEach>
 </c:if>
 var acquirerBinData=[];
 <c:if test="${not empty memberOnBoardingDTO.acqBinList}">
 <c:forEach var="acqBin" items="${memberOnBoardingDTO.acqBinList}" varStatus="status">
  acquirerBinData.push({acquirerId:"${acqBin.acquirerId}", status: "${acqBin.status}", acqDomainUsage:"${acqBin.acqDomainUsage}",acqBankGroup :"${acqBin.acqBankGroup}",acqFrmDateStr:"${acqBin.acqFrmDateStr}",acqToDateStr:"${acqBin.acqToDateStr}",acqProductType:"${acqBin.acqProductType}",offlineAllowed:"${acqBin.offlineAllowed}",acqSettlementBin:"${acqBin.acqSettlementBin}",isNew:false,domainUsage:"${acqBin.binDetailsCodeDescription.domainUsage}",productTypeName:"${acqBin.binDetailsCodeDescription.productType}",bankGroupName:"${acqBin.binDetailsCodeDescription.bankGroup}"}); </c:forEach>
</c:if>
var issuerBinData=[];
<c:if test="${not empty memberOnBoardingDTO.issBinList}">
<c:forEach var="issBin" items="${memberOnBoardingDTO.issBinList}" varStatus="status">
issuerBinData.push({networkSelection:"${issBin.networkSelection}",networkLicenseId:"${issBin.networkLicenseId}",networkIssId:"${issBin.networkIssId}",markUp:"${issBin.markUp}",issBinType:"${issBin.issBinType}",issBankGroup:"${issBin.issBankGroup}",binNumber:"${issBin.binNumber}",binLength:"${issBin.binLength}",additionalParams:"${issBin.additionalParams}",lowBin:"${issBin.lowBin}",highBin:"${issBin.highBin}",issFrmDateStr:"${issBin.issFrmDateStr}",issToDateStr:"${issBin.issToDateStr}",panLength:"${issBin.panLength}",binCardType:"${issBin.binCardType}",binProductType:"${issBin.binProductType}",binCardVariant:"${issBin.binCardVariant}",binCardBrand:"${issBin.binCardBrand}",issDomainUsage:"${issBin.issDomainUsage}",messageType:"${issBin.messageType}",cardTechnology:"${issBin.cardTechnology}",authMechanism:"${issBin.authMechanism}",subScheme:"${issBin.subScheme}",cardSubVariant:"${issBin.cardSubVariant}",programDetails:"${issBin.programDetails}",formFactor:"${issBin.formFactor}",issProductType:"${issBin.issProductType}",issSettlementBin:"${issBin.issSettlementBin}",offlineAllowed:"${issBin.offlineAllowed}",status:"${issBin.status}",isNew:false,binCardTypeName:"${issBin.binDetailsCodeDescription.binCardType}",binProductTypeName:"${issBin.binDetailsCodeDescription.binProductType}",binCardBrandName:"${issBin.binDetailsCodeDescription.binCardBrand}",binCardVariantName:"${issBin.binDetailsCodeDescription.binCardVariant}",issBinTypeName:"${issBin.binDetailsCodeDescription.issBinType}",domainUsage:"${issBin.domainUsage}",productTypeName:"${issBin.productTypeName}",subSchemeName:"${issBin.subSchemeName}",featureIssBin:"${issBin.featureIssBin}",bankGroupName:"${issBin.binDetailsCodeDescription.bankGroup}",authMechanismName:"${issBin.binDetailsCodeDescription.authMechanism}",cardTechnologyName:"${issBin.binDetailsCodeDescription.cardTechnology}",messageTypeName:"${issBin.binDetailsCodeDescription.messageType}",domainUsageName:"${issBin.binDetailsCodeDescription.domainUsage}",cardSubVariantName:"${issBin.binDetailsCodeDescription.cardSubVariant}",programDetailsName:"${issBin.binDetailsCodeDescription.programDetails}",formFactorName:"${issBin.binDetailsCodeDescription.formFactor}"}); </c:forEach>
</c:if>
var documentData=[];
<c:if test="${not empty memberOnBoardingDTO.documents}">
<c:forEach var="document" items="${memberOnBoardingDTO.documents}" varStatus="status">
documentData.push({documentId:"${document.documentId}", documentName:"${document.documentName}",documentPath:"${document.documentPath}", isNew:false, status:"${document.status}"});</c:forEach>
</c:if>
</script>




<div id="memberBody" class="container-fluid height-min">

<input type="hidden" id="networkUsed"
			value="${InternationalParticipant}" />
				<input type="hidden" id="codeForNetwork"
			value="${networkCode}" />
	<div class="overlay"></div>
	<div class="row">
		<div role="alert" style="display: none" id="jqueryError2">
			<div id="errorStatus2" class="alert alert-danger" role="alert">${errorStatus}</div>
		</div>
		<div role="alert" style="display: none" id="jquerySuccess">
			<div id="successStatus" class="alert alert-success" role="alert">${successStatus}</div>
		</div>
		<div id="errLvType" class="alert alert-danger" role="alert"
			style="display: none"></div>
	</div>
	<div>
		<input type="hidden" id="binCardTypesForJS"
			value="${binCardTypesForJS}" /> <input type="hidden" id="recStatus"
			value="${recStatus}" /> <input type="hidden" id="submitdisableCheck" />
		<input type="hidden" id="featureList" /> <input type="hidden"
			id="newMemberRejectedFlag" value="${NEWMEMREJECTED}" />

	</div>
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="am.lbl.memberReg" /></span></strong>
		</div>


		<div class="panel-body">
			<form:form onsubmit="removeSpace(this); encodeForm(this);"
				method="POST" id="addEditMember"
				modelAttribute="memberOnBoardingDTO" enctype="multipart/form-data"
				action="addMember" autocomplete="off">
				<br />
				<form:hidden path="reqType" value="${reqType}" />
				<form:hidden path="requestState" />
				<input type="hidden" id="ApproveMemberFlag"
					value="${memberOnBoardingDTO.isActive}" />

				<form:hidden path="memberId" value="${memberDTO.memberId}" />
				<form:hidden path="participantId" value="${memberDTO.participantId}" />
				<form:hidden path="bankMasterCode"
					value="${memberDTO.bankMasterCode}" />
				<form:hidden path="funcID" value="${memberDTO.funcID}" />
				<input type="hidden" id="hiddenUnregDealer"
					value="${memberDTO.unregDealer}" />

				<div class="bs-example">
					<div class="panel-group" id="accordion">
						<div class="panel panel-default">
							<div class="panel-heading">
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#accordion"
										href="#collapseOne" id="collapseOneLink"><spring:message
											code="member.bnkrgstrtn" /> <span
										class="glyphicon glyphicon-plus areaShowHde  "></span> </a>
								</h4>
							</div>
							<div id="collapseOne"
								class="panel-collapse collapse in panelHideShow">
								<div class="row">
									<div class="panel-body">
										<table class="table" style="font-size: 12px">
										<caption style="display:none;">Member View</caption> 
											<tbody>
											<th scope="col"></th>
												<tr>

													<td><label><spring:message
																code="member.bankType" /></label></td>
													<td>${memberOnBoardingDTO.memberTypeName}</td>
													<!--  list -->
													<c:if test="${memberOnBoardingDTO.memberTypeName ne 'Sponsor Bank'}">
														<td><label><spring:message
															code="member.spBank" /></label></td>
														<td>${memberOnBoardingDTO.parentParticipantName}</td>
														<td><label><spring:message
																	code="member.bankName" /></label></td>
														<td>${memberOnBoardingDTO.memberName }</td>
														<td><label><spring:message code="member.IFSC" /></label></td>
														<td>${memberOnBoardingDTO.ifscCode }</td>
														<td></td>
														<td></td>
													</c:if>
													<c:if test="${memberOnBoardingDTO.memberTypeName eq 'Sponsor Bank'}">
														<td><label><spring:message
																	code="member.bankName" /></label></td>
														<td>${memberOnBoardingDTO.memberName }</td>
														<td><label><spring:message code="member.IFSC" /></label></td>
														<td>${memberOnBoardingDTO.ifscCode }</td>
														<td></td>
														<td></td>
														<td></td>
														<td></td>
													</c:if>
													
													<!--  list -->

												</tr>


												<tr>
												<c:if test = "${InternationalParticipant ne 'Y'}">
													<td><label><spring:message
																code="member.bankSector" /></label></td>
													<td>${memberOnBoardingDTO.bankSectorName}</td>
												</c:if>
													<!-- list -->
													<td><label><spring:message
																code="member.bnkmastrcode" /></label></td>
													<td>${memberOnBoardingDTO.bankMasterCode }</td>
													<td><label><spring:message
																code="member.RTGScode" /></label></td>
													<td>${memberOnBoardingDTO.rtgsCode}</td>
													<td><label><spring:message
																code="member.participantid" /></label></td>
													<td>${memberOnBoardingDTO.participantId }</td>
													<td></td>
													<td></td>
												</tr>


												<tr>
													<td><label><spring:message
																code="member.savingsAccNo" /></label></td>
													<td>${memberOnBoardingDTO.savingsAccNumber}</td>
													<td><label><spring:message
																code="member.currentAccNo" /></label></td>
													<td>${memberOnBoardingDTO.currentAccNumber }</td>
												<c:if test = "${InternationalParticipant ne 'Y'}">
													<td><label><spring:message
																code="member.uniqBankName" /></label></td>
													<td>${memberOnBoardingDTO.uniqueBnk}</td>
												</c:if>
													<!-- uniqueBnk -->
													<td><label><spring:message
																code="member.nfsParticipantId" /></label></td>
													<td>${memberOnBoardingDTO.participantIdNFS }</td>
													<td></td>
													<td></td>
												</tr>
												<tr>
													<td><label><spring:message
																code="member.subNet" /></label></td>
													<td>${memberOnBoardingDTO.subNet}</td>
													<td><label><spring:message
																code="member.maxUser" /></label></td>
													<td>${memberOnBoardingDTO.maxUser}</td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
												</tr>

												<tr>
													<td colspan="6"><div
															class="panel-heading-red  clearfix">
															<strong><span class="glyphicon glyphicon-info-sign"></span>
																<span data-i18n="Data"><spring:message
																		code="member.contactInfo" /></span></strong>
														</div></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
												</tr>



												<tr>
													<td><label><spring:message
																code="member.phoneNo1" /></label></td>
													<td>${memberOnBoardingDTO.bnkPhone}</td>
													<td><label><spring:message
																code="member.phoneNo2" /></label></td>
													<td>${memberOnBoardingDTO.bnkPhone2 }</td>
													<td><label><spring:message
																code="member.mobileNo1" /></label></td>
													<td>${memberOnBoardingDTO.bnkMobile}</td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>

												</tr>


												<tr>
													<td><label><spring:message
																code="member.mobileNo2" /></label></td>
													<td>${memberOnBoardingDTO.bnkMobile2 }</td>
													<td><label><spring:message
																code="member.emailadrs1" /></label></td>
													<td>${memberOnBoardingDTO.bnkEmail}</td>
													<td><label><spring:message
																code="member.emailadrs2" /></label></td>
													<td>${memberOnBoardingDTO.bnkEmail2 }</td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>

												</tr>

												<tr>
													<td><label><spring:message
																code="member.legaladrs" /></label></td>
													<td>${memberOnBoardingDTO.bnkAdd}</td>
													<td><label><spring:message
																code="member.country" /></label></td>
													<td>${memberOnBoardingDTO.bnkCountryName}</td>
													<!--  list -->
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>

												</tr>


												<tr>
													<td><label><spring:message code="member.state" /></label></td>
													<td>${memberOnBoardingDTO.bnkStateName}</td>
													<!--  list -->
													<td><label><spring:message code="member.city" /></label>
													</td>
													<td>${memberOnBoardingDTO.bnkCityName}</td>
													<!--  list -->
													<td><label><spring:message
																code="member.zipcode" /></label></td>
													<td>${memberOnBoardingDTO.bnkPincode}</td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>

												</tr>


	<c:if test="${memberOnBoardingDTO.memberTypeName ne 'Network'}">
												<tr>
													<td colspan="6"><div
															class="panel-heading-red  clearfix">
															<strong><span class="glyphicon glyphicon-info-sign"></span>
																<span data-i18n="Data"><spring:message
																		code="member.gstdetails" /></span></strong>
														</div></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>


												</tr>

												<tr>
													<td><label><spring:message code="member.gstin" /></label></td>
													<td>${memberOnBoardingDTO.gstIn}</td>
													<td><label><spring:message
																code="member.gstaddrs" /></label></td>
													<td>${memberOnBoardingDTO.gstAdd}</td>
													<td><label><spring:message
																code="member.gstcountry" /></label></td>
													<td>${memberOnBoardingDTO.gstCountryName}</td>
													<!--  list -->
													<td></td>
													<td></td>
													<td></td>
													<td></td>

												</tr>


												<tr>
													<td><label><spring:message
																code="member.gststate" /></label></td>
													<td>${memberOnBoardingDTO.gstStateName}</td>
													<!--  list -->
													<td><label><spring:message
																code="member.gstcity" /></label></td>
													<td>${memberOnBoardingDTO.gstCityName}</td>
													<!--  list -->
													<td><label><spring:message
																code="member.gstzipcode" /></label></td>
													<td>${memberOnBoardingDTO.gstPincode}</td>
													<td><label><spring:message
																code="member.website" /></label></td>
													<td>${memberOnBoardingDTO.webSite}</td>
													<td></td>
													<td></td>

												</tr>
												</c:if>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
						
						
						

						
						
						
						
						<div id="afterSave">
						<c:if test = "${InternationalParticipant eq 'Y'}">
						<c:if test="${memberOnBoardingDTO.memberTypeName eq 'Network'}">
						<div class="panel panel-default">	
							<div class="panel-heading">
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#accordion"
										href="#collapseSeven" id="collapseSevenLink"><spring:message
											code="member.settlementInfo" /> <span
										class="glyphicon glyphicon-plus areaShowHde  "></span> </a>
								</h4>
							</div>
							<div id="collapseSeven"
								class="panel-collapse collapse panelHideShow">
								<div class="row">
									<div class="panel-body">
										<table class="table" style="font-size: 12px">
										<caption style="display:none;">Member View</caption> 
											<tbody>
											<th scope="col"></th>

												<tr>
													<td><label><spring:message
																code="member.currencyConversion" /></label></td>
													<td>${memberOnBoardingDTO.currencyConversionBy}</td>
													<!--  list -->
													<td><label><spring:message
																code="member.isNPCISettlement" /></label></td>
													<td>${memberOnBoardingDTO.isType}</td>
													<!--  list -->
													<td><label><spring:message
																code="member.currencyConversionType" /></label></td>
													<td>${memberOnBoardingDTO.currencyConversionType}</td>
													<td><label><spring:message
																code="member.forexId" /></label></td>
													<td>${memberOnBoardingDTO.forexId}</td>
													<td></td>
													<td></td>

												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
							</div>
						</c:if>
						</c:if>
						
							<div class="panel panel-default">
								<div class="panel-heading">
									<h4 class="panel-title">
										<a data-toggle="collapse" data-parent="#accordion"
											href="#collapseThree" id="collapseThreeLink"><spring:message
												code="member.contactinfo" /> <span
											class="glyphicon glyphicon-plus areaShowHde"></span> </a>
									</h4>
								</div>
								<div id="collapseThree"
									class="panel-collapse collapse panelHideShow">
									<div class="panel-body">
										<table class="table" style="font-size: 12px">
										<caption style="display:none;">Member View</caption> 
											<tbody>
											<th scope="col"></th>
												<tr>

													<td><label><spring:message
																code="member.addrstype" /></label></td>
													<td>${memberOnBoardingDTO.addressTypeName}</td>
													<!--  list -->
													<td><label><spring:message code="member.name" /></label></td>
													<td>${memberOnBoardingDTO.cntChkrName}</td>
													<td><label><spring:message code="member.phno" /></label></td>
													<td>${memberOnBoardingDTO.cntPhone }</td>
													<td><label><spring:message
																code="member.moblno" /></label></td>
													<td>${memberOnBoardingDTO.cntMobile }</td>
													<td></td>
													<td></td>
													<td></td>
												</tr>


												<tr>
													<td><label><spring:message code="member.faxno" /></label></td>
													<td>${memberOnBoardingDTO.cntFax}</td>
													<td><label><spring:message
																code="member.legaladdrs" /></label></td>
													<td>${memberOnBoardingDTO.cntAdd1 }</td>
													<td><label><spring:message
																code="member.country" /></label></td>
													<td>${memberOnBoardingDTO.cntCountryName}</td>
													<!-- list -->
													<td><label><spring:message code="member.state" /></label>
													</td>
													<td>${memberOnBoardingDTO.cntStateName }</td>
													<!-- list -->
													<td></td>
													<td></td>
													<td></td>
												</tr>


												<tr>
													<td><label><spring:message code="member.city" /></label></td>
													<td>${memberOnBoardingDTO.cntCityName}</td>
													<!-- list -->
													<td><label><spring:message
																code="member.zipcode" /></label></td>
													<td>${memberOnBoardingDTO.cntPincode }</td>
													<td><label><spring:message
																code="member.emailad" /></label></td>
													<td>${memberOnBoardingDTO.cntEmail}</td>
													<td><label><spring:message
																code="member.authOffDesgn" /></label></td>
													<td>${memberOnBoardingDTO.cntDesignation}</td>
													<td></td>
													<td></td>
													<td></td>
												</tr>



											</tbody>
										</table>
									</div>
								</div>
							</div>
							<div class="panel panel-default">
								<div class="panel-heading">
									<h4 class="panel-title">
										<a data-toggle="collapse" data-parent="#accordion"
											href="#collapseTwo" id="collapseTwoLink"><spring:message
												code="member.bindetails" /><span
											class="glyphicon glyphicon-plus areaShowHde "></span></a>
									</h4>
								</div>
								<div id="collapseTwo"
									class="panel-collapse collapse panelHideShow">
									<div class="panel-body">
										<!-- Settlement Bin start -->
										<div class="row">
											<div class="col-md-12">
												<div class="card">
													<div class="card-header">
														<div class="card-title">
															<spring:message code="member.settlmntbindetails" />
														</div>
													</div>
													<div class="card-body">
														<div id="SettlementBinDetailsDiv">
															<table id="dataTable" border="0">
															
										<caption style="display:none;">Member View</caption> 
															<th scope="col"></th>
																<div class="panel-body">
																	<table class="table table-striped">
																	
										<caption style="display:none;">Member View</caption> 
																		<thead>
																			<tr>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.participantId" /> </label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.settlmntBin" /> </label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.settlmntcurrency" /> </label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.clearingAgencyType" /> </label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.isdefaultbin" /> </label></th>
																				<th scope="col"><label for="squareInput"><spring:message
																							code="member.status" /></label></th>

																			</tr>
																		</thead>
																		<tbody id="settlementBinsList">
																			<c:if
																				test="${not empty memberOnBoardingDTO.settlementBinList}">
																				<c:forEach var="settlementBin"
																					items="${memberOnBoardingDTO.settlementBinList}"
																					varStatus="status">

																					<tr>
																						<td>${settlementBin.participantId}</td>
																						<td>${settlementBin.settlementBinNumber}</td>
																						<td>${settlementBin.settlementCurrencyDescription}</td>
																						<td>${settlementBin.clearingAgencyType}</td>
																						<td>${settlementBin.isDefault}</td>
																						<td><c:choose>
																								<c:when test="${settlementBin.status eq 'B'}">
																									<spring:message code="member.blckd" />
																								</c:when>
																								<c:otherwise>Active</c:otherwise>
																							</c:choose></td>
																					</tr>
																				</c:forEach>
																			</c:if>
																		</tbody>
																	</table>
																</div>
															</table>
														</div>
													</div>
													<!-- Settlement Bin end -->
													<div class="row">
														<div class="col-md-12">
															<div class="card">
																<div class="card-header">
																	<div class="card-title">
																		<spring:message code="member.acqbindetails" />
																	</div>
																</div>
																<div class="card-body">
																	<div id="AcquirerBinDetailsDiv">
																		<table class="table table-striped"
																			style="font-size: 12px" id="newAcquirerBinTable">
																			
										<caption style="display:none;">Member View</caption> 
																			<tbody>
																			<th scope="col"></th>
																				<tr>
																					<td><label><spring:message
																								code="member.bankgroup" /></label></td>
																					<td id="acqBankGroupTD"></td>
																					<td><label><spring:message
																								code="member.acqid" /></label></td>
																					<td id="acquirerIdTD"></td>
																					<td><label><spring:message
																								code="member.domainusage" /></label></td>
																					<td id="acqDomainUsageTD"></td>
																					<td><label><spring:message
																								code="member.activtndate" /></label></td>
																					<td id="acqFrmDateTD"></td>
																				</tr>
																				<tr>
																					<td><label><spring:message
																								code="member.deactdate" /></label></td>
																					<td id="acqToDateTD"></td>
																					<td><label><spring:message
																								code="member.pdttype" /></label></td>
																					<td id="acqProductTypeTD"></td>
																					<td><label><spring:message
																								code="member.settmntBIN" /></label></td>
																					<td id="acqSettlementBinTD"></td>
																					<td><label><spring:message
																								code="member.isOfflAllowd" /></label></td>
																					<td id="isOfflineAllowedTD"></td>
																				</tr>
																			</tbody>
																		</table>
																		<table id="dataTable" border="0">
																		<caption style="display:none;">Member View</caption> 
																		<th scope="col"></th>
																			<div class="panel-body">
																				<table class="table table-striped">
																					<caption style="display:none;">Member View</caption> 
																					<thead>
																					
																						<tr>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.acqid" /></label></th>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.domainusage" /></label></th>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.settlmntBin" /></label></th>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.pdttype" /></label></th>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.status" /></label></th>
																							<th scope="col"><label for="squareInput"><spring:message
																										code="member.action" /></label></th>
																						</tr>
																					</thead>
																					<tbody id="acquirerBinsList">
																						<c:if
																							test="${not empty memberOnBoardingDTO.acqBinList}">
																							<c:forEach var="acqBin"
																								items="${memberOnBoardingDTO.acqBinList}"
																								varStatus="status">

																								<tr>
																									<td>${acqBin.acquirerId}</td>
																									<td>${acqBin.binDetailsCodeDescription.domainUsage}</td>
																									<td>${acqBin.acqSettlementBin}</td>
																									<td>${acqBin.binDetailsCodeDescription.productType}</td>
																									<td><c:choose>
																											<c:when test="${acqBin.status eq 'B'}">
																												<spring:message code="member.blckd" />
																											</c:when>
																											<c:when
																												test="${acqBin.status eq 'D'}">
																												<spring:message code="member.deleted" />
																											</c:when>
																											<c:otherwise>
																												<spring:message code="member.active" />
																											</c:otherwise>
																										</c:choose></td>
																									<td style="text-align:center"><a
																										href="javascript:viewAcquirerBin('${acqBin.acquirerId}')"><span
																											class="glyphicon  glyphicon-eye-open my-tooltip"
																											title="VIEW"></span></a></td>
																								</tr>
																							</c:forEach>
																						</c:if>
																					</tbody>
																				</table>
																			</div>
																		</table>
																	</div>
																</div>
																<div class="row" id="issrow">
																	<div class="col-md-13">
																		<div class="card">
																			<div class="card-header">
																				<div class="card-title">
																					<spring:message code="member.issbindetails" />
																				</div>
																			</div>
																			<div class="card-body">
																				<div id="IssuerBinDetailsDiv">
																					<table class="table" style="font-size: 12px"
																						id="IssuerBinDetailsDivTable">
																				<caption style="display:none;">Member View</caption> 
																						<tbody>
																						<th scope="col"></th>
																							<tr>
																								<td><label><spring:message
																											code="member.bintype" /></label></td>
																								<td id="issBinTypeTD"></td>
																								<td><label><spring:message
																											code="member.bankgroup" /></label></td>
																								<td id="issBankGroupTD"></td>
																								<td><label><spring:message
																											code="member.binno" /></label></td>
																								<td id="binNumberTD"></td>
																							</tr>
																							<tr>
																								<td><label><spring:message
																											code="member.lowbin" /></label></td>
																								<td id="lowBinTD"></td>
																								<td><label><spring:message
																											code="member.highbin" /></label></td>
																								<td id="highBinTD"></td>
																								<td><label><spring:message
																											code="member.activtndate" /></label></td>
																								<td id="issFrmDateTD"></td>
																							</tr>
																							<tr>
																								<td><label><spring:message
																											code="member.deactdate" /></label></td>
																								<td id="issToDateTD"></td>
																								<td><label><spring:message
																											code="member.panlength" /></label></td>
																								<td id="panLengthTD"></td>
																								<td><label><spring:message
																											code="member.bincardtype" /></label></td>
																								<td id="binCardTypeTD"></td>
																							</tr>
																							<tr>
																								<td><label><spring:message
																											code="member.binpdttype" /></label></td>
																								<td id="binProductTypeTD"></td>
																							<c:if test = "${InternationalParticipant ne 'Y'}">
																								<td><label><spring:message
																											code="member.bincardvarnt" /></label></td>
																								<td id="binCardVariantTD"></td>
																							</c:if>
																								<td><label><spring:message
																											code="member.bincardbrand" /></label></td>
																								<td id="binCardBrandTD"></td>
																							</tr>
																							<tr>
																								<td><label><spring:message
																											code="member.domainusage" /> </label></td>
																								<td id="issDomainUsageTD"></td>
																								<td><label><spring:message
																											code="member.msgtype" /></label></td>
																								<td id="messageTypeTD"></td>
																								<td><label><spring:message
																											code="member.cardtech" /></label></td>
																								<td id="cardTechnologyTD"></td>
																							</tr>
																							<tr>
																								<td><label><spring:message
																											code="member.authmech" /></label></td>
																								<td id="authMechanismTD"></td>
																							<c:if test = "${InternationalParticipant ne 'Y'}">
																								<td><label><spring:message
																											code="member.pdttype" /></label></td>
																								<td id="issProductTypeTD"></td>
																							</c:if>
																								<td><label><spring:message
																											code="member.subscheme" /></label></td>
																								<td id="subSchemeTD"></td>
																							</tr>
																							
																							<c:if test = "${InternationalParticipant ne 'Y'}">
																								<tr>
																								<td><label><spring:message
																											code="member.cardsubvar" /></label></td>
																								<td id="cardSubVariantTD"></td>
																							
																								<td><label><spring:message
																											code="member.pgdetails" /></label></td>
																								<td id="programDetailsTD"></td>
																							
																								<td><label><spring:message
																											code="member.formfactor" /></label></td>
																								<td id="formFactorTD"></td>
																								</tr>
																							</c:if>
																							
																							<tr>
																								<td><label><spring:message
																											code="member.settmntBIN" /></label></td>
																								<td id="issSettlementBinTD">  </td>
																								<td><label><spring:message
																											code="member.isOfflAllowd" /></label></td>
																								<td id="isIssOfflineAllowedTD"></td>
																							<c:if test = "${InternationalParticipant ne 'Y'}">
																								<td><label><spring:message
																											code="member.featurefee" /></label></td>
 																								<td id="featureIssBinTD"></td>
 																							</c:if>
																							</tr>
																							
																							
																							<tr>
																							<td><label><spring:message
																											code="member.binLength" /></label></td>
 																								<td id="binLengthTD"></td>
 																								<td><label><spring:message
																											code="member.additionalParams" /></label></td>
 																								<td id="additionalParamsTD"></td>
																							</tr>
																							
																							<c:if test = "${InternationalParticipant eq 'Y'}">

																							<tr>
																							<td><label><spring:message
																											code="member.markUp" /></label></td>
 																								<td id="markUp"></td>
 																								<td><label><spring:message
																											code="member.networkSelection" /></label></td>
 																								<td id="networkSelectionTD"></td>
 																								
 																									
																							</tr>
																						<tr>
																						<td><label>Network License id</label></td>
 																								<td id="networkLicenseId"></td>
 																								<td><label>Network Issuer id</label></td>
 																								<td id="networkIssId"></td>
																						</tr>
																							</c:if>
																						</tbody>
																					</table>
																					<table id="dataTable1">
																				<caption style="display:none;">Member View</caption> 
																					<th scope="col"></th>
																						<div class="panel-body">
																							<table class="table">
																						<caption style="display:none;">Member View</caption> 
																								<thead>
																									<tr>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.binno" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.bincardtype" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.binpdttype" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.bincardvarnt" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.bincardbrand" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.bintype" /> </label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.status" /></label></th>
																										<th scope="col"><label for="squareInput"><spring:message
																													code="member.action" /></label></th>

																									</tr>
																								</thead>
																								<tbody id="issuerBinsList">
																									<c:if
																										test="${not empty memberOnBoardingDTO.issBinList}">
																										<c:forEach var="issBin"
																											items="${memberOnBoardingDTO.issBinList}"
																											varStatus="status">
																											<tr>
																												<td>${issBin.binNumber}</td>
																												<td>${issBin.binDetailsCodeDescription.binCardType}</td>
																												<td>${issBin.binDetailsCodeDescription.binProductType}</td>
																												<td>${issBin.binDetailsCodeDescription.binCardVariant}</td>
																												<td>${issBin.binDetailsCodeDescription.binCardBrand}</td>
																												<td>${issBin.binDetailsCodeDescription.issBinType}</td>
																												<td><c:choose>
																														<c:when
																															test="${issBin.status eq 'B'}">
																															<spring:message code="member.blckd" />
																														</c:when>
																														<c:when
																															test="${issBin.status eq 'D'}">
																															<spring:message code="member.deleted" />
																														</c:when>
																														<c:otherwise>
																															<spring:message code="member.active" />
																														</c:otherwise>
																													</c:choose></td>

																												<td style="text-align:center"><a
																													href="javascript:viewIssuerBin('${issBin.binNumber}')"><span
																														class="glyphicon  glyphicon-eye-open my-tooltip"
																														title="VIEW"></span></a></td>
																											</tr>
																										</c:forEach>
																									</c:if>
																								</tbody>
																							</table>
																						</div>
																					</table>
																				</div>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="panel panel-default">
											<div class="panel-heading">
												<h4 class="panel-title">
													<a data-toggle="collapse" data-parent="#accordion"
														href="#collapseFive" id="collapseFiveLink"><spring:message
															code="member.docupld&downld" /> <span
														class="glyphicon glyphicon-plus areaShowHde "></span> </a>
												</h4>
											</div>

											<div id="collapseFive"
												class="panel-collapse collapse panelHideShow">
												<div class="panel-body">
													<div class="">
														<div class="row">
															<div class="col-md-12">
																<div class="card">
																	<div class="card-body">
																		<div class="row">
																			<div class="col-md-6">
																				<div class="form-group">


																					<table class="table table-striped">
																					<caption style="display:none;">Member View</caption> 
																						<thead>
																							<tr>
																								<th scope="col"><input type="checkbox"
																									id="checkAllDocument" /></th>
																								<th scope="col"><label for="squareInput"><spring:message
																											code="member.filename" /></label></th>
																								<th scope="col"><label for="squareInput"><spring:message
																											code="member.status" /></label></th>
																								<th scope="col"><label for="squareInput"><spring:message
																											code="member.action" /></label></th>

																							</tr>


																						</thead>
																						<tbody id="documentList">
																							<c:if
																								test="${not empty memberOnBoardingDTO.documents}">
																								<c:forEach var="document"
																									items="${memberOnBoardingDTO.documents}"
																									varStatus="status">
																									<tr>
																										<td><input type="checkbox"
																											id="${document.documentName}"
																											name="selectMemberDocument"
																											onclick="toggleCheckAllDocuments()" /></td>
																										<td>${document.documentName}</td>
																										<td>Uploaded</td>
																										<td><a
																											href="javascript:downloadMemberDocument('${document.documentName}')"><span
																												class="glyphicon glyphicon-download my-tooltip"
																												title="DOWNLOAD"></span></a></td>
																									</tr>
																								</c:forEach>
																							</c:if>
																							 <c:if
																								test="${empty memberOnBoardingDTO.documents}">
																								<tr>
																										<td colspan="3" style="text-align:center">
																										  <label><spring:message
																											code="member.memberna"/></label>
																										</td>
																										</tr>
																								</c:if>
																						</tbody>
																					</table>
																					<c:if
																						test="${not empty memberOnBoardingDTO.documents}">
																						<div class="card-action">
																							<button type="button" class="btn btn-primary"
																								onclick="downloadMemberDocumentBatch()"
																								id="downloadFiles">
																								<spring:message code="st.lbl.dwnFiles" />
																							</button>
																						</div>
																					</c:if>
																					<div></div>

																				</div>
																			</div>
																		</div>
																	</div>
																</div>
															</div>

														</div>
													</div>
												</div>
											</div>

										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>



		<c:if
			test="${memberOnBoardingDTO.recordStatus eq 'R' or memberOnBoardingDTO.recordStatus eq 'P'}">
			<sec:authorize access="hasAuthority('Approve Member')">
			<div class="panel panel-default no_margin">
				<div class="panel-body">
					<div class="row">

						<div class="col-md-12">
							<table class="table table-striped infobold"
								style="font-size: 12px">
								<caption style="display:none;">Member View</caption> 
								<tbody>
								<th scope="col"></th>
									<tr>
										<td colspan="6"><div class="panel-heading-red clearfix">
												<strong><span class="glyphicon glyphicon-info-sign"></span> <span
													data-i18n="Data"><spring:message
															code="member.requestInformation" /></span></strong>
											</div></td>
										<td></td>
									</tr>
									<tr>
										<td><label><spring:message
													code="member.requestType" /><span style="color: red"></span></label></td>
										<td>${memberOnBoardingDTO.lastOperation}</td>
										<td><label><spring:message
													code="member.requestDate" /><span style="color: red"></span></label></td>
										<c:choose>
											<c:when test="${memberOnBoardingDTO.lastUpdatedOn == null}">
												<td>${memberOnBoardingDTO.createdOn}</td>
											</c:when>
											<c:otherwise>
												<td>${memberOnBoardingDTO.lastUpdatedOn}</td>
												</c:otherwise>
										</c:choose>
										<td><label><spring:message
													code="member.requestStatus" /><span style="color: red"></span></label></td>
										<td id ="request"><c:if
												test="${memberOnBoardingDTO.recordStatus=='A' }">
												<spring:message
													code="member.requestState.approved.description" />
											</c:if> <c:if test="${memberOnBoardingDTO.recordStatus=='P' }">
												<spring:message
													code="member.requestState.pendingApproval.description" />
											</c:if> <c:if test="${memberOnBoardingDTO.recordStatus=='R' }">
												<spring:message
													code="member.requestState.rejected.description" />
											</c:if> <c:if test="${memberOnBoardingDTO.recordStatus=='D' }">
												<spring:message
													code="member.requestState.discarded.description" />
											</c:if> &nbsp;</td>
										<td></td>



									</tr>
									<tr>
										<td><label><spring:message
													code="member.requestBy" /><span style="color: red"></span></label></td>
													
										<c:choose>
											<c:when test="${memberOnBoardingDTO.lastUpdatedBy == null}">
												<td>${memberOnBoardingDTO.createdBy}</td>
											</c:when>
											<c:otherwise>
												<td>${memberOnBoardingDTO.lastUpdatedBy}</td>
												</c:otherwise>
										</c:choose>			
										

										<td><label><spring:message
													code="member.approverComments" /><span style="color: red"></span></label></td>
										<td id ="checker" colspan=2>${memberOnBoardingDTO.checkerComments}</td>
										<td></td>
										<td></td>

									</tr>
								</tbody>
							</table>
						</div>
					</div>
					<sec:authorize access="hasAuthority('Approve Member')">
						<div class="row" id="approvepanel">
							<c:if test="${landing eq 'approve'}">
								<c:if test="${memberOnBoardingDTO.recordStatus eq 'P'}">
									<div class="alert alert-danger appRejMust" role="alert">
										<spring:message code="member.selectmsg" />
									</div>
									<div class="alert alert-danger remarkMust" role="alert">
										<spring:message code="member.remarksmsg" />
									</div>
									<div class="col-md-1">
										&nbsp;
									</div>
									<div class="col-md-3">
										<div class="form-group">
											<label><spring:message code="AM.lbl.approveReject" /><span
												class="red">*</span> </label> <select name="select" id="apprej"
												onchange="enadisReject()" class="form-control input-square">
												<option value="N"><spring:message
														code="AM.lbl.select" /></option>
												<option value="A" id="approve"><spring:message
														code="AM.lbl.approve" /></option>
												<option value="R" id="reject"><spring:message
														code="AM.lbl.reject" /></option>
											</select>
										</div>
									</div>
									<div class="col-md-6">
										<div class="form-group">
											<label for="squareSelect"><spring:message
													code="member.remarks" /><span class="red">*</span></label>
											<textarea rows="4" cols="50"
												class="form-control input-square" maxlength="100"
												id="rejectReason"></textarea>
											<div id="errorrejectReason" class="error"></div>
										</div>
									</div>
								</c:if>
							</c:if>
						</div>
						</sec:authorize>
				</div>
			</div>
			</sec:authorize>
		</c:if>


		<div class="text-center">
			<div class="card" style="padding: 15px;">
				<div class="">
					<sec:authorize access="hasAnyAuthority('Add Member', 'Edit Member')">
						<c:if
							test="${(memberOnBoardingDTO.recordStatus eq 'A') or ((memberOnBoardingDTO.recordStatus eq 'R') and ( landing eq 'approve')) or ((memberOnBoardingDTO.recordStatus eq 'I') and ( landing eq 'saveData')) or ((memberOnBoardingDTO.recordStatus eq 'I') and ( landing eq 'main'))}">
							<a
								href="javascript:editMember('${memberOnBoardingDTO.participantId}');"
								class="btn btn-success"><spring:message
									code="member.editBtn" /></a>
						</c:if>
						<c:if
							test="${(memberOnBoardingDTO.recordStatus eq 'R') and ( landing eq 'approve')}">
							<a href="#" id="discardData"
									class="btn btn-success button"><spring:message
										code="member.discardBtn" /></a>
						</c:if>
					</sec:authorize>

					<c:if test="${  landing eq 'main'}">
						<a href="javascript:userAction('N','/showFinalMembers');"
							class="btn btn-danger"><spring:message code="member.backBtn" /></a>
					</c:if>
					<c:if test="${landing eq 'saveData'}">
						<a href="javascript:userAction('N','/showSavedMembers');"
							class="btn btn-danger"><spring:message code="member.backBtn" /></a>
					</c:if>
					<c:if test="${landing eq 'approve'}">
						<sec:authorize access="hasAuthority('Approve Member')">
							<c:if test="${memberOnBoardingDTO.recordStatus eq 'P'}">
								<input name="button10" type="button"
									class="btn btn-success button" id="approveMember"
									value="Submit"
									onclick="postActionMember('/approveMemberRequest');" />

							</c:if>
						</sec:authorize>
						<a href="javascript:userAction('N','/pendingMembersFrAppr');"
							class="btn btn-danger"><spring:message code="member.backBtn" /></a>
					</c:if>
				</div>
			</div>
		</div>
		</form:form>
	</div>
</div>
</div>

</html>