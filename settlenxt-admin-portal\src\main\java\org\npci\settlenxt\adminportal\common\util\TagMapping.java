package org.npci.settlenxt.adminportal.common.util;

import java.util.List;

import org.npci.settlenxt.adminportal.common.mapping.NameMappingContext;
import org.npci.settlenxt.adminportal.common.mapping.TagFieldTypes;

public class TagMapping {
	private String tagName;
	private String xmlName;

	private List<TagFieldTypes> fields;

	private NameMappingContext context;
	private String parent;

	private String configFile;
	private String configProperty;
	private String type;

	public TagMapping() {
		//empty
	}

	public TagMapping(String tagName, String xmlName) {
		this.tagName = tagName;
		this.xmlName = xmlName;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public NameMappingContext getContext() {
		return context;
	}

	public void setContext(NameMappingContext context) {
		this.context = context;
	}

	public String getConfigFile() {
		return configFile;
	}

	public void setConfigFile(String configFile) {
		this.configFile = configFile;
	}

	public String getConfigProperty() {
		return configProperty;
	}

	public void setConfigProperty(String configProperty) {
		this.configProperty = configProperty;
	}

	public String getParent() {
		return parent;
	}

	public void setParent(String parent) {
		this.parent = parent;
	}

	public String getXmlName() {
		return xmlName;
	}

	public void setXmlName(String xmlName) {
		this.xmlName = xmlName;
	}

	public List<TagFieldTypes> getFields() {
		return fields;
	}

	public void setFields(List<TagFieldTypes> fields) {
		this.fields = fields;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

}
