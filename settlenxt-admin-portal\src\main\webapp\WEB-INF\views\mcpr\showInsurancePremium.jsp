<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript">
	var actionColumnIndex = 8;
	var firstColumnToBeSkippedInFilterAndSort = false;
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 10;
	firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	<c:if test="${approvedlist eq 'N'}">
	actionColumnIndex = 9;
	</c:if>
	</c:if>
</script>
<script>
	var referenceNoListPendings = [];

	<c:if test="${not empty insurancePremiumlist}">
	<c:forEach items="${insurancePremiumlist}" var="insurancePremium">
	<c:if test="${insurancePremium.requestState eq 'P' }">
	referenceNoListPendings.push('${insurancePremium.insurancePremId}');

	</c:if>
	</c:forEach>
	</c:if>
</script>

<script src="./static/js/validation/mcpr/SearchInsurancePremiumRGCS.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="./static/js/jszip.min.js"></script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js"></script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>
<!-- Modal -->
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalNews" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Insurance
					Primium Approval/Rejection</label>
				<p id="newsIds" />
			</div>



			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulkInsurancePremium('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulkInsurancePremium('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Modal -->
<!-- Modal -->
<input:hidden id="refNum" />
<div class="row">
	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${approvedlist eq 'Y' }">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home"
			onclick="submitForm('/showMcprInsurancePremiumConfig');" role="tab"
			data-toggle="tab"> <span class="glyphicon glyphicon-credit-card">&nbsp;</span>
			<spring:message code="insurancePremium.tabname" />
		</a>

		<c:choose>
			<c:when test="${approvedlist eq 'N'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#profile" role="tab"
			onclick="submitForm('/insurancePremiumPendingForApproval');"
			data-toggle="tab"> <span class="glyphicon glyphicon-ok">&nbsp;</span>
			<spring:message code="insurancePremium.approval" />
		</a>
	</ul>

	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
				<div class="col-sm-12">
					<sec:authorize access="hasAuthority('Add Insurance Premium')">
						<c:if test="${approvedlist eq 'Y'}">
							<a class="btn btn-success pull-right btn_align" href="#"
								onclick="submitForm('/insurancePremiumCreation','P');"
								style="margin: -8px 0px 5px 0px;"><em class="glyphicon-plus"></em>
								<spring:message code="insurancePremium.listscreen.title" /></a>
						</c:if>
					</sec:authorize>
					<div class="row">
						<div class="col-sm-12">
							<button class="btn  pull-right btn_align" id="clearFilters">
								<spring:message code="ifsc.clearFiltersBtn" />
							</button>
							&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
								id="csvExport"> <spring:message code="ifsc.csvBtn" />
							</a> <a class="btn btn-success pull-right btn_align" href="#"
								id="excelExport">Excel </a>
						</div>
					</div>
					<div class="panel panel-default">
						<div class="panel-heading">
							<c:if test="${approvedlist eq 'Y'}">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="insurancePremium.insurancePremiumInformation" /></span></strong>
							</c:if>
							<c:if test="${approvedlist eq 'N'}">

								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="insurancePremium.insurancePremiumPendingConfiguration" /></span></strong>
							</c:if>
							<c:if test="${approvedlist eq 'N'}">

								<sec:authorize
									access="hasAuthority('Approve Insurance Premium')">
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveorRejectBulkInsurancePremium('A','No')"
										id="submitButtonA"
										value="<spring:message code="feeRate.Approve" />" />
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveorRejectBulkInsurancePremium('R','No')"
										id="submitButtonR"
										value="<spring:message code="feeRate.Reject" />" />
								</sec:authorize>
							</c:if>
						</div>


						<div class="panel-body">
							<div class="table-responsive">
								<table id="tabnew" class="table table-striped table-bordered"
									style="width: 100%;">
									<caption style="display: none;">Insurance Premium</caption>
									<thead>
										<tr>
											<c:if test="${(approvedlist eq 'N') }">
												<sec:authorize
													access="hasAuthority('Approve Insurance Premium')">
													<th scope = "col"><input type=checkbox name='selectAllCheck'
														id="selectAll" data-target="toggleModalNews" value="All"></input></th>
												</sec:authorize>
											</c:if>
											<th scope = "col"><spring:message code="insurancePremium.cardType" /></th>
											<th scope = "col"><spring:message code="insurancePremium.cardVariant" /></th>
											<th scope="col"><spring:message
													code="insurancePremium.premiumPerCardPerAnnum" /></th>
											<th scope = "col"><spring:message code="insurancePremium.vendor" /></th>
											<th scope="col"><spring:message
													code="insurancePremium.fromMonthYear" /></th>
											<th scope = "col"><spring:message code="insurancePremium.toMonthYear" /></th>
											<th scope = "col"><spring:message code="insurancePremium.status" /></th>
											<c:if test="${approvedlist eq 'N'}">
												<th scope = "col"><spring:message code="rebate.approverComments" /></th>
											</c:if>
										</tr>
									</thead>
									<tbody>
										<c:forEach var="insurancePremium"
											items="${insurancePremiumlist}">

											<tr>
												<c:if test="${approvedlist eq 'N'}">
													<sec:authorize
														access="hasAuthority('Approve Insurance Premium') ">
														<c:if test="${insurancePremium.requestState eq'P'}">
															<th scope = "col"><input type=checkbox name='type'
																id="selectSingle" onclick="mySelect();"
																value="${insurancePremium.insurancePremId}"></input></th>
														</c:if>
														<c:if test="${insurancePremium.requestState ne'P'}">
															<th scope = "col"></th>
														</c:if>
													</sec:authorize>
													<td
														onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.cardTypeName}</td>
													<td
														onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.cardVariantName}</td>
													<td
														onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.annualPremiumAmtPerCard}</td>
													<td
														onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.vendorName}</td>
													<td
														onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.fromYearMonth}</td>
													<td
														onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.toYearMonth}</td>
													<td
														onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.status}</td>
													<td
														onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.checkerComments}</td>
												</c:if>
												<c:if test="${approvedlist eq 'Y'}">
													<sec:authorize
														access="hasAuthority('Edit Insurance Premium')">
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.cardTypeName}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.cardVariantName}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.annualPremiumAmtPerCard}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.vendorName}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.fromYearMonth}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.toYearMonth}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.status}</td>
													</sec:authorize>
													<sec:authorize
														access="hasAuthority('Approve Insurance Premium') ">
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.cardTypeName}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.cardVariantName}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.annualPremiumAmtPerCard}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.vendorName}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.fromYearMonth}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.toYearMonth}</td>
														<td
															onclick="javascript:viewInsurancePremiumGrid('${insurancePremium.insurancePremId}','${insurancePremium.requestState}','${approvedlist}','${originPage}')">${insurancePremium.status}</td>
													</sec:authorize>
												</c:if>
											</tr>
										</c:forEach>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>


		</div>

	</div>



</div>
