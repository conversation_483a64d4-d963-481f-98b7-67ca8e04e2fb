<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/showRejectReasonCodeRule.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	
	<c:url value="getRejectReasonCodeRule" var="getRejectReasonCodeRule" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveRejectReasonCode" modelAttribute="rejectReasonCodeRuleDto"
		action="${getRejectReasonCodeRule}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"></span><spring:message code="rejectReasonCodeRules.mainTab.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="crtuser"
							value="${rejectReasonCodeRuleDto.createdBy}" />
						<input type="hidden" id="seqId"
							value="${rejectReasonCodeRuleDto.seqId}" />
						<table class="table table-striped infobold"
							style="font-size: 12px">
								<caption style="display:none;">View Reject Reason Code</caption>  
							<thead style="display:none;"><th scope="col"></th></thead>
						
							<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"></span><spring:message code="rejectReasonCodeRules.viewscreen.title" /></span></strong>
										</div></td>
										<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									
								</tr>
								<tr>

								</tr>
								
								<tr>
									<td><label><spring:message code="rejectReasonCodeRule.functionCode" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.funcCode}</td>
									<td><label><spring:message code="rejectReasonCodeRule.fieldName" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.fieldName}</td>
									<td><label><spring:message code="rejectReasonCodeRule.fieldValue" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.fieldValue}</td>
									<td><label><spring:message code="rejectReasonCodeRule.relationalOperator" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.relationalOperator}</td>
									</tr>
									<tr>
									<td><label><spring:message code="rejectReasonCodeRule.fieldOperator" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.fieldOperator}</td>
									<td><label><spring:message code="rejectReasonCodeRule.subFieldName" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.subFieldName}</td>
									<td><label><spring:message code="rejectReasonCodeRule.rejectCode" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.rejectCode}</td>
									<td><label><spring:message code="rejectReasonCodeRule.status" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.status}</td>
								</tr>
				
							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									
								

									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/showRejectReasonCode');"><spring:message code="budget.backBtn" /></button>
									<sec:authorize access="hasAuthority('Edit Reject Reason Code')">
								
								<button type="button" class="btn btn-success"
										onclick="viewRejectReasonCodeRule('${rejectReasonCodeRuleDto.seqId}','V')">
										<spring:message code="sm.lbl.edit" /></button>
								
								</sec:authorize>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

