<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>


<script type="text/javascript"> 
var actionColumnIndex=8;
var firstColumnToBeSkippedInFilterAndSort=false;
</script>

<script src="./static/js/validation/mcpr/viewProjectionEntry.js"
	type="text/javascript"></script>
<script type="text/javascript" src=	"./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src=	"./static/js/jszip.min.js">
</script>
 
<script type="text/javascript" src=	"./static/js/buttons.html5.min.js">
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />

<div class="container-fluid height-min">

	
	<c:url value="getBudget" var="getBudget" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveBudgetConfig" modelAttribute="projectionEntryDTO"
		action="${getBudget}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="projectionEntry.mainTab.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="excId" value="${projectionEntryDTO.cardProjectionId}">

						<input type="hidden" id="crtuser"
							value="${projectionEntryDTO.createdBy}" />

						<table class="table table-striped infobold" style="font-size: 12px">
							<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
						<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message code="projectionEntry.viewscreen.title" /></span></strong>
										</div></td>
										
									
								</tr>
								
								<tr>
									<td><label><spring:message code="projectionEntry.cardProjectionId" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.cardProjectionId}</td>
									<td><label><spring:message code="projectionEntry.cardType" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.cardTypeName}</td>
									<td><label><spring:message code="projectionEntry.cardVariant" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.cardVariantName}</td>
									
									
									
								</tr>
								<tr>
								<td><label><spring:message code="projectionEntry.month" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.month}</td>
									<td><label><spring:message code="projectionEntry.totalCards" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.totalCards}</td>
									<td colspan="2"></td>
									</tr>
								
								<%-- 
								<tr>
								<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message code="projectionEntry.requestInfo" /></span></strong>
										</div></td>
										
									
									</tr>
									<tr>
										
									<td><label><spring:message code="projectionEntry.requestBy" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.lastUpdatedBy}</td>
									<td><label><spring:message code="projectionEntry.requestDate" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.lastUpdatedOn}</td>
									<td><label><spring:message code="projectionEntry.requestType" /><span style="color: red"></span></label></td>
									<td>${projectionEntryDTO.lastOperation}</td>

									<td><label><spring:message code="projectionEntry.approverComments" /><span
											style="color: red"></span></label></td>
									<td colspan=2>${projectionEntryDTO.checkerComments}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr> --%>

								


								
							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									
								

									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/showProjectionEntry');"><spring:message code="budget.backBtn" /></button>
									<sec:authorize access="hasAuthority('Edit Card Projection')">
								
									<c:if test="${projectionEntryDTO.requestState eq 'A'}">
										 <button type="button" class="btn btn-success"
											onclick="edit('${projectionEntryDTO.cardProjectionId}','V','mainTab')">
											<spring:message code="sm.lbl.edit" />
										</button>
									</c:if>
									<button type="button" value="Submit" class="btn btn-success"
										onclick="postCalculateAction('/calculateAmount');">
										Calculate
									</button>
								
								</sec:authorize>
								</div>
							</div>
						</div>

						<c:if test="${not empty premiumCalculationDTO}">
						<div class="panel-body">
								
								<div class="row">
											<div class="col-sm-12">
													<%-- <button class="btn  pull-right btn_align" 
													id="clearFilters"><spring:message code="currencyMaster.clearBtn" /></button> --%>
													&nbsp; <a class="btn btn-success pull-right btn_align" href="#" id="csvExport">
													<spring:message code="currencyMaster.csvBtn" /> </a>
													<a class="btn btn-success pull-right btn_align" href="#"
														id="excelExport"  ><spring:message code="currencyMaster.exportBtn" />
													</a>
		
											</div>
										</div>
								
								
								<!-- exportdatainfo -->
								<div id="exportdatainfo" style="display:none;">
									<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
										<caption style="display:none;">Projection Entry</caption>
										<thead style="display:none;"><th scope = "col"></th></thead>
										<thead>
											<tr>
												<th scope="col">${premiumCalculationDTO.nextProjectedCardsLabel}</th>
												<th scope="col">${premiumCalculationDTO.nextPremiumPerCardPerAnnumLabel}</th>
												<th scope="col">${premiumCalculationDTO.nextTotalPremiumPayableExclTaxesLabel}</th>
												<th scope="col">${premiumCalculationDTO.nextGSTLabel}</th>
												<th scope="col">Total Premium Payable (Inclusive of all taxes)</th>
												
												<c:if test="${IsAdjustmentShowFlag eq 'Y'}">
													<th scope="col">Total No. of Rupay cards projected for ${premiumCalculationDTO.previousMonth}</th>
										<th scope = "col">th>Premium Per Card Per Annum (Excl. of GST) </th>
													<th scope="col">${premiumCalculationDTO.previousProjectedTotalPremiumPayableExclTaxesLebal}</th>
													<th scope="col">Total No. of Rupay cards issued for ${premiumCalculationDTO.previousMonth}</th>
													<th scope="col">Premium Per Card Per Annum (Excl. of GST)</th>
													<th scope="col">Total Premium Paid (Excl. of GST) ${premiumCalculationDTO.previousMonth}</th>
													<th scope="col">${premiumCalculationDTO.premiumDiffLebal}</th>
												</c:if>
												
												<c:if test="${IsAprFlag eq 'Y'}">
													<th scope="col">${premiumCalculationDTO.totalNoCardIssuetillFebLebal}</th>
													<th scope="col">${premiumCalculationDTO.totalNoCardProjectionForMarchLebal}</th>
													<th scope="col">Total Card Base</th>
													<th scope="col">Premium Per Card Per Annum</th>
													<th scope="col">${premiumCalculationDTO.previousProjectedTotalPremiumPayableExclTaxesLebal} </th>
													<th scope="col">${premiumCalculationDTO.nextGSTLabel}</th>
													<th scope="col">Total Premium payable for existing card base (inclusive of all taxes)</th>
												</c:if>
												<c:if test="${IsMayFlag eq 'Y'}">
													<th scope="col">Total No. of Rupay cards projected for Base Line</th>
													<th scope="col">Premium Per Card Per Annum (Excl. of GST) </th>
													<th scope="col">${premiumCalculationDTO.baseLineProjectedTotalPremiumPayableExclTaxesLebal}</th>
													<th scope="col">Total No. of Rupay cards issued for Base Line</th>
													<th scope="col">Premium Per Card Per Annum (Excl. of GST)</th>
													<th scope="col">Total Premium Paid (Excl. of GST) Base Line</th>
													<th scope="col">${premiumCalculationDTO.baseLinepremiumDiffLebal}</th>
												</c:if>
		
												<th scope="col">${premiumCalculationDTO.previousProjectedTotalPremiumPayableInclTaxesLebal}</th>
												<th scope="col">${premiumCalculationDTO.previousActualTotalPremiumPayableInclTaxesLebal}</th>
												<c:if test="${IsAprFlag eq 'Y'}">
													<th scope="col">Total Premium payable for existing card base (Excl of taxes)</th>
												</c:if>
												<th scope="col">${premiumCalculationDTO.totalPremiumAfterAdjustmentExclTaxesLebal}</th>
												<th scope="col">${premiumCalculationDTO.totalPremiumAfterAdjustmentInclTaxesLebal}</th>
		
											</tr>
										</thead>
										<tbody>
												<tr>
												<td>${premiumCalculationDTO.nextProjectedCards}</td>
												<td>${premiumCalculationDTO.nextPremiumPerCardPerAnnumDisplay}</td>
												<td>${premiumCalculationDTO.nextTotalPremiumPayableExclTaxesDisplay}</td>
												<td>${premiumCalculationDTO.nextGSTAmountDisplay}</td>
												<td>${premiumCalculationDTO.nextTotalPremiumPayableInclTaxesDisplay}</td>

												<c:if test="${IsAdjustmentShowFlag eq 'Y'}">
													<td>${premiumCalculationDTO.previousProjectedCards}</td>
													<td>${premiumCalculationDTO.previousProjectedPremiumPerCardPerAnnumDisplay}</td>
													<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableExclTaxesDisplay}</td>
													<td>${premiumCalculationDTO.previousActualCards}</td>
													<td>${premiumCalculationDTO.previousActualPremiumPerCardPerAnnumDisplay}</td>
													<td>${premiumCalculationDTO.previousActualTotalPremiumPayableExclTaxesDisplay}</td>
													<td>${premiumCalculationDTO.premiumDiffDisplay}</td>
												</c:if>

												<c:if test="${IsAprFlag eq 'Y'}">
													<td>${premiumCalculationDTO.previousMonthActualLabel}</td>
													<td>${premiumCalculationDTO.totalNoCardProjectionForMarch}</td>
													<td>${premiumCalculationDTO.totalCardBase}</td>
													<td>${premiumCalculationDTO.premiumPerCardPerAnnumForMarchDisplay}</td>
													<td>${premiumCalculationDTO.premiumPayableExcTaxForMarchDisplay}</td>
													<td>${premiumCalculationDTO.premiumPayableGSTTaxForMarchDisplay}</td>
													<td>${premiumCalculationDTO.premiumPayableIncTaxForMarchDisplay}</td>
												</c:if>
												<c:if test="${IsMayFlag eq 'Y'}">
													<td>${premiumCalculationDTO.baseLineProjectedCards}</td>
													<td>${premiumCalculationDTO.baseLineProjectedPremiumPerCardPerAnnumDisplay}</td>
													<td>${premiumCalculationDTO.baseLineProjectedTotalPremiumPayableExclTaxesDisplay}</td>
													<td>${premiumCalculationDTO.baseLineActualCards}</td>
													<td>${premiumCalculationDTO.baseLineActualPremiumPerCardPerAnnumDisplay}</td>
													<td>${premiumCalculationDTO.baseLineActualTotalPremiumPayableExclTaxesDisplay}</td>
													<td>${premiumCalculationDTO.baseLinepremiumDiffDisplay}</td>
												</c:if>
		
												<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableInclTaxesDisplay}</td>
												<td>${premiumCalculationDTO.previousActualTotalPremiumPayableInclTaxesDisplay}</td>
												<c:if test="${IsAprFlag eq 'Y'}">
													<td>${premiumCalculationDTO.premiumPayableExcTaxForMarchDisplay}</td>
												</c:if>
												<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentExclTaxesDisplay}</td>
												<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentInclTaxesDisplay}</td>
												</tr>
										</tbody>
									</table>
								</div>
								</div>
		
								
								
								<div class="panel-body">
								<div class="table-responsive">
								<table id="tabnewMain" class="table table-striped table-bordered" style="width:100%;">	
								<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope = "col"></th></thead>
						<tr>
									<td colspan=4><strong>Projection Calculation</strong></td>
								</tr>
								<tr>
									<td>Summary</td>
									<td>${premiumCalculationDTO.cardInfo}</td> 
								</tr>
								<tr>
									<td>${premiumCalculationDTO.nextProjectedCardsLabel}</td>
									<td>${premiumCalculationDTO.nextProjectedCards}</td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.nextPremiumPerCardPerAnnumLabel}</td>
									<td>${premiumCalculationDTO.nextPremiumPerCardPerAnnumDisplay}</td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.nextTotalPremiumPayableExclTaxesLabel}</td>
									<td>${premiumCalculationDTO.nextTotalPremiumPayableExclTaxesDisplay}</td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.nextGSTLabel}</td>
									<td>${premiumCalculationDTO.nextGSTAmountDisplay}</td>
								</tr>
								<tr>
									<td>Total Premium Payable (Inclusive of all taxes)</td>
									<td>${premiumCalculationDTO.nextTotalPremiumPayableInclTaxesDisplay}</td>
								</tr>
								</table>
								</div>						
								<Br>
								<Br>	
								<c:if test="${IsAdjustmentShowFlag eq 'Y'}">
								
								    <div class="form-group">
									<label>Adjustment</label>
								    </div>
								
								<div class="table-responsive">
								<table id="tabnewMain" class="table table-striped table-bordered" style="width:100%;">	
								<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope = "col"></th></thead>
						<tr>
									<td colspan=4><strong>Table II</strong></td>
								</tr>
								<tr>
									<td colspan=2>${premiumCalculationDTO.previousMonthProjectedLabel}</td>
									<td colspan=2>${premiumCalculationDTO.previousMonthActualLabel} </td>
								</tr>
								<tr>
									<td>Summary</td>
									<td>${premiumCalculationDTO.previousProjectedLabel}</td>
									<td>Summary</td>
									<td>${premiumCalculationDTO.previousActualLabel}</td>
								</tr>
								<tr>
									<td>Total No. of Rupay cards projected</td>
									<td>${premiumCalculationDTO.previousProjectedCards}</td>
									<td>	Total No. of Rupay cards issued</td>
									<td>${premiumCalculationDTO.previousActualCards}</td>
								</tr>
								<tr>
									<td>Premium Per Card Per Annum (Excl. of GST)</td>
									<td>${premiumCalculationDTO.previousProjectedPremiumPerCardPerAnnumDisplay}</td>
									<td>Premium Per Card Per Annum (Excl. of GST)</td>
									<td>${premiumCalculationDTO.previousActualPremiumPerCardPerAnnumDisplay}</td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableExclTaxesLebal}</td>
									<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableExclTaxesDisplay}</td>
									<td>	Total Premium Paid (Excl. of GST)</td>
									<td>${premiumCalculationDTO.previousActualTotalPremiumPayableExclTaxesDisplay}</td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.premiumDiffLebal}</td>
									<td colspan=3>${premiumCalculationDTO.premiumDiffDisplay}</td>
								</tr></table>
								</div>						
								<Br>
								<Br>	
								</c:if>
								<c:if test="${IsMayFlag eq 'Y'}">
								
								    <div class="form-group">
									<label>Base Line Adjustment</label>
								    </div>
								
								<div class="table-responsive">
								<table id="tabnewMain" class="table table-striped table-bordered" style="width:100%;">	
								<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope = "col"></th></thead>
						<tr>
									<td colspan=4><strong>Table II</strong></td>
								</tr>
								<tr>
									<td colspan=2>${premiumCalculationDTO.baseLineMonthProjectedLabel}</td>
									<td colspan=2>${premiumCalculationDTO.baseLineMonthActualLabel} </td>
								</tr>
								<tr>
									<td>Summary</td>
									<td>${premiumCalculationDTO.baseLineProjectedLabel}</td>
									<td>Summary</td>
									<td>${premiumCalculationDTO.baseLineActualLabel}</td>
								</tr>
								<tr>
									<td>Total No. of Rupay cards projected</td>
									<td>${premiumCalculationDTO.baseLineProjectedCards}</td>
									<td>	Total No. of Rupay cards issued</td>
									<td>${premiumCalculationDTO.baseLineActualCards}</td>
								</tr>
								<tr>
									<td>Premium Per Card Per Annum (Excl. of GST)</td>
									<td>${premiumCalculationDTO.baseLineProjectedPremiumPerCardPerAnnumDisplay}</td>
									<td>Premium Per Card Per Annum (Excl. of GST)</td>
									<td>${premiumCalculationDTO.baseLineActualPremiumPerCardPerAnnumDisplay}</td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.baseLineProjectedTotalPremiumPayableExclTaxesLebal}</td>
									<td>${premiumCalculationDTO.baseLineProjectedTotalPremiumPayableExclTaxesDisplay}</td>
									<td>	Total Premium Paid (Excl. of GST)</td>
									<td>${premiumCalculationDTO.baseLineActualTotalPremiumPayableExclTaxesDisplay}</td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.baseLinepremiumDiffLebal}</td>
									<td colspan=3>${premiumCalculationDTO.baseLinepremiumDiffDisplay}</td>
								</tr></table>
								</div>						
								<Br>
								<Br>	
								</c:if>
		
								<c:if test="${IsAprFlag eq 'Y'}">
								    <div class="form-group">
									<label><strong>Insurence for Existing Card Base</strong></label>
								    </div>
								
								<div class="table-responsive">
								<table id="tabnewMain" class="table table-striped table-bordered" style="width:100%;">	
								<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope = "col"></th></thead>
						<tr>
									<td >${premiumCalculationDTO.totalNoCardIssuetillFebLebal}</td>
									<td >${premiumCalculationDTO.totalNoCardIssuetillFeb} </td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.totalNoCardProjectionForMarchLebal}</td>
									<td>${premiumCalculationDTO.totalNoCardProjectionForMarch}</td>
								</tr>
								<tr>
									<td>Total Card Base</td>
									<td>${premiumCalculationDTO.totalCardBase}</td>
								</tr>
								<tr>
									<td>Premium Per Card Per Annum</td>
									<td>${premiumCalculationDTO.premiumPerCardPerAnnumForMarchDisplay}</td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableExclTaxesLebal}</td>
									<td>${premiumCalculationDTO.premiumPayableExcTaxForMarchDisplay}</td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.nextGSTLabel}</td>
									<td>${premiumCalculationDTO.premiumPayableGSTTaxForMarchDisplay}</td>
								</tr>
								<tr>
									<td>Total Premium payable for existing card base (inclusive of all taxes)</td>
									<td >${premiumCalculationDTO.premiumPayableIncTaxForMarchDisplay}</td>
								</tr></table>
								</div>						
								<Br>
								<Br>	
								</c:if>
		
								<div class="table-responsive">
								<table id="tabnewMain" class="table table-striped table-bordered" style="width:100%;">	
								<caption style="display:none;">Projection Entry</caption>
						<thead style="display:none;"><th scope = "col"></th></thead>
						<tr>
									<td><strong>Particulars</strong></td>
									<td><strong>Amount</strong></td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableInclTaxesLebal}</td>
									<td>${premiumCalculationDTO.previousProjectedTotalPremiumPayableInclTaxesDisplay}</td>
								</tr>
								<c:if test="${IsAdjustmentShowFlag eq 'Y'}">
								<tr>
									<td>${premiumCalculationDTO.previousActualTotalPremiumPayableInclTaxesLebal}</td>
									<td>${premiumCalculationDTO.previousActualTotalPremiumPayableInclTaxesDisplay}</td>
								</tr>
								</c:if>
								<c:if test="${IsMayFlag eq 'Y'}">
								<tr>
									<td>${premiumCalculationDTO.baseLineActualTotalPremiumPayableInclTaxesLebal}</td>
									<td>${premiumCalculationDTO.baseLineActualTotalPremiumPayableInclTaxesDisplay}</td>
								</tr>
								</c:if>
								<c:if test="${IsAprFlag eq 'Y'}">
								<tr>
									<td>Total Premium payable for existing card base (Excl of taxes)</td>
									<td>${premiumCalculationDTO.premiumPayableExcTaxForMarchDisplay}</td>
								</tr>
								</c:if>
								<tr>
									<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentExclTaxesLebal}</td>
									<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentExclTaxesDisplay}</td>
								</tr>
								<tr>
									<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentInclTaxesLebal}</td>
									<td>${premiumCalculationDTO.totalPremiumAfterAdjustmentInclTaxesDisplay}</td>
								</tr>
								
								</table>
		
		
							</div>	
							</div>					
							
										
						</c:if>
						
						
						
					</div>
					
				</div>
			</div>
		</div>
	</form:form>
</div>

