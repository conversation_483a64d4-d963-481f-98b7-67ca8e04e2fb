package org.npci.settlenxt.adminportal.gateway;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

import javax.annotation.PostConstruct;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.io.EmptyInputStream;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.protocol.HttpContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.google.gson.JsonObject;

@Component
public class RestGateway {
	
	protected HttpClientBuilder httpClientBuilder = null;
	protected CloseableHttpClient httpClient;
	protected RequestConfig requestConfig;
	
	private static final Logger logger = LogManager.getLogger(RestGateway.class);
	
	@PostConstruct
	public void init() {
		setupHttpClient();
	}
	
	/**
	 * Post API request to switch with the request Json.
	 * @param url 
	 * @param requestJson
	 * @return Response from the switch by converting to Json.
	 */
	public String postAPIForJsonObject(String url, JsonObject requestJson) {
		logger.info("RestGateway getCycleStatusDetails() in: ");
		String response = postDataToURL(url, requestJson.toString());
		logger.info("RestGateway getCycleStatusDetails() out: ");
		return response;
		
	}
	/**
	 * Post's the request to provide url with provided requestbody
	 * @param url
	 * @param requestBody
	 * @return 
	 *	String value of the response 
	 */
	public String postDataToURL(String url, String requestBody) {
		   logger.info("RestGateway postDataToURL() in: ");
	       CloseableHttpClient httpclient = httpClient;
	       HttpPost postRequest = new HttpPost(url);
           postRequest.setHeader("Content-Type", "application/json");
           postRequest.setEntity(new StringEntity(requestBody, "UTF-8"));
	       String result = null;
	        CloseableHttpResponse response = null;
	        try {
	        	logger.debug("posting request to URL :{} with Request body : {}",url,requestBody);
	            response = httpclient.execute(postRequest);
	            logger.debug("Response received : {}",response);
	        } catch(IOException e) {
	            logger.error("Error while posting the request to URL : {} {} {}",url,e.getMessage(),e);
	            return null;
	        }
	        HttpEntity entity = response.getEntity();
	        if(entity!=null) {
                try(InputStream instream = entity.getContent();) {
                	entity.getContentLength();
                	if(instream.getClass() == EmptyInputStream.class) {
                    	result = failedResponseHandle();
                    }else {
                        result = convertStreamToString(instream);
                    }
                } catch (UnsupportedOperationException | IOException e) {
                    logger.error("Error while posting data to URL {} {}",e.getMessage(),e);
                    result= failedResponseHandle();
                }
                logger.debug("response body : {}", result);
           }else {
        	   result = failedResponseHandle();
           }
	        logger.info("RestGateway postDataToURL() out: ");
	        return result;
	    }
	
		private String failedResponseHandle() {
			JsonObject failedReason = new JsonObject();
			failedReason.addProperty("Status", "FAILED");
			failedReason.addProperty("errorMessage", "Internal server error");
			return failedReason.toString();
		}

	/**
	 * Converts the InputStream object to String object
	 * @param instream
	 * @return
	 */
	private String convertStreamToString(InputStream instream) {
		logger.info("RestGateway convertStreamToString() in: ");
		
		StringBuilder response = new StringBuilder();
		String line = null;
		try (BufferedReader reader = new BufferedReader(new InputStreamReader(instream));) {
			while((line = reader.readLine())!=null) {
				response.append(line+"\n");
			}
		} catch(IOException e) {
			logger.error("Exception occured in convertStreamToString. Error : {}", e.getMessage(), e);
		}
		logger.info("RestGateway convertStreamToString() out: ");
		return response.toString();
	}

	/**
	 * HTTPClient setup
	 */
	private void setupHttpClient() {
		logger.info("RestGateway setupHttpClient() in: ");
		if(httpClientBuilder==null) {
			IOReactorConfig.Builder ioReactorConfig = IOReactorConfig.custom();
			ioReactorConfig.setIoThreadCount(8);
			ioReactorConfig.setConnectTimeout(6000);
			ioReactorConfig.setSoTimeout(15000);
			
			httpClientBuilder = HttpClients.custom();
			httpClientBuilder.setMaxConnTotal(1);
			httpClientBuilder.setMaxConnPerRoute(1);
			httpClientBuilder.disableCookieManagement();
			httpClientBuilder.setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy(){
				@Override
				public long getKeepAliveDuration(HttpResponse response,HttpContext context) {
					long duration = super.getKeepAliveDuration(response, context);
					if(duration < 0 ) {
						return 12000;
					}
					return duration;
				}
			});
			httpClient = HttpClients.custom().build();
			RequestConfig.Builder config = RequestConfig.copy(RequestConfig.DEFAULT);
			int iasTimeout = 5 * 1000;
			config.setConnectionRequestTimeout(iasTimeout);
			config.setConnectTimeout(6000);
			config.setSocketTimeout(iasTimeout);
			requestConfig = config.build();
		}
		logger.info("RestGateway setupHttpClient() out: ");
	}

}
