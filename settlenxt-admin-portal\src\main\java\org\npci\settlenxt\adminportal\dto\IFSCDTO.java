package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class IFSCDTO {

	private String ifscCode;
	private String ifscDescription;
	private String bankCode;
	private String nfsCode;
	private String savingsAccId;
	private String currAccId;
	private String rtgsAccId;
	private String status;
	private String addEditFlag;
	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private String userType;
	private String requestState;
	private String lastOperation;
	private String checkerComments;
	private String makerComments;
}
