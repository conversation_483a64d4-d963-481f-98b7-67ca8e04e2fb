<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.BinDetailRepository">
	
<select id="getUserBinByUserId" resultType="BinDTO">
            SELECT BIN_ID as binId, PARTICIPANT_ID as participantId, ACTIVATION_DATE as activationDate , DOMAIN_USAGE as domainUsage, 
			DEACTIVATION_DATE as deactivationDate, BANK_GROUP as bankGroup, OFFLINE_ALLOWED as offlineAllowed, BIN_TYP<PERSON> as binType, BIN_NUMBER as binNumber, LOW_BIN as lowBin, HIGH_BIN as highBin, 
			PAN_LENGTH as panLength, <PERSON>IN_CARD_TYPE as binCardType, BIN_PRODUCT_TYPE as binProductType, BIN_CARD_VARIANT as binCardvariant, BIN_CARD_BRAND as binCardBrand, MESSAGE_TYPE as messageType, 
			CARD_TECHNOLOGY as cardTechnology, AUTHENTICATION_MECHANISM as authenticationMechanism, SUB_SCHEME as subScheme, CARD_SUB_VARIANT  as cardSubVariant, PROGRAM_DETAILS as programDetails, 
			FORM_FACTOR as formFactor, FEATURES as features, CREATED_BY as createdBy, CREATED_ON as createdOn, LAST_UPDATED_BY as lastUpdatedBy, LAST_UPDATED_ON as lastUpdatedOn, STATUS as status, 
			MEMBER_ID as memberId, ACQUIRER_ID as acquirerId, PRODUCT_TYPE as productType, SETTLEMENT_BIN as settlementBin FROM MEMBIN_DETAILS WHERE status='A' and  BIN_ID  IN 
			<foreach item='item' index='index' collection='binIdList' open='(' separator=',' close=')'> #{item} </foreach>
</select>	

</mapper>	
