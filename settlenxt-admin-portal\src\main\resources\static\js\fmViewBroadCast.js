$(document).ready(function() {
	var table = $('#tabnew').DataTable();
	$('.editBroadCast').click(function() {

		var a = $(this).data("value");
		var b = a.split(",");
		url = '/editBroadCastMessage';
		data = "mgsid," + b[0] ;
		postData(url, data);
	});

	$('.backAction').click(function() {
		var url = $(this).data("value");
		var data = "";
		postData(url, data);
	});

});
$(document).on("click", ".launch-modal", function() {

	var data = $(this).data('id');
	var array = data.split(",")
	var bankName = array[0];
	var bankRemarks = array[1];
	// alert("modal clicked" + array, bankName, bankRemarks);
	$("#bankName").text(bankName);
	$("#bankRemarks").text(bankRemarks);

	// As pointed out in comments,
	// it is superfluous to have to manually call the modal.
	// $('#addBookDialog').modal('show');
});

function activeDeactiveLkp(mgsid, type) {
	if (type == 'I') {
		url = '/actDevBroadCastMessage';
		status = 'I'
	} else if (type == 'A') {
		url = '/actDevBroadCastMessage';
		status = 'A'
	}
	var data = "mgsid," + mgsid + ",status," + status ;
	postData(url, data);
}