 $(document).ready(function () {
 
 

	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});
 
 
 
 
 
 })
 
 function display() {
		$(".appRejMust").hide();
	}
 
 function submitForm(url) {
     var data = "" ;
    postData(url, data);
}

function approve(url){

var actionCode = $("#actionCode").val(); 
var mccGroup = $("#mccGroup").val(); 
var binCardBrandId = $("#binCardBrandId").val(); 
var binCardTypeId = $("#binCardTypeId").val(); 
var fieldName = $("#fieldName").val(); 
var relOperator = $("#relOperator").val(); 
var fieldValue = $("#fieldValue").val(); 

var remarks=$("#rejectReason").val();

if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				
				 let combined=actionCode+mccGroup+binCardBrandId+binCardTypeId+fieldName+relOperator+fieldValue;
var data = "cappingId," + escape(combined) +",status," + "A" + ",remarks," + remarks;
				postData(url, data);
			} 
			else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} 
		
		
		else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				
				 let combined=actionCode+mccGroup+binCardBrandId+binCardTypeId+fieldName+relOperator+fieldValue;
var dataRej = "cappingId," + escape(combined) +",status," + "R" + ",remarks," + remarks;
				postData(url, dataRej);
			}
			else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		 } else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
			
}



function viewCappingAmountInfo(actionCode,mccGroup,binCardBrandId,binCardTypeId,fieldName,relOperator,fieldValue,action) {

var brandId = binCardBrandId;
var typeId = binCardTypeId;
    var data = "actionCode," + actionCode +  ",mccGroup," + mccGroup +  ",binCardBrandId," + escape(brandId) +  ",binCardTypeId," + escape(typeId) +  ",fieldName," + fieldName +  ",relOperator," + relOperator +  ",fieldValue," + fieldValue ;
    postData(action,data);
}