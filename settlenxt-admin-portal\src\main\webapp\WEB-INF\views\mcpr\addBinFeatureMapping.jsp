<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/addBinFeatureMapping.js" type="text/javascript"></script>

<script type="text/javascript">
var binFeatureMappingValidationMessages={};
binFeatureMappingValidationMessages['participantName']="<spring:message code='binFeatureMapping.participant.validation.msg' javaScriptEscape='true' />";
binFeatureMappingValidationMessages['binNumber']="<spring:message code='binFeatureMapping.bin.validation.msg' javaScriptEscape='true' />";
binFeatureMappingValidationMessages['feeSelectionType']="<spring:message code='binFeatureMapping.featureId.validation.msg' javaScriptEscape='true' />";
binFeatureMappingValidationMessages['fromMonth']="<spring:message code='binFeatureMapping.fromMonth.validation.msg' javaScriptEscape='true' />";
binFeatureMappingValidationMessages['toMonth']="<spring:message code='binFeatureMapping.toMonth.validation.msg' javaScriptEscape='true' />";
binFeatureMappingValidationMessages['fromYear']="<spring:message code='binFeatureMapping.fromYear.validation.msg' javaScriptEscape='true' />";
binFeatureMappingValidationMessages['toYear']="<spring:message code='binFeatureMapping.toYear.validation.msg' javaScriptEscape='true' />";
binFeatureMappingValidationMessages['pastMonthValidation']="<spring:message code='binFeatureMapping.pastMonthValidation.validation.msg' javaScriptEscape='true' />";
binFeatureMappingValidationMessages['dateValidation']="<spring:message code='binFeatureMapping.dateValidation.validation.msg' javaScriptEscape='true' />";
binFeatureMappingValidationMessages['featureId']="<spring:message code='binFeatureMapping.featureId.validation.msg' javaScriptEscape='true' />";
</script>


	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
		<c:if test="${not empty addNew}">
		<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><label><spring:message code="binFeatureMapping.addscreen.title" /></label></span></strong>
			
		</c:if>
		<c:if test="${not empty editBinFeatureMapping}">
		<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><label><spring:message code="binFeatureMapping.editscreen.title" /></label></span></strong>
			
		</c:if>
			
		</div>
		<div id="errvErrorInfo" class="error">
							<span for="ErrorInfo" class="error"><form:errors
									path="ErrorInfo" /></span>
						</div>
		
		<c:if test="${not empty addNew}">
			<c:url value="addBinFeatureMapping" var="submitBinFeatureMappingDetails" />
		</c:if>
		<c:if test="${not empty editBinFeatureMapping}">
			<c:url value="updateBinFeatureMapping" var="submitExclBinFeatureMappingDetails" />
		</c:if>
		<div class="panel-body">
			<form:form onsubmit="return encodeForm(this);" method="POST"
				id="addEditBinFeatureMapping" modelAttribute="binFeatureMappingDTO"
				action="${submitExclBinConfigDetails}" autocomplete="off">
				<br />
				<form:hidden path="binFeatureId" id="binFeatureId" name="binFeatureId" value="${binFeatureMappingDTO.binFeatureId}"/>
				<input type="hidden" id="binFeatureMappingBin" name="binFeatureMappingBin" value="${binFeatureMappingDTO.bin}"/>
				<input type="hidden" id="hFeatureId" name="hFeatureId" value="${binFeatureMappingDTO.featureId}"/>
				
				<input type="hidden" id="deleteBinFeatureMapping" name="hdeleteBinFeatureMapping" value="${deleteBinFeatureMapping}"/>
				
				<input id="hparentPage" type="hidden" value="${parentPage}" />
				<c:if test="${not empty showbutton}">
				<div class="row">
				<div class="col-sm-3">
			
						<div class="form-group">
							<label><spring:message code="binFeatureMapping.participantName" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
								<form:select path="participantName" id="participantName" name="participantName"
									class="form-control" onChange="populateBinData()">
									<form:option value="SELECT" label="SELECT" />
									<form:options 
										items="${participantList}" itemLabel = "participantName" itemValue = "participantId"/>
								</form:select>
							</c:if>
							<c:if test="${not empty editBinFeatureMapping}">
								<form:input path="participantName" id="participantName"
									value="${binFeatureMappingDTO.bankName}" name="participantName" readonly="true"
									maxlength="50" cssClass="form-control medantory" />
							</c:if>
							<div id="errparticipantName">
								<span for="participantName" class="error"><form:errors
										path="participantName" /></span>
							</div>
						</div>
					</div>

					<div class="col-sm-3 ">
						<div class="form-group">
							<label><spring:message code="binFeatureMapping.bin" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
								<form:select path="bin" id="binNumber" name="binNumber"
									class="form-control" onChange="populateFeatureData()" >
									<form:option value="SELECT" label="SELECT" />
									<form:options 
										items="${binList}" itemLabel = "binNumber" itemValue = "binNumber"/>

								</form:select>
							</c:if>
							<c:if test="${not empty editBinFeatureMapping}">
                                
                                <form:input path="bin" id="binNumber"
                                    value="${binFeatureMappingDTO.bin}" name="binNumber" readonly="true"
                                    maxlength="50" cssClass="form-control medantory" />
                           </c:if>

							<div id="errbinNumber">
								<span for="binNumber" class="error"><form:errors
										path="binNumber" /></span>
							</div>
						</div>
					</div>
					
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="binFeatureMapping.fromDate" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
											<form:select id="fromMonth" name="fromMonth" 
												path="fromMonth" class="form-control medantory" onChange="populateFeatureData()" >
												<form:option value="SELECT" label="SELECT" />
												<form:options items="${beginningMonthList}" itemLabel="description" itemValue="code"/>
											</form:select>
										</c:if>
										<c:if test="${not empty editBinFeatureMapping}">
										
										<form:select path="fromMonth" id="fromMonth" name="fromMonth"
                            maxlength="10"  value="${binFeatureMappingDTO.fromMonth}"
                            cssClass="form-control medantory">
                            <form:options items="${beginningMonthList}"  itemValue="code" itemLabel="description"/>                      
                        </form:select>	
										</c:if>
							<div id="errfromMonth">
								<span for="fromMonth" class="error"><form:errors
										path="fromMonth" /></span>
							</div>
							

							
						</div>
					</div>
					
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="binFeatureMapping.year" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
											<form:select id="fromYear" name="fromYear" width ="50%"
												path="fromYear" class="form-control medantory" onChange="populateFeatureData()" >
												<form:option value="SELECT" label="SELECT" />
												<form:options items="${yearList}" />
											</form:select>
										</c:if>
										<c:if test="${not empty editBinFeatureMapping}">
											<form:select id="fromYear" name="fromYear" 
											path="fromYear" class="form-control medantory" >
												<form:option value="" label="SELECT" />
												<form:option value="${binFeatureMappingDTO.fromYear}">
									</form:option>
									<form:options items="${yearList}" />
											</form:select>

										</c:if>
							
							<div id="errfromYear">
								<span for="fromYear" class="error" style="color:red;"><form:errors
										path="fromYear" /></span>
							</div>

							
						</div>
					</div>
					</div>
					<div class="row">
					<div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="binFeatureMapping.toDate" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
											<form:select id="toMonth" name="toMonth" width ="50%"
												path="toMonth" class="form-control medantory" onChange="populateFeatureData()" >
												<form:option value="SELECT" label="SELECT" />
												<form:options items="${endingMonthList}" itemLabel="description" itemValue="code" />
											</form:select>
										</c:if>
										<c:if test="${not empty editBinFeatureMapping}">
											<form:select id="toMonth" name="toMonth" 
												path="toMonth" class="form-control medantory"  value="${binFeatureMappingDTO.fromMonth}">
												<form:option value="SELECT" label="SELECT" />
												<form:options items="${endingMonthList}" itemLabel="description" itemValue="code" />
											</form:select>
										
										</c:if>
									<div id="errtoMonth">
										<span for="toMonth" class="error"><form:errors
										path="toMonth" /></span>
									</div>
							

							
						</div>
					</div>
				
					

			 <div class="col-sm-3">
						<div class="form-group">
							<label><spring:message code="binFeatureMapping.year" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}">
											<form:select id="toYear" name="toYear" 
											path="toYear" class="form-control medantory" onChange="populateFeatureData()" >
									<form:option value="SELECT" label="SELECT" />
								    <form:options items="${yearList}" />
											</form:select>
								</c:if>
										<c:if test="${not empty editBinFeatureMapping}">
											<form:select id="toYear" name="toYear" 
											path="toYear" class="form-control medantory" value="${binFeatureMappingDTO.toYear}">
												<form:option value="SELECT" label="SELECT" />
									<form:options items="${yearList}" />
											</form:select>

										</c:if>
							<div id="errtoYear">
								<span for="toYear" class="error" ><form:errors
										path="toYear" /></span>
							</div>
						</div>
					</div> 
					<div class="col-sm-3 ">
						<div class="form-group">
							<label><spring:message code="binFeatureMapping.binFeatureId" /><span
								style="color: red">*</span></label>
							<c:if test="${not empty addNew}"> 
											<form:select id="featureId" name="featureName"
												path="featureId" class="form-control medantory">
												<form:option value="0" label="SELECT" />
											</form:select>
										</c:if>
										<c:if test="${not empty editBinFeatureMapping}">
												<form:select id="featureId" name="featureName"
												 value="${binFeatureMappingDTO.featureId}" path="featureId" class="form-control medantory">
												<form:option value="0" label="SELECT" />
											</form:select>
										</c:if>
										<div id="errfeatureId">
								<span for="featureId" class="error"><form:errors
										path="featureId" /></span>
							</div>
						</div>					
						</div>
				</div>	
					</c:if>
				<c:if test="${empty showbutton}">
							<div class="row">
						<div class="col-sm-12">
							<div class="panel panel-default no_margin">
								<div class="panel-body">
									<table class="table table-striped" style="font-size: 12px">
									<caption style="display:none;">Bin Feature Mapping</caption>
									<thead style="display:none;"><th scope = "col"></th></thead>
										<tbody>
								<tr>
									<td><label><spring:message code="binFeatureMapping.participantName" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.bankName}</td>
									<td><label><spring:message code="binFeatureMapping.baseorfeature" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.featureName}</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="binFeatureMapping.bin" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.bin}</td>
									<td><label><spring:message code="binFeatureMapping.fromDate" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.fromDate}</td>
									<td><label><spring:message code="binFeatureMapping.toDate" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.toDate}</td>
								</tr>
							</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</c:if>
				

				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center">
						<c:if test="${not empty addNew}">
						<c:if test="${not empty addReset}">
						<button type="button" value="Submit" class="btn btn-success"
									onclick="resetAction();">
									<spring:message code="binFeatureMapping.reset" />
								</button>
					     </c:if>
							<c:if test="${not empty showbutton}">
								<button type="button" id="BAddSubmit" value="Submit" class="btn btn-success"
									onclick="validateAddEditForm('A');">
									<spring:message code="msg.lbl.submit" />
								</button>
							</c:if>
						</c:if>
						
						<sec:authorize access="hasAuthority('Edit Bin Exclusion Config')">
						<c:if test="${not empty editBinFeatureMapping}">
							<c:if test="${not empty showbutton}">
								<c:if test="${empty deleteBinFeatureMapping}">
									<button type="button" id="BEditSubmit" value="Submit" class="btn btn-success"
										onclick="validateAddEditForm('E');">
										<spring:message code="msg.lbl.update" />
									</button>
								</c:if>
								<c:if test="${not empty deleteBinFeatureMapping}">
									<button type="button" id="BEditSubmit" value="Submit" class="btn btn-success"
										onclick="deleteForm();">
										<spring:message code="msg.lbl.submit" />
									</button>
								</c:if>
							</c:if>
						</c:if>
							
						<c:if test="${binFeatureMappingDTO.requestState eq 'R'and not empty showbutton}">
							<button type="button" class="btn btn-danger"
							onclick="postDiscardBinAction('/discardRejectedBinFeatureMapping');">
							<spring:message code="binFeatureMapping.discardBtn" /></button>
						
						<button type="button" class="btn btn-danger"
							onclick="userAction1('N','/binFeatureMappingPendingForApproval');">
							<spring:message code="binFeatureMapping.backBtn" /></button>
						</c:if>
						</sec:authorize>
						
						<c:if test="${binFeatureMappingDTO.requestState  ne 'R'}">
						<c:if test="${parentPage  eq 'approvalTab'}">
							<button type="button" class="btn btn-danger"
							onclick="userAction1('N','/binFeatureMappingPendingForApproval');">
							<spring:message code="binFeatureMapping.backBtn" /></button>
						</c:if>
						<c:if test="${parentPage  ne 'approvalTab'}">
							<button type="button" class="btn btn-danger"
							onclick="userAction1('N','/showMemBinFeatureMapping');">
							<spring:message code="binFeatureMapping.backBtn" /></button>
						</c:if>
								
						</c:if>
					</div>
				</div>
			</form:form>
				</div>
		</div>

