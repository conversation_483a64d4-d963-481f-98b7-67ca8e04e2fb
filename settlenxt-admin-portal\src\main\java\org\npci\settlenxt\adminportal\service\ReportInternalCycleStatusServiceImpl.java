package org.npci.settlenxt.adminportal.service;

import java.util.ArrayList;
import java.util.Collections;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.DateUtils;
import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.gateway.RestGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

/**
 * <li>This service gives response to api called from ui screen. It is used for
 * getting all report internal cycle status</li>
 * 
 * <AUTHOR>
 *
 */
@Service
@PropertySource("/application-dev.properties")
public class ReportInternalCycleStatusServiceImpl implements IReportInternalCycleStatusService {

	private static final Logger logger = LogManager.getLogger(ReportInternalCycleStatusServiceImpl.class);

	@Autowired
	private Environment environment;

	@Autowired
	RestGateway restGateway;

	/**
	 * <li>This method is received data from UI screen and prepare request and send
	 * request to the reportOrchestation service for getting all details of report
	 * internal cycle status</li>
	 */
	@Override
	public CycleManagementDTO getReportInternalCycleStatus(CycleManagementDTO cycleManagementDTO) {
		if (StringUtils.isBlank(cycleManagementDTO.getSystemDate())) {
			String formattedRequestDate = DateUtils.getTodayLocalDate(DateUtils.YYYY_MM_DD);
			cycleManagementDTO.setSystemDate(formattedRequestDate);
		}
		if (StringUtils.isBlank(cycleManagementDTO.getReportStatus())) {
			cycleManagementDTO.setReportStatus("ALL");
		}
		try {
			JsonObject requestBody = new JsonObject();
			requestBody.addProperty(CommonConstants.SYSTEM_DATE, cycleManagementDTO.getSystemDate());
			requestBody.addProperty(CommonConstants.REPORT_STATUS, cycleManagementDTO.getReportStatus());
			requestBody.addProperty(CommonConstants.ICN_TYPE, CommonConstants.ICN);
			String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
					+ CommonConstants.SETTLENXT_FETCH_REPORCESS_ICN_DATA;
			List<Map<String, String>> cycleDetails = new ArrayList<>();
			String resultCycleData = restGateway.postAPIForJsonObject(url, requestBody);
			logger.info("Response of report internal cycle status is {}", resultCycleData);
			if (StringUtils.isBlank(resultCycleData)) {
				cycleManagementDTO.setCycleData(cycleDetails);
				return cycleManagementDTO;
			}
			JsonObject respBody = (JsonObject) JsonParser.parseString(resultCycleData);
			JsonArray cycleData = respBody.getAsJsonArray(CommonConstants.DEFFERED_INTERNAL_CYCLE);
			cycleDetails = new Gson().fromJson(cycleData, ArrayList.class);
			

		Collections.sort(cycleDetails, (o1,o2) ->  o1.get(CommonConstants.VIEW_PRIORITY).compareTo(o2.get(CommonConstants.VIEW_PRIORITY)));
		
		
			
			cycleManagementDTO.setCycleData(cycleDetails);
		} catch (Exception e1) {
			logger.error("Error occured while getting report cycle status {}", e1.getMessage(), e1);
		}
		return cycleManagementDTO;
	}

	/**
	 * This method is implemented to modify the cycle
	 * status(Retry/ForceClose/ForceMerge).
	 */
	@Override
	public String updateCycleStatus(CycleManagementDTO cycleManagementDTO) {
		String result = "";
		if (StringUtils.isBlank(cycleManagementDTO.getForceOperation())) {
			cycleManagementDTO.setForceOperation(CommonConstants.RETRY);
		}
		try {
			String[] icnArray = cycleManagementDTO.getInternalCycleNumber().split(",");
			JsonArray icnNumber = new Gson().toJsonTree(icnArray).getAsJsonArray();
			JsonObject requestBody = new JsonObject();
			requestBody.addProperty(CommonConstants.SYSTEM_DATE, cycleManagementDTO.getSystemDate());
			requestBody.add(CommonConstants.INTERNAL_CYCLE_NUMBER, icnNumber);
			requestBody.addProperty(CommonConstants.FORCE_OPERATION, cycleManagementDTO.getForceOperation());
			String url = environment.getProperty(CommonConstants.REPORT_ORCHESTRATION_DOMAIN)
					+ CommonConstants.SETTLENXT_UPDATE_CYCLE_STATUS;
			result = restGateway.postAPIForJsonObject(url, requestBody);
			logger.info("Response of update cycle status for retry,force close and force merge {}", result);
		} catch (Exception e1) {
			logger.error("Error occured while getting report cycle status {}", e1.getMessage(), e1);
		}
		return result;
	}

}
