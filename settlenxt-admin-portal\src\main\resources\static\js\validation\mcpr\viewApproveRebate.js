	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	
	}
	function backAction(type, action,originPage) {
		var url = action;
		var data = "userType," + type + ",originPage," + originPage ;
		postData(url, data);
	}
	
	function postAction(url,originPage) {
		var rebateId;
		var crtuser;
		var remarks;
		
		var data;
		
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 rebateId = $("#rebateId").val();
				 crtuser = $("#crtuser").val();
				 remarks=$("#rejectReason").val();
		
				 url = '/approveRebateStatus';
				 data = "rebateId," + escape(rebateId) + ",status," +escape("Approved") + ",crtuser,"
						+ escape(crtuser) + ",remarks," + escape(remarks) + ",originPage," + escape(originPage) ;
			
				
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				postRejectedData(rebateId, crtuser, remarks, url, data, originPage);
	
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		


function postRejectedData(rebateId, crtuser, remarks, url, data, originPage) {
    if ('${requestInfo.requestName}' == 'ROLE FUNCTIONALITY MAP') {
        rebateId = $("#rebateId").val();

        crtuser = $("#crtuser").val();
        remarks = $("#rejectReason").val();
        url = '/rejectRebateStatus';

        data = "rebateId," + escape(rebateId) + ",status," + escape("Rejected") + ",crtuser," + escape(crtuser) + ",remarks," + escape(remarks) + ",originPage," + escape(originPage);

        postData(url, data);
    }
    else {
        rebateId = $("#rebateId").val();
        crtuser = $("#crtuser").val();
        remarks = $("#rejectReason").val();
        url = '/approveRebateStatus';

        data = "rebateId," + escape(rebateId) + ",status," + escape("Rejected") + ",crtuser," + escape(crtuser) + ",remarks," + escape(remarks) + ",originPage," + escape(originPage);
        postData(url, data);
    }
    
}
