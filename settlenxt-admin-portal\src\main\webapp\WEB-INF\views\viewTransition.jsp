<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script src="./static/js/validation/viewApproveTransition.js"
	type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">
		<div class="alert alert-danger appRejMust" role="alert">
			<spring:message code="dispute.transition.apprejecterrormsg" />
		</div>
		<div class="alert alert-danger remarkMust" role="alert">
			<spring:message code="dispute.transition.remarkserror" />
		</div>
		<form:form onsubmit="removeSpace(this); encodeForm(this);"
			id="viewApproveTransition" modelAttribute="disputeTransitionDTO"
			autocomplete="off">

			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<sec:authorize
								access="hasAuthority('View Dispute Transition Rules')">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"></span> <spring:message
										code="dispute.transition.view" /></strong>
							</sec:authorize>
						</div>

						<div class="panel-body">

							<input type="hidden" id="seqid" name="seqid"
								value="${disputeTransitionDTO.id}">

							<c:if test="${not empty disputeTransitionDTO}">
								<table class="table table-striped " style="font-size: 12px"
									aria-labelledby="To display dispute transition rule">
									<thead style="display:none;"><th scope="col"></th></thead>

									<tbody>
										<tr>
											<td colspan="6">
												<div class="panel-heading-red clearfix">
													<strong><span
														class="glyphicon glyphicon-info-sign"></span> <span
														data-i18n="Data"></span> <spring:message
															code="dispute.transition.mainTab.title" /></strong>
												</div>
											</td>
										</tr>
										<tr>

										</tr>

										<tr>
											<td><label><spring:message
														code="dispute.transition.entity" /><span
													style="color: red"></span></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.entityDesc}">N/A</c:if>${disputeTransitionDTO.entityDesc}</td>
											<td><label><spring:message
														code="dispute.transition.currState" /><span
													style="color: red"></span></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.currStateDesc}">N/A</c:if>${disputeTransitionDTO.currStateDesc}</td>
											<td><label><spring:message
														code="dispute.transition.toState" /><span
													style="color: red"></span></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.toStateDesc}">N/A</c:if>${disputeTransitionDTO.toStateDesc}</td>

										</tr>
										<tr>
											<td><label><spring:message
														code="dispute.transition.fieldName" /><span
													style="color: red"></span></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.fieldNameDesc}">N/A</c:if>${disputeTransitionDTO.fieldNameDesc}</td>
											<td><label><spring:message
														code="dispute.transition.secFieldName" /></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.secFieldNameDesc}">N/A</c:if>${disputeTransitionDTO.secFieldNameDesc}</td>
											<td><label><spring:message
														code="dispute.transition.relationalOp" /><span
													style="color: red"></span></label></td>
											<td><c:if test="${empty disputeTransitionDTO.relOpDesc}">N/A</c:if>${disputeTransitionDTO.relOpDesc}</td>
										</tr>
										<tr>
											<td><label><spring:message
														code="dispute.transition.operator" /><span
													style="color: red"></span></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.operatorDesc}">N/A</c:if>${disputeTransitionDTO.operatorDesc}</td>
											<td><label><spring:message
														code="dispute.transition.fieldVal" /><span
													style="color: red"></span></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.fieldValue}">N/A</c:if>${disputeTransitionDTO.fieldValue}</td>
											<td><label><spring:message
														code="dispute.transition.fieldOp" /></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.fieldOperatorDesc}">N/A</c:if>${disputeTransitionDTO.fieldOperatorDesc}</td>
										</tr>
										<tr>
											<td><label><spring:message
														code="dispute.transition.createdBy" /><span
													style="color: red"></span></label></td>
											<td><c:if test="${empty disputeTransitionDTO.createdBy}">N/A</c:if>${disputeTransitionDTO.createdBy}</td>
											<td><label><spring:message
														code="dispute.transition.createdOn" /><span
													style="color: red"></span></label></td>
											<td><c:if test="${empty disputeTransitionDTO.createdOn}">N/A</c:if>${disputeTransitionDTO.createdOn}</td>
											<td><label><spring:message
														code="dispute.transition.lastUpdatedBy" /><span
													style="color: red"></span></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.lastUpdatedBy}">N/A</c:if>${disputeTransitionDTO.lastUpdatedBy}</td>
										</tr>
										<tr>
											<td><label><spring:message
														code="dispute.transition.lastUpdatedOn" /><span
													style="color: red"></span></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.lastUpdatedOn}">N/A</c:if>${disputeTransitionDTO.lastUpdatedOn}</td>
											<td><label><spring:message
														code="dispute.transition.checkerComment" /><span
													style="color: red"></span></label></td>
											<td><c:if
													test="${empty disputeTransitionDTO.checkerComments}">N/A</c:if>${disputeTransitionDTO.checkerComments}</td>
											<td></td>
											<td></td>
										</tr>
										<sec:authorize
											access="hasAuthority('Approve Dispute Transition Rules')">
											<c:if test="${approveOrPending eq 'P'}">
												<tr>
													<td colspan="6"><div
															class="panel-heading-red  clearfix">
															<strong><span
																class="glyphicon glyphicon-info-sign"></span> <span
																data-i18n="Data"><spring:message
																		code="AM.lbl.reqInfo" /></span></strong>
														</div></td>
												</tr>
												<tr>
													<td><label><spring:message
																code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
													<td><select name="select" id="apprej"
														onchange="display()">
															<option value="N"><spring:message
																	code="AM.lbl.select" /></option>
															<option value="A" id="approve"><spring:message
																	code="AM.lbl.approve" /></option>
															<option value="R" id="reject"><spring:message
																	code="AM.lbl.reject" /></option>
													</select></td>
													<td>
														<div style="display: flex; justify-content: center;">
															<label><spring:message code="AM.lbl.remarks" /><span
																style="color: red">*</span></label>
														</div>
													</td>
													<td colspan="4"><textarea rows="4" cols="50"
															maxlength="100" id="rejectReason"></textarea>
														<div id="errorrejectReason" class="error"></div></td>
												</tr>
											</c:if>
										</sec:authorize>
									</tbody>
								</table>
							</c:if>

							<div class="row">
								<div class="col-sm-12 bottom_space">
									<hr />
									<div style="display: flex; justify-content: center;">
										<c:if test="${showEditDiscardBtn eq 'Y'}">
											<sec:authorize
												access="hasAuthority('Edit Dispute Transition Rules')">
												<c:if
													test="${not empty disputeTransitionDTO and ((disputeTransitionDTO.requestState eq 'A' and makChkFlag eq 'MAKER') or (disputeTransitionDTO.requestState eq 'R' and makChkFlag eq 'MAKER'))}">
													<button type="button" class="btn btn-success"
														id="editTransition"
														onclick="editTransRule('/forwardTransition');">
														<spring:message code="dispute.transition.editBtn" />
													</button>
												</c:if>
												<c:if
													test="${not empty disputeTransitionDTO and disputeTransitionDTO.requestState eq 'R' and makChkFlag eq 'MAKER'}">
													<button type="button" class="btn btn-success"
														id="discardTransition"
														onclick="editTransRule('/discardTransition');">
														<spring:message code="dispute.transition.discardBtn" />
													</button>
												</c:if>
											</sec:authorize>
										</c:if>

										<sec:authorize
											access="hasAuthority('Approve Dispute Transition Rules')">
											<c:if test="${approveOrPending eq 'P'}">
												<button type="button" class="btn btn-success"
													id="submitTransition" onclick="appRejTrans();">
													<spring:message code="dispute.transition.submitBtn" />
												</button>
											</c:if>
										</sec:authorize>

										<c:choose>
											<c:when test="${approveOrPending eq 'A'}">
												<input type="button" id="backTransition" value="Back"
													onclick="navigateTo('/showTransitionRules');"
													class="btn btn-danger"></input>
											</c:when>
											<c:otherwise>
												<input type="button" id="backTransition1" value="Back"
													onclick="navigateTo('/showPendingTransitionRules');"
													class="btn btn-danger"></input>
											</c:otherwise>
										</c:choose>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</form:form>
	</div>
</div>
