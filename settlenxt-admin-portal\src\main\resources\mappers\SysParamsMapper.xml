<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="org.npci.settlenxt.adminportal.service.SysParamsMapper">

	<select id="getSysParams" resultType="org.npci.settlenxt.common.dto.SystemParams">
		SELECT 
			sys_type as sysType,
			sys_key as sysKey,
			sys_value as sysValue,
			description as description
		FROM 
			sys_params
	</select>
	
	<select id="getByType" resultType="org.npci.settlenxt.common.dto.SystemParams">
		SELECT 
			sys_type as sysType,
			sys_key as sysKey,
			sys_value as sysValue,
			description as description
		FROM 
			sys_params 
		WHERE 
			sys_type = #{type}
	</select>
	
</mapper>
