var requestStateFlag;
$(document).ready(function (_event) {
	requestStateFlag=$('#requestState').val();
	$('#submitMemberData').attr('disabled', true);
	$('#binNumber').prop('disabled', true);
	if ($('#requestState').val() == 'SAVE' || $('#requestState').val() == 'EDIT' || $('#requestState').val() == 'ADD' || $('#requestState').val() == 'SUBMIT') {
		$('#afterSave').show();
		$('#submitMemberData').attr('disabled', false);

	}
	setBkgColorAndValues();
	
	if(document.getElementById("networkUsed").value=='Y'){
		
		$('#bankSectorDiv').hide();
		$('#uniqueBnkNameDiv').hide()
		$('#uniqueBnkDiv').hide()
		$('#cardSubVariantDiv').hide()
		$('#programDetailsDiv').hide()
		$('#formFactorDiv').hide()
		$('#cardSubVariant').hide()
		$('#programDetailsDiv').hide()
		$('#featureMultiples').hide()
		$('#issProductTypeDiv').hide()
		$('#bankMasterCode').prop("readonly",true);
		$('#rtgsCode').prop("readonly",true);
		$('#participantId').prop("readonly",true);
		$('#savingsAccNumber').prop("readonly",true);
		disableFiledOnFlag();
		
	if($('#memberType').val()==codeForNetwork.value){
			$('#settlementPanel').show();
			$('#gstDiv').hide();
		
		}
		else {
			$('#settlementPanel').hide();
			$('#currencyConversionBy').val(0);
			$('#isType').val('0');
			$('#currencyConversionType').val(0);
			$('#forexId').val(0);
			$('#gstDiv').show();
			
		}
		
		
		  		  $('#currencyConversionBy').on('keyup keypress blur change',function () {
			   	if ($('#currencyConversionBy').val() == undefined
		|| $('#currencyConversionBy').val() == '0') {
		hasErrors = true;
		$("#errcurrencyConv").find('.error').html("Please select Currency Conversion");
		$('#errcurrencyConv').show();
	} else {
		$('#errcurrencyConv').hide();
	}
		  });
		  
		   $('#isType').on('keyup keypress blur change',function () {
			   	
		  if ($('#isType').val() == undefined
		|| $('#isType').val() == '0') {
		hasErrors = true;
		$("#errisType").find('.error').html("Please select NPCI Settlement");
		$('#errisType').show();
	} else {
		$('#errisType').hide();
	}
   });
 
 
  $('#currencyConversionType').on('keyup keypress blur change',function () {
			   	
		  if ($('#currencyConversionType').val() == undefined
		|| $('#currencyConversionType').val() == '0') {
		hasErrors = true;
		$("#errcurrencyConvType").find('.error').html("Please select Currency Conversion Type");
		$(errcurrencyConvType).show();
	} else {
		$('#errcurrencyConvType').hide();
	}
   });
 
 
  $('#forexId').on('keyup keypress blur change',function () {
			   	
			if ($('#forexId').val() == undefined
		|| $('#forexId').val() == '0') {
		hasErrors = true;
		$("#errforexId").find('.error').html("Please select Forex Id");
		$('#errforexId').show();
	} else {
		$('#errforexId').hide();
	}	
				});
 
		 $('#markUp').on('keyup keypress blur change',function () {
 			  validateFromCommonVal('markUp', true, "ZeroToHundredWithTwoDecimal", "", false);
 			  
 			  
 
 });
 
   $('#networkLicenseId').on('keyup keypress blur change',function () {
 			  validateFromCommonVal('networkLicenseId', false, "NumericsOnly", "6", true);
 			  });
 			  
 			   $('#networkIssId').on('keyup keypress blur change',function () {
 			  validateFromCommonVal('networkIssId', false, "NumericsOnly", "6", true);
 			  });
	}
		
	bindFieldValidationsOnLoad();
	
	 $('#memberType').on('keyup keypress blur change',function (){
		if($('#memberType').val()==codeForNetwork.value&&document.getElementById("networkUsed").value=='Y'){
			$('#settlementPanel').show();
			$('#gstDiv').hide();
		$('#bankMasterCode').prop("readonly",false);
		$('#rtgsCode').prop("readonly",false);
		$('#participantId').prop("readonly",false);
		$('#savingsAccNumber').prop("readonly",false);
		$('#currentAccNumber').prop("readonly",false);
		}
		else if(document.getElementById("networkUsed").value=='Y'){
			$('#settlementPanel').hide();
			$('#currencyConversionBy').val(0);
			$('#isType').val('0');
			$('#currencyConversionType').val(0);
			$('#forexId').val(0);
			$('#gstDiv').show();
		$('#bankMasterCode').prop("readonly",true);
		$('#rtgsCode').prop("readonly",true);
		$('#participantId').prop("readonly",true);
		$('#savingsAccNumber').prop("readonly",true);
		$('#currentAccNumber').prop("readonly",true);
		}
	});
		

	$('#parentParticipantId').change(
		function () {
			var parentParticipantId = $("#parentParticipantId").val();
			$("#ifscCode").val(bankIFSCMapping[parentParticipantId]);
			$('#ifscCode').trigger('change');
			$('#ifscCode').attr('disabled', true);
		});

	if ($('#requestState').val() != "") {
		$('#memberType').attr('disabled', true);
		$('#parentParticipantId').attr('disabled', true);
		$('#ifscCode').attr('disabled', true);
		$('#rtgsCode').attr('disabled', true);
		$('#participantId').attr('disabled', true);
		$('#savingsAccNumber').attr('disabled', true);
		$('#currentAccNumber').attr('disabled', true);
	}

	$(".collapse").on('show.bs.collapse', function () {
		$(this).parent().find(".glyphicon").removeClass("glyphicon-plus").addClass("glyphicon-minus");
	}).on('hide.bs.collapse', function () {
		$(this).parent().find(".glyphicon").removeClass("glyphicon-minus").addClass("glyphicon-plus");
	});

	bindDateValidationsOnload();
	
		if (submemberBankType.indexOf($('#memberType').val()) != -1) {
			$('#parentMemIdDiv').show();
		}
		else {
			$('#parentMemIdDiv').hide();
		}
	

	$('#addEditMember').on('keyup change paste', 'input, select, textarea', function () {
		$(this).closest('#addEditMember').data('changed', true);
	});
	if ($('#success').val() == 'successStatus') {
		$("input").prop('disabled', true);
		$("select").prop('disabled', true);

	}
	$('#cntState').change(function () { getCity('cntState', 'cntCity'); });
	$('#gstState').change(function () { getCity('gstState', 'gstCity'); });
	$('#ifscCode').change(function () { 
		if(document.getElementById("networkUsed").value!='Y' || $('#memberType').val()!=codeForNetwork.value){
			getIFSCDetails(); }
			$('#SettlementIFSCCode').html('<label for="squareSelect">'+$('#ifscCode').val()+'</label>');
	});
	$('#bnkState').change(function () { getCity('bnkState', 'bnkCity'); });
	bindSettlementBinOnloadFunctions();
	bindAcquirerBinOnloadFunctions();
	bindIssuerBinOnloadFunctions();

	$("#memberType").change(function () {
		handleMemberType();

		$('#parentParticipantId').val("0")
		$('#memberName').val("")
		$('#ifscCode').val("SELECT")
		$('#bankSector').val("0")
		$('#bankMasterCode').val("")
		$('#rtgsCode').val("")
		$('#participantId').val("")
		$('#savingsAccNumber').val("")
		$('#currentAccNumber').val("")
		$('#uniqueBnkName').val("SELECT")
		$('#uniqueBnk').val("")
		$('#participantIdNFS').val("")

	});

	hideErrorDivOnDataChange('parentParticipantId', '0');
	hideErrorDivOnDataChange('ifscCode', '0');
	hideErrorDivOnDataChange('bankSector', '0');
	hideErrorDivOnDataChange('addressType', '0');
	hideErrorDivOnDataChange('bnkCity', '0');
	hideErrorDivOnDataChange('cntCity', '0');
	hideErrorDivOnDataChange('gstCity', '0');
	hideErrorDivOnDataChange('currencyCode', '0');
	hideErrorDivOnDataChange('acqBankGroup', '0');
	hideErrorDivOnDataChange('acqDomainUsage', '0');
	hideErrorDivOnDataChange('acqProductType', '0');
	hideErrorDivOnDataChange('acqSettlementBin', '0');
	hideErrorDivOnDataChange('issSettlementBin', '0');
	hideErrorDivOnDataChange('issBankGroup', '0');
	hideErrorDivOnDataChange('binCardType', '0');
	hideErrorDivOnDataChange('binProductType', '0');
	hideErrorDivOnDataChange('binCardVariant', '0');
	hideErrorDivOnDataChange('binCardVariant', '0');
	hideErrorDivOnDataChange('binCardBrand', '0');
	hideErrorDivOnDataChange('issDomainUsage', '0');
	hideErrorDivOnDataChange('messageType', '0');
	hideErrorDivOnDataChange('cardTechnology', '0');
	hideErrorDivOnDataChange('authMechanism', '0');
	hideErrorDivOnDataChange('issProductType', '0');
	hideErrorDivOnDataChange('subScheme', '0');
	hideErrorDivOnDataChange('cardSubVariant', '0');
	hideErrorDivOnDataChange('programDetails', '0');
	hideErrorDivOnDataChange('formFactor', '0');
	$('#saveMemberData').click(function () {
		saveMemberDetails();
	});


	$('#submitMemberData').click(function () {
		submitForApproval();
	});


	$("input").change(function () {
		$('.submitBtn').attr('disabled', false);
	});

//$(':checkbox').change(function () 
	$(':input[type="checkbox"]').change(function ()
	{
		$('.submitBtn').attr('disabled', false);
	});


	$('#discardData').click(function () {
		document.querySelector(".button").disabled = true;
		discardMemberData();
	});

	$('#uniqueBnkName').change(function () {
		if ($('#uniqueBnkName').val() == 1) {
			$('#uniqueBnk').val("");
			$('#uniqueBnkDiv').show();
			$('#erruniqueBnkName').hide();
			$('#uniqueBnk').attr('readonly', false);
			$('#erruniqueBnk').show();
		} else {
			
			$('#uniqueBnk').attr('readonly', true);
			$('#uniqueBnk').val($('#uniqueBnkName').val());
			$('#erruniqueBnk').hide();
		}

	});

	$('#clearMemData').click(function () {
		clearSavedMemberData();
	});

	$('#clearAddMemData').click(function () {
		clearNewMemberData();
	});


	$('#clearMemContactData').click(function () {
		clearMemberContactData();
	});
	$(".appRejMust").hide();
	$(".remarkMust").hide();
	
	$('#checkAllDocument').click(function () {
		   var checkBoxElements=document.getElementsByName("selectMemberDocument");
		   for(var i of checkBoxElements){
		   	i.checked=$('#checkAllDocument').prop("checked");   	
		    }
		    $('#downloadFiles').attr('disabled',!$('#checkAllDocument').prop("checked"))
	});
	disableSave();
	if($('#recStatus').val() == "A" || $('#recStatus').val() == "R")
	{
		$('#submitMemberData').attr('disabled', true);
	}

	if($('#reqType').val()=="ADD")
	{
		$('#subNet').val($("#subNetDefaultValue").val());
	}
	
	      $('#issBankGroup').selectize({
	          sortField: 'text'
	      });
	      
	      $('#acqBankGroup').selectize({
	          sortField: 'text'
	      });
	      
 	$("#settlementBinId").on('keyup keypress blur change', function () {
        validateSettBinId('settlementBinId','errsettlementBinId');
    });
});

function disableFiledOnFlag(){
	if(document.getElementById('domesticFlagParticipant')&&document.getElementById('domesticFlagParticipant').value=='Y'){
		$('.disableFlag').prop('disabled',true)
	}
}
function handleMemberType() {
	if ($('#memberType').val() != '0') {
		$('#errmemberType').hide();
	}
	if (submemberBankType.indexOf($('#memberType').val()) == -1 || $('#memberType').val() == '0') {
		$('#ifscCode').attr('disabled', false);
		$('#parentMemIdDiv').hide();
	}
	else {
		$('#ifscCode').attr('disabled', true);
		$('#parentMemIdDiv').show();
	}
}

function setBkgColorAndValues() {
	const acqFrmDate = document.getElementById('acqFrmDate');
	if (acqFrmDate)
		acqFrmDate.style.backgroundColor = 'white';

	const acqToDate = document.getElementById('acqToDate');
	if (acqToDate)
		acqToDate.style.backgroundColor = 'white';

	const issFrmDate = document.getElementById('issFrmDate');
	if (issFrmDate)
		issFrmDate.style.backgroundColor = 'white';

	const issToDate = document.getElementById('issToDate');
	if (issToDate)
		issToDate.style.backgroundColor = 'white';
}

function supDocDownload() {
	var url = "/supDocFileDownload";
var data =  "supfilePath," + $("#supfilePath").val() + $("#supfileName").val();
	postData(url, data);
}

function docDownload(fileName) {
	var url = "/supDocFileDownload";
var data =  "supfilePath," + $("#supfilePath").val() + fileName;
	postData(url, data);
}

function userAction(_type, action) {
	var data = "";
	postData(action, data);
}
 


function openConfirmDialog(message, action, data) {
 if(dataSubmittedApproval){
  return;
 }
	$('<div></div>').appendTo('body')
		.html('<div><h6>' + message + '</h6></div>')
		.dialog({
			modal: true,
			title: 'Confirm',
			zIndex: 10000,
			autoOpen: true,
			width: 'auto',
			resizable: false,
			buttons: {
				Yes: function () {
					$(this).dialog("close");
					action(data);
					
				},
				No: function () {
					$(this).dialog("close");
				}
			},
			close: function (_event, _ui) {
				$(this).remove();
			}
		});
}

function openInformationDialog(message, titleData) {
	$('<div></div>').appendTo('body')
		.html('<div><h6>' + message + '</h6></div>')
		.dialog({
			modal: true,
			title: titleData,
			zIndex: 10000,
			autoOpen: true,
			width: 'auto',
			resizable: false,
			buttons: {
				Ok: function () {
					$(this).dialog("close");
					
				}
			},
			close: function (_event, _ui) {
				$(this).remove();
			}
		});
}


function clearSavedMemberData() {
	$(".error").each(function () {
		handleText(this);
	});

	if (!($('#requestState').val() == 'SAVE' || $('#requestState').val() == 'EDIT' || $('#requestState').val() == 'ADD' || $('#requestState').val() == 'SUBMIT')) {
		$('#parentParticipantId').val("0")
		$('#memberName').val("")
		$('#bankSector').val("SELECT")
		$('#uniqueBnk').val("")
		$('#participantIdNFS').val("")
		$('#uniqueBnkName').val("SELECT")
	}

	$('#bnkPhone').val("")
	$('#bnkPhone2').val("")
	$('#bnkMobile').val("")
	$('#bnkMobile2').val("")
	$('#bnkEmail').val("")
	$('#bnkEmail2').val("")
	$('#bnkAdd').val("")
	$('#bnkState').val("0")
	$('#bnkCity').val("0")
	$('#bnkPincode').val("")

	$('#gstn').val("")
	$('#gstAdd').val("")
	$('#gstState').val("0")
	$('#gstCity').val("0")
	$('#gstPincode').val("")
	$('#webSite').val("")
	$('#bnkCountry').val("0")

	$('#gstCountry').val("0")
}
function clearNewMemberData() {
	$(".error").each(function () {
		handleText(this);
	});

	if (!($('#requestState').val() == 'SAVE' || $('#requestState').val() == 'EDIT' || $('#requestState').val() == 'ADD' || $('#requestState').val() == 'SUBMIT')) {
		$('#memberType').val(0);
		$('#parentParticipantId').val("0")
		$('#memberName').val("")
		$('#ifscCode').val("SELECT")
		$('#bankSector').val("SELECT")
		$('#bankMasterCode').val("")
		$('#rtgsCode').val("")
		$('#participantId').val("")
		$('#savingsAccNumber').val("")
		$('#currentAccNumber').val("")
		$('#uniqueBnk').val("")
		$('#participantIdNFS').val("")
		$('#uniqueBnkName').val("SELECT")
	}

	$('#bnkPhone').val("")
	$('#bnkPhone2').val("")
	$('#bnkMobile').val("")
	$('#bnkMobile2').val("")
	$('#bnkEmail').val("")
	$('#bnkEmail2').val("")
	$('#bnkAdd').val("")
	$('#bnkCountry').val("0")
	$('#bnkState').val("0")
	$('#bnkCity').val("0")
	$('#bnkPincode').val("")
	$('#gstCountry').val("0")


	$('#gstn').val("")
	$('#gstAdd').val("")
	$('#gstState').val("0")
	$('#gstCity').val("0")
	$('#gstPincode').val("")

	$('#webSite').val("")
}

function clearMemberContactData() {
	$('#addressType').val(0);
	$('#cntChkrName').val("");
	$('#cntPhone').val("");
	$('#cntMobile').val("");
	$('#cntFax').val("");
	$('#cntAdd1').val("");
	$('#cntState').val(0);
	$('#cntCity').val(0);
	$('#cntPincode').val("");
	$('#cntCountry').val("0");
	$('#cntDesignation').val("");
	$('#cntEmail').val("");

		$('#erraddressType').hide();
		$('#errcntChkrName').hide();
		$('#errcntPhone').hide();
		$('#errcntMobile').hide();
		$('#errcntFax').hide();
		$('#errcntAdd1').hide();
		$('#errcntState').hide();
		$('#errcntCity').hide();
		$('#errcntPincode').hide();
		$('#errcntCountry').hide();
		$('#errcntDesignation').hide();
		$('#errcntEmail').hide();

}

function handleText(ref){
if ($(ref).text().trim().length > 0) {
			$(ref).empty();
		}
}

function getTrimmedString(str){
  if(str!=null){
     return str.toString().trim();
   }
   return "";
   }
