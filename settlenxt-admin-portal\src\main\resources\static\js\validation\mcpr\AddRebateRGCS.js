$(document).ready(function () {
	
	if (typeof vNewCardCount2 != "undefined") {
	    $("#errvCardType").hide();
	    $("#errVFinancialYear").hide();
	    $("#errVFeatureOrBaseFee").hide();
	    $("#errVOperatorIndi").hide();
	    $("#errvNewCardCount1").hide();
	    $("#errvNewCardCount2").hide();
	    $("#errvRebatePercentage").hide();
			disableToValueLoading();
	} 
	
	$("#vCardType").on('keyup keypress blur change', function () {
        validateField('vCardType', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#VFinancialYear').on('keyup keypress blur change', function () {
        validateField('VFinancialYear', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#VFeatureOrBaseFee').on('keyup keypress blur change', function () {
        validateField('VFeatureOrBaseFee', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#VOperatorIndi').on('keyup keypress blur change', function () {
        validateField('VOperatorIndi', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#vNewCardCount1').on('keyup keypress blur change', function () {
        validateField('vNewCardCount1', true, "Integer", 100, false,1,1999999999,true);
    });
    $('#vNewCardCount2').on('keyup keypress blur change', function () {
        validateField('vNewCardCount2', true, "Integer", 100, false,1,1999999999,true);
    });
    $('#vRebatePercentage').on('keyup keypress blur change', function () {
        validateField('vRebatePercentage', true, "Decimal", 100, false,0,100,true);
    });
	
	
	
	$("#errvCardType").on('keyup keypress blur change', function () {
        validateField('vCardType', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#errVFinancialYear').on('keyup keypress blur change', function () {
        validateField('VFinancialYear', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#errVFeatureOrBaseFee').on('keyup keypress blur change', function () {
        validateField('VFeatureOrBaseFee', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#errVOperatorIndi').on('keyup keypress blur change', function () {
        validateField('VOperatorIndi', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#errvNewCardCount1').on('keyup keypress blur change', function () {
        validateField('vNewCardCount1', true, "Integer", 100, false,1,1999999999,true);
    });
    $('#errvNewCardCount2').on('keyup keypress blur change', function () {
        validateField('vNewCardCount2', true, "Integer", 100, false,1,1999999999,true);
    });
    $('#errvRebatePercentage').on('keyup keypress blur change', function () {
        validateField('vRebatePercentage', true, "Decimal", 100, false,0,100,true);
    });
	    
	
	
	
});

function disableToValueLoading()
{

	if (typeof vNewCardCount2 != "undefined") {
		$('#vNewCardCount2').attr('disabled', true);
		if (document.getElementById("VOperatorIndi").value == "Between") {
		$('#vNewCardCount2').attr('disabled', false);
			}
	}

}
function resetAction() {
document.getElementById("addEditRebate").reset();
$("#errvCardType").find('.error').html('');
$("#errVFinancialYear").find('.error').html('');
$("#errVFeatureOrBaseFee").find('.error').html('');
$("#errVOperatorIndi").find('.error').html('');
$("#errvNewCardCount1").find('.error').html('');
$("#errvNewCardCount2").find('.error').html('');
$("#errvRebatePercentage").find('.error').html('');
$("#errSelectedCard").find('.error').html('');
}

function viewRebate(rebateID, type,originPage) {
var url;
	if (type == 'V')
		url = '/getRebate';
	else if (type == 'E')
		url = '/getRebate';
	else if (type == 'P')
		url = '/getPendingRebate';
	 var data = "rid," + rebateID + ",viewType," + type + ",originPage," + originPage ;
	
	postData(url, data);
}
function viewRebateGrid(rebateID, type,approvedlist,originPage) {
var url;
	if(approvedlist=='Y')
		url = '/getRebate';
	else if (approvedlist == 'N')
	{
		if(type=='R')
		{
			url = '/getRebate';
		}
		else
		{
			url = '/getPendingRebate';
		}
	}
	
	
	var data = "rid," + rebateID + ",viewType,V,originPage," + originPage ;
	
	postData(url, data);
}
function disableToValue()
{
$('#vNewCardCount2').attr('disabled', true);
document.getElementById("vNewCardCount2").value =0;

if (document.getElementById("VOperatorIndi").value == "Between") {
$('#vNewCardCount2').attr('disabled', false);

	}
	
}

function viewRebateAdd(_userID, _type,originPage) {

	
	var isValid = true;
    isValid = validateFields(isValid);
	if (document.getElementById("VOperatorIndi").value == "Between") {
	    if (!validateField('vNewCardCount2', true, "Integer", 100, false,1,1999999999,true) && isValid) {
	        isValid = false;
	    }
	}
    if (!validateField('vRebatePercentage', true, "Decimal", 100, false,0,100,true) && isValid) {
        isValid = false;
    }
	var arr = document.getElementsByClassName("selectedRoles");
	var cardVariants = "";
	
	var i = 0;
		 for (i of arr){
		  cardVariants = cardVariants + i.id.replace('remove', '') + "|"
				+ $('#' + i.id).attr('value') + "|";  
		 }
	
	cardVariants = cardVariants.substring(0, cardVariants.length - 1);
	if (cardVariants.length == 0) {
    	if(rebateValidationMessages["SelectedCard"]){
    		$("#errSelectedCard" ).find('.error').html(rebateValidationMessages["SelectedCard"]);
    	}
        $("#errSelectedCard").show();
        isValid = false;
	}
	else
	{
        $("#errSelectedCard" ).hide();
	}	
	

	if (!isValid) {
		return false;
	}

	checkDuplicateData(originPage);
	
}
function validateFields(isValid) {
	if (!validateField('vCardType', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}
	if (!validateField('VFinancialYear', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}
	if (!validateField('VFeatureOrBaseFee', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}
	if (!validateField('VOperatorIndi', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}
	if (!validateField('vNewCardCount1', true, "Integer", 100, false, 1, 1999999999, true) && isValid) {
		isValid = false;
	}
	return isValid;
}

function homeRebate(_userID, _type,originPage) {
var url;
	if(originPage=='mainPage')
	{
		url = '/showRebateList';
	}
	else
	{
		url = '/rebatePendingForApproval';
	}	
	var data = "originPage," + originPage ;
	postData(url, data);
}


function submitForm(url,_userType,originPage) {
	var data = "userType," + "P" + ",originPage," + originPage ;
	postData(url, data);
}



var optionFunctionalityList = new Array();

function removeTag(id, arg1) {
	
	var moduleId = "1";
	var trid="'" + id + "'";	

	var elem = document.getElementById("row" + id);
	var roleName = "'" + arg1 + "'";

	if (typeof (elem) != 'undefined' && elem != null) {
		var oldmoduleId = "1";
		if (oldmoduleId == moduleId) {
			$('#optionList')
					.append(
							'<tr class="optionRoles" id="option'
									+ id
									+ '"><td>'
									+ arg1
									+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right" onclick="addToAssignedList('
									+ trid + ',' + roleName + ')"></td></tr>');
		}
		$('.dataTables_empty').remove();
		$('#remove' + id).remove();

		$('#changeFlag').val('true');
		
	} else {
		$('#changeFlag').val('true');
		
		$('.dataTables_empty').remove();
		$('#remove' + id).remove();
	}

}
function addToAssignedList(id, arg1) {

	var moduleId = 1;
	var roleName = "'" + arg1 + "'";
	
	var trid="'" + id + "'";	
	
	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}
	$('#assignedList')
			.append(
					'<tr class="selectedRoles" value="'
							+ arg1
							+ '" id="remove'
							+ Number(id)
							+ '"><td >'
							+ arg1
							+ '</td><td><i class="glyphicon glyphicon-remove-circle" style="color: blue" onclick="removeTag('
							+ trid + ',' + roleName
							+ ')" ></i><input type="hidden" id="row' + id
							+ '" value="' + moduleId + '" ></td></tr>');
	
	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');
	$('#errSelectedCard').hide();
	
	$('#option' + id).remove();
}

function checkDuplicateData(originPage) {

var url;
	var arr = document.getElementsByClassName("selectedRoles");
	
	var cardVariants = "";
	
	var i = 0;
		 for (i of arr){
		  cardVariants = cardVariants + i.id.replace('remove', '') + "|";  
		 }
	
	cardVariants = cardVariants.substring(0, cardVariants.length - 1);
	var cardType=document.getElementById("vCardType").value;
	var financialYear=document.getElementById("VFinancialYear").value;
	var featureOrBaseFee=document.getElementById("VFeatureOrBaseFee").value;
	var operatorIndi=document.getElementById("VOperatorIndi").value;
	var newCardCount1=document.getElementById("vNewCardCount1").value;
	var newCardCount2=document.getElementById("vNewCardCount2").value;
	if(newCardCount2=="")
	{
		newCardCount2="0";
	}
	var rebatePercentage=document.getElementById("vRebatePercentage").value;
	var newRecord="Y";
	
	
	var rebateID="0";
	var msUrl = "checkDuplicateData";

	var tokenVal = document.getElementsByName("_TransactToken")[0].value;
		$.ajax({
			url: msUrl,
			type: "POST",
			dataType: "json",
			
		"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenVal);},
			data: {
				"cardType": cardType,
				"financialYear": financialYear,
				"featureOrBaseFee": featureOrBaseFee,
				"operatorIndi": operatorIndi,
				"newCardCount1": newCardCount1,
				"newCardCount2": newCardCount2,
				"rebatePercentage": rebatePercentage,
				"cardVariantIds": cardVariants,
				"newRecord":newRecord,
				"rebateID":rebateID,
				_TransactToken: tokenVal
			},
			success: function(response) {
				if (response.status == "BSUC_0001") {
					
					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "";
					url = '/asignRebateToFunctionAdd';
					
					var data = "rebateID,0,cardType," + cardType + 
					",financialYear," + financialYear + ",featureOrBaseFee," + featureOrBaseFee + 
					",operatorIndi," + operatorIndi + 
					",cardVariantIds," + cardVariants + 
					",newCardCount1," + newCardCount1 + ",newCardCount2," + newCardCount2 + ",rebatePercentage," + rebatePercentage +",originPage," + originPage ;
					postData(url, data);
				} else {
					
 					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "Rebate Configuration Already Exists";
				}
			},
			error: function(_request, _status, _error) {
				errvErrorInfo.className = 'error';
				errvErrorInfo.innerHTML = "";
			}
		});
	
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType !="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    isValid = validateFieldTypeIntAlpha(fieldType, fieldValue, isValid);
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
  	if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
    if(fieldId=="vNewCardCount2")
    {
	    var fieldValue1 = $("#vNewCardCount1").val();
	    if(Number(fieldValue1) > Number(fieldValue))
	    {
        	isValid = false;
    	}
    }
    handleShowErrMsg(isValid, fieldId);
    return isValid;
}


function handleShowErrMsg(isValid, fieldId) {
	if (isValid) {
		$("#err" + fieldId).hide();
	} else {
		if (rebateValidationMessages[fieldId]) {
			$("#err" + fieldId).find('.error').html(rebateValidationMessages[fieldId]);
		}
		$("#err" + fieldId).show();
	}
}

function validateFieldTypeIntAlpha(fieldType, fieldValue, isValid) {
	var regEx;
	if (fieldType == "Alphabet") {
		regEx = /^[a-zA-Z]+$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphabetWithSpace") {
		regEx = /^[a-zA-Z ]+$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphanumericNoSpace") {
		regEx = /^[A-Za-z0-9]+$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "Integer") {
		regEx = /^\d*$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "Decimal") {
		regEx = /^\d+\.?\d*$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

function mySelect(){
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	 
	  var referenceNoList = document.getElementById("newsIds");
   
	 if(array.length==referenceNoListPendings.length){
		 $('#selectAll').prop('checked', true);
		
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
			 $("#toggleModalNews").modal('show');
	 }
	 else{
		 $("#toggleModalNews").modal('hide');
		 
	 }
	
}

function ApproveorRejectBulkRebate(type,action){
	
	 var url = '/approveRebateBulk';
	var data;
	 var array = [];

	 if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(action=='All'){
		 array=referenceNoListPendings; 
	 }
	   
		var referenceIdIdList = "";
		
		var i = 0;
		 for (i of array){
		  referenceIdIdList = referenceIdIdList + i + "|";  
		 }
		
	if(type=='A'){
			
		 data =  "status,"+"A"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	else if(type=='R'){
		
		 data = "status,"+"R"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	
	postData(url, data);
	
}


function deselectAll() {

	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  
		 let i=0;
		 for (i of ele){
		  if(i.type=='checkbox')  
         i.checked=false;  
		 }

 
}


function selectAllFunctionlities() {

	var moduleId = 1;


	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}

	
	 var optionsArray = document.getElementsByClassName("optionRoles");

	
	
let i=0;
	if (optionsArray.length > 0) {
for (i of optionsArray) {
		
			var id = i.id.replace(
				"option", '').replace("remove", "");
			var trid="'" + id + "'";	
			var roleNameData = i.innerText;
			roleNameData = $('<div>').text(roleNameData).html();
			var roleName = "'" + roleNameData + "'";
			roleName = $('<div>').text(roleName).html();
			id = $('<div>').text(id).html();

			$('#assignedList')
				.append(
					'<tr class="selectedRoles" value="'
					+ roleNameData
					+ '" id="remove'
					+ id
					+ '"><td >'
					+ roleNameData
					+ '</td><td><i class="glyphicon glyphicon-remove-circle" style="color: blue" onclick="removeTag('
					+ trid + ',' + roleName
					+ ')" ></i><input type="hidden" id="row' + id
							+ '" value="' + moduleId + '" ></td></tr>');

		}
		$('#optionList').empty();
	}
	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');
	
	 $('#forms').prop('disabled', false);


}



function removeAllFunctionalities() {

	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}

	var selectedArray = document.getElementsByClassName("selectedRoles");
	
	if (selectedArray.length > 0) {

	  for (let j of selectedArray) {
       	var id = j.id.replace(
				"option", '').replace("remove", "");
		var trid="'" + id + "'";	
			var roleNameData = j.innerText;
			roleNameData = $('<div>').text(roleNameData).html();
			var roleName = "'" + roleNameData + "'";
			roleName = $('<div>').text(roleName).html();
			
			$('#optionList')
				.append(
					'<tr class="optionRoles" id="option'
					+ id
					+ '"><td>'
					+ roleNameData
					+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right" onclick="addToAssignedList('
					+ trid + ',' + roleName + ')"></td></tr>');
	
   
}
	
		
		$('#assignedList').empty();

	}

	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');
	 $('#forms').prop('disabled', false);
}