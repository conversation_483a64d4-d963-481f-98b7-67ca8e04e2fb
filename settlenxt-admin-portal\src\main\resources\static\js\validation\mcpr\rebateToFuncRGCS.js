$(document).ready(function() {
	var rebateNewRecord = document.getElementById("rebateNewRecord").value;
	if(rebateNewRecord=='N')
	{
		$('#vCardType').attr('disabled', true);
		$('#VFinancialYear').attr('disabled', true);
		$('#VFeatureOrBaseFee').attr('disabled', true);
		$('#VOperatorIndi').attr('disabled', true);
	}
	disableToValueLoading();
	$("#errMsg").hide();
	
	$("#vCardType").on('keyup keypress blur change', function () {
        validateField('vCardType', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#VFinancialYear').on('keyup keypress blur change', function () {
        validateField('VFinancialYear', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#VFeatureOrBaseFee').on('keyup keypress blur change', function () {
        validateField('VFeatureOrBaseFee', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#VOperatorIndi').on('keyup keypress blur change', function () {
        validateField('VOperatorIndi', true, "SelectionBox", 100, false,0,0,false);
    });
    $('#vNewCardCount1').on('keyup keypress blur change', function () {
        validateField('vNewCardCount1', true, "Integer", 100, false,1,1999999999,true);
    });
    $('#vNewCardCount2').on('keyup keypress blur change', function () {
        validateField('vNewCardCount2', true, "Integer", 100, false,1,1999999999,true);
    });
    $('#vRebatePercentage').on('keyup keypress blur change', function () {
        validateField('vRebatePercentage', true, "Decimal", 100, false,0,100,true);
    });
	
	
	disableSave();
	$("#vCardType").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#VFinancialYear").on('keyup keypress blur change', function () {
        unableSave();
    });
	$("#VFeatureOrBaseFee").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#VOperatorIndi").on('keyup keypress blur change', function () {
        unableSave();
    });	$("#vNewCardCount1").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#vNewCardCount2").on('keyup keypress blur change', function () {
        unableSave();
    });	$("#vRebatePercentage").on('keyup keypress blur change', function () {
        unableSave();
    });
	
});

function viewRebate(rebateID, type,originPage) {
var url;
	if (type == 'V')
		url = '/getRebate';
	else if (type == 'E')
		url = '/getRebate';
	else if (type == 'P')
		url = '/getPendingRebate';
	
	var data = "rid," + rebateID + ",viewType," + type + ",originPage," + originPage ;
	
	postData(url, data);
}
function disableSave()
{
	if (typeof bSubmit != "undefined") {
		document.getElementById("bSubmit").disabled = true;
	}
}

function unableSave()
{
	if (typeof bSubmit != "undefined") {
		document.getElementById("bSubmit").disabled = false;
	}
}

function disableToValueLoading()
{
if (typeof vNewCardCount2 != "undefined") {
	$('#vNewCardCount2').attr('disabled', true);
	if (document.getElementById("VOperatorIndi").value == "Between") {
	$('#vNewCardCount2').attr('disabled', false);
		}
	}	
}
function disableToValue()
{
if (typeof vNewCardCount2 != "undefined") {
	$('#vNewCardCount2').attr('disabled', true);
	document.getElementById("vNewCardCount2").value =0;
	if (document.getElementById("VOperatorIndi").value == "Between") {
	$('#vNewCardCount2').attr('disabled', false);
		}
}
}

function saveRebate(_userID, _type,originPage) {
	var rebateNewRecord = document.getElementById("rebateNewRecord").value;
	
	
	
		

	var isValid = true;

	if(rebateNewRecord=='Y')
	{
	    isValid = validateFields(isValid);
		var arr = document.getElementsByClassName("selectedRoles");
		var cardVariants = "";
		
		var i = 0;
		 for (i of arr){
		  cardVariants = cardVariants + i.id.replace('remove', '') + "|"
					+ $('#' + i.id).attr('value') + "|";  
		 }
		
		
		cardVariants = cardVariants.substring(0, cardVariants.length - 1);
		isValid = errorShowAndHide(cardVariants,isValid);	
	}
    if (!validateField('vNewCardCount1', true, "Integer", 100, false,1,1999999999,true) && isValid) {
        isValid = false;
    }
	if (document.getElementById("VOperatorIndi").value == "Between") {
	    if (!validateField('vNewCardCount2', true, "Integer", 100, false,1,1999999999,true) && isValid) {
	        isValid = false;
	    }
	}
    if (!validateField('vRebatePercentage', true, "Decimal", 100, false,0,100,true) && isValid) {
        isValid = false;
    }
    if(!isValid)
    {
    	return false;
    }
	if(rebateNewRecord=='Y')
	{
		checkDuplicateData(originPage);	
	}
	else
	{
		checkDuplicateDataforUpdate(originPage);
	}	
}



function validateFields(isValid) {
    if (!validateField('vCardType', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
        isValid = false;
    }
    if (!validateField('VFinancialYear', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
        isValid = false;
    }
    if (!validateField('VFeatureOrBaseFee', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
        isValid = false;
    }
    if (!validateField('VOperatorIndi', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
        isValid = false;
    }
    return isValid;
}

function errorShowAndHide(cardVariants,isValid) {
	
    if (cardVariants.length == 0) {
        if (rebateValidationMessages["SelectedCard"]) {
            $("#errSelectedCard").find('.error').html(rebateValidationMessages["SelectedCard"]);
        }
        $("#errSelectedCard").show();
        isValid = false;
    }
    else {
        $("#errSelectedCard").hide();
    }
    return isValid;
}

function urlPostAction(_type, action,originPage) {
	var data = "originPage," + originPage ;
	postData(action, data);
}
function homeRebate(_userID, _type,originPage) {
	var url;
	
	if(originPage=='mainPage')
	{
		url = '/showRebateList';
	}
	else
	{
		url = '/rebatePendingForApproval';
	}	
	 
	var data = "originPage," + originPage ;

	postData(url, data);
}


var optionFunctionalityList = new Array();


function postDiscardAction(action,originPage) { 
		var url = action;
		var rebateId = document.getElementById("hrebateID").value;
		var data = "rebateId," + rebateId + ",originPage," + originPage ;
		postData(url, data);
 }
	

function removeTag(id, arg1) {
	
	
	var roleName = "'" + arg1 + "'";

			$('#optionList')
					.append(
							'<tr class="optionRoles" id="option'
									+ id
									+ '"><td>'
									+ arg1
									+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right" onclick="addToAssignedList('
									+ id + ',' + roleName + ')"></td></tr>');
		$('.dataTables_empty').remove();
		$('#remove' + id).remove();

		$('#changeFlag').val('true');
		unableSave();

}


function addToAssignedList(id, arg1) {

	var roleName = "'" + arg1 + "'";
	

	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}
	$('#assignedList')
			.append(
					'<tr class="selectedRoles" value="'
							+ arg1
							+ '" id="remove'
							+ id
							+ '"><td >'
							+ arg1
							+ '</td><td><i class="glyphicon glyphicon-remove-circle" style="color: blue" onclick="removeTag('
							+ id + ',' + roleName
							+ ')" ></i></td></tr>');

	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');
	$('#errSelectedCard').hide();
	$('#option' + id).remove();
	unableSave();
}


function checkDuplicateData(originPage) {
var data;
var url;
	var arr = document.getElementsByClassName("selectedRoles");
	
	var cardVariants = "";
	
	var i = 0;
		 for (i of arr){
		  cardVariants = cardVariants + i.id.replace('remove', '') + "|";  
		 }
	
	
	cardVariants = cardVariants.substring(0, cardVariants.length - 1);
	var cardType=document.getElementById("vCardType").value;
	var financialYear=document.getElementById("VFinancialYear").value;
	var featureOrBaseFee=document.getElementById("VFeatureOrBaseFee").value;
	var operatorIndi=document.getElementById("VOperatorIndi").value;
	var newCardCount1=document.getElementById("vNewCardCount1").value;
	var newCardCount2=document.getElementById("vNewCardCount2").value;
	var rebatePercentage=document.getElementById("vRebatePercentage").value;
	var newRecord="Y";
	var rebateID=document.getElementById("hrebateID").value ;
	
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var validvRoleName=false;
	var msUrl = "checkDuplicateData";
	if(newCardCount2=="")
	{
		newCardCount2="0";
	}


		$.ajax({
			url: msUrl,
			type: "POST",
			dataType: "json",
			
		"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenValue);},
			data: {
				"cardType": cardType,
				"financialYear": financialYear,
				"featureOrBaseFee": featureOrBaseFee,
				"operatorIndi": operatorIndi,
				"newCardCount1": newCardCount1,
				"newCardCount2": newCardCount2,
				"rebatePercentage": rebatePercentage,
				"cardVariantIds": cardVariants,
				"newRecord":newRecord,
				"rebateID":rebateID,
				"_TransactToken": tokenValue
			},
			success: function(response) {
				if (response.status == "BSUC_0001") {
					validvRoleName = true;
					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "";
					cardVariants = "";
					
					
		 for (i of arr){
		  cardVariants = cardVariants + i.id.replace('remove', '') + "|"
								+ $('#' + i.id).attr('value') + "|";  
		 }
					
					cardVariants = cardVariants.substring(0, cardVariants.length - 1);

					url = '/editRebate';
					data = "rebateID," + document.getElementById("hrebateID").value + ",cardType," + cardType + 
					",financialYear," + financialYear + ",featureOrBaseFee," + featureOrBaseFee + 
					",operatorIndi," + operatorIndi + 
					",cardVariantIds," + cardVariants + 
					",newCardCount1," + newCardCount1 + ",newCardCount2," + newCardCount2 + ",rebatePercentage," + rebatePercentage + 
					 ",originPage," + originPage ;
					postData(url, data);					
				} else {
					validvRoleName = false;
 					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "Rebate Configuration Already Exists";
				}
			},
			error: function(_request, _status, _error) {
				errvErrorInfo.className = 'error';
				errvErrorInfo.innerHTML = "";
			}
		});

	return validvRoleName;
}
function checkDuplicateDataforUpdate(originPage) {

var url;
	
	var cardVariants = "";
	var cardType=document.getElementById("vCardType").value;
	var financialYear=document.getElementById("VFinancialYear").value;
	var featureOrBaseFee=document.getElementById("VFeatureOrBaseFee").value;
	var operatorIndi=document.getElementById("VOperatorIndi").value;
	var newCardCount1=document.getElementById("vNewCardCount1").value;
	var newCardCount2=document.getElementById("vNewCardCount2").value;
	var rebatePercentage=document.getElementById("vRebatePercentage").value;
	var newRecord="N";
	var rebateID=document.getElementById("hrebateID").value 
	if(newCardCount2=="")
	{
		newCardCount2="0";
	}
	
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var validvRoleName=false;
	var msUrl = "checkDuplicateData";


		$.ajax({
			url: msUrl,
			type: "POST",
			dataType: "json",
			
		"beforeSend": function (xhr) { xhr.setRequestHeader('_TransactToken', tokenValue);},
			data: {
				"cardType": cardType,
				"financialYear": financialYear,
				"featureOrBaseFee": featureOrBaseFee,
				"operatorIndi": operatorIndi,
				"newCardCount1": newCardCount1,
				"newCardCount2": newCardCount2,
				"rebatePercentage": rebatePercentage,
				"cardVariantIds": cardVariants,
				"newRecord":newRecord,
				"rebateID":rebateID,
				"_TransactToken": tokenValue
			},
			success: function(response) {
				if (response.status == "BSUC_0001") {
					validvRoleName = true;
					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "";
					
					url = '/editRebate';
				var	data = "rebateID," + document.getElementById("hrebateID").value  + 
					",newCardCount1," + newCardCount1 + ",newCardCount2," + newCardCount2 + ",rebatePercentage," + rebatePercentage + 
					 ",originPage," + originPage ;
					postData(url, data);					
					
				} else {
					validvRoleName = false;
 					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "Rebate Configuration Already Exists";
				}
			},
			error: function(_request, _status, _error) {
				errvErrorInfo.className = 'error';
				errvErrorInfo.innerHTML = "";
			}
		});

	return validvRoleName;
}
function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var regEx;
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType!="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    
    
     isValid  = alphaAndNumericValidation(fieldType, fieldValue, isValid);
     if (fieldType == "Decimal") {
        regEx = /^\d+\.?\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
  	if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
    if(fieldId=="vNewCardCount2")
    {
	    var fieldValue1 = $("#vNewCardCount1").val();
	    if(Number(fieldValue1) > Number(fieldValue))
	    {
        	isValid = false;
    	}
    }
    handleErrMsg(isValid, fieldId);
    return isValid;
}



function handleErrMsg(isValid, fieldId) {
	if (isValid) {
		$("#err" + fieldId).hide();
	} else {
		if (rebateValidationMessages[fieldId]) {
			$("#err" + fieldId).find('.error').html(rebateValidationMessages[fieldId]);
		}
		$("#err" + fieldId).show();
	}
}

function alphaAndNumericValidation(fieldType, fieldValue, isValid) {
	var regEx;
    if (fieldType == "Alphabet") {
        regEx = /^[A-Z]+$/i;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "AlphabetWithSpace") {
        regEx = /^[A-Z ]+$/i;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "AlphanumericNoSpace") {
        regEx = /^[A-Za-z0-9]+$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }

    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }

    if (fieldType == "Integer") {
        regEx = /^\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    
    return { regEx, isValid };
}
