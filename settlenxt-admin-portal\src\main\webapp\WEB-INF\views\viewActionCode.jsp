<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript"
	src="./static/js/validation/ApproveActionCode.js"></script>

<form:form onsubmit="removeSpace(this); encodeForm(this);" method="POST"
	id="viewApproveActionCode" modelAttribute="actionCodeDTO"
	action="/approveActionCode" autocomplete="off">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="action.viewscreen.title" /></span></strong>
			<div class="icon_bar">
				<sec:authorize access="hasAuthority('Edit Action Code') ">
					

<c:if test="${mainTab eq 'Y'}">
<a data-toggle="tooltip" title="Edit"
onclick="viewActionCodeInfo('${actionCodeDTO.actionCodeId}','/editActionCode','mainTab')"
href="#"><img src="./static/images/edit-grey.png" alt="edit"></a>
</c:if>
<c:if test="${approvalTab eq 'Y'}">
<a data-toggle="tooltip" title="Edit"
 onclick="viewActionCodeInfo('${actionCodeDTO.actionCodeId}','/editActionCode','approvalTab')"
 href="#"><img src="./static/images/edit-grey.png" alt="edit"></a>
</c:if>
				</sec:authorize>
			</div>
		</div>

		<div class="panel-body">
			<form:hidden path="actionCodeId" value="${actionCodeDTO.actionCodeId}" />

			<table class="table table-striped" style="font-size: 12px">
			<caption style="display:none;">viewactioncode</caption>
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
						
						
						<td><label><spring:message code="am.lbl.mti" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.mti}</td>

						<td><label><spring:message code="am.lbl.funcCode" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.funcCode}</td>

						<td><label><spring:message code="am.lbl.actionCode" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.actionCode}</td>

						


					</tr>

					<tr>

								<td><label><spring:message code="am.lbl.actionCodeDesc" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.actionCodeDesc}</td>

						<td><label><spring:message code="am.lbl.funcCodeDesc" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.funcCodeDesc}</td>

						

						<td><label><spring:message code="am.lbl.raisedBy" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.raisedBy}</td>

						<%-- <td><label><spring:message code="ifsc.requestBy" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.lastUpdatedBy}</td> --%>


					</tr>
					
					<tr>

								<td><label><spring:message code="am.lbl.allowedActncdToRemove" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.allowedActncdToRemove}</td>

						<td><label><spring:message code="st.lbl.capAmtCalReq" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.capAmtCalReq}</td>

						

						<td><label><spring:message code="am.lbl.transitionActionCode" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.transitionActionCode}</td>



					</tr>
					
					<tr>

								<td><label><spring:message code="am.lbl.tatPeriodDayType" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.tatPeriodDayType}</td>

						<td><label><spring:message code="am.lbl.tatPeriod" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.tatPeriod}</td>

						

						<td><label><spring:message code="am.lbl.defaultReasonRejCode" /><span
								style="color: red"></span></label></td>
						<td>${actionCodeDTO.defaultReasonRejCode}</td>

					

					</tr>

				</tbody>
			</table>
		<c:if test="${actionCodeDTO.requestState  eq 'R' or 'P' and not empty showbutton}">
			<div style="text-align:center">
				<button type="button" class="btn btn-danger"
					onclick="submitForm('/actionCodePendingForApproval');">
					<spring:message code="ifsc.backBtn" />
				</button>
			</div>
			</c:if>
			
			
			
			<c:if test="${mainTab  eq 'Y'}">
			<div style="text-align:center">
				<button type="button" class="btn btn-danger"
					onclick="submitForm('/showActionCode');">
					<spring:message code="ifsc.backBtn" />
				</button>
			</div>
			
			</c:if>

		</div>
	</div>
</form:form>
