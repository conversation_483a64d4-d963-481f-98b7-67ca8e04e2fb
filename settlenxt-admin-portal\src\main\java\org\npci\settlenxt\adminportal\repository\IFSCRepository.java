package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.adminportal.dto.IFSCDTO;

@Mapper
public interface IFSCRepository {
	 List<IFSCDTO> getApprovedIFSCList();

	 List<IFSCDTO> getApprovedIFSCListMain();

	 List<IFSCDTO> getPendingForAppovalIFSCList();

	 IFSCDTO getIFSC(String ifscCode);

	 IFSCDTO getIFSCDataMain(String ifscCode);

	 List<IFSCDTO> getIFSCByBankCode(String bankCode);

	 IFSCDTO getIFSCMain(String ifscCode);

	 int updateIFSC(IFSCDTO ifscdto);

	 long saveIFSC(IFSCDTO ifscdto);

	 void updateIFSCMain(IFSCDTO ifscdto);

	 void saveIFSCMain(IFSCDTO ifscdto);

	 void updateIFSCRequestState(IFSCDTO ifscdto);

	 void deleteDiscardedEntry(IFSCDTO ifscdto);

	

}
