$(document).ready(function() {
	$("#errMsg").hide();
	
$('#roleName').on('keyup keypress blur change',function () {
        validateFromCommonVal('roleName', true, "AlphaNumericWithSpace", 30, false);
    });

$('#roleDesc').on('keyup keypress blur change',function () {
        validateFromCommonVal('roleDesc', true, "AlphaNumericWithSpaceDotHyphenAnd", 50, false);
    });
$('#roleType').on('keyup keypress blur change',function () {
        validateFromCommonVal('roleType', true, "SelectionBox", "", false);
    });
$('#makChkFlag').on('keyup keypress blur change',function () {
        validateFromCommonVal('makChkFlag', true, "SelectionBox", "", false);
    });     
	   
	
	var funcList = "";
	function assignedFuncList(roleId) {
		funcList = funcList + "|" + roleId;
	}

	function getFuncList() {
		while (funcList.charAt(0) === '|')
			funcList = funcList.slice(1);
		return funcList;
	}

	$('#addSingle').click(function() {
		return !$('#source option:selected').remove().appendTo('#destination');
	});

	$('#addAll').click(function() {
		return !$('#source option').remove().appendTo('#destination');
	});

	$('#removeSingle').click(function() {
		return !$('#destination option:selected').remove().appendTo('#source');
	});

	$('#removeAll').click(function() {
		return !$('#destination option').remove().appendTo('#source');
	});

	$("#SUBMIT").click(function() {
		$('select#destination').find('option').each(function() {
			assignedFuncList($(this).val() + "|" + $(this).text());
		});

		var list = getFuncList();
		if (list == "") {
			return false;
		} else {
			$("#assFuncList").val(getFuncList());
			funcList = "";
		}
	});
	
	
	 $('#roleName').on('keyup keypress blur change',function () {
 if (! validateFromCommonVal('roleName', true, "AlphaNumericWithSpace", 30, false))
{
console.log("roleName")
}else{
checkDuplicateRoleName();  }
    
});
	
		$('#update').prop('disabled', true);

$('#moduleId').change(
    		function () {
if ($('#moduleId').val() != '0') {
$('#tabnew').on('click', function(){
    $('#update').prop('disabled', false);
    });
$('#removeAllId').on('click', function(){
    $('#update').prop('disabled', false);
    });
$('#selectAllId').on('click', function(){
    $('#update').prop('disabled', false);
    });        
}
else {
    $('#update').prop('disabled',true);
}
});



$('#tabnew1').on('click', function(){
   $('#update').prop('disabled', false);
});

});
function restFunsTab() {
	var Table = document.getElementById("assignedList");
	Table.innerHTML = "<tr class='odd'><td valign='top' colspan='2' class='dataTables_empty'>No data available in table</td></tr>";

console.log("Hi");
}
function checkDuplicateRoleName() {
	var roleName = $('#roleName').val();
	var validvRoleName = false;
	var reqType = document.getElementById('reqType').value;
	 var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	
	var msUrl = "";
	if (reqType == 'B') {
		msUrl = "checkDuplicateBankRoleName";
	} else {
		msUrl = "checkDuplicateRoleName";
	}
	if (roleName != "") {

		$.ajax({
			url : msUrl,
			type : "POST",
			dataType : "json",
			data : {
				"roleName" : roleName,
				"_TransactToken" : tokenValue
			},
			success : function(response) {
				if (response.status == "BSUC_0001") {
					validvRoleName = true;
					
					$('#errRoleId2').find('.error').html('RoleName already Exists');
					 $('#errRoleId2').show();
					 callBackDupRoleName(true);
					 
					
				} else {
					validvRoleName = false;
					$('#errRoleId2').find('.error').html('');
						 $('#errRoleId2').hide();
						 callBackDupRoleName(false);
				}
			},
			error : function(_request, _status, _error) {
					$('#errRoleId2').find('.error').html('');
					 $('#errRoleId2').hide();
					 callBackDupRoleName(false);
		}
		});
	} else {
		validvRoleName = false;
	}
	return validvRoleName;
}

function urlPostAction(_type, action) {
		var data = "";
	postData(action, data);
}

function userAction(type, action) {
    let url = action;
   
    var rid = document.getElementById("roleId").value;
    var data = "rid," + rid + ",userType," + type + ",status,"
            + status;
    postData(url, data);
}



var ajaxValidRoleName;

function callBackDupRoleName(flag){
	ajaxValidRoleName=flag;
}

function viewUserAdd(_userID, type) {
$("#errMsg2").hide();
	var check = handleCheck();
   
	var arr = document.getElementsByClassName("selectedRoles");

	var roles = "";
	var i="";
	for ( i = 0; i < arr.length; i++) {
		roles = roles + arr[i].id.replace('remove', '') + "|"
			+ $('#' + arr[i].id).attr('value') + "|";
	}

	roles = roles.substring(0, roles.length - 1);

	if (roles.length == 0) {
	$("#errMsg2").show();
	var moduleId = document.getElementById("moduleId").value;
	if (moduleId == 'SELECT') {
	 $("#errMsg2").find('.error').html('Please select Module name');
	}else{
	
	 $("#errMsg2").find('.error').html('Please select func to assign to  role');}
		$("#errModuleId").show();
		$("#errModuleId").text("Please select function name");
		return false;
	}else{
	$("#errMsg2").hide();
		  $("#errMsg2").find('.error').html('');
	}
	
	var roleDesc = $("#roleDesc").val().replace("/^\s*|\s*$/g", '');
	var roleName = $("#roleName").val().replace("/^\s*|\s*$/g", '');
	var makChkFlag = $("#makChkFlag").val().replace("/^\s*|\s*$/g", '');
	var userType = $("#roleType").val().replace("/^\s*|\s*$/g", '');
	let url = '/asignRoleToFunctionAdd';
	 arr = document.getElementsByClassName("selectedRoles");
	
	 roles = "";
	for (i = 0; i < arr.length; i++) {
		roles = roles + arr[i].id.replace('remove', '') + "|"
				+ $('#' + arr[i].id).attr('value') + "|";
	}
	roles = roles.substring(0, roles.length - 1);
	if (roles.length == 0) {
		$("#errMsg").show();
		return false;
	}
	var userId = '0';
	var data = "roleDesc," + roleDesc + ",makChkFlag," + makChkFlag + ",roleName,"
			+ roleName + ",functionalityIds," + roles + ",roleId," + userId+ ",userType," + userType;
	
	if ($('#changeFlag').val() != 'true') {
		console.log("Inside changeFlag")
		$('html, body').animate({
			scrollTop : 0
		}, 'slow');
		$("#errMsg").show();
		if (type == 'A') {
			$("#errMsg").text("Please Add the Role information.");

		} else {
			$("#errMsg").text("No data modified");
		}
		return false;
	}
	
	if (check) {
	    document.querySelector(".button").disabled = true;
		retrieveAllFunctionalityList(url, data);
	} else {
		return false;
	}
	return false;
}

function handleCheck() {
	var check = true;


	if (!validateFromCommonVal('roleName', true, "AlphaNumericWithSpace", 30, false)) {
		check = false;
	} else {
		checkDuplicateRoleName();


		if (ajaxValidRoleName) {

			check = false;
		}
	}

	if (!validateFromCommonVal('roleDesc', true, "AlphaNumericWithSpaceDotHyphenAnd", 50, false)) {
		check = false;
	}
	if (!validateFromCommonVal('roleType', true, "Mandatory", 30, false)) {
		check = false;
	}
	if (!validateFromCommonVal('makChkFlag', true, "Mandatory", 30, false)) {
		check = false;
	}
	return check;
}

function viewUser(_action) {
	
$("#errMsg2").hide();
	let url = '/editRole';
	var arr = document.getElementsByClassName("selectedRoles");
	
	var roles = "";
	for (var i of arr) {
		roles = roles + i.id.replace('remove', '') + "|"
				+ $('#' + i.id).attr('value') + "|";
	}
	roles = roles.substring(0, roles.length - 1);
	if (roles.length == 0) {
		
		$("#errMsg2").show();
		  $("#errMsg2").find('.error').html('Please select func to assign to  role');
		return false;
	}else{
	$("#errMsg2").hide();
	}
	var state=document.getElementById("requestStateEdit").value;
	var userId = document.getElementById("roleId").value;
	var data = "functionalityIds," + roles + ",roleId," + userId+",requestState," + state;

	retrieveAllFunctionalityList(url, data);
}

function addRoleName(arg1, arg2) {
	document.getElementById("roleNamehidden" + arg2).value = arg1;
	document.getElementById("roleIdhidden" + arg2).value = arg3;
}



function rejectUser() {
	var testRemark = "test";
	var testWorkFlowId = "22250";
	document.approveRejectUser.action = "rejectRoleStatus?wid="
			+ testWorkFlowId + "&rm=" + testRemark;
	document.approveRejectUser.submit();
}

var optionFunctionalityList = new Array();

function getFunctionalityList(reqType) {
	
	$("#errMsg").hide();
	var roleId = document.getElementById("roleId").value;
	var moduleId = document.getElementById("moduleId").value;
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/') + 1);
	var makChkFlag = $("#makChkFlag").val().replace("/^\s*|\s*$/g", '');
	var url = pathName + "getFunctionalityList?rtype=" + reqType + "&moduleId="
			+ moduleId + "&makChkFlag=" + makChkFlag;
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	$
			.ajax({
				type : "POST",
				url : url,
				data : {
					roleId : roleId,
					_TransactToken : tokenValue
				},
				success : function(data) {
					optionFunctionalityList = new Array();
					let i="";
					let id="";
					$('#optionList').empty();
					for ( i = 0; i < data.length; i++) {
						var options = new Object();
						options.id = data[i].funcId;
						options.name = data[i].funcName;
						optionFunctionalityList.push(options);
						 id = data[i].funcId;
						var name = data[i].funcName;
						var name1 = "'" + name + "'";

						$('#optionList')
								.append(
										'<tr class="optionRoles" id="option'
												+ id
												+ '"><td>'
												+ name
												+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right"  onclick="addToAssignedList('
												+ id + ',' + name1
												+ ')"></td></tr>');
					}
					var selectedArray = document
							.getElementsByClassName("selectedRoles");
					var counter = 0;
					if (selectedArray.length > 0) {
						for ( i = 0; i < selectedArray.length; i++) {
							 id = selectedArray[i].id.replace(
									"selectedRoles", '').replace("remove", "");
							$('table#tabnew tr#option' + id).remove();

						}
					}
					for ( i = 0; i < data.length; i++) {
						for (var j of selectedArray) {
							 id = j.id.replace(
									"selectedRoles", '');
							if ((data[i].funcid) == id) {
								counter++;
							}
						}
					}

				},

				error : function() {
					console.log("error");
				}
			});
}
optionFunctionalityList = new Array();
function getFunctionalityAddList(reqType) {

	$("#errMsg").hide();
	var roleId = "0";
	var makChkFlag = $("#makChkFlag").val().replace("/^\s*|\s*$/g", '');
	var moduleId = document.getElementById("moduleId").value;
	if (moduleId == 'SELECT') {
		$('#optionList').empty();
		return false;
	}
	if (makChkFlag == 'SELECT') {
		$('#moduleId').val('SELECT').change();
		$('#optionList').empty();
		return false;
	}
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/') + 1);
	

	var url = pathName + "getFunctionalityList?rtype=" + reqType + "&moduleId="
			+ moduleId + "&makChkFlag=" + makChkFlag;
	 var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	$
			.ajax({
				type : "POST",
				url : url,
				data : {
					roleId : roleId,
					_TransactToken : tokenValue
				},
				success : function(data) {
					optionFunctionalityList = new Array();
					let i ="";
					$('#optionList').empty();
					for ( i = 0; i < data.length; i++) {
						var options = new Object();
						options.id = data[i].funcId;
						options.name = data[i].funcName;
						optionFunctionalityList.push(options);
						 let id = data[i].funcId;
						var name = data[i].funcName;
						var name1 = "'" + name + "'";
						$('#optionList')
								.append(
										'<tr class="optionRoles"  id="option'
												+ id
												+ '"><td>'
												+ name
												+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right"  onclick="addToAssignedList('
												+ id + ',' + name1
												+ ')"></td></tr>');
					}
					var selectedArray = document
							.getElementsByClassName("selectedRoles");
					var counter = 0;
					handleSelectedArray(selectedArray, i, data, counter);
				},
				error : function() {
					console.log("error");
				}
			});
}

function handleSelectedArray(selectedArray, i, data, counter) {
	if (selectedArray.length > 0) {
		for (i = 0; i < selectedArray.length; i++) {
			let id = selectedArray[i].id.replace(
				"selectedRoles", '').replace("remove", "");
			$('table#tabnew tr#option' + id).remove();

		}
	}
	for (i = 0; i < data.length; i++) {
		for (var j of selectedArray) {
			let id = j.id.replace(
				"selectedRoles", '');
			if ((data[i].funcid) == id) {
				counter++;
			}
		}
	}
	
}

function postDiscardAction(action) {
        document.querySelector(".button").disabled = true;
		var url = action;
		var roleId = $("#roleId").val();
		var data = "roleId," + roleId  ;
		postData(url, data);
 }
	

function removeTag(funcId, arg1) {
$("#errMsg2").hide();
var id  = funcId;
var roleNameData = arg1;
 var roleName = "'" + roleNameData + "'";
$('#optionList')
.append(
'<tr class="optionRoles" id="option'
+ id
+ '"><td>'
+ roleNameData
+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right" onclick="addToAssignedList('
+ id + ',' + roleName + ')"></td></tr>');
$('.dataTables_empty').remove();
$('#remove' + id).remove();
$('#changeFlag').val('true');
}


function addToAssignedList(id, arg1) {
$("#errMsg2").hide();
	var moduleId = document.getElementById("moduleId").value;
	moduleId = $('<div>').text(moduleId).html();
	var roleName = "'" + arg1 + "'";
	roleName = $('<div>').text(roleName).html();

	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}
	$('#assignedList')
			.append(
					'<tr class="selectedRoles" value="'
							+ arg1
							+ '" id="remove'
							+ id
							+ '"><td >'
							+ arg1
							+ '</td><td><i class="glyphicon glyphicon-remove-circle" style="color: blue" onclick="removeTag('
							+ id + ',' + roleName
							+ ')" ></i><input type="hidden" id="row' + id
							+ '" value="' + moduleId + '" ></td></tr>');

	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');

	$('#option' + id).remove();
}

function selectAll(_arg) {
$("#errMsg2").hide();
	let i="";
	if (document.getElementById("checkBoxSet").checked) {
		
		for ( i = 0; i < optionFunctionalityList.length; i++) {
			document.getElementById(optionFunctionalityList[i].id).click();
			
	}
	
	} else {
		for (i = 0; i < optionFunctionalityList.length; i++) {
			document.getElementById("icon" + optionFunctionalityList[i].id)
					.click();
		}
	}
}

function removeAll() {
$("#errMsg2").hide();
	var removeData = new Array();
	let i ="";
	var removeList = document.getElementsByClassName("addedList");
	for ( i = 0; i < removeList.length; i++) {
		removeData.push(removeList[i].id.replace("added", ""));
	}
	
	for ( i = 0; i < removeData.length; i++) {
		
		document.getElementById("icon" + removeData[i]).click();
	}
	removeList = document.getElementsByClassName("addedList");
	if (removeList.length == 0) {
		$('#remove').remove();
	}
}

function redirectToGrid() {
	window.location.href = "showRole";
}

$(document).ready(function() {

	let oTable="";
	if (!$.fn.DataTable.isDataTable('#tabnew1')) {

	 oTable = $('#tabnew1').DataTable({
			"bSort" : false,
			"bFilter" : false,
			"bJQueryUI" : true,
			"bPaginate" : false,
			"bLengthChange" : false,
			"bInfo" : false,
			"bAutoWidth" : false
		});
	}

	$('#oTable tbody').on('click', 'tr', function() {

		if ($(this).hasClass('selected')) {
			$(this).removeClass('selected');
		} else {
			table.$('tr.selected').removeClass('selected');
			$(this).addClass('selected');
		}
	});
	oTable.row('.selected').remove().draw(false);

	$("#ap").click(function() {
	
	console.log("ap");
	});

	$('#subMitForm').click(function() {
		return false;
	});
	if (!$.fn.DataTable.isDataTable('#tabnew1')) {

		$('#tabnew1').dataTable({
			"bSort" : false,
			"bFilter" : false,
			"bJQueryUI" : true,
			"bPaginate" : false,
			"bLengthChange" : false,
			"bInfo" : false,
			"bAutoWidth" : false
		});
	}
});



function retrieveAllFunctionalityList(url, data) {
$("#errMsg2").hide();
	var makChkFlag = $("#makChkFlag").val().replace("/^\s*|\s*$/g", '');
	 var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var selectedArray = new Array();
	selectedArray = document.getElementsByClassName("selectedRoles");
	
	var allFuncCount;
	$.ajax({
		url : "retrieveAllFunctionalityList",
		type : "POST",
		dataType : "json",
		data : {
			"makChkFlag" : makChkFlag,
			"_TransactToken" : tokenValue
		},
		success : function(response) {
			

			if (selectedArray.length == response.count) {
				$('html, body').animate({
					scrollTop : 0
				}, 'slow');
				$('#jqueryError').text(
						'SUPER ADMIN ' + makChkFlag + ' already exist');
				$('#jqueryError').show();
			} else {
			    document.querySelector(".button").disabled = true; 
				postData(url, data);
			}
			allFuncCount = response.count;
		},
		error : function(_request, _status, _error) {
		
		console.log("error")
		}
	});
	return allFuncCount;
}

function removeAllFunctionalities(){
	$("#errMsg2").hide();


	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}
	
		
	var	selectedArray = document.getElementsByClassName("selectedRoles");
	
	if (selectedArray.length > 0 ) {
		for (var i of selectedArray) {
			var id = i.id.replace(
					"option", '').replace("remove", "");
					id = $('<div>').text(id).html();
			var roleNameData=i.innerText;
			roleNameData = $('<div>').text(roleNameData).html();
			var roleName = "'" + roleNameData + "'";
			roleName = $('<div>').text(roleName).html();
			 
			$('#optionList')
			.append(
					'<tr class="optionRoles" id="option'
							+ id
							+ '"><td>'
							+ roleNameData
							+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right" onclick="addToAssignedList('
							+ id + ',' + roleName + ')"></td></tr>');
}
		$('#assignedList').empty();
	
	}
	
	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');
	
}


function selectAllFunctionlities() {
$("#errMsg2").hide();
	var moduleId = document.getElementById("moduleId").value;

moduleId = $('<div>').text(moduleId).html();
	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}
	
	
		
		var optionsArray = document.getElementsByClassName("optionRoles");
		
	
	if (optionsArray.length > 0 ) {
		for (var i of optionsArray) {
			var id = i.id.replace(
					"option", '').replace("remove", "");
					id = $('<div>').text(id).html();
			var roleNameData=i.innerText;
			roleNameData = $('<div>').text(roleNameData).html();
			var roleName = "'" + roleNameData + "'";
			roleName = $('<div>').text(roleName).html();
			 
			$('#assignedList')
			.append(
					'<tr class="selectedRoles" value="'
							+ roleNameData
							+ '" id="remove'
							+ id
							+ '"><td >'
							+ roleNameData
							+ '</td><td><i class="glyphicon glyphicon-remove-circle" style="color: blue" onclick="removeTag('
							+ id + ',' + roleName
							+ ')" ></i><input type="hidden" id="row' + id
							+ '" value="' + moduleId + '" ></td></tr>');
			
		}
		$('#optionList').empty();
	}
	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');
	
	
}

function loadEditRole(roleId) {
	
let url = '/getRole';	
	var data = "rid," + roleId ;
	postData(url, data);
}

function loadDelete(rid, action) {
    let url = action;
    var data = "rid," + rid + ",userType," + 'N'  ;
    postData(url, data);
}

function backAction(type, action) {
		let url = action;
		var data = "userType," + type ;
		postData(url, data);
	}