<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/viewApproveTipSurcharge.js" type="text/javascript"></script>

<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">
		<span data-i18n="Data"><spring:message
				code="am.lbl.appRejAction" /></span>
	</div>
	<div class="alert alert-danger remarkMust" role="alert">
		<span data-i18n="Data"><spring:message code="sm.lbl.remarkMust" /></span>
	</div>
	<c:url value="approveTipSurcharge" var="approveTipSurcharge" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveTipSurcharge" modelAttribute="tipSurchargeDto"
		action="${approveTipSurcharge}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="tipSurcharge.viewscreen.title" /></span></strong>
					</div>
					<div class="panel-body">
						<input type="hidden" id="tipSurchargeId" value="${tipSurchargeDto.tipSurchargeId}" />
						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">Tip Surcharge</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6">
									<div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> 
											<span data-i18n="Data"><spring:message code="tipSurcharge.requestInformation" /></span></strong>
									</div></td>
								</tr>
								<tr>
									<td><label><spring:message code="tipSurcharge.requestType" /><span style="color: red"></span></label></td>
									<td>${tipSurchargeDto.lastOperation}</td>
									<td><label><spring:message code="tipSurcharge.requestDate" /><span style="color: red"></span></label></td>
									<c:choose>
											<c:when test="${tipSurchargeDto.lastUpdatedOn == null}">
												<td>${tipSurchargeDto.createdOn}</td>
											</c:when>
											<c:otherwise>
												<td>${tipSurchargeDto.lastUpdatedOn}</td>
												</c:otherwise>
										</c:choose>
									
									<td><label><spring:message code="tipSurcharge.requestStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${tipSurchargeDto.requestState =='A' }"><spring:message	code="tipSurcharge.requestState.approved.description" /></c:if>
										<c:if test="${tipSurchargeDto.requestState =='P' }"><spring:message code="tipSurcharge.requestState.pendingApproval.description" /></c:if>
										<c:if test="${tipSurchargeDto.requestState =='R' }"><spring:message code="tipSurcharge.requestState.rejected.description" /></c:if>
										<c:if test="${tipSurchargeDto.requestState =='D' }"><spring:message code="tipSurcharge.requestState.discared.description" /></c:if>
									</td>
								</tr>
								<tr>
									<td><label><spring:message code="tipSurcharge.requestBy" /><span style="color: red"></span></label></td>
									<c:choose>
											<c:when test="${tipSurchargeDto.lastUpdatedBy == null}">
												<td>${tipSurchargeDto.createdBy}</td>
											</c:when>
											<c:otherwise>
												<td>${tipSurchargeDto.lastUpdatedBy}</td>
												</c:otherwise>
										</c:choose>
									<td><label><spring:message code="tipSurcharge.approverComments" /><span style="color: red"></span></label></td>
									<td colspan=2>${tipSurchargeDto.checkerComments}</td>
									<td></td>
									
								</tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-credit-card"></span> <span data-i18n="Data">
										<spring:message code="tipSurcharge.viewscreen.title" /></span></strong></div>
									</td>
								<tr>
									<td><label><spring:message code="tipSurcharge.tipSurchargeId" /> <span style="color: red"></span></label></td>
									<td>${tipSurchargeDto.tipSurchargeId }</td>
									<td><label><spring:message code="tipSurcharge.tipSurchargeType" /><span style="color: red"></span></label></td>
									<td >${tipSurchargeDto.tipSurType }</td>
									<td><label><spring:message code="tipSurcharge.operator" /><span style="color: red"></span></label></td>
									<td >${tipSurchargeDto.operatorName }</td>
									
								</tr>
								<tr>
									<td><label><spring:message code="tipSurcharge.settlementAmount" /><span style="color: red"></span></label></td>
									<td >${tipSurchargeDto.settlementAmount }</td>
									<td><label><spring:message code="tipSurcharge.amountPercentFlag" /><span style="color: red"></span></label></td>
									<td >${tipSurchargeDto.amtFlag }</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<c:if test="${tipSurchargeDto.amtFlag  eq 'Percent'}">
									<td><label><spring:message code="tipSurcharge.percentage" /><span style="color: red"></span></label></td>
									<td >${tipSurchargeDto.percentage }</td>
									</c:if>
									<c:if test="${tipSurchargeDto.amtFlag  eq 'Amount'}">
									<td><label><spring:message code="tipSurcharge.amount" /><span style="color: red"></span></label></td>
									<td >${tipSurchargeDto.amount }</td>
									</c:if>
									<td><label><spring:message code="tipSurcharge.tipSurchargeName" /><span style="color: red"></span></label></td>
									<td >${tipSurchargeDto.tipSurchargeName }</td>
									<td></td>
									<td></td>	
								</tr>
								<tr>
									<td><label><spring:message code="tipSurcharge.binCardBrand" /><span style="color: red"></span></label></td>
									<td >${tipSurchargeDto.cardBrand }</td>
									<td><label><spring:message code="tipSurcharge.binCardType" /><span style="color: red"></span></label></td>
									<td >${tipSurchargeDto.cardType }</td>
									<td></td>
									<td></td>	
								</tr>
								<sec:authorize access="hasAuthority('Approve Tip Surcharge')">
									<c:if test="${tipSurchargeDto.requestState eq 'P'}">
									<tr>
										<td colspan="6"><div class="panel-heading-red  clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span>
											<span data-i18n="Data">
											<spring:message code="tipSurcharge.approvalPanel.title" /></span></strong></div>
										</td>
									</tr>
									<tr>
										<td><label><spring:message code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td>
												<select name="select" id="apprej" onchange="display()">
													<option value="N"><spring:message code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message code="AM.lbl.reject" /></option>
												</select>
											</td>
											<td>
												<div style="text-align:center">
												<label><spring:message code="AM.lbl.remarks" /><span style="color: red">*</span></label>
												</div>
											</td>
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div>
											</td>
										</tr>
									</c:if>
								</sec:authorize>
							</tbody>
						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Tip Surcharge')">
										<c:if test="${tipSurchargeDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approvecard" value="Submit"
												onclick="postAction('/approveTipSurcharge');" />
										</c:if>
									</sec:authorize>
													
									<sec:authorize access="hasAuthority('Edit Tip Surcharge')">				
									<c:if test="${tipSurchargeDto.requestState  eq 'R' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="userAction('/discardTipSurcharge','${tipSurchargeDto.tipSurchargeId}');" />
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="edit('/editTipSurcharge','${tipSurchargeDto.tipSurchargeId}','pendingApprove');"/>
									</c:if>
									</sec:authorize>
									
										<button type="button" class="btn btn-danger"
										onclick="backAction('P','/tipSurchargeForApproval');">
										<spring:message code="tipSurcharge.backBtn" /></button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

