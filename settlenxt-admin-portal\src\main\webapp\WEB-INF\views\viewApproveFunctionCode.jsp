<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/viewApproveFunctionCode.js" type="text/javascript"></script>

<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">
		<span data-i18n="Data"><spring:message
				code="am.lbl.appRejAction" /></span>
	</div>
	<div class="alert alert-danger remarkMust" role="alert">
		<span data-i18n="Data"><spring:message code="sm.lbl.remarkMust" /></span>
	</div>
	<c:url value="approveTipSurcharge" var="approveTipSurcharge" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveFunctionCode" modelAttribute="functionCodeDto"
		action="${approveFunctionCode}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="functionCode.viewscreen.title" /></span></strong>
					</div>
					<div class="panel-body">
						<input type="hidden" id="funcCodeId" value="${functionCodeDto.funcCodeId}" />
						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">Function Code</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6">
									<div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> 
											<span data-i18n="Data"><spring:message code="functionCode.requestInformation" /></span></strong>
									</div></td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="functionCode.requestType" /><span style="color: red"></span></label></td>
									<td>${functionCodeDto.lastOperation}</td>
									<td><label><spring:message code="functionCode.requestDate" /><span style="color: red"></span></label></td>
									<td>${functionCodeDto.lastUpdatedOn}</td>
									<td><label><spring:message code="functionCode.requestStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${functionCodeDto.requestState =='A' }"><spring:message	code="functionCode.requestState.approved.description" /></c:if>
										<c:if test="${functionCodeDto.requestState =='P' }"><spring:message code="functionCode.requestState.pendingApproval.description" /></c:if>
										<c:if test="${functionCodeDto.requestState =='R' }"><spring:message code="functionCode.requestState.rejected.description" /></c:if>
										<c:if test="${functionCodeDto.requestState =='D' }"><spring:message code="functionCode.requestState.discared.description" /></c:if>
									</td>
									<td></td>
									<td></td>
								</tr>
								<tr>
									<td><label><spring:message code="functionCode.requestBy" /><span style="color: red"></span></label></td>
									<td>${functionCodeDto.lastUpdatedBy}</td>
									<td><label><spring:message code="functionCode.approverComments" /><span style="color: red"></span></label></td>
									<td colspan=2>${functionCodeDto.checkerComments}</td>
									<td></td>
									<td></td>
									<td></td>
									
								</tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-credit-card"></span> <span data-i18n="Data">
										<spring:message code="functionCode.viewscreen.title" /></span></strong></div>
									</td>
								<td></td>
									<td></td>
									<tr>
										
									<td><label><spring:message code="functionCode.mti" /></label></td>
									<td>${functionCodeDto.mti }</td>
									<td><label><spring:message code="functionCode.procCode" /></label></td>
									<td >${functionCodeDto.procCode }</td>
									<td><label><spring:message code="functionCode.funcCode" /></label></td>
									<td >${functionCodeDto.funcCode }</td>
									<td></td>
									<td></td>
									</tr>
									<tr>
										
									<td><label><spring:message code="functionCode.funcCodeDesc" /></label></td>
									<td>${functionCodeDto.funcCodeDesc }</td>
									<td><label><spring:message code="functionCode.feeType" /></label></td>
									<td >${functionCodeDto.feeType }</td>
									<td><label><spring:message code="functionCode.fundMovement" /></label></td>
									<td >${functionCodeDto.fundMovement }</td>
									<td></td>
									<td></td>
									</tr>
									<tr>
										
									<td><label><spring:message code="functionCode.fundMovementSide" /></label></td>
									<td>${functionCodeDto.fundMovementSide }</td>
									<td><label><spring:message code="functionCode.recalculate" /></label></td>
									<td >${functionCodeDto.recalculate }</td>
									<td><label><spring:message code="functionCode.transactionType" /></label></td>
									<td >${functionCodeDto.transactionType }</td>
									<td><label><spring:message code="functionCode.networkTxnType" /></label></td>
									<td >${functionCodeDto.networkTxnType }</td>
									</tr>
								
								
								<sec:authorize access="hasAuthority('Approve Function Code')">
									<c:if test="${functionCodeDto.requestState eq 'P'}">
									<tr>
										<td colspan="6"><div class="panel-heading-red  clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span>
											<span data-i18n="Data">
											<spring:message code="functionCode.approvalPanel.title" /></span></strong></div>
										</td>
										<td></td>
									<td></td>
									</tr>
									<tr>
										<td><label><spring:message code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td>
												<select name="select" id="apprej" onchange="display()">
													<option value="N"><spring:message code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message code="AM.lbl.reject" /></option>
												</select>
											</td>
											<td>
												<div style="text-align:center">
												<label><spring:message code="AM.lbl.remarks" /><span style="color: red">*</span></label>
												</div>
											</td>
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div>
											</td>
										</tr>
									</c:if>
								</sec:authorize>
							</tbody>
						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Function Code')">
										<c:if test="${functionCodeDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approvecard" value="Submit"
												onclick="postAction('/approveFunctionCode');" />
										</c:if>
									</sec:authorize>
													
									<sec:authorize access="hasAuthority('Edit Function Code')">				
									<c:if test="${functionCodeDto.requestState  eq 'R' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="userAction('/discardFunctionCode','${functionCodeDto.funcCodeId}');" />
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="userAction('/editFunctionCode','${functionCodeDto.funcCodeId}');"/>
									</c:if>
									</sec:authorize>
									
										<button type="button" class="btn btn-danger"
										onclick="backAction('P','/functionCodeForApproval');">
										<spring:message code="functionCode.backBtn" /></button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

