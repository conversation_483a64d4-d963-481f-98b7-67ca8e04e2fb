package org.npci.settlenxt.adminportal.service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.birt.core.exception.BirtException;
import org.eclipse.birt.core.framework.Platform;
import org.eclipse.birt.report.engine.api.EXCELRenderOption;
import org.eclipse.birt.report.engine.api.EngineConfig;
import org.eclipse.birt.report.engine.api.EngineException;
import org.eclipse.birt.report.engine.api.IRenderOption;
import org.eclipse.birt.report.engine.api.IReportEngine;
import org.eclipse.birt.report.engine.api.IReportEngineFactory;
import org.eclipse.birt.report.engine.api.IReportRunnable;
import org.eclipse.birt.report.engine.api.IRunAndRenderTask;
import org.eclipse.birt.report.model.api.DesignElementHandle;
import org.eclipse.birt.report.model.api.OdaDataSourceHandle;
import org.eclipse.birt.report.model.api.PropertyHandle;
import org.eclipse.birt.report.model.api.ReportDesignHandle;
import org.eclipse.birt.report.model.api.SlotHandle;
import org.eclipse.birt.report.model.api.core.IModuleModel;
import org.npci.base.commonlib.utils.security.AES;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.common.util.FieldConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.NewsAlertsRepository;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.EmailGateWayDTO;
import org.npci.settlenxt.portal.common.dto.NewsAlertsDTO;
import org.npci.settlenxt.portal.common.dto.NewsDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.npci.settlenxt.portal.common.util.TransactConstant;
import org.npci.settlenxt.portal.common.util.Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;

import com.ibm.icu.text.DateFormatSymbols;

@Service
public class NewsAlertsServiceImpl implements NewsAlertsService {

	@Autowired
	private NewsAlertsRepository newsAlertsRepository;

	@Autowired
	private UserService userService;
	@Autowired
	BaseLookupService lookUpService;

	@Autowired
	private SessionDTO sessionDTO;

	@Autowired
	EmailGateWayUtil emailGateWayUtil;

	@Autowired
	Environment environment;

	static final String TIME = " 23:59:59";
	static final String WEEKLY = "Weekly";
	static final String MONTHLY = "Monthly";
	static final String SUCCESS_STATUS = "successStatus";
	static final String NEWS_SUCCESS_MSG = "News and Alerts Approved Successfully";

	public Long getRowCount() {
		return newsAlertsRepository.getTotalCount();
	}

	public Long getRowCountApproval() {
		return newsAlertsRepository.getApprovalTotalCount();
	}

	public Long getSavedRowCount() {
		return newsAlertsRepository.getSavedTotalCount();
	}

	public Long getDeleteRowCount() {
		return newsAlertsRepository.getDeleteTotalCount();
	}

	public List<NewsAlertsDTO> getFinalNewsAlertsList(SearchCriteriaDTO searchCriteriaDTO) {

		return newsAlertsRepository.getFinalNewsAlertsList(searchCriteriaDTO.getStartVal(),
				searchCriteriaDTO.getEndVal());
	}

	public List<NewsAlertsDTO> getTempNewsAlertsList(SearchCriteriaDTO searchCriteriaDTO) {

		return newsAlertsRepository.getTempNewsAlertsList(searchCriteriaDTO.getStartVal(),
				searchCriteriaDTO.getEndVal());

	}

	public List<NewsAlertsDTO> getSavedNewsAlertsList(SearchCriteriaDTO searchCriteriaDTO) {

		return newsAlertsRepository.getSavedNewsAlertsList(searchCriteriaDTO.getStartVal(),
				searchCriteriaDTO.getEndVal());
	}

	public List<NewsAlertsDTO> getDeletedNewsAlertsList(SearchCriteriaDTO searchCriteriaDTO) {

		return newsAlertsRepository.getDeletedNewsAlertsList(searchCriteriaDTO.getStartVal(),
				searchCriteriaDTO.getEndVal());
	}

	@Override
	public void addDefaultListData(Model model) {
		model.addAttribute("periodTypeList", lookUpService.getLookupData(CommonConstants.PERIOD_TYPE));
		String[] days = new DateFormatSymbols().getWeekdays();
		List<String> dayss = Arrays.asList(days);
		List<CodeValueDTO> weekList = lookUpService.getLookupData(CommonConstants.WEEK_LIST);
		List<CodeValueDTO> monthList = lookUpService.getLookupData(CommonConstants.MONTH_LIST);
		Collections.sort(weekList, Comparator.comparing(item -> dayss.indexOf(item.getDescription())));
		model.addAttribute("weekList", weekList);
		monthList.sort(Comparator.comparing(x -> Integer.parseInt(x.getDescription())));
		model.addAttribute("monthList", monthList);
		model.addAttribute("bankName", userService.getParticipantList());
		model.addAttribute("RoleList", userService.getRoleTypes());

	}

	@Override
	public NewsDTO addNewsInfo(NewsAlertsDTO newsAlertsDTO) {
		NewsDTO newsDTO = new NewsDTO();

		try {
			SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");

			newsDTO.setIsType(newsAlertsDTO.getIsType());
			newsDTO.setTitle(newsAlertsDTO.getTitle());
			newsDTO.setSubTitle(newsAlertsDTO.getSubTitle());
			newsDTO.setFromDateStr(newsAlertsDTO.getFromDateStr());
			if (null != newsAlertsDTO.getFromDateStr()) {

				Date fromDate = dateFormat.parse(String.valueOf(newsAlertsDTO.getFromDateStr()));
				newsDTO.setFromDate(fromDate);
			}
			newsDTO.setToDateStr(newsAlertsDTO.getToDateStr());
			if (null != newsAlertsDTO.getToDateStr()) {
				SimpleDateFormat dateFormat1 = new SimpleDateFormat("dd-MM-yyyy hh:mm:ss");
				String todates = "";
				todates = newsAlertsDTO.getToDateStr() + TIME;
				Date toDate = dateFormat1.parse(todates);
				newsDTO.setToDate(toDate);
			}
			newsDTO.setSummary(newsAlertsDTO.getSummary());
			newsDTO.setDetails(newsAlertsDTO.getDetails());
			newsDTO.setFooterData(newsAlertsDTO.getFooterData());

			newsDTO.setPublishType(newsAlertsDTO.getPublishType());
			newsDTO.setSendMail(newsAlertsDTO.getSendMail());
			newsDTO.setCreatedOn(new Date());
			newsDTO.setCreatedBy(sessionDTO.getUserName());
			newsDTO.setRequestState("I");
			newsDTO.setStatus("I");
			newsDTO.setCritical(newsAlertsDTO.getCritical());
			newsDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);

			newsAlertsDTO.setNewsId(fetchNewsIdSeq());
			newsDTO.setNewsId(newsAlertsDTO.getNewsId());
			newsDTO.setReferenceNumber("NPCI" + "/" + newsAlertsDTO.getIsType() + "/" + newsDTO.getNewsId());
			int result = newsAlertsRepository.addNewsAlerts(newsDTO);
			if (result == 1) {
				newsDTO.setSaveNewsResult("News/Alerts Saved");
			}
		} catch (Exception ex) {

			throw new SettleNxtException("Error In Saving News & Alerts", "", ex);
		}

		return newsDTO;
	}

	@Override
	public int fetchNewsIdSeq() {
		return newsAlertsRepository.fetchNewsIdSeq();
	}

	@Override
	public int fetchReferenceNoSeq() {
		return newsAlertsRepository.fetchReferenceNoSeq();
	}

	@Override
	public int updateNewsInfo(NewsAlertsDTO newsAlertsDTO) {

		try {
			newsAlertsDTO.setStatus("A");

			if (null != newsAlertsDTO.getToDateStr()) {
				SimpleDateFormat dateFormat1 = new SimpleDateFormat("dd-MM-yyyy hh:mm:ss");
				String todates = "";
				todates = newsAlertsDTO.getToDateStr() + TIME;
				Date toDate = dateFormat1.parse(todates);
				Date fromDate = dateFormat1.parse(newsAlertsDTO.getFromDateStr() + " 00:00:00");
				newsAlertsDTO.setFromDate(fromDate);
				newsAlertsDTO.setToDate(toDate);
			}
			long count = newsAlertsRepository.checkIfExistsInMain(newsAlertsDTO.getNewsId());
			if (count > 0) {
				newsAlertsDTO.setLastUpdatedBy(sessionDTO.getUserName());
				newsAlertsDTO.setLastUpdatedOn(new Date());
				newsAlertsDTO.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
				newsAlertsDTO.setStatus(newsAlertsDTO.getStatus());
			} else {
				newsAlertsDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
				newsAlertsDTO.setLastUpdatedBy(null);
				newsAlertsDTO.setLastUpdatedOn(null);
				newsAlertsDTO.setStatus(newsAlertsDTO.getStatus());
			}

			if (newsAlertsDTO.getPeriodType().equalsIgnoreCase(WEEKLY)) {
				newsAlertsDTO.setFreqFrom(newsAlertsDTO.getWeekFrom());
				newsAlertsDTO.setFreqTo(newsAlertsDTO.getWeekTo());
			} else if (newsAlertsDTO.getPeriodType().equalsIgnoreCase(MONTHLY)) {
				newsAlertsDTO.setFreqFrom(newsAlertsDTO.getMonthFrom());
				newsAlertsDTO.setFreqTo(newsAlertsDTO.getMonthTo());
			}
		} catch (Exception ex) {
			throw new SettleNxtException("Error In Saving News & Alerts", "", ex);
		}
		return newsAlertsRepository.updateNewsInfo(newsAlertsDTO);
	}

	@Override
	public int updateSchedulerInfo(NewsAlertsDTO newsAlertsDTO) {
		long count = newsAlertsRepository.checkIfExistsInMain(newsAlertsDTO.getNewsId());
		if (count > 0) {
			newsAlertsDTO.setLastUpdatedBy(sessionDTO.getUserName());
			newsAlertsDTO.setLastUpdatedOn(new Date());
			newsAlertsDTO.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
		} else {
			newsAlertsDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
			newsAlertsDTO.setLastUpdatedBy(null);
			newsAlertsDTO.setLastUpdatedOn(null);
		}

		if (newsAlertsDTO.getPeriodType().equalsIgnoreCase(WEEKLY)) {
			newsAlertsDTO.setFreqFrom(newsAlertsDTO.getWeekFrom());
			newsAlertsDTO.setFreqTo(newsAlertsDTO.getWeekTo());
		} else if (newsAlertsDTO.getPeriodType().equalsIgnoreCase(MONTHLY)) {
			newsAlertsDTO.setFreqFrom(newsAlertsDTO.getMonthFrom());
			newsAlertsDTO.setFreqTo(newsAlertsDTO.getMonthTo());
		}
		return newsAlertsRepository.updateSchedulerInfo(newsAlertsDTO);
	}

	@Override
	public Boolean checkNewsInfoSaved(String title) {
		return newsAlertsRepository.checkNewsInfoSaved(title) > 0;
	}

	@Override
	public NewsDTO getNewsAlerts(String refNumber) {
		NewsDTO newsDTO = newsAlertsRepository.getNewsAlertsEdit(refNumber);
		if (WEEKLY.equalsIgnoreCase(newsDTO.getPeriodType())) {
			newsDTO.setWeekFrom(newsDTO.getFreqFrom());
			newsDTO.setWeekTo(newsDTO.getFreqTo());
		} else if (MONTHLY.equalsIgnoreCase(newsDTO.getPeriodType())) {
			newsDTO.setMonthFrom(newsDTO.getFreqFrom());
			newsDTO.setMonthTo(newsDTO.getFreqTo());
		}
		return newsDTO;
	}

	@Override
	public Map<Integer, Object> getNewsAlerts(SearchCriteriaDTO searchCriteriaDTO) {
		return getNewsAlertsInfo(searchCriteriaDTO);
	}

	public Map<Integer, Object> getNewsAlertsInfo(SearchCriteriaDTO searchCriteriaDTO) {
		NewsDTO newsDTO = new NewsDTO();
		NewsAlertsDTO newsAlertsDTO = new NewsAlertsDTO();
		Map<Integer, Object> map = new HashMap<>();
		try {
			if (searchCriteriaDTO.getSearchType() == TransactConstant.VTRANSACT_SEARCH_FROM_FINAL_REPOSITORY) {
				newsAlertsDTO = newsAlertsRepository.getNewsAlerts(searchCriteriaDTO.getReferenceNumber());

				map.put(1, newsAlertsDTO);
			} else {
				newsDTO = newsAlertsRepository.getNewsAlertsInfo(searchCriteriaDTO.getReferenceNumber());
				newsAlertsDTO = newsAlertsRepository.getNewsAlerts(searchCriteriaDTO.getReferenceNumber());
				map.put(1, newsAlertsDTO);
				map.put(2, newsDTO);
			}
			newsAlertsDTO.setStatusCode(TransactConstant.VTRANSACT_SUCCESS);
			return map;
		} catch (EmptyResultDataAccessException ex) {
			if (newsAlertsDTO != null) {

				map.put(1, newsAlertsDTO);
				return map;
			}
		} catch (Exception ex) {
			newsAlertsDTO.setStatusCode(TransactConstant.VTRANSACT_FAIL);
		} finally {

			map = null;
		}

		return map;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
	public void approveNewsAlerts(NewsDTO newsDTO, Model model) {

		String remarks = newsDTO.getRemarks();
		String status = newsDTO.getStatus();

		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
		searchCriteriaDTO.setSearchType(TransactConstant.VTRANSACT_SEARCH_FROM_TEMP_REPOSITORY);
		searchCriteriaDTO.setNewsId(newsDTO.getNewsId());
		searchCriteriaDTO.setReferenceNumber(newsDTO.getReferenceNumber());

		Map<Integer, Object> newsMap = getNewsAlerts(searchCriteriaDTO);
		newsDTO = (NewsDTO) newsMap.get(2);

		newsDTO.setApprovalStatus(status);
		newsDTO.setApprovalRemarks(remarks);
		newsDTO.setLastUpdatedBy(sessionDTO.getUserName());
		newsDTO.setLastUpdatedOn(new Date());

		if ("A".equals(newsDTO.getApprovalStatus())) {

			if ("D".equalsIgnoreCase(newsDTO.getStatus())) {

				newsDTO.setLastOperation(CommonConstants.LAST_OPERATION_DELETE);
				newsDTO.setStatus(CommonConstants.REQUEST_STATE_DISCARDED);
				newsDTO.setRequestState(CommonConstants.REQUEST_STATE_DISCARDED);
				newsDTO.setLastUpdatedBy(sessionDTO.getUserName());
				newsDTO.setLastUpdatedOn(new Date());
				newsAlertsRepository.updateNewsAlertStgDeactivate(newsDTO);
				newsAlertsRepository.updateDistributionStgDeactivate(newsDTO);
				newsAlertsRepository.updateNewsAlertStgDeactivateMain(newsDTO);
				newsAlertsRepository.updateDistributionStgDeactivateMain(newsDTO);
				model.addAttribute(SUCCESS_STATUS, NEWS_SUCCESS_MSG);

			} else {
				newsDTO.setStatus("A");
				NewsDTO newsDTOs = createAndUpdateFinalNewsAlerts(newsDTO);

				setNewsStatus(newsDTO, model, newsDTOs);
			}
		} else {
			try {
				newsDTO.setStatus("A");
				newsAlertsRepository.newsAlertsRejectStg(newsDTO.getNewsId(), newsDTO.getApprovalRemarks(),
						CommonConstants.LAST_OPERATION_REJECT);
				newsAlertsRepository.updateDistributionStgDeactivate(newsDTO);

				model.addAttribute(SUCCESS_STATUS, "News/Alerts Details Rejected by Checker ");
			} catch (Exception e) {

				newsDTO.setStatusCode(CommonConstants.TRANSACT_FAIL);
				model.addAttribute(SUCCESS_STATUS, "News/Alerts Details Rejected by Checker Failed ");
			}
		}
		model.addAttribute("newsDTO", newsDTO);
		model.addAttribute("Approved", "Yes");
	}

	private void setNewsStatus(NewsDTO newsDTO, Model model, NewsDTO newsDTOs) {
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(newsDTOs.getStatusCode())) {

			if (null != newsDTO.getSendMail() && !newsDTO.getPublishType().equalsIgnoreCase("specific")) {
				if ("Y".equals(newsDTO.getSendMail()) && (newsDTO.getPublishType().equalsIgnoreCase("public")
						|| newsDTO.getPublishType().equalsIgnoreCase("common"))) {

					prepareEmailContent(newsDTO);
					
					model.addAttribute(SUCCESS_STATUS, NEWS_SUCCESS_MSG);

				} else {
					model.addAttribute(SUCCESS_STATUS, NEWS_SUCCESS_MSG);
				}
			} else {
				model.addAttribute(SUCCESS_STATUS, NEWS_SUCCESS_MSG);
			}
		} else {
			model.addAttribute("errorStatus", "News and Alerts Approve Failed");
		}
	}

	private void prepareEmailContent(NewsDTO newsDTO) {
		String mailContent = "";
		String purposeCode = "";
		String newsMailContent = "";
		purposeCode = CommonConstants.TRANSACT_NEWS_ALL_USERS;
		EmailGateWayDTO emailGateWayDTO = emailGateWayUtil.fetchEmailContentData(purposeCode);

		mailContent = emailGateWayDTO.getEmailContent();
		String result = Utils.generateDelimiteredString(" ",emailGateWayDTO.getSubject(),newsDTO.getTitle());
		emailGateWayDTO.setSubject(result);
		String newsDetail=newsDTO.getDetails();

		newsDetail=newsDetail.replace("\\n", " <br><br>");
		newsDetail=newsDetail.replace("(<br><br>){2,}", "<br><br> ");
		
		
		
		
		newsMailContent = mailContent.replace("DETAILS", newsDetail);
		
		emailGateWayDTO.setUserId(sessionDTO.getUserId());
		emailGateWayDTO.setReceiverEmailID("");
		emailGateWayDTO.setPurpose(purposeCode);
		emailGateWayDTO.setEmailContent(newsMailContent);
		emailGateWayDTO.setIsPwdEmail("N");
		emailGateWayDTO.setStatus("P");
		emailGateWayDTO.setStatusDesc("Email Pending");
		
		emailGateWayUtil.logEmailData(emailGateWayDTO);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
	public NewsDTO createAndUpdateFinalNewsAlerts(NewsDTO newsDTO) {

		try {

			if (CommonConstants.LAST_OPERATION_ADD.equalsIgnoreCase(newsDTO.getLastOperation())) {
				newsDTO.setCreatedOn(new Date());
				newsDTO.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
				newsDTO.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);

				newsAlertsRepository.updateNewsAlertApproveStg(newsDTO);
				newsAlertsRepository.updateDistributionStgDeactivate(newsDTO);

				newsAlertsRepository.addNewsAlertsFinal(newsDTO);
				List<NewsAlertsDTO> distributionList = newsAlertsRepository.fetchDistributionList(newsDTO.getNewsId());
				if (CollectionUtils.isNotEmpty(distributionList)) {
					newsAlertsRepository.deleteDistributionMain(newsDTO.getNewsId());
					for (NewsAlertsDTO news : distributionList) {
						news.setCreatedOn(new Date());
						newsAlertsRepository.addNewsDistributionInfo(news);
					}
				}
				newsDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

			}

			if (CommonConstants.LAST_OPERATION_EDIT.equalsIgnoreCase(newsDTO.getLastOperation())) {
				newsDTO.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
				newsDTO.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
				newsAlertsRepository.updateNewsAlertApproveStg(newsDTO);

				newsAlertsRepository.updateNewsAlertsInfoMain(newsDTO);
				List<NewsAlertsDTO> distributionList = newsAlertsRepository.fetchDistributionList(newsDTO.getNewsId());
				if (CollectionUtils.isNotEmpty(distributionList)) {
					newsAlertsRepository.deleteDistributionMain(newsDTO.getNewsId());
					for (NewsAlertsDTO news : distributionList) {
						newsAlertsRepository.addNewsDistributionInfo(news);
					}
				}
				newsDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

			}
		} catch (Exception e) {

			newsDTO.setStatusCode(CommonConstants.TRANSACT_FAIL);
			throw new SettleNxtException("Failed to add News/Alerts Details for NewsId : " + newsDTO.getNewsId(), "",
					e);

		}
		return newsDTO;
	}

	@Override
	public NewsAlertsDTO discardNewsAndAlerts(String referenceNumber) {
		NewsAlertsDTO newsAlertsDTO = getNewsAndAlerts(referenceNumber);
		NewsAlertsDTO newsAlertsDTOMain = newsAlertsRepository.getnewsAlertsMain(referenceNumber);
		if (newsAlertsDTOMain != null) {
			newsAlertsDTOMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			newsAlertsDTOMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);

			newsAlertsRepository.updateNewsInfo(newsAlertsDTOMain);
			addDistributionDetails(newsAlertsDTOMain);

			return newsAlertsDTOMain;
		} else {
			newsAlertsRepository.deleteDiscardedEntry(newsAlertsDTO);
			newsAlertsRepository.deleteExistingDistributiondetails(newsAlertsDTO.getNewsId());

		}

		return newsAlertsDTO;
	}

	public NewsAlertsDTO getNewsAndAlerts(String referenceNumber) {

		return newsAlertsRepository.getNewsAlerts(referenceNumber);
	}

	@Override
	public int addDistributionDetails(NewsAlertsDTO newsAlertsDTO) {
		long count = newsAlertsRepository.checkIfExistsInMain(newsAlertsDTO.getNewsId());
		if (count > 0) {
			newsAlertsDTO.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
		} else {
			newsAlertsDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
		}
		List<NewsAlertsDTO> distributionList = newsAlertsRepository.fetchDistributionList(newsAlertsDTO.getNewsId());
		newsAlertsRepository.deleteExistingDistributiondetails(newsAlertsDTO.getNewsId());

		if (newsAlertsDTO.getPublishType() != null) {

			if (newsAlertsDTO.getPublishType().equalsIgnoreCase(CommonConstants.PUBLISH_TYPE_SPECIFIC)) {

				if (!newsAlertsDTO.getUsers().isEmpty()) {
					String[] tableIdArray = newsAlertsDTO.getUsers().split("\\,");

					setNewsAlertVals(newsAlertsDTO, distributionList, tableIdArray);
				}
			} else {
				NewsDTO newsDistDTO = new NewsDTO();
				String x = "-1";
				newsDistDTO.setUsers(x);
				newsDistDTO.setBankName(x);
				newsDistDTO.setRoles(x);
				newsDistDTO.setPublishType(newsAlertsDTO.getPublishType());
				newsDistDTO.setNewsId(newsAlertsDTO.getNewsId());
				newsDistDTO.setCreatedOn(new Date());
				if (!distributionList.isEmpty()) {
					newsDistDTO.setCreatedOn(distributionList.get(0).getCreatedOn());
				}
				newsDistDTO.setCreatedBy(sessionDTO.getUserName());
				newsDistDTO.setStatus(CommonConstants.USER_STATUS_ACTIVE);
				newsAlertsRepository.addNewsDistributionDetails(newsDistDTO);
			}
		}
		return 1;

	}

	private void setNewsAlertVals(NewsAlertsDTO newsAlertsDTO, List<NewsAlertsDTO> distributionList, String[] tableIdArray) {
		for (String tableId:tableIdArray) {
			NewsDTO newsDistDTO = new NewsDTO();
			newsDistDTO.setUsers(tableId);
			newsDistDTO.setPublishType(newsAlertsDTO.getPublishType());
			NewsDTO tempDTO = newsAlertsRepository.getParticipantByUserName(newsDistDTO.getUsers());
			if (tempDTO != null) {
				newsDistDTO.setBankName(tempDTO.getBankName());
				newsDistDTO.setRoles(tempDTO.getRoles());
				newsDistDTO.setNewsId(newsAlertsDTO.getNewsId());
				newsDistDTO.setCreatedOn(new Date());
				if (!distributionList.isEmpty()) {
					for (NewsAlertsDTO distDTO : distributionList) {
						if (distDTO.getUserName().equals(newsDistDTO.getUsers())) {
							newsDistDTO.setCreatedOn(distDTO.getCreatedOn());
						}
					}
				}
				newsDistDTO.setCreatedBy(sessionDTO.getUserName());
				newsDistDTO.setStatus(CommonConstants.USER_STATUS_ACTIVE);
				newsAlertsRepository.addNewsDistributionDetails(newsDistDTO);
			}
		}
	}

	@Override
	public void deleteNewsAlert(NewsDTO newsDto) {

		// Need to set the status as I in news_alert_stg and SCHEDULE_DETAILS
		newsDto.setLastOperation(CommonConstants.LAST_OPERATION_DELETE);
		newsDto.setLastUpdatedBy(sessionDTO.getUserName());
		newsDto.setLastUpdatedOn(new Date());
		newsDto.setStatus("D");
		newsDto.setRequestState(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
		newsAlertsRepository.updateNewsAlertStgDeactivate(newsDto);
		newsAlertsRepository.updateDistributionStgDeactivate(newsDto);

	}

	@Override
	public NewsAlertsDTO newsAlertBirtReportGeneration(String fromDateStr, String toDateStr) throws SettleNxtException,
	BirtException,IOException {
		NewsAlertsDTO newsalertDTO = new NewsAlertsDTO();
		IRunAndRenderTask task = null;
		IReportEngine engine = null;

		
		try {

			EngineConfig config = null;
			String fileName = "SettleNxtExport" + System.nanoTime();
			newsalertDTO.setFileName(fileName);
			newsalertDTO.setRptFileName(CommonConstants.NEWS_ALERT_REPORTS_RPT_NAME);
			newsalertDTO.setFilePath(environment.getProperty(CommonConstants.FILE_DOWNLOAD_PATH));

			config = new EngineConfig();
			
				Platform.startup(config);
				
			
			final IReportEngineFactory factory = (IReportEngineFactory) Platform
					.createFactoryObject(IReportEngineFactory.EXTENSION_REPORT_ENGINE_FACTORY);
			engine = factory.createReportEngine(config);

			IReportRunnable design = null;
			IRenderOption excelOption = null;

			String outFileName = newsalertDTO.getFilePath() + File.separator + fileName + "."
					+ CommonConstants.FILE_TYPE_XLS;
			newsalertDTO.setFilePath(outFileName);

			try(InputStream rptDesign = Thread.currentThread().getContextClassLoader()
					.getResourceAsStream(CommonConstants.NEWS_ALERT_REPORTS_RPT_NAME)){
			design = engine.openReportDesign(rptDesign);
			}
			populateDatabaseConnectionParameters(design);
		   
			task = engine.createRunAndRenderTask(design);
			if(task != null) {
			ReportDesignHandle desingHandle = (ReportDesignHandle) design.getDesignHandle();

			desingHandle.setStringProperty(IModuleModel.TITLE_PROP, CommonConstants.NEWS_ALERT_REPORTS_TITLE);
			if (!toDateStr.contains(TIME)) {
				toDateStr = toDateStr + TIME;
			}

			task.setParameterValue(FieldConstants.FROM_DATE_STR, fromDateStr);
			task.setParameterValue(FieldConstants.TO_DATE_STR, toDateStr);

			newsalertDTO.setFromDateStr(fromDateStr);
			newsalertDTO.setToDateStr(toDateStr);
			excelOption = new EXCELRenderOption();
			excelOption.setOutputFileName(outFileName);
			excelOption.setOutputFormat(CommonConstants.FILE_TYPE_XLS);
			task.setRenderOption(excelOption);
			task.run();
			}
		    
		} catch (EngineException|SettleNxtException e) {

			throw new SettleNxtException("Failed to Generate News/Alerts Report", "", e);
		}  finally {
			if(task != null) {
			task.close();
			}
			if(engine != null) {
			engine.destroy();
			}
		}
		return newsalertDTO;
	}



	@Override
	public boolean fetchCountNewsAlerts(String fromDateStr, String toDateStr) {

		try {
			SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
			SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
			Date fromDate = dateformat.parse(fromDateStr);
			if (!toDateStr.contains(TIME)) {
				toDateStr = toDateStr + TIME;
			}

			Date toDate;

			toDate = dateFormat1.parse(toDateStr);

			long count = newsAlertsRepository.checkIfNewsAlertsExistReports(fromDate, toDate,
					CommonConstants.REQUEST_STATE_APPROVED);
			if (count > 0) {
				return true;
			}

		} catch (ParseException e) {
			throw new SettleNxtException("Parsing Exception", "", e);
		}
		return false;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public List<NewsAlertsDTO> fetchDistributionDetails(int newsId) {
		return newsAlertsRepository.getDistributionDetails(newsId, BaseCommonConstants.ROLE_STATUS_ACTIVE);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
	public String approveNewsAlertsBulk(String bulkApprovalReferenceNoList, NewsDTO newsDTo, Model model) {

		String[] referenceNoArr = bulkApprovalReferenceNoList.split("\\|");

		String remarks = newsDTo.getRemarks();
		String status = newsDTo.getStatus();

		List<String> referenceNoArrayList = Arrays.asList(referenceNoArr);

		List<NewsDTO> newsDto = newsAlertsRepository.getNewsAlertsInfoList(referenceNoArrayList);

		Map<String, List<NewsDTO>> newsMap = newsDto.stream()
				.collect(Collectors.groupingBy(NewsDTO::getReferenceNumber));

		for (String refNum:referenceNoArr) {

			try {
				if (!StringUtils.isEmpty(refNum)) {

					List<NewsDTO> newsDTOtemp = newsMap.get(refNum);
					NewsDTO newsDTO = newsDTOtemp.get(0);
					if (newsDTO == null) {
						newsDTo.setStatusCode(CommonConstants.TRANSACT_FAIL);
						throw new SettleNxtException("Exception occurred with Ref No" + refNum, "");
					}
					else {
						newsDTO.setApprovalStatus(status);
						newsDTO.setApprovalRemarks(remarks);
						newsDTO.setLastUpdatedBy(sessionDTO.getUserName());
						newsDTO.setLastUpdatedOn(new Date());

						setNewsAndEmails(newsDTo, newsDTO);
						model.addAttribute("newsDTO", newsDTO);
						model.addAttribute("Approved", "Yes");
					}
				}

			} catch (Exception ex) {

				newsDTo.setStatusCode(CommonConstants.TRANSACT_FAIL);
				throw new SettleNxtException("Exception for Ref no" + refNum, "", ex);

			}
		}

		return newsDTo.getStatusCode();

	}

	private void setNewsAndEmails(NewsDTO newsDTo, NewsDTO newsDTO) {
		if ("A".equals(newsDTO.getApprovalStatus())) {

			if ("D".equalsIgnoreCase(newsDTO.getStatus())) {

				newsDTO.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
				newsDTO.setStatus(CommonConstants.REQUEST_STATE_DISCARDED);
				newsDTO.setRequestState(CommonConstants.REQUEST_STATE_DISCARDED);
				newsDTO.setLastUpdatedBy(sessionDTO.getUserName());
				newsDTO.setLastUpdatedOn(new Date());
				newsAlertsRepository.updateNewsAlertStgDeactivate(newsDTO);
				newsAlertsRepository.updateDistributionStgDeactivate(newsDTO);
				newsAlertsRepository.updateNewsAlertStgDeactivateMain(newsDTO);
				newsAlertsRepository.updateDistributionStgDeactivateMain(newsDTO);
				newsDTo.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

			} else {
				newsDTO.setStatus("A");
				NewsDTO newsDTOs = createAndUpdateFinalNewsAlerts(newsDTO);

				if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(newsDTOs.getStatusCode())) {

					newsDTo.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
					if (null != newsDTO.getSendMail()
							&& !newsDTO.getPublishType().equalsIgnoreCase("specific")
							&& "Y".equals(newsDTO.getSendMail())
							&& (newsDTO.getPublishType().equalsIgnoreCase("public")
									|| newsDTO.getPublishType().equalsIgnoreCase("common"))) {

						
						prepareEmailContent(newsDTO);
					}
				}
			}
		} else {

			tryblock(newsDTO, newsDTo);

		}
	}

	private void tryblock(NewsDTO newsDTO, NewsDTO newsDTo) {

		try {
			newsAlertsRepository.newsAlertsRejectStg(newsDTO.getNewsId(), newsDTO.getApprovalRemarks(),
					CommonConstants.LAST_OPERATION_REJECT);
			newsAlertsRepository.updateDistributionStgDeactivate(newsDTO);

			newsDTo.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
		} catch (Exception e) {

			newsDTO.setStatusCode(CommonConstants.TRANSACT_FAIL);
			newsDTo.setStatusCode(CommonConstants.TRANSACT_FAIL);

		}

	}

	@Override
	public long checkIfExistsInStg(int newsId) {
		return newsAlertsRepository.checkIfExistsInStg(newsId);

	}

	protected void populateDatabaseConnectionParameters(IReportRunnable pDesign) throws SettleNxtException {

		DesignElementHandle deh = pDesign.getDesignHandle();
		SlotHandle slotHandle = deh.getSlot(IModuleModel.DATA_SOURCE_SLOT);
		

		try {
			Iterator<?> iter = slotHandle.iterator();
			while (iter.hasNext()) {
				Object obj = iter.next();
				OdaDataSourceHandle odaSrcHdl = (OdaDataSourceHandle) obj;
				Iterator<?> propIter = odaSrcHdl.getPropertyIterator();

				while (propIter.hasNext()) {
					PropertyHandle propHdl = (PropertyHandle) propIter.next();

					if (CommonConstants.BIRT_REPORT_DB_URL.equalsIgnoreCase(propHdl.getPropertyDefn().getName())) {
						propHdl.setStringValue(environment.getProperty(CommonConstants.DB_CONFIG_URL));
					} else if (CommonConstants.BIRT_REPORT_USERNAME
							.equalsIgnoreCase(propHdl.getPropertyDefn().getName())) {
						propHdl.setStringValue(environment.getProperty(CommonConstants.DB_CONFIG_USERNAME));
					} else if (CommonConstants.BIRT_REPORT_PD.equalsIgnoreCase(propHdl.getPropertyDefn().getName())) {
						String creds = environment.getProperty(BaseCommonConstants.JDBC_ENC_PASSWORD);
						String secretKey = environment.getProperty(BaseCommonConstants.AES_CRYPT_KEY);
						String decPassword = AES.decrypt(creds, secretKey);
						propHdl.setStringValue(decPassword);
					} else if (CommonConstants.BIRT_REPORT_DRIVERCLASS
							.equalsIgnoreCase(propHdl.getPropertyDefn().getName())) {
						propHdl.setStringValue(environment.getProperty(CommonConstants.DB_CONFIG_DRIVERCLASS));

					}
				}
			}
		} catch (Exception e) {
			throw new SettleNxtException(e.getMessage(),"",e);
		}
	}

	
	

	


}
