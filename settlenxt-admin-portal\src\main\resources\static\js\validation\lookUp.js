 $(document).ready(function () {
 lookUpIds=[];

	/* Initialization of datatables */
	$(document).ready(function () {
    	
     $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                //If first column to be skipped to include the filter for the reasons line check box 
                if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   handleInput(colIdx, cell, title, api);
                   }
                });
            $('#tabnew_filter').hide();
           
        },
        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'LookUp',
                header: 'false',
                title: null,
                sheetName: 'LookUp',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'LookUp' ,
				header:'false', 
				title: null,
				sheetName:'LookUp',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });
});	
   
 $('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	
	
	 $("#excelExport").on("click", function () {
	        $(".buttons-excel").trigger("click");
	    });
	    
	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
	 
	     $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
 
 
 
 
 $("#selectAll").click(function(){
$('#errorStatus4').hide();
        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
      
        var lookUpData = document.getElementById("lookUp");
lookUpData.innerHTML = lookUpIds.length+"     "+"records are selected";


	if(lookUpIds.length>0){
	
			if( $('#selectAll').is(':checked') )
			{
 				$("#toggleModal").modal('show');
 			}
			else
			{
			  $("#toggleModal").modal('hide');
			}
	}
		
	else{
var i=0;
var lookUpIds2=[];
 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
		                    lookUpIds2.push(this.value);
		                    i++;
		                });
if(lookUpIds2.length>0){


if(LookUpListPendings.length>0){
	lookUpData.innerHTML = LookUpListPendings.length+"     "+"records are selected";
	
	if( $('#selectAll').is(':checked') ){
 $("#toggleModal").modal('show');
        
}
else{
   $("#toggleModal").modal('hide');
        
}
}
}}
	
});

 
 
 
 
 
 });


function handleInput(colIdx, cell, title, api) {
	 var cursorPosition =null;
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function (_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
                lookUpIds = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
                        lookUpIds.push(this.value);
                        i++;
                    });
                }
                else {
                    lookUpIds = [];
                }

            })
            .on('click', function (e) {
                e.stopPropagation();
            })
            .on('keyup', function (e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    } else {
        $(cell).html(title + '<br> &nbsp;');
    }
   
}

function ApproveOrRejectBulk(type,action){

	var url = '/approveOrRejectBulkLookUp';

	
	 var array = [];
		if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	   }
	   else if(action=='All'){
	 
	   		 if(lookUpIds.length>0){
	   array= lookUpIds;
	   		}else{
	   		array=LookUpListPendings;
	   	}	
	   		
	   }
	 
	var bulkLookUpList = "";
		for ( var i of array) {
			bulkLookUpList = bulkLookUpList + i + "|"
					;
		}
		
		
		console.log(array)
		var data;
		if(array.length!=0){
		if(type=='A'){
		
			 data =  "status,"+"A"+",bulkLookUpList,"+bulkLookUpList+",remarks,"+"Approved";
		}
	
	else if(type=='R'){
			
		data =  "status,"+"R"+",bulkLookUpList,"+bulkLookUpList+",remarks,"+"Rejected";
		}
		
	postData(url, data);
		$('#errorStatus2').hide();
	$('#errorStatus2').html("");
	
	}else{
	
	$('#errorStatus2').html("Please select one or more records to bulk approve/reject records");
	$('#errorStatus2').show();
	}
	
}


function submitForm(url) {
	var data = "";
	postData(url, data);
}

function viewLookUpInfo(lookupId,action) {
    var data = "lookupId," + lookupId ;
    postData(action, data);
}

function viewRejLookUpInfo(lookupId,action) {
   var data = "lookupId," + lookupId ;
    postData(action, data);
}

function deselectAll() {



   $('#selectAll').prop('checked', false);
         var ele=document.getElementsByName('type');  
   for(let i of ele){  
       if(i.type=='checkbox')  
           i.checked=false;  
   }
   
   $('#selectAll1').prop('checked', false);
         ele=document.getElementsByName('type');  
   for(let i of ele){  
       if(i.type=='checkbox')  
           i.checked=false;  
   }
   
}
