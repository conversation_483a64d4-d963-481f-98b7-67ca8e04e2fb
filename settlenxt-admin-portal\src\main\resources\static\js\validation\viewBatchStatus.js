$(document).ready(function() {
	$( "#crtDate" ).datepicker({
		defaultDate: "+1w",
		dateFormat : "yy-mm-dd",
		changeMonth: true,
		changeYear: true,
		maxDate: new Date()
	});
	
	$('#batchStatusTable').dataTable({
		responsive: true,
		bSort:false
	});
	
	$("#searchButon").attr("disabled", true);
	
	loadDataTable();
	
	$('#batchId,#clrBatchId').change(function() {
		var flag = validateForm();
		if (flag) {
			loadDataTable();			
		}
	});
	
	$('#crtDate').change(function() {
		var tokenValue = document.getElementsByName("_TransactToken")[0].value
		$("#searchForm").attr('action','viewBatchStatus?_TransactToken='+tokenValue);
		$("#searchForm").submit();
	});
	
	$('#searchButon').click(function() {
		var res = validateForm();
		if (res) {
			loadDataTable();			
		}
	});
	
	function loadDataTable(){
		
		$("#batchStatusTable").dataTable({
			"bServerSide": true,
			"bSort":false,
			"bFilter" : true,
			"sAjaxSource": "fetchBatchStatus",
			"bProcessing": true,
			"bJQueryUI": true,
			"sServerMethod": "POST",
			"bDestroy": true,
			"bAutowidth": false,
			"bScrollCollapse": true,
			"pageLength": 10,
			"searching": false,
			"lengthChange": false,
			"fnServerParams": function ( aoData ) {
				aoData.push( { "name": "crtDate", "value": $('#crtDate').val() == "" ? "" : $('#crtDate').val() },
						{ "name": "batchId", "value": $('#batchId').val() == "" ? "" : $('#batchId').val() },
						{ "name": "clrBatchId", "value": $('#clrBatchId').val() == "" ? "" : $('#clrBatchId').val() },
						{ "name": "_TransactToken", "value": document.getElementsByName("_TransactToken")[0].value}
				);
			},
			"fnServerData": function ( sSource, aoData, fnCallback ) {
				$.ajax({
					"dataType": 'json',
					"type": "POST",
					"url": sSource,
					"data": aoData,
					"success": function(json){
						
						var errorMessage=json.errorMessage;
						var errorStatus=json.errorStatus;
						fnCallback(json);
						if(errorMessage != "" || errorStatus !="")
						{
							
							$('.alert-danger').html(errorMessage);
							$('.alert-danger').show();
							
						}
						
						else
						{
							$('.alert-danger').html('');
							$('.alert-danger').hide();
							$('.trn').show();
						}
					}
				});               
			},
			"initComplete": function(_settings,_json) {
				$("#searchButon").attr("disabled", false);
			}
		});
	}
	
	function validateForm()
	{
		var result = true;

		if($('#crtDate').val() == "") {	
			$('#errCrtDate').html("Please select a date");
			return false;
			
		}else{
			$('#errCrtDate').html("");
		}
		
		return result;
	}
});

/*
 * function viewFunByTxnId(txId){ alert(txnId); }
 */

