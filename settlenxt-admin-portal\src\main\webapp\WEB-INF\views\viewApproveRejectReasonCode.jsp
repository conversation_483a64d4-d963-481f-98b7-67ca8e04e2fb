<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

	<script src="./static/js/validation/viewApproveRejectResonCode.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert"><spring:message code="budget.apprejecterrormsg" /></div>
	<div class="alert alert-danger remarkMust" role="alert"><spring:message code="budget.remarkserror" /></div>
	<c:url value="approveRejectReasonCode" var="approveRejectReasonCode" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveRejectReasonCode" modelAttribute="rejectReasonCodeRuleDto"
		action="${approveRejectReasonCode}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="rejectReasonCodeRules.approvalPendingViewScreen.title" /></span></strong>
					</div>

					<div class="panel-body">
						<input type="hidden" id="seqId" value="${rejectReasonCodeRuleDto.seqId}">
						<input type="hidden" id="functionCode" value="${rejectReasonCodeRuleDto.funcCode}">
						<input type="hidden" id="relationalOperator" value="${rejectReasonCodeRuleDto.relationalOperator}">
						<input type="hidden" id="fieldName" value="${rejectReasonCodeRuleDto.fieldName}">
						<input type="hidden" id="fieldOperator" value="${rejectReasonCodeRuleDto.fieldOperator}">

						<input type="hidden" id="crtuser"
							value="${rejectReasonCodeRuleDto.lastUpdatedBy}" />

						<table class="table table-striped infobold"
							style="font-size: 12px">
							<caption style="display:none;">View Approve Reject Reason</caption> 
							<thead style="display:none;"><th scope="col"></th></thead>
							
							<tbody>
								
								<tr>
									<td colspan="6">
										<div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-user"></span> <span
												data-i18n="Data"><spring:message code="rejectReasonCodeRules.viewscreen.title" /></span></strong>
										</div>
									</td>

									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>


								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"></span><spring:message code="rejectReasonCodeRules.viewscreen.title" /></span></strong>
										</div></td>
										<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									
								</tr>
								<tr>

								</tr>
								
								<tr>
									<td><label><spring:message code="rejectReasonCodeRule.functionCode" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.funcCode}</td>
									<td><label><spring:message code="rejectReasonCodeRule.fieldName" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.fieldName}</td>
									<td><label><spring:message code="rejectReasonCodeRule.fieldValue" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.fieldValue}</td>
									<td><label><spring:message code="rejectReasonCodeRule.relationalOperator" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.relationalOperator}</td>
									</tr>
									<tr>
									<td><label><spring:message code="rejectReasonCodeRule.fieldOperator" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.fieldOperator}</td>
									<td><label><spring:message code="rejectReasonCodeRule.subFieldName" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.subFieldName}</td>
									<td><label><spring:message code="rejectReasonCodeRule.rejectCode" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.rejectCode}</td>
									<td><label><spring:message code="rejectReasonCodeRule.status" /><span style="color: red"></span></label></td>
									<td>${rejectReasonCodeRuleDto.status}</td>
								</tr>
				

								
								
								<sec:authorize access="hasAuthority('Approve Reject Reason Code')">
									<c:if test="${rejectReasonCodeRuleDto.requestState eq 'P'}">
										<tr>
											<td colspan="6"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"><spring:message
																code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
										</tr>
										

										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="AM.lbl.reject" /></option>
											</select></td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											<!-- //Added by deepak on 31-03-2016 -->
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										</tr>
									</c:if>
							</sec:authorize>	

							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Reject Reason Code')">
										<c:if test="${rejectReasonCodeRuleDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approveRole" value="Submit"
												onclick="postAction('/approveRejectReasonCode');" />
										</c:if>
									</sec:authorize>	
										<c:choose>
										<c:when test="${editRejectReasonCodeRule eq 'Yes'}">
											<button type="button" class="btn btn-danger"
										onclick="backAction('P','/showRejectReasonCode');"><spring:message code="budget.backBtn" /></button>
										</c:when>
										<c:otherwise>
											<button type="button" class="btn btn-danger"
										onclick="backAction('P','/rejectReasonCodeRulePendingForApproval');"><spring:message code="budget.backBtn" /></button>
										</c:otherwise>
									</c:choose>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

