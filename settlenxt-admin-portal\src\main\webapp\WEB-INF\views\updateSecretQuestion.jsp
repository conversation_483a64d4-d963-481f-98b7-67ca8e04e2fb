<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>

<script type="text/javascript" src="./static/js/jquery.3.6.0.min.js"></script>
<script>

function myValidation()
{
	
	
   if($('#secretId').val() == '0')
   {
	   
	   $('#errorStatus3').html('Please Select Secret Question');
		$('#jqueryError3').show();
	
	
	
    
     
      return false;
   }
   
   else if($('#secretAnswer').val() == '')
	   
	   {
	   
	   $('#errorStatus3').html('Please Provide Secret Answer');
		$('#jqueryError3').show();
	   
	   return false;   
	   }
   
	$('#jqueryError3').hide();
	
   
   return true;
}

</script>

<div class="space_block">
	<div class="row">
	
	
		<div role="alert" style="display: none" id="jqueryError3">
			<div id="errorStatus3" class="alert alert-danger" role="alert"></div>
		</div>
				<div id="errLvType" class="alert alert-danger" role="alert"
			style="display: none"></div>
	
	</div>
	
	<!-- 
	
	removeSpace(this); encodeForm(this);
	 --><div class="row">
		<div class="col-sm-2"></div>
		<div class="col-sm-8">
			<div class="panel panel-default">
				<div class="panel-heading">
					<strong><span class="glyphicon glyphicon-th"></span> <span data-i18n="Data">Update Secret Question</span></strong>
				</div>
				<div class="panel-body">
					<form:form onsubmit="return myValidation();"
								method="POST" id="updateUserSecretQuestion" modelAttribute="userDTO"
								action="${updateUserSecretQuestion}" autocomplete="off">
						<div class="row">
							<div class="col-sm-12">
								<div class="row">
									<div class="col-sm-9">
										<div class="form-group">
										</div>
										<div class="form-group">
											<label>Secret Question </label> <span style="color: red">*</span>
											<form:select path="secretId" id="secretId" name="secretId"
												class="form-control">
												<form:option value="0"> Select </form:option>
												<form:options itemLabel="description" itemValue="secretId"
													items="${secretQuestionDTOs}" />
											</form:select>
											<div id="erroldSecretId" class="error">
												<span for="secretId" class="error"><form:errors
														path="secretId" /></span>
											</div>
										</div>
									</div>

								</div>
								<!-- Second row -->
								<div class="row">
									<div class="col-sm-9">
										<div class="form-group">
											<label>Secret Answer</label><span style="color: red">*</span>
											<form:input path="secretAnswer" id="secretAnswer"
												name="secretAnswer" placeholder=""
												cssClass="form-control medantory" />
											<form:hidden path="userId" id="userID"
												name="userId"
												cssClass="form-control medantory" />
											<div id="errsecretAnswer" class="error">
												<span for="secretAnswer" class="error"><form:errors
														path="secretAnswer" /></span>
											</div>
										</div>
									</div>
								</div>
								<!--Third row  -->
								<div class="row">
								<div style="text-align:center">
									<div class="col-sm-12 bottom_space">
										<hr />
										
										
											<button type="submit" id="updateUserSecretQuestion" value="Submit"
												class="btn btn-success">Submit</button>
											<button id="resetBtn" type="reset" value="Reset"
												class="btn btn-danger">Reset</button>
												</div>
										
									</div>
								</div>
								</div>
							</div>
						</div>
					</form:form>
				</div>	
			</div>
		</div>
	</div>
</div>
