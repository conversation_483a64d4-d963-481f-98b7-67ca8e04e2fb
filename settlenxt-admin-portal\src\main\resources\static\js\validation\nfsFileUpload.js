$(document).ready(function() {	
	$('#netBinFileUploadSuccessMsg').hide();
	$('#netBinFileUploadErrorMsg').hide();
	$('#netBinFileUploadInprogressMsg').hide();
	$('#back').hide();
	const overlay = document.getElementById('overlay');
	$('#fileArr').on('keyup keypress blur change',function () {
		validateFileArr(); 
    });
	$('#fileType').on('keyup keypress blur change',function () {
		validateFileType();
		populateLabel();
    });
	$("#uploadId").click(function() {
		var checkFileArr = validateFileArr();
		var checkFileType = validateFileType();
		if (checkFileArr && checkFileType) {
			if ($("#fileArr").val() != '') {
				var isFileTypeSelected = false;
				var isFileJCBAndTxt = false;
				var isFileCUPAndDat = false;
				var isFileDCIAndXML = false;
				var flag = false;
				var fileType = $("#fileType").val()
				for (var i of $("#fileArr").get(0).files) {
					var uplodedFile = i.name;
					var fileNameSplit = uplodedFile.split('.');
					var fileExtension = fileNameSplit[fileNameSplit.length - 1].toString().toLowerCase();
					if(fileType == ""){
						isFileTypeSelected = true;
					}
					
					({ flag, isFileCUPAndDat, isFileJCBAndTxt, isFileDCIAndXML } = handleAsPerFileExtension(fileExtension, flag, fileType, isFileCUPAndDat, isFileJCBAndTxt, isFileDCIAndXML));
				}

				handleUnSupportedFiles(isFileTypeSelected, flag, isFileCUPAndDat, isFileJCBAndTxt, isFileDCIAndXML, overlay);
			} else {
				$("#errFile").text("Please select file");  
			}
		}
	});

	$("#reset").click(function() {
		$("#errFile").text('');
		$("#errfileType").text('');
		$('#netBinFileUploadInprogressMsg').hide();
		$("#fileArr").val('');
		$('#fileType').val("");
		document.getElementById("fileArr").disabled = false;
		document.getElementById("fileType").disabled = false;
		$('#back').hide();

		$('#netBinFileUploadSuccessMsg').hide();
		$('#netBinFileUploadErrorMsg').hide();
		$('#uploadId').show();
		$('#reset').show();
	});
});

function handleUnSupportedFiles(isFileTypeSelected, flag, isFileCUPAndDat, isFileJCBAndTxt, isFileDCIAndXML, overlay) {
	if (isFileTypeSelected) {
		$('#errfileType').text('Please select the fileType');
	}
	else if (flag) {
		$('#errFile').text('File format not supported Upload only .dat or .xml or .txt extension file');
	}
	else if (isFileCUPAndDat) {
		$('#errFile').text('File format not supported Upload only .dat extension file');
	} else if (isFileJCBAndTxt) {
		$('#errFile').text('File format not supported Upload only .txt extension file');
	} else if (isFileDCIAndXML) {
		$('#errFile').text('File format not supported Upload only .xml extension file');
	} else {
		overlay.style.display = 'block';

		$('#uploadId').hide();
		$('#reset').hide();
		$('#errfileType').text('');
		$('#errFile').text('');
		var url = getURL("/bulkNetBinFileUpload");
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		var formData = new FormData();
		var fileLength = document.getElementById('fileArr').files.length;
		for (var x = 0; x < fileLength; x++) {
			formData.append("document", document.getElementById("fileArr").files[x]);
		}
		formData.append("fileType", document.getElementById("fileType").value);
		formData.append("_TransactToken", tokenValue);

		$.ajax({
			method: 'POST',
			url: url,
			cache: false,
			processData: false,
			contentType: false,
			data: formData,
			success: async function (response) {
				if (response == "SUCCESS") {
					overlay.style.display = 'none';
					await sleep(500);

					document.getElementById("fileArr").disabled = true;
					document.getElementById("fileType").disabled = true;

					$('#netBinFileUploadSuccessMsg').show();
					$('#back').show();



				}
				else {
					overlay.style.display = 'none';
					$('#netBinFileUploadInprogressMsg').hide();
					$('#netBinFileUploadErrorMsg').show();
					$('#back').show();
				}
			},
			error: function (_request, _status, _error) {
				overlay.style.display = 'none';
				document.getElementById('netBinFileUploadErrorMsg').style.display = 'block';
				document.getElementById("fileArr").disabled = true;
				document.getElementById("fileType").disabled = true;
				$('.panel').hide();
				$("#uploadId").hide();
				$('#back').show();
			}
		});
	}
}

function handleAsPerFileExtension(fileExtension, flag, fileType, isFileCUPAndDat, isFileJCBAndTxt, isFileDCIAndXML) {
	if (fileExtension !== 'txt' && fileExtension !== 'dat' && fileExtension !== "xml") {
		flag = true;
	} else if ((fileExtension === 'txt' || fileExtension === 'dat' || fileExtension === 'xml') && !flag) {
		({ isFileCUPAndDat, isFileJCBAndTxt, isFileDCIAndXML } = fileExtensionTxtDatXml(fileExtension, fileType, isFileCUPAndDat, isFileJCBAndTxt, isFileDCIAndXML));
	}
	return { flag, isFileCUPAndDat, isFileJCBAndTxt, isFileDCIAndXML };
}

function fileExtensionTxtDatXml(fileExtension, fileType, isFileCUPAndDat, isFileJCBAndTxt, isFileDCIAndXML) {
	if ((fileExtension === 'txt' || fileExtension === "xml") && (fileType == "CUP" || fileType == "RUPAY")) {
		isFileCUPAndDat = true;

	}
	else if ((fileExtension === 'dat' || fileExtension === "xml") && fileType == "JCB") {
		isFileJCBAndTxt = true;

	}
	else if ((fileExtension === 'dat' || fileExtension === "txt") && fileType == "DCI") {
		isFileDCIAndXML = true;

	}
	return { isFileCUPAndDat, isFileJCBAndTxt, isFileDCIAndXML };
}

function validateFileArr() {

	var flag = true;

	if ($('#fileArr').val().trim() == '') {
		$('#errFile').text('Please attach file');
		flag = false;
	}else {
		$('#errFile').text('');
	}


	return flag;
}

function validateFileType() {

	var flag = true;

	if($('#fileType').val().trim() == ''){
		$('#errfileType').text('Please Select File Type');
		flag = false;
	} else {
		$('#errfileType').text('');
	}


	return flag;
}

function populateLabel() {
	var fileType = $("#fileType").val();
	if(fileType === ''){
		document.getElementById("title").innerHTML = "(.dat or .txt or .xml formats allowed.)";
	}
	else if(fileType === 'CUP' ||fileType === 'RUPAY' ){
		document.getElementById("title").innerHTML = "(.dat formats allowed.)";
	}
	else if(fileType === 'JCB'){
		document.getElementById("title").innerHTML = "(.txt formats allowed.)";
	}
	else {
		document.getElementById("title").innerHTML = "(.xml formats allowed.)";
	}
}

function noBack() {
	window.history.forward();
}

function backCall(url) {
	
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "_vTransactToken," + tokenValue;
	postData(url, data);
}

async function sleep(msec) {
    return new Promise(resolve => setTimeout(resolve, msec));
}
