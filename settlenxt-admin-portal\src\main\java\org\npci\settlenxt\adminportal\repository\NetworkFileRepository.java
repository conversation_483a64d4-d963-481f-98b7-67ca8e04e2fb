package org.npci.settlenxt.adminportal.repository;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.adminportal.dto.NetworkFileSummaryDTO;
import org.npci.settlenxt.adminportal.model.NetworkOutgoingModel;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface NetworkFileRepository {
    NetworkFileSummaryDTO getNetworkFileDetails(String tableName, LocalDateTime fromDate, LocalDateTime toDate, String status, String networkType);
    List<NetworkOutgoingModel> getAllTransactions(String tableName, LocalDateTime fromDate, LocalDateTime toDate, String status, String networkType);
}
