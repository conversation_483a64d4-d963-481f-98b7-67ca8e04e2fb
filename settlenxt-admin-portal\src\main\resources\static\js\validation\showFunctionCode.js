$(document).ready(function () {
	 userIds=[];
    
     $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                	// If first column to be skipped to include the filter for
					// the reasons line check box
                    if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    searchBoxFunc(colIdx, cell, title, api);
                    }
                });
            $('#tabnew_filter').hide();
            
        },
     // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
        order: [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Function Code',
                header: 'false',
                title: null,
                sheetName: 'Function Code',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Function Code' ,
						header:'false', 
						title: null,
						sheetName:'Function Code',
						className:'defaultexport',
						exportOptions: {
				            columns: 'th:not(:last-child)'
				         }
                    }
        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true
    });

    $("#excelExport").on("click", function () {
        $(".buttons-excel").trigger("click");
    });
 	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
 
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });
     
     /*
		 * $("#selectAll").click(function(){
		 * 
		 * $('#jqueryError4').hide(); $("input[type=checkbox]").prop('checked',
		 * $(this).prop('checked')); if( $('#selectAll').is(':checked') ) {
		 * 
		 * $("#toggleModalNews").modal('show'); } else {
		 * $("#toggleModalNews").modal('hide');
		 * 
		 * var ele=document.getElementsByName('type'); var i=0; for(i of ele){
		 * if(i.type=='checkbox') i.checked=false; } }
		 * 
		 * 
		 * var referenceNoList = document.getElementById("newsIds");
		 * referenceNoList.innerHTML = referenceNoListPendings.length+"
		 * "+"records are selected";
		 * 
		 * });
		 */ 
     
     $("#selectAll").click(function(){
	 		
		 $('#jqueryError4').hide();
		 $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
		 
		 var referenceNoList = document.getElementById("newsIds");
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
        
		 if(userIds.length>0){
	referenceNoList.innerHTML = userIds.length+"     "+"records are selected";
	
     		if( $('#selectAll').is(':checked') )
		   {
			  
			  $("#toggleModalNews").modal('show');	
		  }
		 else
	     {
			 $("#toggleModalNews").modal('hide');
			 
			 }}else{
                var i=0;
                var userId2=[];
                 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
                                            userId2.push(this.value);
                                            i++;
                                        });
                showAndHideModel(userId2, referenceNoList);}
            });
     
  // Disabling SelectAll option diabling
   	if(referenceNoListPendings.length==0)
   	{
  		if (typeof selectAll != "undefined") {
  			document.getElementById("selectAll").disabled = true;
  			document.getElementById("submitButtonA").disabled = true;
 			document.getElementById("submitButtonR").disabled = true;
  		}
   	}
  	// Disabling SelectAll option diabling
 
});


function showAndHideModel(userId2, referenceNoList) {
    if (userId2.length > 0) {


        if (referenceNoListPendings.length > 0) {
            referenceNoList.innerHTML = referenceNoListPendings.length + "     " + "records are selected";

            if ($('#selectAll').is(':checked')) {
                $("#toggleModalNews").modal('show');

            }
            else {
                $("#toggleModalNews").modal('hide');

            }
        }
    }
}

function searchBoxFunc(colIdx, cell, title, api) {
	var cursorPosition = null;
    if (colIdx < actionColumnIndex) {
        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function(_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
                userIds = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
                        userIds.push(this.value);
                        i++;
                    });
                }
                else {
                    userIds = [];
                }
            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
    
}

function getPendingFunctionCodeList(){

	$('#userType').val($('#userType').val());
	var url = '/functionCodeForApproval';
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "_vTransactToken," + tokenValue ;
	postData(url, data);
}



function viewFunctionCode(funcCodeId, type) {
	var url="";
	if (type == 'V')
		url = '/getFunctionCode';
	else if (type == 'P')
		url = '/getPendingFunctionCode';
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "funcCodeId," + funcCodeId + ",userType," + "P" + ",_vTransactToken,"+ tokenValue;
	postData(url, data);
}


function submitForm(url, userType)
{
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "_vTransactToken,"+ tokenValue +",userType," + userType;
	postData(url, data);
}

function mySelect(){
$('#jqueryError4').hide();
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	  var referenceNoList = document.getElementById("newsIds");
 
	 if(array.length==referenceNoListPendings.length){
		 $('#selectAll').prop('checked', true);
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
			 $("#toggleModalNews").modal('show');
	 }
	 else{
		 $("#toggleModalNews").modal('hide');
		 
	 }
	
}

function ApproveorRejectBulkFunctionCode(type,action){
	
	 var url = '/approveFunctionCodeBulk';
	 var array = [];
	 var i = 0;
	 var referenceIdIdList = "";
	var data ="";
	 if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(action=='All'){
		 array=referenceNoListPendings; 
	 }
	 if(userIds.length>0){
			  
		  $('#jqueryError4').hide();
	 
		
		
		
 		for(i of userIds){  
	         referenceIdIdList = referenceIdIdList + i + "|" ;  
	     }
	if(type=='A'){
		 data =  "status,"+"A"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	else if(type=='R'){
		 data = "status,"+"R"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}

	postData(url, data);
	}
	 else if(array.length>0){
			$('#jqueryError4').hide();
			
			
			for (i of array) {
			referenceIdIdList = referenceIdIdList + i + "|";
			}
			if (type == 'A') {
			data = "status," + "A" + ",bulkApprovalReferenceNoList," + referenceIdIdList;
			}
			else if (type == 'R') {
			data = "status," + "R" + ",bulkApprovalReferenceNoList," + referenceIdIdList;
			}
			postData(url, data);
			}
	else{
			  
			  $('#errorStatus4').html('Please Select  Atleast One Record');
				$('#jqueryError4').show();
		  }
}


function deselectAll() {
$('#jqueryError4').hide();
	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  
var i=0;
 		for(i of ele){  
	         if(i.type=='checkbox')  
	             i.checked=false;  
	     } 

}
