var complexRelOperators = [];
var disputeFeeRuleList = [];
var entry = {};
var index = 0;
var actionCode;
var feeType;
var priority;
var feeCode;
var relationalOperator;
var fieldName1;
var fieldName2;
var fieldName;
var fieldValue;
var fieldOperator;
$(document).ready(function () {
	actionCode = $('#actionCode').val();
	feeType = $('#feeType').val();
	priority = $('#priority').val();
	feeCode = $('#feeCode').val();
	relationalOperator = $('#relationalOperator').val();
	fieldName1 = $('#fieldName1').val();
	fieldName2 = $('#fieldName2').val();
	fieldName = fieldName1+","+fieldName2;
	fieldValue = $('#fieldValue').val();
	fieldOperator = $('#fieldOperator').val();
	$('#actionCode').on('keyup keypress blur change', function() {
		validateFromCommonVal('actionCode', true, "SelectionBox", 10, false);
		validateEditDisputeFeeRule();
	});

	$('#feeType').on('keyup keypress blur change', function() {
		validateFromCommonVal('feeType', true, "SelectionBox", 30, false);
		validateEditDisputeFeeRule();
	});

	$('#priority').on('keyup keypress blur change', function() {
		validateFromCommonVal('priority', true, "priorityVal", 2, false);
		validateEditDisputeFeeRule();
	});

	$('#feeCode').on('keyup keypress blur change', function() {
		myfunc();
		validateFromCommonVal('feeCode', true, "NumericsOnly", 10, false);
		validateEditDisputeFeeRule();
		
	});
	
	$('#relationalOperator').on('keyup keypress blur change', function() {
		validateFromCommonVal('relationalOperator', true, "SelectionBox", 30, false);
		validateEditDisputeFeeRule();
	});

	$('#fieldName1').on('keyup keypress blur change', function() {
		validateFromCommonVal('fieldName1', true, "SelectionBox", 30, false);
		validateEditDisputeFeeRule();
	});

	$('#fieldName2').on('keyup keypress blur change', function() {
		validateFromCommonVal('fieldName2', false, "SelectionBox", 30, false);
		validateEditDisputeFeeRule();
	});

	$('#fieldValue').on('keyup keypress blur change', function() {
		validateFromCommonVal('fieldValue', true, "AlphaNumCommaHyphenUndScStar", 20, false);
		validateEditDisputeFeeRule();
	});

	$('#fieldOperator').on('keyup keypress blur change', function() {
		validateFromCommonVal('fieldOperator', false, "SelectionBox", 30, false);
		validateEditDisputeFeeRule();
	});
	
	$('#logicalOperator').on('keyup keypress blur change', function() {
		validateFromCommonVal('logicalOperator', true, "SelectionBox", 30, false);
		validateEditDisputeFeeRule();
	});
	$('#submitDisputeFee').prop('disabled', true);
    $('#bUpdate').prop('disabled', true);
	$('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
	$('#tabnew').hide();
	
	document.getElementById("fieldName2").disabled = true;
	document.getElementById("fieldOperator").disabled = true;
	document.getElementById("relCompOpList").style.visibility = 'hidden';
	complexRelOperators = [...document.querySelectorAll("#relCompOpList > option")].map(element => (element.value));
	
	$("#relationalOperator").change(function(_e){
    var relOp = document.getElementById("relationalOperator").value;
    if(complexRelOperators.includes(relOp)){
    	document.getElementById('fieldName2').value = "";
    	document.getElementById('fieldOperator').value = "";
        document.getElementById("fieldName2").disabled = false;
        document.getElementById("fieldOperator").disabled = false;
    }else{
    	document.getElementById('fieldName2').value = "";
    	document.getElementById('fieldOperator').value = "";
    	document.getElementById("fieldName2").disabled = true;
    	document.getElementById("fieldOperator").disabled = true;
    }
	});
});

function resetAction() {
document.getElementById("addEditDisputeFee").reset();

$('#erractionCode').find('.error').html('');
$('#errfeeType').find('.error').html('');
$('#errpriority').find('.error').html('');
$('#errfeeCode').find('.error').html('');
$("#errfieldName1").find('.error').html('');
$("#errfieldName2").find('.error').html('');
$("#errfieldValue").find('.error').html('');
$('#errrelationalOperator').find('.error').html('');
$('#errfieldOperator').find('.error').html('');
document.getElementById("fieldName2").disabled = true;
document.getElementById("fieldOperator").disabled = true;
}

function validateDisputeFeeRule() {
	var relOp = document.getElementById("relationalOperator").value;
    var isValid = true;
   if (!validateFromCommonVal('actionCode', true, "SelectionBox", 10, false)) {
        isValid = false;
    }
    if (!validateFromCommonVal('feeType', true, "SelectionBox", 30, false)) {
        isValid = false;
    }
    if (!validateFromCommonVal('priority', true, "priorityVal", 6, false)) {
        isValid = false;
    }
    if (!validateFromCommonVal('feeCode', true, "NumericsOnly", 10, false)) {
        isValid = false;
    }
    if (!validateFromCommonVal('relationalOperator', true, "SelectionBox", 30, false)) {
        isValid = false;
    }
    if (!validateFromCommonVal('fieldName1', true, "SelectionBox", 30, false)) {
        isValid = false;
    }
    if(complexRelOperators.includes(relOp)){
    	if (!validateFromCommonVal('fieldName2', true, "SelectionBox", 30, false)) {
    		isValid = false;
    	}
    	if (!validateFromCommonVal('fieldOperator', false, "SelectionBox", 30, false)) {
    		isValid = false;
    	}
    }
    if (!validateFromCommonVal('fieldValue', true, "AlphaNumCommaHyphenUndScStar", 20, false)) {
        isValid = false;
    }
   
    return isValid;
}

function getLookupDesc(id, options) {
    const objectId = document.getElementById(id).value
    if (objectId === "SELECT") {
        return "";
    } else {
        const item = [...document.querySelectorAll(options)].map(element => ({ [element.value]: element.label })).filter(itm => !("SELECT" in itm));
        return Object.values(item.filter(elem => elem[objectId]).pop())[0];
    }
}

function saveDisputeFee() {
    var isValid = validateDisputeFeeRule();
    if (isValid) {
    	$('#submitDisputeFee').prop('disabled', false);
        entry['id'] = index;
        entry['actionCode'] = document.getElementById('actionCode').value;
        entry['actionCodeDesc'] = getLookupDesc("actionCode", "#actionCode > option");
        entry['feeType'] = document.getElementById('feeType').value;
        entry['feeTypeDesc'] = getLookupDesc("feeType", "#feeType > option");
        entry['priority'] = document.getElementById('priority').value;
        entry['feeCode'] = document.getElementById('feeCode').value;
        entry['relationalOperator'] = document.getElementById('relationalOperator').value;
        entry['relationalOperatorDesc'] = getLookupDesc("relationalOperator", "#relationalOperator > option");
        entry['fieldName1'] = document.getElementById('fieldName1').value;
        entry['fieldName1Desc'] = getLookupDesc("fieldName1", "#fieldName1 > option");
        entry['fieldName2'] = ","+document.getElementById('fieldName2').value;
        entry['fieldName2Desc'] = getLookupDesc("fieldName2", "#fieldName2 > option");
        entry['fieldName'] =  entry['fieldName1'] +  entry['fieldName2'];
        entry['logicalFeeCode'] = document.getElementById('logicalFeeCode').value;
        entry['fieldValue'] = document.getElementById('fieldValue').value;
        entry['fieldOperator'] = document.getElementById('fieldOperator').value;
        entry['fieldOperatorDesc'] = getLookupDesc("fieldOperator", "#fieldOperator > option");
        entry['logicalOperator'] = document.getElementById('logicalOperator').value;
        disputeFeeRuleList.push(entry);
var actionCodeDescNode = document.createTextNode(entry['actionCodeDesc']);
        var feeNodeTypeDesc = document.createTextNode(entry['feeTypeDesc']);
        var priorityNode = document.createTextNode(entry['priority']);
        var feeCodeNode = document.createTextNode(entry['feeCode']);
        var relationalOperatorDescNode = document.createTextNode(entry['relationalOperatorDesc']);
        var logicalFeeCodeNode = document.createTextNode(entry['logicalFeeCode']);
        var fieldNameNode = document.createTextNode(entry['fieldName']);
        var fieldValueNode = document.createTextNode(entry['fieldValue']);
        var fieldOperatorNode = document.createTextNode(entry['fieldOperator']);
       
        if (disputeFeeRuleList.length > 0) {
        	var id = disputeFeeRuleList.map(e => e.id).indexOf(entry['id']);	
        	$('#tabnew').hide();
        	    
    
    $('#tabnew').append(
                      $('<tr>').attr('id', 'tabnew_' + id).append(
                        $('<td>').append(actionCodeDescNode),
                        $('<td>').append(feeNodeTypeDesc),
                        $('<td>').append(priorityNode),
                        $('<td>').append(feeCodeNode),
                        $('<td>').append(relationalOperatorDescNode),
                        $('<td>').append(logicalFeeCodeNode),
                        $('<td>').append(fieldNameNode),
                        $('<td>').append(fieldValueNode),
                        $('<td>').append(fieldOperatorNode),
                        $('<td>').append($('<input>').attr({
                          'type': 'button',
                          'class': 'btn btn-danger remDisputeFee',
                          'onclick': 'removeDisputeFeeList(' + id + ')',
                          'value': 'Remove',
                        }))
                      )
                    );
    
    
    	$('#tabnew').show()
        	index++;
        } else {
        	$('#tabnew').hide();
        }
        clearDisputeFeeRule();
    }
    
    
}

function addDisputeFeeRule(actionUrl) {
	if (disputeFeeRuleList.length > 0) {
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		var linkurl = pathName + actionUrl;
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		$.ajax({
			url: linkurl,
			type: "POST",
			dataType: "json",
			data: JSON.stringify(disputeFeeRuleList),
			"beforeSend": function (xhr) {
                xhr.setRequestHeader('_TransactToken', tokenValue);
            },
			contentType: "application/json; charset=utf-8",
			cache: false,
			success: function(response) {
				console.log(response);
				 if (response.status == "BSUC_0001") {
				document.querySelectorAll('.remDisputeFee').forEach(item => item.disabled = true);
				document.getElementById('addDisputeFee').disabled = true;
				document.getElementById('submitDisputeFee').disabled = true;
				document.getElementById('resetDisputeFee').disabled = true;
				[...document.querySelectorAll("#addEditDisputeFee .row")].splice(0, 3).forEach(item => item.remove())
				$('#transitionSuccessMsg').show();
				 }
				 else {
					 $('#transitionErrorMsg').show();
				 } 
			},
			 error: function(_request, _status, _error) {
	                document.getElementById('transitionErrorMsg').style.display = 'block';
	                $('.panel').hide();
	            }
		});
	} else {
		alert("Please add the record first");
	}
}


function editDisputeFeeRule(_id) {
	
	let url = '/updateDisputeFeeRule';
	fieldName = document.getElementById('fieldName1').value+document.getElementById('fieldName2').value;
    var data = "actionCode," + $('#actionCode').val()  + ",feeType," + $('#feeType').val() + ",priority," + $('#priority').val() + ",feeCode," + $('#feeCode').val() +",relationalOperator," + $('#relationalOperator').val() + ",fieldOperator," + $('#fieldOperator').val()+",seqId," + $('#seqId').val()+",logicalFeeCode,"+$('#logicalFeeCode').val()+",logicalOperator,"+$('#logicalOperator')+",fieldName,"+fieldName+ ",fieldValue," + $('#fieldValue').val();
    postEditDisputeFeeRule(url, data);
}

function postDiscardDisputeFeeRule(action) {
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var url = action;
	var data = "seqId," + $('#seqId').val() + ",_vTransactToken," + tokenValue;
	postData(url, data);
}

function userAction(_type, action) {
	let url = action;
	var tokenValue= document.getElementsByName("_TransactToken")[0].value;
	
	var data = "seqId,"+$("#seqId").val()+",_vTransactToken," + tokenValue;
	postData(url, data);
} 

function removeDisputeFeeList(id) {
	
	if (disputeFeeRuleList.length > 0) {
		$(`#tabnew_${id}`).remove();
	} else {
		$(`#tabnew_${id}`).remove();
		$('#tabnew').hide();
	}
	disputeFeeRuleList.splice(id, 1);
}

function clearDisputeFeeRule() {
	document.getElementById('actionCode').value = "";
	document.getElementById('feeType').value = "";
	document.getElementById('priority').value = "";
	document.getElementById('feeCode').value = "";
	document.getElementById('logicalFeeCode').value = "";
	document.getElementById('relationalOperator').value = "";
	document.getElementById('fieldName1').value = "";
	document.getElementById('fieldName2').value = "";
	document.getElementById('fieldValue').value = "";
	document.getElementById('fieldOperator').value = "";
	document.getElementById('logicalOperator').value = "";
	document.getElementById("fieldName2").disabled = true;
    document.getElementById("fieldOperator").disabled = true;
	entry = {};
}

function myfunc() {
	document.getElementById("logicalFeeCode").value = document.getElementById("feeCode").value;
}

function validateEditDisputeFeeRule() {
	if (actionCode != document.getElementById("actionCode").value || fieldOperator != document.getElementById("fieldOperator").value || 
			fieldValue != document.getElementById("fieldValue").value || fieldName2 != document.getElementById("fieldName2").value || 
			fieldName1 != document.getElementById("fieldName1").value ||
			relationalOperator != document.getElementById("relationalOperator").value || feeCode != document.getElementById("feeCode").value || 
			feeType != document.getElementById("feeType").value || priority != document.getElementById("priority").value ) {
		if ($("#bUpdate")) {
			$("#bUpdate").prop("disabled", false);
		}
	} else {
		if ($("#bUpdate")) {
			$("#bUpdate").prop("disabled", true);
		}
	}
}


function postEditDisputeFeeRule(action, data) {
	var dynInput;
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	let linkurl = pathName + action;
	var form = document.createElement("form");
	form.method = "POST";
	var tokenValue=null
	if(document.getElementsByName("_TransactToken")[0])
		tokenValue= document.getElementsByName("_TransactToken")[0].value;
	tokenValue = $('<div>').text(tokenValue).html();
	var parameters = data.split(",");

	for (var i = 0; i < 17; ++i) {
		dynInput = document.createElement("input");
		
		dynInput.setAttribute("type", "hidden");
		
parameters[i] = $('<div>').text(parameters[i]).html();
		dynInput.setAttribute("id", parameters[i]);
		dynInput.setAttribute("name", parameters[i]);
		++i;
		dynInput.setAttribute("value", parameters[i]);

		form.appendChild(dynInput);
	}
	dynInput = document.createElement("input");
	dynInput.setAttribute("type", "hidden");
	dynInput.setAttribute("id", "fieldName");
	dynInput.setAttribute("name", "fieldName");
	var tempfieldName = $('#fieldName1').val()+","+$('#fieldName2').val();
	tempfieldName = $('<div>').text(tempfieldName).html();
	dynInput.setAttribute("value", tempfieldName);
	
	form.appendChild(dynInput);
	dynInput = document.createElement("input");
	dynInput.setAttribute("type", "hidden");
	dynInput.setAttribute("id", "fieldValue");
	dynInput.setAttribute("name", "fieldValue");
	var tempfieldValue = $('#fieldValue').val();
	dynInput.setAttribute("value", tempfieldValue);
	form.appendChild(dynInput);
	dynInput = document.createElement("input");
	dynInput.setAttribute("type", "hidden");
	dynInput.setAttribute("id", "_TransactToken");
	dynInput.setAttribute("name", "_TransactToken");
	dynInput.setAttribute("value", tokenValue);
	form.appendChild(dynInput);
	
	document.body.appendChild(form); // added this	for firefox Browser
	encodeForm(form);	//Added by piyush for form encode

	form.action = linkurl;
	form.submit();
}