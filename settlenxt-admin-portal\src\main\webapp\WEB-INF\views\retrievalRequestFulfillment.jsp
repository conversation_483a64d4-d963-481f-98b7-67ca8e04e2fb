<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head><title></title></head></html>
<tr>
			<td><label><spring:message
						code="txn.detail.lbl.raiseDateAndTime" /></label></td>
			<td class="raiseDateAndTime"  id="raiseDateAndTime"><c:if
					test="${empty disTxnList.tstampLocal}">N/A</c:if>${disTxnList.tstampLocal}</td>
			<td><label><spring:message code="txn.detail.lbl.internalTrackingNo" /></label></td>
			<td id="internalTrackingNo"><c:if test="${empty disTxnList.internalTrackNo}">N/A</c:if>${disTxnList.internalTrackNo}</td>																		
</tr>
<tr>
			<td><label><spring:message code="txn.detail.lbl.rgcsSettlementDate" /></label></td>
			<td class="rgcsSettlementDate"  id="rgcsSettlementDate"><c:if test="${empty disTxnList.netReconDate}">N/A</c:if>${disTxnList.netReconDate}</td>
			<td><label><spring:message code="txn.detail.lbl.documentIndicator" /></label></td>
			<td id="documentIndicator">
				<c:choose>
						<c:when test="${empty disTxnList.docInd}">
							N/A
						</c:when>
						<c:otherwise>
							<c:choose>
								<c:when test = "${disTxnList.docInd == 'Y'}">
									${disTxnList.docInd} - Yes
								</c:when>
								<c:when test = "${disTxnList.docInd == 'N'}">
									${disTxnList.docInd} - No
								</c:when>
							</c:choose>
						</c:otherwise>
					</c:choose>
			</td>								
</tr>
<tr>
			<td><label><spring:message code="txn.detail.lbl.memberMsgText" /></label></td>
			<td id="memberMessageText"><c:if test="${empty disTxnList.memMsgTxt}">N/A</c:if>${disTxnList.memMsgTxt}</td>	
			<td><label><spring:message code="txn.detail.lbl.makerUserName" /></label></td>
			<td id="makerUserName"><c:if test="${empty disTxnList.makerId}">N/A</c:if>${disTxnList.makerId}</td>										
</tr>
<tr>
			<td><label><spring:message code="txn.detail.lbl.checkerUserName" /></label></td>
			<td id="checkerUserName"><c:if test="${empty disTxnList.checkerId}">N/A</c:if>${disTxnList.checkerId}</td>
			<td></td>
			<td></td>
</tr>