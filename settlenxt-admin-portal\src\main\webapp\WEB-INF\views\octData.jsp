<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<title></title>
<script>
function exportOctData(){
	
	   const dataMap = new Map([
		   ['',''],
		   ['STAN', '${transactionDetailSummary.stan}' != '' ? '${transactionDetailSummary.stan}' : "N/A"],
		   ['OCT Date Time','${transactionDetailSummary.tstampLocal}' != '' ? '${transactionDetailSummary.tstampLocal}' : "N/A"],
		   ['Remitter Instrument Type','${transactionDetailSummary.remitterInstType}' != '' ? '${transactionDetailSummary.remitterInstType}' : "N/A"],
		   ['Remitter Instrument Id','${transactionDetailSummary.remitterInstId}' != '' ? '${transactionDetailSummary.remitterInstId}' : "N/A"],
		   ['Merchant Bank Account Number','${transactionDetailSummary.merchantBankActNo}' != '' ? '${transactionDetailSummary.merchantBankActNo}' : "N/A"],
		   ['Payload Format Indicator','${transactionDetailSummary.payloadFormatInd}' != '' ? '${transactionDetailSummary.payloadFormatInd}' : "N/A"],
		   ['Point of Initiation Method','${transactionDetailSummary.pointOfInitMethod}' != '' ? '${transactionDetailSummary.pointOfInitMethod}' : "N/A"],
		   ['Tip Fee Indicator','${transactionDetailSummary.tipFeeInd}' != '' ? '${transactionDetailSummary.tipFeeInd}' : "N/A"],
		   ['Tip Fee Amount','${transactionDetailSummary.tipFeeAmt}' != '' ? '${transactionDetailSummary.tipFeeAmt}' : "N/A"],	   
		 ]);

		 // Convert the Map data to an array of arrays
		 const dataArray = Array.from(dataMap, ([key, value]) => [key, value]);

		 // Create a new Excel workbook
		 const workbook = XLSX.utils.book_new();

		 // Convert the data array to a worksheet
		 const worksheet = XLSX.utils.aoa_to_sheet([['OCT DATA', ''], ...dataArray]);

		 // Add the worksheet to the workbook
		 XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');

		 // Save the workbook to a file
		 XLSX.writeFile(workbook, 'OctData.xlsx');
}
</script>
</head>
<div class="panel panel-default">
	<div class="panel-heading">
		<h4 class="panel-title">
			<a data-toggle="collapse" data-parent="#childAccordion"
				href="#collapseOct${disTxnList.mti}${disTxnList.funcCode}${disTxnList.txnId}"
				class="collapsed"> OCT Data <span
				class="glyphicon glyphicon-plus accor-pos-icon"></span>
			</a>
		</h4>
	</div>
	<div
		id="collapseOct${disTxnList.mti}${disTxnList.funcCode}${disTxnList.txnId}"
		class="panel-collapse collapse">
		<div class="panel-body">
			<div class="">
				<div class="row">
					<div class="col-md-12">
						<div class="card">
							<div class="card-body">
								<table name="tabNew" class="table table-striped"
									style="font-size: 12px">
									<caption style="display: none;">OCT DATA</caption>
									<thead style="display: none;">
										<th scope="col"></th>
									</thead>
									<tbody>
										<tr>
											<td><label><spring:message
														code="txn.detail.oct.lbl.stan" /></label></td>
											<td id="stan"><c:if
													test="${empty transactionDetailSummary.stan}">N/A</c:if>${transactionDetailSummary.stan}</td>
											<td><label><spring:message
														code="txn.detail.oct.lbl.octDt" /></label></td>
											<td class="tStampLocal" id="octDt"><c:if
													test="${empty transactionDetailSummary.tstampLocal}">N/A</c:if>
												${transactionDetailSummary.tstampLocal}</td>
										</tr>
										<tr>
											<td><label><spring:message
														code="txn.detail.oct.lbl.remitterInstrumentType" /></label></td>
											<td id="remitterInstrumentType"><c:if
													test="${empty transactionDetailSummary.remitterInstType}">N/A</c:if>${transactionDetailSummary.remitterInstType }</td>
											<td><label><spring:message
														code="txn.detail.oct.lbl.remitterInstrumentId" /></label></td>
											<td id="remitterInstrumentId"><c:if
													test="${empty transactionDetailSummary.remitterInstId}">N/A</c:if>${transactionDetailSummary.remitterInstId }</td>
										</tr>
										<tr>
											<td><label><spring:message
														code="txn.detail.oct.lbl.merchantBankAccNo" /></label></td>
											<td id="merchantBankAccNo"><c:if
													test="${empty transactionDetailSummary.merchantBankActNo}">N/A</c:if>${transactionDetailSummary.merchantBankActNo }</td>
											<td><label><spring:message
														code="txn.detail.oct.lbl.payloadFormatInd" /></label></td>
											<td id="payloadFormatInd"><c:if
													test="${empty transactionDetailSummary.payloadFormatInd}">N/A</c:if>${transactionDetailSummary.payloadFormatInd }</td>
										</tr>
										<tr>
											<td><label><spring:message
														code="txn.detail.oct.lbl.pointOfInitMethod" /></label></td>
											<td id="pointOfInitMethod"><c:if
													test="${empty transactionDetailSummary.pointOfInitMethod}">N/A</c:if>${transactionDetailSummary.pointOfInitMethod }</td>
											<td><label><spring:message
														code="txn.detail.oct.lbl.tipFeeInd" /></label></td>
											<td id="tipFeeInd"><c:if
													test="${empty transactionDetailSummary.tipFeeInd}">N/A</c:if>${transactionDetailSummary.tipFeeInd }</td>
										</tr>
										<tr>
											<td><label><spring:message
														code="txn.detail.oct.lbl.tipFeeAmt" /></label></td>
											<td id="tipFeeAmt"><c:if
													test="${empty transactionDetailSummary.tipFeeAmt}">N/A</c:if>${transactionDetailSummary.tipFeeAmt }</td>
											<td></td>
											<td></td>
										</tr>
									</tbody>
								</table>
							</div>
							<button style="margin-top: 10px"
								class="btn btn-success pull-left btn_align"
								onclick="exportOctData();">
								<spring:message code="ifsc.exportBtn" />
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>