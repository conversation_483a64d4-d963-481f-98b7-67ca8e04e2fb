$(document).ready(function() {
	
	$('.glyphicon-print').click(function() {
		printInfo();
	});
	
	function printInfo() {
		 var divToPrint=document.getElementById('printDiv');

		  var newWin=window.open('','Print-Window');

		  newWin.document.open();

		  newWin.document.write('<html><head><style>table,th,td {'+
					'border: 1px solid #c2d1e8;'+
					'border-collapse: collapse;'+
					'padding: 10px;'+
					'}'+
		
					'table {'+
						'margin: 1%;'+
					'}'+
			
					'th {'+
						'width: 10%;'+
						'text-align: center;'+
						'background-color: #ecf4ff;'+
					'}'+
			
					'.width {'+
						'width: 1%;'+
					'}</style></head><body onload="window.print()"><div>'+divToPrint.innerHTML+'</div></body></html>');

		  newWin.document.close();

		  setTimeout(function(){newWin.close();},10);
	}
	
	
	$('#memberId').change(function(){
		var data ="memberId,"+$('#memberId option:selected').val();
		postData('/fetchEscalationMatrix',data);
	});
	
});