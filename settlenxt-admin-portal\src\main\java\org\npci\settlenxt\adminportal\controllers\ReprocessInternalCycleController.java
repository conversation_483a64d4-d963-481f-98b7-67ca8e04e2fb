package org.npci.settlenxt.adminportal.controllers;

import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.service.IReprocessInternalCycleService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Controller used for
 * <li>View Internal cycle status</li>
 * <li>Reprocess Internal cycle status</li>
 * 
 * <AUTHOR>
 *
 */

@Controller
public class ReprocessInternalCycleController extends BaseController {

	@Autowired
	IReprocessInternalCycleService reprocessInternalCycle;
	
	private static final String CYCLE_MAN_DTO="cycleManagementDTO";
	private static final String SHOW_REP_INT_CYCLE="showReprocessInternalCycle";

	/**
	 * Fetch internal cycle data from RO and redirect to showReprocessInternalCycle
	 * JSP page.
	 * 
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/showReprocessInternalCycle")
	public String showReprocessICN(@ModelAttribute("cycleManagementDTO") CycleManagementDTO cycleManagementDTO,
			Model model) throws SettleNxtException {

		cycleManagementDTO = reprocessInternalCycle.getInternalCycleData(cycleManagementDTO);
		model.addAttribute(CYCLE_MAN_DTO, cycleManagementDTO);

		return getView(model, SHOW_REP_INT_CYCLE);
	}

	/**
	 * Send request to RO to Reprocess internal cycle
	 * 
	 * @param requestBody
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/reprocessInternalCycle")
	@ResponseBody
	public String reprocessICN(@RequestBody String requestBody) throws SettleNxtException {
		return reprocessInternalCycle.reprocessInternalCycleData(requestBody);
	}

}
