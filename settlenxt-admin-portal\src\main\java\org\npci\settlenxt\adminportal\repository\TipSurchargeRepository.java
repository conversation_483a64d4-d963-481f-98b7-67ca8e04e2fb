package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.adminportal.dto.TipSurchargeDTO;

@Mapper
public interface TipSurchargeRepository {

	 List<TipSurchargeDTO> getTipSurchargeListMain();

	 List<TipSurchargeDTO> getTipSurchargePendingForApproval();

	 TipSurchargeDTO getTipSurchargeProfileMain(int tipSurchargeId);

	 TipSurchargeDTO getTipSurchargeStgInfoById(int tipSurchargeId);

	 TipSurchargeDTO getTipSurchargeMain(int tipSurchargeId);

	 TipSurchargeDTO getTipSurchargeStg(int tipSurchargeId);

	 void insertTipSurchargeStg(TipSurchargeDTO tipSurchargeDto);

	 int fetchTipSurchargeIdSequence();

	 void insertTipSurchargeMain(TipSurchargeDTO tipSurchargeDto);

	 void updateTipSurchargeMain(TipSurchargeDTO tipSurchargeDto);

	 void updateTipSurcharge(TipSurchargeDTO tipSurchargeDto);

	 void updateTipSurchargeDiscard(TipSurchargeDTO tipSurchargeDto);

	 void updateTipSurchargeRequestState(TipSurchargeDTO tipSurchargeDto);

	 void deleteDiscardedEntry(TipSurchargeDTO tipSurchargeDto);

	 List<TipSurchargeDTO> getDuplicateTipSurchargeData(TipSurchargeDTO tipSurchargeDto);
}
