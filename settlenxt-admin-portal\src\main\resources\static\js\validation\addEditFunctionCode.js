
$(document).ready(
		function() {
			$("form :input").change(function() {
				$(this).closest('form').data('changed', true);
			});
			
			$('#feeType').multiselect({
		        buttonWidth : '160px',
		        paddingLeft: '-50px',
				nonSelectedText:'Select FeeType',
				includeSelectAllOption: true,
				enableFiltering: true,
		 });
			 if(document.getElementById("hFeeType")!=null){
					var y = document.getElementById("hFeeType").value;
											var dataarray;
											if(y!=null){
								             dataarray=y.split("|");
								        	 
								        	  }
											$('#feeType').multiselect('select', dataarray);
											 $('#feeType').multiselect('refresh');
											}	
			
		    $("#errmti").hide();
		    $("#errprocCode").hide();
		    $("#errfuncCode").hide();
		    $("#errfuncCodeDesc").hide();
		    $("#errfeeType").hide();
		    $("#errfundMovement").hide();
		    $("#errfundMovementSide").hide();
		    $("#errrecalculate").hide();
		    $("#errtransactionType").hide();
		    $("#errnetworkTxnType").hide();
		    
	$('#mti').on('keyup keypress blur change', function () {
        validateField('mti', true, "SelectionBox",0,false,1,99999,false);
    });
    $('#procCode').on('keyup keypress blur change', function () {
        validateField('procCode', true,"SelectionBox", 0, false,1,99999,false);
    });
    $('#funcCode').on('keyup keypress blur change', function () {
        validateField('funcCode', true, "SelectionBox",0,false,1,99999,false);
    });
    $('#funcCodeDesc').on('keyup keypress blur change', function () {
        validateField('funcCodeDesc', true,"SelectionBox", 0, false,0,0,false);
    });
    $('#feeType').on('keyup keypress blur change', function () {
    if($('#feeType').val()==0){
    $('#errfeeType').show();
		   $('#errfeeType').find('.error').html(functionCodeValidationMessages['feeType']);
		  
    }    
    else{
     $('#errfeeType').hide();
    }
    });
    $('#fundMovement').on('keyup keypress blur change', function () {
        validateField('fundMovement', true, "SelectionBox",0,false,0,0,false);
    });
    $('#fundMovementSide').on('keyup keypress blur change', function () {
        validateField('fundMovementSide', true,"SelectionBox", 0, false,0,0,false);
    });
    $('#recalculate').on('keyup keypress blur change', function () {
        validateField('recalculate', true, "SelectionBox",0,false,0,0,false);
    });
    $('#transactionType').on('keyup keypress blur change', function () {
        validateField('transactionType', true, "SelectionBox",0,false,0,0,false);
    });
    $('#networkTxnType').on('keyup keypress blur change', function () {
        validateField('networkTxnType', true, "SelectionBox",0,false,0,0,false);
    });
    	    
	disableSave();
	$("#mti").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#procCode").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#funcCode").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#funcCodeDesc").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#feeType").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#fundMovement").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#fundMovementSide").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#recalculate").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#transactionType").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#networkTxnType").on('keyup keypress blur change', function () {
        unableSave();
    });
 	
});


function disableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = true;
}
}

function unableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = false;
}
}


window.history.forward();
function noBack() {
	window.history.forward();
}

function resetAction() {
	$("#mti").val("SELECT");
    $("#procCode").val("SELECT");
    $("#funcCode").val("SELECT");
    $("#funcCodeDesc").val("SELECT");
    $("#feeType").val("SELECT");
    $("#fundMovement").val("SELECT");
    $("#fundMovementSide").val("SELECT");
    $("#recalculate").val("SELECT");
    $("#transactionType").val("SELECT");
    $("#networkTxnType").val("SELECT");
    
    $("#snxtErrorMessage").hide();
	
    $("#errmti").find('.error').html('');
    $("#errprocCode").find('.error').html('');
    $("#errfuncCode").find('.error').html('');
    $("#errfuncCodeDesc").find('.error').html('');
    $("#errfeeType").find('.error').html('');
    $("#errfundMovement").find('.error').html('');
    $("#errfundMovementSide").find('.error').html('');
    $("#errrecalculate").find('.error').html('');
    $("#errtransactionType").find('.error').html('');
    $("#errnetworkTxnType").find('.error').html('');
	}

function viewFunctionCodeAdd(url, type) {
var feeTypeValue = "";
	if($('#feeType').val()!=0){
		   var a = $('#feeType option:selected').toArray().map(item => item.value).join();
		   
		   var arr1= a.split(",");
		
		var i=0;
				for (i of arr1) {
					feeTypeValue = feeTypeValue + i + "|";
				}
						

		   console.log(arr1);
		   console.log(feeTypeValue);
		   $('#errfeeType').hide();
		   }
		   else{
		   $('#errfeeType').show();
		   $('#errfeeType').find('.error').html(functionCodeValidationMessages['feeType']);
		   
		   }
	
	
	
	
	
	var isValid = true;
	
	if (!validateField('funcCode', true, "SelectionBox", 0, false, 1, 99999, false) && isValid) {
        isValid = false;
    }
    if (!validateField('funcCodeDesc', true, "SelectionBox", 0, false, 0, 0, false) && isValid) {
        isValid = false;
    }
    isValid = validateFields(isValid);

var data="";	
if (isValid ) {
    var tokenValue = "lIpLsRjLtLDPSzoS2xPf9WXiF/M="; 
	if(type=='E'){
	
	 data = "funcCodeId," + $("#funcCodeId").val()+",mti," + $("#mti").val() +",procCode," + $("#procCode").val()
	+",funcCode," + $("#funcCode").val()+",funcCodeDesc," + $("#funcCodeDesc").val() +",feeType," + feeTypeValue
	+",fundMovement," + $("#fundMovement").val()+",fundMovementSide," + $("#fundMovementSide").val() 
	+",recalculate," + $("#recalculate").val()+",transactionType," + $("#transactionType").val()+",networkTxnType," + $("#networkTxnType").val()
    + ",_vTransactToken," + tokenValue;
	}
	
	else if(type=='A'){
	 data = "mti," + $("#mti").val() +",procCode," + $("#procCode").val()
	+",funcCode," + $("#funcCode").val()+",funcCodeDesc," + $("#funcCodeDesc").val() +",feeType," + feeTypeValue
	+",fundMovement," + $("#fundMovement").val()+",fundMovementSide," + $("#fundMovementSide").val() 
	+",recalculate," + $("#recalculate").val()+",transactionType," + $("#transactionType").val() +",networkTxnType," + $("#networkTxnType").val() 
	+",_vTransactToken," + tokenValue;
	}
	postData(url,data)

}
}




function validateFields(isValid) {



    if (!validateField('fundMovement', true, "SelectionBox", 0, false, 0, 0, false) && isValid) {
        isValid = false;
    }
    if (!validateField('fundMovementSide', true, "SelectionBox", 0, false, 0, 0, false) && isValid) {
        isValid = false;
    }
    if (!validateField('recalculate', true, "SelectionBox", 0, false, 0, 0, false) && isValid) {
        isValid = false;
    }
    if (!validateField('transactionType', true, "SelectionBox", 0, false, 0, 0, false) && isValid) {
        isValid = false;
    }
    if (!validateField('networkTxnType', true, "SelectionBox", 0, false, 0, 0, false) && isValid) {
        isValid = false;
    }
    return isValid;
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType!="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
   
    isValid = isDecimal(fieldType, fieldValue, isValid);
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
      if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
      

    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
        if(functionCodeValidationMessages[fieldId]){
            $("#err" + fieldId).find('.error').html(functionCodeValidationMessages[fieldId]);
        }
        $("#err" + fieldId).show();
    }
    return isValid;
}


function isDecimal(fieldType, fieldValue, isValid) {
    if (fieldType == "Decimal") {
        let regEx = /^\d+\.?\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return isValid;
}

function userAction(_type, action) {
	var tokenValue = "lIpLsRjLtLDPSzoS2xPf9WXiF/M=";
	var data = "_vTransactToken," + tokenValue + ",status," + status;
	postData(action, data);
}

function postDiscardAction(action,_id) {
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var url = action;
	var funcCodeId = $("#funcCodeId").val();
	var data = "funcCodeId," + funcCodeId + ",_vTransactToken," + tokenValue ;
	postData(url, data);
}

function discard(action, funcCodeId) {
	
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "funcCodeId," + funcCodeId + ",_vTransactToken,"
			+ tokenValue;
	postData(action, data);
}







