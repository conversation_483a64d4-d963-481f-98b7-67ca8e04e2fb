package org.npci.settlenxt.adminportal.controllers;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.MasterService;
import org.npci.settlenxt.adminportal.service.SecretQuestionService;
import org.npci.settlenxt.adminportal.service.UserService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.ApprovalDataDTO;
import org.npci.settlenxt.portal.common.dto.BinDTO;
import org.npci.settlenxt.portal.common.dto.LookUpDTO;
import org.npci.settlenxt.portal.common.dto.ParticipantDTO;
import org.npci.settlenxt.portal.common.dto.RoleDTO;
import org.npci.settlenxt.portal.common.dto.SecretQuestionDTO;
import org.npci.settlenxt.portal.common.dto.UserDTO;
import org.npci.settlenxt.portal.common.dto.UserInfoDTO;
import org.npci.settlenxt.portal.common.dto.UserToRoleDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseLookupServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.npci.settlenxt.portal.common.util.CredentialGenUtil;
import org.npci.settlenxt.portal.validator.TransactValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.google.gson.JsonObject;

@Controller
public class UserController extends BaseController {
	private static final String UTF_8 = "UTF-8";
	@Autowired
	private MessageSource messageSource;

	@Autowired
	private UserService userService;
	@Autowired
	private MasterService masterService;

	@Autowired
	BaseLookupServiceImpl lookUpService;
	
	@Autowired
	private SecretQuestionService secretQuestionService;

	
	@Autowired
	private SessionDTO sessionDto;
	
	
	@Autowired
	Environment environment;

	@Value("${DEPLOYMENT_NETWORK}")
	private String networkType;

	private static final String ADD_EDIT_USER = "addEditUser";
	private static final String SHOW_USERS = "showUsers";
	private static final String VIEW_USER_INFO = "viewUserInfo";
	private static final String VIEW_APPROVE_USER_INFO = "viewApproveUser";
	private static final String CHANGE_PASSWD = "changePassword";
	private static final String USER_PROFILE = "viewProfile";
	private static final String USER_BIN_MAPPING = "showUsersBinMapping";


	private static final String USER_DTO = "userDTO";
	private static final String SPLIT_CONST = ",";
	private static final String ASTERISK_OP="\\*";
	private static final String LOGIN_LAYOUT = "LoginLayout";
	private static final String SHOWBUTTON = "showButton";
	private static final String YES = "yes";
	private static final String EDIT = "edit";
	private static final String EDIT_TRUE = "edittrue";
	private static final String USERROLE = "userrole";
	private static final String ROLEIDS = "roleIds";
	private static final String ACQUIRER = "Acquirer";
	private static final String ISSUER = "Issuer";
	private static final String BODY = "body";
	private static final String DEACTIVATE = "deactivate";
	private static final String LOCKUSER = "lockuser";
	private static final String UNLOCKUSER = "unlockuser";
	private static final String COMMA_SEPERATOR=",";
	private static final String ACTIV = "A";
	private static final String RESET = "R";
	private static final String SUSPEND = "S";
	private static final String NEW_USER = "Z";
	private static final String BIN_LISTS="binLists";
	private static final String BIN_UNIQUE="binListUnique";
	private static final String ROLE_OPTION_LIST="roleOptionList";
	private static final String AROLE_OPTION_LIST="aRoleOptionList";
	private static final String REQ_TYPE="reqType";
	private static final String AM_MSG_PENDINGUSER="AM_MSG_pendingUser";
	private static final String BACK_BUTTON="backbutton";
	private static final String INVALID_INPUT="Invalid input";
	private static final String AM_MSG_PENDING_UPDATED_USER="AM_MSG_pendingUpdatedUser";
	private static final String PIPE="\\|";
	private static final String DASH="\\-";
	private static final String PSD_EXP_FOR_USER="Password Expired for User : ";
	private static final String YEAR="yyyy";
	private static final String HYPHEN="-";
	private static final String AM_MSC_PWDCHNGSUCCESS="AM_MSG_pwdChngSuccess";
	private static final String AM_MSG_PEDCHNGERROROCCURS="AM_MSG_pwdChngErrorOccurs";
	private static final String AM_MSG_USER_ENTRYDISCARDED="am.msg.UserEntryDiscarded";
	private static final String AM_MSG_OLDPSD="AM_MSG_oldPwd";
	private static final String AM_MSG_SECQUENOTPRESENT="AM_MSG_secQueNotPresent";
	private static final String AM_MSG_INVALIDSECANS="AM_MSG_invalidSecAns";
	private static final String AM_MSG_NEWANDCONFIRMPWDNOTMATCHED="AM_MSG_newAndConfirmPwdNotMatched";
	private static final String AM_MSG_SAMEPSD="AM_MSG_samePwd";
	private static final String AM_MSG_SAMEPWDASNAME="AM_MSG_samePwdAsName";
	private static final String AM_MSG_CUSTOMPWDLENGTH="AM_MSG_customPwdlength";
	private static final String AM_MSG_INVALIDLCPSD="AM_MSG_invalidLcPassword";
	private static final String AM_MSG_INVALIDUCPSD="AM_MSG_invalidUcPassword";
	private static final String AM_MSG_INVALIDNUMPSD="AM_MSG_invalidNumPassword";
	private static final String AM_MSG_PWDSPECIALCHAR="AM_MSG_pwdSpecialChar";
	private static final String AM_MSG_REPEATED_CHAR="AM_MSG_REPEATED_CHAR";
	private static final String AM_MSG_USERAPPROVED="AM_MSG_userApproved";
	private static final String AM_MSG_USERADDFAIELD="AM_MSG_userAddFailed";
	private static final String AM_MSG_USERREJECTED="AM_MSG_userRejected";
	private static final String AM_MSG_EMAILSENTSUCCESS="AM_MSG_EmailSentSuccess";
	private static final String AM_MSG_USERDEACTIVATIONPENDING="AM_MSG_userDeactivationPending";
	private static final String AM_MSG_USERACTIVATIONPENDING="AM_MSG_userActivationPending";
	private static final String AM_MSG_PENDINGLOCKUSER="AM_MSG_pendingLockUser";
	private static final String AM_MSG_PENDINGUNLOCKUSER="AM_MSG_pendingUnlockUser";
	private static final String AM_MSG_ERRORUSERRESETPSD="AM_MSG_errorUserResetPwd";
	private static final String AM_MSG_ERRORUPDATEUSER="AM_MSG_errorUpdateUser";
	private static final String AM_MSG_DEFAULTPSD ="AM_MSG_defaultPwd";
	private static final String CHANGE_PSWD = "changePassword";
	private static final String REDIRECT = "redirect:/login";
	private static final String LOGIN_MSG = " <br/> Please login to continue";
	private static final String LOGINLAYOUT = "LoginLayout";
	private static final String FORGOT_PSWD = "forgotPassword";
	private static final String UIPINGINTERVAL = "uiPingInterval";
	private static final String SETTLENXT_UI_PING_INTERVAL = "settlenxt.ui.ping.interval";
	private static final String SETTLENXTLAYOUT = "SettleNxtLayout";
	private static final String VERIFYSECANSWER = "verifySecretAnswer";
	private static final String UPDATE_SECRET_QUESTION = "updateSecretQuestion";
	private static final String UPDATE_USER_SECRET_QUESTION = "updateUserSecretQuestion";
	private static final String UPDATE_USER_SECRET_QUE = "/updateUserSecretQuestion";
	private static final String CHANGE_USER_PSWD = "changeUserPassword";
	private static final String CHANGE_USER_PASWD_API = "/changeUserPassword";

	
	private static final String AM_MSG_UPTSECQUESSUCCESS="AM_MSG_uptSecQuesSuccess";
	private static final String AM_MSG_UPTSECQUESERROROCCURS="AM_MSG_uptSecQuesErrorOccurs";
	
	@PostMapping("/showUsers")
	@PreAuthorize("hasAuthority('View User')")
	public String showUsers(Model model, @RequestParam(value = "userType", required = false) String userType) {
		String accessLevel = userService.getUserHierarchyAccessLevel();
		List<String> accessList = new ArrayList<>();
		if (StringUtils.isNotBlank(accessLevel) && accessLevel.contains(COMMA_SEPERATOR)) {
			accessList = Arrays.asList(accessLevel.split(SPLIT_CONST));
		} else {
			accessList.add(accessLevel);
		}
		if (StringUtils.isBlank(userType)) {
			userType = accessLevel;
		}
		List<UserDTO> userList = userService.getUserList(userType);

		model.addAttribute(BaseCommonConstants.USER_LIST, userList);

		model.addAttribute(BaseCommonConstants.SELECT_USER_TYPE, userService.getUserHierarchyAccessLevelList(accessList));
		UserInfoDTO userInfoDto = new UserInfoDTO();
		userInfoDto.setUserType(userType);
		model.addAttribute(BaseCommonConstants.SHOW_USER, CommonConstants.YES);
		model.addAttribute(BaseCommonConstants.ADDUSER, CommonConstants.TRANSACT_YES);
		model.addAttribute(BaseCommonConstants.USER_INFO, userInfoDto);

		return getView(model, SHOW_USERS);

	}

	@PostMapping("/getUser")
	@PreAuthorize("hasAuthority('View User')")
	public String getUser(@RequestParam("uid") String uid, Model model) {

		List<UserDTO> userDTOList = new ArrayList<>();
		try {
			userDTOList = userService.getUserProfileForView(Integer.valueOf(uid));
			if (CollectionUtils.isNotEmpty(userDTOList)) {
				userService.checkUserStatusForLock(userDTOList.get(0));
				userService.checkUserStatusForDeactivate(userDTOList.get(0));

				model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userDTOList);
			}
			List<BinDTO> binList = userService.getBinDetailListByUserIdForView(uid);
			if (CollectionUtils.isNotEmpty(binList)) {

				StringBuilder bins = new StringBuilder(String.valueOf(binList.get(0).getBinNumber()));

				for (int i = 1; i < binList.size(); i++) {
					bins.append(COMMA_SEPERATOR);
					bins.append(binList.get(i).getBinNumber());

				}

				userDTOList.get(0).setBins(bins.toString());

			}
		} catch (Exception ex) {

			handleErrorCodeAndForward(model, VIEW_USER_INFO, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);

		}
		if (CollectionUtils.isNotEmpty(userDTOList)) {
			fetchUserRoleList(uid, userDTOList.get(0), model);
			setFunctionality(userDTOList.get(0), model);
			List<RoleDTO> roleList = userService.getAvailableRoleList(userDTOList.get(0).getUserType(),
					userDTOList.get(0).getMakChkFlag(), userDTOList.get(0).getUserId());

			model.addAttribute(BaseCommonConstants.AVAILABLE_ROLE_LIST, roleList);
		}
		return getView(model, VIEW_USER_INFO);
	}

	@PostMapping("/userPendingForApproval")
	@PreAuthorize("hasAuthority('View User')")
	public String userPendingForApproval(Model model,
			@RequestParam(name = "userType", required = false) String userType) {

		return loadShowUsers(userType, model);
	}

	public String setUserStatus(String status) {
		switch (status) {
		case ACTIV:
			return BaseCommonConstants.USER_STAT_ACTIVE;

		case SUSPEND:
			return BaseCommonConstants.USER_STAT_SUSPENDED;

		case RESET:
			return BaseCommonConstants.USER_STAT_RESET;

		case NEW_USER:
			return BaseCommonConstants.USER_STAT_RESET;

		default:
			return BaseCommonConstants.USER_STAT_ACTIVE;
		}

	}

	public void fetchUserRoleList(String uid, UserDTO userDTO, Model model) {

		if (uid != null) {
			UserToRoleDTO userToRoleDTO = userService.fetchUserRoleList(uid, userDTO);
			List<RoleDTO> assignRole = userToRoleDTO.getRoleDTOs();
			model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userDTO);
			model.addAttribute(BaseCommonConstants.USER_ROLE, userDTO);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.TRANSACT_YES);
			model.addAttribute(BaseCommonConstants.ROLE_USER_OPTION_LIST, assignRole);
			model.addAttribute(CommonConstants.REQ_TYPE, userDTO.getUserType());
		}
	}

	@PostMapping("/editUser")
	@PreAuthorize("hasAuthority('Edit User')")
	public String editUser(@RequestParam("uid") String uid, @RequestParam("userType") String userType, Model model) {
		UserInfoDTO userInfoDto = new UserInfoDTO();
		try {

			userInfoDto = userService.getUserForEditById(uid);
			model.addAttribute(SHOWBUTTON, YES);
			userInfoDto.setAddEditFlag(BaseCommonConstants.EDIT_USER);
			model.addAttribute(BaseCommonConstants.USER_EDIT, BaseCommonConstants.EDIT_USER);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, BaseCommonConstants.EDIT_USER);
			model.addAttribute(EDIT, EDIT_TRUE);
			model.addAttribute(BaseCommonConstants.STATE_LIST, masterService.getStateMasterList());
			model.addAttribute(BaseCommonConstants.PARTICIPANT_USER_LIST, userService.getParticipantList());

			model.addAttribute(BaseCommonConstants.MAIN_TAB_EDIT, CommonConstants.YES_FLAG);

			model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userInfoDto);
			if (BaseCommonConstants.USER_TYPE_BANK_ADMIN.equalsIgnoreCase(userType)) {
				model.addAttribute(BaseCommonConstants.SHOW_PREFIX, CommonConstants.YES_FLAG);
			}
			if (0 != userInfoDto.getStateId()) {
				model.addAttribute(BaseCommonConstants.CITY_LIST,
						masterService.getCityMasterListByStateId(userInfoDto.getStateId()));
			}
			if (StringUtils.isNotEmpty(userInfoDto.getBankName())
					&& BaseCommonConstants.SETTLEMENT.equalsIgnoreCase(userInfoDto.getAccessLevel())) {
				List<BinDTO> binListUnique = new ArrayList<>();

				List<BinDTO> binList = userService.getBinDetailListByUserId(uid);

				List<BinDTO> binLists = userService
						.getBinDetailListByParticipantId(userInfoDto.getBankName().split(DASH)[0]);

				List<Integer> binIdList = new ArrayList<>();
				for (BinDTO temp : binList) {
					binIdList.add(temp.getBinId());
				}

				for (BinDTO temp : binLists) {

					if (!binIdList.contains(temp.getBinId())) {

						binListUnique.add(temp);
					}
				}

				model.addAttribute(BIN_LISTS, binLists);

				model.addAttribute(BIN_UNIQUE, binListUnique);

				List<String> lookUpList = new ArrayList<>();

				lookUpList.add(BaseCommonConstants.BIN_PRODUCT_TYPE);
				lookUpList.add(BaseCommonConstants.BIN_CARD_TYPE);
				lookUpList.add(BaseCommonConstants.BIN_CARD_VARIANT);
				lookUpList.add(BaseCommonConstants.PRODUCT_TYPE);

				setBinDTO(userInfoDto, binList, lookUpList);
				model.addAttribute(BaseCommonConstants.USER_BIN_LIST, binList);

			}

			List<UserDTO> userDTOList = userService.getUserProfile(Integer.valueOf(uid));

			userInfoDto.setParticipantName(userInfoDto.getBankName());

			userDTOList.get(0).setParticipantName(userDTOList.get(0).getBankName());
			userRoleMapping(userType, userInfoDto, model);
			fetchUserRoleList(uid, userDTOList.get(0), model);
			userInfoDto.setRequestState(userDTOList.get(0).getRequestState());
			model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userInfoDto);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_USER, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
		}
		handleSetStatus(userInfoDto);

		model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userInfoDto);
		model.addAttribute(BaseCommonConstants.SALUTATION_LIST, lookUpService.getLookupData(BaseCommonConstants.SALUTATION));
		return getView(model, ADD_EDIT_USER);
	}

	private void setBinDTO(UserInfoDTO userInfoDto, List<BinDTO> binList, List<String> lookUpList) {
		List<Map<String, String>> lookUpMapArr = masterService.prepareLookUpMap(lookUpList);
		if (CollectionUtils.isNotEmpty(binList)) {

			for (BinDTO x : binList) {
				if (x.getBinType().equalsIgnoreCase(BaseCommonConstants.ACQUIRER_BIN_FLAG)) {
					x.setBinType(ACQUIRER);
				} else {
					x.setBinType(ISSUER);
				}
				x.setBinProductType(lookUpMapArr.get(0).get(x.getBinProductType()));
				x.setBinCardType(lookUpMapArr.get(1).get(x.getBinCardType()));
				x.setBinCardvariant(lookUpMapArr.get(2).get(x.getBinCardvariant()));
				x.setProductType(lookUpMapArr.get(3).get(x.getProductType()));

			}

			StringBuilder bins = new StringBuilder(String.valueOf(binList.get(0).getBinNumber()));

			for (int i = 1; i < binList.size(); i++) {
				bins.append(COMMA_SEPERATOR);
				bins.append(binList.get(i).getBinNumber());

			}
			userInfoDto.setBinFlags(bins.toString());
		}
	}

	private void handleSetStatus(UserInfoDTO userInfoDto) {
		if (ACTIV.equalsIgnoreCase(userInfoDto.getStatus())) {
			userInfoDto.setStatus(BaseCommonConstants.USER_STAT_ACTIVE);
		} else if (SUSPEND.equalsIgnoreCase(userInfoDto.getStatus())) {
			userInfoDto.setStatus(BaseCommonConstants.USER_STAT_SUSPENDED);
		} else if (NEW_USER.equalsIgnoreCase(userInfoDto.getStatus()) || RESET.equalsIgnoreCase(userInfoDto.getStatus())) {
			userInfoDto.setStatus(BaseCommonConstants.USER_STAT_RESET);
		}
	}

	public void userRoleMapping(String userType, UserInfoDTO userInfoDto, Model model) {

		UserToRoleDTO userToRoleDTO = new UserToRoleDTO();
		List<RoleDTO> assignRoleList = new ArrayList<>();

		if (BaseCommonConstants.EDIT_USER.equalsIgnoreCase(userInfoDto.getAddEditFlag())) {
			userToRoleDTO = userService.getUserToRole(userType, userInfoDto.getUserId(), userInfoDto.getRequestState());
			assignRoleList = userToRoleDTO.getRoleDTOs();
		}

		userInfoDto.setUserType(userType);
		List<RoleDTO> roleOptionList = userService.getAvailableRoleByUserType(userInfoDto);
		model.addAttribute(ROLE_OPTION_LIST, roleOptionList);
		model.addAttribute(AROLE_OPTION_LIST, assignRoleList);
		model.addAttribute(ROLEIDS, assignRoleList);
		model.addAttribute(SHOWBUTTON, YES);
		model.addAttribute(REQ_TYPE, userType);
		userToRoleDTO.setMakerChecker(userInfoDto.getMakChkFlag());

		model.addAttribute(USERROLE, userToRoleDTO);

	}

	@PostMapping("/userCreation")
	@PreAuthorize("hasAuthority('Add User')")
	public String userCreation(@RequestParam("userType") String userType, Model model) {
		UserInfoDTO userInfoDto = new UserInfoDTO();

		try {
			addDefaultListData(model);
			model.addAttribute(BaseCommonConstants.USER_ADD, BaseCommonConstants.ADD_USER);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, BaseCommonConstants.ADD_USER);

			if (BaseCommonConstants.USER_TYPE_BANK_ADMIN.equalsIgnoreCase(userType)) {
				model.addAttribute(BaseCommonConstants.SHOW_PREFIX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(BaseCommonConstants.DEPLOYMENT_NETWORK_TYPE, networkType);
			userInfoDto.setAddEditFlag(BaseCommonConstants.ADD_USER);
			userRoleMapping(userType, userInfoDto, model);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_USER, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);

		}
		model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userInfoDto);
		return getView(model, ADD_EDIT_USER);

	}

	public void addDefaultListData(Model model) {
		List<LookUpDTO> stateList = masterService.getStateMasterList();
		model.addAttribute(BaseCommonConstants.STATE_LIST, stateList);

		List<ParticipantDTO> participantList = userService.getParticipantList();
		model.addAttribute(BaseCommonConstants.PARTICIPANT_USER_LIST, participantList);
		
		model.addAttribute(BaseCommonConstants.SALUTATION_LIST, lookUpService.getLookupData(BaseCommonConstants.SALUTATION));
	}

	@PostMapping("/getBinDetail")
	@PreAuthorize("hasAuthority('View User')")
	public ResponseEntity<Object> getBinDetail(Model model, @RequestParam("participantId") String participantId) {

		List<BinDTO> binList = new ArrayList<>();

		List<String> lookUpList = new ArrayList<>();

		lookUpList.add(BaseCommonConstants.BIN_PRODUCT_TYPE);
		lookUpList.add(BaseCommonConstants.BIN_CARD_TYPE);
		lookUpList.add(BaseCommonConstants.BIN_CARD_VARIANT);
		lookUpList.add(BaseCommonConstants.PRODUCT_TYPE);

		List<Map<String, String>> lookUpMapArr = masterService.prepareLookUpMap(lookUpList);

		if (StringUtils.isNotBlank(participantId)) {

			binList = userService.getBinDetailListByParticipantId(String.valueOf(participantId));

			for (BinDTO x : binList) {
				if (x.getBinType().equalsIgnoreCase(BaseCommonConstants.ACQUIRER_BIN_FLAG)) {
					x.setBinType(ACQUIRER);
				} else {
					x.setBinType(ISSUER);
				}
				x.setBinProductType(lookUpMapArr.get(0).get(x.getBinProductType()));
				x.setBinCardType(lookUpMapArr.get(1).get(x.getBinCardType()));
				x.setBinCardvariant(lookUpMapArr.get(2).get(x.getBinCardvariant()));
				x.setProductType(lookUpMapArr.get(3).get(x.getProductType()));

			}
			model.addAttribute(BaseCommonConstants.USER_BIN_LIST, binList);
		}

		return new ResponseEntity<>(binList, HttpStatus.OK);

	}

	@PostMapping("/addUser")
	@PreAuthorize("hasAuthority('Add User')")
	public String addUser(@RequestParam("userType") String userType,
			@ModelAttribute("userInfoDto") @Valid UserInfoDTO userInfoDto, BindingResult result, Model model) {

		try {

			userInfoDto.setParticipantName(URLDecoder.decode(userInfoDto.getParticipantName(), UTF_8));
			userInfoDto.setAddEditFlag(BaseCommonConstants.ADD_USER);
			streetAddressValidation(userInfoDto);

			if (result.hasErrors()) {
				model.addAttribute(BaseCommonConstants.USER_ADD, BaseCommonConstants.ADD_USER);
				model.addAttribute(BaseCommonConstants.SHOW_BUTTON, BaseCommonConstants.ADD_USER);
			} else {
				Locale locale=Locale.ROOT;
				userInfoDto.setUserType(userType);
				userService.addEditUser(userInfoDto);
				addUserToRole(userInfoDto, model);
				if (BaseCommonConstants.SETTLEMENT.equalsIgnoreCase(userInfoDto.getAccessLevel())) {

					String[] bin = userInfoDto.getBinIds().split(PIPE);

					StringBuilder bins = new StringBuilder(bin[1]);
					for (int i = 2; i < bin.length; i = i + 2) {
						bins.append(COMMA_SEPERATOR);
						bins.append(bin[i + 1]);
					}
					userInfoDto.setBins(bins.toString());
				}
				model.addAttribute(CommonConstants.SUCCESS_STATUS,
						messageSource.getMessage(AM_MSG_PENDINGUSER, null, locale));
				model.addAttribute(BaseCommonConstants.USER_ADD, BaseCommonConstants.ADD_USER);
				if (BaseCommonConstants.USER_TYPE_BANK_ADMIN.equalsIgnoreCase(userType)) {
					model.addAttribute(BaseCommonConstants.SHOW_PREFIX, CommonConstants.YES_FLAG);
				}

			}
		} catch (Exception ex) {
			model.addAttribute(BaseCommonConstants.USER_ADD, BaseCommonConstants.ADD_USER);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, BaseCommonConstants.ADD_USER);
			handleErrorCodeAndForward(model, ADD_EDIT_USER, ex);
		}
		addDefaultListData(model);

		userInfoDto.setParticipantName(userInfoDto.getBankName());
		model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userService.decryptUserSensitiveData(userInfoDto));
		model.addAttribute(BaseCommonConstants.CITY_LIST,
				masterService.getCityMasterListByStateId(userInfoDto.getStateId()));
		model.addAttribute(BaseCommonConstants.USER_BIN_LIST,
				userService.getBinDetailListByParticipantId(userInfoDto.getParticipantId()));
		model.addAttribute(BaseCommonConstants.MAIN_TAB_EDIT, CommonConstants.YES_FLAG);
		return getView(model, ADD_EDIT_USER);
	}

	public void addUserToRole(UserInfoDTO userInfoDTO, Model model) {

		List<RoleDTO> assignRoleList = userService.getPendingRolesForApproving(userInfoDTO);

		model.addAttribute(USERROLE, userInfoDTO);
		model.addAttribute(BACK_BUTTON, YES);
		model.addAttribute(AROLE_OPTION_LIST, assignRoleList);
		model.addAttribute(ROLEIDS, assignRoleList);

	}

	@PostMapping("/updateUser")
	@PreAuthorize("hasAuthority('Edit User')")
	public String updateUser(@ModelAttribute("userInfoDto") UserInfoDTO userInfoDto, BindingResult result,
			@RequestParam("userType") String userType, Model model) {
		Locale locale=Locale.ROOT;
		try {
			userInfoDto.setParticipantName(URLDecoder.decode(userInfoDto.getParticipantName(), UTF_8));
			userInfoDto.setAddEditFlag(BaseCommonConstants.EDIT_USER);

			if (null != userInfoDto.getRequestState() && userInfoDto.getRequestState().equalsIgnoreCase(RESET)) {
				model.addAttribute(BaseCommonConstants.APRROVAL_TAB_EDIT, CommonConstants.YES_FLAG);
			} else {
				model.addAttribute(BaseCommonConstants.MAIN_TAB_EDIT, CommonConstants.YES_FLAG);
			}

			streetAddressValidation(userInfoDto);
			if (result.hasErrors()) {

				model.addAttribute(CommonConstants.ERROR_STATUS, INVALID_INPUT);
			} else {
				userService.addEditUser(userInfoDto);
				addUserToRole(userInfoDto, model);

				model.addAttribute(CommonConstants.SUCCESS_STATUS,
						messageSource.getMessage(AM_MSG_PENDING_UPDATED_USER, null, locale));
				if (BaseCommonConstants.USER_TYPE_BANK_ADMIN.equalsIgnoreCase(userType))
					{
					model.addAttribute(BaseCommonConstants.SHOW_PREFIX, CommonConstants.YES_FLAG);
					}

			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_USER, ex);
		}
		model.addAttribute(BaseCommonConstants.USER_EDIT, BaseCommonConstants.EDIT_USER);

		userInfoDto.setStatus(setUserStatus(userInfoDto.getStatus()));

		if (BaseCommonConstants.SETTLEMENT.equalsIgnoreCase(userInfoDto.getAccessLevel())) {

			String[] bin = userInfoDto.getBinIds().split(PIPE);

			StringBuilder bins = new StringBuilder(bin[1]);

			for (int i = 2; i < bin.length; i = i + 2) {
				bins.append(COMMA_SEPERATOR);
				bins.append(bin[i + 1]);

			}
			userInfoDto.setBins(bins.toString());
		}

		model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userService.decryptUserSensitiveData(userInfoDto));
		addDefaultListData(model);

		model.addAttribute(BaseCommonConstants.CITY_LIST,
				masterService.getCityMasterListByStateId(userInfoDto.getStateId()));
		model.addAttribute(BaseCommonConstants.USER_BIN_LIST,
				userService.getBinDetailListByParticipantId(userInfoDto.getParticipantId()));

		return getView(model, ADD_EDIT_USER);

	}

	@PostMapping("/checkDuplicateLoginId")
	@PreAuthorize("hasAuthority('Add User')")
	public ResponseEntity<Object> checkDuplicateLoginId(Model model, @RequestParam("loginId") String loginId) {

		boolean result = userService.validateLoginID(loginId);

		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty(BaseCommonConstants.RESPONSE_STATUS, CommonConstants.TRANSACT_SUCCESS);

		} else {
			jsonResponse.addProperty(BaseCommonConstants.RESPONSE_STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/getUserCityMaster")
	@PreAuthorize("hasAuthority('View User')")
	public ResponseEntity<Object> getCityMaster(Model model, @RequestParam("stateId") String stateId) {

		List<LookUpDTO> cityList = new ArrayList<>();

		if (StringUtils.isNotBlank(stateId)) {
			cityList = masterService.getCityMasterListByStateId(Integer.parseInt(stateId));
			model.addAttribute(BaseCommonConstants.CITY_LIST, cityList);
		}

		return new ResponseEntity<>(cityList, HttpStatus.OK);

	}

	@PostMapping("/getRoleListBasedOnUserType")
	@PreAuthorize("hasAuthority('View User')")
	public ResponseEntity<Object> getRoleListBasedOnUserType(@RequestParam("makerChecker") String makerChecker,
			@RequestParam("rtype") String rtype, @RequestParam("roleId") String roleId, Model model) {

		UserInfoDTO userInfoDto = new UserInfoDTO();
		userInfoDto.setMakChkFlag(makerChecker);
		userInfoDto.setUserType(rtype);
		List<RoleDTO> roleOptionList = userService.getAvailableRoleByUserType(userInfoDto);

		roleOptionList.sort(Comparator.comparing(RoleDTO::getRoleName, String.CASE_INSENSITIVE_ORDER));
		return new ResponseEntity<>(roleOptionList, HttpStatus.OK);
	}

	@PostMapping("/getUserProfile")
	@PreAuthorize("hasAuthority('View User Profile')")
	public ModelAndView getUserProfile(Model model) {
		ModelAndView modelAndView = new ModelAndView(getView(model, USER_PROFILE));
		UserDTO userDTO = userService.getLoggedInUser();
		userService.getRolesAndFunctionsForUser(userDTO);

		userDTO.setUserType(userDTO.getMakChkFlag());

		modelAndView.addObject(USER_DTO, userDTO);

		return modelAndView;
	}

	@PostMapping("/changePassword")
	public ModelAndView changePassword(Model model, HttpServletRequest request) {
		Locale locale=Locale.ROOT;
		ModelAndView modelAndView = new ModelAndView(getView(model, CHANGE_PASSWD));
		UserDTO usDTO = userService.getLoggedInUser();
		
		if (usDTO.getSecretId() == 0) {
			getView(model, UPDATE_SECRET_QUESTION);
			model.addAttribute(UPDATE_USER_SECRET_QUESTION, request.getContextPath() + UPDATE_USER_SECRET_QUE);
			usDTO.setSecretId(0);
			usDTO.setSecretAnswer("");
			model.addAttribute(USER_DTO, usDTO);
			List<SecretQuestionDTO> secretQuestionDTOs = secretQuestionService.getSecretQuestionList();
			model.addAttribute(BaseCommonConstants.SECRET_QUESTION_DTO, secretQuestionDTOs);

			return modelAndView;
		}

		SecretQuestionDTO secretQuestionDTO = secretQuestionService.getSecretQuestionById(usDTO.getSecretId());
		if (secretQuestionDTO != null && !StringUtils.isBlank(secretQuestionDTO.getDescription())) {
			modelAndView.addObject(BaseCommonConstants.SECRET_QUE, secretQuestionDTO.getDescription());
			modelAndView.addObject(BaseCommonConstants.SEC_QUE, secretQuestionDTO.getDescription());
		}
		usDTO.setSecretAnswer("");

		model.addAttribute(USER_DTO, usDTO);
		model.addAttribute(CHANGE_USER_PSWD, request.getContextPath() + CHANGE_USER_PASWD_API);
		String userAction = request.getAttribute(BaseCommonConstants.USER_ACTION) != null
				? (String) request.getAttribute(BaseCommonConstants.USER_ACTION)
				: BaseCommonConstants.USER_ACTION_CHANGE_PASSWORD;
		model.addAttribute(BaseCommonConstants.USER_ACTION, userAction);
		
		if (request.getAttribute(BaseCommonConstants.HIDE_OLD_PSWD) != null)
		{
			model.addAttribute(BaseCommonConstants.HIDE_OLD_PSWD, request.getAttribute(BaseCommonConstants.HIDE_OLD_PSWD));
		}

		UserDTO historydto = userService.getPasswordHistory(usDTO.getUserId());
		if (null != historydto && (!StringUtils.isBlank(historydto.getStatusCode()))
				&& (historydto.getPwdAlertDays() == -1)) {

			String x = PSD_EXP_FOR_USER + usDTO.getUserName();

			x = x.replace(HYPHEN, "");
			model.addAttribute(CommonConstants.ERROR_STATUS, messageSource.getMessage(YEAR, null, x, locale));
			model.addAttribute(BaseCommonConstants.HIDE_SECRET_QUE_ANS, "");
			model.addAttribute(BaseCommonConstants.HIDE_OLD_PSWD, "");

		}

		model.addAllAttributes(userService.getUserPasswordPolicyMap());
		return modelAndView;
	}
	
	 //Decoding for password
	  private static String decode(String data) {
		  StringBuilder decryptedPd=new StringBuilder();
	        for (int i = 0; i < data.length(); i = i + 3) {
	            if (data.charAt(i) == '0') {
	            	decryptedPd.append(Character.toString((char) Integer.parseInt(data.substring(i + 1, i + 3))));
	            } else {
	            	decryptedPd.append(Character.toString((char) Integer.parseInt(data.substring(i, i + 3))));
	            }
	        }
	        return decryptedPd.toString();
	    }

	@PostMapping(CHANGE_USER_PASWD_API)
	public String changeUserPassword(@RequestParam(BaseCommonConstants.USER_ACTION) String userAction,
			@ModelAttribute(USER_DTO) UserDTO userDTO, HttpSession session, Model model, RedirectAttributes redir) {
		Locale locale=Locale.ROOT;
		try {
			
			userDTO.setNewPasswd(CredentialGenUtil.decode(userDTO.getNewPasswd()));
			handleSetPswd(userDTO);		
			userDTO.setConfirmPasswd(CredentialGenUtil.decode(userDTO.getConfirmPasswd()));
			
			model.addAttribute(BaseCommonConstants.USER_ACTION, userAction);
			model.addAllAttributes(userService.getUserPasswordPolicyMap());
			if (handleChangePswdExtracted(userAction)) {
				userService.changeUserPassword(userDTO);
			} else if (!StringUtils.isBlank(userAction)
					&& (BaseCommonConstants.USER_ACTION_FORGOT_PASSWORD.equals(userAction)
							|| BaseCommonConstants.USER_ACTION_RESET_PASSWORD.equals(userAction))) {
				model.addAttribute(BaseCommonConstants.HIDE_PSWD, BaseCommonConstants.HIDE_PSWD);
				if (StringUtils.isBlank(userDTO.getNewPasswd()) || StringUtils.isBlank(userDTO.getConfirmPasswd())) {
					model.addAttribute(BaseCommonConstants.HIDE_OLD_PSWD, CommonConstants.YES_FLAG);
					model.addAttribute(BaseCommonConstants.HIDE_SECRET_QUE_ANS, CommonConstants.YES_FLAG);
					model.addAttribute(CommonConstants.ERROR_STATUS,
							messageSource.getMessage(BaseCommonConstants.PASSWD_EMPTY, null, locale));
					model.addAttribute(CommonConstants.SUCCESS_STATUS, "");
					return getView(model, CHANGE_PSWD);
				}

				userDTO = handleUserActionReset(userAction, userDTO, model);
			}

			if (!StringUtils.isBlank(userDTO.getStatusCode())
					&& userDTO.getStatusCode().equals(CommonConstants.SUCCESS_STATUS)) {
				model.addAttribute(CommonConstants.ERROR_STATUS, "");
				session.setAttribute(CommonConstants.ERROR_STATUS, "");
				session.setAttribute(CommonConstants.SUCCESS_STATUS,
						messageSource.getMessage(AM_MSC_PWDCHNGSUCCESS, null, locale)
								+ LOGIN_MSG);
				return REDIRECT;
			} else {
				if (StringUtils.isBlank(userDTO.getStatusCode())) {
					model.addAttribute(CommonConstants.ERROR_STATUS,
							messageSource.getMessage(AM_MSG_PEDCHNGERROROCCURS, null, locale));
					return getView(model, CHANGE_PASSWD);
				}
				addMessageByStatusCode(userDTO, model, locale);
			}
		} catch (Exception ex) {

			throw new SettleNxtException("Error Updating Password", "", ex);
		}
		return getView(model, CHANGE_PASSWD);
	}

	private boolean handleChangePswdExtracted(String userAction) {
		return !StringUtils.isBlank(userAction) && BaseCommonConstants.USER_ACTION_CHANGE_PASSWORD.equals(userAction);
	}

	

	private void addMessageByStatusCode(UserDTO userDTO, Model model, Locale locale) {
		switch (userDTO.getStatusCode()) {
		case BaseCommonConstants.OLD_PASSWORD_WRONG:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_OLDPSD, null, locale));
			break;
		case BaseCommonConstants.SECANS_PASSWORD_NOT_PRESENT:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_SECQUENOTPRESENT, null, locale));
			break;
		case BaseCommonConstants.SECANS_PASSWORD_WRONG:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_INVALIDSECANS, null, locale));
			break;
		case BaseCommonConstants.NEW_PASSWORD_CONFIRM_PASSWORD_NOT_MATCHED:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_NEWANDCONFIRMPWDNOTMATCHED, null, locale));
			break;
		case BaseCommonConstants.OLD_NEW_PASSWORD_MATCHED:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_SAMEPSD, null, locale));
			break;
		case BaseCommonConstants.NEW_PASSWORD_USERNAME_MATCHED:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_SAMEPWDASNAME, null, locale));
			break;
		case BaseCommonConstants.PASSWORD_POLICY_MIN_LENGTH:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_CUSTOMPWDLENGTH, null, locale));
			break;
		case BaseCommonConstants.PASSWORD_POLICY_LOWER_CASE_REQUIRED:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_INVALIDLCPSD, null, locale));
			break;
		case BaseCommonConstants.PASSWORD_POLICY_UPPER_CASE_REQUIRED:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_INVALIDUCPSD, null, locale));
			break;
		case BaseCommonConstants.PASSWORD_POLICY_DIGIT_REQUIRED:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_INVALIDNUMPSD, null, locale));
			break;
		case BaseCommonConstants.PASSWORD_POLICY_SPECIAL_REQUIRED:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_PWDSPECIALCHAR, null, locale));
			break;
		case BaseCommonConstants.PASSWORD_POLICY_PASSWORD_HISTORY_COUNT:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_SAMEPSD, null, locale));
			break;
		case BaseCommonConstants.DEFAULT_NEW_PASSWORD_MATCHED:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_DEFAULTPSD, null, locale));
			break;
		case BaseCommonConstants.PASSWORD_POLICY_REPEATING_CHARS:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_REPEATED_CHAR, null, locale));
			break;
		default:
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_PEDCHNGERROROCCURS, null, locale));
		}
	}


	private void handleSetPswd(UserDTO userDTO) {
		if (!StringUtils.isBlank(userDTO.getPasswd())) {
			userDTO.setPasswd(CredentialGenUtil.decode(userDTO.getPasswd()));
		}
	}

	private UserDTO handleUserActionReset(String userAction, UserDTO userDTO, Model model) throws SettleNxtException {
		if (BaseCommonConstants.USER_ACTION_RESET_PASSWORD.equals(userAction)) {
			userDTO = userService.resetUserPassword(userDTO); // resetPassword
		} else {
			userDTO = userService.recoveryUserPassword(userDTO); // forgotPassword
		}

		if (StringUtils.isBlank(userDTO.getStatusCode())
				|| !CommonConstants.SUCCESS_STATUS.equals(userDTO.getStatusCode())) {
			model.addAttribute(BaseCommonConstants.HIDE_OLD_PSWD, CommonConstants.YES_FLAG);
			
		}
		return userDTO;
	}


	@PostMapping("/getPendingUser")
	@PreAuthorize("hasAuthority('View User')")
	public String getPendingUser(@RequestParam("uid") String userId, Model model) {
		ApprovalDataDTO approvalDataDto = new ApprovalDataDTO();
		UserDTO userDto = new UserDTO();
		List<RoleDTO> roleList = new ArrayList<>();
		try {

			userDto = userService.getUserStgInfo(userId);
			roleList = userService.getUserRoleStg(userId);

			List<BinDTO> binList = userService.getBinDetailListByUserId(userId);
			if (CollectionUtils.isNotEmpty(binList)) {
				StringBuilder bins = new StringBuilder(String.valueOf(binList.get(0).getBinNumber()));

				for (int i = 1; i < binList.size(); i++) {
					bins.append(COMMA_SEPERATOR);
					bins.append(binList.get(i).getBinNumber());

				}
				userDto.setBins(bins.toString());

			}

		} catch (Exception ex) {
			Map<String, Object> errorMap = new HashMap<>();
			errorMap.put(CommonConstants.ERROR_STATUS, "Data Not Exist.");
			handleErrorAndForward(model, errorMap, VIEW_APPROVE_USER_INFO, ex);
		}

		model.addAttribute(BaseCommonConstants.REQUEST_INFO, approvalDataDto);
		model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userDto);
		model.addAttribute(BaseCommonConstants.USER_ROLES, roleList);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.TRANSACT_YES);

		model.addAttribute(CommonConstants.REQ_TYPE, userDto.getUserType());
		return getView(model, VIEW_APPROVE_USER_INFO);

	}

	@PostMapping("/approveUserStatus")
	@PreAuthorize("hasAuthority('Approve User')")
	public String approveUserStatus(@RequestParam("userId") String userId,
			@RequestParam(BaseCommonConstants.RESPONSE_STATUS) String status, @RequestParam("remarks") String remarks,
			Model model, HttpServletRequest request) {
		try {
			UserDTO userDTO = userService.updateApproveOrRejectUser(userId, status, remarks);
			checkUserApproveStatus(userDTO, model, request);

			model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userService.decryptUserSensitiveData(userDTO));
			fetchUserRoleList(String.valueOf(userDTO.getUserId()), userDTO, model);

			List<BinDTO> binList = userService.getBinDetailListByUserId(userId);
			if (CollectionUtils.isNotEmpty(binList)) {
				StringBuilder bins = new StringBuilder(String.valueOf(binList.get(0).getBinNumber()));

				for (int i = 1; i < binList.size(); i++) {
					bins.append(COMMA_SEPERATOR);
					bins.append(binList.get(i).getBinNumber());

				}
				userDTO.setBins(bins.toString());

			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_USER_INFO, ex);
		}
		return getView(model, VIEW_APPROVE_USER_INFO);
	}

	private void checkUserApproveStatus(UserDTO userDTO, Model model, HttpServletRequest request) {
		Locale locale=Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(userDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage(AM_MSG_USERAPPROVED, null, locale));
		} else if (CommonConstants.TRANSACT_FAIL.equalsIgnoreCase(userDTO.getStatusCode())
				&& (!request.getParameter(BaseCommonConstants.RESPONSE_STATUS).equalsIgnoreCase(RESET))) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage(AM_MSG_USERADDFAIELD, null, locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage(AM_MSG_USERREJECTED, null, locale));
		}
	}

	@PostMapping("/forgotPassword")
	public ModelAndView forgotPassword(Model model, @RequestParam("username") String username,
			HttpServletRequest request, HttpSession session) {
		Locale locale=Locale.ROOT;
		request.getSession().removeAttribute(CommonConstants.ERROR_STATUS);
		request.getSession().removeAttribute(CommonConstants.SUCCESS_STATUS);
		String portal = env.getProperty(BaseCommonConstants.PORTAL);

		if (StringUtils.isBlank(portal)) {
			portal = CommonConstants.SETTLENXT_ADMIN_PORTAL;
		}
		model.addAttribute("portal", portal);
		if (StringUtils.isBlank(username)) {
			model.addAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage("NotNull.LoginDTO.loginID", null, locale)); // Change
			model.addAttribute(BODY, BaseCommonConstants.USER_LOGIN);
			return new ModelAndView(LOGIN_LAYOUT);

		} else {
			request.getSession().removeAttribute(CommonConstants.ERROR_STATUS);
		}
		UserDTO usDTO = userService.getUser(username);

		if (usDTO == null) {

			session.setAttribute(CommonConstants.ERROR_STATUS,
					messageSource.getMessage("NotCorrect.LoginDTO.loginID", null, locale));
			model.addAttribute(BODY, BaseCommonConstants.USER_LOGIN);
			return new ModelAndView(LOGIN_LAYOUT);

		} else {

			if ("3".equals(usDTO.getUserType()) || "4".equals(usDTO.getUserType())) {
				session.setAttribute(CommonConstants.ERROR_STATUS,
						messageSource.getMessage("NotCorrect.LoginDTO.loginID", null, locale));
				model.addAttribute(BODY, BaseCommonConstants.USER_LOGIN);
				return new ModelAndView(LOGIN_LAYOUT);
			}
			model.addAttribute(CommonConstants.ERROR_STATUS, "");

			request.getSession().removeAttribute(CommonConstants.ERROR_STATUS);

		}

		if (usDTO.getSecretId() != 0) {
			SecretQuestionDTO secretQuestionDTO = secretQuestionService.getSecretQuestionById(usDTO.getSecretId());
			model.addAttribute(BaseCommonConstants.SECRET_QUE, secretQuestionDTO.getDescription());
			model.addAttribute(BaseCommonConstants.SEC_QUE, secretQuestionDTO.getDescription());
		} else {
			model.addAttribute(BaseCommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_SECQUENOTPRESENT, null,Locale.ROOT)); // Change

			model.addAttribute(BODY, BaseCommonConstants.USER_LOGIN);
			return new ModelAndView(LOGINLAYOUT);
		}

		usDTO.setSecretAnswer("");

		model.addAttribute(BODY, FORGOT_PSWD);
		model.addAttribute(UIPINGINTERVAL, environment.getProperty(SETTLENXT_UI_PING_INTERVAL));
		refreshSession();

		ModelAndView modelAndView = new ModelAndView(SETTLENXTLAYOUT);

		model.addAttribute(VERIFYSECANSWER, request.getContextPath() + "/verifySecretAnswer");
		model.addAttribute(USER_DTO, usDTO);
		model.addAttribute(BaseCommonConstants.HIDE_PSWD, BaseCommonConstants.HIDE_PSWD);
		return modelAndView;
	}

	
	@PostMapping("/verifySecretAnswer")
	public String verifyShecretAnswer(@ModelAttribute(USER_DTO) UserDTO userDTO, HttpSession session, Model model,
			RedirectAttributes redir, HttpServletRequest request) {
		model.addAttribute(BaseCommonConstants.HIDE_PSWD, BaseCommonConstants.HIDE_PSWD);
		if (StringUtils.isBlank(userDTO.getSecretAnswer())) {
			model.addAttribute(BaseCommonConstants.ERROR_STATUS,
					messageSource.getMessage(BaseCommonConstants.PD_EMPTY, null, Locale.ROOT)); // Change
			userDTO.setSecretAnswer("");
			model.addAttribute(USER_DTO, userDTO);
			SecretQuestionDTO secretQuestionDTO = secretQuestionService.getSecretQuestionById(userDTO.getSecretId());
			model.addAttribute(BaseCommonConstants.SECRET_QUE, secretQuestionDTO.getDescription());
			model.addAttribute(BaseCommonConstants.SEC_QUE, secretQuestionDTO.getDescription());
			return getView(model, FORGOT_PSWD);
		} else {
			userDTO.setLastUpdatedBy(userDTO.getUserName());
			userDTO.setLastUpdatedOn(LocalDateTime.now());
			userDTO = userService.verifyUserSecretAnswer(userDTO);
		}
		List<SecretQuestionDTO> secretQuestionDTOs = secretQuestionService.getSecretQuestionList();
		model.addAttribute(BaseCommonConstants.SECRET_QUESTION_DTO, secretQuestionDTOs);
		if (!StringUtils.isBlank(userDTO.getStatusCode())
				&& userDTO.getStatusCode().equals(BaseCommonConstants.SUCCESS_STATUS)) {

			model.addAttribute(VERIFYSECANSWER, "");
			model.addAttribute(BaseCommonConstants.USER_ACTION, BaseCommonConstants.USER_ACTION_FORGOT_PSD);
			model.addAttribute(BaseCommonConstants.HIDE_OLD_PSWD, "Y");

			model.addAttribute(BaseCommonConstants.HIDE_SECRET_QUE_ANS, "Y");

			model.addAttribute(BaseCommonConstants.ERROR_STATUS, "");
			session.setAttribute(BaseCommonConstants.ERROR_STATUS, "");
			session.setAttribute(BaseCommonConstants.SUCCESS_STATUS,
					messageSource.getMessage(AM_MSG_EMAILSENTSUCCESS, null,Locale.ROOT)
							+ LOGIN_MSG);
			return REDIRECT;

		} else {
			model.addAttribute(BaseCommonConstants.ERROR_STATUS,
					messageSource.getMessage(AM_MSG_INVALIDSECANS, null,Locale.ROOT));
			userDTO.setSecretAnswer("");
			model.addAttribute(USER_DTO, userDTO);
			SecretQuestionDTO secretQuestionDTO = secretQuestionService.getSecretQuestionById(userDTO.getSecretId());
			model.addAttribute(BaseCommonConstants.SECRET_QUE, secretQuestionDTO.getDescription());
			model.addAttribute(BaseCommonConstants.SEC_QUE, secretQuestionDTO.getDescription());
			return getView(model, FORGOT_PSWD);
		}
	}

	
	@PostMapping("/updateSecretQuestion")
	public ModelAndView updateShecretQuestion(Model model, HttpServletRequest request) {

		model.addAttribute(BODY, UPDATE_SECRET_QUESTION);
		model.addAttribute(UIPINGINTERVAL, environment.getProperty(SETTLENXT_UI_PING_INTERVAL));
		refreshSession();

		ModelAndView modelAndView = new ModelAndView(SETTLENXTLAYOUT);

		model.addAttribute(UPDATE_USER_SECRET_QUESTION, request.getContextPath() + UPDATE_USER_SECRET_QUE);
		UserDTO userDTO = userService.getLoggedInUser();

		if (userDTO.getSecretId() != 0
				&& (BaseCommonConstants.USER_PD_RESET_PD.equalsIgnoreCase(userDTO.getStatus())
						|| "Z".equalsIgnoreCase(userDTO.getStatus())
						|| userDTO.getIsPwdChangeReq().equalsIgnoreCase(BaseCommonConstants.YES_FLAG))) {

			model.addAttribute(BODY, CHANGE_PSWD);
			model.addAttribute(UIPINGINTERVAL, environment.getProperty(SETTLENXT_UI_PING_INTERVAL));
			refreshSession();

			ModelAndView modelAndViewName = new ModelAndView(SETTLENXTLAYOUT);

			model.addAttribute(BaseCommonConstants.USER_ACTION, BaseCommonConstants.USER_ACTION_RESET_PD);
			model.addAttribute(BaseCommonConstants.HIDE_OLD_PSWD, "Y");
			model.addAttribute(BaseCommonConstants.HIDE_SECRET_QUE_ANS, "Y");
			model.addAttribute(BaseCommonConstants.HIDE_PSWD, BaseCommonConstants.HIDE_PSWD);
			model.addAttribute(UPDATE_USER_SECRET_QUESTION, "");
			model.addAttribute(USER_DTO, userDTO);
			model.addAllAttributes(userService.getUserPasswordPolicyMap());
			model.addAttribute(CHANGE_USER_PSWD, request.getContextPath() + CHANGE_USER_PASWD_API);
			model.addAttribute(BaseCommonConstants.KEY, BaseCommonConstants.PUBLIC_KEY);
			return modelAndViewName;

		}
		userDTO.setSecretId(0);
		userDTO.setSecretAnswer("");
		model.addAttribute(USER_DTO, userDTO);
		List<SecretQuestionDTO> secretQuestionDTOs = secretQuestionService.getSecretQuestionList();
		model.addAttribute(BaseCommonConstants.SECRET_QUESTION_DTO, secretQuestionDTOs);
		return modelAndView;
	}
	
	
	@PostMapping(UPDATE_USER_SECRET_QUE)
	public String updateUserShecretQuestion(@ModelAttribute(USER_DTO) UserDTO usDTO, HttpServletRequest request,
			Model model) {
		UserDTO usData = userService.getLoggedInUser();
		Locale locale=Locale.ROOT;
		try {
			List<SecretQuestionDTO> secretQuestionDTOs = secretQuestionService.getSecretQuestionList();
			model.addAttribute(BaseCommonConstants.SECRET_QUESTION_DTO, secretQuestionDTOs);

			if (usDTO.getSecretId() == 0 || StringUtils.isBlank(usDTO.getSecretAnswer())) {
				model.addAttribute(BaseCommonConstants.ERROR_STATUS,
						messageSource.getMessage(BaseCommonConstants.PD_EMPTY, null, locale));
			} else {
				usDTO = userService.updateUserSecretQuestion(usDTO);
			}
			usDTO.setSecretId(0);
			usDTO.setSecretAnswer("");
			if (!StringUtils.isBlank(usDTO.getStatusCode())) {
				if (usDTO.getStatusCode().equals(BaseCommonConstants.SUCCESS_STATUS)) {
					model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
							messageSource.getMessage(AM_MSG_UPTSECQUESSUCCESS, null, locale));
				} else {
					model.addAttribute(BaseCommonConstants.ERROR_STATUS,
							messageSource.getMessage(AM_MSG_UPTSECQUESERROROCCURS, null, locale));
				}
			}

		} catch (Exception ex) {
			throw new SettleNxtException("Error Updating Secret Question", "", ex);
		}
		if (BaseCommonConstants.USER_PSD_RESET_PSD.equalsIgnoreCase(usData.getStatus())
				|| "Z".equalsIgnoreCase(usData.getStatus())
				|| usData.getIsPwdChangeReq().equalsIgnoreCase(CommonConstants.YES_FLAG)) {
			model.addAttribute(BaseCommonConstants.USER_ACTION, BaseCommonConstants.USER_ACTION_RESET_PSD);
			model.addAttribute(BaseCommonConstants.HIDE_OLD_PSWD, CommonConstants.YES_FLAG);
			model.addAttribute(BaseCommonConstants.HIDE_SECRET_QUE_ANS, CommonConstants.YES_FLAG);
			model.addAttribute(BaseCommonConstants.HIDE_PSWD, BaseCommonConstants.HIDE_PSWD);
			model.addAttribute(UPDATE_USER_SECRET_QUESTION, "");
			model.addAttribute(USER_DTO, usData);
			model.addAllAttributes(userService.getUserPasswordPolicyMap());
			model.addAttribute(CHANGE_USER_PSWD, request.getContextPath() + CHANGE_USER_PASWD_API);

			return getView(model, CHANGE_PASSWD);
		} else {
			return getView(model, UPDATE_SECRET_QUESTION);
		}
	}
	
	public void setFunctionality(UserDTO userDto, Model model) {
		if (!"D".equalsIgnoreCase(userDto.getStatus())) {
			model.addAttribute(DEACTIVATE, DEACTIVATE);
			if (BaseCommonConstants.USER_STATUS_SUSPENDED.equalsIgnoreCase(userDto.getStatus())) {
				model.addAttribute(UNLOCKUSER, UNLOCKUSER);
			} else {
				model.addAttribute(LOCKUSER, LOCKUSER);
			}
		}
		model.addAttribute(BaseCommonConstants.USER_RESET_PASSWD, BaseCommonConstants.USER_RESET_PASSWD);

	}

	@PostMapping("/deactivateUser")
	@PreAuthorize("hasAuthority('Edit User')")
	public String deactivateUserAccount(@RequestParam("uid") String userId, Model model,
			@RequestParam(value = "userType", required = true) String userType, HttpServletRequest request) {

		UserInfoDTO userInfoDto = new UserInfoDTO();
		List<UserDTO> userDTOList = new ArrayList<>();

		try {
			Locale locale=Locale.ROOT;
			userDTOList = userService.getUserProfile(Integer.valueOf(userId));

			model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userDTOList);
			userInfoDto = userService.getUserForEditById(userId);
			fetchUserRoleList(userId, userDTOList.get(0), model);
			userInfoDto.setUserType(userType);
			userService.deactivateUser(userInfoDto);
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage(AM_MSG_USERDEACTIVATIONPENDING, null, locale));

			model.addAttribute(BaseCommonConstants.PENDING, BaseCommonConstants.PENDING);
		}

		catch (Exception ex) {

			handleErrorCodeAndForward(model, VIEW_USER_INFO, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
		}

		return getView(model, VIEW_USER_INFO);
	}

	@PostMapping("/approveUserActivateDeactivate")
	@PreAuthorize("hasAuthority('Approve User')")
	public String approveUserActivateDeactivate(@RequestParam("userId") String userId,
			@RequestParam(BaseCommonConstants.RESPONSE_STATUS) String status, @RequestParam("crtuser") String crtuser,
			@RequestParam("remarks") String remarks, Model model, HttpServletRequest request) {
		return loadActivateDeactivateUnlockLockUser(userId, status, remarks, model, request);
	}

	private String loadActivateDeactivateUnlockLockUser(String userId, String status, String remarks, Model model,
			HttpServletRequest request) {
		try {
			UserDTO userDTO = userService.updateApproveOrRejectUser(userId, status, remarks);
			checkUserApproveStatus(userDTO, model, request);
			model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userDTO);
			fetchUserRoleList(String.valueOf(userDTO.getUserId()), userDTO, model);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_USER_INFO, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
		}
		return getView(model, VIEW_APPROVE_USER_INFO);
	}

	@PostMapping("/activateUser")
	@PreAuthorize("hasAuthority('Edit User')")
	public String activateUserAccount(@RequestParam("uid") String userId, Model model,
			@RequestParam(value = "userType", required = true) String userType, HttpServletRequest request) {

		UserInfoDTO userInfoDto = new UserInfoDTO();

		try {
			Locale locale=Locale.ROOT;
			List<UserDTO> userDTOList = userService.getUserProfile(Integer.valueOf(userId));
			userInfoDto = userService.getUserForEditById(userId);
			userInfoDto.setUserType(userType);
			model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userDTOList);
			fetchUserRoleList(userId, userDTOList.get(0), model);
			userService.activateUser(userInfoDto);

			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage(AM_MSG_USERACTIVATIONPENDING, null, locale));

			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
		}

		catch (Exception ex) {

			handleErrorCodeAndForward(model, VIEW_USER_INFO, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
		}

		return getView(model, VIEW_USER_INFO);
	}

	@PostMapping("/unlockUser")
	@PreAuthorize("hasAuthority('Edit User')")
	public String lockunlockUser(@RequestParam("uid") String userId, Model model,
			@RequestParam(value = "userType", required = true) String userType, HttpServletRequest request) {

		UserInfoDTO userInfoDto = new UserInfoDTO();
		List<UserDTO> userDTOList = new ArrayList<>();
		Locale locale=Locale.ROOT;
		try {

			userDTOList = userService.getUserProfile(Integer.valueOf(userId));
			userInfoDto = userService.getUserForEditById(userId);
			model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userDTOList);
			userInfoDto.setUserType(userType);
			fetchUserRoleList(userId, userDTOList.get(0), model);
			userService.unlockUser(userInfoDto);

			if (SUSPEND.equalsIgnoreCase(userInfoDto.getStatus())) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS,
						messageSource.getMessage(AM_MSG_PENDINGLOCKUSER, null, locale));
				model.addAttribute(BaseCommonConstants.PENDING, BaseCommonConstants.PENDING);
			} else if (ACTIV.equalsIgnoreCase(userInfoDto.getStatus())) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS,
						messageSource.getMessage(AM_MSG_PENDINGUNLOCKUSER, null, locale));
				model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING,
						BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
			}

		}

		catch (Exception ex) {

			handleErrorCodeAndForward(model, VIEW_USER_INFO, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
		}

		return getView(model, VIEW_USER_INFO);
	}

	@PostMapping("/approveLockUnlockUser")
	@PreAuthorize("hasAuthority('Approve User')")
	public String approveLockUnlockUser(@RequestParam("userId") String userId,
			@RequestParam(BaseCommonConstants.RESPONSE_STATUS) String status, @RequestParam("remarks") String remarks,
			Model model, HttpServletRequest request) {
		return loadActivateDeactivateUnlockLockUser(userId, status, remarks, model, request);
	}

	@PostMapping("/resetPasswordsUser")
	@PreAuthorize("hasAuthority('Edit User')")
	public String resetPasswordsUser(@RequestParam("uid") String userId, Model model, HttpServletRequest request,
			@RequestParam(value = "userType", required = false) String userType) {

		UserInfoDTO userInfoDto = new UserInfoDTO();
		Locale locale=Locale.ROOT;
		try {
			userInfoDto = userService.getUserForEditById(userId);

			userInfoDto.setUserType(userType);
			userInfoDto.setStatus(CommonConstants.USER_PD_RESET_PD_STATUS);

			String result = userService.resetPwd(userInfoDto);

			userInfoDto.setAddEditFlag(BaseCommonConstants.RESET_PWD);

			if (result.equals(CommonConstants.TRANSACT_SUCCESS)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS,
						messageSource.getMessage("AM_MSG_userResetPwd", null, locale));
				model.addAttribute(BaseCommonConstants.PENDING, BaseCommonConstants.PENDING);
			} else if (result.equals(BaseCommonConstants.FAIL_STATUS)) {
				model.addAttribute(BaseCommonConstants.FAIL_STATUS,
						messageSource.getMessage(AM_MSG_ERRORUSERRESETPSD, null, locale));

			} else {
				model.addAttribute(CommonConstants.ERROR_STATUS,
						messageSource.getMessage(AM_MSG_ERRORUPDATEUSER, null, locale));
			}

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_USER_INFO, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
		}
		List<UserDTO> userDTOList = userService.getUserProfile(Integer.valueOf(userId));
		if (CollectionUtils.isNotEmpty(userDTOList)) {
			fetchUserRoleList(userId, userDTOList.get(0), model);
			setFunctionality(userDTOList.get(0), model);
			List<RoleDTO> roleList = userService.getAvailableRoleList(userDTOList.get(0).getUserType(),
					userDTOList.get(0).getMakChkFlag(), userDTOList.get(0).getUserId());

			model.addAttribute(BaseCommonConstants.AVAILABLE_ROLE_LIST, roleList);
		}
		model.addAttribute(BaseCommonConstants.USER_EDIT, BaseCommonConstants.USER_EDIT);
		model.addAttribute(BaseCommonConstants.USER_RESET_PASSWD, BaseCommonConstants.USER_RESET_PASSWD);
		model.addAttribute("userInfoDto", userDTOList.get(0));
		return getView(model, VIEW_USER_INFO);
	}

	@PostMapping("/getUserBinMapping")
	@PreAuthorize("hasAuthority('View User')")
	public String getUserBinMapping(Model model) {
		List<BinDTO> baseBinDTO = userService.getUserBinDetails();
		model.addAttribute("BaseBinDTO", baseBinDTO);
		return getView(model, USER_BIN_MAPPING);
	}

	@PostMapping("/editRejectedUser")
	@PreAuthorize("hasAuthority('Edit User')")
	public String editRejectedUser(@RequestParam("uid") String uid, @RequestParam("userType") String userType,
			Model model) {
		UserInfoDTO userInfoDto = new UserInfoDTO();
		try {

			userInfoDto = userService.getRejectedUserForEditById(uid);
			model.addAttribute(SHOWBUTTON, YES);
			userInfoDto.setAddEditFlag(BaseCommonConstants.EDIT_USER);
			model.addAttribute(BaseCommonConstants.USER_EDIT, BaseCommonConstants.EDIT_USER);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, BaseCommonConstants.EDIT_USER);
			model.addAttribute(EDIT, EDIT_TRUE);
			model.addAttribute(BaseCommonConstants.STATE_LIST, masterService.getStateMasterList());
			model.addAttribute(BaseCommonConstants.PARTICIPANT_USER_LIST, userService.getParticipantList());

			model.addAttribute(BaseCommonConstants.APRROVAL_TAB_EDIT, CommonConstants.YES_FLAG);

			if (BaseCommonConstants.USER_TYPE_BANK_ADMIN.equalsIgnoreCase(userType))
				{
				model.addAttribute(BaseCommonConstants.SHOW_PREFIX, CommonConstants.YES_FLAG);
				}

			if ((!userService.checkIfUserExists(Integer.parseInt(uid)))
					&& userInfoDto.getRequestState().equalsIgnoreCase(RESET))
				{
				model.addAttribute("newUserRejected", CommonConstants.TRANSACT_YES);
				}

			if (0 != userInfoDto.getStateId()) {
				model.addAttribute(BaseCommonConstants.CITY_LIST,
						masterService.getCityMasterListByStateId(userInfoDto.getStateId()));
			}

			userInfoDto.setStatus(setUserStatus(userInfoDto.getStatus()));

			userBinModifications(userInfoDto, model, uid);
			userType = userInfoDto.getUserType();
			userRoleMapping(userType, userInfoDto, model);
			model.addAttribute(CommonConstants.REQ_TYPE, userInfoDto.getUserType());
			model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userInfoDto);
			model.addAttribute(BaseCommonConstants.SALUTATION_LIST, lookUpService.getLookupData(BaseCommonConstants.SALUTATION));

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_USER, ex);
			model.addAttribute(BaseCommonConstants.TRANSACT_WORKFLOW_PENDING, BaseCommonConstants.TRANSACT_WORKFLOW_PENDING);
		}
		model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userInfoDto);
		return getView(model, ADD_EDIT_USER);
	}

	private void userBinModifications(UserInfoDTO userInfoDto, Model model, String uid) {
		if (StringUtils.isNotEmpty(userInfoDto.getBankName())
				&& BaseCommonConstants.SETTLEMENT.equalsIgnoreCase(userInfoDto.getAccessLevel()))

		{

			List<BinDTO> binListUnique = new ArrayList<>();

			List<BinDTO> binList = userService.getBinDetailListByUserId(uid);

			List<BinDTO> binLists = userService
					.getBinDetailListByParticipantId(userInfoDto.getBankName().split(DASH)[0]);

			List<Integer> binIdList = new ArrayList<>();
			for (BinDTO tempBin : binList) {
				binIdList.add(tempBin.getBinId());
			}

			for (BinDTO tempBin : binLists) {

				if (!binIdList.contains(tempBin.getBinId())) {

					binListUnique.add(tempBin);
				}
			}

			model.addAttribute(BIN_LISTS, binLists);
			model.addAttribute(BaseCommonConstants.USER_BIN_LIST, binList);
			model.addAttribute(BIN_UNIQUE, binListUnique);

			if (CollectionUtils.isNotEmpty(binList)) {

				StringBuilder bins = new StringBuilder(String.valueOf(binList.get(0).getBinNumber()));

				for (int i = 1; i < binList.size(); i++) {
					bins.append(COMMA_SEPERATOR);
					bins.append(binList.get(i).getBinNumber());

				}
				userInfoDto.setBinFlags(bins.toString());
			}

		}
	}

	@PostMapping("/discardRejectedUserEntry")
	@PreAuthorize("hasAuthority('Edit User')")
	public String discardRejectedUserEntry(@RequestParam("userId") String userId, Model model,
			HttpServletRequest request) {
		Locale locale=Locale.ROOT;
		try {
			UserDTO userDto = userService.discardRejectedUserEntry(userId);

			userDto.setRequestState(CommonConstants.REQUEST_STATE_DISCARDED);
			model.addAttribute(BaseCommonConstants.USER_INFO_DTO, userDto);
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage(AM_MSG_USER_ENTRYDISCARDED, null, locale));
			fetchUserRoleList(String.valueOf(userDto.getUserId()), userDto, model);
			
			List<BinDTO> binList = userService.getBinDetailListByUserId(userId);
			if (CollectionUtils.isNotEmpty(binList)) {
				StringBuilder bins = new StringBuilder(String.valueOf(binList.get(0).getBinNumber()));

				for (int i = 1; i < binList.size(); i++) {
					bins.append(COMMA_SEPERATOR);
					bins.append(binList.get(i).getBinNumber());

				}
				userDto.setBins(bins.toString());

			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_USER_INFO, ex);
		}
		return getView(model, VIEW_APPROVE_USER_INFO);
	}

	@PostMapping("/checkMaxUserIdForParticipant")
	@PreAuthorize("hasAuthority('Add User')")
	public ResponseEntity<Object> checkMaxUserBasedParticipant(Model model,
			@RequestParam("participantName") String participantName) {

		JsonObject jsonResponse = new JsonObject();
		try {
			participantName = URLDecoder.decode(participantName, UTF_8);

			boolean result = userService.maxUserCreation(participantName);
			if (result) {
				jsonResponse.addProperty(BaseCommonConstants.RESPONSE_STATUS, CommonConstants.TRANSACT_SUCCESS);

			} else {
				jsonResponse.addProperty(BaseCommonConstants.RESPONSE_STATUS, CommonConstants.TRANSACT_FAIL);
			}

		} catch (UnsupportedEncodingException e) {
			handleErrorCodeAndForward(model, ADD_EDIT_USER, e);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/approveBulkUserStatus")
	@PreAuthorize("hasAuthority('Approve User')")
	public String approveBulkUserStatus(@RequestParam("userIdList") String userIdList,
			@RequestParam(BaseCommonConstants.RESPONSE_STATUS) String status, @RequestParam("userType") String userType,
			@RequestParam("remarks") String remarks, Model model, HttpServletRequest request) {

		UserDTO userDTO = new UserDTO();

		try {

			String[] userIdArr = userIdList.split(PIPE);
			if (userIdArr.length == 0) {
				throw new SettleNxtException("UserId's should not be empty", "");
			}
			int[] values = Arrays.stream(userIdArr).mapToInt(Integer::parseInt).toArray();
			List<Integer> list = Arrays.stream(values).boxed().toList();

			userDTO = userService.updateApproveOrRejectBulkUser(list, status, remarks);
			checkBulkUserApproveStatus(userDTO, model, request);
			userDTO.setUserType(userType);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_USERS, ex);
		}

		return loadShowUsers(userType, model);
	}

	private String loadShowUsers(String userType, Model model) {
		try {
			String accessLevel = userService.getUserHierarchyAccessLevel();
			List<String> accessList = new ArrayList<>();
			if (StringUtils.isNotBlank(accessLevel) && accessLevel.contains(COMMA_SEPERATOR)) {
				accessList = Arrays.asList(accessLevel.split(SPLIT_CONST));
			} else {
				accessList.add(accessLevel);
			}
			if (StringUtils.isBlank(userType)) {
				userType = accessLevel;
			}
			model.addAttribute(BaseCommonConstants.SELECT_USER_TYPE,
					userService.getUserHierarchyAccessLevelList(accessList));
			List<UserDTO> pendingUserList = userService.getPendingUsers(userType);
			model.addAttribute(BaseCommonConstants.USER_PENDING_LIST, pendingUserList);
			model.addAttribute(BaseCommonConstants.USER_APP_PENDING, CommonConstants.YES);
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
				model.addAttribute(BaseCommonConstants.SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(BaseCommonConstants.SHOW_CHECKBOX, CommonConstants.YES_FLAG);
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_USERS, ex);
		}
		UserInfoDTO userInfoDto = new UserInfoDTO();
		userInfoDto.setUserType(userType);
		model.addAttribute(BaseCommonConstants.USER_INFO, userInfoDto);
		return getView(model, SHOW_USERS);
	}

	private void checkBulkUserApproveStatus(UserDTO userDTO, Model model, HttpServletRequest request) {
		Locale localeNew = Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(userDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage(AM_MSG_USERAPPROVED, null, localeNew));
		} else if (CommonConstants.TRANSACT_FAIL.equalsIgnoreCase(userDTO.getStatusCode())
				&& (!request.getParameter(BaseCommonConstants.RESPONSE_STATUS).equalsIgnoreCase(RESET))) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage(AM_MSG_USERADDFAIELD, null, localeNew));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage(AM_MSG_USERREJECTED, null, localeNew));
		}
	}

	public void streetAddressValidation(UserInfoDTO user) {
		String[] streeAddresArrs;
		if (BaseCommonConstants.ADD_USER.equalsIgnoreCase(user.getAddEditFlag())
				&& StringUtils.isNotBlank(user.getStreetAddress())) {

			streeAddresArrs = user.getStreetAddress().trim().split(ASTERISK_OP);

		} else {
			streeAddresArrs = TransactValidator.checkForAddress(user.getStreetAddress())
					? user.getStreetAddress().trim().split(ASTERISK_OP)
					: "".split(ASTERISK_OP);
		}

		StringBuilder streetAdd = new StringBuilder();
		for (String streetAddress:streeAddresArrs) {
			streetAdd.append(COMMA_SEPERATOR);
			streetAdd.append(streetAddress);

		}
		streetAdd.replace(0, 1, "");

		user.setStreetAddress(streetAdd.toString());
	}

}
