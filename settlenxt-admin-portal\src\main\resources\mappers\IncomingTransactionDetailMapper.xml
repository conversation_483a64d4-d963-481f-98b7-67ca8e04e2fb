<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="org.npci.settlenxt.adminportal.repository.IncomingTransactionDetailRepo">

	<update id="approveRejectMaker">
		update dispute_txn_settlement_staging set status =#{status}
		<if test="amount != ''">
			, transaction_amount = Cast(#{amount} as NUMERIC)
		</if>
		<if test="message != ''">
			, mem_msg_txt = #{message}
		</if>
		<if test="rejectReason != ''">
			, reason_code = #{rejectReason}
		</if>
		where stage_id=cast(#{stageId} as integer)

	</update>
	<select id="getDisputesDetailsAdmin"
		resultType="org.npci.settlenxt.portal.common.dto.DisputeTxnModel">
		select stage_id as txnId,rgcs_received_date as
		rGCSReceivedDate,mti as
		mti,func_code as orgFuncCode,processing_code as pCode,transaction_amount as
		amountTran,mem_msg_txt as
		memMsgTxt,status as status,
		reason_code as reasonCode,encrypted_pan as
		pan,acq_ref_data as acqRefData,org_transaction_date as
		orgDateCapture,rrn as rrn,pan_prefix as panPrefix,pan_suffix as panSuffix from
		dispute_txn_settlement_staging dtss
		where rgcs_received_date =#{transaction.dateSearch}
		<if test="isSchemeCode">and (acquirer_subnet = #{transaction.schemeCode} or
			issuer_subnet = #{transaction.schemeCode})  </if>
		<if test="isFuncCode">and func_code = #{transaction.funcCodeValue}  </if>
		<if test="isPan">and encrypted_pan = #{transaction.decryptedPan}   </if>
		<if test="isRrn">and rrn = #{transaction.rrn} </if>
		and status in ('PR','RPA');

	</select>
	<update id="approveRejectChecker">
		update dispute_txn_settlement_staging set status =#{status}
		<if test="rejectReason != ''">
			, reason_code = #{rejectReason}
		</if>
		where stage_id=cast(#{stageId} as integer)

	</update>

</mapper>