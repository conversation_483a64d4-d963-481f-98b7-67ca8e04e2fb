$(document).ready(
	function() {
		$("form :input").change(function() {
			$(this).closest('form').data('changed', true);
		});

		$('#roleSubmit').click(

			function() {


				$("#errroleName").text("");
				$("#errroleDesc").text("");
				var check = false;
				
				if (!check) {


					if ($('#addEditRole').data('changed')) {
						
						var roleDesc = $("#roleDesc").val().replace(
							"/^\s*|\s*$/g", '');
						var roleName = $("#roleName").val().replace(
							"/^\s*|\s*$/g", '');
						var mVoleID = 0;
						var data = "roleDesc," + roleDesc
							+ ",roleName," + roleName
							+ ",hiddenRoleName,"
							+ $('#hiddenRoleName').val()
							+ ",roleId," + mVoleID;

						var url = "/addRole";


						postData(url, data);
					} else {
						$('#errMsg').text('No data modified');
						$('#errMsg').show();
						return false;
					}
				} else {
					return false;
				}

			});




	});







window.history.forward();
function noBack() {
	window.history.forward();
}

function maxLengthTextArea(id) {
	var length = 50;
	if ($('#' + id).val().length > length) {
		$('#errroleDesc').text('Maximum ' + length + ' character allowed.');
		return false;
	} else {
		$('#errroleDesc').text('');
		return true;
	}
}

function roleSubmit(_userType) {
$("#errMsg2").hide();

	$("#errroleName").text("");
	$("#errroleDesc").text("");

	var check = false;


	if (!validateRoleName()) {
		check = true;
	}
	if (!validateRoleDesc()) {
		check = true;
	}


	if (!check) {
		if ($('#addEditRole').data('changed')) {

			
			var roleDesc = $("#roleDesc").val().replace(
				"/^\s*|\s*$/g", '');

			var roleName = $("#roleName").val().replace(
				"/^\s*|\s*$/g", '');

			//Functions Logic Starts
			var arr = document.getElementsByClassName("selectedRoles");
			var roles = "";
			for (var i of arr) {
				roles = roles + i.id.replace('remove', '') + "|"
					+ $('#' + i.id).attr('value') + "|";
			}
			roles = roles.substring(0, roles.length - 1);
			if (roles.length == 0) {
			$("#errMsg2").show();
		  $("#errMsg2").find('.error').html('Please select func to assign to  role');
				

				return false;
			}else{
				$("#errMsg2").hide();
		  $("#errMsg2").find('.error').html('');
			}
			//Functions Logic Ends
			var makChkFlag = $("#makChkFlag").val().replace("/^\s*|\s*$/g", '');
			var data = "roleDesc," + roleDesc + ",makChkFlag," + makChkFlag
				+ ",roleName," + roleName + ",functionalityIds," + roles
				+ ",hiddenRoleName,"
				+ $('#hiddenRoleName').val()
				+ ",roleId," +

				+ ",userType," + $('#userType').val();


			var url = "/addRole";

			postData(url, data);
		} else {
			$('#errMsg').text('No data modified');
			$('#errMsg').show();
			return false;
		}
	} else {
		return false;
	}


}
function backAction(_type, action) {

	let url = action;
	var data = "";
	postData(url, data);
}
