<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="org.npci.settlenxt.adminportal.repository.RoleRepository">
	
	<select id="getRoleHierarchyList" resultType="CodeValueDTO">
	SELECT USER_HIERARCHY as DESCRIPTION, HIERARCHY_ID as CODE from user_hierarchy where HIERARCHY_ID!=#{fieldValue} order by HIERARCHY_ID
	</select>
	
	<select id="getBulkRoleStgInfoByRoleId" resultType="RoleDTO">
		SELECT RR.REQUEST_STATE as requestState, RR.ROLE_ID as roleId, RR.ROLE_NAME as roleName, RR.ROLE_TYPE as roleType, RR.ROLE_DESC as roleDesc, RR.STATUS as status, RR.MAKER_CHECKER_FLAG as makChkFlag, RR.CHECKER_COMMENTS as checkerComments, RR.LAST_UPDATED_BY as lastUpdatedBy, RR.LAST_UPDATED_ON as lastUpdatedOn, RR.CREATED_ON as createdOn, RR.CREATED_BY as createdBy, RR.LAST_OPERATION as lastOperation  FROM ROLE_STG RR  WHERE RR.ROLE_ID in <foreach item='item' index='index' collection='list' open='(' separator=',' close=')'> #{item}</foreach>
	</select>


	<select id ="getRoleFuncStgByRoleId" resultType="RoleFunctionalityDTO">
	SELECT RRFM.func_id as funcId, RRFM.ROLE_ID as roleId, RRFM.CREATED_BY as createdBy, RRFM.CREATED_ON as createdOn, RRFM.LAST_UPDATED_BY as lastUpdatedBy, RRFM.LAST_UPDATED_ON as lastUpdatedOn, RRFM.STATUS as status FROM role_func_mapping_stg RRFM  WHERE RRFM.role_id =#{roleId}
	</select>
	
	<select id="getRoleList" resultType="RoleDTO">
	SELECT R.ROLE_ID as roleId, R.ROLE_NAME as roleName, R.ROLE_DESC as roleDesc, R.STATUS as status, R.MAKER_CHECKER_FLAG as makChkFlag FROM ROLE_STG RL inner join role R on RL.role_id=R.role_id WHERE R.ROLE_TYPE = #{userType} and (RL.REQUEST_STATE=#{status}) ORDER BY R.last_updated_on DESC
	</select>
	
	<select id="getModuleList" resultType="MenuDTO">
	SELECT MODULE_ID as moduleId, MODULE_NAME as moduleName FROM module WHERE STATUS=#{status}
	</select>
	
	<select id="getFunctionalityList" resultType="FunctionalityDTO">
	SELECT distinct MFM.func_id as funcId, F.func_name as funcName FROM menu_func_mapping MFM INNER JOIN menu ME ON ME.MENU_ID=MFM.MENU_ID INNER JOIN functionality F ON MFM.FUNC_ID =F.FUNC_ID WHERE ME.MODULE_ID= #{moduleId} AND ( F.MAKER_CHECKER_FLAG=#{status} OR F.MAKER_CHECKER_FLAG=#{makChkFlag}  ) AND F.STATUS = #{statusCode}
	</select>
	
	<insert id="insertRoleStg">
	INSERT INTO role_stg(ROLE_ID,ROLE_NAME,ROLE_DESC,STATUS,ROLE_TYPE,CREATED_BY,CREATED_ON,REQUEST_STATE,MAKER_CHECKER_FLAG, LAST_OPERATION) VALUES( #{roleId}, #{roleName} , #{roleDesc}, #{status}, #{roleType},#{createdBy},#{createdDate}, #{requestState}, #{makChkFlag}, #{lastOperation} )
	</insert>
	
	<select id="retrieveAllFunctionalityList" resultType="FunctionalityDTO">
	SELECT MFM.FUNC_ID AS funcId, F.FUNC_NAME AS funcName FROM menu_func_mapping MFM INNER JOIN menu ME ON ME.MENU_ID=MFM.MENU_ID INNER JOIN module MD ON MD.Module_id=ME.Module_id INNER JOIN functionality F ON MFM.FUNC_ID =F.FUNC_ID WHERE ( F.MAKER_CHECKER_FLAG=#{status} OR F.MAKER_CHECKER_FLAG=#{makChkFlag} )  AND F.STATUS = #{statusCode}
	</select>
	
	<select id="getApprovedRole" resultType="RoleDTO">
	SELECT RR.ROLE_ID as roleId, RR.ROLE_NAME as roleName, RR.ROLE_DESC as roleDesc, RR.STATUS as status, RR.MAKER_CHECKER_FLAG as makChkFlag, RR.ROLE_TYPE as roleType, RR.USER_TYPE as userType, RR.REQUEST_STATE as requestState FROM ROLE_STG RR  WHERE RR.ROLE_ID =#{roleId}
	</select>
	
	<select id="getRolesPendingForApproval" resultType="RoleDTO">
	SELECT R.ROLE_ID as roleId, R.ROLE_NAME as roleName, R.ROLE_TYPE as roleType, R.ROLE_DESC as roleDesc, R.CHECKER_COMMENTS as checkerComments, R.LAST_OPERATION as lastOperation, R.REQUEST_STATE as requestState, R.CREATED_BY as createdBy, R.CREATED_ON as createdOn FROM ROLE_STG R WHERE R.REQUEST_STATE in  <foreach item='item' index='index' collection='requestStateList' open='(' separator=',' close=')'>#{item}</foreach>  and  R.ROLE_TYPE=#{roleType}  ORDER BY last_updated_on DESC
	</select>
	
	<select id="getRoleStgInfoByRoleId" resultType="RoleDTO">
	SELECT RR.REQUEST_STATE as requestState, RR.ROLE_ID as roleId, RR.ROLE_NAME as roleName, RR.ROLE_TYPE as roleType, RR.ROLE_DESC as roleDesc, RR.STATUS as status, RR.MAKER_CHECKER_FLAG  as makChkFlag, RR.CHECKER_COMMENTS as checkerComments, RR.LAST_UPDATED_BY as lastUpdatedBy, RR.LAST_UPDATED_ON as lastUpdatedOn, RR.CREATED_ON as createdOn, RR.CREATED_BY as createdBy, RR.LAST_OPERATION as lastOperation FROM ROLE_STG RR  WHERE RR.ROLE_ID =#{roleId}
	</select>
	
	<update id="updateRoleStgOperation">
	UPDATE ROLE_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} , LAST_OPERATION=#{lastOperation} WHERE ROLE_ID =  #{roleId}
	</update>
	
	<update id="updateRoleStgState">
	UPDATE ROLE_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, last_operation=#{lastOperation}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} WHERE ROLE_ID = #{roleId}
	</update>
	
	<delete id="deleteRoleStgDiscard">
	DELETE FROM  ROLE_STG  WHERE ROLE_ID =  #{roleId}
	</delete> 
	
	<insert id="saveRole">
	INSERT INTO ROLE (ROLE_ID, ROLE_NAME, ROLE_DESC, ROLE_TYPE, MAKER_CHECKER_FLAG, CREATED_BY, CREATED_ON, STATUS) VALUES(#{roleId}, #{roleName}, #{roleDesc}, #{roleType}, #{makChkFlag}, #{createdBy}, #{createdOn},  #{status})
	</insert> 
	
	<select id="getRoleInfoByRoleId" resultType="RoleDTO">
	SELECT ROLE_ID as roleId, ROLE_NAME as roleName, ROLE_DESC as roleDesc, ROLE_TYPE as roleType, MAKER_CHECKER_FLAG as makChkFlag, CREATED_BY as createdBy, CREATED_ON as createdOn, LAST_UPDATED_BY as lastUpdatedBy, LAST_UPDATED_ON as lastUpdatedOn, STATUS as status FROM ROLE WHERE ROLE_ID = #{roleId}
	</select>
	
	<update id="updateRole">
	UPDATE ROLE SET ROLE_NAME=#{roleName}, ROLE_DESC=#{roleDesc}, ROLE_TYPE=#{roleType}, MAKER_CHECKER_FLAG=#{makChkFlag}, STATUS=#{status}, USER_TYPE=#{userType}, LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn} WHERE ROLE_ID = #{roleId}
	</update>
	
	<select id="fetchIdFromRoleIdSequence" resultType="int">
	SELECT nextval('roleid_seq')
	</select>
	
	<select id="fetchRoleFunctionalityStgForEdit" resultType="RoleToFuncDTO">
			SELECT DISTINCT RFM.ROLE_ID as roleId, R.ROLE_NAME as roleName, RFM.FUNC_ID as funcId, FU.FUNC_NAME as funcName, RFM.STATUS as status, R.MAKER_CHECKER_FLAG as makChkFlag FROM role_func_mapping_stg RFM INNER JOIN role_stg R ON (RFM.ROLE_ID = R.ROLE_ID) INNER JOIN functionality FU  ON (FU.FUNC_ID = RFM.FUNC_ID) WHERE RFM.ROLE_ID = #{roleId}	
	</select>
	
	<select id="validateRoleName" resultType="int">
	SELECT COUNT(role_id) FROM ROLE WHERE role_name = #{roleName}
	</select>
	
	<select id="validateroleNameStg" resultType="int">
	SELECT count(DISTINCT role_id) FROM ROLE_STG   WHERE role_name = #{roleName}  and request_state=#{status}
	</select>	
	
	<update id="updateRoleStgDeactivate">
	UPDATE ROLE_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} , LAST_OPERATION=#{lastOperation}, status=#{status} where ROLE_ID =#{roleId}
	</update>
	
	<select id="checkInUserRoleMapping" resultType="int">
	SELECT COUNT(*) from USER_ROLE_MAPPING  where ROLE_ID =#{roleId}
	</select>
	
	<select id="getRoleFuncByRoleId" resultType="RoleFunctionalityDTO">
	SELECT RRFM.func_id as funcId, RRFM.ROLE_ID as roleId, RRFM.CREATED_BY as createdBy, RRFM.CREATED_ON as createdOn, RRFM.LAST_UPDATED_BY as lastUpdatedBy, RRFM.LAST_UPDATED_ON as lastUpdatedOn, RRFM.STATUS as status FROM role_func_mapping RRFM  WHERE RRFM.role_id  = #{roleId}
	</select>
	
	<select id="fetchRoleFunctionality" resultType="RoleToFuncDTO">
	SELECT DISTINCT RFM.ROLE_ID as roleId, R.ROLE_NAME as roleName, RFM.FUNC_ID as funcId, FU.FUNC_NAME as funcName, RFM.STATUS as status, R.MAKER_CHECKER_FLAG as makChkFlag FROM role_func_mapping RFM INNER JOIN role R ON (RFM.ROLE_ID = R.ROLE_ID) INNER JOIN functionality FU  ON (FU.FUNC_ID = RFM.FUNC_ID) WHERE RFM.ROLE_ID = #{roleId}
	</select>
	
</mapper>