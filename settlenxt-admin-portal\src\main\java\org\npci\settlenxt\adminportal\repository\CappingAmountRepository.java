package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.adminportal.dto.CappingAmountDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;

@Mapper
public interface CappingAmountRepository {

	 List<CappingAmountDTO> getCappingAmountFromMainList();

	 List<CappingAmountDTO> getPendingCappingAmountFromStg(
			@Param("requestStateList") List<String> requestStateList);

	 List<CappingAmountDTO> getFunctionCodeList();

	 int insertCappingAmountintoStg(CappingAmountDTO cappingAmountDto);

	 CappingAmountDTO getCappingAmountfromStg(@Param("actionCode") String actionCode,
			@Param("mccGroup") String mccGroup, @Param("binCardBrandId") String binCardBrandId,
			@Param("binCardTypeId") String binCardTypeId, @Param("fieldName") String fieldName,
			@Param("relOperator") String relOperator, @Param("fieldValue") String fieldValue);

	 CappingAmountDTO getCappingAmountMain(@Param("actionCode") String actionCode,
			@Param("mccGroup") String mccGroup, @Param("binCardBrandId") String binCardBrandId,
			@Param("binCardTypeId") String binCardTypeId, @Param("fieldName") String fieldName,
			@Param("relOperator") String relOperator, @Param("fieldValue") String fieldValue);

	 void updateCappingAmount(CappingAmountDTO cappingAmountDto);

	 void saveCappingAmountMain(CappingAmountDTO cappingAmountDto);

	 void updateCappingAmountRequestState(CappingAmountDTO cappingAmountDto);

	 int updateCappingAmountStg(CappingAmountDTO cappingAmountDto);

	 CappingAmountDTO getCappingAmountMainEdit(@Param("cappingId") int cappingId);

	 int updateStgCappingAmount(CappingAmountDTO cappingAmountDtoMain);

	 int deleteDiscardedEntry(@Param("actionCode") String actionCode, @Param("mccGroup") String mccGroup,
			@Param("binCardBrandId") String binCardBrandId, @Param("binCardTypeId") String binCardTypeId,
			@Param("fieldName") String fieldName, @Param("relOperator") String relOperator,
			@Param("fieldValue") String fieldValue);

	 int checkDuplicatePriority(@Param("priority") int priority, @Param("cappingId") int cappingId);

	 int checkDuplicatePriorityStg(@Param("priority") int priority,
			@Param("requestState") List<String> requestState, @Param("cappingId") int cappingId);

	 List<Integer> getCappingId(@Param("priority") int priority, @Param("requestState") List<String> requestState,
			@Param("cappingId") int cappingId);

	 List<CappingAmountDTO> fetchCappingAmountStgAppList(@Param("cappingIdList") List<String> cappingIdList);

	 List<CodeValueDTO> getActionCodeList();

	 List<CodeValueDTO> getMccGroupList();

	 CappingAmountDTO getCappingAmountFromStgEdit(@Param("actionCode") String actionCode,
			@Param("mccGroup") String mccGroup, @Param("binCardBrandId") String binCardBrandId,
			@Param("binCardTypeId") String binCardTypeId, @Param("fieldName") String fieldName,
			@Param("relOperator") String relOperator, @Param("fieldValue") String fieldValue);

	 CappingAmountDTO getCappingAmountfromStgCombined(@Param("capId") String capId);

	 CappingAmountDTO getCappingAmountfromMainCombined(@Param("capId") String capId);

	 int checkDuplicateRecords(String actionCode, String mccGroup, String binCardBrandId, String binCardTypeId,
			String fieldName, String fieldValue, String relOperator);

}