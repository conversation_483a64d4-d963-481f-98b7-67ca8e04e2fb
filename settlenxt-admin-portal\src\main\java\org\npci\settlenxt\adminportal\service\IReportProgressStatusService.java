package org.npci.settlenxt.adminportal.service;

import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;

/**
 * Service used for
 * <li>Fetch Report Progress Status</li>
 * 
 * <AUTHOR>
 *
 */
public interface IReportProgressStatusService {

	/**
	 * Fetch Report Progress Status from RO
	 * @param cycleManagementDTO
	 * @return
	 */
	CycleManagementDTO getReportProgressStatusList(CycleManagementDTO cycleManagementDTO);
	
	String fetchProductIdDetails(CycleManagementDTO cycleManagementDTO);

}
