package org.npci.settlenxt.adminportal.controllers;

import java.util.List;
import java.util.Locale;

import javax.servlet.http.HttpServletRequest;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.dto.TipSurchargeDTO;
import org.npci.settlenxt.adminportal.service.CodeValueService;
import org.npci.settlenxt.adminportal.service.TipSurchargeService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

//@Slf4j
@Controller
public class TipSurchargeController extends BaseController {
	private static final String SHOW_TIP_SURCHARGE = "showTipSurcharge";
	private static final String VIEW_TIP_SURCHARGE = "viewTipSurcharge";
	private static final String VIEW_APPROVE_TIP_SURCHARGE = "viewApproveTipSurcharge";
	private static final String ADD_EDIT_TIP_SURCHARGE = "addEditTipSurcharge";
	private static final String SHOW_CHECK_BOX = "showCheckBox";
	private static final String STATUS = "status";
	public static final String CARD_TYPE="CARD_TYPE";
	public static final String SCHEME_CODE="SCHEME_CODE";
	public static final String AMT_PER_LIST="AmountPercentList";
	public static final String OPR_LIST="OperatorList";
	public static final String TYPE_LIST="TypeList";
	public static final String BIN_CARD_TYPE_LIST="binCardTypeList";
	public static final String BIN_CARD_BRAND_LIST="binCardBrandList";
	public static final String AMT_PER_LIST_ATT="amountPercentList";
	public static final String OPR_LIST_ATT="operatorList";
	public static final String TYPE_LIST_ATT="typeList"; 
	
	// @SuppressWarnings({ "unchecked", "unused" })
	@Autowired
	private MessageSource messageSource;
	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	private TipSurchargeService tipSurchargeService;
	@Autowired
	private CodeValueService codeValueService;

	// show Main tab of TipSurcharge
	@PostMapping("/tipSurchargeMain")
	@PreAuthorize("hasAuthority('View Tip Surcharge')")
	public String fetchApprovedTipSurchargeList(Model model) {
		try {
			List<TipSurchargeDTO> tipSurchargeList = tipSurchargeService.getTipSurchargeList();
			model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.TRANSACT_YES);//
			model.addAttribute(CommonConstants.TIP_SURCHARGE_LIST, tipSurchargeList);
			model.addAttribute(CommonConstants.TIP_SURCHARGE, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.ADD_TIP_SURCHARGE, CommonConstants.TRANSACT_YES);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, SHOW_TIP_SURCHARGE, ex);
		}
		return getView(model, SHOW_TIP_SURCHARGE);
	}

	// show Approval TAB of TipSurcharge
	@PostMapping("/tipSurchargeForApproval")
	@PreAuthorize("hasAuthority('View Tip Surcharge')")
	public String tipSurchargeForApproval(Model model) {
		try {
			List<TipSurchargeDTO> pendingTipSurchargeList = tipSurchargeService.getPendingTipSurcharge();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.TIP_SURCHARGE_PENDING_LIST, pendingTipSurchargeList);
			model.addAttribute(CommonConstants.TIP_SURCHARGE_APP_PENDING, CommonConstants.TRANSACT_YES);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_TIP_SURCHARGE, ex);
		}
		return getView(model, SHOW_TIP_SURCHARGE);
	}

	// view TipSurcharge in Main tab
	@PostMapping("/getTipSurcharge")
	@PreAuthorize("hasAuthority('View Tip Surcharge')")
	public String getTipSurcharge(@RequestParam("tipSurchargeid") String tipSurchargeId, Model model) {
		TipSurchargeDTO tipSurchargeDto = new TipSurchargeDTO();
		try {
			tipSurchargeDto = tipSurchargeService.getTipSurchargeMainInfo(Integer.valueOf(tipSurchargeId));
			model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, tipSurchargeDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_TIP_SURCHARGE, ex);
		}
		return getView(model, VIEW_TIP_SURCHARGE);
	}

	// view TipSurcharge in Approval tab
	@PostMapping("/getPendingTipSurcharge")
	@PreAuthorize("hasAuthority('View Tip Surcharge')")
	public String getPendingTipSurcharge(@RequestParam("tipSurchargeid") String tipSurchargeId, Model model,
			HttpServletRequest request) {
		TipSurchargeDTO tipSurchargeDto = new TipSurchargeDTO();
		try {
			tipSurchargeDto = tipSurchargeService.getTipSurchargeStgInfo(tipSurchargeId);
			model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, tipSurchargeDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_TIP_SURCHARGE, ex);
		}
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.DISCARD_TIP_SURCHARGE);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_TIP_SURCHARGE);
		return getView(model, VIEW_APPROVE_TIP_SURCHARGE);
	}

	// add TipSurcharge
	@PostMapping("/tipSurchargeCreation")
	@PreAuthorize("hasAuthority('Add Tip Surcharge')")
	public String tipSurchargeCreation(Model model) {
		TipSurchargeDTO tipSurchargeDto = new TipSurchargeDTO();
		fetchDropdownValues(model);
		model.addAttribute(CommonConstants.ADD_TIP_SURCHARGE, CommonConstants.ADD_TIP_SURCHARGE);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_TIP_SURCHARGE);
		model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, tipSurchargeDto);
		return getView(model, ADD_EDIT_TIP_SURCHARGE);
	}

	// To save given input
	@PostMapping("/addTipSurcharge")
	@PreAuthorize("hasAuthority('Add Tip Surcharge')")
	public String addTipSurcharge(@ModelAttribute(CommonConstants.TIP_SURCHARGE_DTO) TipSurchargeDTO tipSurchargeDto,
			Model model) {
		tipSurchargeDto.setAddEditFlag(CommonConstants.ADD_TIP_SURCHARGE);
		try {
			fetchDropdownValues(model);
			tipSurchargeDto = tipSurchargeService.addEditTipSurcharge(tipSurchargeDto);
			model.addAttribute(CommonConstants.ADD_TIP_SURCHARGE, CommonConstants.ADD_TIP_SURCHARGE);
		} catch (Exception ex) {
			model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, tipSurchargeDto);
			model.addAttribute(CommonConstants.ADD_TIP_SURCHARGE, CommonConstants.ADD_TIP_SURCHARGE);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_TIP_SURCHARGE);
			return handleErrorCodeAndForward(model, ADD_EDIT_TIP_SURCHARGE, ex);
		}
		model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, tipSurchargeDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("tipSurcharge.addSuccess.msg"));
		return getView(model, ADD_EDIT_TIP_SURCHARGE);
	}

	// To Edit TipSurcharge
	@PostMapping("/editTipSurcharge")
	@PreAuthorize("hasAuthority('Edit Tip Surcharge')")
	public String editTipSurcharge(@RequestParam("tipSurchargeId") String tipSurchargeId, Model model,
			@RequestParam("parentPage") String parentPage) {
		TipSurchargeDTO tipSurchargeDto = new TipSurchargeDTO();

		try {
			fetchDropdownValues(model);
			tipSurchargeDto = tipSurchargeService.getTipSurchargeForEdit(Integer.valueOf(tipSurchargeId));
			tipSurchargeDto.setAddEditFlag(CommonConstants.EDIT_TIP_SURCHARGE);
			model.addAttribute(CommonConstants.EDIT_TIP_SURCHARGE, CommonConstants.EDIT_TIP_SURCHARGE);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_TIP_SURCHARGE);
			model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, tipSurchargeDto);
			model.addAttribute(CommonConstants.PARENT_PAGE, parentPage);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_TIP_SURCHARGE, ex);
		}

		model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, tipSurchargeDto);
		return getView(model, ADD_EDIT_TIP_SURCHARGE);
	}

	// To save edited input
	@PostMapping("/updateTipSurcharge")
	@PreAuthorize("hasAuthority('Edit Tip Surcharge')")
	public String updateTipSurcharge(@ModelAttribute("tipSurchargeDto") TipSurchargeDTO tipSurchargeDto,
			BindingResult result, Model model, HttpServletRequest request, Locale locale,
			@RequestParam("parentPage") String parentPage) {
		TipSurchargeDTO cardDtolocal;
		try {
			fetchDropdownValues(model);
			tipSurchargeDto.setAddEditFlag(CommonConstants.EDIT_TIP_SURCHARGE);
			tipSurchargeService.addEditTipSurcharge(tipSurchargeDto);
			cardDtolocal = tipSurchargeService.getTipSurchargeStg(tipSurchargeDto.getTipSurchargeId());

			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("tipSurcharge.updateSuccess.msg", null, locale));
		} catch (Exception ex) {
			cardDtolocal = tipSurchargeDto;
			handleErrorCodeAndForward(model, ADD_EDIT_TIP_SURCHARGE, ex);
		}
		model.addAttribute(CommonConstants.EDIT_TIP_SURCHARGE, CommonConstants.EDIT_TIP_SURCHARGE);
		model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, cardDtolocal);
		model.addAttribute(CommonConstants.PARENT_PAGE, parentPage);

		return getView(model, ADD_EDIT_TIP_SURCHARGE);
	}

	// For Checker Approval
	@PostMapping("/approveTipSurcharge")
	@PreAuthorize("hasAuthority('Approve Tip Surcharge')")
	public String approveTipSurcharge(@RequestParam("tipSurchargeId") String tipSurchargeId,
			@RequestParam("status") String status, @RequestParam("remarks") String remarks, Model model) {
		TipSurchargeDTO tipSurchargeDto = new TipSurchargeDTO();
		try {
			tipSurchargeDto = tipSurchargeService.approveOrRejectTipSurcharge(Integer.valueOf(tipSurchargeId), status,
					remarks);
			model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, tipSurchargeDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_TIP_SURCHARGE, ex);
		}
		model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, tipSurchargeDto);
		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {

			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("tipSurcharge.approvalSuccess.msg"));
		} else if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("tipSurcharge.rejectionSuccess.msg"));
		}

		return getView(model, VIEW_APPROVE_TIP_SURCHARGE);
	}

	// For Checker Approval
	@PostMapping("/approveTipSurchargeBulk")
	@PreAuthorize("hasAuthority('Approve Tip Surcharge')")
	public String approveTipSurchargeBulk(
			@RequestParam("bulkApprovalReferenceNoList") String bulkApprovalReferenceNoList,
			@RequestParam("status") String status, Model model) {
		String successStatus = "";
		try {
			String remarks = "";
			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}
			successStatus = tipSurchargeService.approveOrRejectTipSurchargeBulk(bulkApprovalReferenceNoList, status,
					remarks);
			List<TipSurchargeDTO> pendingTipSurchargeList = tipSurchargeService.getPendingTipSurcharge();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.TIP_SURCHARGE_PENDING_LIST, pendingTipSurchargeList);
			model.addAttribute(CommonConstants.TIP_SURCHARGE_APP_PENDING, CommonConstants.TRANSACT_YES);
			if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_APPROVED)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("tipSurcharge.approvalSuccess.msg"));
			} else if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_REJECTED)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("tipSurcharge.rejectionSuccess.msg"));
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_TIP_SURCHARGE, ex);
		}
		return getView(model, SHOW_TIP_SURCHARGE);
	}

	// For Discard
	@PostMapping("/discardTipSurcharge")
	@PreAuthorize("hasAuthority('Edit Tip Surcharge')")
	public String discardTipSurcharge(@RequestParam("tipSurchargeId") String tipSurchargeId, Model model) {
		TipSurchargeDTO tipSurchargeDto = new TipSurchargeDTO();
		try {
			tipSurchargeDto = tipSurchargeService.discardTipSurcharge(Integer.valueOf(tipSurchargeId));
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_TIP_SURCHARGE, ex);
		}
		model.addAttribute(CommonConstants.TIP_SURCHARGE_DTO, tipSurchargeDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("tipSurcharge.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_TIP_SURCHARGE);
	}

	@PostMapping("/validationCheckBinFeatureMapping")
	@PreAuthorize("hasAuthority('Add Tip Surcharge')")
	public ResponseEntity<Object> checkDuplicateData(@ModelAttribute TipSurchargeDTO tipSurchargeDto) {

		List<TipSurchargeDTO> tipSurchargeDTOList = tipSurchargeService
				.checkDuplicateDataForTipSurcharge(tipSurchargeDto);
		JsonObject jsonResponse = new JsonObject();
		if (tipSurchargeDTOList.isEmpty()) {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_SUCCESS);

		} else {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_FAIL);
		}
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	// value from LookUp table
	private void fetchDropdownValues(Model model) {
		List<CodeValueDTO> typeList = codeValueService.getCodeValueData(TYPE_LIST);
		List<CodeValueDTO> operatorList = codeValueService.getCodeValueData(OPR_LIST);
		List<CodeValueDTO> amountPercentList = codeValueService.getCodeValueData(AMT_PER_LIST);
		List<CodeValueDTO> binCardBrandList = codeValueService.getCodeValueData(SCHEME_CODE);
		List<CodeValueDTO> binCardTypeList = codeValueService.getCodeValueData(CARD_TYPE);
		model.addAttribute(TYPE_LIST_ATT, typeList);
		model.addAttribute(OPR_LIST_ATT, operatorList);
		model.addAttribute(AMT_PER_LIST_ATT, amountPercentList);
		model.addAttribute(BIN_CARD_BRAND_LIST, binCardBrandList);
		model.addAttribute(BIN_CARD_TYPE_LIST, binCardTypeList);
	}

}
