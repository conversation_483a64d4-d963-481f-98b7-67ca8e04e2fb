<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript">
	var actionColumnIndex = 7;
	var firstColumnToBeSkippedInFilterAndSort = false;
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 11;
	firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 10;
	firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
</script>

<script>
	var referenceNoListPendings = [];
	<c:if test="${not empty pendingFeatureFeeList}">
	<c:forEach items="${pendingFeatureFeeList}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	referenceNoListPendings.push('${operator.cardConfigId}');
	</c:if>
	</c:forEach>
	</c:if>
</script>

<script src="./static/js/validation/mcpr/showFeatureFee.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js"></script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js"></script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>
<!-- Model -->
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalNews" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Feature Fee
					Approval/Rejection</label>
				<p id="newsIds" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulkFeatureFee('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulkFeatureFee('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Model -->
<!-- Model -->
<input:hidden id="refNum" />
<div class="row">
	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/featureFeeConfiguration');"
			role="tab" data-toggle="tab"><span
			class="glyphicon glyphicon-credit-card"> </span> <spring:message
				code="featureFee.mainTab.title" /></a>

		<c:choose>
			<c:when test="${pendingAppFeatureFee eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" role="tab" onclick="getPendingFeatureFeeList();"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> </span> <spring:message
				code="featureFee.approvalTab.title" /></a>

	</ul>

	<div class="tab-content">
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
			<div class="col-sm-12">
			<sec:authorize access="hasAuthority('Add Feature Fee')">
									<c:if test="${addFeatureFee eq 'Yes'}">
										<a class="btn btn-success pull-right btn_align" href="#"
											onclick="submitForm('/featureFeeCreation','P');"
											style="margin-top: -5px 0px 2px 0px;"><em class="glyphicon-plus"></em>
											<spring:message code="featureFee.addFeatureFeeBtn" /></a>
									</c:if>
								</sec:authorize>
								</div>
				<div class="col-sm-12">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="ifsc.clearFiltersBtn" />
					</button>
					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="csvExport"> <spring:message code="ifsc.csvBtn" />
					</a> <a class="btn btn-success pull-right btn_align" href="#"
						id="excelExport"><spring:message code="featureFee.exportBtn" /></a>

				</div>
			</div>
			<c:if test="${showMainTab eq 'Yes'}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="featureFee.listscreen.title" /></span></strong>
								
							</div>
							<div class="panel-body">


								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Feature Fee</caption>
										<thead>
											<tr>
												<th scope="col"><label><spring:message
															code="featureFee.cardType" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.cardVariant" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.feature" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.details" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.featureFee" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.fromDate" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.toDate" /></label></th>
											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty featureFeeList}">
												<c:forEach var="feature" items="${featureFeeList}">

													<tr>
														<td
															onclick="javascript:viewFeatureFee('${feature.cardConfigId}','V')">${feature.cardTypeName}</td>
														<td
															onclick="javascript:viewFeatureFee('${feature.cardConfigId}','V')">${feature.cardVariantName}</td>
														<td
															onclick="javascript:viewFeatureFee('${feature.cardConfigId}','V')">${feature.feature}</td>
														<td
															onclick="javascript:viewFeatureFee('${feature.cardConfigId}','V')">${feature.details}</td>
														<td
															onclick="javascript:viewFeatureFee('${feature.cardConfigId}','V')">${feature.featureFee}</td>
														<td
															onclick="javascript:viewFeatureFee('${feature.cardConfigId}','V')">${feature.fromDate}</td>
														<td
															onclick="javascript:viewFeatureFee('${feature.cardConfigId}','V')">${feature.toDate}</td>
													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>
			<c:if test="${showApprovalTab eq 'Yes'}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="featureFee.listscreen.title" /></span></span></strong>
								<c:if test="${not empty pendingFeatureFeeList}">
									<sec:authorize access="hasAuthority('Approve Feature Fee')">
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkFeatureFee('A','No')"
											id="submitButtonA"
											value="<spring:message code="feeRate.Approve" />" />
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkFeatureFee('R','No')"
											id="submitButtonR"
											value="<spring:message code="feeRate.Reject" />" />
									</sec:authorize>
								</c:if>
							</div>
							<div class="panel-body">
							<%-- 	<div class="row">
									<div class="col-sm-12">
										<button class="btn  pull-right btn_align" id="clearFilters">
											<spring:message code="ifsc.clearFiltersBtn" />
										</button>
										&nbsp; <a class="btn btn-success pull-right btn_align"
											href="#" id="csvExport"> <spring:message
												code="ifsc.csvBtn" />
										</a> <a class="btn btn-success pull-right btn_align" href="#"
											id="excelExport"> <spring:message
												code="featureFee.exportBtn" /></a>
									</div>
								</div> --%>
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Feature Fee</caption>
										<thead>
											<tr>
												<sec:authorize access="hasAuthority('Approve Feature Fee')">
													<th scope = "col"><input type=checkbox name='selectAllCheck'
														id="selectAll" data-target="toggleModalNews" value="All"></input></th>
												</sec:authorize>
												<th scope="col"><label><spring:message
															code="featureFee.cardType" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.cardVariant" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.feature" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.details" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.featureFee" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.fromDate" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.toDate" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.requestType" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.status" /></label></th>
												<th scope="col"><label><spring:message
															code="featureFee.checkerComents" /></label></th>
											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty pendingFeatureFeeList}">
												<c:forEach var="features" items="${pendingFeatureFeeList}">
													<tr>
														<sec:authorize
															access="hasAuthority('Approve Feature Fee')">
															<c:if test="${features.requestState =='P' }">
																<td><input type=checkbox name='type'
																	id="selectSingle" onclick="mySelect();"
																	value="${features.cardConfigId}"></input></td>
															</c:if>
															<c:if test="${features.requestState !='P' }">
																<td></td>
															</c:if>
														</sec:authorize>
														<td
															onclick="javascript:viewFeatureFee('${features.cardConfigId}','P')">${features.cardTypeName}</td>
														<td
															onclick="javascript:viewFeatureFee('${features.cardConfigId}','P')">${features.cardVariantName}</td>
														<td
															onclick="javascript:viewFeatureFee('${features.cardConfigId}','P')">${features.feature}</td>
														<td
															onclick="javascript:viewFeatureFee('${features.cardConfigId}','P')">${features.details}</td>
														<td
															onclick="javascript:viewFeatureFee('${features.cardConfigId}','P')">${features.featureFee}</td>
														<td
															onclick="javascript:viewFeatureFee('${features.cardConfigId}','P')">${features.fromDate}</td>
														<td
															onclick="javascript:viewFeatureFee('${features.cardConfigId}','P')">${features.toDate}</td>
														<td
															onclick="javascript:viewFeatureFee('${features.cardConfigId}','P')">${features.lastOperation}</td>
														<td
															onclick="javascript:viewFeatureFee('${features.cardConfigId}','P')"><c:if
																test="${features.requestState =='A' }">
																<spring:message
																	code="featureFee.requestState.approved.description" />
															</c:if> <c:if test="${features.requestState =='P' }">
																<spring:message
																	code="featureFee.requestState.pendingApproval.description" />
															</c:if> <c:if test="${features.requestState =='R' }">
																<spring:message
																	code="featureFee.requestState.rejected.description" />
															</c:if> <c:if test="${features.requestState =='D' }">
																<spring:message
																	code="featureFee.requestState.discared.description" />
															</c:if></td>
														<td
															onclick="javascript:viewFeatureFee('${features.cardConfigId}','P')">${features.checkerComments}</td>
													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>

							</div>
						</div>
					</div>
				</div>
			</c:if>
		</div>
	</div>
</div>
