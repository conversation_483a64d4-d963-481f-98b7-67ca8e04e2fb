package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.dto.LookUpDTO;
import org.npci.settlenxt.portal.common.service.BaseLookupService;

public interface LookupService extends BaseLookupService {

	List<LookUpDTO> getLookupList();

	LookUpDTO addEditLookUp(LookUpDTO lookUpDTO);

	List<LookUpDTO> getPendingLookupListStg();

	LookUpDTO getLookUpInfoFromMain(int lookupId);

	LookUpDTO getEditLookUpInfoFromMain(int lookupId);

	int updateLookUp(LookUpDTO lookUpDTO);

	LookUpDTO getLookUpInfoFromStg(int lookupId);

	LookUpDTO approveOrRejectLookUp(int lookupId, String status, String remarks);

	LookUpDTO getLookUpFrmStg(int lookupId);

	LookUpDTO discardLookUp(int lookupId);

	LookUpDTO updateApproveOrRejectBulkLookUp(String bulkLookUpList, String status, String remarks);

	List<LookUpDTO> getLookupListType();

	boolean checkDupLookUpDetails(String lkpType, String lkpVal, String lkpDesc, String flow);

}
