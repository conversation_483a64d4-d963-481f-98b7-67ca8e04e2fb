<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<title></title>
<%-- <title><spring:message code="am.lbl.title" /></title>
<meta http-equiv="Cache-control" content="no-cache" />
<meta http-equiv="Cache-control" content="no-store" />
<meta http-equiv="Expires" content="0" />
<meta http-equiv="pragma" content="no-cache" />
<%@include file="Header.jsp"%>--%>
<script type="text/javascript"
	src="./static/js/validation/viewTransactionDetail.js"></script>
<script type="text/javascript"
		src="./static/js/xlsx.full.min.js"></script>
<script >
	var docNameList = [];
	<c:if test="${not empty docUploadedList}">
		<c:forEach items="${docUploadedList}" var="docInfo">
			docNameList.push("${docInfo.documentName}");
		</c:forEach>
	</c:if>
	var AlltableToExcel = (function() {
		var uri = 'data:application/vnd.ms-excel;base64,', template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>', base64 = function(
				s) {
			return window.btoa(unescape(encodeURIComponent(s)))
		}, format = function(s, c) {
			return s.replace(/{(\w+)}/g, function(m, p) {
				return c[p];
			})
		}
		return function(table, name) {
			if (!table.nodeType) {
				table = document.getElementsByName(table);
			}
			var tableInnerHtml = "<tbody><tr><td><label><strong>Transaction Summary</strong></label></td></tr><tr></tr></tbody>";
			tableInnerHtml += table[0].innerHTML;
			for (var i = 1; i < table.length; i++) {
				//get tbody for once
				var label = table[i].closest(".panel").querySelector(
						".panel-title a");
				var tempElement = document.createElement("div");
				tempElement.innerHTML = label.innerHTML;
				tableInnerHtml += "<tbody><tr></tr><tr><td><label><strong>"
				tableInnerHtml += tempElement.textContent.trim();
				tableInnerHtml += "</strong></label></td></tr><tr></tr></tbody>"
				console.log(tempElement.textContent.trim())
				tableInnerHtml += table[i].tBodies[0].innerHTML;

			}
			var ctx = {
				worksheet : name || 'Worksheet',
				table : tableInnerHtml
			}
			var a = document.createElement('a');
			a.href = uri + base64(format(template, ctx))
			a.download = name + '.xls';
			//triggering the function
			a.click();
		}
	})()
</script>
<style>
.align_right{
	float: right;
    width: 250px;
    text-align: right;
}
.panel .panel-title a{
	display: block;
}
.overlay {
	display: none;
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 999;
	background: rgba(255, 255, 255, 0.8) url("./static/images/spinner.gif")
		center no-repeat;
}

.loadingdata {
	overflow: hidden;
}

.loadingdata .overlay {
	display: block;
}
</style>
<div id="body-content-msg">
	<div class="space_block">
		<div class="alert alert-success" role="alert"
			id="transitionSuccessMsg" style="display: none;">
			<spring:message code="txn.detail.success.msg" />
		</div>
		
		<div class="alert alert-danger" role="alert" id="transitionErrorMsg" style="display: none;">
			<spring:message code="E00001" />
		</div>
		<div class="alert alert-danger" role="alert" id="documentErrorMsg" style="display: none;">
			<spring:message code="document.download.error" />
		</div>
		<div class="alert alert-danger" role="alert" id="actionErrorMsg" style="display: none;">
			<spring:message code="txn.detail.action.error" />
		</div>
		<div class="alert alert-danger appRejMust" role="alert" style="display: none;">Please Select Approve/Reject action.</div>
		<div class="alert alert-danger remarkMust" role="alert" style="display: none;">Please Enter Remarks.</div>
	</div>
</div>
</head>
<input type="hidden" id="txnId" name="txnId" value="${transactionDetailSummary.txnId}"></input>
<input type="hidden" id="orgTxnId" name="orgTxnId" value="${transactionDetailSummary.orgTxnId}"></input>
<input type="hidden" id="rrnval" name="rrnval" value="${transactionDetailSummary.rrn}"></input>
<input type="hidden" id="orgTabName" name="orgTabName" value="${transactionDetailSummary.originalTableName}"></input>
<input type="hidden" id="orgMti" name="orgMti" value="${transactionDetailSummary.mti}"></input>
<input type="hidden" id="orgFuncCode" name="orgFuncCode" value="${transactionDetailSummary.funcCode}"></input>
<input type="hidden" id="codeList" name="codeList" value="${codeList}"></input>
<input type="hidden" id="availableReasonSubType" name="availableReasonSubType" value='${fn:escapeXml(availableReasonSubType)}'></input>
<input type="hidden" id="availableReasonCodes" name="availableReasonCodes" value='${fn:escapeXml(availableReasonCodes)}'></input>
<input type="hidden" id="reasonCodeURL" name="reasonCodeURL" value="${reasonCodeURL}"></input>
<input type="hidden" id="mappedDupData" name="mappedDupData" value="${transactionDetailSummary.mappedDupData}"></input>
<input type="hidden" id="acqRefData" name="acqRefData" value="${prsntArd}"></input>
<input type="hidden" id="caseNo" name="caseNo" value="${transactionDetailSummary.caseNo}"></input>
<input type="hidden" id="searchedFromDate" name="searchedFromDate" value="${transactionSearchInfo.fromDate}"></input>
<input type="hidden" id="searchedToDate" name="searchedToDate" value="${transactionSearchInfo.toDate}"></input>
<input type="hidden" id="searchedRrn" name="searchedRrn" value="${transactionSearchInfo.rrn}"></input>
<input type="hidden" id="searchedTokenPan" name="searchedTokenPan" value="${transactionSearchInfo.tokenPan}"></input>
<input type="hidden" id="searchedActionCode" name="searchedActionCode" value="${transactionSearchInfo.actionCode}"></input>
<input type="hidden" id="searchedPartId" name="searchedPartId" value="${transactionSearchInfo.partId}"></input>
<input type="hidden" id="searchedCrn" name="searchedCrn" value="${transactionSearchInfo.complaintNumber}"></input>
<input type="hidden" id="txnStatus" name="txnStatus" value="${transactionSearchInfo.txnStatus}"></input>
<input type="hidden" id="authAmount" name="authAmount" value="${transactionDetailSummary.amtTran}"></input>
<input type="hidden" id="authAmtAdd" name="authAmtAdd" value="${transactionDetailSummary.amtAdd}"></input>
<input type="hidden" id="cardAcceptorZipCode" name="cardAcceptorZipCode" value="${transactionDetailSummary.cardAcptPstCode}"></input>
<input type="hidden" id="merchantTelephoneNumber" name="merchantTelephoneNumber" value="${transactionDetailSummary.merchTelNo}"></input>
<input type="hidden" id="cardAcceptorAdditionalAddress" name="cardAcceptorAdditionalAddress" value="${transactionDetailSummary.cardAcptAdnlAddress}"></input>
<input type="hidden" id="jsonData" name="jsonData" value='${dynamicData}'></input>
<input type="hidden" id="amtApproval" name="amtApproval" value="${amtApproval}"></input>
<input type="hidden" id="amtAddApproval" name="amtAddApproval" value="${amtAddApproval}"></input>
<input type="hidden" id="internalTrackingNumber" name="internalTrackingNumber" value="${internalTrackingNumber}"></input>
<input type="hidden" id="processingCode" name="processingCode" value="${transactionDetailSummary.pcode}"></input>
<input type="hidden" id="tipAmount" name="tipAmount" value="${tipAmount}"></input>
<input type="hidden" id="totalRefundedAmt" name="totalRefundedAmt" value="${totalRefundedAmt}"></input>
<input type="hidden" id="orgAmtPresentment" name="orgAmtPresentment" value="${orgAmtPresentment}"></input>
<input type="hidden" id="latestTxnId" name="latestTxnId" value="${latestTxnId}"></input>
<input type="hidden" id="tstampLocal" name="tstampLocal" value="${tstampLocal}"></input>
<input type="hidden" id="docFilePath" name="docFilePath" value='${docFilePath}'></input>
<input type="hidden" id="fileExtensionList" name="fileExtensionList" value='${allowedFileExtensionList}'></input>
<input type="hidden" id="schemeCode" name="schemeCode" value="${transactionSearchInfo.schemeCode}"></input>
<input type="hidden" id="searchedFuncCodeDescription" name="searchedCrn" value="${transactionSearchInfo.funcCodeDesc}"></input>
<input type="hidden" id="searchedMessageDirection" name="searchedCrn" value="${transactionSearchInfo.messageDirection}"></input>
<input type="hidden" id="searchedSchemeCodeBank" name="searchedSchemeCodeBank" value="${transactionSearchInfo.schemeCodeBank}"></input>
<!-- Modal -->
  <c:import url="modalDisputeDynamic.jsp" /> 
   <c:import url="fileUploadModule.jsp" />
   <c:import url="customerComplaint.jsp" />  
<!-- Modal Close -->

<div id="memberBody" class="container-fluid height-min">
	<div class="overlay"></div>
<div class="body-content">
	<div class="bs-example">
		<div class="row">
			<div class="alert alert-danger jqueryError" role="alert"
				style="display: none" id="jqueryError"></div>
			<div id="errLvType" class="alert alert-danger" role="alert"
				style="display: none"></div>
		</div>
		<br />
		<div class="row">
			<button class="btn btn-success"
				onclick="AlltableToExcel('tabNew','TransactionSummaryData')">Export</button>
			
			<div class="align_right">
			<sec:authorize access="hasAuthority('Raise Transaction Dispute')">
				<c:if test="${showUploadDoc == 'Y'}">
					<button type="button" class="btn btn-primary"
						onclick="openUploadFile()">
						<spring:message code="fileUpload.uploadFile" />
					</button>

				</c:if>
			</sec:authorize>
			<c:set var="authenticated" value="${true}" />
			<sec:authorize access="hasAuthority('Approve Transaction Dispute')">
						<c:choose>
							<c:when test="${not empty transactionSearchInfo.txnStatus}">
								<c:set var="authenticated" value="${false}" />
								<button type="button" class="btn btn-danger"
									onclick="backAction('P','/approveTransaction');">Back</button>
							</c:when>
						</c:choose>
			</sec:authorize>
			<c:if test="${authenticated}">
			<c:if test = "${!transactionSearchInfo.backToIncomingTxnScn }">
					<button type="button" class="btn btn-danger"
						onclick="backViewTxnAction('N','/viewTransactionSearch');">Back</button>
			</c:if>
			<c:if test = "${transactionSearchInfo.backToIncomingTxnScn }">
					<button type="button" class="btn btn-danger"
						onclick="backViewIncomingTxnAction('N','/incomingTransactionDetails');">Back</button>
			</c:if>
			</c:if>
			<button type="button" class="btn btn-danger refreshButton"
				onclick="refreshScreen();">Refresh</button>
			</div>
		</div>
			<div class="panel panel-default no_margin">
			<div class="panel-heading clearfix">
				<strong><span class="glyphicon glyphicon-th"></span> <span
					data-i18n="Data">Transaction Summary</span></strong>
				<sec:authorize access="hasAuthority('Raise Transaction Dispute')">
				<div class="icon_bar">
					<c:choose>
						<c:when test="${empty availableOptions}">
							<p>No Actions Available</p>
						</c:when>
						<c:otherwise>
							<select class="form-control-select" id="actions" onchange="openDynamicModal()" style="width:100%;">	
									<option lable="SELECT" value="SELECT">Select</option>
									<c:forEach var="txnList" items="${availableOptions}">
										<option value="${txnList.key}">${txnList.value}</option>
									</c:forEach>
						</select>	
						</c:otherwise>
					</c:choose>	
				</div>
				</sec:authorize>
				<div class="icon_bar">
					<c:if test="${not empty custCompInfo}">
								<button type="button" class="btn btn-primary" id="custCompInfo"
									onclick="openCustCompInfo()">
									<spring:message code="txn.detail.lbl.viewCustCompBtn" />
								</button>
						</c:if>
				</div>
			</div>
			<div class="panel-body">
				<div class="row">
					<div class="col-md-12">
						<table name="tabNew" class="table table-striped" style="font-size: 12px">
						<caption style="display:none;">Transaction Detail</caption> 
						
						<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td><label><spring:message
												code="txn.detail.lbl.panTokenPanNo" /></label></td>
									<td id="pan"><c:if
											test="${empty transactionDetailSummary.maskPan}">N/A</c:if>${transactionDetailSummary.maskPan}</td>
									<td><label><spring:message
												code="txn.detail.lbl.dateAndTimeLocalTransaction" /></label></td>
									<td class="tStampLocal" id="tstampLocal"><c:if
											test="${empty transactionDetailSummary.tstampLocal}">N/A</c:if>${transactionDetailSummary.tstampLocal}</td>
								</tr>
								<tr>
									<td><label><spring:message
												code="txn.detail.lbl.acquirerReferenceData" /> / <spring:message
												code="txn.detail.lbl.rrn" /></label></td>
									<td id="rrn">
										<c:choose>
											<c:when test="${not empty transactionDetailSummary.acqRefData}">${transactionDetailSummary.acqRefData}</c:when>
											<c:otherwise>N/A</c:otherwise>
										</c:choose>
										<strong>/</strong>
										<c:choose>
											<c:when test="${not empty transactionDetailSummary.rrn}">${transactionDetailSummary.rrn }</c:when>
											<c:otherwise>N/A</c:otherwise>
										</c:choose>
									</td>
									<td><label><spring:message
												code="txn.detail.lbl.transactionAmount" /></label></td>
										<td id="amtTran">
											<c:choose>
												<c:when test="${not empty transactionDetailSummary.amtTran && not empty transactionDetailSummary.tranCurDesc }">${transactionDetailSummary.tranCurDesc} ${transactionDetailSummary.amountTran}</c:when>
												<c:otherwise>
													<c:choose>
														<c:when test="${not empty transactionDetailSummary.amtTran &&  empty transactionDetailSummary.tranCurDesc}">N/A ${transactionDetailSummary.amountTran}</c:when>
													</c:choose>
													<c:choose>
														<c:when test="${empty transactionDetailSummary.amtTran}">N/A</c:when>
													</c:choose>
												</c:otherwise>
											</c:choose>
										</td>
								</tr>
								<tr>
									<td><label><spring:message
												code="txn.detail.lbl.transactionAdditionalAmount" /></label></td>
									<td id="amtAdd">
										<c:choose>
											<c:when test="${not empty transactionDetailSummary.amtAdd && not empty transactionDetailSummary.tranCurDesc }">${transactionDetailSummary.tranCurDesc} ${transactionDetailSummary.amtAdd}</c:when>
											<c:otherwise>
												<c:choose>
													<c:when test="${not empty transactionDetailSummary.amtAdd &&  empty transactionDetailSummary.tranCurDesc}">N/A ${transactionDetailSummary.amtAdd}</c:when>
												</c:choose>
												<c:choose>
													<c:when test="${empty transactionDetailSummary.amtAdd}">N/A</c:when>
												</c:choose>
											</c:otherwise>
										</c:choose>
									</td>
									<td><label><spring:message
												code="txn.detail.lbl.approvalCode" /></label></td>
									<td id="approvalCode"><c:if
											test="${empty transactionDetailSummary.approvalCode}">N/A</c:if>${transactionDetailSummary.approvalCode }</td>
								</tr>
								<tr>
									<td><label><spring:message
												code="txn.detail.lbl.acquirerBank" /></label></td>
									<td id="participantIdAcq"><c:if
											test="${empty transactionDetailSummary.acqBankName}">N/A</c:if>${transactionDetailSummary.acqBankName}</td>
									<td><label><spring:message
												code="txn.detail.lbl.issuerBank" /></label></td>
									<td id="participantIdIss"><c:if
											test="${empty transactionDetailSummary.issBankName}">N/A</c:if>${transactionDetailSummary.issBankName }</td>
								</tr>
								<tr>
									<td><label><spring:message
												code="txn.detail.lbl.cardAcceptorBusinessCode" /></label></td>
									<td id="cardAcceptorBusinessCode">
										<c:choose>
											<c:when test="${not empty transactionDetailSummary.mcc && not empty transactionDetailSummary.cardAcptBsnsCode }">${transactionDetailSummary.mcc} - ${transactionDetailSummary.cardAcptBsnsCode}</c:when>
											<c:otherwise>
												<c:choose>
													<c:when test="${not empty transactionDetailSummary.mcc &&  empty transactionDetailSummary.cardAcptBsnsCode}">${transactionDetailSummary.mcc}</c:when>
												</c:choose>
												<c:choose>
													<c:when test="${empty transactionDetailSummary.mcc}">N/A</c:when>
												</c:choose>
											</c:otherwise>
										</c:choose>
									
									<%-- <c:if
											test="${empty transactionDetailSummary.cardAcptBsnsCode}">N/A</c:if>${transactionDetailSummary.cardAcptBsnsCode } --%>
									</td>
									<td><label><spring:message
												code="txn.detail.lbl.eCommerceIndicator" /></label></td>
									<td id="eciInd"><c:if
											test="${empty transactionDetailSummary.eciInd}">N/A</c:if>${transactionDetailSummary.eciInd}</td>

								</tr>
								<tr>
									<td><label><spring:message
												code="txn.detail.lbl.caseNumber" /></label></td>
									<td id="caseNumber"><c:if
											test="${empty transactionDetailSummary.caseNo}">N/A</c:if>${transactionDetailSummary.caseNo}</td>
									<td><label><spring:message
												code="txn.detail.lbl.cardAcceptorTerminalID" /></label></td>
									<td id="cardAcptTermId"><c:if
											test="${empty transactionDetailSummary.cardAcptTermId}">N/A</c:if>${transactionDetailSummary.cardAcptTermId }</td>

								</tr>
								<tr>
									<td><label><spring:message
												code="txn.detail.lbl.originator" /></label></td>
									<td id="txnOrgInstID">
										<c:choose>
											<c:when test="${not empty transactionDetailSummary.txnOrgInstId && not empty transactionDetailSummary.txnOrgInstIdDesc }">${transactionDetailSummary.txnOrgInstId}-${transactionDetailSummary.txnOrgInstIdDesc}</c:when>
											<c:otherwise>
												<c:choose>
													<c:when test="${not empty transactionDetailSummary.txnOrgInstId &&  empty transactionDetailSummary.txnOrgInstIdDesc}">${transactionDetailSummary.txnOrgInstId}</c:when>
												</c:choose>
												<c:choose>
													<c:when test="${empty transactionDetailSummary.txnOrgInstId}">N/A</c:when>
												</c:choose>
											</c:otherwise>
										</c:choose>
									</td>
									<td><label><spring:message
												code="txn.detail.lbl.destination" /></label></td>
									<td id="txnDestInstID">
										<c:choose>
											<c:when test="${not empty transactionDetailSummary.txnDestInstId && not empty transactionDetailSummary.txnDestInstIdDesc }">${transactionDetailSummary.txnDestInstId}-${transactionDetailSummary.txnDestInstIdDesc}</c:when>
											<c:otherwise>
												<c:choose>
													<c:when test="${not empty transactionDetailSummary.txnDestInstId &&  empty transactionDetailSummary.txnDestInstIdDesc}">${transactionDetailSummary.txnDestInstId}</c:when>
												</c:choose>
												<c:choose>
													<c:when test="${empty transactionDetailSummary.txnDestInstId}">N/A</c:when>
												</c:choose>
											</c:otherwise>
										</c:choose>
									</td>
								</tr>
								<tr>
									<td><label><spring:message
												code="txn.detail.lbl.currentStatus" /></label></td>
									<td id="currentStatus"><c:if
											test="${empty transactionDetailSummary.disputeName}">N/A</c:if>${transactionDetailSummary.disputeName}</td>
									<td><label><spring:message
												code="txn.detail.lbl.actionCode" /></label></td>
									<td id="actionCode"><c:if
											test="${empty transactionDetailSummary.actionCodeDesc}">N/A</c:if>${transactionDetailSummary.actionCodeDesc }</td>
								</tr>
								<tr>
									<td><label><spring:message
												code="txn.detail.lbl.arqcAuthorizationIndicator" /></label></td>
									<td id="arqcAuthorizationIndicator"><c:if
											test="${empty transactionDetailSummary.arqcAuthInd}">N/A</c:if>${transactionDetailSummary.arqcAuthInd }</td>
									<td></td>
									<td></td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				
				<div>
					<h4>Transaction Life Cycle</h4>
				</div>

				<div class="panel-group" id="accordion">

					<c:forEach var="disTxnList" items="${txnDetailList}">
						<c:set var="disTxnList" value="${disTxnList}" scope="request" />
						<div class="panel panel-default">
							<div class="panel-heading">
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#accordion"
										href="#collapse${disTxnList.mti}${disTxnList.funcCode}${disTxnList.txnId}"><c:if
											test="${empty disTxnList.transactionCycle}">N/A</c:if>${disTxnList.transactionCycle}
										<%-- <c:if test="${transactionDetailSummary.funcCode eq '100' }">DMS Authorization</c:if>
										<c:if test="${transactionDetailSummary.funcCode eq '200' }">SMS Authorization</c:if> --%>

										<span class="glyphicon glyphicon-plus accor-auth-icon"></span>
									</a>
								</h4>
							</div>
							<div id="collapse${disTxnList.mti}${disTxnList.funcCode}${disTxnList.txnId}"
								class="panel-collapse collapse">
								<div class="">
									<div class="row">
										<div class="col-md-12">
											<table name="tabNew" id="tabId" class="table table-striped" style="font-size: 12px">
													<caption></caption>	
													<thead  style="display:none;">
														<tr>
															<th scope="col"></th>
														</tr>
													</thead>
													<tbody>													   
														<!-- disputes loaded from Json -->
														<c:import url="disputeWiseTxnAttributesFromJson.jsp" />
													</tbody>
												</table>
												<div class="bs-example">
													<div class="panel-group" id="childAccordion">
														<c:if test="${disTxnList.feeDetailsInd eq 'Y'}">
															<c:import url="seeFeesAndPenalties.jsp" />
														</c:if>
														<c:if test="${disTxnList.mti eq '0210' || disTxnList.mti eq '0110' || disTxnList.funcCode eq '260'}">
															<c:if test="${not empty transactionDetailSummary.txnIccDTO}">
																<c:import url="iccData.jsp" />
															</c:if>
															
															<c:if test="${transactionDetailSummary.posDetailsInd eq 'Y' }"> 
																<c:import url="posDataCode.jsp" />
															</c:if>
														</c:if>
														<c:if test="${disTxnList.docInd eq 'Y'}">
															<c:import url="documentsUpload.jsp" />
														</c:if>
														<c:if test="${not empty disTxnList.octData}">
															<c:import url="octData.jsp" />
														</c:if>
													</div>
												</div>
											
										</div>
									</div>

								</div>
							</div>

						</div>

					</c:forEach>
					<%-- <sec:authorize
							access="hasAuthority('Approve Transaction Dispute')"> --%>

							<c:if test="${(makerCheckerFlag eq 'checker' and status eq 'P') or (status eq 'PR' and makerCheckerFlag eq 'maker') or (status eq 'RPA' and makerCheckerFlag eq 'checker') }">
							<div id="collapseSix"
								class="panel-collapse collapse in panelHideShow approveReject">
								<div class="panel-body">
									<div>
										<table class="table table-striped infobold"
											style="font-size: 12px">
											<caption style="display:none;">Transaction Detail</caption> 
											<thead style="display:none;"><th scope="col"></th></thead>
											<tbody>
													<tr>
														<td colspan="6"><div
																class="panel-heading-red  clearfix">
																<strong><span class="glyphicon glyphicon-info-sign"></span>
																	<span data-i18n="Data"><spring:message
																			code="AM.lbl.reqInfo" /></span></strong>
															</div></td>
													</tr>

													<tr>
														<td><label><spring:message
																	code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
														<td><select name="select" id="apprej">
																<option value="N"><spring:message
																		code="AM.lbl.select" /></option>
																<c:if test="${status eq 'PR' and makerCheckerFlag eq 'maker' }">
																	<option value="PR" id="approve"><spring:message
																		code="AM.lbl.approve" /></option>
																</c:if>
																<c:if test="${status eq 'RPA' and makerCheckerFlag eq 'checker'}">
																	<option value="RPA" id="approve"><spring:message
																		code="AM.lbl.approve" /></option>
																</c:if>
																<c:if test="${status eq 'P' }">
																	<option value="IP" id="approve"><spring:message
																		code="AM.lbl.approve" /></option>
																</c:if>
																
																<option value="FAILED" id="reject"><spring:message
																		code="AM.lbl.reject" /></option>
														</select></td>
														<td><div style="text-align:center">
																<label><spring:message code="AM.lbl.remarks" /><span
																	style="color: red">*</span></label>
															</div></td>
														<!-- //Added by deepak on 31-03-2016 -->
														<td colspan="2"><textarea rows="4" cols="50"
																maxlength="100" id="rejectReason"></textarea>
															<div id="errorrejectReason" class="error"></div></td>
													</tr>
											</tbody>
										</table>
									</div>
									<div class="row">
										<div class="col-md-12">
											<div class="card">
												<div class="card-body">
													<div class="row">
															<div class="col-sm-12 text-center">
																<button type="button" class="btn btn-danger"
																	id="submitButton" onclick="submitResponse();">
																	Submit</button>
															</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							</c:if>
						<%-- </sec:authorize> --%>
					</div>

			</div>
		</div>
	</div>
</div>
</div>
</html>
