


var reasonCodeList = [];
var entry = {};
var index = 0;
$(document).ready(function () {
$('#tabnew').hide();
	document.getElementById('reasonCodeSuccessMsg').style.display = 'none';
	document.getElementById('reasonCodeErrorMsg').style.display = 'none';

$('#actionCode').on('keyup keypress blur change',function () {
		validateFromCommonVal('actionCode', true, "SelectionBox", '', false);
	});
$('#reasonCode').on('keyup keypress blur change',function () {
		validateFromCommonVal('reasonCode', true, "SelectionBox", '', false);
	});
$('#relationOperator').on('keyup keypress blur change',function () {
		validateFromCommonVal('relationOperator', true, "SelectionBox", '', false);
	});
$('#logicalReasonCode').on('keyup keypress blur change',function () {
		validateFromCommonVal('logicalReasonCode', true, "SelectionBox", '', false);
	});
$('#fieldValue').on('keyup keypress blur change',function () {
		validateFromCommonVal('fieldValue', true, "fieldValueRegex", '50', false);
	});
		$('#fieldName').multiselect({
		buttonWidth : '280px',
		paddingLeft: '0px',
		paddingRight: '15px',
		nonSelectedText:'Select Field Name',
		includeSelectAllOption: true,
		enableFiltering: true,
	});
	
		$('#fieldName1').multiselect({
		buttonWidth : '280px',
		paddingLeft: '0px',
		paddingRight: '15px',
		nonSelectedText:'Select Field Name',
		includeSelectAllOption: true,
		enableFiltering: true,
	});
	
	$('#relationOperator1').on('keyup keypress blur change',function () {
		validateFromCommonVal('relationOperator1', true, "SelectionBox", '', false);
	});

$('#fieldValue1').on('keyup keypress blur change',function () {
		validateFromCommonVal('fieldValue1', true, "fieldValueRegex", '50', false);
	});



if($('#editFlow').val()=='Y'){
$('#logicalReasonCode').val('A');
$('#logicalReasonCode1').val('A');
$('#reasonCode1').val($('#hlogicalReasonCode').val());
$('#reasonCode').val($('#hlogicalReasonCode').val());
}


$('form')
    .each(function(){
        $(this).data('serialized', $(this).serialize())
    })
    .on('change input', function(){
        $(this)             
            .find('input:submit, button:submit')
                .prop('disabled', $(this).serialize() == $(this).data('serialized'))
        ;

        $('#submitButton').prop('disabled', false);
        $('#submitButtonAdd').prop('disabled', false);
     })

      $('#submitButton').prop('disabled', true);
     $('#submitButtonAdd').prop('disabled', true);
 if (($('#showbutton').val() == "Y")) {
	  $('#submitButton').prop('disabled', true);
	 $('#submitButtonAdd').prop('disabled', true);
	 $("input").prop('disabled', true);
		$("select").prop('disabled', true);
	}




$('#clearRcRules').click(function()
			{
			
			entry = {};
		
		$('#fieldValue').val("")
		$('.jqueryError').hide();

		$('#actionCode').val("SELECT")
$('#reasonCode').val("SELECT")
$('#relationOperator').val("SELECT")
$('#fieldOperator').val("")
		$('#logicalReasonCode').val("0")
		$("#fieldName").multiselect("rebuild");
		$("#fieldName").multiselect("refresh");
		document.getElementById("fieldName").value = "SELECT";
		$("#errfieldName").hide();
		$("#errfieldValue").hide();
		$("#errreasonCode").hide();
		$("#errrelationOperator").hide();
		$("#erractionCode").hide();
		$("#errlogicalReasonCode").hide();

		$('#status').val("A");  
	     
	 	$("#fieldName").multiselect("rebuild");
	 	$("#fieldName").multiselect("refresh");
	 	$('#errorStatus2').hide();
		$('#errorStatus2').html("");
	 	
			});

	
	if(document.getElementById("editFieldName")!=null){

		var y = document.getElementById("editFieldName").value;
		var dataarray=[];
		if(y!=null){
			 dataarray=y.split(",");

		}

		$('#fieldName').multiselect('refresh');
		$('#fieldName').multiselect('select', dataarray);

	}







});
var callBackFunc;

function callBackFunction(flag){
	callBackFunc=flag;
}



function clearRcRulesEdit(){
			
			
			entry = {};
		
		$('#fieldValue1').val("")
		$('.jqueryError').hide();

		
$('#relationOperator1').val("SELECT")
		
		$("#fieldName1").multiselect("rebuild");
		$("#fieldName1").multiselect("refresh");
		document.getElementById("fieldName1").value = "SELECT";
		$("#errfieldName1").hide();
		$("#errfieldValue1").hide();
		$('#fieldOperator1').val("")
		$('#errfieldOperator1').val("")
		$("#errrelationOperator1").hide();
	
		$('#status1').val("A");  
	     
	 	$("#fieldName1").multiselect("rebuild");
	 	$("#fieldName1").multiselect("refresh");
			}

function validateRcRules(action,type){

	var check = false;

	check = validateFields(check);
	var fieldNameList = "";
			var fieldNm=""
		var arr1=[];
	

		fieldNm = $('#fieldName option:selected').toArray().map(item => item.value).join();

		arr1= fieldNm.split(",");
if(arr1.length==0){
check = true;
$("#errfieldName").show();
						$("#errfieldName").find('.error').html('Please Select Field Name');
}else{
$("#errfieldName").hide();
						$("#errfieldName").find('.error').html('');
		
		


	}
if(type=='Add')	
		{
		var actionCode =  $('#actionCode').val();
		var reasonCode= $('#reasonCode').val();
		
		var relationOperator =  $('#relationOperator').val();
		
			
			
			
			if($('#fieldName').val()!=0){

		var a = $('#fieldName option:selected').toArray().map(item => item.value).join();

		arr1= a.split(",");


		
		for ( let i of arr1) {
			fieldNameList = fieldNameList + i + "|"
			;
		}


	}
		var y=checkDupRcRules(actionCode,reasonCode,relationOperator,fieldNameList,type);
    	console.log(y);
		if(callBackFunc){
        	check = true;	
        }
}
	if (!check) {
		if(type=='Edit'){
		addOrUpdateRcRules(action)}
		else{
		return false;
		}
	}else{
		return true;
	}


}


function validateFields(check) {
	if (!validateFromCommonVal('actionCode',
		true, "SelectionBox", "", false)) {
		check = true;
	}

	if (!validateFromCommonVal('reasonCode',
		true, "SelectionBox", "", false)) {
		check = true;
	}

	if (!validateFromCommonVal('relationOperator',
		true, "SelectionBox", "", false)) {
		check = true;

	}

	if (!validateFromCommonVal('fieldValue',
		true, "fieldValueRegex", "50", false)) {
		check = true;
	}

	if (!validateFromCommonVal('logicalReasonCode',
		true, "SelectionBox", "", false)) {
		check = true;
	}
	return check;
}

function validateRcRulesEdit(action,type){

var check = false;

	check = validateRules(check);
	var fieldNameList = "";
			var fieldNm=""
		var arr1=[];
	

		fieldNm = $('#fieldName1 option:selected').toArray().map(item => item.value).join();

		arr1= fieldNm.split(",");
if(arr1.length==0){
$("#errfieldName1").show();
						$("#errfieldName1").find('.error').html('Please Select Field Name');
						check = true;
}else{
$("#errfieldName1").hide();
						$("#errfieldName1").find('.error').html('');
		}
		


	
if(type=='Add')	
		{var actionCode =  $('#actionCode1').val();
		var reasonCode= $('#toStateEdit').val();
		
		var relationOperator =  $('#relationOperator1').val();
		
			
			
			
			if($('#fieldName1').val()!=0){

		var a = $('#fieldName1 option:selected').toArray().map(item => item.value).join();

		arr1= a.split(",");


		
		for ( let i of arr1) {
			fieldNameList = fieldNameList + i + "|"
			;
		}


	}
		var y=checkDupRcRules(actionCode,reasonCode,relationOperator,fieldNameList,"Edit");
console.log(y);
		if(callBackFunc){
        	check = true;	
        }
}
	if (!check) {
		if(type=='Edit'){
		addOrUpdateRcRules(action)}
		else{
		return false;
		}
	}else{
		return true;
	}


}



function validateRules(check) {
	if (!validateFromCommonVal('actionCode1',
		true, "SelectionBox", "", false)) {
		check = true;
	}

	if (!validateFromCommonVal('reasonCode1',
		true, "SelectionBox", "", false)) {
		check = true;
	}

	if (!validateFromCommonVal('relationOperator1',
		true, "SelectionBox", "", false)) {
		check = true;

	}

	if (!validateFromCommonVal('fieldValue1',
		true, "fieldValueRegex", "50", false)) {
		check = true;
	}

	if (!validateFromCommonVal('logicalReasonCode1',
		true, "SelectionBox", "", false)) {
		check = true;
	}
	return check;
}

function addOrUpdateRcRules(action){
	var fieldNameList = "";
	var a;
	if($('#fieldName').val()!=0){

		a = $('#fieldName option:selected').toArray().map(item => item.value).join();

		var arr1= a.split(",");


		
		for ( let i of arr1) {
			fieldNameList = fieldNameList + i + "|"
			;
		}


	}
	
	
	var fievalue1=document.getElementById('toEditFieldValue').value;
	var relOp1=document.getElementById('toEditRelOp').value;
	var fieldName1=document.getElementById('editFieldName').value;
	
	if(!((fievalue1==$('#fieldValue').val()) && (relOp1==$('#relationOperator').val()) &&(fieldName1==a)) ){
		entry['type'] = "Edit";
	entry['id'] = document.getElementById('hseqid').value;
		entry['seqId'] = document.getElementById('hseqid').value;

		entry['actionCode'] = document.getElementById('actionCode').value;
		entry['toState'] = document.getElementById('reasonCode').value;
		entry['logToState'] = document.getElementById('reasonCode').value;
		entry['reasonCode'] = document.getElementById('toEditReasonCode').value;
		entry['fieldOperator'] = document.getElementById('fieldOperator').value;
		entry['logicalReasonCode'] = document.getElementById('logicalReasonCode').value;
		entry['fieldValue'] = document.getElementById('fieldValue').value;
		
		entry['relationOperator'] = document.getElementById('relationOperator').value;
		
		entry['status'] = document.getElementById('status').value;
entry['fieldName'] = fieldNameList;
			reasonCodeList.push(entry);

	
	}
addReasonCodeRuless(action);
	


}

function submitForm(url) {

	var data = "" ;
	postData(url, data);
}

function checkDupRcRules(actionCode,reasonCode,relationOperator,fieldNameList,type) {
console.log("inside dup rules 1");
		var validFlag = false;
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
			
			if (actionCode != "" && reasonCode!="" && fieldNameList!="" && relationOperator!="") {

				$.ajax({
					url : "checkDupReasonCodeRules",
					type : "POST",
					dataType : "json",
					data: {"actionCode" : actionCode,"reasonCode" : reasonCode,"fieldName" :fieldNameList,"relationOperator" :relationOperator,
						    "_TransactToken" : tokenValue},
					success : function(response){

						if (response.status == "BSUC_0001") {
							callBackFunction(true);
							validFlag = true;
							if(type=="Add"){
							$("#errreasonCode").show();
						$("#errreasonCode").find('.error').html('Reason Code Rules already exist');
							}else{
							$("#errreasonCode1").show();
						$("#errreasonCode1").find('.error').html('Reason Code Rules already exist');
						
							}

						} else {
						callBackFunction(false);
							validFlag = false;
							$("#errreasonCode").hide();
						$("#errreasonCode").find('.error').html('');
							
						}
					},
					error : function() {
						$("#errreasonCode").hide();
						$("#errreasonCode").find('.error').html('');
						callBackFunction(false);
					}
				});

			} else {
				validFlag = false;
			}
			return validFlag;
		}
		

function saveReasonCodeRule(type) {
	var isValid = validateRcRules('/saveReasonCodeRules',type);
	if (!isValid) {
		setEntryIdVal(type);
		
		
		entry['actionCode'] = document.getElementById('actionCode').value;
		entry['actionCodeDesc'] =getLookupDesc("actionCode", "#actionCode > option");
		entry['toState'] = document.getElementById('reasonCode').value;
		entry['logToState'] = document.getElementById('reasonCode').value;
		entry['reasonCode'] = document.getElementById('reasonCode').value;
		entry['reasonCodeDesc'] =getLookupDesc("reasonCode", "#reasonCode > option");
		entry['fieldOperator'] = document.getElementById('fieldOperator').value;
		entry['fieldOpDesc'] = getLookupDesc("fieldOperator", "#fieldOperator > option");
			if(entry['fieldOpDesc']=="--Select--"){
		entry['fieldOpDesc']='';
		}
		entry['logicalReasonCode'] = document.getElementById('logicalReasonCode').value;
		entry['fieldValue'] = document.getElementById('fieldValue').value;
		
		entry['relationOperator'] = document.getElementById('relationOperator').value;
		entry['relOpDesc'] = getLookupDesc("relationOperator", "#relationOperator > option");
		entry['logicalReasonCodeDesc'] =  getLookupDesc("logicalReasonCode", "#logicalReasonCode > option");
	
		entry['status'] = document.getElementById('status').value;
		var status="";
		status = setStatus(status);
			var fieldNameList = "";
			var fieldNm=""
		
	if($('#fieldName').val()!=0){

		fieldNm = $('#fieldName option:selected').toArray().map(item => item.value).join();

		var arr1= fieldNm.split(",");


		
		fieldNameList = prepareFieldNameList(arr1, fieldNameList);


	}
		entry['fieldName'] = fieldNameList;
			reasonCodeList.push(entry);
			
			var actionCodeNode = document.createTextNode(entry['actionCodeDesc']);
			var reasonCodeNode = document.createTextNode(entry['reasonCodeDesc']);
			var relOpDescNode = document.createTextNode(entry['relOpDesc']);
			var fieldNmNode = document.createTextNode(fieldNm);
			var fieldValueNode = document.createTextNode(entry['fieldValue']);
			var logicalReasonCodeDescNode = document.createTextNode(entry['logicalReasonCodeDesc']);
			var fieldOperatorNode = document.createTextNode(entry['fieldOpDesc']);
			var statusNode = document.createTextNode(status);
			

		if (reasonCodeList.length > 0) {
			var id = reasonCodeList.map(e => e.id).indexOf(entry['id']);
			$('#tabnew').hide();
			
			
$('#tabnew').append(
					  $('<tr>').attr('id', 'tabnew_' + id).append(
					    $('<td>').append(actionCodeNode),
					    $('<td>').append(reasonCodeNode),
					    $('<td>').append(relOpDescNode),
					    $('<td>').append(fieldNmNode),
					    $('<td>').append(fieldValueNode),
					    $('<td>').append(logicalReasonCodeDescNode),
					    $('<td>').append(fieldOperatorNode),
					    $('<td>').append(statusNode),
					    $('<td>').append($('<input>').attr({
					      'type': 'button',
					      'class': 'btn btn-danger remReasonCodeRule',
					      'onclick': 'removeReasonCodeRules(' + id + ')',
					      'value': 'Remove',
					      'style':'width: 150px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;'
					    }))
					  )
					);
			if (type === "Add") {
				$('#tabnew').show();
				$('#errorStatus2').hide();
		$('#errorStatus2').html("");
				
			}
			index++;
		} else {
			$('#tabnew').hide();
		}

	

		if (type === 'Add') {
			$('#clearRcRules').click();
		}
	} else {
		
		$('#errorStatus2').html("Please provide all data properly");
		$('#errorStatus2').show();
	}
}



function setEntryIdVal(type) {
	if (type === "Add") {
		entry['id'] = index;
	} else {
		entry['id'] = document.getElementById('hseqid').value;
	}
}

function setStatus(status) {
	if (entry['status'] == 'A') {
		status = "Active";
	}
	else {
		status = "InActive";
	}
	return status;
}



function saveReasonCodeRuleEdit(type) {
	var isValid = validateRcRulesEdit('/updateReasonCodeRules',type);
	if (!isValid) {
		setEntryId(type);
		/* else {
			entry['id'] = document.getElementById('seqid').value;
		}*/
		
		entry['type'] = type;
		entry['actionCode'] = document.getElementById('actionCode1').value;
		entry['actionCodeDesc'] =getLookupDesc("actionCode1", "#actionCode1 > option");
		entry['toState'] = document.getElementById('toStateEdit').value;
		entry['logToState'] = document.getElementById('reasonCode1').value;
		entry['reasonCode'] = document.getElementById('toEditReasonCode').value;
		entry['reasonCodeDesc'] =getLookupDesc("reasonCode1", "#reasonCode1 > option");
		entry['fieldOperator'] = document.getElementById('fieldOperator1').value;
		
		entry['fieldOpDesc'] = getLookupDesc("fieldOperator1", "#fieldOperator1 > option");
		if(entry['fieldOpDesc']=="--Select--"){
		entry['fieldOpDesc']='';
		}
		entry['logicalReasonCode'] = document.getElementById('logicalReasonCode1').value;
		entry['fieldValue'] = document.getElementById('fieldValue1').value;
		
		entry['relationOperator'] = document.getElementById('relationOperator1').value;
		entry['relOpDesc'] = getLookupDesc("relationOperator1", "#relationOperator1 > option");
		entry['logicalReasonCodeDesc'] =  getLookupDesc("logicalReasonCode1", "#logicalReasonCode1 > option");
		entry['status'] = document.getElementById('status1').value;

			var fieldNameList = "";
			var fieldNm=""
		
	if($('#fieldName1').val()!=0){

		fieldNm = $('#fieldName1 option:selected').toArray().map(item => item.value).join();

		var arr1= fieldNm.split(",");


		
		fieldNameList = prepareFieldNameList(arr1, fieldNameList);


	}
		entry['fieldName'] = fieldNameList;
		
		
			reasonCodeList.push(entry);
			
			 document.createTextNode(entry['type']);
			var actionCodeNode = document.createTextNode(entry['actionCodeDesc']);
			 document.createTextNode(entry['toState']);
			 document.createTextNode(entry['logToState']);
			var reasonCodeNode = document.createTextNode(entry['reasonCodeDesc']);
			var fieldOperatorNode = document.createTextNode(entry['fieldOpDesc']);
			document.createTextNode(entry['logicalReasonCode']);
			var fieldValueNode = document.createTextNode(entry['fieldValue']);
			
		 document.createTextNode(entry['relationOperator']);
			var relOpDescNode = document.createTextNode(entry['relOpDesc']);
			var logicalReasonCodeDescNode = document.createTextNode(entry['logicalReasonCodeDesc']);
			var statusNode = document.createTextNode(entry['status']);
			var fieldNmNode = document.createTextNode(fieldNm);

		if (reasonCodeList.length > 0) {
			var id = reasonCodeList.map(e => e.id).indexOf(entry['id']);
			$('#tabnew2').hide();
				
		
$('#tabnew2').append(
					  $('<tr>').attr('id', 'tabnew2_' + id).append(
					    $('<td>').append(actionCodeNode),
					    $('<td>').append(reasonCodeNode),
					    $('<td>').append(relOpDescNode),
					    $('<td>').append(fieldNmNode),
					    $('<td>').append(fieldValueNode),
					    $('<td>').append(logicalReasonCodeDescNode),
					    $('<td>').append(fieldOperatorNode),
					    $('<td>').append(statusNode),
					    $('<td>').append($('<input>').attr({
					      'type': 'button',
					      'class': 'btn btn-danger remReasonCodeRule',
					      'onclick': 'removeReasonCodeRulesEdit(' + id + ')',
					      'value': 'Remove',
					      'style':'width: 150px; height: 24px;padding: 5px 10px;font-size: 12.5px;color: #000;border-radius: 3px;'
					    }))
					  )
					);
		
		if (type === "Add") {
				$('#tabnew2').show()
				$('#errorStatus2').hide();
				$('#errorStatus2').html("");
			}
			index++;
		} else {
			$('#tabnew2').hide();
		}

	

		if (type == "Add") {
			clearRcRulesEdit();
		}
	} else {
		
$('#errorStatus2').html('Please provide all data properly');
		$('#errorStatus2').show();
	}
}

function setEntryId(type) {
	if (type === "Add") {
		entry['id'] = index;
	}
}

function prepareFieldNameList(arr1, fieldNameList) {
	for (let i of arr1) {
		fieldNameList = fieldNameList + i + "|";
	}
	return fieldNameList;
}

function addReasonCodeRuless(actionUrl) {

console.log(reasonCodeList);
$("#errfieldName1").hide();

$('#errorStatus2').hide();
$('#errorStatus2').html("");
		$("#errfieldValue1").hide();
		
		$("#errrelationOperator1").hide();


	if (reasonCodeList.length > 0) {
	if (actionUrl.substring(1, 5) === "upda") {
	$('#tabnew2').show();
	}else{
	$('#tabnew').show();}
		
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		var linkurl = pathName + actionUrl;
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		$.ajax({
			url: linkurl,
			type: "POST",
			dataType: "json",
			data: JSON.stringify(reasonCodeList),
			"beforeSend": function (xhr) {
                    xhr.setRequestHeader('_TransactToken', tokenValue);
                },
			contentType: "application/json; charset=utf-8",
			cache: false,
			success: function(response) {
				if (response.status == "BSUC_0001") {
					document.querySelectorAll('.remReasonCodeRule').forEach(item => item.disabled = true);
					disableButtons(actionUrl);
					$("input").prop('disabled', true);
					$("select").prop('disabled', true);
				document.getElementById('fieldName').disabled = true;
				  emptyErrorFields(); 
		$('#fieldName').empty();
				
					document.getElementById('reasonCodeSuccessMsg').style.display = 'block';
				} else {
					document.getElementById('reasonCodeErrorMsg').style.display = 'block';
					$('.panel').hide();
				}
			},
			error: function() {
				document.getElementById('reasonCodeErrorMsg').style.display = 'block';
				$('.panel').hide();
			}
		});
	} else {
		
		$('#errorStatus2').html('Please add the record first');
		$('#errorStatus2').show();
	}
}


function disableButtons(actionUrl) {
	if (actionUrl.substring(1, 5) === "save") {

		document.getElementById('addReasonCodeRule').disabled = true;
		document.getElementById('submitButtonAdd').disabled = true;
		document.getElementById('clearRcRules').disabled = true;
	}
	else {
		document.getElementById('submitButton').disabled = true;
		if (document.getElementById('approveRole')) {
			document.getElementById('approveRole').disabled = true;
		}
		if (document.getElementById('addReasonCodeRule')) {
			document.getElementById('addReasonCodeRule').disabled = true;
		}
	}
}

function emptyErrorFields() {
	$(".error").each(function() {
		if ($(this).text().trim().length > 0) {

			$(this).empty();

		}
	});
}

function removeReasonCodeRules(id) {
	if (reasonCodeList.length > 1) {
		$(`#tabnew_${id}`).remove();
	} else {
		$(`#tabnew_${id}`).remove();
		$('#tabnew').hide();
	}
	reasonCodeList.splice(id, 1);
}

function getLookupDesc(id, options) {
	const objectId = document.getElementById(id).value
	if (objectId === "SELECT") {
		return "";
	} else {
		const item = [...document.querySelectorAll(options)].map(element => ({ [element.value]: element.label })).filter(item1 => !("SELECT" in item1));
		return Object.values(item.filter(elem => elem[objectId]).pop())[0];
	}
}
function removeReasonCodeRulesEdit(id) {
	if (reasonCodeList.length > 1) {
		$(`#tabnew2_${id}`).remove();
	} else {
		$(`#tabnew2_${id}`).remove();
		$('#tabnew2').hide();
	}
	reasonCodeList.splice(id, 1);
}

function discardAction(url) {

	var data = "seqId," + escape($('#hseqid').val());
	postData(url, data);
}
