package org.npci.settlenxt.adminportal.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.DisputeFeeRuleDTO;
import org.npci.settlenxt.adminportal.dto.DisputeTransitionDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.DisputeRepository;
import org.npci.settlenxt.portal.common.dto.ActionCodeDTO;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DisputeServiceImpl implements DisputeService {

	@Autowired
	private DisputeRepository disputeRepository;

	@Autowired
	private BaseLookupService lookupService;

	@Autowired
	private SessionDTO sessionDTO;

	private int index = 1;
	private String dbToState = "";
	private String currOperator = "";
	private List<DisputeTransitionDTO> fullTransitionList = new ArrayList<>();
	

	@Override
	public List<DisputeFeeRuleDTO> disputeFeeRuleList() {
		return disputeRepository.getDisputeFeeRuleList();
	}

	@Override
	public DisputeFeeRuleDTO getDisputeFeeRuleInfo(int seqId) {
		return disputeRepository.getDisputeFeeRuleInfo(seqId);
	}

	@Override
	public List<ActionCodeDTO> getActionCodeList() {
		return disputeRepository.getActionCodeList();
	}

	@Override
	public List<String> getRelationComplexOpList(String type) {
		return disputeRepository.getRelationComplexOpList(type);
	}

	@Override
	public String addDisputeFeeRule(List<DisputeFeeRuleDTO> disputeFeeRuleDtoList) {
		try {
			for (DisputeFeeRuleDTO disputeFeeRuleDto : disputeFeeRuleDtoList) {
				disputeFeeRuleDto.setFeeCode(getFeeCode(disputeFeeRuleDto));
				DisputeFeeRuleDTO disputeFeeRuleDtoSeqId = disputeRepository.fetchSeqId();
				disputeFeeRuleDto.setSeqId(disputeFeeRuleDtoSeqId.getSeqId());
				disputeFeeRuleDto.setLastUpdatedOn(new Date());
				disputeFeeRuleDto.setLastUpdatedBy(sessionDTO.getUserName());
				disputeFeeRuleDto.setCreatedOn(new Date());
				disputeFeeRuleDto.setCreatedBy(sessionDTO.getUserName());
				disputeFeeRuleDto.setRequestState(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
				disputeFeeRuleDto.setLastOperation(CommonConstants.ADD_REASON_CODE_LAST_OPERATION);
				disputeFeeRuleDto.setStatus(CommonConstants.USER_ACTIVE_STATUS);
				disputeRepository.addDisputeFeeRule(disputeFeeRuleDto);
			}
		} catch (Exception e) {
			return "Error";
		}
		return "Success";
	}

	public String getFeeCode(DisputeFeeRuleDTO disputeFeeRuleDto) {

		
		int maxValue = 0;
		List<DisputeFeeRuleDTO> disputeFeeRuleList = disputeRepository
				.fetchDisputeFeeRuleUsingLogicalFeeCode(disputeFeeRuleDto.getLogicalFeeCode());

		if (disputeFeeRuleList.isEmpty()) {
			if ("AND".equalsIgnoreCase(disputeFeeRuleDto.getLogicalOperator())
					|| "OR".equals(disputeFeeRuleDto.getLogicalOperator())) {
				disputeFeeRuleDto.setFeeCode(disputeFeeRuleDto.getFeeCode() + "_1");
			}
		} else {
			maxValue = getMaxFeeValue(maxValue, disputeFeeRuleList);
			if (maxValue == 0) {
				if ("AND".equalsIgnoreCase(disputeFeeRuleDto.getLogicalOperator())
						|| "OR".equals(disputeFeeRuleDto.getLogicalOperator())) {
					disputeFeeRuleDto.setFeeCode(disputeFeeRuleDto.getFeeCode() + "_1");
				}
			} else {
				setMaxFeeCode(disputeFeeRuleDto, maxValue);
			}
		}
		return disputeFeeRuleDto.getFeeCode();
	}

	private void setMaxFeeCode(DisputeFeeRuleDTO disputeFeeRuleDto, int maxValue) {
		String maxFeeCode = "";
		if ("AND".equalsIgnoreCase(disputeFeeRuleDto.getLogicalOperator())) {
			maxFeeCode = Integer.toString(maxValue);
			disputeFeeRuleDto.setFeeCode(disputeFeeRuleDto.getFeeCode() + "_" + maxFeeCode);
		} else if ("OR".equals(disputeFeeRuleDto.getLogicalOperator())) {
			maxFeeCode = Integer.toString(maxValue + 1);
			disputeFeeRuleDto.setFeeCode(disputeFeeRuleDto.getFeeCode() + "_" + maxFeeCode);
		}
	}

	private int getMaxFeeValue(int maxValue, List<DisputeFeeRuleDTO> disputeFeeRuleList) {
		int afterValue;
		for (DisputeFeeRuleDTO disputeFeeRule : disputeFeeRuleList) {
			if (disputeFeeRule.getFeeCode().contains("_")) {
				afterValue = Integer.parseInt(disputeFeeRule.getFeeCode().split("_")[1]);
				if (afterValue > maxValue) {
					maxValue = afterValue;
				}
			}
		}
		return maxValue;
	}

	@Override
	public DisputeFeeRuleDTO updateDisputeFeeRuleStg(DisputeFeeRuleDTO disputeFeeRuleDto) {
		String feeCode = getFeeCode(disputeFeeRuleDto);
		if (StringUtils.isNotEmpty(feeCode)) {
			disputeFeeRuleDto.setFeeCode(feeCode);
		}
		disputeFeeRuleDto.setLastUpdatedBy(sessionDTO.getUserName());
		disputeFeeRuleDto.setLastUpdatedOn(new Date());
		disputeFeeRuleDto.setStatus(CommonConstants.USER_ACTIVE_STATUS);
		disputeFeeRuleDto.setRequestState(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
		disputeFeeRuleDto.setLastOperation("E");
		disputeRepository.updateDisputeFeeRuleStg(disputeFeeRuleDto);
		return disputeFeeRuleDto;
	}

	@Override
	public List<DisputeFeeRuleDTO> getPendingDisputeFeeRuleList() {
		return disputeRepository.getPendingDisputeFeeRuleList();
	}

	@Override
	public DisputeFeeRuleDTO updateApproveDisputeFeeRule(int seqId, String status, String remarks) {
		DisputeFeeRuleDTO disputeFeeRuleDto = getDisputeFeeRuleInfo(seqId);
		disputeFeeRuleDto.setRequestState(status);
		disputeFeeRuleDto.setCheckerComments(remarks);
		disputeFeeRuleDto.setLastUpdatedOn(new Date());
		disputeFeeRuleDto.setLastUpdatedBy(sessionDTO.getUserName());

		if ("Approved".equals(status)) {
			disputeFeeRuleDto.setStatusCode(BaseCommonConstants.TRANSACT_SUCCESS);
			disputeFeeRuleDto.setRequestState(BaseCommonConstants.REQUEST_STATE_APPROVED);
		} else {
			disputeFeeRuleDto.setRequestState(BaseCommonConstants.REQUEST_STATE_REJECTED);
		}

		if (disputeFeeRuleDto.getRequestState().equals(BaseCommonConstants.REQUEST_STATE_APPROVED)) {
			try {
				updateDisputeFeeRule(disputeFeeRuleDto);
			} catch (Exception e) {
				log.error("Error while updateDisputeFeeRule :", e);
			}
		}
		disputeFeeRuleDto.setLastUpdatedBy(disputeFeeRuleDto.getLastUpdatedBy());
		disputeFeeRuleDto.setLastUpdatedOn(disputeFeeRuleDto.getLastUpdatedOn());
		disputeFeeRuleDto.setCheckerComments(remarks);
		disputeRepository.updateDisputeFeeRuleStg(disputeFeeRuleDto);

		return disputeFeeRuleDto;
	}

	private void updateDisputeFeeRule(DisputeFeeRuleDTO disputeFeeRuleDto) {
		DisputeFeeRuleDTO disputeFeeRuleDtoMain = disputeRepository.getDisputeFeeRule(disputeFeeRuleDto.getSeqId());
		if (ObjectUtils.isEmpty(disputeFeeRuleDtoMain)) {
			disputeRepository.saveDisputeFeeRule(disputeFeeRuleDto);
		} else {
			disputeRepository.updateDisputeFeeRule(disputeFeeRuleDto);
		}
		disputeFeeRuleDto.setStatusCode(BaseCommonConstants.TRANSACT_SUCCESS);
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public String updateBulkStgDisputeFee(String disputeFeeList, String status) {

		try {
			String checkerComments = CommonConstants.RECORD_DESC_REJECTED;
			String reqState = CommonConstants.RECORD_REJECTED;
			
			if (StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
				checkerComments = CommonConstants.RECORD_DESC_APPROVED;
				reqState = "Approved";
			}
			String[] tranArray = disputeFeeList.split("\\|");

			int id;
			for (String seqId : tranArray) {
				id = Integer.parseInt(seqId);
				updateApproveDisputeFeeRule(id, reqState, checkerComments);
			}
		} catch (Exception e) {
			log.error("Error while approving/rejecting transition rule: ", e);
			return CommonConstants.RESULT_ERROR;
		}
		return CommonConstants.RESULT_SUCCESS;
	}

	@Override
	public DisputeFeeRuleDTO discardDisputeFeeRule(int seqId) {
		DisputeFeeRuleDTO disputeFeeRuleDto = disputeRepository.getDisputeFeeRuleInfo(seqId);
		DisputeFeeRuleDTO disputeFeeRuleDtoMain = disputeRepository.getDisputeFeeRule(seqId);

		if (disputeFeeRuleDtoMain != null) {
			disputeFeeRuleDtoMain.setRequestState(BaseCommonConstants.REQUEST_STATE_APPROVED);
			disputeFeeRuleDtoMain.setLastOperation("Discarded");
			disputeFeeRuleDtoMain.setLastUpdatedBy(sessionDTO.getUserName());
			disputeFeeRuleDtoMain.setLastUpdatedOn(new Date());
			disputeFeeRuleDtoMain.setLastUpdatedBy(disputeFeeRuleDto.getLastUpdatedBy());
			disputeFeeRuleDtoMain.setStatus(disputeFeeRuleDto.getStatus());
			disputeFeeRuleDtoMain.setCreatedBy(disputeFeeRuleDto.getCreatedBy());
			disputeFeeRuleDtoMain.setCreatedOn(disputeFeeRuleDto.getCreatedOn());
			disputeRepository.updateDisputeFeeRuleStg(disputeFeeRuleDtoMain);
		} else {
			disputeRepository.deleteDisputeFeeRule(seqId);
		}
		return disputeFeeRuleDto;
	}

	@Override
	@Transactional(readOnly = true)
	public List<DisputeTransitionDTO> getTransitionRulesList(String type) {
		List<DisputeTransitionDTO> fetchTransitionList;
		fetchTransitionList = getTransitionList(type);
		Map<String, String> lookupMap = lookupService.getLookupCodeValueDescMap();
		List<ActionCodeDTO> actionList = disputeRepository.getActionCodeList();
		try {
			Map<String, String> actionMap = actionListAsMap(actionList);
			if (CollectionUtils.isNotEmpty(fetchTransitionList) && Boolean.FALSE.equals(lookupMap.isEmpty())
					&& Boolean.FALSE.equals(actionMap.isEmpty())) {
				for (DisputeTransitionDTO transitionRule : fetchTransitionList) {
					String operator;
					operator = getOperator(fetchTransitionList, transitionRule);
					transitionRule.setOperator(operator);
					setOperatorDesc(transitionRule, operator);
					String key;
					setEntityAndState(lookupMap, actionMap, transitionRule);
					// sec field name
					setSecFieldNameAndDesc(lookupMap, transitionRule);

					String relOp;
					String relOpDesc;
					// complex rel op
					relOpDesc = getRelOpAndDesc(lookupMap,transitionRule)[0];
					relOp = getRelOpAndDesc(lookupMap,transitionRule)[1];

					transitionRule.setRelOp(relOp != null ? relOp : transitionRule.getRelOp());
					transitionRule.setRelOpDesc(relOpDesc != null ? relOpDesc : transitionRule.getRelOp());

					key = CommonConstants.DISPUTE_FIELD_OP + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR
							+ transitionRule.getFieldOperator();
					transitionRule.setFieldOperatorDesc(
							lookupMap.get(key) != null ? lookupMap.get(key) : transitionRule.getFieldOperator());
					setReqStateDesc(transitionRule);
				}
			}
		} catch (Exception e) {
			log.error("Error while viewing transition rule: ", e);
		}
		fullTransitionList = fetchTransitionList;
		return fetchTransitionList;
	}

	private void setEntityAndState(Map<String, String> lookupMap, Map<String, String> actionMap,
			DisputeTransitionDTO transitionRule) {
		String key;
		key = CommonConstants.DISPUTE_ENTITY + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR
				+ transitionRule.getEntity();
		transitionRule.setEntityDesc(
				lookupMap.get(key) != null ? lookupMap.get(key) : transitionRule.getEntity());
		key = transitionRule.getCurrState();
		transitionRule.setCurrStateDesc(
				actionMap.get(key) != null ? actionMap.get(key) : transitionRule.getCurrState());
		key = transitionRule.getLogToState();
		transitionRule.setToStateDesc(
				actionMap.get(key) != null ? actionMap.get(key) : transitionRule.getLogToState());
	}

	private List<DisputeTransitionDTO> getTransitionList(String type) {
		List<DisputeTransitionDTO> fetchTransitionList;
		if (StringUtils.equals(type, CommonConstants.RECORD_APPROVED)) {
			fetchTransitionList = disputeRepository.getPendingTransitionList(type, type);
		} else if (StringUtils.equals(type, CommonConstants.RECORD_PENDING)) {
			fetchTransitionList = disputeRepository.getPendingTransitionList(type, CommonConstants.RECORD_REJECTED);
		} else {
			fetchTransitionList = disputeRepository.getTransitionRulesList();
		}
		return fetchTransitionList;
	}

	private void setOperatorDesc(DisputeTransitionDTO transitionRule, String operator) {
		if (StringUtils.equals(operator, CommonConstants.DISPUTE_TRANS_OP_AND)) {
			transitionRule.setOperatorDesc(CommonConstants.DISPUTE_TRANS_OP_AND_DESC);
		} else if (StringUtils.equals(operator, CommonConstants.DISPUTE_TRANS_OP_OR)) {
			transitionRule.setOperatorDesc(CommonConstants.DISPUTE_TRANS_OP_OR_DESC);
		} else {
			transitionRule.setOperatorDesc(CommonConstants.DISPUTE_TRANS_OP_NONE_DESC);
		}
	}

	private void setSecFieldNameAndDesc(Map<String, String> lookupMap, DisputeTransitionDTO transitionRule) {
		String key;
		if (StringUtils.contains(transitionRule.getFieldName(),
				CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR)) {
			key = CommonConstants.FIELD_EX + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR
					+ transitionRule.getFieldName().substring(transitionRule.getFieldName()
							.indexOf(CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR) + 1);
			transitionRule.setSecFieldName(transitionRule.getFieldName().substring(
					transitionRule.getFieldName().indexOf(CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR)
							+ 1));
			transitionRule.setSecFieldNameDesc(
					lookupMap.get(key) != null ? lookupMap.get(key) : transitionRule.getSecFieldName());
			key = CommonConstants.FIELD_EX + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR
					+ transitionRule.getFieldName().substring(0, transitionRule.getFieldName()
							.indexOf(CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR));
			transitionRule.setFieldName(transitionRule.getFieldName().substring(0,
					transitionRule.getFieldName().indexOf(CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR)));
			transitionRule.setFieldNameDesc(
					lookupMap.get(key) != null ? lookupMap.get(key) : transitionRule.getFieldName());
		} else {
			key = CommonConstants.FIELD_EX + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR
					+ transitionRule.getFieldName();
			transitionRule.setFieldNameDesc(
					lookupMap.get(key) != null ? lookupMap.get(key) : transitionRule.getFieldName());
		}
	}

	private void setReqStateDesc(DisputeTransitionDTO transitionRule) {
		if (StringUtils.equals(transitionRule.getRequestState(), CommonConstants.RECORD_REJECTED)) {
			transitionRule.setReqStateDesc(CommonConstants.RECORD_DESC_REJECTED);
		} else if (StringUtils.equals(transitionRule.getRequestState(), CommonConstants.RECORD_APPROVED)) {
			transitionRule.setReqStateDesc(CommonConstants.RECORD_DESC_APPROVED);
		} else {
			transitionRule.setReqStateDesc(CommonConstants.RECORD_PENDING_FOR_APPROVAL);
		}
	}
	
	private String[] getRelOpAndDesc(Map<String, String> lookupMap, DisputeTransitionDTO transitionRule) {
		String relOp;
		String relOpDesc;
		if (lookupMap.get(CommonConstants.REL_OP_CMPLX + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR
				+ transitionRule.getRelOp() + CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_NAME) != null) {
			relOp = transitionRule.getRelOp() + CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_NAME;
			relOpDesc = lookupMap
					.get(CommonConstants.REL_OP_CMPLX + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR
							+ transitionRule.getRelOp() + CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_NAME);
		} else if (lookupMap.get(CommonConstants.REL_OP_CMPLX + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR
				+ transitionRule.getRelOp() + CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_OPER) != null) {
			relOp = transitionRule.getRelOp() + CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_OPER;
			relOpDesc = lookupMap
					.get(CommonConstants.REL_OP_CMPLX + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR
							+ transitionRule.getRelOp() + CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_OPER);
		} else {
			relOp = CommonConstants.REL_OPR + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR
					+ transitionRule.getRelOp();
			relOpDesc = lookupMap.get(relOp);
			relOp = transitionRule.getRelOp();
		}
		return new String[]{relOpDesc, relOp};
		
		
	}

	private String getOperator(List<DisputeTransitionDTO> fetchTransitionList, DisputeTransitionDTO transitionRule) {
		String operator;
		if (fetchTransitionList.indexOf(transitionRule) < fetchTransitionList.size() - 1) {
			operator = getOperator(transitionRule.getToState(),
					fetchTransitionList.get(fetchTransitionList.indexOf(transitionRule) + 1).getToState(),
					transitionRule.getEntity(),
					fetchTransitionList.get(fetchTransitionList.indexOf(transitionRule) + 1).getEntity(),
					transitionRule.getCurrState(), fetchTransitionList
							.get(fetchTransitionList.indexOf(transitionRule) + 1).getCurrState());
		} else if (fetchTransitionList.size() > 1
				&& fetchTransitionList.indexOf(transitionRule) == fetchTransitionList.size() - 1) {
			operator = getOperator(transitionRule.getToState(),
					fetchTransitionList.get(fetchTransitionList.indexOf(transitionRule) - 1).getToState(),
					transitionRule.getEntity(),
					fetchTransitionList.get(fetchTransitionList.indexOf(transitionRule) - 1).getEntity(),
					transitionRule.getCurrState(), fetchTransitionList
							.get(fetchTransitionList.indexOf(transitionRule) - 1).getCurrState());
		} else {
			operator = CommonConstants.DISPUTE_TRANS_OP_NONE;
		}
		return operator;
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public String addTransitionRule(List<DisputeTransitionDTO> transitionDTO) {
		try {
			List<DisputeTransitionDTO> fetchTransitionList = disputeRepository
					.getPendingTransitionList(CommonConstants.RECORD_APPROVED, CommonConstants.RECORD_PENDING);
			String currState = "";
			for (DisputeTransitionDTO disputeObj : transitionDTO) {
				String prevState = disputeObj.getLogToState();
				String prevOperator = disputeObj.getOperator();
				String prevEntity = disputeObj.getEntity();
				String prevCurrState = disputeObj.getCurrState();
				if (transitionDTO.indexOf(disputeObj) >= 1) {
					prevState = transitionDTO.get(transitionDTO.indexOf(disputeObj) - 1).getToState();
					prevOperator = transitionDTO.get(transitionDTO.indexOf(disputeObj) - 1).getOperator();
					prevEntity = transitionDTO.get(transitionDTO.indexOf(disputeObj) - 1).getEntity();
					prevCurrState = transitionDTO.get(transitionDTO.indexOf(disputeObj) - 1).getCurrState();
				}
				if (StringUtils.isBlank(currState)) {
					currState = prevState;
				}
				currState = getToState(
						disputeObj.getEntity() + CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR + prevEntity,
						disputeObj.getCurrState() + CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR + prevCurrState,
						disputeObj.getToState(), disputeObj.getOperator(), currState, prevOperator,
						fetchTransitionList);
				disputeObj.setToState(currState);
				if (StringUtils.endsWith(disputeObj.getRelOp(), CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_NAME)) {
					disputeObj.setFieldName(disputeObj.getFieldName() + CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR
							+ disputeObj.getSecFieldName());
				}
				if (StringUtils.endsWith(disputeObj.getRelOp(), CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_NAME)
						|| StringUtils.endsWith(disputeObj.getRelOp(),
								CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_OPER)) {
					disputeObj.setRelOp(disputeObj.getRelOp().substring(0, disputeObj.getRelOp().length() - 2));
				}
				if (StringUtils.equals(disputeObj.getFieldOperator(), CommonConstants.DROPDOWN_SELECT)) {
					disputeObj.setFieldOperator("");
				}
				disputeObj.setCreatedBy(sessionDTO.getUserName());
				disputeObj.setCreatedOn(LocalDateTime.now());
				disputeObj.setLastUpdatedBy(sessionDTO.getUserName());
				disputeObj.setLastUpdatedOn(LocalDateTime.now());
				disputeObj.setRequestState(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
				disputeRepository.addTransition(disputeObj);
			}
		} catch (Exception e) {
			log.error("Error while adding transition rule: ", e);
			return CommonConstants.RESULT_ERROR;
		} finally {
			index = 1;
			dbToState = "";
		}
		return CommonConstants.RESULT_SUCCESS;
	}

	private String getToState(String entity, String currState, String toState, String operator, String prevState,
			String prevOperator, List<DisputeTransitionDTO> transitionList) {
		boolean fromTable = false;
		String[] entList = StringUtils.split(entity, CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR);
		String[] currStateList = StringUtils.split(currState, CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR);
		entity = entList[0];
		currState = currStateList[0];
		String prevEntity = entList.length > 1 ? entList[1] : "";
		String prevCurrState = currStateList.length > 1 ? currStateList[1] : "";
		for (DisputeTransitionDTO tranObj : transitionList) {
			if (StringUtils.equals(tranObj.getEntity(), entity) && StringUtils.equals(tranObj.getCurrState(), currState)
					&& StringUtils.equals(tranObj.getLogToState(), toState)
					&& Boolean.FALSE.equals(StringUtils.startsWith(dbToState, toState))) {
				prevState = tranObj.getToState();
				prevOperator = getPrevOperator(transitionList, tranObj);
				fromTable = true;
				dbToState = prevState;
			}
		}
		String[] splitArr = StringUtils.split(prevState, CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR);
		String result = toState;
		if (splitArr.length > 1 && StringUtils.equals(operator, prevOperator)
				&& StringUtils.equals(operator, CommonConstants.DISPUTE_TRANS_OP_AND)
				&& Boolean.FALSE.equals(fromTable)) {
			index = Integer.parseInt(splitArr[1]);
		}
		index = getIndex(operator, prevOperator, fromTable, splitArr);
		if (splitArr.length > 0
				&& (Boolean.FALSE.equals(StringUtils.equals(toState, splitArr[0]))
						|| Boolean.FALSE.equals(StringUtils.equals(entity, prevEntity))
						|| Boolean.FALSE.equals(StringUtils.equals(currState, prevCurrState)))
				&& Boolean.FALSE.equals(fromTable)) {
			index = 1;
		}
		if (StringUtils.equals(operator, CommonConstants.DISPUTE_TRANS_OP_AND)
				|| StringUtils.equals(operator, CommonConstants.DISPUTE_TRANS_OP_OR)) {
			result = toState + CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR + index;
			index++;
		}
		return result;
	}

	private int getIndex(String operator, String prevOperator, boolean fromTable, String[] splitArr) {
		if (splitArr.length > 1 && fromTable
				&& Boolean.FALSE.equals(StringUtils.equals(operator, CommonConstants.DISPUTE_TRANS_OP_NONE))) {
			index = (StringUtils.equals(operator, prevOperator)
					&& StringUtils.equals(operator, CommonConstants.DISPUTE_TRANS_OP_AND))
							? Integer.parseInt(splitArr[1])
							: Integer.parseInt(splitArr[1]) + 1;
		}
		return index;
	}

	private String getPrevOperator(List<DisputeTransitionDTO> transitionList, DisputeTransitionDTO tranObj) {
		String prevOperator;
		prevOperator = transitionList.indexOf(tranObj) >= 1 && StringUtils.equals(
				transitionList.get(transitionList.indexOf(tranObj) - 1).getToState(), tranObj.getToState())
						? CommonConstants.DISPUTE_TRANS_OP_AND
						: CommonConstants.DISPUTE_TRANS_OP_OR;
		return prevOperator;
	}

	public Map<String, String> actionListAsMap(List<ActionCodeDTO> actionList) {
		Map<String, String> recordsMap = new HashMap<>();
		for (ActionCodeDTO action : actionList) {
			recordsMap.put(action.getActionCode(), action.getActionCodeDesc());
		}
		return recordsMap;
	}

	private String getOperator(String toState, String nextToState, String entity, String nextEntity, String currState,
			String nextCurrState) {
		String result = currOperator;
		if (StringUtils.isNotBlank(result)) {
			currOperator = "";
			return result;
		} else if (StringUtils.equals(toState, nextToState) && StringUtils.equals(entity, nextEntity)
				&& StringUtils.equals(currState, nextCurrState)
				&& StringUtils.contains(toState, CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR)) {
			result = CommonConstants.DISPUTE_TRANS_OP_AND;
			currOperator = CommonConstants.DISPUTE_TRANS_OP_AND;
		} else if (StringUtils.contains(toState, CommonConstants.DISPUTE_TRANS_KEY_SEPERATOR)) {
			result = CommonConstants.DISPUTE_TRANS_OP_OR;
		} else {
			result = CommonConstants.DISPUTE_TRANS_OP_NONE;
		}
		return result;
	}

	public DisputeTransitionDTO getTransitionObj(String id) {
		if (Boolean.FALSE.equals(fullTransitionList.isEmpty())) {
			for (DisputeTransitionDTO dispObj : fullTransitionList) {
				if (dispObj.getId() == Integer.parseInt(id)) {
					return dispObj;
				}
			}
		} else {
			log.error("Dispute Transition Rule List is Empty");
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public String editTransitionRule(List<DisputeTransitionDTO> transitionDTO) {
		try {
			for (DisputeTransitionDTO disputeObject : transitionDTO) {
				if (StringUtils.endsWith(disputeObject.getRelOp(), CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_NAME)) {
					disputeObject.setFieldName(disputeObject.getFieldName() + CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR
							+ disputeObject.getSecFieldName());
				}
				if (StringUtils.endsWith(disputeObject.getRelOp(), CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_NAME)
						|| StringUtils.endsWith(disputeObject.getRelOp(),
								CommonConstants.DISPUTE_TRANS_RELOP_CMPLX_OPER)) {
					disputeObject.setRelOp(disputeObject.getRelOp().substring(0, disputeObject.getRelOp().length() - 2));
				}
				if (StringUtils.equals(disputeObject.getFieldOperator(), CommonConstants.DROPDOWN_SELECT)) {
					disputeObject.setFieldOperator("");
				}
				disputeObject.setLastUpdatedBy(sessionDTO.getUserName());
				disputeObject.setLastUpdatedOn(LocalDateTime.now());
				disputeObject.setRequestState(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
				disputeRepository.editTransition(disputeObject);
			}
		} catch (Exception e) {
			log.error("Error while editing transition rule: ", e);
			return CommonConstants.RESULT_ERROR;
		}
		return CommonConstants.RESULT_SUCCESS;
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public String updateStgTransitionRule(String tranIDList, String status, String comments) {
		try {
			String checkerComments = CommonConstants.RECORD_DESC_REJECTED;
			String reqState = CommonConstants.RECORD_REJECTED;
			String userName = sessionDTO.getUserName();
			if (StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
				checkerComments = CommonConstants.RECORD_DESC_APPROVED;
				reqState = CommonConstants.RECORD_APPROVED;
			}
			if (StringUtils.isNotBlank(comments)) {
				checkerComments = comments;
			}
			String[] tranArray = tranIDList.split("\\|");
			String tranID = String.join(CommonConstants.DISPUTE_TRANS_FIELD_SEPERATOR, tranArray);
			List<DisputeTransitionDTO> disputeList = disputeRepository.getTransitionRulesInId(tranID);
			List<Integer> seqIdList = disputeRepository.getSeqID();
			for (DisputeTransitionDTO disputeTransitionDTO : disputeList) {
				disputeTransitionDTO.setRequestState(reqState);
				disputeTransitionDTO.setLastUpdatedBy(userName);
				disputeTransitionDTO.setLastUpdatedOn(LocalDateTime.now());
				disputeTransitionDTO.setCheckerComments(checkerComments);
				disputeRepository.updateTransitionById(disputeTransitionDTO);
				if (Boolean.FALSE.equals(seqIdList.isEmpty()) && seqIdList.contains(disputeTransitionDTO.getId())
						&& StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
					disputeRepository.updateTransRule(disputeTransitionDTO);
				} else if (Boolean.FALSE.equals(seqIdList.isEmpty())
						&& !seqIdList.contains(disputeTransitionDTO.getId())
						&& StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
					disputeRepository.addTransitionInMaster(disputeTransitionDTO);
				}
			}
		} catch (Exception e) {
			log.error("Error while approving/rejecting transition rule: ", e);
			return CommonConstants.RESULT_ERROR;
		}
		return CommonConstants.RESULT_SUCCESS;
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public String discardTransRule(String id) {
		try {
			DisputeTransitionDTO disputeDTO = disputeRepository.getTransitionById(Integer.parseInt(id));
			if (disputeDTO != null) {
				disputeDTO.setRequestState(CommonConstants.RECORD_APPROVED);
				disputeDTO.setLastUpdatedBy(sessionDTO.getUserName());
				disputeDTO.setLastUpdatedOn(LocalDateTime.now());
				disputeDTO.setCheckerComments(CommonConstants.RECORD_DESC_APPROVED);
				disputeRepository.editTransition(disputeDTO);
			} else {
				disputeRepository.deleteRule(Integer.parseInt(id));
			}
		} catch (Exception e) {
			log.error("Error while discarding transition rule: ", e);
			return CommonConstants.RESULT_ERROR;
		}

		return CommonConstants.RESULT_SUCCESS;
	}

}
