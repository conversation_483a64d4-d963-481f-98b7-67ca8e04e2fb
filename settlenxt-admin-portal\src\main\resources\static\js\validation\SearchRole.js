$(document).ready(function() {
 	roleIds=[];
	
	/* Initialization of datatables */
	$(document).ready(function () {
    	
    $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                //If first column to be skipped to include the filter for the reasons line check box 
                if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                   handleInput(colIdx, cell, title, api);
                   }
                });
            $('#tabnew_filter').hide();
           
        },

        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 

            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Roles',
                header: 'false',
                title: null,
                sheetName: 'Roles',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'Roles' ,
				header:'false', 
				title: null,
				sheetName:'Roles',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });
});	
   


       
   
	
	 
	$('#button').click(function() {
		table.row('.selected').remove().draw(false);
	});
	
	 $("#excelExport").on("click", function () {
	        $(".buttons-excel").trigger("click");
	    });
	    
	 $("#csvExport").on("click", function () {
            $(".buttons-csv").trigger("click");
        });   
	 
	     $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
	    
	    
	    
	    
	

	    $("#selectAll").click(function(){
	    	
	    	 $('#jqueryError5').hide();
        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
      
        var InfoData = document.getElementById("Info");
       
        if(roleIds.length>0){
	InfoData.innerHTML = roleIds.length+"     "+"records are selected";
	
	if( $('#selectAll').is(':checked') ){
 $("#toggleModal4").modal('show');
        
}
else{
   $("#toggleModal4").modal('hide');
        
}}
else{
var i=0;
var roleId2=[];
 $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
		                    roleId2.push(this.value);
		                    i++;
		                });
if(roleId2.length>0){


if(roleIdpending.length>0){
	InfoData.innerHTML = roleIdpending.length+"     "+"records are selected";
	
	if( $('#selectAll').is(':checked') ){
 $("#toggleModal4").modal('show');
        
}
else{
   $("#toggleModal4").modal('hide');
        
}
}
}}
		
});
	
	
		
});



function handleInput(colIdx, cell, title, api) {
	var cursorPosition =null;
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function (_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )
                    .draw();
                roleIds = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
                        roleIds.push(this.value);
                        i++;
                    });
                }
                else {
                    roleIds = [];
                }
            })
            .on('click', function (e) {
                e.stopPropagation();
            })
            .on('keyup', function (e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    } else {
        $(cell).html(title + '<br> &nbsp;');
    }
    
}

function mySelect(){
	
	$('#jqueryError4').hide();
	
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	    
	 
	  var footerDataHeader = document.getElementById("Info");
	  if(array.length>0){
	 if(array.length==roleIdpending.length){
		 $('#selectAll').prop('checked', true);
		 
			footerDataHeader.innerHTML = roleIdpending.length+"     "+"records are selected";
			 $("#toggleModal4").modal('show');
	 }
	 else{
		 $("#toggleModal4").modal('hide');
		 
	 }
	  }
}


function viewRole(roleId, type) {
let url='';
	if (type == 'V')
		url = '/viewRole';
	else if (type == 'P')
		url = '/getPendingRole';
		var data = "rid," + roleId + ",viewType," + type ;
	postData(url, data);
}

function submitForm(url) {
		var data =  "userType," + $('#userType').val();
	postData(url, data);
}


function getRoleList() {
	$('#userType').val($('#userType').val());
	var url = "/showRole";
	var data =  "userType,"
		+ $('#userType').val();
	postData(url, data);
}

function getPendingRoleList() {
	$('#userType').val($('#userType').val());
	let url = '/rolePendingForApproval';
	var data =  "userType,"
		+ $('#userType').val();
	postData(url, data);
}

function deselectAll() {

	 $('#jqueryError5').hide();
	 
	 let i="";

   $('#selectAll').prop('checked', false);
         var ele=document.getElementsByName('type');  
   for( i=0; i<ele.length; i++){  
       if(ele[i].type=='checkbox')  
           ele[i].checked=false;  
   }
   
   $('#selectAll1').prop('checked', false);
          ele=document.getElementsByName('type');  
   for( i=0; i<ele.length; i++){  
       if(ele[i].type=='checkbox')  
           ele[i].checked=false;  
   }
   
}




function ApproveorRejectBulk(type,action){
	
	 
	
	var url = '/approveBulkRoleStatus';
	
	
	 var array = [];
		if(action=='no'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	   }
	   else if(action=='All'){
	   
	   		if(roleIds.length>0){
	   array= roleIds;
	   		}else{
	   		array=roleIdpending;}
	   		
	   }
		
		
  if(array.length>0){
			  
			  
			  
			  $('#jqueryError5').hide();
	 
	 let data="";
	var LoginIdList = "";
		for ( var i of array) {
			LoginIdList = LoginIdList + i + "|"
					;
		}
	if(type=='A'){
		
		 data =  "status,"+"A"+",roleIdList,"+LoginIdList+",remarks,"+"Approved"+",userType," + $('#userType').val();
	}
	 if(type=='R'){
		
		 data =  "status,"+"R"+",roleIdList,"+LoginIdList+",remarks,"+"Rejected"+",userType," + $('#userType').val();
		
	}
	
		postData(url, data);
	}
else{



	  $('#errorStatus5').html('Please Select  Atleast One Record');
		$('#jqueryError5').show();
}
}

