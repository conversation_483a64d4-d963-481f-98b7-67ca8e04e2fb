spring.mvc.static-path-pattern=/static/**
server.servlet.context-path=/SNxtAdminPortal
server.port=6001
spring.aop.auto = true
spring.banner.location=settlenxt_adminportal_banner.txt
spring.mvc.format.date=yyyy-MM-dd

application.version=V1.0

spring.profiles.active=dev

#Ping interval in millisecs
settlenxt.ui.ping.interval=100000

#Idle Time Interval in minutes
settlenxt.ui.idleTime.interval=15
settlenxt.ui.idleTime.passwordPrompt.interval=17



#session time out period in seconds . Do not append 's' or 'm' as it being converted to number in custom session handling
server.servlet.session.timeout=1200
#Session security settings
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=true
server.servlet.session.tracking-modes=cookie
server.servlet.jsp.init-parameters.mappedfile=false
server.servlet.session.cookie.same-site=strict

#logging.level.org.npci.settlenxt.adminportal=DEBUG


 
#logging.file.name=adminportal.log

#logging.file.path=D:/logs/settlenxt
 
# Logging pattern for the console
#logging.pattern.console=%d{yyyy-MM-dd HH:mm:ssSSS} [%thread] %-5level %logger{36} - %msg%n
 
# Logging pattern for file
#logging.pattern.file=%d{yyyy-MM-dd HH:mm:ssSSS} [%thread] %-5level %logger{36} - %msg%


available.option.api=http://127.0.0.1:9309/settlenxt/possibleaction
reasonCode.option.api=http://127.0.0.1:9309/settlenxt/possiblereasoncodes
capAmount.fetch.api=http://127.0.0.1:9309/settlenxt/gettip
submember.bank.type=M
sponsor.bank.type=S
network.bank.type=P

sub.network.default=RPY

MAIN_LOC_PATH=/npci/guiserver/portal/bankportal/

DISPUTE_FILE_UPLOAD_PATH=D:/uploads/files/
DISPUTE_FILE_UPLOAD_STAGE_PATH=D:/uploads/stage/files
FILE_CLASSPATH=D:/uploads/
NPCIVersion=01.00
POS01=00
FILE_ENV_TYPE=T
PROCESS_ENCRYPTED_FILE=N

FILE_VALIDATION_BATCH_TIME_CRON=0 */1 * * * *
FileValidationThreadStart=true
CashbackFileValidation=true
FILE_UPLOAD_VALID_TIME_DURATION=30


#kafka Properties
kafka.bootstrap.servers = 127.0.0.1:9092
kafka.client.id = settlenxtIntegrityChecker
kafka.topic.fileUpload = FileProcessor
kafka.topic.disputeTxn = disputeTxn
kafka.topic.jcbFileTopic = jcbFileTopic
kafka.topic.dispute_acknowledge = multiLegAcknowledgement
kafka.publisher.compression.type = none
kafka.topic.fileValidate = FileValidator
kafka.topic.currencyFileUpload = CurrencyRateFileTopic

kafka.consumer.poll.interval = 1000
kafka.max.poll.records = 10
kafka.consumer.count = 2
kafka.session.timeout.ms = 15000
kafka.key.deserializer = org.apache.kafka.common.serialization.StringDeserializer
kafka.value.deserializer = org.apache.kafka.common.serialization.StringDeserializer
kafka.auto.commit = false
kafka.auto.commit.inteval.ms = 1000

#topic to publish fcfd
kafka.topic.fcfd=fcfd-entry

SITE_ID=01
INSTANCE_ID=01

DISPUTE_DOCUMENT_UPLOAD_PATH=D:/dispute/files/

CASHBACK_FILE_UPLOAD_PATH=D:/uploads/cashbackFiles/
IV_PARAM_SPEC_STRING=****************
PORTAL_TYPE=ADMIN
PRODUCT=RPY

is.international.enabled=Y
bankType.network.code=P

MEM_TXT_MSG_REGEX = ^[a-zA-Z0-9\\[\\]\"!@#$%*()_+{}`^:|<>?~ .,-;=\\\\:&apos;]+$