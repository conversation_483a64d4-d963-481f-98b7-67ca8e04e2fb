package org.npci.settlenxt.adminportal.common.mapping;

import java.io.File;
import java.util.Collections;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.npci.settlenxt.adminportal.common.util.TagMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Configuration
@PropertySource("/application.properties")
public class CustomMappingLoader extends NameMappingLoader {
	
	@Autowired
	private static Environment environment;
		
	private static String messageFormatFile;
	private static String messageFormatNpciMakerFile;
	private static String dataElementsFile;
	private static String fileTypes;
	private static String tagMappingsFile;
	private static String fileLocation = "/npci/guiserver/portal/adminportal/";

	@PostConstruct
	public static void init() {
		String mainLocPath;
		if (environment == null) {
			log.info("environment is empty");
			mainLocPath = fileLocation;
		} else {
			mainLocPath = environment.getProperty("FILE_CLASSPATH");
			log.info("classpath location: {}", mainLocPath);
		}
		messageFormatFile = mainLocPath + "mappings/NPCI/messageformats.json";
		messageFormatNpciMakerFile = mainLocPath + "mappings/NPCI/messageformatsNPCIMaker.json";
		dataElementsFile = mainLocPath + "mappings/NPCI/dataelements.json";
		fileTypes = mainLocPath + "mappings/NPCI/filetypes.json";
		tagMappingsFile = mainLocPath + "mappings/NPCI/tags_transactions.json";
	}
	
	private static Map<NameMappingContext, List<MessageFormatNameMapping>> messageFormatMappings;
	
	private static Map<NameMappingContext, List<MessageFormatNameMapping>> messageFormatNpciMakerMappings;
	
	private static Map<NameMappingContext, List<DataElementNameMapping>> dataElementMappings;
	
	private static Map<NameMappingContext, List<FileTypeNameMapping>> fileTypeMappings;
	

	private static Map<NameMappingContext, List<TagMapping>> messageFormatTagMappings;
	
	
	public static List<MessageFormatNameMapping> getMessageFormatMappings(NameMappingContext context) {
		synchronized (CustomMappingLoader.class) {
		if (messageFormatMappings == null) {
			messageFormatMappings = new EnumMap<>(NameMappingContext.class);
		}}
		List<MessageFormatNameMapping> messageFormatList;
		if (messageFormatMappings.get(context) == null) {
			
			ObjectMapper mapper = new ObjectMapper();

			File file = getCustomMappingFile(messageFormatFile);

			try {
				
				messageFormatList = mapper.readValue(file, new TypeReference<List<MessageFormatNameMapping>>(){});
				
			} catch (Exception e) {
				log.error("Error while getMessageFormatMappings: ", e);
				return Collections.emptyList();
			} 
			messageFormatMappings.put(context, messageFormatList);
			log.info("Message format mappings context: {}", context.getContext());
			log.info("Message format mappings context list size: {}", messageFormatList.size());
		}
	
		return messageFormatMappings.get(context);
	}
	
	public static List<MessageFormatNameMapping> getMessageFormatNpciMakerMappings(NameMappingContext context) {
		synchronized (CustomMappingLoader.class) {
		if (messageFormatNpciMakerMappings == null) {
			messageFormatNpciMakerMappings = new EnumMap<>(NameMappingContext.class);
		}}
		List<MessageFormatNameMapping> messageFormatList;
		if (messageFormatNpciMakerMappings.get(context) == null) {

			ObjectMapper mapper = new ObjectMapper();

			File file = getCustomMappingFile(messageFormatNpciMakerFile);

			try {

				messageFormatList = mapper.readValue(file, new TypeReference<List<MessageFormatNameMapping>>() {
				});

			} catch (Exception e) {
				log.error("Error while getMessageFormatNpciMakerMappings: ", e);
				return Collections.emptyList();
			}
			messageFormatNpciMakerMappings.put(context, messageFormatList);
		}

		return messageFormatNpciMakerMappings.get(context);
	}

	public static List<TagMapping> getTagMappings(NameMappingContext context) {
		synchronized (CustomMappingLoader.class) {
		if (messageFormatTagMappings == null) {
			messageFormatTagMappings = new EnumMap<>(NameMappingContext.class);
		}}
		List<TagMapping> messageFormatListForTags;
		if (messageFormatTagMappings.get(context) == null) {
			
			ObjectMapper mapper = new ObjectMapper();

			File file = getCustomMappingFile(tagMappingsFile);

			try {
				
				messageFormatListForTags = mapper.readValue(file, new TypeReference<List<TagMapping>>(){});
				
			} catch (Exception e) {
				log.error("Error while getTagMappings: ", e);
				return Collections.emptyList();
			} 
			messageFormatTagMappings.put(context, messageFormatListForTags);
		}
	
		return messageFormatTagMappings.get(context);
	}
	
	public static List<DataElementNameMapping> getDataElementNameMappings(NameMappingContext context) {
		synchronized (CustomMappingLoader.class) {
		if (dataElementMappings == null) {
			dataElementMappings = new EnumMap<>(NameMappingContext.class);
		}}
		List<DataElementNameMapping> dataElementList;
		if (dataElementMappings.get(context) == null) {
			
			ObjectMapper mapper = new ObjectMapper();

			File file = getCustomMappingFile(dataElementsFile);

			try {
				
				dataElementList = mapper.readValue(file, new TypeReference<List<DataElementNameMapping>>(){});
				
			}  catch (Exception e) {
				log.error("Error while getDataElementNameMappings: ", e);
				return Collections.emptyList();
			}
			dataElementMappings.put(context, dataElementList);
		}
	
		return dataElementMappings.get(context);
	}
	
	public static List<FileTypeNameMapping> getFileTypeMappings(NameMappingContext context) {
		synchronized (CustomMappingLoader.class) {
		if (fileTypeMappings == null) {
			fileTypeMappings = new EnumMap<>(NameMappingContext.class);
		}}
		List<FileTypeNameMapping> fileTypeList;
		if (fileTypeMappings.get(context) == null) {
			
			ObjectMapper mapper = new ObjectMapper();

			File file = getCustomMappingFile(fileTypes);

			try {
				
				fileTypeList = mapper.readValue(file, new TypeReference<List<FileTypeNameMapping>>(){});
				
			} catch (Exception e) {
				log.error("Error while getFileTypeMappings: ", e);
				return Collections.emptyList();
			}
			fileTypeMappings.put(context, fileTypeList);
		}
	
		return fileTypeMappings.get(context);
	}
	
	public static void main(String[] args) {
		new CustomMappingLoader();
		getMessageFormatMappings(NameMappingContext.MESSAGEFORMAT);
		getMessageFormatNpciMakerMappings(NameMappingContext.MESSAGEFORMAT_NPCIMAKER);
		getDataElementNameMappings(NameMappingContext.DATAELEMENTS);
		getDataElementNameMappings(NameMappingContext.FILETYPES);
		getTagMappings(NameMappingContext.TAGGED);
	}
}
