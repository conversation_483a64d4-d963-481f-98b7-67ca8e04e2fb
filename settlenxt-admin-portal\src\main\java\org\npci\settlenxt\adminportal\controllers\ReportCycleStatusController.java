package org.npci.settlenxt.adminportal.controllers;

import org.npci.settlenxt.adminportal.dto.CycleManagementDTO;
import org.npci.settlenxt.adminportal.service.IReportInternalCycleStatusService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * This controller class is used for report cycle status of UI screens
 * 
 * <AUTHOR>
 *
 */
@Controller
public class ReportCycleStatusController extends BaseController {

	@Autowired
	IReportInternalCycleStatusService reportInternalCycleStatusService;
	public static final String REPORT_INT_STATUS="reportInternalCycleStatus";
	public static final String CYCLE_MAN_DTO="cycleManagementDTO";

	/**
	 * This method is used to getting report internal cycle status
	 * 
	 * @param cycleManagementDTO
	 * @param model
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/reportInternalCycleStatus")
	public String reportInternalCycleStatus(@ModelAttribute("cycleManagementDTO") CycleManagementDTO cycleManagementDTO,
			Model model) throws SettleNxtException {
		cycleManagementDTO = reportInternalCycleStatusService.getReportInternalCycleStatus(cycleManagementDTO);
		model.addAttribute(CYCLE_MAN_DTO, cycleManagementDTO);
		return getView(model, REPORT_INT_STATUS);
	}

	/**
	 * Update the report cycle status for retry, force close and force merge
	 * 
	 * @param cycleManagementDTO
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/updateInternalCycle")
	@ResponseBody
	public String updateInternalCycleStatus(@RequestBody CycleManagementDTO cycleManagementDTO) throws SettleNxtException {
		return reportInternalCycleStatusService.updateCycleStatus(cycleManagementDTO);
	}

}
