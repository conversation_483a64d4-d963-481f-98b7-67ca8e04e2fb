package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.dto.IncomingTransactionDetailDTO;
import org.npci.settlenxt.portal.common.service.BaseIncomingTransactionDetailService;

public interface IncomingTransactionDetailService extends BaseIncomingTransactionDetailService{

	List<IncomingTransactionDetailDTO> parseJsonToTransactionListChecker(List<IncomingTransactionDetailDTO> disputeList,
			String jsonString);

	List<IncomingTransactionDetailDTO> parseJsonToTransactionList(List<IncomingTransactionDetailDTO> disputeList,
			String jsonString);
}
