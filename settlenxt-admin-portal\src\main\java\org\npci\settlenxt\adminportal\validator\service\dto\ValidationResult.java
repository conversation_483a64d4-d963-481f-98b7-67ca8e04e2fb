package org.npci.settlenxt.adminportal.validator.service.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.ToString;

@Data
@AllArgsConstructor
@JGlobalMap
@ToString
public class ValidationResult {
	
	private int fileId;
	private String fileName;
	private String filePath;
	private long fileSize;
	private String participantId;
	private int invalidRecordCount;
	private int validRecordCount = 0;
	private int totalRecordCount = 0;
	private BigDecimal totalAmount;
	private String fileDate;
	private String version;
	private String headerSettlementBin;
	private String rgcsRecievedDate;
	private boolean fullFileReject;
	private boolean fileStageStatus;
	
	private String xmlHeader;
	private String xmlBody;
	private String xmlTrailer;
	
	private List<String> xmlTransactions;
	private String xmlTransactionsWithErrorsWritten;

	private Map<Integer, List<RecordError>> recordErrors;
	private List<FileError> fileErrors;
	
	private Map<Integer, List<Record>> validTxnRecords;
	private Map<Integer, List<Record>> inValidTxnRecords;
	
	private boolean isHeaderRecordValid;
	private boolean isTrailerRecordValid;
	private boolean isFileNameValid;
	private boolean isRecordSeqNoCheckValid;
	private String fileGenDateTime;
	private String fileStatus;
	private String insertDayStr;

	public ValidationResult() {
		super();
		
		recordErrors = new HashMap<>();
		fileErrors = new ArrayList<>();
	}
	
	
	public boolean isValid() {
        return fileErrors.isEmpty() && recordErrors.isEmpty();
    }

	public void addRecordErrors(Map<Integer, List<RecordError>> records) {

		for(Entry<Integer, List<RecordError>> data : records.entrySet()) {
			Integer key = data.getKey();
			for (RecordError error : data.getValue()) {
				this.recordErrors.computeIfAbsent(key,k->new ArrayList<>()).add(error);
		}
		}
	}
	
	public void addRecordError(Integer recordNumber, RecordError error) {
		recordErrors.computeIfAbsent(recordNumber, k-> new ArrayList<>()).add(error);
	}
	
	public void increaseValidRecordCount() {
		validRecordCount++;
	}
	
	public void increaseInvalidRecordCount() {
		validRecordCount++;
	}
	
	
}
