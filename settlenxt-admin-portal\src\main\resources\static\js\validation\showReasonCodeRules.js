var cursorPosition=null;
var reasonCodeRuleIds = [];
$(document).ready(function () {
$(document).ready(function () {
    	
    $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
               //If first column to be skipped to include the filter for the reasons line check box 
               if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                // Set the header cell to contain the input element
                var cell = $('#tabnew thead tr th').eq(
                    $(api.column(colIdx).header()).index()
                );
                var title = $(cell).text();
                searchBoxFunc(colIdx, cell, title, api);
               }
                });
            $('#tabnew_filter').hide();
           
        },
        // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
         "order": [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Reason Code Rules',
                header: 'false',
                title: null,
                sheetName: 'Reason Code Rules',
                className: 'defaultexport',
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'Reason Code Rules' ,
				header:'false', 
				title: null,
				sheetName:'Reason Code Rules',
				className:'defaultexport',
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }
            }

        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true,
    });
});	
   

   
    $("#excelExport").on("click", function () {
        $(".buttons-excel").trigger("click");
    });
 
	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
 
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });
    
    
$("#selectAll").click(function(){
		
		$('#jqueryError4').hide();
        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
      
        var footerDataHeader = document.getElementById("detailsHeadersss");
       if(reasonCodeRuleIds.length>0){
        	footerDataHeader.innerHTML = reasonCodeRuleIds.length+"     "+"records are selected";
        	
        	if( $('#selectAll').is(':checked') ){
         $("#toggleModal").modal('show');
                
        }
        else{
           $("#toggleModal").modal('hide');
                
        }}else{
        var i=0;
        var reasonCodeRuleIds2=[];
         $("#tabnew tr").find("input"+"[type="+"checkbox"+"]"+"[name="+"type"+"]").each(function() {
        	 reasonCodeRuleIds2.push(this.value);
        		                    i++;
        		                });
        		                 if(reasonCodeRuleIds2.length>0){
        if(recordIdpending.length>0){
	footerDataHeader.innerHTML = recordIdpending.length+"     "+"records are selected";
	
	if( $('#selectAll').is(':checked') ){
 $("#toggleModal").modal('show');
        
}
else{
   $("#toggleModal").modal('hide');
        
}}
	}}
});
	    
   
 
});


function searchBoxFunc(colIdx, cell, title, api) {
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function (_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )

                    .draw();
                reasonCodeRuleIds = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
                        reasonCodeRuleIds.push(this.value);
                        i++;
                    });
                }
                else {
                    reasonCodeRuleIds = [];
                }
            })
            .on('click', function (e) {
                e.stopPropagation();
            })
            .on('keyup', function (e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    } else {
        $(cell).html(title + '<br> &nbsp;');
    }
}

function viewAction(seqId, type) {
	if (type == 'V')
		var url = '/viewReasonCodeRules';
	else if (type == 'P')
		url = '/viewPendingReasonCodeRules';
	else if (type == 'R')
		url = '/viewRejPendingReasonCodeRules';
		
	var data = "seqId," + seqId ;
	postData(url, data);
}



function submitForm(url) {
	var data = "userType," + $('#userType').val();
	postData(url, data);
}

function deselectAll() {

	$('#jqueryError4').hide();

   $('#selectAll').prop('checked', false);
         var ele=document.getElementsByName('type');  
   for(let i of ele){  
       if(i.type=='checkbox')  
           i.checked=false;  
   }
   
   $('#selectAll1').prop('checked', false);
         ele=document.getElementsByName('type');  
   for(let i of ele){  
       if(i.type=='checkbox')  
           i.checked=false;  
   }
   
}




function ApproveorRejectBulk(type,action){
	
	 
	var url = '/approveOrRejectBulkReasonCodeRules';
	
	
	 var array = [];
		if(action=='no'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	   
	   

	   }
	   else if(action=='All'){
	   if(reasonCodeRuleIds.length>0){
	   array= reasonCodeRuleIds;
	   		}else{
	   		array= recordIdpending;}
	   		
	   		
	   }
		
		
		  if(array.length>0){
			  
			
			  
			  $('#jqueryError4').hide();
			  
		  
	 
	var idList = "";
		for ( let i of array) {
			idList = idList + i + "|"
					;
		}
		var data="";
	if(type=='A'){
		
		 data =  "status,"+"A"+",idList,"+idList+",remarks,"+"Approved";
	}
	 if(type=='R'){
		
		 data =  "status,"+"R"+",idList,"+idList+",remarks,"+"Rejected";
		
	}
	 
	 
	
	
		postData(url, data);
		
		
		  }
		  else{
			  	$('#snxtSuccessMessage').hide();
			  $('#errorStatus4').html('Please Select  Atleast One Record');
				$('#jqueryError4').show();
			
				
		  }
	}


function mySelect(){
	
	$('#jqueryError4').hide();
	
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	    
	 
	  var footerDataHeader = document.getElementById("detailsHeadersss");
	  if(array.length>0){
	 if(array.length==recordIdpending.length){
		 $('#selectAll').prop('checked', true);
		 
			footerDataHeader.innerHTML = recordIdpending.length+"     "+"records are selected";
			 $("#toggleModal").modal('show');
	 }
	 else{
		 $("#toggleModal").modal('hide');
		 
	 }
	  }
}
