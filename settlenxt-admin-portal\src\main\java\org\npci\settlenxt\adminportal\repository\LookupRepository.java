/**
 * 
 */
package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.portal.common.dto.LookUpDTO;
import org.npci.settlenxt.portal.common.repository.BaseLookupRepository;

@Mapper
public interface LookupRepository extends BaseLookupRepository {

	 List<LookUpDTO> getLookupListMain(@Param("requestStateList") List<String> requestStateList);

	 int insertLookUpList(LookUpDTO lookUpDTO);

	 int insertLookUpListMain(LookUpDTO lookUpDTO);

	 List<LookUpDTO> getPendingLookupListStg(@Param("requestStateList") List<String> requestStateList);

	 List<LookUpDTO> getApprovedLookupListStg(@Param("requestState") String requestState);

	 LookUpDTO getLookUpInfoFromMain(@Param("lookupId") int lookupId);

	 LookUpDTO getLookUpInfoFromStg(@Param("lookupId") int lookupId);

	 LookUpDTO getEditLookUpInfoFromMain(@Param("lookupId") int lookupId);

	 int updateLookUpStg(LookUpDTO lookUpDTO);

	 LookUpDTO approveLookUpFromMain(@Param("lookupId") int lookupId);

	 int updateLookUpReqState(LookUpDTO lookUpDTO);

	 int updateLookUpApproval(LookUpDTO lookUpDTO);

	 int getNextSequenceId();

	 LookUpDTO getLookUpFrmStg(@Param("lookupId") int lookupId);

	 int updateStgLookUp(LookUpDTO lookUpDTO);

	 void deleteDiscardedEntry(@Param("lookupId") int lookupId);

	 List<LookUpDTO> fetchLookUpStgList(@Param("lookUpList") List<Integer> lookUpList);

	 List<LookUpDTO> getLookUpTypeListFrmMain();

	 int checkDupLookUpDetails(LookUpDTO lookUpDTO);

	 int checkDupLookUpDetailsAdd(LookUpDTO lookUpDTO);

	 int checkDupLookUpDetailsMain(LookUpDTO lookUpDTO);

}
