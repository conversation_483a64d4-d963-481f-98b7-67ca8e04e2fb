<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.PenaltyRepository">

	<select id="getApprovedPenaltyData" resultType="penaltyDTO">
		SELECT R.penalty_id as penaltyId, R.penalty_for_modify_upload as penaltyForModifyUpload, R.penalty_for_nonmodify_upload as penaltyForNonModifyUpload,S.request_state as status 
		FROM MCPR_PENALTY_CONFIG R 
		inner join MCPR_PENALTY_CONFIG_STG S on R.penalty_id=S.penalty_id
		order by R.penalty_id limit 1
	</select>
	<select id="getPendingForAppovalPenaltyData" resultType="penaltyDTO">
		SELECT R.penalty_for_modify_upload as penaltyForModifyUpload, R.penalty_for_nonmodify_upload as penaltyForNonModifyUpload,R.status, R.request_state as requestState,R.checker_comments as checkerComments , penalty_id as penaltyId, R.LAST_UPDATED_BY as lastUpdatedBy,R.LAST_UPDATED_ON as lastUpdatedOn, R.CREATED_ON as createdOn, R.CREATED_BY as createdBy, R.LAST_OPERATION as lastOperation FROM MCPR_PENALTY_CONFIG_STG R WHERE (R.REQUEST_STATE='S'  or R.REQUEST_STATE='P'  or R.REQUEST_STATE='R')  ORDER BY penalty_id DESC limit 1
	</select>
	<select id="getPenaltyData" resultType="penaltyDTO">
		SELECT R.penalty_id as penaltyId, R.penalty_for_modify_upload as penaltyForModifyUpload, R.penalty_for_nonmodify_upload as penaltyForNonModifyUpload,R.status as status , REQUEST_STATE as requestState, CHECKER_COMMENTS checkerComments, CREATED_BY as createdBy, CREATED_ON as createdOn, LAST_UPDATED_BY as lastUpdatedBy, LAST_UPDATED_ON as lastUpdatedOn, LAST_OPERATION as lastOperation FROM MCPR_PENALTY_CONFIG_STG as R	WHERE penalty_id= #{penaltyId}
	</select>
	<select id="getPenaltyMainData" resultType="penaltyDTO">
		SELECT R.penalty_id as penaltyId, R.penalty_for_modify_upload as penaltyForModifyUpload, R.penalty_for_nonmodify_upload as penaltyForNonModifyUpload,R.status as status  FROM MCPR_PENALTY_CONFIG as R	WHERE penalty_id= #{penaltyId}
	</select>
	<update id="updatePenaltyStgMaker" >
		UPDATE MCPR_PENALTY_CONFIG_STG SET penalty_for_modify_upload = #{penaltyForModifyUpload}, penalty_for_nonmodify_upload = #{penaltyForNonModifyUpload}, LAST_UPDATED_BY= #{lastUpdatedBy}, LAST_UPDATED_ON= #{lastUpdatedOn}, STATUS= #{requestState},REQUEST_STATE=#{requestState}, CHECKER_COMMENTS=#{checkerComments},LAST_OPERATION='Edit Penalty' WHERE penalty_id = #{penaltyId}
	</update>
	<insert id="savePenaltyStg" >
		insert into MCPR_PENALTY_CONFIG_STG(penalty_id,penalty_for_modify_upload,penalty_for_nonmodify_upload,created_by
			,created_on,last_updated_by,status,LAST_UPDATED_ON,REQUEST_STATE, LAST_OPERATION)  VALUES 
			(#{penaltyId}, #{penaltyForModifyUpload}, #{penaltyForNonModifyUpload} , 
			#{createdBy},#{createdOn},#{lastUpdatedBy}, #{requestState},#{lastUpdatedOn}, 'P', 'Add Penalty' )
	</insert>
	<update id="updatePenaltyMain" >
		UPDATE MCPR_PENALTY_CONFIG SET penalty_for_modify_upload = #{penaltyForModifyUpload}, penalty_for_nonmodify_upload = #{penaltyForNonModifyUpload} WHERE penalty_id in( select penalty_id from MCPR_PENALTY_CONFIG where penalty_id  = #{penaltyId})
	</update>
	<insert id="savePenaltyMain" >
		insert into MCPR_PENALTY_CONFIG(penalty_id,penalty_for_modify_upload,penalty_for_nonmodify_upload,created_by,created_on,last_updated_by,status)  VALUES 
			(#{penaltyId}, #{penaltyForModifyUpload}, #{penaltyForNonModifyUpload}, 
			#{createdBy},#{createdOn},#{lastUpdatedBy}, #{requestState})
	</insert>
	<update id="updatePenaltyRequestState" >
		UPDATE MCPR_PENALTY_CONFIG_STG SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments} WHERE penalty_id  = #{penaltyId}
	</update>
	<delete id="deleteDiscardedEntry" >
		DELETE FROM MCPR_PENALTY_CONFIG_STG 	WHERE penalty_id= #{penaltyId}
	</delete>
	<select id="fetchIdFromPenaltyIdSequence" resultType="int">
		SELECT nextval('penaltyid_seq')
	</select>

</mapper>