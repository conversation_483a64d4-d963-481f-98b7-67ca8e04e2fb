package org.npci.settlenxt.adminportal.config.kafka.consumer;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.npci.settlenxt.portal.common.config.kafka.consumer.BaseConsumerRecordsAction;
import org.springframework.stereotype.Component;

@Component("switchMessageConsumer")
public class SwitchMessageConsumer implements BaseConsumerRecordsAction {


	@Override
	public void processConsumerRecord(ConsumerRecord<String, String> consumerRecord) {
		//do nothing
	}

}
