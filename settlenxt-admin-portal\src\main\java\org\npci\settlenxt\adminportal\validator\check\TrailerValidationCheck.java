package org.npci.settlenxt.adminportal.validator.check;

import java.math.BigDecimal;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.mapping.ReasonCodes;
import org.npci.settlenxt.adminportal.common.util.LoadRejectReasonCode;
import org.npci.settlenxt.adminportal.common.util.ValidationType;
import org.npci.settlenxt.adminportal.common.util.ValidationUtils;
import org.npci.settlenxt.adminportal.config.kafka.utils.Utility;
import org.npci.settlenxt.adminportal.validator.service.dto.FileError;
import org.npci.settlenxt.adminportal.validator.service.dto.HeaderRecord;
import org.npci.settlenxt.adminportal.validator.service.dto.Record;
import org.npci.settlenxt.adminportal.validator.service.dto.RecordError;
import org.npci.settlenxt.adminportal.validator.service.dto.TrailerRecord;
import org.npci.settlenxt.adminportal.validator.service.dto.TransactionRecord;
import org.npci.settlenxt.adminportal.validator.service.dto.ValidationResult;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.util.NumberUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
/**
 * Process files trailer tags validation
 * <AUTHOR>
 *
 */

public class TrailerValidationCheck implements IValidationCheck {
	private TrailerRecord trailerRecord;
	private HeaderRecord headerRecord;
	
	private static final Logger logger = LogManager.getLogger(TrailerValidationCheck.class);
	private static final String NRECNUM = "nRecNum";
	
	public TrailerValidationCheck(TrailerRecord trailerRecord, HeaderRecord headerRecord) {
		this.trailerRecord = trailerRecord;
		this.headerRecord = headerRecord;
	}

	@Override
	public void validate(ValidationResult validationResult, LoadRejectReasonCode rejReasonCode) {
		// Add the logic to validate the trailer data
//		Trailer Validation:
//
//			Number of transactions in File against Trailer Count
//			Run total of transaction amount against trailer run total
//			File Name in Header and File Name in trailer.
//			All Common elements of Header and Trailer should be same.
		
		int noOfTranInBody = headerRecord.getTranCount();
//		
		
		
		List<Record> trailerRecordsList = trailerRecord.getRecords();
		
		List<RecordError> requiredErrors =  ValidationUtils.validateRequiredFields(ValidationType.TRAILER, trailerRecordsList, validationResult.getXmlTrailer(), rejReasonCode);
		
		handleReqError(validationResult, requiredErrors);

		boolean trailerMtiAvailableAndChecked = false;

		for (Record trailRec:trailerRecordsList) {
		   Record trailerElement =  trailRec;
		   ReasonCodes reasonCodes = trailerElement.getMapping().getReasonCodes();
		   boolean validPatternAndLength = true;
			int invalidLength = -1;
			int invalidType = -1;
			if (reasonCodes != null) {
				invalidLength = reasonCodes.getInvalidLength();
				invalidType = reasonCodes.getInvalidType();
			}

			// 3404 is "Invalid Data Input" - so we will use that if there is
			// nothing specifically defined
			invalidLength = setInvalidLen(invalidLength);

			invalidType = setInvalidLen(invalidType);
			
			String currentTrailerElementName = trailerElement.getName();
			boolean recordNumberChecked = false;
			boolean twoSameMessages = false;
			boolean currentElementTypeNumeric = false;
			

			if (!trailerElement.isLengthValid()) {

				if (invalidLength == invalidType) {
					twoSameMessages = true;
				}
				if (NRECNUM.equalsIgnoreCase(currentTrailerElementName)) {
					recordNumberChecked = true;
					invalidLength = 5036;
				}
				
				validationResult.getFileErrors().add(
						new FileError(invalidLength, rejReasonCode
								.getRejectReasonDesc(String
										.valueOf(invalidLength)), currentTrailerElementName));
				validPatternAndLength = false;
			}
			
			
			if (!recordNumberChecked && !twoSameMessages && !trailerElement.isPatternValid()) {
				validPatternAndLength = handleAsPerTypeOfCurrentEle(validationResult, rejReasonCode, trailerElement,
						validPatternAndLength, invalidType, currentTrailerElementName, currentElementTypeNumeric);
			}
		trailerMtiAvailableAndChecked = validPatternAndLen(validationResult, noOfTranInBody,
				trailerMtiAvailableAndChecked, trailerElement, validPatternAndLength, currentTrailerElementName);
		}
		handleTrailerMtiAvl(validationResult, trailerMtiAvailableAndChecked);
		
	}

	private int setInvalidLen(int invalidLength) {
		if (invalidLength == -1) {
			invalidLength = 3404;
		}
		return invalidLength;
	}

	private void handleTrailerMtiAvl(ValidationResult validationResult, boolean trailerMtiAvailableAndChecked) {
		if (!trailerMtiAvailableAndChecked) {
			List<Record> headerRecordsListFor1644 = headerRecord.getRecords();
			for (Record headRec:headerRecordsListFor1644) {
				Record headerElementFor1644 =  headRec;
				String currentHeaderElementName = headerElementFor1644.getName();
				if ("nMTI".equalsIgnoreCase(currentHeaderElementName) && (!"1644".equals(headerElementFor1644.getValue()))) {
					
						validationResult.getFileErrors()
								.add(new FileError(5001, "Invalid MTI", validationResult.getXmlTrailer(), currentHeaderElementName));
						break;
					
				}
			}
		}
	}

	private void handleReqError(ValidationResult validationResult, List<RecordError> requiredErrors) {
		if (!requiredErrors.isEmpty()) {
			for (RecordError error : requiredErrors) {
				validationResult.getFileErrors().add(new FileError(error.getErrorNo(), error.getErrorDescription(), error.getXml(), error.getName()));
			}
		}
	}

	private boolean validPatternAndLen(ValidationResult validationResult, int noOfTranInBody,
			boolean trailerMtiAvailableAndChecked, Record trailerElement, boolean validPatternAndLength,
			String currentTrailerElementName) {
		if(validPatternAndLength){	
		   
		   trailerMtiAvailableAndChecked = handleCurrentTrailerElementName(validationResult, noOfTranInBody,
				trailerMtiAvailableAndChecked, trailerElement, currentTrailerElementName);
			}
		return trailerMtiAvailableAndChecked;
	}

	private boolean handleAsPerTypeOfCurrentEle(ValidationResult validationResult, LoadRejectReasonCode rejReasonCode,
			Record trailerElement, boolean validPatternAndLength, int invalidType, String currentTrailerElementName,
			boolean currentElementTypeNumeric) {
		if (NRECNUM.equalsIgnoreCase(currentTrailerElementName)) {
			invalidType = 5036;
		}
		String typeOfCurrentElement  = trailerElement.getMapping().getDataType();
		if("N".equals(typeOfCurrentElement)){
			try{
				if(StringUtils.isNumeric(trailerElement.getValue())){
					currentElementTypeNumeric = true;
				}
			}catch(Exception ex){
				logger.info(ex.getMessage());
			}
		}
		if(!currentElementTypeNumeric){
		validationResult.getFileErrors().add(
				new FileError(invalidType, rejReasonCode
						.getRejectReasonDesc(String
								.valueOf(invalidType)), currentTrailerElementName));
		validPatternAndLength = false;
		}
		return validPatternAndLength;
	}

	private boolean handleCurrentTrailerElementName(ValidationResult validationResult, int noOfTranInBody,
			boolean trailerMtiAvailableAndChecked, Record trailerElement, String currentTrailerElementName) {
		int trailTranCount;
		int trailerTranRecNum;
		BigDecimal runTotalInTrail;
		if (NRECNUM.equalsIgnoreCase(currentTrailerElementName)) {
			   
			   trailerTranRecNum = Integer.parseInt(trailerElement.getValue());
			   if (trailerTranRecNum != noOfTranInBody+2) {
				   logger.info("{} --- {}" ,trailerTranRecNum ,noOfTranInBody+2);
				   validationResult.getFileErrors().add(new FileError(5025, "Incorrect Record Number", validationResult.getXmlTrailer(), currentTrailerElementName));
			   }
			   
		   }else if ("nTxnCnt".equalsIgnoreCase(currentTrailerElementName)) {
			   
			   trailTranCount = Integer.parseInt(trailerElement.getValue());
			   if (trailTranCount != noOfTranInBody) {
				   validationResult.getFileErrors().add(new FileError(5027, "Transaction Count Not-Matched", validationResult.getXmlTrailer(), currentTrailerElementName));
			   }
			   
		   } else if ("nRnTtlAmt".equalsIgnoreCase(currentTrailerElementName)) {
			   
			   runTotalInTrail = NumberUtils.parseNumber(
						Utility.getStringAmounFromIsoAmount(trailerElement.getValue(), 2), BigDecimal.class);
				validationResult.setTotalAmount(runTotalInTrail);
			   
//		
			   
		   } else if (!"nFunCd".equalsIgnoreCase(currentTrailerElementName) && !NRECNUM.equalsIgnoreCase(currentTrailerElementName)) {
			   
			   List<Record> headerRecordsList = headerRecord.getRecords();
			   for (Record headRec:headerRecordsList) {
				   Record headerElement =  headRec;
				   String currentHeaderElementName = headerElement.getName();
				   if (currentHeaderElementName.equals(currentTrailerElementName) && (!trailerElement.getValue().equals(headerElement.getValue()))) {
					   
						   trailerMtiAvailableAndChecked = handleFileErrors(validationResult,
								trailerMtiAvailableAndChecked, currentTrailerElementName, headerElement);
						   break;
						}
					

				}

			}
		return trailerMtiAvailableAndChecked;
	}

	private boolean handleFileErrors(ValidationResult validationResult, boolean trailerMtiAvailableAndChecked,
			String currentTrailerElementName, Record headerElement) {
		if("nUnFlNm".equalsIgnoreCase(currentTrailerElementName)){
			   validationResult.getFileErrors().add(new FileError(5026, "File name not matched with Header message", validationResult.getXmlTrailer(), currentTrailerElementName));
		   } else if("nMTI".equalsIgnoreCase(currentTrailerElementName)){
			   validationResult.getFileErrors().add(new FileError(5001, "Invalid MTI", validationResult.getXmlTrailer(), currentTrailerElementName));
			   trailerMtiAvailableAndChecked = true;
		   } else{
			   validationResult.getFileErrors().add(new FileError(-1, headerElement.getName() + " does not match the header value", validationResult.getXmlTrailer(), currentTrailerElementName));
		   }
		return trailerMtiAvailableAndChecked;
	}

	@Override
	public void validate(ValidationResult validationResult, List<TransactionRecord> txnRecords) throws SettleNxtException {
//   Not in use for now
		
	}

	@Override
	public void validate(ValidationResult validationResult, List<TransactionRecord> txnRecords, int taskIdsSizeForSplit,
			LoadRejectReasonCode rejReasonCode) throws SettleNxtException {
		// Not in use for now
		
	}
}
