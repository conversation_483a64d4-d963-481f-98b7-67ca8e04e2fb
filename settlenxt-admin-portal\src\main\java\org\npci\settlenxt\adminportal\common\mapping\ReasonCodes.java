package org.npci.settlenxt.adminportal.common.mapping;

public class ReasonCodes {
	private int invalidLength = -1;
	private int invalidType = -1;
	private int missing = -1;
	private int present = -1;
	
	public int getInvalidLength() {
		return invalidLength;
	}
	public void setInvalidLength(int invalidLength) {
		this.invalidLength = invalidLength;
	}
	public int getInvalidType() {
		return invalidType;
	}
	public void setInvalidType(int invalidType) {
		this.invalidType = invalidType;
	}
	public int getMissing() {
		return missing;
	}
	public void setMissing(int missing) {
		this.missing = missing;
	}
	public int getPresent() {
		return present;
	}
	public void setPresent(int present) {
		this.present = present;
	}
}
