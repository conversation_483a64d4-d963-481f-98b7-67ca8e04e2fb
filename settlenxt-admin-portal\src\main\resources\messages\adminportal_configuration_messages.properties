#Configuration Module Messages


#IFSC Configuration screen Messages
ifsc.ifsccode.validation.msg=IFSC code must be 4 character alphabets
ifsc.bankName.validation.msg=Bank Name is mandatory
ifsc.bankCode.validation.msg=Bank code must be 3 digit numeric value
ifsc.nfsCode.validation.msg=NFS code  must be 3 character alphabets
ifsc.savingsAccId.validation.msg=Savings Account ID must be alphanumeric
ifsc.currAccId.validation.msg=Current Account ID must be alphanumeric
ifsc.rtgsAccId.validation.msg=RTGS Code  must be 11 character alphanumeric value

ifsc.updateSuccess.msg=Edited IFSC data has been sent for approval
ifsc.addSuccess.msg=Added IFSC data has been sent for approval
ifsc.approvalSuccess.msg=IFSC data has been approved successfully
ifsc.rejectionSuccess.msg=IFSC data has been rejected successfully
ifsc.discardSuccess.msg=Rejected IFSC entry has been discarded successfully
ifsc.apprejecterror.msg=Please Select Approve/Reject action
ifsc.remarksMandatory.msg=Please Enter Remarks

#Lookup

lookUp.editSuccess.msg=Edited LookUp data has been sent for approval

lookUp.reject.msg=Look Up Data Rejected Successfully
lookup.discard.msg=Look Up Data Discarded Successfully

# ERROR MESSAGE 
ERR_IFSC_EXISTS=IFSC with same ifsc code already exists

##############Rebate Master bhushan 22.06.22##############
rebate.AM_MSG_rebateApproved =Rebate approved successfully.
rebate.AM_MSG_rebateRejectionSuccess =Operation successful.
rebate.rebateApproved=Rebate approved successfully.	
rebate.rebateEntryDiscarded=Rejected Rebate data has been discarded successfully.
rebate.AM_MSG_errorUpdateRebate=Error while approving role.
rebate.AM_MSG_pendingRebate=Request sent for approval.
rebate.AM_MSG_pendingUpdatedRebate=Request sent for approval.
rebate.AM_MSG_errorRebate=ADD REBATE
rebate.AM_MSG_RebateApprovedMailSendingFail=Error sending mail for rebate creation.
rebate.AM_MSG_RebatePending=Rebate is already pending for approval.
rebate.AM_MSG_errorRebateApprove=Error occurred while processing.
rebate.AM_MSG_rebateRejected=Rebate rejected successfully.
rebate.AM_MSG_wfid_Empty=Data is not present for Rebate ID.
rebate.AM_MSG_rebateToCardVariantError=Error occur while inserting Card Variant Data


rebate.cardType.validation.msg=Kindly select Card Type is mandatory
rebate.financialYear.validation.msg=Kindly select Financial Year is mandatory
rebate.featureOrBaseFee.validation.msg=Kindly Feature or base Fee is mandatory
rebate.operatorIndi.validation.msg=Kindly Sign operator is mandatory
rebate.newCardCount1.validation.msg=Kindly enter Card Number range. Kindly enter numeric without decimal between 1 to 1999999999.
rebate.newCardCount2.validation.msg=Kindly enter Card Number range. Kindly enter numeric without decimal between 1 to 1999999999. To range should be greater then From range.
rebate.rebatePercentage.validation.msg=Kindly enter Rebate percentage. Kindly enter numeric between 1 to 100.
rebate.selectedCard.validation.msg=Kindly select Card Variant.
#REBATE Configuration screen Messages

#Penalty Configuration screen Messages
penalty.modifyingData.validation.msg=Kindly enter Penalty amount for modifying uploaded data. Kindly enter numeric between 0 to 999999999.
penalty.notUpdatingData.validation.msg=Kindly enter Penalty amount for not updating data for a bin. Kindly enter numeric between 0 to 999999999.

penalty.AM_MSG_penaltyApproved =Penalty approved successfully.
penalty.AM_MSG_penaltyRejectionSuccess =Operation successful.
penalty.penaltyApproved=Penalty approved successfully.	
penalty.penaltyEntryDiscarded=Rejected Penalty data has been discarded successfully.
penalty.AM_MSG_errorUpdatePenalty=Error while approving role.
penalty.AM_MSG_pendingPenalty=Request sent for approval.
penalty.AM_MSG_pendingUpdatedPenalty=Request sent for approval.
penalty.AM_MSG_errorPenalty=ADD Penalty
penalty.AM_MSG_PenaltyApprovedMailSendingFail=Error sending mail for Penalty creation.
penalty.AM_MSG_PenaltyPending=Penalty is already pending for approval.
penalty.AM_MSG_errorPenaltyApprove=Error occurred while processing.
penalty.AM_MSG_penaltyRejected=Penalty rejected successfully.
penalty.AM_MSG_wfid_Empty=Data is not present for Penalty ID.
penalty.AM_MSG_noDataForApproval=No Data is for Approval.

#Penalty Configuration screen Messages
#Mcpr TAT Configuration screen Messages
mcprTAT.dataUploadWindow.validation.msg=Kindly enter MCPR Data upload Window TAT. Sum of Upload TAT and EDIT TAT less then 29.
mcprTAT.dataEditWindow.validation.msg=Kindly enter MCPR Data upload Window TAT. Sum of Upload TAT and EDIT TAT less then 29.

mcprTAT.AM_MSG_mcprTATApproved =MCPR TAT approved successfully.
mcprTAT.AM_MSG_mcprTATRejectionSuccess =Operation successful.
mcprTAT.mcprTATApproved=MCPR TAT approved successfully.	
mcprTAT.mcprTATEntryDiscarded=Rejected MCPR TAT data has been discarded successfully.
mcprTAT.AM_MSG_errorUpdateMcprTAT=Error while approving role.
mcprTAT.AM_MSG_pendingMcprTAT=Request sent for approval.
mcprTAT.AM_MSG_pendingUpdatedMcprTAT=Request sent for approval.
mcprTAT.AM_MSG_errorMcprTAT=ADD MCPR TAT
mcprTAT.AM_MSG_McprTATApprovedMailSendingFail=Error sending mail for MCPR TAT creation.
mcprTAT.AM_MSG_McprTATPending=MCPR TAT is already pending for approval.
mcprTAT.AM_MSG_errorMcprTATApprove=Error occurred while processing.
mcprTAT.AM_MSG_mcprTATRejected=MCPR TAT rejected successfully.

#Mcpr TAT Configuration screen Messages


##############Insurance Premium Master bhushan 22.06.22##############
insurancePremium.AM_MSG_approved =Insurance Premium approved successfully.
insurancePremium.AM_MSG_rejectionSuccess =Operation successful.
insurancePremium.approved=Insurance Premium  approved successfully.	
insurancePremium.entryDiscarded=Rejected Insurance Premium data has been discarded successfully.
insurancePremium.AM_MSG_errorUpdate=Error while approving Insurance Premium.
insurancePremium.AM_MSG_pending=Request sent for approval.
insurancePremium.AM_MSG_pendingUpdated=Request sent for approval.
insurancePremium.AM_MSG_error=ADD INSURANCE PREMIUM
insurancePremium.AM_MSG_approvedMailSendingFail=Error sending mail for Insurance Premium creation.
insurancePremium.AM_MSG_Pending=Insurance Premium  is already pending for approval.
insurancePremium.AM_MSG_errorApprove=Error occurred while processing.
insurancePremium.AM_MSG_rejected=Insurance Premium  rejected successfully.
insurancePremium.AM_MSG_wfid_Empty=Data is not present for Insurance Premium ID.


insurancePremium.cardType.validation.msg=Kindly select Card Type is mandatory
insurancePremium.fromYear.validation.msg=Kindly select From Year is mandatory. From Month Year should be greater then today Month Year.
insurancePremium.toYear.validation.msg=Kindly select To Year is mandatory. To Month Year should be greater then From Month Year.
insurancePremium.fromMonth.validation.msg=Kindly select From Month is mandatory. From Month Year should be greater then today Month Year.
insurancePremium.toMonth.validation.msg=Kindly select To Month is mandatory. To Month Year should be greater then From Month Year.
insurancePremium.cardVariant.validation.msg=Kindly select Card Variant is mandatory
insurancePremium.vendor.validation.msg=Kindly select Vendor is mandatory
insurancePremium.insurenceAmtPerCardPerAnnum.validation.msg=Kindly enter Insurance Premium. Kindly enter numeric between 0 to 10000000.
#Insurance Premium Configuration screen Messages


# Card Configuration screen Messages
cardConfig.updateSuccess.msg=Edited Base Fee data has been sent for approval
cardConfig.addSuccess.msg=Added Base Fee data has been sent for approval
cardConfig.approvalSuccess.msg=Base Fee data has been approved successfully
cardConfig.rejectionSuccess.msg=Base Fee data has been rejected successfully
cardConfig.discardSuccess.msg=Rejected Base Fee entry has been discarded successfully
ERR_CARD_CONFIG_EXISTS1=Base Fee Entry already exists 
ERR_CARD_CONFIG_EXISTS2=Discontinuation in Base Fee Configuration
# Card Configuration Validation 
cardConfig.cardType.validation.msg=Card Type is mandatory
cardConfig.cardVariant.validation.msg=Card Variant is mandatory
cardConfig.baseFee.validation.msg=Base Fee should be a positive numeric value
cardConfig.fromYear.validation.msg=From Year is mandatory
cardConfig.fromMonth.validation.msg=From Month is mandatory
cardConfig.toYear.validation.msg=To Year is mandatory
cardConfig.toMonth.validation.msg=To Month is mandatory
cardConfig.fromToDateValidation.validation.msg=From Date should be less than To Date
cardConfig.toFromDateValidation.validation.msg=To Date should be greater than From Date  
cardConfig.fromToSysDateValidation.validation.msg=Can not edit this field for past From Date
cardConfig.pastYearValidation.validation.msg=From Year should be greater than System Date
cardConfig.pastMonthValidation.validation.msg=From Month should be greater than System Date

# Feature Fee screen Messages
featureFee.updateSuccess.msg=Edited Feature Fee data has been sent for approval
featureFee.addSuccess.msg=Added Feature Fee data has been sent for approval
featureFee.approvalSuccess.msg=Feature Fee data has been approved successfully
featureFee.rejectionSuccess.msg=Feature Fee data has been rejected successfully
featureFee.discardSuccess.msg=Rejected Feature Fee entry has been discarded successfully
ERR_Feature_Fee_EXISTS1=Feature Fee Entry already exists 
ERR_Feature_Fee_EXISTS2=Discontinuation in Base Fee Configuration
# Feature Fee Validation 
featureFee.cardType.validation.msg=Card Type is mandatory
featureFee.cardVariant.validation.msg=Card Variant is mandatory
featureFee.featureFee.validation.msg=Feature Fee should be a positive numeric value
featureFee.feature.validation.msg=Feature is mandatory
featureFee.details.validation.msg=Details is mandatory
featureFee.fromYear.validation.msg=From Year is mandatory
featureFee.fromMonth.validation.msg=From Month is mandatory
featureFee.toYear.validation.msg=To Year is mandatory
featureFee.toMonth.validation.msg=To Month is mandatory
featureFee.fromToDateValidation.validation.msg=From Date should be less than To Date
featureFee.toFromDateValidation.validation.msg=To Date should be greater than From Date
featureFee.fromToSysDateValidation.validation.msg=Can not edit this field for past From Date
featureFee.pastYearValidation.validation.msg=From Year should be greater than System Date
featureFee.pastMonthValidation.validation.msg=From Month should be greater than System Date


# product Feature Fee Validation  
productFeature.quater.validation.msg=Quater is mandatory
productFeature.year.validation.msg=Year is mandatory
productFeature.binNo.validation.msg=6 Digit Bin Number is mandatory
productFeature.bankName.validation.msg=BankName is mandatory


# Mcpr Bin details Validation 
mcprBinDetails.month.validation.msg=Mandatory
mcprBinDetails.year.validation.msg=Mandatory
mcprBinDetails.bankName.validation.msg=Mandatory

mcprBinDetails.binNumber.validation.msg=Mandatory

mcprBinDetails.phyContactCardCummRuPayCard.validation.msg=Mandatory
mcprBinDetails.phyContactCardIncrementalCard.validation.msg=Mandatory
mcprBinDetails.phyContactlessCummRuPayCard.validation.msg=Mandatory
mcprBinDetails.phyContactlessIncrementalCard.validation.msg=Mandatory
mcprBinDetails.virtualCardCummRuPayCard.validation.msg=Mandatory
mcprBinDetails.virtualCardIncrementalCard.validation.msg=Mandatory
mcprBinDetails.ecommTxnOnusCount.validation.msg=Mandatory
mcprBinDetails.ecommTxnOnusAmt.validation.msg=Mandatory
mcprBinDetails.posConcactCardPresentDomTxnOnusCount.validation.msg=Mandatory
mcprBinDetails.posConcactCardPresentDomTxnOnusAmt.validation.msg=Mandatory
mcprBinDetails.posContactlessOnlRetailsDomTxnOnusCount.validation.msg=Mandatory
mcprBinDetails.posContactlessOnlRetailsDomTxnOnusAmt.validation.msg=Mandatory
mcprBinDetails.posContactlessOnlTransitDomTxnOnusCount.validation.msg=Mandatory
mcprBinDetails.posContactlessOnlTransitDomTxnOnusAmt.validation.msg=Mandatory
mcprBinDetails.posContactlessOffRetailsDomTxnOnusCount.validation.msg=Mandatory
mcprBinDetails.posContactlessOffRetailsDomTxnOnusAmt.validation.msg=Mandatory
mcprBinDetails.posContactlessOffTransitDomTxnOnusCount.validation.msg=Mandatory
mcprBinDetails.posContactlessOffTransitDomTxnOnusAmt.validation.msg=Mandatory
mcprBinDetails.atmCardPresentDomTxnOnusCount.validation.msg=Mandatory
mcprBinDetails.atmCardPresentDomTxnOnusAmt.validation.msg=Mandatory

mcprBinDetails.updateSuccess.msg=Edited MCPR data has been sent for approval
mcprBinDetails.addSuccess.msg=Added MCPR data has been sent for approval
mcprBinDetails.deleteSuccess.msg=Deleted MCPR data has been sent for approval
mcprBinDetails.approvalSuccess.msg=MCPR data has been approved successfully
mcprBinDetails.rejectionSuccess.msg=MCPR data has been rejected successfully
mcprBinDetails.discardSuccess.msg=Rejected MCPR entry has been discarded successfully

# Tip Surcharge Validation 
tipSurcharge.updateSuccess.msg=Edited Tip Surcharge data has been sent for approval
tipSurcharge.addSuccess.msg=Added Tip Surcharge data has been sent for approval
tipSurcharge.approvalSuccess.msg=Tip Surcharge data has been approved successfully
tipSurcharge.rejectionSuccess.msg=Tip Surcharge data has been rejected successfully
tipSurcharge.discardSuccess.msg=Rejected Tip Surcharge entry has been discarded successfully
tipSurcharge.tipSurchargeId.validation.msg=Tip Surcharge ID is mandatory
tipSurcharge.tipSurchargeType.validation.msg=Type is mandatory
tipSurcharge.operator.validation.msg=Operator is mandatory
tipSurcharge.settlementAmount.validation.msg=Settlement Amount is mandatory
tipSurcharge.settlementAmount1.validation.msg=Settlement amount field must be a valid amount
tipSurcharge.capFlat.validation.msg=Cap Flat is mandatory
tipSurcharge.capPercentage.validation.msg=Cap Percentage is mandatory
tipSurcharge.binCardBrand.validation.msg=Bin Card Brand is mandatory
tipSurcharge.binCardType.validation.msg=Bin Card Type is mandatory
tipSurcharge.amountPercentFlag.validation.msg=Amount/Percent is mandatory
tipSurcharge.percentage.validation.msg=Percent Value (Inclusive GST) is mandatory
tipSurcharge.amount.validation.msg=Amount Value (Inclusive GST) is mandatory
tipSurcharge.percentage1.validation.msg=Percentage value(Inclusive GST) must be a valid amount
tipSurcharge.amount1.validation.msg=Amount value(Inclusive GST) must be a valid amount
tipSurcharge.tipSurchargeName.validation.msg=Tip Surcharge Name is mandatory
tipSurcharge.tipSurchargeName1.validation.msg=Tip Surcharge Name must be a valid

# MCC Tip Surcharge Validation 
mccTipSurcharge.updateSuccess.msg=Edited MCC Tip Surcharge data has been sent for approval
mccTipSurcharge.addSuccess.msg=Added MCC Tip Surcharge data has been sent for approval
mccTipSurcharge.approvalSuccess.msg=MCC Tip Surcharge data has been approved successfully
mccTipSurcharge.rejectionSuccess.msg=MCC Tip Surcharge data has been rejected successfully
mccTipSurcharge.discardSuccess.msg=Rejected MCC Tip Surcharge entry has been discarded successfully
mccTipSurcharge.tipSurchargeId.validation.msg=MCC Tip Surcharge Name is mandatory
mccTipSurcharge.mccId.validation.msg=MCC Name is mandatory

currencyFileUpload.network.validation.msg=Please select Network.
currencyFileUpload.forexId.validation.msg=Please select ForexId.

# Currency Master Validation 
currencyMaster.updateSuccess.msg=Edited Currency Master data has been sent for approval
currencyMaster.addSuccess.msg=Added Currency Master data has been sent for approval
currencyMaster.approvalSuccess.msg=Currency Master data has been approved successfully
currencyMaster.rejectionSuccess.msg=Currency Master data has been rejected successfully
currencyMaster.discardSuccess.msg=Rejected Currency Master entry has been discarded successfully
currencyMaster.currencyMasterCode.validation.msg=3 digit Currency Code is mandatory
currencyMaster.currencyMasterDescription.validation.msg=Currency Description is mandatory
currencyMaster.currencyAlpha.validation.msg=Currency Alpha should be 3 digit Alphabets
currencyMaster.currencyDecimalPosition.validation.msg=Currency Decimal Position should be 1 digit numeric
ERR_CURRENCY_MASTER_EXISTS1=Currency Master already exists 
# Function Code Validation 
functionCode.updateSuccess.msg=Edited Function Code data has been sent for approval
functionCode.addSuccess.msg=Added Function Code data has been sent for approval
functionCode.approvalSuccess.msg=Function Code data has been approved successfully
functionCode.rejectionSuccess.msg=Function Code data has been rejected successfully
functionCode.discardSuccess.msg=Rejected Function Code entry has been discarded successfully

functionCode.functionCodeId.validation.msg=Function Code ID is mandatory
functionCode.mti.validation.msg=mti is mandatory
functionCode.procCode.validation.msg=Proc Code is mandatory
functionCode.funcCode.validation.msg=Func Code is mandatory
functionCode.funcCodeDesc.validation.msg=Func Code Desc is mandatory
functionCode.feeType.validation.msg=Fee Type is mandatory
functionCode.fundMovement.validation.msg=Fund Movement is mandatory
functionCode.fundMovementSide.validation.msg=Fund Movement Side is mandatory
functionCode.recalculate.validation.msg=Recalculate is mandatory
functionCode.transactionType.validation.msg=Transaction Type is mandatory
functionCode.networkTxnType.validation.msg=Network Txn Type is mandatory


#BinExclusionConfig Messages
binexcl.updateSuccess.msg=Edited Bin Exclusion data has been sent for approval
binexcl.addSuccess.msg=Added Bin Exclusion data has been sent for approval
binexcl.approvalSuccess.msg=Bin Exclusion data has been approved successfully
binexcl.rejectionSuccess.msg=Bin Exclusion data has been rejected successfully
binexcl.discardSuccess.msg=Rejected Bin Exclusion entry has been discarded successfully
binexcl.duplicateBinError.msg=Bin Exclusion with same date range already exists


binExcl.participant.validation.msg=Please Enter Participant Name.
binExcl.bin.validation.msg=Please Enter Bin number.
binExcl.feeType.validation.msg=Please Enter fee type.
binExcl.fromMonth.validation.msg=Please Enter starting month.
binExcl.toMonth.validation.msg=Please Enter ending month.
binExcl.fromYear.validation.msg=Please Enter starting year.
binExcl.toYear.validation.msg=Please Enter ending year.
binExcl.toDate.validation.msg=To date should be greater than from date.
binExcl.pastMonthValidation.validation.msg=Starting month should be greater than current month.
binExcl.dateValidation.validation.msg=To Date should be greater than from Date.



ERR_BIN_EXC=Unable to add Bin Exclusion 
ERR_BUDGET=Unable to Add Budget
ERR_MCC=Unable to Add MCC
ERR_PROJECTION_ENTRY=Unable to Add Projection Entry
ERR_BIN_FEATURE=Unable to add Bin Feature

ERR_BUDGET_EXISTS=Budget Configuration for the same vendor already exists
budget.mainTab.title=Budget
budget.approvalPanel.title=Budget Approval
budget.viewscreen.title=Budget List
budget.addscreen.title=Add New Budget Information
budget.editscreen.title=Edit New Budget Information
budget.budgetNumber.validation.msg=Budget is mandatory
budget.budgetNumber1.validation.msg=Budget must be a positive numeric value range(0.0-9999999999999999.99)
budget.vendor.validation.msg=Vendor Name is mandatory
budget.year.validation.msg=Financial Year is mandatory
budget.updateSuccess.msg=Edited Budget data has been sent for approval
budget.addSuccess.msg=Added Budget data has been sent for approval
budget.approvalSuccess.msg=Budget data has been approved successfully
budget.rejectionSuccess.msg=Budget data has been rejected successfully
budget.discardSuccess.msg=Rejected Budget entry has been discarded successfully
budget.duplicate.msg=Budget entry already present

ERR_BANKCODE_EXISTS=IFSC with same bank code already exists

ERR_PREMIUM_NOT_FOUND=Historical Premium with the given values does not exist
ERR_CARD_PROJECTION_EXISTS=Card Projection entry with same value already exists

cardProjection.totalCards.validation.msg=Total number of cards should be an integer value.
cardProjection.cardVariant.validation.msg=Please select card variant.
cardProjection.cardType.validation.msg=Please select card type.
cardprojection.updateSuccess.msg=Edited Card Projection data has been sent for approval
cardprojection.addSuccess.msg=Added Card Projection data has been sent for approval
cardprojection.approvalSuccess.msg=Card Projection data has been approved successfully
cardprojection.rejectionSuccess.msg=Card Projection data has been rejected successfully
cardprojection.discardSuccess.msg=Rejected Card Projection entry has been discarded successfully
cardprojection.deleteSuccess.msg= Card Projection entry has been discarded successfully
cardprojection.insurancePremiumConfigNotPresent.msg=Insurance Premium Configuration is not Present.

cardprojection.addscreen.title=Add New Card Projection Information
cardprojection.editscreen.title=Edit New Card Projection Information


historicalPremium.historyYear.validation.msg=Please enter year.
historicalPremium.historyMonth.validation.msg=Please enter month.
historicalPremium.cardType.validation.msg=Please select card type.
historicalPremium.cardVariant.validation.msg=Please select card variant.
historicalPremium.vendorId.validation.msg=Please select vendor.
netPremium.vendorId.validation.msg=Please select vendor.

historicalPremium.pastMonthValidation.validation.msg=Search date should be less than system date.

ERR_MCC_EXISTS1=MCC configuration for this Business Code already exists 
mcc.updateSuccess.msg=Edited MCC data has been sent for approval
mcc.addSuccess.msg=Added MCC data has been sent for approval
mcc.approvalSuccess.msg=MCC data has been approved successfully
mcc.rejectionSuccess.msg=MCC data has been rejected successfully
mcc.discardSuccess.msg=Rejected MCC entry has been discarded successfully

mcc.mccCode.validation.msg=MCC Code should be a 4 digit numeric value
mcc.mccDesc.validation.msg=MCC Description is mandatory
mcc.mccGroup.validation.msg=MCC Group is mandatory

escalation.name.validation.msg= Name should be Alphabets Only
escalation.email.validation.msg= Enter Valid Email
escalation.designation.validation.msg=Designation should be Alphabets Only
escalation.mobile.validation.msg= Mobile No should be 10 digit 
escalation.landline.validation.msg= LandLine should be between 10-12 digit
escalation.pinCode.validation.msg= Pincode should be 6 digit only
escalation.address.validation.msg= Enter Address
escalation.state.validation.msg= Please Select State


cappingAmount.discardSuccess.msg = Rejected Capping Amount Discarded Successfully



reasonCode.reasonCode = Reason Code
reasonCode.reasonCodeDesc = Reason Code Description
reasonCode.status = Status
reasonCode.code.validation.msg=Reason Code should be a 4 digit numeric value.
reasonCode.desc.validation.msg=Reason Code Description is mandatory.
reasonCode.addSuccess.msg=Added Reason Code data has been sent for approval
reasonCode.updateSuccess.msg=Edited Reason Code data has been sent for approval
reasonCode.approvalSuccess.msg=Reason Code data has been approved successfully
reasonCode.rejectionSuccess.msg=Reason Code data has been rejected successfully
reasonCode.discardSuccess.msg=Rejected Reason Code entry has been discarded successfully


reasonCode.rules.actionCode=Action Code
reasonCode.rules.reasonCode=Reason Code
reasonCode.rules.fieldName=Field Name
reasonCode.rules.fieldValue=Field Value
reasonCode.rules.relational=Rel Operator
reasonCode.rules.logicalOp=Logical Operator
reasonCode.rules.status=Status
reasonCode.rules.action=Action

jsonValidator.reasonCode = Reason Code
jsonValidator.reasonCodeDesc = Reason Code Description
jsonValidator.status = status
jsonValidator.pcode = Processing code
jsonValidator.priority = Priority
jsonValidator.api = API Type
jsonValidator.code = code
jsonValidator.fieldName = Field Name
jsonValidator.regexps = Regular Expression
jsonValidator.isMandatory = Mandatory
reasonCode.code.validation.msg=Reason Code should be a 4 digit numeric value.
reasonCode.desc.validation.msg=Reason Code Description is mandatory.
jsonValidator.addSuccess.msg=Added Json Validator data has been sent for approval
jsonValidator.updateSuccess.msg=Edited Json Validator data has been sent for approval
jsonValidator.approvalSuccess.msg=Json Validator data has been approved successfully
jsonValidator.rejectionSuccess.msg=Json Validator data has been rejected successfully
jsonValidator.discardSuccess.msg=Rejected Json Validator entry has been discarded successfully


rejectReasonCodeRule.functionCode = Function Code
rejectReasonCodeRule.fieldName = Field Name
rejectReasonCodeRule.fieldValue = Field Value
rejectReasonCodeRule.relationalOperator = Relational Operator
rejectReasonCodeRule.fieldOperator = Field Operator
rejectReasonCodeRule.subFieldName = Sub Field Name
rejectReasonCodeRule.rejectCode = Reject Code
rejectReasonCodeRule.status = status
rejectReasonCodeRule.createdBy = Created By
rejectReasonCodeRule.createdOn = Created On
rejectReasonCodeRule.lastUpdatedBy = Last Updated By
rejectReasonCodeRule.lastUpdatedOn  = Last Updated On


rejectReasonCodeRule.addSuccess.msg=Added Reject Reason Code data has been sent for approval
rejectReasonCodeRule.updateSuccess.msg=Edited Reject Reason Code data has been sent for approval
rejectReasonCodeRule.approvalSuccess.msg=Reject Reason Code data has been approved successfully
rejectReasonCodeRule.rejectionSuccess.msg=Reject Reason Code data has been rejected successfully
rejectReasonCodeRule.discardSuccess.msg=Rejected Reject Reason Code entry has been discarded successfully

binFeatureMapping.participant.validation.msg=Please Select Participant Name.
binFeatureMapping.bin.validation.msg=Please Select Bin number.
binFeatureMapping.featureId.validation.msg=Please Select Feature Id.
binFeatureMapping.fromMonth.validation.msg=Please Enter starting month.
binFeatureMapping.toMonth.validation.msg=Please Enter ending month.
binFeatureMapping.fromYear.validation.msg=Please Enter starting year.
binFeatureMapping.toYear.validation.msg=Please Enter ending year.
binFeatureMapping.toDate.validation.msg=To date should be greater than from date.
binFeatureMapping.pastMonthValidation.validation.msg=Starting month should be greater than current month.
binFeatureMapping.dateValidation.validation.msg=To Date should be greater than from Date.
binFeatureMapping.updateSuccess.msg=Edited Bin Feature Mapping data has been sent for approval
binFeatureMapping.addSuccess.msg=Added Bin Feature Mapping data has been sent for approval
binFeatureMapping.approvalSuccess.msg=Bin Feature Mapping data has been approved successfully
binFeatureMapping.rejectionSuccess.msg=Bin Feature Mapping data has been rejected successfully
binFeatureMapping.discardSuccess.msg=Rejected Bin Feature Mapping entry has been discarded successfully
binFeatureMapping.duplicateBinError.msg=Bin Feature Mapping with same date range already exists
binFeatureMapping.deleteSuccess.msg=Delete Bin Feature Mapping data has been sent for approval



disputeFeeRule.addSuccess.msg=Added Dispute Fee Rule data has been sent for approval
disputeFeeRule.updateSuccess.msg=Edited Dispute Fee Rule data has been sent for approval
disputeFeeRule.approvalSuccess.msg=Dispute Fee Rule data has been approved successfully
disputeFeeRule.rejectionSuccess.msg=Dispute Fee Rule data has been rejected successfully
disputeFeeRule.discardSuccess.msg=Rejected Dispute Fee Rule entry has been discarded successfully




##Settlement cycle

settlementCycle.addSuccess.msg=Added Settlement Cycle Config data has been sent for approval
settlementCycle.updateSuccess.msg=Edited Settlement Cycle Config data has been sent for approval
settlementCycle.approvalSuccess.msg=Settlement Cycle Config data has been approved successfully
settlementCycle.rejectionSuccess.msg=Settlement Cycle Config data  has been rejected successfully
settlementCycle.discardSuccess.msg=Rejected Settlement Cycle Config data has been discarded successfully

cashback.fileUpload.yearMsg = Please select year
cashback.fileUpload.monthMsg = Please select month
cashback.success.msg = Cashback File Uploaded



jsonValidator.api.validation.msg=select file or Auth.
jsonValidator.code.validation.msg=Code should be 3 digit numeric value.
jsonValidator.pcode.validation.msg=select Processing Code.
jsonValidator.regex.validation.msg=Regex should not be empty.
jsonValidator.reasonCode.validation.msg=Select Reason Code.
jsonValidator.priority.validation.msg=Priority can be max 2 digit value.
jsonValidator.fieldName.validation.msg=Field name must contain alphabetical value.


rejectReasonCode.funcCode.validation.msg=select function code.
rejectReasonCode.fieldName.validation.msg=select field name.
rejectReasonCode.fieldValue.validation.msg=Enter field value.
rejectReasonCode.relationalOperator.validation.msg=select relational Operator. 
rejectReasonCode.fieldOperator.validation.msg=select field Operator.
rejectReasonCode.subFieldName.validation.msg=select Sub field name.
rejectReasonCode.rejectCode.validation.msg=select reject code.


disputefeerules.actionCode.validation.msg=select action code.
disputefeerules.feeType.validation.msg=select Fee Type.
disputefeerules.priority.validation.msg=Enter Priority in max 2 digit only.
disputefeerules.feeCode.validation.msg=Enter Fee code in numbers only. 
disputefeerules.relationalOperator.validation.msg=select relational Operator.
disputefeerules.logicalFeeCode.validation.msg=Enter Fee code in numbers only.
disputefeerules.fieldName.validation.msg=Select Field Name.
disputefeerules.fieldValue.validation.msg=Field Value should be Alphanumeric. 
disputefeerules.fieldOperator.validation.msg=select field Operator.
reasonCode.subType.validation.msg=Reason Code SubType should be 3 digit value.
reasonCode.reasonType.validation.msg=Please select Reason Type.
reasonCode.subTypeDesc.validation.msg=Reason Code SubType Description should be alphabetic.


#holiday
ERR_HOLIDAY_MASTER_EXISTS=Holiday Master with same configuration already exists
ERR_NO_CONFIGURATION_EXISTS=No Configuration Available for Holiday Master Date