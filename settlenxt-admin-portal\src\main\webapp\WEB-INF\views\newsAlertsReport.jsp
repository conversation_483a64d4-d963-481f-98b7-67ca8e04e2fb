<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="EN" xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>News Report</title>
<script type="text/javascript"
	src="./static/js/bootstrap-multiselect.js"></script>
<link rel="stylesheet" href="./static/css/bootstrap-multiselect.css"
	type="text/css" />

<script type="text/javascript"
	src="./static/js/validation/newsAlertReport.js"></script>
<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />

</head>

<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data">News & Alerts Report</span></strong>
</div>
		</div>
	<form:form onsubmit="return encodeForm(this);" 
			id="newsAndAlertsReports" modelAttribute="newsAndAlertInfoDTO" 
			autocomplete="off">
			<br />						
			
				<div class="row">
					<div class="col-sm-12">	
							
							<div class="col-sm-3">
								<div class="form-group">
									<label for="squareSelect">From Date <span
										class="red">*</span></label>
									<form:input path="fromDateStr" id="fromDateStr"
										name="fromDateStr"  data-date-format="yyyy-mm-dd"
										cssClass="form-control input-square" readonly="true" onkeyup="validateFromDate('fromDateStrErr')"  />
									<div id="fromDateStrErr">
										<span for="fromDateStrErr" class="error"></span>
										<form:errors path="fromDateStr"  />
									</div>
								</div>
								</div>
					
								<div class="col-sm-3">
								<div class="form-group">
									<label for="squareSelect">To Date <span
										class="red">*</span></label>
									<form:input path="toDateStr" id="toDateStr"
										name="toDateStr"  data-date-format="yyyy-mm-dd"
										cssClass="form-control input-square" readonly="true"  onkeyup="validateToDate('toDateStrErr')"  />
									<div id="toDateStrErr">
										<span for="toDateStrErr" class="error"></span>
										<form:errors path="toDateStr"  />
									</div>
								</div>
								</div>
							
								<button type="button" class="btn btn-success"
									onclick="submitForm('/downloadNewsAlertsBirtReport');" style="height: 34px;margin-top: 20px;">
									<spring:message code="newsreports.downloadButton" />
								</button>
								
								</div>
								</div>
								
	
</form:form>	
	</div>							

								