package org.npci.settlenxt.adminportal.controllers;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.ReasonCodeMasterService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.ReasonCodeDto;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

@Controller
public class ReasonCodeMasterController extends BaseController {

	private static final String SHOW_REASON_CODE_MASTER = "showResonCodeMaster";
	private static final String ADD_EDIT_REASON_CODE_MASTER = "addEditReasonCodeMaster";
	private static final String VIEW_REASON_CODE = "viewReasonCode";
	private static final String VIEW_APPROVE_REASON_CODE = "viewApproveReasonCode";

	private static final String SHOW_REASON_CODE_RULES = "showReasonCodeRules";
	private static final String ADD_EDIT_REASON_CODE_RULES = "addEditReasonCodeRules";
	private static final String VIEW_REASON_CODE_RULES = "viewReasonCodeRules";
	private static final String APPROVE_REASON_CODE_RULES = "viewApproveReasonCodeRules";
	private static final String ACTION_CODE = "actionCode";
	private static final String FIELD_NAME = "fieldName";
	private static final String FIELD_NAME_LIST = "fieldNameList";
	private static final String LOGICAL_REASON_CODE = "logicalReasonCode";
	private static final String REASON_CODE = "reasonCode";
	private static final String RELATION_OPERATOR = "relationOperator";
	private static final String SHOW_CHECK_BOX = "showCheckBox";
	private static final String ERR_ENCODING = "Error Encoding seqId";
	private static final String UTF_8 = "UTF-8";
	private static final String ERROR_CODE="E00001";
	private static final String EDIT_ADD_FLOW_RULE="editAddRuleFlow";
	private static final String REASON_CODE_MAP="reasonCodeMap";
	private static final String ACTION_CODE_MAP="actionCodeMap";
	private static final String REL_OPR_MAP="relOpMap";
	private static final String SHOW_REASON_CODE="showReasonCode";
	private static final String TRAN_SIZE="tranSize";
	private static final String STATUS="status";
	private static final String SUCCESS="Success"; 

	@Autowired
	ReasonCodeMasterService reasonCodeMasterService;

	@Autowired
	BaseLookupService lookUpService;

	@Autowired
	private SessionDTO sessionDto;

	@Autowired
	private MessageSource messageSource;

	@PostMapping("/showReasonCodeMaster")
	@PreAuthorize("hasAuthority('view Reason Code')")
	public String showReasonCodeMaster(Model model) {
		model.addAttribute(SHOW_REASON_CODE, CommonConstants.YES);
		List<ReasonCodeDto> reasonCodeMasterList = reasonCodeMasterService.getReasonCode();

		model.addAttribute(CommonConstants.REASON_CODE_MASTER_LIST, reasonCodeMasterList);

		model.addAttribute(CommonConstants.ADD_REASON_CODE_MASTER, CommonConstants.TRANSACT_YES);
		return getView(model, SHOW_REASON_CODE_MASTER);
	}

	@PostMapping("/createReasonCode")
	@PreAuthorize("hasAuthority('Add Reason Code')")
	public String createReasonCodeMaster(Model model) {
		List<ReasonCodeDto> reasonCodeList = reasonCodeMasterService.getReasonCodeList();
		model.addAttribute(CommonConstants.ADD_REASON_CODE_MASTER, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.ADD_REASON_CODE, CommonConstants.TRANSACT_YES);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_BUDGET);
		ReasonCodeDto reasonCodeDto = new ReasonCodeDto();
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);
		model.addAttribute(CommonConstants.REASON_CODE_STG_LIST, reasonCodeList);

		return getView(model, ADD_EDIT_REASON_CODE_MASTER);
	}

	@PostMapping("/addReasonCode")
	@PreAuthorize("hasAuthority('Add Reason Code')")
	public String addReasonCodeMaster(@ModelAttribute("reasonCodeDto") ReasonCodeDto reasonCodeDto, Model model) {

		model.addAttribute(CommonConstants.ADD_REASON_CODE, CommonConstants.ADD_REASON_CODE);

		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);
		reasonCodeMasterService.addReasonCodeMaster(reasonCodeDto);

		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("reasonCode.addSuccess.msg"));

		return getView(model, ADD_EDIT_REASON_CODE_MASTER);

	}

	@PostMapping("/getReasonCode")
	@PreAuthorize("hasAuthority('view Reason Code')")
	public String getReasonCode(@RequestParam("reasonCode") String reasonCode, Model model) {
		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.getReasonCodeDetail(reasonCode);
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);
		return getView(model, VIEW_REASON_CODE);

	}

	@PostMapping("/editReasonCode")
	@PreAuthorize("hasAuthority('Edit Reason Code')")
	public String editReasonCode(@RequestParam("reasonCode") String reasonCode, Model model) {
		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.getReasonCodeDetail(reasonCode);

		model.addAttribute(CommonConstants.EDIT_REASON_CODE, CommonConstants.TRANSACT_YES);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);
		return getView(model, ADD_EDIT_REASON_CODE_MASTER);
	}

	@PostMapping("/addAllReasonCode")
	@PreAuthorize("hasAuthority('Add Reason Code')")
	public ResponseEntity<Object> addAllReasonCode(Model model, @RequestBody List<ReasonCodeDto> reasonCodeList,
			HttpServletResponse response) {
		for (ReasonCodeDto reasonCodeDto : reasonCodeList) {
			reasonCodeMasterService.addReasonCodeMaster(reasonCodeDto);
		}
			String result = SUCCESS;
		JsonObject jsonResponse = new JsonObject();
		if (StringUtils.equals(result, SUCCESS)) {
			jsonResponse.addProperty(STATUS, BaseCommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(STATUS, BaseCommonConstants.TRANSACT_FAIL);
		}
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/updateReasonCode")
	@PreAuthorize("hasAuthority('Edit Reason Code')")
	public String updateReasonCode(@ModelAttribute("reasonCodeDto") ReasonCodeDto reasonCodeDto, Model model) {
		reasonCodeDto = reasonCodeMasterService.updateReasonCode(reasonCodeDto);
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);

		model.addAttribute(CommonConstants.EDIT_REASON_CODE, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("reasonCode.updateSuccess.msg"));
		return getView(model, VIEW_APPROVE_REASON_CODE);
	}

	@PostMapping("/reasonCodePendingForApproval")
	@PreAuthorize("hasAuthority('view Reason Code')")
	public String reasonCodePendingForApproval(Model model) {

		model.addAttribute("showPendingReasonCode", CommonConstants.TRANSACT_YES);
		List<ReasonCodeDto> reasonCodePendingList = reasonCodeMasterService.getPendingReasonCode();
		Long tranSize = reasonCodePendingList.stream()
				.filter(reasonCodedto -> reasonCodedto.getRequestState().equalsIgnoreCase("P"))
				.collect(Collectors.counting());

		model.addAttribute(TRAN_SIZE, tranSize);
		model.addAttribute(CommonConstants.REASON_CODE_PENDING_LIST, reasonCodePendingList);
		model.addAttribute(CommonConstants.REASON_CODE_APP_PENDING, CommonConstants.YES);

		return getView(model, SHOW_REASON_CODE_MASTER);
	}

	@PostMapping("/getPendingReasonCode")
	@PreAuthorize("hasAuthority('view Reason Code')")
	public String getPendingReasonCodeDetail(@RequestParam("reasonCode") String reasonCode, Model model) {
		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.getReasonCodeDetail(reasonCode);
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);

		return getView(model, VIEW_APPROVE_REASON_CODE);

	}

	@PostMapping("/approveReasonCode")
	@PreAuthorize("hasAuthority('Approve Reason Code')")
	public String approveReasonCode(@RequestParam("reasonCode") String reasonCode,
			@RequestParam("status") String status, @RequestParam("remarks") String remarks, Model model) {
		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.updateApproveOrRejectReasonCode(reasonCode, status,
				remarks);
		checkReasonCodeApproveStatus(reasonCodeDto, model);
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);

		return getView(model, VIEW_APPROVE_REASON_CODE);
	}

	private void checkReasonCodeApproveStatus(ReasonCodeDto reasonCodeDto, Model model) {
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(reasonCodeDto.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("reasonCode.approvalSuccess.msg"));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("reasonCode.rejectionSuccess.msg"));
		}

	}

	@PostMapping("/discardRejectedReasonCode")
	@PreAuthorize("hasAuthority('Edit Reason Code')")
	public String discardReasonCode(@RequestParam("reasonCode") String reasonCode, Model model) {
		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.discardReasonCode(reasonCode);

		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("reasonCode.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_REASON_CODE);
	}

	@PostMapping("/showReasonCodeRules")
	@PreAuthorize("hasAuthority('View Reason Code Rules')")
	public String showReasonCodeRules(Model model) {
		model.addAttribute(SHOW_REASON_CODE, CommonConstants.YES);
		List<ReasonCodeDto> reasonCodeMasterList = reasonCodeMasterService.showReasonCodeRules();

		prepareReasonCodeRulesMap(model);

		model.addAttribute(CommonConstants.REASON_CODE_MASTER_LIST, reasonCodeMasterList);

		model.addAttribute(CommonConstants.ADD_REASON_CODE_RULES, CommonConstants.TRANSACT_YES);
		return getView(model, SHOW_REASON_CODE_RULES);
	}

	private void prepareReasonCodeRulesMap(Model model) {
		List<CodeValueDTO> list1 = new ArrayList<>();
		List<CodeValueDTO> list2 = new ArrayList<>();
		List<CodeValueDTO> list3 = new ArrayList<>();
		List<CodeValueDTO> list4 = new ArrayList<>();

		list1.addAll(lookUpService.getLookupData(CommonConstants.REL_OPR));
		list2.addAll(reasonCodeMasterService.fetchActionCodeList());
		list3.addAll(reasonCodeMasterService.fetchReasonCodeList());
		list4.addAll(lookUpService.getLookupData(CommonConstants.LOGICAL_REASON_CODE));

		Map<String, String> lookUpMap = null;
		lookUpMap = list1.stream().collect(Collectors.toMap(CodeValueDTO::getCode, CodeValueDTO::getDescription));
		model.addAttribute(REL_OPR_MAP, lookUpMap);

		lookUpMap = list2.stream().collect(Collectors.toMap(CodeValueDTO::getCode, CodeValueDTO::getDescription));
		model.addAttribute(ACTION_CODE_MAP, lookUpMap);

		lookUpMap = list3.stream().collect(Collectors.toMap(CodeValueDTO::getCode, CodeValueDTO::getDescription));
		model.addAttribute(REASON_CODE_MAP, lookUpMap);

	}

	@PostMapping("/showPendingReasonCodeRules")
	@PreAuthorize("hasAuthority('View Reason Code Rules')")
	public String showPendingReasonCodeRules(Model model) {

		List<ReasonCodeDto> reasonCodePendingList = reasonCodeMasterService.showPendingReasonCodeRules();
		prepareReasonCodeRulesMap(model);
		model.addAttribute(CommonConstants.REASON_CODE_PENDING_LIST, reasonCodePendingList);
		model.addAttribute(CommonConstants.REASON_CODE_APP_PENDING, CommonConstants.YES);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
		}

		return getView(model, SHOW_REASON_CODE_RULES);
	}

	@PostMapping("/addReasonCodeRules")
	@PreAuthorize("hasAuthority('Add Reason Code Rules')")
	public String addReasonCodeRules(Model model) {

		ReasonCodeDto reasonCodeDto = new ReasonCodeDto();
		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(RELATION_OPERATOR, lookUpService.getLookupData(CommonConstants.REL_OPR));
		model.addAttribute(ACTION_CODE, reasonCodeMasterService.fetchActionCodeList());
		model.addAttribute(REASON_CODE, reasonCodeMasterService.fetchReasonCodeList());
		model.addAttribute(FIELD_NAME, lookUpService.getLookupData(CommonConstants.FIELD_EX));
		model.addAttribute(LOGICAL_REASON_CODE, lookUpService.getLookupData(CommonConstants.LOGICAL_REASON_CODE));

		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);

		return getView(model, ADD_EDIT_REASON_CODE_RULES);
	}

	@PostMapping("/saveReasonCodeRules")
	@PreAuthorize("hasAuthority('Add Reason Code Rules')")
	public ResponseEntity<Object> saveReasonCodeMaster(Model model, @RequestBody List<ReasonCodeDto> reasonCodeDto,
			HttpServletResponse response) {

		List<ReasonCodeDto> reasonCodeList = reasonCodeDto;

		String result = reasonCodeMasterService.saveReasonCodeRulesList(reasonCodeList);

		JsonObject jsonResponse = new JsonObject();
		if (StringUtils.equals(result, CommonConstants.RESULT_SUCCESS)) {
			jsonResponse.addProperty(CommonConstants.STATUS, BaseCommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(CommonConstants.STATUS, BaseCommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PostMapping("/updateReasonCodeRules")
	@PreAuthorize("hasAuthority('Edit Reason Code Rules')")
	public ResponseEntity<Object> updateReasonCodeRules(Model model, @RequestBody List<ReasonCodeDto> reasonCodeDto,
			HttpServletResponse response) {

		String result = reasonCodeMasterService.updateReasonCodeRulesList(reasonCodeDto);

		JsonObject jsonResponse = new JsonObject();
		if (StringUtils.equals(result, CommonConstants.RESULT_SUCCESS)) {
			jsonResponse.addProperty(CommonConstants.STATUS, BaseCommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(CommonConstants.STATUS, BaseCommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	public void selectFieldName(ReasonCodeDto reasonCodeDto) {

		StringBuilder fieldName = new StringBuilder();
		String[] fieldArray = reasonCodeDto.getFieldName().split("\\|");
		fieldName.append(fieldArray[0]);
		for (int i = 1; i < fieldArray.length; i++) {
			fieldName.append(",");
			fieldName.append(fieldArray[i]);

		}

		String fieldNameVal = fieldName.toString();
		if (fieldNameVal.contains("|")) {
			fieldNameVal = fieldNameVal.replace("|", "");
		}
		reasonCodeDto.setFieldName(fieldNameVal);

	}

	@PostMapping("/editReasonCodeRules")
	@PreAuthorize("hasAuthority('Edit Reason Code Rules')")
	public String editReasonCodeRules(@RequestParam("seqId") String seqId, Model model) {

		try {
			seqId = URLDecoder.decode(seqId, UTF_8);
		} catch (UnsupportedEncodingException e) {
			throw new SettleNxtException(ERR_ENCODING, "",e);

		}

		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.fetchReasonCodeRulesStg(seqId);

		model.addAttribute(RELATION_OPERATOR, lookUpService.getLookupData(CommonConstants.REL_OPR));
		model.addAttribute(ACTION_CODE, reasonCodeMasterService.fetchActionCodeList());
		model.addAttribute(REASON_CODE, reasonCodeMasterService.fetchReasonCodeList());
		model.addAttribute(FIELD_NAME, lookUpService.getLookupData(CommonConstants.FIELD_EX));
		model.addAttribute(LOGICAL_REASON_CODE, lookUpService.getLookupData(CommonConstants.LOGICAL_REASON_CODE));
		model.addAttribute(FIELD_NAME_LIST, reasonCodeDto.getFieldName());
		if (reasonCodeDto.getRequestState().equalsIgnoreCase(CommonConstants.REQUEST_STATE_APPROVED))

		{
			model.addAttribute(EDIT_ADD_FLOW_RULE, CommonConstants.YES_FLAG);
		}
		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);

		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);
		return getView(model, ADD_EDIT_REASON_CODE_RULES);
	}

	@PostMapping("/viewReasonCodeRules")
	@PreAuthorize("hasAuthority('View Reason Code Rules')")
	public String getBulkDataEntry(@RequestParam("seqId") String seqId, Model model) {
		try {
			seqId = URLDecoder.decode(seqId, UTF_8);
		} catch (UnsupportedEncodingException e) {
			throw new SettleNxtException(ERR_ENCODING, "",e);

		}
		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.fetchReasonCodeRulesMain(seqId);
		prepareReasonCodeRulesMap(model);
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);

		return getView(model, VIEW_REASON_CODE_RULES);
	}

	@PostMapping("/viewPendingReasonCodeRules")
	@PreAuthorize("hasAuthority('View Reason Code Rules')")
	public String viewPendingReasonCodeRules(@RequestParam("seqId") String seqId, Model model) {
		try {
			seqId = URLDecoder.decode(seqId, UTF_8);
		} catch (UnsupportedEncodingException e) {
			throw new SettleNxtException(ERR_ENCODING, "",e);

		}
		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.fetchReasonCodeRulesStg(seqId);
		prepareReasonCodeRulesMap(model);
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);

		return getView(model, APPROVE_REASON_CODE_RULES);
	}

	@PostMapping("/viewRejPendingReasonCodeRules")
	@PreAuthorize("hasAuthority('View Reason Code Rules')")
	public String viewRejPendingReasonCodeRules(@RequestParam("seqId") String seqId, Model model) {
		try {
			seqId = URLDecoder.decode(seqId, UTF_8);
		} catch (UnsupportedEncodingException e) {
			throw new SettleNxtException(ERR_ENCODING, "",e);

		}
		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.fetchReasonCodeRulesStg(seqId);
		prepareReasonCodeRulesMap(model);
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);

		return getView(model, VIEW_REASON_CODE_RULES);
	}

	@PostMapping("/discardReasonCodeRules")
	@PreAuthorize("hasAuthority('Edit Reason Code Rules')")
	public String discardBulkDataEntry(@RequestParam("seqId") String seqId, Model model) {
		try {
			seqId = URLDecoder.decode(seqId,UTF_8);
		} catch (UnsupportedEncodingException e) {
			throw new SettleNxtException("Error Encodinf seqId", "",e);

		}
		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.discardReasonCodeRules(seqId);
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);

		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("reasonCodeRules.discardSuccess.msg"));
		prepareReasonCodeRulesMap(model);
		return getView(model, APPROVE_REASON_CODE_RULES);
	}

	@PostMapping("/approveReasonCodeRules")
	@PreAuthorize("hasAuthority('Approve Reason Code Rules')")
	public String approveReasonCodeRules(@RequestParam("seqId") String seqId, @RequestParam("status") String status,
			@RequestParam("remarks") String remarks, Model model) {

		ReasonCodeDto reasonCodeDto = reasonCodeMasterService.fetchReasonCodeRulesStgBySeqId(seqId);
		reasonCodeDto = reasonCodeMasterService.approveReasonCodeRules(reasonCodeDto, status, remarks);
		checkReasonCodeRulesStatus(reasonCodeDto, model);
		model.addAttribute(CommonConstants.REASON_CODE_DTO, reasonCodeDto);
		prepareReasonCodeRulesMap(model);
		return getView(model, APPROVE_REASON_CODE_RULES);
	}

	private void checkReasonCodeRulesStatus(ReasonCodeDto reasonCodeDto, Model model) {
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(reasonCodeDto.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("reasonCodeRules.approveSuccess.msg"));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("reasonCodeRules.rejectSuccess.msg"));
		}
	}

	@PostMapping("/checkDupReasonCodeRules")
	@PreAuthorize("hasAuthority('View Reason Code Rules')")
	public ResponseEntity<Object> checkDupReasonCodeRules(@RequestParam("actionCode") String actionCode,
			@RequestParam("reasonCode") String reasonCode, @RequestParam("relationOperator") String relationOperator,
			@RequestParam("fieldName") String fieldName, Model model) {

		boolean result = reasonCodeMasterService.checkDupReasonCodeRules(actionCode, reasonCode, fieldName,
				relationOperator);

		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PostMapping("/approveOrRejectBulkReasonCodeRules")
	@PreAuthorize("hasAuthority('Approve Reason Code Rules')")
	public String approveReasonCodeBulkStatus(@RequestParam("idList") String idList,
			@RequestParam("status") String status, Model model) {
		try {
			String remarks = "";
			if (status.equals(CommonConstants.REQUEST_STATE_APPROVED)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.REQUEST_STATE_REJECTED)) {
				remarks = CommonConstants.BULK_REJECT;
			}
			ReasonCodeDto reasonCodeDto = reasonCodeMasterService.updateApproveOrRejectBulkRcRules(idList, status,
					remarks);
			checkReasonCodeApproveStatus(reasonCodeDto, model);
			prepareReasonCodeRulesMap(model);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, APPROVE_REASON_CODE_RULES, ex);
		}

		List<ReasonCodeDto> reasonCodePendingList = reasonCodeMasterService.showPendingReasonCodeRules();

		model.addAttribute(CommonConstants.REASON_CODE_PENDING_LIST, reasonCodePendingList);
		model.addAttribute(CommonConstants.REASON_CODE_APP_PENDING, CommonConstants.YES);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDto.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
		}

		return getView(model, SHOW_REASON_CODE_RULES);

	}

	@PostMapping("/bulkApproveResonCodeMaster")
	@PreAuthorize("hasAuthority('Approve Reason Code')")
	public String bulkApproveResonCodeMaster(Model model, @RequestParam("status") String status,
			@RequestParam("reasonCodeList") String reasonCodeList, HttpServletResponse response,
			HttpServletRequest request) {

		String resultBulkAppReasonMaster = reasonCodeMasterService.updateBulkStgReasonCodeMaster(reasonCodeList, status);

		if (StringUtils.equals(resultBulkAppReasonMaster, CommonConstants.RESULT_SUCCESS)
				&& StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("reasonCode.approvalSuccess.msg", null, request.getLocale()));
		} else if (StringUtils.equals(resultBulkAppReasonMaster, CommonConstants.RESULT_SUCCESS)
				&& StringUtils.equals(status, CommonConstants.RECORD_REJECTED)) {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("reasonCode.rejectionSuccess.msg", null, request.getLocale()));
		} else {
			model.addAttribute(BaseCommonConstants.ERROR_STATUS,
					messageSource.getMessage(ERROR_CODE, null, request.getLocale()));
			model.addAttribute(CommonConstants.REASON_CODE_PENDING_LIST, null);
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, BaseCommonConstants.NO_FLAG);

			return getView(model, SHOW_REASON_CODE_MASTER);
		}

		model.addAttribute("showPendingReasonCode", CommonConstants.TRANSACT_YES);
		List<ReasonCodeDto> reasonCodePendingList = reasonCodeMasterService.getPendingReasonCode();
		Long tranSize = reasonCodePendingList.stream()
				.filter(reasonCodedto -> reasonCodedto.getRequestState().equalsIgnoreCase("P"))
				.collect(Collectors.counting());
		model.addAttribute(CommonConstants.REASON_CODE_PENDING_LIST, reasonCodePendingList);
		model.addAttribute(CommonConstants.REASON_CODE_APP_PENDING, CommonConstants.YES);
		model.addAttribute(TRAN_SIZE, tranSize);
		return getView(model, SHOW_REASON_CODE_MASTER);

	}
}
