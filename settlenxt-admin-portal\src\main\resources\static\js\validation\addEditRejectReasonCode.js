var rejectReasonCodeList = [];
var index = 0;
var datas = {};
var funcCode;
var fieldName;
var fieldValue;
var relationalOperator;
var subFieldName;
var rejectCode;
var fieldOperator;
$(document).ready(function () {
	funcCode = $('#funcCode').val();
	fieldName = $('#fieldName').val();
	fieldValue = $('#fieldValue').val();
	relationalOperator = $('#relationalOperator').val();
	subFieldName = $('#subFieldName').val();
	rejectCode = $('#rejectCode').val();
	fieldOperator = $('#fieldOperator').val();
    $("#errfuncCode").hide();
    $("#errfieldName").hide();
    $("#errfieldValue").hide();
    $("#errrelationalOperator").hide();
    $("#errfieldOperator").hide();

    $("#errsubFieldName").hide();
    $("#errrejectCode").hide();
    $('#bUpdate').prop('disabled', true);
    $('#bSubmit').prop('disabled', true);
    $('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();    
    $('#funcCode').on('keyup keypress blur change', function() {
    	validateFromCommonVal('funcCode', true, "SelectionBox", 3, false);
    	validateEditRejectReasonCode();
	});
    $('#fieldName').on('keyup keypress blur change', function() {
    	validateFromCommonVal('fieldName', true, "SelectionBox", 2, false);
    	validateEditRejectReasonCode();
	});
    $('#fieldValue').on('keyup keypress blur change', function() {
    	validateFromCommonVal('fieldValue', true, "NumComma", 3, false);
    	validateEditRejectReasonCode();
	});
    $('#relationalOperator').on('keyup keypress blur change', function() {
    	validateFromCommonVal('relationalOperator', true, "SelectionBox", 3, false);
    	validateEditRejectReasonCode();
	});
    $('#fieldOperator').on('keyup keypress blur change', function() {
    	validateFromCommonVal('fieldOperator', true, "SelectionBox", 3, false);
    	validateEditRejectReasonCode();
	});
    $('#subFieldName').on('keyup keypress blur change', function() {
    	validateFromCommonVal('subFieldName', true, "SelectionBox", 3, false);
    	validateEditRejectReasonCode();
	});
    $('#rejectCode').on('keyup keypress blur change', function() {
    	validateFromCommonVal('rejectCode', true, "SelectionBox", 3, false);
    	validateEditRejectReasonCode();
	}); 
});

function userAction(_type, action) {
	let url = action;
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var data ="_vTransactToken," + tokenValue + ",seqId,"
		+ $('#seqId').val();
	postData(url, data);
} 

function addOrUpdateRejectReasonCode(id) {
	

	if (id == 'A' && rejectReasonCodeList.length >= 1) {
		addOrUpdateRejectReasonCodeAll();
	}
	else if (id == 'E') {
		let url = '/updateRejectReasonCode';
		var isValid = validateField();
	    if (isValid) {
	        var data = "funcCode," + $('#funcCode').val() +",fieldName," + $('#fieldName').val() +",relationalOperator," + $('#relationalOperator').val() +",fieldOperator," + $('#fieldOperator').val()  +",subFieldName," + $('#subFieldName').val() +",rejectCode," + $('#rejectCode').val()+",seqId,"+$('#seqId').val() +",fieldValue," + $('#fieldValue').val();
	        editRejectReasonCode(url,data);
	    }
	}
	else{
		alert("Please add the record first");
	}
    
}

function validateField() {
    var isValid = true;
    
    if(!validateFromCommonVal('funcCode', true, "SelectionBox", 3, false)){
		isValid = false;
	}
    if(!validateFromCommonVal('fieldName', true, "SelectionBox", 2, false)){
		isValid = false;
	}
    if(!validateFromCommonVal('fieldValue', true, "NumComma", 3, false)){
		isValid = false;
	}
    if(!validateFromCommonVal('relationalOperator', true, "SelectionBox", 3, false)){
		isValid = false;
	}
    if(!validateFromCommonVal('fieldOperator', true, "SelectionBox", 3, false)){
		isValid = false;
	}
    if(!validateFromCommonVal('subFieldName', true, "SelectionBox", 3, false)){
		isValid = false;
	}
    if(!validateFromCommonVal('rejectCode', true, "SelectionBox", 3, false)){
		isValid = false;
	}
    return isValid;
}


function postDiscardRejectReasonCode(action) {
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var url = action;
	var data = "seqId," + $('#seqId').val() + ",_vTransactToken," + tokenValue;
	postData(url, data);
	
}



function saveRejectReasonCode() {
	 $('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
	$('#snxtSuccessMessage').hide();
	if(validateField()){
		$('#bSubmit').prop('disabled', false);
	datas['id'] = index; 
	datas['funcCode'] = document.getElementById('funcCode').value;
	datas['fieldName'] = document.getElementById('fieldName').value;
	datas['fieldValue'] = document.getElementById('fieldValue').value;
	datas['relationalOperator'] = document.getElementById('relationalOperator').value;
	datas['fieldOperator'] = document.getElementById('fieldOperator').value;
	datas['subFieldName'] = document.getElementById('subFieldName').value;
	datas['rejectCode'] = document.getElementById('rejectCode').value;
	rejectReasonCodeList.push(datas);
	
	
	
	var funcCodeNode = document.createTextNode(datas['funcCode']);
    var fieldNameNode = document.createTextNode(datas['fieldName']);
    var fieldValueNode = document.createTextNode(datas['fieldValue']);
    var relationalOperatorNode = document.createTextNode(datas['relationalOperator']);
    var fieldOperatorNode = document.createTextNode(datas['fieldOperator']);
    var subFieldNameNode = document.createTextNode(datas['subFieldName']);
    var rejectCodeNode = document.createTextNode(datas['rejectCode']);

 

	if (rejectReasonCodeList.length > 0) {
		var id = rejectReasonCodeList.map(e => e.id).indexOf(datas['id']);
		$('#tabnew').hide();
		
		$('#tabnew').append(
                  $('<tr>').attr('id', 'tabnew_' + id).append(
                    $('<td>').append(funcCodeNode),
                    $('<td>').append(fieldNameNode),
                    $('<td>').append(fieldValueNode),
                    $('<td>').append(relationalOperatorNode),
                    $('<td>').append(fieldOperatorNode),
                    $('<td>').append(subFieldNameNode),
                    $('<td>').append(rejectCodeNode),
                    $('<td>').append($('<input>').attr({
                      'type': 'button',
                      'class': 'btn btn-danger remRejectReasonCode',
                      'onclick': 'removeDisputeFeeList(' + id + ')',
                      'value': 'Remove',
                    }))
                  )
                );
		
		
		$('#tabnew').show();
		index++;
	} else {
		$('#tabnew').hide();
	}
	clear();
	}
}

function removeDisputeFeeList(id) {
	if (rejectReasonCodeList.length > 0) {
		$(`#tabnew_${id}`).remove();
	} else {
		$(`#tabnew_${id}`).remove();
		$('#tabnew').hide();
	}
	rejectReasonCodeList.splice(id, 1);
}

function addOrUpdateRejectReasonCodeAll() {
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	if (rejectReasonCodeList.length > 0) {
		var loc = window.location;
		var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
		var linkurl = pathName + "/addAllRejectReasonCode";
		$.ajax({
			url: linkurl,
			type: "POST",
			dataType: "json",
			data: JSON.stringify(rejectReasonCodeList),
			"beforeSend": function (xhr) {
                xhr.setRequestHeader('_TransactToken', tokenValue);
            },
			contentType: "application/json; charset=utf-8",
			cache: false,
			success: function(response) {
				console.log(response)
				 if (response.status == "BSUC_0001") {
					 document.querySelectorAll('.remRejectReasonCode').forEach(item => item.disabled = true);
				document.getElementById('addmultiple').disabled = true;
				document.getElementById('bSubmit').disabled = true;
				document.getElementById('resetRejectReasonCode').disabled = true;
				document.getElementById('addRejectReasonCode').disabled = true;
				[...document.querySelectorAll("#addRejectReasonCode .row")].splice(0, 3).forEach(item => item.remove())
				$('#transitionSuccessMsg').show();
				 }
				 else {
					 $('#transitionErrorMsg').show();
				 }
				 
			},
			 error: function(_request, _status, _error) {
	                document.getElementById('transitionErrorMsg').style.display = 'block';
	                $('.panel').hide();
	            }
		});
	} else {
		alert("Please add the record first");
	}
}

function editRejectReasonCode(action, data) {
	var dynInput;
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	let linkurl = pathName + action;
	var form = document.createElement("form");
	form.method = "POST";
	var tokenValue=null
	if(document.getElementsByName("_TransactToken")[0])
		tokenValue= document.getElementsByName("_TransactToken")[0].value;
	tokenValue = $('<div>').text(tokenValue).html();
	var parameters = data.split(",");

	for (var i = 0; i < 13; ++i) {
		dynInput = document.createElement("input");
		dynInput.setAttribute("type", "hidden");
		dynInput.setAttribute("id", parameters[i]);
		dynInput.setAttribute("name", parameters[i]);
		++i;
		dynInput.setAttribute("value", parameters[i]);

		form.appendChild(dynInput);
	}
	dynInput = document.createElement("input");
	dynInput.setAttribute("type", "hidden");
	dynInput.setAttribute("id", parameters[14]);
	dynInput.setAttribute("name", parameters[14]);
	fieldValue = parameters.slice(15).join();
	console.log(fieldValue);
	dynInput.setAttribute("value", fieldValue);
	form.appendChild(dynInput);
	 dynInput = document.createElement("input");
	dynInput.setAttribute("type", "hidden");
	dynInput.setAttribute("id", "_TransactToken");
	dynInput.setAttribute("name", "_TransactToken");
	dynInput.setAttribute("value", tokenValue);
	form.appendChild(dynInput);
	
	document.body.appendChild(form); // added this	for firefox Browser
	encodeForm(form);	//Added by piyush for form encode

	form.action = linkurl;
	form.submit();
}

function clear() {
	$('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
	$('#snxtSuccessMessage').hide();
	document.getElementById('funcCode').value = "";
	document.getElementById('fieldName').value = "";
	document.getElementById('fieldValue').value = "";
	document.getElementById('relationalOperator').value = "";
	document.getElementById('fieldOperator').value = "";
	document.getElementById('subFieldName').value = "";
	document.getElementById('rejectCode').value = "";
	datas = {};
}

function resetAction() {
	$('#transitionSuccessMsg').hide();
	$('#transitionErrorMsg').hide();
	$('#snxtSuccessMessage').hide();
	document.getElementById("addRejectReasonCode").reset();

	$("#errfuncCode").find('.error').html('');
	$("#errfieldName").find('.error').html('');
	$("#errfieldValue").find('.error').html('');
	$("#errrelationalOperator").find('.error').html('');
	$("#errfieldOperator").find('.error').html('');
	$("#errsubFieldName").find('.error').html('');
	$("#errrejectCode").find('.error').html('');
}

function validateEditRejectReasonCode() {
	if (funcCode != document.getElementById("funcCode").value || fieldName != document.getElementById("fieldName").value|| fieldValue != document.getElementById("fieldValue").value || relationalOperator != document.getElementById("relationalOperator").value || subFieldName != document.getElementById("subFieldName").value || fieldOperator != document.getElementById("fieldOperator").value || rejectCode != document.getElementById("rejectCode").value) {
		if ($("#bUpdate")) {
			$("#bUpdate").prop("disabled", false);
		}
	} else {
		if ($("#bUpdate")) {
			$("#bUpdate").prop("disabled", true);
		}
	}
}

