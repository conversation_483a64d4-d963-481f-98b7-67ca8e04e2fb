package org.npci.settlenxt.adminportal.service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.cache.DataSecurityUtility;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.NewsAlertsRepository;
import org.npci.settlenxt.adminportal.repository.UserRepository;
import org.npci.settlenxt.common.cache.BaseSysParams;
import org.npci.settlenxt.portal.common.dto.BinDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.EmailGateWayDTO;
import org.npci.settlenxt.portal.common.dto.NewsAlertsDTO;
import org.npci.settlenxt.portal.common.dto.PasswordSettingsDTO;
import org.npci.settlenxt.portal.common.dto.RoleDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.npci.settlenxt.portal.common.dto.UserDTO;
import org.npci.settlenxt.portal.common.dto.UserInfoDTO;
import org.npci.settlenxt.portal.common.dto.UserToRoleDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BasePasswordSettingsService;
import org.npci.settlenxt.portal.common.service.BaseUserServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.npci.settlenxt.portal.common.util.BaseFunctionalityEnum;
import org.npci.settlenxt.portal.common.util.CredentialGenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;

@Transactional(rollbackFor = Throwable.class)
@Slf4j
@Service
public class UserServiceImpl extends BaseUserServiceImpl implements UserService {



	@Autowired
	UserRepository userRepository;

	@Autowired
	EmailGateWayUtil emailGateWayUtil;

	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	NewsAlertsRepository newsAlertsRepository;
	@Autowired
	BaseSysParams sysParams;
	@Autowired
	private BasePasswordSettingsService passwordSettingsService;

	@Autowired
	DataSecurityUtility dataSecurityUtil;

	@Value("${DEPLOYMENT_NETWORK}")
	private String networkType;

	private static final String PASSWORD = "PASSWORD";

	private static final String NPCI_PARTICIPANT = "Participant_Id";
	
	private static final String ALERT_SETTLEMENT_JOB = "AlertSettlementJob";

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void addEditUser(UserInfoDTO userInfoDto)  {

		int funcId;

		funcId = addEditAction(userInfoDto);
		userInfoDto.setCreatedBy(sessionDTO.getUserName());

		if (BaseCommonConstants.USER_TYPE_NPCI_ADMIN.equalsIgnoreCase(userInfoDto.getUserType())) {

			userInfoDto.setBankName(networkType);
			userInfoDto.setParticipantId(sysParams.getSystemKeyValue(networkType, NPCI_PARTICIPANT));

		}

		userInfoDto.setLastUpdatedBy(sessionDTO.getUserName());

		if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(userInfoDto.getRequestState())) {
			if (StringUtils.isNotBlank(userInfoDto.getParticipantName())
					&& userInfoDto.getParticipantName().contains("-")) {
				String participantId = userInfoDto.getParticipantName().substring(0,
						userInfoDto.getParticipantName().indexOf("-"));
				userInfoDto.setParticipantId(participantId);
				userInfoDto.setBankName(userRepository.getBankName(participantId));
			}
			setUserStatus(userInfoDto);
			userInfoDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
			userInfoDto.setLastOperation(BaseFunctionalityEnum.EDIT_USER.name());
			userInfoDto.setLastUpdatedOn(new Date());

			List<RoleDTO> mappedRole = userRepository.getUserRoleStgByUserId(userInfoDto.getUserId());

			Map<String, List<RoleDTO>> map = mappedRole.stream().collect(Collectors.groupingBy(RoleDTO::getRoleName));

			userRepository.updateUserStgOpr(encryptUserSensitiveData(userInfoDto));
			userRepository.deleteUserRoleMappingStg(userInfoDto.getUserId());
			mapUserToRole(userInfoDto, funcId, map);

		} else if (funcId == BaseCommonConstants.TRANSACT_FUCTIONALITY_EDITUSER) {

			if (StringUtils.isNotBlank(userInfoDto.getParticipantName())
					&& userInfoDto.getParticipantName().contains("-")) {
				String participantId = userInfoDto.getParticipantName().substring(0,
						userInfoDto.getParticipantName().indexOf("-"));
				userInfoDto.setParticipantId(participantId);
				userInfoDto.setBankName(userRepository.getBankName(participantId));
			}
			setUserStatus(userInfoDto);

			userInfoDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);
			userInfoDto.setLastUpdatedOn(new Date());

			userInfoDto.setLastOperation(BaseFunctionalityEnum.EDIT_USER.name());

			List<RoleDTO> mappedRole = userRepository.getUserRoleStgByUserId(userInfoDto.getUserId());

			Map<String, List<RoleDTO>> map = mappedRole.stream().collect(Collectors.groupingBy(RoleDTO::getRoleName));

			userRepository.updateUserStgOpr(encryptUserSensitiveData(userInfoDto));
			userRepository.deleteUserRoleMappingStg(userInfoDto.getUserId());

			mapUserToRole(userInfoDto, funcId, map);

		} else {
			userInfoDto.setLastOperation(BaseFunctionalityEnum.ADD_USER.name());
			userInfoDto.setUserId(userRepository.fetchIdFromUserIdSequence());
			userInfoDto.setIsPwdChangeReq("Y");
			userInfoDto.setLockStatus("U");
			userInfoDto.setStatus(BaseCommonConstants.USER_PASSWORD_RESET_PASSWORD);
			if (StringUtils.isNotBlank(userInfoDto.getParticipantName())
					&& userInfoDto.getParticipantName().contains("-")) {
				String participantId = userInfoDto.getParticipantName().substring(0,
						userInfoDto.getParticipantName().indexOf("-"));
				userInfoDto.setParticipantId(participantId);
				userInfoDto.setBankName(userInfoDto.getParticipantName());
				userInfoDto.setLoginId(userInfoDto.getParticipantId().substring(0, 4) + userInfoDto.getLoginId());
			} else {
				userInfoDto.setPrefix(networkType);
				userInfoDto.setLoginId(userInfoDto.getPrefix() + userInfoDto.getLoginId());
			}

			userInfoDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

			userInfoDto.setCreatedOn(new Date());
			userInfoDto.setLastUpdatedOn(null);
			userInfoDto.setLastUpdatedBy(null);

			userRepository.insertUserStg(encryptUserSensitiveData(userInfoDto));

			mapUserToRole(userInfoDto, funcId, null);

		}

		// mapping user bin details
		insertUserBinList(userInfoDto, funcId);

	}

	private void setUserStatus(UserInfoDTO userInfoDto) {
		if ("Active".equalsIgnoreCase(userInfoDto.getStatus())) {
			userInfoDto.setStatus(CommonConstants.USER_STATUS_ACTIVE);
		} else if ("Suspended".equalsIgnoreCase(userInfoDto.getStatus())) {
			userInfoDto.setStatus(BaseCommonConstants.USER_STATUS_SUSPENDED);
		} else if ("Reset".equalsIgnoreCase(userInfoDto.getStatus())) {
			userInfoDto.setStatus("Z");

		}
	}

	private int addEditAction(UserInfoDTO userInfoDto) {
		int funcId;
		if (BaseCommonConstants.EDIT_USER.equalsIgnoreCase(userInfoDto.getAddEditFlag())) {
			funcId = BaseCommonConstants.TRANSACT_FUCTIONALITY_EDITUSER;
		} else {
			funcId = BaseCommonConstants.TRANSACT_FUCTIONALITY_ADDUSER;
		}
		return funcId;
	}

	public void insertUserBinList(UserInfoDTO userInfoDto, int funcId) {


		List<BinDTO> mappedAcqBin = userRepository.getBinAcqListByUserId(userInfoDto.getUserId(),
				BaseCommonConstants.ACQUIRER_BIN_FLAG);

		Map<String, List<BinDTO>> map1 = mappedAcqBin.stream().collect(Collectors.groupingBy(BinDTO::getBinNumber));

		List<BinDTO> mappedIssBin = userRepository.getBinIssListByUserId(userInfoDto.getUserId(),
				BaseCommonConstants.ISSUE_BIN_FLAG);

		Map<String, List<BinDTO>> map2 = mappedIssBin.stream().collect(Collectors.groupingBy(BinDTO::getBinNumber));

		
		if (BaseCommonConstants.USER_TYPE_NPCI_ADMIN.equalsIgnoreCase(userInfoDto.getUserType())) {

			if (BaseCommonConstants.TRANSACT_FUCTIONALITY_ADDUSER == funcId) {
				mappingBinDetailsForNetworkAdminUser(userInfoDto,null, null);
					
			}
			else if (BaseCommonConstants.TRANSACT_FUCTIONALITY_EDITUSER == funcId) {
				userRepository.deleteUserBinStg(userInfoDto.getUserId());
				mappingBinDetailsForNetworkAdminUser(userInfoDto,map1,map2);
					
			}
			
			
			
		}

		if (BaseCommonConstants.TRANSACT_FUCTIONALITY_ADDUSER == funcId
				&& (BaseCommonConstants.PARTICIPANT.equalsIgnoreCase(userInfoDto.getAccessLevel())
						|| BaseCommonConstants.SETTLEMENT.equalsIgnoreCase(userInfoDto.getAccessLevel()))) {
			mappingBinDetailsForUser(userInfoDto, null, null);

		} else if (BaseCommonConstants.TRANSACT_FUCTIONALITY_EDITUSER == funcId
				&& (BaseCommonConstants.PARTICIPANT.equalsIgnoreCase(userInfoDto.getAccessLevel())
						|| BaseCommonConstants.SETTLEMENT.equalsIgnoreCase(userInfoDto.getAccessLevel()))) {

			userRepository.deleteUserBinStg(userInfoDto.getUserId());
			mappingBinDetailsForUser(userInfoDto, map1, map2);

		}

	}

	public void mappingBinDetailsForNetworkAdminUser(UserInfoDTO userInfoDto, Map<String, List<BinDTO>> map1,
			Map<String, List<BinDTO>> map2) {
		
		
		prepareBinListForUser(userInfoDto, map1, map2);
	}

	private void setAcqIssCreatedOn(Map<String, List<BinDTO>> map1, Map<String, List<BinDTO>> map2, BinDTO binDTO) {
		
		if (BaseCommonConstants.ACQUIRER_BIN_FLAG.equalsIgnoreCase(binDTO.getBinType())) {
	
				if (map1 != null && map1.containsKey(binDTO.getBinNumber())) {
	
					binDTO.setCreatedOn(map1.get(binDTO.getBinNumber()).get(0).getCreatedOn());
	
				} else {
					binDTO.setCreatedOn(new Date());
	
				}
	
			}
			else if (map2 != null && BaseCommonConstants.ISSUE_BIN_FLAG.equalsIgnoreCase(binDTO.getBinType())) {
	
				if (map2.containsKey(binDTO.getBinNumber())) {
	
					binDTO.setCreatedOn(map2.get(binDTO.getBinNumber()).get(0).getCreatedOn());
	
				} else {
					binDTO.setCreatedOn(new Date());
	
				}
	
			}
		
		

			binDTO.setCreatedOn(new Date());
			binDTO.setLastUpdatedOn(null);
		
	}

	

	public void mappingBinDetailsForUser(UserInfoDTO userInfoDto, Map<String, List<BinDTO>> map1,
			Map<String, List<BinDTO>> map2) {

		if (BaseCommonConstants.PARTICIPANT.equalsIgnoreCase(userInfoDto.getAccessLevel())) {

			prepareBinListForUser(userInfoDto, map1, map2);
		} else if (BaseCommonConstants.SETTLEMENT.equalsIgnoreCase(userInfoDto.getAccessLevel())) {
			BinDTO binDTO;
			List<BinDTO> binList = new ArrayList<>();

			String[] bin = userInfoDto.getBinIds().split("\\|");

			List<String> binDetailList = Arrays.asList(bin);
			List<Integer> binIdList = new ArrayList<>();
			int size = binDetailList.size();
			addBinIds(binDetailList, binIdList, size);
			List<BinDTO> settlement = userRepository.fetchBinDetailsByBinIds(binIdList);

			Map<Integer, String> binmap = new HashMap<>();
			for (BinDTO i : settlement)
				{
				binmap.put(i.getBinId(), i.getBinType());
				}

			for (int i = 0; i < bin.length; i = i + 2) {

				binDTO = new BinDTO();
				binDTO.setBinId(Integer.parseInt(bin[i]));
				binDTO.setParticipantId(userInfoDto.getParticipantId());
				binDTO.setBinNumber(bin[i + 1]);
				binDTO.setBinType(binmap.get(Integer.parseInt(bin[i]))); // setting bin type

				binList.add(binDTO);

			}

			if (CollectionUtils.isNotEmpty(binList)) {
				binList.forEach(binDTOs -> {
					binDTOs.setUserId(userInfoDto.getUserId());
					binDTOs.setCreatedBy(sessionDTO.getUserName());

					

						setAcqIssCreatedOn(map1,map2,binDTOs);

					

					userRepository.insertBinStg(binDTOs);
				});
			}

		}

	}

	private void prepareBinListForUser(UserInfoDTO userInfoDto, Map<String, List<BinDTO>> map1,
			Map<String, List<BinDTO>> map2) {
		List<BinDTO> binList = userRepository.getBinDetailListByParticipantId(userInfoDto.getParticipantId(),
				BaseCommonConstants.ISSUE_BIN_FLAG);
		List<BinDTO> acqBinList = userRepository.getAcqDetailListByParticipantId(userInfoDto.getParticipantId(),
				BaseCommonConstants.ACQUIRER_BIN_FLAG);

		if (!CollectionUtils.isEmpty(acqBinList)) {

			binList.addAll(acqBinList);
		}
		if (CollectionUtils.isNotEmpty(binList)) {

			binList.forEach(binDTO -> {
				binDTO.setUserId(userInfoDto.getUserId());
				binDTO.setCreatedBy(sessionDTO.getUserName());
				setAcqIssCreatedOn(map1,map2,binDTO);
				userRepository.insertBinStg(binDTO);
			});
		}
	}

	private void addBinIds(List<String> binDetailList, List<Integer> binIdList, int size) {
		if (size > 0) {
			for (int i = 0; i < size; i+=2) {
				binIdList.add(Integer.valueOf(binDetailList.get(i)));
				
			}
		}
	}

	@Override
	@Transactional
	public UserInfoDTO mapUserToRole(UserInfoDTO userInfoDTO, Integer funcId, Map<String, List<RoleDTO>> map) {
		RoleDTO roleDTO;

		List<RoleDTO> roleList = new ArrayList<>();

		String[] role = userInfoDTO.getRoleIds().split("\\|");

		userInfoDTO.setCreatedBy(sessionDTO.getUserName());

		for (int i = 0; i < role.length; i+=2) {
			roleDTO = new RoleDTO();
			roleDTO.setRoleName(role[i + 1]);
			if (role[i].matches("[0-9]+")) {
				roleDTO.setRoleId(Integer.parseInt(role[i]));

				roleList.add(roleDTO);
			}
			
		}

		roleList.forEach(roleDto -> {

			if (map != null) {

				if (map.containsKey(roleDto.getRoleName())) {

					roleDto.setCreatedOn(map.get(roleDto.getRoleName()).get(0).getCreatedOn());

				} else {
					roleDto.setCreatedOn(new Date());

				}
			} else {
				roleDto.setCreatedOn(new Date());
				roleDto.setLastUpdatedOn(null);

			}
		});

		userInfoDTO.setRoleList(roleList);

		if (CollectionUtils.isNotEmpty(roleList)) {
			userRepository.insertBatchUserRoletoFunc(userInfoDTO);
		}

		return userInfoDTO;
	}

	@Override
	@Transactional
	public UserDTO updateApproveOrRejectUser(String userId, String status, String remarks)  {

		if (StringUtils.isBlank(userId)) {
			throw new SettleNxtException("User id should not be empty", "");
		}
		long workFlowId = Long.parseLong(userId);
		UserDTO userDTO = userRepository.getUserStgInfoByUserId(workFlowId);

		userDTO.setCheckerComments(remarks);

		if (BaseCommonConstants.USER_TYPE_NPCI_ADMIN.equalsIgnoreCase(userDTO.getUserType())) {

			userDTO.setBankName(networkType);

		}
		LocalDateTime lt = LocalDateTime.now();

		userDTO.setLastUpdatedBy(sessionDTO.getUserName());

		// Update work flow tables here
		if ("A".equals(status)) {
			userDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			userDTO.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
		} else {
			userDTO.setRequestState(CommonConstants.REQUEST_STATE_REJECTED);
		}

		if (BaseFunctionalityEnum.ACTIVATE_USER.name().equalsIgnoreCase(userDTO.getLastOperation())
				|| BaseFunctionalityEnum.DELETE_USER.name().equalsIgnoreCase(userDTO.getLastOperation())) {
			updateActivateDeactivateUserFinal(userDTO, status);

		} else if (BaseFunctionalityEnum.ADD_USER.name().equalsIgnoreCase(userDTO.getLastOperation())
				|| BaseFunctionalityEnum.EDIT_USER.name().equalsIgnoreCase(userDTO.getLastOperation())) {
			updateLastOp(userDTO, lt);

			setStatus(userDTO);
			updateUserStaging(userDTO);
			if ("A".equals(status)) {

				userDTO.setStatusCode(updateApprovedUser(userDTO));
			}
		} else if (BaseFunctionalityEnum.BLOCK_USER.name().equalsIgnoreCase(userDTO.getLastOperation())
				|| BaseFunctionalityEnum.UNBLOCK_USER.name().equalsIgnoreCase(userDTO.getLastOperation())) {

			updateLockUnlockUser(userDTO, status);
		}
		return userDTO;
	}

	private void setStatus(UserDTO userDTO) {
		if ("R".equals(userDTO.getStatus()) || "Z".equals(userDTO.getStatus()) || "S".equals(userDTO.getStatus())
				|| "D".equals(userDTO.getStatus())) {
			userDTO.setStatus(userDTO.getStatus());
		} else {

			userDTO.setStatus("A");
		}
	}

	@Transactional
	public String updateApprovedUser(UserDTO userDTO) {

		SearchCriteriaDTO searchCriteriaDTO = new SearchCriteriaDTO();
		searchCriteriaDTO.setUserId(userDTO.getUserId());
		userDTO.setLockStatus("N");
		LocalDateTime lt = LocalDateTime.now();
		if (BaseFunctionalityEnum.ADD_USER.name().equalsIgnoreCase(userDTO.getLastOperation())
				|| !StringUtils.isNotBlank(userDTO.getPasswd())) {
			List<PasswordSettingsDTO> psswPolicyDTOs = passwordSettingsService.getPswdSettingsList();
			Map<String, String> policyMap = psswPolicyDTOs.stream()
					.collect(Collectors.toMap(PasswordSettingsDTO::getName, PasswordSettingsDTO::getValue));

			userDTO.setPasswd(CredentialGenUtil.generatePassword(policyMap));
			userDTO.setHashAsInfo(hashUserPassword(userDTO.getPasswd()));
		}

		UserDTO userDtoExist = userRepository.getUserInfoByUserId(searchCriteriaDTO);

		if (ObjectUtils.isEmpty(userDtoExist)) {
			// userDTO.getLastUpdatedOn()

			userDTO.setCreatedOn(lt);
			userDTO.setLastUpdatedOn(null);
			userDTO.setLastUpdatedBy(null);
			userRepository.saveUser(userDTO);
		} else {

			userRepository.updateUser(userDTO);
			if (userDTO.getStatus().equalsIgnoreCase("Z")) {
				UserInfoDTO userInfoDtos;
				userInfoDtos = editUserById(userDTO);
				userInfoDtos.setUserType(userDTO.getUserType());
				userInfoDtos.setStatus(CommonConstants.USER_PD_RESET_PD_STATUS);
				userDTO.setStatus(CommonConstants.USER_PD_RESET_PD_STATUS);
				resetPwd(userInfoDtos);
			}
		}

		// check if user has existing roles
		long count = userRepository.getAvailableUsertoRoleCountByUserId(searchCriteriaDTO);

		if (count == 0) {

			createUserRoleMapping(searchCriteriaDTO);
		} else {
			// delete existing userid mapped roleids and create new .

			userRepository.deleteUserRoleMapping(userDTO.getUserId());
			createUserRoleMapping(searchCriteriaDTO);
		}

		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(userDTO.getRequestState())) {

			List<BinDTO> binList = userRepository.getBinDetailListByUserIdApproval(userDTO.getUserId());

			if (CollectionUtils.isNotEmpty(binList)) {
				userRepository.deleteUserBin(userDTO.getUserId());
				binList.forEach(binDTO -> {
					binDTO.setCreatedBy(sessionDTO.getUserName());
					userRepository.insertUserBin(binDTO);
				});

			}

		}

		userDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);

		if (BaseFunctionalityEnum.ADD_USER.name().equalsIgnoreCase(userDTO.getLastOperation())) {
			String userNameContent = "";
			String mailContent = "";
			String purposeCode = "";
			purposeCode = BaseCommonConstants.TRANSACT_ADDNEWUSER;
			EmailGateWayDTO emailGateWayDTO = emailGateWayUtil.fetchEmailContentData(purposeCode);

			mailContent = emailGateWayDTO.getEmailContent();
			UserDTO userInfo = setName(userDTO);
			String userName=userNameStringBuilder(userInfo);
			


 userNameContent = mailContent.replace(BaseCommonConstants.EMAIL_USER_ID, userDTO.getLoginId())
			 .replace(PASSWORD, userDTO.getPasswd()).replace(BaseCommonConstants.EMAIL_USER_NAME, userName);
		

			emailGateWayDTO.setUserId(userDTO.getUserId());
			emailGateWayDTO.setReceiverEmailID(userDTO.getEmailId());
			emailGateWayDTO.setPurpose(purposeCode);
			emailGateWayDTO.setEmailContent(userNameContent);
			emailGateWayDTO.setIsPwdEmail("Y");
			emailGateWayDTO.setStatus("P");
			emailGateWayDTO.setStatusDesc(BaseCommonConstants.EMAIL_DESC_PENDING);

			emailGateWayDTO = emailGateWayUtil.logEmailData(emailGateWayDTO);

			if (emailGateWayDTO.getStatusCode().equals(CommonConstants.TRANSACT_SUCCESS)) {

				userDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			} else {
				userDTO.setStatusCode(CommonConstants.TRANSACT_FAIL);

			}
		}
		return userDTO.getStatusCode();
	}

	private UserInfoDTO editUserById(UserDTO userDTO) {
		UserInfoDTO userInfoDtos;
		if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(userDTO.getRequestState())) {
			userInfoDtos = getRejectedUserForEditById(String.valueOf(userDTO.getUserId()));
		} else {
			userInfoDtos = getUserForEditById(String.valueOf(userDTO.getUserId()));
		}
		return userInfoDtos;
	}

	public void createUserRoleMapping(SearchCriteriaDTO searchCriteriaDTO) {

		List<UserToRoleDTO> userroleStgDtoList = userRepository.getUserRoleStgMappingByUserId(searchCriteriaDTO);
		userroleStgDtoList.forEach(userroleStgDtoLists -> {

			userroleStgDtoLists.setStatus("A");
			userroleStgDtoLists.setLastUpdatedBy(sessionDTO.getUserName());

		});

		userroleStgDtoList.forEach(userroleStgDto -> userRepository.saveUserRoleMapping(userroleStgDto));
	}

	@SuppressWarnings("null")
	@Override
	public UserDTO discardRejectedUserEntry(String userId) throws SettleNxtException {

		if (StringUtils.isBlank(userId)) {
			throw new SettleNxtException("User Id is empty", "");
		}

		UserDTO userDTO = getUserStgInfo(userId);
		SearchCriteriaDTO search = new SearchCriteriaDTO();
		search.setUserId(Integer.parseInt(userId));

		UserDTO userDtoDB = userRepository.getUserInfoByUserId(search);

		UserInfoDTO userInfoDTO = new UserInfoDTO();
		userInfoDTO.setLastUpdatedBy(sessionDTO.getUserName());

		if (null == userDtoDB) {
			userInfoDTO.setRequestState(CommonConstants.REQUEST_STATE_DISCARDED);

			userInfoDTO.setLockStatus("U");
		} else {
			userDtoDB.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			userDtoDB.setLastUpdatedBy(sessionDTO.getUserName());
			LocalDateTime lt = LocalDateTime.now();
			userDtoDB.setLastUpdatedOn(lt);
		}
		userInfoDTO.setUserId(userDTO.getUserId());

		if (null == userDtoDB) {
			userRepository.deleteUserStg(userDTO.getUserId());
			userRepository.deleteUserRoleMappingStg(userDTO.getUserId());
			userRepository.deleteUserBinStg(userDTO.getUserId());

		} else {

			userRepository.updateUserStg(userDtoDB);
			userRepository.deleteUserRoleMappingStg(userDTO.getUserId());
			createUserRoleMappingDiscard(userDTO.getUserId());

			if (BaseCommonConstants.USER_TYPE_BANK_ADMIN.equalsIgnoreCase(userDtoDB.getUserType())) {
				createUserBinDetails(userDtoDB);
			}
		}

		return userDTO;

	}

	private void createUserBinDetails(UserDTO userDTO) {

		List<BinDTO> binList = userRepository.getBinDetailListByUserId(userDTO.getUserId());
		if (CollectionUtils.isNotEmpty(binList)) {

			userRepository.deleteUserBinStg(userDTO.getUserId());
			binList.forEach(binDTO -> userRepository.insertBinStg(binDTO));
		}
	}

	private void createUserRoleMappingDiscard(int userId) {
		List<UserToRoleDTO> userroleStgDtoList = userRepository.getRoleUserMappingByUserId(userId);

		userroleStgDtoList.forEach(userroleStgDtoLists -> {
			userroleStgDtoLists.setStatus("A");
			userroleStgDtoLists.setLastUpdatedBy(sessionDTO.getUserName());

			userroleStgDtoLists.setWorkFlowId(0);
			userroleStgDtoLists.setFuncId(0);

		});

		userroleStgDtoList.forEach(userroleStgDto -> userRepository.insertUserRoleMappingStg(userroleStgDto));
	}

	@Override
	@Transactional
	public String resetPwd(UserInfoDTO userInfoDto) {

		String userNameContent = "";
		String mailContent = "";
		String purposeCode = "";

		List<PasswordSettingsDTO> policySettingsList = passwordSettingsService.getPswdSettingsList();
		Map<String, String> policyMap = policySettingsList.stream()
				.collect(Collectors.toMap(PasswordSettingsDTO::getName, PasswordSettingsDTO::getValue));
		userInfoDto.setIsPwdChangeReq(BaseCommonConstants.TRANSACT_STATUS_YES);
		if (!StringUtils.isBlank(userInfoDto.getEmailId())) {
			userInfoDto.setEmailId(dataSecurityUtil.encrypt(userInfoDto.getEmailId()));
		}
		EmailGateWayDTO emailGateWayDTO = new EmailGateWayDTO();

		try {
			if (userInfoDto.getStatus().equals(BaseCommonConstants.USER_PD_RESET_PD_STATUS))

			{
				userInfoDto.setPasswd(CredentialGenUtil.generatePassword(policyMap));
				userInfoDto.setHashAsInfo(hashUserPassword(userInfoDto.getPasswd()));

				userRepository.resetPwd(userInfoDto);
				userRepository.resetUserStatusStg(userInfoDto);

				purposeCode = BaseCommonConstants.TRANSACT_RESETPASSWORD;

				emailGateWayDTO = emailGateWayUtil.fetchEmailContentData(purposeCode);

				mailContent = emailGateWayDTO.getEmailContent();
				
				
				userNameContent = mailContent.replace(BaseCommonConstants.EMAIL_USER_ID, userInfoDto.getLoginId())
						.replace(PASSWORD, userInfoDto.getPasswd());

				return prepareEmailContentForAdminPortal(userInfoDto, userNameContent, purposeCode, emailGateWayDTO);
			} else if (userInfoDto.getStatus().equals(BaseCommonConstants.TRANSACT_UNLOCK)) {
				purposeCode = "UNLOCK_USR";
				emailGateWayDTO = emailGateWayUtil.fetchEmailContentData(purposeCode);
				mailContent = emailGateWayDTO.getEmailContent();

				userNameContent = mailContent.replace(BaseCommonConstants.EMAIL_USER_ID, userInfoDto.getLoginId());

				return prepareEmailContentForAdminPortal(userInfoDto, userNameContent, purposeCode, emailGateWayDTO);
			}
		} catch (Exception ex) {
			log.error("exception {} ", ex.getMessage(), ex);
		} finally {
			policySettingsList.clear();
			userNameContent = null;
			mailContent = null;
			purposeCode = null;
			emailGateWayDTO = null;
		}

		return BaseCommonConstants.FAIL_STATUS;
	}

	private String prepareEmailContentForAdminPortal(UserInfoDTO userInfoDto, String userNameContent,
			String purposeCode,
			EmailGateWayDTO emailGateWayDTO) {
		emailGateWayDTO.setUserId(userInfoDto.getUserId());
		emailGateWayDTO.setReceiverEmailID(userInfoDto.getEmailId());
		emailGateWayDTO.setPurpose(purposeCode);
		
		emailGateWayDTO.setIsPwdEmail("Y");
		emailGateWayDTO.setStatus("P");
		emailGateWayDTO.setStatusDesc(BaseCommonConstants.EMAIL_DESC_PENDING);
		UserInfoDTO userInfo=userInfoDto;
		String userName=userNameStringBuilderWithoutDecrypt(userInfo);
		userNameContent = userNameContent.replace(BaseCommonConstants.EMAIL_USER_NAME, userName);
		emailGateWayDTO.setEmailContent(userNameContent);
		emailGateWayDTO = emailGateWayUtil.logEmailData(emailGateWayDTO);
		if (emailGateWayDTO.getStatusCode().equals(BaseCommonConstants.TRANSACT_SUCCESS)) {
			return BaseCommonConstants.TRANSACT_SUCCESS;
		}
		return BaseCommonConstants.TRANSACT_FAIL;
	}

	
	public static <T> List<T> convertArrayToList(T[] array) {

		return Arrays.asList(array);
	}

	@Override
	public List<UserDTO> getUserList(String[] bankList, String[] roleList) {

		List<String> bankNameList = convertArrayToList(bankList);

		List<String> roleTypeList = convertArrayToList(roleList);
		return userRepository.getUserListForNewsandAlerts(CommonConstants.REQUEST_STATE_APPROVED, bankNameList,
				roleTypeList);

	}

	@Override
	public UserDTO verifyUserSecretAnswer(UserDTO userDTO) {

		return verifyUserSecretAnswerCommon(userDTO);
	}

	@Override
	public List<NewsAlertsDTO> getNewsAlertsList(String userName) {
		List<NewsAlertsDTO> listSpecific = newsAlertsRepository.getNewsAlertsListSpecific(userName);
		List<NewsAlertsDTO> listCommon = newsAlertsRepository.getNewsAlertsListCommon();
		listSpecific.addAll(listCommon);

		return verifySchConditionNewsList(listSpecific);

	}

	public static void moveObjectToTop(List<NewsAlertsDTO> list, NewsAlertsDTO targetObject) {
        if (list == null || list.isEmpty() || targetObject == null) {
            return;
        }

 

        int index = list.indexOf(targetObject);
        if (index >= 0) {
            list.remove(index);
            list.add(0, targetObject);
        }
    }
	@Override
	public List<NewsAlertsDTO> getNewsAlertsListPublic() {
		List<NewsAlertsDTO> listPublic = newsAlertsRepository.getNewsAlertsListPublic();
		NewsAlertsDTO targetObject = listPublic.stream()
                .filter(newsAlertObj -> ALERT_SETTLEMENT_JOB.equals(newsAlertObj.getCreatedBy())).findFirst()
                .orElse(null);
        if (targetObject != null) {
            moveObjectToTop(listPublic, targetObject);
        }
		return verifySchConditionNewsList(listPublic);
	}

	@Override
	public String getUserHierarchyAccessLevel() {

		return userRepository.getUserAccessHierarchy(sessionDTO.getUserType());
	}

	@Override
	public List<CodeValueDTO> getUserHierarchyAccessLevelList(List<String> userList) {
		return userRepository.getUserAccessHierarchyList(userList);
	}

	private Map<String, Integer> prepareWeekMapForAdminPortal() {
		Map<String, Integer> weekMap = new HashMap<>();
		weekMap.put("Monday", 1);
		weekMap.put("Tuesday", 2);
		weekMap.put("Wednesday", 3);
		weekMap.put("Thursday", 4);
		weekMap.put("Friday", 5);
		weekMap.put("Saturday", 6);
		weekMap.put("Sunday", 7);
		return weekMap;
	}

	public List<NewsAlertsDTO> verifySchConditionNewsList(List<NewsAlertsDTO> list) {
		List<NewsAlertsDTO> newsList = new ArrayList<>();
		LocalDate currentdate = LocalDate.now();

		if (CollectionUtils.isNotEmpty(list)) {
			for (NewsAlertsDTO newsDTO : list) {

				int newsAlertDays = CommonConstants.NEWS_ALERT_NEW_DAYS;

				LocalDateTime startDate = LocalDateTime.ofInstant(newsDTO.getFromDate().toInstant(),
						ZoneId.systemDefault());

				LocalDateTime endDate = startDate.plusDays(newsAlertDays);
				LocalDateTime testDate = LocalDateTime.now();

				if (testDate.isAfter(startDate) && testDate.isBefore(endDate)) {
					newsDTO.setOldOrNewFlag("Y");
					if (!("ALERTS".equalsIgnoreCase(newsDTO.getIsType())
							&& "Y".equalsIgnoreCase(newsDTO.getCritical())) && (!ALERT_SETTLEMENT_JOB.equals(newsDTO.getCreatedBy()))) {
						String newData = "*New* ";
						newsDTO.setTitle(newData + newsDTO.getTitle() + " " + newData);}
					
				}

				periodToShowNewsAlerts(newsList, currentdate, newsDTO);
			}

		}

		return newsList;
	}

	private void periodToShowNewsAlerts(List<NewsAlertsDTO> newsList, LocalDate currentdate, NewsAlertsDTO newsDTO) {
		if (CommonConstants.PERIOD_TYPE_DAILY.equalsIgnoreCase(newsDTO.getPeriodType())) {
			newsList.add(newsDTO);
		} else if (CommonConstants.PERIOD_TYPE_WEEKLY.equalsIgnoreCase(newsDTO.getPeriodType())) {
			DayOfWeek week = currentdate.getDayOfWeek();
			Map<String, Integer> weekMap = prepareWeekMapForAdminPortal();
			if (week.getValue() >= weekMap.get(newsDTO.getFreqFrom())
					&& week.getValue() <= weekMap.get(newsDTO.getFreqTo())) {
				newsList.add(newsDTO);
			}

		} else if (CommonConstants.PERIOD_TYPE_MONTHLY.equalsIgnoreCase(newsDTO.getPeriodType())) {
			int currentDay = currentdate.getDayOfMonth();

			if (currentDay >= Integer.parseInt(newsDTO.getFreqFrom())
					&& currentDay <= Integer.parseInt(newsDTO.getFreqTo())) {
				newsList.add(newsDTO);
			}
		}
	}

	@Override
	public UserDTO updateApproveOrRejectBulkUser(List<Integer> userIdList, String status, String remarks) {

		UserDTO userInfoDto = new UserDTO();

		List<UserDTO> userIdarr = getBulkUserStgInfoByUserIdList(userIdList);

		// mapping userId to DTO
		Map<Integer, List<UserDTO>> userMap = userIdarr.stream().collect(Collectors.groupingBy(UserDTO::getUserId));

		if (!userIdarr.isEmpty()) {
			for (UserDTO uId:userIdarr) {

				try {

					List<UserDTO> userdto = userMap.get(uId.getUserId());
					UserDTO userDTO = userdto.get(0);

					if (userDTO == null) {
						throw new SettleNxtException("UserId should not be empty", "");
					}
					else {
						userDTO.setCheckerComments(remarks);
						setStatus(userDTO);

						userDTO.setCountry("INDIA");
						userDTO.setDesignation("USER");
						if (BaseCommonConstants.USER_TYPE_NPCI_ADMIN.equalsIgnoreCase(userDTO.getUserType())) {

							userDTO.setBankName(networkType);
						}
						LocalDateTime lt = LocalDateTime.now();

						userDTO.setLastUpdatedBy(sessionDTO.getUserName());

						setUserRequestState(status, userInfoDto, userDTO);

						updateUserAccount(status, userInfoDto, userDTO, lt);
					}
				} catch (Exception ex) {

					throw new SettleNxtException("Exception for UserId" + uId.getUserId(), "", ex);
				}

			}
		}

		return userInfoDto;
	}

	private void updateUserAccount(String status, UserDTO userInfoDto, UserDTO userDTO, LocalDateTime lt) {
		if (BaseFunctionalityEnum.ACTIVATE_USER.name().equalsIgnoreCase(userDTO.getLastOperation())
				|| BaseFunctionalityEnum.DELETE_USER.name()
						.equalsIgnoreCase(userDTO.getLastOperation())) {
			updateActivateDeactivateUserFinal(userDTO, status);

		} else if (BaseFunctionalityEnum.ADD_USER.name().equalsIgnoreCase(userDTO.getLastOperation())
				|| BaseFunctionalityEnum.EDIT_USER.name()
						.equalsIgnoreCase(userDTO.getLastOperation())) {
			// To update STG tables
			updateLastOp(userDTO, lt);
			updateUserStaging(userDTO);
			if ("A".equals(status)) {
				userDTO.setStatusCode(updateApprovedUser(userDTO));
				userInfoDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			}
		} else if (BaseFunctionalityEnum.BLOCK_USER.name().equalsIgnoreCase(userDTO.getLastOperation())
				|| BaseFunctionalityEnum.UNBLOCK_USER.name()
						.equalsIgnoreCase(userDTO.getLastOperation())) {

			updateLockUnlockUser(userDTO, status);
		}
	}

	private void setUserRequestState(String status, UserDTO userInfoDto, UserDTO userDTO) {
		if ("A".equals(status)) {
			userDTO.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			userInfoDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
			userDTO.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
		} else {
			userDTO.setRequestState(CommonConstants.REQUEST_STATE_REJECTED);
		}
	}

	private void updateLastOp(UserDTO userDTO, LocalDateTime lt) {
		if (BaseFunctionalityEnum.EDIT_USER.name().equalsIgnoreCase(userDTO.getLastOperation())) {
			userDTO.setLastOperation(BaseFunctionalityEnum.EDIT_USER.name());
			userDTO.setLastUpdatedOn(lt);
		} else {

			userDTO.setLastUpdatedOn(lt);
			userDTO.setLastOperation(BaseFunctionalityEnum.ADD_USER.name());
		}
	}

	public List<UserDTO> getBulkUserStgInfoByUserIdList(List<Integer> list) {

		return userRepository.getBulkUserInfoByUserIdList(list);
	}

	@Override
	public boolean checkIfDashBoardFuncExists(String funcName, int userId) {
		int count = userRepository.checkIfDashBoardFuncExists(funcName, userId);
		return count > 0;
	}

}
