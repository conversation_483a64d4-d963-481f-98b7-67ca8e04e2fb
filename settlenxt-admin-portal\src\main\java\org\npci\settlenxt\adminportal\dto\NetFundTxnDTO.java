package org.npci.settlenxt.adminportal.dto;

import java.time.LocalDate;
import java.util.Date;

import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JGlobalMap
public class NetFundTxnDTO {

	private static final long serialVersionUID = 1L;
	
	private String txnId;
	private String mti;
	private String funcCode;
	private String pan;
	private String panPrefix;
	private String panSuffix;
	private String acqRefData;
	private Double amtTran;
	private String tstampLocal;
	private String instIdAcq;
	private String feeTypCode;
	private String settlementDrCrIndicator;
	private String feeCcy;
	private String memMsgTxt;
	private LocalDate orgDateCapture;
	private String entityType;
	private String uniqueFileName;
	private String txnOriginatorInstId;
	private String txnDestinationInstId;
	private String txnInitiator;
	private boolean ack;
	private boolean incoming;
	private String recordNumber;
	private int userId;	
	private String checkerId;
	private String currentState;
	private String toState;
	private String tranCur;
	private String encryptedPan;
	private String encryptedtokenPan;
	private String tokenPan;
	private String participantIdIss;
	private String participantIdAcq;
	private String instIdIss;
	private String internalTrackNo;
	private String netReconDate;
	private String docFilePath;
	private Date dateRcvd;
	private String custCompDisputeFeeCode;
	private String filingDisputeFeeCode;
	private String penaltyDisputeFeeCode;
	private String memberDisputeFeeCode;
	private String caseNo;
	private String settlementBin;
	private String actionCode;
	private String feeName;
}
