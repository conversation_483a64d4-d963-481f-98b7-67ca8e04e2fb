<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script src="./static/js/validation/viewMcc.js" type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">

		<c:url value="getMcc" var="getMcc" />
		<form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewApproveMccConfig" modelAttribute="mccDTO"
			action="${getMcc}" autocomplete="off">
			<input type="hidden" id="mccId" value="${mccDTO.mccId}" />
			<input type="hidden" id="crtuser" value="${mccDTO.createdBy}" />
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default no_margin">
						<div class="panel-heading clearfix">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"> <spring:message
										code="mcc.listscreen.title" /></span></strong>
						</div>
						<div class="panel-body">
							<table class="table table-striped" style="font-size: 12px">
							<caption style="display:none;">MCC</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
								<tbody>
									<tr>
										<td><label><spring:message code="mcc.mccGroup" /><span
												style="color: red"></span></label></td>
										<td>${mccDTO.mccGroupName}</td>
										<td><label><spring:message code="mcc.mccCode" /><span
												style="color: red"></span></label></td>
										<td>${mccDTO.mccCode}</td>
										<td><label><spring:message code="mcc.mccDesc" /><span
												style="color: red"></span></label></td>
										<td>${mccDTO.mccDesc}</td>
										<td><label><spring:message code="mcc.status" /><span
												style="color: red"></span></label></td>
										<td><c:if test="${mccDTO.status =='A' }">ENABLE</c:if> <c:if
												test="${mccDTO.status =='I' }">DISABLE</c:if></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</form:form>

		<div class="row">
			<div class="col-sm-12 bottom_space">
				<hr />
				<div style="text-align:center">
					<button type="button" class="btn btn-danger"
						onclick="backAction('P','/showMcc');">
						<spring:message code="budget.backBtn" />
					</button>
					<c:if test="${mccDTO.requestState =='A' }">
					<sec:authorize access="hasAuthority('Edit Mcc Config')">

						<button type="button" class="btn btn-success"
							onclick="viewMcc('${mccDTO.mccId}','V','mainTab')">
							<spring:message code="sm.lbl.edit" />
						</button>
					
					</sec:authorize>
					</c:if>
				</div>
			</div>
		</div>
	</div>
</div>
