 $(document).ready(function () {

	 

	
	 $("#divmove").prop('disabled',true);
 

		$('#fileInput').on('change',function (){
				validateFileName();
	});



$('#year').on('keyup keypress blur change',function (){
	validateFromCommonVal('year', true, "SelectionBox", "", false);
	});
$('#month').on('keyup keypress blur change',function (){
	validateFromCommonVal('month', true, "SelectionBox", "", false);
	});
$('#reportNetwork').on('keyup keypress blur change',function () {
	validateFromCommonVal('reportNetwork', true, "SelectionBox", "", false);

});

 
 
 });

function specialCharCheck(){
	
	let flag = true;
	
	 const rawFileName = $("#fileInput").val().split('\\').pop().split(".");
	 var filenamearr = rawFileName[0].split("");
	 var specialChars = "#%&$*:<>?,\-+=@^`'()_;~[]/{|}";
	
	for (var j of specialChars) {
		if (filenamearr.indexOf(j) > -1) {
			flag = false;
		}
	}
	
	return flag;
	
}
 
 function validateFileName(){
	 
	 
	 const rawFileName = $("#fileInput").val().split('\\').pop().split(".");
	 const fileExtension = rawFileName[1];

	 const fileInput=document.getElementById('fileInput');
	 const fileSize=fileInput.files[0].size;
	 const MAX_FILE_SIZE =document.getElementById('maxFileSize').value;
	 
	 
	 
	

	 if (flag) {
		 
		 if (fileSize > MAX_FILE_SIZE) {
			 console.log("jere")
				$('#errFile').html('Size limit exceeded');
				$("#uploadId").prop('disabled', true);
				
			}

		 else if(fileExtension !== "zip"){
				$('#errFile').html('Only zip files can be uploaded');	
				$("#uploadId").prop('disabled', true);
			}
		 else{
			 	$('#errFile').html('');
				 $("#uploadId").prop('disabled', false);		
			}
				
		}
		else{
			$('#errFile').text('Special characters are not allowed in file name');
			$("#uploadId").prop('disabled', true);	
   }


 }
	
 
 
 
 function uploadFile(){
	
		
		var reportNetwork = document.getElementById("reportNetwork").value;
		
		 const fileInput=document.getElementById('fileInput');
		 const file=fileInput.files[0];

					
					var tokenValue = document.getElementsByName("_TransactToken")[0].value;
					var formData = new FormData();
					formData.append('file',file);
					formData.append('reportNetwork',reportNetwork);
					formData.append("_TransactToken", tokenValue);
					$.ajax({
						method: 'POST',
						url: "uploadReportandSignment",
						cache: false,
						processData: false,
						contentType: false,
						data: formData,
						success: async function(response) {
							if (response === "File uploaded successfully") {
								
								 $("#divmove").prop('disabled',false);
								
							}  else if(response.startsWith("File upload not success")){
								$('#errorStatus3').text('Something went wrong. Please try again!');
								$('#errorStatus3').show();
							}
						},
						
					});
				
			
		
	}



function MoveReports(){
    
	 var isValid=true;


         if (!validateFromCommonVal('year', true, "SelectionBox", "", false)) {
            isValid = false;
        }
         if (!validateFromCommonVal('month', true, "SelectionBox", "", false)) {
            isValid = false;
        }
         if (!validateFromCommonVal('reportNetwork', true, "SelectionBox", "", false)) {
            isValid = false;
        }
         

         
	if(isValid){
		var data ="reportNetwork,"+document.getElementById("reportNetwork").value+
		",year," + document.getElementById("year").value+
		",month," + document.getElementById("month").value;

		
	postData("/submitReportMovementAndSigning",data);
	}
}




function clearFields(){
    $("#year").val("0");
    $("#month").val("0");
  	$("#reportNetwork").val("0");
  	$("#fileInput").val('');
  	 $('#errorStatus3').hide();
  	 $('#errFile').text('');
 
 	
	$("#uploadId").prop('disabled', false);
 	
    }


 
function navigateTo(action) {
    var tokenValue = "lIpLsRjLtLDPSzoS2xPf9WXiF/M=";
    var data = "_vTransactToken," + tokenValue;
    postData(action, data);
}
