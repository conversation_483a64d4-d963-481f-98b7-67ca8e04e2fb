<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<tr>
	<td><label><spring:message code="RP_LBL_ReasonCodeDesc" /></label></td>
	<td id="messageReasonCode" style="max-width: 10rem;"><c:if
			test="${empty disTxnList.reasonCodeDesc}">N/A</c:if>${disTxnList.reasonCodeDesc }</td>
	<td><label><spring:message
				code="txn.detail.lbl.amountsAdditional" /></label></td>
	<td id="amtAdd"><c:choose>
			<c:when
				test="${not empty disTxnList.amtAdd && not empty disTxnList.tranCurDesc }">${disTxnList.tranCurDesc} ${disTxnList.amountAdd}</c:when>
			<c:otherwise>
				<c:choose>
					<c:when
						test="${not empty disTxnList.amtAdd &&  empty disTxnList.tranCurDesc}">N/A ${disTxnList.amountAdd}</c:when>
				</c:choose>
				<c:choose>
					<c:when test="${empty disTxnList.amtAdd}">N/A</c:when>
				</c:choose>
			</c:otherwise>
		</c:choose></td>
</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.amountTransaction" /></label></td>
	<td id="txnAmount">
				<c:choose>
					<c:when test="${not empty disTxnList.amtTran && not empty disTxnList.tranCurDesc }">${disTxnList.tranCurDesc} ${disTxnList.amountTran}</c:when>
				<c:otherwise>
					<c:choose>
						<c:when test="${not empty disTxnList.amtTran &&  empty disTxnList.tranCurDesc}">N/A ${disTxnList.amountTran}</c:when>
					</c:choose>
					<c:choose>
						<c:when test="${empty disTxnList.amtTran}">N/A</c:when>
					</c:choose>
				</c:otherwise>
				</c:choose>
			</td>
	<td><label><spring:message code="st.lbl.settAmt" /></label></td>
	<td id="settlementAmount">					
				<c:choose>
					<c:when test="${not empty disTxnList.settlementAmount && not empty disTxnList.tranCurDesc }">${disTxnList.tranCurDesc} ${disTxnList.settlementAmount}</c:when>
					<c:otherwise>
						<c:choose>
							<c:when test="${not empty disTxnList.settlementAmount &&  empty disTxnList.tranCurDesc}">N/A ${disTxnList.settlementAmount}</c:when>
						</c:choose>
						<c:choose>
							<c:when test="${empty disTxnList.settlementAmount}">N/A</c:when>
						</c:choose>
					</c:otherwise>
				</c:choose>
	</td>
</tr>
<tr>
	<td><label><spring:message code="txn.detail.lbl.controlNo" /></label></td>
	<td id="controlNumber"><c:if
			test="${empty disTxnList.ctrlNo}">N/A</c:if>${disTxnList.ctrlNo }</td>
	<td><label><spring:message
				code="txn.detail.lbl.raiseDateAndTime" /></label></td>
	<td class="raiseDateAndTime"  id="raiseDateAndTime"><c:if
			test="${empty disTxnList.tstampLocal}">N/A</c:if>${disTxnList.tstampLocal}</td>	
</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.rgcsSettlementDate" /></label></td>
	<td class="rgcsSettlementDate"  id="rgcsSettlementDate"><c:if test="${empty disTxnList.netReconDate}">N/A</c:if>${disTxnList.netReconDate}</td>
	<td><label><spring:message
				code="txn.detail.lbl.fullPartialIndicator" /></label></td>
	<td id="fullPartialIndicator">
					<c:choose>
						<c:when test="${empty disTxnList.partialInd}">
							N/A
						</c:when>
						<c:otherwise>
							<c:choose>
								<c:when test = "${disTxnList.partialInd == 'F'}">
									${disTxnList.partialInd} - Full
								</c:when>
								<c:when test = "${disTxnList.partialInd == 'P'}">
									${disTxnList.partialInd} - Partial
								</c:when>
							</c:choose>
						</c:otherwise>
					</c:choose>
			</td>
</tr>
<tr>
	<td><label><spring:message
				code="txn.detail.lbl.internalTrackingNo" /></label></td>
	<td id="internalTrackingNumber"><c:if
			test="${empty disTxnList.internalTrackNo}">N/A</c:if>${disTxnList.internalTrackNo}</td>
	<td><label><spring:message code="txn.detail.lbl.checkerUserName" /></label></td>
	<td id="checkerUserName"><c:if
			test="${empty disTxnList.checkerId}">N/A</c:if>${disTxnList.checkerId}</td>
</tr>
<tr>
	<td><label><spring:message code="txn.detail.lbl.makerUserName" /></label></td>
	<td id="makerUserName"><c:if 
			test="${empty disTxnList.makerId}">N/A</c:if>${disTxnList.makerId}</td>
	<td></td>
	<td></td>
</tr>