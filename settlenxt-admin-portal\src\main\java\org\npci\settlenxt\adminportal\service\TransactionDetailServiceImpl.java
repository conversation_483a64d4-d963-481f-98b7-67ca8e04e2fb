package org.npci.settlenxt.adminportal.service;

import java.io.IOException;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;
import org.npci.settlenxt.adminportal.common.cache.SysParams;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.FeeDTO;
import org.npci.settlenxt.adminportal.repository.TransactionDetailRepository;
import org.npci.settlenxt.common.cache.BaseDataSecurityUtility;
import org.npci.settlenxt.portal.common.dto.ActionCodeDTO;
import org.npci.settlenxt.portal.common.dto.BinDTO;
import org.npci.settlenxt.portal.common.dto.DisputeDocumentDTO;
import org.npci.settlenxt.portal.common.dto.DisputeTxnModel;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;
import org.npci.settlenxt.portal.common.dto.ParticipantDTO;
import org.npci.settlenxt.portal.common.dto.SettlementBinDTO;
import org.npci.settlenxt.portal.common.dto.TxnSettlementDTO;
import org.npci.settlenxt.portal.common.service.BaseTransactionDetailServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.npci.settlenxt.portal.common.util.TLVParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class TransactionDetailServiceImpl extends BaseTransactionDetailServiceImpl implements TransactionDetailService {

	private static final String FAILURE = "failure";

	@Autowired
	private TransactionDetailRepository transactionDetailRepo;

	@Autowired
	BaseDataSecurityUtility baseDataSecurityutil;
	

	private static final String YYYYMMDD_WITH_SPERATOR = "yyyy-MM-dd";
	
	private static final String SUCCESS = "Success";
	
	private static DateTimeFormatter formatterYYYYMMDD = DateTimeFormatter.ofPattern(YYYYMMDD_WITH_SPERATOR);

	private DateTimeFormatter formatterYYMMDD = DateTimeFormatter.ofPattern("yyMMdd");

	private static final String FETCH_TXN_ERROR_LOG="Error while Fetching the transaction details {}";
	private DateTimeFormatter formatterYYYYMMDDHHMMSS = DateTimeFormatter.ofPattern("YYYYMMDDHHMMSS");

	@Autowired
	SysParams sysParam;

	@Autowired
	private FileMoverHandler fileMover;
	
	@Autowired
	ActionCodeCacheService actionCodeCache;
	
	@Override
	public List<ParticipantDTO> getParticipantIdList() {
		return transactionDetailRepo.getParticipantIdList();
		
	}

	@Override
	public List<TxnSettlementDTO> getTransactionDetails(TxnSettlementDTO txnSettlementDTO) {

		List<String> presentmentTableList = new ArrayList<>();
		List<String> txnTableNameList = new ArrayList<>();
		List<TxnSettlementDTO> txnDetails = new ArrayList<>();
		Set<String> orgTxnIdSet = new HashSet<>();
		setTxnPresenmentTableCommon(txnSettlementDTO, txnTableNameList, presentmentTableList);
		setFromDateFuncCode(txnSettlementDTO);
		String revTxnActCodes = sysParam.getSystemKeyValue(BaseCommonConstants.KEY_DISPUTE, "REV_TXN_ACT_CD");
		
		try {
			if (StringUtils.isNotBlank(txnSettlementDTO.getComplaintNumber())) {
				txnDetails = disputeTxnDetailSearchWithCRN(txnSettlementDTO);
			} else {
			if (StringUtils.isBlank(txnSettlementDTO.getActionCode())) {
				txnSettlementDTO.setFuncCode("");
				txnDetails = getAuthDetails(txnSettlementDTO, txnTableNameList, txnDetails, orgTxnIdSet);
			
				List<TxnSettlementDTO> revtxnDetails = reversalTxnDetails(txnSettlementDTO,orgTxnIdSet);
				txnDetails.addAll(revtxnDetails);
				List<TxnSettlementDTO> presentmentTxnDetails = presentmentDetails(txnSettlementDTO, presentmentTableList,orgTxnIdSet);
				txnDetails.addAll(presentmentTxnDetails);
				List<TxnSettlementDTO> disputeTxnDetails = disputeTranactionDetails(txnSettlementDTO,orgTxnIdSet);
				txnDetails.addAll(disputeTxnDetails);
				List<TxnSettlementDTO> netFundTxnDetails = netFundTranactionDetails(txnSettlementDTO,orgTxnIdSet);
				txnDetails.addAll(netFundTxnDetails);
			} else {
				txnDetails = setTxnDetailsBasedOnCodes(txnSettlementDTO, txnTableNameList, presentmentTableList, revTxnActCodes,
						txnDetails, orgTxnIdSet);
			}
			}
			for (TxnSettlementDTO txnSettlementDTO2 : txnDetails) {
				populateSubFieldDe48(txnSettlementDTO2);
				populateSubFieldDe61(txnSettlementDTO2);
			}
		} catch (Exception ex) {
			ex.printStackTrace();;
			log.info("Error while Fetching the transaction details {} ", ex.getCause(),ex);
		}
		return txnDetails;

	}

	

	private List<TxnSettlementDTO> getAuthDetails(TxnSettlementDTO txnSettlementDTO, List<String> txnTableNameList,
			List<TxnSettlementDTO> txnDetails, Set<String> orgTxnIdSet) {
		for (String tableName : txnTableNameList) {
			txnSettlementDTO.setOriginalTableName(tableName);
			String originalTableName = txnSettlementDTO.getOriginalTableName();
			String funcCode = txnSettlementDTO.getFuncCode();
			String tokenPan = txnSettlementDTO.getTokenPan();
			String rrn = txnSettlementDTO.getRrn();
			String partId = txnSettlementDTO.getPartId();

			List<TxnSettlementDTO> authTxnDetails = null;
			authTxnDetails = internationalNetworkType
					? transactionDetailRepo.getIntrntlTransactionDetails(txnSettlementDTO,
							concatRRNForInternational(txnSettlementDTO.getRrn()))
					: transactionDetailRepo.getTransactionDetails(originalTableName, funcCode, tokenPan, rrn, partId);
			if (!authTxnDetails.isEmpty()) {
				txnDetails.addAll(authTxnDetails);
				txnDetails = txnDetails.stream().map(ele -> setDefaultData(ele, txnSettlementDTO,orgTxnIdSet))
						.collect(Collectors.toList());
			}
		}
		return txnDetails;
	}

	private List<TxnSettlementDTO> setTxnDetailsBasedOnCodes(TxnSettlementDTO txnSettlementDTO, List<String> txnTableNameList,
			List<String> presentmentTableList, String revTxnActCodes, List<TxnSettlementDTO> txnDetails,
			Set<String> orgTxnIdSet) {
		if (txnSettlementDTO.getActionCode().equalsIgnoreCase(BaseCommonConstants.SMS_ACTION_CODE)
				|| txnSettlementDTO.getActionCode().equalsIgnoreCase(BaseCommonConstants.DMS_ACTION_CODE)
				|| txnSettlementDTO.getActionCode().equalsIgnoreCase(BaseCommonConstants.OFFLINE_PRESENTMENT)) {
			for (String tableName : txnTableNameList) {
				txnSettlementDTO.setOriginalTableName(tableName);
				String originalTableName = txnSettlementDTO.getOriginalTableName();
				String funcCode = txnSettlementDTO.getFuncCode();
				String tokenPan = txnSettlementDTO.getTokenPan();
				String rrn = txnSettlementDTO.getRrn();
				String partId = txnSettlementDTO.getPartId();
				txnDetails.addAll(internationalNetworkType
						? transactionDetailRepo.getIntrntlTransactionDetails(txnSettlementDTO,
								concatRRNForInternational(txnSettlementDTO.getRrn()))
						: transactionDetailRepo.getTransactionDetails(originalTableName, funcCode, tokenPan, rrn,
								partId));
				txnDetails = txnDetails.stream().map(ele -> setDefaultData(ele, txnSettlementDTO,orgTxnIdSet))
						.collect(Collectors.toList());
			}
			if(txnSettlementDTO.getActionCode().equalsIgnoreCase(BaseCommonConstants.OFFLINE_PRESENTMENT)) {
				txnDetails.addAll(presentmentDetails(txnSettlementDTO, presentmentTableList, orgTxnIdSet));
			}
		} else if (txnSettlementDTO.getActionCode().equalsIgnoreCase(BaseCommonConstants.PRESENTMENT_ACTION_CODE)) {
			txnDetails = presentmentDetails(txnSettlementDTO, presentmentTableList,orgTxnIdSet);
		} else if (StringUtils.equalsIgnoreCase(txnSettlementDTO.getActionCode(),
				BaseCommonConstants.NPCI_FUND_COLLECT_ACTION)
				|| StringUtils.equalsIgnoreCase(txnSettlementDTO.getActionCode(),
						BaseCommonConstants.NPCI_FUND_DISBURSE_ACTION)
				|| StringUtils.equalsIgnoreCase(txnSettlementDTO.getActionCode(),
						BaseCommonConstants.CASHBACK_ACTION)
				|| StringUtils.equalsIgnoreCase(txnSettlementDTO.getActionCode(),
						BaseCommonConstants.CASHBACK_REV_ACTION)) {
			txnDetails = netFundTranactionDetails(txnSettlementDTO,orgTxnIdSet);
		}else if(StringUtils.contains(revTxnActCodes,txnSettlementDTO.getActionCode())) {
			txnDetails = reversalTxnDetails(txnSettlementDTO, orgTxnIdSet);
		} else {
			txnDetails = disputeTranactionDetails(txnSettlementDTO, orgTxnIdSet);
		}
		return txnDetails;
	}

	public List<TxnSettlementDTO> presentmentDetails(TxnSettlementDTO txnSettlementDTO,List<String> presentmentTableList,Set<String> orgTxnIdSet) {
		List<TxnSettlementDTO> txnDetail = new ArrayList<>();
		for (String tableName : presentmentTableList) {
			txnSettlementDTO.setOriginalTableName(tableName);
			List<TxnSettlementDTO> presentmentTxnDetails = new ArrayList<>();
			List<TxnSettlementDTO> presentmentTxnDetailLists;
			Set<String> hashSet = new HashSet<>();

			presentmentTxnDetailLists = internationalNetworkType
					? transactionDetailRepo.getIntrntlPresentmentDetails(txnSettlementDTO,
							concatRRNForInternational(txnSettlementDTO.getRrn()))
					: transactionDetailRepo.getPresentmentDetails(txnSettlementDTO.getOriginalTableName(),
							txnSettlementDTO.getFuncCode(), txnSettlementDTO.getTokenPan(), txnSettlementDTO.getRrn(),
							txnSettlementDTO.getPartId());

			for (TxnSettlementDTO presentmentInfoDto : presentmentTxnDetailLists) {
				if (!hashSet.contains(presentmentInfoDto.getOrgTxnId())  && !orgTxnIdSet.contains(presentmentInfoDto.getOrgTxnId())) {
					presentmentTxnDetails.add(presentmentInfoDto);
				}
				hashSet.add(presentmentInfoDto.getOrgTxnId());
				orgTxnIdSet.add(presentmentInfoDto.getOrgTxnId());
			}
			txnDetail.addAll(presentmentTxnDetails);
			txnDetail = txnDetail.stream().map(ele -> setPresentmentDefaultData(ele, txnSettlementDTO))
					.collect(Collectors.toList());
		}
		return txnDetail;
	}
	
	public List<TxnSettlementDTO> reversalTxnDetails(TxnSettlementDTO txnSettlementDTO,Set<String> orgTxnIdSet){
		List<TxnSettlementDTO> txnDetail = new ArrayList<>();
		List<TxnSettlementDTO> reversalTxnDetail = new ArrayList<>();
		List<TxnSettlementDTO> reversalTxnDetailLists;
		Set<String> hashSet = new HashSet<>();
		reversalTxnDetailLists = internationalNetworkType
				? transactionDetailRepo.getIntrntlRevTranDetails(txnSettlementDTO,
						concatRRNForInternational(txnSettlementDTO.getRrn()))
				: transactionDetailRepo.getRevTranDetails(txnSettlementDTO.getFuncCode(),
				txnSettlementDTO.getTokenPan(), txnSettlementDTO.getRrn(), txnSettlementDTO.getPartId(),
				txnSettlementDTO.getFromDate(), txnSettlementDTO.getToDate());
		for (TxnSettlementDTO reversalTxnInfo : reversalTxnDetailLists) {
			if (!hashSet.contains(reversalTxnInfo.getOrgTxnId()) && !orgTxnIdSet.contains(reversalTxnInfo.getOrgTxnId())) {
				reversalTxnDetail.add(reversalTxnInfo);
			}
			hashSet.add(reversalTxnInfo.getOrgTxnId());
			orgTxnIdSet.add(reversalTxnInfo.getOrgTxnId());
		}
		txnDetail.addAll(reversalTxnDetail);
		txnDetail = txnDetail.stream().map(this::setRevTranDetails)
				.collect(Collectors.toList());
	
		return txnDetail;
	}

	public TxnSettlementDTO setDefaultData(TxnSettlementDTO txnSettlementDto, TxnSettlementDTO txnInfo,Set<String> orgTxnIdSet) {
		if(StringUtils.isBlank(txnSettlementDto.getOriginalTableName())) {
			txnSettlementDto.setOriginalTableName(txnInfo.getOriginalTableName());
		}
		txnSettlementDto.setPartId(txnInfo.getPartId());
		txnSettlementDto.setFromDate(txnInfo.getFromDate());
		TxnSettlementDTO disputeTxnDto;
		if ((StringUtils.isNotBlank(txnSettlementDto.getPresentmentRecvdDate())
				|| txnInfo.getActionCode().equalsIgnoreCase(BaseCommonConstants.PRESENTMENT_ACTION_CODE))
				&& ("0110".equals(txnSettlementDto.getMti()) || "1240".equals(txnSettlementDto.getMti()))) {
			disputeTxnDto = transactionDetailRepo.getDisputeTxnStatus(txnSettlementDto.getAcqRefData(),
					txnSettlementDto.getPartId(), txnSettlementDto.getFromDate());
			if (disputeTxnDto != null) {
				txnSettlementDto.setMti(disputeTxnDto.getMti());
				txnSettlementDto.setFuncCode(disputeTxnDto.getFuncCode());
				txnSettlementDto.setToState(disputeTxnDto.getToState());
			} else {
				txnSettlementDto.setMti(BaseCommonConstants.PRESENTMENT_MTI);
				txnSettlementDto.setFuncCode(BaseCommonConstants.PRESENTMENT_FUNCCODE);
			}
		} else if (txnSettlementDto.getPresentment() == 1 && "0210".equals(txnSettlementDto.getMti())) {
			disputeTxnDto = transactionDetailRepo.getDisputeTxnStatus(txnSettlementDto.getAcqRefData(),
					txnSettlementDto.getPartId(), txnSettlementDto.getFromDate());
			if (disputeTxnDto != null) {
				txnSettlementDto.setMti(disputeTxnDto.getMti());
				txnSettlementDto.setFuncCode(disputeTxnDto.getFuncCode());
				txnSettlementDto.setToState(disputeTxnDto.getToState());
			}
		}
		preapreAcqPlatProdIdExtracted(txnSettlementDto);
		String maskedPanPrefix = StringUtils.rightPad(txnSettlementDto.getPanPrefix(), 12, "*");
		String panSuf = StringUtils.isNotBlank(txnSettlementDto.getPanSuffix()) ? txnSettlementDto.getPanSuffix()
				: "N/A";
		txnSettlementDto.setMaskPan(maskedPanPrefix + panSuf);
		txnSettlementDto.setOrgTxnId(txnSettlementDto.getTxnId());
		orgTxnIdSet.add(txnSettlementDto.getOrgTxnId());
		txnSettlementDto.setOrgTstampLocal(txnSettlementDto.getTstampLocal());
		return txnSettlementDto;
	}

	public TxnSettlementDTO setPresentmentDefaultData(TxnSettlementDTO txnSettlementDto, TxnSettlementDTO txnInfo) {
		LocalDate dateCapture = LocalDate.parse(txnSettlementDto.getDateCapture());
		String orginalTableName = BaseCommonConstants.TXN_SETTLEMENT_TABLE;
		orginalTableName += StringUtils.leftPad(String.valueOf(dateCapture.getDayOfMonth()), 2, "0")
				+ StringUtils.leftPad(String.valueOf(dateCapture.getMonthValue()), 2, "0")
				+ String.valueOf(dateCapture.getYear()).substring(2);
		txnSettlementDto.setOriginalTableName(orginalTableName);

		setMaskPanUserIdFuncCodeTostateMtiReceivedate(txnSettlementDto, txnInfo);
		setAcqProdId(txnSettlementDto);
		
		return txnSettlementDto;
	}

	private void setAcqProdId(TxnSettlementDTO txnSettlementDto) {
		preapreAcqPlatProdIdExtracted(txnSettlementDto);
	}

	public void populateSubFieldDe48(TxnSettlementDTO fintable) {
		String de48 = fintable.getAddlData48();
		Map<String, String> fields = new TLVParser().parseTLV(de48);
		fintable.setCardHldrInTaxPan(fields.get("067"));
	}

	public void populateSubFieldDe61(TxnSettlementDTO fintable) {
		String de61 = fintable.getAddlData61();
		if (de61 != null) {
			fintable.setMerchTelNo(StringUtils.substring(de61, 22, 25));
		}

	}

	@Override
	public String saveMemberActionData(TxnSettlementDTO transactionDetail,JSONObject jsonDocInfo) {
		try {
			transactionDetailRepo.saveTxnActionDetail(transactionDetail);
		} catch (Exception e) {
			log.info("Error while saving the Dispute Info {}",e.getCause());
			return "Error";
		}
		return BaseCommonConstants.DISPUTE_SUCCESS;

	}

	@Override
	public List<TxnSettlementDTO> getTransactionDetailsStaging(String searchType) {
		List<TxnSettlementDTO> txnDetails = new ArrayList<>();
		try {
			if (StringUtils.equals(searchType, BaseCommonConstants.APPROVE_REJECT_DISPUTE)) {
				txnDetails.addAll(transactionDetailRepo.getApprovedRejectedTxnDetails());
			} else {
				txnDetails.addAll(transactionDetailRepo.getTransactionDetailsStage());
			}
		} catch (Exception ex) {
			log.info(" Error while Fetching the transaction details {}", ex.getCause(),ex);
		}
		return txnDetails;
	}

	@Override
	public TxnSettlementDTO getTxnInfoStaging(String txnId,String status) {
		TxnSettlementDTO txnDetails;
		txnDetails = transactionDetailRepo.getTxnInfoStaging(txnId,status);
		if(StringUtils.equals(txnDetails.getDocInd(), BaseCommonConstants.YES_FLAG) && StringUtils.equals(txnDetails.getDocFilePath(), BaseCommonConstants.YES_FLAG)) {
			List<FileUploadDTO> filePathInfoList = transactionDetailRepo.getFilePathListStg(txnDetails.getTxnId());
			StringBuilder fileDocPathWithDocType = new StringBuilder();
			for(FileUploadDTO fileInfoDto : filePathInfoList) { 
				fileDocPathWithDocType.append(fileInfoDto.getDocumentPath() + "|" + fileInfoDto.getDocumentType());
				fileDocPathWithDocType.append(",");
			}
			fileDocPathWithDocType = new StringBuilder(StringUtils.substring(fileDocPathWithDocType.toString(), 0, fileDocPathWithDocType.length() - 1));
			txnDetails.setDocFilePath(fileDocPathWithDocType.toString());
		}
		return txnDetails;
	}

	@Override
	public void approveRejectDisputeInfo(TxnSettlementDTO disputePendingInfo) throws InterruptedException {
		String portal=BaseCommonConstants.ADMIN_PORTAL_TYPE;
		
		approveRejectDisputeInfoCommon(disputePendingInfo, portal);
	}
	

	@Override
	public TxnSettlementDTO setTransactionData(TxnSettlementDTO txnDetail, DisputeTxnModel txnActionDetail) {
		LocalDateTime currTime = LocalDateTime.now();
		txnDetail.setStageId(transactionDetailRepo.fetchIdFromDisputeStagSequence());
		setMtiFuncodeAmtPhoneDataCommon(txnDetail, txnActionDetail);

		txnDetail.setEntityType("N");

		txnDetail.setTxnOrgInstId(transactionDetailRepo.fetchOriginatorInstId());
		setDataForTransactionDetailCommon(txnDetail, txnActionDetail);
		setTxnIdOrgDateDateStatusCommon(txnDetail, currTime);

		return txnDetail;
	}

	
	@Override
	public List<TxnSettlementDTO> getPendingDisputeTxnList(TxnSettlementDTO transactionDetailDTO, String txnId) {
		LocalDate orgTranDate = LocalDate.parse(transactionDetailDTO.getDateCapture());
		List<TxnSettlementDTO> pendingDisputeTxnInfo = transactionDetailRepo.getPendingDisputeTxnList(txnId,
				orgTranDate);
		pendingDisputeTxnInfo = pendingDisputeTxnInfo.stream()
				.map(ele -> disputePendingFormatData(ele, transactionDetailDTO.getPartId(),
						transactionDetailDTO.getSearchPartId()))
				.toList();
		return pendingDisputeTxnInfo;
	}

	@Override
	public List<TxnSettlementDTO> getPendingDisputeNpciFundCashRevInfo(String txnId, String partId,
			String searchParticipant) {
		List<TxnSettlementDTO> pendingDisputeTxnInfo = transactionDetailRepo
				.getPendingDisputeNpciFundCashRevInfo(txnId);
		pendingDisputeTxnInfo = pendingDisputeTxnInfo.stream()
				.map(ele -> disputePendingFormatData(ele, partId, searchParticipant))
				.toList();
		return pendingDisputeTxnInfo;
	}

	@Override
	public List<FeeDTO> getFeeNameList() {
		return transactionDetailRepo.getFeeNameList();
		
	}

	@Override
	public Map<String, String> getPartSettleBinList() {
		Map<String, String> settlementBinList = new HashMap<>();
		List<SettlementBinDTO> binList = transactionDetailRepo.getSettlementBinList();
		for (SettlementBinDTO bin : binList) {
			settlementBinList.put(bin.getParticipantId(), bin.getSettlementBinNumber());
		}
		return settlementBinList;
	}

	@Override
	public List<TxnSettlementDTO> getPendingDisputeBulkTxnInfo(TxnSettlementDTO transactionDetailDTO) {
		LocalDate orgTranDate = LocalDate.parse(transactionDetailDTO.getDateCapture());
		List<TxnSettlementDTO> pendingDisputeTxnInfo = transactionDetailRepo
				.getPendingDisputeBulkTxnInfo(transactionDetailDTO.getTxnId(), orgTranDate);
		pendingDisputeTxnInfo = pendingDisputeTxnInfo.stream().map(ele -> disputePendingFormatData(ele,
				transactionDetailDTO.getPartId(), transactionDetailDTO.getSearchPartId()))
				.toList();
		return pendingDisputeTxnInfo;
	}

	@Override
	public Map<String, List<String>> getAcquirerIdList() {
		Map<String, List<String>> acquirerIdList = new HashMap<>();
		List<BinDTO> acqIdList = transactionDetailRepo.getAcquirerIdList();
		if (!acqIdList.isEmpty()) {
			for (BinDTO acqId : acqIdList) {
				if (!acquirerIdList.containsKey(acqId.getParticipantId())) {
					acquirerIdList.put(acqId.getParticipantId(), new ArrayList<>());
				}
				acquirerIdList.get(acqId.getParticipantId()).add(acqId.getAcquirerId());
			}
		}
		return acquirerIdList;
	}

	@Override
	public Map<String, List<String>> getIssuerIdList() {
		Map<String, List<String>> issuerIdList = new HashMap<>();
		List<BinDTO> issIdList = transactionDetailRepo.getIssuerIdList();
		if (!issIdList.isEmpty()) {
			for (BinDTO issId : issIdList) {
				if (!issuerIdList.containsKey(issId.getParticipantId())) {
					issuerIdList.put(issId.getParticipantId(), new ArrayList<>());
				}
				issuerIdList.get(issId.getParticipantId()).add(issId.getBinNumber());
			}
		}
		return issuerIdList;
	}

	@Override
	public String addFundCollectDisburse(TxnSettlementDTO disputeTxnDto) {
		try {
			ActionCodeDTO actionCodeInfo = transactionDetailRepo.getMtiFuncCode(disputeTxnDto.getActionCode());
			disputeTxnDto.setMti(actionCodeInfo.getMti());
			disputeTxnDto.setFuncCode(actionCodeInfo.getFunctionCode());
			disputeTxnDto.setToState(disputeTxnDto.getActionCode());
			LocalDateTime currTime = LocalDateTime.now();
			disputeTxnDto.setTxnOrgInstId(transactionDetailRepo.fetchOriginatorInstId());
			if (StringUtils.equals(disputeTxnDto.getEntityType(), BaseCommonConstants.ISSUE_BIN_FLAG)) {
				disputeTxnDto.setPanPrefix(disputeTxnDto.getInstIdIss());
				disputeTxnDto.setPanSuffix(CommonConstants.PAN_SUFFIX);
				disputeTxnDto.setPan(
						StringUtils.rightPad(disputeTxnDto.getInstIdIss(), 12, "9") + disputeTxnDto.getPanSuffix());
				disputeTxnDto.setInstIdAcq(
						transactionDetailRepo.getBinNumberAcq(disputeTxnDto.getTxnOrgInstId()));
				disputeTxnDto.setProcIdIss(disputeTxnDto.getSettlementBin());
				disputeTxnDto.setParticipantIdIss(disputeTxnDto.getTxnDestInstId());
				disputeTxnDto.setParticipantIdAcq(disputeTxnDto.getTxnOrgInstId());
			} else {
				String binNumber = transactionDetailRepo.getBinNumberIss(disputeTxnDto.getTxnOrgInstId());
				disputeTxnDto.setInstIdIss(binNumber);
				disputeTxnDto.setPanPrefix(binNumber);
				disputeTxnDto.setPanSuffix(CommonConstants.PAN_SUFFIX);
				disputeTxnDto.setPan(StringUtils.rightPad(binNumber, 12, "9") + disputeTxnDto.getPanSuffix());
				disputeTxnDto.setProcIdAcq(disputeTxnDto.getSettlementBin());
				disputeTxnDto.setParticipantIdAcq(disputeTxnDto.getTxnDestInstId());
				disputeTxnDto.setParticipantIdIss(disputeTxnDto.getTxnOrgInstId());
			}
			disputeTxnDto.setIncoming(true);
			disputeTxnDto.setTxnId(generateTxnId(currTime));
			disputeTxnDto.setMakerId(sessionDTO.getUserName());
			disputeTxnDto.setStageId(transactionDetailRepo.fetchIdFromDisputeStagSequence());
			disputeTxnDto.setTstampLocal(currTime.format(dtFormatYYYYMMDDhhmmssWithSep));
			disputeTxnDto.setrGCSReceivedDate(currTime.format(formatterYYMMDD));
			disputeTxnDto.setDateLocal(LocalDate.parse(currTime.format(formatterYYYYMMDD), formatterYYYYMMDD));
			disputeTxnDto.setStatus(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
			disputeTxnDto.setOriginalTableName(BaseCommonConstants.NET_FUND_TXN_TBL);
			disputeTxnDto.setEntityType("N");
			disputeTxnDto.setPcode(BaseCommonConstants.NPCI_FUND_PCODE);
			disputeTxnDto.setEncryptedPan(baseDataSecurityutil.encrypt(disputeTxnDto.getPan()));
			disputeTxnDto.setDateTimeLocal(currTime.format(formatterYYYYMMDDHHMMSS));
			transactionDetailRepo.saveFundCollectDisburse(disputeTxnDto);
		} catch (Exception e) {
			return "Error";
		}
		return BaseCommonConstants.DISPUTE_SUCCESS;
	}

	@Override
	public String getSettlementBin(String participantId, String entityType, String instId) {
		String settlementBin = "";
		if (StringUtils.equals(entityType, BaseCommonConstants.ISSUE_BIN_FLAG)) {
			settlementBin = transactionDetailRepo.getSettlementBinIss(participantId, entityType, instId);
		} else if (StringUtils.equals(entityType, BaseCommonConstants.ACQUIRER_BIN_FLAG)) {
			settlementBin = transactionDetailRepo.getSettlementBinAcq(participantId, entityType, instId);
		}
		return settlementBin;
	}

	@Override
	public String getNpciPartId() {
		return sysParam.getSystemKeyValue("NPCI","Participant_Id");
	}

	
	@Override
	public List<DisputeDocumentDTO> saveUploadedDocument(List<MultipartFile> document,String txnId, String tstampLocal, String participantId,List<String> docTypeList, JSONObject jsonDocInfo) throws IOException, ParseException {
		return saveUploadedDocumentCommon(document, txnId, participantId, docTypeList, jsonDocInfo);
		
	}
	
	public static int indexOfExtension(String filename) {
		if (filename == null) {
			return -1;
		}
		int extensionPos = filename.lastIndexOf('.');
		int lastSeparator = indexOfLastSeparator(filename);
		return lastSeparator > extensionPos ? -1 : extensionPos;
	}

	public static int indexOfLastSeparator(String filename) {
		if (filename == null) {
			return -1;
		}
		int lastUnixPos = filename.lastIndexOf('/');
		int lastWindowsPos = filename.lastIndexOf('\\');
		return Math.max(lastUnixPos, lastWindowsPos);
	}
	
	@Override
	public String approveRejectDisputesbulk(String bulkApprovalStageIdList, DisputeTxnModel disputeTxnDto, Model model)
			throws InterruptedException {
		String portal=BaseCommonConstants.ADMIN_PORTAL_TYPE;
		
		return approveRejectDisputeBulkCommon(bulkApprovalStageIdList, disputeTxnDto,
				portal);
	}

	

	

	@Override
	public List<TxnSettlementDTO> getPendingApprRejectTranDetails(TxnSettlementDTO txnSettlementDto) {
		List<TxnSettlementDTO> txnDetails = new ArrayList<>();
		try {
			if (StringUtils.isNotBlank(txnSettlementDto.getActionCode())) {
				String actionCode = txnSettlementDto.getActionCode();
				String funcCode = actionCodeCache.getFuncCodeByActnCd(actionCode);
				txnSettlementDto.setFuncCode(funcCode);
			} else {
				txnSettlementDto.setFuncCode("");
			}
			if(StringUtils.isNotBlank(txnSettlementDto.getTokenPan())) {
				txnSettlementDto.setTokenPan(baseDataSecurityutil.encrypt(txnSettlementDto.getTokenPan()));
			}
			txnDetails.addAll(transactionDetailRepo.getDisputeTranDetailsStage(txnSettlementDto.getFuncCode(),
					txnSettlementDto.getTokenPan(), txnSettlementDto.getRrn(), txnSettlementDto.getFromDate(),
					txnSettlementDto.getToDate(), txnSettlementDto.getStatus()));
		} catch (Exception ex) {
			log.error(FETCH_TXN_ERROR_LOG,ex.getCause(),ex);
		}
		return txnDetails;
	}
	
	@Override
	public String insertDispDocInfo(List<DisputeDocumentDTO> docfilePathList,String prevDocFilePath,String txnId,String status,JSONObject jsonDocInfo) {
		try {
		int value = 0;
		if(StringUtils.equals(status, "P")) {
			value = transactionDetailRepo.insertDocPathStg(docfilePathList);
			if(StringUtils.isBlank(prevDocFilePath)) {
				transactionDetailRepo.updateDisputeDocFilePathStg(txnId);
			}
		}else {
			value = transactionDetailRepo.insertDocPath(docfilePathList);
			if(StringUtils.isBlank(prevDocFilePath)) {
				transactionDetailRepo.updateDisputeDocFilePath(txnId);
			}
		}
		if(value > 0 ) {
			fileMover.handle(env.getProperty("kafka.topic.genericFileMover"),jsonDocInfo);
			return SUCCESS;
			}
		} catch (Exception ex) {
			log.info("Error while updating the Document Path {} ", ex.getCause());
		}
		return FAILURE;
	}

	/**
	 * This function prepares the filteredTxnInfoList list for dispute
	 * 
	 * @param filteredTxnInfoList
	 * @param transactionDetailDTO
	 * 
	 */
	@Override
	public List<TxnSettlementDTO> formatTxnFileds(List<TxnSettlementDTO> filteredTxnInfoList,
			TxnSettlementDTO transactionDetailDTO) {

		return formatTxnFiledsCommon(filteredTxnInfoList, transactionDetailDTO);

	}

	

}
