<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/viewApproveCardConfig.js" type="text/javascript"></script>

<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">
		<span data-i18n="Data"><spring:message
				code="am.lbl.appRejAction" /></span>
	</div>
	<div class="alert alert-danger remarkMust" role="alert">
		<span data-i18n="Data"><spring:message code="sm.lbl.remarkMust" /></span>
	</div>
	<c:url value="approveCardConfig" var="approveCardConfig" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveCardConfig" modelAttribute="cardDto"
		action="${approveCardConfig}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="cardConfig.viewscreen.title" /></span></strong>
					</div>
					<div class="panel-body">
						<input type="hidden" id="cardId" value="${cardDto.cardConfigId}" />
						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">Base Fee</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6">
									<div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> 
											<span data-i18n="Data"><spring:message code="cardConfig.requestInformation" /></span></strong>
									</div></td>
								</tr>
								<tr>
									<td><label><spring:message code="cardConfig.requestType" /><span style="color: red"></span></label></td>
									<td>${cardDto.lastOperation}</td>
									<td><label><spring:message code="cardConfig.requestDate" /><span style="color: red"></span></label></td>
									<td>${cardDto.lastUpdatedOn}</td>
									<td><label><spring:message code="cardConfig.requestStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${cardDto.requestState =='A' }"><spring:message	code="cardConfig.requestState.approved.description" /></c:if>
										<c:if test="${cardDto.requestState =='P' }"><spring:message code="cardConfig.requestState.pendingApproval.description" /></c:if>
										<c:if test="${cardDto.requestState =='R' }"><spring:message code="cardConfig.requestState.rejected.description" /></c:if>
										<c:if test="${cardDto.requestState =='D' }"><spring:message code="cardConfig.requestState.discared.description" /></c:if>
									</td>
								</tr>
								<tr>
									<td><label><spring:message code="cardConfig.requestBy" /><span style="color: red"></span></label></td>
									<td>${cardDto.lastUpdatedBy}</td>
									<td><label><spring:message code="cardConfig.approverComments" /><span
										style="color: red"></span></label></td>
									<td colspan=2>${cardDto.checkerComments}</td>
									<td></td>
									
								</tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-credit-card"></span> <span
										data-i18n="Data"><spring:message code="cardConfig.viewscreen.title" /></span></strong></div>
									</td>
								<tr>
									<td><label><spring:message code="cardConfig.cardConfigId" /><span style="color: red"></span></label></td>
									<td>${cardDto.cardConfigId }</td>
									<td><label><spring:message code="cardConfig.cardType" /><span style="color: red"></span></label></td>
									<td>${cardDto.cardTypeName }</td>
									<td><label><spring:message code="cardConfig.cardVariant" /><span style="color: red"></span></label></td>
									<td>${cardDto.cardVariantName }</td>
								</tr>
								<tr>
									<td><label><spring:message code="cardConfig.baseFee" /><span style="color: red"></span></label></td>
									<td>${cardDto.baseFee }</td>
									<td><label><spring:message code="cardConfig.fromDate" /><span style="color: red"></span></label></td>
									<td>${cardDto.fromDate }</td>
									<td><label><spring:message code="cardConfig.toDate" /><span style="color: red"></span></label></td>
									<td>${cardDto.toDate }</td>
								</tr>
								<sec:authorize access="hasAuthority('Approve Base Fee')">
									<c:if test="${cardDto.requestState eq 'P'}">
									<tr>
										<td colspan="6"><div class="panel-heading-red  clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span>
											<span data-i18n="Data">
											<spring:message code="cardConfig.approvalPanel.title" /></span></strong></div>
										</td>
									</tr>
									<tr>
										<td><label><spring:message
											code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td>
												<select name="select" id="apprej"
													onchange="display()">
													<option value="N"><spring:message
													code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
													code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
													code="AM.lbl.reject" /></option>
												</select>
											</td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
													style="color: red">*</span></label>
												</div>
											</td>
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div>
											</td>
										</tr>
									</c:if>
								</sec:authorize>
							</tbody>
						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Base Fee')">
										<c:if test="${cardDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approvecard" value="Submit"
												onclick="postAction('/approveCardConfig');" />
										</c:if>
									</sec:authorize>
													
									<sec:authorize access="hasAuthority('Edit Base Fee')">				
									<c:if test="${cardDto.requestState  eq 'R' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="userAction('/discardCardConfig','${cardDto.cardConfigId}');" />
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="edit('/editCardConfig','${cardDto.cardConfigId}','approvalTab');"/>
									</c:if>
									</sec:authorize>
									
										<button type="button" class="btn btn-danger"
										onclick="backAction('P','/cardConfigPendingForApproval');">
										<spring:message code="cardConfig.backBtn" /></button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

