<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
namespace="org.npci.settlenxt.adminportal.repository.CycleStatusMonitorRepository">

<select id="getCycleStatusDetails" resultType="CycleStatusMonitorDTO">
select guid as guid, product_code as productCode,activity_code as activityCode,status as status,
cycle_date as cycleDate,cycle_number as cycleNumber,child_flag as childFlag,start_time as startTime,
end_time as endTime from cycle_status where product_code=#{productCode} and cycle_date=#{cycleDate} and cycle_number=#{cycleNumber}
</select>
<select id="getCycleDetailsByGuid" resultType="CycleStatusMonitorDTO">
select key,value from cycle_status_details where guid=#{guid}
</select>
<select id="getCycleStatusByGuid" resultType="CycleStatusMonitorDTO">
select product_code as productCode,activity_code as activityCode ,status,cycle_date as cycleDate ,cycle_number as cycleNumber from cycle_status where guid=#{guid}
</select>
</mapper>