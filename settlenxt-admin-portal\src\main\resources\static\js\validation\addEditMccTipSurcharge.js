
$(document).ready(
		function() {
			$("form :input").change(function() {
				$(this).closest('form').data('changed', true);
			});
			
			
		    $("#errmccId").hide();
		    $("#errtipSurchargeId").hide();
	
	
	$('#mccId').blur(function () {
        validateField('mccId', true, "SelectionBox",0,false,1,99999999999,true);
    });	
    $('#tipSurchargeId').blur(function () {
        validateField('tipSurchargeId', true,"SelectionBox", 0, false,1,99999999999,true);
    });    
	disableSave();
	$("#mccId").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#tipSurchargeId").on('keyup keypress blur change', function () {
        unableSave();
    });
		    
});


function disableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = true;
}
}

function unableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = false;
}
}


window.history.forward();
function noBack() {
	window.history.forward();
}

function resetAction() {

$('#tipSurchargeId').val("");
$('#mccId').val("");
$("#snxtErrorMessage").hide();	
	$("#errmccId").find('.error').html('');
    $("#errtipSurchargeId").find('.error').html('');
	
	}

function viewTipSurchargeAdd(url, type) {
	
	var isValid = true;
	var data="";
	
    if (!validateField('mccId', true, "SelectionBox",0,false,1,99999999999,true) && isValid) {
        isValid = false;
    }
    if (!validateField('tipSurchargeId', true,"SelectionBox", 0, false,1,99999999999,true) && isValid) {
        isValid = false;
    }
    

	
if (isValid ) {
     
	if(type=='E'){
	
	 data = "mccTipSurchargeId," + $("#mccTipSurchargeId").val() 
    +",tipSurchargeId," + $("#tipSurchargeId").val() 
	+",mccId," + $("#mccId").val() +",parentPage," + $("#hparentPage").val();
	}
	
	else if(type=='A'){
	 data = "tipSurchargeId," + $("#tipSurchargeId").val() 
    +",mccId," + $("#mccId").val() +",parentPage," + $("#hparentPage").val();
	}
	postData(url,data)

}
}




function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    isValid = checkMandatory(isMandatory, fieldValue, fieldType, isValid);
    var regEx;
    isValid = checkNaN(fieldType, fieldValue, isValid);
    isValid = isAlphabet(fieldType, fieldValue, isValid);
    isValid = isAlphabetWithSpace(fieldType, fieldValue, isValid);
    isValid = isAlphanumericNoSpace(fieldType, fieldValue, isValid);
    if (fieldType == "Integer") {
         regEx =/^\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "Decimal") {
         regEx = /^\d+\.?\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
      if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
      

    if (isValid) {        
        $("#err" + fieldId).hide();
    } else {
        if(mccTipSurchargeValidationMessages[fieldId]){
            $("#err" + fieldId).find('.error').html(mccTipSurchargeValidationMessages[fieldId]);
        }
        $("#err" + fieldId).show();
    }
    return isValid;
}


function isAlphanumericNoSpace(fieldType, fieldValue, isValid) {
	if (fieldType == "AlphanumericNoSpace") {
		let regEx = /^[A-Za-z0-9]+$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

function isAlphabetWithSpace(fieldType, fieldValue, isValid) {
	if (fieldType == "AlphabetWithSpace") {
		let regEx = /^[A-Z ]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

function newFunction(fieldType, regEx, fieldValue, isValid) {
	if (fieldType == "AlphabetWithSpace") {
		regEx = /^[A-Z ]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return { regEx, isValid };
}

function isAlphabet(fieldType, fieldValue, isValid) {
	if (fieldType == "Alphabet") {
		let regEx = /^[A-Z]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

function checkNaN(fieldType, fieldValue, isValid) {
	if (fieldType == "Number" && isNaN(fieldValue)) {
		isValid = false;
	}
	return isValid;
}

function checkMandatory(isMandatory, fieldValue, fieldType, isValid) {
	if ((isMandatory && fieldValue.trim() == "" && (fieldType != "SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType == "SelectionBox")) {
		isValid = false;
	}
	return isValid;
}

function userAction(_type, action) {
	
	var data = "status," + status;
	postData(action, data);
}

function postDiscardAction(action,_id) {
	
	var url = action;
	var mccTipSurchargeId = $("#mccTipSurchargeId").val();
	var data = "mccTipSurchargeId," + mccTipSurchargeId  ;
	postData(url, data);
}

function discard(action, mccTipSurchargeId) {
	 
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "mccTipSurchargeId," + mccTipSurchargeId + ",_vTransactToken,"
			+ tokenValue;
	postData(action, data);
}







