<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/viewApproveInsurancePremium.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">Please
		Select Approve/Reject action.</div>
	<div class="alert alert-danger remarkMust" role="alert">Please
		Enter Remarks.</div>
	<c:url value="approveInsurancePremiumStatus"
		var="approveInsurancePremiumStatus" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveInsurancePremium"
		modelAttribute="insurancePremiumInfoDto"
		action="${approveRebateStatus}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"> <spring:message
									code="insurancePremium.insurancePremiumPendingConfiguration" />
						</span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="insurancePremId"
							value="${insurancePremiumInfoDto.insurancePremId}" /> <input
							type="hidden" id="crtuser"
							value="${insurancePremiumInfoDto.lastUpdatedBy}" />
						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">Insurance Premium</caption>
						<thead style="display:none;"><th scope = "col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message
														code="insurancePremium.requestInformation" /></span></strong>
										</div></td>
									<td colspan="8"></td>
								</tr>
								<tr>

									<td><label><spring:message
												code="insurancePremium.requestType" /><span
											style="color: red"></span></label></td>
									<td>${insurancePremiumInfoDto.lastOperation}</td>
									<td><label><spring:message
												code="insurancePremium.requestDate" /><span
											style="color: red"></span></label></td>
									<td>${insurancePremiumInfoDto.lastUpdatedOn}</td>
									<td><label><spring:message code="insurancePremium.requestStatus" /><span
											style="color: red"></span></label></td>
									<td><c:if test="${insurancePremiumInfoDto.requestState=='A' }">Approved</c:if></td>
									<td><c:if test="${insurancePremiumInfoDto.requestState=='P' }">Pending for Approval</c:if></td>
									<td><c:if test="${insurancePremiumInfoDto.requestState=='R' }">Rejected</c:if></td>
									<td><c:if test="${insurancePremiumInfoDto.requestState=='D' }">Discarded</c:if></td>
									<%-- <td>${insurancePremiumInfoDto.requestStateDiscription}</td> --%>
									<td colspan="8"></td>
								</tr>
								<tr>
									<td><label><spring:message
												code="insurancePremium.requestBy" /><span
											style="color: red"></span></label></td>
									<td>${insurancePremiumInfoDto.lastUpdatedBy}</td>

									<td><label><spring:message
												code="insurancePremium.approverComments" /><span
											style="color: red"></span></label></td>
									<td>${insurancePremiumInfoDto.checkerComments}</td>
									<td colspan="10"></td>
								</tr>

								<tr>
									<td colspan="6">
										<div class="panel-heading clearfix">
											<strong><span class="glyphicon glyphicon-th"></span> <span
												data-i18n="Data"><spring:message
														code="insurancePremium.insurancePremiumInformation" /></span></strong>
										</div></td>
										<td colspan="8"></td>
									</tr>
										<div class="panel-body">
											 <input
												type="hidden" id="status"
												value="${insurancePremiumInfoDto.status}" />
											<table class="table table-striped" style="font-size: 12px">
											<caption style="display:none;">Insurance Premium</caption>
											<thead style="display:none;"><th scope = "col"></th></thead>
												<tbody>
													<tr>
														<td><label><spring:message
																	code="insurancePremium.cardType" /><span
																style="color: red"></span></label></td>
														<td>${insurancePremiumInfoDto.cardTypeName }</td>
														<td><label><spring:message
																	code="insurancePremium.cardVariant" /><span
																style="color: red"></span></label></td>
														<td>${insurancePremiumInfoDto.cardVariantName }</td>
														<td colspan="10"></td>

													</tr>
													<tr>
														<td><label><spring:message
																	code="insurancePremium.premiumPerCardPerAnnum" /><span
																style="color: red"></span></label></td>
														<td>
															${insurancePremiumInfoDto.annualPremiumAmtPerCard }</td>
														<td><label><spring:message
																	code="insurancePremium.vendor" /><span
																style="color: red"></span></label></td>
														<td>${insurancePremiumInfoDto.vendorName }</td>
														<td colspan="10"></td>
													</tr>
													<tr>
														<td><label><spring:message
																	code="insurancePremium.fromMonthYear" /><span
																style="color: red"></span></label></td>
														<td>${insurancePremiumInfoDto.fromMonthName }</td>
														<td><label><spring:message
																	code="insurancePremium.Year" /><span
																style="color: red"></span></label></td>
														<td>${insurancePremiumInfoDto.fromYear }</td>
														<td colspan="10"></td>
													</tr>
													<tr>
														<td><label><spring:message
																	code="insurancePremium.toMonthYear" /><span
																style="color: red"></span></label></td>
														<td>${insurancePremiumInfoDto.toMonthName }</td>
														<td><label><spring:message
																	code="insurancePremium.Year" /><span
																style="color: red"></span></label></td>
														<td>${insurancePremiumInfoDto.toYear }</td>
														<td colspan="10"></td>
													</tr>
													
													
													<sec:authorize access="hasAuthority('Approve Insurance Premium')">
											<c:if
												test="${(insurancePremiumInfoDto.requestState eq 'S' or insurancePremiumInfoDto.requestState eq 'P') }">
												<tr>
													<td colspan="6"><div
															class="panel-heading-red  clearfix">
															<strong><span class="glyphicon glyphicon-info-sign"></span>
																<span data-i18n="Data"><spring:message
																		code="AM.lbl.reqInfo" /></span></strong>
														</div></td>
														<td colspan="8"></td>
														
												</tr>

												<tr>
													<td><label><spring:message
																code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
													<td><select name="select" id="apprej"
														onchange="display()">
															<option value="N"><spring:message
																	code="AM.lbl.select" /></option>
															<option value="A" id="approve"><spring:message
																	code="AM.lbl.approve" /></option>
															<option value="R" id="reject"><spring:message
																	code="AM.lbl.reject" /></option>
													</select></td>
													<td>
														<div style="text-align:center">
															<label><spring:message code="AM.lbl.remarks" /><span
																style="color: red">*</span></label>
														</div>
													</td>
													
													<td colspan="2"><textarea rows="4" cols="50"
															maxlength="100" id="rejectReason"></textarea>
														<div id="errorrejectReason" class="error"></div></td>
												</tr>
											</c:if>
										</sec:authorize>
													
												</tbody>
											</table>
										</div> 
							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize
										access="hasAuthority('Approve Insurance Premium')">
										<c:if
											test="${(insurancePremiumInfoDto.requestState eq 'S' or insurancePremiumInfoDto.requestState eq 'P') }">

											<button type="button" class="btn btn-success"
												id="approveRole"
												onclick="postAction('/approveInsurancePremiumStatus','${originPage}');">
												<spring:message code="insurancePremium.submit" />
											</button>
										</c:if>
									</sec:authorize>

									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/insurancePremiumPendingForApproval','${originPage}');">
										<spring:message code="insurancePremium.back" />
									</button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

