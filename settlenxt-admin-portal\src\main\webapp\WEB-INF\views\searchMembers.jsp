<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head><title></title></head>
<script type="text/javascript">
<c:if  test="${showFinalMembers eq 'YES'}">
	var actionColumnIndex = 10;
	var firstColumnToBeSkippedInFilterAndSort=false;
	</c:if>
	<c:if test="${pendingMembersFrAppr eq 'YES'}">	
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 14;
	var firstColumnToBeSkippedInFilterAndSort=true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 13;
	var firstColumnToBeSkippedInFilterAndSort=false;
	</c:if>
	</c:if>
	<c:if test="${showSavedMembers eq 'YES'}">
	var firstColumnToBeSkippedInFilterAndSort=false;
	actionColumnIndex = 12;
	</c:if>
</script>

<script>
	var memIds = [];
	 var networkUsed='<spring:eval expression="@environment.getProperty('is.international.enabled', 'N')" />';
	<c:if test="${not empty memListPending}">
	<c:forEach items="${memListPending}" var="operator">
	<c:if test="${operator.recordStatus eq 'P' }">
	memIds.push(${operator.participantId});
	
	</c:if>
	</c:forEach>
	</c:if>
</script>
<script type="text/javascript" src=	"./static/js/dataTables.buttons.min.js">
</script>
<script type="text/javascript" src=	"./static/js/jszip.min.js">
</script>
 
<script type="text/javascript" src=	"./static/js/buttons.html5.min.js">
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />

<script src="./static/js/validation/searchMembers.js"
	type="text/javascript"></script>
<style>
	 
	.defaultexport {
  visibility: hidden;
}

table.dataTable thead  { vertical-align: top;}
table.dataTable thead .sorting { vertical-align: bottom; background: url('./static/images/sort_both.png') no-repeat center right; }
table.dataTable thead .sorting_asc { vertical-align: bottom;background: url('./static/images/sort_asc.png') no-repeat center right; }
table.dataTable thead .sorting_desc { vertical-align: bottom;background: url('./static/images/sort_desc.png') no-repeat center right; }
table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before{ vertical-align: top;content:""}
table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after{ vertical-align: top;content:""}
.search-box  {	
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;	
	background-color: transparent;
    width: 100%;
    border-width:1px;
	border-style:inset;
    }
</style>		
<!-- Modal -->
<div class="modal fade" id="toggleModalMember" tabindex="-1"
	role="dialog" aria-labelledby="toggleApproveMember" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel"><spring:message code="member.approve.msg"/>
				</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true"> &times;
					</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;"><spring:message code="member.approvalRejection.msg"/>
					</label>
				<p id="memIds" />
			</div>



			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="approveorRejectBulkMem('R','All')"><spring:message code="member.rejectBtn"/></button>
				<button type="button" class="btn btn-primary"
					onclick="approveorRejectBulkMem('A','All')"><spring:message code="member.approveBtn"/></button>
			</div>
		</div>
	</div>
</div>

<div class="row"> 


<spring:eval expression="@environment.getProperty('is.international.enabled', 'N')" var="networkRgcs" />
<div role="alert" style="display: none" id="jqueryError4">
<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
</div>
<div id="errLvType" class="alert alert-danger" role="alert"
style="display: none"></div>
</div>


<div class="container-fluid height-min">

	<div id="body-content">

		<div class="space_block">


			<ul class="nav nav-tabs" role="tablist" id="myTab">
				<c:choose>
					<c:when test="${showFinalMembers eq 'YES'}">
						<li role="presentation" class="active" />
					</c:when>
					<c:otherwise>
						<li role="presentation" />
					</c:otherwise>
				</c:choose>
				<a href="#home" onclick="submitForm('/showFinalMembers');"
					role="tab" data-toggle="tab"><span
					class="glyphicon glyphicon-user">&nbsp;</span><spring:message code="member.maintab.title"/> </a>

				<c:choose>
					<c:when test="${pendingMembersFrAppr eq 'YES'}">
						<li role="presentation" class="active" />
					</c:when>
					<c:otherwise>
						<li role="presentation" />
					</c:otherwise>
				</c:choose>
				<a href="#profile" role="tab"
					onclick="submitForm('/pendingMembersFrAppr');" data-toggle="tab"><span
					class="glyphicon glyphicon-ok">&nbsp;</span><spring:message code="member.approvalTab.title"/></a>

				<sec:authorize access="hasAuthority('Add Member')">
					<c:choose>
						<c:when test="${showSavedMembers eq 'YES'}">
							<li role="presentation" class="active" />
						</c:when>
						<c:otherwise>
							<li role="presentation" />
						</c:otherwise>
					</c:choose>


					<a href="#profile" role="tab"
						onclick="submitForm('/showSavedMembers');" data-toggle="tab"><span
						class="glyphicon glyphicon-star">&nbsp;</span><spring:message code="member.savedMembers.title"/></a>
				</sec:authorize>



			</ul>
			<form:form id="userSearch" name="userSearch">
			</form:form>

			<div class="tab-content">
				<!-- tabpanel -->
				<div role="tabpanel" class="tab-pane active" id="home">
					<div class="row">
						<div class="col-sm-12">
							<sec:authorize access="hasAuthority('Add Member')">
								<c:if test="${not empty showAddButton }">
									<%-- <c:if test="${not empty finalMember}"> --%>
									<a class="btn btn-success pull-right btn_align"
									onclick="submitForm('/memberCreation');"	> 
										<em class="glyphicon-plus"></em><spring:message code="member.addMemberBtn"/> 
									</a>
									<%-- 	</c:if> --%>
								</c:if>

								<c:if test="${not empty showDownloadButton}">
									<c:if test="${not empty finalMember}">
										<a class="btn btn-success pull-right btn_align" href="#"
											onclick="submitForm('/downloadAllMembers');"><spring:message code="member.download.title"/>
											</a>
									</c:if>
								</c:if>
							</sec:authorize>
						</div>
					</div>

			<div class="row">
				<div class="col-sm-12">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="ifsc.clearFiltersBtn" />
					</button>


					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="excelExport"><spring:message code="ifsc.exportBtn" /> </a>
						
						&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="csvExport"><spring:message code="ifsc.csvBtn" /> </a>
						

				</div>
			</div>



					<div class="row">
						<div class="col-sm-12">
							<div class="panel panel-default">
								<div class="panel-heading">
									<strong><span class="glyphicon glyphicon-th"></span> <span
										data-i18n="Data"><spring:message code="member.memberList"/></span></strong>
										<c:if test="${not empty TmemberList}">
										<sec:authorize access="hasAuthority('Approve Member')">

											<input type="button"
												class="btn btn-success pull-right btn_align"
												onclick="approveorRejectBulkMem('A','No')" id="submitButton"
												value="<spring:message code="feeRate.Approve" />" />
											<input type="button"
												class="btn btn-success pull-right btn_align"
												onclick="approveorRejectBulkMem('R','No')" id="submitButton"
												value="<spring:message code="feeRate.Reject" />" />
										</sec:authorize>
									</c:if>
								</div>
								<sec:authorize access="hasAuthority('Add Member')">
								<div class="panel-body">
								<c:if test="${IsMaker eq 'Y'}">
									<c:if test="${not empty memberList}">
										<div class="table-responsive">
											<table id="tabnew" class="table table-striped table-bordered"
												style="width:150%;">
												<caption style="display:none;">Seach Member</caption>
												<thead>
													<tr>
														<th scope="col"><spring:message code="member.participantCode"/></th>
														<th scope="col"><spring:message code="member.participantName"/></th>
														<th scope="col"><spring:message code="member.bankType"/></th>
														<th scope="col"><spring:message code="member.bankCode"/></th>
														<th scope="col"><spring:message code="member.address"/></th>
														<th scope="col"><spring:message code="member.phoneNumber"/></th>
														<th scope="col"><spring:message code="member.email"/></th>
														<th scope="col"><spring:message code="member.active"/></th>
														<th scope="col"><spring:message code="member.createdDate"/></th>
														<th scope="col"><spring:message code="member.modifiedDate"/></th>
													</tr>
												</thead>
												<tbody>

												</tbody>
											</table>
										</div>
									</c:if>
									<c:if test="${not empty TmemberList}">
										<div class="table-responsive">
											<table id="tabnewPending"
												class="table table-striped table-bordered" style="width:200%;">
												<caption style="display:none;">Seach Member</caption>
												<thead>
													<tr>
														<th scope="col"><spring:message code="member.participantCode"/></th>
														<th scope="col"><spring:message code="member.participantName"/></th>
														<th scope="col"><spring:message code="member.bankType"/></th>
														<th scope="col"><spring:message code="member.bankCode"/></th>
														<th scope="col"><spring:message code="member.address"/></th>
														<th scope="col"><spring:message code="member.phoneNumber"/></th>
														<th scope="col"><spring:message code="member.email"/></th>
														<th scope="col"><spring:message code="member.active"/></th>
														<th scope="col"><spring:message code="member.createdDate"/></th>
														<th scope="col"><spring:message code="member.modifiedDate"/></th>
														<th scope="col"><spring:message code="member.status"/></th>
														<th scope="col"><spring:message code="member.rejectReason"/></th>
														<th scope="col"><spring:message code="member.rejectDate&Time"/></th>
														
																						</tr>
												</thead>
												<tbody>

												</tbody>
											</table>
										</div>
									</c:if>
									<c:if test="${not empty savedMemberList}">
										<div class="table-responsive">
											<table id="tabnewSavedMem"
												class="table table-striped table-bordered" style="width:150%;">
												<caption style="display:none;">Seach Member</caption>
												<thead>
													<tr>
														<th scope="col"><spring:message code="member.participantCode"/></th>
														<c:if test="${networkRgcs eq 'Y'}">
														<th scope="col">Domestic Creation Date</th>
														</c:if>
														<th scope="col"><spring:message code="member.participantName"/></th>
														<th scope="col"><spring:message code="member.bankType"/></th>
														<th scope="col"><spring:message code="member.bankCode"/></th>
														<th scope="col"><spring:message code="member.address"/></th>
														<th scope="col"><spring:message code="member.phoneNumber"/></th>
														<th scope="col"><spring:message code="member.email"/></th>
														<th scope="col"><spring:message code="member.active"/></th>
														<th scope="col"><spring:message code="member.createdDate"/></th>
														<th scope="col"><spring:message code="member.modifiedDate"/></th>
														<th scope="col"><spring:message code="member.status"/></th>
														
													</tr>
												</thead>
												<tbody>

												</tbody>
											</table>
										</div>
									</c:if>
								</c:if>
								</div>
								</sec:authorize>
								
								<sec:authorize access="hasAuthority('Approve Member')">
								<div class="panel-body">
								<c:if test="${IsMaker ne 'Y'}">
									<c:if test="${not empty memberList}">
										<div class="table-responsive">
											<table id="tabnewchkr" class="table table-striped table-bordered"
												style="width:150%;">
												<caption style="display:none;">Seach Member</caption>
												<thead>
													<tr>
														<th scope="col"><spring:message code="member.participantCode"/></th>
														<th scope="col"><spring:message code="member.participantName"/></th>
														<th scope="col"><spring:message code="member.bankType"/></th>
														<th scope="col"><spring:message code="member.bankCode"/></th>
														<th scope="col"><spring:message code="member.address"/></th>
														<th scope="col"><spring:message code="member.phoneNumber"/></th>
														<th scope="col"><spring:message code="member.email"/></th>
														<th scope="col"><spring:message code="member.active"/></th>
														<th scope="col"><spring:message code="member.createdDate"/></th>
														<th scope="col"><spring:message code="member.modifiedDate"/></th>
													</tr>
												</thead>
												<tbody>

												</tbody>
											</table>
										</div>
									</c:if>
									<c:if test="${not empty TmemberList}">
										<div class="table-responsive">
											<table id="tabnewPendingchkr"
												class="table table-striped table-bordered" style="width:200%;">
												<caption style="display:none;">Seach Member</caption>
												<thead>
													<tr>
													<sec:authorize access="hasAuthority('Approve Member')">
																<th scope="col"><input type=checkbox name='selectAllCheck'
																	id="selectAll" data-target="toggleModalMember" value='All'></input></th>
															</sec:authorize>
														<th scope="col"><spring:message code="member.participantCode"/></th>
														<th scope="col"><spring:message code="member.participantName"/></th>
														<th scope="col"><spring:message code="member.bankType"/></th>
														<th scope="col"><spring:message code="member.bankCode"/></th>
														<th scope="col"><spring:message code="member.address"/></th>
														<th scope="col"><spring:message code="member.phoneNumber"/></th>
														<th scope="col"><spring:message code="member.email"/></th>
														<th scope="col"><spring:message code="member.active"/></th>
														<th scope="col"><spring:message code="member.createdDate"/></th>
														<th scope="col"><spring:message code="member.modifiedDate"/></th>
														<th scope="col"><spring:message code="member.status"/></th>
														<th scope="col"><spring:message code="member.rejectReason"/></th>
														<th scope="col"><spring:message code="member.rejectDate&Time"/></th>
													</tr>
												</thead>
												<tbody>

												</tbody>
											</table>
										</div>
									</c:if>
								</c:if>
								</div>
								</sec:authorize>
							</div>
						</div>
					</div>




					<div class="row"></div>
				</div>
				<div role="tabpanel" class="tab-pane" id="profile"></div>
			</div>


		</div>
		<!-- space block -->
	</div>


</div>




<div class="modal fade" id="exampleModalLong" data-backdrop="static"
	tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle"
	aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered modal-lg"
		role="document">
		<div class="modal-content">
			<div class="modal-header">
				<span class="modal-title" id="exampleModalLongTitle"><spring:message code="member.activDeactive"/>
					 </span>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="col-md-12">
						<div class="card">
							<div class="card-header">
								<div class="card-title displayActDeactBtn"><spring:message code="member.deactivate"/></div>

							</div>
							<div class="card-body">

								<div class="row">
									<div class="col-md-6">
										<form class="form-inline"
											style="border-right: solid 1px #bcd1f1;">
											<div class="form-group">
												<label><spring:message code="member.memberId"/></label> <input type="text"
													class="form-control" id="memId" />
											</div>

											<div class="form-group">
												<label><spring:message code="member.memberName"/></label> <input type="text"
													class="form-control" id="memName" />
											</div>



										</form>
									</div>



									<div class="col-md-6">
										<form class="form-inline">
											<div class="form-group">
												<label><spring:message code="member.bankType"/></label> <input type="text"
													class="form-control" id="memType" />
											</div>
											<div class="form-group">
												<label><spring:message code="member.participantId"/></label> <input type="text"
													class="form-control" id="partId" />
											</div>



										</form>
									</div>


									<br />

									<div class="col-md-12" style="display: none;">
										<div class="form-group">
											<label for=""><strong><spring:message code="member.remarks"/></strong> <span class="red">*</span></label>


											<textarea class="form-control" id="remrks"
												placeholder="Please enter remarks"></textarea>

											<div>
												<span id="remrksErr" class="error"></span>
											</div>
										</div>
									</div>




								</div>
							</div>
						</div>
					</div>



				</div>
			</div>
			<div class="modal-footer text-center">
				<button type="button" class="btn btn-primary displayActDeactBtn"><spring:message code="member.deactivate"/></button>
			</div>
		</div>
	</div>
</div>



<%--<%@include file="Footer.jsp"%>
</body>--%>
</html>
