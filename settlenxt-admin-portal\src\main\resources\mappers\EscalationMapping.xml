<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.npci.settlenxt.adminportal.repository.EscalationRepository">

<select id="getMemberList" resultType="BaseEscalationDTO">
select CONCAT (PARTICIPANT_ID,'-', bank_name) as label, participant_id as value from PARTICIPANT WHERE STATUS = 'A' and participant_id not in (select member_id as memberId from bank_escalation_details_stg where request_state in ('P','R')) ORDER BY bank_name
</select>

<select id="getMemberListForReject" resultType="BaseEscalationDTO">
select CONCAT (PARTICIPANT_ID,'-', bank_name) as label, participant_id as value from PARTICIPANT WHERE STATUS = 'A'  ORDER BY bank_name
</select>


<select id="fetchEscalationStgAppList" resultType="BaseEscalationDTO">
select member_id  as memberId,department_id  as departmentId,esc_level as escLevel ,emp_name as name ,designation as designation ,email as email ,landline as landline,mobile as mobile ,
created_by as createdBy ,created_on  as createdOn,last_updated_by as lastUpdatedBy ,last_updated_on  as lastUpdatedOn,zipcode as pincode ,state as state,legal_address as address,request_state as requestState ,
last_operation as lastOperation,
checker_comments as checkerComments ,esc_id as escId,esc_id as userId  from bank_escalation_details_stg 
WHERE member_id in<foreach item='item' index='index' collection='memberIdList' open='(' separator=',' close=')'>#{item}</foreach>
</select>

<select id="getDepartmentList" resultType="BaseEscalationDTO">
SELECT department_id as departmentId, department_name as departmentName FROM DEPARTMENT 
WHERE STATUS = 'A' ORDER BY department_id
</select>
<select id="getContactList" resultType="BaseEscalationDTO">
SELECT CONCAT(bd.member_id,'-',p.bank_name) as bankMemberName, bd.esc_id as escId, bd.esc_id as userId, bd.member_id as memberId, bd.request_state as requestState, 
bd.department_id as departmentId, bd.esc_level as escLevel,bd.zipcode as pinCode ,
bd.legal_address as address,bd.state as state, s.state_name as stateName, bd.emp_name as name, bd.designation as designation,
bd.email as email, bd.landline as landline, bd.mobile as mobile, bd.created_by as createdBy,
bd.created_on as createdOn, bd.last_updated_by as lastUpdatedBy, bd.last_updated_on as lastUpdatedOn
FROM bank_escalation_details_stg bd
left join state s on s.state_id=CAST(bd.state as INTEGER) left join participant p on p.participant_id=bd.member_id
WHERE bd.member_id = #{memberId} ORDER BY bd.department_id, bd.esc_level
</select>
<select id="getContactListMain" resultType="BaseEscalationDTO">
SELECT bed.esc_id  as escId, bed.esc_id  as userId,bed.member_id as memberId, bed.department_id as departmentId, 
bed.esc_level as escLevel, bed.emp_name as name, bed.designation as designation,
bed.email as email, bed.landline as landline, bed.mobile as mobile, bed.legal_address as address,
bed.state as state,bed.zipcode as pinCode, s.state_name as stateName,bed.created_by as createdBy , bed.created_on as createdOn ,
bed.last_updated_by as lastUpdatedBy, bed.last_updated_on as lastUpdatedOn
FROM bank_escalation_details bed
left join state s on s.state_id=CAST(bed.state as INTEGER)
WHERE bed.member_id = #{memberId} ORDER BY bed.department_id, bed.esc_level
</select>

<select id="getContactListStg" resultType="BaseEscalationDTO">
SELECT bed.esc_id  as escId, bed.esc_id  as userId,bed.member_id as memberId, bed.department_id as departmentId, 
bed.esc_level as escLevel, bed.emp_name as name, bed.designation as designation, 
bed.email as email, bed.landline as landline, bed.mobile as mobile, bed.legal_address as address, 
bed.state as state,bed.zipcode as pinCode, s.state_name as stateName,bed.created_by as createdBy,bed.created_on as createdOn, bed.request_state as requestState, bed.last_updated_by as lastUpdatedBy, bed.last_updated_on as lastUpdatedOn
FROM bank_escalation_details_stg bed
left join state s on s.state_id=CAST(bed.state as INTEGER)
WHERE bed.member_id = #{memberId} ORDER BY bed.department_id, bed.esc_level
</select>

<insert id="addEscalationStg">
INSERT INTO bank_escalation_details_stg(member_id, department_id, esc_level, emp_name, designation, email,landline, mobile,created_by, created_on, last_updated_by, last_updated_on,zipcode, state, legal_address, request_state, last_operation)VALUES(#{memberId},#{departmentId},#{escLevel},#{name},#{designation},#{email},#{landline},#{mobile},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{pinCode},#{state},#{address},#{requestState},#{lastOperation})
</insert>

<update id="updateEscalationStg">
UPDATE bank_escalation_details_stg SET department_id=#{departmentId},esc_level=#{escLevel},emp_name=#{name},
designation=#{designation},email=#{email},landline=#{landline},mobile=#{mobile},
last_updated_by=#{lastUpdatedBy}, last_updated_on=#{lastUpdatedOn},zipcode=#{pinCode},
state=#{state}, legal_address=#{address}, request_state=#{requestState}, last_operation=#{lastOperation}
where member_id=#{memberId}and department_id=#{departmentId} AND esc_level=#{escLevel}
</update>

<update id="updateEscalationStgState">
UPDATE bank_escalation_details_stg SET LAST_UPDATED_BY = #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},
REQUEST_STATE = #{requestState}, CHECKER_COMMENTS=#{checkerComments}, LAST_OPERATION= #{lastOperation}
WHERE esc_id=#{userId}
</update>
  
<insert id="addContatctListMainTable">
insert into bank_escalation_details(
	esc_id , member_id, department_id, esc_level, emp_name, designation, email, landline, mobile,
    created_by, created_on, last_updated_by, last_updated_on, zipcode, state, legal_address)values<foreach collection='escalationDtoList' item='escalation' separator=','>(
#{escalation.escId}, #{escalation.memberId},#{escalation.departmentId}, #{escalation.escLevel},#{escalation.name} , #{escalation.designation} , #{escalation.email},
#{escalation.landline},#{escalation.mobile},#{escalation.createdBy},#{escalation.createdOn},#{escalation.lastUpdatedBy},
#{escalation.lastUpdatedOn},#{escalation.pinCode},#{escalation.state},#{escalation.address})
</foreach>

</insert>


<select id="getMembersList" resultType="BaseEscalationDTO">
SELECT participant_id as value,CONCAT (PARTICIPANT_ID,'-', bank_name) as label 
from PARTICIPANT where STATUS = 'A'  and participant_id  in (select member_id as memberId from bank_escalation_details_stg where request_state in ('A')) ORDER BY bank_name
</select>
<select id="getPendingForEscalationList" resultType="BaseEscalationDTO">
select distinct on (beds.member_id) beds.member_id as memberId, CONCAT(beds.member_id ,'-', p.bank_name) as bankMemberName, beds.member_id as memberId,beds.esc_id as escId, beds.request_state as requestState, beds.department_id as departmentId,beds.esc_level as 
escLevel,beds.zipcode as pinCode,beds.legal_address as address,beds.state as state, beds.emp_name as name, beds.designation as designation, beds.email as email, beds.landline 
as landline, beds.mobile as mobile,beds.created_by as createdBy, beds.created_on as createdOn, beds.last_updated_by as lastUpdatedBy, beds.last_updated_on  as lastUpdatedOn,
beds.checker_comments as checkerComments FROM bank_escalation_details_stg beds LEFT JOIN participant p ON beds.member_id = p.participant_id WHERE beds.request_state in <foreach item='item' index='index' collection='requestState' open='(' separator=',' close=')'>#{item}</foreach> ORDER BY beds.member_id,beds.department_id, beds.esc_level
</select>
<update id="updateEscalation">
UPDATE bank_escalation_details_stg SET department_id=#{departmentId},esc_level=#{escLevel},emp_name=#{name},designation=#{designation},email=#{email},landline=#{landline},mobile=#{mobile},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},zipcode=#{pinCode},state=#{state},legal_address=#{address},request_state=#{requestState},last_operation=#{lastOperation} where member_id=#{memberId} and department_id=#{departmentId} AND esc_level=#{escLevel};
</update>


<delete id="deleteDiscardedEntry">
delete from bank_escalation_details_stg where member_id=#{memberId}
</delete>

  

<update id="updateEscalationMain">
UPDATE bank_escalation_details SET department_id=#{departmentId}, esc_level=#{escLevel}, emp_name=#{name},
designation=#{designation}, email=#{email}, landline=#{landline}, mobile=#{mobile}, last_updated_by=#{lastUpdatedBy},
last_updated_on=#{lastUpdatedOn},zipcode=#{pinCode},state=#{state},legal_address=#{address} where member_id=#{memberId} and department_id=#{departmentId} and esc_level=#{escLevel}
</update>


<insert id="addEscalationMain">
insert into bank_escalation_details (esc_id , member_id, department_id, esc_level, emp_name, designation, email, landline, mobile,
created_by, created_on, zipcode, state, legal_address )
values(#{escId}, #{memberId},#{departmentId}, #{escLevel},
#{name} ,#{designation} , #{email},
#{landline},#{mobile},#{createdBy},#{createdOn},
#{pinCode},#{state},#{address})
</insert>

</mapper>
