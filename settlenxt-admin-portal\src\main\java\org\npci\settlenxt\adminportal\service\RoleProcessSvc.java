/**
 * 
 */
package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.RoleDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseRoleProcessSvc;

/**
 * <AUTHOR>
 *
 */
public interface RoleProcessSvc extends BaseRoleProcessSvc {

	 void deactivateRole(RoleDTO roleDto) throws SettleNxtException;

	 void checkRoleAssignedToUser(RoleDTO roleDto);

	 void checkRoleStatusForDeactivate(RoleDTO roleDto);

	 List<CodeValueDTO> getRoleHierarchyList();

	 RoleDTO updateApproveOrRejectBulkRole(String roleIdList, String status, String remarks) throws SettleNxtException;

}
