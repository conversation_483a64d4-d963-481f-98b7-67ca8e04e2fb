$(document).ready(function() {
	$("#cycleDate").datepicker({
		dateFormat: 'yyyy-MM-dd',
		todayHighlight: true,
		autoclose: true,
		changeMonth: true,
		changeYear: true
	});

	var reportStatusArray = [];
	$("#search").click(function() {
		var cycleDate = $('#cycleDate').val();
		var bankType = $('#select-bankType').val();
		var valuesArray = $('#select-reportStatus option:selected').map(function() {
			return this.value;
		}).get().join("-");

		reportStatusArray.push(valuesArray);
		var reportStatusArr = reportStatusArray.toString();
		var cycleNumber = $('#select-cycleNumber').val();
		var settlementProductId = $('#select-settlementProductId').val();
		var url = "/reportProgressStatus";
		var data = "cycleDate," + cycleDate
			+ ",bankType," + bankType
			+ ",reportStatusArr," + reportStatusArr
			+ ",cycleNumber," + cycleNumber
			+ ",settlementProductId," + settlementProductId;
		postData(url, data);
	});

	$("#select-settlementProductId").change(function() {
		var prodId = $('#select-settlementProductId option:selected').attr("data-product_id");
		var split_string = prodId.split(",");
		var sortedProductId = split_string.sort();
		var dropdown = $('#select-cycleNumber');
		$(dropdown).empty();
		$("#select-cycleNumber").append(new Option("ALL", "ALL"));
		$.each(sortedProductId, function(_key, item) {
			$("#select-cycleNumber").append(new Option(item, item));
		});
		console.log(prodId);

	});

	$('#select-reportStatus').multiselect({
		includeSelectAllOption: true,
		buttonWidth: '100%',
		buttonClass: 'form-control margin-top-0px'
	});

	$("#tabnew").DataTable({
		"fnRowCallback": function(nRow, aData, _iDisplayIndex, _iDisplayIndexFull) {
			if (aData[5] == "COMPLETED") {
				$('td', nRow).addClass('bg-success');
			} else if (aData[5] == "INPROGRESS") {
				$('td', nRow).addClass('bg-warning');
			} else if (aData[5] == "FAILED") {
				$('td', nRow).addClass('bg-danger');
			}
		},
		dom: 'lBfrtip',
		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true,
	});


});