package org.npci.settlenxt.adminportal.dto;

import java.util.Date;

import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Component
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
public class MCCTipSurchargeDTO {

	private int mccTipSurchargeId;
	private String mccId;
	private String tipSurchargeId;
	private String checkerComments;
	private String addEditFlag;

	private String mccNameLookup;
	private String tipSurchargeLookup;
	private String requestState;
	private String lastOperation;
	private String status;
	private String createdBy;
	private Date createdOn;
	private String lastUpdatedBy;
	private Date lastUpdatedOn;
	private String userName;

}
