<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript">
	var actionColumnIndex = 5;
	var firstColumnToBeSkippedInFilterAndSort = false;
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 9;
	firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 8;
	firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
</script>

<script>
	var referenceNoListPendings = [];
	<c:if test="${not empty pendingCurrencyMasterList}">
	<c:forEach items="${pendingCurrencyMasterList}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	referenceNoListPendings.push('${operator.currencyId}');
	</c:if>
	</c:forEach>
	</c:if>
</script>

<script src="./static/js/validation/showCurrencyMaster.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="./static/js/jszip.min.js"></script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js"></script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: bottom;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>

<!-- Model -->
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalNews" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Currency
					Master Approval/Rejection</label>
				<p id="newsIds" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulkCurrencyMaster('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulkCurrencyMaster('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Model -->
<!-- Model -->
<input:hidden id="refNum" />
<div class="row">
	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>

<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/currencyMasterMain');"
			role="tab" data-toggle="tab"><span
			class="glyphicon glyphicon-credit-card"> </span> <spring:message
				code="currencyMaster.mainTab.title" /></a>

		<c:choose>
			<c:when test="${pendingAppCurrencyMaster eq 'Yes'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" role="tab" onclick="getPendingCurrencyMasterList();"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> </span> <spring:message
				code="currencyMaster.approvalTab.title" /></a>

	</ul>
	<div class="row">
		<div class="col-sm-12"></div>
	</div>

	<div class="tab-content">
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
			<div class="col-sm-12">
			<sec:authorize access="hasAuthority('Add Currency Master')">
									<c:if test="${addCurrencyMaster eq 'Yes'}">
										<a class="btn btn-success pull-right btn_align" href="#"
											onclick="submitForm('/currencyMasterCreation','P');"
											style="margin-top: -5px;"><em class="glyphicon-plus"></em>
											<spring:message code="currencyMaster.addCurrencyMasterBtn" /></a>
									</c:if>
								</sec:authorize>
								</div>
				<div class="col-sm-12">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="currencyMaster.clearBtn" />
					</button>
					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="csvExport"> <spring:message code="currencyMaster.csvBtn" />
					</a> <a class="btn btn-success pull-right btn_align" href="#"
						id="excelExport"><spring:message
							code="currencyMaster.exportBtn" /> </a>
				</div>
			</div>
			<c:if test="${showMainTab eq 'Yes'}">

				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="currencyMaster.listscreen.title" /></span></strong>
								
							</div>
							<div class="panel-body">

								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Currency Master</caption>
										<thead>
											<tr>
												<th scope="col"><label><spring:message
															code="currencyMaster.currencyId" /></label></th>
												<th scope="col"><label><spring:message
															code="currencyMaster.currencyMasterCode" /></label></th>
												<th scope = "col"><label><spring:message
															code="currencyMaster.currencyMasterDescription" /></label></th>
												<th scope="col"><label><spring:message
															code="currencyMaster.currencyAlpha" /></label></th>
												<th scope="col"><label><spring:message
															code="currencyMaster.currencyDecimalPosition" /></label></th>
											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty currencyMasterList}">
												<c:forEach var="currencyMaster"
													items="${currencyMasterList}">

													<tr>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMaster.currencyId}','V')">${currencyMaster.currencyId}</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMaster.currencyId}','V')">${currencyMaster.currencyCode}</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMaster.currencyId}','V')">${currencyMaster.currencyDescription}</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMaster.currencyId}','V')">${currencyMaster.currencyAlpha}</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMaster.currencyId}','V')">${currencyMaster.currencyDecimalPosition}</td>
													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>
			<c:if test="${showApprovalTab eq 'Yes'}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="currencyMaster.listscreen.title" /></span></span></strong>
								<c:if test="${not empty pendingCurrencyMasterList}">
									<sec:authorize access="hasAuthority('Approve Currency Master')">
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkCurrencyMaster('A','No')"
											id="submitButtonA"
											value="<spring:message code="feeRate.Approve" />" />
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkCurrencyMaster('R','No')"
											id="submitButtonR"
											value="<spring:message code="feeRate.Reject" />" />
									</sec:authorize>
								</c:if>
							</div>
							<div class="panel-body">
								<%-- <div class="row">
									<div class="col-sm-12">
										<button class="btn  pull-right btn_align" id="clearFilters">
											<spring:message code="currencyMaster.clearBtn" />
										</button>
										&nbsp; <a class="btn btn-success pull-right btn_align"
											href="#" id="csvExport"> <spring:message
												code="currencyMaster.csvBtn" />
										</a> <a class="btn btn-success pull-right btn_align" href="#"
											id="excelExport"><spring:message
												code="currencyMaster.exportBtn" /> </a>
									</div>
								</div> --%>
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Currency Master</caption>
										<thead>
											<tr>
												<sec:authorize
													access="hasAuthority('Approve Currency Master')">
													<th scope = "col"><input type=checkbox name='selectAllCheck'
														id="selectAll" data-target="toggleModalNews" value="All"></input></th>
												</sec:authorize>
												<th scope="col"><label><spring:message
															code="currencyMaster.currencyId" /></label></th>
												<th scope="col"><label><spring:message
															code="currencyMaster.currencyMasterCode" /></label></th>
												<th scope="col"><label><spring:message
															code="currencyMaster.currencyMasterDescription" /></label></th>
												<th scope="col"><label><spring:message
															code="currencyMaster.currencyAlpha" /></label></th>
												<th scope="col"><label><spring:message
															code="currencyMaster.currencyDecimalPosition" /></label></th>
												<th scope="col"><label><spring:message
															code="currencyMaster.requestType" /></label></th>
												<th scope="col"><label><spring:message
															code="currencyMaster.status" /></label></th>
												<th scope="col"><label><spring:message
															code="currencyMaster.checkerComents" /></label></th>
											</tr>
										</thead>
										<tbody>
											<c:if test="${not empty pendingCurrencyMasterList}">

												<c:forEach var="currencyMasters"
													items="${pendingCurrencyMasterList}">
													<tr>
														<sec:authorize
															access="hasAuthority('Approve Currency Master')">
															<c:if test="${currencyMasters.requestState =='P' }">
																<td><input type=checkbox name='type'
																	id="selectSingle" onclick="mySelect();"
																	value="${currencyMasters.currencyId}"></input></td>
															</c:if>
															<c:if test="${currencyMasters.requestState !='P' }">
																<td></td>
															</c:if>
														</sec:authorize>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMasters.currencyId}','P')">${currencyMasters.currencyId}</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMasters.currencyId}','P')">${currencyMasters.currencyCode}</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMasters.currencyId}','P')">${currencyMasters.currencyDescription}</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMasters.currencyId}','P')">${currencyMasters.currencyAlpha}</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMasters.currencyId}','P')">${currencyMasters.currencyDecimalPosition}</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMasters.currencyId}','P')">${currencyMasters.lastOperation}</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMasters.currencyId}','P')">
															<c:if test="${currencyMasters.requestState =='A' }">
																<spring:message
																	code="currencyMaster.requestState.approved.description" />
															</c:if> <c:if test="${currencyMasters.requestState =='P' }">
																<spring:message
																	code="currencyMaster.requestState.pendingApproval.description" />
															</c:if> <c:if test="${currencyMasters.requestState =='R' }">
																<spring:message
																	code="currencyMaster.requestState.rejected.description" />
															</c:if> <c:if test="${currencyMasters.requestState =='D' }">
																<spring:message
																	code="currencyMaster.requestState.discared.description" />
															</c:if>
														</td>
														<td
															onclick="javascript:viewCurrencyMaster('${currencyMasters.currencyId}','P')">${currencyMasters.checkerComments}</td>
													</tr>
												</c:forEach>
											</c:if>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>
		</div>
	</div>
</div>
