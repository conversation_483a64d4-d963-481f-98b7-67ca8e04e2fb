<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ page import="java.time.format.DateTimeFormatter"%>
<script type="text/javascript">
<c:if test="${showUser eq 'YES'}">
	var actionColumnIndex = 13;
	var firstColumnToBeSkippedInFilterAndSort=false;
	</c:if>
	<c:if test="${pendingAppUser eq 'YES'}">	
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 12;
	var firstColumnToBeSkippedInFilterAndSort=true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 12;
	var firstColumnToBeSkippedInFilterAndSort=false;
	</c:if>
	</c:if>
</script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>
<script>
var userIdpending=[];

<c:if test="${not empty pendingUserList}">
<c:forEach items="${pendingUserList}" var="operator">
<c:if test="${operator.requestState eq 'P'}">

userIdpending.push(${operator.userId});

</c:if>
</c:forEach>
</c:if>
</script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: bottom;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>


<!-- Modal -->

<div class="modal fade" id="toggleModal" tabindex="-1" role="dialog"
	aria-labelledby="toggleModal" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">User IDs</label>
				<p id="detailsHeadersss" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulk('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulk('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Modal Close-->

<script src="./static/js/validation/SearchUsersAdminPortal.js"
	type="text/javascript"></script>

<div class="row">
	
	
		<div role="alert" style="display: none" id="jqueryError4">
			<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
		</div>
				<div id="errLvType" class="alert alert-danger" role="alert"
			style="display: none"></div>
	
	</div>
	

<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showUser eq 'YES'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/showUsers');" role="tab"
			data-toggle="tab"><span class="glyphicon glyphicon-user">
				<spring:message code="msg.lbl.user" />
		</span> </a>

		<c:choose>
			<c:when test="${pendingAppUser eq 'YES'}">
				<li role="presentation" class="active">
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" role="tab" onclick="getPendingUserList();"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> <spring:message
					code="sm.lbl.approval" />
		</span></a>


	</ul>
	<form:form id="userSearch" name="userSearch">
	</form:form>

	
	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
				<div class="col-sm-12">
					<form:form onsubmit="removeSpace(this); encodeForm(this);"
						method="POST" id="addEditUser" modelAttribute="userInfoDto"
						action="" autocomplete="off">
						<c:choose>
							<c:when test="${pendingAppUser eq 'YES'}">
								<form:select path="userType" id="userType" class="form-control"
									style="width: 135px;" onchange="getPendingUserList();">
									<form:options itemLabel="description" itemValue="code"
										items="${selectUserTypeList}" />
								</form:select>
							</c:when>
							<c:when test="${showUser eq 'YES'}">
								<form:select path="userType" id="userType" class="form-control"
									style="width: 135px;" onchange="getUserList();">
									<form:options itemLabel="description" itemValue="code"
										items="${selectUserTypeList}" />
								</form:select>
							</c:when>
						</c:choose>
					</form:form>

					<sec:authorize access="hasAuthority('Add User')">
						<c:if test="${addUser eq 'Yes'}">

							<a class="btn btn-success pull-right btn_align" href="#"
								onclick="submitForm('/userCreation');"
								style="margin-top: -30px;"><em class="glyphicon-plus"></em> <spring:message
									code="msg.lbl.addUser" /></a>

						</c:if>
					</sec:authorize>


				</div>
			</div>


			<div class="row">
				<div class="col-sm-12">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="ifsc.clearFiltersBtn" />
					</button>


					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="excelExport"><spring:message code="ifsc.exportBtn" /> </a>

					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="csvExport"><spring:message code="ifsc.csvBtn" /> </a>


				</div>
			</div>
			<c:if test="${not empty userlist}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message code="msg.lbl.userList" /></span></strong>
							</div>
							<div class="panel-body">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width:150%;">
										<caption style="display:none;">USERS</caption>
										<thead>
											<tr>
												<th scope="col" style="display: none;"><spring:message
														code="am.lbl.userId" /></th>
												<th scope="col"><spring:message code="am.lbl.loginId" /></th>
												<th scope="col"><spring:message code="am.lbl.bankName" /></th>
												<th scope="col"><spring:message code="am.lbl.name" /></th>
                                                  
												<th scope="col"><spring:message code="am.lbl.emailId" /></th>
												<th scope="col"><spring:message code="am.lbl.mobileNo" /></th>
												<th scope="col"><spring:message code="am.lbl.contactNo" /></th>
                                                   
												<th scope="col"><spring:message code="am.lbl.empId" /></th>
												<th scope="col"><spring:message code="am.lbl.createdDate" /></th>
												<th scope="col"><spring:message code="am.lbl.status" /></th>
												<th scope="col"><spring:message code="am.lbl.lastLogin" /></th>
                                                   
												<th scope="col"><spring:message code="am.lbl.createdBy" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="user" items="${userlist}">

												<tr onclick="javascript:viewUserInfo('${user.userId}','V')">
													<td style="display: none;">${user.userId}</td>
													<td>${user.loginId}</td>
													<td>${user.bankName}</td>
													<td>${user.firstName}&nbsp;${user.middleName}&nbsp;${user.lastName}</td>

													<td>${user.emailId}</td>
													<td>${user.mobileNo}</td>
													<td>${user.contactNo}</td>
													<td>${user.empId}</td>
													<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
															value="${user.createdDate}" /></td>

													<c:choose>
														<c:when test="${user.lockStatus ne 'L' }">
															<c:choose>
																<c:when test="${user.status eq 'D' }">
																	<td style="color: red">Deleted</td>
																</c:when>
																
																<c:when test="${user.status eq 'S' }">
																	<td style="color: red">Suspended</td>
																</c:when>
																<c:when test="${user.status eq 'Z' or  user.status eq 'R' }">
																	<td style="color: red"><spring:message
																			code="common.msg.lbl.reset" /></td>
																</c:when>
																<c:when test="${user.status eq 'A'  }">
																	<td style="color: red">Active</td>
																</c:when>

																<c:otherwise>
																	<td style="color: blue"><spring:message
																			code="common.msg.lbl.active" /></td>
																</c:otherwise>
															</c:choose>
														</c:when>
														<c:when test="${user.lockStatus eq 'L'}">
															<td style="color: red">Locked</td>
														</c:when>
														<c:otherwise>
															<td><spring:message code="common.msg.lbl.null" /></td>
														</c:otherwise>
													</c:choose>

													<td>${user.lastLoginTs.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>

													<td>${user.createdBy}</td>




												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>

			</c:if>

			<c:if test="${empty userlist}">
				<div class="row">
					<div class="col-md-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<c:if test="${pendingAppUser eq 'YES'}">
									<sec:authorize access="hasAuthority('Approve User')">
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulk('A','no')" id="submitButton"
											value="<spring:message code="feeRate.Approve" />" />
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulk('R','no')" id="submitButton"
											value="<spring:message code="feeRate.Reject" />" />
									</sec:authorize>
								</c:if>
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message code="msg.lbl.userList" /></span></strong>
							</div>
							<div class="panel-body">
								<c:choose>
									<c:when test="${not empty pendingUserList}">
										<div class="table-responsive">
											<table id="tabnew" class="table table-striped table-bordered"
												style="width:100%;">
												<caption style="display:none;">USERS</caption>
												<thead>
													<tr>
														<sec:authorize access="hasAuthority('Approve User')">
															<th scope="col"><input type=checkbox name='selectAllCheck'
																id="selectAll" data-toggle="modal"
																data-target="toggleModal" value=""></input></th>
														</sec:authorize>

														<th scope="col"><spring:message code="am.lbl.loginId" /></th>
														<th scope="col"><spring:message code="am.lbl.firstName" /></th>
														<th scope="col"><spring:message code="am.lbl.lastName" /></th>
														<th scope="col"><spring:message code="am.lbl.emailId" /></th>
														<th scope="col"><spring:message code="am.lbl.mobileNo" /></th>
														<th scope="col">User Status</th>
														<th scope="col"><spring:message code="am.lbl.requestType" /></th>
														<th scope="col"><spring:message code="am.lbl.status" /></th>
														<th scope="col">Request By</th>
														<th scope="col">Request Date</th>
                                                           
														<th scope="col"><spring:message code="sm.lbl.checkerComments" /></th>
													</tr>
												</thead>
												<tbody>
													<c:forEach var="users" items="${pendingUserList}">



														<c:if test="${users.requestState eq 'P' }">
															<tr
																onclick="javascript:viewUserInfo('${users.userId}','P')">
																<sec:authorize access="hasAuthority('Approve User')">
																	<td onclick=event.stopPropagation()><input
																		type=checkbox name='type' id="selectSingle"
																		onclick="mySelect();" value='${users.userId}'></input></td>
																</sec:authorize>
														</c:if>

														<c:if test="${users.requestState eq 'R' }">
															<tr
																onclick="javascript:viewUserInfoRej('${users.userId}','V')">
																<sec:authorize access="hasAuthority('Approve User')">
																	<td onclick=event.stopPropagation()><input
																		type=checkbox name='types' style="display: none"
																		value='${users.userId}'></input></td>
																</sec:authorize>
														</c:if>



														<td>${users.loginId}</td>
														<td>${users.firstName}</td>
														<td>${users.lastName}</td>
														<td>${users.emailId}</td>
														<td>${users.mobileNo}</td>
														<c:choose>
														<c:when test="${users.lockStatus ne 'L' }">
															<c:choose>
																<c:when test="${users.status eq 'D' }">
																	<td style="color: red">Deleted</td>
																</c:when>
																
																<c:when test="${users.status eq 'S' }">
																	<td style="color: red">Suspended</td>
																</c:when>
																<c:when test="${users.status eq 'Z' or  users.status eq 'R' }">
																	<td style="color: red"><spring:message
																			code="common.msg.lbl.reset" /></td>
																</c:when>

                                                                   <c:when test="${users.status eq 'A' }">
																	<td style="color: red">Active</td>
																</c:when>
																<c:otherwise>
																	<td style="color: blue"><spring:message
																			code="common.msg.lbl.active" /></td>
																</c:otherwise>
															</c:choose>
														</c:when>
														<c:when test="${users.lockStatus eq 'L'}">
															<td style="color: red">Locked</td>
														</c:when>
														<c:otherwise>
															<td><spring:message code="common.msg.lbl.null" /></td>
														</c:otherwise>
													</c:choose>

														<td>${users.lastOperation}</td>

														<td>${users.requestState =='P' ? 'Pending for Approval' : 'Rejected'}</td>
														<c:choose>
															<c:when test="${not empty users.lastUpdatedBy}">
																<td>${users.lastUpdatedBy}</td>


															</c:when>
															<c:otherwise>
																<td>${users.createdBy}</td>


															</c:otherwise>
														</c:choose>


														<c:choose>
															<c:when test="${not empty users.lastUpdatedOn}">

																<td>${users.lastUpdatedOn.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>

															</c:when>
															<c:otherwise>

																<td>${users.createdOn.format( DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"))}</td>


															</c:otherwise>
														</c:choose>



														<c:choose>
															<c:when test="${users.requestState eq 'R' }">

																<td>${users.checkerComments}</td>

															</c:when>
															<c:otherwise>

																<td></td>


															</c:otherwise>
														</c:choose>


														</tr>
													</c:forEach>
												</tbody>
											</table>
										</div>
									</c:when>
									<c:otherwise>

										<div class="table-responsive">
											<table id="tabnew" class="table table-striped table-bordered"
												style="width:100%;">
												<caption style="display:none;">USERS</caption>
												<thead>
													<tr>
														<th scope="col"><spring:message code="am.lbl.requestId" /></th>
														<th scope="col"><spring:message code="am.lbl.loginId" /></th>
														<th scope="col"><spring:message code="am.lbl.firstName" /></th>
														<th scope="col"><spring:message code="am.lbl.lastName" /></th>
														<th scope="col"><spring:message code="am.lbl.emailId" /></th>
														<th scope="col"><spring:message code="am.lbl.mobileNo" /></th>
														<th scope="col"><spring:message code="am.lbl.requestType" /></th>
														<th scope="col"><spring:message code="am.lbl.status" /></th>
													</tr>
												</thead>
												<tbody>
												</tbody>
											</table>
										</div>
									</c:otherwise>
								</c:choose>

							</div>
						</div>
					</div>
				</div>
			</c:if>

		</div>

	</div>





</div>
