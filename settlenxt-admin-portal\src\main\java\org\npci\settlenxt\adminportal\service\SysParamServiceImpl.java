package org.npci.settlenxt.adminportal.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.dto.SysParamsDTO;
import org.npci.settlenxt.adminportal.repository.SysParamRepository;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SysParamServiceImpl implements SysParamService {

	@Autowired
	SysParamRepository sysParamRepository;

	@Autowired
	SessionDTO sessionDTO;

	@Override
	public List<SysParamsDTO> getSysParamApprovedList() {

		return sysParamRepository.getSysParamApprovedList(CommonConstants.REQUEST_STATE_APPROVED);
	}

	@Override
	public List<SysParamsDTO> getSysParamPendingList() {

		List<String> requestStateList = new ArrayList<>();
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_SUBMITTED);
		requestStateList.add(BaseCommonConstants.REQUEST_STATE_REJECTED);
		return sysParamRepository.getSysParamPendingList(requestStateList);
	}

	// checker approval
	@Override
	public SysParamsDTO approveOrRejectSysParam(String sysType, String sysKey, String status, String remarks) {
		SysParamsDTO sysParamsDto = getSysParamStgInfo(sysType, sysKey);
		sysParamsDto.setRequestState(status);
	    sysParamsDto.setCheckersComment(remarks);
		Date date = new Date();
		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			sysParamsDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			SysParamsDTO ifscdtoMain = sysParamRepository.getSysParamsInfoFromMain(sysType, sysKey);
			if (ifscdtoMain != null) {
				sysParamsDto.setLastUpdatedOn(date);
				sysParamsDto.setLastUpdatedBy(sessionDTO.getUserName());
				sysParamRepository.updateSysParamMain(sysParamsDto);
			} else {
				sysParamsDto.setCreatedOn(date);
				sysParamRepository.insertSysParamMain(sysParamsDto);
			}
		}
		if (CommonConstants.REQUEST_STATE_REJECTED.equals(status)) {
			sysParamsDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
		}
		sysParamsDto.setCheckersComment(remarks);
		sysParamsDto.setLastUpdatedOn(date);
		sysParamsDto.setLastUpdatedBy(sessionDTO.getUserName());
		sysParamRepository.updateSysParamRequestState(sysParamsDto);

		return sysParamsDto;
	}

	@Override
	public SysParamsDTO getSysParamStgInfo(String sysType, String sysKey) {
		SysParamsDTO sysParamStg = sysParamRepository.getSysParamStgInfoBy(sysType, sysKey);
		if (sysParamStg == null) {
			throw new SettleNxtException("No Data Exist", "");
		}
		return sysParamStg;
	}

	@Override
	public SysParamsDTO getSysParamsInfoFromMain(String sysType, String sysKey) {

		return sysParamRepository.getSysParamsInfoFromMain(sysType, sysKey);
	}

	@Override
	public List<SysParamsDTO> getSysParamListType() {
		return sysParamRepository.getSysParamListType();
	}

	@Override
	public void addSysParam(SysParamsDTO sysParamDTO) {
		sysParamRepository.addSysParam(sysParamDTO);

	}

	@Override
	public void updateSysParam(SysParamsDTO sysParamDTO) {
		sysParamRepository.updateStgSysParam(sysParamDTO);

	}

	@Override
	public boolean checkDupSysDetails(String sysType, String sysKey) {

		return sysParamRepository.checkDuplicateinStg(sysType, sysKey) > 0;
	}

	@Override
	public SysParamsDTO discardSysParamInfo(String sysType, String sysKey) {
		SysParamsDTO sysParamDTO = getSysParamStgInfo(sysType, sysKey);
		SysParamsDTO sysParamDTOMain = sysParamRepository.getSysParamsInfoFromMain(sysType, sysKey);
		if (sysParamDTOMain != null) {
			sysParamDTOMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			sysParamDTOMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			sysParamDTOMain.setLastUpdatedBy(sessionDTO.getUserName());
			sysParamDTOMain.setLastUpdatedOn(new Date());
			sysParamRepository.updateStgSysParam(sysParamDTOMain);
			return sysParamDTOMain;
		} else {
			sysParamRepository.deleteDiscardedEntry(sysType, sysKey);
		}
		return sysParamDTO;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public SysParamsDTO updateApproveOrRejectSysParamBulk(String[] bulkSysParamList, String status, String remarks) {
		SysParamsDTO sysParam = new SysParamsDTO();
		List<String> sysParamSeqIdArray = Arrays.asList(bulkSysParamList);

		List<SysParamsDTO> sysParamDtoArr = sysParamRepository.getSysParamStgInfoList(sysParamSeqIdArray);
		Map<String, List<SysParamsDTO>> sysParamMap = sysParamDtoArr.stream()
				.collect(Collectors.groupingBy(x -> x.getSysType() + x.getSysKey()));

		for (String bulkSysParam:bulkSysParamList) {

			try {
				List<SysParamsDTO> sysParamListDto = sysParamMap.get(bulkSysParam);
				SysParamsDTO sysParamDto = sysParamListDto.get(0);
				if (sysParamDto == null) {
					throw new SettleNxtException("Exception occurred with Sys Param : " + bulkSysParam, "");
				} else {
					sysParamDto.setRequestState(status);
					sysParamDto.setCheckersComment(remarks);
					sysParamDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);

					if (sysParamDto.getRequestState().equals(CommonConstants.REQUEST_STATE_APPROVED)) {
						updateApprovedSysParam(sysParamDto);
						sysParamDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
						sysParam.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
						sysParamDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
					}
					sysParamDto.setLastUpdatedOn(new Date());
					sysParamDto.setLastUpdatedBy(sessionDTO.getUserName());
					sysParamRepository.updateSysParamStgState(sysParamDto);
				}
			} catch (Exception ex) {
				throw new SettleNxtException("Exception for Sys Param : " + bulkSysParam, "", ex);
			}
		}
		return sysParam;
	}

	private void updateApprovedSysParam(SysParamsDTO sysParamDto) {
		SysParamsDTO sysParamDtoDb = sysParamRepository.getSysParamsInfoFromMain(sysParamDto.getSysType(),
				sysParamDto.getSysKey());
		sysParamDto.setSysType(sysParamDto.getSysType());
		sysParamDto.setSysKey(sysParamDto.getSysKey());
		if (ObjectUtils.isEmpty(sysParamDtoDb)) {
			sysParamDto.setCreatedOn(new Date());
			sysParamDto.setLastUpdatedOn(null);
			sysParamDto.setLastUpdatedBy(null);
			sysParamRepository.insertSysParamMain(sysParamDto);
		} else {

			sysParamRepository.updateSysParamMain(sysParamDto);
		}

		sysParamDto.setStatusCode(CommonConstants.TRANSACT_SUCCESS);
	}

}
