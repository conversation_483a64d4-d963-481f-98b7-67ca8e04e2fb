<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.JsonValidatorRepository">
	
	<select id="getJsonValidatorList" resultType="org.npci.settlenxt.portal.common.dto.JsonValidatorDTO">
		
		SELECT api as api , code as code , field_name as fieldName , regexps as regexps , is_mandatory as isMandatory ,created_by as createdBy ,created_on as createdOn
		, last_updated_by as lastUpdatedBy , last_updated_on as lastUpdatedOn , reason_code as reasonCode , reason_code_desc as reasonCodeDesc , pcode as pcode,
		priority as priority,request_state as requestState,status as status , last_operation as lastOperation , checker_comments as checkerComments,seq_id as seqId from jsonvalidator_stg where request_state= 'A' ;

	</select>
	
	<select id="getJsonValidatorMainList" resultType="org.npci.settlenxt.portal.common.dto.JsonValidatorDTO">
		
		SELECT jv.api as api , jv.code as code , jv.field_name as fieldName , jv.regexps as regexps , jv.is_mandatory as isMandatory ,jv.created_by as createdBy ,jv.created_on as createdOn
		, jv.last_updated_by as lastUpdatedBy , jv.updated_on as lastUpdatedOn , jv.status as status,jv.reason_code as reasonCode , jv.reason_code_desc as reasonCodeDesc , jv.pcode as pcode,
		jv.priority as priority,jv.seq_id as seqId from jsonvalidator jv inner join jsonvalidator_stg  jvs on jv.seq_id = jvs.seq_id where jvs.request_state = 'A';
	</select>
	
	<select id="getPcodeList" resultType="java.lang.String">
		select DISTINCT(proc_code) from func_code;
	</select>

	<select id="getJsonValidatorPendingList" resultType="org.npci.settlenxt.portal.common.dto.JsonValidatorDTO">
		
		SELECT api as api , code as code , field_name as fieldName , regexps as regexps , is_mandatory as isMandatory ,created_by as createdBy ,created_on as createdOn
		, last_updated_by as lastUpdatedBy , last_updated_on as lastUpdatedOn , reason_code as reasonCode , reason_code_desc as reasonCodeDesc , pcode as pcode,
		priority as priority,request_state as requestState ,status as status, last_operation as lastOperation , checker_comments as checkerComments, seq_id as seqId from jsonvalidator_stg where request_state in ('P','R');

	</select>


	<select id="getJsonValidator" resultType="org.npci.settlenxt.portal.common.dto.JsonValidatorDTO">
		
		SELECT api as api , code as code , field_name as fieldName , regexps as regexps , is_mandatory as isMandatory ,created_by as createdBy ,created_on as createdOn
		, last_updated_by as lastUpdatedBy , updated_on as lastUpdatedOn , reason_code as reasonCode ,status as status, reason_code_desc as reasonCodeDesc , pcode as pcode,
		priority as priority,seq_id as seqId from jsonvalidator where seq_id = #{seqId} ;

	</select>
	
	
	<select id="getJsonValidatorStgDetail" resultType="org.npci.settlenxt.portal.common.dto.JsonValidatorDTO">
		
		SELECT api as api , code as code , field_name as fieldName , regexps as regexps , is_mandatory as isMandatory ,created_by as createdBy ,created_on as createdOn
		, last_updated_by as lastUpdatedBy , last_updated_on as lastUpdatedOn , reason_code as reasonCode , reason_code_desc as reasonCodeDesc , pcode as pcode,
		priority as priority,request_state as requestState ,status as status,last_operation as lastOperation , checker_comments as checkerComments,seq_id as seqId from jsonvalidator_stg where seq_id = #{seqId} ;

	</select>

	<insert id="addJsonValidatorStg">
        INSERT INTO  JSONVALIDATOR_STG (api, code,field_name,regexps,is_mandatory,reason_code,reason_code_desc,pcode,priority,CREATED_BY,CREATED_ON,LAST_UPDATED_BY,LAST_UPDATED_ON,request_state,last_operation,checker_comments,seq_id,status)VALUES(#{api}, #{code}, 
        #{fieldName},#{regexps},#{isMandatory},#{reasonCode},#{reasonCodeDesc},#{pcode},#{priority},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{requestState}, #{lastOperation},#{checkerComments},#{seqId},#{status})
	</insert>
	
	<update id="updateJsonValidatorStg">
	UPDATE  jsonValidator_stg SET api = #{api},is_mandatory=#{isMandatory},code =#{code}, field_name=#{fieldName}, regexps=#{regexps}, pcode=#{pcode}, priority=#{priority}, reason_code = #{reasonCode},reason_code_desc = #{reasonCodeDesc},LAST_UPDATED_BY = #{lastUpdatedBy},last_updated_on = #{lastUpdatedOn},REQUEST_STATE =#{requestState},LAST_OPERATION=#{lastOperation},checker_comments = #{checkerComments},status = #{status} where seq_id = #{seqId};
	</update>
	
	<update id="updateJsonValidator">
	UPDATE  jsonValidator SET api = #{api},code =#{code}, field_name=#{fieldName}, regexps=#{regexps}, pcode=#{pcode}, priority=#{priority}, reason_code = #{reasonCode},reason_code_desc = #{reasonCodeDesc},LAST_UPDATED_BY = #{lastUpdatedBy},updated_on = #{lastUpdatedOn},status = #{status} where seq_id = #{seqId};
	</update>
	
	<insert id="saveJsonValidator">
        INSERT INTO  JSONVALIDATOR (api, code,field_name,regexps,is_mandatory,reason_code,reason_code_desc,pcode,priority,CREATED_BY,CREATED_ON,LAST_UPDATED_BY,UPDATED_ON,seq_id,status)VALUES(#{api}, #{code}, 
        #{fieldName},#{regexps},#{isMandatory},#{reasonCode},#{reasonCodeDesc},#{pcode},#{priority},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{seqId},#{status})
	</insert>

	<delete id="deleteDiscardedJsonValidator">
		delete from JSONVALIDATOR_STG where seq_id = #{seqId};
	</delete>
	
	<select id="fetchSeqId" resultType="org.npci.settlenxt.portal.common.dto.JsonValidatorDTO">
		SELECT nextval('jsonValidator_stg_seq') as seqId;
	</select>
	
</mapper>