<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript"> 
	<c:if test="${showMainTab eq 'Y'}">
	var actionColumnIndex = 8;
	var firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
	<c:if test="${showApprovalTab eq 'Y'}">
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 10;
	var firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 9;
	var firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
</c:if>
</script>

<script>
    var holidayMasterListPendings = [];
    
	<c:if test="${not empty holidayMasterList}">
		<c:forEach items="${holidayMasterList}" var="operator">
			<c:if test="${operator.requestState eq 'P' }">
			holidayMasterListPendings.push(${operator.holidaySeqId});
			</c:if>
		</c:forEach>
	</c:if>
</script>

<script src="./static/js/validation/showHolidayMaster.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js"></script>
<script type="text/javascript" src="./static/js/jszip.min.js"></script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js"></script>

<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />

<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: bottom;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>

<div id="errorStatus2" class="alert alert-danger" role="alert"
	style="display: none"></div>
<div id="errorStatus5" class="alert alert-danger" role="alert"
	style="display: none"></div>

<!-- Model -->
<div class="modal fade" id="toggleModal" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalForexRate" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Holiday
					Master Seq Id</label>
				<p id="holidayMasterSeqIds" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulkHolidayMaster('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulkHolidayMaster('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Model -->

<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Y'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/getHolidayMasterList');"
			role="tab" data-toggle="tab"> <span
			class="glyphicon glyphicon-list-alt"></span> <spring:message
				code="holidayMaster.mainTab.title" /></a>
		<c:choose>
			<c:when test="${showApprovalTab eq 'Y'}">
				<li role="presentation" class="active">
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" role="tab" onclick="getPendingHolidayMasterList();"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> </span> <spring:message
				code="holidayMaster.approvalTab.title" /></a>
	</ul>

	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default">
						<div class="panel-heading">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message
										code="holidayMaster.listscreen.title" /></span></strong>
						</div>
						<c:if test="${showMainTab eq 'Y'}">
							<sec:authorize access="hasAuthority('Add Holiday Master')">
								<c:if test="${showAddButton eq 'Y'}">
									<div class="row">
										<a class="btn btn-success pull-right btn_align" href="#"
											onclick="submitForm('/addHoliday','P');"
											style="margin: 3px 90px 3px 0px;"><em
											class="glyphicon-plus"></em> <spring:message
												code="holidayMaster.addHolidayMasterBtn" /> </a>
									</div>
								</c:if>
							</sec:authorize>
						</c:if>
						<c:if test="${showApprovalTab eq 'Y'}">
							<c:if test="${not empty holidayMasterList}">
								<sec:authorize access="hasAuthority('Approve Holiday Master')">
									<div class="row" style="margin: 3px 78px 3px 0px;">
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkHolidayMaster('A','No')"
											id="submitButton"
											value="<spring:message code="holidayMaster.approve" />" /> <input
											type="button" class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkHolidayMaster('R','No')"
											id="submitButton"
											value="<spring:message code="holidayMaster.reject" />" />
									</div>
								</sec:authorize>
							</c:if>

						</c:if>
						<div class="row">
								<div class="col-sm-12">
									<button class="btn  pull-right btn_align" id="clearFilters">
										<spring:message code="ifsc.clearFiltersBtn" />
									</button>
									&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
										id="csvExport"><spring:message code="ifsc.exportBtn" /> </a>
									<a class="btn btn-success pull-right btn_align" href="#"
										id="excelExport"><spring:message code="ifsc.csvBtn" /> </a>

								</div>
						</div>
						

						<div class="panel-body">
							<div class="table-responsive">
								<table id="tabnew" class="table table-striped table-bordered"
									style="width: 100%">
									<caption style="display: none;">Holiday Master</caption>

									<thead>
										<tr>
											<c:if test="${showApprovalTab eq 'Y'}">
												<sec:authorize
													access="hasAuthority('Approve Holiday Master')">
													<th scope="col"><input type=checkbox
														name='selectAllCheck' id="selectAll" value='Hi'></input></th>
												</sec:authorize>
											</c:if>
											<th scope="col"><spring:message
													code="holidayMaster.holidaySeqId" /></th>
											<th scope="col"><spring:message
													code="holidayMaster.holidayDate" /></th>
											<th scope="col"><spring:message
													code="holidayMaster.holidayDesc" /></th>
											<th scope="col"><spring:message
													code="holidayMaster.periodType" /></th>
											<th scope="col"><spring:message
													code="holidayMaster.product" /></th>
											<th scope="col"><spring:message
													code="holidayMaster.dayOfTheWeek" /></th>
											<th scope="col"><spring:message
													code="holidayMaster.weekType" /></th>
											<th scope="col"><spring:message
													code="holidayMaster.createdDate" /></th>

											<c:choose>
												<c:when test="${showApprovalTab eq 'Y'}">
													<th scope="col"><spring:message
															code="holidayMaster.approvalStatus" /></th>
													<th scope="col"><spring:message
															code="sm.lbl.checkerComments" /></th>
												</c:when>
												<c:otherwise>
													<th scope="col"><spring:message
															code="holidayMaster.status" /></th>
												</c:otherwise>
											</c:choose>

										</tr>
									</thead>

									<tbody>
										<c:forEach var="holidayMaster" items="${holidayMasterList}">
											<c:choose>
												<c:when test="${showMainTab eq 'Y' }">
													<tr
														onclick="javascript:viewHolidayMasterInfo('${holidayMaster.holidaySeqId}','${showMainTab}', '/viewApproveHolidayMaster')">
												</c:when>

												<c:otherwise>


													<c:if test="${ holidayMaster.requestState  eq 'P'}">

														<tr
															onclick="javascript:viewHolidayMasterInfo('${holidayMaster.holidaySeqId}','${showMainTab}', '/viewApproveHolidayMaster')">
															<sec:authorize
																access="hasAuthority('Approve Holiday Master')">
																<td onclick=event.stopPropagation()><input
																	type=checkbox name='type' id="selectSingle"
																	onclick="mySelect();"
																	value='${holidayMaster.holidaySeqId}'></input></td>
															</sec:authorize>
													</c:if>

													<c:if test="${holidayMaster.requestState  eq 'R'}">

														<tr
															onclick="javascript:viewHolidayMasterInfo('${holidayMaster.holidaySeqId}','${showMainTab}', '/viewApproveHolidayMaster')">
															<sec:authorize
																access="hasAuthority('Approve Holiday Master')">
																<td><input type=checkbox name='types'
																	style="display: none;"
																	value='${holidayMaster.holidaySeqId}'></input></td>
															</sec:authorize>
													</c:if>

												</c:otherwise>
											</c:choose>

											<td>${holidayMaster.holidaySeqId}</td>
											<td><fmt:formatDate pattern="dd/MM/yyyy"
													value="${holidayMaster.holidayDate}" /></td>

											<td>${holidayMaster.holidayDesc}</td>
											<td>${holidayMaster.periodType}</td>
											<td>${holidayMaster.product}</td>
											<td>${holidayMaster.dayOfWeek}</td>
											<td>${holidayMaster.weekType}</td>
											<c:choose>
												<c:when test="${not empty holidayMaster.lastUpdatedOn }">
													<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
															value="${holidayMaster.lastUpdatedOn}" /></td>
												</c:when>
												<c:otherwise>
													<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
															value="${holidayMaster.createdOn}" /></td>
												</c:otherwise>
											</c:choose>

											<c:choose>
												<c:when test="${showApprovalTab eq 'Y'}">
													<td><c:if test="${holidayMaster.requestState=='A' }">
															<spring:message
																code="holidayMaster.requestState.approved.description" />
														</c:if> <c:if test="${holidayMaster.requestState=='P' }">
															<spring:message
																code="holidayMaster.requestState.pendingApproval.description" />
														</c:if> <c:if test="${holidayMaster.requestState=='R' }">
															<spring:message
																code="holidayMaster.requestState.rejected.description" />
														</c:if> <c:if test="${holidayMaster.requestState=='D' }">
															<spring:message
																code="holidayMaster.requestState.discarded.description" />
														</c:if> &nbsp;</td>
													<td>${holidayMaster.checkerComments}</td>
												</c:when>
												<c:otherwise>
													<td><c:if test="${holidayMaster.status=='A' }">
															<spring:message code="holidayMaster.activeStatus" />
														</c:if> <c:if test="${holidayMaster.status=='I' }">
															<spring:message code="holidayMaster.inactiveStatus" />
														</c:if></td>
												</c:otherwise>
											</c:choose>
										</c:forEach>
									</tbody>

								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>