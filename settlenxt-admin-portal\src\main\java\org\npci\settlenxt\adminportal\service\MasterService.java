package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.dto.CityDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.CountryDTO;
import org.npci.settlenxt.portal.common.dto.LookUpDTO;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.TxnSettlementDTO;
import org.npci.settlenxt.portal.common.service.BaseMasterService;

public interface MasterService extends BaseMasterService {
	
	List<LookUpDTO> getIfscCodes();

	List<CountryDTO> getCountryList();

	List<CityDTO> getCityMaster(int stateId);

	String getStateCode(int stateId);

	List<LookUpDTO> getStateMaster();

	List<LookUpDTO> getCityMaster();

	String getBinFeatures(String cardType);

	String getBinCardVariant(String cardType);

	MemberDTO getUniqueBankName(String participantId);

	List<String> getListUniqueBankName(String participantId);

	List<String> getFunctionCode();

	TxnSettlementDTO getFunctionCode(String funcCode);

	List<CodeValueDTO> getCachedLookUpData(String lookupType);

	List<CodeValueDTO> getCachedLookUpDataSortedByDescription(String lookupType);

}
