let myTable;
$(document).ready(function() {


	$(document).on('input', 'input[id^="input1_"], input[id^="input2_"]', function(event) {
		var target = event.target;
		dataMap[target.id] = target.value;
		console.log(dataMap); // To see the updated map in the console
	});



	$("#outgoingCreatedDate").datepicker({
		dateFormat: "yy-mm-dd",
		changeMonth: true,
		changeYear: true,
		maxDate: 0
	});


	$('#outgoingCreatedDate').on('keyup keypress blur change', function() {
		validateDate();
	});

	$('#pan').on('keyup keypress blur change', function() {
		validatePan();
	});
	
	$('#rrn').on('keyup keypress blur change', function() {
		validateRrn();
	});



	$('#schemeCode').on(' keypress change', function() {
		validateSchemeCode();
	});


	$('#messageDirection').on(' keypress  change', function() {
		validateMessageDirection();
	});




	myTable = $("#tabnew").DataTable({

		initComplete: function() {
			var api = this.api();

			// For each column
			api
				.columns()
				.eq(0)
				.each(function(colIdx) {
					//If first column to be skipped to include the filter for the reasons line check box 
					if (!(colIdx == 0 && firstColumnToBeSkippedInFilterAndSort) && !(colIdx < skipLevel && document.getElementById("isInput").value == 'Y')) {
						// Set the header cell to contain the input element
						var cell = $('#tabnew thead tr th').eq(
							$(api.column(colIdx).header()).index()
						);
						var title = $(cell).text();
						if (colIdx < 15) {

							$(cell).html(title + '<br><input class="search-box"   type="text" />');

							// On every keypress in this input
							$(
								'input',
								$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
							)
								.off('keyup change')
								.on('change', function() {
									// Get the search value
									$(this).attr('title', $(this).val());
									var regexr = '({search})';

									cursorPosition = this.selectionStart;
									// Search the column for that value
									api
										.column(colIdx)
										.search(
											this.value != ''
												? regexr.replace('{search}', '(((' + this.value + ')))')
												: '',
											this.value != '',
											this.value == ''
										)
										.draw();
								})
								.on('click', function(e) {
									e.stopPropagation();
								})
								.on('keyup', function(e) {
									e.stopPropagation();

									$(this).trigger('change');
									if (cursorPosition && cursorPosition != null) {
										$(this)
											.focus()[0]
											.setSelectionRange(cursorPosition, cursorPosition);
									}
								});
						} else {
							$(cell).html(title + '<br> &nbsp;');
						}
					}
				});
			$('#tabnew_filter').hide();

		},
		// Disabled ordering for first column in case
		columnDefs: [
			{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
		],
		"order": [],
		dom: 'lBfrtip',
		buttons: [
			{
				extend: 'excelHtml5',
				text: 'Export',
				filename: 'Incoming txn',
				header: 'false',
				title: null,
				sheetName: 'Incoming txn',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			},
			{
				extend: 'csvHtml5',
				text: 'Export',
				filename: 'Incoming txn',
				header: 'false',
				title: null,
				sheetName: 'Incoming txn',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			}

		],

		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true,
	});












	$('#example').on('click', '.tabRowId', function() {


		var txnId = $(this).attr('data-value');
		var trnDateStr = $('#transactionDate').val();

		var txntype = $('#txntype').val();
		var url = "/getTransaction";
		var data = "txnId," + txnId + ",trnDateStr," + trnDateStr + ",txntype," + txntype;
		postData(url, data);




	});



	$('#backButton').click(function() {


		var data = "";
		postData('/viewTransactionSearch', data);
	});

	$('#resetBtn').click(function() {
		$('#outgoingCreatedDate').val('');

		$('#schemeCode').val('0');
		$('#funcCodeDescription').val('0');
		$('#pan').val('');
		$('#rrn').val('');
		document.querySelectorAll(".error").forEach(ele => {
			ele.innerHTML = '';
		});
		$('#tableDiv').hide();

	});

	$("#excelExport").on("click", function() {
		$(".buttons-excel").trigger("click");
	});

	$("#csvExport").on("click", function() {
		$(".buttons-csv").trigger("click");
	});

	$("#clearFilters").on("click", function() {
		$(".search-box").each(function() {
			$(this).val("");
			$(this).trigger("change");
		});
	});

	$('#selectAll').click(function() {
		var filteredData = myTable.rows({ search: 'applied' }).nodes();

		if ($(this).hasClass('allChecked')) {
			$(filteredData).find('input.selectSingle').prop('checked', false);
		} else {
			$(filteredData).find('input.selectSingle').prop('checked', true);
		}
		$(this).toggleClass('allChecked');
	});

	$(document).on('change', '.selectSingle', function() {
		updateSelectAllState(myTable);
	});
	$('#approve, #reject').click(function() {
		if (this.id === 'approve') {
			submitForApproval('A', myTable);
		} else {
			submitForApproval('R', myTable);
		}
	});

	$('#approveC, #rejectC').click(function() {
		if (this.id === 'approveC') {
			submitForApprovalChecker('A', myTable);
		} else {
			submitForApprovalChecker('R', myTable);
		}
	});

	$(document).on('keyup keypress blur change', 'input[id^="input1_"]', function() {
		var inputId = $(this).attr('id');
		validateNumberOrEmpty(inputId);
	});

	// Attach event handler for input2 (string)
	$(document).on('keyup keypress blur change', 'input[id^="input2_"]', function() {
		var inputId = $(this).attr('id');
		validateStringInput(inputId);
	});



});

function validateRrn(){
	let regEx = /^$|^\d{12}$/;
	let rrnValue = $('#rrn').val();
	if(regEx.test(rrnValue)){
		$('#errrrn').text('');
		$('#errrrn').hide();
		return true;
	}else{
		$('#errrrn').text('RRN should be Numeric of length 12');
		$('#errrrn').show();
		return false;
	}
}
function validatePan(){
	let regEx = /^$|^[0-9a-zA-Z]{12,}$/;
	let panValue = $('#pan').val();
	if(regEx.test(panValue)){
		$('#errpan').text('');
		$('#errpan').hide();
		return true;
	}else{
		$('#errpan').text('PAN should be AlphaNumeric, having length atleast 12');
		$('#errpan').show();
		return false;
	}
}
function validateNumberOrEmpty(inputId) {

	inputId = inputId.replace(/[:/ ]/g, '');
	var value = $('#' + inputId).val() || "";
	var errorDiv = $('#err' + inputId);
	if (value === "" || /^(\d+(\.\d{1,2})?)?$/.test(value)) {
		errorDiv.hide();
		errorDiv.find('.error').text(""); // Clear error message
		return true;
	} else {
		errorDiv.show();
		errorDiv.find('.error').text("Invalid input: must be empty or a number");
		return false;
	}
}

function validateStringInput(inputId) {
	inputId = inputId.replace(/[:/ ]/g, '');
	var value = $('#' + inputId).val() || "";
	var errorDiv = $('#err' + inputId);
	if (/^[a-zA-Z\s]*$/.test(value) || true) {
		errorDiv.hide();
		errorDiv.find('.error').text(""); // Clear error message
		return true;
	} else {
		errorDiv.show();
		errorDiv.find('.error').text("Invalid input: must be a string");
		return false;
	}
}
function viewTransaction(data) {
	var loc = window.location;
	var pathName = loc.pathname.substring(0,
		loc.pathname.lastIndexOf('/') + 1);
	url = '/transactionDetail';

	var txnId = data.split('&')[0];
	var funcCode = data.split('&')[1];
	var mti = data.split('&')[2];
	var originalTableName = data.split('&')[3];
	var acquirerReferenceData = data.split('&')[4];
	var caseNumber = data.split('&')[5];
	var orgFuncCode = data.split('&')[6];
	var fromDate = $('#fromDate').val();
	var toDate = $('#toDate').val();


	var schemeCode = $('#schemeCode').val();
	var funcCodeDesc = $('#funcCodeDescription').val();
	var messageDirection = $('#messageDirection').val();

	var rrn = data.split('&')[7];
	var tokenPan = data.split('&')[8];
	var actionCode = data.split('&')[9];
	var data = "txnId," + txnId + ",funcCode," + funcCode + ",mti," + mti + ",originalTableName," + originalTableName + ",acqRefData," + acquirerReferenceData + ",caseNo," + caseNumber + ",orgFuncCode," + orgFuncCode +
		",fromDate," + fromDate + ",toDate," + toDate + ",rrn," + rrn + ",tokenPan," + tokenPan + ",actionCode," + actionCode
		+ ",backToIncomingTxnScn," + 'true' + ",schemeCode," + schemeCode + ",funcCodeDesc," + funcCodeDesc + ",messageDirection," + messageDirection;
	postData(url, data);
}

function calculateMinimumDate(selectedDate) {
	const currentDateTime = new Date();
	const newDateTime = new Date(selectedDate.getTime() + 86400000 * 30);
	if (newDateTime.getTime() > currentDateTime.getTime()) {
		return currentDateTime;
	} else {
		return newDateTime;
	}
}




function validateSchemeCode() {
	var result = true;
	if (($('#schemeCode').val() == "0")) {
		$('#errSchemeCode').text('Please select scheme code');
		result = false;
	} else {
		$('#errSchemeCode').text('');
	}
	return result;
}

function validateMessageDirection() {
	var result = true;
	if (($('#messageDirection').val() == "0")) {
		$('#errMessageDirection').text('Please select message direction');
		result = false;
	} else {
		$('#errMessageDirection').text('');
	}
	return result;
}

function validateDate() {
	var result = true;
	var regxDate = /^\d{4}-\d{2}-\d{2}$/;


	if (($('#outgoingCreatedDate').val() != "")) {
		if (!regxDate.test($('#outgoingCreatedDate').val())) {
			$('#erroutgoingCreatedDate').text('Please enter the valid date');
			result = false;
		} else {
			$('#erroutgoingCreatedDate').text('');
		}

	}

	if (($('#outgoingCreatedDate').val() == "")) {

		$('#erroutgoingCreatedDate').text('Please enter  date');


	}
	return result;
}

function submitForm(url, tab, originPage) {
	var result = true;
	result = validateDate() && result;

	result = validateSchemeCode() && result;

	result = validateMessageDirection() && result;

	result = validateRrn() && result;

	result = validatePan() && result;

	if (result) {
		data = 'outgoingCreatedDate,' + $('#outgoingCreatedDate').val() + ',schemeCode,' + $('#schemeCode').val() + ',funcCodeDescription,' + $('#funcCodeDescription').val() + ',page,' + tab+ ',pan,' + $('#pan').val() + ',rrn,' + $('#rrn').val()+',searchBtnPressed,'+true;
		postData('/incomingTransactionDetails', data);
	}
}


function searchData() {
	var result = true;
	result = validateDate() && result;

	result = validateSchemeCode() && result;

	result = validateMessageDirection() && result;

	result = validateRrn() && result;

	result = validatePan() && result;

	if (result) {
		data = 'outgoingCreatedDate,' + $('#outgoingCreatedDate').val() + ',schemeCode,' + $('#schemeCode').val() + ',funcCodeDescription,' + $('#funcCodeDescription').val() + ',pan,' + $('#pan').val() + ',rrn,' + $('#rrn').val()+',searchBtnPressed,'+true;
		postData('/incomingTransactionDetails', data);
	}
}

function validateReject(rejectReason, statusVal) {
	if (statusVal == 'R' && rejectReason == '0') {
		$('#errrejectReason').text('Reject Reason field is mandatory');
		return false;

	}
	$('#errrejectReason').text('');
	return true;
}
function updateSelectAllState(myTable) {
	var filteredData = myTable.rows({ search: 'applied' }).nodes();
	var allChecked = $(filteredData).find('.selectSingle').length === $(filteredData).find('.selectSingle:checked').length;

	$('#selectAll').prop('checked', allChecked).toggleClass('allChecked', allChecked);
}



function submitForApproval(statusVal, myTable) {
	var selectedData = [];
	var checkboxes = myTable.$('.selectSingle:checked');
	var checkboxesArray = Array.from(checkboxes);
	var isValid = true;
	var rejectReason = document.getElementById("rejectReason").value;
	checkboxesArray.forEach(function(checkbox) {
		var row = checkbox.closest('tr');
		var txnId = checkbox.value;
		var input1 = dataMap["input1_" + txnId] || "";
		var input2 = dataMap["input2_" + txnId] || "";
		var status = statusVal;
		if (!validateStringInput('input2_' + txnId) || !validateNumberOrEmpty('input1_' + txnId) || !validateReject(rejectReason, statusVal))
			isValid = false;

		selectedData.push({
			stageId: txnId,
			amount: input1,
			message: input2,
			status: status,
			outgoingCreatedDate: $('#outgoingCreatedDate').val(),
			schemeCode: $('#schemeCode').val(),
			funcCodeDescription: $('#funcCodeDescription').val(),
			pan: $('#pan').val(),
			rrn: $('#rrn').val(),
			rejectReason: rejectReason

		});
	});
	if (selectedData.length != 0 && isValid) {
		var data = 'data,' + btoa(JSON.stringify(selectedData).replace(/,/g, '|'));

		postData('/approveMakerData', data);
	}
}

function submitForApprovalChecker(statusVal, myTable) {
	var selectedData = [];
	var checkboxes = myTable.$('.selectSingle:checked');
	var checkboxesArray = Array.from(checkboxes);

	checkboxesArray.forEach(function(checkbox) {
		var row = checkbox.closest('tr');
		var txnId = checkbox.value;

		var status = statusVal;
		isValid = true;
		var rejectReason = document.getElementById("rejectReason").value;
		if (!validateReject(rejectReason, statusVal))
			isValid = false;
		selectedData.push({
			stageId: txnId,
			status: status,
			outgoingCreatedDate: $('#outgoingCreatedDate').val(),
			schemeCode: $('#schemeCode').val(),
			funcCodeDescription: $('#funcCodeDescription').val(),
			pan: $('#pan').val(),
			rrn: $('#rrn').val(),
			rejectReason: rejectReason

		});
	});
	if (selectedData.length != 0 && isValid) {
		var data = 'data,' + btoa(JSON.stringify(selectedData).replace(/,/g, '|'));

		postData('/approveCheckerData', data);
	}
}



