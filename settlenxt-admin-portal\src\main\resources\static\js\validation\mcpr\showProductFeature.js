var showbuttonflag=0;
$(document).ready(function() {
	
	 $("#quater").on('keyup keypress blur change', function () {
			        validateField('quater', true, "Number", 3, false);
			    });
	$("#year").on('keyup keypress blur change', function () {
			        validateField('year', true, "Number",6,false);
			    });
	$("#binNo").on('keyup keypress blur change', function () {
			        validateField('binNo', true, "Number", 6, true);
			    });
	$("#bankName").on('keyup keypress blur change', function () {
			        validateField('bankName', true, "SelectionBox",100,false);
			    });   

    var cursorPosition = null;
    $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    if (colIdx < actionColumnIndex) {
                        $(cell).html(title + '<br><input class="search-box"   type="text" />');

                        // On every keypress in this input
                        $(
                            'input',
                            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
                        )
                            .off('keyup change')
                            .on('change', function (_e) {
                                // Get the search value
                                $(this).attr('title', $(this).val());
                                var regexr = '({search})'; 

                                cursorPosition = this.selectionStart;
                                // Search the column for that value
                                api
                                    .column(colIdx)
                                    .search(
                                        this.value != ''
                                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                                            : '',
                                        this.value != '',
                                        this.value == ''
                                    )
                                    .draw();
                            })
                            .on('click', function (e) {
                                e.stopPropagation();
                            })
                            .on('keyup', function (e) {
                                e.stopPropagation();

                                $(this).trigger('change');
                                if (cursorPosition && cursorPosition != null) {
                                    $(this)
                                        .focus()[0]
                                        .setSelectionRange(cursorPosition, cursorPosition);
                                }
                            });
                    } else {
                        $(cell).html(title + '<br> &nbsp;');
                    }
                });
            $('#tabnew_filter').hide();
            
        },
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Product Feature Fee Details',
                header: 'false',
                title: null,
                sheetName: 'Product Feature Fee Details',
                className: 'defaultexport'
                /*,
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }*/
            },
            {
                extend: 'csvHtml5',
                text: 'Export',
                filename: 'Product Feature Fee Details' ,
				header:'false', 
				title: null,
				sheetName:'Product Feature Fee Details',
				className:'defaultexport'
				/*,
				exportOptions: {
		            columns: 'th:not(:last-child)'
		         }*/
            }

            
        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true
    });

    $("#excelExport").on("click", function () {
        $(".buttons-excel").trigger("click");
    });
 
    $("#csvExport").on("click", function () {
        $(".buttons-csv").trigger("click");
    });
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });
   
 
	$("#viewBinProductFee").hide();
	$("#viewBankProductFee").hide();
	$("#viewFeatureFee").hide();
	$("#binNo").hide();
	$("#bankName").hide();
	$("#labelBinNo").hide();
	$("#labelbankName").hide();
	
	 $("#errquater").hide();
	 $("#erryear").hide();
	 $("#errbinNo").hide();
	 $("#errbankName").hide();
	$("#viewBinProductFee").hide();
	$("#viewBankProductFee").hide();
	$("#viewFeatureFee").hide();
	 showbuttonflag=0;
	 loaddata();
});

function loaddata()
{
	var hquater= $("#hquater").val();
	var hyear= $("#hyear").val();
	var hbinNo= $("#hbinNo").val();
	var hbankName= $("#hbankName").val();
	var htype= $("#htype").val();
	const ids = ["quater","year","bankName"];
	const value = [hquater,hyear,hbankName];
	if (htype!="")
	{
		var i;
		var j;
		showControl(htype);
		for(j=0; j<ids.length; j++){
	    var selectObj = document.getElementById(ids[j]);
     	for (i = 0; i < selectObj.options.length; i++) {
        if (selectObj.options[i].value== value[j]) {
            selectObj.options[i].selected = true;
            break;
        	}
        }
        }
        
	    
		document.getElementById("binNo").value = hbinNo;
	   	showbuttonflag=1;
		$("#viewBinProductFee").show();
		$("#viewBankProductFee").show();
		$("#viewFeatureFee").show();
	}
}

function resetAction() {
	showbuttonflag=0;
	document.getElementById("quater").options[0].selected = true;
	document.getElementById("year").options[0].selected = true;
	$("#errquater").find('.error').html('');
	$("#erryear").find('.error').html('');
	$("#errbinNo").find('.error').html('');
	$("#errbankName").find('.error').html('');
	$("#viewBinProductFee").hide();
	$("#viewBankProductFee").hide();
	$("#viewFeatureFee").hide();
	$("#binNo").hide();
	$("#bankName").hide();
	$("#labelBinNo").hide();
	$("#labelbankName").hide();
	
	$("#binNo").val("");
	$("#bankName").val("SELECT");
	
	$("#hideTable").hide();
	
	
	$('#jqueryError').hide();
	}

function showAllButton()
{
    var quaterValue = $("#quater").val();
    var yearValue = $("#year").val();
    if(showbuttonflag==0 && (quaterValue != "SELECT" && yearValue != "SELECT"))
    {
    	showbuttonflag=1;
		$("#viewBinProductFee").show();
		$("#viewBankProductFee").show();
		$("#viewFeatureFee").show();
    }
}


function showControl(type){
	
	document.getElementById('showControl').value=type;
	document.getElementById('binNo').value="";
	document.getElementById("bankName").options[0].selected = true;
	 $("#errbinNo").hide();
	 $("#errbankName").hide();
	
	if(type=="vbpf")
	{
		$("#labelBinNo").show();
		$("#binNo").show();
		$("#labelbankName").hide();
		$("#bankName").hide();
	}
	else if(type=="vbkpf")
	{
		$("#labelBinNo").hide();
		$("#binNo").hide();
		$("#labelbankName").show();
		$("#bankName").show();
	}
	else if(type=="vff")
	{
		$("#binNo").show();
		$("#bankName").show();
		$("#labelBinNo").show();
		$("#labelbankName").show();
	}
}



function viewData(url) {
	
	var type = document.getElementById('showControl').value;
	
	var isValid = true;

    if (!validateField('quater', true, "Number", 3, false) && isValid) {
        isValid = false;
    }
    if (!validateField('year', true, "Number",6,false) && isValid) {
        isValid = false;
    }

if(isValid){
	isValid = validateBinAndBankName(type, isValid);
	}

    if(isValid){
    	
		var month = $("#quater").val();
		var quater = month.toString();
		var year = $("#year").val();
		var bankName = $("#bankName").val();
		var binNo = $("#binNo").val();
		 url = "/productFeatureFeeViewSearch";
		if(type=="vbkpf")
		{
			binNo="0";
		}
		var data = "type," + type 
		 + ",quater," + quater + ",year," + year + ",bankName," + bankName + ",binNo," + binNo;
		
		
		postData(url, data);
	}
}




function validateBinAndBankName(type, isValid) {
    if (type == "vbpf") {
        if (!validateField('binNo', true, "Number", 6, true) && isValid) {
            isValid = false;
        }
    }
    else if (type == "vbkpf") {
        if (!validateField('bankName', true, "SelectionBox", 100, false) && isValid) {
            isValid = false;
        }
    }
    else if (type == "vff") {
        if (!validateField('bankName', true, "SelectionBox", 100, false) && isValid) {
            isValid = false;
        }
        if (!validateField('binNo', true, "Number", 6, true) && isValid) {
            isValid = false;
        }
    }
    return isValid;
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength) {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if (isMandatory && fieldValue.trim() == "") {
        isValid = false;
    }
    if ((isMandatory && fieldValue.trim() == "" && (fieldType!="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    var regEx;
    ({ regEx, isValid } = validateAlphabet(fieldType, regEx, fieldValue, isValid));
    if (fieldType == "AlphanumericNoSpace") {
         regEx = /^[A-Za-z0-9]+$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
    hideAndShowErrorMsg(isValid, fieldId);
    return isValid;
}

function validateAlphabet(fieldType, regEx, fieldValue, isValid) {
    if (fieldType == "Alphabet") {
        regEx = /^[A-Z]+$/i;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "AlphabetWithSpace") {
        regEx = /^[A-Z ]+$/i;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    return { regEx, isValid };
}

function hideAndShowErrorMsg(isValid, fieldId) {
    if (isValid) {
        $("#err" + fieldId).hide();
    }
    else {
        if (productFeatureValidationMessages[fieldId]) {
            $("#err" + fieldId).find('.error').html(productFeatureValidationMessages[fieldId]);
        }
        $("#err" + fieldId).show();
    }
}

function checkAlreadyPresent()
{
	
		var bankName = $("#bankName").val();
		var binNo = $("#binNo").val();
	
	if(bankName !="SELECT" && binNo!="")
	{
	
		var validvRoleName=false;
		var msUrl = "checkBinBank";
	
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		$.ajax({
			url: msUrl,
			type: "POST",
			dataType: "json",
			headers: {
			'_TransactToken': tokenValue
			},
			
			data: {
				"bankName": bankName,
				"binNo": binNo
			},
			success: function(response) {
			
				if (response.status == "BSUC_0001") {
					validvRoleName = true;
					var url ="/productFeatureFeeViewSearch";
					viewData(url);
							
				} else {
					validvRoleName = false;
					
					$('#errorStatus').html(binNo + " does not belongs to " +bankName);
				$('#jqueryError').show();
					$('#jquerySuccess').hide();
				}
			},
			
		});

	return validvRoleName;
	}	
	else{
	var url1 ="/productFeatureFeeViewSearch";
					viewData(url1);
	}
}

