<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script src="./static/js/validation/viewApproveDisputeFeeRule.js"
	type="text/javascript"></script>
	
<div class="space_block">
<div class="container-fluid height-min">
	
	<div class="alert alert-danger appRejMust" role="alert"><spring:message code="budget.apprejecterrormsg" /></div>
	<div class="alert alert-danger remarkMust" role="alert"><spring:message code="budget.remarkserror" /></div>
	<c:url value="approveDisputeFeeRule" var="approveDisputeFeeRule" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveDisputeFeeRule" modelAttribute="disputeFeeRuleDto"
		action="${approveDisputeFeeRule}" autocomplete="off">


		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"></span><spring:message code="disputeFeeRule.mainTab.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="seqId" value="${disputeFeeRuleDto.seqId}">
						
						<input type="hidden" id="crtuser"
							value="${disputeFeeRuleDto.lastUpdatedBy}" />

	
						<table class="table table-striped "
							style="font-size: 12px">
							<caption style="display:none;">View Approve Dispute Fee Rule</caption> 
							<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
										<td colspan="6">
											<div class="panel-heading-red clearfix">
												<strong><span class="glyphicon glyphicon-info-sign"></span> <span
													data-i18n="Data"></span>
												<spring:message code="disputeFeeRule.viewscreen.title" /></span></strong>
											</div>
										</td>
									</tr>
								<tr>

								</tr>

								<tr>
									<td><label><spring:message code="disputeFeeRules.actionCode" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.actionCode}">N/A</c:if>${disputeFeeRuleDto.actionCode}</td>
									<td><label><spring:message code="disputeFeeRules.feeType" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.feeType}">N/A</c:if>${disputeFeeRuleDto.feeType}</td>
									<td><label><spring:message code="disputeFeeRules.priority" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.priority}">N/A</c:if>${disputeFeeRuleDto.priority}</td>		
									
								</tr>
								<tr>
									<td><label><spring:message code="disputeFeeRules.feeCode" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.feeCode}">N/A</c:if>${disputeFeeRuleDto.feeCode}</td>
									<td><label><spring:message code="disputeFeeRules.logicalFeeCode" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.logicalFeeCode}">N/A</c:if>${disputeFeeRuleDto.logicalFeeCode}</td>
									<td><label><spring:message code="disputeFeeRules.fieldName" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.fieldName}">N/A</c:if>${disputeFeeRuleDto.fieldName}</td>
								</tr>
								<tr>
									<td><label><spring:message code="disputeFeeRules.relationalOperator" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.relationalOperator}">N/A</c:if>${disputeFeeRuleDto.relationalOperator}</td>
									<td><label><spring:message code="disputeFeeRules.fieldValue" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.fieldValue}">N/A</c:if>${disputeFeeRuleDto.fieldValue}</td>
									<td><label><spring:message code="disputeFeeRules.fieldOperator" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.fieldOperator}">N/A</c:if>${disputeFeeRuleDto.fieldOperator}</td>
								</tr>
								<tr>
									<td><label><spring:message code="disputeFeeRules.createdBy" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.createdBy}">N/A</c:if>${disputeFeeRuleDto.createdBy}</td>
									<td><label><spring:message code="disputeFeeRules.createdOn" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.createdOn}">N/A</c:if>${disputeFeeRuleDto.createdOn}</td>
									<td><label><spring:message code="disputeFeeRules.checkerComments" /><span style="color: red"></span></label></td>
									<td><c:if test="${empty disputeFeeRuleDto.checkerComments}">N/A</c:if>${disputeFeeRuleDto.checkerComments}</td>
								</tr>
								
									
								<sec:authorize access="hasAuthority('Approve Dispute Fee Rule')">
									<c:if test="${disputeFeeRuleDto.requestState eq 'P'}">
										<tr>
											<td colspan="6"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"><spring:message
																code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
										</tr>
										

										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="AM.lbl.reject" /></option>
											</select></td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											<!-- //Added by deepak on 31-03-2016 -->
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										</tr>
									</c:if>
							</sec:authorize>	
								
							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Dispute Fee Rule')">
										<c:if test="${disputeFeeRuleDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approveRole" value="Submit"
												onclick="postAction('/approveDisputeFeeRule');" />
										</c:if>
								</sec:authorize>
									
									<c:choose>
										<c:when test="${editDisputeFeeRule eq 'Yes'}">
											<button type="button" class="btn btn-danger"
										onclick="backAction('P','/showDisputeFeeRule');"><spring:message code="budget.backBtn" /></button>
										</c:when>
										<c:otherwise>
											<button type="button" class="btn btn-danger"
										onclick="backAction('P','/disputeFeeRulePendingForApproval');"><spring:message code="budget.backBtn" /></button>
										</c:otherwise>
									</c:choose>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>
</div>
