package org.npci.settlenxt.adminportal.controllers;

import java.util.Date;
import java.util.List;
import java.util.Locale;

import javax.servlet.http.HttpServletRequest;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SysParamsDTO;
import org.npci.settlenxt.adminportal.service.SysParamService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.BaseSessionDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

@Controller
public class SysParamController extends BaseController {
	@Autowired
	SysParamService sysParamService;

	@Autowired
	private BaseSessionDTO sessionDTO;
	
	@Autowired
	private MessageSource messageSource;
	
	private static final String SHOW_SYS_PARAMS = "showSysParams";
	private static final String SYS_PARAMS_LIST = "sysParamList";
	private static final String SHOW_MAIN_TAB = "showMainTab";
	private static final String VIEW_APPROVE_SYS_PARAM = "viewApproveSysParam";
	private static final String VIEW_SYS_PARAM = "viewSysParam";

	private static final String SYS_PARAMS_DTO = "sysParamsDTO";

	public static final String SYS_PARAMS_APPROVAL_LIST = "sysParamApprovalList";
	public static final String SYS_PARAMS_PENDING_LIST = "sysParamPendingList";
	public static final String SYS_PARAMS_LIST_TYPE = "sysParamListType";
	private static final Object DISCARD_SYS_PARAM = "discardSysParam";
	private static final Object EDIT_SYS_PARAM = "editSysParam";
	public static final String SHOW_ADD_BUTTON = "showAddButton";
	public static final String ADD_EDIT_SYS_PARAM = "addEditSysParam";
	public static final String PREVIOUS_URL = "previousUrl";

	public static final String MAIN_TAB_URL = "/showSysParamsMain";
	public static final String APPROVAL_TAB_URL = "/showSysParamsApproval";
	public static final String ONLY_VIEW = "onlyView";
	public static final String ADD_SUCCESS_MSG = "Sys Param Detail added successfully and sent for Approval";
	public static final String EDIT_SUCCESS_MSG = "Sys Param Detail Updated successfully and sent for Approval";
	public static final String DISCARD_SUCCESS_MSG = "Sys Param Detail Discarded successfully";


	private static final String SHOW_CHECKBOX = "showCheckBox";
	private static final String APPROVED_SUCCESS_MSG = "Sys Param Approved Successfully!!";
	private static final String REJECTED_SUCCESS_MSG = "Sys Param Rejected Successfully!!";

	@PostMapping("/showSysParamsMain")
	@PreAuthorize("hasAuthority('View Sys Params')")
	public String fetchSysParamsApproved(Model model) {
		List<SysParamsDTO> sysParamList = sysParamService.getSysParamApprovedList();
		model.addAttribute(SYS_PARAMS_LIST, sysParamList);
		model.addAttribute(SHOW_MAIN_TAB, CommonConstants.YES_FLAG);
		return getView(model, SHOW_SYS_PARAMS);
	}

	@PostMapping("/showSysParamsApproval")
	@PreAuthorize("hasAuthority('View Sys Params')")
	public String fetchSysParamsPending(Model model) {
		List<SysParamsDTO> sysParamList = sysParamService.getSysParamPendingList();
		model.addAttribute(SYS_PARAMS_LIST, sysParamList);
		model.addAttribute(SHOW_MAIN_TAB, BaseCommonConstants.NO_FLAG);
		return getView(model, SHOW_SYS_PARAMS);
	}

	@PostMapping("/viewSysParams")
	@PreAuthorize("hasAuthority('View Sys Params')")
	public String viewSysParam(Model model, @RequestParam("sysType") String sysType,
			@RequestParam("sysKey") String sysKey) {
		SysParamsDTO sysParamsDTO = sysParamService.getSysParamsInfoFromMain(sysType, sysKey);
		model.addAttribute(SYS_PARAMS_DTO, sysParamsDTO);
		return getView(model, VIEW_SYS_PARAM);

	}

	// view ForexRate in Approval tab
	@PostMapping("/viewApproveSysParam")
	@PreAuthorize("hasAuthority('View Sys Params')")
	public String getPendingSysParam(@RequestParam("sysType") String sysType, @RequestParam("sysKey") String sysKey,
			Model model, HttpServletRequest request) {
		SysParamsDTO sysParamsDTO = new SysParamsDTO();
		try {
			sysParamsDTO = sysParamService.getSysParamStgInfo(sysType, sysKey);
			model.addAttribute(SYS_PARAMS_DTO, sysParamsDTO);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_SYS_PARAM, ex);
		}
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, DISCARD_SYS_PARAM);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, EDIT_SYS_PARAM);
		return getView(model, VIEW_APPROVE_SYS_PARAM);
	}

// For Checker Approval
	@PostMapping("/approveSysParams")
	@PreAuthorize("hasAuthority('Approve Sys Params')")
	public String approveSysParam(@RequestParam("sysType") String sysType, @RequestParam("sysKey") String sysKey,
			@RequestParam("status") String status, @RequestParam("remarks") String remarks, Model model) {


		SysParamsDTO sysParamsDTO = new SysParamsDTO();
		try {
			sysParamsDTO = sysParamService.approveOrRejectSysParam(sysType, sysKey, status, remarks);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_SYS_PARAM, ex);
		}
		model.addAttribute(SYS_PARAMS_DTO, sysParamsDTO);

		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, APPROVED_SUCCESS_MSG);
		} else if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, REJECTED_SUCCESS_MSG);
		}
		return getView(model, VIEW_APPROVE_SYS_PARAM);
	}

	@PostMapping("/addSysParam")
	@PreAuthorize("hasAuthority('Add Sys Params')")
	public String addSysParam(Model model) {
		SysParamsDTO sysParamDTO = new SysParamsDTO();
		try {
			List<SysParamsDTO> sysParamListType = sysParamService.getSysParamListType();
			model.addAttribute(SYS_PARAMS_LIST_TYPE, sysParamListType);
			model.addAttribute(SYS_PARAMS_DTO, sysParamDTO);
			model.addAttribute(PREVIOUS_URL, MAIN_TAB_URL);
			model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		} catch (Exception ex) {
			model.addAttribute(SYS_PARAMS_DTO, sysParamDTO);
			model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_EDIT_SYS_PARAM, ex);
		}
		return getView(model, ADD_EDIT_SYS_PARAM);
	}

	@PostMapping("/saveSysParam")
	@PreAuthorize("hasAuthority('Add Sys Params')")
	public String saveSysParam(@ModelAttribute SysParamsDTO sysParamsDTO, Model model) {
		try {
			sysParamsDTO.setCreatedOn(new Date());
			sysParamsDTO.setCreatedBy(sessionDTO.getUserName());
			sysParamsDTO.setRequestState(CommonConstants.REQUEST_STATE_PENDING);
			sysParamsDTO.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
			sysParamService.addSysParam(sysParamsDTO);
		} catch (Exception ex) {
			model.addAttribute(SYS_PARAMS_DTO, sysParamsDTO);
			model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_EDIT_SYS_PARAM, ex);
		}
		model.addAttribute(PREVIOUS_URL, APPROVAL_TAB_URL);
		model.addAttribute(SYS_PARAMS_DTO, sysParamsDTO);
		model.addAttribute(ONLY_VIEW, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, ADD_SUCCESS_MSG);
		return getView(model, ADD_EDIT_SYS_PARAM);
	}

	@PostMapping("/editSysParam")
	@PreAuthorize("hasAuthority('Edit Sys Params')")
	public String editSysParam(@RequestParam("sysType") String sysType, @RequestParam("sysKey") String sysKey,
			Model model, HttpServletRequest request) {
		SysParamsDTO sysParamsDTO = new SysParamsDTO();
		try {
			sysParamsDTO = sysParamService.getSysParamStgInfo(sysType, sysKey);
			List<SysParamsDTO> sysParamListType = sysParamService.getSysParamListType();
			model.addAttribute(SYS_PARAMS_LIST_TYPE, sysParamListType);
			model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
			model.addAttribute(PREVIOUS_URL, MAIN_TAB_URL);
		} catch (Exception ex) {
			model.addAttribute(SYS_PARAMS_DTO, sysParamsDTO);
			model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_EDIT_SYS_PARAM, ex);
		}
		model.addAttribute(SYS_PARAMS_DTO, sysParamsDTO);
		return getView(model, ADD_EDIT_SYS_PARAM);
	}

	@PostMapping("/updateSysParam")
	@PreAuthorize("hasAuthority('Edit Sys Params')")
	public String updateSysParam(@ModelAttribute SysParamsDTO sysParamDTO, Model model) {
		try {
			sysParamDTO.setLastUpdatedOn(new Date());
			sysParamDTO.setLastUpdatedBy(sessionDTO.getUserName());
			sysParamDTO.setRequestState(CommonConstants.REQUEST_STATE_PENDING);
			sysParamDTO.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
			sysParamService.updateSysParam(sysParamDTO);
		} catch (Exception ex) {
			model.addAttribute(SYS_PARAMS_DTO, sysParamDTO);
			model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_EDIT_SYS_PARAM, ex);
		}
		model.addAttribute(SYS_PARAMS_DTO, sysParamDTO);
		model.addAttribute(ONLY_VIEW, CommonConstants.YES_FLAG);
		model.addAttribute(PREVIOUS_URL, APPROVAL_TAB_URL);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, EDIT_SUCCESS_MSG);
		return getView(model, ADD_EDIT_SYS_PARAM);
	}

	@PostMapping("/checkDupSysDetails")
	@PreAuthorize("hasAuthority('View Fees')")
	public ResponseEntity<Object> checkDupLookUpDetails(@RequestParam("sysType") String sysType,
			@RequestParam("sysKey") String sysKey) {

		boolean result = sysParamService.checkDupSysDetails(sysType, sysKey);

		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty("status", CommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty("status", CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/discardSysParam")
	@PreAuthorize("hasAuthority('Edit Sys Params')")
	public String discardSysParam(@RequestParam("sysType") String sysType, @RequestParam("sysKey") String sysKey,
			Model model, HttpServletRequest request) {
		SysParamsDTO sysParamsDTO = new SysParamsDTO();
		try {
			sysParamsDTO = sysParamService.discardSysParamInfo(sysType, sysKey);
			model.addAttribute(SYS_PARAMS_DTO, sysParamsDTO);
			model.addAttribute(PREVIOUS_URL, MAIN_TAB_URL);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_SYS_PARAM, ex);
		}
		model.addAttribute(SYS_PARAMS_DTO, sysParamsDTO);
		model.addAttribute(ONLY_VIEW, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, DISCARD_SUCCESS_MSG);
		return getView(model, ADD_EDIT_SYS_PARAM);
	}
	@PostMapping("/approveOrRejectBulkSysParam")
	@PreAuthorize("hasAuthority('Approve Sys Params')")
	public String approveSysParam(@RequestParam("bulkSysParamList") String bulkSysParamList,
			@RequestParam("status") String status, Model model) {
		try {

			String remarks = "";
			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}
		
				String sysParamIdList = bulkSysParamList;
				String[] idArray = sysParamIdList.split("\\|");
				SysParamsDTO sysParamDTO = sysParamService.updateApproveOrRejectSysParamBulk(idArray, status, remarks);
				checkSysParamApproveStatus(sysParamDTO, model);
		

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_SYS_PARAMS, ex);
		}

		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);

		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}

		List<SysParamsDTO> sysParamList = sysParamService.getSysParamPendingList();
		model.addAttribute(SYS_PARAMS_LIST, sysParamList);
		model.addAttribute(SHOW_MAIN_TAB, BaseCommonConstants.NO_FLAG);
		return getView(model, SHOW_SYS_PARAMS);

	}

	private void checkSysParamApproveStatus(SysParamsDTO sysParamDTO, Model model) {
		Locale locale=new Locale(null);
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(sysParamDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_SysParamApproved", null,locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_SysParamRejected", null, locale));
		}
	}	
	
	

}
