package org.npci.settlenxt.adminportal.repository;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.adminportal.dto.SysParamsDTO;

@Mapper
public interface SysParamRepository {

	List<SysParamsDTO> getSysParamApprovedList(@Param("requestState")String requestState);

	List<SysParamsDTO> getSysParamPendingList(@Param("requestStateList")List<String> requestStateList);

	SysParamsDTO getSysParamsInfoFromMain(@Param("sysType")String sysType,@Param("sysKey") String sysKey);

	SysParamsDTO getSysParamStgInfoBy(@Param("sysType")String sysType,@Param("sysKey") String sysKey);

	void updateSysParamRequestState(@Param("sysParamsDto")SysParamsDTO sysParamsDto);

	void insertSysParamMain(@Param("sysParamsDto")SysParamsDTO sysParamsDto);

	void updateSysParamMain(@Param("sysParamsDto")SysParamsDTO sysParamsDto);

	int checkDuplicateinStg(@Param("sysType")String sysType,@Param("sysKey") String sysKey);

	void addSysParam(@Param("sysParamsDto")SysParamsDTO sysParamDTO);

	List<SysParamsDTO> getSysParamListType();

	void updateStgSysParam(@Param("sysParamsDto")SysParamsDTO sysParamDTO);

	void deleteDiscardedEntry(@Param("sysType")String sysType,@Param("sysKey") String sysKey);
	
	List<SysParamsDTO> getSysParamStgInfoList(@Param("sysParamlist") List<String> sysParamlist);

	void updateSysParamStgState(@Param("sysParamsDto")SysParamsDTO sysParamDTO);
}
