<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/addEditCardConfig.js"
	type="text/javascript"></script>
<script type="text/javascript">
	var cardConfigValidationMessages = {};
	cardConfigValidationMessages['cardType'] = "<spring:message code='cardConfig.cardType.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['cardVariant'] = "<spring:message code='cardConfig.cardVariant.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['baseFee'] = "<spring:message code='cardConfig.baseFee.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['fromYear'] = "<spring:message code='cardConfig.fromYear.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['fromMonth'] = "<spring:message code='cardConfig.fromMonth.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['toYear'] = "<spring:message code='cardConfig.toYear.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['toMonth'] = "<spring:message code='cardConfig.toMonth.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['fromToDateValidation'] = "<spring:message code='cardConfig.fromToDateValidation.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['toFromDateValidation'] = "<spring:message code='cardConfig.toFromDateValidation.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['fromToSysDateValidation'] = "<spring:message code='cardConfig.fromToSysDateValidation.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['pastYearValidation'] = "<spring:message code='cardConfig.pastYearValidation.validation.msg' javaScriptEscape='true' />";
	cardConfigValidationMessages['pastMonthValidation'] = "<spring:message code='cardConfig.pastMonthValidation.validation.msg' javaScriptEscape='true' />";
</script>

<div class="panel panel-default no_margin">
	<div class="panel-heading clearfix">
		<strong><span class="glyphicon glyphicon-th"></span> <span
			data-i18n="Data"> <c:if test="${not empty addCard}">
					<spring:message code="cardConfig.addscreen.title" />
				</c:if> <c:if test="${not empty editCard}">
					<spring:message code="cardConfig.editscreen.title" />
				</c:if>
		</span> </strong>
	</div>
	<c:if test="${not empty addCard}">
		<c:url value="addCard" var="submitCardDetails" />
	</c:if>
	<c:if test="${not empty editCard}">
		<c:url value="updateCard" var="submitCardDetails" />
	</c:if>
	<div class="panel-body">
		<form:form onsubmit="return encodeForm(this);" method="POST"
			id="addEditCardConfig" modelAttribute="cardDto"
			action="${submitCardDetails}" autocomplete="off">
			<br />
			<form:hidden path="cardConfigId" id="cardConfigId"
				name="cardConfigId" value="${cardDto.cardConfigId}" />
			<input type="hidden" id="cardId" value="${cardDto.cardConfigId}" />
			<input type="hidden" id="hparentPage" value="${parentPage}" />
			<c:if test="${not empty showbutton}">

				<div class="row">
					<div class="col-sm-12">
						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="cardConfig.cardType" /><span
									style="color: red">*</span></label>
								<c:if test="${not empty addCard}">
									<form:select path="cardType" id="cardType" name="cardType"
										maxlength="10" value="${cardDto.cardType}"
										cssClass="form-control medantory">
										<form:option value="" label="SELECT" />
										<form:options items="${cardTypeList}" itemValue="code"
											itemLabel="description" />
									</form:select>

									<div id="errcardType">
										<span for="cardType" class="error"><form:errors
												path="cardType" /> </span>
									</div>
								</c:if>
								<c:if test="${not empty editCard}">
									<form:hidden path="cardType" id="cardType" name="cardType"
										value="${cardDto.cardType}" />

									<form:input path="cardTypeName" id="cardTypeName"
										name="cardTypeName" maxlength="10"
										value="${cardDto.cardTypeName}" readonly="true"
										cssClass="form-control medantory" />
								</c:if>
							</div>
						</div>

						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="cardConfig.cardVariant" /><span
									style="color: red">*</span></label>
								<c:if test="${not empty addCard}">
									<form:select path="cardVariant" id="cardVariant"
										name="cardVariant" maxlength="10"
										value="${cardDto.cardVariant}"
										cssClass="form-control medantory">
										<form:option value="" label="SELECT" />
										<form:options items="${cardVariantList}" itemValue="code"
											itemLabel="description" />
									</form:select>

									<div id="errcardVariant">
										<span for="cardVariant" class="error"><form:errors
												path="cardVariant" /> </span>
									</div>
								</c:if>
								<c:if test="${not empty editCard}">
									<form:hidden path="cardVariant" id="cardVariant"
										name="cardVariant" value="${cardDto.cardVariant}" />

									<form:input path="cardVariantName" id="cardVariantName"
										value="${cardDto.cardVariantName}" name="cardVariantName"
										readonly="true" maxlength="10"
										cssClass="form-control medantory" />
								</c:if>
							</div>
						</div>

						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="cardConfig.baseFee" /><span
									style="color: red">*</span></label>
								<form:input path="baseFee" id="baseFee" name="baseFee"
									cssClass="form-control medantory" value="${cardDto.baseFee}" />
								<div id="errbaseFee">
									<span for="baseFee" class="error"><form:errors
											path="baseFee" /> </span>
								</div>
							</div>
						</div>
						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="cardConfig.fromDate" /><span
									style="color: red">*</span></label>
								<form:select path="fromMonth" id="fromMonth" name="fromMonth"
									maxlength="10" value="${cardDto.fromMonth}"
									cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${beginingQuarterMonths}" itemValue="code"
										itemLabel="description" />
								</form:select>
								<div id="errfromMonth">
									<span for="fromMonth" class="error"><form:errors
											path="fromMonth" /> </span>
								</div>
							</div>
						</div>
					</div>
				</div>


				<div class="row">
					<div class="col-sm-12">


						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="cardConfig.year" /><span
									style="color: red">*</span></label>
								<form:select path="fromYear" id="fromYear" name="fromYear"
									maxlength="10" value="${cardDto.fromYear}"
									cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${baseFeeYears}" itemValue="code"
										itemLabel="description" />
								</form:select>
								<div id="errfromYear">
									<span for="fromYear" class="error"><form:errors
											path="fromYear" /> </span>
								</div>
							</div>
						</div>

						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="cardConfig.toDate" /><span
									style="color: red">*</span></label>
								<form:select path="toMonth" id="toMonth" name="toMonth"
									maxlength="10" value="${cardDto.toMonth}"
									cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${endingQuarterMonths}" itemValue="code"
										itemLabel="description" />
								</form:select>
								<div id="errtoMonth">
									<span for="toMonth" class="error"><form:errors
											path="toMonth" /> </span>
								</div>
							</div>
						</div>
						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="cardConfig.year" /><span
									style="color: red">*</span></label>
								<form:select path="toYear" id="toYear" name="toYear"
									maxlength="10" value="${cardDto.toYear}"
									cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${baseFeeYears}" itemValue="code"
										itemLabel="description" />
								</form:select>
								<div id="errtoYear">
									<span for="toYear" class="error"><form:errors
											path="toYear" /> </span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>
			<c:if test="${empty showbutton}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default no_margin">
							<div class="panel-body">
								<table class="table table-striped" style="font-size: 12px">
									<caption style="display: none;">Base Fee</caption>
									<thead style="display: none;">
										<th scope="col"></th>
									</thead>
									<tbody>
										<tr>
											<td><label><spring:message
														code="cardConfig.cardType" /></label></td>
											<td id="cardType">${cardDto.cardTypeName }</td>
											<td><label><spring:message
														code="cardConfig.cardVariant" /></label></td>
											<td id="cardVariant">${cardDto.cardVariantName }</td>
											<td><label><spring:message
														code="cardConfig.baseFee" /></label></td>
											<td id="baseFee">${cardDto.baseFee }</td>
										</tr>
										<tr>

											<td><label><spring:message
														code="cardConfig.fromDate" /></label></td>
											<td id="fromDate">${cardDto.fromDate }</td>
											<td><label><spring:message
														code="cardConfig.toDate" /></label></td>
											<td id="toDate">${cardDto.toDate }</td>
											<td></td>
											<td></td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</c:if>

			<div class="col-sm-12 bottom_space">
				<div style="text-align: center">
					<c:if test="${not empty addCard}">
						<c:if test="${not empty showbutton}">
							<button type="button" value="Submit" class="btn btn-success"
								onclick="resetAction();">
								<spring:message code="am.lbl.reset" />
							</button>
						</c:if>
						<c:if test="${not empty showbutton}">
							<input type="button" class="btn btn-success"
								onclick="viewCardAdd('/addCard','A')" id="submitButton"
								value="<spring:message code="cardConfig.submitBtn" />" />
						</c:if>
					</c:if>
					<sec:authorize access="hasAuthority('Edit Base Fee')">
						<c:if test="${not empty editCard}">
							<c:if test="${not empty showbutton}">
								<input type="button" class="btn btn-success" id="bEdit"
									onclick="viewCardAdd('/updateCardConfig','E')"
									id="submitButton"
									value="<spring:message code="cardConfig.submitBtn" />" />
							</c:if>
						</c:if>
						<c:if
							test="${cardDto.requestState  eq 'R' and not empty showbutton}">
							<input name="discardButton" type="button" class="btn btn-danger"
								id="approveRole" value="Discard"
								onclick="discard('/discardCardConfig','${cardDto.cardConfigId}');" />

							<button type="button" class="btn btn-danger"
								onclick="userAction('N','/cardConfigPendingForApproval');">
								<spring:message code="cardConfig.backBtn" />
							</button>

						</c:if>
					</sec:authorize>
					<c:if test="${cardDto.requestState  ne 'R'}">
						<c:if test="${parentPage  eq 'approvalTab'}">
							<button type="button" class="btn btn-danger"
								onclick="userAction('N','/cardConfigPendingForApproval');">
								<spring:message code="currencyMaster.backBtn" />
							</button>
						</c:if>
						<c:if test="${parentPage ne 'approvalTab'}">
							<button type="button" class="btn btn-danger"
								onclick="userAction('N','/baseFeeConfiguration');">
								<spring:message code="cardConfig.backBtn" />
							</button>
						</c:if>

					</c:if>
				</div>
			</div>
		</form:form>
	</div>
</div>
