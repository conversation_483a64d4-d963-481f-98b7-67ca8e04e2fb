<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

	<script src="./static/js/validation/mcpr/viewApproveBinFeatureMapping.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert"><spring:message code="binFeatureMapping.apprejecterrormsg" /></div>
	<div class="alert alert-danger remarkMust" role="alert"><spring:message code="binFeatureMapping.remarkserror" /></div>
	<c:url value="approveBinExclConfig" var="approveBinExclConfig" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveBinExclConfig" modelAttribute="binExclDTO"
		action="${approveBinExclConfig}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="binFeatureMapping.approvalPendingViewScreen.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="binFeatureId" value="${binFeatureMappingDTO.binFeatureId}">

						<input type="hidden" id="crtuser"
							value="${binFeatureMappingDTO.lastUpdatedBy}" />

						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">Bin Feature Mapping</caption>
									<thead style="display:none;"><th scope = "col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message code="binFeatureMapping.requestInfo" /></span></strong>
										</div></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>
								<tr>


									<td><label><spring:message code="binFeatureMapping.requestType" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.lastOperation}</td>
									<td><label><spring:message code="binFeatureMapping.requestDate" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.lastUpdatedOn}</td>
									<td><label><spring:message code="binFeatureMapping.requestStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${binFeatureMappingDTO.requestState=='A' }">Approved</c:if></td>
									<td><c:if test="${binFeatureMappingDTO.requestState=='P' }">Pending for Approval</c:if></td>
									<td><c:if test="${binFeatureMappingDTO.requestState=='R' }">Rejected</c:if></td>
									<td><c:if test="${binFeatureMappingDTO.requestState=='D' }">Discarded</c:if></td>
									<td></td>
									<td></td>
									<td></td> 
									<td></td>

								</tr>
								<tr>
									<td><label><spring:message code="binFeatureMapping.requestBy" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.lastUpdatedBy}</td>

									<td><label><spring:message code="binFeatureMapping.approverComments" /><span
											style="color: red"></span></label></td>
									<td colspan=2>${binFeatureMappingDTO.checkerComments}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>

								<tr>
									<td colspan="6">
										<div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message code="binFeatureMapping.viewscreen.title" /></span></strong>
										</div>
									</td>

									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>


								<tr>
									<td><label><spring:message code="binFeatureMapping.binFeatureId" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.featureId}</td>
									<td><label><spring:message code="binFeatureMapping.participantName" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.bankName}</td>
									
									<td><label><spring:message code="binFeatureMapping.bin" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.bin}</td>
								</tr>
								<tr>	
									
									<td><label><spring:message code="binFeatureMapping.baseorfeature" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.featureName}</td>
									<td><label><span
											style="color: red"></span></label></td>
									<td><label><spring:message code="binFeatureMapping.fromDate" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.fromDate}</td>
									<td><label><spring:message code="binFeatureMapping.toDate" /><span style="color: red"></span></label></td>
									<td>${binFeatureMappingDTO.toDate}</td>
									<td></td>
									<td></td>
									<td></td>
								</tr>

								
								
								<sec:authorize access="hasAuthority('Approve Bin Feature Mapping')">
									<c:if test="${binFeatureMappingDTO.requestState eq 'P'}">
										<tr>
											<td colspan="6"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"><spring:message
																code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
										</tr>
										

										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="AM.lbl.reject" /></option>
											</select></td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										</tr>
									</c:if>
							</sec:authorize>	

							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
								<sec:authorize access="hasAuthority('Approve Bin Feature Mapping')">
									<c:if test="${binFeatureMappingDTO.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approveRole" value="Submit"
												onclick="postAction('/approveBinFeatureMapping');" />
										</c:if>
									</sec:authorize>
													
									<sec:authorize access="hasAuthority('Edit Bin Feature Mapping')">				
									<c:if test="${binFeatureMappingDTO.requestState  eq 'R' and not empty showbutton}">
										<c:if test="${binFeatureMappingDTO.lastOperation  ne 'Delete Bin Feature Mapping' }">
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="javascript:viewBinFeatureMapping('${binFeatureMappingDTO.binFeatureId}','V','approvalTab');"/>						
									</c:if>
									
									<button type="button" class="btn btn-danger" 
										onclick="postDiscardBinAction('/discardRejectedBinFeatureMapping');">
										<spring:message code="binFeatureMapping.discardBtn" /></button>
									</c:if>
									</sec:authorize>
									
									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/binFeatureMappingPendingForApproval');">
										<spring:message code="binFeatureMapping.backBtn" /></button>
								
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

