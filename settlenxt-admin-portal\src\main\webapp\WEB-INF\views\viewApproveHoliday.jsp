<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<script type="text/javascript"
	src="./static/js/validation/viewApproveHoliday.js"></script>
 <c:if test="${holidayMasterDTO.requestState eq 'P'}"> 
	<sec:authorize access="hasAuthority('Approve Holiday Master')">
		<div class="alert alert-danger appRejMust" role="alert">
			<spring:message code="ifsc.apprejecterror.msg" />
		</div>
		<div class="alert alert-danger remarkMust" role="alert">
			<spring:message code="ifsc.remarksMandatory.msg" />
		</div>
	 </sec:authorize> 
</c:if> 


<form:form onsubmit="removeSpace(this); encodeForm(this);" method="POST"
	id="viewApproveIfsc" modelAttribute="holidayMasterDTO" 
	autocomplete="off">
	
	<form:hidden path="requestState" id="requestState" />
	
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data">Holiday Information</span></strong>
				<c:if
				test="${holidayMasterDTO.requestState eq 'R' or (holidayMasterDTO.requestState eq 'A' and holidayMasterDTO.status eq 'A')}">
				<sec:authorize access="hasAuthority('Edit Holiday Master') ">
					<a data-toggle="tooltip" title="Delete"
						onclick="loadDelete('${holidayMasterDTO.holidaySeqId}','${holidayMasterDTO.requestState}','/deleteHolidayMaster');"
						href="#"><img style="text-align:right" width="35" height="35"
						src="./static/images/user-deactivate-grey.png" alt="Delete"></a>

				</sec:authorize>
			</c:if>
		</div>
		<div class="panel-body">
			<form:hidden path="holidaySeqId" id="holidaySeqId" />
			<input type='hidden' id="showMainTab" value="${showMainTab}"/>
			<input type='hidden' id="showDiscard" value="${showDiscard}"/>
			
			<c:if test="${showMainTab ne 'Y'}">
			<table class="table table-striped infobold" style="font-size: 12px">
			<caption style="display:none;">Holiday</caption>
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
						<td colspan="6"><div class="panel-heading-red clearfix">
								<strong><span class="glyphicon glyphicon-info-sign"></span> <span
									data-i18n="Data"><spring:message
											code="ifsc.requestInformation" /></span></strong>
							</div></td>
						<td></td>
					</tr>
					<tr>
						<td><label><spring:message code="ifsc.requestType" /><span
								style="color: red"></span></label></td>
						<td>${holidayMasterDTO.lastOperation}</td>
						<td><label><spring:message code="ifsc.requestDate" /><span
								style="color: red"></span></label></td>
								<c:if test = "${empty holidayMasterDTO.lastUpdatedOn }">
<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss" value="${holidayMasterDTO.createdOn}" /></td>
</c:if>
<c:if test = "${not empty holidayMasterDTO.lastUpdatedOn }">
<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss" value="${holidayMasterDTO.lastUpdatedOn}" /></td>
</c:if>
						
						<td><label><spring:message code="ifsc.requestStatus" /><span
								style="color: red"></span></label></td>
						<td><c:if test="${holidayMasterDTO.requestState=='A' }">
								<spring:message code="ifsc.requestState.approved.description" />
							</c:if> <c:if test="${holidayMasterDTO.requestState=='P' }">
								<spring:message
									code="ifsc.requestState.pendingApproval.description" />
							</c:if> <c:if test="${holidayMasterDTO.requestState=='R' }">
								<spring:message code="ifsc.requestState.rejected.description" />
							</c:if> <c:if test="${holidayMasterDTO.requestState=='D' }">
								<spring:message code="ifsc.requestState.discarded.description" />
							</c:if> &nbsp;</td>
						<td></td>



					</tr>
					<tr>
						<td><label><spring:message code="ifsc.requestBy" /><span
								style="color: red"></span></label></td>
								<c:if test = "${empty holidayMasterDTO.lastUpdatedBy }">
								<td>${holidayMasterDTO.createdBy}</td>

</c:if>
<c:if test = "${not empty holidayMasterDTO.lastUpdatedBy }">
	<td>${holidayMasterDTO.lastUpdatedBy}</td>

</c:if>
						

						<td><label><spring:message
									code="ifsc.approverComments" /><span style="color: red"></span></label></td>
						<td colspan=2>${holidayMasterDTO.checkerComments}</td>
						<td></td>
						<td></td>

					</tr>
				</tbody>
			</table>
			</c:if>
			<table class="table table-striped infobold" style="font-size: 12px">
			<caption style="display:none;">Holiday</caption>
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
						<td colspan="6">
							<div class="panel-heading-red clearfix">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data">Holiday Information</span></strong>
							</div>
						</td>

						<td></td>
						<td></td>

					</tr>

					<tr>
						<td><label>Holiday Id<span
								style="color: red"></span></label></td>
						<td>${holidayMasterDTO.holidaySeqId }</td>
						<td><label>Holiday Date<span
								style="color: red"></span></label></td>
								<td><fmt:formatDate pattern="dd/MM/yyyy"
														value="${holidayMasterDTO.holidayDate}" /></td>
                		
                
						

						<td><label>Holiday Discription<span
								style="color: red"></span></label></td>
						<td>${holidayMasterDTO.holidayDesc }</td>
						<td><label>Period Type<span
								style="color: red"></span></label></td>
						<td>${holidayMasterDTO.periodType }</td>
								
						

					</tr>
					<tr>
						<td><label>Product<span
								style="color: red"></span></label></td>
						<td>${holidayMasterDTO.product }</td>
						<td><label>Day of The Week<span
								style="color: red"></span></label></td>
						<td>${holidayMasterDTO.dayOfWeek}</td>
						<td><label>Week Type<span
								style="color: red"></span></label></td>
						<td>${holidayMasterDTO.weekType}</td>
						<td><label><spring:message code="ifsc.status" /><span
								style="color: red"></span></label></td>
						<td><c:if test="${holidayMasterDTO.status=='A' }">
								<spring:message code="ifsc.activeStatus" />
							</c:if> <c:if test="${holidayMasterDTO.status=='I' }">
								<spring:message code="ifsc.inactiveStatus" />
							</c:if></td>
					</tr>
			</table>
			<c:if test="${showMainTab ne 'Y'}">
				<sec:authorize access="hasAuthority('Approve Holiday Master')">
					<c:if test="${holidayMasterDTO.requestState eq 'P'}">
						<table class="table table-striped infobold"
							style="font-size: 12px">
							<caption style="display: none;">Holiday</caption>
							<thead style="display: none;">
								<th scope="col"></th>
							</thead>
							<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red  clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span>
												<span data-i18n="Data">Holiday Approval</span></strong>
										</div></td>
								</tr>
								<tr>
									<td><label><spring:message
												code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
									<td><select name="select" id="apprej" onchange="display()">
											<option value="N"><spring:message
													code="AM.lbl.select" /></option>
											<option value="A" id="approve"><spring:message
													code="AM.lbl.approve" /></option>
											<option value="R" id="reject"><spring:message
													code="AM.lbl.reject" /></option>
									</select></td>
									<td>
										<div style="text-align: center">
											<label><spring:message code="AM.lbl.remarks" /><span
												style="color: red">*</span></label>
										</div>
									</td>
									<td colspan="5"><textarea rows="4" cols="50"
											maxlength="100" id="rejectReason"></textarea>
										<div id="errorrejectReason" class="error"></div></td>
								</tr>
							</tbody>
						</table>
					</c:if>
				</sec:authorize>
			</c:if>

			<div class="row">
				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center">
				       <c:if test="${showMainTab ne 'Y'}">
						<sec:authorize access="hasAuthority('Approve Holiday Master')">
							<c:if test="${holidayMasterDTO.requestState eq 'P'}">
								<input name="button10" type="button" class="btn btn-success"
									id="approveRole"
									value="<spring:message
							code="ifsc.submitBtn" />"
									onclick="postAction('/approveOrRejectHoliday');" />
							</c:if>
						</sec:authorize>
						</c:if>
						<sec:authorize access="hasAuthority('Edit Holiday Master')">
							
							<c:if test="${holidayMasterDTO.requestState ne 'P' and showDiscard ne 'N' and holidayMasterDTO.status eq 'A'}">
								<input name="editButton" type="button" class="btn btn-success"
									id="editButton"
									value="<spring:message
							code="ifsc.editBtn" />"
									onclick="userAction('/editHoliday');" />
							</c:if>
							
<c:if test="${holidayMasterDTO.requestState eq 'R' and holidayMasterDTO.status eq 'I'}">
<input name="editButton" type="button" class="btn btn-success"
 id="editButton"
value="<spring:message
code="ifsc.editBtn" />"
onclick="userAction('/editHoliday');" />
</c:if>
						</sec:authorize>
						
						<c:if test="${showMainTab ne 'Y'}">
						<button type="button" class="btn btn-danger"
						
							onclick="userAction('/getHolidayMasterPendingForApproval');">
							<spring:message code="ifsc.backBtn" />
						</button>
						</c:if>
						<c:if test="${showMainTab eq 'Y'}">
						<button type="button" class="btn btn-danger"
						
							onclick="userAction('/getHolidayMasterList');">
							<spring:message code="ifsc.backBtn" />
						</button>
						</c:if>
						
					</div>
				</div>
			</div>
		</div>
	</div>
</form:form>
