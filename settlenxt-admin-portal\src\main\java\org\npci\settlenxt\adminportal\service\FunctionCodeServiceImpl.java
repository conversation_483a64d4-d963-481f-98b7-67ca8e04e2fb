package org.npci.settlenxt.adminportal.service;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.FunctionCodeDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.FunctionCodeRepository;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Throwable.class)
public class FunctionCodeServiceImpl implements FunctionCodeService {

	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	FunctionCodeRepository functionCodeRepository;

	// show main tab
	public List<FunctionCodeDTO> getFunctionCodeList() {
		return functionCodeRepository.getFunctionCodeListMain();
	}

	// show approval tab
	@Override
	@Transactional(readOnly = true)
	public List<FunctionCodeDTO> getPendingFunctionCode() {
		return functionCodeRepository.getFunctionCodePendingForApproval();
	}

	// view main tab info
	@Override
	@Transactional(readOnly = true)
	public FunctionCodeDTO getFunctionCodeMainInfo(int funcCodeId) {
		FunctionCodeDTO functionCode = functionCodeRepository.getFunctionCodeProfileMain(funcCodeId);
		if (functionCode == null) {
			throw new SettleNxtException("No Data Exist", "");
		}
		return functionCode;
	}

	// view approval tab info
	@Override
	public FunctionCodeDTO getFunctionCodeStgInfo(String funcCodeId) {
		FunctionCodeDTO functionCode = functionCodeRepository.getFunctionCodeStgInfoById(Integer.parseInt(funcCodeId));
		if (functionCode == null) {
			throw new SettleNxtException("No Data Exist", "");
		}
		return functionCode;
	}

	// add edit
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public FunctionCodeDTO addEditFunctionCode(FunctionCodeDTO functionCodeDto) {
		int funcId = CommonConstants.TRANSACT_FUCTIONALITY_ADD_FUNCTION_CODE_CONFIG;
		if (CommonConstants.EDIT_FUNCTION_CODE.equalsIgnoreCase(functionCodeDto.getAddEditFlag())) {
			funcId = CommonConstants.TRANSACT_FUCTIONALITY_EDIT_FUNCTION_CODE_CONFIG;
		}
		Date lt = new Date();
		functionCodeDto.setStatus("I");
		functionCodeDto.setRequestState("P");
		functionCodeDto.setLastUpdatedOn(lt);
		functionCodeDto.setLastUpdatedBy(sessionDTO.getUserName());
		functionCodeDto.setCreatedOn(lt);
		functionCodeDto.setCreatedBy(sessionDTO.getUserName());
		functionCodeDto.setRequestState(CommonConstants.REQUEST_STATE_SUBMITTED);

		if (funcId == CommonConstants.TRANSACT_FUCTIONALITY_EDIT_FUNCTION_CODE_CONFIG) {
			functionCodeDto.setLastOperation(CommonConstants.LAST_OPERATION_EDIT);
			functionCodeRepository.updateFunctionCode(functionCodeDto);
		}
		if (funcId == CommonConstants.TRANSACT_FUCTIONALITY_ADD_FUNCTION_CODE_CONFIG) {
			int returnValue = checkDuplicateData(functionCodeDto);
			if (returnValue > 0) {
				throw new SettleNxtApplicationException("Function Code Entry already exists", "Duplicate Record");
			}
			functionCodeDto.setFuncCodeId(functionCodeRepository.fetchFunctionCodeIdSequence());
			functionCodeDto.setLastOperation(CommonConstants.LAST_OPERATION_ADD);
			functionCodeDto
					.setFeeType(functionCodeDto.getFeeType().substring(0, functionCodeDto.getFeeType().length() - 1));
			functionCodeRepository.insertFunctionCodeStg(functionCodeDto);
		}

		return functionCodeRepository.getFunctionCodeStg(functionCodeDto.getFuncCodeId());
	}

	// edit
	@Override
	@Transactional(readOnly = true)
	public FunctionCodeDTO getFunctionCodeForEdit(int funcCodeId) {
		return functionCodeRepository.getFunctionCodeStgInfoById(funcCodeId);
	}

	// for checker
	@Override
	public FunctionCodeDTO approveOrRejectFunctionCode(int funcCodeId, String status, String remarks) {
		FunctionCodeDTO functionCodeDto = getFunctionCodeStg(funcCodeId);
		functionCodeDto.setRequestState(status);
		functionCodeDto.setCheckerComments(remarks);
		functionCodeDto.setStatus("A");
		prepareFunctionCodeApproval(status, remarks, funcCodeId, functionCodeDto);
		return functionCodeDto;
	}

	// for bulk checker
	@Override
	public String approveOrRejectFunctionCodeBulk(String bulkApprovalReferenceNoList, String status, String remarks) {
		String[] referenceNoArr = bulkApprovalReferenceNoList.split("\\|");
		int funcCodeId = 0;

		for (String refNum:referenceNoArr) {
			try {
				if (!StringUtils.isEmpty(refNum)) {
					funcCodeId = Integer.parseInt(refNum);
					FunctionCodeDTO functionCodeDto = getFunctionCodeStg(funcCodeId);
					if (functionCodeDto == null) {

						throw new SettleNxtException("Exception occurred with Ref No" + refNum, "");
					}
					approveOrRejectBulk(status, remarks, funcCodeId, functionCodeDto);
				}
			} catch (Exception ex) {
				throw new SettleNxtException("Exception for Ref no" + refNum, "", ex);
			}
		}
		return CommonConstants.YES_FLAG;
	}

	private void approveOrRejectBulk(String status, String remarks, int funcCodeId, FunctionCodeDTO functionCodeDto) {
		if (functionCodeDto != null) {
			functionCodeDto.setRequestState(status);
			functionCodeDto.setCheckerComments(remarks);
			if ("A".equals(status)) {
				functionCodeDto.setStatus("A");
			} else {
				functionCodeDto.setStatus("I");
			}
			prepareFunctionCodeApproval(status, remarks, funcCodeId, functionCodeDto);
		}
	}

	private void prepareFunctionCodeApproval(String status, String remarks, int funcCodeId,
			FunctionCodeDTO functionCodeDto) {
		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			functionCodeDto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			FunctionCodeDTO functionCodeMain = functionCodeRepository.getFunctionCodeMain(funcCodeId);
			if (functionCodeMain != null) {
				functionCodeRepository.updateFunctionCodeMain(functionCodeDto);
			} else {
				functionCodeRepository.insertFunctionCodeMain(functionCodeDto);
			}
		}
		if (CommonConstants.REQUEST_STATE_REJECTED.equals(status)) {
			functionCodeDto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
		}
		functionCodeDto.setCheckerComments(remarks);
		
		Date lt = new Date();
		functionCodeDto.setLastUpdatedOn(lt);
		functionCodeDto.setLastUpdatedBy(sessionDTO.getUserName());
		functionCodeRepository.updateFunctionCodeRequestState(functionCodeDto);
	}

	// for discard
	@Override
	public FunctionCodeDTO discardFunctionCode(int funcCodeId) {
		FunctionCodeDTO functionCodeDto = getFunctionCodeStg(funcCodeId);
		FunctionCodeDTO functionCodeDtoMain = functionCodeRepository.getFunctionCodeMain(funcCodeId);
		if (functionCodeDtoMain != null) {
			functionCodeDtoMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			functionCodeDtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			functionCodeRepository.updateFunctionCodeDiscard(functionCodeDtoMain);
		} else {
			functionCodeRepository.deleteDiscardedEntry(functionCodeDto);
		}
		return functionCodeDto;
	}

	@Override
	public FunctionCodeDTO getFunctionCodeStg(int funcCodeId) {
		return functionCodeRepository.getFunctionCodeStg(funcCodeId);
	}

	@Override
	public int checkDuplicateData(FunctionCodeDTO functionCodeDto) {

		return functionCodeRepository.validateDuplicateCheck(functionCodeDto);

	}

}
