package org.npci.settlenxt.adminportal.service;

import org.npci.settlenxt.adminportal.dto.NetworkFileSummaryDTO;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public interface NetworkFileService {

    List<NetworkFileSummaryDTO> getNetworkFileDetails(String fromDateStr, String toDateStr, String networkType, String status);

    InputStream downloadCsv(String fromDateStr, String toDateStr, String networkType, String status) throws IOException;
}
