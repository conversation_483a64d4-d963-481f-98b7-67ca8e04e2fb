$(document).ready(function() {
	$('.appRejMust').hide();
	$('.remarkMust').hide();
});

function display() {
	$(".remarkMust").hide();
	$('#apprej').change(function() {
		if ($("#apprej").val() != "N") {
			$(".appRejMust").hide();
		} else {
			$(".appRejMust").show();
		}
	});
}

function navigateTo(action) {
	postData(action, "");
}

function editTransRule(action) {
	var data = "id," + document.getElementById('seqid').value;
	postData(action, data)
}

function appRejTrans() {
	if (type == "N") {
		$('.appRejMust').show();
		$('.remarkMust').hide();
		return false;
	}
	if ($("#rejectReason").val() == "") {
		$('.remarkMust').show();
		$('.appRejMust').hide();
		return false;
	}
	var type = $('#apprej option:selected').val();
	var seqId = document.getElementById('seqid').value;
	var remarks = document.getElementById('rejectReason').value;
	var data = "status," + type + ",tranIDList," + seqId + ",comments," + remarks;
	postData('/approveRejectTransition', data);
}