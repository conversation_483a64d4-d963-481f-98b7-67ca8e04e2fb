<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@    taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<script type="text/javascript">
	var actionColumnIndex = 12;
</script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>


<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>
<script type="text/javascript" src="./static/js/custom_js/jquery-ui.js"></script>
<script type="text/javascript" src="./static/js/custom_js/sha256.min.js"></script>
<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>


<style>
.green {
	color: green;
}

.red {
	color: red;
}

.blue {
	color: blue;
}
</style>




<script type="text/javascript">
	var validationMessages = {};
	validationMessages['productCode'] = "<spring:message code='productCode.validation.msg' javaScriptEscape='true' />";
	validationMessages['cycleDate'] = "<spring:message code='cycleDate.validation.msg' javaScriptEscape='true' />";
	validationMessages['cycleNumber'] = "<spring:message code='cycleNumber.validation.msg' javaScriptEscape='true' />";
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />

<script type="text/javascript"
	src="./static/js/validation/commonValidation.js"></script>
<script src="./static/js/validation/viewCycleStatusMonitor.js"
	type="text/javascript"></script>



<input type="hidden" id="flag" name="flag" value="${flag}" />
<input type="hidden" id="refreshFlag" name="refreshFlag"
	value="${refreshFlag}" />

<input type="hidden" id="cycleNumbers" name="cycleNumbers"
	value="${cycleNumbers}" />
<input type="hidden" id="defaultDate" name="defaultDate"
	value="${defaultDate}" />
<input type="hidden" id="runningCycleInfo" name="runningCycleInfo"
	value="${runningCycleInfo}" />
	

<head>
<title></title>



<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: bottom;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: bottom;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: bottom;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>




</head>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
	</ul>



	<div class="row">
		<div class="col-sm-12">
			<div class="panel panel-default">
				<div class="panel-heading">
					<strong><span class="glyphicon glyphicon-th"></span> <span
						data-i18n="Data"><spring:message
								code="activityModule.listscreen.title" /></span></strong>



				</div>


				<div class=" ">
					<div role="tabpanel" class="tab-pane active" id="home">
						<form:form onsubmit="return encodeForm(this);" method="POST"
							id="cycleStatusMonitor" modelAttribute="cycleStatusMonitorDto"
							action="#" autocomplete="off">
							<br />
							<div class="row">
								<div class="col-sm-12">
									<div class="col-sm-3">
										<div class="form-group">
											<label><spring:message code="am.lbl.productCode" /><span
												style="color: red">*</span></label>
											<form:select path="productCode" id="productCode"
												name="productCode" placeholder="Select Product Code"
												class="form-control">
												<form:option value="">
													<spring:message code="msg.lbl.select"></spring:message>
												</form:option>
												<form:options items="${prodCd}" />
											</form:select>
											<div id="errproductCode">
												<span for="productCode" class="error"><form:errors
														path="productCode" /></span>
											</div>
										</div>
									</div>

									<div class="col-sm-3">
										<div class="form-group">
											<label><spring:message code="am.lbl.cycleDate" /><span
												style="color: red">*</span></label>
											<form:input path="cycleDate" id="cycleDate" name="cycleDate"
												placeholder="Enter Cycle Date"
												cssClass="form-control medantory"/>
											<div id="errcycleDate">
												<span for="cycleDate" class="error"><form:errors
														path="cycleDate" /></span>
											</div>
										</div>
									</div>
									<div class="col-sm-3">
										<div class="form-group">
											<label><spring:message code="am.lbl.cycleNumber" /><span
												style="color: red">*</span></label>
											<form:select path="cycleNumber"
												placeholder="Select Cycle Number" id="cycleNumber"
												name="cycleNumber" class="form-control">

											</form:select>
											<div id="errcycleNumber">
												<span for="cycleNumber" class="error"><form:errors
														path="cycleNumber" /></span>
											</div>
										</div>
									</div>
									<div class="col-sm-3">
										<div style="margin-top: 22px;">
											<button type="button" class="btn btn-success"
												onclick="viewData();">
												<spring:message code="am.lbl.search" />
											</button>
											<button type="button" value="Submit" class="btn btn-success"
												onclick="resetAction();">
												<spring:message code="am.lbl.reset" />
											</button>





											<input type="checkbox" id="autoReload" checked />
											<spring:message code="am.lbl.Refresh" />




										</div>
									</div>
								</div>
							</div>

						</form:form>
					</div>
				</div>
				<div id="afterSave">
					<c:if test="${not empty cycleData}">
						<div class=" ">
							<div class="row">
								<div class="col-sm-12">
									<div class=" ">
										<div class=" "></div>
										<div class="panel-body">
											<div class="row">
												<div class="col-sm-12">
													<button class="btn  pull-right btn_align" id="clearFilters">
														<spring:message code="ifsc.clearFiltersBtn" />
													</button>
													&nbsp; <a class="btn btn-success pull-right btn_align"
														href="#" id="excelExport"><spring:message
															code="ifsc.exportBtn" /> </a> &nbsp; <a
														class="btn btn-success pull-right btn_align" href="#"
														id="csvExport"><spring:message code="ifsc.csvBtn" />
													</a>
												</div>
											</div>
											<div class="table-responsive">
												<table id="tabnew"
													class="table table-striped table-bordered"
													style="width: 100%;">
													<caption style="display: none;">SHOW CYCLE DATA</caption>
													<thead>
														<tr>
															<th scope="col">ID</th>
															<th scope="col">Product Code</th>
															<th scope="col">Status</th>
															<th scope="col">Cycle Date</th>
															<th scope="col">Cycle Number</th>
															<th scope="col">Activity Code</th>
															<th scope="col">Child Info</th>
														</tr>
													</thead>

													<tbody>
														<c:forEach var="member" items="${cycleData}">
															<td>${member.guid}</td>
															<td>${member.productCode}</td>


															<c:if test="${member.status == 'DONE'}">
																<td class="green">${member.status}</td>
															</c:if>

															<c:if test="${member.status == 'FAILED'}">
																<td class="red">${member.status}</td>
															</c:if>

															<c:if test="${member.status == 'INITIATED'}">
																<td class="blue">${member.status}</td>
															</c:if>



															<td>${member.cycleDate}</td>
															<td>${member.cycleNumber}</td>
															<td>${member.activityCode}</td>

															<td><c:if test="${member.childFlag eq 'Y'}">
																	<a href="javascript:viewChildInfo('${member.guid}')">
																		<spring:message code="actModule.viewBtn" />
																	</a>
																</c:if></td>
															</tr>
														</c:forEach>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

					</c:if>


					<c:if test="${empty cycleData}">
						<div class=" ">
							<div class="row">
								<div class="col-sm-12">
									<div class=" ">
										<div class=" "></div>
										<div class="panel-body">
											<div class="table-responsive">
												<table id="tabnew"
													class="table table-striped table-bordered"
													style="width: 100%;">
													<caption style="display: none;">SHOW CYCLE DATA</caption>
													<thead>
														<tr>
															<th scope="col">ID</th>
															<th scope="col">Product Code</th>
															<th scope="col">Status</th>
															<th scope="col">Cycle Date</th>
															<th scope="col">Cycle Number</th>
															<th scope="col">Activity Code</th>
															<th scope= "col">Child Info</th>

														</tr>
													</thead>
													<tbody>
													</tbody>
												</table>

											</div>
										</div>
									</div>
								</div>
							</div>

						</div>
					</c:if>
				</div>
			</div>
		</div>



	</div>
</div>