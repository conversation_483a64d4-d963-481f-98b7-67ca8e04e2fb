package org.npci.settlenxt.adminportal.controllers;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.CodeValueService;
import org.npci.settlenxt.adminportal.service.MccService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.MccDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class MccController extends BaseController {

	private static final String SHOW_MCC = "showMcc";
	private static final String ADD_EDIT_MCC = "addEditMcc";
	private static final String VIEW_APPROVE_MCC = "viewApproveMcc";
	private static final String VIEW_MCC = "viewMcc";
	private static final String SHOW_CHECK_BOX = "showCheckBox";
	private static final String MCC_GROUP_LIST = "mccGroupList";
	private static final String MCC_GROUP_LIST_LOOKUP = "MCCGroupList";
	private static final String SHOW_PENDING_MCC="showPendingMcc";
	
	@Autowired
	MccService mccService;
	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	private CodeValueService codeValueService;

	@PostMapping("/showMcc")
	@PreAuthorize("hasAuthority('View Mcc Config')")
	public String showMcc(Model model) {
		try {
			model.addAttribute(SHOW_MCC, CommonConstants.YES);
			List<MccDTO> mccList = mccService.getMccList();
			model.addAttribute(CommonConstants.MCC_LIST, mccList);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, SHOW_MCC, ex);
		}
		model.addAttribute(CommonConstants.ADD_MCC, CommonConstants.TRANSACT_YES);
		return getView(model, SHOW_MCC);
	}

	@PostMapping("/mccPendingForApproval")
	@PreAuthorize("hasAuthority('View Mcc Config')")
	public String budgetPendingForApproval(Model model) {
		try {

			List<MccDTO> pendingMccList = mccService.getPendingMccConfigs();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(SHOW_PENDING_MCC, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.MCC_PENDING_LIST, pendingMccList);
			model.addAttribute(CommonConstants.MCC_APP_PENDING, CommonConstants.YES);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_MCC, ex);
		}
		return getView(model, SHOW_MCC);
	}

	@PostMapping("/getMcc")
	@PreAuthorize("hasAuthority('View Mcc Config')")
	public String getMccConfig(@RequestParam("mccId") Integer mccId, Model model) {
		MccDTO mccConfigById = mccService.getMccConfigById(mccId);
		model.addAttribute(CommonConstants.MCC_DTO, mccConfigById);
		return getView(model, VIEW_MCC);
	}

	@PostMapping("/getPendingMcc")
	@PreAuthorize("hasAuthority('View Mcc Config')")
	public String getPendingMccConfig(@RequestParam("mccId") Integer mccId, Model model) {
		MccDTO mccConfigById = mccService.getPendingMccConfigById(mccId);
		model.addAttribute(CommonConstants.MCC_DTO, mccConfigById);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.DISCARD_MCC);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_MCC);
		return getView(model, VIEW_APPROVE_MCC);

	}

	@PostMapping("/createMcc")
	@PreAuthorize("hasAuthority('Add Mcc Config')")
	public String createMcc(Model model) {
		try {
			MccDTO mccDTO = new MccDTO();
			List<CodeValueDTO> mccGroupList = codeValueService.getMccGroupList(MCC_GROUP_LIST_LOOKUP);
			model.addAttribute(MCC_GROUP_LIST, mccGroupList);
			model.addAttribute(CommonConstants.ADD_MCC, CommonConstants.TRANSACT_YES);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_BUDGET);
			model.addAttribute(CommonConstants.MCC_DTO, mccDTO);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, ADD_EDIT_MCC, ex);
		}
		return getView(model, ADD_EDIT_MCC);
	}

	@PostMapping("/addMccConfig")
	@PreAuthorize("hasAuthority('Add Mcc Config')")
	public String addMcc(@ModelAttribute("mccDTO") MccDTO mccDTO, Model model) {
		try {
			List<CodeValueDTO> mccGroupList = codeValueService.getMccGroupList(MCC_GROUP_LIST_LOOKUP);
			model.addAttribute(MCC_GROUP_LIST, mccGroupList);
			model.addAttribute(CommonConstants.ADD_MCC, CommonConstants.ADD_MCC);
			mccDTO = mccService.addMccConfig(mccDTO);
			model.addAttribute(CommonConstants.MCC_DTO, mccDTO);
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mcc.addSuccess.msg"));
		} catch (Exception ex) {
			model.addAttribute(CommonConstants.MCC_DTO, mccDTO);
			return handleErrorCodeAndForward(model, ADD_EDIT_MCC, ex);
		}
		return getView(model, ADD_EDIT_MCC);

	}

	@PostMapping("/editMcc")
	@PreAuthorize("hasAuthority('Edit Mcc Config')")
	public String editMcc(@ModelAttribute("mccDTO") MccDTO mccDTO, @RequestParam("mccId") Integer mccId,
			@RequestParam("parentPage") String parentPage, Model model) {
		try {
			List<CodeValueDTO> mccGroupList = codeValueService.getMccGroupList(MCC_GROUP_LIST_LOOKUP);
			model.addAttribute(MCC_GROUP_LIST, mccGroupList);

			MccDTO mccData = mccService.getPendingMccConfigById(mccId);

			String operation = mccData.getLastOperation();
			String status = mccData.getRequestState();

			if (status.equals(CommonConstants.REQUEST_STATE_REJECTED)) {
				MccDTO dataStg = mccService.getMccConfigStgById(mccId);
				if (operation.equals(CommonConstants.ADD_MCC_LAST_OPERATION)) {
					model.addAttribute(CommonConstants.NEW_MCC, CommonConstants.TRANSACT_YES);
				}
				model.addAttribute(CommonConstants.MCC_DTO, dataStg);
			} else {
				MccDTO dataMain = mccService.getMccConfigById(mccId);
				model.addAttribute(CommonConstants.MCC_DTO, dataMain);
			}

			model.addAttribute(CommonConstants.PARENT_PAGE, parentPage);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_MCC, ex);
		}

		model.addAttribute(CommonConstants.EDIT_MCC, CommonConstants.TRANSACT_YES);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.TRANSACT_YES);
		return getView(model, ADD_EDIT_MCC);
	}

	@PostMapping("/updateMccConfig")
	@PreAuthorize("hasAuthority('Edit Mcc Config')")
	public String updateMcc(@ModelAttribute("mccDTO") MccDTO mccDTO, @RequestParam("mccId") Integer mccId, Model model,
			@RequestParam("parentPage") String parentPage) {
		try {
			List<CodeValueDTO> mccGroupList = codeValueService.getMccGroupList(MCC_GROUP_LIST_LOOKUP);
			model.addAttribute(MCC_GROUP_LIST, mccGroupList);

			MccDTO mcclocal = mccService.updateMccConfig(mccDTO);

			model.addAttribute(CommonConstants.MCC_BACK, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.MCC_DTO, mcclocal);

		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, ADD_EDIT_MCC, ex);
		}
		model.addAttribute(CommonConstants.EDIT_MCC, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.PARENT_PAGE, parentPage);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mcc.updateSuccess.msg"));

		return getView(model, ADD_EDIT_MCC);
	}

	@PostMapping("/discardRejectedMcc")
	@PreAuthorize("hasAuthority('Edit Mcc Config')")
	public String discardMcc(@RequestParam("mccId") Integer mccId, Model model) {
		MccDTO mccDTO = new MccDTO();
		try {
			mccDTO = mccService.discardMcc(mccId);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_MCC, ex);
		}
		model.addAttribute(CommonConstants.MCC_DTO, mccDTO);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mcc.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_MCC);
	}
// checker approval

	@PostMapping("/approveMcc")
	@PreAuthorize("hasAuthority('Approve Mcc Config')")
	public String approveMccConfig(@RequestParam("mccId") Integer mccId, @RequestParam("status") String status,
			@RequestParam("remarks") String remarks, Model model, HttpServletRequest request) {
		try {
			MccDTO mccDTO = mccService.updateApproveOrRejectMcc(mccId, status, remarks);
			checkMccApproveStatus(mccDTO, model);
			model.addAttribute(CommonConstants.MCC_DTO, mccDTO);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_MCC, ex);
		}
		return getView(model, VIEW_APPROVE_MCC);
	}

	private void checkMccApproveStatus(MccDTO mccDTO, Model model) {
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(mccDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mcc.approvalSuccess.msg"));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mcc.rejectionSuccess.msg"));
		}
	}

	// For Checker Approval bulk
	@PostMapping("/approveMccBulk")
	@PreAuthorize("hasAuthority('Approve Mcc Config')")
	public String approveMccBulk(@RequestParam("bulkApprovalReferenceNoList") String bulkApprovalReferenceNoList,
			@RequestParam("status") String status, Model model) {
		String successStatus = "";

		try {
			String remarks = "";

			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}
			successStatus = mccService.approveOrRejectMccBulk(bulkApprovalReferenceNoList, status, remarks);
			List<MccDTO> pendingMccList = mccService.getPendingMccConfigs();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.MCC_PENDING_LIST, pendingMccList);
			model.addAttribute(CommonConstants.MCC_APP_PENDING, CommonConstants.YES);
			if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && "A".equalsIgnoreCase(status)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mcc.approvalSuccess.msg"));
			} else if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && "R".equalsIgnoreCase(status)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("mcc.rejectionSuccess.msg"));
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_MCC, ex);
		}
		return getView(model, SHOW_MCC);
	}

}
