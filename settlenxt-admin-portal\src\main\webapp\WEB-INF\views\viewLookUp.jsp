<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript"
	src="./static/js/validation/viewLookUp.js"></script>

<form:form onsubmit="removeSpace(this); encodeForm(this);" method="POST"
	id="viewApprovalLookUp" modelAttribute="lookUpDTO"
	action="/approveLookUp" autocomplete="off">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="lookUp.viewscreen.title" /></span></strong>
			<div class="icon_bar">
				<sec:authorize access="hasAuthority('Edit LookUp') ">
					<a data-toggle="tooltip" title="Edit"
						onclick="viewLookUpInfo('${lookUpDTO.lookupId}','/editLookUp')"
						href="#"><img src="./static/images/edit-grey.png" alt="edit"></a>

				</sec:authorize>
			</div>
		</div>

		<div class="panel-body">
			<form:hidden path="lookupId" value="${lookUpDTO.lookupId}" />

			<table class="table table-striped" style="font-size: 12px">
				<caption style="display:none;">View LookUp</caption> 
			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
						
						
						<td><label><spring:message code="sm.lbl.lkpType" /><span
								style="color: red"></span></label></td>
						<td>${lookUpDTO.lkpType}</td>

						<td><label><spring:message code="sm.lbl.lkpValue" /><span
								style="color: red"></span></label></td>
						<td>${lookUpDTO.lkpValue}</td>

						<td><label><spring:message code="sm.lbl.lkpDescp" /><span
								style="color: red"></span></label></td>
						<td>${lookUpDTO.lkpDesc}</td>
						
						
						

						


					</tr>

					<tr>
	<td><label><spring:message code="sm.lbl.lkpStatus" /><span
								style="color: red"></span></label></td>
								<c:choose>
												<c:when test="${lookUpDTO.status=='A' }">
													<td>Active</td>
												</c:when>
												<c:otherwise>
												<td>InActive</td>
												</c:otherwise>
											</c:choose>
								
						
						<td><label><spring:message code="sm.lbl.lkpCreatedBy" /><span
								style="color: red"></span></label></td>
						<td>${lookUpDTO.createdBy}</td>


					</tr>

				</tbody>
			</table>

			
			
			<div style="text-align:center">

            <c:if test="${lookUpDTO.requestState ne 'R'}">

                <button type="button" class="btn btn-danger"

                    onclick="submitForm('/getLookUpList');">

                    <spring:message code="ifsc.backBtn" />

                </button>

            </c:if>

            <c:if test="${lookUpDTO.requestState eq 'R'}">

                <button type="button" class="btn btn-danger"

                    onclick="submitForm('/getPendingLookUpList');">

                    <spring:message code="ifsc.backBtn" />

                </button>

            </c:if>

            </div>
			</div>
		</div>
	</div>
</form:form>
