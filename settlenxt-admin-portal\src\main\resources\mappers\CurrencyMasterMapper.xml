<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.CurrencyMasterRepository">
	
	<select id="getCurrencyMasterListMain" resultType="CurrencyMasterDTO">
		select c.currency_master_id as currencyId, c.currency_code as currencyCode, c.currency_description as currencyDescription,
		c.currency_alpha as currencyAlpha, c.currency_decimal_position as currencyDecimalPosition,stg.request_state as requestState
		from  currency_master c 
		inner join currency_master_stg stg on c.currency_master_id=stg.currency_master_id
		 ORDER BY c.last_updated_on desc
	</select>
	<select id="getCurrencyMasterPendingForApproval" resultType="CurrencyMasterDTO">
		select c.currency_master_id as currencyId, c.currency_code as currencyCode, c.currency_description as currencyDescription,
		c.currency_alpha as currencyAlpha, c.currency_decimal_position as currencyDecimalPosition,c.request_state as requestState,c.CHECKER_COMMENTS as checkerComments, 
		c.status,c.last_operation as lastOperation
		from  currency_master_stg c WHERE c.request_state in('P','R') ORDER BY c.last_updated_on desc
	</select>
	<select id="getCurrencyMasterProfileMain" resultType="CurrencyMasterDTO">
		select c.currency_master_id as currencyId, c.currency_code as currencyCode, c.currency_description as currencyDescription,
		c.currency_alpha as currencyAlpha, c.currency_decimal_position as currencyDecimalPosition,stg.request_state as requestState
		from  currency_master c 
		inner join currency_master_stg stg on c.currency_master_id=stg.currency_master_id
		WHERE c.currency_master_id = #{currencyId} ORDER BY c.last_updated_on desc
	</select>
	<select id="getCurrencyMasterStgInfoById" resultType="CurrencyMasterDTO">
		select c.currency_master_id as currencyId, c.currency_code as currencyCode, c.currency_description as currencyDescription,status,
		c.currency_alpha as currencyAlpha, c.currency_decimal_position as currencyDecimalPosition,c.created_by as createdBy, c.created_on createdOn,c.last_updated_by as lastUpdatedBy,
		 c.last_updated_on as lastUpdatedOn, c.request_state as requestState,c.CHECKER_COMMENTS as checkerComments, c.status,c.last_operation as lastOperation
		from  currency_master_stg c WHERE c.currency_master_id = #{currencyId}
	</select>
	<select id="getCurrencyMasterMain" resultType="CurrencyMasterDTO">
		select c.currency_master_id as currencyId, c.currency_code as currencyCode, c.currency_description as currencyDescription,
		c.currency_alpha as currencyAlpha, c.currency_decimal_position as currencyDecimalPosition,status,c.created_by as createdBy, c.created_on createdOn,
		c.last_updated_by as lastUpdatedBy, c.last_updated_on as lastUpdatedOn
		from  currency_master c WHERE c.currency_master_id  = #{currencyId}
	</select>
	<select id="getCurrencyMasterStg" resultType="CurrencyMasterDTO">
		select c.currency_master_id as currencyId, c.currency_code as currencyCode, c.currency_description as currencyDescription,
		c.currency_alpha as currencyAlpha, c.currency_decimal_position as currencyDecimalPosition,c.created_by as createdBy, c.created_on createdOn,
		 c.last_updated_by as lastUpdatedBy, c.last_updated_on as lastUpdatedOn, c.request_state as requestState,c.CHECKER_COMMENTS as checkerComments, c.status,c.last_operation as lastOperation
		from  currency_master_stg c WHERE c.currency_master_id  = #{currencyId}
	</select>
	<select id="fetchCurrencyMasterIdSequence" resultType="int">	
		SELECT nextval('currency_master_id_seq')
	</select>
	<insert id="insertCurrencyMasterStg" >
		INSERT INTO   currency_master_stg (currency_master_id, currency_code,currency_description,currency_alpha,currency_decimal_position,CREATED_BY,CREATED_ON, LAST_UPDATED_BY,LAST_UPDATED_ON, request_state, last_operation, status) VALUES 
		(#{currencyId}, #{currencyCode} , #{currencyDescription},#{currencyAlpha}, #{currencyDecimalPosition}, #{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{requestState}, #{lastOperation}, #{status})
	</insert>
	<insert id="insertCurrencyMasterMain">
		INSERT INTO   currency_master (currency_master_id, currency_code,currency_description,currency_alpha,currency_decimal_position,CREATED_BY,CREATED_ON, LAST_UPDATED_BY,LAST_UPDATED_ON, status) VALUES
		(#{currencyId}, #{currencyCode} , #{currencyDescription},#{currencyAlpha}, #{currencyDecimalPosition}, #{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{status})
	</insert>
	<update id="updateCurrencyMasterMain">
		UPDATE  currency_master SET currency_master_id=#{currencyId}, currency_code= #{currencyCode},currency_description=#{currencyDescription},currency_alpha= #{currencyAlpha}, currency_decimal_position= #{currencyDecimalPosition},
		LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},STATUS=#{status}  WHERE currency_master_id = #{currencyId}
	</update>
	<update id="updateCurrencyMaster">
		UPDATE  currency_master_stg SET currency_master_id=#{currencyId}, currency_code= #{currencyCode},currency_description=#{currencyDescription}, currency_alpha= #{currencyAlpha}, currency_decimal_position= #{currencyDecimalPosition},
		LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, last_operation= #{lastOperation},STATUS=#{status},request_state =#{requestState}, CHECKER_COMMENTS=''  WHERE currency_master_id = #{currencyId}	
	</update>
	<update id="updateCurrencyMasterRequestState">
		UPDATE  currency_master_stg SET  request_state= #{requestState}, CHECKER_COMMENTS= #{checkerComments}, LAST_UPDATED_BY= #{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn}, last_operation= #{lastOperation} WHERE currency_master_id = #{currencyId}
	</update>
	<delete id="deleteDiscardedEntry">
		DELETE FROM  currency_master_stg WHERE currency_master_id = #{currencyId}
	</delete>
	<select id="validateDuplicateCheck" resultType="int">
		SELECT count(*) from  currency_master_stg c where c.currency_code =#{currencyCode}
	</select>
	<select id="getAll" resultType="org.npci.settlenxt.portal.common.dto.CurrencyMasterDTO">
		select currency_master_id as currencyId,currency_code as
		currencyCode,currency_description as currencyDescription,currency_alpha as currencyAlpha,status from
		currency_master where status='A';
	</select>
</mapper>


