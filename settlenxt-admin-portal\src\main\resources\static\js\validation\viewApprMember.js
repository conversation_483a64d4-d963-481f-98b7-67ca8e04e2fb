$(document).ready(function() {
	$('.appRejMust').hide();
	$('.remarkMust').hide();
	
	$('#pgpKey').attr("disabled", "disabled");
	$('#cmRefNum').attr("disabled", "disabled");
	
	if ($('#isNewTopic').val() == 'N')
		$('#newTopic').attr('checked', 'checked');

	$('.viewOnly').attr('readonly', 'true');

	$('#apprej').change(function() {
		if ($("#apprej").val() != "N") {
			$(".appRejMust").hide();
		} else {
			$(".appRejMust").show();
			$(".remarkMust").hide();
		}
	});

	$('#rejectReason').change(function() {
		showAndHideRemark();
	});

	if ($('#memberType').val() == 'SB' || $('#memberType').val() == 'NBFC') {
		$('#memSubTypeDiv').hide();
		$('#parentMemIdDiv').hide();
		if($('#memberType').val() == 'NBFC')
			{						
			if(document.getElementById("hiddenUnregDealer").value == "Yes")
				{
					document.getElementById("rdYes").checked=true;
				}
				else
				{
					document.getElementById("rdNo").checked=true;
				}
				document.getElementById("rdYes").disabled = true;
				document.getElementById("rdNo").disabled = true;
				$('#issid').hide();
			}
	} else {
		$('#memSubTypeDiv').show();
		$('#parentMemIdDiv').show();
		$('#issid').show();
	}

	if ($('#memberType').val() != 'NBFC')
	{
		$("#unregdealerDiv").hide();
	}

	$('select').attr("disabled", "disabled");
	$('#apprej').removeAttr("disabled");
	setAccordian();
});

function showAndHideRemark() {
    if ($('#rejectReason').val() == "")
        $(".remarkMust").show();
    else
        $(".remarkMust").hide();
}

function supDocDownload() {
	var url = "/supDocFileDownload";
	
	var data =  "supfilePath," + $("#upfilePath").val()+$("#upfileName").val();
	postData(url, data);
}

function backAction(_type, action) {
	var url = action;
	var data = "";
	postData(url, data);
}

function enadisReject() {
	$(".appRejMust").hide();
}

function postAction(action) {
	

	
	if ($("#apprej").val() != "N") {
		$(".appRejMust").hide();
	} else {
		$(".appRejMust").show();
		return false;
	}

	if ($('#rejectReason').val() == "") {
		$(".remarkMust").show();
		return false;
	} else {
	document.querySelector(".button").disabled = true;
		$(".remarkMust").hide();
		if (maxLengthTextArea('rejectReason')) {
			$('button').prop('disabled', true);
			var url = action;
			var data = "memberId," + $('#memberId').val() + ",participantId,"+ $('#participantId').val() + ",funcID,"
					+ $('#funcID').val() + ",status,"
					+ $('#apprej option:selected').val() + ",remarks," + $('#rejectReason').val();
			postData(url, data);
		}
	}

	
}


function setAccordian(){	
	
	$("#next1").click(function () {

		$("#collapseTwo").addClass("panel-collapse collapse in");
		
		
});

$("#next2").click(function () {

		$("#collapseThree").addClass("panel-collapse collapse in");
		
		
});

$("#next3").click(function () {

		$("#collapseFour").addClass("panel-collapse collapse in");
		
		
});
	
	$("#approve").click(function(){
    $(".success").show();
	 $(".bs-example").css("margin-top", "60px");
	});
	
	 // Add minus icon for collapse element which is open by default
    $(".collapse.in").each(function(){
    	$(this).siblings(".panel-heading").find(".glyphicon").addClass("glyphicon-minus").removeClass("glyphicon-plus");
    });
    
    // Toggle plus minus icon on show hide of collapse element
    $(".collapse").on('show.bs.collapse', function(){
    	$(this).parent().find(".glyphicon").removeClass("glyphicon-plus").addClass("glyphicon-minus");
    }).on('hide.bs.collapse', function(){
    	$(this).parent().find(".glyphicon").removeClass("glyphicon-minus").addClass("glyphicon-plus");
    });
	
	
	
$('#example').DataTable( {
    responsive: true
} );



}


function docDownload(fileName) {
	var url = "/supDocFileDownload";
	var data =  "supfilePath," + $("#supfilePath").val()+fileName;
	postData(url, data);
}

function populateAcqBinDtls(bankGroup,acquirerId,domainUsage,acqProductType,acqSettlementBin,acqFrmDate,acqToDate)
{
	$('#oldOrNewBinFlag').val("OldBin");
	$('#acqBankGroup').val(bankGroup);
	$('#oldAcquirerId').val(acquirerId);
	$('#acquirerId').val(acquirerId);
	$('#acqDomainUsage').val(domainUsage);
	$('#acqProductType').val(acqProductType);
	$('#acqSettlementBin').val(acqSettlementBin);
	$('#acqFrmDate').val(acqFrmDate);
	$('#acqToDate').val(acqToDate);
	$('#newAcquirerBin').show();
	
	
}

function populateIssBinDtls(isOffline,featureList,binType,bankGroup,binNumber,binCardType,binProductType,binCardVariant,productType,binCardBrand,messageType,cardTechnology,authMechanism,settlementBin,lowBin,highBin,panLength,domainUsage,subScheme,cardSubVariant,programDetails,formFactor,issFrmDate,issToDate)
{
	$('#oldOrNewBinFlag').val("OldBin");
	$('#issBankGroup').val(bankGroup);
	$('#binNumber').val(binNumber);	
	if(binType=='I'){
		$('#binType').val('Issuer Bin');
		$('#issSettlementBinDisplay').show();
		$('#issSettlementBin').val(settlementBin);
	}
	else{
		$('#binType').val('Token Bin');
		$('#issSettlementBinDisplay').hide();
	}
	$('#featureIssBin').val(featureList);
	$('#isIssOfflineAllowed').val(isOffline);
	
	$('#oldBinNumber').val(binNumber);
	$('#lowBin').val(lowBin);
	$('#highBin').val(highBin);
	$('#panLength').val(panLength);
	$('#binCardType').val(binCardType);
	$('#binProductType').val(binProductType);
	$('#binCardVariant').val(binCardVariant);
	$('#binCardBrand').val(binCardBrand);
	$('#issDomainUsage').val(domainUsage);
	$('#messageType').val(messageType);
	$('#cardTechnology').val(cardTechnology);
	$('#authMechanism').val(authMechanism);
	$('#issProductType').val(productType);
	$('#subScheme').val(subScheme);
	$('#cardSubVariant').val(cardSubVariant);
	$('#programDetails').val(programDetails);
	$('#formFactor').val(formFactor);

	$('#issFrmDate').val(issFrmDate);
	$('#issToDate').val(issToDate);
	$('#newIssuerBin').show();
	
}

function getFileIdsAndDownload()
{
	var idList;
	var fileIDs = new Array();
	var flag = false;
	var i;
	
	idList = document.getElementsByName("fileCheckbox");

	for ( i = 0; i < idList.length; i++) {
		if(idList[i].checked)
		{
			flag=true;

				fileIDs[i] = idList[i].id;

		}
		}
		if(idList.length>0 && flag)
			{
				downloadParticipantDocuments(fileIDs);
			}
		else if(idList.length>0)
			{
			for ( i = 0; i < idList.length; i++) {
				fileIDs[i] = idList[i].id;
			}
				downloadParticipantDocuments(fileIDs);
			 
			}
	}




function downloadParticipantDocuments(fileIDs) {
	var url = "/downloadParticipantDocuments/participantId/"+$('#participantId').val() + "/fileIDs/" + fileIDs;
	var data = ""; 
	postData(url, data);	
}

