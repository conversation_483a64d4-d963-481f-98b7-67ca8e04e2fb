<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.npci.settlenxt.adminportal.repository.ReportRepository">

<select id="reportGeneratedList" resultType="AuditBatchLogDTO">
select distinct LOG_ID as logId,REPORT_NAME as reportName,FILE_TYPE as fileType,FILE_GEN_DATE as fileGenDate,created_on as createdOn, participant_id as participantId,FILE_PATH as filePath,REPORT_TYPE as reportType FROM AUDIT_BATCH_LOG WHERE status=#{auditBatchLogDTO.status} and 
			(FILE_GEN_DATE >= TO_TIMESTAMP(#{auditBatchLogDTO.fromDate},'YYYY-MM-DD HH24:MI:SS')
			 <![CDATA[ and FILE_GEN_DATE <=  TO_TIMESTAMP(#{auditBatchLogDTO.toDate},'YYYY-MM-DD HH24:MI:SS')  ]]>)
			 and file_type in 
			<foreach item='item' index='index' collection='fileTypeList' open='(' separator=',' close=')'> #{item} </foreach>
			<if test="isFileType"> and FILE_TYPE = #{auditBatchLogDTO.fileType} </if>
			<if test="isMemberName"> and participant_id =#{auditBatchLogDTO.memberName} </if>
</select>

<select id="generatedList" resultType="AuditBatchLogDTO">
select distinct on (aud.report_name,aud.file_gen_date,aud.cycle_number,aud.participant_id,aud.file_path) aud.LOG_ID as logId,aud.REPORT_NAME as reportName,aud.FILE_TYPE as fileType,aud.cycle_number as cycleNum,aud.participant_id as participantId,aud.created_on as createdOn,aud.FILE_PATH as filePath,aud.REPORT_TYPE as reportType 
FROM AUDIT_BATCH_LOG aud inner join batch_master bm on bm.file_type=aud.file_type
where aud.log_id in
(
select  max(log_id) FROM AUDIT_BATCH_LOG  
 WHERE 
(FILE_GEN_DATE >= TO_TIMESTAMP(#{auditBatchLogDTO.fromDate},'YYYY-MM-DD HH24:MI:SS') 
<![CDATA[ and FILE_GEN_DATE <=  TO_TIMESTAMP(#{auditBatchLogDTO.toDate},'YYYY-MM-DD HH24:MI:SS')  ]]>) 
 <if test="isMemberName"> and participant_id =#{auditBatchLogDTO.memberName} </if> 
<if test="isFileType"> and FILE_TYPE = #{auditBatchLogDTO.fileType}  </if>
<if test="isCycle"> and cycle_number = #{auditBatchLogDTO.cycleNum}  </if> 
group by participant_id,report_name,file_gen_date,cycle_number,file_path
) and bm.report_type='Y';


</select>

<select id="getParticipantNameList" resultType="ParticipantDTO">
SELECT DISTINCT CONCAT (PARTICIPANT_ID,'-', bank_name) as bank_name , bank_name as participant_name,
 participant_id FROM PARTICIPANT where status=#{status} and participant_id=#{participantId}
</select>

<select id="getSettlementBinList" resultType="BinDTO">
Select settlement_bin_no  as settlementBin from participant_settlementbin where is_active=#{status}
</select>

<select id="getSettlementBinLists" resultType="BinDTO">
Select settlement_bin_no as settlementBin from participant_settlementbin where participant_id=#{participantId} and is_active=#{status}
</select>

<select id="getSearchMemberList" resultType="MemberDTO">

	select distinct(s.participant_id) as participantId,s.phone_number2 as bnkPhone2,s.zip_code as bnkPincode,s.bank_code as bankCode,ps.bin_type as binType, ps.bin_number as binNumber,
	ps.acquirer_id as acquirerId,ps.settlement_bin as settlementBinNumber,s.bank_type as bankType,s.member_id as memberId,s.participant_id as participantId,s.bank_name as participantName,
	TO_CHAR(s.created_on,'DD/MM/YYYY  HH24:MI:SS') as createdDate,s.request_state as requestState,s.legal_address as bnkAdd,
	s.phone_number1 as bnkPhone,s.email_id1 as bnkEmail,s.last_updated_on as lastUpdatedOn,s.status as status 
			from participant s   join membin_details ps on (s.participant_id=ps.participant_id) where ps.status !='D' and s.status ='A' <if test="isParticpant">and s.participant_id=#{participantId}
			 </if>
			<if test="isSetbin"> and ps.settlement_bin =#{settlementBinNumber} </if>
			<if test="isBin">
and ps.bin_number =#{binNumber} or ps.acquirer_id =#{binNumber} 
			</if>

</select>

<select id="getMemberDetails" resultType="MemberDTO">
select p.participant_id as participantId,p.bank_name as memberName,p.legal_address as bnkAdd,p.phone_number1 as bnkPhone ,p.phone_number2 as bnkPhone2,p.zip_code as bnkPincode,p.email_id1 as bnkEmail,  settlement_bin as settlementBinNumber ,bin_number as binNumber ,acquirer_id  as acquirerId,bin_type as binType , c.country_name as bankCountry  
,s.state_name as bankState  ,g.city_name as bankCity ,d.country_name as gstCntry,
e.state_name as gstSt ,f.city_name as gstCty,df.country_name as contactCountry,ef.state_name as
contactState,ff.city_name as contactCity, i.ifsc_code as ifscCode ,i.savings_acct_id as savingsAccNumber,
pc.legal_address as cntAdd1, pc.email_id as cntEmail,pc.auth_officer_desg as cntDesignation,pc.fax_number as cntFax,pc.phone_number as cntPhone ,pc.mobile_number as cntMobile,pc.zip_code as cntPincode,
df.country_id as contactCountry,ef.state_id as contactState,ff.city_id as contactCity,
i.current_acct_id as currentAccNumber,p.savings_acct_id as savingsAccNumber,p.current_acct_id as currentAccNumber,
i.nfs_code as participantIdNFS, p.nfs_id as nfsId,* from participant p
left join participant_contact pc on  p.participant_id=pc.participant_id
left join ifsc i on  SUBSTRING(P.participant_id, 1, 4) = i.ifsc_code
left join country c on p.country_id=c.country_id left join state s
on p.state_id =s.state_id left join country d on p.country_id=d.country_id
left join state e on p.state_id =e.state_id left join city f on
p.city_id=f.city_id left join city g on p.city_id=g.city_id left join
country df on pc.country_id=df.country_id left
join state ef on pc.state_id =ef.state_id left join city ff on pc.city_id=ff.city_id
left join membin_details mds on mds.participant_id =p.participant_id
where mds.status!='D' and P.participant_id =#{participantId}  and ( mds.bin_number=#{binNumber} or mds.acquirer_id=#{acqId})
</select>

<select id="getFileTypeList" resultType="AuditBatchLogDTO">
SELECT FILE_TYPE as fileType,file_desc as fileDesc FROM BATCH_MASTER WHERE REPORT_TYPE IS NULL OR
REPORT_TYPE = '';
</select>
<select id="getSettlementFileTypeList" resultType="String">
SELECT FILE_TYPE as fileType FROM BATCH_MASTER WHERE REPORT_TYPE=#{reportType}
</select>

<select id="getMemberList" resultType="CodeValueDTO">
SELECT PARTICIPANT_ID as code ,BANK_NAME as description  FROM PARTICIPANT
</select>

<select id="getBasePath" resultType="String">
select base_folder_path  as baseFolderPath from batch_master where file_type = #{name}
</select>

<insert id="insertAuditBatchLog">

insert into audit_batch_log(
participant_id, file_gen_date,
 file_type,report_type,report_name,file_path, status,created_by,
 created_on,participant_type
 )
 values(
 #{participantId},now(),#{fileType},
 #{reportType},#{reportName},#{filePath},#{status},#{createdBy},now(),#{participantType}
 )

</insert>


<select id="checkIfExistsInAudit" resultType="String">
select distinct report_name  as reportName from audit_batch_log 
where participant_id= #{participantId} and file_type = #{fileType} and report_type= #{reportType} 
and report_name in 
<foreach item='item' index='index' collection='reportName' open='(' separator=',' close=')'>
	#{item}
</foreach> 
</select>

<select id="getSettlementCycleList" resultType="String">
SELECT distinct cycle_number as cycleNum FROM settlement_cycle_config where is_active='true' order by cycle_number asc
</select>

<select id="getSettlementCycleListBasedOnProdCode" resultType="String">
SELECT distinct cycle_number as cycleNum FROM settlement_cycle_config where is_active='true' and product_id =#{productCode} order by cycle_number asc 
</select>

<select id="fetchFilePathList" resultType="AuditBatchLogDTO">
select distinct log_id as logId,participant_id as participantId,cycle_number as cycleNum,file_path as filePath,created_on as createdOn,report_name as reportName 
from audit_batch_log 
where log_id in
<foreach item='item' index='index' collection='idList' open='(' separator=',' close=')'>#{item}</foreach>
</select>

<update id="updateFileDelStatus">
update audit_batch_log set status=#{status} where log_id = #{logId}
</update>

<select id="getProductId" resultType="String">
select distinct product_id as productId from settlement_cycle_config 
</select>

<select id="getMemberListDoc" resultType="AuditBatchLogDTO">
select CONCAT (PARTICIPANT_ID,'-', bank_name) as label, participant_id as value from PARTICIPANT WHERE STATUS = 'A'
</select>

<select id="getReportStatusData" resultType="ReportStatusDTO">
select distinct on (report_name,participant_id,status,cycle_date,report_type) guid as guid,product_code as productCode, report_name as reportName,report_type as reportType,status as status,cycle_date as cycleDate,created_ts as createdTs,updated_ts as updatedTs,regen_flag as regenFlag,cycle_number as cycleNumber, pgp_status as pgpStatus,participant_id as participantId,gen_start_ts as genStartTs, gen_end_ts as genEndTs 
from product_report_status prs where prs.guid in
(
select  max(guid) FROM product_report_status where 
product_code =#{reportStatusDTO.productCode} and cycle_date=#{reportStatusDTO.cycleDate} and cycle_number=#{reportStatusDTO.cycleNumber}
<if test="isParticipant">and</if>
<if test="isParticipant"> participant_id=#{reportStatusDTO.participantId} </if>
<if test="isStatus">and</if>
<if test="isStatus"> status=#{reportStatusDTO.status} </if>
<if test="isReportType">and</if>
<if test="isReportType"> report_type=#{reportStatusDTO.reportType} </if>
group by participant_id,report_name,cycle_date,cycle_number,report_type  
) 
and prs.created_ts is NOT NULL and prs.participant_id is NOT NULL;

</select>

<select id="getStatusCount" resultType="ReportStatusDTO">
SELECT count(status), status as status FROM product_report_status where product_code=#{productCode} and cycle_date=#{cycleDate} and cycle_number=#{cycleNumber}  GROUP by status order by status
</select>


<select id="getLatestCycleDN" resultType="ReportStatusDTO">
select max(cycle_number) as cycleNumber,cycle_date as cycleDate from  product_report_status ts  where TO_DATE( ts.cycle_date, 'DDMMYY' ) in (select max(  TO_DATE( cycle_date, 'DDMMYY' )) as cycleDate 
FROM product_report_status
where product_code =#{productCode} and status =#{status}
) and ts.status=#{status} GROUP BY cycle_date
</select>
<select id="getStatusDataForProd" resultType="ReportStatusDTO">
SELECT m.product_code as productCode,m.report_name as reportName , m.report_type as reportType,m.status as status, m.cycle_date as cycleDate ,m.cycle_number as cycleNumber , m.regen_flag as regenFlag,
m.pgp_status as pgpStatus , m.participant_id as participantId, m.gen_start_ts as genStartTs,m.gen_end_ts as genEndTs
FROM  product_report_status m where  m.product_code =#{productCode} and m.status =#{status} and  m.cycle_date =#{cycleDate} and m.cycle_number = #{cycleNumber} and m.participant_id is not null

</select>

<select id="getCycleInfo" resultType="ReportStatusDTO">
select max(cycle_number) as cycleNumber,cycle_date as cycleDate from  product_report_status ts  where TO_DATE( ts.cycle_date, 'DDMMYY' ) in (select max(  TO_DATE( cycle_date, 'DDMMYY' )) as cycleDate 
FROM product_report_status
where product_code =#{productCode}) GROUP BY cycle_date
</select>


</mapper> 