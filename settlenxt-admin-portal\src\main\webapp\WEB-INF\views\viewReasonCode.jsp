<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script src="./static/js/validation/viewApproveBinExclConfig.js"
	type="text/javascript"></script>
<script src="./static/js/validation/viewBinExclRGCS.js"
	type="text/javascript"></script>
<script src="./static/js/validation/showReasonCodeMaster.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	
	<c:url value="getReasonCode" var="getReasonCode" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveReasonCode" modelAttribute="reasonCodeDto"
		action="${getReasonCode}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"></span><spring:message code="reasonCode.mainTab.title" /></span></strong>
					</div>

					<div class="panel-body">

						<input type="hidden" id="crtuser"
							value="${reasonCodeDto.createdBy}" />

						<table class="table table-striped infobold"
							style="font-size: 12px">
							<caption style="display:none;">REASON CODE</caption>
							<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"></span><spring:message code="reasonCode.viewscreen.title" /></span></strong>
										</div></td>
							
								</tr>
								
								<tr>
									<td><label><spring:message code="reasonCode.reasonCode" /><span style="color: red"></span></label></td>
									<td>${reasonCodeDto.reasonCode}</td>
									<td><label><spring:message code="reasonCode.reasonCodeDesc" /><span style="color: red"></span></label></td>
									<td>${reasonCodeDto.reasonCodeDesc}</td>
						
									<td><label><spring:message code="reasonCode.reasonCodeSubType" /></label></td>
									<td>${reasonCodeDto.reasonCodeSubType}</td>
								</tr>
								<tr>
									<td><label><spring:message code="reasonCode.reasonCodeSubTypeDesc" /></label></td>
									<td>${reasonCodeDto.reasonCodeSubTypeDesc}</td>
									<td><label><spring:message code="reasonCode.reasonType" /></label></td>
									<td>${reasonCodeDto.reasonType}</td>
									<td><label><spring:message code="reasonCode.status" /></label></td>
									<td>${reasonCodeDto.status}</td></tr>
				
							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center" >
									
								

									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/showReasonCodeMaster');"><spring:message code="budget.backBtn" /></button>
									<sec:authorize access="hasAuthority('Edit Reason Code')">
								
								<button type="button" class="btn btn-success"
										onclick="viewMcc('${reasonCodeDto.reasonCode}','V')">
										<spring:message code="sm.lbl.edit" /></button>
								
								</sec:authorize>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

