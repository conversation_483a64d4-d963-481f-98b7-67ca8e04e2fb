<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ page import="java.time.format.DateTimeFormatter"%>

<script type="text/javascript">
<c:if test="${showMainTab eq 'Y'}">
var actionColumnIndex = 6;
var firstColumnToBeSkippedInFilterAndSort=false;
</c:if>
<c:if test="${showApprovalTab eq 'Y'}">
<c:if test="${showCheckBox eq 'Y'}">
var actionColumnIndex = 12;
var firstColumnToBeSkippedInFilterAndSort=true;
</c:if>
<c:if test="${showCheckBox eq 'N'}">
var actionColumnIndex = 11;
var firstColumnToBeSkippedInFilterAndSort=false;
</c:if>
</c:if>
</script>



<div id="errorStatus2" class="alert alert-danger" role="alert"
	style="display: none"></div>
<div id="errorStatus4" class="alert alert-danger" role="alert"
	style="display: none"></div>

<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>
<script src="./static/js/validation/SearchActionCode.js"
	type="text/javascript"></script>


<script>
	var actionCodeListPendings = [];
	

	<c:if test="${not empty pendingActionCodeList}">
	<c:forEach items="${pendingActionCodeList}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	
	actionCodeListPendings.push(${operator.actionCodeId});
	
	</c:if>
	</c:forEach>
	</c:if>
	

	</script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />



<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>

<div class="modal fade" id="toggleModal" tabindex="-1" role="dialog"
	aria-labelledby="toggleApproveCappingAmount" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Action Code</label>
				<p id="actionCodeee" />
			</div>



			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveOrRejectBulk('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveOrRejectBulk('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>

<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showMainTab eq 'Y'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" onclick="submitForm('/showActionCode');" role="tab"
			data-toggle="tab"><span class="glyphicon glyphicon-list-alt">
		</span> <spring:message code="actioncode.MainTab.title" /></a>


		<c:choose>
			<c:when test="${showApprovalTab eq 'Y'}">
				<li role="presentation" class="active">
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>
		<a href="#home" role="tab"
			onclick="submitForm('/actionCodePendingForApproval');"
			data-toggle="tab"><span class="glyphicon glyphicon-ok"> </span> <spring:message
				code="ifsc.approvalTab.title" /></a>
	</ul>




	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">

		<div class="row">
		<div class="col-sm-12">
			<sec:authorize access="hasAuthority('Add Action Code')">
								<c:if test="${showAddButton eq 'Y'}">

									<a class="btn btn-success pull-right btn_align" href="#"
										onclick="submitForm('/addActionCode');"
										style="margin-top: -5px 0px 2px 0px;"><em class="glyphicon-plus"></em> <spring:message
											code="action.addActionCodeBtn" /> </a>

								</c:if>
							</sec:authorize>
							</div>
				<div class="col-sm-12">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="ifsc.clearFiltersBtn" />
					</button>


					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="excelExport"><spring:message code="ifsc.exportBtn" /> </a>

					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="csvExport"><spring:message code="ifsc.csvBtn" /> </a>

				</div>
			</div>


			<div class="row">
				<div class="col-sm-12">
					<div class="panel panel-default">
						<div class="panel-heading">
							<strong><span class="glyphicon glyphicon-th"></span> <span
								data-i18n="Data"><spring:message
										code="msg.lbl.actionCodeList" /></span></strong>

							
							<c:if test="${showApprovalTab eq 'Y'}">
								<sec:authorize access="hasAuthority('Approve Action Code')">


									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveOrRejectBulk('A','No')" id="submitButton"
										value="<spring:message code="am.lbl.Approve" />" />
									<input type="button"
										class="btn btn-success pull-right btn_align"
										onclick="ApproveOrRejectBulk('R','No')" id="submitButton"
										value="<spring:message code="am.lbl.Reject" />" />
								</sec:authorize>
							</c:if>
						</div>

						<c:if test="${not empty actionCodeList and showMainTab eq 'Y'}">
							<div class="panel-body">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width:150%;">
										<caption style="display:none;">actionCode</caption>
									
										<thead>
											<tr>
												<th scope = "col"><spring:message code="am.lbl.mti" /></th>

												<th scope = "col"><spring:message code="am.lbl.funcCode" /></th>
												<th scope = "col"><spring:message code="am.lbl.funcCodeDesc" /></th>
												<th scope = "col"><spring:message code="am.lbl.actionCode" /></th>
												<th scope = "col"><spring:message code="am.lbl.actionCodeDesc" /></th>
												<th scope = "col"><spring:message code="am.lbl.raisedBy" /></th>
											</tr>
										</thead>
										<tbody>

											<c:forEach var="ac" items="${actionCodeList}">

												<tr
													onclick="javascript:viewActionCodeInfo('${ac.actionCodeId}','/getActionCode')">
													<td>${ac.mti}</td>
													<td>${ac.funcCode}</td>
													<td>${ac.funcCodeDesc}</td>
													<td>${ac.actionCode}</td>
													<td>${ac.actionCodeDesc}</td>
													<td>${ac.raisedBy =='A' ? 'Acquirer' : 'Issuer'}</td>
												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</c:if>


						<c:if test="${empty actionCodeList and  showMainTab eq 'Y'}">
							<div class="table-responsive">
								<table id="tabnew" class="table table-striped table-bordered"
								style="width:150%;">
								<caption style="display:none;">actionCode</caption>
									<thead>
										<tr>
												<th scope = "col"><spring:message code="am.lbl.mti" /></th>
												<th scope = "col"><spring:message code="am.lbl.funcCode" /></th>
													<th scope = "col"><spring:message code="am.lbl.funcCodeDesc" /></th>
												<th scope = "col"><spring:message code="am.lbl.actionCode" /></th>
												<th scope = "col"><spring:message code="am.lbl.actionCodeDesc" /></th>
											
												<th scope = "col"><spring:message code="am.lbl.raisedBy" /></th>
										</tr>
									</thead>
									<tbody>
									</tbody>
								</table>
							</div>
						</c:if>






						<div class="row">
							<div class="col-md-12">
								<div class="panel panel-default">

									<div class="panel-body">

										<c:if
											test="${not empty pendingActionCodeList and showApprovalTab eq 'Y'}">
											<div class="table-responsive">
											<table id="tabnew"
													class="table table-striped table-bordered" style="width:150%;">
													<caption style="display:none;">actionCode</caption>
													<thead>
														<tr>
															<sec:authorize
																access="hasAuthority('Approve Action Code')">
																<th scope = "col"><input type=checkbox name='selectAllCheck'
																	id="selectAll" value='Hi' data-toggle="modal"
																	data-target="toggleModal"></input></th>
															</sec:authorize>
															
															<th scope = "col"><spring:message code="am.lbl.mti" /></th>
															<th scope = "col"><spring:message code="am.lbl.funcCode" /></th>
															<th scope = "col"><spring:message code="am.lbl.funcCodeDesc" /></th>
															<th scope = "col"><spring:message code="am.lbl.actionCode" /></th>
															<th scope = "col"><spring:message code="am.lbl.actionCodeDesc" /></th>
															
															<th scope = "col"><spring:message code="am.lbl.raisedBy" /></th>
															
															<th scope = "col"><spring:message code="am.lbl.approvalstatus" /></th>
															<th scope = "col"><spring:message code="sm.lbl.checkerComments" /></th>
															<th scope = "col"><spring:message code="am.lbl.createdBy" /></th>
															<th scope = "col"><spring:message code="am.lbl.createdDate" /></th>
															<th scope = "col"><spring:message code="am.lbl.lastUpdatedOn" /></th>


														</tr>
													</thead>
													<tbody>
														<c:forEach var="users" items="${pendingActionCodeList}">

															<c:if test="${users.requestState  eq 'P'}">
																<tr
																	onclick="javascript:viewActionCodeInfo('${users.actionCodeId}','/viewApproveActionCode')">
																	<sec:authorize
																		access="hasAuthority('Approve Action Code')">
																		<td onclick=event.stopPropagation()><input
																			type=checkbox name='type' id="selectSingle"
																			 value='${users.actionCodeId}'></input></td>
																	</sec:authorize>
															</c:if>
															<c:if test="${ users.requestState  eq 'R'}">
																<tr
																	onclick="javascript:viewRejActionCodeInfo('${users.actionCodeId}','/getRejActionCode')">
																	<sec:authorize
																		access="hasAuthority('Approve Action Code')">
																		<td><input type=checkbox name='types'
																			style="display: none;" value='${users.actionCodeId}'></input></td>
																	</sec:authorize>
															</c:if>
															<td>${users.mti}</td>

															<td>${users.funcCode}</td>
															<td>${users.funcCodeDesc}</td>
															<td>${users.actionCode}</td>
															<td>${users.actionCodeDesc}</td>
													
															<td>${users.raisedBy =='A' ? 'Acquirer' : 'Issuer'}</td>
															
															<td>${users.requestState =='P' ? 'Pending for Approval' : 'Rejected'}</td>
															
																<c:choose>
															<c:when test="${users.requestState eq 'R' }">

																<td>${users.checkerComments}</td>

															</c:when>
															<c:otherwise>

																<td></td>


															</c:otherwise>
														</c:choose>
															
															
															<td>${users.createdBy}</td>
															<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
																	value="${users.createdOn}" /></td>
															<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
																	value="${users.lastUpdatedOn}" /></td>
															</tr>
														</c:forEach>
													</tbody>
												</table>
											</div>
										</c:if>

										<c:if
											test="${empty pendingActionCodeList and showApprovalTab eq 'Y'}">
											<div class="table-responsive">
												<table id="tabnew"
													class="table table-striped table-bordered" style="width:100%;">
													<caption style="display:none;">actionCode</caption>
													<thead>
														<tr>
															<th scope = "col"><spring:message code="am.lbl.mti" /></th>
															<th scope = "col"><spring:message code="am.lbl.funcCode" /></th>
															<th scope = "col"><spring:message code="am.lbl.actionCode" /></th>
															<th scope = "col"><spring:message code="am.lbl.actionCodeDesc" /></th>
															<th scope = "col"><spring:message code="am.lbl.funcCodeDesc" /></th>
															<th scope = "col"><spring:message code="am.lbl.raisedBy" /></th>
															
															<th scope = "col"><spring:message code="am.lbl.approvalstatus" /></th>
															<th scope = "col"><spring:message code="sm.lbl.checkerComments" /></th>
															<th scope = "col"><spring:message code="am.lbl.createdBy" /></th>
															<th scope = "col"><spring:message code="am.lbl.createdDate" /></th>
															<th scope = "col"><spring:message code="am.lbl.lastUpdatedOn" /></th>
														</tr>
													</thead>
													<tbody>
													</tbody>
												</table>
											</div>
										</c:if>
									</div>
								</div>
							</div>
						</div>







					</div>
				</div>
			</div>



		</div>

	</div>







</div>