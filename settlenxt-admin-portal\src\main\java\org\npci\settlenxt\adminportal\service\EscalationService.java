
package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.portal.common.dto.BaseEscalationDTO;
import org.npci.settlenxt.portal.common.service.BaseEscalationService;

public interface EscalationService extends BaseEscalationService {

	 String updateEscalation(BaseEscalationDTO escalationDTO);

	 String approveOrRejectEscalation(String memberId, String status, String remarks);

	 List<BaseEscalationDTO> discardEscalationInfo(String memberId);

	 BaseEscalationDTO updateApproveOrRejectBulk(String[] idArray, String status, String remarks);

	 List<BaseEscalationDTO> getMemberListForReject();

	 List<BaseEscalationDTO> getContactListStg(String pId);

}
