<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript">
var rebateValidationMessages = {};
rebateValidationMessages['vCardType'] = "<spring:message code='rebate.cardType.validation.msg' javaScriptEscape='true' />";
rebateValidationMessages['VFinancialYear']= "<spring:message code="rebate.financialYear.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['VFeatureOrBaseFee']= "<spring:message code="rebate.featureOrBaseFee.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['VOperatorIndi']= "<spring:message code="rebate.operatorIndi.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vNewCardCount1']= "<spring:message code="rebate.newCardCount1.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vNewCardCount2']= "<spring:message code="rebate.newCardCount2.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['vRebatePercentage']= "<spring:message code="rebate.rebatePercentage.validation.msg" javaScriptEscape='true' />";
rebateValidationMessages['SelectedCard']= "<spring:message code="rebate.selectedCard.validation.msg" javaScriptEscape='true' />";

</script>
<script type="text/javascript"
	src="./static/js/validation/mcpr/AddRebateRGCS.js"></script>
	

<div class="row">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="rebate.listscreen.title" /></span></strong>
		</div>
		<div id="errvErrorInfo" class="error">
							<span for="ErrorInfo" class="error"><form:errors
									path="ErrorInfo" /></span>
						</div>

		<c:if test="${not empty addRebate}">
			<c:url value="addRebate" var="submitRebateDetails" />
		</c:if>
		<c:if test="${not empty editRebate}">
			<c:url value="updateRebate" var="submitRebateDetails" />
		</c:if>
		
		<div class="panel-body">
			<form:form onsubmit="return encodeForm(this);" method="POST"
				id="addEditRebate" modelAttribute="rebateVO"
				action="${submitRebateDetails}" autocomplete="off">
				<br />
				<form:hidden path="rebateID" id="vRebateID" name="vRebateID" />
				<form:hidden path="cardType" id="vhcardType" />

				<c:if test="${showbutton ne 'SAVE'}">
					<table class="table table-striped" style="font-size: 12px">
					<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
									<tbody>
						<tr>
							<td> 								
							<label><spring:message code="rebate.cardType" /><span style="color: red">*</span></label></td>
							<td>	<form:select path="cardType" id="vCardType" name="VCardType"
								maxlength="10"
								cssClass="form-control medantory">
									<form:option value="SELECT" label="SELECT" />
									<form:options items="${cardTypeList}"  itemValue="code" itemLabel="description"/>
								</form:select>
								<div id="errvCardType" class="error">
								<span for="VCardType" class="error"><form:errors
										id="VCardType" /></span>
							</div>

							</td>
							<td> 								
							<label><spring:message code="rebate.financialYear" /><span style="color: red">*</span></label></td>
							<td>	<form:select path="financialYear" id="VFinancialYear" name="VFinancialYear"
								maxlength="10"
								cssClass="form-control medantory">
								<form:option value="SELECT" lable="SELECT" />
								<form:option value="Present" lable="Present" />
								<form:option value="Previous" lable="Previous" />
								</form:select><div id="errVFinancialYear" class="error">
								<span for="VFinancialYear" class="error"><form:errors
										id="VFinancialYear" /></span>
							</div>
							</td>
						</tr>
						<tr>
							<td> 								
							<label><spring:message code="rebate.featureOrBaseFee" /><span style="color: red">*</span></label></td>
							<td>	<form:select path="featureOrBaseFee" id="VFeatureOrBaseFee" name="VFeatureOrBaseFee"
								maxlength="10"
								cssClass="form-control medantory">
								<form:option value="SELECT" lable="SELECT" />
								<form:option value="Feature Fee" lable="Feature Fee" />
								<form:option value="Base Fee" lable="Base Fee" />
								</form:select><div id="errVFeatureOrBaseFee" class="error">
								<span for="VFeatureOrBaseFee" class="error"><form:errors
										id="VFeatureOrBaseFee" /></span>
							</div>
							</td>
							<td> 								
							<label><spring:message code="rebate.operationalIndi" /><span style="color: red">*</span></label></td>
							<td>	<form:select path="operatorIndi" id="VOperatorIndi" name="VOperatorIndi"
								maxlength="10"
								cssClass="form-control medantory" onChange="disableToValue()">
								<form:option value="SELECT" label="SELECT" />
								<form:option value="=" label="=" />
								<form:option value=">" label=">" />
								<form:option value="<" label="<" />
								<form:option value=">=" label=">=" />
								<form:option value="<=" label="<=" />
								<form:option value="Between" label="Between" />
								</form:select><div id="errVOperatorIndi" class="error">
								<span for="VOperatorIndi" class="error"><form:errors
										id="VOperatorIndi" /></span>
							</div>
							</td>
						</tr>
						<tr>
							<td> 								
							<label><spring:message code="rebate.newCardCount1" /><span style="color: red">*</span></label></td>
							<td>	<form:input path="newCardCount1" id="vNewCardCount1" name="VNewCardCount1"
									maxlength="30" cssClass="form-control medantory" class="number-only" />
									<div id="errvNewCardCount1" class="error">
								<span for="vNewCardCount1" class="error"><form:errors
										id="vNewCardCount1" /></span>
							</div>
							</td>
							<td> 								
							<label><spring:message code="rebate.newCardCount2" /><span style="color: red">*</span></label></td>
							<td>	<form:input path="newCardCount2" id="vNewCardCount2" name="VNewCardCount2"
									maxlength="30" cssClass="form-control medantory" />
									<div id="errvNewCardCount2" class="error">
								<span for="vNewCardCount2" class="error"><form:errors
										id="vNewCardCount2" /></span></div>
							</td>
						</tr>
						<tr>
							<td> 								
							<label><spring:message code="rebate.rebatePercentage" /><span style="color: red">*</span></label></td>
							<td colspan=3>	<form:input path="rebatePercentage" id="vRebatePercentage" name="VRebatePercentage"
									maxlength="30" cssClass="form-control medantory" /><div id="errvRebatePercentage" class="error">
								<span for="vRebatePercentage" class="error"><form:errors
										id="vRebatePercentage" /></span></div>
							</td>
							
						</tr>
						</tbody>
					</table>
				</c:if>
				</form:form>
				</div>
				<c:if test="${showbutton eq 'SAVE'}">
					<table class="table table-striped" style="font-size: 12px">
						<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
									<tbody>
							<tr>
								<td><label><spring:message code="rebate.cardType" /><span style="color: red"></span></label></td>
								<td>${rebateVO.cardTypeName }</td>
								<td><label><spring:message code="rebate.financialYear" /><span
										style="color: red"></span></label></td>
								<td>${rebateVO.financialYear }</td>
								</tr>
								<tr>
								<td><label><spring:message code="rebate.featureOrBaseFee" /><span style="color: red"></span></label></td>
								<td>${rebateVO.featureOrBaseFee}</td>
								<td><label><spring:message code="rebate.operationalIndi" /><span style="color: red"></span></label></td>
								<td>${rebateVO.operatorIndi}</td>
								</tr>
								<tr>
								<td><label><spring:message code="rebate.newCardCount1" /><span style="color: red"></span></label></td>
								<td>${rebateVO.newCardCount1}</td>
								<td><label><spring:message code="rebate.newCardCount2" /><span style="color: red"></span></label></td>
								<td>${rebateVO.newCardCount2}</td>
								</tr>
								<tr>
								<td><label><spring:message code="rebate.rebatePercentage" /><span style="color: red"></span></label></td>
								<td>${rebateVO.rebatePercentage}</td>
								<td><label><spring:message code="rebate.rebateStatus" /><span style="color: red"></span></label></td>
								<td>${rebateVO.requestStateDiscription}</td>
							</tr>

						</tbody>
					</table>

				</c:if>
							

				<div class="row">
					<input type="hidden" id="changeFlag" /> <br />
					<div class="row"></div>

				</div>
						<c:if test="${showbutton ne 'SAVE'}">
										
						<div class="form-group clearfix" style="padding-top: 20px">

							<div class="col-xs-6">

								<div class="panel panel-default">
									<div class="panel-heading">
										<strong><span class="glyphicon glyphicon-th"></span>
											<span data-i18n="Data"><spring:message code="rebate.availableCardVariant" /></span></strong>
									<div style="float:right;">
									<a data-toggle="tooltip" title="select all" onclick="selectAllFunctionlities()" href="#/">
									<img src="./static/images/selectall.png" alt="selectall" height="15px"></a>
									</div>
									</div>
									
									<div >

										<table id="tabnew" class="table table-striped table-bordered" style="width:100%;">
												<caption style="display:none;">Rebate</caption>
												<tr>
													<th scope="col"><spring:message code="rebate.cardVariantName" /></th>
													<th scope="col"><spring:message code="rebate.add" /></th>
												</tr>
											<tbody id="optionList">
												<c:if test="${not empty cardVariantList}">
													<c:forEach items="${cardVariantList}"
														var="functionality">
															<tr class="optionRoles"  id="option${functionality.cardVariantId}">
																<td>${functionality.cardVariantName}</td>
																<td><em
																class="glyphicon glyphicon-circle-arrow-right"
																onclick="addToAssignedList('${functionality.cardVariantId}','${functionality.cardVariantName}')">
																</em></td>
															</tr>
														
													</c:forEach>
												</c:if>

											</tbody>
										</table>



										

									</div>
								</div>

							</div>
							
							<div class="col-xs-6">

								<div class="panel panel-default">
									<div class="panel-heading">
										<strong><span class="glyphicon glyphicon-th"></span>
											<span data-i18n="Data"><spring:message code="rebate.selectedCardVariant" /></span></strong>
									<c:if test="${not empty showbutton}">
										<div style="float:right;">
											<a data-toggle="tooltip" title="remove all"
												onclick="removeAllFunctionalities();" href="#/"><img
												src="./static/images/removeall.png" alt="removeall"
												height="15px"></a>
										</div>
										</c:if>
									
									</div>
									<div class="table-responsive">

										<table id="tabnew1" class="table table-striped table-bordered" style="width:100%;">
												<caption style="display:none;">Rebate</caption>
												<tr>
													<th scope="col"><spring:message code="rebate.cardVariantName" /></th>
													<th scope="col"><spring:message code="rebate.remove" /></th>
												</tr>
											<tbody  id="assignedList">
												
											</tbody>
										</table>
										<div id="errSelectedCard" class="error">
										<span for="SelectedCard" class="error"><form:errors
										id="SelectedCard" /></span>					

									</div>
								</div>

							</div>
							</div>
							</div>

							
						</c:if>							
						<c:if test="${showbutton eq 'SAVE'}">
									
						<div class="form-group clearfix" style="padding-top: 20px">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span>
									<span data-i18n="Data"><spring:message code="rebate.selectedCardVariant" /></span></strong>
							</div>
							<div class="panel-body">
								<table class="table table-striped" style="font-size: 12px">
									<caption style="display:none;">Rebate</caption>
								<thead style="display:none;"><th scope="col"></th></thead>
									<tbody>
										<c:if test="${not empty cardVariantList}">
										<c:forEach items="${cardVariantList}" var="cardVariant">
										<tr class="selectedRoles1">
										<td>${cardVariant.cardVariantName}</td>
										</tr>
										</c:forEach>
										</c:if>
									</tbody>
								</table>
							</div>
						</div>
						</c:if>							
						

				<div class="col-sm-12 bottom_space">
					<hr />
					<div style="text-align:center">

						<c:if test="${showbutton ne 'SAVE'}">
									<button type="button" value="Submit" class="btn btn-success" onclick="resetAction();">
										    <spring:message code="am.lbl.reset" /> </button>
							<button type="button" class="btn btn-success"
								onclick="viewRebateAdd('','A','${originPage}');"><spring:message code="rebate.submit" /></button>
						</c:if>							
						

							<button type="button" class="btn btn-danger"
								onclick="homeRebate('N','/previewHoliday','${originPage}');"><spring:message code="rebate.back" /></button>
	
					</div>

				</div>
			
		</div>
	</div>

