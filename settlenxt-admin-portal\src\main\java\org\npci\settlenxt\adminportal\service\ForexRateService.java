package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.ForexRateDTO;
import org.springframework.stereotype.Service;

@Service
public interface ForexRateService {
	List<ForexRateDTO> getPendingForexRateList();

	ForexRateDTO viewForexRateTab(int forexRateId);

	ForexRateDTO getForexRateStgInfo(String forexRateId);

	ForexRateDTO addEditForexRate(ForexRateDTO forexRateDto);

	ForexRateDTO approveOrRejectForexRate(int forexRateId, String status, String remarks);

	ForexRateDTO getForexRateStg(int forexRateId);

	ForexRateDTO getForexRateForEdit(int forexRateId);

	ForexRateDTO discardForexRate(int forexRateId);

	String approveOrRejectForexRateBulk(String bulkApprovalReferenceNoList, String status, String remarks);

	List<ForexRateDTO> getforexRateList(ForexRateDTO forexRateDto);

	void addForexRate(List<ForexRateDTO> forexRates);

	String getConversionRate(String networkId, String code, String description);

}
