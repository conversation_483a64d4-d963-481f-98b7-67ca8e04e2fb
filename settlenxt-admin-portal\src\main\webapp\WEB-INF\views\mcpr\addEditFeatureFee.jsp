<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/addEditFeatureFee.js"
	type="text/javascript"></script>
<script type="text/javascript">
	var featureFeeValidationMessages = {};
	featureFeeValidationMessages['cardType'] = "<spring:message code='featureFee.cardType.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['cardVariant'] = "<spring:message code='featureFee.cardVariant.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['feature'] = "<spring:message code='featureFee.feature.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['details'] = "<spring:message code='featureFee.details.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['featureFee'] = "<spring:message code='featureFee.featureFee.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['fromYear'] = "<spring:message code='featureFee.fromYear.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['fromMonth'] = "<spring:message code='featureFee.fromMonth.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['toYear'] = "<spring:message code='featureFee.toYear.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['toMonth'] = "<spring:message code='featureFee.toMonth.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['fromToDateValidation'] = "<spring:message code='featureFee.fromToDateValidation.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['toFromDateValidation'] = "<spring:message code='featureFee.toFromDateValidation.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['fromToSysDateValidation'] = "<spring:message code='featureFee.fromToSysDateValidation.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['pastYearValidation'] = "<spring:message code='featureFee.pastYearValidation.validation.msg' javaScriptEscape='true' />";
	featureFeeValidationMessages['pastMonthValidation'] = "<spring:message code='featureFee.pastMonthValidation.validation.msg' javaScriptEscape='true' />";
</script>

<div class="panel panel-default no_margin">
	<div class="panel-heading clearfix">
		<strong><span class="glyphicon glyphicon-th"></span> <span
			data-i18n="Data"> <c:if test="${not empty addFeatureFee}">
					<spring:message code="featureFee.addscreen.title" />
				</c:if> <c:if test="${not empty editFeatureFee}">
					<spring:message code="featureFee.editscreen.title" />
				</c:if></span> </strong>

	</div>
	<c:if test="${not empty addFeatureFee}">
		<c:url value="addFeatureFee" var="submitCardDetails" />
	</c:if>
	<c:if test="${not empty editFeatureFee}">
		<c:url value="updateFeatureFee" var="submitCardDetails" />
	</c:if>
	<div class="panel-body">
		<form:form onsubmit="return encodeForm(this);" method="POST"
			id="addEditFeatureFee" modelAttribute="featureFeeDto"
			action="${submitCardDetails}" autocomplete="off">
			<br />
			<form:hidden path="cardConfigId" id="cardConfigId"
				name="cardConfigId" value="${featureFeeDto.cardConfigId}" />
			<input type="hidden" id="cardId"
				value="${featureFeeDto.cardConfigId}" />
			<input type="hidden" id="hparentPage" value="${parentPage}" />

			<c:if test="${not empty showbutton}">

				<div class="row">
					<div class="col-sm-12">
						<div class="col-sm-3">
							<div class="form-group">

								<label><spring:message code="featureFee.cardType" /><span
									style="color: red">*</span></label>
								<c:if test="${not empty addFeatureFee}">
									<form:select path="cardType" id="cardType" name="cardType"
										maxlength="10" value="${featureFeeDto.cardType}"
										cssClass="form-control medantory">
										<form:option value="" label="SELECT" />
										<form:options items="${cardTypeList}" itemValue="code"
											itemLabel="description" />
									</form:select>

									<div id="errcardType">
										<span for="cardType" class="error"><form:errors
												path="cardType" /> </span>
									</div>
								</c:if>
								<c:if test="${not empty editFeatureFee}">
									<form:hidden path="cardType" id="cardType" name="cardType"
										value="${featureFeeDto.cardType}" />
									<form:input path="cardTypeName" id="cardTypeName"
										name="cardTypeName" maxlength="10"
										value="${featureFeeDto.cardTypeName}" readonly="true"
										cssClass="form-control medantory" />
								</c:if>
							</div>
						</div>
						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="featureFee.cardVariant" /><span
									style="color: red">*</span></label>
								<c:if test="${not empty addFeatureFee}">
									<form:select path="cardVariant" id="cardVariant"
										name="cardVariant" maxlength="10"
										value="${featureFeeDto.cardVariant}"
										cssClass="form-control medantory">
										<form:option value="" label="SELECT" />
										<form:options items="${cardVariantList}" itemValue="code"
											itemLabel="description" />
									</form:select>

									<div id="errcardVariant">
										<span for="cardVariant" class="error"><form:errors
												path="cardVariant" /> </span>
									</div>
								</c:if>
								<c:if test="${not empty editFeatureFee}">
									<form:hidden path="cardVariant" id="cardVariant"
										name="cardVariant" value="${featureFeeDto.cardVariant}" />
									<form:input path="cardVariantName" id="cardVariantName"
										value="${featureFeeDto.cardVariantName}"
										name="cardVariantName" readonly="true" maxlength="10"
										cssClass="form-control medantory" />
								</c:if>
							</div>
						</div>
						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="featureFee.featureFee" /><span
									style="color: red">*</span></label>
								<form:input path="featureFee" id="featureFee" name="featureFee"
									cssClass="form-control medantory"
									value="${featureFeeDto.featureFee}" />

								<div id="errfeatureFee">
									<span for="featureFee" class="error"><form:errors
											path="featureFee" /> </span>
								</div>
							</div>
						</div>
						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="featureFee.feature" /><span
									style="color: red">*</span></label>
								<c:if test="${not empty addFeatureFee}">
									<form:input path="feature" id="feature" name="feature"
										cssClass="form-control medantory"
										value="${featureFeeDto.feature}" />
								</c:if>
								<c:if test="${not empty editFeatureFee}">
									<form:input path="feature" id="feature"
										value="${featureFeeDto.feature}" name="feature"
										readonly="true" maxlength="10"
										cssClass="form-control medantory" />
								</c:if>

								<div id="errfeature">
									<span for="feature" class="error"><form:errors
											path="feature" /> </span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-12">

						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="featureFee.details" /><span
									style="color: red">*</span></label>
								<form:input path="details" id="details" name="details"
									cssClass="form-control medantory"
									value="${featureFeeDto.details}" />

								<div id="errdetails">
									<span for="details" class="error"><form:errors
											path="details" /> </span>
								</div>
							</div>
						</div>
						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="featureFee.fromDate" /><span
									style="color: red">*</span></label>
								<form:select path="fromMonth" id="fromMonth" name="fromMonth"
									maxlength="10" value="${featureFeeDto.fromMonth}"
									cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${beginingQuarterMonths}" itemValue="code"
										itemLabel="description" />
								</form:select>
								<div id="errfromMonth">
									<span for="fromMonth" class="error"><form:errors
											path="fromMonth" /> </span>
								</div>
							</div>
						</div>

						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="featureFee.year" /><span
									style="color: red">*</span></label>
								<form:select path="fromYear" id="fromYear" name="fromYear"
									maxlength="10" value="${featureFeeDto.fromYear}"
									cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${baseFeeYears}" itemValue="code"
										itemLabel="description" />
								</form:select>
								<div id="errfromYear">
									<span for="fromYear" class="error"><form:errors
											path="fromYear" /> </span>
								</div>
							</div>
						</div>
						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="featureFee.toDate" /><span
									style="color: red">*</span></label>
								<form:select path="toMonth" id="toMonth" name="toMonth"
									maxlength="10" value="${featureFeeDto.toMonth}"
									cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${endingQuarterMonths}" itemValue="code"
										itemLabel="description" />
								</form:select>
								<div id="errtoMonth">
									<span for="toMonth" class="error"><form:errors
											path="toMonth" /> </span>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-sm-12"></div>
				</div>

				<div class="row">
					<div class="col-sm-12">

						<div class="col-sm-3">
							<div class="form-group">
								<label><spring:message code="featureFee.year" /><span
									style="color: red">*</span></label>
								<form:select path="toYear" id="toYear" name="toYear"
									maxlength="10" value="${featureFeeDto.toYear}"
									cssClass="form-control medantory">
									<form:option value="0" label="SELECT" />
									<form:options items="${baseFeeYears}" itemValue="code"
										itemLabel="description" />
								</form:select>
								<div id="errtoYear">
									<span for="toYear" class="error"><form:errors
											path="toYear" /> </span>
								</div>
							</div>
						</div>
					</div>
				</div>

			</c:if>
			<c:if test="${empty showbutton}">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default no_margin">
							<div class="panel-body">
								<table class="table table-striped" style="font-size: 12px">
									<caption style="display: none;">Feature Fee</caption>
									<thead style="display: none;">
										<th scope="col"></th>
									</thead>
									<tbody>
										<tr>
											<td><label><spring:message
														code="featureFee.cardType" /></label></td>
											<td id="cardType">${featureFeeDto.cardTypeName }</td>
											<td><label><spring:message
														code="featureFee.cardVariant" /></label></td>
											<td id="cardVariant">${featureFeeDto.cardVariantName }</td>
											<td></td>
											<td></td>
										</tr>
										<tr>
											<td><label><spring:message
														code="featureFee.featureFee" /></label></td>
											<td id="featureFee">${featureFeeDto.featureFee }</td>
											<td><label><spring:message
														code="featureFee.feature" /></label></td>
											<td id="feature">${featureFeeDto.feature }</td>
											<td><label><spring:message
														code="featureFee.details" /></label></td>
											<td id="details">${featureFeeDto.details }</td>
										</tr>
										<tr>
											<td><label><spring:message
														code="featureFee.fromDate" /></label></td>
											<td id="fromDate">${featureFeeDto.fromDate }</td>
											<td><label><spring:message
														code="featureFee.toDate" /></label></td>
											<td id="toDate">${featureFeeDto.toDate }</td>
											<td></td>
											<td></td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>

			</c:if>

			<div class="col-sm-12 bottom_space">

				<div style="text-align: center">
					<c:if test="${not empty addFeatureFee}">
						<c:if test="${not empty showbutton}">
							<button type="button" value="Submit" class="btn btn-success"
								onclick="resetAction();">
								<spring:message code="am.lbl.reset" />
							</button>
						</c:if>
						<c:if test="${not empty showbutton}">
							<input type="button" class="btn btn-success"
								onclick="viewFeatureFeeAdd('/addFeatureFee','A')"
								id="submitButton"
								value="<spring:message code="featureFee.submitBtn" />" />
						</c:if>
					</c:if>
					<sec:authorize access="hasAuthority('Edit Feature Fee')">
						<c:if test="${not empty editFeatureFee}">
							<c:if test="${not empty showbutton}">
								<input type="button" class="btn btn-success" id="bEdit"
									onclick="viewFeatureFeeAdd('/updateFeatureFee','E')"
									id="submitButton"
									value="<spring:message code="featureFee.submitBtn" />" />
							</c:if>
						</c:if>

						<c:if
							test="${featureFeeDto.requestState  eq 'R' and not empty showbutton}">
							<input name="discardButton" type="button" class="btn btn-danger"
								id="approveRole" value="Discard"
								onclick="discard('/discardFeatureFee','${featureFeeDto.cardConfigId}');" />

							<button type="button" class="btn btn-danger"
								onclick="userAction('N','/featureFeePendingForApproval');">
								<spring:message code="featureFee.backBtn" />
							</button>
						</c:if>
					</sec:authorize>

					<c:if test="${featureFeeDto.requestState  ne 'R'}">
						<c:if test="${parentPage ne 'approvalTab'}">
							<button type="button" class="btn btn-danger"
								onclick="userAction('N','/featureFeeConfiguration');">
								<spring:message code="featureFee.backBtn" />
							</button>
						</c:if>
						<c:if test="${parentPage  eq 'approvalTab'}">
							<button type="button" class="btn btn-danger"
								onclick="userAction('N','/featureFeePendingForApproval');">
								<spring:message code="featureFee.backBtn" />
							</button>
						</c:if>

					</c:if>

				</div>
			</div>
		</form:form>
	</div>
</div>
