package org.npci.settlenxt.adminportal.controllers;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.DisputeFeeRuleDTO;
import org.npci.settlenxt.adminportal.dto.DisputeTransitionDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.DisputeService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.ActionCodeDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

@Controller
public class DisputeController extends BaseController {

	private static final String SHOW_DISPUTE_FEE_RULE = "showDisputeFeeRules";
	private static final String ADD_EDIT_DISPUTE_FEE_RULE = "addEditDisputeFeeRule";
	private static final String VIEW_DISPUTE_FEE_RULE = "viewDisputeFeeRule";
	private static final String VIEW_APPROVE_DISPUTE_FEE_RULE = "viewApproveDisputeFeeRule";
	private static final String E00001="E00001";
	@Autowired
	private DisputeService disputeService;

	@Autowired
	private BaseLookupService lookupService;

	@Autowired
	private SessionDTO sessionDTO;

	@Autowired
	private MessageSource messageSource;

	@PostMapping("/showDisputeFeeRule")
	public String showDisputeFeeRule(Model model) {
		try {
			model.addAttribute("showDisputeFeeRule", BaseCommonConstants.YES);
			List<DisputeFeeRuleDTO> disputeFeeRuleList = disputeService.disputeFeeRuleList();
			model.addAttribute(CommonConstants.DISPUTE_FEE_RULE_LIST, disputeFeeRuleList);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, SHOW_DISPUTE_FEE_RULE, ex);
		}
		model.addAttribute(CommonConstants.ADD_DISPUTE_FEE_RULE, BaseCommonConstants.TRANSACT_YES);
		return getView(model, SHOW_DISPUTE_FEE_RULE);

	}

	@PostMapping("/createDisputeFeeRule")
	@PreAuthorize("hasAuthority('Add Dispute Fee Rule')")
	public String createDisputeFeeRule(Model model) {
		try {
			List<ActionCodeDTO> actionCodeList = disputeService.getActionCodeList();
			List<CodeValueDTO> relOpList = lookupService.getLookupData(CommonConstants.DISPUTE_RELATIONAL_OP);
			List<CodeValueDTO> relCompOpList = lookupService.getLookupData(CommonConstants.DISPUTE_RELATIONAL_OP_COMP);
			List<CodeValueDTO> dispFeeList = lookupService.getLookupData(CommonConstants.DISPUTE_FEE_TYPE);
			List<CodeValueDTO> fieldNameList = lookupService.getLookupData(CommonConstants.DISPUTE_FIELD_NAME);
			List<CodeValueDTO> fieldOperatorList = lookupService.getLookupData(CommonConstants.DISPUTE_FIELD_OP);
			DisputeFeeRuleDTO disputeFeeRuleDto = new DisputeFeeRuleDTO();
			model.addAttribute(CommonConstants.DISPUTE_FEE_RULE_DTO, disputeFeeRuleDto);
			model.addAttribute(CommonConstants.ACTION_CODE_LIST, actionCodeList);
			model.addAttribute(CommonConstants.REL_OPERATOR_LIST, relOpList);
			model.addAttribute(CommonConstants.REL_OPERATOR_LIST_COMP, relCompOpList);
			model.addAttribute(CommonConstants.DISP_FEE_LIST, dispFeeList);
			model.addAttribute(CommonConstants.DISP_FIELD_NAME_LIST, fieldNameList);
			model.addAttribute(CommonConstants.DISP_FIELD_OP_LIST, fieldOperatorList);
			model.addAttribute(CommonConstants.ADD_DISPUTE_FEE_RULE, BaseCommonConstants.TRANSACT_YES);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_BUDGET);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, ADD_EDIT_DISPUTE_FEE_RULE, ex);
		}
		return getView(model, ADD_EDIT_DISPUTE_FEE_RULE);
	}

	@PostMapping("/addDisputeFeeRule")
	@PreAuthorize("hasAuthority('Add Dispute Fee Rule')")
	public ResponseEntity<Object> addRejectReasonCode(Model model,
			@RequestBody List<DisputeFeeRuleDTO> disputeFeeRuleDtoList, HttpServletResponse response) {
		List<DisputeFeeRuleDTO> disputeFeeRuleList = disputeFeeRuleDtoList.stream()
				.sorted((d1, d2) -> d1.getLogicalFeeCode().compareTo(d2.getLogicalFeeCode()))
				.collect(Collectors.toList());
		String result = disputeService.addDisputeFeeRule(disputeFeeRuleList);

		JsonObject jsonResponse = new JsonObject();
		if (StringUtils.equals(result, "Success")) {
			jsonResponse.addProperty("status", BaseCommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty("status", BaseCommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/getDisputeFeeRule")
	@PreAuthorize("hasAuthority('View Dispute Fee Rule')")
	public String getDisputeFeeRule(@RequestParam("seqId") int seqId, Model model) {
		DisputeFeeRuleDTO disputeFeeRuleDtoInfo = disputeService.getDisputeFeeRuleInfo(seqId);
		model.addAttribute(CommonConstants.DISPUTE_FEE_RULE_INFO, disputeFeeRuleDtoInfo);
		return getView(model, VIEW_DISPUTE_FEE_RULE);
	}

	@PostMapping("/editDisputeFeeRule")
	@PreAuthorize("hasAuthority('Edit Dispute Fee Rule')")
	public String editDisputeFeeRule(@RequestParam("seqId") int seqId, Model model) {
		DisputeFeeRuleDTO disputeFeeRuleDto = new DisputeFeeRuleDTO();
		try {
			disputeFeeRuleDto = disputeService.getDisputeFeeRuleInfo(seqId);
			String[] fieldName = disputeFeeRuleDto.getFieldName().split(",");
			if (fieldName.length > 1) {
				disputeFeeRuleDto.setFieldName1(fieldName[0]);
				disputeFeeRuleDto.setFieldName2(fieldName[1]);
			} else {
				disputeFeeRuleDto.setFieldName1(fieldName[0]);
			}

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_DISPUTE_FEE_RULE, ex);
		}
		List<ActionCodeDTO> actionCodeList = disputeService.getActionCodeList();
		List<CodeValueDTO> relOpList = lookupService.getLookupData(CommonConstants.DISPUTE_RELATIONAL_OP);
		List<CodeValueDTO> relCompOpList = lookupService.getLookupData(CommonConstants.DISPUTE_RELATIONAL_OP_COMP);

		List<CodeValueDTO> dispFeeList = lookupService.getLookupData(CommonConstants.DISPUTE_FEE_TYPE);
		List<CodeValueDTO> fieldNameList = lookupService.getLookupData(CommonConstants.DISPUTE_FIELD_NAME);
		List<CodeValueDTO> fieldOperatorList = lookupService.getLookupData(CommonConstants.DISPUTE_FIELD_OP);
		model.addAttribute(CommonConstants.ACTION_CODE_LIST, actionCodeList);
		model.addAttribute(CommonConstants.REL_OPERATOR_LIST, relOpList);
		model.addAttribute(CommonConstants.REL_OPERATOR_LIST_COMP, relCompOpList);
		model.addAttribute(CommonConstants.DISP_FEE_LIST, dispFeeList);
		model.addAttribute(CommonConstants.DISP_FIELD_NAME_LIST, fieldNameList);
		model.addAttribute(CommonConstants.DISP_FIELD_OP_LIST, fieldOperatorList);
		model.addAttribute(CommonConstants.EDIT_DISPUTE_FEE_RULE, BaseCommonConstants.TRANSACT_YES);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, BaseCommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.DISPUTE_FEE_RULE_DTO, disputeFeeRuleDto);
		return getView(model, ADD_EDIT_DISPUTE_FEE_RULE);
	}

	@PostMapping("/updateDisputeFeeRule")
	@PreAuthorize("hasAuthority('Edit Dispute Fee Rule')")
	public String updateDisputeFeeRule(@ModelAttribute("disputeFeeRuleDto") DisputeFeeRuleDTO disputeFeeRuleDto,
			Model model) {
		DisputeFeeRuleDTO disputeFeeRuleDtoMain = new DisputeFeeRuleDTO();
		try {
			disputeFeeRuleDtoMain = disputeService.getDisputeFeeRuleInfo(disputeFeeRuleDto.getSeqId());
			disputeFeeRuleDto.setLogicalOperator(disputeFeeRuleDtoMain.getLogicalOperator());
			disputeFeeRuleDto.setStatus(disputeFeeRuleDtoMain.getStatus());
			disputeFeeRuleDto.setCreatedBy(disputeFeeRuleDtoMain.getCreatedBy());
			disputeFeeRuleDto.setCreatedOn(disputeFeeRuleDtoMain.getCreatedOn());
			disputeFeeRuleDto = disputeService.updateDisputeFeeRuleStg(disputeFeeRuleDto);
			model.addAttribute(CommonConstants.DISPUTE_FEE_RULE_DTO, disputeFeeRuleDto);
			List<CodeValueDTO> relCompOpList = lookupService.getLookupData(CommonConstants.DISPUTE_RELATIONAL_OP_COMP);

			model.addAttribute(CommonConstants.REL_OPERATOR_LIST_COMP, relCompOpList);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, ADD_EDIT_DISPUTE_FEE_RULE, ex);
		}

		model.addAttribute(CommonConstants.EDIT_DISPUTE_FEE_RULE, BaseCommonConstants.TRANSACT_YES);
		model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
				getMessageFromBundle("disputeFeeRule.updateSuccess.msg"));
		return getView(model, VIEW_APPROVE_DISPUTE_FEE_RULE);
	}

	@PostMapping("/disputeFeeRulePendingForApproval")
	@PreAuthorize("hasAuthority('View Dispute Fee Rule')")
	public String disputeFeeRulePendingForApproval(Model model) {
		try {
			
			model.addAttribute("pendingDisputeFeeRule", BaseCommonConstants.YES);
			List<DisputeFeeRuleDTO> disputeFeeRuleDtoPendingList = disputeService.getPendingDisputeFeeRuleList();
			Long tranSize = disputeFeeRuleDtoPendingList.stream()
					.filter(disputeFeeRuleDto -> disputeFeeRuleDto.getRequestState().equalsIgnoreCase("P")).count();
			model.addAttribute("tranSize", tranSize);
			model.addAttribute(CommonConstants.PENDING_DISPUTE_FEE_RULE_LIST, disputeFeeRuleDtoPendingList);
			model.addAttribute(CommonConstants.SHOW_PENDING_DISPUTE_FEE_RULE_LIST, BaseCommonConstants.YES);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_DISPUTE_FEE_RULE, ex);
		}
		return getView(model, SHOW_DISPUTE_FEE_RULE);
	}

	@PostMapping("/getPendingDisputeFeeRule")
	@PreAuthorize("hasAuthority('View Dispute Fee Rule')")
	public String getPendingDisputeFeeRule(@RequestParam("seqId") int seqId, Model model) {
		DisputeFeeRuleDTO disputeFeeRuleDtoInfo = disputeService.getDisputeFeeRuleInfo(seqId);
		model.addAttribute(CommonConstants.DISPUTE_FEE_RULE_DTO, disputeFeeRuleDtoInfo);
		return getView(model, VIEW_APPROVE_DISPUTE_FEE_RULE);

	}

	@PostMapping("/bulkApproveRejectDisputeFee")
	@PreAuthorize("hasAuthority('Approve Dispute Fee Rule')")
	public String bulkApproveRejectDisputeFee(Model modelForDisp, @RequestParam("status") String status,
			@RequestParam("disputeFeeList") String disputeFeeList, HttpServletResponse response,
			HttpServletRequest request) {

		String result = disputeService.updateBulkStgDisputeFee(disputeFeeList, status);

		if (StringUtils.equals(result, CommonConstants.RESULT_SUCCESS)
				&& StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
			modelForDisp.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("disputeFeeRule.approvalSuccess.msg", null, request.getLocale()));
		} else if (StringUtils.equals(result, CommonConstants.RESULT_SUCCESS)
				&& StringUtils.equals(status, CommonConstants.RECORD_REJECTED)) {
			modelForDisp.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("disputeFeeRule.rejectionSuccess.msg", null, request.getLocale()));
		} else {
			modelForDisp.addAttribute(BaseCommonConstants.ERROR_STATUS,
					messageSource.getMessage(E00001, null, request.getLocale()));
			modelForDisp.addAttribute(CommonConstants.PENDING_DISPUTE_FEE_RULE_LIST, null);
			modelForDisp.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, BaseCommonConstants.NO_FLAG);

			return getView(modelForDisp, SHOW_DISPUTE_FEE_RULE);
		}

		modelForDisp.addAttribute("pendingDisputeFeeRule", BaseCommonConstants.TRANSACT_YES);
		List<DisputeFeeRuleDTO> disputeFeeRuleDtoPendingList = disputeService.getPendingDisputeFeeRuleList();
		Long tranSize = disputeFeeRuleDtoPendingList.stream()
				.filter(disputeFeeRuleDto -> disputeFeeRuleDto.getRequestState().equalsIgnoreCase("P")).count();
		modelForDisp.addAttribute("tranSize", tranSize);
		modelForDisp.addAttribute(CommonConstants.PENDING_DISPUTE_FEE_RULE_LIST, disputeFeeRuleDtoPendingList);
		modelForDisp.addAttribute(CommonConstants.SHOW_PENDING_DISPUTE_FEE_RULE_LIST, BaseCommonConstants.YES);

		return getView(modelForDisp, SHOW_DISPUTE_FEE_RULE);
	}

	@PostMapping("/approveDisputeFeeRule")
	@PreAuthorize("hasAuthority('Approve Dispute Fee Rule')")
	public String approveDisputeFeeRule(@RequestParam("seqId") int seqId, @RequestParam("status") String status,
			@RequestParam("remarks") String remarks, Model model, HttpServletRequest request) {
		try {
			DisputeFeeRuleDTO disputeFeeRuleDto = disputeService.updateApproveDisputeFeeRule(seqId, status, remarks);
			checkDisputeFeeRuleApproveStatus(disputeFeeRuleDto, model);
			model.addAttribute(CommonConstants.DISPUTE_FEE_RULE_DTO, disputeFeeRuleDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_DISPUTE_FEE_RULE, ex);
		}
		return getView(model, VIEW_APPROVE_DISPUTE_FEE_RULE);
	}

	private void checkDisputeFeeRuleApproveStatus(DisputeFeeRuleDTO disputeFeeRuleDto, Model model) {
		if (BaseCommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(disputeFeeRuleDto.getStatusCode())) {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("disputeFeeRule.approvalSuccess.msg"));
		} else {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("disputeFeeRule.rejectionSuccess.msg"));
		}
	}

	@PostMapping("/discardRejectedDisputeFeeRule")
	@PreAuthorize("hasAuthority('Edit Dispute Fee Rule')")
	public String discardRejectedDisputeFeeRule(@RequestParam("seqId") int seqId, Model model) {
		DisputeFeeRuleDTO disputeFeeRuleDto = new DisputeFeeRuleDTO();
		try {
			disputeFeeRuleDto = disputeService.discardDisputeFeeRule(seqId);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_DISPUTE_FEE_RULE, ex);
		}
		model.addAttribute(CommonConstants.DISPUTE_FEE_RULE_DTO, disputeFeeRuleDto);
		model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
				getMessageFromBundle("disputeFeeRule.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_DISPUTE_FEE_RULE);
	}

	@PostMapping("/showTransitionRules")
	@PreAuthorize("hasAuthority('View Dispute Transition Rules')")
	public String getTransitionRuleList(Model model) {

		List<DisputeTransitionDTO> fetchList = disputeService.getTransitionRulesList(CommonConstants.RECORD_APPROVED);

		model.addAttribute(CommonConstants.DISPUTE_TRANSITION_LIST, fetchList);
		model.addAttribute(CommonConstants.SHOW_DISPUTE_TRANSITION_RULE, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, CommonConstants.YES_FLAG);

		return getView(model, CommonConstants.SHOW_DISPUTE_TRANSITION_RULE);
	}

	@PostMapping("/showPendingTransitionRules")
	@PreAuthorize("hasAuthority('View Dispute Transition Rules')")
	public String getPendingTransitionRuleList(Model model) {

		List<DisputeTransitionDTO> fetchList = disputeService.getTransitionRulesList(CommonConstants.RECORD_PENDING);

		Long tranSize = fetchList.stream().filter(disputeTransitionObj -> StringUtils
				.equals(disputeTransitionObj.getRequestState(), CommonConstants.RECORD_PENDING)).count();
		model.addAttribute(CommonConstants.DISPUTE_TRANSITION_LIST, fetchList);
		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, BaseCommonConstants.NO_FLAG);
		if (StringUtils.equals(sessionDTO.getMakChkFlag(), CommonConstants.ROLE_TYPE_MAKER) || fetchList.isEmpty()) {
			model.addAttribute(CommonConstants.SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(CommonConstants.DISPUTE_TRANS_LIST_SIZE, tranSize);
			model.addAttribute(CommonConstants.SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
		}

		return getView(model, CommonConstants.SHOW_DISPUTE_TRANSITION_RULE);
	}

	@PostMapping("/createTransitionRule")
	@PreAuthorize("hasAuthority('Add Dispute Transition Rules')")
	public String createTransitionRule(Model model) {
		DisputeTransitionDTO disputeDTO = new DisputeTransitionDTO();
		List<CodeValueDTO> entityList = lookupService.getLookupData(CommonConstants.DISPUTE_ENTITY);
		List<ActionCodeDTO> actionList = disputeService.getActionCodeList();
		List<CodeValueDTO> fieldNameList = lookupService.getLookupData(CommonConstants.FIELD_EX);
		List<CodeValueDTO> relOpList = getRelOperatorList();
		List<CodeValueDTO> fieldOpList = lookupService.getLookupData(CommonConstants.DISPUTE_FIELD_OP);

		model.addAttribute(CommonConstants.ADD_TRANSITION, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.EDIT_TRANSITION, BaseCommonConstants.NO_FLAG);
		model.addAttribute(CommonConstants.DISPUTE_TRANSITION_DTO, disputeDTO);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_ENTITY_LIST, entityList);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_STATE_LIST, actionList);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_FIELD_NAME_LIST, fieldNameList);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_RELOP_LIST, relOpList);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_FIELD_OP_LIST, fieldOpList);
		return getView(model, CommonConstants.ADD_EDIT_DISPUTE_TRANSITION_RULE);
	}

	@PostMapping("/addTransitionRules")
	@PreAuthorize("hasAuthority('Add Dispute Transition Rules')")
	public ResponseEntity<Object> addTransitionRule(Model model, @RequestBody List<DisputeTransitionDTO> transitionDTO,
			HttpServletResponse response) {

		List<DisputeTransitionDTO> transitionList = transitionDTO.stream()
				.sorted((d1, d2) -> d1.getToState().compareTo(d2.getToState())).collect(Collectors.toList());

		String result = disputeService.addTransitionRule(transitionList);

		JsonObject jsonResponse = new JsonObject();
		if (StringUtils.equals(result, CommonConstants.RESULT_SUCCESS)) {
			jsonResponse.addProperty(CommonConstants.STATUS, BaseCommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(CommonConstants.STATUS, BaseCommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PostMapping("/viewTransition")
	@PreAuthorize("hasAuthority('View Dispute Transition Rules')")
	public String viewTransitionRule(Model model, @RequestParam("id") String id,
			@RequestParam("reqState") String reqState, HttpServletResponse response) {

		DisputeTransitionDTO disputeDTO = disputeService.getTransitionObj(id);
		model.addAttribute(CommonConstants.DISPUTE_TRANSITION_DTO, disputeDTO);
		model.addAttribute(CommonConstants.MAK_CHK_FLAG, sessionDTO.getMakChkFlag());
		model.addAttribute(CommonConstants.DISPUTE_TRANS_REQ_STATE_FORW, reqState);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_SHOW_EDIT_DISCARD_BTN, CommonConstants.YES_FLAG);
		return getView(model, CommonConstants.VIEW_DISPUTE_TRANSITION_RULE);
	}

	@PostMapping("/forwardTransition")
	@PreAuthorize("hasAuthority('Edit Dispute Transition Rules')")
	public String forwardTransitionRule(Model model, @RequestParam("id") String id, HttpServletResponse response) {
		List<CodeValueDTO> entityList = lookupService.getLookupData(CommonConstants.DISPUTE_ENTITY);
		List<ActionCodeDTO> actionList = disputeService.getActionCodeList();
		List<CodeValueDTO> fieldNameList = lookupService.getLookupData(CommonConstants.FIELD_EX);
		List<CodeValueDTO> relOpList = getRelOperatorList();
		List<CodeValueDTO> fieldOpList = lookupService.getLookupData(CommonConstants.DISPUTE_FIELD_OP);

		model.addAttribute(CommonConstants.ADD_TRANSITION, BaseCommonConstants.NO_FLAG);
		model.addAttribute(CommonConstants.EDIT_TRANSITION, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_ENTITY_LIST, entityList);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_STATE_LIST, actionList);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_FIELD_NAME_LIST, fieldNameList);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_RELOP_LIST, relOpList);
		model.addAttribute(CommonConstants.DISPUTE_TRANS_FIELD_OP_LIST, fieldOpList);

		DisputeTransitionDTO disputeDTO = disputeService.getTransitionObj(id);
		model.addAttribute(CommonConstants.DISPUTE_TRANSITION_DTO, disputeDTO);

		return getView(model, CommonConstants.ADD_EDIT_DISPUTE_TRANSITION_RULE);
	}

	@PostMapping("/editTransitionRules")
	@PreAuthorize("hasAuthority('Edit Dispute Transition Rules')")
	public ResponseEntity<Object> editTransitionRule(Model model, @RequestBody List<DisputeTransitionDTO> transitionDTO,
			HttpServletResponse response) {

		List<DisputeTransitionDTO> transitionList = transitionDTO.stream()
				.sorted((d1, d2) -> d1.getToState().compareTo(d2.getToState())).collect(Collectors.toList());

		String result = disputeService.editTransitionRule(transitionList);

		JsonObject jsonResponse = new JsonObject();
		if (StringUtils.equals(result, CommonConstants.RESULT_SUCCESS)) {
			jsonResponse.addProperty(CommonConstants.STATUS, BaseCommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(CommonConstants.STATUS, BaseCommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PostMapping("/approveRejectTransition")
	@PreAuthorize("hasAuthority('Approve Dispute Transition Rules')")
	public String approveOrRejectTransitionRule(Model model, @RequestParam("status") String status,
			@RequestParam("tranIDList") String tranIDList,
			@RequestParam(value = "comments", required = false) String comments, HttpServletResponse response,
			HttpServletRequest request) {

		String resultApproveReject = disputeService.updateStgTransitionRule(tranIDList, status, comments);

		if (StringUtils.equals(resultApproveReject, CommonConstants.RESULT_SUCCESS)
				&& StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("dispute.transition.approve.msg", null, request.getLocale()));
		} else if (StringUtils.equals(resultApproveReject, CommonConstants.RESULT_SUCCESS)
				&& StringUtils.equals(status, CommonConstants.RECORD_REJECTED)) {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("dispute.transition.reject.msg", null, request.getLocale()));
		} else {
			if (StringUtils.isNotBlank(comments)) {
				model.addAttribute(BaseCommonConstants.ERROR_STATUS,
						messageSource.getMessage(E00001, null, request.getLocale()));
				model.addAttribute(CommonConstants.DISPUTE_TRANSITION_DTO, null);
				model.addAttribute(CommonConstants.MAK_CHK_FLAG, sessionDTO.getMakChkFlag());
				model.addAttribute(CommonConstants.DISPUTE_TRANS_REQ_STATE_FORW, null);
				return getView(model, CommonConstants.VIEW_DISPUTE_TRANSITION_RULE);
			}
			model.addAttribute(BaseCommonConstants.ERROR_STATUS,
					messageSource.getMessage(E00001, null, request.getLocale()));
			model.addAttribute(CommonConstants.DISPUTE_TRANSITION_LIST, null);
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, BaseCommonConstants.NO_FLAG);

			return getView(model, CommonConstants.SHOW_DISPUTE_TRANSITION_RULE);
		}

		if (StringUtils.isNotBlank(comments)) {
			DisputeTransitionDTO disputeDTO = disputeService.getTransitionObj(tranIDList);
			model.addAttribute(CommonConstants.DISPUTE_TRANSITION_DTO, disputeDTO);
			model.addAttribute(CommonConstants.MAK_CHK_FLAG, sessionDTO.getMakChkFlag());
			model.addAttribute(CommonConstants.DISPUTE_TRANS_REQ_STATE_FORW, status);
			model.addAttribute(CommonConstants.DISPUTE_TRANS_SHOW_EDIT_DISCARD_BTN, BaseCommonConstants.NO_FLAG);
			return getView(model, CommonConstants.VIEW_DISPUTE_TRANSITION_RULE);
		}

		List<DisputeTransitionDTO> fetchList = disputeService.getTransitionRulesList(CommonConstants.RECORD_PENDING);

		Long tranSize = fetchList.stream().filter(disputeTransitionObj -> StringUtils
				.equals(disputeTransitionObj.getRequestState(), CommonConstants.RECORD_PENDING)).count();
		model.addAttribute(CommonConstants.DISPUTE_TRANSITION_LIST, fetchList);
		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, BaseCommonConstants.NO_FLAG);
		if (StringUtils.equals(sessionDTO.getMakChkFlag(), CommonConstants.ROLE_TYPE_MAKER)) {
			model.addAttribute(CommonConstants.SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(CommonConstants.DISPUTE_TRANS_LIST_SIZE, tranSize);
			model.addAttribute(CommonConstants.SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
		}

		return getView(model, CommonConstants.SHOW_DISPUTE_TRANSITION_RULE);
	}

	@PostMapping("/discardTransition")
	@PreAuthorize("hasAuthority('Edit Dispute Transition Rules')")
	public String discardTransitionRule(Model model, @RequestParam("id") String id, HttpServletResponse response,
			HttpServletRequest request) {

		String result = disputeService.discardTransRule(id);

		DisputeTransitionDTO disputeDTO = disputeService.getTransitionObj(id);
		if (StringUtils.equals(result, CommonConstants.RESULT_SUCCESS)) {
			model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("dispute.transition.discard.msg", null, request.getLocale()));
			model.addAttribute(CommonConstants.DISPUTE_TRANSITION_DTO, disputeDTO);
			model.addAttribute(CommonConstants.MAK_CHK_FLAG, sessionDTO.getMakChkFlag());
		} else {
			model.addAttribute(BaseCommonConstants.ERROR_STATUS,
					messageSource.getMessage(E00001, null, request.getLocale()));
			model.addAttribute(CommonConstants.DISPUTE_TRANSITION_DTO, disputeDTO);
		}
		model.addAttribute(CommonConstants.DISPUTE_TRANS_SHOW_EDIT_DISCARD_BTN, BaseCommonConstants.NO_FLAG);

		return getView(model, CommonConstants.VIEW_DISPUTE_TRANSITION_RULE);
	}

	private List<CodeValueDTO> getRelOperatorList() {
		List<CodeValueDTO> relOpList = lookupService.getLookupData(CommonConstants.REL_OPR);
		List<CodeValueDTO> complexRelOpList = lookupService.getLookupData(CommonConstants.REL_OP_CMPLX);
		List<CodeValueDTO> newRelOpList = new ArrayList<>();

	
		for(CodeValueDTO i : relOpList) {
			for(CodeValueDTO j : complexRelOpList) {
				if (Boolean.FALSE.equals(StringUtils.equals(i.getCode(),
						j.getCode().substring(0, j.getCode().length() - 2)))
						&& Boolean.FALSE.equals(newRelOpList.contains(i))) {
					newRelOpList.add(i);
				}
			}
		}

		if (CollectionUtils.isEmpty(complexRelOpList)) {
			newRelOpList = relOpList;
		} else {
			for (CodeValueDTO codeValueDTO : complexRelOpList) {
				newRelOpList.add(codeValueDTO);
			}
		}
		return newRelOpList;
	}
}
