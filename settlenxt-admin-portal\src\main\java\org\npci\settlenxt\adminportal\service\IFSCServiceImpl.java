package org.npci.settlenxt.adminportal.service;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.IFSCDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.repository.IFSCRepository;
import org.npci.settlenxt.portal.common.exception.SettleNxtApplicationException;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional(rollbackFor = Throwable.class)
@Service
public class IFSCServiceImpl implements IFSCService {

	@Autowired
	IFSCRepository ifscRepository;
	@Autowired
	SessionDTO sessionDTO;

	@Override
	public IFSCDTO getIFSC(String ifscCode) {
		return ifscRepository.getIFSCDataMain(ifscCode);
	}

	public IFSCDTO getIFSCStg(String ifscCode) {
		return ifscRepository.getIFSC(ifscCode);

	}

	@Override
	public int updateIFSC(IFSCDTO ifscdto) {
		return ifscRepository.updateIFSC(ifscdto);
	}

	@Override
	public List<IFSCDTO> getApprovedIFSCList() {
		return ifscRepository.getApprovedIFSCListMain();
	}

	@Override
	public IFSCDTO getApprovedIFSC(String ifscCode) {
		return ifscRepository.getIFSCMain(ifscCode);
	}

	@Override
	public List<IFSCDTO> getPendingForAppovalIFSCList() {
		return ifscRepository.getPendingForAppovalIFSCList();
	}

	@Override
	public long saveIFSC(IFSCDTO ifscdto) {
		IFSCDTO existingIfscdto = ifscRepository.getIFSC(ifscdto.getIfscCode());
		if (existingIfscdto != null) {
			throw new SettleNxtApplicationException("ERR_IFSC_EXISTS", "IFSC with same ifsc code already exists");
		}
		List<IFSCDTO> existingIFSCsWithSameBankcode = ifscRepository.getIFSCByBankCode(ifscdto.getBankCode());
		if (existingIFSCsWithSameBankcode != null && !existingIFSCsWithSameBankcode.isEmpty()) {
			throw new SettleNxtApplicationException("ERR_BANKCODE_EXISTS", "IFSC with same bank code already exists");
		}
		return ifscRepository.saveIFSC(ifscdto);
	}

	@Override
	public IFSCDTO approveOrRejectIFSC(String ifscCode, String status, String remarks) {
		IFSCDTO ifscdto = getIFSCStg(ifscCode);
		prepareIfscForApproval(ifscCode, status, remarks, ifscdto);
		ifscRepository.updateIFSCRequestState(ifscdto);

		return ifscdto;
	}

	private void prepareIfscForApproval(String ifscCode, String status, String remarks, IFSCDTO ifscdto) {
		ifscdto.setRequestState(status);
		ifscdto.setCheckerComments(remarks);
		if (CommonConstants.REQUEST_STATE_APPROVED.equals(status)) {
			ifscdto.setLastOperation(CommonConstants.LAST_OPERATION_APPROVE);
			IFSCDTO ifscdtoMain = ifscRepository.getIFSCMain(ifscCode);
			if (ifscdtoMain != null) {
				ifscRepository.updateIFSCMain(ifscdto);
			} else {
				ifscRepository.saveIFSCMain(ifscdto);
			}
		}
		if (CommonConstants.REQUEST_STATE_REJECTED.equals(status)) {
			ifscdto.setLastOperation(CommonConstants.LAST_OPERATION_REJECT);
			}
	}

	// for bulk checker
	@Override
	public String approveOrRejectIFSCForBulk(String bulkApprovalReferenceNoList, String status, String remarks) {
		String[] referenceNoArr = bulkApprovalReferenceNoList.split("\\|");
		String ifscCode = "";

		for (String refNum:referenceNoArr) {

			try {
				if (!StringUtils.isEmpty(refNum)) {
					ifscCode = refNum;
					IFSCDTO ifscdto = getIFSCStg(ifscCode);
					if (ifscdto == null) {

						throw new SettleNxtException("Exception occurred with Ref No" + refNum, "");
					}
					approveOrRejectIFSCBulk(status, remarks, ifscCode, ifscdto);
				}

			} catch (Exception ex) {
				throw new SettleNxtException("", "Exception for Ref no" + refNum, ex);

			}
		}

		return CommonConstants.YES_FLAG;
	}

	private void approveOrRejectIFSCBulk(String status, String remarks, String ifscCode, IFSCDTO ifscdto) {
		if (ifscdto != null) {
			prepareIfscForApproval(ifscCode, status, remarks, ifscdto);
			ifscdto.setCheckerComments(remarks);
			
			Date lt = new Date();
			ifscdto.setLastUpdatedOn(lt);
			ifscdto.setLastUpdatedBy(sessionDTO.getUserName());
			ifscRepository.updateIFSCRequestState(ifscdto);

		}
	}

	@Override
	public IFSCDTO discardIFSC(String ifscCode) {
		IFSCDTO ifscdto = getIFSCStg(ifscCode);
		IFSCDTO ifscdtoMain = ifscRepository.getIFSCMain(ifscCode);
		if (ifscdtoMain != null) {
			ifscdtoMain.setLastOperation(CommonConstants.LAST_OPERATION_DISCARD);
			ifscdtoMain.setRequestState(CommonConstants.REQUEST_STATE_APPROVED);
			ifscRepository.updateIFSC(ifscdtoMain);
		} else {
			ifscRepository.deleteDiscardedEntry(ifscdto);
		}

		return ifscdto;
	}

}
