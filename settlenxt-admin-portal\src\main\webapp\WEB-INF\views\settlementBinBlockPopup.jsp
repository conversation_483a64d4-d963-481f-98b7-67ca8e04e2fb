<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<div class="modal fade" id="togglesettlebin" tabindex="-1" role="dialog" aria-labelledby="togglesettlebin" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Are you sure you want to Block/Unblock the Settlement Bin?</h5>
        <button type="button" class="close" data-dismiss="modal"  aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
     <div>
          <label style="color:blue;font-weight:bold;" >Settlement Bin Number</label>
          <p id="settleBinBlockUnblock"/>
          </div>

<input type="hidden" name="settlebinNumber" id="settlebinNumber" value="">
<input type="hidden" name="settleparticipantId" id="settleparticipantId" value="">

<input type="hidden" name="settlebuttonId" id="settlebuttonId" value="">



     <div class="modal-footer">
        
        <button type="button" class="btn btn-primary" onclick="blockSettlementBin(document.getElementById('settlebinNumber').value,document.getElementById('settleparticipantId').value,document.getElementById('settlebuttonId').value);">Approve</button>
      <button type="button" class="btn btn-primary" data-dismiss="modal"  aria-label="Close" >Reject</button>
      </div>
    </div>
  </div>
</div>