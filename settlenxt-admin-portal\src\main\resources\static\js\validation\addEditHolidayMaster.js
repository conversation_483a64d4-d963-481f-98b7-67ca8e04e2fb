var y;
var weekTypeList="";
var data;
$(document).ready(function() {
	
	
	if (document.getElementById("editFlow").value == "Y") {
	$('form')
    .each(function(){
        $(this).data('serialized', $(this).serialize())
    })
    .on('change input', function(){
        $(this)             
            .find('input:submit, button:submit')
                .prop('disabled', $(this).serialize() == $(this).data('serialized'))
        ;
        $('#updateHolidayBtn').prop('disabled', false);
       
     })
$('#updateHolidayBtn').prop('disabled', true);
}
	$("#errholidayDesc").hide();
	$("#errproduct").hide();
	$("#errperiodType").hide();
	$("#errfromDateStr").hide();
	$("#errdayOfWeek").hide();
	$("#errweekType").hide();
	$("#errtoDateStr").hide();

	$("#divfromDateStr").hide();
	$("#divtoDateStr").hide();
	$("#divweekday").hide();
	$("#divweekType").hide();
	if ($('#periodType').val() == 'Weekly Holiday') {
		$("#divfromDateStr").show();
		$("#divtoDateStr").show();
		$("#divweekday").show();
		$("#divweekType").show();
	}
	if ($('#periodType').val() == 'Public Holiday') {
		$("#divfromDateStr").show();
	}

	setOnChangeOnPeriodType();


	$('#product').on('keyup keypress blur change', function() {
	$("#errfromDateStr").hide();
		validateFromCommonVal('product', true, "SelectionBox", "", false);
	});
	$('#holidayDesc').on('keyup keypress blur change', function() {
		validateFromCommonVal('holidayDesc', true, "Alphanumeric", 100, false);
	});
	$('#periodType').on('keyup keypress blur change', function() {
		validateFromCommonVal('periodType', true, "SelectionBox", "", false);
	});
	$('#fromDateStr').on('keyup keypress blur change', function() {
		validateFromCommonVal('fromDateStr', true, "", "", false);
	});
	$('#toDateStr').on('keyup keypress blur change', function() {
		validateFromCommonVal('toDateStr', true, "", "", false);
	});
	$('#dayOfWeek').on('keyup keypress blur change', function() {
		validateFromCommonVal('dayOfWeek', true, "SelectionBoxHoliday", "", false);
	});
	




	if (document.getElementById("afterSave").value == "Y") {
		document.getElementById("product").disabled = true;
		document.getElementById("periodType").disabled = true;
		document.getElementById("holidayDesc").disabled = true;
		document.getElementById("dayOfWeek").disabled = true;
		document.getElementById("weekType").disabled = true;

	}



	$('#clearHolidayMaster').click(function() {
		$('#product').val("0")
		$('#holidayDesc').val("")
		$('#periodType').val("0")
		$('#fromDateStr').val("")
		$('#toDateStr').val("")
		$('#dayOfWeek').val("0")
		$('#weekType').val("0")
		$("#divfromDateStr").hide();
		$("#divtoDateStr").hide();
		$("#divweekday").hide();
		$("#divweekType").hide();

		$("#errholidayDesc").hide();
		$("#errproduct").hide();
		$("#errperiodType").hide();
		$("#errfromDateStr").hide();
		$("#errdayOfWeek").hide();
		$("#errweekType").hide();
		$("#errtoDateStr").hide();
		$('#snxtErrorMessage').hide();
	});
	$("#fromDateStr").datepicker({
minDate:'0',
changeMonth: true,
changeYear: true,
dateFormat: 'yy-mm-dd'
});
$("#toDateStr").datepicker({
        minDate:'0',
        changeMonth: true,
changeYear: true,
dateFormat: 'yy-mm-dd'
});
$('#fromDateStr').change(function(){
let startDate=$(this).datepicker('getDate');
$('#toDateStr').datepicker('option','minDate',startDate
);});
$('#toDateStr').change(function(){

$('#fromDateStr').datepicker('option','minDate','0'
);});
	if (($('#showbuttonCatch').val() == "Y")) {
		setHolidayDescription();


		setWeekType();}
	if (($('#showbutton').val() == "Y")) {
	
		$("input").prop('disabled', true);
		$("select").prop('disabled', true);

		setHolidayDescription();

		setWeekType();
		

	}

	if (($('#editFlow').val() == "Y")) {


		setHolidayDescription();


		setWeekType();
	}


	$('#weekType').multiselect({
		buttonWidth: '280px',
		paddingLeft: '0px',
		paddingRight: '15px',
		nonSelectedText: 'Select weekType',
		includeSelectAllOption: true,
		enableFiltering: true,
	});

});






function setWeekType() {
	if (document.getElementById("toWeekType") != null) {
		y = document.getElementById("toWeekType").value;
		let dataarray = "";
		if (y != null) {
			dataarray = y.split(",");

		}
		$('#weekType').multiselect('select', dataarray);
		$('#weekType').multiselect('refresh');
	}
}

function setHolidayDescription() {
	let toHolidayDesc = $('#toHolidayDesc').val();
	$('#holidayDesc').html(toHolidayDesc);
}

function setOnChangeOnPeriodType() {
	$('#periodType').on('change', function() {

		if ($(this).val() == "0") { $("#divfromDateStr").hide(); $("#divtoDateStr").hide(); $("#divweekday").hide(); $("#divweekType").hide(); }

		if ($(this).val() == "Weekly Holiday") {
			$('#frmdtLabel').text('From Date');
			$('#frmdtLabel').append("<span class='red'>*</span>");
			validationMessages['fromDateStr'] = "From Date is necessary";
			$("#errfromDateStr").hide();
			$('#divfromDateStr').show(); $('#divtoDateStr').show(); $('#divweekday').show(); $('#divweekType').show();
		}
		if ($(this).val() == "Public Holiday") {
			$('#frmdtLabel').text('Date');
			$('#frmdtLabel').append("<span class='red'>*</span>");
			validationMessages['fromDateStr'] = "Date is necessary";
			$("#errfromDateStr").hide();
			$('#divfromDateStr').show(); $("#divtoDateStr").hide(); $("#divweekday").hide(); $("#divweekType").hide();
		}
	});
}

function submitForm(url) {

	data = "";
	postData(url, data);
}

function userAction(action) {
	var holidaySeqId = $("#holidaySeqId").val();
 holidaySeqId=btoa(holidaySeqId);
	data = "holidaySeqId," + holidaySeqId;
	console.log(data);
	postData(action, data);
}

function saveDetails(action) {

	var value = $('#periodType').val();
	var check = false;



	

	if (!validateFromCommonVal('product', true, "SelectionBox", "", false)) {
		check = true;
	}

	if (!validateFromCommonVal('holidayDesc', true, "Alphanumeric", 100, false)) {

		check = true;

	}
	if (!validateFromCommonVal('periodType', true, "SelectionBox", "", false)) {

		check = true;

	}
	if (value == 'Public Holiday'&&($('#editFlow').val() != "Y")&&!validateFromCommonVal('fromDateStr', true, "", "", false)) {
		check = true;
	}
	check = validationForWeeklyHoliday(value, check);

	if (!check) {

		addDetails(action)
	} else {
		return false;
	}
}

function validationForWeeklyHoliday(value, check) {
    if (value == 'Weekly Holiday' && ($('#editFlow').val() != "Y")) {
        if ($('#weekType').val().length == 0) {
            check = true;
            $('#errweekType').show();
            $('#errweekType').find('.error').html(validationMessages['weekType']);
        }
        else {

            $('#errweekType').hide();
        }

        if (!validateFromCommonVal('fromDateStr', true, "", "", false)) {

            check = true;
        }
        if (!validateFromCommonVal('toDateStr', true, "", "", false)) {

            check = true;
        }

        if (!validateFromCommonVal('dayOfWeek', true, "SelectionBoxHoliday", "", false)) {
            check = true;
        }



    }
    return check;
}

function addDetails(action) {
var dayOfWeek;
if($('#weekType').val()==0){
	weekTypeList="";
	}
	let a="";
	if ($('#weekType').val() != 0) {
		 a = $('#weekType option:selected').toArray().map(item => item.value).join();
		var arr1 = a.split(",");
		 weekTypeList = "";
		for (var i of arr1) {
			weekTypeList = weekTypeList + i + "|";
		}
	}
	
	
	if($('#dayOfWeek').val()=='SELECT'){
	 dayOfWeek="";
	}else{
	dayOfWeek=$('#dayOfWeek').val();
	}
	
	
	
	
	//in case of edit we can only change the holiday description, product type
	if ($('#fromDateStr').val() == undefined || $('#toDateStr').val() == undefined) {
		 data = "product," + $('#product').val() + ",holidayDesc," + $('#holidayDesc').val() + ",periodType," + $('#periodType').val() + ",dayOfWeek," + dayOfWeek + ",weekType," + a + ",holidaySeqId," + $('#holidaySeqId').val();
	}
	else {
		 data = "product," + $('#product').val() + ",holidayDesc," + $('#holidayDesc').val() + ",periodType," + $('#periodType').val() + ",fromDate," + $('#fromDateStr').val() + ",toDate," + $('#toDateStr').val() + ",dayOfWeek," + dayOfWeek+ ",weekType," + weekTypeList + ",holidaySeqId," + $('#holidaySeqId').val();
	}
	
	
	postData(action, data);
}



function disableSave() {
	if (typeof bEdit != "undefined") {
		document.getElementById("bEdit").disabled = true;
	}
}
function unableSave() {
	if (typeof bEdit != "undefined") {
		document.getElementById("bEdit").disabled = false;
	}
}
