package org.npci.settlenxt.adminportal.controllers;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.CappingAmountDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.CappingAmountService;
import org.npci.settlenxt.adminportal.service.CodeValueService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseLookupServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

@Controller
public class CappingAmountController extends BaseController {

	@Autowired
	private MessageSource messageSource;

	@Autowired
	CappingAmountService cappingAmountService;

	@Autowired
	CodeValueService codeValueService;

	@Autowired
	private SessionDTO sessionDTO;
	@Autowired
	BaseLookupServiceImpl lookUpService;

	private static final String SHOW_CAPPING_AMOUNT = "showCappingAmount";
	private static final String ADD_CAPPING_AMOUNT = "addEditCappingAmount";
	private static final String APPROVE_CAPPING_AMOUNT = "viewApproveCappingAmount";
	private static final String VIEW_CAPPING_AMOUNT = "viewCappingAmount";
	private static final String SHOW_CHECKBOX = "showCheckBox";
	private static final String SCHEME_CODE = "SCHEME_CODE";
	private static final String CARD_TYPE = "CARD_TYPE";
	private static final String CAP_NAME = "CAP_NAME";
	private static final String FIELD_NAME = "FIELD_NAME";
	private static final String REL_OPERATOR_LIST = "REL_OPERATOR_LIST";
	private static final String AMOUNT_FLAG = "AMOUNT_FLAG";
	private static final String CAPPING_AMOUNT_DTO = "cappingAmountDto";
	private static final String ACTION_CODE_LIST = "actionCodeList";
	private static final String MCC_GROUP = "mccGroup";
	private static final String BIN_CARD_BRAND = "binCardBrand";
	private static final String CAP_NAME_ATT = "capName";
	private static final String BIN_CARD_TYPE = "binCardType";
	private static final String FILE_NAME = "filedName";
	private static final String REL_OPR = "relOpr";
	private static final String CAPPING_AMT_FLAG_LST = "capAmountFlagList";
	private static final String SHOW_BUTTON = "showButton";
	private static final String UTF_8 = "UTF-8";

	@PostMapping("/showCappingAmount")
	@PreAuthorize("hasAuthority('View Capping Amount')")
	public String showCappingAmount(Model model) {
		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.YES_FLAG);
		List<CappingAmountDTO> cappingamtDtoList = cappingAmountService.getApprovedCappingAmount();
		model.addAttribute(CommonConstants.CAPPING_AMOUNT_LIST, cappingamtDtoList);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}

		return getView(model, SHOW_CAPPING_AMOUNT);
	}

	@PostMapping("/cappingAmountPendingForApproval")
	@PreAuthorize("hasAuthority('View Capping Amount')")
	public String getPendingCappingAmount(Model model) {

		return loadCappingAmount(model);
	}

	private String loadCappingAmount(Model model) {
		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);

		List<CappingAmountDTO> pendingcappingamountDTOList = cappingAmountService
				.getPendingForApprovalCappingAmountList();
		model.addAttribute(CommonConstants.PENDING_CAP_AMOUNT_LIST, pendingcappingamountDTOList);
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(SHOW_CHECKBOX, CommonConstants.YES_FLAG);
		}

		return getView(model, SHOW_CAPPING_AMOUNT);
	}

	@PostMapping("/addCappingAmount")
	@PreAuthorize("hasAuthority('Add Capping Amount')")
	public String addCappingAmount(Model model) {

		CappingAmountDTO cappingAmountDto = new CappingAmountDTO();
		List<CodeValueDTO> actionCodeList = cappingAmountService.getActionCodeList();

		List<CodeValueDTO> mccGroup = cappingAmountService.getMccGroupList();

		List<CodeValueDTO> binCardBrand = lookUpService.getLookupData(SCHEME_CODE);

		List<CodeValueDTO> binCardType = lookUpService.getLookupData(CARD_TYPE);

		List<CodeValueDTO> capName = lookUpService.getLookupData(CAP_NAME);

		List<CodeValueDTO> filedName = lookUpService.getLookupData(FIELD_NAME);

		List<CodeValueDTO> relOpr = lookUpService.getLookupData(REL_OPERATOR_LIST);

		List<CodeValueDTO> capAmountFlagList = lookUpService.getLookupData(AMOUNT_FLAG);

		cappingAmountDto.setAddEditFlag(CommonConstants.ADD_CAPPING);
		model.addAttribute(CAPPING_AMOUNT_DTO, cappingAmountDto);
		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(ACTION_CODE_LIST, actionCodeList);
		model.addAttribute(MCC_GROUP, mccGroup);
		model.addAttribute(BIN_CARD_BRAND, binCardBrand);
		model.addAttribute(CAP_NAME_ATT, capName);
		model.addAttribute(BIN_CARD_TYPE, binCardType);
		model.addAttribute(FILE_NAME, filedName);
		model.addAttribute(REL_OPR, relOpr);
		model.addAttribute(CAPPING_AMT_FLAG_LST, capAmountFlagList);

		return getView(model, ADD_CAPPING_AMOUNT);
	}

	@PostMapping("/saveCappingAmount")
	@PreAuthorize("hasAuthority('Add Capping Amount')")
	public String saveCappingAmount(@ModelAttribute CappingAmountDTO cappingAmountDto, Model model) {

		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		try {
			cappingAmountDto = cappingAmountService.addEditCapping(cappingAmountDto);

		} catch (Exception ex) {
			model.addAttribute(CommonConstants.CAPPING_AMOUNT_DTO, cappingAmountDto);
			model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_CAPPING_AMOUNT, ex);

		}
		List<CodeValueDTO> actionCodeList = cappingAmountService.getActionCodeList();

		List<CodeValueDTO> mccGroup = cappingAmountService.getMccGroupList();

		List<CodeValueDTO> binCardBrand = lookUpService.getLookupData(SCHEME_CODE);

		List<CodeValueDTO> binCardType = lookUpService.getLookupData(CARD_TYPE);

		List<CodeValueDTO> capName = lookUpService.getLookupData(CAP_NAME);

		List<CodeValueDTO> filedName = lookUpService.getLookupData(FIELD_NAME);

		List<CodeValueDTO> relOpr = lookUpService.getLookupData(REL_OPERATOR_LIST);

		List<CodeValueDTO> capAmountFlagList = lookUpService.getLookupData(AMOUNT_FLAG);
		model.addAttribute(ACTION_CODE_LIST, actionCodeList);
		model.addAttribute(MCC_GROUP, mccGroup);
		model.addAttribute(BIN_CARD_BRAND, binCardBrand);
		model.addAttribute(CAP_NAME_ATT, capName);
		model.addAttribute(BIN_CARD_TYPE, binCardType);
		model.addAttribute(FILE_NAME, filedName);
		model.addAttribute(REL_OPR, relOpr);
		model.addAttribute(CAPPING_AMT_FLAG_LST, capAmountFlagList);

		model.addAttribute(CommonConstants.CAPPING_AMOUNT_DTO, cappingAmountDto);
		model.addAttribute(SHOW_BUTTON, CommonConstants.YES_FLAG);

		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("Cap.addSuccess.msg"));

		return getView(model, ADD_CAPPING_AMOUNT);

	}

	public void selectMCCValue(CappingAmountDTO cappingAmountDto) {

		String[] mccId = cappingAmountDto.getMccId().split("\\|");

		StringBuilder mccIdval = new StringBuilder();
		for (String mId:mccId) {
			mccIdval.append(",");
			mccIdval.append(mId);

		}

		String mccVal = "";
		if (mccIdval.toString().contains("|")) {
			mccVal = mccIdval.toString().replace("|", "");
		}
		cappingAmountDto.setMccId(mccVal);

	}

	@PostMapping("/viewApproveCappingAmount")
	@PreAuthorize("hasAuthority('View Capping Amount')")
	public String viewApproveCappingAmount(@RequestParam("actionCode") String actionCode,
			@RequestParam("mccGroup") String mccGroup, @RequestParam("binCardBrandId") String binCardBrandId,
			@RequestParam("binCardTypeId") String binCardTypeId, @RequestParam("fieldName") String fieldName,
			@RequestParam("relOperator") String relOperator, @RequestParam("fieldValue") String fieldValue, Model model)
			throws UnsupportedEncodingException {

		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);

		CappingAmountDTO cappingAmountDto = cappingAmountService.getCappingAmount(actionCode, mccGroup, binCardBrandId,
				binCardTypeId, fieldName, relOperator, fieldValue);

		model.addAttribute(CommonConstants.CAPPING_AMOUNT_DTO, cappingAmountDto);
		return getView(model, APPROVE_CAPPING_AMOUNT);

	}

	private void checkCappingAmountApproveStatus(CappingAmountDTO cappingAmountDTO, Model model) {
		Locale locale = Locale.ROOT;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(cappingAmountDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_CappingAmountApproved", null, locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("AM_MSG_CappingAmountRejected", null, locale));
		}
	}

	@PostMapping("/approveOrRejectBulk")
	@PreAuthorize("hasAuthority('Approve Capping Amount')")
	public String approveOrRejectCappingStatus(
			@RequestParam("bulkApprovalCappingIdList") String bulkApprovalCappingIdList,
			@RequestParam("status") String status, Model model) {
		try {

			bulkApprovalCappingIdList = URLDecoder.decode(bulkApprovalCappingIdList, UTF_8);
			String remarks = "";
			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}

		
				String capIdList = bulkApprovalCappingIdList;

				String[] idArray = capIdList.split("\\|");

				CappingAmountDTO cappingAmountDTO = cappingAmountService.updateApproveOrRejectBulk(idArray, status,
						remarks);
				checkCappingAmountApproveStatus(cappingAmountDTO, model);
			
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, APPROVE_CAPPING_AMOUNT, ex);
		}

		return loadCappingAmount(model);

	}

	@PostMapping("/approveCappingAmount")
	@PreAuthorize("hasAuthority('Approve Capping Amount')")
	public String approveCappingAmount(@RequestParam("cappingId") String cappingId,
			@RequestParam("status") String status, @RequestParam("remarks") String remarks, Model model) {

		try {
			cappingId = URLDecoder.decode(cappingId, UTF_8);
		} catch (UnsupportedEncodingException e) {
			throw new SettleNxtException("Capping Id", "",e);
		}
		String capId = cappingId;

		CappingAmountDTO cappingAmountDto = cappingAmountService.approveOrRejectCappingAmount(capId, status, remarks);
		model.addAttribute(CommonConstants.CAPPING_AMOUNT_DTO, cappingAmountDto);

		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("cap.approvalSuccess.msg"));
		} else if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("cap.rejectSuccess.msg"));
		}
		return getView(model, APPROVE_CAPPING_AMOUNT);

	}

	@PostMapping("/getCappingAmount")
	@PreAuthorize("hasAuthority('View Capping Amount')")
	public String getCappingAmount(@RequestParam("actionCode") String actionCode,
			@RequestParam("mccGroup") String mccGroup, @RequestParam("binCardBrandId") String binCardBrandId,
			@RequestParam("binCardTypeId") String binCardTypeId, @RequestParam("fieldName") String fieldName,
			@RequestParam("relOperator") String relOperator, @RequestParam("fieldValue") String fieldValue, Model model)
			throws UnsupportedEncodingException {

		CappingAmountDTO cappingAmountDTO = cappingAmountService.getCappingAmount(actionCode, mccGroup, binCardBrandId,
				binCardTypeId, fieldName, relOperator, fieldValue);

		model.addAttribute(CommonConstants.CAPPING_AMOUNT_DTO, cappingAmountDTO);
		return getView(model, VIEW_CAPPING_AMOUNT);

	}

	@PostMapping("/getRejCappingAmount")
	@PreAuthorize("hasAuthority('View Capping Amount')")
	public String getRejCappingAmount(@RequestParam("actionCode") String actionCode,
			@RequestParam("mccGroup") String mccGroup, @RequestParam("binCardBrandId") String binCardBrandId,
			@RequestParam("binCardTypeId") String binCardTypeId, @RequestParam("fieldName") String fieldName,
			@RequestParam("relOperator") String relOperator, @RequestParam("fieldValue") String fieldValue, Model model)
			throws UnsupportedEncodingException {

		CappingAmountDTO cappingAmountDto = cappingAmountService.getCappingAmount(actionCode, mccGroup, binCardBrandId,
				binCardTypeId, fieldName, relOperator, fieldValue);

		model.addAttribute(CommonConstants.CAPPING_AMOUNT_DTO, cappingAmountDto);
		return getView(model, VIEW_CAPPING_AMOUNT);

	}

	@PostMapping("/editCappingAmount")
	@PreAuthorize("hasAuthority('Edit Capping Amount')")
	public String editCappingAmount(@RequestParam("actionCode") String actionCode,
			@RequestParam("mccGroup") String mccGroup, @RequestParam("binCardBrandId") String binCardBrandId,
			@RequestParam("binCardTypeId") String binCardTypeId, @RequestParam("fieldName") String fieldName,
			@RequestParam("relOperator") String relOperator, @RequestParam("fieldValue") String fieldValue, Model model)
			throws UnsupportedEncodingException {

		CappingAmountDTO cappingAmountDto = new CappingAmountDTO();

		List<CodeValueDTO> actionCodeList = cappingAmountService.getActionCodeList();

		List<CodeValueDTO> mcc = cappingAmountService.getMccGroupList();

		List<CodeValueDTO> binCardBrand = lookUpService.getLookupData(SCHEME_CODE);

		List<CodeValueDTO> binCardType = lookUpService.getLookupData(CARD_TYPE);

		List<CodeValueDTO> capName = lookUpService.getLookupData(CAP_NAME);

		List<CodeValueDTO> filedName = lookUpService.getLookupData(FIELD_NAME);

		List<CodeValueDTO> relOpr = lookUpService.getLookupData(REL_OPERATOR_LIST);

		List<CodeValueDTO> capAmountFlagList = lookUpService.getLookupData(AMOUNT_FLAG);

		cappingAmountDto.setAddEditFlag(CommonConstants.ADD_CAPPING);
		model.addAttribute(CAPPING_AMOUNT_DTO, cappingAmountDto);
		model.addAttribute(ACTION_CODE_LIST, actionCodeList);
		model.addAttribute(MCC_GROUP, mcc);
		model.addAttribute(BIN_CARD_BRAND, binCardBrand);
		model.addAttribute(CAP_NAME_ATT, capName);
		model.addAttribute(BIN_CARD_TYPE, binCardType);
		model.addAttribute(FILE_NAME, filedName);
		model.addAttribute(REL_OPR, relOpr);
		model.addAttribute(CAPPING_AMT_FLAG_LST, capAmountFlagList);

		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);

		cappingAmountDto = cappingAmountService.getCappingAmount(actionCode, mccGroup, binCardBrandId, binCardTypeId,
				fieldName, relOperator, fieldValue);

		cappingAmountDto.setAddEditFlag(CommonConstants.EDIT_CAPPING);
		model.addAttribute(CommonConstants.CAPPING_AMOUNT_DTO, cappingAmountDto);
		return getView(model, ADD_CAPPING_AMOUNT);
	}

	@PostMapping("/updateCappingAmount")
	@PreAuthorize("hasAuthority('Edit Capping Amount')")
	public String updateCappingAmount(@ModelAttribute CappingAmountDTO cappingAmountDto, Model model) {

		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);

		List<CodeValueDTO> actionCodeList = cappingAmountService.getActionCodeList();

		List<CodeValueDTO> mcc = cappingAmountService.getMccGroupList();

		List<CodeValueDTO> binCardBrand = lookUpService.getLookupData(SCHEME_CODE);

		List<CodeValueDTO> binCardType = lookUpService.getLookupData(CARD_TYPE);

		List<CodeValueDTO> capName = lookUpService.getLookupData(CAP_NAME);

		List<CodeValueDTO> filedName = lookUpService.getLookupData(FIELD_NAME);

		List<CodeValueDTO> relOpr = lookUpService.getLookupData(REL_OPERATOR_LIST);

		List<CodeValueDTO> capAmountFlagList = lookUpService.getLookupData(AMOUNT_FLAG);

		model.addAttribute(CAPPING_AMOUNT_DTO, cappingAmountDto);
		model.addAttribute(ACTION_CODE_LIST, actionCodeList);
		model.addAttribute(MCC_GROUP, mcc);
		model.addAttribute(BIN_CARD_BRAND, binCardBrand);
		model.addAttribute(CAP_NAME_ATT, capName);
		model.addAttribute(BIN_CARD_TYPE, binCardType);
		model.addAttribute(FILE_NAME, filedName);
		model.addAttribute(REL_OPR, relOpr);
		model.addAttribute(CAPPING_AMT_FLAG_LST, capAmountFlagList);

		cappingAmountService.updateCappingAmount(cappingAmountDto);

		model.addAttribute(CommonConstants.CAPPING_AMOUNT_DTO, cappingAmountDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("cap.updateSuccess.msg"));
		model.addAttribute(SHOW_BUTTON, CommonConstants.YES_FLAG);
		return getView(model, ADD_CAPPING_AMOUNT);
	}

	@PostMapping("/discardCappingAmount")
	@PreAuthorize("hasAuthority('Edit Capping Amount')")
	public String discardCappingAmount(@RequestParam("actionCode") String actionCode,
			@RequestParam("mccGroup") String mccGroup, @RequestParam("binCardBrandId") String binCardBrandId,
			@RequestParam("binCardTypeId") String binCardBrandTypeId, @RequestParam("fieldName") String fieldName,
			@RequestParam("relOperator") String relOperator, @RequestParam("fieldValue") String fieldValue, Model model)
			throws UnsupportedEncodingException {

		CappingAmountDTO cappingAmountDto = cappingAmountService.discardCappingAmount(actionCode, mccGroup,
				binCardBrandId, binCardBrandTypeId, fieldName, relOperator, fieldValue);
		List<CodeValueDTO> cappingAmountFlag = lookUpService.getLookupData(CommonConstants.CAPPING_AMT_FLAG);

		Map<String, String> cappingAmountFlagMap = cappingAmountFlag.stream()
				.collect(Collectors.toMap(CodeValueDTO::getCode, CodeValueDTO::getDescription));
		cappingAmountDto.setCapAmountFlag(cappingAmountFlagMap.get(cappingAmountDto.getCapAmountFlag()));

		List<CodeValueDTO> mccList = codeValueService.getMccNameList();

		Map<String, String> mccListMap = mccList.stream()
				.collect(Collectors.toMap(CodeValueDTO::getCode, CodeValueDTO::getDescription));
		cappingAmountDto.setMccId(mccListMap.get(cappingAmountDto.getMccId()));

		model.addAttribute(CAPPING_AMOUNT_DTO, cappingAmountDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("cappingAmount.discardSuccess.msg"));
		return getView(model, APPROVE_CAPPING_AMOUNT);
	}

	@PostMapping("/checkDuplicateRecords")
	public ResponseEntity<Object> checkDuplicateRecords(Model model, @RequestParam("actionCode") String actionCode,
			@RequestParam("mccGroup") String mccGroup, @RequestParam("binCardBrandId") String binCardBrandId,
			@RequestParam("binCardTypeId") String binCardTypeId, @RequestParam("fieldName") String fieldName,
			@RequestParam("relOperator") String relOperator, @RequestParam("fieldValue") String fieldValue) {

		boolean result = cappingAmountService.checkDuplicateRecords(actionCode, mccGroup, binCardBrandId, binCardTypeId,
				fieldName, fieldValue, relOperator);

		JsonObject jsonResponse = new JsonObject();
		if (result) {
			jsonResponse.addProperty(BaseCommonConstants.RESPONSE_STATUS, CommonConstants.TRANSACT_SUCCESS);

		} else {
			jsonResponse.addProperty(BaseCommonConstants.RESPONSE_STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

}
