<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="EN" xmlns="http://www.w3.org/1999/xhtml">
<head>


<script>
window.onload = () => {
	 const passwordText = document.getElementById('passwordForSessResume');
	 passwordText.onpaste = e => e.preventDefault();
	 

	}
	
/* var elem = document.getElementById("passwordForSessResume");
elem.onkeyup = function(e){
    if(e.keyCode == 13){
    	verifyPassword();
    }
}


function verifyPasswordEvent(e){
	
	if(e.keyCode==13){
		verifyPassword();
	}
	else{
		return true;
	}
	
}
	 */


</script>
<script type="text/javascript" src="./static/js/jsencrypt.min.js"></script>	

<jsp:include page="regEx.jsp" />

<style>
.w3-modal{z-index:3;display:none;padding-top:100px;position:fixed;left:0;top:0;width:100%;height:100%;overflow:auto;background-color:rgb(0,0,0);background-color:rgba(0,0,0,0.4)}
.w3-modal-content{margin:auto;background-color:#fff;position:relative;padding:0;outline:0;width:600px}
.w3-card-4,.w3-hover-shadow:hover{box-shadow:0 4px 10px 0 rgba(0,0,0,0.2),0 4px 20px 0 rgba(0,0,0,0.19)}
.w3-animate-zoom {animation:animatezoom 0.6s}@keyframes animatezoom{from{transform:scale(0)} to{transform:scale(1)}}
.w3-container:after,.w3-container:before,.w3-panel:after{content:"";display:table;clear:both}
.w3-section,.w3-code{margin-top:16px!important;margin-bottom:16px!important}
.w3-border-top{border-top:1px solid #ccc!important}.w3-border-bottom{border-bottom:1px solid #ccc!important}
.w3-button:hover{color:#000!important;background-color:#ccc!important}
.w3-border{border:1px solid #ccc!important}
.w3-padding{padding:8px 16px!important}
.w3-padding-16{padding-top:16px!important;padding-bottom:16px!important}.w3-padding-24{padding-top:24px!important;padding-bottom:24px!important}
.w3-light-grey,.w3-hover-light-grey:hover,.w3-light-gray,.w3-hover-light-gray:hover{color:#000!important;background-color:#f1f1f1!important}

.modal-header {
  padding: 2px 16px;
  background-color: #768caf;
  color: white;
}
</style>

<title><spring:message code="common.application.title" /> </title>


<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    
<meta http-equiv="Cache-control" content="no-cache" />
<meta http-equiv="Cache-control" content="no-store" />
<meta http-equiv="Expires" content="0" />
<meta http-equiv="pragma" content="no-cache" />
 <script type="text/javascript" >
		var uiPingInterval="${uiPingInterval}";
	</script>
</head>
<body onload="noBack()">
	<%@include file="Header.jsp"%>
	<script type="text/javascript" src="./static/js/validation/validatesession.js"></script>
	<div class="container-fluid height-min">






  <div id="id01" class="w3-modal">
    <div class="w3-modal-content w3-card-4 w3-animate-zoom" style="max-width:600px">

      
      <div class="modal-header">
    <h2 style="    margin-top: -3px;
    margin-bottom: -3px;">Session Timeout</h2>
  </div>
      

      <form class="w3-container" action=" ">
        <div class="w3-section">
        <p  style="text-align: center">Your Settlenxt Application has been InActive for <span id="idletimeinterval">${idleTimeInterval}</span> minutes.</p>
   <p style="text-align: center">Click Ok to resume activity, or Log Off to close the application</p> 
          </div>
           <div class="w3-container w3-light-grey">
          <button class="w3-section" type="button" style="float:right; margin-right :5px; margin-left :10px; color: #fff!important;
    background-color: #768caf!important;" onclick="reloadApp();">Log Off</button>
          <button  onclick="refreshSessionBackEnd();" class="w3-section " type="button" style="float:right; color: #fff!important;
    background-color: #768caf!important">Ok</button>
         
          </div>
       
      </form>


    </div>
  </div>
  
    <div id="id02" class="w3-modal">
    <div class="w3-modal-content w3-card-4 w3-animate-zoom" style="max-width:600px">      
      <div class="modal-header">
    <h2 style="    margin-top: -3px;
    margin-bottom: -3px;">Session Timeout</h2>
  </div>
      

      <form class="w3-container" action=" ">
        <div class="w3-section">
        <p style="text-align: center">Your Settlenxt Application has been InActive for <span id="idletimepwdinterval">${idleTimePwdInterval}</span> minutes.</p>
        <p style="text-align: center">Please re-enter your password to regain access to the system. You will lose unsaved data</p>
        <p style="text-align: center"> at <span id="sessionTimeout">${sessionTimeout}</span>  minutes unless you resume activity.</p>
        </div>
          
          <label style="margin-bottom: 2px; margin-left: 25px;"><strong>Password</strong></label>
          <div   id="errPassword">
          <span class="error"></span></div>
          <input class="w3-input w3-border"  style="margin-bottom: 15px; margin-left: 25px;"  onkeydown="return  (event.keyCode!=13);"    id="passwordForSessResume" type="password" placeholder="Enter Password" name="passwordForSessResume" required> 
          <input type="hidden" id="publicKey" value="${PublicKey}">	
             <div class="w3-container w3-light-grey">
          <button class="w3-section " type="button" style="float:right; margin-right :5px; margin-left :10px; color: #fff!important;
    background-color: #768caf!important;" onclick="reloadApp();">Log Off</button>
          <button onclick= "verifyPassword();" class="w3-section " type="button" style="float:right; color: #fff!important;
    background-color: #768caf!important">Ok</button>
         
   </div>
      </form>

   
     
       
      

    </div>
  </div>
		<div id="body-content">
			<div class="space_block">
				<c:if test="${not empty successStatus}">
					<div class="alert alert-success" role="alert" id="snxtSuccessMessage" >${successStatus}</div>
				</c:if>
				<c:if test="${not empty errorStatus}">
					<div class="alert alert-danger" role="alert" id="snxtErrorMessage">${errorStatus}</div>
				</c:if>
			</div>
			<jsp:include page="${body}.jsp"/>
		</div>
	 
	</div>
	<div id="footer" class="footer">


		<%@include file="Footer.jsp"%>


	</div>
</body>
</html>
