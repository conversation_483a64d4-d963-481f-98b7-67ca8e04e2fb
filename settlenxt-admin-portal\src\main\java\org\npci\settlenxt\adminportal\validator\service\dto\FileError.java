package org.npci.settlenxt.adminportal.validator.service.dto;

public class FileError {
	private int code;
	private String fieldName;
	private String description;
	private String additionalText;
	
	public FileError(int code, String description, String fieldName) {
		this.code = code;
		this.description = description;
		this.fieldName = fieldName;
	}
	
	public FileError(int code, String description, String additionalText, String fieldName) {
		this.code = code;
		this.description = description;
		this.additionalText = additionalText.replace("&amp;", "&");
		this.fieldName = fieldName;
	}
	
	public int getCode() {
		return code;
	}
	public void setCode(int code) {
		this.code = code;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}

	public String getAdditionalText() {
		return additionalText;
	}

	public void setAdditionalText(String additionalText) {
		this.additionalText = additionalText;
	}

	public String getFieldName() {
		return fieldName;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}
	
}
