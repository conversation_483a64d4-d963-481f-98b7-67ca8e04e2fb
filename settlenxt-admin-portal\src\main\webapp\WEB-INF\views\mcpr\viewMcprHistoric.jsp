<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/mcpr/viewMcprHistoric.js" type="text/javascript"></script>

<script src="./static/js/validation/mcpr/showMcprHistoric.js"
	type="text/javascript"></script>

<div class="space_block">
	<div class="container-fluid height-min">
		<form:form onsubmit="removeSpace(this); encodeForm(this);"
			method="POST" id="viewMcprBinDetails" modelAttribute="mcprViewEditHistoricDto"
			action="${approveMcprBinDetailsStatus}" autocomplete="off"> 
			
		
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> 
						<span data-i18n="Data"><spring:message code="mcprBinDetails.viewscreen.title" /></span></strong>
					</div>
					<div class="panel-body">
					<input type="hidden" id="mcprId" value="${mcprViewEditHistoricDto.mcprBinDataDetailsId}" />
						<table class="table table-striped" style="font-size: 12px">
						<caption style="display:none;">MCPR Bin Data</caption>
						<thead style="display:none;"><th scope = "col"></th></thead>
							<tbody>
							<tr>
								<td><label><spring:message code="mcprBinDetails.mcprBinDetailsId" /></label></td>
								<td id="mcprBinDataDetailsId">${mcprViewEditHistoricDto.mcprBinDataDetailsId }</td>
								<td><label><spring:message code="mcprBinDetails.monthEnding" /></label></td>
								<td id="monthEnding">${mcprViewEditHistoricDto.monthEnding }</td>
								<td><label><spring:message code="mcprBinDetails.binNumber" /></label></td>
								<td id="binNumber">${mcprViewEditHistoricDto.binNumber}</td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.physicalContactCardCumulativeRpay" /></label></td>
								<td id="phyContactCardCummRuPayCard">${mcprViewEditHistoricDto.phyContactCardCummRuPayCard }</td>
								<td><label><spring:message code="mcprBinDetails.physicalContactCardIncremental" /></label></td>
								<td id="phyContactCardIncrementalCard">${mcprViewEditHistoricDto.phyContactCardIncrementalCard }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.physicalContactlessCardCumulativeRpay" /></label></td>
								<td id="phyContactlessCummRuPayCard">${mcprViewEditHistoricDto.phyContactlessCummRuPayCard}</td>
								<td><label><spring:message code="mcprBinDetails.physicalContactlessCardIncremental" /></label></td>
								<td id="phyContactlessIncrementalCard">${mcprViewEditHistoricDto.phyContactlessIncrementalCard }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.virtualCardCumulativeRpay" /></label></td>
								<td id="virtualCardCummRuPayCard">${mcprViewEditHistoricDto.virtualCardCummRuPayCard }</td>
								<td><label><spring:message code="mcprBinDetails.virtualCardIncremental" /></label></td>
								<td id="virtualCardIncrementalCard">${mcprViewEditHistoricDto.virtualCardIncrementalCard}</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.ecomTxnOnusCount" /></label></td>
								<td id="ecommTxnOnusCount">${mcprViewEditHistoricDto.ecommTxnOnusCount }</td>
								<td><label><spring:message code="mcprBinDetails.ecomTxnOnusAmt" /></label></td>
								<td id="ecommTxnOnusAmt">${mcprViewEditHistoricDto.ecommTxnOnusAmt }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.posContactDomesticTxnCount" /></label></td>
								<td id="posConcactCardPresentDomTxnOnusCount">${mcprViewEditHistoricDto.posConcactCardPresentDomTxnOnusCount}</td>
								<td><label><spring:message code="mcprBinDetails.posContactDomesticTxnAmt" /></label></td>
								<td id="posConcactCardPresentDomTxnOnusAmt">${mcprViewEditHistoricDto.posConcactCardPresentDomTxnOnusAmt }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.posContactlessOnlineRetailsDomesticTxnCount" /></label></td>
								<td id="posContactlessOnlRetailsDomTxnOnusCount">${mcprViewEditHistoricDto.posContactlessOnlRetailsDomTxnOnusCount }</td>
								<td><label><spring:message code="mcprBinDetails.posContactlessOnlineRetailsDomesticTxnAmt" /></label></td>
								<td id="posContactlessOnlRetailsDomTxnOnusAmt">${mcprViewEditHistoricDto.posContactlessOnlRetailsDomTxnOnusAmt}</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.posContactlessOnlineTransitDomesticTxnCount" /></label></td>
								<td id="posContactlessOnlTransitDomTxnOnusCount">${mcprViewEditHistoricDto.posContactlessOnlTransitDomTxnOnusCount }</td>
								<td><label><spring:message code="mcprBinDetails.posContactlessOnlineTransitDomesticTxnAmt" /></label></td>
								<td id="posContactlessOnlTransitDomTxnOnusAmt">${mcprViewEditHistoricDto.posContactlessOnlTransitDomTxnOnusAmt }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.posContactlessOfflineRetailsDomesticTxnCount" /></label></td>
								<td id="posContactlessOffRetailsDomTxnOnusCount">${mcprViewEditHistoricDto.posContactlessOffRetailsDomTxnOnusCount}</td>
								<td><label><spring:message code="mcprBinDetails.posContactlessOfflineRetailsDomesticTxnAmt" /></label></td>
								<td id="posContactlessOffRetailsDomTxnOnusCount">${mcprViewEditHistoricDto.posContactlessOffRetailsDomTxnOnusCount }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.posContactlessOfflineTransitDomesticTxnCount" /></label></td>
								<td id="posContactlessOffTransitDomTxnOnusCount">${mcprViewEditHistoricDto.posContactlessOffTransitDomTxnOnusCount }</td>
								<td><label><spring:message code="mcprBinDetails.posContactlessOfflineTransitDomesticTxnAmt" /></label></td>
								<td id="posContactlessOffTransitDomTxnOnusAmt">${mcprViewEditHistoricDto.posContactlessOffTransitDomTxnOnusAmt}</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.atmTxnCount" /></label></td>
								<td id="atmCardPresentDomTxnOnusCount">${mcprViewEditHistoricDto.atmCardPresentDomTxnOnusCount }</td>
								<td><label><spring:message code="mcprBinDetails.atmTxnAmt" /></label></td>
								<td id="atmCardPresentDomTxnOnusAmt">${mcprViewEditHistoricDto.atmCardPresentDomTxnOnusAmt }</td>
								<td></td>
								<td></td>
							</tr>
							<tr>
								<td><label><spring:message code="mcprBinDetails.totalCumulativeCards" /></label></td>
								<td id="totalCumulativeCards">${mcprViewEditHistoricDto.totalCumulativeCards }</td>
								<td><label><spring:message code="mcprBinDetails.totalIncrementalCards" /></label></td>
								<td id="totalIncrementalCards">${mcprViewEditHistoricDto.totalIncrementalCards }</td>
								<td></td>
								<td></td>
							</tr>
							</tbody>
						</table>
					</div>
				</div>
		</form:form>
	<div class="row">
		<div class="col-sm-12 bottom_space ">
			<hr />
			<div style="text-align:center">
				<c:if test="${EditMenu eq 'Yes'}">
					<sec:authorize access="hasAuthority('Edit MCPR Data')">	
					<c:if test="${mcprViewEditHistoricDto.requestState  eq 'A' and  not empty showbutton}">	
						
						<c:if test="${viewEditButton eq 'Y'}">
							<input name="deleteButton" type="button" class="btn btn-danger"
								id="approveRole" value="Delete"
								onclick="userAction('${mcprViewEditHistoricDto.mcprBinDataDetailsId}','/deleteMcprBinDetails');" />
								<input name="editButton" type="button" class="btn btn-success"
								id="approveRole" value="Edit" 
								onclick="userAction('${mcprViewEditHistoricDto.mcprBinDataDetailsId}','/editViewMcprData');"/>
						</c:if>
					</c:if>
					</sec:authorize>
					<button type="button" class="btn btn-danger"
					onclick="userAction('N','/dataViewEditDelete');"><spring:message code="mcprBinDetails.backBtn" /></button>
					
					</c:if>	
					
					<c:if test="${EditMenu eq 'No'}">
				 <input type="hidden" id="hiddenMonth" value="${month}">
				 <input type="hidden" id="hiddenYear" value="${year}">
				 <input type="hidden" id="hiddenBank" value="${bankName}"> 
				<button type="button" class="btn btn-danger"
					onclick="backToList();"><spring:message code="mcprBinDetails.backBtn" /></button>
					</c:if>							
				</div>
			</div>
		</div>
	</div>

</div>
