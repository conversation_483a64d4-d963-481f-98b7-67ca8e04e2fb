<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib uri = "http://java.sun.com/jsp/jstl/core" prefix = "c" %>
<%@ taglib uri = "http://java.sun.com/jsp/jstl/functions" prefix = "fn" %>
<script type="text/javascript"
	src="./static/js/validation/pwdEncode.js"></script>
	<script type="text/javascript" src="./static/js/jsencrypt.min.js"></script>
	 
<script type="text/javascript">


	window.onbeforeunload = function() {
		

		var inputs = document.getElementsByTagName("button");
		for (var i = 0; i < inputs.length; i++) {
			if (inputs[i].type == "submit") {
				inputs[i].disabled = true;
			}
		}
	};
	$(document).ready(function(event) {	

 		  // Get the user-agent string
        let userAgentString = navigator.userAgent;
    


	       // Detect Chrome
	        let chromeAgent = userAgentString.indexOf("Chrome") > -1;
	        // Detect Edge
	        let edgeAgent = userAgentString.indexOf("Edge") > -1;

            if(!chromeAgent && !edgeAgent)
            	{
	               	document.getElementById("passwordText").setAttribute("style","");
	               	document.getElementById("passwordText").setAttribute("type","password");
            	}


            const pd = document.querySelector('#passwordText');
         
       	 const message = document.querySelector('.message');

       	pd.addEventListener('keyup', function (event) {
       	     if (event.getModifierState('CapsLock')) {
       	         message.textContent = 'Caps lock is on';
       	     } else {
       	         message.textContent = '';
       	     }
       	 });



       
	});
	$(document)
	.on('click', '#forgotPassword', function(){
		var usernameValue= $('#loginIDText').val();
		var data="username,"+usernameValue;
		postData('/forgotPassword', data);
	});
	window.onload = () => {
		 const pdText = document.getElementById('passwordText');
		 pdText.onpaste = e => e.preventDefault();
		 
		
		}
	
	

	function userLogin(){
   		$('#password').val($('#passwordText').val());
   		$('#loginID').val($('#loginIDText').val());
   		const userName=$('#loginIDText').val();
   		const pd=$('#passwordText').val();
        if(!userName || !pd){
            return;
        }
        var pds = encode(pd);
        $('#passwordText').val(pds);
         $('#password').val(pds);
return true;

}

	

	
</script>



<div class="row ">
	<form id="command" class="form-signin" onsubmit="userLogin()" action="${portal}/login"
		method="post" autocomplete="off">
		<div class="col-10" id="alertMessages">
			<c:if test="${not empty successStatus}">
				<div class="alert alert-success" role="alert" id="successLogin">${successStatus}</div>
			</c:if>
			<c:if test="${not empty errorStatus}">
				<div class="alert alert-danger" role="alert" id="errorLogin">${errorStatus}</div>
			</c:if>
		</div>
		 <input type="hidden" id="publicKey" value="${PublicKey}">			
		<input class="form-control input-square" path="loginIDText"
			id="loginIDText" name="username" autofocus="autofocus"   style="text-transform:uppercase"
			placeholder="<spring:message code="login.username" text="Enter Login ID" />" maxlength="50" value = "" autocomplete="off"/>
		<input class="form-control" path="pwdText" id="passwordText" name="password"
			placeholder="<spring:message code="login.pd" text="Enter Password" />" maxlength="50" value="" type="password" autocomplete="off"/>
			<div class="message" style="color:red"></div>
		<div>
			<button type="submit" id="loginBtn" name="loginBtn"
				style="width: 40%; float: left;"
				class="btn btn-lg btn-primary btn-block"><spring:message code="login.login.button" text="Login" /> </button>
		</div>

		<div>
			<button type="button" id="forgotPassword" name="forgotPassword"
				style="width: 50%; float: right;"
				class="btn btn-lg btn-primary btn-block">
				<spring:message code="login.forgotpasswd.button" text="Forgot Password" />
			</button>


		</div>

		<div></div>
	</form>
</div>
