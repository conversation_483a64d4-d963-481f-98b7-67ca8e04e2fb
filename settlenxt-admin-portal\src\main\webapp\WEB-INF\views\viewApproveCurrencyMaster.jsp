<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/viewApproveCurrencyMaster.js" type="text/javascript"></script>

<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">
		<span data-i18n="Data"><spring:message
				code="am.lbl.appRejAction" /></span>
	</div>
	<div class="alert alert-danger remarkMust" role="alert">
		<span data-i18n="Data"><spring:message code="sm.lbl.remarkMust" /></span>
	</div>
	<c:url value="approveTipSurcharge" var="approveTipSurcharge" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveCurrencyMaster" modelAttribute="currencyMasterDto"
		action="${approveCurrencyMaster}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="currencyMaster.viewscreen.title" /></span></strong>
					</div>
					<div class="panel-body">
						<input type="hidden" id="currencyId" value="${currencyMasterDto.currencyId}" />
						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">Currency Master</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td colspan="8">
									<div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> 
											<span data-i18n="Data"><spring:message code="currencyMaster.requestInformation" /></span></strong>
									</div></td>
								</tr>
								<tr>
									<td><label><spring:message code="currencyMaster.requestType" /><span style="color: red"></span></label></td>
									<td>${currencyMasterDto.lastOperation}</td>
									<td><label><spring:message code="currencyMaster.requestDate" /><span style="color: red"></span></label></td>
									<td>${currencyMasterDto.lastUpdatedOn}</td>
									<td><label><spring:message code="currencyMaster.requestStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${currencyMasterDto.requestState =='A' }"><spring:message	code="currencyMaster.requestState.approved.description" /></c:if>
										<c:if test="${currencyMasterDto.requestState =='P' }"><spring:message code="currencyMaster.requestState.pendingApproval.description" /></c:if>
										<c:if test="${currencyMasterDto.requestState =='R' }"><spring:message code="currencyMaster.requestState.rejected.description" /></c:if>
										<c:if test="${currencyMasterDto.requestState =='D' }"><spring:message code="currencyMaster.requestState.discared.description" /></c:if>
									</td>
								</tr>
								<tr>
									<td><label><spring:message code="currencyMaster.requestBy" /><span style="color: red"></span></label></td>
									<td>${currencyMasterDto.lastUpdatedBy}</td>
									<td><label><spring:message code="currencyMaster.approverComments" /><span style="color: red"></span></label></td>
									<td colspan=2>${currencyMasterDto.checkerComments}</td>
									<td></td>
									
								</tr>
									<td colspan="8"><div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-credit-card"></span> <span data-i18n="Data">
										<spring:message code="currencyMaster.viewscreen.title" /></span></strong></div>
									</td>
								<tr>
									<td><label><spring:message code="currencyMaster.currencyId" /> <span style="color: red"></span></label></td>
									<td>${currencyMasterDto.currencyId }</td>
									<td><label><spring:message code="currencyMaster.currencyMasterCode" /><span style="color: red"></span></label></td>
									<td >${currencyMasterDto.currencyCode }</td>
									<td><label><spring:message code="currencyMaster.currencyMasterDescription" /><span style="color: red"></span></label></td>
									<td >${currencyMasterDto.currencyDescription }</td>
									
								</tr>
								<tr>
									<td><label><spring:message code="currencyMaster.currencyAlpha" /></label></td>
									<td>${currencyMasterDto.currencyAlpha }</td>
									<td><label><spring:message code="currencyMaster.currencyDecimalPosition" /></label></td>
									<td>${currencyMasterDto.currencyDecimalPosition }</td>
									<td></td>
									<td></td>
									</tr>
								
								<sec:authorize access="hasAuthority('Approve Currency Master')">
									<c:if test="${currencyMasterDto.requestState eq 'P'}">
									<tr>
										<td colspan="8"><div class="panel-heading-red  clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span>
											<span data-i18n="Data">
											<spring:message code="currencyMaster.approvalPanel.title" /></span></strong></div>
										</td>
									</tr>
									<tr>
										<td><label><spring:message
											code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td>
												<select name="select" id="apprej" onchange="display()">
													<option value="N"><spring:message code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message code="AM.lbl.reject" /></option>
												</select>
											</td>
											<td>
												<div style="text-align:center">
												<label><spring:message code="AM.lbl.remarks" /><span style="color: red">*</span></label>
												</div>
											</td>
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div>
											</td>
										</tr>
									</c:if>
								</sec:authorize>
							</tbody>
						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Currency Master')">
										<c:if test="${currencyMasterDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approvecard" value="Submit"
												onclick="postAction('/approveCurrencyMaster');" />
										</c:if>
									</sec:authorize>
													
									<sec:authorize access="hasAuthority('Edit Currency Master')">				
									<c:if test="${currencyMasterDto.requestState  eq 'R' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="userAction('/discardCurrencyMaster','${currencyMasterDto.currencyId}');" />
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="edit('/editCurrencyMaster','${currencyMasterDto.currencyId}','pendingApprove');"/>
									</c:if>
									</sec:authorize>
									
										<button type="button" class="btn btn-danger"
										onclick="backAction('P','/currencyMasterForApproval');">
										<spring:message code="currencyMaster.backBtn" /></button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

