var modalConfigData = [];
var showMsgRsonCd = true;
var star = '*';
var pCodeStatus = false;
var availableReasonCodes = $("#availableReasonCodes").val();
var actionCode = '';
function openDisputeModal(jsonData, action) {
	actionCode = action;
	$('#actions').val("SELECT");
	$("#submitDispute").prop('disabled', false);
	$('#actionErrorMsg').hide();
	$('#transitionErrorMsg').hide();
	$('#errfileArr').hide();
	modalConfigData = jsonData;

	var modalConfigIndex = modalConfigData.findIndex(obj => obj.actionCode == action);
	var modalConfig = modalConfigData[modalConfigIndex];

	if (modalConfig) {
		$('#modelName').val(modalConfig.name);
		$("#modalerrorMessage").html('');
		$("#disputeModal").modal("show");
		$("#disputeModalTitle").html(modalConfig.title);
		$("#mti").val(modalConfig.mti);
		$("#funcCode").val(modalConfig.funcCode);
		var actionCodeVal = (modalConfig.actionCode).split('_')[0];
		$("#actionCode").val(actionCodeVal);
		var controls = '';
		var onloadfunctions = [];
		var attributeInitialData = [];
		var hideOnloadAttributes = [];
		modalConfig.attributes.forEach(function(attribute) {
			var subAttributes = '';
			var attributeSrcData = {};
			if (attribute.onchange) {
				subAttributes += ' onChange="' + attribute.onchange + '()" ';
			}

			if (attribute.onload) {
				onloadfunctions.push(attribute.onload);
			}

			if (attribute.hideOnload == "Y") {
				hideOnloadAttributes.push(attribute.attributeName);
			}

			if (attribute.srcAttribute && attribute.srcAttribute != "") {
				attributeSrcData.name = 'modal' + attribute.attributeName;
				attributeSrcData.type = attribute.type;
				attributeSrcData.disabled = attribute.disabled;
				attributeSrcData.srcAttribute = attribute.srcAttribute;
				attributeInitialData.push(attributeSrcData);
			}
			if (attribute.type == "amount" || attribute.type == "text") {
				controls += '<div class="input__container div' + attribute.attributeName + '">';
				if (attribute.mandatory == "Y") {
					controls += '<label>' + attribute.label + '&nbsp; <span style="color: red">' + star + '</label>';
				} else {
					controls += '<label>' + attribute.label + '</label>';
				}
				if (attribute.disabled == "Y") {
					controls += '<input type="text" autocomplete="off" ' + subAttributes + ' id="modal' + attribute.attributeName + '" class="sub_input" maxlength="100" disabled />';
				} else {
					controls += '<input type="text" autocomplete="off" ' + subAttributes + ' id="modal' + attribute.attributeName + '" class="sub_input" maxlength="100" />';
				}
				controls += '</div>';
				controls += '<div class="error" id="err' + attribute.attributeName + '"></div>';
			}
			if (attribute.type == "textarea") {
				controls += '<div class="input__container div' + attribute.attributeName + '">';
				if (attribute.mandatory == "Y") {
					controls += '<label>' + attribute.label + '&nbsp; <span style="color: red">' + star + '</label>';
				} else {
					controls += '<label>' + attribute.label + '</label>';
				}
				if (attribute.disabled == "Y") {
					controls += '<textarea autocomplete="off" ' + subAttributes + ' id="modal' + attribute.attributeName + '" class="sub_input"  disabled ></textarea>';
				} else {
					controls += '<textarea autocomplete="off" ' + subAttributes + ' id="modal' + attribute.attributeName + '" class="sub_input" ></textarea>';
				}
				controls += '</div>';
				controls += '<div class="error" id="err' + attribute.attributeName + '"></div>';
			}
			if (attribute.type == "document") {
				controls += '<div class="card-body-document fileContorl div' + attribute.attributeName + '">';
				controls += '<div class="row"><div class="col-md-12 file_upload"><div>';
				if (attribute.mandatory == "Y") {
					controls += '<label>' + attribute.label + '&nbsp; <span style="color: red">' + star + '</label></div>';
				} else {
					controls += '<label>' + attribute.label + '</label></div>';
				}
				controls += '<div class="file_select">';
				if (attribute.disabled == "Y") {
					controls += '<input type="file"  ' + subAttributes + '  multiple="true" name="modal' + attribute.attributeName + '"  id="modal' + attribute.attributeName + '" class="sub_input" disabled/>';
				} else {
					controls += '<input type="file"  ' + subAttributes + '  multiple="true" name="modal' + attribute.attributeName + '"  id="modal' + attribute.attributeName + '" class="sub_input" />';
				}
				controls += '<label id="title">(.xml,. jpeg,.jpg,.png,.tif,.tiff,.pdf,.bmp format allowed.)</label></div>';
				controls += '<div class="file_select">';
				controls += '<button class="btn btn-default" id="reset" type="reset" onclick="resetDoc()">Reset</button>';

				controls += '</div></div></div></div>';
				controls += '<div class="error" id="err' + attribute.attributeName + '"></div>';
			}

			if (attribute.type == "lookup") {
				controls += '<div class="input__container div' + attribute.attributeName + '">';
				if (attribute.mandatory == "Y") {
					controls += '<label>' + attribute.label + '&nbsp; <span style="color: red">' + star + '</label>';
				} else {
					controls += '<label>' + attribute.label + '</label>';
				}
				if (attribute.disabled == "Y") {
					controls += '<select  ' + subAttributes + ' id="modal' + attribute.attributeName + '" class="form-control sub_select" disabled>';
				} else {
					controls += '<select  ' + subAttributes + ' id="modal' + attribute.attributeName + '" class="form-control sub_select">';
				}
				controls += '<option value="0">--Select--</option>';
				if (attribute.lookupData) {
					attribute.lookupData.forEach(function(option) {
						controls += '<option value="' + option.code + '">' + option.value + '</option>';
					});
				}
				controls += '</select>';

				controls += '</div>';
				controls += '<div class="error" id="err' + attribute.attributeName + '"></div>';
			}

			if (attribute.type == "lookup-api") {
				if (attribute.lookupData) {
					attribute.lookupData.forEach(function(option) {
						document.getElementById(attribute.attributeName).value = '';
						var tokenValue = document.getElementsByName("_TransactToken")[0].value;
						if (option.type == "regular") {
							const resultValue = document.getElementById(option.data).value;
							$.ajax({
								url: option.url,
								type: "POST",
								async: false,
								data: {
									reasonCode: resultValue, "_TransactToken": tokenValue,
								},
								dataType: "json",
								"beforeSend": function(xhr) {
									xhr.setRequestHeader('_TransactToken', tokenValue);
								},
								success: function(data) {
									var controls1 = '<div class="input__container div' + attribute.attributeName + '">';
									if (attribute.mandatory == "Y") {
										controls1 += '<label>' + attribute.label + '&nbsp; <span style="color: red">' + star + '</label>';
									} else {
										controls1 += '<label>' + attribute.label + '</label>';
									}
									if (attribute.disabled == "Y") {
										controls1 += '<select  ' + subAttributes + ' id="modal' + attribute.attributeName + '" class="form-control sub_select" disabled>';
									} else {
										controls1 += '<select  ' + subAttributes + ' id="modal' + attribute.attributeName + '" class="form-control sub_select">';
									}
									controls1 += '<option value="0">--Select--</option>';
									for (var i of data) {
										controls1 += '<option value="' + i.value + '">' + i.code + '</option>';
									}

									controls1 += '</select>';
									controls1 += '</div>';
									controls1 += '<div class="error" id="err' + attribute.attributeName + '"></div>';
									controls  += controls1;
									
								}
							});
						} else {
							const value = document.getElementById(option.data).value;
							const json = JSON.parse(value);
							if (action in json) {
								const resultValue = json[action];
							 	if (resultValue.length > 0) {
									if(attribute.reasonCodeDefault == "Y"){
										 onloadfunctions.push("setDefaultReasonCode");
									}	
									else if(resultValue.length == 1 && resultValue[0] == "Default" ){
									    onloadfunctions.push("setReasonCodeDropDown");
									}
									else{
										console.log("In Ajax");
										$.ajax({
											url: option.url,
											type: "POST",
											async: false,
											data: {
												reasonSubType: resultValue, "_TransactToken": tokenValue,
											},
											dataType: "json",
											"beforeSend": function(xhr) {
												xhr.setRequestHeader('_TransactToken', tokenValue);
											},
											success: function(data) {
												var controls2 = '<div class="input__container div' + attribute.attributeName + '">';
												if (attribute.mandatory == "Y") {
													controls2 += '<label>' + attribute.label + '&nbsp; <span style="color: red">' + star + '</label>';
												} else {
													controls2 += '<label>' + attribute.label + '</label>';
												}
												if (attribute.disabled == "Y") {
													controls2 += '<select  ' + subAttributes + ' id="modal' + attribute.attributeName + '" class="form-control sub_select" disabled>';
												} else {
													controls2 += '<select  ' + subAttributes + ' id="modal' + attribute.attributeName + '" class="form-control sub_select">';
												}
												controls2 += '<option value="0">--Select--</option>';
												if (data.length > 0) {
													for (var i = 0; i < data.length; i++) {
														if (resultValue.length > 0) {
															if (attribute.showDescOnly == "Y"){
																if (resultValue[i] == attribute.selectValue) {
																	controls2 += '<option value="' + resultValue[i] + '" selected>' + data[i] + '</option>';
																} else {
																	controls2 += '<option value="' + resultValue[i] + '">' +  data[i] + '</option>';
																}
															}else{
																if (resultValue[i] == attribute.selectValue) {
																	controls2 += '<option value="' + resultValue[i] + '" selected>' + resultValue[i] + ' - ' + data[i] + '</option>';
																} else {
																	controls2 += '<option value="' + resultValue[i] + '">' + resultValue[i] + ' - ' + data[i] + '</option>';
																}
															}
														}
													}
												} else {
													$('.div' + attribute.attributeName).hide();
													$('#modal' + attribute.attributeName).val('');
												}
	
												controls2 += '</select>';
												controls2 += '</div>';
												controls2 += '<div class="error" id="err' + attribute.attributeName + '"></div>';
												controls  += controls2;
												
											}
										});
										}
										}
										else
										{
											showMsgRsonCd = false;
										}
									}
								}
					});
				}
			}
		});

		$("#inputControls").html(controls);
		hideOnloadAttributes.forEach(function(attributeName) {
			$('.div' + attributeName).hide();
		});
		onloadfunctions.forEach(function(onloadfn) {
			if (window[onloadfn]) {
				window[onloadfn]();
			}
		});
		attributeInitialData.forEach(function(attributeSrcData) {
			if (attributeSrcData.type == "amount") {
				if (!$('#' + attributeSrcData.srcAttribute).val()) {
					$('#' + attributeSrcData.name).val(0.00);
				} else {
					if ($('#' + attributeSrcData.srcAttribute).val() == '' && attributeSrcData.srcAttribute == "amtApproval") {
						$('#' + attributeSrcData.name).val($('#modalamountTransaction').val());
					} else {
						$('#' + attributeSrcData.name).val($('#' + attributeSrcData.srcAttribute).val());
					}
					document.querySelectorAll("#" + attributeSrcData.name).forEach(ele => {
						var x = parseFloat(ele.value)
						ele.value = x.toFixed(2)
					})
				}
				if (attributeSrcData.disabled == "Y") {
					$('#' + attributeSrcData.name).prop('disabled', true);
				}
			} else {
				$('#' + attributeSrcData.name).val($('#' + attributeSrcData.srcAttribute).val());		
				if (attributeSrcData.disabled == "Y" && ($('#' + attributeSrcData.srcAttribute).val() !== "")) {
					$('#' + attributeSrcData.name).prop('disabled', true);
				}else{
					$('#' + attributeSrcData.name).prop('disabled', false);
				}
			}
		});

		if ($('#processingCode').val() && $('#processingCode').val() !== '' && !$('#processingCode').val().startsWith('09')) {
			$('.divamountAdditional').hide();
			$('#modalamountAdditional').val('');
			pCodeStatus = true;
		}
	} else {
		$('#actionErrorMsg').show();
		$('#actions').val("SELECT");
	}
}

function showHideFileUpload() {
	if ($('#modaldocumentIndicator').val() == "Y") {
		$(".fileContorl").show();
	} else {
		$(".fileContorl").hide();
	}

}
function filterReasonCode() {
	var subType = $("#modalreasonSubtype").val();
	$("#modalmessageReasonCode").empty();
	var subTypeIndex = reasonSubtypeCodes.findIndex(obj => obj.subType == subType);
	var subTypeData = reasonSubtypeCodes[subTypeIndex];
	if (subTypeData) {
		$("#modalmessageReasonCode").append('<option value="0">--Select--</option>');

		subTypeData.reasonCodes.forEach(function(reasonCode) {
			$("#modalmessageReasonCode").append('<option value="' + reasonCode.code + '">' + reasonCode.value + '</option>');

		});
	}
}
function populateReasonSubtype() {
	$("#modalreasonSubtype").empty();
	$("#modalreasonSubtype").append('<option value="0">--Select--</option>');
	if (reasonSubtypeCodes) {
		reasonSubtypeCodes.forEach(function(reasonSubtype) {
			$("#modalreasonSubtype").append('<option value="' + reasonSubtype.subType + '">' + reasonSubtype.subType + '</option>');
		});
	}

}
function enableDisableAmountTxn() {
	if ($('#modalfullPartial').val() == "P") {
		$("#modalamountTransaction").prop('disabled', false);
	} else {
		$("#modalamountTransaction").prop('disabled', true);
	}
}
function showOrHideMsgRsonCode(){
	if(!showMsgRsonCd){
		$('.div' + 'messageReasonCode').hide();
	}
}
function setReasonCodeDropDown(){
	const json = JSON.parse(availableReasonCodes);
	var rsonSubType = $("#modalreasonSubtype").val();
	if(rsonSubType === undefined){
		rsonSubType = 'Default';
	}
	if(rsonSubType in json){
		let data = json[rsonSubType];
		const map = new Map(Object.entries(data));
		let options = '<option value="0">--Select--</option>';
    	map.forEach(function(value, key) {
			options += '<option value="' + key + '">' + key + ' - ' + value + '</option>';
		})
   		$("select#modalmessageReasonCode").html(options); 
	}else{
		 let options = '<option value="0">--Select--</option>';
		$("select#modalmessageReasonCode").html(options); 
	}
}
function setDefaultReasonCode(){
	var availableReasonSubTypes = $("#availableReasonSubType").val();
	var jsonAvailableRsonSubTypes = JSON.parse(availableReasonSubTypes);
	var jsonAvailableRsonCodes = JSON.parse(availableReasonCodes);
	if((Object.keys(jsonAvailableRsonSubTypes).length > 0) && (Object.keys(jsonAvailableRsonCodes).length > 0)){
		var rsonSubType = (jsonAvailableRsonSubTypes[(Object.keys(jsonAvailableRsonSubTypes))[0]])[0];
		var rsonCode = (Object.keys(jsonAvailableRsonCodes[rsonSubType])[0]);
		var defaultRsonCode = '<option value="' + rsonCode + '" selected>' + rsonCode + '</option>';
		$('#modalmessageReasonCode').append(defaultRsonCode);
	}
}
function setReasonCodesWithoutSubType(){ 
	var availableReasonSubTypes = $("#availableReasonSubType").val();
	var jsonAvailableRsonSubTypes = JSON.parse(availableReasonSubTypes);
	var jsonAvailableRsonCodes = JSON.parse(availableReasonCodes);
	var rsonSubTypeList = jsonAvailableRsonSubTypes[actionCode];
	var options = '<option value="0">--Select--</option>';
	if((rsonSubTypeList.length > 0) && (Object.keys(jsonAvailableRsonCodes).length > 0)){
		 for(var i of rsonSubTypeList){
					let data = jsonAvailableRsonCodes[i];
					const map = new Map(Object.entries(data));
			   		map.forEach(function(value, key) {
					options += '<option value="' + key + '">' + key + ' - ' + value + '</option>'; 
				})
				
			}
	}
	$("select#modalmessageReasonCode").html(options); 
}

function setAmountFromReasonCode() {
	if ($('#modalmessageReasonCode').val() == "1065") {
		$('#modalamountTransaction').val($('#amountTransaction').val());
		$('#modalamountTransaction').prop('disabled', true);
	}
}

function resetDoc() {
	$('#modalfileArr').val('');
	$('#errfileArr').hide();
}

function validateMemMsg() {
	$("#submitDispute").prop('disabled', false);
	$('#errmemberMessageText').text('');
	$('#errmemberMessageText').hide();
	var memMsgTxt = document.getElementById("modalmemberMessageText").value;
		if(memMsgTxt.length > 100){
			$('#errmemberMessageText').text('Message length should be less than 100 characters');
			$('#errmemberMessageText').show();
			$("#submitDispute").prop('disabled', true);
		}else{
			$('#errmemberMessageText').text('');
			$('#errmemberMessageText').hide();
		}
	
}

function validateXmlFile() {
	$("#submitDispute").prop('disabled', false);
	$('#errfileArr').text('');
	$('#errfileArr').hide();
	var specialChars = "#%&$*:<>?,\+=@^`'()_;~[]/{|}";
	var filesLength = document.getElementById('modalfileArr').files.length;
	var flag = false;
	var flagExtension = false;
	for (var x = 0; x < filesLength; x++) {
		var uplodedFile = $("#modalfileArr").get(0).files[x].name;
		var fileNameSplit = uplodedFile.split('.');

		for (var j of specialChars) {
			if (fileNameSplit[0].length > 100 || fileNameSplit[0].indexOf(j) > -1) {
				flag = true;
				break;
			}
		}

		var fileExtension = fileNameSplit[fileNameSplit.length - 1].toString().toLowerCase();
		if (fileExtension !== 'xml' && fileExtension !== 'jpeg' && fileExtension !== 'png' && fileExtension !== 'tif' && fileExtension !== 'pdf' && fileExtension !== 'tiff' && fileExtension !== 'bmp') {
			flagExtension = true;
			break;
		} 
	}

	if (flag && !flagExtension) {
		$('#errfileArr').text("File Name is having special characters or file name is too long");
		$('#errfileArr').show();
		$("#submitDispute").prop('disabled', true);
	}else if(flagExtension){
		$('#errfileArr').text("File Extension is not allowed");
		$('#errfileArr').show();
		$("#submitDispute").prop('disabled', true);
	}
}

function submitDynData() {
	$("#submitDispute").prop('disabled', true);
	var screenName = $('#modelName').val();
	$("#modalerrorMessage").html('');
	var modalConfigIndex = modalConfigData.findIndex(obj => obj.name == screenName);
	var modalConfig = modalConfigData[modalConfigIndex];
	var result = false;
	if (modalConfig) {
		modalConfig.attributes.forEach(function(attribute) {
			$('#err' + attribute.attributeName).text('');
			$('#err' + attribute.attributeName).hide();
			
			if (attribute.attributeName == "amountAdditional" && pCodeStatus) {
				return;
			}
			if (attribute.disabled !== "Y" && (attribute.type == "amount" || attribute.type == "text" || attribute.type == "textarea")) {
				if (attribute.mandatory === "Y") {
					if (!$("#modal" + attribute.attributeName).val() || $("#modal" + attribute.attributeName).val() === '') {
						$('#err' + attribute.attributeName).text(attribute.validationMessage + " Reason: Value not set");
						$('#err' + attribute.attributeName).show();
						result = true;
						return;
					}
					if (attribute.minlength && $("#modal" + attribute.attributeName).val().length < attribute.minlength) {
						$('#err' + attribute.attributeName).text(attribute.validationMessage + " Reason: Length is less than " + attribute.minlength);
						$('#err' + attribute.attributeName).show();
						result = true;
						return;
					}
					if (attribute.maxlength && $("#modal" + attribute.attributeName).val().length > attribute.maxlength) {
						$('#err' + attribute.attributeName).text(attribute.validationMessage + " Reason: Length is more than " + attribute.maxlength);
						$('#err' + attribute.attributeName).show();
						result = true;
						return;
					}
				} else {
					if ($("#modal" + attribute.attributeName).val() && $("#modal" + attribute.attributeName).val() !== '' && attribute.minlength && $("#modal" + attribute.attributeName).val().length < attribute.minlength) {
						$('#err' + attribute.attributeName).text(attribute.validationMessage + " Reason: Length is less than " + attribute.minlength);
						$('#err' + attribute.attributeName).show();
						result = true;
						return;
					}
					if ($("#modal" + attribute.attributeName).val() && $("#modal" + attribute.attributeName).val() !== '' && attribute.maxlength && $("#modal" + attribute.attributeName).val().length > attribute.maxlength) {
						$('#err' + attribute.attributeName).text(attribute.validationMessage + " Reason: Length is more than " + attribute.maxlength);
						$('#err' + attribute.attributeName).show();
						result = true;
						return;
					}
				}
			}
			if (attribute.type == "amount" && attribute.disabled !== "Y") {
				if ($("#modal" + attribute.attributeName).val() && isNaN($("#modal" + attribute.attributeName).val())) {
					$('#err' + attribute.attributeName).text(attribute.validationMessage + " Reason: It is not a number");
					$('#err' + attribute.attributeName).show();
					result = true;
					return;
				}
				if (attribute.compAttribute !== '' && $("#modal" + attribute.attributeName).val() && $("#" + attribute.compAttribute).val()
					&& $("#modal" + attribute.attributeName).val() !== '' && $("#" + attribute.compAttribute).val() !== '') {
					var amount = $("#modal" + attribute.attributeName).val();
					var compAmount = $("#" + attribute.compAttribute).val();
					if (attribute.sumationAttribute != '' && $("#" + attribute.sumationAttribute).val() && $("#" + attribute.sumationAttribute).val() !== ''
						&& parseFloat($("#" + attribute.sumationAttribute).val()) > 0) {
						amount = parseFloat($("#modal" + attribute.attributeName).val()) + parseFloat($("#" + attribute.sumationAttribute).val());
					}
					if (attribute.primCompAttribute != '' && $("#" + attribute.primCompAttribute).val() && $("#" + attribute.primCompAttribute).val() !== ''
						&& parseFloat($("#" + attribute.primCompAttribute).val()) > 0) {
						compAmount = $("#" + attribute.primCompAttribute).val();
					}
					if ((attribute.operator == "GTE" && parseFloat(compAmount) > parseFloat(amount)) ||
						(attribute.operator == "GT" && parseFloat(compAmount) >= parseFloat(amount)) ||
						(attribute.operator == "LTE" && parseFloat(compAmount) < parseFloat(amount)) ||
						(attribute.operator == "LT" && parseFloat(compAmount) <= parseFloat(amount))) {
						var desc;
						if (attribute.operator.startsWith("GT")) {
							desc = "greater than or equal to";
						} else {
							desc = "less than or equal to";
						}
						$('#err' + attribute.attributeName).text(attribute.validationMessage + " Reason: The auto-populated value is " + desc + "  entered value");
						$('#err' + attribute.attributeName).show();
						result = true;
						return;
					}
				}
			}
			
			if (attribute.type == "document") {
				const input = document.querySelector("#modal" + attribute.attributeName);
				if (attribute.minlength && attribute.maxlength && input.files && input.files.length > 0 && input.files.length < attribute.minlength && input.files.length > attribute.maxlength) {
					$('#err' + attribute.attributeName).text(attribute.validationMessage + " Reason: Files to upload should be between " + attribute.minlength + " and " + attribute.maxlength);
					$('#err' + attribute.attributeName).show();
					result = true;
					return;
				}
			}
			if ($("#modal" + attribute.attributeName).val() && $("#modal" + attribute.attributeName).val() !== '' && attribute.validationExp && attribute.validationExp !== '' && (attribute.type == "text" || attribute.type == "amount" || attribute.type == "textarea") && attribute.disabled !== "Y") {
				var regex = new RegExp(attribute.validationExp);
				if (!regex.test($("#modal" + attribute.attributeName).val())) {
					$('#err' + attribute.attributeName).text(attribute.validationMessage + " Reason: Validation expression not fulfilled");
					$('#err' + attribute.attributeName).show();
					result = true;
					return;
				}
			}
			if (attribute.type.startsWith("lookup") && $("#modal" + attribute.attributeName).val() === "0" && attribute.mandatory === "Y" && attribute.disabled !== "Y") {
				$('#err' + attribute.attributeName).text(attribute.validationMessage);
				$('#err' + attribute.attributeName).show();
				result = true;
				return;
			}
		});
		if (result) {
			$("#submitDispute").prop('disabled', false);
		}
		else {
			saveMemberActionData();
		}
	}
}
function saveMemberActionData() {
	var screenName = $('#modelName').val();
	var modalConfigIndex = modalConfigData.findIndex(obj => obj.name == screenName);
	var modalConfig = modalConfigData[modalConfigIndex];
	if (modalConfig) {
		modalConfig.attributes.forEach(function(attribute) {
			if (attribute.type !== "document") {
				if((!(attribute.type == "lookup" && !showMsgRsonCd))|| (attribute.attributeName == 'documentIndicator')){
					$("#" + attribute.destAttribute).val($("#modal" + attribute.attributeName).val());
				}
			}
		});
	}
	var url = "/saveMemberActionData";
	var mti = $("#mti").val();
	var funcCode = $("#funcCode").val();
	var orgFuncCode = $("#orgFuncCode").val();
	 actionCode = $("#actionCode").val();
	var amtTran = $("#amountTransaction").val();
	var cardAcceptorZipCode = $("#cardAcceptorZipCode").val();
	var merchantTelephoneNumber = $("#merchantTelephoneNumber").val();
	var cardHolderUID = $("#cardHolderUID").val();
	var cardHolderIncomeTaxPan = $("#cardHolderIncomeTaxPan").val();
	var cardAcptAdnlAddress = $("#cardAcceptorAdditionalAddress").val();
	var merchantIndicator = $("#merchantIndicator").val();
	var partialInd = $("#fullPartial").val();
	var messageReasonCode = $("#messageReasonCode").val();
	var internalTrackNo = $("#internalTrackingNumber").val();
	var docInd = $("#documentIndicator").val();
	var memMsgTxt = $("#memberMessageText").val();
	var amtAdd = $("#amountAdditional").val();
	var ctrlNo = $("#controlNo").val();
	var txnId = $("#txnId").val();
	var rrn = $("#rrnval").val();
	var originalTableName = $("#orgTabName").val();
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	var temp = document.getElementById("acqRefData").value;
	var acqRefData = $('<div>').text(temp).html();
	
	const data = {
		"mti": mti, "funcCode": funcCode, "toState": actionCode, "amtTran": amtTran, "cardAcceptorZipCode": cardAcceptorZipCode,
		"merchantTelephoneNumber": merchantTelephoneNumber, "cardHldrUID": cardHolderUID, "cardHldrInTaxPan": cardHolderIncomeTaxPan,
		"cardAcptAdnlAddress": cardAcptAdnlAddress, "merchantCatInd": merchantIndicator, "reasonCode": messageReasonCode, "partialInd": partialInd,
		"docInd": docInd, "memMsgTxt": memMsgTxt, "ctrlNo": ctrlNo, "amtAdd": amtAdd, "internalTrackNo": internalTrackNo, "txnId": txnId, "rrn": rrn,
		"originalTableName": originalTableName, "orgFuncCode": orgFuncCode, "acqRefData":acqRefData
	};

	var formData = new FormData();
	formData.append("objectData", JSON.stringify(data));

	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	var linkurl = pathName + url;
	$.ajax({
		url: linkurl,
		type: "POST",
		cache: false,
		processData: false,  // tell jQuery not to process the data
		contentType: false,  // tell jQuery not to set contentType
		dataType: "json",
		data: formData,
		"beforeSend": function(xhr) {
			xhr.setRequestHeader('_TransactToken', tokenValue);
		},

		success: function(response) {
			$("#disputeModal").modal("hide");
			if (response.status === "BSUC_0001") {
				$('#actions').hide();
				$('#transitionSuccessMsg').show();
				$(".refreshButton").show();
			}
			else {
				document.getElementById("navbar").scrollIntoView({ behavior: "smooth" });
				$('#transitionErrorMsg').show();
			}
		},
		error: function(_request, _status, _error) {
			// Display Error Message
		}
	});
}

function resetDisputeData() {
	var screenName = $('#modelName').val();
	var modalConfigIndex = modalConfigData.findIndex(obj => obj.name == screenName);
	var modalConfig = modalConfigData[modalConfigIndex];
	if (modalConfig) {
		modalConfig.attributes.forEach(function(attribute) {
			$('#modal' + attribute.attributeName).val('');
			if (attribute.type.startsWith("lookup")) {
				$('#modal' + attribute.attributeName).val('0');
				//Reset options from the dropdown list when clear
				if(attribute.resetOnClear == "Y"){
					var dropdown = document.getElementById('modal' + attribute.attributeName);
				    for (var i = dropdown.options.length - 1; i > 0; i--) {
				        dropdown.remove(i);
				    }
				}
			}
			$('#modal' + attribute.attributeName).prop('disabled', false);
			$('#err' + attribute.attributeName).hide();
		});
	}
}

function hideData() {
	resetDisputeData();
	$("#disputeModal").modal("hide");
	$('#actions').val("SELECT");
}
