package org.npci.settlenxt.adminportal.validator.check;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;

@PropertySource("mappings/NPCI/duplicateFileCheck.properties")
public class DuplicateCheckConfig {
	@Value("${fileName}")
	private boolean fileName = true;
	
	@Value("${header}")
	private boolean header = true;
	
	@Value("${fileSize}")
	private boolean fileSize = true;
	
	@Value("${content}")
	private boolean content = true;
	
	@Value("${mode}")
	private String mode = "T";


	
	public boolean isFileNameCheckEnabled() {
		return fileName;
	}

	public boolean isHeaderCheckEnabled() {
		return header;
	}

	public boolean isFileSizeCheckEnabled() {
		return fileSize;
	}

	public boolean isContentCheckEnabled() {
		return content;
	}

	public String getMode() {
		return mode;
	}
}
