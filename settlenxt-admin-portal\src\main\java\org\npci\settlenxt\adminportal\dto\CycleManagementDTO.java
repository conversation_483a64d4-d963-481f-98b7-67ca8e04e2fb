package org.npci.settlenxt.adminportal.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.google.gson.JsonArray;
import com.googlecode.jmapper.annotations.JGlobalMap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JGlobalMap
@ToString
public class CycleManagementDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	private String cycleDate;
	private String bankType;
	private String[] reportStatusArr;
	private String cycleNumber;
	private String currentCycleNumber;
	private transient JsonArray retryData;
	private List<Map<String, String>> cycleData;
	private String reportStatus;
	private String internalCycleNumber;
	private String statusSource;
	private String statusMessage;
	private String statusId;
	private String forceOperation;
	private String systemDate;
	private String settlementProductId;
	private Map<String, String> settleProduct;
	private String requestType;
	private String reportType;
	private List<String> participantList;
	private String participantId;
	private String errorMessage;
	private String statusCode;
	private String uid;
	private String requestDate;
	private Map<String, String> regenerationStatusMap;
	private String regenerationStatus;
	private String mergingStatus;
	private String fileName;
	private String outgoingFileStatus;
	private String startTime;
	private String icStatus;
	private String faInstance1;
	private String faInstance2;
	private String faInstance3;
	private String faInstance4;
	private String icnOrOutgoingFileName;
	private List<String> reportTypeArr;
}
