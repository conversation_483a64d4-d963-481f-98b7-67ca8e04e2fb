<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.npci.settlenxt.adminportal.repository.ForexRateRepository">

<select id="getForexRateList" resultType="ForexRateDTO">
        select d.currency_rate_id as forexRateId,d.forex_id as networkId,d.central_rate as rateConversion,
        d.valid_date as settleDate,d.from_currency as currencyFrom ,d.to_currency as currencyTo,
        e.description as networkIdLookup,
        f.description as currencyFromLookup,
        g.description as currencyToLookup
         from currency_rate_stg d
         left join lookup e on e.code = d.forex_id and e.type='ForexRate_NetworkId'
         left join lookup f on f.code = d.from_currency and f.type='FromForexRate_Currency' 
         left join lookup g on g.code = d.to_currency and g.type='ToForexRate_Currency' 
         WHERE d.forex_id=#{networkId} and Date(d.valid_date)=#{settleDate} and d.request_state ='A';
</select>

<select id="getPendingForexRateList" resultType="ForexRateDTO">
		SELECT t.currency_rate_id as forexRateId, t.forex_id as networkId,t.central_rate as rateConversion,
		t.valid_date as settleDate, t.from_currency as currencyFrom,t.to_currency as currencyTo, t.checker_comments  as checkerComments, 
		t.status as status,t.last_operation as lastOperation, t.request_state as requestState,
		e.description as networkIdLookup,
        f.description as currencyFromLookup,
        g.description as currencyToLookup from  currency_rate_stg t 
		left join lookup e on e.code = t.forex_id and e.type='ForexRate_NetworkId'
        left join lookup f on f.code = t.from_currency and f.type='FromForexRate_Currency'
        left join lookup g on g.code = t.to_currency and g.type='ToForexRate_Currency'
		WHERE t.request_state in ('P','R');
		</select>
		
<select id="getForexRateViewPage" resultType="ForexRateDTO">
		select d.currency_rate_id as forexRateId,d.forex_id as networkId,d.central_rate as rateConversion,
        d.valid_date as settleDate,d.from_currency as currencyFrom ,d.to_currency as currencyTo,
        e.description as networkIdLookup,
        f.description as currencyFromLookup,
        g.description as currencyToLookup
        from currency_rate_stg d 
        left join lookup e on e.code = d.forex_id and e.type='ForexRate_NetworkId'
         left join lookup f on f.code = d.from_currency and f.type='FromForexRate_Currency'
         left join lookup g on g.code = d.to_currency and g.type='ToForexRate_Currency'
         where d.currency_rate_id=#{forexRateId} and d.request_state ='A'
        </select>

<select id="getForexRateStgInfoById" resultType="ForexRateDTO">
	SELECT d.currency_rate_id as forexRateId, d.forex_id as networkId,d.central_rate as rateConversion ,
	d.valid_date as settleDate,d.from_currency as currencyFrom, d.to_currency as currencyTo,
	d.last_operation as lastOperation,d.request_state as requestState,
	d.created_by as lastUpdatedBy,d.checker_comments as
	checkerComments,d.last_updated_on as lastUpdatedOn,
	e.description as networkIdLookup,
    f.description as currencyFromLookup,
    g.description as currencyToLookup
	from currency_rate_stg d
	 left join lookup e on e.code = d.forex_id and e.type='ForexRate_NetworkId'
         left join lookup f on f.code = d.from_currency and f.type='FromForexRate_Currency'
         left join lookup g on g.code = d.to_currency and g.type='ToForexRate_Currency'
	where d.currency_rate_id = #{forexRateId}
</select>

<insert id="insertForexRateStg" >
	INSERT INTO currency_rate_stg (currency_rate_id,forex_id,
	central_rate,valid_date,from_currency,to_currency,CREATED_BY,CREATED_ON,
	LAST_UPDATED_BY,LAST_UPDATED_ON, request_state, last_operation,
	status,buy_rate,sell_rate) VALUES
	(#{forexRateId}, #{networkId}, #{rateConversion}, #{settleDate},
	#{currencyFrom},#{currencyTo},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},
	#{requestState}, #{lastOperation}, #{status}, #{rateConversion}, #{rateConversion})
</insert>

<insert id="insertForexRateListStg">
INSERT INTO currency_rate_stg (currency_rate_id,forex_id,
	central_rate,valid_date,from_currency,to_currency,CREATED_BY,CREATED_ON,
	LAST_UPDATED_BY,LAST_UPDATED_ON, request_state, last_operation,
	status,buy_rate,sell_rate) VALUES <foreach  collection='forexRates' item='forexRate' separator=','>
	(#{forexRate.forexRateId}, #{forexRate.networkId}, #{forexRate.rateConversion}, #{forexRate.settleDate},
	#{forexRate.currencyFrom},#{forexRate.currencyTo},#{forexRate.createdBy},#{forexRate.createdOn},#{forexRate.lastUpdatedBy},#{forexRate.lastUpdatedOn},
	#{forexRate.requestState}, #{forexRate.lastOperation}, #{forexRate.status}, #{forexRate.rateConversion}, #{forexRate.rateConversion})</foreach>
</insert>

<select id="getLatestConversionRate" resultType="string">
	select central_rate from currency_rate where forex_id =#{networkId} and
	from_currency =#{fromCurrency} and to_currency =#{toCurrency} order by last_updated_on
	desc limit 1;
</select>	
	
<select id="fetchForexRateIdSequence" resultType="int">	
		SELECT nextval('currency_rate_id_seq')
	</select>
	
<select id="getForexRateStg" resultType="ForexRateDTO">
	SELECT d.currency_rate_id as forexRateId, d.forex_id as
	networkId,d.central_rate as rateConversion ,
	d.valid_date as settleDate,d.from_currency as currencyFrom,d.to_currency as
	currencyTo,d.last_operation as lastOperation,
	d.request_state as requestState,d.created_by as createdBy,d.checker_comments as
	checkerComments,d.last_updated_on as lastUpdatedOn,d.created_on as createdOn,
	e.description as networkIdLookup,
    f.description as currencyFromLookup,
    g.description as currencyToLookup
	from currency_rate_stg d
	left join lookup e on e.code = d.forex_id and e.type='ForexRate_NetworkId'
    left join lookup f on f.code = d.from_currency and f.type='FromForexRate_Currency'
    left join lookup g on g.code = d.to_currency and g.type='ToForexRate_Currency'
	where d.currency_rate_id = #{forexRateId}
</select>

<select id="getForexRateMain" resultType="ForexRateDTO">
	SELECT d.currency_rate_id as forexRateId, d.forex_id as
	networkId,d.central_rate as rateConversion ,
	d.valid_date as
	settleDate,
	d.from_currency as currencyFrom,d.to_currency as currencyTo,
	d.created_by as createdBy, d.created_on as createdOn,d.last_updated_by
	as lastUpdatedBy, d.last_updated_on as lastUpdatedOn, d.status,

	e.description as networkIdLookup,
	f.description as currencyFromLookup,
	g.description as currencyToLookup
	from currency_rate d
	left join
	lookup e on e.code = d.forex_id and
	e.type='ForexRate_NetworkId'
	left join lookup f on f.code =
	d.from_currency and
	f.type='FromForexRate_Currency'
	left join
	lookup g on g.code = d.to_currency and
	g.type='ToForexRate_Currency'
	WHERE d.currency_rate_id = #{forexRateId}
</select>

<update id="updateForexRate">
        UPDATE  currency_rate_stg SET currency_rate_id=#{forexRateId},forex_id=#{networkId},central_rate=#{rateConversion},
        valid_date=#{settleDate},from_currency=#{currencyFrom}, to_currency=#{currencyTo}, last_operation = #{lastOperation},
        request_state=#{requestState},created_by =#{lastUpdatedBy}, checker_comments= #{checkerComments},
        last_updated_on= #{lastUpdatedOn},buy_rate=#{rateConversion},sell_rate=#{rateConversion}
        WHERE currency_rate_id=#{forexRateId}
</update>
<update id="updateForexRateRequestState">
	UPDATE currency_rate_stg SET request_state= #{requestState},
	checker_comments= #{checkerComments}, LAST_UPDATED_BY=
	#{lastUpdatedBy},
	LAST_UPDATED_ON = #{lastUpdatedOn}, last_operation= #{lastOperation}
	WHERE currency_rate_id = #{forexRateId}
</update>
 <insert id="insertForexRateMain">
	INSERT INTO currency_rate (currency_rate_id, forex_id,
	central_rate,valid_date,from_currency,to_currency,CREATED_BY,CREATED_ON,
	LAST_UPDATED_BY,LAST_UPDATED_ON,status,buy_rate,sell_rate) VALUES
	(#{forexRateId}, #{networkId}, #{rateConversion},
	#{settleDate},#{currencyFrom},#{currencyTo},
	#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{status}, #{rateConversion}, #{rateConversion})
</insert>
<update id="updateForexRateMain">
	UPDATE currency_rate SET
	currency_rate_id=#{forexRateId},forex_id=#{networkId},central_rate=#{rateConversion},
	valid_date=#{settleDate},from_currency=#{currencyFrom},to_currency=#{currencyTo},
	LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON = #{lastUpdatedOn},
     STATUS=#{status},buy_rate=#{rateConversion},sell_rate=#{rateConversion}
	WHERE currency_rate_id=#{forexRateId}

</update>

<delete id="deleteForexRateDiscardedEntry">
	DELETE FROM currency_rate_stg WHERE currency_rate_id = #{forexRateId}
</delete>
	
<update id="updateForexRateDiscard">
	UPDATE currency_rate_stg SET currency_rate_id=#{forexRateId},
	forex_id=#{networkId},central_rate=#{rateConversion},valid_date=#{settleDate},from_currency=#{currencyFrom},to_currency=#{currencyTo},
	LAST_UPDATED_BY=#{lastUpdatedBy}, LAST_UPDATED_ON =#{lastUpdatedOn},request_state =#{requestState},
	buy_rate=#{rateConversion},sell_rate=#{rateConversion}, checker_comments='' WHERE currency_rate_id = #{forexRateId}
</update>
<select id="validateDuplicate" resultType="ForexRateDTO">
	select * from currency_rate_stg where forex_id=#{networkId} and valid_date=#{settleDate} and
	from_currency=#{currencyFrom} and to_currency=#{currencyTo}
</select>

<select id="getCurrencyRateIdBasedonNetwork" resultType="int">
	select COALESCE(max(currency_rate_id),1) from currency_rate where forex_id=#{networkId};
</select>

<insert id="insertCurrencyRateInfo" >
	INSERT INTO currency_rate
(currency_rate_id, forex_id, from_currency, to_currency, buy_rate, sell_rate, central_rate, valid_date, created_on, created_by, last_updated_on, last_updated_by)
VALUES(#{currencyId}, #{forexDto.networkId}, #{forexDto.currencyFrom}, #{forexDto.currencyTo}, #{forexDto.rateConversion}, #{forexDto.rateConversion}, #{forexDto.rateConversion},
 #{forexDto.settleDate}, #{forexDto.createdOn}, #{forexDto.createdBy},#{forexDto.lastUpdatedOn}, #{forexDto.lastUpdatedBy});
	
</insert>

</mapper>