package org.npci.settlenxt.adminportal.repository;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.npci.settlenxt.adminportal.dto.FunctionCodeDTO;
import org.npci.settlenxt.portal.common.dto.FunctionCode;
import org.npci.settlenxt.portal.common.service.BaseFunctionCodeMapper;

@Mapper
public interface FunctionCodeRepository extends BaseFunctionCodeMapper{

	 List<FunctionCodeDTO> getFunctionCodeListMain();

	 List<FunctionCodeDTO> getFunctionCodePendingForApproval();

	 FunctionCodeDTO getFunctionCodeProfileMain(int functionCodeId);

	 FunctionCodeDTO getFunctionCodeStgInfoById(int functionCodeId);

	 FunctionCodeDTO getFunctionCodeMain(int functionCodeId);

	 FunctionCodeDTO getFunctionCodeStg(int functionCodeId);

	 void insertFunctionCodeStg(FunctionCodeDTO functionCodeDto);

	 int fetchFunctionCodeIdSequence();

	 void insertFunctionCodeMain(FunctionCodeDTO functionCodeDto);

	 void updateFunctionCodeMain(FunctionCodeDTO functionCodeDto);

	 void updateFunctionCode(FunctionCodeDTO functionCodeDto);

	 void updateFunctionCodeDiscard(FunctionCodeDTO functionCodeDto);

	 void updateFunctionCodeRequestState(FunctionCodeDTO functionCodeDto);

	 void deleteDiscardedEntry(FunctionCodeDTO functionCodeDto);

	 int validateDuplicateCheck(FunctionCodeDTO functionCodeDto);
	
	 List<FunctionCode> getAll();
}
