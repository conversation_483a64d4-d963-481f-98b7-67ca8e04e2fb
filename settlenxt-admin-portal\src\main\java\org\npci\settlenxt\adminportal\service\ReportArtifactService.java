package org.npci.settlenxt.adminportal.service;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.npci.settlenxt.portal.common.exception.SettleNxtException;

public interface ReportArtifactService {

	 void downloadFile(String filePath, HttpServletRequest request, HttpServletResponse response)
			throws SettleNxtException,IOException;

	 List<String> getFiles(String path);

	 List<String> getFolders(String path);

}
