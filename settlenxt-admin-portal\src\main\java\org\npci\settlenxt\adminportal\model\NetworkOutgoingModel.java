package org.npci.settlenxt.adminportal.model;

import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Data;

@Data
public class NetworkOutgoingModel {
    private String funcCode;
    private String tranId;
    private String batchNum;
    private String seqNum;
    private String encryptedPanNum;
    private LocalDate chargeDate;
    private String rrn;
    private double transactionAmt;
    private String status;
    private LocalDateTime fileGenDate;
    private String refNum;
    private double netChargeAmt;
    private double grossSettlAmt;
    private double netSettlAmt;
    private double interchangeCommissionSettlAmt;
    private String networkRefId;
    private String mccCode;
    private String fieldName;
    private String fieldValue;
    private String suspensionCode;
    private String suspensionMessage;
    private String rejectionCode;
    private String rejectionMessage;
    private LocalDateTime createdOn;
    private LocalDateTime lastUpdatedOn;
    private String isMultilegTxn ;
    private String outgoingFileName ;
    private String network ;

}
