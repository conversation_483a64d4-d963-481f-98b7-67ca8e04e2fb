<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.MoveReportRepository">
<insert id="saveMoveReportToDatabase">
		insert into report_mvmt_signing (request_id,request_time,report_type,status,report_network)
		 values(#{requestId},#{requestTime},#{reportType},#{status},#{reportNetwork});
</insert>

<select id="fetchIdFromSeq" resultType="Integer">
select nextval('move_report_id_seq');
</select>

<select id="getReportList" resultType="MoveReportDTO">
		   	SELECT t.request_id as requestId, t.request_time as requestTime , l.description  as
		reportType,t.status as status,t.report_network as reportNetwork
 from report_mvmt_signing t inner join lookup l  on l.code=t.report_type   where t.status='A' and l.type='reportType'  order by t.request_id;
</select>

<select id="searchReportList" resultType="MoveReportDTO">
		SELECT t.request_id as requestId, t.request_time as requestTime , t.report_type as
		reportType,t.report_network as reportNetwork,t.status as status
 from report_mvmt_signing t where t.request_time between #{movereportDTO.fromDateStr} and #{movereportDTO.toDateStr}
<if test="isRepoNet">and report_network=#{movereportDTO.reportNetwork}</if>;
</select>

<select id="getMonths" resultType="CodeValueDTO">
select t.description as description ,t.code as code from lookup t where t.type='Month' order by t.code;
</select>


<select id="getBasePath" resultType="String">
select sys_value as sysValue from sys_params where sys_key =#{sysKey}
</select>

<select id="getDestPath" resultType="String">
select base_folder_path as baseFolderPath from batch_master where file_type =#{fileType}
</select>

<insert id="insertProductReportStatus">
insert into 
product_report_status(guid,product_code,report_name,report_type,status,cycle_date,
cycle_number,regen_flag,engine_code,accum_code,row_count,pgp_status,parent_file_id,created_ts,updated_ts,gen_start_ts,
gen_end_ts,accum_start_ts,accum_end_ts,pull_start_ts,pull_end_ts,participant_id,last_updated_by,file_type)
values(#{guid},#{settlementProductId},#{reportName},#{reportType},#{status},#{cycleDate},#{cycleNumber},#{regenFlag},#{engineCode},#{accumCode},#{rowCount},#{pgpStatus},
#{parentFileId},#{createdTs},#{updatedTs},#{genStartTs},#{genEndTs},#{accumStartTs},#{accumEndTs},#{pullStartTs},#{pullEndTs},#{participantId},#{lastUpdatedBy},
#{fileType})
</insert>

<select id="getAllCyclesByProductId" resultType="String">
select cycle_number as cycleNumber from settlement_cycle_config scc where product_id =#{productId}
</select>

</mapper>