<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.CardConfigurationRepository">
	
	<select id="getCardListMain" resultType="CardDTO">
		SELECT c.card_config_id as cardConfigId, c.card_type as cardType, c.card_variant as cardVariant, 
		c.base_fee as baseFee, c.from_date as fromDate, c.to_date as toDate, c.last_updated_by as lastUpdatedBy, 
		c.last_updated_on as lastUpdatedOn, ct.description as cardTypeName,cv.description as cardVariantName,
		c.from_Date_full as fromDateComplate,c.to_Date_full as toDateComplate, stg.request_state requestState
		FROM mcpr_base_fee_config c 
		inner join mcpr_base_fee_config_stg stg on c.card_config_id = stg.card_config_id
		left join lookup ct on c.card_type=ct.code and ct.type='CARD_TYPE' 
		left join lookup cv on c.card_variant=cv.code and cv.type='CARD_VARIANT' 
		 ORDER BY c.last_updated_on DESC
	</select>
	
	
	<select id="getCardConfigPendingForApproval" resultType="CardDTO">
		SELECT c.card_config_id as cardConfigId, c.card_type as cardType, c.card_variant as cardVariant, 
		c.base_fee as baseFee, c.from_date as fromDate, c.to_date as toDate, c.last_updated_by as lastUpdatedBy, 
		c.last_updated_on as lastUpdatedOn, c.last_operation as lastOperation, c.request_state as requestState, 
		c.checker_comments as checkerComments, ct.description as cardTypeName,cv.description as cardVariantName,
		c.from_Date_full as fromDateComplate,c.to_Date_full as toDateComplate  
		from mcpr_base_fee_config_stg c 
		left join lookup ct on card_type=ct.code and ct.type='CARD_TYPE' 
		left join  lookup cv on card_variant=cv.code and cv.type='CARD_VARIANT' WHERE c.request_state in ('P','R')
	</select>
	
	<select id="getCardProfileMain" resultType="CardDTO">
		SELECT c.card_config_id as cardConfigId, c.card_type as cardType, c.card_variant as cardVariant, 
		c.base_fee as baseFee, c.from_date as fromDate, c.to_date as toDate, c.last_updated_by as lastUpdatedBy,
		 c.last_updated_on as lastUpdatedOn, stg.request_state as requestState,
		 ct.description as cardTypeName,cv.description as cardVariantName,
		 c.from_Date_full as fromDateComplate,c.to_Date_full as toDateComplate, stg.last_operation as lastOperation
		 FROM mcpr_base_fee_config c 
		inner join mcpr_base_fee_config_stg stg on c.card_config_id = stg.card_config_id
		 left join lookup ct on c.card_type=ct.code and ct.type='CARD_TYPE' 
		 left join  lookup cv on c.card_variant=cv.code and cv.type='CARD_VARIANT' 
		 where c.card_config_id = #{cardid}
	</select>
	
	
	
	<select id="getCardConfigStgInfoByCardId" resultType="CardDTO">
		SELECT c.card_config_id as cardConfigId, c.card_type as cardType, c.card_variant as cardVariant, c.base_fee as baseFee, c.from_date as fromDate, c.to_date as toDate, c.last_updated_by as lastUpdatedBy, c.last_updated_on as lastUpdatedOn,c.created_on as createdOn, c.created_by as createdBy, c.request_state as requestState,c.status, c.last_operation as lastOperation, c.checker_comments as checkerComments, ct.description as cardTypeName,cv.description as cardVariantName,c.from_Date_full as fromDateComplate,c.to_Date_full as toDateComplate  FROM  mcpr_base_fee_config_stg c left join lookup ct on card_type=ct.code and ct.type='CARD_TYPE' left join  lookup cv on card_variant=cv.code and cv.type='CARD_VARIANT' WHERE c.card_config_id =#{c.card_config_id}
	</select>
	<select id="getCardConfigMain" resultType="CardDTO">
		SELECT c.card_config_id as cardConfigId, c.card_type as cardType, c.card_variant as cardVariant, c.base_fee as baseFee, c.from_date as fromDate, c.to_date as toDate, c.last_updated_by as lastUpdatedBy, c.last_updated_on as lastUpdatedOn,c.created_on as createdOn, c.created_by as createdBy, c.status, ct.description as cardTypeName,cv.description as cardVariantName,c.from_Date_full as fromDateComplate,c.to_Date_full as toDateComplate   FROM  mcpr_base_fee_config c left join lookup ct on card_type=ct.code and ct.type='CARD_TYPE' left join  lookup cv on card_variant=cv.code and cv.type='CARD_VARIANT' where c.card_config_id = #{c.card_config_id}
	</select>
	<select id="getCardConfigStg" resultType="CardDTO">
		SELECT c.card_config_id as cardConfigId, c.card_type as cardType, c.card_variant as cardVariant, c.base_fee as baseFee, c.from_date as fromDate, c.to_date as toDate, c.last_updated_by as lastUpdatedBy, c.last_updated_on as lastUpdatedOn,c.created_on as createdOn, c.created_by as createdBy, c.request_state as requestState, c.status, c.last_operation as lastOperation, ct.description as cardTypeName,cv.description as cardVariantName,c.from_Date_full as fromDateComplate,c.to_Date_full as toDateComplate  FROM mcpr_base_fee_config_stg c left join lookup ct on card_type=ct.code and ct.type='CARD_TYPE' left join lookup cv on card_variant=cv.code and cv.type='CARD_VARIANT' where c.card_config_id = #{c.card_config_id}
	</select>
	<select id="fetchIdFromCardConfigSequence" resultType="int">	
		SELECT nextval('card_config_id_seq')
	</select>
	<insert id="insertCardConfigStg" >
		insert into  mcpr_base_fee_config_stg (card_config_id, card_type, card_variant, base_fee, from_date, to_date, created_by,created_on, last_updated_by,last_updated_on, request_state, last_operation, status,from_date_full,to_date_full) values
		(#{cardConfigId}, #{cardType}, #{cardVariant}, #{baseFee}, #{fromDate}, #{toDate},
		#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{requestState}, #{lastOperation}, #{status},
		to_date(concat('01',#{fromMonth},#{fromYear}),'ddmmyyyy')  ,to_date(concat(case  when #{toMonth} in ('03','12') then '31' else '30' end,#{toMonth},#{toYear}),'ddmmyyyy')   )
	</insert>
	<insert id="insertCardConfigMain">
		insert into mcpr_base_fee_config (card_config_id, card_type, card_variant, base_fee, from_date, to_date, created_by, created_on, last_updated_by, last_updated_on, status,from_date_full,to_date_full) values
		(#{cardConfigId},#{cardType}, #{cardVariant}, #{baseFee}, #{fromDate}, #{toDate}, #{createdBy}, #{createdOn}, #{lastUpdatedBy}, #{lastUpdatedOn},#{status}
				,#{fromDateComplate},#{toDateComplate}	 )
	</insert>
	<update id="updateCardConfigMain">
		update mcpr_base_fee_config set card_config_id=#{cardConfigId}, card_type=#{cardType}, card_variant=#{cardVariant},base_fee=#{baseFee}, from_date=#{fromDate}, to_date=#{toDate}, created_by =#{createdBy},created_on =#{createdOn}, last_updated_by=#{lastUpdatedBy}, last_updated_on = #{lastUpdatedOn},status=#{status},from_Date_full=#{fromDateComplate},to_Date_full=#{toDateComplate} WHERE card_config_id = #{cardConfigId}	
	</update>
	<update id="updateCardConfig">
		update mcpr_base_fee_config_stg set card_type=#{cardType}, card_variant=#{cardVariant},base_fee=#{baseFee}, from_date=#{fromDate}, to_date=#{toDate}, created_by =#{createdBy},created_on =#{createdOn}, last_updated_by=#{lastUpdatedBy}, last_updated_on = #{lastUpdatedOn}, status=#{status}, request_state =#{requestState}, last_operation= #{lastOperation}, checker_comments='',from_Date_full=to_date(concat('01',#{fromMonth},#{fromYear}),'ddmmyyyy') ,to_Date_full=to_date(concat(case  when #{toMonth} in ('03','12') then '31' else '30' end,#{toMonth},#{toYear}),'ddmmyyyy')	 where card_config_id= #{cardConfigId}
	</update>
	<update id="updateCardConfigdiscard">
		update mcpr_base_fee_config_stg set card_type=#{cardType}, card_variant=#{cardVariant},base_fee=#{baseFee}, from_date=#{fromDate}, to_date=#{toDate}, created_by =#{createdBy},created_on =#{createdOn}, last_updated_by=#{lastUpdatedBy}, last_updated_on = #{lastUpdatedOn},last_operation= #{lastOperation}, status=#{status}, request_state =#{requestState}, checker_comments='',from_Date_full=#{fromDateComplate} ,to_Date_full=#{toDateComplate}	 where card_config_id= #{cardConfigId}
	</update>
	<update id="updateCardConfigRequestState">
		update mcpr_base_fee_config_stg set request_state=#{requestState}, checker_comments=#{checkerComments}, last_updated_by=#{lastUpdatedBy}, last_updated_on = #{lastUpdatedOn}, last_operation= #{lastOperation}	WHERE card_config_id= #{cardConfigId}
	</update>
	<delete id="deleteDiscardedEntry">
		delete from mcpr_base_fee_config_stg where card_config_id= #{cardConfigId}
	</delete>
	<select id="validateDuplicateCheckList" resultType="CardDTO">
		select R.card_config_id as cardConfigId, R.card_type as cardType, R.card_variant as cardVariant from mcpr_base_fee_config_stg as R where card_config_id!= #{cardConfigId} and R.card_type= #{cardType} and R.card_variant= #{cardVariant}
		and ((from_date_full=to_date(#{fromYearMonth},'ddmmyyyy') and to_date_full=to_date(#{toYearMonth},'ddmmyyyy')) 
		or (to_date(#{fromYearMonth},'ddmmyyyy') between from_date_full and to_Date_full ) 
		or (to_date(#{toYearMonth},'ddmmyyyy') between from_date_full and to_Date_full)
		or (from_date_full between to_date(#{fromYearMonth},'ddmmyyyy') and to_date(#{toYearMonth},'ddmmyyyy') ) 
		or (to_Date_full between to_date(#{fromYearMonth},'ddmmyyyy') and to_date(#{toYearMonth},'ddmmyyyy')  ) )
		
	</select>
	<select id="validateFromDateList" resultType="CardDTO">	
		select R.card_config_id as cardConfigId, R.card_type as cardType, R.card_variant as cardVariant from mcpr_base_fee_config_stg as R where card_config_id!= #{cardConfigId} and R.card_type=#{cardType} and R.card_variant=#{cardVariant}
		and to_Date_full= to_date(#{fromYearMonth},'ddmmyyyy')  -interval '1 day'
	</select> 
	<select id="validateCombination" resultType="CardDTO">	
		select R.card_config_id as cardConfigId, R.card_type as cardType, R.card_variant as cardVariant from mcpr_base_fee_config_stg as R where card_config_id!= #{cardConfigId} and R.card_type=#{cardType} and R.card_variant=#{cardVariant}
	</select>
	<select id="getLastTodate" resultType="String">    
        select to_char(max(to_Date_full),'MONTH-YYYY') as lastToMonth from mcpr_base_fee_config_stg as R where card_config_id!= #{cardConfigId} and R.card_type=#{cardType} and R.card_variant=#{cardVariant}
        and to_Date_full  &lt; to_date(#{fromYearMonth},'ddmmyyyy')  
    </select>
</mapper>
