$(document).ready(function() {
	

	disableSave();		  
	$("#vuploadWTAT").on('keyup keypress blur change', function () {
        unableSave();
    });
 	$("#veditWTAT").on('keyup keypress blur change', function () {
        unableSave();
    });
	
});
function disableSave()
{
if (typeof bEdit != "undefined") {
	document.getElementById("bEdit").disabled = true;
}
}

function unableSave()
{
   	if (typeof bEdit != "undefined") {
			document.getElementById("bEdit").disabled = false;
		}	
}

function homeMcprTAT(_userID, _type) {
	let url = '/showMcprTATConfig';
	var data = "";
	postData(url, data);
}
function saveMcprTAT(_userID, type) {
   
	var tatId = document.getElementById("htaTId").value;
	var data = "";
	
		

	var isValid = true;

    if (!validateField('vuploadWTAT', true, "Integer", 100, false,365,true) && isValid) {
        isValid = false;
    }
    if (!validateField('veditWTAT', true, "Integer", 100, false,365,true) && isValid) {
        isValid = false;
    }
    if(!isValid)
    {
    	return false;
    }

	tatId="1";
	let url = '/asignMcprTATToFunctionAdd';
	 data = "taTId," + tatId + ",uploadWTAT," + document.getElementById("vuploadWTAT").value + ",editWTAT," + document.getElementById("veditWTAT").value  +",type,"+type;
	postData(url, data);
}


function submitForm(url,_userType) {
	var data = "userType," + "P";
	postData(url, data);
}

function urlPostAction(_type, action) {
	var data = "";
	postData(action, data);
}

	function postAction(_action) {
		var tatId = $("#htaTId").val();
		var crtuser = $("#crtuser").val();
		var remarks=$("#rejectReason").val();
		let url = '/approveMcprTATStatus';
		let data ="";
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				
				 data = "taTId," + tatId + ",status," + "Approved" + ",crtuser,"
						+ crtuser  + ",remarks,"
						+ remarks;
				
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				data = handleReqNameRoleMap(data, tatId, crtuser, remarks, url);
	
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}

var optionFunctionalityList = new Array();


function handleReqNameRoleMap(data, tatId, crtuser, remarks, url) {
	if ('${requestInfo.requestName}' == 'ROLE FUNCTIONALITY MAP') {

		data = "taTId," + tatId + ",status," + "Rejected"
			+ ",crtuser," + crtuser + ",rejectReason," + remarks;

		postData(url, data);
	} else {

		data = "taTId," + tatId + ",status," + "Rejected"
			+ ",crtuser," + crtuser + ",remarks," + remarks;
		postData(url, data);
	}
	return data;
}

function postDiscardAction(action) {
		var url = action;
		var tatId = document.getElementById("htaTId").value;
		var data = "taTId," + tatId  ;
		postData(url, data);
 }
	

function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType !="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    if (fieldType == "Number" && isNaN(fieldValue)) {
        isValid = false;
    }
    isValid = handleFieldTypeInt(fieldType, fieldValue, isValid);
    if (fieldType == "Integer") {
         let regEx =/^\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (fieldType == "Decimal") {
         let regEx = /^\d+\.?\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    isValid = validateUsingLengthAndRange(isExactLength, fieldValue, length, isValid, isRange, minNumber, maxNumber);
    if(fieldId=="veditWTAT")
    {
	    var fieldValue1 = $("#vuploadWTAT").val();
	    var fieldValue2 = $("#veditWTAT").val();
	    if((Number(fieldValue1) + Number(fieldValue2))  > 28)
	    {
        	isValid = false;
    	}
    }
    handleDisplayErrorMsg(isValid, fieldId);
    return isValid;
}
function validateUsingLengthAndRange(isExactLength, fieldValue, length, isValid, isRange, minNumber, maxNumber) {
	if (isExactLength && fieldValue.length != length) {
		isValid = false;
	}
	if (isRange && !(Number(fieldValue) >= Number(minNumber) && Number(fieldValue) <= Number(maxNumber))) {
		isValid = false;
	}
	return isValid;
}

function handleDisplayErrorMsg(isValid, fieldId) {
	if (isValid) {
		$("#err" + fieldId).hide();
	} else {
		if (rebateValidationMessages[fieldId]) {
			$("#err" + fieldId).find('.error').html(rebateValidationMessages[fieldId]);
		}
		$("#err" + fieldId).show();
	}
}

function handleFieldTypeInt(fieldType, fieldValue, isValid) {
	if (fieldType == "Alphabet") {
		let regEx = /^[a-zA-Z]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphabetWithSpace") {
		let regEx = /^[a-zA-Z ]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphanumericNoSpace") {
		let regEx = /^[A-Za-z0-9]+$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return isValid;
}

