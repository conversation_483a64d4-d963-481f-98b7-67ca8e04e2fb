<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ page import="java.time.format.DateTimeFormatter"%>
<script src="./static/js/validation/viewApproveReasonCodeRules.js"
	type="text/javascript"></script>

<style>
table {
	table-layout: fixed;
}

td {
	width: 36%;
}
</style>

<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">
		<spring:message code="budget.apprejecterrormsg" />
	</div>
	<div class="alert alert-danger remarkMust" role="alert">
		<spring:message code="budget.remarkserror" />
	</div>
	<c:url value="approveReasonCode" var="approveReasonCode" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveRcRules" modelAttribute="reasonCodeDto"
		autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message
									code="reasonCodeRules.approvalPendingViewScreen.title" /></span></strong>
					</div>

					<div class="panel-body">



						<table class="table table-striped infobold"
							style="font-size: 12px">
						
<caption style="display:none;">ReasonCode</caption>

							
							<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>

								<tr>
									<td colspan="6">
										<div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-user"></span> <span
												data-i18n="Data"><spring:message
														code="reasonCodeRules.viewscreen.title" /></span></strong>
										</div>
									</td>
									
									<tr>
									</tr>
									<tr>
									

									<td><label><spring:message
												code="reasonCode.actionCode" /></label></td>
								
									<td>${actionCodeMap[reasonCodeDto.actionCode]}</td>
									<td><label><spring:message
												code="reasonCode.reasonCode" /></label></td>
									<td>${reasonCodeDto.reasonCode }</td>
									
								</tr>


								<tr>

									<td><label><spring:message
												code="reasonCode.relationOperator" /></label></td>
									<td>${relOpMap[reasonCodeDto.relationOperator]}</td>
									
									<td><label><spring:message
												code="reasonCode.fieldValue" /></label></td>
									<td>${reasonCodeDto.fieldValue }</td>

								</tr>

								<tr>
									<td><label><spring:message
												code="reasonCode.logicalReasonCode" /></label></td>
									<td>${reasonCodeMap[reasonCodeDto.logicalReasonCode]}</td>
									
									<td><label><spring:message code="am.lbl.createdBy" /></label></td>
									<td>${reasonCodeDto.createdBy }</td>
									<td><label><spring:message
												code="am.lbl.createdDate" /></label></td>
									<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
											value="${reasonCodeDto.createdOn}" /></td>
								</tr>
								<tr>
<td><label><spring:message
													code="reasonCode.fieldName" /></label></td>
													<td style="text-align:left;" colspan="4">${reasonCodeDto.fieldName}</div></td>
    
									
											
										

</tr>

								<sec:authorize
									access="hasAuthority('Approve Reason Code Rules')">
									<c:if test="${reasonCodeDto.requestState eq 'P'}">
										<tr>
											<td colspan="6"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"><spring:message
																code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
										</tr>


										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="AM.lbl.reject" /></option>
											</select></td>
											<td>
												<div style="text-align: center;" >
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											<!-- //Added by deepak on 31-03-2016 -->
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										</tr>
									</c:if>
								</sec:authorize>

							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align: center;">
									<sec:authorize
										access="hasAuthority('Approve Reason Code Rules')">
										<c:if test="${reasonCodeDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approveRole" value="Submit"
												onclick="postAction('${reasonCodeDto.seqId}', '/approveReasonCodeRules');" />
										</c:if>
									</sec:authorize>

									<button type="button" class="btn btn-danger"
										onclick="submitForm('/showPendingReasonCodeRules');">
										<spring:message code="budget.backBtn" />
									</button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

