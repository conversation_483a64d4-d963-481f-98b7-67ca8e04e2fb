package org.npci.settlenxt.adminportal.controllers;


import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.npci.settlenxt.adminportal.common.cache.ActionCodeCache;
import org.npci.settlenxt.adminportal.common.cache.ParticipantCache;
import org.npci.settlenxt.adminportal.common.cache.ReasonCodeCache;
import org.npci.settlenxt.adminportal.common.cache.SysParams;
import org.npci.settlenxt.adminportal.common.cache.TxnDynamicAttributeJsonCache;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.FeeDTO;
import org.npci.settlenxt.adminportal.dto.NetFundTxnDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.ActionCodeCacheService;
import org.npci.settlenxt.adminportal.service.TransactionDetailService;
import org.npci.settlenxt.portal.common.controllers.BaseTransactionDetailController;
import org.npci.settlenxt.portal.common.dto.ActionCodeDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.DisputeDocumentDTO;
import org.npci.settlenxt.portal.common.dto.DisputeTxnModel;
import org.npci.settlenxt.portal.common.dto.FunctionCode;
import org.npci.settlenxt.portal.common.dto.ParticipantDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.npci.settlenxt.portal.common.dto.TxnSettlementDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.service.BaseLookupService;
import org.npci.settlenxt.portal.common.service.BaseTransactionDetailService;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.npci.settlenxt.portal.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

@Controller
public class TransactionDetailController extends BaseTransactionDetailController {


	private static final String MAKER_CHECKER_FLAG = "makerCheckerFlag";

	@Autowired
	ParticipantCache participantCache;

	@Autowired
	SessionDTO sessionDTO;

	@Autowired
	private MessageSource messageSource;

	@Autowired
	TransactionDetailService transactionDetailService;

	@Autowired
	BaseTransactionDetailService baseTransactionDetailSvc;

	@Autowired
	ActionCodeCacheService actionCodeCache;
	
	@Autowired
	ActionCodeCache actionCodeChe;
	
	@Autowired
	TxnDynamicAttributeJsonCache txnDynamicAttrCache;


	private static final Logger logger = LogManager.getLogger(TransactionDetailController.class);

	private static final String YYYYMMDD_HHMMSS_WITH_SEPERATOR = "yyyy-MM-dd HH:mm:ss";

	private static final String DDMMYYYY_HHMMSS_FORMAT = "dd/MM/yyyy HH:mm:ss";

	private static final String PRESENTMENT_TBL = "presentment_txn_";

	private String dynamicData = "";

	@Autowired
	private Environment environment;

	@Autowired
	SysParams sysParam;

	@Autowired
	ReasonCodeCache reasonCodeCache;

	@Autowired
	BaseLookupService baseLookUpSvc;


	private static final String SHOW_CHECKBOX = "showCheckBox";


	
	private static final String VIEW_TRANSACTION_DETAIL="viewTransactionDetail";
	private static final String APPROVE_TRANSACTION_DETAIL="approveTransactionDetail";
	private static final String CURRENT_STATE="current_state";
	private static final String ERROR_MSG="errorMessage";
	private static final String ERROR_STATUS="errorStatus";
	private static final String FUNC_CD_LIST="funcCodeList";
	private static final String PARTICIPANT_LIST="participantlist";
	private static final String STATUS="status";
	private static final String TO_STATE="to_state";
	private static final String AA_DATA="aaData";
	private static final String ORG_ACTION="orgAction";
	private static final String BULK_FUNC_CD="BULK_FUNC_CD";
	private static final String NON_DISPUTE_ACTNS="NON_DISPUTE_ACTNS";
	private static final String ENTITY_TYPE="entity_type";
	private static final String CUST_COMP_INFO="custCompInfo";
	private static final String TIP_AMOUNT="tipAmount";
	private static final String COUNTER="counter";
	private static final String TXN_DETAIL_LIST="txnDetailList";
	private static final String DYNAMIC_TXN_ATTR_DATA="dynamicTxnAttrData";
	private static final String AVAILABLE_OPTIONS="availableOptions";
	private static final String TXN_DETIAL_SUMMARY="transactionDetailSummary";
	private static final String TXN_SEARCH_INFO="transactionSearchInfo";
	private static final String DYNAMIC_DATA="dynamicData";
	private static final String DISP_DOC_TYPE_LIST="dispDocTypeList";
	private static final String ACQ_ID_LIST="acqIdList";
	private static final String ISS_ID_LIST="issIdList";
	private static final String TXN_ORG_INST_ID="txnOrgInstId";
	private static final String FEE_NAME_LIST="feeNameList";
	private static final String PART_SETTLE_BIN_LIST="partSettleBinList";
	private static final String MEM_TXT_MSG_REGX="memTxtMsgRegex";
	
	@PostMapping("/transactionDetail")
	@PreAuthorize("hasAuthority('View Transaction Search')")
	public String getTransactionDetail(@ModelAttribute("txnSettlementDTO") TxnSettlementDTO txnSettlementDTO,
			BindingResult result, Model model, String viewName, HttpSession session)
			throws ParseException, JsonProcessingException {
		String mti = txnSettlementDTO.getMti();
		String funcCode = txnSettlementDTO.getFuncCode();
		String orgFuncCode = getorgFunCode(txnSettlementDTO);
		String txnId = txnSettlementDTO.getTxnId();
		String originalTableName = txnSettlementDTO.getOriginalTableName();
		TxnSettlementDTO transactionDetailDTO;
		List<TxnSettlementDTO> txnDetailList = new ArrayList<>();
		List<TxnSettlementDTO> txnPresentMentInfo = new ArrayList<>();
		List<TxnSettlementDTO> disputeTxnList = new ArrayList<>();
		List<TxnSettlementDTO> disputeTxnListPending;
		List<TxnSettlementDTO> reversalTxnInfo = new ArrayList<>();
		String partId = transactionDetailService.getNpciPartId();
		boolean isNpciFundCashRev = transactionDetailService.isNpciFundRuPayCashBack(mti, funcCode);
		String bulkFuncCodes = sysParam.getSystemKeyValue(BaseCommonConstants.KEY_DISPUTE,BULK_FUNC_CD);
		if (isNpciFundCashRev) {
			transactionDetailDTO = transactionDetailService.getNpciFundCashRevInfo(txnId, partId,
					txnSettlementDTO.getPartId());
			if (transactionDetailDTO != null) {
				txnDetailList = transactionDetailService.getDisputeNpciFundCashRevInfo(
						transactionDetailDTO.getOrgTxnId(), partId, transactionDetailDTO.getOrgDateCapture(),
						txnSettlementDTO.getPartId());
				disputeTxnListPending = transactionDetailService
						.getPendingDisputeNpciFundCashRevInfo(transactionDetailDTO.getTxnId(), partId,
								txnSettlementDTO.getPartId());
			} else {
				disputeTxnListPending = transactionDetailService.getPendingDisputeNpciFundCashRevInfo(txnId, partId,
						txnSettlementDTO.getPartId());
			}

			transactionDetailDTO = prepareTxnDTO(originalTableName, transactionDetailDTO, disputeTxnListPending,
					partId, txnSettlementDTO.getPartId());
		} else if (funCodeCheck(funcCode, orgFuncCode, bulkFuncCodes)) {
			transactionDetailDTO = prepareTxnInfo(txnId, originalTableName, txnDetailList, partId,
					txnSettlementDTO.getPartId());
			transactionDetailDTO.setSearchPartId(txnSettlementDTO.getPartId());
			String disputeOrgTableName = StringUtils.EMPTY;
			txnPresentMentInfo = prepareBulkPresentMentInfo(txnId, transactionDetailDTO, txnPresentMentInfo, partId,
					disputeOrgTableName);
			transactionDetailDTO.setPartId(partId);
			disputeTxnList = transactionDetailService.getDisputeBulkTxnInfo(txnId, partId,
					transactionDetailDTO.getDateCapture(), txnSettlementDTO.getPartId());
			disputeTxnListPending = transactionDetailService.getPendingDisputeBulkTxnInfo(transactionDetailDTO);	
		} else {
			transactionDetailDTO = transactionDetailService.getTransactionDetailData(txnId,
					originalTableName, partId, txnSettlementDTO.getPartId());
			transactionDetailDTO.setSearchPartId(txnSettlementDTO.getPartId());
			txnDetailList.add(transactionDetailDTO);
			reversalTxnInfo = transactionDetailService.getReversalTxnDetailData(transactionDetailDTO.getTxnId(),
					transactionDetailDTO.getOrgAction(), partId, transactionDetailDTO.getApprovalCode(),
					txnSettlementDTO.getPartId());
			disputeTxnList = transactionDetailService.getDisputeTxnDetailData(transactionDetailDTO.getTxnId(), partId,
					transactionDetailDTO.getDateCapture(), txnSettlementDTO.getPartId());
			transactionDetailDTO.setPartId(partId);
			String disputeOrgTableName = "";
			disputeOrgTableName = getDisputeTableName(disputeTxnList, disputeOrgTableName);
			List<String> presentTableList = new ArrayList<>();
			preparePresentmentInfo(txnId, transactionDetailDTO, txnPresentMentInfo, partId, disputeOrgTableName,
					presentTableList);
			disputeTxnListPending = transactionDetailService.getPendingDisputeTxnList(transactionDetailDTO, txnId);
		}
		List<TxnSettlementDTO> filteredTxnInfoList;
		List<TxnSettlementDTO> custCompInfoList;
		String revFuncCd = baseLookUpSvc.getLookupDesc(BaseCommonConstants.REV_FUNC_CD,
				BaseCommonConstants.FUNC_CODE);
		reversalTxnInfo = reversalTxnInfo.stream().filter(rev -> !StringUtils.contains(revFuncCd, rev.getFuncCode()))
				.toList();
		List<TxnSettlementDTO> finalTxnInfoList = Stream
				.of(txnDetailList, reversalTxnInfo, txnPresentMentInfo, disputeTxnList, disputeTxnListPending)
				.flatMap(Collection::stream).toList();
		String nonDispActions = sysParam.getSystemKeyValue(BaseCommonConstants.KEY_DISPUTE, NON_DISPUTE_ACTNS);
		filteredTxnInfoList = finalTxnInfoList.stream()
				.filter(dispute -> !StringUtils.contains(nonDispActions, dispute.getToState()))
				.toList();
		filteredTxnInfoList = filteredTxnInfoList.stream().sorted(Comparator.comparing(TxnSettlementDTO::getTstampLocal))
				.toList();
		//Customer Complain List
		custCompInfoList = disputeTxnList.stream()
				.filter(dispute -> StringUtils.contains(nonDispActions, dispute.getToState()))
				.toList();
		custCompInfoList = custCompInfoList.stream().sorted(Comparator.comparing(TxnSettlementDTO::getTstampLocal))
				.toList();
		transactionDetailService.setCustComplaintData(custCompInfoList);
		JSONObject jsonObjectAvailableOpt = new JSONObject();
		String entityType = "N";
		jsonObjectAvailableOpt.put(ENTITY_TYPE, entityType);
		JSONArray actionList;
		Map<String, String> availableOptions = new HashMap<>();
		Boolean flag = false;
		Double tipAmount = 0.0;
		String mkrCkrFlag = null;
		if(sessionDTO.getMakChkFlag().equalsIgnoreCase(BaseCommonConstants.MAKER)) {
			mkrCkrFlag= BaseCommonConstants.MAKER;
		}else {
			mkrCkrFlag= BaseCommonConstants.CHECKER;
		}
		if (sessionDTO.getMakChkFlag().equalsIgnoreCase(BaseCommonConstants.MAKER)) {
			if (disputeTxnListPending.isEmpty()) {
				for (TxnSettlementDTO tx : txnPresentMentInfo) {
					if ("1240".equals(tx.getMti()) && "200".equals(tx.getFuncCode())) {
						flag = true;
						break;
					}
				}
				if (!disputeTxnList.isEmpty() && disputeTxnList.size() != custCompInfoList.size()) {
					TxnSettlementDTO disputeTxnStateInfoDto = prepareDisputeAvailableJsonData(transactionDetailDTO,
							txnPresentMentInfo, disputeTxnList, filteredTxnInfoList, entityType, flag);
					Double amtTran = disputeTxnStateInfoDto.getAmtTran();
					disputeTxnStateInfoDto.setAmtTran(disputeTxnStateInfoDto.getOrgAmtPrsntmt());
					String jsonString = new ObjectMapper().writeValueAsString(disputeTxnStateInfoDto);
					Map<String, String> json = new Gson().fromJson(jsonString, Map.class);
					jsonObjectAvailableOpt.put(CURRENT_STATE, disputeTxnStateInfoDto.getToState());
					jsonObjectAvailableOpt.remove(TO_STATE);
					jsonObjectAvailableOpt.putAll(json);
					logger.debug("Dispute Model Info: {}", jsonObjectAvailableOpt);
					actionList = getAvailableOptionsList(jsonObjectAvailableOpt);
					tipAmount = getCapAmount(actionList, json);
					JSONObject jsonObj= new JSONObject();
					jsonObj.put(ORG_ACTION,disputeTxnStateInfoDto.getOrgAction());
					jsonObj.put(TIP_AMOUNT,tipAmount);
					availableOptions = handleDisputeActionList(model, jsonObjectAvailableOpt, actionList,
							availableOptions, jsonObj, json);
					model.addAttribute(AVAILABLE_OPTIONS, availableOptions);
					disputeTxnStateInfoDto.setAmtTran(amtTran);
				} else if (Boolean.TRUE.equals(flag)) {
					txnPresentMentInfo.get(0).setOrgAmtPrsntmt(txnPresentMentInfo.get(0).getAmtTran());
					txnPresentMentInfo.get(0).setRevBy(transactionDetailDTO.getRevBy());
					String jsonString = new ObjectMapper().writeValueAsString(txnPresentMentInfo.get(0));
					Map<String, String> json = new Gson().fromJson(jsonString, Map.class);
					jsonObjectAvailableOpt.putAll(json);
					jsonObjectAvailableOpt.put(CURRENT_STATE, actionCodeCache.getActionCode(txnPresentMentInfo.get(0).getMti(),
							txnPresentMentInfo.get(0).getFuncCode()));
					logger.debug("Presentment Model Info: {}", jsonObjectAvailableOpt);
					actionList = getAvailableOptionsList(jsonObjectAvailableOpt);
					tipAmount = getCapAmount(actionList, json);
					JSONObject jsonObj= new JSONObject();
					jsonObj.put(ORG_ACTION,transactionDetailDTO.getOrgAction());
					jsonObj.put(TIP_AMOUNT,tipAmount);
					handleActionList(model, jsonObj, jsonObjectAvailableOpt,
							actionList, json);
					model.addAttribute(AVAILABLE_OPTIONS, availableOptions);
					
				} else {
					TxnSettlementDTO txnInfo = txnDetailList.get(txnDetailList.size() - 1);
					if ("0110".equals(transactionDetailDTO.getMti()) || "0210".equals(transactionDetailDTO.getMti()) 
							|| transactionDetailDTO.getFuncCode().equals(BaseCommonConstants.CASHBACK_FUNC)
							|| transactionDetailDTO.getFuncCode().equals(BaseCommonConstants.OFFLINE_PRESENTMENT_FUNC)) {
						txnInfo.setOrgAmtPrsntmt(txnInfo.getAmtTran());
						String jsonString = new ObjectMapper().writeValueAsString(txnInfo);
						Map<String, String> json = new Gson().fromJson(jsonString, Map.class);
						jsonObjectAvailableOpt.put(CURRENT_STATE,
								actionCodeCache.getActionCode(txnInfo.getMti(),txnInfo.getFuncCode()));
						jsonObjectAvailableOpt.putAll(json);
						logger.debug("Transaction Model Info: {}", jsonObjectAvailableOpt);
						actionList = getAvailableOptionsList(jsonObjectAvailableOpt);
						tipAmount = getCapAmount(actionList, json);
						JSONObject jsonObj= new JSONObject();
						jsonObj.put(ORG_ACTION,transactionDetailDTO.getOrgAction());
						jsonObj.put(TIP_AMOUNT,tipAmount);
						handleActionList(model, jsonObj, jsonObjectAvailableOpt,
								actionList, json);
					}
				}
			}
			setCustomerCompensation(model, custCompInfoList);
		}
		prepareModelInfo(model, transactionDetailDTO, txnPresentMentInfo, disputeTxnListPending, partId,
				filteredTxnInfoList);
		int counter = 1;
		List<CodeValueDTO> dispDocTypeList = baseLookUpSvc.getLookupData(BaseCommonConstants.DISP_DOC_TYPE);
		dispDocTypeList = dispDocTypeList.stream()
				.sorted(Comparator.comparing(CodeValueDTO::getCode)).toList();
		model.addAttribute(COUNTER, counter);
		model.addAttribute(TIP_AMOUNT, tipAmount);
		model.addAttribute(TXN_DETAIL_LIST,
				transactionDetailService.formatTxnFileds(filteredTxnInfoList, transactionDetailDTO));
		model.addAttribute(DYNAMIC_TXN_ATTR_DATA, txnDynamicAttrCache.getDynamicTxnAttributeData());
		
		model.addAttribute(TXN_DETIAL_SUMMARY, transactionDetailDTO);
		model.addAttribute(TXN_SEARCH_INFO, txnSettlementDTO);
		model.addAttribute(DYNAMIC_DATA, getDynamicDisputeData());
		model.addAttribute(DISP_DOC_TYPE_LIST, dispDocTypeList);
		model.addAttribute(MAKER_CHECKER_FLAG, mkrCkrFlag);
		return getView(model, VIEW_TRANSACTION_DETAIL);
	}

	private TxnSettlementDTO prepareTxnInfo(String txnId, String originalTableName,
			List<TxnSettlementDTO> txnDetailList, String partId, String searchParticipant) {
		TxnSettlementDTO transactionDetailDTO;
		if(StringUtils.isNotBlank(originalTableName)) {
			transactionDetailDTO = transactionDetailService.getTransactionDetailData(txnId, originalTableName, partId,
					searchParticipant);
			txnDetailList.add(transactionDetailDTO);
		}else {
			transactionDetailDTO = transactionDetailService.getBulkTxnInfo(txnId, partId, searchParticipant);
		}
		return transactionDetailDTO;
	}

	private boolean funCodeCheck(String funcCode, String orgFuncCode, String bulkFuncCodes) {
		return StringUtils.contains(bulkFuncCodes, funcCode) || StringUtils.contains(bulkFuncCodes, orgFuncCode);
	}


	private String getorgFunCode(TxnSettlementDTO txnSettlementDTO) {
		return StringUtils.isNotBlank(txnSettlementDTO.getOrgFuncCode()) ? txnSettlementDTO.getOrgFuncCode() : null;
	}

	private void preparePresentmentInfo(String txnId, TxnSettlementDTO transactionDetailDTO,
			List<TxnSettlementDTO> txnPresentmentInfo, String partId, String disputeOrgTableName,
			List<String> presentTableList) {
		if (StringUtils.isNotBlank(transactionDetailDTO.getPresentmentRecvdDate())) {
			handlePresentmentInfo(txnId, transactionDetailDTO, txnPresentmentInfo, partId, disputeOrgTableName,
					presentTableList);
		}
	}

	private String getDisputeTableName(List<TxnSettlementDTO> disputeTxnList, String disputeOrgTableName) {
		if (!disputeTxnList.isEmpty()) {
			disputeOrgTableName = disputeTxnList.get(disputeTxnList.size() - 1).getOriginalTableName();
		}
		return disputeOrgTableName;
	}

	private List<TxnSettlementDTO> prepareBulkPresentMentInfo(String txnId, TxnSettlementDTO transactionDetailDTO,
			List<TxnSettlementDTO> txnPresentmentInfo, String partId, String disputeOrgTableName) {
		if(StringUtils.isNotBlank(transactionDetailDTO.getPresentmentRecvdDate())) {
			txnPresentmentInfo = handleBulkPresentmentInfo(txnId, transactionDetailDTO, partId,
					disputeOrgTableName);
		}
		return txnPresentmentInfo;
	}

	private TxnSettlementDTO prepareTxnDTO(String originalTableName, TxnSettlementDTO transactionDetailDTO,
			List<TxnSettlementDTO> disputeTxnListPending, String partId, String searchParticipant) {
		if (transactionDetailDTO != null) {
			transactionDetailDTO.setOriginalTableName(originalTableName);
			transactionDetailDTO.setPartId(sessionDTO.getParticipantName());
		} else {
			if (!disputeTxnListPending.isEmpty()) {
				transactionDetailDTO = disputeTxnListPending.get(0);
				transactionDetailDTO = transactionDetailService.setNpciFundCashRevDetails(transactionDetailDTO,
						partId, searchParticipant);
			}
		}
		return transactionDetailDTO;
	}

	private void setCustomerCompensation(Model model, List<TxnSettlementDTO> custCompInfoList) {
		if (!custCompInfoList.isEmpty()) {
			model.addAttribute(CUST_COMP_INFO,custCompInfoList.get(custCompInfoList.size() - 1));
		}
	}

	private Map<String, String> handleDisputeActionList(Model model, JSONObject jsonObjectAvailableOpt,
			JSONArray actionList, Map<String, String> availableOptions,
			JSONObject jsonObj , Map<String, String> json)
			throws ParseException, JsonProcessingException {
		Double tipAmt = (Double) jsonObj.get(TIP_AMOUNT);
		String orgAction = (String) jsonObj.get(ORG_ACTION);
		if (tipAmt.equals(0.0) && actionList.contains(CommonConstants.TIP_SURCHARGE_ACTION)) {
			actionList.remove(CommonConstants.TIP_SURCHARGE_ACTION);
		}
		availableOptions = getMappingOfAvailableOptionsList(availableOptions, actionList,
				String.valueOf(jsonObjectAvailableOpt.get(CURRENT_STATE)), orgAction);
		getAllPossibleReasonCodes(availableOptions, json, environment.getProperty("reasonCode.option.api"), model);
		return availableOptions;
	}

	private TxnSettlementDTO prepareDisputeAvailableJsonData(TxnSettlementDTO transactionDetailDTO,
			List<TxnSettlementDTO> txnPresentmentInfo, List<TxnSettlementDTO> disputeTxnList,
			List<TxnSettlementDTO> filteredTxnInfoList, String entityType, Boolean flag) {
		TxnSettlementDTO disputeTxnStateInfoDto = filteredTxnInfoList.get(filteredTxnInfoList.size() - 1);
		if (Boolean.TRUE.equals(flag)) {
			disputeTxnStateInfoDto.setOrgAction(txnPresentmentInfo.get(0).getToState());
			disputeTxnStateInfoDto.setLatePresentmentIndicator(txnPresentmentInfo.get(0).getLatePresentmentIndicator());
		} else {
			disputeTxnStateInfoDto.setOrgAction(transactionDetailDTO.getOrgAction());
		}
		disputeTxnStateInfoDto.setServiceCode(transactionDetailDTO.getServiceCode());
		disputeTxnStateInfoDto.setAmtPurchase(transactionDetailDTO.getAmtTran());
		disputeTxnStateInfoDto.setEntityType(entityType);
		disputeTxnStateInfoDto.setRevBy(transactionDetailDTO.getRevBy());
		if (StringUtils.isNotBlank(disputeTxnStateInfoDto.getOrgNetReconDate())) {
			disputeTxnStateInfoDto.setOrgNetReconDate(
					DateUtils.getChangeDateFormat(disputeTxnStateInfoDto.getOrgNetReconDate(),
							DateUtils.YYYY_MM_DD, YYYYMMDD_HHMMSS_WITH_SEPERATOR));
		}
		loadDisputeLifeCycleDetails(disputeTxnList, disputeTxnStateInfoDto);
		return disputeTxnStateInfoDto;
	}

	private void handleActionList(Model model,JSONObject jsonObj ,
			JSONObject jsonObjectAvailableOpt, JSONArray actionList, Map<String, String> json)
			throws ParseException, JsonProcessingException {
		Map<String, String> availableOptions = new HashMap<>();
		Double tipAmount = (Double) jsonObj.get(TIP_AMOUNT);
		String orgAction = (String) jsonObj.get(ORG_ACTION);
		if (tipAmount.equals(0.0) && actionList.contains(CommonConstants.TIP_SURCHARGE_ACTION)) {
			actionList.remove(CommonConstants.TIP_SURCHARGE_ACTION);
		}
		availableOptions = getMappingOfAvailableOptionsList(availableOptions, actionList,
				String.valueOf(jsonObjectAvailableOpt.get(CURRENT_STATE)),orgAction);
		getAllPossibleReasonCodes(availableOptions, json, environment.getProperty("reasonCode.option.api"), model);
	}

	private void handlePresentmentInfo(String txnId, TxnSettlementDTO transactionDetailDTO,
			List<TxnSettlementDTO> txnPresentmentInfo, String partId, String disputeOrgTableName,
			List<String> presentTableList) {
		String presentMentTableName = PRESENTMENT_TBL;
		presentMentTableName += transactionDetailDTO.getPresentmentRecvdDate();
		if (disputeOrgTableName != null && !disputeOrgTableName.isEmpty()
				&& disputeOrgTableName.startsWith(PRESENTMENT_TBL)) {
			if (!StringUtils.equals(disputeOrgTableName, presentMentTableName)) {
				presentTableList.add(presentMentTableName);
				presentTableList.add(disputeOrgTableName);
			} else {
				presentTableList.add(presentMentTableName);
			}
		} else {
			presentTableList.add(presentMentTableName);
		}
		for (String tableName : presentTableList) {
			txnPresentmentInfo.addAll(transactionDetailService.getTxnPresentmentData(txnId,
					transactionDetailDTO.getOrgAction(), partId, tableName, transactionDetailDTO.getSearchPartId()));
		}
		if (!txnPresentmentInfo.isEmpty() && StringUtils.equals(txnPresentmentInfo.get(txnPresentmentInfo.size() - 1).getToState(),
				BaseCommonConstants.OFFLINE_PRESENTMENT)) {
			txnPresentmentInfo.remove(txnPresentmentInfo.size() - 1);
		}
	}

	private List<TxnSettlementDTO> handleBulkPresentmentInfo(String txnId, TxnSettlementDTO transactionDetailDTO,
			String partId, String disputeOrgTableName) {
		List<TxnSettlementDTO> txnPresentmentInfo;
		List<String> presentTableList = new ArrayList<>();
		txnPresentmentInfo = new ArrayList<>();
		preparePresentmentInfo(txnId, transactionDetailDTO, txnPresentmentInfo, partId, disputeOrgTableName,
				presentTableList);
		return txnPresentmentInfo;
	}

	public Map<String, String> getMappingOfAvailableOptionsList(Map<String, String> availableOptions,
			JSONArray actionList, String currentState, String orgAction) {
		Map<String, String> actionCodeMap = new HashMap<>();
		Map<String, ActionCodeDTO> actionCodeLists = actionCodeCache.getActionCodeList();
		for (Map.Entry<String, ActionCodeDTO> actionCdMap : actionCodeLists.entrySet()) {
			actionCodeMap.put(actionCdMap.getKey(), actionCdMap.getValue().getActionCodeDesc());
		}
		if (actionList != null) {
			for (int j = 0; j < actionList.size(); j++) {
				String actionCode = (String) actionList.get(j);
				if (StringUtils.equalsAny((String) actionList.get(j), BaseCommonConstants.REFUND_ACTION,
						BaseCommonConstants.PRESENTMENT_REV_ACTION_CODE)
						&& (StringUtils.equals(currentState, BaseCommonConstants.OFFLINE_PRESENTMENT) || StringUtils.equals(orgAction, BaseCommonConstants.OFFLINE_PRESENTMENT))) {
					actionCode += BaseCommonConstants.REFUND_PRSNTMNT_NO_RSN_CD;
					availableOptions.put(actionCode, actionCodeMap.get(actionList.get(j)));
				} else {
					availableOptions.put(actionCode, actionCodeMap.get(actionList.get(j)));
				}
			}
		}
		return availableOptions;

	}

	private void loadDisputeLifeCycleDetails(List<TxnSettlementDTO> disputeTxnModels,
			TxnSettlementDTO disputeTxnModel) {

		StringBuilder disputeLifeCycleHistory = new StringBuilder();
		int size = disputeTxnModels.size();
		AtomicInteger atomiNo = new AtomicInteger();
		JsonObject allDisputesInfo = new JsonObject();
		double totalRefundedAmt = 0.0;
		for (TxnSettlementDTO dispTxn : disputeTxnModels) {
			JsonObject newDisp = new JsonObject();
			disputeLifeCycleHistory.append(dispTxn.getToState());
			disputeLifeCycleHistory.append(atomiNo.incrementAndGet() == size ? "" : ",");
			newDisp.put("raised_date",
					DateUtils.getFormattedDate(dispTxn.getDateRcvd(), YYYYMMDD_HHMMSS_WITH_SEPERATOR));
			newDisp.put("reason_code", dispTxn.getReasonCode());
			newDisp.put("amount", dispTxn.getAmtTran());
			newDisp.put("recon_date", DateUtils.getChangeDateFormat(dispTxn.getNetReconDate(), DateUtils.YYYY_MM_DD,
					YYYYMMDD_HHMMSS_WITH_SEPERATOR));
			if (!dispTxn.getToState().isEmpty()) {
				String actKey = "DISP-" + dispTxn.getToState();
				allDisputesInfo.put(actKey, newDisp);
			}
			if (StringUtils.equalsAny(dispTxn.getFuncCode(), BaseCommonConstants.REFUND, BaseCommonConstants.CHARGEBACK)) {
				totalRefundedAmt += dispTxn.getAmtTran();
			}
		}
		disputeTxnModel.setCurrentState(disputeTxnModel.getToState());
		disputeTxnModel.setDisputeLifeCycle(disputeLifeCycleHistory.toString());
		disputeTxnModel.setLifecyclehistory(disputeTxnModel.getDisputeLifeCycle());
		disputeTxnModel.setAllDisputeInfo(allDisputesInfo);
		disputeTxnModel.setTotalRefundedAmount(totalRefundedAmt);
	}

	public double getCapAmount(JSONArray actionList, Map<String,String> jsonString) throws ParseException {
		double capAmount = 0.0;
		JSONObject jsonObjectCapAmount = new JSONObject();
		if (actionList != null) {
			for (int i = 0; i < actionList.size(); i++) {
				if (actionList.get(i).equals(BaseCommonConstants.TIP_SURPCHARGE_CODE)) {
					jsonObjectCapAmount.put(TO_STATE, actionList.get(i));
					jsonObjectCapAmount.putAll(jsonString);
					String apiResult = sendLogData(environment.getProperty("capAmount.fetch.api"), jsonObjectCapAmount);
					if (!apiResult.isEmpty() && !"ERROR".equals(apiResult)) {
						capAmount = getCapAmtExtracted(apiResult);
					}
				}
			}
		}
		return capAmount;

	}

	private double getCapAmtExtracted(String apiResult) throws ParseException {
		double capAmount;
		String responseCode;
		JSONParser parser = new JSONParser();
		JSONObject jsonObject = (JSONObject) parser.parse(apiResult);
		capAmount = (double) jsonObject.get("tip_amont");
		responseCode = (String) jsonObject.get("Status");
		if (!responseCode.equals(BaseCommonConstants.HTTP_SUCCESS)) {
			capAmount = 0.0;
		}
		return capAmount;
	}

	public JSONArray getAvailableOptionsList(JSONObject jsonObjectAvailableOpt) throws ParseException {
		JSONArray jsonArray = new JSONArray();
		String availableOptionsList = sendLogData(environment.getProperty("available.option.api"),
				jsonObjectAvailableOpt);
		if (!availableOptionsList.isEmpty() && !"ERROR".equals(availableOptionsList)) {
			JSONParser parser = new JSONParser();
			JSONObject jsnobject = (JSONObject) parser.parse(availableOptionsList);
			jsonArray = (JSONArray) jsnobject.get("validToStates");
		}
		return jsonArray;
	}

	@PostMapping("/viewTransactionSearch")
	@PreAuthorize("hasAuthority('View Transaction Search')")
	public String viewTransactionSearch(Model model, String viewName, HttpServletRequest request,
			@ModelAttribute("txnSettlementDTO") TxnSettlementDTO txnSettlementDTO) {
		Map<String, String> funcCodeList = transactionDetailService.getFunctionCodeList();
		model.addAttribute(PARTICIPANT_LIST, transactionDetailService.getParticipantIdList());
		model.addAttribute(FUNC_CD_LIST, funcCodeList);
		if (StringUtils.equals(internationalNetworkType, "Y")) {
			Map<String, String> schemeCodeMap = participantCache
					.getNetworkBankListByPid(sessionDTO.getParticipantName());
			model.addAttribute(SCHEME_CODE_LIST, mapToListConversion(schemeCodeMap));
		}
		model.addAttribute(NETWORK_TYPE, internationalNetworkType);
		return getView(model, "transactionDetailSearch");
	}

	@PostMapping("/searchTransactionData")
	public ResponseEntity<Object> searchTransactionData(
			@ModelAttribute("searchCriteriaDTO") SearchCriteriaDTO searchCriteriaDTO, HttpServletResponse response,
			BindingResult result, Locale locale, ModelAndView modelAndView, ModelMap model,
			@ModelAttribute("txnSettlementDTO") TxnSettlementDTO txnSettlementDTO) {
		JsonObject jsonResponse = new JsonObject();
		JsonArray data = new JsonArray();
		String errorMessage = "";
		String errorStatus = "";
		List<TxnSettlementDTO> finalList = null;
		if (!result.hasErrors()) {
			finalList = transactionDetailService.getTransactionDetails(txnSettlementDTO);
			if (finalList != null && !finalList.isEmpty()) {
				setTxnSearchedDataExtracted(data, finalList);
			}
		}
		// Sending JSON response to page
		jsonResponse.put(ERROR_STATUS, errorStatus);
		jsonResponse.put(ERROR_MSG, errorMessage);
		jsonResponse.put(AA_DATA, data);
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	private void setTxnSearchedDataExtracted(JsonArray data, List<TxnSettlementDTO> finalList) {
		FunctionCode funcCodeInfo;
		for (TxnSettlementDTO c : finalList) {
			funcCodeInfo = setFuncCodeInfoExtracted(c);
			if (funcCodeInfo != null) {
				if (!StringUtils.equals(c.getPcode(), "*")) {
					c.setPcode(c.getPcode().substring(0, 2) + "-" + funcCodeInfo.getTransactionType());
				} else {
					c.setPcode(funcCodeInfo.getTransactionType());
				}
			}
			if (StringUtils.isNotBlank(c.getRespCode())) {
				c.setRespCode(baseTransactionDetailSvc.getResponseCodeDesc(c.getRespCode()));
			}
			c.setOrgTstampLocal(DateUtils.getChangeDateFormat(c.getOrgTstampLocal(), YYYYMMDD_HHMMSS_WITH_SEPERATOR,
					DDMMYYYY_HHMMSS_FORMAT));
			JsonArray row = new JsonArray();
			setSearchTranSearchFields(row,c);
			data.add(row);
		}
	}

	private FunctionCode setFuncCodeInfoExtracted(TxnSettlementDTO c) {
		FunctionCode funcCodeInfo;
		String actionCode="";
		if (c.getToState() != null && !c.getToState().isEmpty()) {
			if (StringUtils.isNotBlank(c.getPcode()) && !StringUtils.equals(c.getPcode(), "*")) {
				funcCodeInfo = baseTransactionDetailSvc.getFuncCodeDesc(c.getMti(),
						c.getPcode().substring(0, 2), c.getFuncCode(), c.getToState());
			} else {
				c.setPcode("*");
				funcCodeInfo = baseTransactionDetailSvc.getFuncCodeDesc(c.getMti(), c.getPcode(),
						c.getFuncCode(), c.getToState());
			}
		} else {
			actionCode = actionCodeCache.getActionCode(c.getMti(), c.getFuncCode());
			if (StringUtils.isNotBlank(c.getPcode()) && !StringUtils.equals(c.getPcode(), "*")) {
				funcCodeInfo = baseTransactionDetailSvc.getFuncCodeDesc(c.getMti(),
						c.getPcode().substring(0, 2), c.getFuncCode(), actionCode);
			} else {
				c.setPcode("*");
				funcCodeInfo = baseTransactionDetailSvc.getFuncCodeDesc(c.getMti(), c.getPcode(),
						c.getFuncCode(), actionCode);
			}
		}
		return funcCodeInfo;
	}

	public void setSearchTranSearchFields(JsonArray row, TxnSettlementDTO c) {
		DecimalFormat decimalFormat = new DecimalFormat("#,##,##0.00");
		String tranCurrCode = "";
		if (StringUtils.isNotEmpty(c.getTranCur())) {
			tranCurrCode = transactionDetailService.getTxnCurrencyCode(c.getTranCur());
		}
		row.add(c.getMaskPan() == null ? "N/A" : c.getMaskPan());
		setOrgTstampAcqRefPcodeTranCurAdd(c, tranCurrCode, row, decimalFormat);
	}

	@PostMapping("/saveMemberActionData")
	@PreAuthorize("hasAuthority('Raise Transaction Dispute')")
	public ResponseEntity<Object> saveMemberActionData(@RequestParam("objectData") String disputeInfo, Model model,
			Locale locale, HttpSession session, HttpServletRequest request) throws JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		DisputeTxnModel txnActionDetail = mapper.readValue(disputeInfo, DisputeTxnModel.class);
		TxnSettlementDTO txnDetailDto;
		String partId = transactionDetailService.getNpciPartId();
		if (StringUtils.equals(txnActionDetail.getOriginalTableName(), BaseCommonConstants.NET_FUND_TXN_TBL)) {
			txnDetailDto = transactionDetailService.getNpciFundCashRevInfo(txnActionDetail.getTxnId(), partId, "");
		} else if (StringUtils.equalsAnyIgnoreCase(txnActionDetail.getOrgFuncCode(),
				BaseCommonConstants.PRE_COMPLIANCE_FUNC_CODE, BaseCommonConstants.GOOD_FAITH_FUNC_CODE,
				BaseCommonConstants.COMPLIANCE_FUNC_CODE)) {
			txnDetailDto = transactionDetailService.getBulkTxnInfo(txnActionDetail.getTxnId(), partId, "");
			if (StringUtils.isNotBlank(txnDetailDto.getOrgTstampLocal())) {
				txnDetailDto.setTstampLocal(txnDetailDto.getOrgTstampLocal());
			}
		} else {
			txnDetailDto = transactionDetailService.getTransactionDetailData(txnActionDetail.getTxnId(),
					txnActionDetail.getOriginalTableName(), partId, "");
		}
		txnDetailDto = transactionDetailService.setTransactionData(txnDetailDto, txnActionDetail);
		JsonObject jsonObjResponse = new JsonObject();
		JSONObject jsonObjInfo = new JSONObject();
		try {
			String results = transactionDetailService.saveMemberActionData(txnDetailDto, jsonObjInfo);
			if (StringUtils.equals(results,BaseCommonConstants.DISPUTE_SUCCESS)) {
				jsonObjResponse.put(STATUS, BaseCommonConstants.TRANSACT_SUCCESS);
			} else {
				jsonObjResponse.put(STATUS, BaseCommonConstants.TRANSACT_FAIL);
			}
		} catch (Exception e) {
			handleErrorCodeAndForward(model,VIEW_TRANSACTION_DETAIL, e);
		}
		
		logger.debug("JSON Response : {}", jsonObjResponse);
		return new ResponseEntity<>( jsonObjResponse.toString() , HttpStatus.OK);
	}

	@PostMapping("/approveTransaction")
	@PreAuthorize("hasAuthority('Approve Transaction Dispute')")
	public String approveTransaction(Model modelApprov, String viewName, HttpServletRequest request,
			@ModelAttribute(name = "txnSettlementDTO", binding = false) TxnSettlementDTO txnSettlementDTO,
			@RequestParam(value = "searchType", required = false) String searchType){
		Map<String, String> funcCodeList = transactionDetailService.getFunctionCodeList();
		modelApprov.addAttribute(FUNC_CD_LIST, funcCodeList);
		if (StringUtils.equals(searchType, BaseCommonConstants.APPROVE_REJECT_DISPUTE)) {
			modelApprov.addAttribute(CommonConstants.SHOW_PENDING_DISPUTE, BaseCommonConstants.NO_FLAG);
			modelApprov.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.NO_FLAG);
		} else {
			modelApprov.addAttribute(CommonConstants.SHOW_PENDING_DISPUTE, BaseCommonConstants.YES_FLAG);
			modelApprov.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.YES_FLAG);
		}
		modelApprov.addAttribute(PARTICIPANT_LIST, transactionDetailService.getParticipantIdList());
		return getView(modelApprov,APPROVE_TRANSACTION_DETAIL);
	}

	@PostMapping("/approveTransactionData")
	public ResponseEntity<Object> approveTransactionData(HttpServletResponse response, Locale locale,
			ModelAndView modelAndView, ModelMap model,
			@RequestParam(value = "searchType", required = false) String searchType) {
		JsonObject jsonResponse = new JsonObject();
		JsonArray data = new JsonArray();
		String errorMessage = "";
		String errorStatus = "";
		List<TxnSettlementDTO> finalList = null;
		finalList = transactionDetailService.getTransactionDetailsStaging(searchType);
		if (finalList != null && !finalList.isEmpty()) {
			for (TxnSettlementDTO c : finalList) {
				JsonArray row = new JsonArray();
				String pCode = "*";
				FunctionCode funcCodeInfo = setPanAndPcode(c, pCode);
				approveTxnDataExtracted(searchType, c, row, funcCodeInfo);	
				setFieldsForTransactionSearch(row,c,searchType);
				data.add(row);
			}
		}
		jsonResponse.put(ERROR_STATUS, errorStatus);
		jsonResponse.put(ERROR_MSG, errorMessage);
		jsonResponse.put(AA_DATA, data);

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	private FunctionCode setPanAndPcode(TxnSettlementDTO c, String pCode) {
		FunctionCode funcCodeInfo;
		funcCodeInfo = setFunctionCodeInfo(c, pCode);
		if (funcCodeInfo != null) {
			if (!StringUtils.equals(funcCodeInfo.getProcCode(), "*")) {
				c.setPcode(c.getPcode().substring(0, 2) + "-" + funcCodeInfo.getTransactionType());
			} else {
				c.setPcode(funcCodeInfo.getTransactionType());
			}
		}
		c.setTstampLocal(DateUtils.getChangeDateFormat(c.getTstampLocal(), YYYYMMDD_HHMMSS_WITH_SEPERATOR,
				DDMMYYYY_HHMMSS_FORMAT));
		if (StringUtils.isNotBlank(c.getEncryptedtokenPan())
				&& StringUtils.equals(c.getParticipantIdAcq(), sessionDTO.getParticipantName())) {
			String maskedPanPrefix = StringUtils.rightPad(c.getTokenPanPrefix(), 12, "*");
			String panSuf = StringUtils.isNotBlank(c.getTokenPanSuffix()) ? c.getTokenPanSuffix() : "N/A";
			c.setMaskTokenPan(maskedPanPrefix+panSuf);		
		} else {
			String maskedPanPrefix = StringUtils.rightPad(c.getPanPrefix(), 12, "*");
			String panSuf = StringUtils.isNotBlank(c.getPanSuffix()) ? c.getPanSuffix() : "N/A";
			c.setMaskPan(maskedPanPrefix + panSuf);
		}
		return funcCodeInfo;
	}

	private void approveTxnDataExtracted(String searchType, TxnSettlementDTO c, JsonArray row,
			FunctionCode funcCodeInfo) {
		if (funcCodeInfo != null) {
			c.setTransactionCycle(funcCodeInfo.getFunctionCodeDescription());
		}
		if (!StringUtils.equals(searchType, BaseCommonConstants.APPROVE_REJECT_DISPUTE)) {
			row.add(c.getStageId());
		}
		if (StringUtils.isNotBlank(c.getReasonCode())) {
			c.setReasonCodeDesc(
					c.getReasonCode() + " - " + reasonCodeCache.getReasonCodeDesc(c.getReasonCode()));
		}
		if (StringUtils.isNotBlank(c.getProcessingStatus())) {
			c.setProcessingStatus(
					reasonCodeCache.getReasonCodeDesc(c.getProcessingStatus()) == null ? c.getProcessingStatus()
							: reasonCodeCache.getReasonCodeDesc(c.getProcessingStatus()));
		}
	}

	public void setFieldsForTransactionSearch(JsonArray row, TxnSettlementDTO c, String searchType)  {
		setFieldsTxnSearchExtracted(row, c);
		
		if (StringUtils.equals(searchType, BaseCommonConstants.APPROVE_REJECT_DISPUTE)) {
			row.add(StringUtils.isBlank(c.getProcessingStatus()) ? "N/A" : c.getProcessingStatus());
		} else {
			row.add(StringUtils.isBlank(c.getReasonCode()) ? "N/A" : c.getReasonCodeDesc());
		}
		setFieldsForDisputes(row, c);
		setFeildTxnSearchExtracted(row, c);
	}
	
	private String checkDispApproveStatus(TxnSettlementDTO disputeTxn, HttpServletRequest request) {
		String messages;
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(disputeTxn.getStatusCode())) {
			messages = messageSource.getMessage("am.msg.DisputeApproved", null, request.getLocale());
		} else if (BaseCommonConstants.REQUEST_STATE_PROGRESS.equalsIgnoreCase(disputeTxn.getStatusCode())) {
			messages = messageSource.getMessage("am.msg.InProgress", null, request.getLocale());
		} else if(disputeTxn.getStatus().equals(BaseCommonConstants.PENDING_REVIEW)){
			messages = messageSource.getMessage("am.msg.sentForReview", null, request.getLocale());
		} else if(disputeTxn.getStatus().equals(BaseCommonConstants.REVIEWED_PENDING_FOR_APPROVAL)) {
			messages = messageSource.getMessage("am.msg.Reviewed", null, request.getLocale());
		}else{
			String rejectReason = "";
			if (StringUtils.isNotBlank(disputeTxn.getProcessingStatus())) {
				rejectReason = disputeTxn.getProcessingStatus();
				String reasonCodeDesc = reasonCodeCache.getReasonCodeDesc(rejectReason) == null ? ""
						: reasonCodeCache.getReasonCodeDesc(rejectReason);
				if (StringUtils.isNotBlank(reasonCodeDesc)) {
					rejectReason += BaseCommonConstants.HYPHEN + reasonCodeDesc;
				}
				messages = messageSource.getMessage("am.msg.DisputeRejected", null, request.getLocale())
						+ BaseCommonConstants.CRLF_CHAR
						+ messageSource.getMessage("am.msg.RejectRson", null, request.getLocale()) + rejectReason;
			} else {
				messages = messageSource.getMessage("am.msg.DisputeRejected", null, request.getLocale());
			}
		}
		return messages;
	}

	@PostMapping("/approveDispute")
	@PreAuthorize("hasAuthority('Approve Transaction Dispute')")
	public ResponseEntity<String> approveOrRejectDispute(@RequestParam("txnId") String txnId,
			@RequestParam("rrn") String rrn, Model model, String viewName, @RequestParam("status") String status,
			@RequestParam("approve_comment") String approveComment, HttpServletRequest request) {
		String messages = null;
		try {
			
			TxnSettlementDTO disputePendingInfoDto = transactionDetailService.getTxnInfoStaging(txnId,status);
			if (disputePendingInfoDto == null) {
				throw new SettleNxtException("Exception occurred while fetching the data from staging table", "");
			}
			disputePendingInfoDto.setStatus(status);
			disputePendingInfoDto.setCheckerComments(approveComment);
			disputePendingInfoDto.setCheckerId(sessionDTO.getUserName());
			transactionDetailService.approveRejectDisputeInfo(disputePendingInfoDto);
			TxnSettlementDTO disputePendingInfoAfterValidation = transactionDetailService
					.getTxnInfoStagingStatus(disputePendingInfoDto);
			messages = checkDispApproveStatus(disputePendingInfoAfterValidation, request);

		} catch (InterruptedException e) {
			handleErrorCodeAndForward(model,VIEW_TRANSACTION_DETAIL, e);
			Thread.currentThread().interrupt();
		}
		return ResponseEntity.ok().contentType(MediaType.TEXT_HTML).body(messages);
	}

	@PostMapping("/npciFundCollectDisburse")
	@PreAuthorize("hasAuthority('Add Fund Collect And Disburse')")
	public String npciFundCollectDisburse(Model model, String viewName, HttpServletRequest request,
			@ModelAttribute("netFundTxnDTO") NetFundTxnDTO netFundTxnDTO) throws JsonProcessingException {
		List<ParticipantDTO> participantlist = transactionDetailService.getParticipantIdList();
		model.addAttribute(PARTICIPANT_LIST, participantlist);
		List<FeeDTO> feeNameList = transactionDetailService.getFeeNameList();
		Map<String, String> partSettleBinList;
		partSettleBinList = transactionDetailService.getPartSettleBinList();
		String jsonSettlementBin = new ObjectMapper().writeValueAsString(partSettleBinList);
		String txnOrgInstId = "National Payments Corporation of India";
		Map<String, List<String>> acquirerIdList = transactionDetailService.getAcquirerIdList();
		Map<String, List<String>> issuerIdList = transactionDetailService.getIssuerIdList();
		String jsonAcqId = new ObjectMapper().writeValueAsString(acquirerIdList);
		String jsonIssId = new ObjectMapper().writeValueAsString(issuerIdList);
		model.addAttribute(ACQ_ID_LIST, jsonAcqId);
		model.addAttribute(ISS_ID_LIST, jsonIssId);
		model.addAttribute(TXN_ORG_INST_ID, txnOrgInstId);
		model.addAttribute(FEE_NAME_LIST, feeNameList);
		model.addAttribute(PART_SETTLE_BIN_LIST, jsonSettlementBin);
		model.addAttribute(MEM_TXT_MSG_REGX, environment.getProperty("MEM_TXT_MSG_REGEX"));
		model.addAttribute(CommonConstants.ADD_FUND_COLLECT_DISBURSE, CommonConstants.TRANSACT_YES);
		return getView(model, "addFundCollectDisburse");
	}

	@PostMapping("/addFundCollectDisburse")
	public ResponseEntity<Object> addFundCollectDisburse(Model model,
			@ModelAttribute("disputeTxnDto") TxnSettlementDTO disputeTxnDto, HttpServletResponse response) {
		String result = transactionDetailService.addFundCollectDisburse(disputeTxnDto);
		JsonObject jsonResponse = new JsonObject();
		if (StringUtils.equals(result,BaseCommonConstants.DISPUTE_SUCCESS)) {
			jsonResponse.put(STATUS, BaseCommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.put(STATUS, BaseCommonConstants.TRANSACT_FAIL);
		}
		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	@PostMapping("/getSettlementBin")
	public ResponseEntity<Object> getSettlementBinInfo(@RequestParam("txnDestInstID") String txnDestInstID,
			@RequestParam("entityType") String entityType, @RequestParam("instId") String instId,
			HttpServletResponse response) {
		String settlementBin = transactionDetailService.getSettlementBin(txnDestInstID, entityType, instId);
		return new ResponseEntity<>(settlementBin, HttpStatus.OK);

	}

	public void verifyFileAvailableInLocation(String filePath) {
		File file = new File(filePath);
		if (!file.exists()) {
			throw new SettleNxtException("File not found", "");
		}
		Path path = Paths.get(filePath);
		Resource resource = null;
		try {
			resource = new UrlResource(path.toUri());
			if (StringUtils.isNotBlank(resource.getFilename())) {
				throw new SettleNxtException("File not found", "");
			}
		} catch (MalformedURLException e) {
			logger.error("File not found {}",e.getCause(),e);
		}	
	}

	@PostMapping("/docDownloadVerify")
	public ResponseEntity<Object> verifyFileAvailable(
			@RequestParam(value = "docPath", required = false) String filePath, Model model) {
		JsonObject jsonResp = new JsonObject();
		try {
			transactionDetailService.verifyFileAvailableInLocation(filePath);
		} catch (Exception ex) {
			jsonResp.put(CommonConstants.STATUS, CommonConstants.TRANSACT_FAIL);
			return new ResponseEntity<>(jsonResp.toString(), HttpStatus.OK);
		}
		jsonResp.put(CommonConstants.STATUS, CommonConstants.TRANSACT_SUCCESS);
		return new ResponseEntity<>(jsonResp.toString(), HttpStatus.OK);
	}

	@PostMapping("/downloadDoc")
	public void downloadDocFile(Model model, HttpServletRequest request, HttpServletResponse response,
			@RequestParam("docPath") String filePath)  {
		if (null != filePath && StringUtils.isNotBlank(filePath)) {
			transactionDetailService.downloadFile(filePath, request, response);
		}
	}

	private String getDynamicDisputeData() {
		if (StringUtils.isBlank(dynamicData)) {
			try (FileReader reader = new FileReader(
					environment.getProperty("FILE_CLASSPATH") + "/disputeModalAdminPortal.json")) {
				JSONParser jsonParser = new JSONParser();
				JSONArray jsonData = (JSONArray) jsonParser.parse(reader);
				dynamicData = jsonData.toJSONString();
			} catch (Exception ex) {
				logger.error("Error while getting dynamic dispute data from JSON: ", ex);
				return null;
			}
		}
		return dynamicData;
	}

	@PostMapping("/approveRejectDisputeBulk")
	@PreAuthorize("hasAuthority('Approve Transaction Dispute')")
	public String approveRejectDisputeBulk(@RequestParam(CommonConstants.STATUS) String status,
			@RequestParam("bulkApprovalStageIdList") String bulkApprovalStageIdList, Model model) {
		String disputeApproveRejctSummary = "";
		try {
			if (status != null) {
				String remark = "";
				if (status.equals(BaseCommonConstants.REQUEST_STATE_PROGRESS)) {
					remark = CommonConstants.BULK_APPROVE;
				} else if (status.equals(BaseCommonConstants.DISPUTE_REJECTED)) {
					remark = CommonConstants.BULK_REJECT;
				}
				DisputeTxnModel disputeTxnDTO = new DisputeTxnModel();
				disputeTxnDTO.setCheckerComments(remark);
				disputeTxnDTO.setStatus(status);
				disputeApproveRejctSummary = transactionDetailService
						.approveRejectDisputesbulk(bulkApprovalStageIdList, disputeTxnDTO, model);
				model.addAttribute(CommonConstants.SUCCESS_STATUS, disputeApproveRejctSummary);
			}
		} catch (InterruptedException e) {
			handleErrorCodeAndForward(model,APPROVE_TRANSACTION_DETAIL, e);
			Thread.currentThread().interrupt();
		}
		TxnSettlementDTO txnSettlementDto = new TxnSettlementDTO();
		Map<String, String> funcCodesList = transactionDetailService.getFunctionCodeList();
		model.addAttribute(FUNC_CD_LIST, funcCodesList);
		model.addAttribute(CommonConstants.SHOW_PENDING_DISPUTE, BaseCommonConstants.YES_FLAG);
		model.addAttribute(SHOW_CHECKBOX, BaseCommonConstants.YES_FLAG);
		model.addAttribute("txnSettlementDTO", txnSettlementDto);
		return getView(model,APPROVE_TRANSACTION_DETAIL);
	}

	// Search Transactions pending/Reject/In Progress
	@PostMapping("/searchPendingApprRejectTran")
	@PreAuthorize("hasAuthority('Approve Transaction Dispute')")
	public ResponseEntity<Object> searchPendingApprRejectTran(
			@ModelAttribute("txnSettlementDTO") TxnSettlementDTO txnSettlementDTO, BindingResult result) {
		JsonObject jsonResponse = new JsonObject();
		JsonArray data = new JsonArray();
		String errorMessage = "";
		String errorStatus = "";
		List<TxnSettlementDTO> finalList = null;
		if (!result.hasErrors()) {
			finalList = transactionDetailService.getPendingApprRejectTranDetails(txnSettlementDTO);
			if (finalList != null && !finalList.isEmpty()) {
				for (TxnSettlementDTO c : finalList) {
					JsonArray row = new JsonArray();
					setFieldsForPendingApprRejectTrans(row,c,txnSettlementDTO);
					data.add(row);
				}
			}
		}
		jsonResponse.put(ERROR_STATUS, errorStatus);
		jsonResponse.put(ERROR_MSG, errorMessage);
		jsonResponse.put(AA_DATA, data);

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);
	}

	private FunctionCode getFunctionCodeInfo(TxnSettlementDTO c) {
		FunctionCode funcCodeInfo;
		String pCode = "*";
		funcCodeInfo = setFunctionCodeInfo(c, pCode);
		return funcCodeInfo;
	}
	
	public void setFieldsForPendingApprRejectTrans(JsonArray row, TxnSettlementDTO c,TxnSettlementDTO txnSettlementDTO) {
		FunctionCode funcCodeInfo;
		funcCodeInfo = getFunctionCodeInfo(c);
		setFieldsForPendingAppRejectTxnExtracted(c, funcCodeInfo);
		addRowMaskPanFuncCodePcodeTimeAmt(row, c, txnSettlementDTO);
	}

	

	

	

	

	private void setFieldsForPendingAppRejectTxnExtracted(TxnSettlementDTO c, FunctionCode funcCodeInfo) {
		if (funcCodeInfo != null) {
			if (!StringUtils.equals(funcCodeInfo.getProcCode(), "*")) {
				c.setPcode(funcCodeInfo.getProcCode().substring(0, 2) + "-"
						+ funcCodeInfo.getTransactionType());
			} else {
				c.setPcode(funcCodeInfo.getTransactionType());
			}
		}
		if (StringUtils.isNotBlank(c.getReasonCode())) {
			c.setReasonCodeDesc(
					c.getReasonCode() + " - " + reasonCodeCache.getReasonCodeDesc(c.getReasonCode()));
		}
		if (StringUtils.isNotBlank(c.getProcessingStatus())) {
			c.setProcessingStatus(reasonCodeCache.getReasonCodeDesc(c.getProcessingStatus()) == null
					? c.getProcessingStatus()
					: reasonCodeCache.getReasonCodeDesc(c.getProcessingStatus()));
		}
		c.setTstampLocal(DateUtils.getChangeDateFormat(c.getTstampLocal(), YYYYMMDD_HHMMSS_WITH_SEPERATOR,
				DDMMYYYY_HHMMSS_FORMAT));
		if (funcCodeInfo != null) {
			c.setTransactionCycle(funcCodeInfo.getFunctionCodeDescription());
		}
	}
	@PostMapping("/uploadTxnFile")
	@PreAuthorize("hasAuthority('Raise Transaction Dispute')")
	public ResponseEntity<Object> uploadTxnFile(@RequestParam("document") List<MultipartFile> document,
			@RequestParam("txnId") String txnId, @RequestParam("tstampLocal") String tstampLocal,
			@RequestParam("status") String status, @RequestParam("docFilePath") String docFilePath,
			@RequestParam("docTypeList") List<String> docTypeList, Model model, Locale locale, HttpSession session,
			HttpServletRequest request) throws IOException, java.text.ParseException{
		String result;
		JSONObject jsonDocInfo = new JSONObject();
		List<DisputeDocumentDTO> docfilePathList;
		if (document != null) {
			String txnOrgInstId = transactionDetailService.getNpciPartId();
					docfilePathList = transactionDetailService.saveUploadedDocument(document, txnId, tstampLocal, txnOrgInstId,
					docTypeList, jsonDocInfo);
			result = transactionDetailService.insertDispDocInfo(docfilePathList, docFilePath, txnId, status,
					jsonDocInfo);
		} else {
			result = "failure";
		}
		return new ResponseEntity<>(result, HttpStatus.OK);
	}



	@PostMapping("/getReasonSubTypeDesc")
	public ResponseEntity<Object> getReasonSubTypeDesc(Model model,
			@RequestParam("reasonSubType[]") List<String> reasonSubTypeList, HttpServletResponse response){
		List<String> reasonSubTypeDescList = new ArrayList<>();
		if (!reasonSubTypeList.isEmpty()) {
			reasonSubTypeDescList = transactionDetailService.getReasonSubTypeDesc(reasonSubTypeList);
		}
		return new ResponseEntity<>(reasonSubTypeDescList, HttpStatus.OK);
	}


}
