<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
	namespace="org.npci.settlenxt.adminportal.repository.RejectReasonCodeRuleRepository">
	
	<select id="getRejectReasonCodeRulemasterList"
	resultType="org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO">

	SELECT reject_reason_code_rules.function_code as funcCode ,
	reject_reason_code_rules.field_name as fieldName ,
	reject_reason_code_rules.field_value as fieldValue ,reject_reason_code_rules.relational_operator
	as relationalOperator,reject_reason_code_rules.field_operator as fieldOperator, reject_reason_code_rules.sub_field_name
	as subFieldName , reject_reason_code_rules.reject_code as rejectCode , reject_reason_code_rules.status as status
	,reject_reason_code_rules.created_by as createdBy ,reject_reason_code_rules.created_on as createdOn
	, reject_reason_code_rules.last_updated_by as lastUpdatedBy , reject_reason_code_rules.last_updated_on as lastUpdatedOn
	,reject_reason_code_rules.seq_id as seqId from reject_reason_code_rules join reject_reason_code_rules_stg 
	on reject_reason_code_rules.seq_id = reject_reason_code_rules_stg.seq_id where 
	reject_reason_code_rules_stg.request_state= 'A' ;

</select>
	
	<select id="getRejectReasonCodeRule" resultType="org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO">
		
		SELECT function_code as funcCode , field_name as fieldName , field_value as fieldValue ,relational_operator as relationalOperator,field_operator as fieldOperator, sub_field_name as subFieldName , reject_code as rejectCode , status as status ,created_by as createdBy ,created_on as createdOn
		, last_updated_by as lastUpdatedBy , last_updated_on as lastUpdatedOn ,request_state as requestState , last_operation as lastOperation , checker_comments as checkerComments,seq_id as seqId from reject_reason_code_rules_stg where request_state= 'A' ;

	</select>
	
	<select id="getPendingRejectReasonCodeList" resultType="org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO">
		
		SELECT function_code as funcCode , field_name as fieldName , field_value as fieldValue ,relational_operator as relationalOperator,field_operator as fieldOperator, sub_field_name as subFieldName , reject_code as rejectCode , status as status ,created_by as createdBy ,created_on as createdOn
		, last_updated_by as lastUpdatedBy , last_updated_on as lastUpdatedOn ,request_state as requestState , last_operation as lastOperation , checker_comments as checkerComments,seq_id as seqId from reject_reason_code_rules_stg where request_state in ('P','R') ;

	</select>
	
	<select id="getRejectReasonCodeStg" resultType="org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO">
		
		SELECT function_code as funcCode , field_name as fieldName , field_value as fieldValue ,relational_operator as relationalOperator,field_operator as fieldOperator, sub_field_name as subFieldName , reject_code as rejectCode , status as status ,created_by as createdBy ,created_on as createdOn
		, last_updated_by as lastUpdatedBy , last_updated_on as lastUpdatedOn ,request_state as requestState , last_operation as lastOperation , checker_comments as checkerComments,seq_id as seqId from reject_reason_code_rules_stg where seq_id = #{seqId} ;

	</select>
	
	<insert id="addRejectReasonCode">
        INSERT INTO  reject_reason_code_rules_stg (function_code,field_name,field_value,relational_operator,field_operator,sub_field_name,reject_code,status,CREATED_BY,CREATED_ON,LAST_UPDATED_BY,LAST_UPDATED_ON,request_state,last_operation,checker_comments,seq_id)VALUES(#{funcCode}, #{fieldName}, 
        #{fieldValue},#{relationalOperator},#{fieldOperator},#{subFieldName},#{rejectCode},#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn}, #{requestState}, #{lastOperation},#{checkerComments},#{seqId})
	</insert>
	
	<select id="getRejectReasonCode" resultType="org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO">
		
		SELECT function_code as funcCode , field_name as fieldName , field_value as fieldValue ,relational_operator as relationalOperator,field_operator as fieldOperator, sub_field_name as subFieldName , reject_code as rejectCode , status as status ,created_by as createdBy ,created_on as createdOn
		, last_updated_by as lastUpdatedBy , last_updated_on as lastUpdatedOn,seq_id as seqId from reject_reason_code_rules where seq_id = #{seqId};

	</select>
	
	<select id="getPendingRejectReasonCode" resultType="org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO">
		
		SELECT function_code as funcCode , field_name as fieldName , field_value as fieldValue ,relational_operator as relationalOperator,field_operator as fieldOperator, sub_field_name as subFieldName , reject_code as rejectCode , status as status ,created_by as createdBy ,created_on as createdOn
		, last_updated_by as lastUpdatedBy , last_updated_on as lastUpdatedOn,request_state as requestState , last_operation as lastOperation , checker_comments as checkerComments,seq_id as seqId from reject_reason_code_rules_stg where seq_id = #{seq_id} ;

	</select>
	
	<update id="updateRejectReasoncodeStg">
		update reject_reason_code_rules_stg set function_code=#{funcCode} , field_name = #{fieldName},field_value = #{fieldValue} , relational_operator = #{relationalOperator} , field_operator = #{fieldOperator} , sub_field_name = #{subFieldName} ,reject_code=#{rejectCode} , status = #{status}, created_by = #{createdBy} , created_on = #{createdOn}
		, last_updated_by = #{lastUpdatedBy} , last_updated_on = #{lastUpdatedOn} , request_state =#{requestState},last_operation = #{lastOperation} , checker_comments = #{checkerComments} where seq_id = #{seqId};
	</update>
	
	<update id="updateRejectReasonCode">
		update reject_reason_code_rules set function_code=#{funcCode} , field_name = #{fieldName},field_value = #{fieldValue} , relational_operator = #{relationalOperator} , field_operator = #{fieldOperator} , sub_field_name = #{subFieldName} ,reject_code=#{rejectCode} , status = #{status}, created_by = #{createdBy} , created_on = #{createdOn}
		, last_updated_by = #{lastUpdatedBy} , last_updated_on = #{lastUpdatedOn}  where seq_id = #{seqId};
	</update>
	
	<insert id="saveRejectReasonCode">
        INSERT INTO  reject_reason_code_rules (function_code,field_name,field_value,relational_operator,field_operator,sub_field_name,reject_code,status,CREATED_BY,CREATED_ON,LAST_UPDATED_BY,LAST_UPDATED_ON,seq_id)VALUES(#{funcCode}, #{fieldName}, 
        #{fieldValue},#{relationalOperator},#{fieldOperator},#{subFieldName},#{rejectCode},#{status},#{createdBy},#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn},#{seqId})
	</insert>
	
	<delete id="deleteRejectReasonCode">
		delete from reject_reason_code_rules_stg where seq_id = #{seqId};
	</delete>
	
	<select id="fetchSeqId" resultType="org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO">
		SELECT nextval('reject_reason_code_rules_stg_seq') as seqId;
	</select>
	
	<select id="fetchFuncCodeList" resultType="org.npci.settlenxt.portal.common.dto.CodeValueDTO">
		SELECT func_code as code , func_code_desc as description from func_code group by func_code,func_code_desc;
	</select>
		
</mapper>