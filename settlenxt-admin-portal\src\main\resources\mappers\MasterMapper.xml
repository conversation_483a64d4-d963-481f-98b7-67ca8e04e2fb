<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.MasterRepository">

	<select id="getStateMaster" resultType="LookUpDTO">
		SELECT STATE_ID AS
		lkpValue,STATE_NAME AS lkpDesc FROM STATE ORDER BY STATE_NAME
	</select>

	<select id="getIfscCodesList" resultType="LookUpDTO">
		SELECT IFSC_CODE AS
		lkpValue,IFSC_CODE||' - '|| IFSC_DESCRIPTION AS lkpDesc FROM IFSC
		ORDER BY IFSC_CODE
	</select>
	<select id="getCityMaster" resultType="LookUpDTO">
		SELECT CITY_ID AS
		lkpValue, CITY_NAME AS lkpDesc FROM CITY
	</select>
	<select id="getCountryList" resultType="CountryDTO">
		SELECT COUNTRY_ID as
		countryId, COUNTRY_NAME as countryName FROM COUNTRY WHERE STATUS='A'
		ORDER BY COUNTRY_NAME
	</select>
	<select id="getStateCityMasterList" resultType="CityDTO">
		SELECT CITY_ID as
		cityId, CITY_NAME as cityName FROM CITY WHERE STATUS='A' AND STATE_ID=
		#{stateId} ORDER BY CITY_NAME
	</select>
	<select id="getStateCode" resultType="String">
		SELECT STATE_CODE as
		stateCode FROM STATE WHERE STATE_ID= #{stateId}
	</select>
	<select id="getBinFeatures" resultType="String">
		SELECT FEATURES as
		feturesIssuerbin FROM MEMBIN_DETAILS_STG WHERE BIN_CARD_TYPE =
		#{cardType}
	</select>
	<select id="getBinCardVariant" resultType="String">
		SELECT
		BIN_CARD_VARIANT as binCardVariant FROM MEMBIN_DETAILS_STG WHERE
		BIN_CARD_TYPE = #{cardType}
	</select>
	<select id="getUniqueBankName" resultType="MemberDTO">
		select distinct
		unique_bank_name as uniqueBnk FROM participant_stg WHERE ifsc_code =
		#{ifscCode} and record_status='A'
	</select>
	<select id="getListUniqueBankName" resultType="String">
		select distinct
		unique_bank_name as uniqueBnk FROM participant WHERE ifsc_code =
		#{ifscCode} and status='A'
	</select>
	<select id="getBankNFSCode" resultType="String">
		SELECT NFS_CODE as
		nfsCode FROM IFSC WHERE IFSC_CODE = #{ifscCode}
	</select>
	<select id="getFunctionCode" resultType="TxnSettlementDTO">
		SELECT code as
		funcCode,description as transactionCycle from lookup where
		type='FUNC_CODE_DESC'
	</select>
	<select id="getFunctionTxnCycle" resultType="TxnSettlementDTO">
		SELECT func_code as
		code,transaction_cycle as description FROM func_code_master
		WHERE func_code=#{funcCode}
	</select>
	<select id="getLookUpData" resultType="LookUpDTO">
		SELECT type AS lkpType,
		code AS lkpValue, description AS lkpDesc FROM lookup WHERE type=
		#{lookupType} AND STATUS='A'
	</select>
	<select id="getLookUpList" resultType="LookUpDTO">
		SELECT type AS lkpType, code AS lkpValue, description AS lkpDesc FROM
		LOOKUP WHERE type in
		<foreach item='item' index='index' collection='lookUpList'
			open='(' separator=',' close=')'>#{item}</foreach>
		AND STATUS='A'
	</select>
	<select id="getCountryMasterList" resultType="LookUpDTO">
		SELECT COUNTRY_ID
		AS lkpValue ,COUNTRY_NAME AS lkpDesc FROM COUNTRY order by
		COUNTRY_NAME
	</select>
	<select id="getStateMasterCodeList" resultType="LookUpDTO">
		SELECT STATE_CODE
		AS lkpValue,STATE_NAME AS lkpDesc FROM STATE ORDER BY
		STATE_NAME
	</select>
	<select id="getStateMasterCodeIdList" resultType="LookUpDTO">
		SELECT
		STATE_CODE AS lkpValue,STATE_ID AS lkpDesc FROM STATE ORDER BY
		STATE_ID
	</select>
	<select id="getCityName" resultType="LookUpDTO">
		SELECT city_name FROM CITY where city_id=#{cityId}
	</select>
	<select id="getCityMasterLkp" resultType="LookUpDTO">
		SELECT CITY_ID as
		lkpValue ,CITY_NAME as lkpDesc FROM CITY WHERE
		STATUS=#{status} AND
		STATE_ID=#{stateId} ORDER BY CITY_NAME
	</select>
	<select id="getStateMasterList" resultType="LookUpDTO">
		SELECT STATE_ID as
		lkpValue,STATE_NAME as lkpDesc FROM STATE ORDER BY
		STATE_NAME
	</select>

	<select id="getCityMasterList" resultType="LookUpDTO">
		SELECT CITY_ID as
		lkpValue ,CITY_NAME as lkpDesc FROM CITY
	</select>
	
</mapper>		