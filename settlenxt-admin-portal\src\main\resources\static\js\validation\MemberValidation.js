function bindFieldValidationsOnLoad() {
	var fieldValidations = [];
	fieldValidations.push({ id: 'memberName', isMandatory: true, fieldType: 'AlphaNumCommaHyphenUndScPoint', length: '100', isExactLength: false });
	fieldValidations.push({ id: 'parentParticipantId', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'memberType', isMandatory: true, fieldType: 'SelectionBox', length: '15', isExactLength: false });
	fieldValidations.push({ id: 'uniqueBnk', isMandatory: true, fieldType: 'AlphaNumCommaHyphenUndSc', length: '100', isExactLength: false });
	fieldValidations.push({ id: 'gstn', isMandatory: true, fieldType: 'Gstin', length: '15', isExactLength: false });
	fieldValidations.push({ id: 'gstPincode', isMandatory: true, fieldType: 'NumericsOnly', length: '6', isExactLength: true });
	fieldValidations.push({ id: 'bnkPhone', isMandatory: true, fieldType: 'NumericsOnlyBtw8to12', length: '12', isExactLength: false });
	fieldValidations.push({ id: 'bnkMobile2', isMandatory: false, fieldType: 'NumericsOnlyBtw10to15', length: '15', isExactLength: false });
	fieldValidations.push({ id: 'bnkPhone2', isMandatory: false, fieldType: 'NumericsOnlyBtw8to12', length: '12', isExactLength: false });
	fieldValidations.push({ id: 'bnkMobile', isMandatory: true, fieldType: 'NumericsOnlyBtw10to15', length: '15', isExactLength: false });
	fieldValidations.push({ id: 'ifscCode', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'subNet', isMandatory: true, fieldType: 'SelectionBox', length: '3', isExactLength: false });
	fieldValidations.push({ id: 'bankSector', isMandatory: true, fieldType: 'SelectionBox', length: '15', isExactLength: false });
	fieldValidations.push({ id: 'clearingAgencyType', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });	
	
	fieldValidations.push({ id: 'bnkEmail', isMandatory: true, fieldType: 'MailValidation', length: '100', isExactLength: false });
	fieldValidations.push({ id: 'bnkEmail2', isMandatory: false, fieldType: 'MailValidation', length: '100', isExactLength: false });
	fieldValidations.push({ id: 'bnkCountry', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'cntCountry', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'bnkState', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'bnkCity', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'bnkPincode', isMandatory: true, fieldType: 'NumericsOnly', length: '6', isExactLength: true });
	fieldValidations.push({ id: 'cntPincode', isMandatory: true, fieldType: 'NumericsOnly', length: '6', isExactLength: true });
	fieldValidations.push({ id: 'gstCountry', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'gstState', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'gstCity', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'cntChkrName', isMandatory: true, fieldType: 'AlphaAndDot', length: '50', isExactLength: false });
	fieldValidations.push({ id: 'cntPhone', isMandatory: true, fieldType: 'NumericsOnlyBtw8to12', length: '12', isExactLength: false });
	fieldValidations.push({ id: 'cntMobile', isMandatory: false, fieldType: 'NumericsOnlyBtw10to15', length: '15', isExactLength: false });
	fieldValidations.push({ id: 'cntFax', isMandatory: false, fieldType: 'NumericsOnlyBtw8to12', length: '15', isExactLength: false });
	fieldValidations.push({ id: 'cntEmail', isMandatory: true, fieldType: 'MailValidation', length: '100', isExactLength: false });
	fieldValidations.push({ id: 'addressType', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'cntState', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'cntCity', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'bnkAdd', isMandatory: true, fieldType: 'streetaddress', length: '200', isExactLength: false });
	fieldValidations.push({ id: 'gstAdd', isMandatory: true, fieldType: 'streetaddress', length: '200', isExactLength: false });
	fieldValidations.push({ id: 'cntAdd1', isMandatory: true, fieldType: 'streetaddress', length: '500', isExactLength: false });
	fieldValidations.push({ id: 'participantIdNFS', isMandatory: true, fieldType: 'AlphaNumericNoSpace', length: '3', isExactLength: true });
	fieldValidations.push({ id: 'bankMasterCode', isMandatory: true, fieldType: 'NumericsOnly', length: '3', isExactLength: true });
	fieldValidations.push({ id: 'rtgsCode', isMandatory: true, fieldType: 'AlphaNumericNoSpace', length: '11', isExactLength: true });
	fieldValidations.push({ id: 'participantId', isMandatory: true, fieldType: 'AlphaNumericNoSpace', length: '11', isExactLength: true });
	fieldValidations.push({ id: 'savingsAccNumber', isMandatory: true, fieldType: 'AlphaNumericNoSpace', length: '7', isExactLength: false });
	fieldValidations.push({ id: 'currentAccNumber', isMandatory: true, fieldType: 'AlphaNumericNoSpace', length: '7', isExactLength: false });
	fieldValidations.push({ id: 'currencyCode', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'acqBankGroup', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'acquirerId', isMandatory: true, fieldType: 'NumericsOnly', length: '6', isExactLength: true });
	fieldValidations.push({ id: 'acqDomainUsage', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'issDomainUsage', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'issBinType', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	
	
	fieldValidations.push({ id: 'binLength', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });	
	fieldValidations.push({ id: 'issBankGroup', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'lowBin', isMandatory: true, fieldType: 'NumericsOnly', length: '9', isExactLength: true });
	fieldValidations.push({ id: 'highBin', isMandatory: true, fieldType: 'NumericsOnly', length: '9', isExactLength: true });
	fieldValidations.push({ id: 'panLength', isMandatory: true, fieldType: 'NumericBtwn8to20', length: '20', isExactLength: false });
	fieldValidations.push({ id: 'binCardType', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'binProductType', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'binCardVariant', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'binCardBrand', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'messageType', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'cardTechnology', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'authMechanism', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'issProductType', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'subScheme', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'cardSubVariant', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'programDetails', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'formFactor', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'issSettlementBin', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'featureMultiple', isMandatory: false, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'acqProductType', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'acqSettlementBin', isMandatory: true, fieldType: 'SelectionBox', length: '', isExactLength: false });
	fieldValidations.push({ id: 'issFrmDate', isMandatory: true, fieldType: 'DateFormat', length: '10', isExactLength: false });
	fieldValidations.push({ id: 'issToDate', isMandatory: true, fieldType: 'DateFormat', length: '10', isExactLength: false });
	fieldValidations.push({ id: 'acqFrmDate', isMandatory: true, fieldType: 'DateFormat', length: '10', isExactLength: false });
	fieldValidations.push({ id: 'acqToDate', isMandatory: true, fieldType: 'DateFormat', length: '10', isExactLength: false });
	fieldValidations.push({ id: 'maxUser', isMandatory: true, fieldType: 'NumericsOnly', length: '3', isExactLength: false });
	fieldValidations.push({ id: 'webSite', isMandatory: false, fieldType: 'AlphaNumCommaHyphenUndSc', length: '100', isExactLength: false });
	fieldValidations.push({ id: 'cntDesignation', isMandatory: false, fieldType: 'AlphaNumCommaHyphenUndSc', length: '100', isExactLength: false });

fieldValidations.forEach(function (fieldValidation) {
		if(fieldValidation.id=='bnkAdd' || fieldValidation.id=='gstAdd' || fieldValidation.id=='cntAdd1'){
		$('#' + fieldValidation.id).on('keyup keypress blur change', function () {
			validateField(fieldValidation.id, fieldValidation.isMandatory, fieldValidation.fieldType, fieldValidation.length, fieldValidation.isExactLength);
			});
		}
		else{
		$('#' + fieldValidation.id).on('keyup keypress blur change', function () {
			validateFromCommonVal(fieldValidation.id, fieldValidation.isMandatory, fieldValidation.fieldType, fieldValidation.length, fieldValidation.isExactLength);
			
		});
		}
	});
	
	
	fieldValidations.forEach(function (fieldValidation) {
		$('#' + fieldValidation.id).on('keyup keypress change', function () {
			unableSave();
		});
	});

}
function disableSave()
{
if (typeof saveMemberData != "undefined") {
	
	if($('#recStatus').val()=='A' || $('#recStatus').val()=='R' || $('#recStatus').val()=='I') 
		$('#saveMemberData').attr('disabled', true);
}
}

function unableSave()
{
if (typeof saveMemberData != "undefined") {
	
	$('#saveMemberData').attr('disabled', false);	
}
if (typeof submitMemberData != "undefined") {
	
	$('#submitMemberData').attr('disabled', false);
}
}

function hideErrorDivOnDataChange(fieldId, defaultValue) {
	$("#" + fieldId).change(function () {
		if ($('#' + fieldId).val() != defaultValue) {
			$('#err' + fieldId).hide();
		}
	});
}
function bindValidationMessageShowHideOnload() {
	hideErrorDivOnDataChange('#parentParticipantId', '0');
}
function validateMemberDataOnSave() {

	var hasErrors = false;
	if ($('#memberType').val() == undefined
		|| $('#memberType').val() == '0') {
		hasErrors = true;
		$("#errmemberType").find('.error').html("Please select Bank Type");
		$('#errmemberType').show();
	} else {
		$('#errmemberType').hide();
	}
	if ((submemberBankType.indexOf($('#memberType').val()) != -1)
		&& $('#parentParticipantId').val() == '0') {
		hasErrors = true;
		$("#errparentParticipantId").find('.error').html("Please select Sponsor Bank");
		$('#errparentParticipantId').show();
	} else {
		$('#errparentParticipantId').hide();
	}
	if ($('#ifscCode').val() == undefined
		|| $('#ifscCode').val() == 'SELECT') {
		hasErrors = true;
		$("#errifscCode").find('.error').html("Please select IFSC Code");
		$('#errifscCode').show();
	} else {
		$('#errifscCode').hide();
	}
	if ($('#subNet').val() == undefined
		|| $('#subNet').val() == '') {
		hasErrors = true;
		$("#errsubNet").find('.error').html("Please select Sub Network");
		$('#errsubNet').show();
	} else {
		$('#errsubNet').hide();
	}
	if ($('#maxUser').val() == undefined
		|| $('#maxUser').val() == '') {
		hasErrors = true;
		$("#errmaxUser").find('.error').html("Please enter Max User Number");
		$('#errmaxUser').show();
	} else {
		$('#errmaxUser').hide();
	}
	if ($('#networkUsed').val()!='Y' && ($('#bankSector').val() == undefined
		|| $('#bankSector').val() == '0')) {
		hasErrors = true;
		$("#errbankSector").find('.error').html("Please select Bank Sector");
		$('#errbankSector').show();
	} else {
		$('#errbankSector').hide();
	}
		
	if($('#networkUsed').val()=='Y'&&!($("#settlementPanel").is(":hidden"))){
		if ($('#currencyConversionBy').val() == undefined
		|| $('#currencyConversionBy').val() == '0') {
		hasErrors = true;
		$("#errcurrencyConv").find('.error').html("Please select Currency Conversion");
		$('#errcurrencyConv').show();
	} else {
		$('#errcurrencyConv').hide();
	}
	if ($('#isType').val() == undefined
		|| $('#isType').val() == '0') {
		hasErrors = true;
		$("#errisType").find('.error').html("Please select NPCI Settlement");
		$('#errisType').show();
	} else {
		$('#errisType').hide();
	}
	if ($('#currencyConversionType').val() == undefined
		|| $('#currencyConversionType').val() == '0') {
		hasErrors = true;
		$("#errcurrencyConvType").find('.error').html("Please select Currency Conversion Type");
		$('#errcurrencyConvType').show();
	} else {
		$('#errcurrencyConvType').hide();
	}
	if ($('#forexId').val() == undefined
		|| $('#forexId').val() == '0') {
		hasErrors = true;
		$("#errforexId").find('.error').html("Please select Forex Id");
		$('#errforexId').show();
	} else {
		$('#errforexId').hide();
	}
	
	}
	
	if (!validateFromCommonVal('bankMasterCode', true, "NumericsOnly", 3, true)) {
		hasErrors = true;
	}
	if (!validateFromCommonVal('participantIdNFS', true, "AlphaNumericNoSpace", 3, true)) {
		hasErrors = true;
	}
	if (!validateFromCommonVal('rtgsCode', true, "AlphaNumericNoSpace", 11, true)) {
		hasErrors = true;
	}
	if (!validateFromCommonVal('savingsAccNumber', true, "AlphaNumericNoSpace", 7, false)) {
		hasErrors = true;
	}
	if (!validateFromCommonVal('currentAccNumber', true, "AlphaNumericNoSpace", 7, false)) {
		hasErrors = true;
	}
	if (!validateFromCommonVal('memberName',
		true, "AlphaNumCommaHyphenUndScPoint", 100, false)) {
		hasErrors = true;
	}

	if (requestStateFlag == 'SAVE' || requestStateFlag == 'EDIT' || requestStateFlag == 'ADD' || requestStateFlag == 'SUBMIT') {
	if( ($('#addressType').val() != "0") || ($('#cntAdd1').val() !='') || ($('#cntPincode').val() !='') || ($('#cntChkrName').val() !='') || ($('#cntFax').val() !='') || ($('#cntEmail').val() !='') || ($('#cntState').val() != "0") || ($('#cntCity').val() != "0") || ($('#cntCountry').val() != "0") || ($('#cntPhone').val() !='') || ($('#cntMobile').val() !='') ){
			if ($('#addressType').val() == undefined
				|| $('#addressType').val() == '0') {
				hasErrors = true;
				

				var error = "Please select Address Type";
				document.getElementById("erraddressType").innerHTML = error.fontcolor("red");

				$('#erraddressType').show();
			} else {
				$('#erraddressType').hide();
			}
			if (!validateField('cntAdd1',true, "streetaddress", 500, false)) {
				hasErrors = true;
			}
			if (!validateFromCommonVal('cntPincode',
				true, "NumericsOnly", 6, true)) {
				
				hasErrors = true;
			}
			if (!validateFromCommonVal('cntChkrName',
				true, "AlphaAndDot", 50, false)) {
				
				hasErrors = true;
			}


			if (!validateFromCommonVal('cntFax',
				false, "NumericsOnlyBtw8to12", 15, false)) {
				
				hasErrors = true;
			}


			if (!validateFromCommonVal('cntEmail',
				true, "MailValidation", 100, false)) {
				
				hasErrors = true;
			}
			if (!validateFromCommonVal('cntState',
				true, "SelectionBox", "", false)) {
				hasErrors = true;
				
			}
				if (!validateFromCommonVal('cntCity',
				true, "SelectionBox", "", false)) {
				hasErrors = true;
				
			}
			if (!validateFromCommonVal('cntCountry', true, "SelectionBox", "", false)) {
				hasErrors = true;
				
			}
			



			if (!validateFromCommonVal('cntPhone',
				true, "NumericsOnlyBtw8to12", 11, false)) {
				hasErrors = true;
				
			}
			if (!validateFromCommonVal('cntMobile',
				false, "NumericsOnlyBtw10to15", 15, false)) {
				hasErrors = true;
				
			}
	}
	}
	return hasErrors;
}
function validateMemberDataOnSubmit() {
	if ($('#submitMemberData').attr('disabled') != 'disabled') {
		var hasErrors = false;
		var SubmitdisableCheck = $('#submitdisableCheck').val();
		if ($('#addEditMember').data('changed') || $('#requestState').val() == "SAVE" || SubmitdisableCheck == "No") {
			$('#errorStatus2').html("");
			$('#jqueryError2').hide();
			var collapseOne = false;
			var collapseTwo = false;
			var collapseThree = false;
			
			var collapseFive = false;

			if ($('#memberType').val() == undefined
				|| $('#memberType').val() == '0') {
				hasErrors = true;
				collapseOne = true;
				$('#errmemberType').find('.error').html(
					'Please select Bank Type');
				$('#errmemberType').show();

			} else {
				$('#errmemberType').hide();
			}
			if ((submemberBankType.indexOf($('#memberType').val()) != -1)
				&& $('#parentParticipantId').val() == '0') {
				hasErrors = true;
				collapseOne = true;
				$('#errparentParticipantId')
					.find('.error').html(
						'Please select Sponsor Bank');
				$('#errparentParticipantId').show();

			} else {
				$('#errparentParticipantId').hide();
			}

			if ($('#ifscCode').val() == undefined
				|| $('#ifscCode').val() == '0') {
				hasErrors = true;
				collapseOne = true;
				$('#errifscCode').find('.error').html(
					'Please select IFSC Code');
				$('#errifscCode').show();

			} else {
				$('#errifscCode').hide();
			}

			if (($('#bankSector').val() == undefined
				|| $('#bankSector').val() == '0')&&document.getElementById("networkUsed").value!='Y') {
				hasErrors = true;
				collapseOne = true;
				$('#errbankSector').find('.error').html(
					'Please select Bank Sector');
				$('#errbankSector').show();

			} else {
				$('#errbankSector').hide();
			}

			if ($('#addressType').val() == undefined
				|| $('#addressType').val() == '0') {
				hasErrors = true;
				collapseThree = true;

				var error = "Please select Address Type";
				document.getElementById("erraddressType").innerHTML = error.fontcolor("red");

				$('#erraddressType').show();
			} else {
				$('#erraddressType').hide();
			}



		
			if (!validateFromCommonVal('uniqueBnk',
				true, "AlphaNumCommaHyphenUndSc", 100, false)&&document.getElementById("networkUsed").value!='Y') {
				hasErrors = true;
				collapseOne = true;
			}




			if (!validateFromCommonVal('bankMasterCode', true, "NumericsOnly", 3, true)) {
				hasErrors = true;
				collapseOne = true;
			}

			if (!validateFromCommonVal('participantIdNFS', true, "AlphaNumericNoSpace", 3, true)) {
				hasErrors = false;
				collapseOne = true;
			}

			if (!validateFromCommonVal('rtgsCode', true, "AlphaNumericNoSpace", 11, true)) {
				hasErrors = false;
				collapseOne = true;
			}

			if (!validateFromCommonVal('participantId', true, "AlphaNumericNoSpace", 11, true)) {
				hasErrors = false;
				collapseOne = true;
			}

			if (!validateFromCommonVal('savingsAccNumber', true, "AlphaNumericNoSpace", 7, false)) {
				hasErrors = false;
				collapseOne = true;
			}

			if (!validateFromCommonVal('currentAccNumber', true, "AlphaNumericNoSpace", 7, false)) {
				hasErrors = false;
				collapseOne = true;
			}

			if (!validateFromCommonVal('maxUser', true, "NumericsOnly", 3, false)) {
				hasErrors = true;
				collapseOne = true;
			}
			if (!validateFromCommonVal('memberName',
				true, "AlphaNumCommaHyphenUndScPoint", 100, false)) {
				hasErrors = true;
				collapseOne = true;
			}

			if (!validateFromCommonVal('bnkState',
				true, "SelectionBox", "", false)) {
				hasErrors = true;
				collapseOne = true;
			}

			

			if (!validateFromCommonVal('cntState',
				true, "SelectionBox", "", false)) {
				hasErrors = true;
				collapseOne = true;
			}


			if (!validateFromCommonVal('bnkCity',
				true, "SelectionBox", "", false)) {
				hasErrors = true;
				collapseOne = true;
			}


			

			if (!validateFromCommonVal('cntCity',
				true, "SelectionBox", "", false)) {
				hasErrors = true;
				collapseOne = true;
			}

			if (!validateFromCommonVal('bnkCountry', true, "SelectionBox", "", false)) {
				hasErrors = true;
				collapseOne = true;
			}
			if (!validateFromCommonVal('cntCountry', true, "SelectionBox", "", false)) {
				hasErrors = true;
				collapseOne = true;
			}
			



			if (!validateFromCommonVal('cntPhone',
				true, "NumericsOnlyBtw8to12", 11, false)) {
				hasErrors = true;
				collapseThree = true;
			}

			if (!validateFromCommonVal('bnkPhone',
				true, "NumericsOnlyBtw8to12", 12, false)) {
				hasErrors = true;
				collapseOne = true;
			}

			if (!validateFromCommonVal('bnkPhone2',
				false, "NumericsOnlyBtw8to12", 12, false)) {
				hasErrors = true;
				collapseOne = true;
			}


			if (!validateFromCommonVal('cntMobile',
				false, "NumericsOnlyBtw10to15", 15, false)) {
				hasErrors = true;
				collapseThree = true;
			}



			if (!validateFromCommonVal('bnkMobile',
				true, "NumericsOnlyBtw10to15", 15, false)) {
				hasErrors = true;
				collapseOne = true;
			}

			if (!validateFromCommonVal('bnkMobile2',
				false, "NumericsOnlyBtw10to15", 15, false)) {
				hasErrors = true;
				collapseOne = true;
			}


			if (!validateFromCommonVal('bnkEmail',
				true, "MailValidation", 100, false)) {
				hasErrors = true;
				collapseOne = true;
			}

			if (!validateFromCommonVal('bnkEmail2',
				false, "MailValidation", 100, false)) {
				hasErrors = true;
				collapseOne = true;
			}

if($('#gstn').val() != ''){

			if (!validateFromCommonVal('gstn',
				true, "Gstin", 15, false)) {
				hasErrors = true;
				collapseOne = true;
			}

			if (!validateField('gstAdd',
					true, "streetaddress", 200, false)) {
					hasErrors = true;
					collapseOne = true;
				}
			if (!validateFromCommonVal('gstCountry', true, "SelectionBox", "", false)) {
				hasErrors = true;
				collapseOne = true;
			}
			if (!validateFromCommonVal('gstState',
					true, "SelectionBox", "", false)) {
					hasErrors = true;
					collapseOne = true;
				}
			if (!validateFromCommonVal('gstCity',
					true, "SelectionBox", "", false)) {
					hasErrors = true;
					collapseOne = true;
				}
			if (!validateFromCommonVal('gstPincode',
					true, "NumericsOnly", 6, true)) {
					collapseOne = true;
					hasErrors = true;
				}
}
			
			if (!validateField('cntAdd1',
				true, "streetaddress", 500, false)) {
				hasErrors = true;
				collapseThree = true;
			}


			if (!validateField('bnkAdd',
				true, "streetaddress", 200, false)) {
				hasErrors = true;
				collapseOne = true;
			}



			


			if (!validateFromCommonVal('cntPincode',
				true, "NumericsOnly", 6, true)) {
				collapseThree = true;
				hasErrors = true;
			}

			if (!validateFromCommonVal('bnkPincode',
				true, "NumericsOnly", 6, true)) {
				collapseOne = true;
				hasErrors = true;
			}

			


			if (!validateFromCommonVal('cntChkrName',
				true, "AlphaAndDot", 50, false)) {
				collapseThree = true;
				hasErrors = true;
			}


			if (!validateFromCommonVal('cntFax',
				false, "NumericsOnlyBtw8to12", 15, false)) {
				collapseThree = true;
				hasErrors = true;
			}


			if (!validateFromCommonVal('cntEmail',
				true, "MailValidation", 100, false)) {
				collapseThree = true;
				hasErrors = true;
			}

			 SubmitdisableCheck = $('#submitdisableCheck').val();

			if (!hasErrors || SubmitdisableCheck == "No") {
				
				return hasErrors;
			}
			else {
				if (collapseOne) {

					if ($("#collapseOne").attr(
						'class').indexOf(
							'collapse in') == -1) {
						$("#collapseOneLink")
							.trigger("click");
					}

				} else if (collapseTwo) {

					if ($("#collapseTwo").attr(
						'class').indexOf(
							'collapse in') == -1) {
						$("#collapseTwoLink")
							.trigger("click");
					}

				} else if (collapseThree) {

					if ($("#collapseThree").attr(
						'class').indexOf(
							'collapse in') == -1) {
						$("#collapseThreeLink")
							.trigger("click");
					}

				} else if (collapseFive) {

					if ($("#collapseFive").attr(
						'class').indexOf(
							'collapse in') == -1) {
						$("#collapseFiveLink")
							.trigger("click");
					}

				}
			}
		}
		else {
			$('#errorStatus2').html('No data modified');
			$('#jqueryError2').show();
		}
		return hasErrors;

	}
}
function validateField(fieldId, isMandatory, fieldType, length,isExactLength) {
	var fieldValue = $("#" + fieldId).val();

	var isValid = true;
	if (isMandatory && fieldValue.trim() == "") {
		isValid = false;
	}
	if (fieldType == "streetaddress") {
		var regEx = streetaddress;
		if (isMandatory && fieldValue.trim() == "" || (!regEx.test(fieldValue))) {
			isValid = false;
		}
	}
	return isValid;
	}
