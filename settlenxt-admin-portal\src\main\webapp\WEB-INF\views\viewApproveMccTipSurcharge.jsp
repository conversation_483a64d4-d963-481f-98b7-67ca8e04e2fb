<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<script src="./static/js/validation/viewApproveMccTipSurcharge.js" type="text/javascript"></script>

<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert">
		<span data-i18n="Data"><spring:message
				code="am.lbl.appRejAction" /></span>
	</div>
	<div class="alert alert-danger remarkMust" role="alert">
		<span data-i18n="Data"><spring:message code="sm.lbl.remarkMust" /></span>
	</div>
	<c:url value="approveTipSurcharge" var="approveTipSurcharge" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApproveMccTipSurcharge" modelAttribute="mccTipSurchargeDto"
		action="${approveMccTipSurcharge}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="mccTipSurcharge.viewscreen.title" /></span></strong>
					</div>
					<div class="panel-body">
						<input type="hidden" id="mccTipSurchargeId" value="${mccTipSurchargeDto.mccTipSurchargeId}" />
						<table class="table table-striped infobold" style="font-size: 12px">
							<caption style="display:none;">Mcc Tip Surcharge</caption>
						<thead style="display:none;"><th scope="col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6">
									<div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-info-sign"></span> 
											<span data-i18n="Data"><spring:message code="mccTipSurcharge.requestInformation" /></span></strong>
									</div></td>
								</tr>
								<tr>
									<td><label><spring:message code="mccTipSurcharge.requestType" /><span style="color: red"></span></label></td>
									<td>${mccTipSurchargeDto.lastOperation}</td>
									<td><label><spring:message code="mccTipSurcharge.requestDate" /><span style="color: red"></span></label></td>
									<c:choose>
											<c:when test="${mccTipSurchargeDto.lastUpdatedOn == null}">
												<td>${mccTipSurchargeDto.createdOn}</td>
											</c:when>
											<c:otherwise>
												<td>${mccTipSurchargeDto.lastUpdatedOn}</td>
												</c:otherwise>
										</c:choose>
									<td><label><spring:message code="mccTipSurcharge.requestStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${mccTipSurchargeDto.requestState =='A' }"><spring:message	code="mccTipSurcharge.requestState.approved.description" /></c:if>
										<c:if test="${mccTipSurchargeDto.requestState =='P' }"><spring:message code="mccTipSurcharge.requestState.pendingApproval.description" /></c:if>
										<c:if test="${mccTipSurchargeDto.requestState =='R' }"><spring:message code="mccTipSurcharge.requestState.rejected.description" /></c:if>
										<c:if test="${mccTipSurchargeDto.requestState =='D' }"><spring:message code="mccTipSurcharge.requestState.discared.description" /></c:if>
									</td>
								</tr>
								<tr>
									<td><label><spring:message code="mccTipSurcharge.requestBy" /><span style="color: red"></span></label></td>
									<c:choose>
											<c:when test="${mccTipSurchargeDto.lastUpdatedBy == null}">
												<td>${mccTipSurchargeDto.createdBy}</td>
											</c:when>
											<c:otherwise>
												<td>${mccTipSurchargeDto.lastUpdatedBy}</td>
												</c:otherwise>
										</c:choose>
									<td><label><spring:message code="mccTipSurcharge.approverComments" /><span style="color: red"></span></label></td>
									<td colspan=2>${mccTipSurchargeDto.checkerComments}</td>
									<td></td>
									
								</tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
										<strong><span class="glyphicon glyphicon-credit-card"></span> <span data-i18n="Data">
										<spring:message code="mccTipSurcharge.viewscreen.title" /></span></strong></div>
									</td>
								<tr>
									<td><label><spring:message code="mccTipSurcharge.mccTipSurchargeId" /> <span style="color: red"></span></label></td>
									<td>${mccTipSurchargeDto.mccTipSurchargeId }</td>
									<td><label><spring:message code="mccTipSurcharge.tipSurchargeId" /><span style="color: red"></span></label></td>
									<td >${mccTipSurchargeDto.tipSurchargeLookup }</td>
									<td><label><spring:message code="mccTipSurcharge.mccId" /><span style="color: red"></span></label></td>
									<td >${mccTipSurchargeDto.mccNameLookup }</td>
									
								</tr>
								
								<sec:authorize access="hasAuthority('Approve MCC Tip Surcharge')">
									<c:if test="${mccTipSurchargeDto.requestState eq 'P'}">
									<tr>
										<td colspan="6"><div class="panel-heading-red  clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span>
											<span data-i18n="Data">
											<spring:message code="mccTipSurcharge.approvalPanel.title" /></span></strong></div>
										</td>
									</tr>
									<tr>
										<td><label><spring:message
											code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td>
												<select name="select" id="apprej" onchange="display()">
													<option value="N"><spring:message code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message code="AM.lbl.reject" /></option>
												</select>
											</td>
											<td>
												<div style="text-align:center">
												<label><spring:message code="AM.lbl.remarks" /><span style="color: red">*</span></label>
												</div>
											</td>
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div>
											</td>
										</tr>
									</c:if>
								</sec:authorize>
							</tbody>
						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve MCC Tip Surcharge')">
										<c:if test="${mccTipSurchargeDto.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approvecard" value="Submit"
												onclick="postAction('/approveMccTipSurcharge');" />
										</c:if>
									</sec:authorize>
													
									<sec:authorize access="hasAuthority('Edit MCC Tip Surcharge')">				
									<c:if test="${mccTipSurchargeDto.requestState  eq 'R' and not empty showbutton}">	
										<input name="discardButton" type="button" class="btn btn-danger"
											id="approveRole" value="Discard"
											onclick="userAction('/discardMccTipSurcharge','${mccTipSurchargeDto.mccTipSurchargeId}');" />
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="edit('/editMccTipSurcharge','${mccTipSurchargeDto.mccTipSurchargeId}','pendingApprove');"/>
									</c:if>
									</sec:authorize>
									
										<button type="button" class="btn btn-danger"
										onclick="backAction('P','/mccTipSurchargeForApproval');">
										<spring:message code="mccTipSurcharge.backBtn" /></button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

