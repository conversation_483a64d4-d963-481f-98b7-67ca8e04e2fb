	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});

function display() {
    $(".appRejMust").hide();

}

function userAction(action, funcCodeId) {
	
	var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
	var data = "funcCodeId," + funcCodeId + ",_vTransactToken,"
			+ tokenValue;
	postData(action, data);
}



function backAction(_type, action) {
	var tokenValue = "lIpLsRjLtLDPSzoS2xPf9WXiF/M=";
	var data = "_vTransactToken," + tokenValue + ",status,"
			+ status;
	postData(action, data);
}

 function postAction(_action) {
	var data="";
	var url ="";
	var funcCodeId;
	var remarks="";
		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 funcCodeId = $("#funcCodeId").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveFunctionCode';
				 data = "funcCodeId," + funcCodeId + ",status," + "A" + ",_vTransactToken," + tokenValue + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				 funcCodeId = $("#funcCodeId").val(); 
				 remarks=$("#rejectReason").val();
		
				 url = '/approveFunctionCode';
				 data = "funcCodeId," + funcCodeId + ",status," + "R" + ",_vTransactToken," + tokenValue + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
	
			 
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		