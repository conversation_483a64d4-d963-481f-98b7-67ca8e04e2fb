$(document).ready(function () {
	userIds=[];
    
    $("#tabnew").DataTable({

        initComplete: function () {
            var api = this.api();

            // For each column
            api
                .columns()
                .eq(0)
                .each(function (colIdx) {
                 	//If first column to be skipped to include the filter for the reasons line check box 
                    if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
                    // Set the header cell to contain the input element
                    var cell = $('#tabnew thead tr th').eq(
                        $(api.column(colIdx).header()).index()
                    );
                    var title = $(cell).text();
                    handleInput(colIdx, cell, title, api);
                    }
                });
            $('#tabnew_filter').hide();
            
        },
      // Disabled ordering for first column in case
        columnDefs: [
          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
         ],
        order: [],
        dom: 'lBfrtip', 
        buttons: [ 
            {
                extend: 'excelHtml5',
                text: 'Export',
                filename: 'Rebate Configuration',
                header: 'false',
                title: null,
                sheetName: 'Rebate Configuration',
                className: 'defaultexport'
                /*,
                exportOptions: {
                    columns: 'th:not(:last-child)'
                }*/
            },
                      {
                        extend: 'csvHtml5',
                        text: 'Export',
                        filename: 'Rebate Configuration' ,
						header:'false', 
						title: null,
						sheetName:'Rebate Configuration',
						className:'defaultexport'
						/*,
						exportOptions: {
				            columns: 'th:not(:last-child)'
				         }*/
                    }	
        ],

        searching: true,
        info: true,
        lengthChange: true,
        bLengthChange: true
    });

    $("#excelExport").on("click", function () {
        $(".buttons-excel").trigger("click");
    });
 	     $("#csvExport").on("click", function () {
	        $(".buttons-csv").trigger("click");
	    });
 
     $("#clearFilters").on("click", function () {
       $(".search-box").each(function() {
			   $(this).val("");
			     $(this).trigger("change");
			});
    });

     
     
     $("#selectAll").click(function(){
	 		
		 $('#jqueryError4').hide();
		 $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
		 
		 var referenceNoList = document.getElementById("newsIds");
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
        
		 if(userIds.length>0){
	referenceNoList.innerHTML = userIds.length+"     "+"records are selected";
	
     		if( $('#selectAll').is(':checked') )
		   {
			  
			  $("#toggleModalNews").modal('show');	
		  }
		 else
	     {
			 $("#toggleModalNews").modal('hide');
			 
			 }}else{
                handleModalShow(referenceNoList);}
            });
     
     
  // Disabling SelectAll option diabling
   	if(referenceNoListPendings.length==0)
   	{
  		if (typeof selectAll != "undefined") {
  			document.getElementById("selectAll").disabled = true;
  			document.getElementById("submitButtonA").disabled = true;
 			document.getElementById("submitButtonR").disabled = true;
  		}
   	}
  	// Disabling SelectAll option diabling
     
   
 
	if (typeof vNewCardCount2 != "undefined") {
	    $("#errvCardType").hide();
	    $("#errVFinancialYear").hide();
	    $("#errVFeatureOrBaseFee").hide();
	    $("#errVOperatorIndi").hide();
	    $("#errvNewCardCount1").hide();
	    $("#errvNewCardCount2").hide();
	    $("#errvRebatePercentage").hide();
			disableToValueLoading();
	} 
});

function handleModalShow(referenceNoList) {
	var i = 0;
	var userId2 = [];
	$("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
		userId2.push(this.value);
		i++;
	});
	if (userId2.length > 0) {


		if (referenceNoListPendings.length > 0) {
			referenceNoList.innerHTML = referenceNoListPendings.length + "     " + "records are selected";

			if ($('#selectAll').is(':checked')) {
				$("#toggleModalNews").modal('show');

			}
			else {
				$("#toggleModalNews").modal('hide');

			}
		}
	}
}

function handleInput(colIdx, cell, title, api) {
	var cursorPosition = null;
	if (colIdx < actionColumnIndex) {
		$(cell).html(title + '<br><input class="search-box"   type="text" />');

		// On every keypress in this input
		$(
			'input',
			$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
		)
			.off('keyup change')
			.on('change', function (_e) {
				// Get the search value
				$(this).attr('title', $(this).val());
				var regexr = '({search})';

				cursorPosition = this.selectionStart;
				// Search the column for that value
				api
					.column(colIdx)
					.search(
						this.value != ''
							? regexr.replace('{search}', '(((' + this.value + ')))')
							: '',
						this.value != '',
						this.value == ''
					)
					.draw();
				userIds = [];
				if (this.value != '') {
					var i = 0;
					$("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function () {
						userIds.push(this.value);
						i++;
					});
				}
				else {
					userIds = [];
				}
			})
			.on('click', function (e) {
				e.stopPropagation();
			})
			.on('keyup', function (e) {
				e.stopPropagation();

				$(this).trigger('change');
				if (cursorPosition && cursorPosition != null) {
					$(this)
						.focus()[0]
						.setSelectionRange(cursorPosition, cursorPosition);
				}
			});
	} else {
		$(cell).html(title + '<br> &nbsp;');
	}
	
}

function disableToValueLoading()
{

	if (typeof vNewCardCount2 != "undefined") {
		$('#vNewCardCount2').attr('disabled', true);
		if (document.getElementById("VOperatorIndi").value == "Between") {
		$('#vNewCardCount2').attr('disabled', false);
			}
	}

}
function resetAction() {
document.getElementById("addEditRebate").reset();
$("#errvCardType").find('.error').html('');
$("#errVFinancialYear").find('.error').html('');
$("#errVFeatureOrBaseFee").find('.error').html('');
$("#errVOperatorIndi").find('.error').html('');
$("#errvNewCardCount1").find('.error').html('');
$("#errvNewCardCount2").find('.error').html('');
$("#errvRebatePercentage").find('.error').html('');
$("#errSelectedCard").find('.error').html('');
}

function viewRebate(rebateID, type,originPage) {
var url;
	if (type == 'V')
		url = '/getRebate';
	else if (type == 'E')
		url = '/getRebate';
	else if (type == 'P')
		url = '/getPendingRebate';
	 var data = "rid," + rebateID + ",viewType," + type + ",originPage," + originPage ;
	
	postData(url, data);
}
function viewRebateGrid(rebateID, type,approvedlist,originPage) {
var url;
	if(approvedlist=='Y')
		url = '/getRebate';
	else if (approvedlist == 'N')
	{
		if(type=='R')
		{
			url = '/getRebate';
		}
		else
		{
			url = '/getPendingRebate';
		}
	}
	
	
	var data = "rid," + rebateID + ",viewType,V,originPage," + originPage ;
	
	postData(url, data);
}
function disableToValue()
{
$('#vNewCardCount2').attr('disabled', true);
document.getElementById("vNewCardCount2").value =0;

if (document.getElementById("VOperatorIndi").value == "Between") {
$('#vNewCardCount2').attr('disabled', false);

	}
	
}

function viewRebateAdd(_userID, _type) {

	
	var isValid = true;
    isValid = checkUsingValidateField(isValid);
	if (document.getElementById("VOperatorIndi").value == "Between") {
	    if (!validateField('vNewCardCount2', true, "Integer", 100, false,1,1999999999,true) && isValid) {
	        isValid = false;
	    }
	}
    if (!validateField('vRebatePercentage', true, "Decimal", 100, false,0,100,true) && isValid) {
        isValid = false;
    }
	var arr = document.getElementsByClassName("selectedRoles");
	var cardVariants = "";
	
	var i = 0;
		 for (i of arr){
		  cardVariants = cardVariants + i.id.replace('remove', '') + "|"
				+ $('#' + i.id).attr('value') + "|";
		 }
	
	cardVariants = cardVariants.substring(0, cardVariants.length - 1);
	if (cardVariants.length == 0) {
    	if(rebateValidationMessages["SelectedCard"]){
    		$("#errSelectedCard" ).find('.error').html(rebateValidationMessages["SelectedCard"]);
    	}
        $("#errSelectedCard").show();
        isValid = false;
	}
	else
	{
        $("#errSelectedCard" ).hide();
	}	
	

	if (!isValid) {
		return false;
	}

	checkDuplicateData();
	
}
function checkUsingValidateField(isValid) {
	if (!validateField('vCardType', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}
	if (!validateField('VFinancialYear', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}
	if (!validateField('VFeatureOrBaseFee', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}
	if (!validateField('VOperatorIndi', true, "SelectionBox", 100, false, 0, 0, false) && isValid) {
		isValid = false;
	}
	if (!validateField('vNewCardCount1', true, "Integer", 100, false, 1, 1999999999, true) && isValid) {
		isValid = false;
	}
	return isValid;
}

function homeRebate(_userID, _type,originPage) {
var url;
	if(originPage=='mainPage')
	{
		url = '/showRebateList';
	}
	else
	{
		url = '/rebatePendingForApproval';
	}	
	var data = "originPage," + originPage ;
	postData(url, data);
}


function submitForm(url,_userType,originPage) {
	var data = "userType," + "P" + ",originPage," + originPage ;
	postData(url, data);
}



var optionFunctionalityList = new Array();

function removeTag(id, arg1) {
	
	var moduleId = "1";

	var elem = document.getElementById("row" + id);
	var roleName = "'" + arg1 + "'";

	if (typeof (elem) != 'undefined' && elem != null) {
		var oldmoduleId = "1";
		if (oldmoduleId == moduleId) {
			$('#optionList')
					.append(
							'<tr id="option'
									+ id
									+ '"><td>'
									+ arg1
									+ '</td><td><i class="glyphicon glyphicon-circle-arrow-right" onclick="addToAssignedList('
									+ id + ',' + roleName + ')"></td></tr>');
		}
		$('.dataTables_empty').remove();
		$('#remove' + id).remove();

		$('#changeFlag').val('true');
		
	} else {
		$('#changeFlag').val('true');
		
		$('.dataTables_empty').remove();
		$('#remove' + id).remove();
	}

}
function addToAssignedList(id, arg1) {

	var moduleId = 1;
	var roleName = "'" + arg1 + "'";
	
	
	if ($('#tabnew1 > tbody > tr:nth-child(1)').text() == "No data available in table") {
		$('#tabnew1 > tbody > tr:nth-child(1)').remove();
	}
	$('#assignedList')
			.append(
					'<tr class="selectedRoles" value="'
							+ arg1
							+ '" id="remove'
							+ id
							+ '"><td >'
							+ arg1
							+ '</td><td><i class="glyphicon glyphicon-remove-circle" style="color: blue" onclick="removeTag('
							+ id + ',' + roleName
							+ ')" ></i><input type="hidden" id="row' + id
							+ '" value="' + moduleId + '" ></td></tr>');
	
	$('.dataTables_empty').remove();
	$('#changeFlag').val('true');
	$('#option' + id).remove();
}

function checkDuplicateData() {

var url;
	var arr = document.getElementsByClassName("selectedRoles");
	
	var cardVariants = "";
	
	var i = 0;
		 for (i of arr){
		  cardVariants = cardVariants + i.id.replace('remove', '') + "|";
		 }
	
	
	cardVariants = cardVariants.substring(0, cardVariants.length - 1);
	var cardType=document.getElementById("vCardType").value;
	var financialYear=document.getElementById("VFinancialYear").value;
	var featureOrBaseFee=document.getElementById("VFeatureOrBaseFee").value;
	var operatorIndi=document.getElementById("VOperatorIndi").value;
	var newCardCount1=document.getElementById("vNewCardCount1").value;
	var newCardCount2=document.getElementById("vNewCardCount2").value;
	if(newCardCount2=="")
	{
		newCardCount2="0";
	}
	var rebatePercentage=document.getElementById("vRebatePercentage").value;
	var newRecord="Y";
	
	
	var rebateID="0";
	var msUrl = "checkDuplicateData";


		$.ajax({
			url: msUrl,
			type: "POST",
			dataType: "json",
			data: {
				"cardType": cardType,
				"financialYear": financialYear,
				"featureOrBaseFee": featureOrBaseFee,
				"operatorIndi": operatorIndi,
				"newCardCount1": newCardCount1,
				"newCardCount2": newCardCount2,
				"rebatePercentage": rebatePercentage,
				"cardVariantIds": cardVariants,
				"newRecord":newRecord,
				"rebateID":rebateID
			},
			success: function(response) {
				if (response.status == "BSUC_0001") {
					
					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "";
					url = '/asignRebateToFunctionAdd';
					
					var data = "rebateID,0,cardType," + cardType + 
					",financialYear," + financialYear + ",featureOrBaseFee," + featureOrBaseFee + 
					",operatorIndi," + operatorIndi + 
					",cardVariantIds," + cardVariants + 
					",newCardCount1," + newCardCount1 + ",newCardCount2," + newCardCount2 + ",rebatePercentage," + rebatePercentage ;
					postData(url, data);
				} else {
					
 					errvErrorInfo.className = 'error';
					errvErrorInfo.innerHTML = "Rebate Configuration Already Exists";
				}
			},
			error: function(_request, _status, _error) {
				errvErrorInfo.className = 'error';
				errvErrorInfo.innerHTML = "";
			}
		});
	
}

function validateField(fieldId, isMandatory, fieldType, length, isExactLength, minNumber, maxNumber ,isRange)  {
    var fieldValue = $("#" + fieldId).val();
    var isValid = true;
    if ((isMandatory && fieldValue.trim() == "" && (fieldType!="SelectionBox")) || (isMandatory && fieldValue.trim() == "SELECT" && fieldType=="SelectionBox")) {
        isValid = false;
    }
    var regEx;
	({ regEx, isValid } = handleFieldTypeIntAlpha(fieldType, fieldValue, isValid));
    if (fieldType == "Decimal") {
         regEx = /^\d+\.?\d*$/;
        if (!regEx.test(fieldValue)) {
            isValid = false;
        }
    }
    if (isExactLength && fieldValue.length != length) {
        isValid = false;
    }
  	if (isRange && !(Number(fieldValue)>=Number(minNumber) && Number(fieldValue)<=Number(maxNumber))) {
        isValid = false;
    }
    if(fieldId=="vNewCardCount2")
    {
	    var fieldValue1 = $("#vNewCardCount1").val();
	    if(Number(fieldValue1) > Number(fieldValue))
	    {
        	isValid = false;
    	}
    }
    showErrorMsg(isValid, fieldId);
    return isValid;
}


function showErrorMsg(isValid, fieldId) {
	if (isValid) {
		$("#err" + fieldId).hide();
	} else {
		if (rebateValidationMessages[fieldId]) {
			$("#err" + fieldId).find('.error').html(rebateValidationMessages[fieldId]);
		}
		$("#err" + fieldId).show();
	}
}

function handleFieldTypeIntAlpha(fieldType, fieldValue, isValid) {
	if (fieldType == "Number" && isNaN(fieldValue)) {
		isValid = false;
	}
	var regEx;
	if (fieldType == "Alphabet") {
		regEx = /^[A-Z]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphabetWithSpace") {
		regEx = /^[A-Z ]+$/i;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "AlphanumericNoSpace") {
		regEx = /^[A-Za-z0-9]+$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	if (fieldType == "Integer") {
		regEx = /^\d*$/;
		if (!regEx.test(fieldValue)) {
			isValid = false;
		}
	}
	return { regEx, isValid };
}

function mySelect(){
$('#jqueryError4').hide();
	 var array = [];

	 $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	 
	  var referenceNoList = document.getElementById("newsIds");
   
	 if(array.length==referenceNoListPendings.length){
		 $('#selectAll').prop('checked', true);
		
		   referenceNoList.innerHTML = referenceNoListPendings.length+"     "+"records are selected";
			 $("#toggleModalNews").modal('show');
	 }
	 else{
		 $("#toggleModalNews").modal('hide');
		 
	 }
	
}

function ApproveorRejectBulkRebate(type,action){
	
	 var url = '/approveRebateBulk';
	
	 var array = [];

	 if(action=='No'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });}
	 else if(action=='All'){
		 array=referenceNoListPendings; 
	 }
	    if(userIds.length>0){
			  
		  $('#jqueryError4').hide();
		let referenceIdIdList = "";
		
		let i = 0;
		 for (i of userIds){
		  referenceIdIdList = referenceIdIdList + i + "|";
		 }
		
		let data;
	if(type=='A'){
		 data =  "status,"+"A"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	else if(type=='R'){
		 data = "status,"+"R"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
	}
	
	postData(url, data);
	}
	    else if(array.length>0){
			  $('#jqueryError4').hide();
			let referenceIdIdList = "";
			let i=0;
			for(i of array){  
		        referenceIdIdList = referenceIdIdList + i + "|" ;  
		     } 
			let data = "";
			if(type=='A'){
			 data =  "status,"+"A"+",bulkApprovalReferenceNoList,"+referenceIdIdList;
			}
			else if(type=='R'){
			 data = "status,"+"R"+ ",bulkApprovalReferenceNoList,"+referenceIdIdList;
			}
		postData(url, data);
		}
	else{
			  
			  $('#errorStatus4').html('Please Select  Atleast One Record');
				$('#jqueryError4').show();
		  }
}


function deselectAll() {
$('#jqueryError4').hide();
	$('#selectAll').prop('checked', false);
		 var ele=document.getElementsByName('type');  
  
 var i = 0;
		 for (i of ele){
		  if(i.type=='checkbox')  
         i.checked=false;  
		 }
}
