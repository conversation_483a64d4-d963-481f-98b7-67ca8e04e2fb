$(document).ready(
	function() {

		var cursorPosition = null;
		$("#tabnew").DataTable({

			initComplete: function() {
				var api = this.api();

				// For each column
				api
					.columns()
					.eq(0)
					.each(function(colIdx) {
						//If first column to be skipped to include the filter for the reasons line check box 

						// Set the header cell to contain the input element
						var cell = $('#tabnew thead tr th').eq(
							$(api.column(colIdx).header()).index()
						);
						var title = $(cell).text();
						if (colIdx < actionColumnIndex) {
							$(cell).html(title + '<br><input class="search-box"   type="text" />');

							// On every keypress in this input
							$(
								'input',
								$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
							)
								.off('keyup change')
								.on('change', function(_e) {
									// Get the search value
									$(this).attr('title', $(this).val());
									var regexr = '({search})'; 

									cursorPosition = this.selectionStart;
									// Search the column for that value
									api
										.column(colIdx)
										.search(
											this.value != ''
												? regexr.replace('{search}', '(((' + this.value + ')))')
												: '',
											this.value != '',
											this.value == ''
										)
										.draw();
								})
								.on('click', function(e) {
									e.stopPropagation();
								})
								.on('keyup', function(e) {
									e.stopPropagation();

									$(this).trigger('change');
									if (cursorPosition && cursorPosition != null) {
										$(this)
											.focus()[0]
											.setSelectionRange(cursorPosition, cursorPosition);
									}
								});
						} else {
							$(cell).html(title + '<br> &nbsp;');
						}

					});
				$('#tabnew_filter').hide();
				
			},
			// Disabled ordering for first column in case
			/* columnDefs: [
			   { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
			  ],*/
			order: [],
			dom: 'lBfrtip',
			buttons: [
				{
					extend: 'excelHtml5',
					text: 'Export',
					filename: 'IFSC',
					header: 'false',
					title: null,
					sheetName: 'IFSC',
					className: 'defaultexport',
					exportOptions: {
						columns: 'th:not(:last-child)'
					}
				},
				{
					extend: 'csvHtml5',
					text: 'Export',
					filename: 'IFSC',
					header: 'false',
					title: null,
					sheetName: 'IFSC',
					className: 'defaultexport',
					exportOptions: {
						columns: 'th:not(:last-child)'
					}
				}
			],

			searching: true,
			info: true,
			lengthChange: true,
			bLengthChange: true
		});

		$("#excelExport").on("click", function() {
			$(".buttons-excel").trigger("click");
		});
		$("#csvExport").on("click", function() {
			$(".buttons-csv").trigger("click");
		});
		$("#clearFilters").on("click", function() {
			$(".search-box").each(function() {
				$(this).val("");
				$(this).trigger("change");
			});
		});
		

	




		$("#fromDateStr").datepicker({
			minDate: "dateToday",
			changeMonth: true,
			dateFormat: 'yy-mm-dd',
			onClose: function(selectedDate, instance) {
				if (selectedDate != '') { //added this to fix the issue               
					$("#toDateStr").datepicker("option", "minDate", selectedDate);
					var date = $.datepicker.parseDate(instance.settings.dateFormat, selectedDate, instance.settings);
					date.setMonth(date.getMonth() + 1);
					$("#toDateStr").datepicker("option", "minDate", selectedDate);
					$("#toDateStr").datepicker("option", "maxDate", date);
				}
			}
		});
		$("#toDateStr").datepicker({
			minDate: "dateToday",
			changeMonth: true,
			endDate: "today",
			dateFormat: 'yy-mm-dd',
			onClose: function(_selectedDate) {
				//
			}
		});


		$("#searchBtn").click(
			function() {

				var check = false;
				if (!validateFromDate('errFromDate')) {
					check = true;
				}
				if (!validateToDate('errtoDate')) {
					check = true;
				}
				if (!check) {
					if (!validateFromToDate('errFromDate')) {
						check = true;
					}
				}

				if (!check) {
					var fromDateStr = $('#fromDateStr').val();
					var toDateStr = $('#toDateStr').val();
					var reportNetwork = $('#reportNetwork').val();
					var url = "/reportSearch";



					var data = "fromDateStr,"
						+ fromDateStr + ",toDateStr," + toDateStr + ",reportNetworks,"
						+ reportNetwork;
					postData(url, data);

				} else {
					return false;
				}
			});

	});

function validateFromDate(msgID) {
	var errFrom = document.getElementById(msgID);
	var dateString = (document.getElementById("fromDateStr").value).replace(/^\s*|\s*$/g, '');
	if (dateString == "") {
		errFrom.className = 'error';
		errFrom.innerHTML = "Please Enter From Date";
		return false;
	} else {
		errFrom.className = 'error';
		errFrom.innerHTML = "";
	}
	return true;
}
function validateToDate(msgID) {
	var errTo = document.getElementById(msgID);
	var dateString = (document.getElementById("toDateStr").value).replace(/^\s*|\s*$/g, '');
	if (dateString == "") {
		errTo.className = 'error';
		errTo.innerHTML = "Please Enter To Date";
		return false;
	} else {
		errTo.className = 'error';
		errTo.innerHTML = "";
	}
	return true;
}
function validateFromToDate(msgID) {
	var errFrom = document.getElementById(msgID);
	var fromDateStr = $('#fromDateStr').val();
	var toDateStr = $('#toDateStr').val();
	if (fromDateStr != "" && toDateStr != "") {
		if (Date.parse(fromDateStr) > Date.parse(toDateStr)) {
			errFrom.className = 'error';
			errFrom.innerHTML = "From Date cannot be greater than To Date";
			return false;
		}
		return true;
	}
}

function addReport() {

	var data = "";
	postData("/addReportAndSignment", data);
}

function clearFields() {
	$("#fromDateStr").val("");
	$("#toDateStr").val("");
	$("#reportNetwork").val("0");
}