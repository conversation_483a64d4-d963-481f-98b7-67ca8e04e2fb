package org.npci.settlenxt.adminportal.controllers;

import java.util.List;
import java.util.Locale;

import javax.servlet.http.HttpServletRequest;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.FunctionCodeDTO;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.CodeValueService;
import org.npci.settlenxt.adminportal.service.FunctionCodeService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

//@Slf4j
@Controller
public class FunctionCodeController extends BaseController {

	private static final String SHOW_FUNCTION_CODE = "showFunctionCode";
	private static final String VIEW_FUNCTION_CODE = "viewFunctionCode";
	private static final String VIEW_APPROVE_FUNCTION_CODE = "viewApproveFunctionCode";
	private static final String ADD_EDIT_FUNCTION_CODE = "addEditFunctionCode";
	private static final String SHOW_CHECK_BOX = "showCheckBox";
	private static final String FC_MTI_TYPE="FC_MTI_TYPE";
	private static final String FC_PROC_CODE_TYPE="FC_PROC_CODE_TYPE";
	private static final String FC_FUNC_CODE_TYPE="FC_FUNC_CODE_TYPE";
	private static final String FC_FUNC_CODE_DESC_LIST="FC_FUNC_CODE_DESC_LIST";
	private static final String FC_FEE_TYPE_LIST="FC_FEE_TYPE_LIST";
	private static final String FC_FUND_MOVEMENT_FLAG="FC_FUND_MOVEMENT_FLAG";
	private static final String FC_FUND_MOVEMENT_SIDE_LIST="FC_FUND_MOVEMENT_SIDE_LIST";
	private static final String FC_RECALCULATE_FLAG="FC_RECALCULATE_FLAG";
	private static final String FC_TRANSACTION_TYPE_LIST="FC_TRANSACTION_TYPE_LIST";
	private static final String FC_NEXTWORK_TXN_TYPE_LIST="FC_NEXTWORK_TXN_TYPE_LIST";
	private static final String MTI_LIST="mtiList";
	private static final String PROC_CODE_LIST="procCodeList";
	private static final String FUNC_CODE_LIST="funcCodeList";
	private static final String FUNC_CODE_DESC_LIST="funcCodeDescList";
	private static final String FEE_TYPE_LIST="feeTypeList";
	private static final String FUND_MOV_FLAG="fundMovementFlag";
	private static final String FUND_MOV_LIST="fundMovementSideList";
	private static final String RECAL_FLAG="recalculateFlag";
	private static final String TXN_TYPE_LIST="transactionTypeList";
	private static final String NTW_TXN_TYPE_LIST="networkTxnTypeList";
	
	// @SuppressWarnings({ "unchecked", "unused" })
	@Autowired
	private MessageSource messageSource;
	@Autowired
	SessionDTO sessionDTO;
	@Autowired
	private FunctionCodeService functionCodeService;
	@Autowired
	private CodeValueService codeValueService;

	// show Main tab of FunctionCode
	@PostMapping("/functionCodeMain")
	@PreAuthorize("hasAuthority('View Function Code')")
	public String fetchApprovedFunctionCodeList(Model model) {
		try {
			List<FunctionCodeDTO> functionCodeList = functionCodeService.getFunctionCodeList();
			model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.FUNCTION_CODE_LIST, functionCodeList);
			model.addAttribute(CommonConstants.ADD_FUNCTION_CODE, CommonConstants.TRANSACT_YES);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, SHOW_FUNCTION_CODE, ex);
		}
		return getView(model, SHOW_FUNCTION_CODE);
	}

	// show Approval TAB of FunctionCode
	@PostMapping("/functionCodeForApproval")
	@PreAuthorize("hasAuthority('View Function Code')")
	public String functionCodeForApproval(Model model) {
		try {
			List<FunctionCodeDTO> pendingFunctionCodeList = functionCodeService.getPendingFunctionCode();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.FUNCTION_CODE_PENDING_LIST, pendingFunctionCodeList);
			model.addAttribute(CommonConstants.FUNCTION_CODE_APP_PENDING, CommonConstants.TRANSACT_YES);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_FUNCTION_CODE, ex);
		}
		return getView(model, SHOW_FUNCTION_CODE);
	}

	// view FunctionCode in Main tab
	@PostMapping("/getFunctionCode")
	@PreAuthorize("hasAuthority('View Function Code')")
	public String getFunctionCode(@RequestParam("funcCodeId") String funcCodeId, Model model) {
		FunctionCodeDTO functionCodeDto = new FunctionCodeDTO();
		try {
			functionCodeDto = functionCodeService.getFunctionCodeMainInfo(Integer.valueOf(funcCodeId));
			model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodeDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_FUNCTION_CODE, ex);
		}
		return getView(model, VIEW_FUNCTION_CODE);
	}

	// view FunctionCode in Approval tab
	@PostMapping("/getPendingFunctionCode")
	@PreAuthorize("hasAuthority('View Function Code')")
	public String getPendingFunctionCode(@RequestParam("funcCodeId") String funcCodeId, Model model,
			HttpServletRequest request) {
		FunctionCodeDTO functionCodeDto = new FunctionCodeDTO();
		try {
			functionCodeDto = functionCodeService.getFunctionCodeStgInfo(funcCodeId);
			model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodeDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FUNCTION_CODE, ex);
		}
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.DISCARD_FUNCTION_CODE);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_FUNCTION_CODE);
		return getView(model, VIEW_APPROVE_FUNCTION_CODE);
	}

	// add TipSurcharge
	@PostMapping("/functionCodeCreation")
	@PreAuthorize("hasAuthority('Add Function Code')")
	public String functionCodeCreation(Model model) {
		FunctionCodeDTO functionCodeDto = new FunctionCodeDTO();
		fetchDropdownValues(model);
		model.addAttribute(CommonConstants.ADD_FUNCTION_CODE, CommonConstants.ADD_FUNCTION_CODE);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_FUNCTION_CODE);
		model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodeDto);
		return getView(model, ADD_EDIT_FUNCTION_CODE);
	}

	// To save given input
	@PostMapping("/addFunctionCode")
	@PreAuthorize("hasAuthority('Add Function Code')")
	public String addFunctionCode(@ModelAttribute(CommonConstants.FUNCTION_CODE_DTO) FunctionCodeDTO functionCodeDto,
			Model model) {
		functionCodeDto.setAddEditFlag(CommonConstants.ADD_FUNCTION_CODE);
		try {
			fetchDropdownValues(model);
			functionCodeDto = functionCodeService.addEditFunctionCode(functionCodeDto);
			model.addAttribute(CommonConstants.ADD_FUNCTION_CODE, CommonConstants.ADD_FUNCTION_CODE);
		} catch (Exception ex) {
			model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodeDto);
			model.addAttribute(CommonConstants.ADD_FUNCTION_CODE, CommonConstants.ADD_FUNCTION_CODE);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_FUNCTION_CODE);
			return handleErrorCodeAndForward(model, ADD_EDIT_FUNCTION_CODE, ex);
		}
		model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodeDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("functionCode.addSuccess.msg"));
		return getView(model, ADD_EDIT_FUNCTION_CODE);
	}

	// To Edit TipSurcharge
	@PostMapping("/editFunctionCode")
	@PreAuthorize("hasAuthority('Edit Function Code')")
	public String editFunctionCode(@RequestParam("funcCodeId") String funcCodeId, Model model) {
		FunctionCodeDTO functionCodeDto = new FunctionCodeDTO();

		try {
			fetchDropdownValues(model);
			functionCodeDto = functionCodeService.getFunctionCodeForEdit(Integer.valueOf(funcCodeId));
			functionCodeDto.setAddEditFlag(CommonConstants.EDIT_FUNCTION_CODE);
			model.addAttribute(CommonConstants.EDIT_FUNCTION_CODE, CommonConstants.EDIT_FUNCTION_CODE);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.EDIT_FUNCTION_CODE);
			model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodeDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_FUNCTION_CODE, ex);
		}

		model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodeDto);
		return getView(model, ADD_EDIT_FUNCTION_CODE);
	}

	// To save edited input
	@PostMapping("/updateFunctionCode")
	@PreAuthorize("hasAuthority('Edit Function Code')")
	public String updateFunctionCode(@ModelAttribute("functionCodeDto") FunctionCodeDTO functionCodeDto,
			BindingResult result, Model model, HttpServletRequest request, Locale locale) {
		FunctionCodeDTO functionCodelocal;
		try {
			fetchDropdownValues(model);
			functionCodeDto.setAddEditFlag(CommonConstants.EDIT_FUNCTION_CODE);
			functionCodeService.addEditFunctionCode(functionCodeDto);
			functionCodelocal = functionCodeService.getFunctionCodeStg(functionCodeDto.getFuncCodeId());

			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("functionCode.updateSuccess.msg", null, locale));
		} catch (Exception ex) {
			functionCodelocal = functionCodeDto;
			handleErrorCodeAndForward(model, ADD_EDIT_FUNCTION_CODE, ex);
		}
		model.addAttribute(CommonConstants.EDIT_FUNCTION_CODE, CommonConstants.EDIT_FUNCTION_CODE);
		model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodelocal);
		return getView(model, ADD_EDIT_FUNCTION_CODE);
	}

	// For Checker Approval
	@PostMapping("/approveFunctionCode")
	@PreAuthorize("hasAuthority('Approve Function Code')")
	public String approveFunctionCode(@RequestParam("funcCodeId") String funcCodeId,
			@RequestParam("status") String status, @RequestParam("remarks") String remarks, Model model) {
		FunctionCodeDTO functionCodeDto = new FunctionCodeDTO();
		try {
			functionCodeDto = functionCodeService.approveOrRejectFunctionCode(Integer.valueOf(funcCodeId), status,
					remarks);
			model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodeDto);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_FUNCTION_CODE, ex);
		}
		model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodeDto);
		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {

			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("functionCode.approvalSuccess.msg"));
		} else if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("functionCode.rejectionSuccess.msg"));
		}

		return getView(model, VIEW_APPROVE_FUNCTION_CODE);
	}

	// For Checker Approval
	@PostMapping("/approveFunctionCodeBulk")
	@PreAuthorize("hasAuthority('Approve Function Code')")
	public String approveFunctionCodeBulk(
			@RequestParam("bulkApprovalReferenceNoList") String bulkApprovalReferenceNoList,
			@RequestParam("status") String status, Model model) {
		String successStatus = "";
		try {
			String remarks = "";
			if (status.equals(CommonConstants.STATUS_APPROVE)) {
				remarks = CommonConstants.BULK_APPROVE;
			} else if (status.equals(CommonConstants.STATUS_REJECT)) {
				remarks = CommonConstants.BULK_REJECT;
			}
			successStatus = functionCodeService.approveOrRejectFunctionCodeBulk(bulkApprovalReferenceNoList, status,
					remarks);
			List<FunctionCodeDTO> pendingFunctionCodeList = functionCodeService.getPendingFunctionCode();
			if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
				model.addAttribute(SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
			} else {
				model.addAttribute(SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
			}
			model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.FUNCTION_CODE_PENDING_LIST, pendingFunctionCodeList);
			model.addAttribute(CommonConstants.FUNCTION_CODE_APP_PENDING, CommonConstants.TRANSACT_YES);
			if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_APPROVED)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("functionCode.approvalSuccess.msg"));
			} else if (CommonConstants.YES_FLAG.equalsIgnoreCase(successStatus) && status.equalsIgnoreCase(CommonConstants.REQUEST_STATE_REJECTED)) {
				model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("functionCode.rejectionSuccess.msg"));
			}
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_FUNCTION_CODE, ex);
		}
		return getView(model, SHOW_FUNCTION_CODE);
	}

	// For Discard
	@PostMapping("/discardFunctionCode")
	@PreAuthorize("hasAuthority('Edit Function Code')")
	public String discardFunctionCode(@RequestParam("funcCodeId") String funcCodeId, Model model) {
		FunctionCodeDTO functionCodeDto = new FunctionCodeDTO();
		try {
			functionCodeDto = functionCodeService.discardFunctionCode(Integer.valueOf(funcCodeId));
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_FUNCTION_CODE, ex);
		}
		model.addAttribute(CommonConstants.FUNCTION_CODE_DTO, functionCodeDto);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("functionCode.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_FUNCTION_CODE);
	}


	
	// value from LookUp table
	private void fetchDropdownValues(Model model) {
		List<CodeValueDTO> mtiList = codeValueService.getCodeValueData(FC_MTI_TYPE);
		List<CodeValueDTO> procCodeList = codeValueService.getCodeValueData(FC_PROC_CODE_TYPE);
		List<CodeValueDTO> funcCodeList = codeValueService.getCodeValueData(FC_FUNC_CODE_TYPE);
		List<CodeValueDTO> funcCodeDescList = codeValueService.getCodeValueData(FC_FUNC_CODE_DESC_LIST);
		List<CodeValueDTO> feeTypeList = codeValueService.getCodeValueData(FC_FEE_TYPE_LIST);
		List<CodeValueDTO> fundMovementFlag = codeValueService.getCodeValueData(FC_FUND_MOVEMENT_FLAG);
		List<CodeValueDTO> fundMovementSideList = codeValueService.getCodeValueData(FC_FUND_MOVEMENT_SIDE_LIST);
		List<CodeValueDTO> recalculateFlag = codeValueService.getCodeValueData(FC_RECALCULATE_FLAG);
		List<CodeValueDTO> transactionTypeList = codeValueService.getCodeValueData(FC_TRANSACTION_TYPE_LIST);
		List<CodeValueDTO> networkTxnTypeList = codeValueService.getCodeValueData(FC_NEXTWORK_TXN_TYPE_LIST);

		model.addAttribute(MTI_LIST, mtiList);
		model.addAttribute(PROC_CODE_LIST, procCodeList);
		model.addAttribute(FUNC_CODE_LIST, funcCodeList);
		model.addAttribute(FUNC_CODE_DESC_LIST, funcCodeDescList);
		model.addAttribute(FEE_TYPE_LIST, feeTypeList);
		model.addAttribute(FUND_MOV_FLAG, fundMovementFlag);
		model.addAttribute(FUND_MOV_LIST, fundMovementSideList);
		model.addAttribute(RECAL_FLAG, recalculateFlag);
		model.addAttribute(TXN_TYPE_LIST, transactionTypeList);
		model.addAttribute(NTW_TXN_TYPE_LIST, networkTxnTypeList);
	}

}
