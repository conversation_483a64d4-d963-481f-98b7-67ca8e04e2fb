<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>  
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="EN" xmlns="http://www.w3.org/1999/xhtml"
	xmlns:th="https://www.thymeleaf.org"
	xmlns:sec="https://www.thymeleaf.org/thymeleaf-extras-springsecurity3">
<head>
<title><spring:message code="common.application.title" /></title>
<c:url value="/static" var="staticBaseURL" />
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1" />

<meta http-equiv="Cache-control" content="no-cache" />
<meta http-equiv="Cache-control" content="no-store" />
<meta http-equiv="Expires" content="0" />
<meta http-equiv="pragma" content="no-cache" />
<script type="text/javascript" src="${staticBaseURL}/js/jquery-3.6.0.js"></script>
<script type="text/javascript"
	src="${staticBaseURL}/js/custom_js/vTransact.js"></script>
<script type="text/javascript"
	src="${staticBaseURL}/js/formencoder/encoderAlgo.js"></script>
<script type="text/javascript"
	src="${staticBaseURL}/js/formencoder/formEncoder.js"></script>
<link rel="stylesheet" type="text/css"
	href="${staticBaseURL}/css/bootstrap.min.css" />
<link rel="stylesheet" type="text/css"
	href="${staticBaseURL}/css/custom.css" />
<link rel="stylesheet" type="text/css"
	href="${staticBaseURL}/css/login.css" />
<link rel="stylesheet"
	href="./static/css/font-awesome.min.css">
	<style>
.height-min {
	min-height: 473px;
	margin-top: 100px;
}

.npci-logo {
	width: 160px;
	margin: 0 auto;
}

.auth-error-div {
	width: 350px;
	margin: 0 auto;
	align-content: center;
}

.npci-logo img {
	width: 100%;
}

.clickable_marquee {
	cursor: pointer;
}
pre#details{
text-wrap: wrap;
font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
font-size: 14px;
line-height: 1.42857143;
color: #333;
background:none;
border:none;
padding:0;
}
</style>
<!-- 
.container2 {
        display: flex;
        align-items: center;
        justify-content: center
      } -->
<style>

.news {
  animation : slide 20s linear infinite;
  
}

@keyframes slide {

  from {
    transform: translateX(100%);
  }to {
     transform: translateX(0%);
  }
 
}
.blink {
	animation: blinker 1s linear infinite;
	color: red;
	font-family: sans-serif;
}
.container2 {
        display: flex;
      }
@keyframes blinker { 
70% {
	opacity: 0;
}
}
</style>
	<script>
		let isModalShowing = false;
		function setModalData(title, subtitle, summary, details, footerData) {
			var Title = document.getElementById("title");
			Title.innerHTML = title;
			var Subtitle = document.getElementById("subtitle");
			Subtitle.innerHTML = subtitle;
			var Summary = document.getElementById("summary");
			Summary.innerHTML = summary;
			var Details = document.getElementById("details");
			Details.innerHTML = details;
			var FooterData = document.getElementById("footerData");
			FooterData.innerHTML = footerData;
			toggleModal();
		}

		function toggleModal() {
			isModalShowing = !isModalShowing;
			document.getElementById("newsModal").style.display = isModalShowing ? "block"
					: "none";
		}
	</script>
</head>
<body>

	<!-- Modal -->
	<div class="modal" id="newsModal" tabindex="-1" role="dialog"
		aria-labelledby="modal" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header title">
					<h5 class="modal-title" id="exampleModalLabel">News And Alerts</h5>
				</div>
				<div class="modal-body">

					<h6 id="title" style="text-align: center; font-weight: bold;"></h6>
					<h6 id="subtitle" style="text-align: center"></h6>

					<div>
						<label style="color: blue; font-weight: bold;">Summary</label>
						<p id="summary" />
					</div>
					<br>
						<div>
							<label style="color: blue; font-weight: bold;">Details</label>
							<pre id="details"></pre>
						</div> <br>
							<div>
								<label style="color: blue; font-weight: bold;">Footer</label>
								<p id="footerData" />
							</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-primary" data-dismiss="modal"
						onclick="toggleModal()">Close</button>
				</div>
			</div>
		</div>
	</div>
	<!-- Modal Close-->


	<div class="container-fluid height-min">
		<div class="row">
			<div class="col-sm-12" style="margin-top:-100px;">
			<div class="container2">
				<div class="npci-logo" style="float: left;margin-left:-20px;">
				
				<spring:eval expression="@environment.getProperty('frontend.portal.logo')" var="frontendLogo" />
					
					<img src="${staticBaseURL}/images/${frontendLogo}" class="npci-logo"
						alt="" />	
						
				</div>
				
			
			<div class="col-sm-6" style="font-size: 30px;font-weight: bold;vertical-align: middle;margin-right: 225px;font-family: 'Times New Roman', serif;">
			Bharat Clearing & Settlement 2.0
			</div> 
			
			</div>
			</div>
		</div>

		<c:if test="${not empty logingErroMessage}">
			<div class="auth-error-div alert alert-danger" role="alert">
				${logingErroMessage}</div>
		</c:if>

<c:set var="singlequote" value="'"/>
<c:set var="emptyString" value=""/>



		<!-- <marquee> -->
		<c:forEach var="newsAlerts" items="${newsAlertsList}">
<c:set var="detailsEscapeString"
		value='${fn:escapeXml(fn:replace(newsAlerts.details,singlequote,emptyString))}' />
	<c:set var="titleEscapeString"
		value='${fn:escapeXml(fn:replace(newsAlerts.title,singlequote,emptyString))}' />
	<c:set var="subTitleEscapeString"
		value='${fn:escapeXml(fn:replace(newsAlerts.subTitle,singlequote,emptyString))}' />
	<c:set var="summaryEscapeString"
		value='${fn:escapeXml(fn:replace(newsAlerts.summary,singlequote,emptyString))}' />
	<c:set var="footerEscapeString"
		value='${fn:escapeXml(fn:replace(newsAlerts.footerData,singlequote,emptyString))}' />

			<c:if test="${newsAlerts.isType eq 'Alerts' }">
				<c:if test="${newsAlerts.critical eq 'Y' }">


					<div class="blink" style="text-align: center" style="margin-top: 3px;">
						<span><em class="fa fa-circle"
							style="color: red; font-weight: bold;"></em></span>
						<c:choose>
							<c:when test="${newsAlerts.oldOrNewFlag eq 'Y' }">
								<a class="clickable_marquee" data-toggle="modal"
									data-target="#toggleModalHeader"
									onclick="setModalData(`${titleEscapeString}`, `${subTitleEscapeString}`,`${summaryEscapeString}`,
                `${detailsEscapeString}`, `${footerEscapeString}`)"
									style="color: red;">${titleEscapeString}</a>
								&nbsp;
							</c:when>
							<c:otherwise>
								<a class="clickable_marquee" data-toggle="modal"
									data-target="#toggleModalHeader"
									onclick="setModalData(`${titleEscapeString}`, `${subTitleEscapeString}`,`${summaryEscapeString}`,
                `${detailsEscapeString}`, `${footerEscapeString}`)"
									style="color: black;">${titleEscapeString}</a>
								&nbsp;
							</c:otherwise>
						</c:choose>
					</div>



				</c:if>

			</c:if>

		</c:forEach>




		<!-- marquee -->
		<div class="scrolling">
			<c:forEach var="newsAlerts"
				items="${newsAlertsList}">
			<c:set var="detailsEscapeString"
		value='${fn:escapeXml(fn:replace(newsAlerts.details,singlequote,emptyString))}' />
	<c:set var="titleEscapeString"
		value='${fn:escapeXml(fn:replace(newsAlerts.title,singlequote,emptyString))}' />
	<c:set var="subTitleEscapeString"
		value='${fn:escapeXml(fn:replace(newsAlerts.subTitle,singlequote,emptyString))}' />
	<c:set var="summaryEscapeString"
		value='${fn:escapeXml(fn:replace(newsAlerts.summary,singlequote,emptyString))}' />
	<c:set var="footerEscapeString"
		value='${fn:escapeXml(fn:replace(newsAlerts.footerData,singlequote,emptyString))}' />
				<c:if test="${newsAlerts.isType eq 'Alerts' }">

					<c:if test="${newsAlerts.critical ne 'Y' }">
					<div class="news">
						<span><em class="fa fa-circle" style="color: blue;"></em></span>
						<c:choose>
							<c:when test="${newsAlerts.oldOrNewFlag eq 'Y' }">
								<a class="clickable_marquee" data-toggle="modal"
									data-target="#toggleModalHeader"
									onclick="setModalData(`${titleEscapeString}`, `${subTitleEscapeString}`,`${summaryEscapeString}`,
                `${detailsEscapeString}`, `${footerEscapeString}`)"
									style="color: green;">${titleEscapeString}</a>
							</c:when>
							<c:otherwise>
								<a class="clickable_marquee" data-toggle="modal"
									data-target="#toggleModalHeader"
									onclick="setModalData(`${titleEscapeString}`, `${subTitleEscapeString}`,`${summaryEscapeString}`,
                `${detailsEscapeString}`, `${footerEscapeString}`)"
									style="color: black;">${titleEscapeString}</a>
							</c:otherwise>
						</c:choose>
						</div>
					</c:if>

				</c:if>
				<c:if test="${newsAlerts.isType eq 'News' }">
				<div class="news">
				<span><em class="fa fa-circle" style="color: green;"></em></span>
					<c:choose>
						<c:when test="${newsAlerts.oldOrNewFlag eq 'Y' }">					
							<a class="clickable_marquee" data-toggle="modal"
								data-target="#toggleModalHeader"
								onclick="setModalData(`${titleEscapeString}`, `${subTitleEscapeString}`,`${summaryEscapeString}`,
                `${detailsEscapeString}`, `${footerEscapeString}`)"
								style="color: green;">${titleEscapeString}</a>
						</c:when>
						<c:otherwise>
							<a class="clickable_marquee" data-toggle="modal"
								data-target="#toggleModalHeader"
								onclick="setModalData(`${titleEscapeString}`, `${subTitleEscapeString}`,`${summaryEscapeString}`,
                `${detailsEscapeString}`, `${footerEscapeString}`)"
								style="color:black;">${titleEscapeString}</a>
						</c:otherwise>
					</c:choose>
					</div>
				</c:if>
	



				&nbsp;
				&nbsp;
				&nbsp;
				&nbsp;
				&nbsp;
			</c:forEach> 
		</div>


		<jsp:include page="${body}.jsp" />
	</div>
	<div id="footer" class="footer" >
		<jsp:include page="Footer.jsp" />


	</div>
</body>
</html>
