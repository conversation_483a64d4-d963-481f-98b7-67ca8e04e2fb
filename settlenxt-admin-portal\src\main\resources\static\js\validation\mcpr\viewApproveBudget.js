	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	
	}
	function backAction(type, action) {
	var	url = action;
		
	var data = "userType," + type ;
		postData(url, data);
	}
	
	function postAction(_action) {
	var budgetId;
	var crtuser;
	var remarks;
	var url;
	var data;
	if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 budgetId = $("#budgetId").val();
				 crtuser = $("#crtuser").val();
				 remarks=$("#rejectReason").val();
		
				 url = '/approveBudget';
				 data = "budgetId," + budgetId + ",status," + "A" + ",crtuser,"
						+ crtuser + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} 
		else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {

				 budgetId = $("#budgetId").val();
			  crtuser= $("#crtuser").val();
			 remarks = $("#rejectReason").val();
			
			 url = '/approveBudget';
			 data = "budgetId," + budgetId + ",status," + "R"
					+ ",crtuser," + crtuser  + ",remarks," + remarks;
			postData(url, data);
		
			
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
	
			 
		}
		
	else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		
	
	function discardBudget(action) {
		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		var url = action;
		var budgetId = document.getElementById("budgetId").value;
		var data = "budgetId," + budgetId + ",_vTransactToken," + tokenValue ;
		postData(url, data);
		
	}	

	function editBudget(budgetId, type,parentPage) {
	var url;
		if (type == 'V')
			url = '/editBudget';
		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		var data = "budgetId," + budgetId + ",viewType," + type + ",_vTransactToken,"
			+ tokenValue +",parentPage," + parentPage;
		postData(url, data);
	}	

