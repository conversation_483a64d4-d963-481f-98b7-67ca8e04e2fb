<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ page import="java.time.format.DateTimeFormatter "%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>

<script type="text/javascript">
<c:if test="${showMainTab eq 'Y'}">
var actionColumnIndex = 6;
var firstColumnToBeSkippedInFilterAndSort=false;
</c:if>
<c:if test="${showApprovalTab eq 'Y'}">
<c:if test="${showCheckBox eq 'Y'}">
var actionColumnIndex = 12;
var firstColumnToBeSkippedInFilterAndSort=true;
</c:if>
<c:if test="${showCheckBox eq 'N'}">
var actionColumnIndex = 11;
var firstColumnToBeSkippedInFilterAndSort=false;
</c:if>
</c:if>
</script>

<div id="errorStatus2" class="alert alert-danger" role="alert"
	style="display: none"></div>
<div id="errorStatus4" class="alert alert-danger" role="alert"
	style="display: none"></div>

<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>
<script src="./static/js/validation/lookUp.js" type="text/javascript"></script>


<script>
	var LookUpListPendings = [];
	

	<c:if test="${not empty pendingLookUpList}">
	<c:forEach items="${pendingLookUpList}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	
	LookUpListPendings.push(${operator.lookupId});
	
	</c:if>
	</c:forEach>
	</c:if>
	

	</script>
<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />



<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: bottom;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>

<div class="modal fade" id="toggleModal" tabindex="-1" role="dialog"
	aria-labelledby="toggleApproveCappingAmount" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">LookUp</label>
				<p id="lookUp" />
			</div>



			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveOrRejectBulk('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveOrRejectBulk('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>


<body onload="noBack();">
	<div class="container-fluid height-min">

		<div id="body-content">

			<div class="space_block">
				<ul class="nav nav-tabs" role="tablist" id="myTab">
					<c:choose>
						<c:when test="${showActive eq 'Yes'}">
							<li role="presentation" class="active" />
						</c:when>
						<c:otherwise>
							<li role="presentation">
						</c:otherwise>
					</c:choose>
					<a href="#home" onclick="submitForm('/getLookUpList');" role="tab"
						data-toggle="tab"> <span class="glyphicon glyphicon-user">&nbsp;</span>
						<spring:message code="sm.lbl.lkpData" />
					</a>

					</li>


					<c:choose>
						<c:when test="${approvalActive eq 'Yes'}">
							<li role="presentation" class="active">
						</c:when>
						<c:otherwise>
							<li role="presentation">
						</c:otherwise>
					</c:choose>

					<a href="#profile" role="tab"
						onclick="submitForm('/getPendingLookUpList');" data-toggle="tab">
						<span class="glyphicon glyphicon-ok">&nbsp;</span> <spring:message
							code="sm.lbl.approval" />
					</a>

					</li>
				</ul>

				<form:form id="lookUpSearch" name="lookUpSearch">
				</form:form>
				<div class="tab-content">
					<div class="row">
						<div class="col-sm-12">
							<div class="pull_right">
								<sec:authorize access="hasAuthority('Add LookUp')">
									<c:if test="${showActive eq 'Yes'}">
										<a class="btn btn-success pull-right btn_align submitLkp"
											href="#" onclick="submitForm('/addLookUp');"style="margin: -5px 0px 2px 0px;"><em
											class="glyphicon-plus"></em> <spring:message
												code="sm.lbl.addLkp" /></a>
									</c:if>
								</sec:authorize>
							</div>
						</div>
					</div>
					<div role="tabpanel" class="tab-pane active" id="home">
						<div class="row">
							<div class="col-sm-12">
								<button class="btn  pull-right btn_align" id="clearFilters">
									<spring:message code="ifsc.clearFiltersBtn" />
								</button>
								&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
									id="excelExport"><spring:message code="ifsc.exportBtn" />
								</a> &nbsp; <a class="btn btn-success pull-right btn_align" href="#"
									id="csvExport"><spring:message code="ifsc.csvBtn" /> </a>
							</div>
						</div>

						<%-- 	<c:if test="${not empty lookUpList}"> --%>
						<div class="row">
							<div class="col-sm-12">

								<div class="panel panel-default">
									<div class="panel-heading">
										<strong><span class="glyphicon glyphicon-th"></span>
											<span data-i18n="Data"><spring:message
													code="sm.lbl.lkpList" /></span></strong>

										<c:if test="${showApprovalTab eq 'Y'}">
											<sec:authorize access="hasAuthority('Approve LookUp')">


												<input type="button"
													class="btn btn-success pull-right btn_align"
													onclick="ApproveOrRejectBulk('A','No')" id="submitButton"
													value="<spring:message code="am.lbl.Approve" />" />
												<input type="button"
													class="btn btn-success pull-right btn_align"
													onclick="ApproveOrRejectBulk('R','No')" id="submitButton"
													value="<spring:message code="am.lbl.Reject" />" />
											</sec:authorize>
										</c:if>
									</div>
									<c:if test="${not empty lookUpList and showMainTab eq 'Y'}">
										<div class="panel-body">
											<div class="table-responsive">
												<table id="tabnew"
													class="table table-striped table-bordered" style="width:100%;">
													<caption style="display:none;">Look Up</caption>
													<thead>
														<tr>
															<th scope="col"><spring:message code="sm.lbl.lkpType" /></th>
															<th scope="col"><spring:message code="sm.lbl.lkpValue" /></th>
															<th scope="col"><spring:message code="sm.lbl.lkpDescp" /></th>
															<th scope="col"><spring:message code="sm.lbl.lkpStatus" /></th>
															<th scope="col"><spring:message code="sm.lbl.lkpCreatedBy" /></th>

														</tr>
													</thead>
													<tbody>
														<c:forEach var="lookUp" items="${lookUpList}">

															<tr
																onclick="javascript:viewLookUpInfo('${lookUp.lookupId}','/viewLookUp')">

																<td>${lookUp.lkpType}</td>
																<td>${lookUp.lkpValue}</td>
																<td>${lookUp.lkpDesc}</td>
																<td>${lookUp.status =='A' ? 'Active' : 'InActive'}</td>

																<td>${lookUp.createdBy}</td>

															</tr>
														</c:forEach>
													</tbody>
												</table>

											</div>
									</c:if>

									<c:if test="${empty lookUpList and showMainTab eq 'Y'}">
										<div class="table-responsive">
											<table id="tabnew" class="table table-striped table-bordered"
												style="width:100%;">
												<caption style="display:none;">Look Up</caption>
												<thead>
													<tr>
														<th scope="col"><spring:message code="sm.lbl.lkpType" /></th>
														<th scope="col"><spring:message code="sm.lbl.lkpValue" /></th>
														<th scope="col"><spring:message code="sm.lbl.lkpDescp" /></th>
														<th scope="col"><spring:message code="sm.lbl.lkpStatus" /></th>
														<th scope="col"><spring:message code="sm.lbl.lkpCreatedBy" /></th>


													</tr>
												</thead>
												<tbody>
												</tbody>
											</table>
										</div>
									</c:if>

									<div class="row">
										<div class="col-md-12">
											<div class="panel panel-default">

												<div class="panel-body">

													<c:if
														test="${not empty pendingLookUpList and  showApprovalTab eq 'Y'}">
														<div class="table-responsive">
															<table id="tabnew"
																class="table table-striped table-bordered" style="width:100%;">
																<caption style="display:none;">Look Up</caption>
																<thead>
																	<tr>

																		<sec:authorize access="hasAuthority('Approve LookUp')">
																			<th scope="col"><input type=checkbox name='selectAllCheck'
																				id="selectAll" value='Hi' data-toggle="modal"
																				data-target="toggleModal"></input></th>
																		</sec:authorize>


																		<th scope="col"><spring:message code="sm.lbl.lkpType" /></th>
																		<th scope="col"><spring:message code="sm.lbl.lkpValue" /></th>
																		<th scope="col"><spring:message code="sm.lbl.lkpDescp" /></th>
																		<th scope="col"><spring:message code="sm.lbl.requestState" /></th>
																		<th scope="col"><spring:message code="sm.lbl.lkpCreatedBy" /></th>
																		<th scope="col"><spring:message code="sm.lbl.lkpCreatedOn" /></th>
																		<th scope="col"><spring:message
																				code="sm.lbl.lkpLastUpdatedOn" /></th>
																		<th scope="col"><spring:message
																				code="sm.lbl.lkpCheckerComments" /></th>

																	</tr>
																</thead>
																<tbody>
																	<c:forEach var="lookUp" items="${pendingLookUpList}">

																		<c:if test="${lookUp.requestState  eq 'P'}">
																			<tr
																				onclick="javascript:viewLookUpInfo('${lookUp.lookupId}','/viewApprovalLookUp')">
																				<sec:authorize
																					access="hasAuthority('Approve LookUp')">
																					<td onclick=event.stopPropagation()><input
																						type=checkbox name='type' id="selectSingle"
																						value='${lookUp.lookupId}'></input></td>
																				</sec:authorize>
																		</c:if>
																		<c:if test="${ lookUp.requestState  eq 'R'}">
																			<tr
																				onclick="javascript:viewRejLookUpInfo('${lookUp.lookupId}','/getRejLookUp')">
																				<sec:authorize
																					access="hasAuthority('Approve LookUp')">
																					<td><input type=checkbox name='types'
																						style="display: none;" value='${lookUp.lookupId}'></input></td>
																				</sec:authorize>
																		</c:if>


																		<%-- <tr 
														onclick="javascript:viewLookUpInfo('${lookUp.lookupId}','/viewApprovalLookUp')"> --%>

																		<td>${lookUp.lkpType}</td>
																		<td>${lookUp.lkpValue}</td>
																		<td>${lookUp.lkpDesc}</td>
																		<td>${lookUp.requestState =='P' ? 'Pending for Approval' : 'Rejected'}</td>
																		<td>${lookUp.createdBy}</td>

																		<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
																				value="${lookUp.createdOn}" /></td>

																		<td><fmt:formatDate pattern="dd/MM/yyyy HH:mm:ss"
																				value="${lookUp.lastUpdatedOn}" /></td>


																		<td>${lookUp.checkerComments}</td>

																		</tr>
																	</c:forEach>
																</tbody>
															</table>
														</div>
													</c:if>

													<c:if
														test="${empty pendingLookUpList and  showApprovalTab eq 'Y'}">
														<div class="table-responsive">
															<table id="tabnew"
																class="table table-striped table-bordered" style="width:100%;">
																<caption style="display:none;">Look Up</caption>
																<thead>
																	<tr>
																		<th scope="col"><spring:message code="sm.lbl.lkpType" /></th>
																		<th scope="col"><spring:message code="sm.lbl.lkpValue" /></th>
																		<th scope="col"><spring:message code="sm.lbl.lkpDescp" /></th>
																		<th scope="col"><spring:message code="sm.lbl.requestState" /></th>
																		<th scope="col"><spring:message code="sm.lbl.lkpCreatedBy" /></th>
																		<th scope="col"><spring:message code="sm.lbl.lkpStatus" /></th>
																		<th scope="col"><spring:message code="sm.lbl.checkerComments" /></th>



																	</tr>
																</thead>
																<tbody>
																</tbody>
															</table>
														</div>
													</c:if>

												</div>
											</div>

										</div>
									</div>


								</div>
								<div role="tabpanel" class="tab-pane" id="profile"></div>
							</div>
						</div>
					</div>
				</div>
</body>

