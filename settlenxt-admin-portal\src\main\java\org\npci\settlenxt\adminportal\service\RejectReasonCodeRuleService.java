package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.springframework.stereotype.Service;

@Service
public interface RejectReasonCodeRuleService {

	List<RejectReasonCodeDTO> getrejectReasonCodeRuleList();

	void addRejectReasonCodeRule(RejectReasonCodeDTO rejectReasonCodeDTO);

	RejectReasonCodeDTO getRejectReasonCode(int seqId);

	List<RejectReasonCodeDTO> getPendingRejectReasonCodeList();

	RejectReasonCodeDTO getPendingRejectReasonCode(int seqId);

	RejectReasonCodeDTO updateApprovalStatus(int seqId, String status, String remarks);

	RejectReasonCodeDTO updateRejectReasonCodeStg(RejectReasonCodeDTO rejectReasonCodeDTO);

	RejectReasonCodeDTO discardRejectReasonCode(int seqId);

	List<CodeValueDTO> fetchFuncCodeList();

	String updateBulkStgDisputeFee(String rejectReasonCodeList, String status);

	RejectReasonCodeDTO getRejectReasonCodeStg(int seqId);

	List<RejectReasonCodeDTO> getRejectReasonCodeRulemasterList();

}
