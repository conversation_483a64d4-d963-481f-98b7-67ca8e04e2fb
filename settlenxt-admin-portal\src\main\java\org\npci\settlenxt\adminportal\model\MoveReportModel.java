package org.npci.settlenxt.adminportal.model;

import java.util.UUID;

import org.npci.settlenxt.portal.common.model.BaseModel;
import org.osgi.service.component.annotations.Component;

import lombok.Data;
import lombok.ToString;

@Component
@Data
@ToString
public class MoveReportModel extends BaseModel {

	private Integer requestId;
	private String reportNetwork;
	private String reportType;
	private String year;
	private String month;
	private UUID guid;
	private String cycleDate;
	private String cycleNumber;
	private String settlementProductId;
	private String reportName;
	private String fileType;
	private String instanceId;
	private String siteId;
	private String reportStatus;
	private String fileName;
	private String srcPath;
	private String destPath;
	private String status;
	private String regenFlag;

	private String engineCode;
	private String accumCode;
	private String rowCount;
	private String pgpStatus;
	private String parentFileId;
	private String createdTs;
	private String updatedTs;
	private String genStartTs;
	private String genEndTs;
	private String accumStartTs;
	private String accumEndTs;
	private String pullStartTs;
	private String pullEndTs;
	private String participantId;
	private String count;

}
