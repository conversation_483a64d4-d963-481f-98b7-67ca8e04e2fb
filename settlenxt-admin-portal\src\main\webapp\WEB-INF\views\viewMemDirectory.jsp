<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html  lang="EN" xmlns="http://www.w3.org/1999/xhtml">
<head>

<title>MEMBER DIRECTORY</title>
<script>
	function userAction(type, action,binNumber,setBin) {
		
		
		var data = "participantId," +type+ ",binNumber," + binNumber+ ",isParticipant," +  $("#isParticipant").val()+ ",isBin," + $("#isBin").val()+ ",isSetBin," + $("#isSetBin").val()+ ",setBin," + setBin;
		
		postData(action, data);
	}
	
	$(document).ready(function() {
        $('.panel-heading a').click(function() {
            var icon = $(this).find('.glyphicon');
            if (icon.hasClass('glyphicon-plus')) {
                icon.removeClass('glyphicon-plus').addClass('glyphicon-minus');
            } else {
                icon.removeClass('glyphicon-minus').addClass('glyphicon-plus');
            }
        });
    });
</script>

<link href="./static/css/jquery-ui.css" rel="stylesheet" type="text/css" />
<style>
.form-control {
	background-color: white !important;
}

input[type=text] {
	width: 100%;
	padding: 10px 15px;
	margin: 5px 0;
	box-sizing: border-box;
	background-color: white;
}

input.largerChcekbox {
	width: 20px;
	height: 20px
}
</style>
</head>
<div class="container-fluid height-min">

	<div class="row">
		<div role="alert" style="display: none" id="jqueryError2">
			<div id="errorStatus2" class="alert alert-danger" role="alert">${errorStatus}</div>
		</div>
		<div role="alert" style="display: none" id="jquerySuccess">
			<div id="successStatus" class="alert alert-success" role="alert">${successStatus}</div>
		</div>

		<div id="errLvType" class="alert alert-danger" role="alert"
			style="display: none"></div>
	</div>

	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="am.lbl.viewMember" /></span></strong>
		</div>

		<div class="panel-body">

			<form:form onsubmit="removeSpace(this); encodeForm(this);"
				method="POST" id="addEditMember"
				modelAttribute="memberOnBoardingDTO" enctype="multipart/form-data"
				action="${submitMemberDetails}" autocomplete="off">
				<br />
<input type="hidden" id="isParticipant" name="isParticipant"
		value="${isParticipant}" />
		<input type="hidden" id="isSetBin" name="isSetBin"
		value="${isSetBin}" />
		<input type="hidden" id="isBin" name="isBin"
		value="${isBin}" />
				<fieldset disabled="disabled">
				<legend style="display:none;">MEM DIREC</legend>
					<div class="bs-example">


						<div class="panel-group" id="accordion">

							<div class="panel panel-default">
								<div class="panel-heading">
									<h4 class="panel-title">
										<a data-toggle="collapse" data-parent="#accordion"
											href="#collapseOne" id="collapseOneLink"> Member Bank
											Registration <span
											class="glyphicon glyphicon-minus areaShowHde  "></span>
										</a>
									</h4>
								</div>
								<div id="collapseOne"
									class="panel-collapse collapse in panelHideShow">

									<div class="panel-body">
										<div class="">
											<div class="row">
												<div class="col-md-12">
													<div class="card">

														<div class="card-body">


															<table class="table table-striped"
																style="font-size: 12px">
																<caption style="display:none;">MEM DIREC</caption>
																<thead style="display:none;"><th scope="col"></th></thead>
																<tbody>
																	<tr>
																		<td><label><spring:message
																					code="am.lbl.participantIdCode" /></label></td>
																		<td id="member">${memberOnBoardingDTO.participantId }</td>
																		<td><label><spring:message
																					code="am.lbl.memberName" /></label></td>
																		<td id="member">${memberOnBoardingDTO.memberName }</td>
																				<td><label>Settlement Bin</label></td>
																		<td id="member">${memberOnBoardingDTO.settlementBinNumber }</td>
																		<%-- <td><label><spring:message
																					code="am.lbl.memberType" /></label></td>
																		<td id="member">${memberOnBoardingDTO.memberType }</td>
																		
																		<td><label><spring:message
																					code="am.lbl.ifscCode" /></label></td>
																		<td id="member">${memberOnBoardingDTO.ifscCode }</td>
																		<td><label><spring:message
																					code="am.lbl.bankSector" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bankSector }</td> --%>

																	</tr>
<tr>
																
																				<td><label>Bin / Acquirer ID </label></td>
																		<c:choose>
																	<c:when test="${memberOnBoardingDTO.binType eq 'A'}">
																	<td>${memberOnBoardingDTO.acquirerId}</td>
																	</c:when>
																	<c:when test="${memberOnBoardingDTO.binType eq 'I' or memberOnBoardingDTO.binType eq 'T' }">
																		<td>${memberOnBoardingDTO.binNumber}</td>
																	</c:when>
																	<c:otherwise>
																		<td>Null</td>
																	</c:otherwise>
																</c:choose>
																		<td><label>BinType</label></td>
																				<c:choose>
																	<c:when test="${memberOnBoardingDTO.binType eq 'A'}">
																		<td>Acquirer</td>
																	</c:when>
																	<c:when test="${memberOnBoardingDTO.binType eq 'I' or memberOnBoardingDTO.binType eq 'T' }">
																		<td>Issuer</td>
																	</c:when>
																	<c:otherwise>
																		<td>Null</td>
																	</c:otherwise>
																</c:choose>
																		
																<td><label><spring:message
																					code="am.lbl.bnkAdd" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bnkAdd }</td>
																		
																

																	</tr>
																	<%-- <tr>
																		<td><label><spring:message
																					code="am.lbl.bankMasterCode" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bankMasterCode }</td>
																		<td><label><spring:message
																					code="am.lbl.rtgsCode" /></label></td>
																		<td id="member">${memberOnBoardingDTO.rtgsCode }</td>
																	
																		<td><label><spring:message
																					code="am.lbl.savingsAccNumber" /></label></td>
																		<td id="member">${memberOnBoardingDTO.savingsAccNumber }</td>


																	</tr> --%>
																	<%-- <tr>
																		<td><label><spring:message
																					code="am.lbl.currentAccNumber" /></label></td>
																		<td id="member">${memberOnBoardingDTO.currentAccNumber }</td>
																		<td><label><spring:message
																					code="am.lbl.uniqueBnkName" /></label></td>
																		<td id="member">${memberOnBoardingDTO.uniqueBnkName }</td>
																		<td><label><spring:message
																					code="am.lbl.participantIdNFS" /></label></td>
																		<td id="member">${memberOnBoardingDTO.participantIdNFS }</td>
																		<td></td>
																		<td></td>

																	</tr>
 --%>



																	<tr>

																		<td><label><spring:message
																					code="am.lbl.bnkPhone" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bnkPhone }</td>
																		<td><label><spring:message
																					code="am.lbl.bnkPhone2" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bnkPhone2 }</td>
																		<%-- <td><label><spring:message
																					code="am.lbl.bnkMobile" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bnkMobile }</td>
																		<td><label><spring:message
																					code="am.lbl.bnkMobile2" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bnkMobile2 }</td> --%>
																		<td><label><spring:message
																					code="am.lbl.bnkPincode" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bnkPincode }</td>
																		
																	</tr>

																	<tr>
																	
																		<td><label><spring:message
																					code="am.lbl.bnkEmail" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bnkEmail }</td>
																		<td><label><spring:message
																					code="am.lbl.bankState" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bankState }</td>
																		<td><label><spring:message
																					code="am.lbl.bankCountry" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bankCountry }</td>
																		<%-- <td><label><spring:message
																					code="am.lbl.bnkEmail" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bnkEmail2 }</td> --%>
																		
																		


																	</tr>


																	
																	
																		
																		<%-- <td><label><spring:message
																					code="am.lbl.bankCity" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bankCity }</td>
																		<td><label><spring:message
																					code="am.lbl.bnkPincode" /></label></td>
																		<td id="member">${memberOnBoardingDTO.bnkPincode }</td> --%>

																	
																	


																	<%-- <tr>
																		<td><label><spring:message
																					code="am.lbl.gstIn" /></label></td>
																		<td id="member">${memberOnBoardingDTO.gstIn }</td>
																		<td><label><spring:message
																					code="am.lbl.gstAdd" /></label></td>
																		<td id="member">${memberOnBoardingDTO.gstAdd }</td>
																		<td><label><spring:message
																					code="am.lbl.webSite" /></label></td>
																		<td id="member">${memberOnBoardingDTO.webSite }</td>
																		<td></td>
																		<td></td>

																	</tr>

																	<tr>
																		<td><label><spring:message
																					code="am.lbl.gstCntry" /></label></td>
																		<td id="member">${memberOnBoardingDTO.gstCntry }</td>
																		<td><label><spring:message
																					code="am.lbl.gstSt" /></label></td>
																		<td id="member">${memberOnBoardingDTO.gstSt }</td>
																		<td><label><spring:message
																					code="am.lbl.gstCty" /></label></td>
																		<td id="member">${memberOnBoardingDTO.gstCty }</td>
																		<td><label><spring:message
																					code="am.lbl.gstPincode" /></label></td>
																		<td id="member">${memberOnBoardingDTO.gstPincode }</td>

																	</tr>
 --%>

																</tbody>
															</table>

														</div>

													</div>
												</div>

											</div>

										</div>

									</div>
								</div>
							</div>
							<div id="afterSave">
								<div class="panel panel-default">
									<div class="panel-heading">
										<h4 class="panel-title">
											<a data-toggle="collapse" href="#collapseThree"
												id="collapseThreeLink"> Member Contact Information <span
												class="glyphicon glyphicon-plus areaShowHde"></span>
											</a>
										</h4>
									</div>
									<div id="collapseThree"
										class="panel-collapse collapse panelHideShow">

										<div class="panel-body">
											<div class="">
												<div class="row">
													<div class="col-md-12">
														<div class="card">

															<div class="card-body">
															


																<table class="table table-striped"
																	style="font-size: 12px">
																	
																	<caption style="display:none;">MEM DIREC</caption>
																	<thead style="display:none;"><th scope="col"></th></thead>
																	<tbody>
																		<tr>
																			<%-- <td><label><spring:message
																						code="am.lbl.addressType" /></label></td>
																			<td id="member">${memberOnBoardingDTO.addressType }</td> --%>
																			<td><label><spring:message
																						code="am.lbl.contactname" /></label></td>
																			<td id="member">${memberOnBoardingDTO.name }</td>
																			<td><label><spring:message
																						code="am.lbl.cntAdd1" /></label></td>
																			<td id="member">${memberOnBoardingDTO.cntAdd1 }</td>
																			<td><label><spring:message
																						code="am.lbl.contactCountry" /></label></td>
																			<td id="member">${memberOnBoardingDTO.contactCountry }</td>
																			
																		<td></td>
																		<td></td>	

																		</tr>
																		<tr>
																		<td><label><spring:message
																						code="am.lbl.contactState" /></label></td>
																			<td id="member">${memberOnBoardingDTO.contactState }</td>
																			<td><label><spring:message
																						code="am.lbl.contactCity" /></label></td>
																			<td id="member">${memberOnBoardingDTO.contactCity }</td>
																		<td><label><spring:message
																						code="am.lbl.cntPincode" /></label></td>
																			<td id="member">${memberOnBoardingDTO.cntPincode }</td>
																		
																			
																		
<td></td>
																		<td></td>
																		</tr>
																		<tr>
																			<td><label><spring:message
																						code="am.lbl.cntPhone" /></label></td>
																			<td id="member">${memberOnBoardingDTO.cntPhone }</td>
																			<td><label><spring:message
																						code="am.lbl.cntMobile" /></label></td>
																			<td id="member">${memberOnBoardingDTO.cntMobile }</td>
																				
																			<td><label><spring:message
																						code="am.lbl.cntEmail" /></label></td>
																			<td id="member">${memberOnBoardingDTO.cntEmail }</td>
																			
																			<td></td>
																		<td></td>
																			
																			

																		</tr>
																		
																		<tr>
																		<td><label><spring:message
																						code="am.lbl.cntFax" /></label></td>
																			<td id="member">${memberOnBoardingDTO.cntFax }</td>
																			
																		<td><label><spring:message
																						code="am.lbl.cntDesignation" /></label></td>
																			<td id="member">${memberOnBoardingDTO.cntDesignation }</td>
																			<td></td>
																		
																		<td></td>
																		<td></td>
																		<td></td>
																		</tr>

																	</tbody>
																</table>




															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>


							</div>
						</div>
					</div>
		</div>
		<div class="text-center">
			<div class="card" style="padding: 15px;">
				<div class="">

<c:if test="${memberOnBoardingDTO.binType eq 'A'}">
																	<a href="#"
						onclick="userAction('${memberOnBoardingDTO.participantId}','/showMemberDirectory','${memberOnBoardingDTO.acquirerId}','${memberOnBoardingDTO.settlementBinNumber}');"
						class="btn btn-danger">Back</a>
																</c:if>
																<c:if
																	test="${memberOnBoardingDTO.binType eq 'I' or memberOnBoardingDTO.binType eq 'T' }">
																	<a href="#"
						onclick="userAction('${memberOnBoardingDTO.participantId}','/showMemberDirectory','${memberOnBoardingDTO.binNumber}','${memberOnBoardingDTO.settlementBinNumber}');"
						class="btn btn-danger">Back</a>
																</c:if>
					
				</div>


			</div>
		</div>
		</fieldset>
		</form:form>
	</div>


</div>
</div>

</html>