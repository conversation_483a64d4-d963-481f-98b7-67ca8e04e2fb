<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="org.npci.settlenxt.adminportal.repository.DisputeRepository">

	<select id="getDisputeFeeRuleList"
		resultType="org.npci.settlenxt.adminportal.dto.DisputeFeeRuleDTO">

		SELECT action_code as actionCode,fee_type as
		feeType,priority as priority,
		fee_code as feeCode,field_name as
		fieldName,relational_operator as
		relationalOperator,
		field_value as
		fieldValue,last_updated_by as lastUpdatedBy, last_updated_on as
		lastUpdatedOn, created_by as createdBy, created_on as createdOn,
		status as status, request_state as requestState, last_operation as
		lastOperation, checker_comments as checkerComments, seq_id as seqId,logical_operator as logicalOperator
		from disputefee_rules_stg where
		disputefee_rules_stg.request_state = 'A';

	</select>

	<select id="fetchDisputeFeeRuleUsingLogicalFeeCode"
		resultType="org.npci.settlenxt.adminportal.dto.DisputeFeeRuleDTO">

		SELECT action_code as actionCode,fee_type as
		feeType,priority as priority,
		fee_code as feeCode,field_name as
		fieldName,relational_operator as
		relationalOperator,
		field_value as
		fieldValue,last_updated_by as lastUpdatedBy, last_updated_on as
		lastUpdatedOn, created_by as createdBy, created_on as createdOn,
		status as status, request_state as requestState, last_operation as
		lastOperation, checker_comments as checkerComments, seq_id as seqId,logical_operator as logicalOperartor
		from disputefee_rules_stg where logical_fee_code =
		#{logicalFeeCode};

	</select>

	<select id="getPendingDisputeFeeRuleList"
		resultType="org.npci.settlenxt.adminportal.dto.DisputeFeeRuleDTO">

		SELECT action_code as actionCode,fee_type as
		feeType,priority as priority,
		fee_code as feeCode,field_name as
		fieldName,relational_operator as
		relationalOperator,
		field_value as
		fieldValue,last_updated_by as lastUpdatedBy, last_updated_on as
		lastUpdatedOn, created_by as createdBy, created_on as createdOn,
		status as status, request_state as requestState, last_operation as
		lastOperation, checker_comments as checkerComments, seq_id as seqId,logical_operator as logicalOperator
		from disputefee_rules_stg where
		disputefee_rules_stg.request_state in ('P','R');

	</select>

	<select id="getDisputeFeeRuleInfo"
		resultType="org.npci.settlenxt.adminportal.dto.DisputeFeeRuleDTO">

		SELECT action_code as actionCode,fee_type as
		feeType,priority as
		priority,fee_code as feeCode,field_name as
		fieldName,logical_fee_code
		as logicalFeeCode,
		relational_operator as
		relationalOperator, field_value as fieldValue,field_operator as
		fieldOperator, status as status,last_updated_by as
		lastUpdatedBy,last_updated_on as lastUpdatedOn,created_by as
		createdBy,
		created_on as createdOn,checker_comments as
		checkerComments,last_operation as
		lastOperation,request_state as
		requestState,seq_id as seqId,logical_operator as logicalOperator from
		disputefee_rules_stg WHERE seq_id =
		#{seqId};
	</select>

	<select id="getDisputeFeeRule"
		resultType="org.npci.settlenxt.adminportal.dto.DisputeFeeRuleDTO">

		SELECT action_code as actionCode,fee_type as
		feeType,priority as
		priority,fee_code as feeCode,field_name as
		fieldName,logical_fee_code
		as logicalFeeCode,
		relational_operator as
		relationalOperator, field_value as fieldValue,field_operator as
		fieldOperator,seq_id as seqId from
		disputefee_rules WHERE seq_id =
		#{seqId};
	</select>

	<select id="getActionCodeList"
		resultType="org.npci.settlenxt.portal.common.dto.ActionCodeDTO">
		SELECT action_code as actionCode,CONCAT(action_code,' -
		',action_code_description) as actionCodeDesc from action_code;
	</select>

	<update id="updateDisputeFeeRuleStg">
		UPDATE disputefee_rules_stg
		SET
		action_code=#{actionCode}, fee_type=#{feeType}, priority=#{priority},
		fee_code=#{feeCode}, logical_fee_code=#{logicalFeeCode},
		field_name=#{fieldName}, relational_operator=#{relationalOperator},
		field_value=#{fieldValue}, field_operator=#{fieldOperator}, status =
		#{status}, created_by = #{createdBy} , created_on = #{createdOn}
		,
		last_updated_by = #{lastUpdatedBy} , last_updated_on =
		#{lastUpdatedOn} , request_state =#{requestState},last_operation =
		#{lastOperation} , checker_comments = #{checkerComments}
		WHERE seq_id =
		#{seqId};
	</update>

	<select id="getRelationComplexOpList"
		resultType="java.lang.String">
		SELECT code from lookup where type=#{type};
	</select>

	<select id="fetchSeqId"
		resultType="org.npci.settlenxt.adminportal.dto.DisputeFeeRuleDTO">

		SELECT nextVal('disputefee_rules_stg_seq') as seqId;

	</select>


	<insert id="addDisputeFeeRule">

		INSERT INTO disputefee_rules_stg(
		action_code, fee_type, priority, fee_code, logical_fee_code,
		field_name,
		relational_operator, field_value, field_operator,
		last_updated_by,
		last_updated_on, created_by, created_on, status,
		request_state,
		last_operation,
		checker_comments,seq_id,logical_operator)
		VALUES (#{actionCode},
		#{feeType}, #{priority}, #{feeCode},
		#{logicalFeeCode},#{fieldName},
		#{relationalOperator}, #{fieldValue},
		#{fieldOperator},#{lastUpdatedBy}, #{lastUpdatedOn}, #{createdBy},
		#{createdOn}, #{status}, #{requestState}, #{lastOperation},
		#{checkerComments},#{seqId},#{logicalOperator});



	</insert>

	<insert id="saveDisputeFeeRule">

		INSERT INTO disputefee_rules(
		action_code,
		fee_type, priority, fee_code, logical_fee_code, field_name,
		relational_operator, field_value,
		field_operator,seq_id,logical_operator)
		VALUES (#{actionCode},
		#{feeType}, #{priority}, #{feeCode},
		#{logicalFeeCode},#{fieldName},
		#{relationalOperator}, #{fieldValue},
		#{fieldOperator},#{seqId},#{logicalOperator});



	</insert>

	<update id="updateDisputeFeeRule">
		UPDATE disputefee_rules
		SET
		action_code=#{actionCode}, fee_type=#{feeType}, priority=#{priority},
		fee_code=#{feeCode}, logical_fee_code=#{logicalFeeCode},
		field_name=#{fieldName}, relational_operator=#{relationalOperator},
		field_value=#{fieldValue}, field_operator=#{fieldOperator}
		WHERE seq_id
		= #{seqId};
	</update>

	<delete id="deleteDisputeFeeRule">
		delete from disputefee_rules_stg
		WHERE
		seq_id = #{seqId};
	</delete>

	<select id="getTransitionRulesList"
		resultType="org.npci.settlenxt.adminportal.dto.DisputeTransitionDTO">
		select seqid as id, entity_type as entity, current_state as currState,
		to_state as toState, logical_tostate as logToState,
		field_name as
		fieldName, relational_operator as relOp, field_value as fieldValue,
		field_operator as fieldOperator from dispute_state_transition_rule
		order by
		to_state, current_state
	</select>

	<select id="getTransitionById"
		resultType="org.npci.settlenxt.adminportal.dto.DisputeTransitionDTO">
		select seqid as id, entity_type as entity, current_state as currState,
		to_state as toState, logical_tostate as logToState,
		field_name as
		fieldName, relational_operator as relOp, field_value as fieldValue,
		field_operator as fieldOperator from dispute_state_transition_rule
		where seqid = #{id}
	</select>

	<select id="getPendingTransitionList"
		resultType="org.npci.settlenxt.adminportal.dto.DisputeTransitionDTO">
		select seqid as id, entity_type as entity, current_state as currState,
		to_state as toState, logical_tostate as logToState,
		field_name as
		fieldName, relational_operator as relOp, field_value as fieldValue,created_on as
		createdOn, created_by as createdBy, last_updated_on as lastUpdatedOn,
		last_updated_by as lastUpdatedBy, request_state as requestState,
		checker_comments as checkerComments,
		field_operator as fieldOperator from dispute_state_transition_rule_stg where
		request_state = #{primType} or request_state = #{secType} order by
		to_state, current_state
	</select>

	<insert id="addTransition">
		insert into
		dispute_state_transition_rule_stg(entity_type,current_state,to_state,field_name,relational_operator,field_value,logical_tostate,field_operator,request_state
		,created_by,created_on,last_updated_by,last_updated_on)
		VALUES (#{entity}, #{currState}, #{toState}
		, #{fieldName},#{relOp},#{fieldValue},
		#{logToState},#{fieldOperator},
		#{requestState}, #{createdBy},
		#{createdOn},#{lastUpdatedBy},#{lastUpdatedOn})
	</insert>

	<update id="editTransition">
		update dispute_state_transition_rule_stg set
		field_name=#{fieldName},
		relational_operator=#{relOp},field_value=#{fieldValue},field_operator=#{fieldOperator},
		request_state=#{requestState},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn}
		where seqid=#{id}
	</update>

	<select id="getTransitionRulesInId"
		resultType="org.npci.settlenxt.adminportal.dto.DisputeTransitionDTO">
		select seqid as id, entity_type as entity, current_state as currState,
		to_state as toState, logical_tostate as logToState,
		field_name as
		fieldName, relational_operator as relOp, field_value as fieldValue,
		field_operator as fieldOperator from dispute_state_transition_rule_stg
		where seqid in (${tranID}) ORDER BY seqid ASC
	</select>

	<update id="updateTransitionById">
		update dispute_state_transition_rule_stg set
		request_state=#{requestState},last_updated_by=#{lastUpdatedBy},last_updated_on=#{lastUpdatedOn},
		checker_comments=#{checkerComments}
		where seqid=#{id}
	</update>

	<insert id="addTransitionInMaster">
		insert into
		dispute_state_transition_rule(seqid,entity_type,current_state,to_state,field_name,relational_operator,field_value,logical_tostate,field_operator)
		VALUES (#{id}, #{entity}, #{currState}, #{toState},
		#{fieldName},#{relOp},#{fieldValue},
		#{logToState},#{fieldOperator})
	</insert>

	<select id="getSeqID" resultType="java.lang.Integer">
		select seqid as id from dispute_state_transition_rule ORDER BY seqid ASC
	</select>

	<update id="updateTransRule">
		update dispute_state_transition_rule set
		field_name=#{fieldName}
		, relational_operator=#{relOp} ,
		field_value=#{fieldValue},
		field_operator=#{fieldOperator}
		where
		seqid=#{id}
	</update>

	<delete id="deleteRule">
		delete from dispute_state_transition_rule_stg where seqid = #{id}
	</delete>

</mapper>