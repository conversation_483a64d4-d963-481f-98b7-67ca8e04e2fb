<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

	<script src="./static/js/validation/mcpr/viewApproveBudget.js"
	type="text/javascript"></script>


<div class="container-fluid height-min">

	<div class="alert alert-danger appRejMust" role="alert"><spring:message code="budget.apprejecterrormsg" /></div>
	<div class="alert alert-danger remarkMust" role="alert"><spring:message code="budget.remarkserror" /></div>
	<c:url value="approvebudgetConfig" var="approvebudgetConfig" />
	<form:form onsubmit="removeSpace(this); encodeForm(this);"
		method="POST" id="viewApprovebudgetConfig" modelAttribute="budgetDTO"
		action="${approveBudget}" autocomplete="off">

		<div class="row">
			<div class="col-sm-12">
				<div class="panel panel-default no_margin">
					<div class="panel-heading clearfix">
						<strong><span class="glyphicon glyphicon-th"></span> <span
							data-i18n="Data"><spring:message code="budget.approvalPendingViewScreen.title" /></span></strong>
					</div>

					<div class="panel-body">
						<input type="hidden" id="budgetId" value="${budgetDTO.budgetId}">
						<input type="hidden" id="crtuser" value="${budgetDTO.lastUpdatedBy}" />

						<table class="table table-striped infobold" style="font-size: 12px">
						<caption style="display:none;">Budget</caption>
						<thead style="display:none;"><th scope = "col"></th></thead>
							<tbody>
								<tr>
									<td colspan="6"><div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-info-sign"></span> <span
												data-i18n="Data"><spring:message code="budget.requestInfo" /></span></strong>
										</div></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>
								<tr>


									<td><label><spring:message code="budget.requestType" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.lastOperation}</td>
									<td><label><spring:message code="budget.requestDate" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.lastUpdatedOn}</td>
									<td><label><spring:message code="budget.requestStatus" /><span style="color: red"></span></label></td>
									<td><c:if test="${budgetDTO.requestState=='A' }">Approved</c:if></td>
									<td><c:if test="${budgetDTO.requestState=='P' }">Pending for Approval</c:if></td>
									<td><c:if test="${budgetDTO.requestState=='R' }">Rejected</c:if></td>
									<td><c:if test="${budgetDTO.requestState=='D' }">Discarded</c:if></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td> 
									<td></td>
									<td></td>
									<td></td>
									

								</tr>
								<tr>
								<td><label><spring:message code="budget.requestBy" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.lastUpdatedBy}</td>
									<td><label><spring:message code="budget.approverComments" /><span style="color: red"></span></label></td>
									<td colspan=2>${budgetDTO.checkerComments}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>

								<tr>
									<td colspan="6">
										<div class="panel-heading-red clearfix">
											<strong><span class="glyphicon glyphicon-credit-card"></span> <span
												data-i18n="Data"><spring:message code="budget.viewscreen.title" /></span></strong>
										</div>
									</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>


								<tr>
									<td><label><spring:message code="budget.budgetId" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.budgetId}</td>
									<td><label><spring:message code="budget.vendor" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.vendor}</td>
									<td><label><spring:message code="budget.year" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.displayYear}</td>
									<td><label><spring:message code="budget.budgetName" /><span style="color: red"></span></label></td>
									<td>${budgetDTO.budget}</td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
								</tr>

								
								
								<sec:authorize access="hasAuthority('Approve IFSC')">
									<c:if test="${budgetDTO.requestState eq 'P'}">
										<tr>
											<td colspan="6"><div class="panel-heading-red  clearfix">
													<strong><span class="glyphicon glyphicon-info-sign"></span>
														<span data-i18n="Data"><spring:message
																code="AM.lbl.reqInfo" /></span></strong>
												</div></td>
												<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
										</tr>
										

										<tr>
											<td><label><spring:message
														code="AM.lbl.approveReject" /><span style="color: red">*</span></label></td>
											<td><select name="select" id="apprej"
												onchange="display()">
													<option value="N"><spring:message
															code="AM.lbl.select" /></option>
													<option value="A" id="approve"><spring:message
															code="AM.lbl.approve" /></option>
													<option value="R" id="reject"><spring:message
															code="AM.lbl.reject" /></option>
											</select></td>
											<td>
												<div style="text-align:center">
													<label><spring:message code="AM.lbl.remarks" /><span
														style="color: red">*</span></label>
												</div>
											</td>
											
											<td colspan="2"><textarea rows="4" cols="50"
													maxlength="100" id="rejectReason"></textarea>
												<div id="errorrejectReason" class="error"></div></td>
										<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									<td></td>
									
										</tr>
									</c:if>
							</sec:authorize>	

							</tbody>

						</table>

						<div class="row">
							<div class="col-sm-12 bottom_space">
								<hr />
								<div style="text-align:center">
									<sec:authorize access="hasAuthority('Approve Budget')">
										<c:if test="${budgetDTO.requestState eq 'P'}">
											<input name="button10" type="button" class="btn btn-success"
												id="approveRole" value="Submit"
												onclick="postAction('/approveBudget');" />
										</c:if>
								</sec:authorize>
								
								<sec:authorize access="hasAuthority('Edit Budget')">				
									<c:if test="${budgetDTO.requestState  eq 'R' and not empty showbutton}">
										<input name="editButton" type="button" class="btn btn-success"
											id="approveRole" value="Edit" 
											onclick="javascript:editBudget('${budgetDTO.budgetId}','V','approvalTab');"/>						
									
									<button type="button" class="btn btn-danger"
										onclick="discardBudget('/discardRejectedBudget');">
										<spring:message code="binexcl.discardBtn" /></button>
									</c:if>
									</sec:authorize>


									<button type="button" class="btn btn-danger"
										onclick="backAction('P','/budgetPendingForApproval');">
										<spring:message code="budget.backBtn" /></button>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form:form>
</div>

