package org.npci.settlenxt.adminportal.controllers;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.RejectReasonCodeDTO;
import org.npci.settlenxt.adminportal.service.LookupService;
import org.npci.settlenxt.adminportal.service.ReasonCodeMasterService;
import org.npci.settlenxt.adminportal.service.RejectReasonCodeRuleService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.exception.SettleNxtException;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

@Controller
public class RejectReasonCodeRuleController extends BaseController {

	private static final String SHOW_REJECT_REASONCODE_RULE = "showRejectReasonCodeRule";
	private static final String ADD_EDIT_REJECT_REASON_CODE = "addEditRejectReasonCode";
	private static final String VIEW_REJECT_REASON_CODE = "viewRejectReasonCode";
	private static final String VIEW_APPROVE_REJECT_REASON_CODE = "viewApproveRejectReasonCode";
	private static final String SUB_FIELD_NAME_LIST="subFieldNameList";
	private static final String FIELD_OPR_LIST="fieldOperatorList";
	private static final String FIELD_NAME_LIST="fieldNameList";
	private static final String REL_OPR_LIST="relationOperatorList";
	private static final String REASON_CODE_LIST="reasonCodeList";
	private static final String FUNC_CODE_LIST="funcCodeList";
	private static final String STATUS="status";
	private static final String SUCCESS="Success";

	@Autowired
	ReasonCodeMasterService reasonCodeMasterService;

	@Autowired
	LookupService lookupService;

	@Autowired
	RejectReasonCodeRuleService rejectReasonCodeRuleService;
	
	@Autowired
	private MessageSource messageSource;


	@PostMapping("/showRejectReasonCode")
	public String showRejectReasonCode(Model model) throws SettleNxtException {
		try {
			model.addAttribute(SHOW_REJECT_REASONCODE_RULE, CommonConstants.YES);
			List<RejectReasonCodeDTO> rejectReasonCodeRuleList = rejectReasonCodeRuleService
					.getRejectReasonCodeRulemasterList();
			model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_LIST, rejectReasonCodeRuleList);

		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, SHOW_REJECT_REASONCODE_RULE, ex);
		}

		model.addAttribute(CommonConstants.ADD_REJECT_REASON_CODE_RULE, CommonConstants.TRANSACT_YES);
		return getView(model, SHOW_REJECT_REASONCODE_RULE);
	}

	@PostMapping("/createRejectReasonCodeRule")
	@PreAuthorize("hasAuthority('Add Reject Reason Code')")
	public String createRejectReasonCode(Model model) throws SettleNxtException {
		try {

			model.addAttribute(CommonConstants.ADD_REJECT_REASON_CODE_RULE, CommonConstants.TRANSACT_YES);
			model.addAttribute(CommonConstants.ADD_REJECT_REASON_CODE, CommonConstants.TRANSACT_YES);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_BUDGET);
			RejectReasonCodeDTO rejectReasonCodeDTO = new RejectReasonCodeDTO();
			rejectReasonCodeDTO.setSeqId(0);
			prepareLookupListRejectReasonRule(model, rejectReasonCodeDTO);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, ADD_EDIT_REJECT_REASON_CODE, ex);
		}
		return getView(model, ADD_EDIT_REJECT_REASON_CODE);
	}

	private void prepareLookupListRejectReasonRule(Model model, RejectReasonCodeDTO rejectReasonCodeDTO) {
		List<CodeValueDTO> codeValueDTOList  = rejectReasonCodeRuleService.fetchFuncCodeList();
		List<CodeValueDTO> codeValueDTOList1 = reasonCodeMasterService.fetchReasonCodeList();
		List<CodeValueDTO> codeValueDTOList2 = lookupService.getLookupData(CommonConstants.REL_OPR);
		List<CodeValueDTO> codeValueDTOList3 = lookupService.getLookupData(CommonConstants.FIELD_EX);
		List<CodeValueDTO> codeValueDTOList4 = lookupService.getLookupData(CommonConstants.DISPUTE_FIELD_OP);
		List<CodeValueDTO> codeValueDTOList5 = lookupService.getLookupData(CommonConstants.SUB_FIELD_NAME);

		model.addAttribute(FUNC_CODE_LIST, codeValueDTOList);
		model.addAttribute(REASON_CODE_LIST, codeValueDTOList1);
		model.addAttribute(REL_OPR_LIST, codeValueDTOList2);
		model.addAttribute(FIELD_NAME_LIST, codeValueDTOList3);
		model.addAttribute(FIELD_OPR_LIST, codeValueDTOList4);
		model.addAttribute(SUB_FIELD_NAME_LIST, codeValueDTOList5);
		model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_DTO,rejectReasonCodeDTO );
	}

	@PostMapping("/addRejectReasonCode")
	@PreAuthorize("hasAuthority('Add Reject Reason Code')")
	public String addRejectReasonCode(
			@ModelAttribute("rejectReasonCodeRuleDto") RejectReasonCodeDTO rejectReasonCodeDTO, Model model)
			throws SettleNxtException {

		try {
			model.addAttribute(CommonConstants.ADD_REJECT_REASON_CODE, CommonConstants.ADD_REASON_CODE);
			
			
			rejectReasonCodeRuleService.addRejectReasonCodeRule(rejectReasonCodeDTO);
			RejectReasonCodeDTO rejectReasonCodeDto = new RejectReasonCodeDTO();
			rejectReasonCodeDTO.setSeqId(0);
			prepareLookupListRejectReasonRule(model, rejectReasonCodeDto);
			model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.ADD_BUDGET);
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("rejectReasonCodeRule.addSuccess.msg"));

		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, ADD_EDIT_REJECT_REASON_CODE, ex);
		}

		return getView(model, VIEW_APPROVE_REJECT_REASON_CODE);

	}
	
	@PostMapping("/addAllRejectReasonCode")
	@PreAuthorize("hasAuthority('Add Reject Reason Code')")
	public ResponseEntity<Object> addAllRejectReasonCode(Model model,
			@RequestBody List<RejectReasonCodeDTO> rejectReasonCodeList, HttpServletResponse response) {
		for(RejectReasonCodeDTO rejectReasonCodeDTO:rejectReasonCodeList)
		{
			rejectReasonCodeRuleService.addRejectReasonCodeRule(rejectReasonCodeDTO);
		}
		String result = SUCCESS;
		JsonObject jsonResponse = new JsonObject();
		if (StringUtils.equals(result, SUCCESS)) {
			jsonResponse.addProperty(STATUS, BaseCommonConstants.TRANSACT_SUCCESS);
		} else {
			jsonResponse.addProperty(STATUS, BaseCommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}
	

	@PostMapping("/getRejectReasonCodeRule")
	@PreAuthorize("hasAuthority('view Reject Reason Code')")
	public String getRejectReasonCodeRule(@RequestParam("seqId") int seqId,
			 Model model) throws SettleNxtException {
	
		RejectReasonCodeDTO rejectReasonCodeDTO = rejectReasonCodeRuleService.getRejectReasonCode(seqId);
		model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_DTO, rejectReasonCodeDTO);
		return getView(model, VIEW_REJECT_REASON_CODE);

	}

	@PostMapping("/rejectReasonCodeRulePendingForApproval")
	@PreAuthorize("hasAuthority('view Reject Reason Code')")
	public String rejectReasonCodeRulePendingForApproval(Model model) throws SettleNxtException {

		try {
			List<RejectReasonCodeDTO> rejectReasonCodeRuleList = rejectReasonCodeRuleService.getPendingRejectReasonCodeList();
			Long tranSize = rejectReasonCodeRuleList.stream().filter(rejectReasonCodeDto->rejectReasonCodeDto.getRequestState().equalsIgnoreCase("P")).count();
			model.addAttribute("tranSize",tranSize);
			model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_PENDING_LIST, rejectReasonCodeRuleList);
			model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_APP_PENDING, CommonConstants.YES);
		} catch (Exception ex) {
			handleErrorCodeAndForward(model, SHOW_REJECT_REASONCODE_RULE, ex);
		}
		return getView(model, SHOW_REJECT_REASONCODE_RULE);
	}

	@PostMapping("/getPendingRejectReasonCodeRule")
	@PreAuthorize("hasAuthority('view Reject Reason Code')")
	public String getPendingRejectReasonCodeRule(@RequestParam("seqId") int seqId,
			 Model model) throws SettleNxtException {
		RejectReasonCodeDTO rejectReasonCodeDTO = rejectReasonCodeRuleService.getPendingRejectReasonCode(seqId);
		model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_DTO, rejectReasonCodeDTO);

		return getView(model, VIEW_APPROVE_REJECT_REASON_CODE);

	}

	@PostMapping("/approveRejectReasonCode")
	@PreAuthorize("hasAuthority('Approve Reject Reason Code')")
	public String approveRejectReasonCodeRule(@RequestParam("seqId") int seqId, @RequestParam("status") String status,
			@RequestParam("remarks") String remarks, Model model, HttpServletRequest request) throws SettleNxtException {
		try {

			
			RejectReasonCodeDTO rejectReasonCodeDTO = rejectReasonCodeRuleService.updateApprovalStatus(seqId, status, remarks);
			checkRejectReasonCodeApproveStatus(rejectReasonCodeDTO, model);
			model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_DTO, rejectReasonCodeDTO);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, VIEW_APPROVE_REJECT_REASON_CODE, ex);
		}
		return getView(model, VIEW_APPROVE_REJECT_REASON_CODE);
	}

	private void checkRejectReasonCodeApproveStatus(RejectReasonCodeDTO rejectReasonCodeDTO, Model model) {
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(rejectReasonCodeDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("rejectReasonCodeRule.approvalSuccess.msg"));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("rejectReasonCodeRule.rejectionSuccess.msg"));
		}

	}

	@PostMapping("/editRejectReasonCode")
	@PreAuthorize("hasAuthority('Edit Reject Reason Code')")
	public String editRejectReasonCode(@RequestParam("seqId") int seqId
			, Model model) {
		// RejectReasonCodeDTO rejectReasonCodeDTO = rejectReasonCodeRuleService.
		RejectReasonCodeDTO rejectReasonCodeDTO = new RejectReasonCodeDTO();
		try {
			rejectReasonCodeDTO = rejectReasonCodeRuleService.getPendingRejectReasonCode(seqId);

		} catch (Exception ex) {
			handleErrorCodeAndForward(model, ADD_EDIT_REJECT_REASON_CODE, ex);
		}
		prepareLookupListRejectReasonRule(model,rejectReasonCodeDTO);
		model.addAttribute(CommonConstants.EDIT_REJECT_REASON_CODE_RULE, CommonConstants.TRANSACT_YES);
		model.addAttribute(BaseCommonConstants.SHOW_BUTTON, CommonConstants.TRANSACT_YES);
		
		return getView(model, ADD_EDIT_REJECT_REASON_CODE);
	}

	@PostMapping("/updateRejectReasonCode")
	@PreAuthorize("hasAuthority('Edit Reject Reason Code')")
	public String updateRejectReasonCode(
			@ModelAttribute("rejectReasonCodeRuleDto") RejectReasonCodeDTO rejectReasonCodeDTO, Model model) {
		try {
			rejectReasonCodeDTO = rejectReasonCodeRuleService.updateRejectReasonCodeStg(rejectReasonCodeDTO);
			model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_DTO, rejectReasonCodeDTO);

		} catch (Exception ex) {

			return handleErrorCodeAndForward(model, ADD_EDIT_REJECT_REASON_CODE, ex);
		}

		model.addAttribute(CommonConstants.EDIT_REJECT_REASON_CODE_RULE, CommonConstants.TRANSACT_YES);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("rejectReasonCodeRule.updateSuccess.msg"));
		return getView(model, VIEW_APPROVE_REJECT_REASON_CODE);
	}

	@PostMapping("/discardRejectReasonCode")
	@PreAuthorize("hasAuthority('Edit Reject Reason Code')")
	public String discardRejectReasonCode(@RequestParam("seqId") int seqId, Model model) {
		RejectReasonCodeDTO rejectReasonCodeDTO = new RejectReasonCodeDTO();
		try {
			rejectReasonCodeDTO = rejectReasonCodeRuleService.discardRejectReasonCode(seqId);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_APPROVE_REJECT_REASON_CODE, ex);
		}
		model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_DTO, rejectReasonCodeDTO);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("rejectReasonCodeRule.discardSuccess.msg"));
		return getView(model, VIEW_APPROVE_REJECT_REASON_CODE);
	}



@PostMapping("/bulkApproveRejectResonCodeRule")
@PreAuthorize("hasAuthority('Approve Reject Reason Code')")
public String bulkApproveRejectResonCodeRule(Model model,@RequestParam("status") String status, @RequestParam("rejectReasonCodeList") String rejectReasonCodeList,
		HttpServletResponse response, HttpServletRequest request){
	
	String resultReasonCode = rejectReasonCodeRuleService.updateBulkStgDisputeFee(rejectReasonCodeList, status);

	if (StringUtils.equals(resultReasonCode, CommonConstants.RESULT_SUCCESS) && StringUtils.equals(status, CommonConstants.RECORD_APPROVED)) {
		model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
				messageSource.getMessage("rejectReasonCodeRule.approvalSuccess.msg", null, request.getLocale()));
	} else if (StringUtils.equals(resultReasonCode, CommonConstants.RESULT_SUCCESS)
			&& StringUtils.equals(status, CommonConstants.RECORD_REJECTED)) {
		model.addAttribute(BaseCommonConstants.SUCCESS_STATUS,
				messageSource.getMessage("rejectReasonCodeRule.rejectionSuccess.msg", null, request.getLocale()));
	} else {
		model.addAttribute(BaseCommonConstants.ERROR_STATUS,
				messageSource.getMessage("E00001", null, request.getLocale()));
		model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_PENDING_LIST, null);
		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB,BaseCommonConstants.NO_FLAG);
		
		return getView(model, SHOW_REJECT_REASONCODE_RULE);
	}

		model.addAttribute("pendingRejectReasonCodeRule", CommonConstants.TRANSACT_YES);
		List<RejectReasonCodeDTO> rejectReasonCodeRuleList = rejectReasonCodeRuleService.getPendingRejectReasonCodeList();
		Long tranSize = rejectReasonCodeRuleList.stream().filter(rejectReasonCodeDto->rejectReasonCodeDto.getRequestState().equalsIgnoreCase("P")).count();
		model.addAttribute("tranSize",tranSize);
		model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_PENDING_LIST, rejectReasonCodeRuleList);
		model.addAttribute(CommonConstants.REJECT_REASON_CODE_RULE_APP_PENDING, CommonConstants.YES);

		return getView(model, SHOW_REJECT_REASONCODE_RULE);

}
}
