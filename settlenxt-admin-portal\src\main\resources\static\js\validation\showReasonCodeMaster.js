var entry = {};
var allPages = 0;
var cursorPosition = null;
var reasonCodeRuleIds = [];
$(document).ready(function() {
	$('#transitionErrorMsg').hide();
	$('#transitionErrorMsg1').hide();

	
	/* Initialization of datatables */
	$(document).ready(function () {
    	
		var myTable = $("#tabnew").DataTable({

	        initComplete: function () {
	            var api = this.api();

	            // For each column
	            api
	                .columns()
	                .eq(0)
	                .each(function (colIdx) {
	                //If first column to be skipped to include the filter for the reasons line check box 
	                if(!(colIdx==0 && firstColumnToBeSkippedInFilterAndSort)){
	                    // Set the header cell to contain the input element
	                    var cell = $('#tabnew thead tr th').eq(
	                        $(api.column(colIdx).header()).index()
	                    );
	                    var title = $(cell).text();
	                   searchBoxFunc(colIdx, cell, title, api);
	                   }
	                });
	            $('#tabnew_filter').hide();
	           
	        },
	        // Disabled ordering for first column in case
	        columnDefs: [
	          { orderable: !firstColumnToBeSkippedInFilterAndSort, targets: 0 }
	         ],
	         "order": [],
	        dom: 'lBfrtip', 
	        buttons: [ 
	            {
	                extend: 'excelHtml5',
	                text: 'Export',
	                filename: 'Reason Code Master',
	                header: 'false',
	                title: null,
	                sheetName: 'Reason Code Master',
	                className: 'defaultexport',
	                exportOptions: {
	                    columns: 'th:not(:last-child)'
	                }
	            },
	            {
	                extend: 'csvHtml5',
	                text: 'Export',
	                filename: 'Reason Code Master' ,
					header:'false', 
					title: null,
					sheetName:'Reason Code Master',
					className:'defaultexport',
					exportOptions: {
			            columns: 'th:not(:last-child)'
			         }
	            }

	        ],

	        searching: true,
	        info: true,
	        lengthChange: true,
	        bLengthChange: true,
	    });
		allPages = myTable.rows().nodes();
	});	
	   
	
	
	   
	    $("#excelExport").on("click", function () {
	        $(".buttons-excel").trigger("click");
	    });
	 
		     $("#csvExport").on("click", function () {
		        $(".buttons-csv").trigger("click");
		    });
	 
	     $("#clearFilters").on("click", function () {
	       $(".search-box").each(function() {
				   $(this).val("");
				     $(this).trigger("change");
				});
	    });
	    
	    
	$("#selectAll").click(function(){
			
			$('#jqueryError4').hide();
	        $("input[type=checkbox]").prop('checked', $(this).prop('checked'));
	      
	        var footerDataHeader = document.getElementById("detailsHeadersss");
	       if(reasonCodeRuleIds.length>0){
	        	footerDataHeader.innerHTML = reasonCodeRuleIds.length+"     "+"records are selected";
	        	
	        	if( $('#selectAll').is(':checked') ){
	         $("#toggleModal").modal('show');
	                
	        }
	        else{
	           $("#toggleModal").modal('hide');
	                
	        }}else{

				$('#snxtSuccessMessage').hide();
				if ($(this).hasClass('allChecked')) {
					$('.selectedId', allPages).prop('checked', false);
				} else {
					$('.selectedId', allPages).prop('checked', true);
				}

				$(this).toggleClass('allChecked');

				var footer = document.getElementById("detailsHeadersss");
				
				var temp = document.getElementById("allPendingSize").value;
				footer.innerHTML = $('<div>').text(temp).html() + "     " + "records are selected";
				
				
				showAndHideModel();
	        }
	});
});


function searchBoxFunc(colIdx, cell, title, api) {
    if (colIdx < actionColumnIndex) {

        $(cell).html(title + '<br><input class="search-box"   type="text" />');

        // On every keypress in this input
        $(
            'input',
            $('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
        )
            .off('keyup change')
            .on('change', function(_e) {
                // Get the search value
                $(this).attr('title', $(this).val());
                var regexr = '({search})';

                cursorPosition = this.selectionStart;
                // Search the column for that value
                api
                    .column(colIdx)
                    .search(
                        this.value != ''
                            ? regexr.replace('{search}', '(((' + this.value + ')))')
                            : '',
                        this.value != '',
                        this.value == ''
                    )

                    .draw();
                reasonCodeRuleIds = [];
                if (this.value != '') {
                    var i = 0;
                    $("#tabnew tr").find("input" + "[type=" + "checkbox" + "]" + "[name=" + "type" + "]").each(function() {
                        reasonCodeRuleIds.push(this.value);
                        i++;
                    });
                }
                else {
                    reasonCodeRuleIds = [];
                }
            })
            .on('click', function(e) {
                e.stopPropagation();
            })
            .on('keyup', function(e) {
                e.stopPropagation();

                $(this).trigger('change');
                if (cursorPosition && cursorPosition != null) {
                    $(this)
                        .focus()[0]
                        .setSelectionRange(cursorPosition, cursorPosition);
                }
            });
    }
    else {
        $(cell).html(title + '<br> &nbsp;');
    }
}

function showAndHideModel() {
    if (document.getElementById("allPendingSize").value > 0) {
        if ($('#selectAll').is(':checked')) {
            $("#toggleModal").modal('show');
        }
        else {
            $("#toggleModal").modal('hide');
        }
    }
    else {
        if ($('#selectAll').is(':checked')) {
            $('#transitionErrorMsg').show();
        }
        else {
            $('#transitionErrorMsg').hide();
        }
    }
}

function viewMcc(reasonCode, type) {
	var url;
	if (type == 'V')
		url = '/editReasonCode';
	else if (type == 'P')
		url = '/getReasonCode';
	else if (type == 'G')
		url = '/getPendingReasonCode';
	
	var data = "reasonCode," + reasonCode + ",viewType," + type ;
	postData(url, data);
}



function submitForm(url) {
	
	var data ="userType," + $('#userType').val();
	postData(url, data);
}


function deselectAll() {
	$('#selectAll').prop('checked', false);
	var ele = document.getElementsByName('type');
	for (let val of ele) {
		if (val.type == 'checkbox')
			val.checked = false
	}
}



function ApproveorRejectBulk(type,action){
	var url = '/bulkApproveResonCodeMaster';
	 var array = [];
		if(action=='no'){
	   $("input:checkbox[name=type]:checked").each(function() {
	       array.push($(this).val());
	   });
	   }
	   else if(action=='All'){
	   if(reasonCodeRuleIds.length>0){
	   array= reasonCodeRuleIds;
	   		}else{
	   			$('.selectedId', allPages).filter(":checked").each(function() {
	   				array.push($(this).val());
	   			});}
	   }
		if(array.length>0){
			  $('#jqueryError4').hide();
			  var idList = "";
			  for ( let i of array) {
				  idList = idList + i + "|";
			  }
			  var data="";
			  if(type=='A'){
				  data =  "status,"+"A"+",reasonCodeList,"+idList+",remarks,"+"Approved";
			  }
			  if(type=='R'){
				  data =  "status,"+"R"+",reasonCodeList,"+idList+",remarks,"+"Rejected";
			  }
		postData(url, data);
		}
		  else{
			  	$('#snxtSuccessMessage').hide();
			  $('#errorStatus4').html('Please Select  Atleast One Record');
				$('#jqueryError4').show();	
		  }
	}

