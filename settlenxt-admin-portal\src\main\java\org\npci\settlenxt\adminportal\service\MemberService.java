package org.npci.settlenxt.adminportal.service;

import java.util.List;
import java.util.Map;

import org.npci.settlenxt.portal.common.dto.BinDetailsDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.FileUploadDTO;
import org.npci.settlenxt.portal.common.dto.MemberDTO;
import org.npci.settlenxt.portal.common.dto.MemberOnBoardingDTO;
import org.npci.settlenxt.portal.common.dto.SearchCriteriaDTO;
import org.npci.settlenxt.portal.common.dto.SettlementBinDTO;
import org.springframework.web.multipart.MultipartFile;

public interface MemberService {
	List<MemberDTO> getMembers(SearchCriteriaDTO searchCriteriaDTO);

	MemberDTO addMember(MemberDTO memberDTO);

	MemberDTO addMemberInfo(MemberOnBoardingDTO memberOnBoardingDTO);

	MemberDTO addMemberContactInfo(MemberOnBoardingDTO memberOnBoardingDTO);

	List<BinDetailsDTO> getBinDetails(String participantId, String binType,  String isInsert);
	List<BinDetailsDTO> getBinDetailsForPendingForApproval(String participantId, String binType);

	void loadBinDetailsDTOs(MemberDTO memberDTO);

	Boolean checkMemberContactInfoSaved(String participantId);

	List<SettlementBinDTO> getsettlementBinsList(String participantId, List<String> statusList);

	List<MemberDTO> getFinalMemberList(SearchCriteriaDTO searchCriteriaDTO);

	MemberOnBoardingDTO getMember(String participantId);

	MemberOnBoardingDTO getMemberForPendingForApproval(String participantId);

	int updateMemberInfo(MemberOnBoardingDTO memberOnBoardingDTO, MultipartFile[] files);

	int updateMemberContactInfo(MemberOnBoardingDTO memberOnBoardingDTO);

	List<MemberDTO> getMembersSponsoBanks();

	int fetchParticipantIdSeq(String ifscCode);

	int fetchMemberIdSeq();

	int fetchBinIdSeq();

	int discardMemberData(MemberOnBoardingDTO memberOnBoardingDTO);

	MemberOnBoardingDTO getMemberFinalDiscard(String participantId);

	boolean deleteMemberBinStgAll(MemberOnBoardingDTO memberOnBoardingDTO);

	boolean deleteSettlementBinStgAll(MemberOnBoardingDTO memberOnBoardingDTO);

	Integer addBinListStg(List<BinDetailsDTO> binList);

	Integer addAcqBinListStg(List<BinDetailsDTO> binList);

	Integer addIssBinListStg(List<BinDetailsDTO> binList);

	Integer addSettlementBinListStg(List<SettlementBinDTO> binList);

	List<BinDetailsDTO> getBinDetailsFinal(String participantId, String binType);

	List<SettlementBinDTO> getsettlementBinsListAll(String participantId);

	List<MemberDTO> getSavedMemberList(SearchCriteriaDTO searchCriteriaDTO);

	Long getRowCount();

	Long getPendingRowCount();

	Long getSavedRowCount();

	 List<FileUploadDTO> getParticipantDocuments(String participantId);

	Map<String, String> getFeatureMap();

	 List<Map<String, String>> prepareLookUpMap();

	boolean checkIfParticipantExists(String participantId);

	boolean checkIfParticipantContactExists(String participantId);

	String approveMemberBulk(String bulkIdList, MemberDTO memberDto);

	boolean checkIfParticipantStgExists(String participantId);

	List<BinDetailsDTO> getIssuerAndTokenBinDetails(String participantId,  String isInsert);
	List<BinDetailsDTO> getIssuerAndTokenBinDetailsForPendingForApproval(String participantId);

	void discardMemberInfo(String participantId);

	List<String> getParticipantActualFileNames(String fileNames, String participantId);

	void approveMember(MemberDTO memberDTO);

	MemberOnBoardingDTO getMemberMain(String participantId);

	MemberOnBoardingDTO encryptParticipantSensitiveData(MemberOnBoardingDTO memBinDTO);

	MemberOnBoardingDTO decryptParticipantSensitiveData(MemberOnBoardingDTO memBinDTO);

	MemberDTO decryptParticipantSensitiveData(MemberDTO memBinDTO);

	MemberOnBoardingDTO getMemberData(String participantId);
	MemberOnBoardingDTO getMemberDataForDeletedBin(String participantId);
	List<BinDetailsDTO> getUnallocatedIssBinData();

	int duplicateSettlementBinCheck(String participantId, String settlementBinNumber);

	List<CodeValueDTO> getNetworkSelection();

	List<CodeValueDTO> getForexId();
}
