# Dev DB 
spring.datasource.url=*********************************************************************
spring.datasource.username=uircsapusrt
spring.datasource.password=Sty4quu/BmK8QFH04eDRZw==
spring.datasource.driverClassName=org.postgresql.Driver


#spring.datasource.url=*********************************************************************
#spring.datasource.username=postgres
#spring.datasource.password=tsCykawb5Mw=:BrArANuJBzxsAyzI/kN+Tiwkr9o=
#spring.datasource.driverClassName=org.postgresql.Driver

is.international.enabled=Y
#spring.datasource.url=*********************************************************************_bkp
#spring.datasource.username=uircsapusrt
#spring.datasource.password=uircsapusrt@#$%
#spring.datasource.url= ***********************************************************************
#spring.datasource.username= settlenxtappusr
#spring.datasource.password= settlenxtappusr@#$%

#Salt Key
spring.datasource.saltKeyForHash=4d5390bef3ef1ee3d4a7e77fd42238cb


# Onboarded Network Configuration
DEPLOYMENT_NETWORK=NPCI


#for txn json
OFFLINE_PRSNT_WITHOUT_AUTH = 260
PRSNT_WITH_AUTH = 200
MTI_1240 = 1240
FUNC_CODE_266_VOID = 266
MTI_8144 = 8144
DISPUTE_LIFE_CYCLE_VOID = void
DISPUTE_LIFE_CYCLE_PRESENTMENT_WITH_AUTH = presentmentWithAuth
DISPUTE_LIFE_CYCLE_DMS_SMS = DmsSms


# UI Logo 
frontend.portal.logo=rgcs_logo.jpg





FILE_UPLOAD_PATH=D:/upload/files/
FEES_BULK_UPLOAD=/home/<USER>/settlenxt/fileuploads
MAX_FILE_UPLOAD=2000
FILE_DOWNLOAD_PATH=/home/<USER>/settlenxt/fileuploads

mybatis.config-location=classpath:/mybatis-config.xml

mybatis.mapper-locations=classpath:/mappers/*.xml

REPORT_ORCHESTRATION_DOMAIN = http://localhost:9302/

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

#application password encoding
CRYPT_CONNECTION_KEY = QWERTYUIOPLKJHGF
IV_PARAM_SPEC_STRING = 0123456701234567


#in milliseconds
#spring.datasource.hikari.connection-timeout=50000
#spring.datasource.hikari.idle-timeout=300000
#spring.datasource.hikari.max-lifetime=900000
#in seconds
spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.pool-name=ConnPool
INTEGRITY_CHECKER_DOMAIN = http://localhost:9304/


#Dont change the order
GENERATION_COMPLETED=green
GENERATION_FAILED = red
GENERATION_INITIATED = blue
GENERATION_STARTED= yellow
MERGE_COMPLETED=olive
MERGE_FAILED=Maroon
MERGE_STARTED = purple
NO_DATA_PRESENT=Teal
PGP_COMPLETED=Lime
PGP_FAILED=Gray
POSTCHECK_FAILED=Fuchsia
PRECHECK_FAILED=Navy
PULL_COMPLETED= orange
PULL_FAILED=black
PULL_STARTED =pink
READY_TO_DOWNLOAD=Aqua


#topic
kafka.topic.reportMovement.signing=GenericFileMover


OLD_REPORTS_ARTIFACT_DOWNLOAD=D:/upload/files/





footer.msg=Disclaimer: Bharat Clearing & Settlement is maintained by NPCI\
 and exclusively intended for the purpose of Clearing, Settlement and Dispute Management of RuPay Card Members.\
 The use of this facility is strictly subject to the terms and conditions of Membership Agreements including confidentiality.\
 NPCI will not be liable for any loss or damage whatsoever arising out of loss of data or profit arising out of it or in connection with the use of this facility &#169

 spring.config.import=regEx.properties
 
 CONTENT_SECURITY_POLIY_HEADER=default-src 'self';style-src 'self'  'unsafe-inline' 'http://localhost:6001' ;script-src 'self' 'unsafe-inline' 'http://localhost:6001'; frame-ancestors 'none';  upgrade-insecure-requests;object-src 'none';
