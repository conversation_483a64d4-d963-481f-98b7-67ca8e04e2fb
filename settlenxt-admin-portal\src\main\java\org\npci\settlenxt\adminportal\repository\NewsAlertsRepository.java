package org.npci.settlenxt.adminportal.repository;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.portal.common.dto.NewsAlertsDTO;
import org.npci.settlenxt.portal.common.dto.NewsDTO;

@Mapper
public interface NewsAlertsRepository {

	 int fetchNewsIdSeq();

	 int fetchReferenceNoSeq();

	 int addNewsAlerts(NewsDTO newsDTO);

	 int addNewsAlertsFinal(NewsDTO newsDTO);

	 int updateNewsInfo(NewsAlertsDTO newsAlertsDTO);

	 int updateSchedulerInfo(NewsAlertsDTO newsAlertsDTO);

	 int updateNewsAlertsInfo(NewsDTO newsDTO);

	 int updateSchedulerDetailsInfo(NewsDTO newsDTO);

	 Integer newsAlertsApproveStg(@Param("newsId") Integer newsId, @Param("rejectReason") String rejectReason);

	 Integer newsAlertsRejectStg(@Param("newsId") Integer newsId, @Param("rejectReason") String rejectReason,
			@Param("lastOp") String lastOp);

	 Integer checkNewsInfoSaved(@Param("title") String title);

	 long getTotalCount();

	 long getApprovalTotalCount();

	 long getSavedTotalCount();

	 long getDeleteTotalCount();

	NewsDTO getNewsAlertsEdit(@Param("refNumber") String refNumber);

	NewsAlertsDTO getNewsAlerts(@Param("referenceNumber") String referenceNumber);

	List<NewsDTO> getNewsAlertsInfoList(@Param("referenceNumberList") List<String> referenceNumberList);

	 List<NewsAlertsDTO> getFinalNewsAlertsList(@Param("startVal") int startVal, @Param("endVal") int endVal);

	 List<NewsAlertsDTO> getTempNewsAlertsList(@Param("startVal") int startVal, @Param("endVal") int endVal);

	 List<NewsAlertsDTO> getSavedNewsAlertsList(@Param("startVal") int startVal, @Param("endVal") int endVal);

	 List<NewsAlertsDTO> getDeletedNewsAlertsList(@Param("startVal") int startVal, @Param("endVal") int endVal);

	 NewsAlertsDTO getnewsAlertsMain(@Param("referenceNumber") String referenceNumber);

	 void deleteDiscardedEntry(NewsAlertsDTO newsAlertsDTO);

	NewsDTO getNewsAlertsInfo(@Param("referenceNumber") String referenceNumber);

	 NewsDTO getParticipantByUserName(@Param("users") String users);

	 void addNewsDistributionDetails(NewsDTO newsDistDTO);

	 void deleteExistingDistributiondetails(@Param("newsId") int newsId);

	 long checkIfExistsInMain(@Param("newsId") int newsId);

	 int updateNewsAlertStgDeactivate(NewsDTO newsDTO);

	 int updateNewsAlertStgDeactivateMain(NewsDTO newsDTO);

	 int updateDistributionStgDeactivate(NewsDTO newsDTO);

	 int updateDistributionStgDeactivateMain(NewsDTO newsDTO);

	 int updateDistributionStg(NewsDTO newsDTO);

	 void addNewsDistributionInfo(NewsAlertsDTO newsDTO);

	NewsDTO getSchedulerDetail(NewsDTO newsAlertsDTO);

	 List<NewsAlertsDTO> getNewsAlertsListPublic();

	 List<NewsAlertsDTO> getNewsAlertsListSpecific(@Param("userName") String userName);

	 List<NewsAlertsDTO> getNewsAlertsListCommon();

	 long checkIfNewsAlertsExistReports(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate,
			@Param("requestState") String requestState);

	 List<NewsAlertsDTO> getDistributionDetails(@Param("newsId") int newsId, @Param("status") String status);

	 void updateNewsAlertsInfoMain(NewsDTO newsDTO);

	 List<NewsAlertsDTO> fetchDistributionList(@Param("newsId") int newsId);

	 int deleteDistributionMain(@Param("newsId") int newsId);

	 int updateNewsAlertApproveStg(NewsDTO newsDTO);

	 long checkIfExistsInStg(@Param("newsId") int newsId);

}
