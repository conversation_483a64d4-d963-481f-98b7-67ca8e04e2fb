	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	
	}
	function backAction(type, action,originPage) {
	var url;
		url = action;
		 var data = "userType," + type + ",originPage," + originPage ;
		postData(url, data);
	}
	
	function postAction(_action,originPage) {
		var insurancePremId;
		var crtuser;
		var remarks;
		var url;
		var data;
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 insurancePremId = $("#insurancePremId").val();
				 crtuser = $("#crtuser").val();
				 remarks=$("#rejectReason").val();
		
				 url = '/approveInsurancePremiumStatus';
				 data = "insurancePremId," + insurancePremId + ",status," + "Approved" + ",crtuser,"
						+ crtuser + ",remarks,"
						+ remarks  + ",originPage," + originPage ;
			
				
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				postRejectedData(insurancePremId, remarks, crtuser, url, data, originPage);
	
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		


function postRejectedData(insurancePremId, remarks, crtuser, url, data, originPage) {
    if ('${requestInfo.requestName}' == 'ROLE FUNCTIONALITY MAP') {
        insurancePremId = $("#insurancePremId").val();
        remarks = $("#rejectReason").val();
        crtuser = $("#crtuser").val();

        url = '/approveInsurancePremiumStatus';

        data = "insurancePremId," + insurancePremId + ",status," + "Rejected"
            + ",crtuser," + crtuser + ",rejectReason," + remarks + ",originPage," + originPage;

        postData(url, data);
    }
    else {
        insurancePremId = $("#insurancePremId").val();
        crtuser = $("#crtuser").val();
        remarks = $("#rejectReason").val();
        url = '/approveInsurancePremiumStatus';

        data = "insurancePremId," + insurancePremId + ",status," + "Rejected"
            + ",crtuser," + crtuser + ",remarks," + remarks + ",originPage," + originPage;
        postData(url, data);
    }
   
}
