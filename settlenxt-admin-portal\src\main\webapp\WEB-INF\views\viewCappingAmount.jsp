<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<script type="text/javascript"
	src="./static/js/validation/ApproveCappingAmount.js"></script>

<form:form onsubmit="removeSpace(this); encodeForm(this);" method="POST"
	id="viewApproveCappingAmount" modelAttribute="cappingAmountDto"
	action="/approveCappingAmount" autocomplete="off">
	<div class="panel panel-default no_margin">
		<div class="panel-heading clearfix">
			<strong><span class="glyphicon glyphicon-th"></span> <span
				data-i18n="Data"><spring:message code="Cap.viewscreen.title" /></span></strong>
			<div class="icon_bar">
				<sec:authorize access="hasAuthority('Edit Capping Amount') ">
					<a data-toggle="tooltip" title="Edit"
						onclick="viewCappingAmountInfo('${cappingAmountDto.actionCode}','${cappingAmountDto.mccGroup}','${cappingAmountDto.binCardBrandId}','${cappingAmountDto.binCardTypeId}','${cappingAmountDto.fieldName}','${cappingAmountDto.relOperator}','${cappingAmountDto.fieldValue}','/editCappingAmount')"
						href="#"><img src="./static/images/edit-grey.png" alt="edit"></a>

				</sec:authorize>
			</div>
		</div>

		<div class="panel-body">
			<form:hidden path="actionCode" value="${cappingAmountDto.actionCode}" />

			<table class="table table-striped" style="font-size: 12px">

			<caption style="display:none;">CAPPING AMT</caption>

			<thead style="display:none;"><th scope="col"></th></thead>
				<tbody>
					<tr>
						<td><label><spring:message code="am.lbl.actionCode" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.actionCode}</td>
					
						<td><label><spring:message code="am.lbl.mccGroup" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.mccGroup}</td>
						
						<td><label><spring:message code="am.lbl.binCardBrandId" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.binCardBrandId}</td>
						
						<td><label><spring:message code="am.lbl.binCardTypeId" /><span
								style="color: red"></span></label></td>
					<td>${cappingAmountDto.binCardTypeId}</td>
					
						</tr>
						
				<tr>
						<td><label><spring:message code="am.lbl.capName" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.capName}</td>
						
						<td><label><spring:message code="am.lbl.capType" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.capType}</td>
						
						<td><label><spring:message code="am.lbl.fieldName" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.fieldName}</td>
						
						<td><label><spring:message code="am.lbl.relOperator" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.relOperator}</td>
					
						<td></td>
						<td></td>
					
						
						</tr>
						
						<tr>
						
						<td><label><spring:message code="am.lbl.fieldValue" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.fieldValue}</td>
						
						<td><label><spring:message code="am.lbl.amountFlag" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.amountFlag}</td>
						
						<td><label><spring:message code="am.lbl.flat" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.flat}</td>
						
						<td><label><spring:message code="am.lbl.percentage" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.percentage}</td>
					
						<td></td>
						<td></td>
					
						
						</tr>
						<tr>
						<td><label><spring:message code="am.lbl.amountCapMax" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.amountCapMax}</td>
						
						<td><label><spring:message code="am.lbl.amountCapMin" /><span
								style="color: red"></span></label></td>
						<td>${cappingAmountDto.amountCapMin}</td>
						
						<td><label><spring:message code="ifsc.status" /><span
								style="color: red"></span></label></td>
						<td><c:if test="${cappingAmountDto.status=='A' }">
								<spring:message code="ifsc.activeStatus" />
							</c:if> <c:if test="${cappingAmountDto.status=='I' }">
								<spring:message code="ifsc.inactiveStatus" />
							</c:if></td>
						<td></td>
					</tr>
				</tbody>
			</table>

			<div style="text-align: center">

				<button type="button" class="btn btn-danger"
					onclick="submitForm('/showCappingAmount');">
					<spring:message code="ifsc.backBtn" />
				</button>
			</div>
		</div>
	</div>
</form:form>