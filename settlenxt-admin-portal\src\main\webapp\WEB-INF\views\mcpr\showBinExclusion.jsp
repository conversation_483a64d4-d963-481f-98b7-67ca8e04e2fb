<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@	taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<script type="text/javascript">
	var actionColumnIndex = 5;
	var firstColumnToBeSkippedInFilterAndSort = false;
	<c:if test="${showCheckBox eq 'Y'}">
	actionColumnIndex = 9;
	firstColumnToBeSkippedInFilterAndSort = true;
	</c:if>
	<c:if test="${showCheckBox eq 'N'}">
	actionColumnIndex = 8;
	firstColumnToBeSkippedInFilterAndSort = false;
	</c:if>
</script>


<script>
	var referenceNoListPendings = [];
	<c:if test="${not empty pendingBinExclList}">
	<c:forEach items="${pendingBinExclList}" var="operator">
	<c:if test="${operator.requestState eq 'P' }">
	referenceNoListPendings.push('${operator.excId}');
	</c:if>
	</c:forEach>
	</c:if>
</script>
<script src="./static/js/validation/mcpr/viewBinExclRGCS.js"
	type="text/javascript"></script>
<script type="text/javascript"
	src="./static/js/dataTables.buttons.min.js">
	
</script>
<script type="text/javascript" src="./static/js/jszip.min.js">
	
</script>

<script type="text/javascript" src="./static/js/buttons.html5.min.js">
	
</script>
<link rel="stylesheet" href="./static/css/jquery.dataTables.min.css" />
<link rel="stylesheet" href="./static/css/buttons.dataTables.min.css" />
<style>
.defaultexport {
	visibility: hidden;
}

table.dataTable thead {
	vertical-align: top;
}

table.dataTable thead .sorting {
	vertical-align: top;
	background: url('./static/images/sort_both.png') no-repeat center right;
}

table.dataTable thead .sorting_asc {
	vertical-align: top;
	background: url('./static/images/sort_asc.png') no-repeat center right;
}

table.dataTable thead .sorting_desc {
	vertical-align: top;
	background: url('./static/images/sort_desc.png') no-repeat center right;
}

table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting_asc:before
	{
	vertical-align: top;
	content: ""
}

table.dataTable thead>tr>th.sorting:after, table.dataTable thead>tr>th.sorting_asc:after
	{
	vertical-align: top;
	content: ""
}

.search-box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-color: transparent;
	width: 100%;
	border-width: 1px;
	border-style: inset;
}
</style>
<!-- Model -->
<div class="modal fade" id="toggleModalNews" tabindex="-1" role="dialog"
	aria-labelledby="toggleModalNews" aria-hidden="true">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">Are you sure you
					want to Approve/Reject these records?</h5>
				<button type="button" class="close" data-dismiss="modal"
					aria-label="Close" onclick="deselectAll()">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div>
				<label style="color: blue; font-weight: bold;">Bin Exclusion
					Approval/Rejection</label>
				<p id="newsIds" />
			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-secondary"
					onclick="ApproveorRejectBulkBinExcl('R','All')">Reject</button>
				<button type="button" class="btn btn-primary"
					onclick="ApproveorRejectBulkBinExcl('A','All')">Approve</button>
			</div>
		</div>
	</div>
</div>
<!-- Model -->

<input:hidden id="refNum" />
<div class="row">
	<div role="alert" style="display: none" id="jqueryError4">
		<div id="errorStatus4" class="alert alert-danger" role="alert"></div>
	</div>
	<div id="errLvType" class="alert alert-danger" role="alert"
		style="display: none"></div>

</div>
<div class="space_block">
	<ul class="nav nav-tabs" role="tablist" id="myTab">
		<c:choose>
			<c:when test="${showBinExclusion eq 'YES' }">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#home" onclick="submitForm('/showBinExclusion');" role="tab"
			data-toggle="tab"> <span class="glyphicon glyphicon-credit-card">&nbsp;</span>
			<spring:message code="binexcl.mainTab.title" />
		</a>

		<c:choose>
			<c:when test="${pendingBinExcl eq 'YES'}">
				<li role="presentation" class="active" />
			</c:when>
			<c:otherwise>
				<li role="presentation" />
			</c:otherwise>
		</c:choose>

		<a href="#profile" role="tab"
			onclick="submitForm('/binPendingForApproval');" data-toggle="tab">
			<span class="glyphicon glyphicon-ok">&nbsp;</span> <spring:message
				code="binexcl.approvalPanel.title" />
		</a>
	</ul>

	<div class="tab-content">
		<!-- tabpanel -->
		<div role="tabpanel" class="tab-pane active" id="home">
			<div class="row">
			<div class="col-sm-12">
			<sec:authorize access="hasAuthority('Add Bin Exclusion Config')">
									<c:if test="${addNew eq 'Yes'}">
										<a class="btn btn-success pull-right btn_align" href="#"
											onclick="submitForm('/createbinExclusion');"
											style="margin-top: -5px 0px 2px 0px;"><em class="glyphicon-plus"></em>
											Add Bin Exclusion</a>
									</c:if>
								</sec:authorize>
								</div>
				<div class="col-sm-12">
					<button class="btn  pull-right btn_align" id="clearFilters">
						<spring:message code="ifsc.clearFiltersBtn" />
					</button>
					&nbsp; <a class="btn btn-success pull-right btn_align" href="#"
						id="csvExport"> <spring:message code="ifsc.csvBtn" />
					</a> <a class="btn btn-success pull-right btn_align" href="#"
						id="excelExport">Excel </a>
				</div>
			</div>
			<c:if test="${showBinExclusion eq 'YES' }">
				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="binexcl.viewscreen.title" /></span></strong>
								
							</div>

							<div class="panel-body">
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Bin Exclusion</caption>
										<thead>
											<tr>
												<th scope = "col"><spring:message code="binexcl.participantName" /></th>
												<th scope = "col"><spring:message code="binexcl.bin" /></th>
												<th scope = "col"><spring:message code="binexcl.baseorfeature" /></th>
												<th scope = "col"><spring:message code="binexcl.fromDate" /></th>
												<th scope = "col"><spring:message code="binexcl.toDate" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="exclbin" items="${binlist}">

												<tr>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','P')">${exclbin.bankName}</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','P')">${exclbin.bin}</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','P')">${exclbin.feeSelectionType}</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','P')">${exclbin.fromDate}</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','P')">${exclbin.toDate}</td>
												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</c:if>

			<c:if test="${pendingBinExcl eq 'YES'}">

				<div class="row">
					<div class="col-sm-12">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong><span class="glyphicon glyphicon-th"></span> <span
									data-i18n="Data"><spring:message
											code="binexcl.mainTab.title" /></span></span></strong>
								<c:if test="${not empty pendingBinExcl}">
									<sec:authorize
										access="hasAuthority('Approve Bin Exclusion Config')">
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkBinExcl('A','No')"
											id="submitButtonA"
											value="<spring:message code="feeRate.Approve" />" />
										<input type="button"
											class="btn btn-success pull-right btn_align"
											onclick="ApproveorRejectBulkBinExcl('R','No')"
											id="submitButtonR"
											value="<spring:message code="feeRate.Reject" />" />
									</sec:authorize>
								</c:if>
							</div>

							<div class="panel-body">
								<%-- 	<div class="row">
									                                    
									<div class="col-sm-12">
										  
										<button class="btn  pull-right btn_align" id="clearFilters">
											<spring:message code="ifsc.clearFiltersBtn" />
										</button>
										&nbsp; <a class="btn btn-success pull-right btn_align"
											href="#" id="csvExport"> <spring:message
												code="ifsc.csvBtn" />
										</a> <a class="btn btn-success pull-right btn_align" href="#"
											id="excelExport">Excel </a>                               
										     
									</div>
									                                
								</div> --%>
								<div class="table-responsive">
									<table id="tabnew" class="table table-striped table-bordered"
										style="width: 100%;">
										<caption style="display: none;">Bin Exclusion</caption>
										<thead>
											<tr>
												<sec:authorize
													access="hasAuthority('Approve Bin Exclusion Config')">
													<th scope = "col"><input type=checkbox name='selectAllCheck'
														id="selectAll" data-target="toggleModalNews" value="All"></input></th>
												</sec:authorize>
												<th scope = "col"><spring:message code="binexcl.excId" /></th>
												<th scope = "col"><spring:message code="binexcl.participantName" /></th>
												<th scope = "col"><spring:message code="binexcl.bin" /></th>
												<th scope = "col"><spring:message code="binexcl.baseorfeature" /></th>
												<th scope = "col"><spring:message code="binexcl.fromDate" /></th>
												<th scope = "col"><spring:message code="binexcl.toDate" /></th>
												<th scope = "col"><spring:message code="binexcl.requestStatus" /></th>
												<th scope = "col"><spring:message code="binexcl.checkerComments" /></th>
											</tr>
										</thead>
										<tbody>
											<c:forEach var="exclbin" items="${pendingBinExclList}">
												<tr>
													<sec:authorize
														access="hasAuthority('Approve Bin Exclusion Config')">
														<c:if test="${exclbin.requestState =='P' }">
															<td><input type=checkbox name='type'
																id="selectSingle" onclick="mySelect();"
																value="${exclbin.excId}"></input></td>
														</c:if>
														<c:if test="${exclbin.requestState !='P' }">
															<td></td>
														</c:if>
													</sec:authorize>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','G')">${exclbin.excId}</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','G')">${exclbin.bankName}</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','G')">${exclbin.bin}</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','G')">${exclbin.feeSelectionType}</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','G')">${exclbin.fromDate}</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','G')">${exclbin.toDate}</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','G')">
														<c:if test="${exclbin.requestState=='P' }">
															<spring:message
																code="binexcl.requestState.pendingApproval.description" />
														</c:if> <c:if test="${exclbin.requestState=='R' }">
															<spring:message
																code="binexcl.requestState.rejected.description" />
														</c:if>
													</td>
													<td
														onclick="javascript:viewBinExclConfig('${exclbin.excId}','G')">${exclbin.checkerComments}</td>

												</tr>
											</c:forEach>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
			</c:if>
		</div>


	</div>

</div>




