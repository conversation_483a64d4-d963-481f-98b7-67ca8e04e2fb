$(document).ready(function() {
	
	
	$('#uploadErrorMsg').hide();
	$('#uploadSuccessMsg').hide();
	$('#rejectFiles').hide();

	$("#fromDate").datepicker({
		dateFormat: "yyyy-mm-dd",
		changeMonth: true,
		changeYear: true,
		maxDate: 0,
		//	minDate : 0,
		onSelect: function(_selectedDate) {
			
			var dt = new Date(selected);
			dt.setDate(dt.getDate() + 1);
			$("#toDate").datepicker("option", "minDate", dt);
		}
	}).datepicker("setDate", 'now');

	$("#toDate").datepicker({
		dateFormat: "yyyy-mm-dd",
		changeMonth: true,
		changeYear: true,
		maxDate: 0,
		onSelect: function(_selectedDate) {
			var dt = new Date(selected);
			dt.setDate(dt.getDate() - 1);
			$("#fromDate").datepicker("option", "maxDate", dt);
		}
	}).datepicker("setDate", 'now');

	var myTable = $("#tabnew").DataTable({
		initComplete: function() {
			var api = this.api();

			// For each column
			api
				.columns()
				.eq(0)
				.each(function(colIdx) {
					//If first column to be skipped to include the filter for the reasons line check box 
					if (!((colIdx == 0 || colIdx == 11) && firstColumnToBeSkippedInFilterAndSort)) {
						// Set the header cell to contain the input element
						var cell = $('#tabnew thead tr th').eq(
							$(api.column(colIdx).header()).index()
						);
						var title = $(cell).text();
						handleInput(colIdx, cell, title, api);
					}
				});
			$('#tabnew_filter').hide();
			
		},
		columnDefs: [
			{ orderable: !firstColumnToBeSkippedInFilterAndSort, targets: [0, 11] }
		],
		"order": [],
		dom: 'lBfrtip',
		buttons: [

			{
				extend: 'excelHtml5',
				text: 'Export',
				filename: 'FileUpload',
				header: 'false',
				title: null,
				sheetName: 'FileUpload',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			},
			{
				extend: 'csvHtml5',
				text: 'Export',
				filename: 'FileUpload',
				header: 'false',
				title: null,
				sheetName: 'FileUpload',
				className: 'defaultexport',
				exportOptions: {
					columns: 'th:not(:last-child)'
				}
			}

		],
		searching: true,
		info: true,
		lengthChange: true,
		bLengthChange: true
	});

	$("#uploadId").click(function() {
		$("#uploadId").prop('disabled', true);
		var check = validatepage();
		var specialCharsXML = "#%&$*:<>?,\-+=@^`'()_;~[]/{|}";
		var specialCharsCSV = /^[a-zA-z0-9\-_]*$/;
		if (check) {
			if ($("#fileArr").val() != '') {
				var flag = false;
				for (var i of $("#fileArr").get(0).files) {
					var uplodedFile = i.name;
					var fileNameSplit = uplodedFile.split('.');

					if(fileNameSplit[1].toUpperCase() == "CSV"){
						if(!specialCharsCSV.test(fileNameSplit[0])){
							flag = true;
						}
					}else{
						flag= handleSpecialCharacters(specialCharsXML, fileNameSplit, flag)
					}


				}
				validateAndHandleAjaxCall(flag);
			} else {
				$("#errFile").text("Please select file");
				$("#uploadId").prop('disabled', false);
			}
		} else {
			$("#uploadId").prop('disabled', false);
		}
	});

	
	
	$("#resetBtn").click(function() {
		$('#rejectFiles').text('');
		$('#rejectFiles').hide();
		$('#uploadErrorMsg').hide();
		$('#uploadSuccessMsg').hide();
		document.getElementById("fileArr").value = "";
		$("#uploadId").prop('disabled', false);
	});

	$("#fileUploadResetBtn").click(function() {
		$("#fromDate").val("");
		$("#toDate").val("");
		
		document.getElementById("documentName").value = "";
		document.getElementById("status").value = "";
		
	});

	$("#fileUploadSearchBtn").click(function() {
		var fromDate = $('#fromDate').val();
		var toDate = $('#toDate').val();
		var documentName = $('#documentName').val();
		var statusSelect = $('#status').val();

		var url = "/searchFileUploadList";

		if ($('#fromDate').val() === "") {
			$('#errFromDate').text('Please select From Date ');
			return false;
		} else {
			$('#errFromDate').text('');
		}
		if ($('#toDate').val() === "") {
			$('#errToDate').text('Please select To Date ');
			return false;
		} else {
			$('#errToDate').text('');
		}
		if (Date.parse(fromDate) > Date.parse(toDate)) {
			$('#errToDate').text('To date cannot be less than From date');
			return false;
		} else {
			$('#errToDate').text('');
		}
		fromDate = fromDate + ' 00:00:00';
		toDate = toDate + ' 23:59:59';


		var fromDateMoment = moment(fromDate, 'MM/DD/YYYY HH:mm:SS').format('YYYY-MM-DDTHH:mm:SS');
		var toDateMoment = moment(toDate, 'MM/DD/YYYY HH:mm:SS').format('YYYY-MM-DDTHH:mm:SS');
		

		

		var data = "fromDateStr,"
			+ fromDateMoment + ",toDateStr," + toDateMoment
			+ ",documentName," + documentName
			+ ",status," + statusSelect;
		postData(url, data);
	});

	var allPages = myTable.rows().nodes();

	$('#selectall').click(function() {
		// 	Fetch all row based on filter
	var filteredData = myTable.rows({ search: 'applied' }).nodes();

    if ($(this).hasClass('allChecked')) {
		// Uncheck all filtered checkboxes
		$(filteredData).find('input.selectedId').prop('checked', false);  
  	} else {
		// Check all filtered checkboxes
		$(filteredData).find('input.selectedId').prop('checked', true);    
	}
    $(this).toggleClass('allChecked');
	});

	$('.selectedId').change(function() {
		var check = ($('.selectedId').filter(":checked").length == $('.selectedId').length);
		$('#selectall').prop("checked", check);
	});


	$('#fileUploadStagehBtn').click(function() {
		var fileIds = "";
		if ($(".selectedId:checkbox:checked").length < 1) {
			$("#stageErr").text("Atleast select one file..!!")
			return;
		}

		$('.selectedId', allPages).filter(":checked").each(function(_i, _obj) {
			fileIds = fileIds + $(this).attr("documentId") + "-";
		});
		$('#stageFileIds').val(fileIds.slice(0, -1));
		var url = getURL('/getFileUploadCycleNumber');
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		$.ajax({
			method: 'POST',
			url: url,
			dataType: "json",
			data: { _TransactToken: tokenValue },
			success: function(response) {
				$("#cycleNumber").val(response.cycleNumber);
				$("#cycleDate").val(response.cycleDate);
				if (response.result === 'Invalid') {
					$('#toggleModalCycleValidation').modal({
						show: 'true'
					});
				} else {
					var data = "stageFileIds," + $("#stageFileIds").val() + ",cycleNumber," +
						$("#cycleNumber").val() + ",cycleDate," + $("#cycleDate").val();
					postData("/stageUploadedFiles", data);
					
				}
			},
			
		});

		
		
	});

	

	$("#excelExport").on("click", function() {
		$(".buttons-excel").trigger("click");
	});

	$("#csvExport").on("click", function() {
		$(".buttons-csv").trigger("click");
	});
	
	$("#refreshId").on("click", function() {
		postData("/filesUploadView", "");
	});

});

function handleSpecialCharacters(specialChars, fileNameSplit, flag) {
	var f=flag;
	for (var j of specialChars) {
		if (fileNameSplit[0].indexOf(j) > -1) {
			f = true;
		}
	}
	return f;
}

function validateAndHandleAjaxCall(flag) {
	if (flag) {
		$('#errFile').text('Special characters are not allowed in file name');
		$('#uploadErrorMsg').hide();
		$('#rejectFiles').hide();
		$('#uploadSuccessMsg').hide();
		$("#uploadId").prop('disabled', false);
	} else {
		$('#errFile').text('');

		var url = getURL("/bulkFilesUpload");
		var tokenValue = document.getElementsByName("_TransactToken")[0].value;
		var formData = new FormData();
		var fileLength = document.getElementById('fileArr').files.length;
		for (var x = 0; x < fileLength; x++) {
			formData.append("document", document.getElementById("fileArr").files[x]);
		}
		formData.append("_TransactToken", tokenValue);
		$.ajax({
			method: 'POST',
			url: url,
			cache: false,
			processData: false,
			contentType: false,
			data: formData,
			success: async function (response) {
				if (response === "Success") {
					$('#uploadSuccessMsg').text('File Uploaded Successfully');
					$('#uploadErrorMsg').hide();
					$('#rejectFiles').hide();
					$('#uploadSuccessMsg').show();

					await sleep(1000);
					var tkn = "_TransactToken," + tokenValue;
					//added this function for forwarding request to new url as common func(vtransact) not working properly
					postDataNew("/filesUploadView", tkn);
				} else if (response.length > 0 && !response.startsWith("ERROR")) {
					$('#rejectFiles').text('Files: ' + response);
					$('#uploadErrorMsg').hide();
					$('#rejectFiles').show();
					$("#uploadId").prop('disabled', false);
				} else if (response.startsWith("ERROR")) {
					$('#uploadErrorMsg').text('Something went wrong. Please try again!');
					$('#rejectFiles').hide();
					$('#uploadErrorMsg').show();
					$("#uploadId").prop('disabled', false);
				}
			},
			error: function (_jqXHR, _textStatus, _errorThrown) {
				$('#uploadErrorMsg').text('Something went wrong. Please try again!');
				$('#uploadErrorMsg').show();
				$("#uploadId").prop('disabled', false);
			}
		});
	}
}

function handleInput(colIdx, cell, title, api) {
	if (colIdx < actionColumnIndex) {

		$(cell).html(title + '<br><input class="search-box"   type="text" />');

		// On every keypress in this input
		$(
			'input',
			$('#tabnew thead tr th').eq($(api.column(colIdx).header()).index())
		)
			.off('keyup change')
			.on('change', function (_e) {
				// Get the search value
				$(this).attr('title', $(this).val());
				var regexr = '({search})';


				// Search the column for that value
				api
					.column(colIdx)
					.search(
						this.value != ''
							? regexr.replace('{search}', '(((' + this.value + ')))')
							: '',
						this.value != '',
						this.value == ''
					)
					.draw();
			})
			.on('click', function (e) {
				e.stopPropagation();
			})
			.on('keyup', function (e) {
				e.stopPropagation();

				$(this).trigger('change');
				if (cursorPosition && cursorPosition != null) {
					$(this)
						.focus()[0]
						.setSelectionRange(cursorPosition, cursorPosition);
				}
			});
	} else {
		$(cell).html(title + '<br> &nbsp;');
	}
}

function submitForm(_formId) {
	
	var data = "stageFileIds," + $("#stageFileIds").val() + ",cycleNumber," +
		$("#cycleNumber").val() + ",cycleDate," + $("#cycleDate").val();
	postData("/stageUploadedFiles", data);
}

function getContextPath() {
	return "${pageContext.request.contextPath}";
}

function isChecked(checkboxId) {
	var id = '#' + checkboxId;
	return $(id).is(":checked");
}

function validatepage() {

	var flag = true;

	if ($('#fileArr').val().trim() == '') {
		$('#errFile').text('Please attach file');
		flag = false;
	} else {
		$('#errFile').text('');
	}


	return flag;
}
//});

function noBack() {
	window.history.forward();
}

function fileRejectView(docId, fromDateStr, toDateStr, docName, status, fromDate, toDate) {
	urlPostActionWithData('/filesRejectView', 'documentId,' + docId + ',fromDateStr,' + fromDateStr + ',toDateStr,' + toDateStr + ',docName,' + docName + ',status,' + status + ',fromDate,' + fromDate + ',toDate,' + toDate);
}

function backToUpload(fromDateStr, toDateStr, docName, status, fromDate, toDate) {
	urlPostActionWithData('/searchFileUploadList', 'fromDateStrr,' + fromDateStr + ',toDateStrr,' + toDateStr + ',docNamer,' + docName + ',statusr,' + status + ',fromDateDb,' + fromDate + ',toDateDb,' + toDate);
}

function discardFile(docId, docName) {
	urlPostActionWithData('/discardFile', 'documentId,' + docId + ",documentName," + docName);
}

function postDataNew(action, data) {
	var loc = window.location;
	var pathName = loc.pathname.substring(0, loc.pathname.lastIndexOf('/'));
	var linkurl = pathName + action;

	var form = document.createElement("form");
	form.method = "POST";

	var parameters = data.split(",");

	for (var i = 0; i < parameters.length; ++i) {
		var dynInput = document.createElement("input");
		dynInput.setAttribute("type", "hidden");
		dynInput.setAttribute("id", parameters[i]);
		dynInput.setAttribute("name", parameters[i]);
		++i;
		dynInput.setAttribute("value", parameters[i]);

		form.appendChild(dynInput);
	}
	document.body.appendChild(form); // added this	for firefox Browser
	encodeForm(form);	//Added by piyush for form encode

	form.action = linkurl;
	form.submit();
}

async function sleep(msec) {
    return new Promise(resolve => setTimeout(resolve, msec));
}
