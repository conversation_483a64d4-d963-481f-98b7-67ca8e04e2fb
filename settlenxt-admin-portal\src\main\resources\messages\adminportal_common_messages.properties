common.welcome =Welcome
# Generic Error message
E00001=Internal error occured. Please contact system administator
E00002=You are not authorized to access the requested page

##Title
msg.lbl.dailyTransCnt=Daily Transaction Count
msg.lbl.dailyTransAmt=Daily Transaction Amount
msg.lbl.perTransLimit=Per Transaction Limit
msg.lbl.all=all
msg.lbl.meBussType = Merchant Business Type
st.lbl.totalICNKEYS= ICN Keys
msg.lbl.MeTitle=RGCS Admin Portal
am.msg.approvalProcessCompleted=Approval process already completed for this request.
#Login Error Messages
login.accountLocked=User Account is blocked
login.accountExpired=User Account is expired
login.invalidCredentials=Invalid Username or Password
login.accountDeactive=User Account is deleted.
login.accountSuspended=User ID revoked. Please contact Admin to reset your password.
login.invalidSession=You have been logged off.
login.logout=You have been logged off.
## addEditUser.jsp
NotNull.LoginDTO.loginID= Please enter Login ID
NotCorrect.LoginDTO.loginID= Please enter correct Login ID
NotEmpty.LoginDTO.loginID= Please enter Login ID
Pattern.LoginDTO.loginID=Please enter valid Login ID
addEditUser.loginId.required = Login ID  is required
addEditUser.firstName.required = First Name is required
addEditUser.lastName.required =	Last Name is required
addEditUser.displayName.required =	Display Name is  required
addEditUser.firstName.invalid = First Name must contain alphabets
addEditUser.lastName.invalid =	Last Name must contain Alphabets
addEditUser.displayName.invalid =	Display Name should be alphanumeric
addEditUser.emailId.required =  Email ID is required
addEditUser.emailId.invalid =Please enter valid Email ID
addEditUser.emailId.lenInvalid=Email ID length can not be  more than 50 characters
addEditUser.loginId.invalid= Login ID must contain alphanumeric
addEditUser.loginId.lenInvalid=Login ID should be 8 to 20 characters long
addEditUser.firstName.lenInvalid=First Name should not exceed 20 characters
addEditUser.lastName.lenInvalid=Last Name  should not exceed 20 characters
addEditUser.displayName.lenInvalid=Display Name should not exceed 20 characters
addEditUser.mobileNo.invalid= Enter valid Mobile No
addEditUser.contactNo.invalid=Enter valid Land-line No
addEditUser.mobileNo.lenInvalid=Mobile no should be 10 digits
addEditUser.contactNo.lenInvalid=Land-Line no  should be above 8 Characters
addEditUser.city.invalid=City must contain alphabets only
addEditUser.city.lenInvalid=City length should not exceed 30 characters
addEditUser.state.invalid=State Field must contain alphabets
addEditUser.state.lenInvalid=State Length Should Not Exceed 30 Characters Long
addEditUser.empId.invalid=Employee ID must be alphanumeric 
addEditUser.pinNo.invalid=Pin Code Must be Numeric
addEditUser.pinNo.lenInvalid=Pin Code Must be 6 digits Long 
addEditUser.streetAddress.invalid =	Street Address should be alphanumeric Only
addEditUser.streetAddress.lenInvalid=Street Address length can not  exceed 50 characters 
NotNull= PLease enter this field
addEditUser.vEntityTyperequired=Entity Type is  required
NotEmpty.roleVO.vRoleName=Please  enter Role Name
NotNull.roleVO.vRoleName=Please  enter Role Name
Pattern.roleVO.vRoleName=Role Name must contain Alphabets
Pattern.roleVO.vRoleDesc=Role Description must contain Alphabets
NotEmpty.roleVO.vRoleDesc=Please  enter Role Name
NotNull.roleVO.vRoleDesc=Please  enter Role Description
role.roleName.validation.msg=Role Name must be alphanumeric and should not exceed 30 characters length.
role.roleDesc.validation.msg=Role Description should contain alphanumeric and "- . & space" only without exceeding 50  characters length.
role.roleType.validation.msg=Please select User Type 
role.makChkFlag.validation.msg=Please select Role Type
role.moduleId.validation.msg=Please select functionality
manualMarking.approve.invalid= Balance can not be more than previous balance
manualMarking.approve.required= Please Enter amount
invalid.vRoleName=Role name must not exceed 30 characters length.
invalid.vRoleDesc=Role description must not exceed 50 characters length.
RP_LBL_show=Get Customer Details 

#4th-may start
cmn.lbl.streetAddress=Street Address
cmn.lbl.mobileNo=Mobile No
cmn.lbl.landlineNo=Landline No.
msg.lbl.update = Update
sm.msg.duplicateAcquirer=Acquiring Bank is already exist with same bank code. Please provide a valid bank code. 
sm.msg.duplicateScheme=Scheme is already available with this name and code.
sm.lbl.settlementType=Settlement Type
sm.msg.duplicateLegalVehicle=Legal Vehicle already exist with this code.Please provide a valid Legal Vehicle code. 
#End
##
addEditRole.roleName.required = Role name required.
addEditRole.roleDesc.required = Role description required.
AM_MSG_roleApproved =Role approved successfully.
AM_MSG_roleRejectionSuccess =Operation successful.
am.msg.roleApproved=Role approved successfully.	
am.msg.roleEntryDiscarded=Rejected Role data has been discarded successfully.
AM_MSG_errorUpdateRole=Error while approving role.
AM_MSG_pendingRole=Request sent for approval.
AM_MSG_pendingUpdatedRole=Request sent for approval.
AM_MSG_errorRole=ADD ROLE
AM_MSG_RoleApprovedMailSendingFail=Error sending mail for role creation.
AM_MSG_RolePending=Role is already pending for approval.
AM_MSG_errorRoleApprove=Error occurred while processing.
AM_MSG_roleRejected=Role rejected successfully.
AM_MSG_roleDeactivationPending = Role Delete Request Sent for approval
##Server side messages
AM_MSG_invalidCredential=Invalid credentials.
AM_MSG_formValidationFailed=Form validation failed.
AM_MSG_pendingUser= User is sent for approval.
AM_MSG_pendingUpdatedUser=User update request sent for approval.
AM_MSG_errorUpdateUser=Error while user updation. 
AM_MSG_errorUser=Error occurred while user creation.
AM_MSG_loginIDExists= This login id is already exists.  
AM_MSG_userPending=Already pending for approval.
AM_MSG_userApproved =User has been successfully approved.
AM_MSG_errorUserApprove=Previous request is pending.
AM_MSG_userRejectByApprover= User has been rejected by approver.
AM_MSG_userUnlock=User successfully unlocked. 
AM_MSG_userUnlockEmailFailed=User successfully unlocked but failed to send mail.  
AM_MSG_errorUserUnlock=Error while user unlock process.
AM_MSG_errorUserActivation=Error while user activation process
AM_MSG_userActivationPending=User account activation request sent for approval.
AM_MSG_errorUserDeactivation=Error while user deactivation process.
AM_MSG_userDeactivationPending=User account deletion request sent for approval.
AM_MSG_userDeactivated=User account has been successfully deleted.
AM_MSG_userActivated=User account has been successfully activated.
AM_MSG_userRejDeactivated=User account deletion request has been successfully rejected.
AM_MSG_userRejActivated=User account activate request has been successfully rejected.
AM_MSG_userLocked=User account has been Blocked successfully.
AM_MSG_userUnLocked=User account has been Unblocked successfully.
AM_MSG_userRejLocked=User account block request has been successfully rejected.
AM_MSG_userRejUnLocked=User account unblock request has been successfully rejected.

#AM_MSG_userResetPwd=Password is reset for user.
AM_MSG_userResetPwd=Password reset successful for the user. Kindly check your mailbox for new login credentials.
AM_MSG_EmailSentSuccess= Kindly check your mailbox for new login credentials.

AM_MSG_userResetPwdEmailFailed=Password is reset for user but mail sending failed
AM_MSG_errorUserResetPwd=Error while reseting user password
AM_MSG_userRoleMap= User to role has been successfully mapped
AM_MSG_errorUserRoleMap=User to role has not been mapped
AM_MSG_pendingUserRoleMap=Role assigned successfully and request sent for approval.
AM_MSG_userRoleApprove=User to role has been successfully approved
AM_MSG_rejectUserRole=User to role has been rejected by approver
AM_MSG_errorRejectUserRole=Error while user to role map approval process
AM_MSG_UserApprovedMailSendingFail=User approved successfully.
AM_MSG_chnagePasswordEmpty=Please enter all the mandatory fields
AM_MSG_oldNewPwd=Old password and New password cannot be same
AM.MSG.formValidationFailed=Form validation failed.
AM_MSG_sessionexists=User already logged in.
am_msg_requestNotPending=Request not pending for approval.
AM_MSG_opFail = Operation failed
RP_LBL_Back=Back
#Role
AM_MSG_duplicateRoleName=Role name already exists.
AM_MSG_currRequestApproved=Current request has been successfully approved.
AM_MSG_currRequestFailed=Current request has been failed.
AM_MSG_noMappingChange=No change in mapping.
AM_MGS_reqSentForApproval= Request submitted for approval.
AM_MSG_errUserActivation=Error while user activation process.
AM_MSG_pendingApproval=Already pending for approval!
AM_MSG_OprationSuccess= Operation is successful.
AM_MSG_OprationFail=Failed to complete your request.
#Settlement
Uploadsettlement.vLVID.invalid=Please Enter a Valid LVID
Uploadsettlement.vMERCHANTID.invalid=Please Enter a Valid Merchant ID
Uploadsettlement.vBATCHID.Invalid=Please Enter a Valid Batch ID
Uploadsettlement.vSCHEMEID.Invalid=Please Enter a Valid Scheme ID
Uploadsettlement.vEFFTO.Invalid=Please Enter a Valid Date Range
Uploadsettlement.vPayID.Invalid=Please Enter a Valid Pay Type Id
Uploadsettlement.vRRN.Numbers=Please Enter Only Numbers.
Uploadsettlement.vRRN.Invalid=Please Enter a Valid RRN No.
Uploadsettlement.vRRN.DateRange.Invalid=Please Enter RRN or Date Range or Both.
Uploadsettlement.BalcSettledAmt.invalid=Please Enter a Valid Amount

##LOGIN PROCESSS
AM_MSG_adminLogin=Admin Login
AM_MSG_loginID=Login ID
AM_MSG_password=Password
AM_MSG_invalidCredentials=Please enter valid Login Id and Password
AM_MSG_accexpire=Your account is expired, please contact your administrator.
AM_MSG_notifyPwdDate1=Password Will be Expired In  
AM_MSG_notifyPwdDate2= days, Please Change Now.
AM_MSG_pwdChngSuccess=Password has been changed successfully .
AM_MSG_pwdChngFail=No of attempts of change password exceeded more than specified times
AM_MSG_acclock=Your account is locked, please contact your administrator.
AM_MSG_accInactive=Your account is not active, please contact your administrator.
AM_MSG_oldPwd=Invalid old password.


#AM_MSG_wrongOldPwd = Old password is not register with given login Id
AM_MSG_samePwd=Cannot reuse Old Password.
AM_MSG_defaultPwd=Cannot use default Password.
login.tempPasswordExpired=Temporary Password is expired. Please Contact Admin to Reset the Password
login.passwordExpired= Account Password is Expired
AM_MSG_pwdChngAlert= Reset Password Before it expires
AM_MSG_newAndConfirmPwdNotMatched=New Password and Confirm Password Should be same.
AM_MSG_samePwdAsLoginName=Password cannot be same as login name
AM_MSG_samePwdAsName=Password cannot be same as User Name
AM_MSG_pwdExpireMsg=Password has been expired.
AM_MSG_pwdlength=Password must be minimum 8 characters long
AM_MSG_pwdChngErrorOccurs=Error occurred during change password
AM_MSG_invalidPassword=Authentication failure
AM_MSG_userAleadyLogin=User is already logged in to the system
AM_MSG_invalidUsername=Authentication failure
AM_MSG_pwdChngEmailFailed=Password has been changed successfully
AM_MSG_pwdSpecialChar=Password must have at-least one Special Characters e.g @,#,$
AM_MSG_invalidNumPassword=Password must Contain at-least 1 numeric characters
AM_MSG_minAlphabet=Password must contains minimum 4 alphabet

AM_MSG_customPwdlength=Invalid password minimum length.
AM_MSG_invalidUcPassword=Password must contain upper-case characters.
AM_MSG_invalidLcPassword=Password must contain lower-case characters.

##ChangeSecurityQuestion
AM_MSG_secQueNotPresent=Secret question not present for provided user.
AM_MSG_chgPwdSecQueNtPresent=Security question has to be set for the user before changing password.
AM_MSG_uptSecQuesSuccess=Successfully updated secret question and answer.
AM_MSG_uptSecQuesErrorOccurs=Error occurred during updated secret question and answer.
AM_MSG_invalidSecAns=Invalid secret answer.

##Role To Functionality
AM_MSG_roleTofunctionalityLabel = Role To Functionality Mapping
AM_MSG_roleInformation = Role Information
AM_MSG_moduleName = Module Name
AM_MSG_availableFunctionality = Available Functionality
AM_MSG_assignedFunctionality = Assigned Functionality
AM_MSG_back = Back
AM_MSG_roleTofunctionalityError= Error while Role To Functionality Mapping
AM_MSG_roleAlreadyPending=Role already pending
AM_MSG_roleToFunctionalitySuccess=Role to Functionality map request sent for approval.
AM_MSG_roleDeactivated=Role has been successfully deleted.
AM_MSG_roleDeleteEdit=Role is deleted,Record can't be edited.
AM_MSG_roleAssigned=Role is assigned to the user, can't be deleted.
##common Label
msg.lbl.state = State 
msg.lbl.city = City 
msg.lbl.currency = Currency
msg.lbl.add = Add
msg.lbl.remove = Remove
msg.lbl.submit = Submit
msg.lbl.cancel = Cancel
msg.lbl.back = Back
msg.lbl.Clear = Clear
msg.lbl.select = --Select--
msg.lbl.default = Default
msg.lbl.verified = Verified
msg.lbl.rejected = Rejected
msg.lbl.notVeri = Not Verified
msg.lbl.entRem = Enter Remark
msg.lbl.CreCard = Credit Card
msg.lbl.debCard = Debit Card
msg.lbl.intBank = Internet Banking
msg.lbl.cug = CUG
msg.lbl.imps = IMPS
msg.lbl.all = All
msg.lbl.effFrom = Effective From
msg.lbl.effTo = Effective To
msg.lbl.addNew =  Add New
msg.lbl.loadTemp = Load Template
msg.lbl.bnkId = Bank Id
msg.lbl.percentage = Percent (%)
msg.lbl.yes = Yes
msg.lbl.no = No
msg.lbl.other = Other
msg.lbl.internet = Internet
msg.lbl.diwali = Diwali
msg.lbl.christmas = Christmas
msg.lbl.merchantOnBoarding=Merchant Onboarding
msg.lbl.home=Home
msg.lbl.paymentType=Payment Type 
msg.lbl.creditCard=Credit Card
msg.lbl.debitCard=Debit Card
msg.lbl.selectBank=Select Bank
msg.lbl.legalVehicle=Legal Vehicle
msg.lbl.addNewStore = Add New Store
msg.lbl.approved = Approved
msg.lbl.pending = Pending 
common.msg.lbl.active = Active
common.msg.lbl.inactive = Inactive
commom.msg.lbl.save = Save
msg.lbl.approve = Approve
msg.lbl.reject = Reject
msg.lbl.remark = Remark
msg.lbl.action = Action
msg.lbl.sendtoInit = Send Back to Initiator
msg.lbl.appRejEmpty= Please select an appropriate Action
##Merchant OnBoarding
## basicDetails.jsp
basicDetail.label.storeStatus = Store Status
##Acquiring Bank JSP
msg.lbl.acqBnkList = Acquiring Bank List
msg.lbl.hash = #
msg.lbl.bnkName = Bank Name
msg.lbl.bnkCode = Bank Code
msg.lbl.legalVhName = Legal Vehicle Name
msg.lbl.zone = Zone
msg.lbl.branch = Branch
msg.lbl.holdpay =  Hold Pay
msg.lbl.status = Status 
msg.lbl.Status = Status
msg.lbl.addNewBank = Add New Acquiring Bank
msg.lbl.mIDType = Merchant ID Type
## acquiringBankDetails
msg.lbl.acqBnkSetup = Acquiring Bank Setup  
msg.lbl.bankName = Bank Name 
msg.lbl.feeType = Fee Type
msg.lbl.pspType = PSP
msg.lbl.trnAmtIncl = Transaction amount inclusive
msg.lbl.holdPayment = Hold Payment
msg.lbl.cardAccp = Card acceptance
msg.lbl.feeSetup = Fee Setup
msg.lbl.settlementSetup = Settlement Setup
msg.lbl.oneTimeFixedFee = One Time Fixed Fee
msg.lbl.stmtFee = Statement Fee
msg.lbl.tmlFee = Terminal Fee
msg.lbl.minUseFeeDays = Minimum Usage(in Days)
msg.lbl.minTrnAmt = Minimum Transaction Amount
msg.lbl.minUseFee = Minimum Usage Fee
msg.lbl.amcFeetype = AMC Type
msg.lbl.amcFee = AMC Amount
msg.lbl.nonUseFeedays = Non Usage (in Days)
msg.lbl.nonUsageFee = Non Usage Fee
msg.lbl.settleType = Settlement Type:
msg.lbl.manual = Manual
msg.lbl.automatic = Automatic
msg.lbl.settleCycle = Settlement Cycle (If Automated)
msg.lbl.daily = Daily
msg.lbl.merpayCurr = Merchant Payment Currency
msg.lbl.merAccDetails = Merchant Account Details
msg.lbl.merBenDetails = Beneficiary Account Details
msg.lbl.twDaily = Twice Daily
msg.lbl.twDayOnce = Two Days Once
msg.lbl.weekly = Weekly
msg.lbl.parrSettle = Partial Settlement
msg.lbl.settleAgedDelivery = Settlement Against Delivery
msg.lbl.payAdv = Payment Advice
msg.lbl.payBy = Payment By
msg.lbl.accNum = Account Number
msg.lbl.accOpenDt = Account open date
msg.lbl.benBnkName = Beneficiary Bank Name
msg.lbl.benBankBrnch = Beneficiary Branch Name
msg.lbl.add1 = Address 1
msg.lbl.add2 = Address 2
msg.lbl.benAccName = Beneficiary Account Name 
msg.lbl.benAccNo = Beneficiary Account No
msg.lbl.benBnkCode = Beneficiary Bank Code
msg.lbl.benBrnName = Beneficiary Branch Code
msg.lbl.phn1 = Phone 
msg.lbl.phn2 = Mobile
msg.lbl.ifsc = IFSC Code
msg.lbl.miscellaneous = Miscellaneous
msg.lbl.dltrnLimit = Daily Transaction Limit
msg.lbl.trnMode = Transaction Mode
msg.lbl.applMode = Application Mode
msg.lbl.firc = FIRC
msg.lbl.fircFreq = FIRC Frequency
msg.lbl.fuelAssc = Fuel Association
msg.lbl.fuelRem = Fuel Remark
msg.lbl.callCharges = Call Charges
msg.lbl.docReq = Document Required
msg.lbl.docPen = Document Pending
msg.lbl.merReimburse = Merchant Reimbursement
msg.lbl.custId = Customer Id
msg.lbl.midSetUp = MID Setup
msg.lbl.mid = MID
msg.lbl.tid = TID
msg.lbl.supCurrency = Supported Currency
msg.lbl.monthly = Monthly
msg.lbl.secretKey=Secret Key
msg.lbl.Domestic=Domestic
msg.lbl.International=International
msg.lbl.international=International
msg.lbl.acqbankRemoved = Acquiring bank is removed successfully
##merchant Rating
msg.lbl.merRatManCal = Merchant Rating Manual Calculate
msg.lbl.option = Options
msg.lbl.criPoints = Criteria Points
msg.lbl.constitution = Constitution
msg.lbl.vintage = Vintage
msg.lbl.premises = Premises
msg.lbl.evPerMon = Expected volume per months
msg.lbl.avgtckSize = Average Ticket Size
msg.lbl.delTime = Delivery Timeline
msg.lbl.loc  = Location
msg.lbl.financial = Financial
msg.lbl.merCat = Merchant Category
msg.lbl.colCov = Collateral coverage
msg.lbl.merOPsSecu = Merchant OPS and Security
msg.lbl.vmtsMatVeri = VMTS /MATCH Verification
msg.lbl.vmts = VMTS
msg.lbl.match = MATCH
msg.lbl.cibil = Cibil
msg.lbl.postFacto = POST FACTO
msg.lbl.waiver = Waiver
msg.lbl.fixed = Fixed
msg.lbl.per = Per(%)
msg.lbl.merWebSec=Web Security
##merchantServiceFeeDetails
msg.lbl.msfConveFeeSetup = MSF / Convenience Fee Setup
smg.lbl.conFee = Convenience Fee
msg.lbl.msf = MSF
msg.lbl.acqBankName = Acquiring Bank Name
msg.lbl.scheme = Scheme 
msg.lbl.slbUpto = Slab Upto
msg.lbl.intlFixed = International Fixed
msg.lbl.intFixedPer = International Per(%)
msg.lbl.domOnFix = Domestic Onus Fixed
msg.lbl.domOnPer = Domestic Onus Per(%) 
msg.lbl.domOffFix = Domestic Offus Fixed
msg.lbl.domOffPer = Domestic Offus Per(%)
msg.lbl.merId = Merchant Id
msg.lbl.schemeId = Scheme
msg.lbl.schemeName = Scheme Name
msg.lbl.inter = International
msg.lbl.domestic = Domestic
msg.lbl.onus = Onus 
msg.lbl.offus = Offus
##blacklist.jsp
msg.lbl.blackListSetup = Blacklist / Whitelist BIN
msg.lbl.fromBin = From Bin
msg.lbl.toBin = To Bin
msg.lbl.listType = List Type
msg.lbl.whiteList = White List
msg.lbl.blackList = Black List
msg.msg.blListDetail = Black List Bin Detail
msg.lbl.noList = No BlackList or WhiteList Defined
##ipgDetails.jsp
msg.lbl.eciSetup = IPG Configuration
msg.lbl.eciValue = ECI Value
msg.lbl.capCardDetails = Capture Card details
msg.lbl.merCardDetails = Merchant Capture card detail
msg.lbl.pciCertNum = PCI DSS Certificate Number
msg.lbl.expiryDate = PCI DSS Expiry Date
msg.lbl.api = API
msg.lbl.apiSelect = Select API
msg.lbl.queryUrl = API URLs
msg.lbl.queryUrl1 = Collect Request(Inbound)
msg.lbl.queryUrl2 = Collect Response(Outbound)
msg.lbl.queryUrl3 = Query(Inbound)
msg.lbl.queryUrl4 = Refund Request(Inbound)
msg.lbl.queryUrl5 = Optional URL
msg.lbl.queryUrl6 = Query API URL 6
msg.lbl.queryUrl7 = Query API URL 7
msg.lbl.queryUrl8 = Query API URL 8
msg.lbl.queryUrl9 = Query API URL 9
msg.lbl.queryUrl10 = Query API URL 10
msg.lbl.eciValueVisa = ECI Value (VISA)
msg.lbl.eciValueMaster = ECI Value (MASTER)
msg.lbl.eciValueMaestro = ECI Value (MAESTRO)
##merchantUrl.jsp
msg.lbl.url = URL's
msg.lbl.message = Just help us with the URLs for the policy pages mentioned below to make your application full proof. The bank reviews your application based on these policies. Hence, it is advised to have a separate page for each of them.
msg.lbl.webOnInt = Is your E-Commerce website already on the Internet?
msg.lbl.merWebUrl = Merchant Website URL
msg.lbl.privPolicy = Privacy Policy
msg.lbl.canRefPoli = Cancellation/ Refund Policy
msg.lbl.termConPoli = Terms and Conditions Policy
msg.lbl.contactUs = Contact Us Information, with a helpdesk phone number
msg.lbl.faqUrl = FAQ's Page URL
msg.lbl.prodWithPrice = A page that shows any one  product along with its price
msg.lbl.aboutUs = About Us
msg.lbl.webManage = How is the website managed?
msg.lbl.comOwnWeb = A page that clearly shows which company owns your website
msg.lbl.outSolHost = Outsourced for Solution + Hosting
msg.lbl.solWeb = Which solution is the website on?
msg.lbl.eComStrat = What is your e-commerce strategy?
msg.lbl.eComTarget = What is your e-commerce target?
msg.lbl.utility = Utility
msg.lbl.travel = Travel
msg.lbl.education = Education Services
msg.lbl.retail = Retail
msg.lbl.chariSoc = Charitable and Social Service Organizations 
msg.lbl.hotMotRes = Hotels,Motels & Resorts
msg.lbl.plsSpecify = Please Specify?
##uploadDoc.jsp
msg.lbl.uploadDoc = Upload documents
msg.lbl.date = Date
msg.lbl.message1 = This step will ensure faster verification of your company and promoters leading to a faster approval.
msg.lbl.item = Item
msg.lbl.resRem = Results/Remarks
msg.lbl.upload = Upload File
msg.lbl.accept= Accept
msg.lbl.deny= Deny
msg.lbl.upload=Upload
msg.lbl.uploadFailed=Failed To Upload File
msg.lbl.uploadSuccess=Files Successfully Uploaded
msg.lbl.fileFormatNotSupported=File Format not supported
msg.lbl.fileIsEmpty=File is empty
msg.lbl.fileValidationFail=File Format not supported for files:
msg.lbl.uploadError=Error Occurred while Uploading
msg.lbl.fileNotUploaded=File Not Uploaded
msg.lbl.fileName=File Name
##paymentSummary.jsp
msg.lbl.paySumm = Website Summary
msg.lbl.message2 = Please provide a brief summary of goods or services that you sell?
msg.lbl.countCust = The Countries where your customers are:
msg.lbl.intCard = Do you need international card acceptance? If yes, please briefly explain why you need it
msg.lbl.cuuExpecVol = What is the current or expected payment volume split (%) across payment channels? 
msg.lbl.mailTelOrd = Mail/Telephone order
msg.lbl.posCreCard = POS Credit Card
msg.lbl.posDebCard = POS Debit Card
msg.lbl.curExpBus = What is the current or expected payment volume split (%) across Business channels?
msg.lbl.b2b = %B2B
msg.lbl.b2c = %B2C
msg.lbl.onlPay = Do you currently accept Online Payments?
msg.lbl.procBank = If yes, provide your processor bank`s or payment gateways Name
msg.lbl.processor = How long have you been with this processor?
msg.lbl.sixMon = 6 Months
msg.lbl.oneYear = 1 Year
msg.lbl.twoYear = 2 Year
msg.lbl.threeYear = 3 Year
msg.lbl.fourYear = 4 Year
msg.lbl.fiveYear = 5 Year
msg.lbl.annualTurn = Annual Online Turnover
msg.lbl.annualTurnOffline = Annual Offline Turnover
msg.lbl.avgTcktSize = Average Ticket Size
msg.lbl.minTcktSize = Minimum Ticket Size
msg.lbl.maxTcktSize = Maximum Ticket Size
msg.lbl.goodServices = How long from the time of payment are the good/services delivered across the following time frames?
msg.lbl.zeroDays =  0 days
msg.lbl.oneThreeDay =  1-3 days 
msg.lbl.fourSevenDays =  4-7 days
msg.lbl.eightDays =  8-14 days 
msg.lbl.fifteenDays = 15-30 days
msg.lbl.askDeposit = Do you ask your customers for a deposit before you deliver the goods/services?
msg.lbl.recurTran = Do any of your transaction involve automatic renewals or recurring transactions?
msg.lbl.refPolicy = What is your refund policy?
msg.lbl.fullRef = Full refund
msg.lbl.excOnly = Exchange only
msg.lbl.noRef = No refund
msg.lbl.processRef = Within how many days do you process a refund?
msg.lbl.zeroThree = 0-3 days
msg.lbl.fourSeven = 4-7 days
msg.lbl.eightFourteen = 8-14 days
msg.lbl.overFourteen = Over 14 days
msg.lbl.riskCheck = Do you perform any risk checks during your customer registration or sign up flow?
msg.lbl.mailConfirm = Do you send an email confirmation to your customer for every transaction?
msg.lbl.turnArTime = How much time do you have to react between the	online payment and delivering your product / service? (What is your turn around time on risk alerts?)?
msg.lbl.criBusPeriod = What are your critical business periods? 
msg.lbl.easter = Easter
msg.lbl.summerSales = Summer Sales 
msg.lbl.trnHIstory = Transaction History
msg.lbl.lastMon = Last month
msg.lbl.twoMonths = 2 months ago
msg.lbl.threeMonths = 3 months ago
msg.lbl.fourMonths = 4 months ago
msg.lbl.fiveMonths = 5 months ago
msg.lbl.sixMonths = 6 months ago
#acquiringBankRule
msg.lbl.acquiringRuleConfiguration=Acquiring Rule Configuration 
msg.lbl.netbannk =NetBank
msg.lbl.effectiveTimeFrom =Effective Time From 
msg.lbl.effectiveTimeTo =Effective Time To 
msg.lbl.priority=Priority 
msg.lbl.addAcqBkRule =Add Acquiring Bank Rule
#PaymentType
msg.lbl.addPaytypeSuccess=Payment Details updated successfully
msg.lbl.addPaytypeFail=Payment Type Details updation failed
msg.lbl.acquiringBankList=Acquiring Bank List
msg.lbl.international=International
msg.lbl.effectiveDate=Effective Date 
msg.lbl.sortOrder=Sort Order 
msg.lbl.addNewAcquiringBank=Add New Acquiring Bank
msg.lbl.sequence=Sequence
msg.lbl.mmid=MMID
msg.lbl.emi=EMI
msg.lbl.ipg=IPG
msg.lbl.recurringPaymentsUpload=Recurring Payments Upload
msg.lbl.recurringPaymentsOnline=Recurring Payments Online
msg.lbl.preAuthCompletion=Pre-Auth Completion
msg.lbl.charityPayments=Charity Payments
msg.lbl.loadReload=  Load/ReLoad
msg.lbl.tipAdjustment=  Tip Adjustment
msg.lbl.balanceInquiry=  Balance Enquiry
msg.lbl.cash=Cash
msg.lbl.pos=POS
msg.lbl.convenienceFee= Convenience fee
msg.lbl.internetBanking=Internet Bank
msg.lbl.mobileNumber=Mobile Number
msg.lbl.productType=Product Type
msg.lbl.preAuth=Pre-Auth
msg.lbl.nbMeSpecificId= Net Bank Account Id
### merchantCheckList.jsp
msg.lbl.checkList = Check List
msg.lbl.merchantName = Name of the Merchant
msg.lbl.address = Address
msg.lbl.payMode = Payment Mode
msg.lbl.borderTrn = Cross border transactions
msg.lbl.secure = 3D secure
msg.lbl.domUsTrnLimit = Domestic User Transaction Limits
msg.lbl.secTrnLimit = Secured Cross border Limit
msg.lbl.unSecTrnLimit = Unsecured transactions Limit
msg.lbl.chgReco = Charge backs recover
msg.lbl.instant = Instant
msg.lbl.crystall = Crystallization
msg.lbl.holdPay = Payment Hold over
msg.lbl.merCheckList = Merchant Check List
msg.lbl.merBussType = Merchant Business Type
msg.lbl.mcc = MCC
msg.lbl.awlmcc = AWL MCC
msg.lbl.existRelation = Existing/New relationship
msg.lbl.existing = Existing
msg.lbl.newRelation = New Relationship
##merchantBoardController
msg.lbl.addStoreSuccess = Store added successfully,please fill other tabs to continue.
msg.lbl.addStoreFail = Error occurred while creating Store.
msg.lbl.addAcqBankSuccess = Acquiring bank inserted successfully.
msg.lbl.addAcqBankFail = Error occurred while creating acquiring bank.
msg.lbl.midValiFail = MID validation failed,please enter valid MID .
msg.lbl.addMerRateSuccess = Merchant rating form submitted successfully .
msg.lbl.addMerRateFail = Error occurred while submitting merchant rating form.
msg.lbl.addBinListSuccess = Bin list has been successfully updated.
msg.lbl.addbinListFail = Error occurred while submitting bin list.
msg.lbl.addIPGInsertSuccess = IPG details have been successfully submitted.
msg.lbl.addIPGInsertFail = Error occurred while submitting IPG details.
msg.lbl.addMerUrlSuccess = Merchant URLs has been successfully submitted.
msg.lbl.addMerUrlFail = Error occurred while submitting merchant URLs. 
msg.lbl.addPaySumSuccess = WebSite summary details have been successfully submitted.
msg.lbl.addPaySumFail = Error occurred while submitting WebSite summary details. 
msg.lbl.addGroupSuccess = Group details have been successfully submitted.
msg.lbl.addGroupFail = Error occurred while submitting group details.
msg.lbl.addDocSuccess = Document details have been successfully submitted.
msg.lbl.addDocFail = Error occurred while submitting document details. 
msg.lbl.addCheckListSuccess = Check list inserted successfully.
msg.lbl.addCheckListFail = Error occurred while inserting check list. 
msg.lbl.appRejSuccess = Operation has been successfully completed.
msg.lbl.appRejReject = Operation has been rejected.
msg.lbl.appRejFail = Error occurred while executing operation.
msg.lbl.appRejSuccessBus = Business Approval has been successfully completed.
msg.lbl.appRejRejectBus = Business Approval has been rejected.
msg.lbl.appRejFailBus = Error occurred while executing operation.
msg.lbl.appMSFSuccess = Merchant service fee details inserted successfully.
msg.lbl.appMSFFail = Error occurred while executing operation.
msg.lbl.appEditSuccess = Store details updated successfully.
msg.lbl.appEditFail = Error occurred while updating store details.
msg.lbl.appEditReject = Store details Request has been rejected.
msg.lbl.appSourceSuccess = Store details inserted successfully.
msg.lbl.appSourceFail = Error occurred while inserting store details.
msg.lbl.initApprovSuccess = Store has been successfully initiated for approval.
msg.lbl.initDeactiveApprovSuccess = Store has been successfully initiated for Deactivation approval.
msg.lbl.initApprovFail = Error occurred while initiating approval process.
msg.lbl.addAcqBnkDet = Please fill acquiring bank details first.
msg.lbl.addMidDet = Please fill mid setup first.
msg.lbl.addPayDet = Please fill pay type first. 
msg.lbl.acquirerbank =Acquirer Bank 
msg.lbl.lvId=Legal Vehicle ID
msg.lbl.lvName=Legal Vehicle 
msg.lbl.rulId=RuleId
msg.lbl.view=View
msg.lbl.save=Save 
msg.lbl.updateAcqBnkRulSuccess=Acquiring Bank Rule details updated successfully
msg.lbl.updateAcqBnkRulFail=Error occurred while updating AcquiringBankRule Details
msg.lbl.emailExists = Contact EmailID already exists . Please enter a new EmailID.
msg.lbl.opsApproved = The request has already been performed by Operations
msg.lbl.busApproved = The request has already been performed by Business
msg.lbl.riskApproved = The request has already been performed by Risk
#Basicdetails
msg.lbl.storeType=Store Type
msg.lbl.chnCode=Channel Code
msg.lbl.storeStatus=Store Status
msg.lbl.crtBy=Created By : 
msg.lbl.crtDate=Created Date :
msg.lbl.msfRating:MSF Rating : 0.0 %
msg.lbl.busApp=Business Approval : Pending
msg.lbl.opsApp=OPS Approval : Pending
msg.lbl.rsikApp=Risk Approval : Pending
msg.lbl.strInfo=Store Information
msg.lbl.strType=Store Type 
msg.lbl.phyStr=Physical Store 
msg.lbl.webStr=Web Store
msg.lbl.channel=Channel
msg.lbl.strName=Store Name (Business Name) 
msg.lbl.basicDetails=Basic Details
msg.lbl.panNo=Pan No.
msg.lbl.storeExists = Store Name already exists . Please enter a new Store Name.
msg.lbl.fetchDetNsdl=Fetch details from NSDL
msg.lbl.awlMcc=AWL MCC  
msg.lbl.visaMcc=VISA MCC 
msg.lbl.masterMCC=MASTER MCC  
msg.lbl.premisesType=Premises Type
msg.lbl.vintageType=Vintage Type 
msg.lbl.merchantBusinessType=Merchant Business Type  
msg.lbl.merchantWebsitUrl=Merchant Website URL 
msg.lbl.regDate=Registration Date  
msg.lbl.appNo=Application No 
msg.lbl.cid=Corporate Identification Number 
msg.lbl.yib=Years in Business 
msg.lbl.closestCompet=Who is your closest competitor? 
msg.lbl.noOfShops=How many shops do you have in the country? 
msg.lbl.expVolPerMon=Expected Volume per month *
msg.lbl.howDoUDefBuss =How do you define your Business *
msg.lbl.fastGrowNat=Fast Growing  Mature
msg.lbl.strAdd=Store Address
msg.lbl.address1=Address 1 
msg.lbl.address2=Address 2 
msg.lbl.address3=Address 3 
msg.lbl.country=Country 
msg.lbl.locationId=Location Id 
msg.lbl.region=Region 
msg.lbl.subRegionSub-Region 
msg.lbl.pincode=Zip Code 
msg.lbl.cntPerDet=Contact Person Details
msg.lbl.name=Name 
msg.lbl.phone=Phone 
msg.lbl.mobile=Mobile 
msg.lbl.fax=Fax 
msg.lbl.emailId=email id 
msg.lbl.billAddDet=Billing Address Detail   Same as above
msg.lbl.relMaDet=Relationship Manager Detail
msg.lbl.rmName=RM Name 
msg.lbl.rmMobNum=RM Mobile Number 
msg.lbl.rmEmailId=RM email ID 
msg.lbl.misDet=Miscellaneous Details
msg.lbl.agreDet=Agreement Date 
msg.lbl.agreExp=Agreement Expiry Date 
msg.lbl.agerN=Agreement No 
msg.lbl.merCla=Merchant Classification 
msg.lbl.merSeg=Merchant Segment 
msg.lbl.braOffDes=Branch Official Designation 
msg.lbl.braOffMob=Branch Official Mobile No 
msg.lbl.braOffName=Branch Official Name 
msg.lbl.braSolId=Branch SOL ID 
msg.lbl.braStaffName=Branch Staff Name 
msg.lbl.braStaffPfi=Branch Staff PFI 
msg.lbl.braStaffSv=Branch Staff SV
RP_LBL_GetCustDetails=Customer Details
#Issuer
sm.msg.issuerBankName.required=Please enter the Bank Name
sm.msg.issuerBankCode.required=Please enter the Bank Code
sm.msg.issuerBankName.invalid=Bank Name must contain alphabets
sm.msg.issuerBankName.leninvalid=Bank Name length should not exceed 50 characters long
sm.msg.issuerBankCode.invalid=Bank Code must contain alphanumeric characters
sm.msg.issuerBankCode.leninvalid=Bank Code length should not exceed 10 characters long
sm.msg.frmBinNo.invalid=Bin Value must contain numbers 
sm.msg.frmBinNo.leninvalid=Bin value length should not exceed 6 characters long
sm.msg.toBinNo.invalid=Bin Value must contain numbers 
sm.msg.toBinNo.leninvalid=Bin value length should not exceed 6 characters long
sm.msg.binType.invalid=Please select the Bin Type
sm.msg.cardType.invalid=Please select the Card Type
sm.msg.schemeID.invalid=Please select the Scheme
sm.msg.schemeAppFlag.invalid=Please select the Scheme status
sm.msg.prevCardType.invalid=Please select the Previous Card Type
sm.msg.onusFlag.invalid=Please select the Onus status
sm.msg.countryCode.invalid=Please select the Country
sm.lbl.issuerBankName=Bank Name
sm.lbl.issuerCountry=Country
sm.lbl.issuerBankCode=Bank Code
sm.lbl.submit=Submit
sm.lbl.back=Back
sm.lbl.requestID=Request ID
sm.lbl.requestName=Request Name
sm.lbl.custname =Customer Name
sm.lbl.requestType=Request Type
sm.lbl.requestDate=Request Date
sm.lbl.requestStatus=Request Status
sm.lbl.requestBy=Request By
sm.lbl.approveReject=Approve/Reject:
sm.lbl.remarks=Remarks
sm.lbl.communicationAddress=Communication Address
sm.lbl.issuerID=Issuer ID
sm.lbl.issuer=Issuer
sm.lbl.approval=Approval
sm.lbl.addIssuer=Add Issuer
sm.lbl.action=Action
sm.lbl.view=View
sm.lbl.edit=Edit
sm.lbl.issuerRegistration=Issuer Registration
sm.msg.pendingIssuer= Issuer has been successfully created and pending for approval
sm.msg.pendingUpdatedIssuer= Issuer has been successfully updated and pending for approval
sm.msg.errorUpdateIssuer=Error occurred while Issuer update 
sm.msg.errorEditIssuer=There are no fields that have been edited 
sm.msg.errorIssuer=Error occurred while Issuer creation 
sm.msg.issuerPending=Already pending for approval
sm.msg.issuerApproved =Issuer has been successfully approved
sm.msg.errorIssuerApprove=Error while issuer approval process
sm.msg.errorIssuerReject=Error while issuer reject process
sm.msg.issuerRejectByApprover= Issuer has been rejected by approver
sm.msg.issuerToBinApproved = Bin has been mapped to the Issuer
sm.msg.issuerToBinUnsuccessful = Bin has not been mapped to the Issuer
sm.msg.issuerActivationPending=Issuer has been successfully activated
sm.msg.errorIssuerDeactivation=Error while issuer deactivate process
sm.msg.issuerDeactivationPending=The request has been sent for approval for Deactivation

#Scheme
sm.lbl.schemeRegistration=Scheme Registration
sm.lbl.schemeName=Scheme Name
sm.lbl.schemeType=Scheme Type
sm.lbl.schemeID=Scheme ID
sm.lbl.scheme=Scheme
sm.lbl.addScheme=Add Scheme
sm.lbl.schemeCode=Scheme Code
sm.msg.schemeName.required=Please enter scheme name
sm.msg.schemeName.invalid = Scheme Name must contain alphabets
sm.msg.schemeName.leninvalid=Scheme Name length should not exceed 20 characters long
sm.msg.schemeCode.required=Please enter Scheme Code
sm.msg.schemeCode.invalid = Scheme Code must contain alphabets
sm.msg.schemeCode.leninvalid=Scheme Code length should not exceed 5 characters long
sm.msg.pendingScheme= Scheme has been successfully created and pending for approval
sm.msg.pendingUpdatedScheme= Scheme has been successfully updated and pending for approval
sm.msg.errorUpdateScheme=Error occurred while Scheme update 
sm.msg.errorScheme=Error occurred while scheme creation 
sm.msg.schemePending=Already pending for approval
sm.msg.schemeApproved =Scheme has been successfully approved
sm.msg.errorSchemeApprove=Error while scheme approval process
sm.msg.errorSchemeReject=Error while scheme reject process
sm.msg.schemeRejectByApprover= Scheme has been rejected by approver
sm.msg.schemeActivationPending=The request has been sent for approval for Activation
sm.msg.errorSchemeDeactivation=Error while issuer deactivate process
sm.msg.schemeDeactivationPending=The request has been sent for approval for Deactivation
sm.msg.duplicateBinValue = Bin Value already exist for another Issuer
#Acquirer
sm.lbl.aa=ADD ACQUIRER
sm.lbl.bsfInfo=BSF Information
sm.lbl.editAcquirer=EDIT ACQUIRER
sm.lbl.addBsf=ADD BSF
sm.lbl.editBsf=EDIT BSF
sm.lbl.addAcquirer=Add Acquirer
sm.lbl.deactactAcq=DEACTIVATE/ACTIVATE ACQUIRER
sm.msg.acquirerBankName.required=Please enter the Bank Name
sm.msg.acquirerBankCode.required= Please enter the Bank Code
sm.msg.dailyTotTcktLimit.required= Please enter the Daily Ticket Limit
sm.msg.dailyTotAmtLimit.required= Please enter the Daily Amount Limit
sm.msg.perTranLimit.required= Please enter per Transaction Limit Amount
sm.msg.acquirermidType.required= Please enter the Super Merchant ID
sm.msg.acquirerAuthnEngine.required= Please enter the Authentication Engine
sm.msg.acquirerAuthzType.required= Please enter the Authorization Type
sm.msg.acquirerAuthzEngine.required= Please enter the Authorization Engine
sm.msg.acquirerUsrName.required= Please enter the Bank User Name
sm.msg.acquirerAnnCharges.required= Please enter the Annual Charges
sm.lbl.dccAcceptFlag=DCC Accept Flag
sm.lbl.emptyTable=No Data Found
sm.lbl.acquirerBsf=Acquirer BSF
sm.lbl.acquirer=Acquirer
sm.lbl.acqScheme=Acquirer to Scheme Mapping
sm.lbl.acquirerBSF=BSF Details
sm.lbl.channelsAvailable=Channels Available
sm.lbl.acquirerBankBasicDetails=Acquiring Bank Basic Details
sm.lbl.bsfSetup=BSF Setup
sm.lbl.acquirerInformation=Acquirer Information
sm.lbl.channelSelected=Channels Selected
sm.lbl.channel=Channel 
sm.lbl.payment=Payment Type
#28th-march  --start
sm.lbl.authnType=Authentication Type
sm.lbl.authnEngine=Authentication Engine
sm.lbl.authzType=Authorization Type
sm.lbl.authzEngine=Authorization Engine
sm.lbl.accessCode=Access Code
sm.lbl.secureSecret=Secure Secret
sm.lbl.authnUrl=Authentication URL
sm.lbl.authzUrl=Authorization URL
#--end
sm.lbl.remove=Remove
sm.lbl.acquirerMaster=Acquirer Master
sm.lbl.acquirerList=Acquirer List
sm.lbl.acquirerMasterTable=Acquirer Master Table
sm.lbl.schemeMapping=Scheme Mapping
sm.lbl.aggregatorBank=Aggregator Bank
sm.lbl.hostingBank=Hosting Bank
sm.lbl.partneringBank=Partnering Bank
sm.lbl.nonPartneringBank=Non-Partnering Bank
sm.lbl.superMerchantID=Super Merchant ID
sm.lbl.subMerchantID=Sub Merchant ID
sm.lbl.acquirerID=Acquirer Bank ID
sm.lbl.effectiveFrom=Effective From
sm.lbl.effectiveTo=Effective To
#sm.lbl.bankType= Bank Type
sm.lbl.bankType= PSP Type
sm.lbl.acquirerBankFor = Acquirer Bank For
sm.lbl.acquirerBankType = Acquirer Bank Type
sm.lbl.partnerLVID = Partner LV ID
sm.lbl.acqUserName = Acquirer Bank User Name
sm.lbl.mIDType = Merchant ID Type
sm.lbl.mIDGenBy = Merchant ID Generated By
sm.lbl.sMID = Super Merchant MID
sm.lbl.stID = Super Merchant TID
sm.lbl.sMIDCurr = Super Merchant MID Currency Code
sm.lbl.sDCCMID =Super Merchant DCC MID
sm.lbl.sDCCTID =Super Merchant DCC TID
#sm.lbl.sDCCMIDCurr = Super Merchant DCC Currency Code
sm.lbl.sDCCMIDCurr = Super Merchant DCC Code
sm.lbl.bankAggFlag = Bank Aggregator Flag
sm.lbl.acqAnnChrgs = Annual Charges
sm.lbl.channelName=Channel Name
sm.lbl.paymentDesc=Payment Description
sm.lbl.autenType=Authentication Type
sm.lbl.autzType=Authorization Type
sm.lbl.authenEng=Authentication Engine
sm.lbl.authzEng=Authorization Engine
sm.lbl.secureSecrete=Secure Secrete
sm.lbl.acquirerBankName=PSP Name
sm.lbl.acquirerBankCode=Acquirer Bank Code
#sm.lbl.dailytottckt=Daily Ticket Size
sm.lbl.dailytottckt=Daily Transaction Count
#sm.lbl.dailyTotAmt=Daily Ticket Limit
sm.lbl.dailyTotAmt=Daily Transaction Volume
sm.lbl.perTran=Per Transaction Limit
sm.lbl.retryCnt=Maximum Retry Count
sm.lbl.settleFlag=Settlement Flag
sm.lbl.agg=Aggregator
sm.lbl.bnk=Bank
sm.lbl.hbi=Hosted Bank (INSIGHT)
sm.lbl.mid=MID Generation Not Required
sm.lbl.npb=Key In for Non Partner Bank
sm.lbl.awl=Settlement is done by AWL
sm.lbl.sbnk=Settlement done by Bank
sm.lbl.add=Add
sm.lbl.Request=Request Name

sm.lbl.slabUpto=Slab Upto
sm.lbl.internationalFixed=Fixed Amount
sm.lbl.internationalPer=Fixed Per(%)
sm.lbl.domOnusFixed=Domestic Onus Fixed
sm.lbl.domOnusPer=Domestic Onus Per(%)
sm.lbl.domOffusFixed=Domestic Offus Fixed
sm.lbl.domOffusPer=Domestic Offus Per(%)
#sm.lbl.domOffusFixed = Fixed
#sm.lbl.domOffusPer = Percentage (%)
sm.lbl.addNew=Add New
sm.lbl.bankName=Bank Name
sm.lbl.inter=International
sm.lbl.fixed=Fixed
sm.lbl.per=Per(%)
sm.lbl.domestic=Domestic
sm.lbl.onus=Onus
sm.lbl.offus=Offus
sm.lbl.effFrom=Effective From
sm.lbl.effTo=Effective To
sm.lbl.CreCard=Credit Card
sm.lbl.debCard=Debit Card
sm.lbl.cug=CUG
sm.lbl.percentage=Percent(%)
sm.lbl.bsfId=BSF ID
sm.msg.legalVehicle.invalid=Please select the Legal Vehicle
sm.msg.bankAggFlag.invalid=Please select the Bank Aggregator Flag 
sm.msg.midGenBy.invalid=Please select the Merchant Generated By
sm.msg.settleResFlag.invalid=Please select the Settlement Flag
sm.msg.acquirerBankName.invalid = Acquirer Bank Name must contain alphabets only
sm.msg.acquirerBankName.leninvalid=Acquirer Bank Name length should not exceed 20 characters long
sm.msg.acquirerBankCode.invalid = Acquirer Bank Code must contain only alphanumeric characters
sm.msg.acquirerBankCode.leninvalid=Acquirer Bank Code length should not exceed 20 characters long
sm.msg.retryCnt.invalid = Maximum Retry Count should contain only numeric characters
sm.msg.partnerLVID.invalid=Please select the Partner LV ID
sm.msg.sMID.invalid=Please enter the Super Merchant ID
sm.msg.sMID.invalidCharacter=Super Merchant ID should contain alphanumeric characters
sm.msg.sMIDCurr.invalid=Please select the currency of Sub Merchant ID
sm.msg.sDccMIDCurr.invalid=Please select the currency of Super Merchant DCC ID
sm.msg.sDccMID.invalidCharacter=Super Merchant DCC ID should contain alphanumeric characters
sm.msg.acquirerBankFor.invalid=Please select the Acquirer Bank For option
sm.msg.acquirerBankFlag.invalid=Please select the Acquirer Bank Flag option
sm.msg.midType.invalid=Please select the Merchant ID option
sm.msg.acquirerUserName.invalid=Acquirer User Name should contain only alphanumeric characters
sm.msg.acquirerUserName.leninvalid=Acquirer User Name length should not exceed 25 characters long
sm.msg.tableIPGValue.invalid=Please remove the IPG Channel Mapping from the table
sm.msg.tablePPOSValue.invalid=Please remove the PC POS Channel Mapping from the table
sm.msg.tablePOSValue.invalid=Please remove the POS Channel Mapping from the table
sm.msg.tableMPOSValue.invalid=Please remove the M POS Channel Mapping from the table
sm.msg.ipgValue.invalid = Please Map IPG to the Mapping table
sm.msg.pposValue.invalid=Please Map PC POS to the Mapping table
sm.msg.posValue.invalid=Please Map POS to the Mapping table
sm.msg.mposValue.invalid=Please Map M POS to the Mapping table
sm.msg.Channel.invalid=Please select a Channel
sm.msg.ChannelTable.invalid=Please select the Mappings
sm.msg.annChrg.invalid=Annual Charges should contain numeric characters or decimal values only
sm.msg.dailyTotTcktLimit.invalid=Daily Total Ticket Limit should contain numeric characters only
sm.msg.dailyTotAmtLimit.invalid=Daily Total Amount Limit should contain numeric characters or decimal values only
sm.msg.perTranLimit.invalid=Transaction Limit should contain numeric characters or decimal values only
sm.msg.acquirerPending=Already pending for approval
sm.msg.acquirerBsfPending=Already pending for approval
sm.msg.acquirerApproved =Acquirer has been successfully approved
sm.msg.errorAcquirerApprove=Error while Acquirer approval process
sm.msg.errorAcquirerReject=Error while Acquirer reject process
sm.msg.acquirerRejectByApprover= Acquirer has been rejected by approver
sm.msg.errorAcquirer=Error occurred while Acquirer creation 
sm.msg.pendingAcquirer= Acquirer has been successfully created and pending for approval
sm.msg.pendingUpdatedAcquirer= Acquirer has been successfully updated and pending for approval
sm.msg.errorUpdateAcquirer=Error occurred while Acquirer update 
sm.msg.duplicateMapping= Duplicate Mapping Found!!!
sm.msg.acquirerActivationPending=The request has been sent for approval for Activation
sm.msg.errorAcquirerDeactivation=Error while Acquirer deactivate process
sm.msg.acquirerDeactivationPending=The request has been sent for approval for Deactivation
sm.msg.errorEditAcquirer=There are no fields that have been edited 
sm.msg.bsfPending=Already pending for approval
sm.msg.bsfApproved =BSF has been successfully approved
sm.msg.errorBsfApprove=Error while BSF approval process
sm.msg.errorBsfReject=Error while BSF reject process
sm.msg.bsfRejectByApprover= BSF has been rejected by approver
sm.msg.errorBsf=Error occurred while BSF creation 
sm.msg.pendingBsf= BSF has been successfully created and pending for approval
sm.msg.pendingUpdatedBsf= BSF has been successfully updated and pending for approval
sm.msg.errorUpdateBsf=Error occurred while BSF update 
sm.msg.bsfActivationPending=The request has been sent for approval for Activation
sm.msg.errorBsfDeactivation=Error while BSF deactivate process
sm.msg.bsfDeactivationPending=The request has been sent for approval for Deactivation
sm.msg.errorEditBsf=There are no fields that have been edited
sm.msg.saveBsf= BSF has been saved successfully
sm.msg.errorApproveBsf = Please save the details before sending for approval 

#Legal Vehicle
sm.lbl.acquirerBnkRule=Acquirer Bank Rule
sm.lbl.ruleSetup=Aggregator Bank Rule Setup
sm.lbl.priority=Priority
sm.lbl.onusFlag=Onus Flag
sm.lbl.offusFlag=Offus Flag
sm.lbl.addLegalVehicle = Add PSP
sm.lbl.addLV = ADD LEGAL VEHICLE
sm.lbl.editLegalVehicle = EDIT LEGAL VEHICLE
sm.lbl.deactActLegalVehicle = ACTIVATE/DEACTIVATE LEGAL VEHICLE
sm.lbl.addNetBanking = ADD NET BANKING
sm.lbl.editNetBanking = EDIT NET BANKING
sm.lbl.addAcqBnkRule = ADD ACQUIRER BANK RULE
sm.lbl.editAcqBnkRule = EDIT ACQUIRER BANK RULE
sm.lbl.deactActNetBanking = ACTIVATE/DEACTIVATE NET BANKING
sm.lbl.lvMasterTable=Legal Vehicle Master
sm.lbl.lvList=PSP Config List
sm.lbl.aggModal=Aggregator Model
sm.lbl.hb=Hosted Bank Model
sm.lbl.legalVehicleID = PSP Config ID
sm.lbl.legalVehicle = PSP Config
sm.lbl.legalVehicleName = PSP Name
sm.lbl.legalVehicleCode = PSP Code
sm.lbl.legalVehicleType = PSP Type
sm.lbl.lvInfo=PSP Config Information
sm.lbl.LVType= PSP Type
sm.lbl.lvNetBank=Net Bank
sm.lbl.nbInfo=Net Banking Information
sm.lbl.netBankingDetails=Net Banking Details
sm.lbl.nbList=Net Banking List
sm.lbl.netBankMeId=Net Bank Merchant ID
sm.lbl.netBankName=Net Bank Name
sm.lbl.netBankCode=Net Bank Code
sm.lbl.netBankID=Net Bank ID
sm.lbl.trnurl=Transaction URL
sm.lbl.statusurl=Status URL
sm.lbl.nbKey=Net Banking Key
sm.lbl.partnerChnl=Partner Channel
sm.lbl.lvSch=Legal Vehicle to Scheme Mapping
sm.lbl.lvPay=Legal Vehicle to Payment Mapping
sm.lbl.lvPdt=Legal Vehicle to Product Mapping
sm.lbl.product=Product
sm.lbl.lvBasicDetails=PSP Basic Details
sm.lbl.addLvBsf = ADD BSF FOR LEGAL VEHICLE
sm.lbl.editLvBsf = EDIT BSF FOR LEGAL VEHICLE
sm.msg.legalVehicleType.invalid=Please select the PSP Type
sm.msg.legalVehicleName.required=Please enter the PSP Name
sm.msg.legalVehicleCode.required= Please enter the PSP Code
sm.msg.legalVehicleType.required= Please enter the PSP Type
sm.msg.legalVehicleName.invalid = PSP Code must contain alphabets
sm.msg.legalVehicleName.lenInvalid=PSP Code length should not exceed 50 characters long
sm.msg.legalVehicleCode.invalid = PSP Code must not contain numbers
sm.msg.legalVehicleCode.lenInvalid=Legal Vehicle Code length should not exceed 10 characters long
sm.msg.BankName.Invalid=Please Enter the Net Bank Name
sm.msg.BankName.lenInvalid=Net Bank Name length should not exceed 30 characters
sm.msg.BankCode.Invalid=Please Enter the Net Bank Code
sm.msg.NetBankMeId.Invalid=Please Enter the Net Bank Merchant ID
sm.msg.StatusUrl.Invalid=Invalid Status URL
sm.msg.NbKey.Invalid=Please Enter the Net Bank Key
sm.msg.partnerChild.Invalid=Please select the Partner Child
sm.msg.BankCode.lenInvalid=Net Bank Code length should not exceed 10 characters
sm.msg.NetBankMeId.lenInvalid=Merchant ID length should not 15 characters
sm.msg.TrnUrl.Invalid=Transaction URL is Invalid
sm.msg.TrnUrl.lenInvalid=Transaction URL length should not exceed 250 characters
sm.msg.StatusUrl.lenInvalid=Status URL length should not exceed 250 characters
sm.msg.NbKey.lenInvalid=Net Bank Key length should not exceed 50 characters
sm.msg.legalVehiclePending=Already pending for approval
#sm.msg.legalVehicleApproved =Legal Vehicle request approved successfully
sm.msg.addLegalVehicleApproved =PSP Config request approved successfully
sm.msg.editLegalVehicleApproved =PSP Config request approved successfully
sm.msg.errorLegalVehicleApprove=Error PSP Config approval process
sm.msg.errorLegalVehicleReject=Error while PSP Config reject process
sm.msg.addLegalVehicleRejectByApprover= PSP Config request rejected by approver
sm.msg.editLegalVehicleRejectByApprover=PSP Config request rejected by approver
sm.msg.errorLegalVehicle=Error occurred while PSP Config creation 
sm.msg.pendingLegalVehicle=PSP Config request submitted successfully
sm.msg.pendingUpdatedLegalVehicle=Edit PSP Config request submitted successfully
sm.msg.errorUpdateLegalVehicle=Error occurred while PSP Config update
sm.msg.legalVehicleActivationPending=The request has been sent for approval for Activation
sm.msg.errorLegalVehicleDeactivation=Error while PSP deactivate process
sm.msg.legalVehicleDeactivationPending=The request has been sent for approval for Deactivation
sm.msg.errorEditLegalVehicle=There are no fields that have been edited 
sm.msg.netBankingPending=Already pending for approval
sm.msg.netBankingApproved =Net Banking has been successfully approved
sm.msg.errorNetBankingApprove=Error while Net Banking approval process
sm.msg.errorNetBankingReject=Error while Net Banking reject process
sm.msg.netBankingRejectByApprover= Net Banking has been rejected by approver
sm.msg.errorNetBanking=Error occurred while Net Banking creation 
sm.msg.pendingNetBanking= Net Banking has been successfully created and pending for approval
sm.msg.pendingUpdatedNetBanking= Edit Net Banking request submitted successfully  
sm.msg.errorUpdateNetBanking=Error occurred while Net Banking update 
sm.msg.netBankingActivationPending=The request has been sent for approval for Activation
sm.msg.errorNetBankingDeactivation=Error while Net Banking deactivate process
sm.msg.netBankingDeactivationPending=The request has been sent for approval for Deactivation
sm.msg.errorEditNetBanking=There are no fields that have been edited
sm.msg.acqBnkRulePending=Already pending for approval
sm.msg.acqBnkRuleApproved =Acquirer Bank Rule has been successfully approved
sm.msg.errorAcqBnkRuleApprove=Error while Acquirer Bank Rule approval process
sm.msg.errorAcqBnkRuleReject=Error while Acquirer Bank Rule reject process
sm.msg.acqBnkRuleRejectByApprover= Acquirer Bank Rule has been rejected by approver
sm.msg.errorAcqBnkRule=Error occurred while Acquirer Bank Rule creation 
sm.msg.pendingAcqBnkRule= Acquirer Bank Rule has been successfully created and pending for approval
sm.msg.pendingUpdatedAcqBnkRule= Acquirer Bank Rule has been successfully updated and pending for approval
sm.msg.errorUpdateAcqBnkRule=Error occurred while Acquirer Bank Rule update 
sm.lbl.manageBsf=Manage BSF
sm.msg.netBankBsfRejectByApprover= Net Bank BSF has been rejected by approver
sm.msg.netBankBsfApproved =Net Bank BSF has been successfully approved

#EMI
sm.msg.duplicateRequest= Failed to process duplicate request.
sm.msg.emi.duplicateRequest=EMI program  already exists. Please enter a valid Program code.
sm.lbl.emiMaster=EMI Master
sm.lbl.addEmi=ADD EMI
sm.lbl.editEmi=EDIT EMI
sm.lbl.rejected=REJECTED
sm.lbl.requestInformation = Request Information
sm.lbl.processingFee=PROCESSING FEE
sm.lbl.interest=INTEREST
sm.lbl.pendingForApproval=PENDING FOR APPROVAL
sm.lbl.actdeactEmi=ACTIVATE/DEACTIVATE EMI
sm.lbl.emi = EMI	 
sm.lbl.approve = Approve
sm.lbl.reject = Reject
sm.lbl.emiBasicDetails = EMI Basic Details
sm.lbl.pgmID=Program ID
sm.lbl.pgmCode=Program Code
sm.lbl.pgmDesc=Program Description
sm.lbl.intRate=Interest Rate
sm.lbl.lvId= Legal Vehicle ID
sm.lbl.lvName=Legal Vehicle Name
#Modified by Prashant on 06.11.15
sm.lbl.procFeePer=Process Fee Percentage 
sm.lbl.procFeeFix=Process Fee Fix
sm.lbl.validFrom=Valid From
sm.lbl.validTo=Valid To
sm.lbl.taxMap=Tax Map
sm.lbl.taxCode=Tax Code
sm.lbl.taxFor=Tax For
sm.lbl.tenure=Tenure
sm.lbl.tenureMnths=Tenure in months
sm.lbl.programMap=Program Map
sm.lbl.issuerBank=Issuer Bank
sm.lbl.acquirerBank=Acquirer Bank
sm.lbl.merchantName=Merchant Name
sm.lbl.manufactName=Manufacturer Name
sm.lbl.prodName=Product Name
sm.lbl.allowBin=Bin Mapping for EMI Program
sm.lbl.emiMasterTable = EMI Master Table
sm.lbl.emiList=EMI List
sm.lbl.emiInfo=EMI Information
sm.lbl.binValFrom=From 
sm.lbl.binValTo=To 
sm.lbl.minAmt=Minimum Amount 
sm.lbl.maxAmt=Maximum Amount 
sm.lbl.emiInformation=EMI Information
sm.lbl.select=-----Select-----
sm.msg.emiPrgmCode.required=Please enter the Program Code
sm.msg.emiPrgmDesc.required=Please enter the Program Description
sm.msg.emiIntRate.required=Please enter the Interest Rate
sm.msg.emiPrgmCode.invalid=Program Code must contain only alphanumeric characters
sm.msg.emiPrgmCode.leninvalid=Program Code should not exceed 20 characters
sm.msg.emiIntRate.invalid=Interest Rate must contain only numerical characters
sm.msg.procFeePer.invalid=Processing Fee Per must contain only numerical characters
sm.msg.procFeeFix.invalid=Processing Fee Fix must contain only numerical characters
sm.msg.minAmt.invalid=Minimum Amount must contain only numerical characters
sm.msg.maxAmt.invalid=Maximum Amount must contain only numerical characters
sm.msg.maxminAmt.invalid=Maximum Amount should be greater than the Minimum Amount
sm.msg.emiPrgmDesc.invalid=Program Description must contain only alphanumeric characters
sm.msg.emiPrgmDesc.leninvalid=Program Description should not exceed 50 characters
sm.msg.validTo.invalid=Valid To Date should not be greater than the Valid From Date
sm.msg.date.invalid=Valid Date should not be empty
sm.msg.taxTable.invalid=Please Map the Taxes to the Program
sm.msg.tenureTable.invalid=Please Map the Tenure to the Program
sm.msg.pgmTable.invalid=Please Map the Programs
sm.msg.emiPending=Already pending for approval
sm.msg.emiApproved =EMI has been successfully approved
sm.msg.errorEmiApprove=Error while EMI approval process
sm.msg.errorEmiReject=Error while EMI reject process
sm.msg.emiRejectByApprover= EMI has been rejected by approver
sm.msg.errorEmi=Error occurred while EMI creation 
sm.msg.pendingEmi= EMI has been successfully created and pending for approval
sm.msg.pendingUpdatedEmi= EMI has been successfully updated and pending for approval
sm.msg.errorUpdateEmi=Error occurred while EMI update 
sm.msg.emiActivationPending=The request has been sent for approval for Activation
sm.msg.errorEmiDeactivation=Error while EMI deactivate process
sm.msg.emiDeactivationPending=The request has been sent for approval for Deactivation
sm.msg.errorEditEmi=There are no fields that have been edited
sm.msg.binMap=Bin has been mapped to the program successfully
sm.msg.errorBinMap=Bin has not been mapped to the program successfully
sm.msg.pendingLedgerAccount= Ledger Account has been successfully created and pending for approval
sm.msg.pendingUpdatedLedgerAccount= Ledger Account has been successfully updated and pending for approval
sm.msg.errorUpdateLedgerAccount=Error occurred while LedgerAccount update 
sm.msg.errorLedgerAccount=Error occurred while Ledger Account creation 
sm.msg.ledgerAccountPending=Already pending for approval
sm.msg.ledgerAccountApproved =Ledger Account has been successfully approved
sm.msg.errorLedgerAccountApprove=Error while Ledger Account approval process
sm.msg.errorLedgerAccountReject=Error while Ledger Account reject process
sm.msg.ledgerAccountRejectByApprover = Ledger Account has been rejected by approver
sm.msg.duplicateAccountNo= Account Number already exists in the Database
sm.msg.ledgerAccountActivationPending=The request has been sent for approval for Activation
sm.msg.errorLedgerAccountDeactivation=Error while Ledger Account deactivate process
sm.msg.ledgerAccountDeactivationPending=The request has been sent for approval for Deactivation
sm.lbl.ledgerAccountRegistration= Ledger Account Registration
sm.msg.accNo.required=Please enter Account Number
sm.msg.accNo.invalid=Account Number must contain only alphanumeric characters
sm.msg.accNo.empty=Account Number must not be empty
sm.msg.accNo.LenInvalid=Account Number length should not more than 18 characters long
sm.msg.accHead.required=Please enter Account Head
sm.msg.accHead.invalid=Account Head must contain only alphanumeric characters
sm.msg.accHead.empty=Account Head must not be empty
sm.msg.accHead.LenInvalid=Account Head length should not more than 10 characters long
sm.msg.accPurpose.required=Please enter Account Purpose
sm.msg.accPurpose.invalid=Account Purpose must contain only alphanumeric characters
sm.msg.accPurpose.empty=Account Purpose must not be empty
sm.msg.accPurpose.LenInvalid=Account Purpose length should not more than 50 characters long
sm.lbl.ledgerAccountID=Ledger Account ID
sm.lbl.ledgerAccountNumber=Ledger Account Number
sm.lbl.ledgerAccountHead=Account Head
sm.lbl.ledgerAccountPurpose=Account Purpose
sm.lbl.ledgerAccountRemarks=Remarks
sm.lbl.whatBalanceDenotes=What Balance Denotes
sm.lbl.Status=Status
sm.lbl.createdBy=Created By
sm.lbl.createdDate=Created Date
sm.lbl.modifiedBy=Last Modified By
sm.lbl.modifiedDate=Modified Date
sm.lbl.accFor = Account For
sm.lbl.accRefId = Account Refernce Number
sm.lbl.acqBank = Acquiring Bank
sm.lbl.netBank = Net Bank
sm.lbl.others = Others
sm.lbl.nodalAccnt = Nodal Account
sm.lbl.merchant = Merchant
sm.msg.recordExists= An Account Number already exists for this Account Reference Number

sm.lbl.emiDiscType=EMI Discount Type
sm.lbl.emiDiscPer=EMI Discount Percentage / Fixed
sm.lbl.emiAddDiscAmt=EMI Additional Discount Amount
sm.lbl.maxCapAmt=Max CAP Amount
sm.msg.vemidiscperamt.invalid= Invalid Percentage amount.
sm.msg.vemiaddldiscamt.invalid=Invalid amount
sm.msg.vemicapamt.invalid=Invalid amount
sm.msg.vemidisctype.invalid=Can't be empty
# >> End

#Tax
fm.msg.pendingTax=Tax Pending for Approval
fm.msg.pendingReq=Request Already Pending for Approval.
fm.msg.pendingUpdatedTax=Update Tax Pending for Approval
fm.msg.errorTax=Error Creating Tax
fm.msg.taxRejected=Tax has been Rejected Successfully.
fm.msg.taxApproved =Request has been Successfully Approved.
fm.msg.formValidationFailed=Form Validation Failed.
fm.msg.errorUpdateTax=Error Occured while Updaing Tax Information.
fm.msg.noChangeInData=You have not updated any data.
msg.lbl.nodal=Nodal
msg.lbl.nodalBankName=Bank Name
msg.lbl.nodalCountry=Country
msg.lbl.nodalBankCode=Bank Code
msg.lbl.addNodal=Add Nodal
msg.lbl.NODALACCID= Acc ID
msg.lbl.nodalACCNO= Acc No.
msg.lbl.nodalIFSCCode=IFSC Code
msg.lbl.nodalLVID=LVID
msg.lbl.nodalstatus=Current Status
addEditNodal.nodalBankCode.required=Please enter the Bank Code
addEditNodal.nodalBankName.required=Please enter the Bank Name
addEditNodal.nodalLVID.required=Please enter the LVID
addEditNodal.nodalACCNO.required=Please enter the ACC NO.
addEditNodal.nodalIFSCCODE.required=Please enter the IFSCCODE
msg.nodal.requestsuccess=Request has been sent successfully
msg.nodal.requestfail=Sorry Request sending failed
msg.nodalPending=Pending for approval
msg.nodal.approved=Request has been approved successfully
msg.nodal.notApproved=Failed to process the request
msg.nodal.addTemp= Request is sent for approval
msg.nodal.addTempFail=Sorry Request failed 
msg.nodal.rejected=Request rejected by approver

#Manual Ledger Posting//rajkiran
msg.lbl.manualLedgerTempName=Template Name
msg.lbl.manualLedgerTransCode=Transaction Code
msg.lbl.manualLedgerDebAcc=Debit Acc.
msg.lbl.manualLedgerCreAcc=Credit Acc.
msg.lbl.manualLedgerAmount= Amount
msg.lbl.addManualLedger=New Ledger
msg.lbl.manualLedgerNarration=Narration
msg.lbl.manualLedgerTranRefNo=Transaction Ref. No.
msg.lbl.manualLedgerCrtBy=Created By
msg.lbl.manualLedgerCrtDate=Created Date
msg.lbl.manualLedgerMdBy=Modify By
msg.lbl.manualLedgerMdByDate=Modify Date
msg.lbl.manualLedger=Manual Ledger
msg.lbl.manualLedgerTrnId=Transaction ID
addEditManualLedger.amount.required=Please enter the Amount
addEditManualLedger.narrattion.required=Please enter the Naration
addEditManualLedger.refno.required=Please enter the Transaction Ref. No.
addEditManualLedger.select.required=Please select appropriate value from List
msg.lbl.approval=Approval
msg.lbl.requestID=Request ID
msg.lbl.requestName=Request Name
msg.lbl.requestType=Request Type
msg.lbl.requestDate=Request Date
msg.lbl.requestStatus=Request Status
msg.lbl.requestBy=Request By
msg.lbl.approveReject=Approve/Reject:
msg.lbl.remarks=Remarks
ST_MSG_SEARCHCRITERIA_INVALID=PLease Select Search Criteria.
#Settlement
st.msg.successfullyuploaded=Successfully uploaded file with fileName-
st.msg.lvid.invalid=Please Enter a Valid LVID
st.msg.merchantid.invalid=Please Enter a Valid Merchant ID
st.msg.acquireid.invalid=Please Enter a Valid Acquiring Name
st.msg.batchid.invalid=Please Enter a Valid Batch ID
st.msg.schemeid.invalid=Please Enter a Valid Scheme ID
st.msg.date.invalid=Please Enter a Valid Date Range
st.msg.rrn.invalid=Please Enter a Valid RRN Number
st.msg.payid.invalid=Please Enter a Valid Pay Type Id
st.msg.rrn.date.invalid=Please Enter RRN or Date Range or Both
st.msg.flegenfail=Sorry File Generation Failed. LVID And Aquiring bank Exists.
st.lbl.stlfilegen.acqname=Acquiring Bank
st.lbl.uploadrequestmarking.title= Settlement/Refund Marking Upload File Status
st.lbl.stebsuploadedgrid.title=Upload Settlement Response Details
st.lbl.batchjobdet.title=Scheduled Job Details
st.lbl.uploadrejfile.title= Uploaded Rejected File Status
st.lbl.manlrefprcs.title=Manual Refund Process
st.lbl.stmtfilerecon.title=Settlement File Reconcialtion Search Screen
st.lbl.stmtrespconf.title=Settlement Response Reconcilition Details
st.lbl.mrchtPeriodicChgCal.title=Merchant Periodic Charge Calculation
st.lbl.manualmarking.terms=You can enter either date range or RRN or both
st.lbl.uploadsettlement.title=Upload Settlement
st.lbl.uploadebsfile.title=Upload EBS File
st.lbl.uploadrej.title=Upload Rejected Files
st.lbl.schjobdet.title=Scheduled Job Details
st.lbl.manalmarking.title=Manual Settlement Process
st.lbl.stlfilegen.title=Settlement File Generation
st.lbl.stlfilegen.grid.title=Settlement File Generation Details
st.msg.balcSettledAmt.invalid=Please Enter Valid Amount
st.lbl.title=RGCS Admin Portal
st.lbl.uploadrequestmarking.btn=Upload
st.lbl.uploadrequestmarking.ut=Upload Type
st.lbl.uploadrequestmarking.ud=Upload Date
st.lbl.uploadrequestmarking.fn=File Name
st.lbl.uploadrequestmarking.mn=Merchant name
st.lbl.uploadrequestmarking.lv=Legal Vehicle
st.lbl.mrchtperdchgcalc.batchname=Batch Name
st.lbl.uploadrequestmarking.st=Status
st.lbl.uploadrequestmarking.tr=Total Records
st.lbl.uploadrequestmarking.sr=Successful Records
st.lbl.uploadrequestmarking.fr=Failed Records
st.lbl.uploadrequestmarking.aname=Acquiring Bank
st.lbl.uploadsettlement.fy=Upload File Type
st.lbl.uploadsettlement.uf=Upload File
st.lbl.manualmarking.df=Date From
st.lbl.manualmarking.dt=Date To
st.lbl.manualmarking.sch=Scheme
st.lbl.manualmarking.pt=Pay Type
st.lbl.manualmarking.rrn=R.R.N
st.lbl.manualmarkingref.on=Order No
st.lbl.manualmarkingref.can=Cancel
st.lbl.manualmarkingref.ref=Refund
bt.lbl.mercperchgcal_mid=Merchant ID
st.lbl.stlfilegen.aid=Acquiring Bank ID
bt.lbl.chgcalcdate=Charge Calculation Date
bt.lbl.chgcaltype=Charge Calculation Type
bt.lbl.startdate=Start Date
bt.lbl.enddate=End Date
bt.lbl.tfa=Total Fee Amount
bt.lbl.tst=Total Service Tax
bt.lbl.totamt=Total Amount
fm.lbl.tax.requestType=Request Type
st.lbl.stlflegen.batchrunid=Batch Run Id
st.lbl.stlflegen.batchid=Batch Id
st.lbl.stlflegen.acbname=Acquiring Bank 
st.lbl.stlflegen.reqdate=Request Date
st.lbl.stlflegen.status=Status 
st.lbl.stlflegen.statusdesc=Status Description
st.lbl.stlflegen.crtby=Created By
st.lbl.stlflegen.crtdate=Created Date
st.lbl.stlflegen.crtip=Created IP
st.lbl.generate=Generate
st.lbl.submit=Submit
st.lbl.search=Search
st.lbl.clr=Clear
st.lbl.back=Back
st.lbl.sresults=Search Results
st.msg.approve.data=Records Successfully Approved
st.lbl.batchJobDetls.batchId= Batch Id
st.lbl.batchJobDetls.lvId= LV Id
st.lbl.batchJobDetls.lvName= Legal Vehicle
st.lbl.batchJobDetls.acqName= Acquiring Bank
st.lbl.batchJobDetls.mId= Merchant Id
st.lbl.batchJobDetls.fleName=File Name
st.lbl.batchJobDetls.reqDate=    Requested Date                
st.lbl.batchJobDetls.prcStrtDate=Process Start Date
st.lbl.batchJobDetls.totRecs=Total Records
st.lbl.batchJobDetls.succRecs=Success Records
st.lbl.batchJobDetls.failRecs=Failed Records
st.lbl.batchJobDetls.status=Status
st.lbl.settleBatchNo=Settle Batch No.
st.lbl.acqBankName=Acquring Bank Name
st.lbl.fileGenerationDate=File Generation Date
st.lbl.fileName=FileName
st.lbl.magnusRejection=Magnus Rejection Count
st.lbl.acqBankConfirm=Acquiring Bank Confirmation Screen
st.lbl.apprInfo=Approval Information
st.lbl.stlBtchNum=Settle Batch Number
st.lbl.totTranCnt=Total Transaction Count
st.lbl.totCrCnt=Total Cr Count
st.lbl.totCrAmt=Total Cr Amount
st.lbl.totDrAmt=Total Dr Amount
st.lbl.totTipAmt=Total Tip Amount
st.lbl.totCashBack=Total Cash Back Amount
st.lbl.totIpgTranCnt=Total IPG Transaction Count
st.lbl.totTipTrnAmt=Total Tip Transaction Amount
st.lbl.totThCount=Total  TH Count
st.lbl.totThAmt=Total  TH Amount
st.lbl.magnusRejCnt=Magnus Rejection Count
st.lbl.transactionNumber=Transaction Number
st.lbl.orderNumber=Order Number
st.lbl.currencyCode= Currency Code
st.lbl.totTranAmt=Total Transaction Amount
st.lbl.settledAmt=Settled Amount
st.lbl.balAmtToBeStdAmt=Balance Amount To Be Settled
st.lbl.curSttReqAmt=Current Settlement Request Amount
st.lbl.trnRefNo=Transaction Reference No.
st.lbl.stReqId=Settlement Request Id
st.lbl.stReqDate=Settlmement Request Date
st.lbl.merchantId=Merchant Id
st.lbl.close=Close
st.lbl.paymentId=Payment Id
st.lbl.totAmt=Total Amount
st.lbl.totSetAmt=Total Settled Amount
st.lbl.AmtAvlCan=Available For Cancel
st.lbl.reqAmtRefCan=Req Amt For Refund/Cancel
st.lbl.manSettResConScr=Manual Settlement Response Confirmation Screen
st.lbl.sttBatchId=Settlement Batch ID
st.lbl.stBatchNo=Settlement Batch Number
st.lbl.meBussName=Merchant Bussiness Name
st.lbl.settAmt=Settlement Amount
st.lbl.totRefAmt=Total Refund Amount
st.lbl.chgBkAmt=Charback Amount
st.lbl.meChgBk=Merchant Charback 
st.lbl.dwnFile=Download File
st.lbl.rfVoidReqId=Refund/Void Request ID
st.lbl.rfReqAmt=Refund Request Amount
st.lbl.rfReqDate=Refund Req Date
st.lbl.Merchant=Merchant
st.lbl.refVoidFlag=Refund/Void Flag
st.lbl.refAprProc=Refund Approval Process
st.lbl.stBatchRunId=Batch Run Id
st.lbl.res.lvName=Legal Vehicle Name
st.lbl.res.acqrBankName=Acquirer Bank Name
st.lbl.res.fileName=FileName
st.lbl.res.apprRec=Approved Record
st.lbl.res.rejRec=Rejected Record
st.lbl.setFilRecScr=Settlement File Reconcialtion Search Screen
st.lbl.uploadFileEBS=uploadFileEBS
st.lbl.uploadedDate=Uploaded Date
st.lbl.mePayAdvcDet=Merchant Payment Advice Generation Details
FileGeneratedSuccessful=File Generated Successful with Batch Run ID:

No.Records.Found=No Records Found
Successful.Update=Records Successfully Updated.
Unsuccessful.Update=Records Not Updated Successfully.
Failed.Upload.Empty.Message=You failed to upload because the file was empty or file-type was not correct
Failed.Upload.Incorrect.Message=You failed to upload because the file-type was not correct
st.lbl.NoRecordFound=No Record Found
st.lbl.stFileRconDet=Settlement File Reconciliation Details
st.lbl.batchJobDetls.prcEndDate=Process End Date
st.lbl.downloadFile=Download File
st.lbl.select=--Select--

#now Added by nitesh
msg.formValidationFailed=Form Validation Failed
msg.lbl.failed=Failed
msg.lbl.error=Error
msg.lbl.mchtPayAdvcGen=Merchant Payment Advice Generation 
msg.lbl.mchtPayAdvcGenDet=Merchant Payment Advice Generation Details
msg.lbl.optionAll=All
msg.lbl.success=Success
msg.lbl.initiated=Initiated
msg.lbl.successful=Successful
msg.lbl.totAmtPayable=Total Amount Payable
msg.lbl.totMeCnt=Total Merchant Count
msg.lbl.mePayoutDet=Merchant Payout Details
msg.lbl.reconWl=Reconciliation for RGCS Admin
msg.lbl.totTranAuth=Total Transaction Authorization Count
msg.lbl.totTranAmt=Total Transaction Amount
msg.lbl.totSetAmt=Total Settled Amount
msg.lbl.totSetCnt=Total Settled Count
msg.lbl.meRecon=Merchant Wise Reconciliation
msg.lbl.stRejRec=Rejected Records
msg.lbl.txnId=Transaction Id
msg.lbl.paymentId=PaymentId
msg.lbl.trnRefNo=Transaction Reference Number
msg.lbl.accId=Account Id
msg.lbl.trnDate=Transaction Date
msg.lbl.trnType=Trn Type
msg.lbl.settlementId=Settlement Id
msg.lbl.recSeq=Record Sequence
msg.lbl.statusCode=Status Code
msg.lbl.statusDesc=Status Desc
msg.lbl.sucRec=Success Records
msg.lbl.failRec=Failed Records
msg.lbl.totRecs=Total Records
msg.lbl.pendingAcqResp=Pending Acquiring Bank Response File Confirmation
msg.lbl.uploadPayoutResponse=Upload Payout Response
#nitesh //15/4/2015
msg.lbl.stFileGenSc=Settlement File Genaration Screen
msg.lbl.noRec=No Record Found
msg.lbl.totChrgbkAmt=Total ChargeBack Amount
msg.lbl.totMrchtCharge=Total Merchant Charge
#msg.lbl.totRefAmt=Total Reference Amount
msg.lbl.uploadStFile=Upload Settlement File
msg.lbl.batchRunId=Batch Run Id
msg.lbl.acqBankName=Acquirer Bank Name
msg.lbl.lvname=Legal Vehicle Name
msg.lbl.totTranCnt=Total Transaction Count
msg.lbl.fileName=File Name
msg.lbl.settledDate=Settled Date
msg.lbl.ebs=EBS
msg.lbl.totPayCnt=Total Payable Count
msg.lbl.totPayAmt=Total Payable Amount
msg.lbl.totRecs=Total Records
msg.lbl.fileGenFail=File Generation Failed
msg.lbl.fileExists=Merchant Payment Generation Request already exits
msg.lbl.fileGenSuccess=File Generation Successful with BatchRunId
msg.lbl.successfullyuploaded=Successfully uploaded file with file Name:
st.msg.enterDate=Please enter Date
msg.lbl.enterOption=Please select atleast one options
st.lbl.holdRelease=Hold And Release Transaction
st.lbl.pgMerchantId=PG Merchant ID
st.lbl.meBussName=ME Business Name
st.lbl.fromDate=From Date
st.lbl.toDate=To Dates
st.lbl.holdFlag=Hold Transaction
st.lbl.trndate= Transaction Date
st.lbl.trnAmount=Transaction Amount
st.lbl.meName=Merchant Name
st.lbl.prcEndDate=Process End Date
st.lbl.prcStartDate=Process Start Date
st.lbl.settleStatus=Settlement Status
st.lbl.currHoldStatus=Current Hold Status
st.lbl.holdRemarks=Remarks
st.lbl.holdSuccess=Transaction put on hold sucessfully
st.lbl.holdFail=Transaction put on hold failed
st.lbl.releaseSuccess=Transaction Released sucessfully
st.lbl.releaseFail=Transaction Release failed
st.lbl.holdReleaseTrnDetail=Hold Release Transaction Details
st.msg.invalidTrnRefNo=Please enter only Numbers
st.msg.invalidMerchantId=Please enter only characters
st.lbl.refId=Reference Id
st.lbl.markedBy=Marked By
st.lbl.markedDate=Marked Date
st.lbl.remarks=Remarks
st.lbl.acquiringBank=Acquiring Bank
st.lbl.schemeName=Scheme Name
st.lbl.netBankName=NetBank Name
st.lbl.authCode=Authorization Code
st.lbl.responseCode=Response Code
st.lbl.uploadHoldRelease=Upload Hold Release
st.lbl.holdReleaseLog=Hold Release Log
st.lbl.lvname=Legal Vehicle Name
st.lbl.paytypeName=Paytype Name
st.lbl.rrn=R.R.N
st.lbl.inVoiceNo=Invoice No
st.lbl.mid=MID
st.lbl.paymentType=Payment type
st.lbl.currencyName=Currency Name
st.lbl.tillNowSettAmt=Till Now Settlement Amount
st.lbl.balAmt=Balance Amount
st.lbl.markAmt=Marked Amount
st.lbl.refund=Refund
st.lbl.uploadRefundFile=Upload Refund File
st.lbl.batchDesc=Batch Description
st.lbl.mark=Mark
#Onboarded Merchant 
me.lbl.storeList=Store List
me.lbl.slNo=Sl.No
me.lbl.storeName=Store Name
me.lbl.awlMcc=AWL MCC
me.lbl.merchantType=Merchant Type
me.lbl.rating=Rating
me.lbl.merchantDetails=Merchant Details
me.lbl.createdBy=Created By : 
me.lbl.createdDate=Created Date : 
me.lbl.action=Action
me.msg.pendingUpdatedStore= Store details have been successfully updated and is pending for approval
me.msg.errorUpdateStore=Error occurred while updating Store Details 
me.msg.pendingUpdatedAcqBnk= Acquiring Bank has been successfully updated and is pending for approval
me.msg.errorUpdateAcqBnk=Error occurred while updating Acquiring Bank Details 
me.msg.alreadyPendingStore=Store is already pending for approval
me.msg.alreadyPendingBnk=Acquiring Bank is already pending for approval
me.msg.pendingUpdatedPayType= Payment Type has been successfully updated and is pending for approval
me.msg.errorUpdatePayType=Error occurred while updating Payment Type Details 
me.msg.alreadyPendingPayType=Payment Type is already pending for approval
me.msg.pendingUpdatedMsf= MSF details have been successfully updated and is pending for approval
me.msg.errorUpdateMsf=Error occurred while updating MSF Details 
me.msg.alreadyPendingMsf=MSF is already pending for approval
me.msg.pendingUpdatedIpg= IPG details have been successfully updated and is pending for approval
me.msg.errorUpdateIpg=Error occurred while updating IPG Details 
me.msg.alreadyPendingIpg=IPG is already pending for approval
me.msg.pendingUpdatedBlackList= Black List details have been successfully updated and is pending for approval
me.msg.errorUpdateBlackList=Error occurred while updating Black List Details 
me.msg.alreadyPendingBlackList=Black List is already pending for approval
me.msg.pendingUpdatedAcqRule= Acquiring Rule has been successfully updated and is pending for approval
me.msg.errorUpdateAcqRule=Error occurred while updating Acquiring Rule
me.msg.alreadyPendingAcqRule=Acquiring Rule is already pending for approval
me.msg.midGenByStatus=MID has not been generated for the Store
me.msg.pendingSaveMsf= MSF details have been successfully saved
me.msg.acqBnkDeactivationPending = Acquiring Bank has been sent for Approval for Deactivation
me.msg.acqBnkAcquirerDeactivation = Error while Deactivating Acquiring Bank
me.msg.acqBnkActivationPending = Acquiring Bank has been sent for Approval for Activation
me.msg.errorAcqBnkActivation = Error while Activating Acquiring Bank
#new for View Merchant
msg.lbl.cardName=Card Name
msg.lbl.integApproach=Integration Approach
mp.lbl.trnRefNo = Transaction Reference No :
mp.lbl.trnHist = Transaction History
msg.lbl.fail=Failed
###REPORTS
#Validation
RP_MSG_NoBlankFields=Please provide atleast one search parameter
RP_MSG_Mandatory=Mandatory fields cannot be left blank
RP_MSG_ToFromDate=Please select To and From date
RP_MSG_NoRecordError=No record found
RP_MSG_DataFound=Data successfully found
RP_MSG_fromDate.required=Please select From date
RP_MSG_toDate.required=Please select To date
RP_MSG_trnDate.required=Please select a Date
RP_MSG_MonthYear.required=Please select Month & Year
RP_MSG_MID.required=Please enter MID
RP_MSG_PgMID.required=Please enter Merchant ID
RP_MSG_TID.required=Please enter TID
RP_MSG_bankName.required=Please enter Bank Name
RP_MSG_status.required=Please select Status
RP_MSG_legalVehicle.required=Please select Legal Vehicle
#Form Labels
RP_LBL_GetReport=Show Report
RP_LBL_Channel=Channel
RP_LBL_Reset=Reset
RP_LBL_Excel=Download as Excel
 am.lbl.file.cycleNum = Settlement Cycle
RP_LBL_cbs=Deemed Transaction (CBS)
RP_LBL_cbs2=Reversal Transaction (CBS)
RP_LBL_Display=Display Option
RP_LBL_Status = Status
RP_LBL_Select=--Select--
RP_LBL_All=All 
RP_LBL_AuthTxn=Authorized Transactions 
RP_LBL_PendingTxn=Pending for Settlement Marking 
RP_LBL_SettledTxn=Settlement In-Progress/Completed 
RP_LBL_PaymtPending=Merchant Payment Pending 
RP_LBL_PaymtCompltd=Merchant Payment Completed 
RP_LBL_RefundTxn=Refund In-Progress/Completed 
RP_LBL_OnHoldTxn=On Hold Transactions 
RP_LBL_CancelledTxn=Cancellation In-Progress/Completed 
RP_LBL_FailedTxn=Failed Transactions 
RP_LBL_Chargeback=Chargeback 

RP_LBL_TransactionWise=Transaction Wise
RP_LBL_MerchantWise=Merchant Wise
RP_LBL_Date=Date
RP_LBL_MonthYear=Month & Year
RP_LBL_fromDate=From Date
RP_LBL_toDate=To Date
RP_LBL_fromTime=From Time
RP_LBL_toTime=To Time
RP_LBL_legalVehicle=Legal Vehicle
RP_LBL_bankName=Bank Name
RP_LBL_txnRefNo=Transaction Ref No.
RP_LBL_OrderId=Order ID
RP_LBL_PgMerchantId=PG Merchant ID
RP_LBL_MerchantId=Merchant ID
RP_LBL_RoleName = Role Name
RP_LBL_RoleCode = Role Code
RP_LBL_optMnuDetails = Option / Menu Details
#Report Titles
RP_LBL_DetailedTransaction=Detailed Transaction Report
RP_LBL_TransactionSearch=Transaction Search Report
RP_LBL_Settlement=All Transactions Report
RP_LBL_Profitability=Profitability Report
RP_LBL_TRN_REF_NO=Trn Ref No
RP_LBL_TRN_AMT=Trn Amount
RP_LBL_TRN_DATE=Trn Date
RP_LBL_TRN_MSF=MSF
RP_LBL_TRN_SVC_TX=Service Tax
RP_LBL_TRN_INT_FEE=Interchange Fee
RP_LBL_TRN_SWCH_FEE=Switch Fee
RP_LBL_TRN_PSP_FEE=PSP Fee
RP_LBL_TRN_PAY_AMT=Payable Amount
RP_LBL_TRN_PAY_REV=Revenue
RP_LBL_MerchantRevSum=Merchant Revenue Summary
RP_LBL_TOT_TRN_CNT=Total Trn Count
RP_LBL_TOT_TRN_AMT=Total Trn Amount
RP_LBL_TOT_MSF=Total MSF
RP_LBL_TOT_SVC_TX=Total Service Tax
RP_LBL_TOT_INT_FEE=Total Interchange Fee
RP_LBL_TOT_SWCH_FEE=Total SwitchFee
RP_LBL_TOT_PSP_FEE=Total PSP Fee
RP_LBL_TOT_PAY_AMT=Total Payable Amount
RP_LBL_TOT_PAY_REV=Total Revenue
RP_LBL_MerchantPostingSum=Merchant Periodic Posting Summary
RP_LBL_MerchantPostingDet=Merchant Periodic Posting Details
RP_LBL_PEN_POST_CNT=Pending Posting Count
RP_LBL_PEN_POST_AMT=Pending Posting Amount
RP_LBL_COM_POST_CNT=Total Posting Count
RP_LBL_COM_POST_AMT=Total Posting Amount
RP_LBL_COM_ORDER_NO=Order No
RP_LBL_COM_POST_STATUS=Posting Status
RP_LBL_COM_POST_DATE=Posting Date
RP_LBL_COM_BNK_REF_NO=Bank Ref No
RP_LBL_COM_POST_BATCH_ID=Posting Batch ID
RP_LBL_ReportType=Report Type
RP_LBL_postType=Posting Type											
RP_LBL_sumRep=Summary Report
RP_LBL_detRep=Detailed Report
RP_LBL_all=All
RP_LBL_merPostPen=Merchant Posting Pending
RP_LBL_merPostCom=Merchant Posting Completed
											
RP_LBL_MerchantVolume=Merchant Revenue Details Report
RP_LBL_MerchantPosting=Merchant Periodic Posting Report
RP_LBL_MerchantScreen=Merchant Details Screen Report
RP_LBL_AcqBankUnsettledTransaction=Acquiring Bank Unsettled Transaction Report
RP_LBL_AcqBankSaleSummary=Acquiring Bank Sale Summary Report
RP_LBL_ChargebackDetailedTransaction=Chargeback Detailed Transaction Report
RP_LBL_SearchTransactionforMerchants=Search Transaction for Merchants Report
RP_LBL_MerchantWiseMISReport=Merchant Wise MIS Report
RP_LBL_MISTransactionReport=MIS Transactions Report
RP_LBL_PaymentAdvice=Payment Advice Report
RP_LBL_AcquiringErrorTransaction=Acquiring Error Transaction Report
RP_LBL_Variance=Variance Report
RP_LBL_FRMAlert=FRM Alert Report
RP_LBL_MonthlyInvc=Monthly Invoicing Report
RP_LBL_ProfitLossAcc=Profit & Loss Account Report
RP_LBL_MerchantTxnDetails=Merchant Transaction Details Report
RP_LBL_ChargebckSales=Chargebacks to Sales Ratio Report
RP_LBL_ChargebckAging=Chargebacks - Daily Aging Report
RP_LBL_ChargebckTxn=Chargeback Detailed Transaction Report
RP_LBL_AllTxns=All Transactions Report   
RP_LBL_MeDetails=Merchant Details Report 
RP_LBL_InMeList=Inactive Merchant List Report 
RP_LBL_MMD=Merchant Modification Details   
RP_LBL_DSR=Daily Sales Report
RP_LBL_RoleAccess = Role Access Report
RP_LBL_TrialBal=Trial Balance Report
RP_LBL_SRR=Self Registration Report

#Report Filenames
RP_FILENAME_DetailedTransaction=Detailed_Transaction
RP_FILENAME_TransactionSearch=Transaction_Search
RP_FILENAME_Profitability=Profitability_Report
RP_FILENAME_Settlement=Details
RP_FILENAME_MerchantVolume=Merchant_Revenue Details
RP_FILENAME_MerchantScreen=Merchant_Details_Screen
RP_FILENAME_AcqBankUnsettledTransaction=AcqBank_Unsettled_Txn
RP_FILENAME_AcqBankSaleSummary=AcqBank_Sale Summary
RP_FILENAME_ChargebackDetailedTransaction=Chargeback_Detailed_Txn
RP_FILENAME_SearchTransactionforMerchants=Search_Txn_for_Merchants
RP_FILENAME_AcquiringErrorTransaction=AcqError_Txn
RP_FILENAME_MerchantWiseMISReport=Merchant_MIS
RP_FILENAME_MISTransactionReport=MIS_Transaction 
RP_FILENAME_PaymentAdvice=Payment_Advice
RP_FILENAME_Variance=Variance 
RP_FILENAME_FRMAlert=FRM_Alert 
RP_FILENAME_MonthlyInvc=Monthly_Invoicing
RP_FILENAME_ProfitLossAcc=Profit_Loss_Account
RP_FILENAME_MerchantTxnDetails=Merchant_Transaction_Details
RP_FILENAME_ChargebckSales=Chargebacks_Sales_Ratio 
RP_FILENAME_ChargebckAging=Chargebacks_Daily_Aging
RP_FILENAME_ChargebckTxn=Chargeback_Transaction 
RP_FILENAME_AllTxns=All_Transaction
RP_FILENAME_OnBoardedMerchant=Merchant_Details
RP_FILENAME_InactiveMerchant=Inactive_Merchant_List
RP_FILENAME_MMD=MMD
RP_FILENAME_DSR=Daily_Sales
RP_FILENAME_TrialBal=Trial_Balance 
RP_FILENAME_SR=Self_Registration

RP_FILENAME_ROLACCESS = Role Access
#Added by Venkatesh 18.02.2016
RP_FILENAME_VPAMaintenance=VPA_Maitenance
#Column Headers
RP_LBL_PaymentType=Payment Type
RP_LBL_SettlementDate=Settlement Date
RP_LBL_MID=MID
RP_LBL_TID=TID
RP_LBL_TransactionDate=Transaction Date
RP_LBL_RRN=RRN
RP_LBL_AuthCode=Auth Code
RP_LBL_CardNumber=Card Number
RP_LBL_TransactionAmount=Transaction Amount
RP_LBL_Currency = Currency
RP_LBL_MerchantCreditAmount=Merchant Credit Amount
RP_LBL_MerchantCommissionAmount=Merchant Commission Amount
RP_LBL_TransactionType=Transaction Type
RP_LBL_ServiceTax=Service Tax
RP_LBL_TransactionMedium=Transaction Medium
RP_LBL_DomesticInternational=Domestic/International
RP_LBL_OnusOffus=Onus/Offus
RP_LBL_AcqBankName=Acquiring Bank Name 
RP_LBL_SettledTransactionCount=Settled Transaction Count
RP_LBL_SettledTransactionAmount=Settled Transaction Amount
RP_LBL_InvoiceNo=Invoice No.
RP_LBL_MerchantName=Merchant Name
RP_LBL_MerchantAddress=Merchant Address
RP_LBL_MerchantCity=Merchant City 
RP_LBL_MerchantRegion=Merchant Region 
RP_LBL_MerchantAcNo=Merchant Current A/c No.
RP_LBL_MerchantAcBankName=Merchant Current A/c Bank Name
RP_LBL_CurrencyCode=Currency Code
RP_LBL_TotalAmountSettled=Total Amount Settled
RP_LBL_TotalRefundSettled=Total Refund Settled
RP_LBL_CardType=Card Type
RP_LBL_Commission=Commission
RP_LBL_NetAmountPayable=Net Amount Payable
RP_LBL_ViewDetails=View Details
RP_LBL_TimeStamp=Time Stamp
RP_LBL_Amount=Amount
RP_LBL_MSFAmount=MSF Amount
RP_LBL_TransactionStatus=Transaction Status
RP_LBL_ChargebackDate=Chargeback Date
RP_LBL_ARN=ARN 
RP_LBL_ChargebackAmount=Chargeback Amount 
RP_LBL_ChargebackReasonCode=Chargeback Reason Code
RP_LBL_Reason=Reason 
RP_LBL_AcquirerErrorCode=Acquirer Error Code
RP_LBL_Count=Count
RP_LBL_TermMCC=Term MCC
RP_LBL_DomainAddressOfURL=Domain Address of URL
RP_LBL_NoTxnLastWeek=No. of Transactions Last Week
RP_LBL_SumAmtLastWeek=Sum of Amount Last Week
RP_LBL_NoTxnLast2Month=No. of Transactions Last Two Months
RP_LBL_SumAmtLast2Month=Sum of Amount Last Two Months
RP_LBL_AvgTicketSize=Average Ticket Size
RP_LBL_Remark=Remark
RP_LBL_InstlDate=Installation Date
RP_LBL_Remark2=Remark 2
RP_LBL_CardHolder=Card Holder 
RP_LBL_CardHolderBank=Card Holder Bank
RP_LBL_Scheme=Scheme  
RP_LBL_SchemeCode=Scheme Code
RP_LBL_NetBnk = Net Bank
RP_LBL_AcqBnk = Acquiring Bank
RP_LBL_LocalIntl=Local/International
RP_LBL_Mcc=MCC Code 
RP_LBL_Location=Location
RP_LBL_BoardingDate=Boarding Date
RP_LBL_TotalNoTransactions=Total No. of Transactions
RP_LBL_Commerce=Commerce
RP_LBL_AverageNoTransactions=Average No. of Transactions
RP_LBL_MSF=MSF
RP_LBL_MSFTAX=MSF Tax Amount
RP_LBL_AverageMSF=Average MSF
RP_LBL_AverageServiceTax=Average Service Tax
RP_LBL_TPF=TPF
RP_LBL_OtherFees=Other Fees
RP_LBL_AcqBankBSF=Acquiring Bank BSF
RP_LBL_AcqBankServiceTax=Acquiring Bank Service Tax 
RP_LBL_Loss=Loss
RP_LBL_NetBankingBank=Net Banking Bank
RP_LBL_RP_LBL_NetBankingBSF=Net Banking BSF
RP_LBL_NetBankingServiceTax=Net Banking Service Tax
RP_LBL_NetBankingOtherFees=Net Banking Other Fees
RP_LBL_OtherCost=Other Cost
RP_LBL_WriteOff=Write Off
RP_LBL_Chargeback=Chargeback
RP_LBL_ProfitLoss=Profit / Loss
RP_LBL_PgTrnRefNo=Transaction Ref No
RP_LBL_TxnReqDate=Transaction Request Date
RP_LBL_MeTxnAmt=ME Transaction Amount
RP_LBL_meName=Merchant Name
RP_LBL_PaymtRefNo=Payment Ref No
RP_LBL_PayType=Pay Type (Credit/ Debit/ Net Banking)
RP_LBL_SettledAmt=Settled Amount
RP_LBL_RefundedAmt=Refunded Amount
RP_LBL_CurrStatus=Current Status
RP_LBL_SrNo=Sr. No.
RP_LBL_AcqICABIN=Acquiring (ICA / BIN)
RP_LBL_LegalName=Legal Name
RP_LBL_DBAName=Merchant Name
RP_LBL_MCC=MCC
RP_LBL_CardCountry=Card Country
RP_LBL_IssBnkName=Issuer Bank Name
RP_LBL_TxnDateTime=Transaction Date & Time
RP_LBL_AppCode=Approval Code
RP_LBL_RespCode=Response Code
RP_LBL_EnrtyMode=Entry Mode
RP_LBL_IssCardCurrCode=Issuer Card Currency Code
RP_LBL_IpAdd=IP Address
RP_LBL_DevId=Device ID
RP_LBL_MerDomURL=Merchant Domain URL
RP_LBL_Email=Email
RP_LBL_CustShipAdd=Customer Address / Shipping Address
RP_LBL_Contact=Contact Details
RP_LBL_TxnId=Transaction ID
RP_LBL_TotalAmount=Total Amount
RP_LBL_TotalMSF=Total MSF 
RP_LBL_Description=Description 
RP_LBL_ServTacDecl=Service Tax Declaration
RP_LBL_Type=Type
RP_LBL_SuccessCount=Success Count
RP_LBL_FailureCount=Failure Count
RP_LBL_SuccessAmt=Success Amount
RP_LBL_FailureAmt=Failure Amount

RP_LBL_NoSaleTxn=No. of Sale Transactions
RP_LBL_NoPreAuthCompTxn=No. of Pre-Auth Completed Transactions
RP_LBL_NoRefundTxn=No. of Refund Transactions
RP_LBL_NoVoidTxn=No. of Void Transactions
RP_LBL_NoPreAuthTxn=No. of Pre-Auth Transactions
RP_LBL_NoTxnCount=Total No. of Transactions Count

RP_LBL_SaleTxn=Sale Transactions
RP_LBL_PreAuthCompTxn=Pre-Auth Completed Transactions
RP_LBL_RefundTxn=Refund Transactions
RP_LBL_VoidTxn=Void Transactions
RP_LBL_PreAuthTxn=Pre-Auth Transactions
RP_LBL_SumAmt=Total Sum of Amount
RP_LBL_TotOtherFees=Total Other Fees
RP_LBL_TotalBSF=Total BSF

RP_LBL_CaseStatusCode=Case Status Code 
RP_LBL_CaseID=Case ID 
RP_LBL_ChargebckReqDate=Chargeback/Request Date  
RP_LBL_TxnCurrency=Transaction Currency
RP_LBL_ReasonCodeDesc=Reason Code & Description  
RP_LBL_ChargebckCurrency=Chargeback Currency 
RP_LBL_DocIndi=Doc Indicator  
RP_LBL_MsgTxt=Message Text  
RP_LBL_MerLoc=Merchant Location   
RP_LBL_AcNo=A/c Number    
RP_LBL_ExpDate=Expiry Date 
RP_LBL_PosCode=Pos Code
RP_LBL_EcomIndi=ECom Indicator
RP_LBL_RefundFlag=Refund Flag (Y/N) 
RP_LBL_ChargebckRevFlag=Chargeback Reversal FLag (Y/N) 
RP_LBL_CaseAging=Case Aging
RP_LBL_RuleNo=Rule No
RP_LBL_RuleDesc=Rule Description
RP_LBL_MeReqStatus=Merchant Transaction Request Status
RP_LBL_TxnStatus=Transaction Status

RP_LBL_ChargebckCurrMnthCnt=Count of Chargebacks in Current Month
RP_LBL_ChargebckCurrMnthSum=Sum of Chargebacks in Current Month
RP_LBL_TxnPrevMnthCnt=Count of Transactions in Previous Month
RP_LBL_TxnPrevMnthSum=Sum of Transactions in Previous Month
RP_LBL_PercentCalc=Percentage Calculation

RP_LBL_REGID=Registration ID
RP_LBL_FULLNAME=Full Name
RP_LBL_USERNAME=User Name
RP_LBL_ACTIVATIONSTATUS=Activation Status
RP_LBL_URL=Website URL

#Nitesh for Reports
RP_LBL_CreationDate=Dt. Of Creation
RP_LBL_MREF_GID=MREF GID 
RP_LBL_MREF_ID=MREF ID	  
RP_LBL_Merchant_Legal_Name=Merchant Legal Name            	
RP_LBL_DBA_Name=Merchant Name               	
RP_LBL_Merchant_web_address=Merchant Web Address		
RP_LBL_PAN/TAN_No=PAN/TAN No	
RP_LBL_Category=Category    
RP_LBL_Merchant_Business_Type=Merchant Business Type
RP_LBL_Website_RegistrationDate=Website	Registration Date	
RP_LBL_Corporate_IdentificationNo=Corporate Identification No
RP_LBL_Current_Office_Add=Current Office Add	
RP_LBL_Landmark	=Landmark	
RP_LBL_City	=City	
RP_LBL_Pin_Code=Pin Code	
RP_LBL_Phone=STD Code & Business Phone	
RP_LBL_Business_Fax=Business Fax		
RP_LBL_Address_Landmark=Address Landmark	
RP_LBL_State=State	
RP_LBL_Pin_Code=Pin Code		
RP_LBL_Business_Fax=Business Fax	
RP_LBL_fname=First Name	
RP_LBL_lname=Last Name	
RP_LBL_Designation=Designation
RP_LBL_emailOff=Official Email-ID	
RP_LBL_DOB=Date of Birth	
RP_LBL_PAN_No=PAN No	
RP_LBL_Passport_No=Passport No	
RP_LBL_Res_Phone=Std Code & Residential Phone No	
RP_LBL_Mobile_No=Mobile No	
RP_LBL_Acc_Type=Account Type	
RP_LBL_Bank_Name=Bank Name	
RP_LBL_Branch_Name=Branch Name	
RP_LBL_MICR=MICR	        
RP_LBL_IFSC=IFSC		
RP_LBL_Account_Number=Account Number	
RP_LBL_Account_Holder_Name=Account Holder`s Name	
RP_LBL_Allow_Refunds=Allow Refunds
RP_LBL_Allow_Void=Allow Void	
RP_LBL_Allow_Cancellation=Allow Cancellation	
RP_LBL_Allow_V/C=Void Cancellation	
RP_LBL_Batch_Refund=Batch Refund	
RP_LBL_Allow_Online_Refunds=Allow Online Refunds
RP_LBL_MCC_Type=MCC Type 
RP_LBL_Channel_type=Channel type	
RP_LBL_Account_Manager_Name=Name of Account Manager
RP_LBL_Fixed=Fixed
RP_LBL_Percentage=Percentage
RP_LBL_Store_Status=Store Status
RP_LBL_Mod_Field_Name=Modified Field Name
RP_LBL_Old_Value=Old Value           
RP_LBL_New_Value=New Value 
RP_LBL_Mod_Date=Modification Date          
RP_LBL_Maker_User_ID=Maker User ID
RP_LBL_Checker_User_ID=Checker User ID
RP_LBL_trnFromDate=Transaction Date From
RP_LBL_trnToDate=Transaction Date To
RP_LBL_Amt=Amount
RP_FILENAME_MerchantRevSum = Merchant Revenue Summary Reports
RP_FILENAME_MerchantPosting = Merchant Posting
#Transaction Detail
cmn.lbl.merchantRequestDetails = Merchant Request Details
cmn.lbl.paymentDetails = Payment Details
cmn.lbl.settleRefundDetails = Settlement/Refund Details
cmn.lbl.cancelDetails = Cancel Details

cmn.lbl.chargebckDetails = Chargeback Details
cmn.lbl.holdReleaseDetails = Hold & Release Log

cmn.lbl.chargbckReqId=Chargeback Req ID
cmn.lbl_ChargebackDate=Chargeback Date
cmn.lbl_ChargebackAmount=Chargeback Amount 
cmn.lbl_Reason=Reason 
cmn.lbl.status=Status
cmn.lbl.holdReleaseId=Hold/Release Log ID
cmn.lbl.holdReleaseDate=Hold/Release Log Date
cmn.lbl.transCurrentStatus = Transaction Current Status 


cmn.lbl.mEOrderId = ME Order Id
cmn.lbl.pgTranRefNo = PG Transaction Ref No
cmn.lbl.tranReqDt = Transaction Request Date
cmn.lbl.meTrnReqType = Request Type
cmn.lbl.merId = Merchant Id
cmn.lbl.tranAmt = Transaction Amount
cmn.lbl.trnRemarks = Transaction Remarks
cmn.lbl.reqUrl = Response URL
cmn.lbl.capBy = Payment Details Captured By
cmn.lbl.reqStatDesc = Request Status Description
cmn.lbl.meTranReqType = ME Transaction Request Type
cmn.lbl.tranRem = Transaction Remarks
cmn.lbl.bill = Billing
cmn.lbl.ship = Shipping
cmn.lbl.name = Name
cmn.lbl.add1 = 	Address 1
cmn.lbl.add2 = Address 2
cmn.lbl.city = City
cmn.lbl.state = State
cmn.lbl.pinCode = Pin Code
cmn.lbl.country = 	Country
cmn.lbl.shipCharges = Shipping Charges
cmn.lbl.shipTaxAmt = Shipping Charges Tax Amount
cmn.lbl.grossAmt = Gross Transaction Amount
cmn.lbl.payRefNo = Payment Reference No
cmn.lbl.lgVehName = Legal Vehicle Name
cmn.lbl.payType = Payment Type
cmn.lbl.payAttempt = Payment Attempt
cmn.lbl.spTran = Split Transaction
cmn.lbl.curStatCode = Authorization Status Code
cmn.lbl.curStatDesc = Authorization Status
cmn.lbl.trnAmt = Transaction Amount
cmn.lbl.conFee = Convenience Fee
cmn.lbl.conFeeTaxAmt = Convenience Fee Tax Amount
cmn.lbl.totTranAmt = Total Transaction Amount
cmn.lbl.currency = Currency Code & Name
cmn.lbl.stanNo = Stan No
cmn.lbl.invNo = Invoice No
cmn.lbl.rrn = RRN
cmn.lbl.tranId = Transaction ID
cmn.lbl.authCode = Authorization Code
cmn.lbl.respCode = Response Code
cmn.lbl.tranDate = Transaction Date
cmn.lbl.eci = ECI		
cmn.lbl.scheme = Scheme
cmn.lbl.domInt = Domestic/International
cmn.lbl.onUs = Onus	
cmn.lbl.cardNo = Card No
cmn.lbl.nameOnCard = Name On Card
cmn.lbl.acqBank = Acquirer Bank
cmn.lbl.mid = MID
cmn.lbl.tid = TID
cmn.lbl.dcc = DCC Flag
cmn.lbl.dccCurr = DCC Currency
cmn.lbl.excRate = Exchange Rate
cmn.lbl.markUpPer = Markup Percentage
cmn.lbl.dccTranAmt = DCC Transaction Amount
cmn.lbl.chnPart = Channel Partner
cmn.lbl.netBnk = Net Bank
cmn.lbl.settleReqRefNo = Settlement Req Reference No
cmn.lbl.settleAmt = Settlement Amount
cmn.lbl.settleReqDate = Settlement Req Date
cmn.lbl.settleMode = Settlement Marking Mode
cmn.lbl.msfAmt = MSF Amount
cmn.lbl.msfTax = MSF Tax
cmn.lbl.canReqRefNo = Cancel Req Reference No
cmn.lbl.canReqDate = Cancel Req Date
cmn.lbl.noRecFound = No Record Found
cmn.lbl.yes = Yes
cmn.lbl.no = No
###REPORTS

st.lbl.toSetFileGenAmt=Total Settle File Genration Amount
st.lbl.toFileGenCnt=Total Refund File Genration Count
st.lbl.toFileGenAmt=Total Refund  File Genration Amount
st.lbl.setfileGenCnt=Settlement Count
st.lbl.setfileGenAmt=Settlement Amount
st.lbl.setfileGenRefCnt=Refund Count
st.lbl.setfileGenRefAmt=Refund Amount
st.lbl.tilNowSetRejCnt=Till Now Settlement Reject Count
st.lbl.tilNowSetRejAmt=Till Now Settlement Reject amount
st.lbl.tilNowSetRefRejCnt=Till Now Settlement Refund Reject Count
st.lbl.tilNowSetRefRejAmt=Till Now Settlement Refund Reject Amount
ST.lbl.gridDnldFile=Download File
msg.lbl.noRec= No Record Found
st.lbl.stFileResDet=Settlement File Response Confirmation
msg.lbl.uploadMagRejFile=Upload Magnus Rejection File
ST.msg.settfileGenerated=Settlement file generated Request Submitted successfully
ST.msg.settFileGenPending=File generation is pending
ST.msg.errFileGenerating=Error occcured while generating settlement file
ST.msg.successfileConfirm=Settlement File confirmed successfully
ST.msg.fileGenReqSucess=File Regeneration Request Submited succesfully
ST.msg.errorUpdating=Error occurred while updating
ST.msg.succResConfirm=Settlement file response confirm successfully
ST.msg.errResConfirm=error occured while confirming settlement response file
ST.msg.magRejFileUpld=Magnus rejection file uploaded successfully
st.lbl.stmerchPerdcChrg.title=Merchant Periodic Charge
st.lbl.entrPgMerchId=Enter PG Merchan ID
st.msg.mid.name=Please enter valid Merchant ID
ST.lbl.gridbusiness.name=Merchant Business Name
ST.lbl.gridmid=PG Merchant ID
ST.lbl.gridChgType=Charge  Type
ST.lbl.gridFeeAmt=Fee Amount
ST.lbl.gridServiceTax=Service Tax
ST.lbl.gridTotAmt=Total Amount
#st.lbl.meBussName=Merchant Business name
#st.lbl.totAuthCnt=Total Transaction Authorization count
#st.lbl.totAuthAmt=Total Transaction Amount
#st.lbl.fileGenCnt=Total File
#st.lbl.fileGenAmt=Total File Amount
sm.lbl.settleType=Settlement Type
#User Adminstration Lable
AM.lbl.approveReject=Approve/Reject:
AM.lbl.select=--Select--
AM.lbl.approve = Approve
AM.lbl.reject = Reject
AM.lbl.remarks=Remarks
AM.lbl.reqInfo=Request Information
msg.lbl.ebs=EBS
msg.lbl.totRefAmt=Total Refund Amount
msg.lbl.uploadStFile=Upload Settlement File
RP_LBL_Status = Status
#msg.lbl.secretKey=Secret Key
cmn.lbl.emailId=Email ID
am.lbl.selectLvs =Selected Legal Vehicles 
am.lbl.availLvs =Available Legal Vehicles
am.lbl.lvMapping =Legal Vehicle Mapping
am.lbl.awlOps =OPS USER
am.lbl.lvOps =LV OPS USER
am.lbl.entityType =Entity Type
am.lbl.empId =Employee ID
am.lbl.displayName =Display Name
am.lbl.lName =Last Name
am.lbl.fName =First Name
am.lbl.userReg =User Registration
am.lbl.loginId =Login ID
am.lbl.title = RGCS Admin Portal
#Magnus Settlement
st.lbl.magnusRejUpload.title = Magnus Rejection Upload 
ST.lbl.gridBatchRunID=Batch Run ID
ST.lbl.gridReqDate=Request Date
ST.lbl.gridLvName=Legal vehicle
ST.lbl.gridAcqBnkName=Acquiring Bank 
ST.lbl.gridStartDate=Start Date
ST.lbl.gridEndDate=End Date
ST.lbl.gridStatus=Status
ST.lbl.gridSuccRec=Success Records
ST.lbl.gridFailRec=Fail Records
ST.lbl.gridTotalRec=Total Records
st.lbl.gridSettleBatchID=Settlement Batch ID
st.lbl.gridFilename=File Name
st.lbl.filegendate=File Generated Date
st.lbl.setleMarkCnt=Settlement Mark Count
st.lbl.setleMarkAmt=Settlement Mark Amount
st.lbl.refMarkCount=Refund Mark Count
st.lbl.refMarkAmt=Refund Mark Amount
st.lbl.toSetFileGenCnt=Total Settlement File Generation Count
st.lbl.toSetFileGenAmt=Total Settlement File Generation Amount
st.lbl.toFileGenCnt=Total Refund File Generation Count
st.lbl.toFileGenAmt=Total Refund  File Generation Amount
st.lbl.setfileGenCnt=Settlement Count
st.lbl.setfileGenAmt=Settlement Amount
st.lbl.setfileGenRefCnt=Refund Count
st.lbl.setfileGenRefAmt=Refund Amount
st.lbl.tilNowSetRejCnt=Till Now Settlement Reject Count
st.lbl.tilNowSetRejAmt=Till Now Settlement Reject amount
st.lbl.tilNowSetRefRejCnt=Till Now Settlement Refund Reject Count
st.lbl.tilNowSetRefRejAmt=Till Now Settlement Refund Reject Amount
msg.lbl.noRec= No Record Found
st.lbl.stFileResDet=Settlement File Response Confirmation
msg.lbl.uploadMagRejFile=Upload Magnus Rejection File
ST.msg.settfileGenerated=Settlement file generated Request Submitted successfully
ST.msg.settFileGenPending=File generation is pending
ST.msg.errFileGenerating=Error occcured while generating settlement file
#ST.msg.successfileConfirm=File Confirm Successfully
ST.msg.fileGenReqSucess=Settlement file Regeneration Request Submited succesfully
ST.msg.errorUpdating=Error occurred while updating
ST.msg.succResConfirm=file response confirm successfully
ST.msg.errResConfirm=Error occured while confirming response file
ST.msg.magRejFileUpld=Magnus rejection file uploaded successfully
st.lbl.stmerchPerdcChrg.title=Merchant Periodic Charge
st.lbl.entrPgMerchId=Enter PG Merchan ID
st.msg.mid.name=Please enter valid Merchant ID
ST.lbl.gridbusiness.name=Merchant Business Name
ST.lbl.gridmid=PG Merchant ID
ST.lbl.gridChgType=Charge  Type
ST.lbl.gridFeeAmt=Fee Amount
ST.lbl.gridServiceTax=Service Tax
ST.lbl.gridTotAmt=Total Amount
msg.lbl.regenerate=Regenerate
msg.lbl.confirmation=Confirm
sm.lbl.settleType=Settlement Type
#msg.lbl.initActiveDeactiveApprovSuccess = Store has been successfully initiated for Activation / Deactivation approval.
#Merchant Boarding
msg.lbl.payTypeCC= CREDIT CARD
msg.lbl.payTypeDC= DEBIT CARD
msg.lbl.schmVisa=VISA
msg.lbl.schmMastc=MASTERCARD
msg.lbl.schmRpy=RUPAY
msg.lbl.schmAmex=AMEX
msg.lbl.schmMaes=MAESTRO
msg.lbl.schmSmnb=SMNB
msg.lbl.invalidMID=Invalid MID entered for the hosted bank. Please correct before sending for approval.

#Look up Configuration
sm.lbl.lkpType=LookUp Type
sm.lbl.lkpType1=LookUp Type

sm.lbl.lkpValue=LookUp Value
sm.lbl.lkpDescp=Description
sm.lbl.lkpSortOrder=Sorting Order
sm.lbl.lkpList=Lookup Information
sm.lbl.lkpConfig=Lookup Configuration
sm.lbl.lkpRejectReason=Reject Reason
sm.lbl.approvalSection=Approval Section
sm.lbl.lkpData=LookUp Config
sm.lbl.addLkp=Add Lookup

sm.msg.lkpType.invalid = Invalid lookup type value
sm.msg.lkpValue.invalid = Invalid lookup  value
sm.msg.lkpDescp.invalid = Invalid lookup  description 
sm.msg.lkpSortOrder.invalid = Invalid sorting order
sm.msg.lkp.noRecordFound= No data found in database.

sm.msg.lkp.creationSuccess= LookUp data created and pending for approval !
sm.msg.lkp.editSuccess = Lookup Edited and sent for approval.
sm.msg.lkp.creationFailed= LookUp data creation failed !
sm.msg.lkp.approved= LookUp data approved successfully by approver !
sm.msg.lkp.rejected= LookUp data rejected by approver !
sm.msg.lkp.pendingForApproval = LookUp data already pending for approval !
sm.msg.lkp.activationRequestSent=Request has been sent for activation.
sm.msg.lkp.requestFailed= Failed to process your request !
sm.msg.lkp.inactiveRequestSent=Request has been sent for deactivation.
sm.msg.lkp.activeInactiveRequestApproved=Request has been approved by approver.
sm.msg.formValidationFailed=Form validation failed.
sm.msg.lkp.duplication= Same lookup information is available in database.


#Tax master related messages
fm.msg.pendingTax=Tax has been successfully created and pending for approval
fm.msg.pendingReq=Request already pending for approval.
fm.msg.pendingUpdatedTax=Tax has been successfully updated and pending for approval
fm.msg.errorTax=Error occurred while Tax creation 
fm.msg.taxRejected=Request has been rejected by approver.
fm.msg.taxApproved =Request has been successfully approved by approver.
fm.msg.formValidationFailed=Form validation failed.
fm.msg.errorUpdateTax=Failed to updating tax information.
fm.msg.noChangeInData=You have not updated any data.
fm.lbl.tax.taxInfo=Tax Information
fm.lbl.tax.taxId=Tax Id
fm.lbl.tax.taxCode=Tax Code
fm.lbl.tax.taxName=Tax Name
fm.lbl.tax.taxPer=Tax Percentage
fm.lbl.tax.parentTaxCode=Parent Tax Code
fm.lbl.tax.legalVehicle=Legal Vehicle
fm.lbl.tax.effFrom=Effective From
fm.lbl.tax.effTo=Effective Till
fm.lbl.tax.action=Action
fm.lbl.tax.status=Status
fm.lbl.tax.tax=Tax
fm.lbl.tax.addEditTax=Add/Edit Tax
fm.lbl.tax.taxList=Tax List
fm.lbl.tax.appr=Approval
fm.lbl.tax.view=View
fm.lbl.tax.edit=Edit
fm.lbl.tax.add=Add Tax

#IP	Configuration
rm.lbl.fromIp=From IP
rm.lbl.toIp=To IP
rm.lbl.ip.status= Status
rm.lbl.ipList=IP Block
rm.lbl.ipConfig=IP Block
rm.lbl.ipRejectReason=Reject Reason
rm.lbl.approvalSection=Approval Section
rm.lbl.ipData=IP Block
rm.lbl.addIp=Add IP
rm.lbl.wfStatus=Workflow Status
rm.lbl.fromIpInt=Integer From IP
rm.lbl.toIpInt=Integer To IP
rm.msg.ip.invalid= Invalid Ip Address. From Ip should be greater than To Ip
rm.msg.toIp.invalid= Invalid from Ip 
rm.msg.toIp.invalid= Invalid to Ip  
rm.msg.ip.noRecordFound= No data found in database.
rm.msg.ip.creationSuccess= IP data created and pending for approval !
rm.msg.ip.creationFailed= IP data creation failed !
rm.msg.ip.approved= IP data approved successfully by approver !
rm.msg.ip.rejected= IP data rejected by approver !
rm.msg.ip.pendingForApproval = IP data already pending for approval !
rm.msg.ip.activationRequestSent=Request has been sent for approval.
rm.msg.ip.requestFailed= Failed to process your request !
rm.msg.ip.inactiveRequestSent=Request has been sent for deactivation.
rm.msg.ip.activeInactiveRequestApproved=Request has been approved by approver.
rm.msg.ip.formValidationFailed=Form validation failed.
rm.msg.ip.duplication= Same IP information is already available in data base.
rm.msg.fromIp.required=From IP can't be empty.
rm.msg.toIp.required=To IP can't be empty.
rm.msg.ip.invalid=Invalid Ip address

#Card Configuration
rm.lbl.cardNo=Card No
rm.lbl.status= Status
rm.lbl.cardConfig=Card Block
rm.lbl.cardRejectReason=Reject Reason
rm.lbl.approvalSection=Approval Section
rm.lbl.cardData=Card Block
rm.lbl.addCard=Add Card
rm.lbl.cardList=Card Block
rm.msg.cardNo.invalid = Invalid Card No .
rm.msg.noRecordFound= No data found in database.
rm.msg.card.creationSuccess= Request sent for approval !
rm.msg.card.creationFailed= Card No creation failed !
rm.msg.card.approved= Card No approved successfully by approver !
rm.msg.card.rejected= Card No rejected by approver !
rm.msg.card.pendingForApproval = Card No already pending for approval !
rm.msg.card.activationRequestSent=Request has been sent for activation.
rm.msg.card.requestFailed= Failed to process your request !
rm.msg.card.inactiveRequestSent=Request has been sent for deactivation.
rm.msg.card.activeInactiveRequestApproved=Request has been approved by approver.
rm.msg.card.formValidationFailed=Form validation failed.
rm.msg.card.duplication= Same Card No is already available in data base.

#Country Configuration
rm.lbl.countryConfig=Country Block
rm.lbl.countryData=Country Block
rm.lbl.addCountry=Add Country
rm.lbl.countryList=Country Block
rm.lbl.countryCode=Country Code
rm.lbl.countryName=Country Name
rm.lbl.countryRejectReason=Reject Reason
rm.lbl.status= Status
rm.lbl.approvalSection=Approval Section
rm.msg.countryCode.invalid = Invalid Country code.
rm.msg.countryName.invalid = Invalid Country name.
rm.msg.noRecordFound= No data found in database.
rm.msg.country.creationSuccess= Request has been sent for approval !
rm.msg.country.duplication= Same Country is already available in data base.
rm.msg.country.creationFailed= Country creation failed !
rm.msg.country.approved= Request approved successfully by approver !
rm.msg.country.rejected= Request rejected by approver !
rm.msg.country.pendingForApproval = Request already pending for approval !
rm.msg.country.activationRequestSent=Request has been sent for activation.
rm.msg.country.requestFailed= Failed to process your request !
rm.msg.country.inactiveRequestSent=Request has been sent for deactivation.
rm.msg.country.activeInactiveRequestApproved=Request has been approved by approver.
rm.msg.country.formValidationFailed=Form validation failed.

#Bin Configuration
msg.lbl.binList=BIN Block
msg.lbl.approvalList=Approval List
msg.lbl.requestType=Request Type
msg.lbl.binTable=BIN Table
msg.lbl.addBin=Add BIN
msg.lbl.user=User
msg.lbl.bin=BIN
msg.lbl.binInformation=BIN Block
msg.lbl.binConfiguration=BIN Block
rm.msg.Bin.requestFailed=Request has been failed.
rm.msg.Bin.deactivationRequestSent=Deactivation Request Sent for approval.
rm.msg.Bin.activationRequestSent=Activation Request Sent for approval.
rm.msg.Bin.pendingForApproval=BIN pending for approval
rm.msg.pendingBin=BIN request sent for approval.
rm.msg.BinRejected=BIN Rejected
rm.msg.BinApproved=BIN Approved
rm.msg.bin.duplication=Same BIN information is already available in data base.
msg.binApproved=BIN Approved Successfully.
msg.binRejected=BIN Rejected Successfully.

msg.lbl.dailyTransCnt=Daily Transaction Count
msg.lbl.dailyTransAmt=Daily Transaction Amount
msg.lbl.perTransLimit=Per Transaction Limit
msg.lbl.eciValueVisa = ECI Value (VISA)
msg.lbl.eciValueMaster = ECI Value (MASTER)
msg.lbl.eciValueMaestro = ECI Value (MAESTRO)

ST.msg.magRecUpload=Recurring file uploaded successfully.
msg.lbl.meBussType=Merchant Bussiness Type
me.msg.alreadyPendingGroup=Group is already pending for approval
msg.lbl.midExists = Entered MID already exists for Selected bank. Please enter different MID.

#26th-may
st.lbl.pgMerId=PG Merchant Id

#Manual Ledger Account
ft.lbl.templateDetls = Select Ledger Template
ft.lbl.templateName = Template Name
ft.lbl.trnCode = Transaction Code
ft.lbl.trnDesc = Transaction Description
ft.lbl.finCode = Finance Code
ft.lbl.finDesc = Finance Description
ft.lbl.templateDesc = Ledger Details
ft.lbl.crDrFlag = Credit/Debit Flag
ft.lbl.accNo = Account No.
ft.lbl.refNo = Reference No.
ft.lbl.effDate = Effective Date
ft.lbl.valDate = Value Date
ft.lbl.narration = Narration
ft.lbl.amount = Amount
ft.lbl.credit = Credit
ft.lbl.debit = Debit
ft.lbl.subTran = Sub-Transaction Description
ft.lbl.subTranCode = Sub-Transaction Code
ft.msg.submitManualLedger = Manual Ledger has been submitted and is pending for Approval
ft.msg.errorManualLedger = Error while submitting Manual Ledger Account
ft.lbl.individualLedger = View Ledger - Individual
ft.lbl.ledgerNo = Journal Voucher No.
ft.lbl.ledgerDetails = Ledger Details
ft.lbl.currencyName = Currency Name
ft.lbl.transactionDetails = Transaction Details
ft.lbl.search = Search
ft.lbl.ledgerAccount = View Ledger
ft.lbl.fromDate = From Date
ft.lbl.toDate = To Date
ft.lbl.date = Date
ft.lbl.download = Download as Excel
ft.lbl.jvType = Journal Voucher Type
ft.msg.errorAccNo = The entered Account Number is not valid
msg.lbl.docSaveSuccess=Documents Details Saved Sucessfully

#Chargeback Messages
st.lbl.viewChargeBack= View Chargeback 
st.lbl.txnRefNo=Txn Ref.No
st.lbl.pgMerId=PG Merchant Id
st.lbl.rrnNumber=RRN
st.lbl.arnNumber=ARN
st.lbl.fromDate=From Date
st.lbl.toDate=To Date
st.lbl.search=Search
st.lbl.chargeBackInfo=Chargeback information
st.lbl.respDate=Response Date
st.lbl.meBussName=Merchant Name
st.lbl.mid=MID
st.lbl.tid=TID
st.lbl.reqDate=Request Date
st.lbl.schemeCode=Scheme Code
st.lbl.tottxnAmt=Total Txn Amount
st.lbl.chrgBakAmt=Chargeback Amount
st.lbl.reasonCode=Reason Code
st.lbl.reasonDesc=Reason Description
st.lbl.statusDesc=Current Status Description
st.lbl.currstatusdesc=Current status Description
st.lbl.lvName=Legal Vehicle Name
st.lbl.acqBkName=Acquiring Bank Name
st.lbl.payTypeDesc=Pay Type Description
st.lbl.schemeName=Scheme Name
st.lbl.trndate=Txn Date
st.lbl.respdate=Response Date
st.lbl.tottrnamt=Total transaction amount
st.lbl.cardno=Card No.

st.msg.invalidPgMerId
st.msg.invalidTxnRefNo=Invalid Txn Ref.No
st.msg.invalidPgMerId=Invalid PG Merchant Id
st.msg.invalidRrnNumber=Invalid RRN
st.msg.invalidArnNumber=Invalid ARN
st.msg.invalidFromDate=Invalid From Date
st.msg.invalidToDate=Invalid To Date


#LimitManagement
st.msg.storeUpdateSuccess=Updated successfully.
st.msg.storeUpdateError=No records updated.
rm.limit.msg.physicalStorage=Physical Storage
rm.limit.msg.webStorage=Web Store
rm.lbl.limit.title=Limit Management
sm.lbl.selectAppRej=Please Select Approve/Reject Button.
sm.lbl.remarkMust=Please Enter Remarks.

sm.lbl.dailyTranCount=Daily Transaction Count
sm.lbl.dailyTranAmount=Daily Transaction Amount 

am_msg_requestNotPending=Request not pending for approval.

#Dispute
mp.lbl.addResSuc = Response has been successfully added.
mp.lbl.addResFail = Failed to add response.

mp.lbl.disCloseSuc = Dispute closed successfully.
mp.lbl.disCloseFail = Failed to Close dispute.

me.msg.alreadyApprovedStore = Store already approved
msg.lbl.mpiKey = MPI checksum key
msg.lbl.ebsKey = EBS checksum key
st.lbl.arn=ARN
st.lbl.mid=MID
st.lbl.arn=Remarks
st.lbl.amount=Amount
st.lbl.holdStatus=Hold Status
st.lbl.srNo=Sr No

st.lbl.uploadPayout=Upload Payout
st.lbl.manualmarking.rrn=RRN
st.lbl.rrn=RRN
st.lbl.refApp=Refund Approval
st.lbl.ManualSettlementMarking=Settlement Manual Marking
st.lbl.RefundMarking=Refund/Cancel Manual Marking 
st.lbl.orderId=Order Id 
st.lbl.reqAmtRef=Req Amt For Refund
st.lbl.reqAmtCan=Req Amt For Cancel
st.lbl.cancel=Cancel
st.lbl.paymentTypeName=Payment Type Name
st.lbl.merchantName=Merchant Name
st.lbl.acqBank=Acquiring Bank
st.lbl.netBank=Netbank Name
st.msg.netBankUpld=Netbank Reconciliation File Uploaded Successfully.
st.lbl.stlrecondwnld.grid.title=Netbank Reconciliation Download

msg.lbl.primaryOwner = Primary Owner 
msg.lbl.secOwner = Secondary Owner

#user mgt module
msg.lbl.action=Action
msg.lbl.roleDesc=Role Description
msg.lbl.roleName=Role Name
msg.lbl.roleId=Role Id
msg.lbl.userList=User List
msg.lbl.user=User
msg.lbl.role=Role
msg.lbl.addUser=Add User
msg.lbl.addRole=Add Role
msg.lbl.roleList=Role List
msg.lbl.contactNo=Contact No
msg.lbl.pinNo=Pin No
msg.lbl.entityId=Entity Id
msg.lbl.entityName=Entity Name
msg.lbl.userInfo=User Information
am.lbl.displayName=Display Name
am.lbl.funcname=Function Name
am.lbl.funcDesc=Function Description
am.lbl.funcInfo=Functional Information
am.lbl.pendingRole=Pending Role
am.lbl.pinCode=Pin Code
am.lbl.lockStatus=Lock Status
am.msg.locked=Locked
am.msg.unlocked=Unlocked
am.lbl.lastLoginDate=Last Login Date
am.lbl.lastPwdChangedDate=Last Password Changed Date
am.lbl.lastAcountLockedDate=Last Acount Locked Date
am.lbl.mappedLegalVehicles=Mapped Legal Vehicles
common.msg.lbl.null=Null
am.lbl.acStatus=Account Status
am.lbl.roleStatus=Role Status
sm.lbl.scheme.title=Scheme Master Table
sm.lbl.scheme.schemeList=Scheme List
sm.lbl.schemeInfo=Scheme Information

msg.lbl.primaryOwner = Primary Owner 
msg.lbl.secOwner = Secondary Owner

sm.msg.activateRejected=Activation or Deactivation request rejected by approver
sm.msg.activateApproved=Activation or Deactivation request approved by approver

st.msg.upltxtfile=Please upload .txt format file
st.msg.uplExcelfile=Please upload .xls format file
st.msg.uplRptfile=Please upload .rpt format file

st.lbl.pdffileName=PDF File
st.lbl.excelfileName=Excel File

#Facilitator 
sm.lbl.facilitatorBasicDetails=Facilitator Basic Details
sm.lbl.facilitatorName=Facilitator Name
sm.lbl.facilitatorCode=Facilitator Code
sm.lbl.facilitatorSch=Facilitator to Scheme Mapping
sm.lbl.facilitatorAssCode=ASS Code
sm.lbl.facilitatorAssFclCode=ASSFCL Code
sm.lbl.facilitator=Facilitator
sm.msg.facilitatorCode.required=Please Enter Facilitator Code
sm.msg.facilitatorName.required=Please Enter Facilitator Name
sm.msg.pendingFacilitator= Facilitator has been successfully created and pending for approval
sm.msg.errorFacilitator=Error occurred while Facilitator creation
sm.lbl.facilitatorMasterTable=Facilitator Master Table
sm.lbl.facilitator=Facilitator
sm.lbl.addFacilitator=Add Facilitator
sm.lbl.facilitatorList=Facilitator List
sm.lbl.facilitatorID=Facilitator Id
sm.lbl.facilitatorName=Facilitator Name
sm.lbl.facilitatorCode=Facilitator Code
sm.msg.FacilitatorPending=Facilitator pending for approval
sm.lbl.facilitatorInformation=Facilitator Information
sm.lbl.editFacilitator=EDIT FACILITATOR
sm.lbl.deactactFac=DEACTIVATE/ACTIVATE FACILITATOR
sm.msg.RejectByApprover=Facilitator Rejected By Approver
sm.msg.facApproved=Facilitator Has Been Successfully Approved
sm.msg.facilitatorPending=Already pending for approval
sm.msg.facilitatorActivationPending=The request has been sent for approval for Activation
sm.msg.facilitatorPending=Already pending for approval
sm.msg.errorFacilitatorDeactivation=Error while facilitator deactivate process
sm.msg.facilitatorDeactivationPending=The request has been sent for approval for Deactivation
sm.msg.errorUpdateFacilitator=Error occurred while Facilitator update
sm.msg.pendingUpdatedFacilitator=Facilitator has been successfully updated and pending for approval
sm.lbl.fac=ADD FACILITATOR
sm.lbl.activateFacilitator=ACTIVATE FACILITATOR
sm.lbl.deactivateFacilitator=DEACTIVATE FACILITATOR
sm.lbl.facilitatorMaster=Facilitator Master
sm.err.invalidFclCode=Facilitator Code Already Exists

am_msg_loginFailed=Same browser can't be used for multiple login at a time.
ST.lbl.gridNetBankName=Bank name

msg.eciVisa.val = 05,06,07
msg.eciMaster.val = 00,01,02
msg.eciMaestro.val = 00,01,02
sm.lbl.wlRatio=WL Ratio
sm.lbl.bankRatio=Bank Ratio
sm.lbl.bsfType=BSF Type

msg.lbl.chargeType = Charge Type
msg.lbl.both = Both
msg.lbl.msfHigher=Whichever Higher
msg.lbl.msfLower=Whichever Lower



am.lbl.sysExcep = System Exceptions
am.lbl.search = Search
am.lbl.home = Home



sm.lbl.category= Category

fm.msg.duplicateTaxCode=Same Tax code already exists .Please enter a valid Tax code.


msg.lbl.balsettcount=Pending Settlement Count
msg.lbl.balsettamt=Pending Settlement Amount

sm.lbl.convenienceFeeConfigMaster=Convenience Fee Configuration Master
sm.lbl.convenienceFeeConfig=Convenience Fee Configuration
sm.lbl.convenienceFeeAg=Convenience Fee Agreement
sm.lbl.convenienceFee=Convenience Fee
sm.lbl.convenienceUpdate=Convenience Fee Update
sm.lbl.pgMerchantId=PG Merchant Id
sm.lbl.pgMerchantName=Merchant Name
sm.lbl.legalName=Legal Name
sm.lbl.dbaName=Merchant Name
sm.lbl.contactPersonName=Contact Person Name
sm.lbl.mobile=Mobile
sm.lbl.payType=Pay Type
sm.lbl.scheme=Scheme
sm.lbl.netBank=Net Bank
sm.lbl.slabs=Slabs
sm.lbl.convFee=Convenience Fee
sm.lbl.serviceTax=Service Tax
sm.lbl.remove=Remove
sm.lbl.agreementConfig=Agreement Config
sm.err.slabs=Please Enter Slabs
sm.err.convFee=Please Enter Convenience Fee
sm.err.servTax=Please Enter Service Tax
sm.lbl.conveyanceFeeConfigMaster=Convenience Fee Configuration Master
sm.lbl.conveyanceFee=Convenience Fee
msg.lbl.allConvenienece=-----All-----
fm.msg.duplicateTaxCode=Same Tax code already exists .Please enter a valid tax code.

me.msg.pendingUpdatedPayTypeMsf=Payment Type has been successfully updated and is pending for approval 
sm.msg.lv.duplicateRequest=Legal Vehicle already exists. Please enter a valid Legal Vehicle Name / Code.
sm.msg.duplicateNetbank=Netbank already exists. Please enter a valid Netbank Code.
sm.msg.duplicateScheme=Scheme already exists with this code. Please enter a valid scheme code.
am.msg.duplicateRole=Role already exists with this name. Please provide a valid name.
am.msg.userRequestApproved=User request approved.

msg.lbl.merLogo=Upload Merchant Logo (Only png files And Dimensions : 150 * 43)

msg.lbl.paymentRefNo=Payment Reference Number

ft.lbl.transDesc = Trans Description
ft.lbl.finaCode = Fin Code
ft.lbl.subTrans = Sub Trans Description
ft.lbl.glCode = GL Code
ft.lbl.glName = GL Name

msg.lbl.merLogoUpl = Uploaded Merchant logo

msg.lbl.businessEntity= Business Entity
msg.lbl.trainingSched= Training Schedule
msg.lbl.merType= Merchant Type
msg.lbl.noOfTerminals=What is the total no. of terminals you want to add ?
msg.lbl.seId = SeID
msg.lbl.seRemarks = SeRemarks
msg.lbl.modelNo = Model No.
msg.lbl.terminals = No. of terminals
msg.lbl.printName=Merchant Charge Slip Print Name
msg.lbl.ownerShip= Ownership

msg.lbl.tccDesc=TCC Descriptor
msg.lbl.ticketSize=Projected AVG Ticket Size
msg.lbl.rental=Rental 
msg.lbl.rentalfreq= Rental Frequency Value
msg.lbl.msfIncentive=MSF Incentive  
msg.lbl.advRentalAmt=Advance Rental Amount 
msg.lbl.advRentalFreq=Advance Rental Frequency 
msg.lbl.advStupFee=Advance Setup Fee
msg.lbl.advRent=Advance Rent 
msg.lbl.advSetup=Advance Setup
msg.lbl.merIncentive=Merchant Incentive
msg.lbl.cadmid=CAD MID 
msg.lbl.newPaymentAdvice=New Payment Advice Value
msg.lbl.tipPercent=Tip Percent Value
msg.lbl.cashBackOnly=Cash@POS

msg.lbl.cashBackPur=Sale with Cash Back
msg.lbl.noOfTerminalsEdit=Total no. of Terminals
msg.lbl.noOfTerminalsAddEdit=Do you want to add more terminals?


msg.lbl.TidSetup=TID Setup
msg.lbl.ModelName=Model Name

st.msg.fileTxt=Please Select .txt or .xls format file
st.msg.fileExcel=Please Select .xls format file
st.msg.fileRpt=Please Select .rpt format file
st.lbl.generaterequest.btn=Generate
msg.lbl.VisaMasterGenDet=Visa Master Details
msg.lbl.VisaMasterFileGen=Visa Master File Generate
ST.lbl.gridNetBankName=Bank Name
ST.msg.binFile=Bin File Uploaded successfully


msg.lbl.promptFlag=Prompt & Process Flag
msg.lbl.tipPercent=Tip Percent

msg.lbl.cashBackAmt=Maximum Cash@POS
msg.lbl.preAuthLimit=Maximum Pre Auth Percentage (above txn amt)

ST.lbl.gridBintype=Bin Type

sm.lbl.fromBin=From BIN
sm.lbl.toBin=To BIN
sm.lbl.binType=BIN Type
sm.lbl.binTypeInternational=International
sm.lbl.binTypeDomestic=Domestic
sm.lbl.cardType=Card Type
sm.lbl.schemeType=Scheme Type
sm.lbl.schemeApproval=Scheme Approval
sm.lbl.onusApproval=ONUS Flag
sm.lbl.currency=Currency
sm.lbl.issueBank=Issuing Bank
sm.lbl.binMapping=BIN Mapping
sm.lbl.prevCardType=Product/Type Of Card
sm.msg.pendingBinMapping= BIN Mapping has been successfully Created and Pending for Approval
sm.msg.errorBinMap=Error occurred while BIN Map creation
sm.msg.BinMapPending=BIN Mapping is pending for approval
sm.msg.fromBin.required=Please enter the From BIN
sm.msg.toBin.required=Please enter the To BIN
sm.msg.fromBin.invlaid=From BIN Is Less Than The Required Range
sm.msg.toBin.invlaid=To BIN Is Less Than The Required Range
sm.lbl.BinMapMasterTable=BIN Mapping Master Table
sm.lbl.BinMapBasicDetails=BIN Mapping Basic Details
sm.lbl.addBinMap=Add BIN Mapping
sm.lbl.binMapList=BIN Map List
sm.lbl.editBinMap=EDIT BIN MAP
sm.lbl.binMappingList=BIN Mapping List
sm.lbl.activateBinMap=ACTIVATE BIN MAP
sm.lbl.deactivateBinMap=DEACTIVATE BIN MAP
sm.lbl.Approved=Approved
sm.lbl.NotApproved=Not Approved
sm.lbl.binMapInformation=BIN Map Information
sm.msg.binMapApproved=BIN Map Has Been Successfully Approved
sm.msg.binMapDeactivationPending=The Request Has Been Sent For Deactivation
sm.msg.errorBinMapDeactivation=Error while BIN Mapping deactivate process
sm.msg.binMapPending=BIN Mapping pending for approval
sm.msg.binMapActivationPending=The Request Has Been Sent For Activation
sm.msg.BinMapPending=BIN Map pending for approval
sm.msg.pendingUpdatedBinMap=BIN Mapping has been successfully updated and pending for approval
sm.msg.errorUpdateBinMap=Error occurred while BIN Map update
sm.lbl.abinMap=ADD BIN MAP
sm.lbl.activate=ACTIVATE
sm.lbl.deActivate=DEACTIVATE
sm.lbl.smsDmsFlag=SMS/DMS
sm.lbl.sms=SMS
sm.lbl.dms=DMS
sm.msg.approved=BIN Map Has Been Succesfully Approved
sm.msg.rejected=BIN Map Has Been Rejected 
sm.err.alreadyExist=BIN Map Range Already Exists
sm.lbl.yes=Yes
sm.lbl.no=NO

st.msg.netBankReCan=Netbank file request generated successfully.
st.msg.netBankRequest=Netbank file generation request already pending.
st.msg.netBankRequest=Error occured while generating netbank request.


mer.lbl.srcChnIdReqd= Please enter Sourcing Channel
mer.lbl.invchnCode= Please enter valid Channel Code
mer.lbl.mebussnameReqd= Please enter Store Name
mer.lbl.legalStrNameReqd = Please enter Legal Store Name
mer.lbl.invfeeType= Please enter valid Fee Type
mer.lbl.invstoreType= Please enter valid Store Type
mer.lbl.merUrlReqd= Please enter Merchant Website URL
mer.lbl.panNoReqd= Please enter PAN no.
mer.lbl.invLenpanNo= Please enter Valid PAN no.
mer.lbl.invLenmebussname= Please enter valid Store Name
mer.lbl.invLenlegalStrName= Please enter valid Legal Store Name
mer.lbl.segmentReqd= Please enter Segment
mer.lbl.awlmccReqd= Please enter AWL MCC
mer.lbl.merUrlReqd= Please enter  Merchant Website URL
mer.lbl.applicationNoReqd=Please enter Application No.
mer.lbl.constitutionIdReqd=Please enter Merchant Business Type
mer.lbl.merUrlinv=Please enter valid Merchant Website URL
mer.lbl.applicationNoinv=Please enter valid Application No.
mer.lbl.stAdd1Reqd=Please enter Address
mer.lbl.stAdd2Reqd=Please enter Address
mer.lbl.stAdd3Reqd=Please enter Address
mer.lbl.stPincodeReqd=Please enter Pincode
mer.lbl.strCntNameReqd=Please enter Full Name
mer.lbl.strCntPhoneReqd=Please enter Phone number
mer.lbl.strCntMobileReqd=Please enter Phone number
mer.lbl.strCntEmailReqd=Please enter Login Email Id
mer.lbl.strEmailIdReqd=Please enter Email Id
mer.lbl.corpIdinv= Please enter valid Corporate Identification Number
mer.lbl.bussExpinv=Please enter valid Years in Business
mer.lbl.bussCompetinv=Please enter valid details
mer.lbl.totShopinv=Please enter valid details
mer.lbl.bussDefinv=Please enter valid details
mer.lbl.stCountryReqd= Please enter Country
mer.lbl.stStateReqd= Please enter State
mer.lbl.stCityReqd= Please enter City
mer.lbl.stAdd1Inv=  Please enter valid Address
mer.lbl.stAdd2Inv=Please enter valid Address
mer.lbl.stAdd3Inv=Please enter valid Address
mer.lbl.stPincodeInv=Please enter valid Zip Code
mer.lbl.strCntNameInv=Please enter valid Name
mer.lbl.strCntPhoneInv=Please enter valid Phone Number
mer.lbl.strCntMobileInv=Please enter valid Mobile
mer.lbl.strCntFaxInv=Please enter valid Fax
mer.lbl.strCntEmailInv=Please enter valid Email Id
mer.lbl.billingAddress1Inv=Please enter valid Billing Address
mer.lbl.billingAddress2Inv=Please enter valid Billing Address
mer.lbl.billingAddress3Inv=Please enter valid Billing Address
mer.lbl.billingPincodeInv=Please enter valid Billing Pincode
mer.lbl.billingPhone1Inv=Please enter valid Billing Phone
mer.lbl.billingPhone2Inv=Please enter valid Billing Mobile
mer.lbl.billingFaxInv=Please enter valid Billing Fax
mer.lbl.rm_mobilenumberInv=Please enter valid RM Mobile number
mer.lbl.rm_emailidInv= Please enter valid RM Email Id
mer.lbl.holdPayFlagInv= Please enter valid HoldPayFlag
mer.lbl.partialFlagInv= Please enter valid PartialFlag
mer.lbl.settleAgdelFlagInv= Please enter valid SettleAgdelFlag
mer.lbl.agreementNoInv= Please enter valid Agreement No
mer.lbl.branch_official_desgInv= Please enter valid Branch Official Designation
mer.lbl.branch_official_mobileInv= Please enter valid  Branch Official Mobile No
mer.lbl.branch_sol_idInv= Please enter valid Branch SOL ID
mer.lbl.branch_staff_nameInv= Please enter valid Branch Staff Name
mer.lbl.branch_staff_pfiInv= Please enter valid Branch Staff PFI
mer.lbl.branch_official_nameInv=Please enter valid Branch Staff PFI
mer.lbl.branch_staff_svInv=Please enter valid Branch Official Name
mer.lbl.InvLength= Invalid length. Please enter valid details
mer.lbl.invData= Please enter details to proceed
mer.lbl.StringInvalid=Please enter only characters
mer.lbl.NumberInvalid=Please enter only numbers
mer.lbl.EmailInvalid=Please enter valid Email
mer.lbl.Urlinv= Please enter valid URL
mer.lbl.AddressInvalid= Please enter valid Address
mer.lbl.payTypeReqd= Please enter a payment Type
mer.lbl.fieldReqd=Please enter details to proceed
mer.lbl.SchemeReqd= Please enter a scheme
mer.lbl.alphaNum= Please enter only alphanumeric characters

mer.lbl.trainingSchedReqd=Please select training schedule
mer.lbl.seIdReqd=Please enter seId
mer.lbl.seIdRemarksReqd=Please enter se remarks
mer.lbl.ownershipReqd=Please select the ownership
mer.lbl.chrgSlipNameReqd=Please enter charge slip name
mer.lbl.dccPromptReqd=Please select dcc prompt
mer.lbl.maxCashBakAmtReqd=Please enter maximum cash back amount
mer.lbl.noOfTerminalsReqd=Please enter number of terminals
mer.lbl.dccPromptInvalid= Please enter valid Prompt & Process Flag
mer.lbl.maxCashBakAmtLength=Amount length will not be greater than 4
mer.lbl.noOfTerminalsLength=No of terminals are only two digits

#Ipg
mer.lbl.eciVisaReqd=Please enter ECI value(Visa)
mer.lbl.eciMasterReqd=Please enter ECI value(Master)
mer.lbl.eciMaestroReqd=Please enter ECI value(Maestro)
mer.lbl.eciVisaLength=Value should not be greater then 14 digit
mer.lbl.eciMasterLength=Value should not be greater then 14 digit
mer.lbl.eciMaestroLength=Value should not be greater then 14 digit
mer.lbl.meIntegAppReqd=Please select the integration approach
mer.lbl.requestUrl1Reqd=Please enter the url
mer.lbl.requestUrl1Length=Url should not be greater than 200 chars 
mer.lbl.pcIdssLength=pcIdss length should not be greater than 20
mer.lbl.mpiKeyLength=MPI checksum key length should not be greater than 50
mer.lbl.ebsKeyLength=EBS checksum key length should not be greater than 50
mer.lbl.urlInvalid=Please enter valid URL

#Blacklist
mer.lbl.typeFlagReqd=Please select list type
mer.lbl.fromBinLength=From bin value should be 9 digits
mer.lbl.toBinLength=To bin value should be 9 digits

#Acquiring Rule
mer.lbl.lvIdReqd=Please select legal vehicle
mer.lbl.acqBankIdReqd=Please select acquiring bank
mer.lbl.payTypeIdReqd=Please select payment type
mer.lbl.schemeIdReqd=Please select scheme type
mer.lbl.effFromReqd=Please select from date
mer.lbl.effTillReqd=Please select to date
mer.lbl.priorityReqd=Please select the priority

#Url
mer.lbl.webAvalReqd=Please select e-commerce website available or not. 
mer.lbl.webAvalMandatory=Please select e-commerce website available or not.
mer.lbl.webUrlLength=Merchant website url should not be greater than 200 chars
mer.lbl.privPolLength=Privacy policy should not be greater than 200 chars
mer.lbl.canRefPolLength=Cancellation/refund policy should not be greater than 200 chars
mer.lbl.terCondPolLength=Terms and condition policy should not be greater than 200 chars 
mer.lbl.contactUsLength=Contact us url should not be greater than 200 chars
mer.lbl.faqUrlLength=FAQ url should not be greater than 200 chars
mer.lbl.prodUrlLength=Product url should not be greater than 200 chars
mer.lbl.abtUsLength=About us url should not be greater than 200 chars
mer.lbl.webManageUrlLength=Website manage url should not be greater than 200 chars
mer.lbl.comNameUrlLength=Company owns url should not be greater than 200 chars
mer.lbl.hostUrlReqd=Please select hosting URL
mer.lbl.hostUrlMandatory=Please select hosting URL
mer.lbl.solUrlLength=Solution web url should not be greater than 200 chars
mer.lbl.e_comUrlLength=E-commerce strategy should not be greater than 200 chars
mer.lbl.specifyUrlLength=Specify url should not be greater than 200 chars
mer.lbl.webUrlInvalid=Please enter Valid URL


#Merchant Rating
mer.lbl.constitutionIdReqd=Please select the constitution
mer.lbl.vintageIdReqd=Please select the vintage
mer.lbl.premisesIdReqd=Please select the premises
mer.lbl.ticketSizeIdReqd=Please select the average ticket size
mer.lbl.rateLocationIdReqd=Please select the location
mer.lbl.financialIdReqd=Please select the financial
mer.lbl.merchantCatIdReqd=Please select the merchant category
mer.lbl.deliveryTimeLineIdReqd=Please select the delivery timeline
mer.lbl.merchantOpsIdReqd=Please select merchant OPS and security
mer.lbl.webSecurityIdReqd=Please select the web security
mer.lbl.visa_VerificationReqd=Please select VMTS
mer.lbl.master_VerificationReqd=Please select MATCH
mer.lbl.cibilFlagReqd=Please select cibil
mer.lbl.waiverRmRkLength=Waiver remark should not be greater than 100 cahracters
mer.lbl.merVirtualHandler=Virtual Address Handler

st.lbl.header=MATCH/VMTS Upload
st.lbl.fileType=File Type
st.msg.succesfulUpld=File Uploaded Successfully
st.msg.failedUpld=File Uploading Failed
st.msg.errFileFormat=Please Upload a .txt File Provided by MATCH System
st.msg.errEmptyFile=File Cannot be Empty
st.lbl.tabHead=List MATCH/VMTS
st.lbl.matchVmtsGen=MATCH/VMTS Generate

st.lbl.merchant=Merchant

st.msg.settlementUpld=Settlement file uploaded successfully.
st.lbl.settlementFileUpload=Settlement Refund/Cancel File upload
st.lbl.select.settlement=Settlement
st.lbl.select.refund=Refund
st.lbl.select.cancel=Cancel
st.lbl.filter=Filter
st.lbl.orderId=Order Id
st.lbl.payType=Pay Type
st.lbl.dateTime=Date Time
st.lbl.scheme=Scheme
st.lbl.tranAmtRefCan=Transaction Amount 
st.lbl.merchantID=Merchant Id
st.lbl.trnamt=Transaction Amount INR
st.lbl.settleAmt=Settled Amount INR
st.lbl.settleReqAmt=Settlement Request Amount INR
st.lbl.tranAmtRefCan = Transaction Amount
st.lbl.settlement.marking= Settlement Marking
st.lbl.settlement.markDtls=Settlement Marking Details
st.lbl.confirm=Confirm
st.lbl.approve=Approve
st.lbl.invalidRequest = Invalid request for settlement for transaction reference no :
st.lbl.invalidRequest = Invalid request for settlement for transaction reference no :
st.lbl.procReqFail = Failed to submit settlement request for transaction reference no : XXXX
st.lbl.successProcReq = Settlement request submitted for XXXX records 
st.lbl.amtRef =  Refund/Cancel Amount
st.lbl.refcanTitle=Cancellation / Refund
st.lbl.AppRefcanTitle=Approve Cancel/Refund request
st.lbl.amtCancel = Cancel Amount

sm.lbl.manufacturerId=Manufacturer Id
sm.lbl.manufacturerName=Manufacturer Name
sm.lbl.manufacturerCode=Manufacturer Code
sm.lbl.manufacturerMaster=Manufacturer Master
sm.lbl.manufacturerDetails=Manufacturer Details
sm.lbl.productDetails=Product Details
sm.lbl.emiProdCode=EMI Product Code
sm.lbl.ProdName=Product Name
sm.lbl.itemCode=Item Code
sm.msg.pendingManufacturer= Manufucturer has been successfully created and pending for approval
sm.msg.errorManufacturer=Error while Manufucturer approval process
sm.msg.manufacturerRejectByApprover= Manufacturer has been rejected by approver
sm.msg.manufacturerApproved= Manufacturer has been successfully approved
sm.msg.manufacturerPending=Already pending for approval
sm.msg.pendingUpdatedManufacturer=Manufacturer has been successfully updated and pending for approval
sm.msg.errorUpdateManufacturer=Error occurred while Manufacturer update 
sm.msg.manufacturerActivationPending=Request Has Been Sent For Activation
sm.msg.manufacturerDeactivationPending=Request Has Been Sent For Deactivation
sm.msg.errorManufacturerDeactivation=Error while Manufacturer deactivate process
sm.lbl.addManufacture=ADD MANUFACTURER
sm.lbl.editManufacture=EDIT MANUFACTURER
sm.lbl.actManufacture=ACTIVATE MANUFACTURER
sm.lbl.deActManufacture=DEACTIVATE MANUFACTURER
st.msg.manufactId.invalid=Please Enter a Valid ManufactId
sm.lbl.uploadFile=Upload File
sm.lbl.uploadManufactList=Upload Manufacturer List
sm.err.manufactCode= Manufacturer Code Already Exists

st.lbl.refresh.btn=Refresh
msg.lbl.discCont =  Content Disclaimer
msg.lbl.instDisclmr = Instrument Disclaimer
msg.lbl.progDisclmr = Program Disclaimer
msg.progDisc.val = HDFC Bank is not responsible for the interest charged by the bank and cannot refund the interest amount in case of cancellation, refund or pre-closure.
msg.instDisc.val = HDFC Bank does not charge any processing fee for availing EMI.

msg.lbl.removeUploadSuccess= Uploaded file is removed successfully.
msg.lbl.removeUploadError= Failed to remove the uploaded file.

sm.err.binMappFormFailed=Validation failed. Please check below detailed error.

sm.err.exceedsLimit=CashBack Amount Exceeds Maximum Limit
sm.err.required=CashBack Amount Required
sm.lbl.maxCCCashBackLimit=Sale With CashBack Limit(Credit Card)
sm.lbl.maxDCCashBackLimit=Sale With CashBack Limit(Debit Card)
sm.err.cashBackNotZero=CashBack Should Be Greater Than Zero

st.lbl.chargeback=Chargeback ID
st.lbl.docInfo=Document Information
st.lbl.pgMerchantId=PG Merchant ID

st.lbl.Payee =Payee file
st.lbl.Payer =Payer file
st.lbl.Acquirer =Acquirer file
st.lbl.Issuer =Issuer file
st.lbl.MerAcq =Merchant Acquirer file
st.lbl.MerIss =Merchant Issuer file
st.lbl.uploadType= File Upload Type
st.lbl.settlementFileUpl =Settlement File upload
st.lbl.fileFmtNotSupported = File format not supported Upload only .miib extension file

#CBS FILE UPLOADS
st.lbl.cbsInwardFile = CBS Inward File
st.lbl.cbsOutwardFile = CBS Outward File
st.lbl.cbsFileUpl = CBS File upload
st.msg.cbsUpld = CBS file uploaded successfully.

msg.merchant.valid=Please enter valid merchant name
sm.lbl.acqDisclaimer=Acquirer Disclaimer

st.refund.valid=Refund not allowed for this merchant
st.cancel.valid=Cancel not allowed for this merchant
st.settlement.valid=Settlement not allowed this for merchant

sm.lbl.acqInrDisclaimer=INR Disclaimer
sm.lbl.acqPreDisclaimer=Preauth Disclaimer


sm.lbl.acqInrDisclaimer=INR Disclaimer
sm.lbl.acqPreDisclaimer=Preauth Disclaimer
sm.lbl.schemeDisclaimer=DCC Disclaimer

#tax
fm.msg.taxAlreadyExist=Tax exist within a date range


mer.lbl.premisesId=Please enter Premises Type
mer.lbl.vintageId=Please enter Vintage Type
mer.lbl.ticketSizeId=Please enter Average Ticket Size
mer.lbl.lenInvChqno=Please enter a cheque no. of maximum 30 characters.
mer.lbl.chqAmtInv=Please enter Cheque amount maximum 10 characters.
mer.lbl.AmountInvalid=Please enter a valid Amount
mer.lbl.chqReamrksInv=Please enter Cheque Remarks of maximum 100 characters.
mer.lbl.invFrombin=To Bin should be greater than From Bin

msg.lbl.kioskId = Kiosk Id

mer.lbl.daily_Tran_Limit=Please enter daily transaction limit
mer.lbl.numeric=Please enter numeric values
mer.lbl.paymentBy=Please select Payment By values
mer.lbl.mer_AccNo_OpDt=Please select valid date format

st.lbl.Merchant=Merchant Business Name
st.lbl.merchant=Merchant Business Name

sm.lbl.ica=ICA

mer.lbl.invMeName=Only alphanumeric and  "& . -  space" are allowed
st.msg.nessieReqSuccess=Nessie file request given successfully
st.lbl.nessieFileDwnld=Nessie File 
st.msg.nessieReqPending=Nessie file generation request already pending.
st.msg.nessieReqSuccess=Nessie file request generated successfully.
st.msg.nessieReqError=Error occures while generating nessie file request.


#PSP
sm.lbl.addPSP = Add PSP
sm.lbl.pspConfig = PSP Config
sm.lbl.pspName = PSP Name
sm.lbl.pspCode = PSP Code
sm.lbl.pspType= PSP Type
sm.lbl.pspSch=Legal Vehicle to Scheme Mapping
sm.lbl.bnkPsp = Bank PSP

#mpDispute
me.lbl.addDisSuc = Query has been raised successfully.
me.lbl.addDisEmFail =  Query has been raised successfully but failed to send email.
me.lbl.addDisFail = Something went wrong while raising query.
me.lbl.addResSuc = Response has been successfully added.
me.lbl.addResFail = Failed to add response. 
me.lbl.addDisFileFail = Failed to upload file.
mp.lbl.downloadFile = Download file

sm.msg.spocEmail.required = Please enter the spoc Email
sm.msg.spocPhone.invalid = Invalid spoc phone
sm.msg.spocUrl.required = Please enter the spoc URL
sm.msg.spocName.required = Please enter the spoc name
sm.msg.errorPspApprove=Error PSP Config Vehicle approval process
sm.msg.pendingPsp= PSP Config request submitted successfully
sm.msg.psp.duplicateRequest = PSP Config already exists. Please enter a valid PSP Name
sm.lbl.PspConfigList=PSP Config List
sm.lbl.pspConfigId = PSP Config ID
sm.msg.PspPending=Already pending for approval
sm.lbl.pspInfo=PSP Config Information
#sm.msg.pspRejectByApprover = Add PSP Config request rejected by approver
sm.msg.pspRejectByApprover = PSP Config request rejected by approver
sm.msg.pspApproved =PSP has been successfully approved

sm.msg.editPspRejectByApprover = Edit PSP Config request rejected by approver
#sm.msg.addPspApproved =Add PSP Config request approved successfully
sm.msg.addPspApproved =PSP Config request approved successfully
sm.msg.editPspApproved =Edit PSP Config request approved successfully

sm.lbl.spocName = Spoc Name
sm.lbl.spocEmail = Spoc Email
sm.lbl.spocPhone = Spoc Phone
sm.lbl.pspUrl = PSP URL
sm.lbl.pubCert = Upload Public Key/Certificate
sm.lbl.chnCode = MPOS
sm.msg.pspPending = Already pending for approval
sm.msg.pendingUpdatedPsp = PSP has been successfully updated and pending for approval
sm.msg.errorUpdatePsp = Error occurred while PSP update
sm.lbl.fixPer = Fixed Per(%)
sm.lbl.fixAmt = Fixed Amount

#Activate/Deactivate labels
sm.msg.pspDeactivationPending = The request has been sent for approval for Deactivation
sm.msg.errorPspDeactivation = Error while PSP deactivate process
sm.msg.pspActivationPending = The request has been sent for approval for Activation

#Block/Unblock Mobile number and Virtual Address
sm.msg.blockDeactivationPending = The Unblock request has been sent for approval 
sm.msg.blockDeactivationPending = The request has been sent for approval 
sm.msg.errorBlockDeactivation = Error while Blocking process
sm.msg.blockActivationPending = The Block request has been sent for approval
sm.lbl.blockType = Block Type
sm.lbl.blockId= Block ID
sm.lbl.wfId = WorkFlow ID
sm.lbl.lvBasicDetailsBlock= Block Virtual Address or Mobile Number
sm.lbl.mobileNumber = Mobile Number
sm.lbl.fvirtualAddress = Full Virtual Address 
sm.lbl.addNewBlock = Add Block
sm.lbl.approvedBlock= Request Approved Successfully
sm.lbl.rejectAddBlock=Request rejected by approver
sm.lbl.requestName= Request Name
sm.lbl.requestId = Request Id
sm.msg.pendingAddBlock= Request Already Pending for Approval
sm.msg.pendingReq=  Request Already Pending for Approval.
sm.msg.pendingUpdatedAddBlock= Update Add Block Pending for Approval
sm.msg.errorAddblock= Error Creating Block 
sm.msg.addBlockRejected= Add Block has been Rejected Successfully.
sm.msg.addBlockApproved = Request has been Successfully Approved.
sm.lbl.blockedList=     Block Virtual Address
sm.lbl.pendingblockedList= Pending Block List
Pattern.roleVO.blckType=Select Block Type
invalid.blckType = Invalid Block Type 
sm.msg.mobileNo.lenInvalid= Maximum Length of Mobile Number Is 10
sm.msg.mobileNo.invalid= Mobile Number Invalid 
sm.lbl.addedBlock.info = VPA Information
sm.lbl.block.id= Block Id



####Payment Type
me.msg.issueBnk.required = Please select bank name
me.msg.payPeriod.required = Please Select Payment Period Type

me.msg.ifscCode.required=Please enter IFSC Code
me.msg.accNo.required=Please enter account number
me.msg.maxTrans.required=Please enter per day transaction count
me.msg.maxTranAmt.required=Please enter per day transaction limit
me.msg.perTranLimit.required=Please enter per transaction limit amount
me.msg.extMid.required=Please enter External MID
me.msg.extMid.invalid = Please enter valid External MID
me.msg.extTid.invalid = Please enter valid External TID



me.msg.ifscCode.invalid=Please enter valid IFSC Code
me.msg.ifscCode.lenInvalid= IFSC Code should not be greater than 11 digit 

me.msg.accNo.invalid=Please enter valid account number
me.msg.accNo.lenInvalid=Account number should not be greater than 16 digit 

me.msg.maxTrans.invalid=Please enter valid per day transaction count
me.msg.maxTrans.Exceeded=Per day max transaction count exceeded
me.msg.maxTrans.lenInvalid=Per day transaction limit should not be greater than 5 digit 

me.msg.maxTranAmt.invalid=Please enter valid per day transaction limit
me.msg.maxTransAmt.Exceeded=Per day max transaction amount exceeded
me.msg.maxTranAmt.lenInvalid=Per day transaction limit amount should not be greater than 8 digit 

me.msg.perTranLimit.invalid=Please enter valid per transaction limit
me.msg.PerTransAmt.Exceeded=Per transaction limit amount exceeded
me.msg.perTranLimit.Exceeded.MaxTranLimit = Per transaction limit should be less than per day transaction limit 

sm.msg.pspApprPending = The request has been sent for approval for Manage Fee
sm.msg.pendingFee = Manage fee request submitted successfully
sm.msg.errorFee = Error in submitting fee request
sm.lbl.ReqName = Request Name
sm.msg.feeRejectByApprover = Fee Config request rejected by approver
sm.msg.feePspApproved = Fee Config request approved successfully
mer.lbl.merchantVirtualAdd = Merchant Virtual Address
mer.err.merchantVirtualAdd = Please enter Merchant Virtual Address
mer.err.requestUrl1 = Please Enter Request URL 1
mer.lbl.upi = Unified Payment (UPI)
mer.lbl.feePostPeriod = Fee Posting Period

##Server side validation messages
sm.msg.pspName.required = Please Enter PSP Name
sm.msg.pspCode.required = Please Enter PSP Code
sm.msg.spocName.required = Please Enter Spoc Name
sm.msg.spocEmail.required = Please Enter Spoc Email
sm.msg.spocPhone.required = Please Enter Spoc Phone
sm.msg.pspUrl.required = Please Enter PSP URL
sm.msg.pubCertificate.required = Please Select Public Certificate
sm.msg.pspType.invalid = Please Select PSP Type 
AM.MSG.formValidationSuccess = Form validation Success.
sm.msg.pspCode.invalid = Please Enter Valid PSP Code
sm.msg.pspCode.lenInvalid = PSP Code length should not exceed 15 characters long
sm.msg.spocEmail.Invalid = Please Enter Valid Spoc Email
sm.msg.spocPhone.invalid = Please Enter Valid Spoc Phone
sm.msg.spocPhone.lenInvalid = Length should be at least 10 digits 
sm.msg.pspName.invalid = PSP Name must contain alphabets only
sm.msg.pspUrl.invalid = Please Enter Valid PSP URL
sm.msg.publicKeyCert.invalid = Please attach only jks file
sm.msg.chnCode.invalid = Please select channel code
sm.msg.KeyExpDate.required = Please Select Key Expiry Date

RP_FILENAME_UserProfileSearch = UserProfile_Search
RP_LBL_UserProfileSearch = User Profile Search Report

msg.lbl.initActiveApprovSuccess= Store has been successfully initiated for Activation approval.
msg.lbl.initDeactiveApprovSuccess=Store has been successfully initiated for Deactivation approval.

#sm.msg.lkp.editSuccess = LookUp data created and pending for approval !
AM_MSG_wrongOldPwd = Old password does not match with logged in user.

sm.lbl.vAddr = Virtual Address
sm.msg.vAddr.required = Please Enter Virtual Address
sm.msg.vAddr.invalid = Please Enter Valid Virtual Address
sm.msg.blockType.invalid = Please Select block Type 
cm.msg.pspCode.exists = PSP Code is already exist, Please provide valid PSP code.
sm.lbl.vAddre.duplicate = Merchant virtual address you entered is already exist.
sm.msg.fileSizeExceeded = File Size Exceeded

mp.lbl.reOpenDisSuc=Dispute reopened successfully
mp.lbl.reOpenDisFail = Failed to Reopened dispute.

#Block
sm.msg.pendingBlock = Block Request Submitted Successfully
sm.lbl.blockalreadypresent = Block Request is Already Present 


#Reconciliation
#Added by Kausar on 02.May.2016
rc_lbl_reconciliation=Reconciliation
rc_lbl_select = Reconciliation
rc_lbl_select_payee=Payee
rc_lbl_select_payer=Payer
rc_lbl_select_acquirer=Acquirer
rc_lbl_select_issuer=Issuer
rc_lbl_select_merchant_acquirer=Merchant Acquirer
rc_lbl_select_merchant_issuer=Merchant Issuer
rc_lbl_summary_button=Summary Report
rc_lbl_detailed_button=Detailed Report
rc_lbl_settlement_button=Settlement Report

rc_lbl_rec_count=Recon Count
rc_lbl_rec_amt=Recon Amount
rc_lbl_unrec_count=Unrecon Count
rc_lbl_unrec_amt=Unrecon Amount
rc_lbl_file_type=File Type


rc_lbl_det_table = Detailed Report
rc_lbl_init_source=Initialization Source
rc_lbl_trnno = Transaction Ref. No
#rc_lbl_psp_trnno = PSP TRN No
#rc_lbl_me_trnno = ME TRN No
rc_lbl_crt_date = Date
rc_lbl_tran_amt = Transaction Amount
rc_lbl_tran_act_fee = Transaction Activation Fee
rc_lbl_stl_fee = Settlement Fee
rc_lbl_stl_prc_fee = Settlement Process Fee
rc_lbl_recon_NPCIstatus = NPCI Recon Status
rc_lbl_recon_NPCIdate = NPCI Recon Date

rc_lbl_recon_CBSstatus = CBS Recon Status
rc_lbl_recon_CBSdate = CBS Recon Date


st_lbl_processDate = Process Date
st_lbl_batchId = Batch Id
st_lbl_totMerchantCount = Total Merchant Count
st_lbl_totAmtPayable = Total Amount Payable

st_lbl_totSettlementAmount = Total Settlement Amount
st_lbl_totRefAmount = Total Refund Amount
st_lbl_totChargeBackAmount = Total Charge Back Amount
st_lbl_totalMerchantCharge = Total merchant Charge 
st_msg_successMessage = Successfully Approved  For Posting
st_msg_failMessage = Failed to Approve

st_crdHldrPrcFee = Process Fee
st_crdHldrSvcFee = Service Fee
st_crdHldrActFee = Activation Fee

st_reconciled = Reconciled
st_unreconciled = Unreconciled

st_lbl_totMsfAmt = Total MSF Amount
st_lbl_totMsfTaxAmt = Total MSF Tax Amount

msg_err_settleType = Settlement Type

msg_err_taxName = Please enter Tax name
msg_err_taxCode = Please enter Tax code
msg_err_taxPer = Please enter Tax percentage
msg.err_valid_taxName = Please enter valid Tax Name
msg.err_valid_taxCode = Please enter valid Tax Code
msg.err_valid_taxPer = Please enter valid Tax Percentage
msg.err_EffFrom = Please select Effective From Date
msg.err_EffTo = Please select Effective To Date

NotNull.bvaDTO.mobileNo = Please enter mobile no.
NotEmpty.bvaDTO.bolckType = Please select block type.
sm_err_mobileNo = Please enter valid mobile no.
msg_err_taxPer = Please enter tax percent upto 99.99
msg.err.sizeExceeded = Please select a file upto 20MB
msg.err.sizeExceeded.npci = Please select a file upto 200MB	


stl.lbl.InvalidRecords = Invalid Records
stl.lbl.ValidRecords = Valid Records
stl.err.duplicateFileName = File name already exists

st.lbl.payoutGenerated = Merchant payout request has been initiated succesfully
st.lbl.payoutGeneratedFailed = Merchant payout request generation failed
stl.lbl.settlementDtlsRpt=Settlement Detailed Report
stl.lbl.cbsDtlsRpt = CBS Detailed Report
stl.err.payoutRequestPending = Merchant payout request is pending / In-progress. Hence new request cannot be initiated
stl.err.fileNameErrNPCI =Incorrect file for FILENAME

AM.MSG.enterValidResponse = Please enter a valid response.
st.msg.cmpUpld = Complaint file uploaded successfully

disp.err.invalidCust = Please select a valid customer
disp.err.invalidDisputeType = Please select a valid dispute type
disp.err.invalidResponseDesc = Please enter a valid response
disp.err.invalidPgMeTrnRefNo = Please enter a valid transaction ref no.
disp.err.blankResponseDesc = Please enter response 

merchant.msg.FileUpld= File Uploaded Successfully.
ST.lbl.gridBusinessName= BUSINESS NAME
ST.lbl.gridVPA=VPA
ST.lbl.gridBankName=BANK NAME
st.lbl.Approval = Merchant Bulk Approval
sm.lbl.select.Ag= Aggregrator
sm.lbl.select.d= Direct Mercahnt

ST.lbl.grid.pgMerchantId=Parent PG Merchant ID
ST.lbl.grid.businessName=Business Name
ST.lbl.grid.MIA=Merchant Integration Approach
ST.lbl.grid.PAN=PANNO
ST.lbl.grid.BussType=ME BUSINESS TYPE
ST.lbl.grid.MCC=MCC
ST.lbl.grid.SType=Settlement Type
ST.lbl.grid.MerType=Merchant Type
ST.lbl.grid.PerDayTranstCnt=Per Day Transaction Count
ST.lbl.grid.PerDayTranstLimit=Per Day Transaction Limit(Amount)
ST.lbl.grid.PerTransactionLimit=Per Transaction Limit(Amount)
ST.lbl.grid.VPA=VPA
ST.lbl.grid.WhiteListedUrl1=Whitelisted URLs1
ST.lbl.grid.WhiteListedUrl2=Whitelisted URLs2
ST.lbl.grid.BankName=Bank Name
ST.lbl.grid.IFSCCODE=IFSC CODE
ST.lbl.grid.A/CNO=A/c NO
ST.lbl.grid.ExternalMid=External MID
ST.lbl.grid.ExternalTid=External TID

Partial.Apprval.Message = Failed to approve some of the selected records
Failed.Apprval.Message = Failed to approve selected records
#Succes.Apprval.Message = Records approved successfully, Merchant will be onboard shortly.
Succes.Apprval.Message =Records approved successfully and will be updated shortly.
partial.approval.message = Some of the records failed to get approved
me.msg.url.invalid=Please enter valid Url
mer.err.requestUrl=Please enter Url 

msg.lbl.intMainFeeSetup = Integration/Maintenance Fee Setup
sm.lbl.oneTimeFee=One-time Fee Amount
msg.lbl.frequencyType=Maintenance Fee Frequency
msg.lbl.mainFeeAmt=Maintenance Fee Amount
me.msg.onetimeAmt.required=Please One-time Fee Amount
msg.lbl.addIntMainFeeSuccess=Integration/Maintenance Fee updated successfully
msg.lbl.addIntMainFeeFail=Integration/Maintenance Fee updation failed
Partial.Apprval.Message = Failed to approve some of the selected records
disp.err.emptyCustRefNo  = Please enter Cust Ref No
disp.err.noDownloadNPCIRecs = There are no downloadable records for NPCI upload 
Rejected.Message = Selected records rejected successfully
Failure.Message = Failure occurred while processing records  

bt.lbl.fileGendate=File Generation Date
bt.lbl.download=Download
Failed.Upload.TickelField.Message = Already generation pending for processing hence new generation cant be done.
Success.Upload.TickelField.Message = New request generated successfully
Failed.Upload.TickelField.Message = Records are already pending for generation
msg.lbl.comFlag=Communication Status
msg.lbl.comDate=Communication Date
msg.lbl.storeId=Store Id

##Added by arjun 20Jan2017 UPI charge module
rp.lbl.GnrateUpiCharge=Download as txt
me.msf.mcc.err=MCC cannot be changed since MSF already defined

me.msg.billingPhone1.required = Please enter phone number
me.msg.billingPhone2.required = Please enter mobile number
me.msg.perTranLimitless.invalid= Please enter amount below 100000
msg.lbl.detailsUpdatedSuccess=Details updated successfully
msg.lbl.incorrectNPCI  = Incorrect NPCI extension for the Legal Vehicle
sm.msg.stdMsfFeeApproved = Standard MSF fee has been successfully approved
sm.msg.stdMsfFeeRejectByApprover = Standard MSF fee request rejected by approver
msg.lbl.stdMSFSuccess = Standard MSF fee details inserted successfully.
msg.lbl.stdMSFFail = Error occurred while creating standard MSF fee.

# Reversal File Approval 
st.msg.rfaUpld = Reversal File Approval
st.msg.rfaSumbit = submit
st.msg.rfaInit = Initiate Reversal File Generation
st.msg.rfaTrnref = Transaction Reference Number
st.msg.rfaTrndate = Transaction Date
st.msg.rfaTrncustno = Transaction Customer Number
st.msg.rfaTrnamt =	Transaction Amount
st.msg.rfaTrnacc =  Account Number	
st.msg.rfaFileName = File Name
st.approve.ReversalFile = Reversal File successfully approved
st.fail.ReversalFile =Failed to approve Reversal File
st.msg.rfaTrncheck = check Box 
merchant.vpa.invalid = Please enter valid merchant virtual address
merchant.vpa.invalid.length =Merchant virtual address should be maximum 50
failedFormValidation=There are some fields which are not valid please correct & submit


#PreRegistrationVPA FILE UPLOADS - Vivek - 24.04.2017
msg.lbl.uploadFailed=Failed To Upload File
msg.lbl.fileFormatNotSupported=File Format not supported
Failed.Upload.Empty.Message=Failed to upload because the file was empty or file-type was not correct
prereg.lbl.preRegFileUpl = Pre-Registration VPA File upload
prereg.lbl.pre = CBS Inward File
prereg.sms=Download File
prereg.err.duplicateFileName = File name already exists
prereg.msg.settlementUpld=Pre Registration file uploaded successfully.
stl.lbl.InvalidRecords = Invalid Records
stl.lbl.ValidRecords = Valid Records

me.msg.issueBnk.invalid = Invalid Issue Bank. Please select HDFC Bank as issue bank.



#PreRegistrationVPA Entry form - Arpit - 24.04.2017
am.lbl.preRegVPA=Create VPA
am.lbl.mobileNo=Mobile No
am.lbl.firstName=First Name
am.lbl.middleName=Middle Name
am.lbl.lastName=Last Name
am.lbl.vpa=VPA
am.lbl.accNo=Account No
am.lbl.ifscCode=IFSC Code
am.lbl.isExclusiveCust=Exclusive Customer
prereg.err.addNewVPA=VPA has been successfully added
AM.MSG.duplicateVPAerror=VPA already exists
AM.MSG.duplicateMobileerror=Mobile Number already exists

mp.lbl.invalidFileName = File Name is Invalid



AM.MSG.mobileNoerror=Please enter valid Mobile Number
AM.MSG.VPAerror=Please enter valid VPA
AM.MSG.IFSCerror=Please enter valid IFSC Code
AM.MSG.fNameerror=Please enter valid First Name
AM.MSG.lNameerror=Please enter valid Last Name
AM.MSG.accNoerror=Please enter valid Account No
msg.lbl.resentKeyEmailSuccess=Merchant key email resent successfully
msg.lbl.resentKeyEmailFailed=Failed to resend merchant key email
msg.lbl.resentuserEmailSuccess=Merchant default user credentials resent successfully
msg.lbl.resentuserEmailFailed=Failed to resend Default User Credentials

st.lbl.deemedTrnsAppr = Deemed Transactions Approval													
												
rc_lbl_report=DRC Report
RP_LBL_download= Download


# created by sneha
RP_FILENAME_SFTPUploadSearch=SFTP Upload Status
RP_LBL_SftpUploadSearch= SFTP Upload Status
ST.lbl.retry= Retry
RP_MSG_fileType.required=Please select file type

# Sneha 14.07.17
RP_LBL_MerchantKey=Merchant Key Report Details
RP_LBL_externalTID= External TID

Rejection.Apprval.Message = Records rejected successfully

###Lock
AM_MSG_pendingUnlockUser = User Unblock Request Send For Approval
AM_MSG_pendingLockUser = User Block Request Send For Approval

# sneha 23.08.17
am.lbl.userclassification= User Classfication

# CHANGES MADE FOR ADDING EXPIRY YEAR ON 28.08.2017 
am.lbl.expYear=Expiry Year


#Addded by jeetu--24.08.2017
am.lbl.branchCode = Branch Code		

# Ganesh 06.09.2017
Rp_LBL_RoleMaster=Role Master Report
RP_FILENAME_ROLEMASTER = Role Master

#Jeetu 06.09.2017
RP_FILENAME_USER_POPULATION_REPORT=RP_USER_POPULATION
Rp_LBL_UserPopulation=User Population Report
RP_LBL_UserId=User Id

#pooja 07.09.17
st_msg_successEmailMessage =Email sent Successfully	
RP_MERCHANTPAYOUT_REPORT=Merchant Payout Report
ERR_MSG_EMAIL=Please Enter Valid Merchant ID
ERR_MSG_EMAIL_ERR=Email not Sent	

###Ganesh 08.09.2017
AM_MSG_pendingDeleteUser = User Delete Request Send For Approval
AM_MSG_userApprovedDelete = User is Deleted successfully.


#pooja 08.09.17
am.lbl.branchName=Branch Name
am.lbl.departmentName=Department Name
am.lbl.departmentCode=Department Code

###Ganesh 11.09.2017
AM_MSG_pendingResetUser = User Reset Password Request Send For Approval

#Jeetu 12.09.2017
RP_FILENAME_LOGIN_LOGOUT_REPORT=RP_Login_Logout
Rp_LBL_LoginLogout=Login Logout Report

RP_FILENAME_ADMIN_ACTIVITY_REPORT = Admin Activity Report

RP_FILENAME_GEFU_EXCEPTION_REPORT=Gefu Exception Report

RP_LBL_TXT=Download as Text



sm.lbl.branchFixedAmount=Branch Fixed Amount
sm.lbl.branchPer=Branch Fixed Per(%)
sm.lbl.departmentFixedAmount=Department Fixed Amount
sm.lbl.departmentPer=Department Fixed Per(%)


msg.lbl.rptReqSucc = Your request has been received. To download report, kindly check after some time.
msg.lbl.fileError = Error in processing file


RP_FILENAME_SUBVENTION_REPORT=Subvention Report
RP_MSG_INCORRECT_FILE_PATH_ERROR=File Path Not Found
RP_MSG_NO_FILE_ERROR=No File Found



##### Added by Hitesh for Upadte TCC/RET Status on
st.lbl.TccRetFileUpl =TCC/RET File Upload
st.lbl.TccRetRemitter=TCC/RET Remitter
st.lbl.TccRetBeneficiary=TCC/RET Beneficiary
msg.lbl.NoRecordForGefuGen=No Record Found For GEFU Generation
msg.lbl.retGefuGenReqSucc= Your request has been received. To generate GEFU file, kindly check after some time.
msg.lbl.retGefuGenReqFail= Your request has been Fail. To generate GEFU file, kindly try again.
msg.lbl.gefuGenPending=GEFU Generation request is pending / In-progress. Hence new request cannot be initiated
RP_MSG_INCORRECT_FILE_PATH_ERROR=Incorrect File Path

##########Pooja 08.12.17
st.lbl.manualReconList=Manual Reconcilation(CBS)
st.lbl.txnType=Transaction Type
st.lbl.debit=Debit
st.lbl.credit=Credit
st.lbl.recError=Some Error Occured
st.lbl.recSuccess=Record Updated Successfully
st.lbl.recMismatch=Record Mismatch


############ Gefu Generation #################
st.lbl.GefuFileGeneration =Gefu File Generation
st.lbl.GefuFileType =Gefu File Type
st.lbl.GefuFileTypes1=Return
st.lbl.GefuFileTypes2=Debit Reversal
st.lbl.GefuFileTypes3=Credit Reversal
st.lbl.GefuFileTypes4=Deemed
st.lbl.GefuFileList=Gefu File List

##### Dispute Rejected File ##########
st.lbl.UploadRejectedFile=Upload Rejected File

#######MERCHANT TYPE REPORT######
st.lbl.MerchantTypeReport=Merchant Type Report

mer.err.subSystem=Please Select SubSystem

#####PSP REPORT####
st.lbl.pspReport=PSP Report
####BLOCKVPA########
st.lbl.UploadBlockVpaFile=Upload Block VPA File
st.lbl.FileUploadDate=File Upload Date
st.lbl.SuccAppr=Approved Successfully
st.lbl.FailAppr=Rejected Successfully
st.lbl.BlockVpaAppr=Block Vpa Approval


####PAY/COLLECT#########
ST.lbl.gridMerVPA=Merchant VPA
ST.lbl.gridPay=Pay
ST.lbl.gridCollect=Collect
ST.lbl.gridOlRefund=Online Refund
ST.lbl.gridMerPortal=Merchant Portal
Succes.ApprBlockPushval.Message=Records Approved Successfully
ST.lbl.reqSent=Your request has been sent for approval


##########Reserve Vpa#########
st.lbl.reserveVpa=Reserve Vpa
st.lbl.reserveVpaReport=Reserve Vpa Report
st.lbl.MerchantTypeReport=Merchant Type Report
st.lbl.blockPushReport=Block Push Vpa Report

######CSP Report########
st.lbl.cspReport=CSP Report
st.lbl.cspReport.response=Response
st.lbl.cspReport.respDesc=Response Desc
st.lbl.cspReport.tat=TAT_CR



############ Block Transactions #######################333
sm.msg.pendingBlkAcc = Block Transaction  has been successfully created and pending for approval
sm.msg.errorPspApprove=Technical error occurred
sm.msg.blkaccRejectByApprover= Block Transaction has been rejected by approver.
sm.msg.addBlkAccApproved = Block Transaction has been approved by approver.
sm.msg.blkaccPending =  Block Transaction already pending for approval.
sm.msg.blkaccActivationPending = Block Transaction activated and pending for approval.
sm.msg.blkaccDeactivationPending = Block Transaction deactivated and pending for approval.
sm.msg.BlkAccPending = Block Transaction already pending for approval.
sm.msg.blockType.required = Please enter Block Type.
sm.msg.blockVal.required = Please select Block Type.
sm.msg.leg.required = Please select Leg.
sm.msg.blkAcc.activateRejected = Activation request rejected by approver.
sm.msg.blkAcc.deActivateRejected = Deactivation request rejected by approver.
sm.msg.blk.activateApproved = Activation request approved by approver.
sm.msg.blk.deActivateApproved =  Deactivation request approved by approver.
#Reshma
blkAcc.upload.empty.message = File is empty.
blkAcc.upload.fail.message = File upload failed.
blkAcc.upload.success.message =  File uploaded successfully.
blkAcc.upload.error.message = Technical error occurred.
blkAcc.upload.fileformat.notsupported=File format not supported.
blkAcc.upload.file.duplicate=File is already processed.
sm.msg.errorBlkAccApprove=Error updating Block transaction
sm.msg.blockVal.exists=Block Value already exists
blkAcc.upload.fileNotValid.message = File name is not valid.
blkAcc.download.success.message =  File download successfully.
blkAcc.download.error.message =  Error in file downloading.Please try after some time.
blkAcc.record.approved.message = Records updated successfully.
blkAcc.record.approved.failed.message = Error in record approval.Please try after some time.
blkAcc.record.reject.message = Records rejected successfully.
blkAcc.record.reject.failed.message = Error in record rejction.Please try after some time.
blkAcc.upload.file.maxsize.message = File size is exceed.

#pooja 29.06.2018
mer.err.subType=Please Select SubType

#jayganesh penalty 25.07.18
am.lbl.penalty.PenaltyType=Penalty Type
am.lbl.penalty.addPenalty=Add Penalty
am.lbl.penalty.editPenalty=Edit Penalty
am.lbl.penalty.TAT=TAT
am.lbl.penalty.penAmt=Penalty Amount
am.lbl.penalty.effFrom=Effective from Date
am.lbl.penalty.activeInactive=Active/Inactive
am.msg.pendingPenalty=Penalty Pending for Approval
am.msg.duplicatePenalty=Same penalty entry already exists for given penalty type,TAT and effective from date.
am.lbl.penalty.penalty=Penalty
am.lbl.penalty.appr=Approval
am.lbl.penalty.penaltyList=Penalty List
am.msg.errorPenalty=Error Creating Penalty
am.lbl.penalty.view=Action
am.msg.penaltyApproved=Penalty Approved
am.msg.penaltyRejected=Penalty Rejected 
am.lbl.penalty.apprstatus=Status
am.lbl.penalty.rejectreason=Reject Reason
am.msg.pendingUpdatedPenalty=Update Penalty Pending for Approval
msg_err_PenTAT=Please enter TAT
msg_err_PenAmt=Please enter Amount
msg_effFromDateStr=Please select effective from date
msg_err_valid_penTAT=TAT value not valid
msg_err_valid_PenAmt=Amount value not valid
msg_err_valid_effFromDateStr=Effective Date not valid
msg_err_future_effFromDateStr=Effective Date must be future date
am.msg.errorUpdatePenalty=Error Occured while Updaing Penalty Information.
msg_err_valid_penType=Penalty type not valid
msg_err_penType=Please select penalty type
msg_err_valid_penStatus=Penalty status not valid
msg_err_penStatus=Please select penalty status
am.lbl.penalty.reqtype=Request Type

msg_err_future_holiday_date=Please enter future date.
msg_err_valid_holiday_date=Please enter valid date
am.lbl.holiday.status=Status
am.lbl.holiday.remarks=Remarks
AM_MSG_holidayApproved=Holiday Activation request approved successfully.
AM_MSG_holidayRejectByApprover=Holiday Activation request rejected successfully.
am.lbl.holiday.action=Action
am.lbl.Holiday.HolidayType=Holiday Type
msg_err_hldType= Please Enter Holiday Type
msg_err_Holiday= Please Enter Holiday
msg_holidaydesc= Please Enter Holiday Description
msg_err_valid_HolidayStr=Please Enter Valid Holiday Remarks
msg_err_valid_HolidayDesc=Please Enter Valid Holiday Description
AM_MSG_errorHolidayDeactivation=Error while Holiday Deactivation.
AM_MSG_holidayDeactivationPending=Holiday Deactivation Request sent for Approval.
am.lbl.Holiday.addEditHoliday=Add Holiday
AM_MSG_holidayPending=Already Pending for Approval.
am.lbl.holiday.holidayType=Holiday Type
am.lbl.holiday.holiday=Holiday
am.lbl.holiday=Holiday
am.lbl.holiday.view=View
am.lbl.remarks=Remarks
am.lbl.holiday.holiday=Remarks
am.lbl.holiday.crtdate=Create Date
am.lbl.holiday.rejectreason=Reject Reason
am.lbl.Holiday.holidayid=HolidayID
am.lbl.Holiday.holiday=Holiday
am.lbl.holiday.holiday=Holiday
AM_MSG_pendingUpdatedHoliday=Succesfully Updated Holiday Details
am.lbl.holiday.appr=Approval
AM_MSG_HolidayPending=Record Already Pending for Approval.
am.msg.successpendingHoliday=New Holiday Request Sent successfully for Approval.
am.lbl.holiday.holidayRemarks=Remarks
am.lbl.Holiday.holidaydesc=Holiday Descriptio
am.lbl.holiday.addEditholiday=Add/Edit Penalty
am.lbl.penalty.addHoliday=Add Holiday
am.lbl.holiday.addHoliday=Add Holiday
am.lbl.Holiday.status=Status
am.lbl.holiday.description=Description
am.lbl.Holiday.penAmt=Holiday Amount
am.lbl.holiday.effFrom=Holiday Date
am.lbl.Holiday.activeInactive=Active/Inactive
am.msg.errorHoliday=Error Creating Holiday
am.msg.pendingHoliday=Holiday Pending for Approval
am.msg.duplicateHoliday=Same Holiday entry already exists for given Holiday type and Holiday Date.
am.lbl.Holiday.Holiday=Holiday
am.lbl.Holiday.appr=Approval
am.lbl.Holiday.HolidayList=Holiday List
am.lbl.Holiday.view=Action
AM_MSG_errorUpdateHoliday=Error Updating Holiday Details

##############LoopUp Master manish 30.07.18##############
msg_err_LKPTYPE = Please Enter Lookup type
msg_err_LKPVALUE = Please enter Lookup value
msg_err_LKPDESC = Please enter Lookup description
msg_err_valid_LKPTYPE = Lookup type not valid
msg_err_valid_LKPVALUE = Lookup value not valid
msg_err_valid_LKPDESC = Lookup description not valid


##############MCC Master jayganesh 30.07.18##############
am.lbl.mcc.mcc=MCC Config
am.lbl.mcc.addMCC=Add MCC Config
am.lbl.mcc.mccList=MCC Config List
am.lbl.mcc.mcccode=Code
am.lbl.mcc.mccname=Description
am.lbl.mcc.status=Active/Inactive
am.lbl.mcc.apprstatus=Status
am.lbl.mcc.rejectreason=Reject Reason
am.lbl.mcc.view=View
msg_err_valid_mccCode=Please enter numeric MCC Code
msg_err_mccCode=Please enter MCC Code
msg_err_valid_mccName=Please enter valid MCC description with less than 500 characters
msg_err_mccName=Please enter valid MCC description
am.msg.duplicateMCC=Same MCC code already exists
am.msg.errorMCC=Error Creating MCC Configuration
am.lbl.mcc.reqtype=Request Type
am.msg.mccApproved=MCC Configuration Approved
am.msg.mccRejected=MCC Configuration Rejected
AM_MSG_mccDeactivationPending=MCC Configuration deactivation request sent for approval
AM_MSG_errorMCCDeactivation=Error while MCC Configuration deactivation process
AM_MSG_mccPending=Already pending for approval.
AM_MSG_mccActivationPending=MCC Configuration activation request sent for approval
AM_MSG_errorMCCActivation=Error while MCC Configuration activation process
msg_err_mccStatus=MCC active/inactive status is not provided
msg_err_valid_mccStatus=MCC active/inactive status is not valid
am.msg.pendingMCC=MCC Configuration pending for approval
am.msg.mccActApproved=MCC Configuration activation request approved successfully
am.msg.mccActRejected=MCC Configuration activation request rejected successfully
am.msg.mccDctApproved=MCC Configuration deactivation request approved successfully
am.msg.mccDctRejected=MCC Configuration deactivation request rejected successfully

am.lbl.cycle.cycle=Cycle
am.lbl.cycle.FSC=First Settlement Cycle Time
am.lbl.cycle.LSC=Last Settlement Cycle Time
am.lbl.cycle.addCycle=Add Cycle
am.lbl.cycle.editCycle=Edit Cycle
am.lbl.cycle.cycleName=Cycle Name
am.lbl.cycle.cycleType=Type of Day
am.lbl.cycle.fsc=Start Time
am.lbl.cycle.lsc=End Time
am.lbl.cycle.effFrom=Effective from date
am.lbl.cycle.effTo=Effective till date
am.lbl.cycle.cycleList=Cycle List
am.lbl.common.activeInactive=Active/Inactive
am.lbl.common.reqtype=Request Type
am.lbl.common.apprstatus=Status
am.lbl.common.rejectreason=Reject Reason
am.lbl.common.view=Action
am.msg.createdCycle=Cycle Created Successfully
am.msg.pendingCycle=Cycle Sent for Approval
am.msg.errorCycle=Error Creating cycle
am.msg.cycleApproved=Cycle Approved
am.msg.cycleRejected=Cycle Rejected 
am.lbl.common.reqid=Request Id
AM_MSG_cycleDeactivationPending=Cycle deactivation request sent for approval
AM_MSG_errorCycleDeactivation=Error while Cycle deactivation process
AM_MSG_Pending=Already pending for approval.
AM_MSG_cycleActivationPending=Cycle activation request sent for approval
AM_MSG_errorCycleActivation=Error while Cycle activation process
msg_err_cycleName=Cycle Name is not provided
msg_err_valid_cycleName=Cycle Name is not valid
msg_err_holidayCycle=Cycle Type is not provided
msg_err_valid_holidayCycle=Cycle Type is not valid
msg_err_fromTimeHH=From Time Hour is not provided
msg_err_valid_fromTimeHH=From Time Hour is not valid
msg_err_fromTimeMM=From Time Minute is not provided
msg_err_valid_fromTimeMM=From Time Minute is not valid
msg_err_toTimeHH=To Time Hour is not provided
msg_err_valid_toTimeHH=To Time Hour is not valid
msg_err_toTimeMM=To Time Minute is not provided
msg_err_valid_toTimeMM=To Time Minute is not valid
msg_err_before_fromTimeHH=From Time is greater than To Time
msg_err_before_fromTimeMM=From Time is greater than or equal to To Time
am.lbl.cycle.time=Time
msg.lbl.add=Add
msg.lbl.sendappr=Send For Approval
am.lbl.cycle.day=Day
am.lbl.cycle.rtgstime=Time
am.lbl.cycle.noofcycles=No of Cycles
am.lbl.cycle.crtdate=Created Date & Time
am.lbl.common.srno=SR.No

##############Member venkatesh 31.07.18##############
AM_ADD_MEMBER_REQ=Member add request has been sent for approval
AM_EDIT_MEMBER_REQ=Member edit request has been sent for approval
AM_FAIL_ADD_MEMBER_REQ=Failed to add member request
AM_FAIL_EDIT_MEMBER_REQ=Failed to edit member
AM_MSG_MemberPendingFrAppr=Member is already pending for approval
AM_MSG_MemberActivationPending=Member account activation request sent for approval.
AM_MSG_MemberDeactivationPending=Member account Deactivation request sent for approval.
AM_MSG_errMemberActivation=Failed to send member activation request for approval
AM_MSG_errMemberDeActivation=Failed to send member Deactivation request for approval
am.lbl.memberReg =Member Registration
AM_FAIL_TO_PRC_REQ=Failed to process request
AM_MSG_memberAddSucc=Member has been successfully approved
AM_MSG_memberAddFailed=Failed to approve member
AM_MSG_memberEditSucc=Member details has been successfully updated
AM_MSG_memberEditFailed=Failed to approve member edit request
AM_MSG_memberActivateSucc=Member account has been successfully activated
AM_MSG_memberActivateFailed=Failed to approve member activate request
AM_MSG_memberDeActivateSucc=Member account has been successfully deactivated
AM_MSG_memberDeActivateFailed=Failed to approve member deactivate request

AM_MSG_memberAddRejSucc=Member add request has been successfully rejected
AM_MSG_memberAddRejFailed=Failed to reject member add request
AM_MSG_memberEditRejSucc=Member edit request has been successfully rejected
AM_MSG_memberEditRejFailed=Failed to reject member edit request
AM_MSG_memberActivateRejSucc=Member activate request has been successfully rejected
AM_MSG_memberActivateRejFailed=Failed to reject member activate request
AM_MSG_memberDeActivateRejSucc=Member deactivate request has been successfully rejected
AM_MSG_memberDeActivateRejFailed=Failed to reject member deactivate request


#### Holiday Bulk Jeetu 06.08.18
am.lbl.Holiday.Date=Holiday Date
am.lbl.Holiday.Desc=Holiday Description
st.lbl.BlockVpaAppr=Holiday Master Approval
am.lbl.Holiday.Type=Holiday Type


###################Pritesh Special Working Day 31.07.18
am.lbl.splWrkDay.splWrkDay=Special Working Day
am.lbl.splWrkDay.splWrkDay=Special Working Day
am.lbl.splWrkDay.splWrkDayList=splWrkDay List
am.lbl.splWrkDay.addsplWrkDay=Add Spl Working Day
am.lbl.splWrkDay.splWrkDayList= Special Working Day List
am.lbl.am.lbl.splWrkDay.splWrkDay= Special Working Day
am.lbl.splWrkDay.Remarks=Remarks
am.lbl.splWrkDay.CreateDate=Create Date
am.lbl.splWrkDay.Status=Status
am.lbl.splWrkDay.rejectreason=Reject Reason
am.lbl.splWrkDay.view=View
am.lbl.splWrkDay.addEditspl=Add Special Working Day
am.lbl.splWrkDay=Special Working Day
am.lbl.splwrkday.description=Remarks
am.msg.errorHoliday=Error Creating Special Working Day
am.msg.successpendingsplwrk=Succesfully Created Special Working Day.
am.msg.duplicateSplWrk=Same Splecial Working Day entry already exists for given  Date.
am.msg.errorSplWrkDay=Error Creating Special Working Day
am.msg.rejectSplWrkDay=Special Working Day Rejected Succesfully.
AM_MSG_SplWrkDayDeactivationPending=Splecial Work Day Deactivation request is sent.
AM_MSG_SplWrkDayActivationPending=Splecial Work Day Activation request is sent.
AM_MSG_errorSplWrkDayDeactivation=Error while Special Working Day Deactivation.
AM_MSG_SplWrkDayPending=Already Pending for Approval.
am.msg.splwrkApproved= Special Working Day Approved Succesfully
msg_err_valid_SPlWrkDescDesc= Please Enter valid Splecial Working Day Description.
msg_SPlWrkDescDesc= Please Enter Splecial Working Day Description
msg_blanksPLwRK=Blank Special Working Day
msg_err_valid_spl_date=Please Enter valid Special Working Day
msg_err_splwrkdayStr=Blank Special Working Day
msg_err_splWrkDayDesc=Blank Special Working Day Remarks 


################# FEES Master Manisha 03.08.18 ###############

am.lbl.fees.fees = Fees Master File Upload 
am.lbl.fees.fileUplMonitoring = File Upload Monitoring
am.lbl.fees.selectFile = Select File
am.lbl.fees.uploadFile = Upload File
am.lbl.fees.reset = Reset
am.lbl.fees.fromDate = From Date
am.lbl.fees.toDate = To Date
am.lbl.fees.fileName = File Name
am.lbl.fees.search = Search
am.lbl.fees.fileProcessingStatus = File Processing Status 
am.lbl.fees.status = Status
am.lbl.fees.batchRunID = Batch Run ID
am.lbl.fees.fileSize = File Size
am.lbl.fees.invalideCount = Invalid Count
am.lbl.fees.validCount = Valid Count
am.lbl.fees.totalCount = Total Count
am.lbl.fees.fileProcStatus = File Processing Status
am.lbl.fees.reqDate = Request Date
am.lbl.fees.selectRecord = Please select at least 1 record
am.lbl.fees.feeType = Fee Type
am.lbl.fees.transType = Transaction Sub Type
am.lbl.fees.mcc = MCC
am.lbl.fees.chargeType = Charge Type
am.lbl.fees.effrom = Effective From
am.lbl.fees.efto = Effective To
am.lbl.fees.slabUpto = Slab Up to
am.lbl.fees.fixAmt = Fixed Amount
am.lbl.fees.fixPer = Fixed Percentage
am.lbl.fees.remarks = Remarks
am.lbl.fees.appRej = Approved/Rejected
am.lbl.fees.transactionDetails = Transaction Details
am.lbl.fees.searchResult = Search Results
am.lbl.fees.view = View

am.lbl.cycle.cycle=Cycle
am.lbl.cycle.FSC=First Settlement Cycle Time
am.lbl.cycle.LSC=Last Settlement Cycle Time
am.lbl.cycle.addCycle=Add Cycle
am.lbl.cycle.editCycle=Edit Cycle
am.lbl.cycle.cycleName=Cycle Name
am.lbl.cycle.cycleType=Type of Day
am.lbl.cycle.fsc=Start Time
am.lbl.cycle.lsc=End Time
am.lbl.cycle.effFrom=Effective from date
am.lbl.cycle.effTo=Effective till date
am.lbl.cycle.cycleList=Cycle List
am.lbl.common.activeInactive=Active/Inactive
am.lbl.common.reqtype=Request Type
am.lbl.common.apprstatus=Status
am.lbl.common.rejectreason=Reject Reason
am.lbl.common.view=Action
am.msg.createdCycle=Cycle Created Successfully
am.msg.pendingCycle=Cycle Sent for Approval
am.msg.errorCycle=Error Creating cycle
am.msg.cycleApproved=Cycle Approved
am.msg.cycleRejected=Cycle Rejected 
am.lbl.common.reqid=Request Id
AM_MSG_cycleDeactivationPending=Cycle deactivation request sent for approval
AM_MSG_errorCycleDeactivation=Error while Cycle deactivation process
AM_MSG_Pending=Already pending for approval.
AM_MSG_cycleActivationPending=Cycle activation request sent for approval
AM_MSG_errorCycleActivation=Error while Cycle activation process
msg_err_cycleName=Cycle Name is not provided
msg_err_valid_cycleName=Cycle Name is not valid
msg_err_holidayCycle=Cycle Type is not provided
msg_err_valid_holidayCycle=Cycle Type is not valid
msg_err_fromTimeHH=From Time Hour is not provided
msg_err_valid_fromTimeHH=From Time Hour is not valid
msg_err_fromTimeMM=From Time Minute is not provided
msg_err_valid_fromTimeMM=From Time Minute is not valid
msg_err_toTimeHH=To Time Hour is not provided
msg_err_valid_toTimeHH=To Time Hour is not valid
msg_err_toTimeMM=To Time Minute is not provided
msg_err_valid_toTimeMM=To Time Minute is not valid
msg_err_before_fromTimeHH=From Time is greater than To Time
msg_err_before_fromTimeMM=From Time is greater than or equal to To Time
am.lbl.cycle.time=Time
msg.lbl.add=Add
msg.lbl.sendappr=Send For Approval
am.lbl.cycle.day=Day
am.lbl.cycle.rtgstime=Time
am.lbl.cycle.noofcycles=No of Cycles
am.lbl.cycle.crtdate=Created Date & Time
am.lbl.common.srno=SR.No

am.lbl.invalidFileName = File Format is invalid
am.lbl.invalidFileRecords= File records are invalid
sm.msg.AdjPendingForApproval=Adjustment request has been sent for approval
sm.msg.FailedToRaiseAdjReq=Failed to raise request
sm.msg.adjReqAprvedSuccesfully=Request has been approved successfully
sm.msg.adjReqRejctdSuccesfully=Request has been rejected successfully
sm.msg.FailedToAprvAdjReq=Failed to Approve adjustment request
sm.msg.FailedToRjectAdjReq=Failed to Reject adjustment request
sm.msg.FailedToUpldEvidence=Failed to upload evidence
sm.msg.TimeoutToRaiseAdjReq=Request got time out. Please try again.



AM_MSG_errorCycleoverride = Override Success
AM_MSG_errorCyclenotoverride = Reconciled Already
AM_FAIL_DUPLICATE_BANK = BankName Already Exists.
AM_FAIL_DUPLICATE_ORGID = Orgid Already Exists.
AM_FAIL_DUPLICATE_NFSPARTICIPANTID= NfsParticipantid Already Exists.
AM_FAIL_DUPLICATE_GSTIN = GSTIN Already Exists.
am.holiday.cutoff.Expired = Holiday Cutoff time expired. 
am.lbl.interfile.fileSearch = International Adjustment Report Search
am.lbl.custComp.addCustCompensation=Add Customer Compensation
am.lbl.custComp.editCustCompnesation =Edit Customer Compensation
am.lbl.custComp.adjustmentType=Adjustment Type
am.lbl.custComp.CustCompensationList = Customer Compensation List
am.lbl.custComp.CustCompensation = Customer Compensation
am.lbl.custComp.transactionType=Transaction Type

AM_MSG_TaxDeactivationPending=Tax Deactivation Request sent for Approval.
AM_MSG_TaxActivationPending=Tax Activation Request sent for Approval.
AM_MSG_errorTaxDeactivation=Error while Tax Deactivation.
AM_MSG_errorTaxActivation=Error while Tax Activation.

sm.msg.lkp.lookupCreationSuccess=Lookup added Successfully.
am.lbl.Adj.RejReasonDesc=Reject Reason
AM_MSG_userRejected=User Rejected successfully.	
AM_MSG_userAddFailed= User Added Failed.
st.lbl.dwnFiles=Download Files
st.lbl.deleteAllFiles=Delete All
AM_MSG_userActivationPending=User Activation sent for Approval.	
am.msg.UserEntryDiscarded=Rejected User data has been discarded successfully.
am.lbl.middleName =Middle Name
am.lbl.salutation =Salutation
cmn.lbl.phoneNo=Phone No
addEditUser.middleName.invalid = Middle Name must contain Alphabets
AM_MSG_userLock=User is blocked.Record can't be edited.
AM_MSG_userDeactivate=User is deleted.Record can't be edited.

sm.lbl.checkerComments= Checker Comments
sm.lbl.loginId= Login Id
sm.lbl.salutation= Salutation
sm.lbl.firstName= First Name
sm.lbl.lastName = Last Name
sm.lbl.middleName= Middle Name
msg.lbl.emailId= Email Id
sm.lbl.mobileNo= Mobile No
am.lbl.dob= Date of Birth
sm.lbl.landLineNo= Landline No
sm.lbl.bankName= Bank Name
sm.lbl.accessLevel= Access Level
am.lbl.lastUpdatedBy= Last Updated By
am.lbl.lastUpdatedOn= Last Updated On
cmn.lbl.streetAddress= Street Address
cmn.lbl.cityName= City
msg.lbl.state= State
am.lbl.pinCode= Pincode
am.lbl.empId= EmpId
am.lbl.makChkFlag= Maker/Checker



##showRoles.jsp
am.lbl.npciRole = NPCI Roles
am.lbl.bankRole = Bank Roles
am.lbl.roleName = Role Name
am.lbl.roleId = Role Id
am.lbl.roleDescription = Role Description
am.lbl.status = Status
am.lbl.action = Action
am.lbl.roleList = Role List
am.lbl.reqType = Request Type
am.lbl.addRole = Add Role
am.lbl.approval = Approval
am.lbl.roleType = Role Type
am.lbl.moduleName = Module Name :
am.lbl.selectModule = -- SELECT MODULE --
am.lbl.availableFunctionalities = Available Functionalities
am.lbl.selectedFunctions = Selected Functions
am.lbl.appRejAction = Please Select Approve/Reject action.
am.lbl.pendingRole = Pending Role
am.lbl.rejected = Rejected
am.lbl.discarded = Discarded
am.lbl.approverComments = Approver Comments

am.lbl.funcCode = Function Code
##showUsers.jsp
am.lbl.npciUsers = NPCI Users
am.lbl.bankUsers = Bank Users
am.lbl.bankAdmin = Bank Admin
am.lbl.addEdit = Add/Edit Role
am.lbl.roleToFunc = Role To Functionality Mapping
am.lbl.select = SELECT
am.lbl.availableFunc = Available Functionalities
am.lbl.selectedFunc = Selected Functions
am.lbl.userId = User Id
am.lbl.loginId = Login Id
am.lbl.name = Name
am.lbl.emailId = Email ID
am.lbl.bankName=Bank Name
am.lbl.createdDate=Created Date
am.lbl.status=Status
am.lbl.lastLogin = Last Login
am.lbl.contactNo=Contact No
am.lbl.createdBy=Created By
am.lbl.action=Action
am.lbl.requestType=Request Type
am.lbl.requestId=Request Id
feeRate.daystobewaived = Days to be waived
feeRate.waiverDayType = Waiver Day Type
feeRate.penaltyDayType = Penalty Day Type
feeRate.compoundFee = Compound Fee
feeRate.dailyFee = Daily Fee
feeRate.impactTo= Impact To
feeRate.dateAction= Date Action
feeRate.dateAction.validation.msg = Please Select Date Action
feeRate.waiverDayType.validation.msg = Please Select Waiver Day Type
feeRate.impactTo.validation.msg= Please Select Impact to.
feeRate.penaltyDayType.validation.msg = Please Select Penalty Day Type
feeRate.dailyFee.validation.msg = Please Select Daily Fee.
feeRate.compoundFee.validation.msg = Please Select Compound Fee.
AM_MSG_FeeRateApproved =Fee Rate has been successfully approved.
AM_MSG_FeeRateRejected =Fee Rate has been successfully rejected.
feeRate.discardSuccess.msg=Rejected Fee Rate data has been discarded successfully.
escalation.discardSuccess.msg=Rejected Escalation data has been discarded successfully.
feeRate.addSuccess.msg=Fee Rate has been successfully added and sent for checker's Approval.
feeRate.updateSuccess.msg=Fee Rate has been edited successfully and sent for checker's Approval.
feeMinor.addSuccess.msg=Fee Minor has been successfully added and sent for checker's Approval.
feeMajor.updateSuccess.msg=Fee Major has been edited successfully and sent for checker's Approval.
feeMajor.addSuccess.msg=Fee Major has been successfully added and sent for checker's Approval.
feeMinor.updateSuccess.msg=Fee Minor has been edited successfully and sent for checker's Approval.
feeConfig.addSuccess.msg=Fee Major Mapping details has been successfully edited and sent for checker's Approval.
feeConfig.updateSuccess.msg=Fee Mapping has been edited successfully and sent for checker's Approval.
AM_MSG_FeeMajorApproved =Fee Major has been successfully approved.
AM_MSG_FeeMajorRejected =Fee Major has been successfully rejected.
AM_MSG_FeeMinorApproved =Fee Minor has been successfully approved.
AM_MSG_FeeMinorRejected =Fee Minor has been successfully rejected.
AM_MSG_FeeConfigApproved =Fee Config has been successfully approved.
AM_MSG_FeeConfigRejected =Fee Config has been successfully rejected.
addEditUser.creation.limit=User creation Limit for Admin has exceeded the Limit
common.msg.lbl.deleted=Deleted
AM_MSG_REPEATED_CHAR=Cannot use repeated characters.

txn.detail.lbl.panTokenPanNo=PAN/Token PAN
txn.detail.lbl.dateAndTimeLocalTransaction=Date and Time, Local Transaction
txn.detail.lbl.acquirerReferenceData=Acquirer Reference Data
txn.detail.lbl.rrn=RRN
txn.detail.lbl.transactionAmount=Transaction Amount
txn.detail.lbl.transactionAdditionalAmount=Transaction Additional Amount
txn.detail.lbl.approvalCode=Approval Code
txn.detail.lbl.acquirerBank=Acquirer Bank
txn.detail.lbl.issuerBank=Issuer Bank
txn.detail.lbl.cardAcceptorBusinessCode=Card Acceptor Business Code
txn.detail.lbl.eCommerceIndicator=E-Commerce Indicator
txn.detail.lbl.caseNumber=Case Number
txn.detail.lbl.cardAcceptorTerminalID=Card Acceptor Terminal ID
txn.detail.lbl.originator=Originator
txn.detail.lbl.destination=Destination
txn.detail.lbl.currentStatus=Current Status
txn.detail.lbl.actionCode=Action Code
txn.detail.lbl.arqcAuthorizationIndicator=ARQC Authorization Indicator
txn.detail.lbl.processingCode=Processing Code
txn.detail.lbl.currencyCodeTransaction=Currency Code, Transaction
txn.detail.lbl.currencyCodeSettlement = Currency Code, Settlement
txn.detail.lbl.amountTransaction=Amount, Transaction
txn.detail.lbl.amountsAdditional=Amounts, Additional
txn.detail.lbl.amountAdjustment=Amount, Adjustment
txn.detail.lbl.posEntryMode=POS Entry Mode
txn.detail.lbl.posConditionCode=POS Condition Code
txn.detail.lbl.acquirerInstitutionIDCode=Acquirer Institution ID Code
txn.detail.lbl.cardAcceptorIDCode=Card Acceptor ID Code
txn.detail.lbl.cardAcceptorLocationAddress=Card Acceptor Location/Address
txn.detail.lbl.cardAcceptorStateCode=Card Acceptor State Code
txn.detail.lbl.cardAcceptorZipCode=Card Acceptor Zip Code
txn.detail.lbl.cardAcceptorName=Card Acceptor Name
txn.detail.lbl.rgcsSettlementDate=RGCS Settlement Date
txn.detail.lbl.acquirerCountryCode=Acquirer Country Code
txn.detail.lbl.amountSettlement=Amount, Settlement
txn.detail.lbl.internationalAuthNetworkID=International Auth Network ID
txn.detail.lbl.cardAcceptorCity=Card Acceptor City
txn.detail.lbl.cardHolderUID=Card Holder UID
txn.detail.lbl.fraudScore=Fraud Score
txn.detail.lbl.loyaltyPointForDebit=Loyalty Point for Debit
txn.detail.lbl.recurringPaymentIndicator=Recurring Payment Indicator
txn.detail.lbl.icsResultCode2=ICS Result code 2
txn.detail.lbl.personalPhraseCode=Personal Phrase Code
txn.detail.lbl.serviceCodePosition2=Service Code Position 2
txn.detail.lbl.ics2DataFromAuthorization=ICS 2 Data from Authorization
txn.detail.lbl.transactionID=Transaction ID
txn.detail.lbl.transactionOriginatorInstitutionIDCode=Transaction Originator Institution ID Code
txn.detail.lbl.cardAcceptorAdditionalAddress=Card Acceptor Additional Address
txn.detail.lbl.pinEntryCapability=PIN Entry Capability
txn.detail.lbl.eciIndicator=ECI Indicator
txn.detail.lbl.cvdIcvdMatchResult=CVD ICVD Match Result
txn.detail.lbl.cardHolderIncomeTaxPan=Card Holder Income Tax PAN
txn.detail.lbl.icsResultCode1=ICS Result Code 1
txn.detail.lbl.productCode=Product Code
txn.detail.lbl.emiAmount=EMI Amount
txn.detail.lbl.stipIndicator=STIP Indicator
txn.detail.lbl.imageCode=Image Code
txn.detail.lbl.serviceCodePosition1=Service Code Position 1
txn.detail.lbl.ics1DataFromAuthorization=ICS 1 Data from Authorization
txn.detail.lbl.serviceCodePosition3=Service Code Position 3
txn.detail.lbl.crossBorderFlag=Cross Border Flag
txn.detail.lbl.loyaltyBalance=Loyalty Balance
txn.detail.lbl.merchantTelephoneNumber=Merchant Telephone Number
txn.detail.lbl.customerMobileTelNumber=Customer Mobile / Tel. Number
txn.detail.lbl.cvd2MatchResult=CVD2 Match Result
txn.detail.lbl.transactionDestinationInstitutionIDCode=Transaction Destination Institution ID Code
txn.detail.lbl.smallMerchant=Small Merchant
txn.detail.lbl.licMerchant=LIC Merchant
txn.detail.lbl.reasonSubtype=Reason Subtype
txn.detail.lbl.reasonCodeAndDescription = Reason Code and Description
txn.detail.lbl.raiseUserId = Raise User ID
txn.detail.lbl.fullPartial = Full / Partial
txn.detail.lbl.full = Full
txn.detail.lbl.Partial = Partial
txn.detail.lbl.internalTrackingNumber = Internal Tracking Number
txn.detail.lbl.messageReasonCode=Message Reason Code
txn.detail.lbl.documentIndicator = Document Indicator
txn.detail.lbl.memberMessageText=Member Message Text
txn.detail.lbl.raiseDate = Raise Date
txn.detail.lbl.raiseDateAndTime = Raise Date and Time
txn.detail.lbl.latePresentmentIndicator = Late Presentment Indicator
txn.detail.lbl.merchantIndicator = Merchant Indicator
txn.detail.lbl.checkerUserName = Checker User Name
txn.detail.lbl.makerUserName = Maker User Name
txn.detail.lbl.controlNo = Control Number
txn.detail.lbl.fullPartialIndicator = Full / Partial Indicator
txn.detail.lbl.internalTrackingNo = Internal Tracking Number
txn.detail.lbl.memberMsgText = Member Message Text
txn.detail.lbl.documentIndicator = Document Indicator
txn.detail.lbl.deadlineDate = DeadLine Date
txn.detail.lbl.cardAcceptorCountryCode =  Card Acceptor Country Code
txn.detail.lbl.cardAcceptorStateCode = Card Acceptor State Code
txn.detail.lbl.cardAcceptorCity = Card Acceptor City
txn.detail.lbl.verdictDecision = Verdict Decision
txn.detail.lbl.verdictAmount = Verdict Amount
txn.detail.iccData.lbl.TS4FICCApplicationID=TS4F- ICC Application ID
txn.detail.iccData.lbl.TS4FLNNA=TS4FLN- N. A
txn.detail.iccData.lbl.TS5F2ATransactionCurrencyCode=TS5F2A- Transaction Currency Code
txn.detail.iccData.lbl.TS82ApplicationInterchangeProfileAIP=TS82- Application Interchange Profile (AIP)
txn.detail.iccData.lbl.TS84DedicatedFileName=TS84- Dedicated File Name
txn.detail.iccData.lbl.TS91LNNA=TS91LN - N.A
txn.detail.iccData.lbl.TS91IssuerAuthenticationData=TS91- Issuer Authentication Data
txn.detail.iccData.lbl.TS95TerminalVerificationResultsTVR=TS95- Terminal Verification Results (TVR)
txn.detail.iccData.lbl.TS9ATransactionDate=TS9A- Transaction Date
txn.detail.iccData.lbl.TS9CTransactionType=TS9C- Transaction Type
txn.detail.iccData.lbl.TS9F02AmountAuthorized=TS9F02- Amount Authorized
txn.detail.iccData.lbl.TS9F03AmountOther=TS9F03- Amount Other
txn.detail.iccData.lbl.TS9F06ApplicationIdentifierAIDTerminal=TS9F06- Application Identifier (AID) - Terminal
txn.detail.iccData.lbl.TS9F07ApplicationUsageControl=TS9F07- Application Usage Control
txn.detail.iccData.lbl.TS9F10IssuerApplicationDataIAD=TS9F10- Issuer Application Data (IAD)
txn.detail.iccData.lbl.TS9F1ATerminalCountryCode=TS9F1A- Terminal Country Code
txn.detail.iccData.lbl.TS9F1EInterfaceDeviceIFDSerialnumber=TS9F1E- Interface Device (IFD) Serial number
txn.detail.iccData.lbl.TS9F26ApplicationCryptogram=TS9F26- Application Cryptogram(ARQC/TC/AAC)
txn.detail.iccData.lbl.TS9F27CryptogramInformationData=TS9F27- Cryptogram Information Data (CID)
txn.detail.iccData.lbl.TS9F33TerminalCapabilities=TS9F33- Terminal Capabilities
txn.detail.iccData.lbl.TS9F34CVMResults=TS9F34- CVM Results
txn.detail.iccData.lbl.TS9F35TerminalType=TS9F35- Terminal Type
txn.detail.iccData.lbl.TS9F36ApplicationTransactionCounter=TS9F36- Application Transaction Counter (ATC)
txn.detail.iccData.lbl.TS9F37UnpredictableNumber=TS9F37- Unpredictable Number
txn.detail.iccData.lbl.TSAIDLNA=TSAIDL- N.A.
txn.detail.iccData.lbl.TSDYNLNA=TSDYNL- N.A.
txn.detail.iccData.lbl.TSPISINA=TSPISI- N.A.
txn.detail.iccData.lbl.TSRAKYNA=TSRAKY- N.A.
txn.detail.iccData.lbl.TSSTANNA=TSSTAN- N.A.
txn.detail.posData.lbl.cardDataInputCapability=Card Data Input Capability
txn.detail.posData.lbl.cardholderAuthenticationCapability=Cardholder Authentication Capability
txn.detail.posData.lbl.cardCaptureCapability=Card Capture Capability
txn.detail.posData.lbl.terminalOperatingEnvironment=Terminal Operating Environment
txn.detail.posData.lbl.cardholderPresentData=Cardholder Present Data
txn.detail.posData.lbl.cardPresentData=Card Present Data
txn.detail.posData.lbl.cardDataInputMode=Card Data Input Mode
txn.detail.posData.lbl.cardholderAuthenticationMethod=Cardholder Authentication Method
txn.detail.posData.lbl.cardholderAuthenticationEntity=Cardholder Authentication Entity
txn.detail.posData.lbl.cardDataOutputCapability=Card Data Output Capability
txn.detail.posData.lbl.terminalDataOutputCapability=Terminal Data Output Capability
txn.detail.posData.lbl.pinCaptureCapability=PIN Capture Capability
txn.detail.fees.lbl.feeName=Fee Name
txn.detail.fees.lbl.feeAmount=Fee Amount
txn.detail.fees.lbl.currency=Currency
txn.detail.fees.lbl.acqFee=Acquirer Fee
txn.detail.fees.lbl.issFee=Issuer Fee
feeMinor.discardSuccess.msg=Fee minor Discarded successfully.
feeMajor.discardSuccess.msg=Fee major Discarded successfully.
txn.detail.success.msg = Request has been raised successfully.
txn.detail.fetch.error = Error while fetching the transaction list
txn.detail.select.error = Please select the record.
txn.detail.action.error=Please configure desired actions in json file properly

txn.detail.oct.lbl.stan=STAN
txn.detail.oct.lbl.octDt=OCT Date Time
txn.detail.oct.lbl.remitterInstrumentType=Remitter Instrument Type
txn.detail.oct.lbl.remitterInstrumentId=Remitter Instrument Id
txn.detail.oct.lbl.merchantBankAccNo=Merchant Bank Account Number
txn.detail.oct.lbl.payloadFormatInd=Payload Format Indicator
txn.detail.oct.lbl.pointOfInitMethod=Point of Initiation Method
txn.detail.oct.lbl.tipFeeInd=Tip Fee Indicator
txn.detail.oct.lbl.tipFeeAmt=Tip Fee Amount
#Customer Complaint
txn.detail.lbl.custCompModalTitle=Complaint Case Details
txn.detail.lbl.bankCompNo=Bank Complaint No
txn.detail.lbl.bankCompDt=Bank Complaint date
txn.detail.lbl.CRN=CRN
txn.detail.lbl.mobileNo=Mobile No
txn.detail.lbl.email=Email Address
txn.detail.lbl.compStatus=Complaint Status
txn.detail.lbl.compExpiry=Complaint Expiry Date Time
txn.detail.lbl.compClosureRson=Complaint Closure Reason
txn.detail.lbl.viewCustCompBtn=View Complaint Case

##News&Alerts
am.lbl.addNewsALerts = Add News & Alerts
am.lbl.newsAlertsList = News & Alerts List
am.lbl.newsAlerts = News and Alerts Management
am.lbl.refNumber = Reference Number
am.lbl.type = Type
am.lbl.title = Title
am.lbl.subTitle = Sub Title
am.lbl.fromDate = From Date 
am.lbl.toDate = To Date
am.lbl.footerData = Footer Data
am.lbl.status = Status
am.lbl.action = Action
fm.lbl.BroadCast.BroadCast=Broadcast Messages
common.msg.lbl.reset = Reset
common.msg.lbl.firsttimereset = First Time Reset
am.lbl.userType = User Type
am.lbl.ntwAdmin = Network Admin
am.lbl.bankAdmin = Bank Admin
am.lbl.bankUsers = Bank Users
newsReports.viewscreen.title= Download Reports
newsreports.downloadButton= Download
am.lbl.viewMember =Member Details
newsAlert.discardSuccess.msg=Rejected News and Alerts data has been discarded successfully.

am.msg.DisputeSubmitted = Dispute Raised successfully.
am.msg.DisputeApproved = Dispute Approved successfully
am.msg.DisputeRejected = Dispute Rejected
am.msg.InProgress = Dispute in Progress
am.msg.RejectRson = Rejected Reason : 
am.msg.Reviewed = Reviewed sent for approval
am.msg.sentForReview = Successfully sent for Review
##REPORTS

am.lbl.file.fromDate = From Date
am.lbl.file.toDate = To Date
am.lbl.file.fileName = File Name
am.lbl.file.fileType = File Type
am.lbl.file.fileGenDate = Report Date
am.lbl.file.action = Action
am.lbl.file.download = Download
am.lbl.title = NPCI Portal
am.lbl.file.memberName= Member Name
am.lbl.file.fileSearch= File Search
am.lbl.file.id = ID



##Fileupload

Elib.addFiles = Add Files
reports.FileUploadScreen.title = Upload Files
Elib.UploadButton = Upload
bulk.filena.error=File not available in specified location
am.lbl.users=User Name
am.lbl.organization=Organization

##News and alerts
news.title.validation.msg=Title is mandatory
news.subTitle.validation.msg=SubTitle is mandatory
news.summary.validation.msg=Summary is mandatory
news.details.validation.msg=Details is mandatory
news.footerData.validation.msg=FooterData is mandatory
news.public.validation.msg=Publish Type is mandatory. Select Public/Specific/Common
news.common.validation.msg=Publish Type is mandatory. Select Public/Specific/Common
news.specific.validation.msg=Publish Type is mandatory. Select the Organization,Roles,Users for Publish type- Specific
news.weekFrom.validation.msg=Week From is mandatory
news.weekTo.validation.msg=Week To is mandatory
news.periodType.validation.msg=Period Type is mandatory
news.monthFrom.validation.msg=Month From is mandatory
news.monthTo.validation.msg=Month To is mandatory
news.comment.validation.msg=Comments is mandatory

##Fees
feeRate.feeTypCode.validation.msg= Please Select Fee Type
feeRate.feeCode.validation.msg=Fee Code is mandatory and should be 4 digits
feeRate.feeDesc.validation.msg= Fee Description is mandatory
feeRate.txnCurrency.validation.msg= Please Select txnCurrency
feeRate.cashFeeFlat.validation.msg= Please provide valid input with max of 6 numerics before decimal and 3 numerics after decimal
feeRate.cashFeePercent.validation.msg=Please provide valid input with max of 2 numerics before decimal and 3 numerics after decimal
feeRate.cashFeeMin.validation.msg= Please provide valid input with max of 6 numerics before decimal and 3 numerics after decimal 
feeRate.cashFeeMax.validation.msg= Please provide valid input with max of 6 numerics before decimal and 3 numerics after decimal
feeRate.purFeeFlat.validation.msg=Please provide valid input with max of 6 numerics before decimal and 3 numerics after decimal
feeRate.purFeePercent.validation.msg=Please provide valid input with max of 2 numerics before decimal and 3 numerics after decimal 
feeRate.purFeeMin.validation.msg=Please provide valid input with max of 6 numerics before decimal and 3 numerics after decimal 
feeRate.purFeeMax.validation.msg=Please provide valid input with max of 6 numerics before decimal and 3 numerics after decimal
feeRate.status.validation.msg=numeric cashFeeMax mandatory
feeRate.validToDt.validation.msg=Please Select validToDt
feeRate.validFromDt.validation.msg=Please Select validFromDt
feeRate.gstCode.validation.msg= Please Select GST CODE
feeRate.creditTo.validation.msg= Please Select Credit To
feeRate.debitTo.validation.msg=Please Select Debit To
feeRate.schemeCode.validation.msg=Please Select Scheme Code
feeRate.cardType.validation.msg=Please Select Card Type
feeRate.cardBrand.validation.msg=Please Select Card Brand
feeRate.productCode.validation.msg=Please Select Prod Code
feeRate.funCd.validation.msg=Please Select Func Code
feeRate.creditTo.validation.msg= Please Select Credit To
feeRate.debitTo.validation.msg=Please Select Debit To
feeRate.schemeCode.validation.msg=Please Select Scheme Code
feeRate.cardType.validation.msg=Please Select Card Type
feeRate.cardBrand.validation.msg=Please Select Card Brand
feeRate.productCode.validation.msg=Please Select Prod Code
feeRate.funCd.validation.msg=Please Select Func Code


#member
memberName.validation.msg=Bank Name must contain alphabets,numbers and (,),_,-,. only.

uniqueBnk.validation.msg=Bank Name must contain alphabets,numbers and (,),_,- only.
gstn.validation.msg=Field must match the pattern ##AAAAA####XXZX (# = number, A = alpha, X = alphanumeric, Z = letter Z).
gstPincode.validation.msg=Pin Code must be numeric and 6 Digit.
bnkPhone.validation.msg=Phone No must be between 8 and 12 numerics.
bnkPhone2.validation.msg=Phone No must be between  8 and 12 numerics.
bnkMobile.validation.msg= Mobile No must be between 10 and 15 numerics.
bnkMobile2.validation.msg = Mobile No must be between 10 and 15 numerics.
ifscCode.validation.msg = IFSC Code is Mandatory.
bankSector.validation.msg =  Bank Sector is Mandatory.
uniqueBnkName.validation.msg = Unique Bank Name is Mandatory.
bnkEmail.validation.msg = Please enter valid Email ID.
bnkEmail2.validation.msg = Please enter valid Email ID.
bnkState.validation.msg = Please select State.
bnkCountry.validation.msg = Please select Country.
cntCountry.validation.msg = Please select Country.
bnkCity.validation.msg = Please select City.
bnkPincode.validation.msg=Pin Code must be 6digit and  numeric only.
cntPincode.validation.msg=Pin Code must be 6digit and numeric only.
gstState.validation.msg = Please select State.
gstCountry.validation.msg = Please select Country.
gstCity.validation.msg = Please select City.
cntChkrName.validation.msg=Name must contain alphabets and . only.
cntPhone.validation.msg=Phone No must be between 8 and 12 numerics.
am.lbl.participantName=Participant Name
cntMobile.validation.msg= Mobile No must be between 10 and 15 numerics.
cntFax.validation.msg = Fax Number should be between 8 and 12 & numeric only
cntEmail.validation.msg = Please enter valid Email ID.
addressType.validation.msg = Please select Address Type.
cntState.validation.msg = Please select State.
cntCity.validation.msg = Please select City.
settlementBin.validation.msg = Please select Settlement Bin.
settlementBinId.validation.msg =Settlement Bin Id must be 2 numerics.
memDir.participant.validation.msg=Select Participant Name
maxUser.validation.msg = Max User Number should be between 1 and 999 & numeric only

binNumber.participant.validation.msg=Bin Number should be 6 digit Numeric.
currencyCode.validation.msg = Please select Currency Code.
acqBankGroup.validation.msg= Please select Bank Group.
acquirerId.validation.msg = Acquirer Id must be 6 numerics and mandatory.
acqDomainUsage.validation.msg = Please select Domain Usage.
acqFrmDate.validation.msg = Please select Activation Date and Activation Date should be less than Deactivation date.
acqToDate.validation.msg = Please select Deactivation date.
issFrmDate.validation.msg = Please select Activation Date and Activation Date should be less than Deactivation date.
issToDate.validation.msg = Please select Deactivation date.
acqProductType.validation.msg = Please select Product Type.
acqSettlementBin.validation.msg = Please select Settlement Bin.
issBankGroup.validation.msg = Please select Bank Group.
issBinType.validation.msg = Please select Bin Type.
lowBin.validation.msg =  Low Bin must be 9 numerics.
highBin.validation.msg = High Bin must be 9 numerics.
panLength.validation.msg=Pan No must be between 8 and 20 numerics.
binCardType.validation.msg = Please select Bin Card Type.
binProductType.validation.msg = Please select Bin Product Type.
binCardVariant.validation.msg = Please select Card Variant.
binCardBrand.validation.msg = Please select Card Brand.
issDomainUsage.validation.msg = Please select Domain Usage.
messageType.validation.msg = Please select Message Type.
cardTechnology.validation.msg = Please select Card Technology.
authMechanism.validation.msg = Please select Auth Mechanism.
issProductType.validation.msg = Please select Product Type.
subScheme.validation.msg = Please select Sub Scheme.
cardSubVariant.validation.msg = Please select Card Sub Variant.
programDetails.validation.msg = Please select Program Details.
formFactor.validation.msg = Please select Form Factor.
issSettlementBin.validation.msg = Please select Settlement Bin.
featureMultiple.validation.msg = Please select Feature Fee.
cntAdd1.validation.msg = Please Enter Address.
bnkAdd.validation.msg = Please Enter Address.
gstAdd.validation.msg = Please Enter Address.
participantIdNFS.validation.msg = Please Enter 3 digit  AlphaNumeric NFS Id.
bankMasterCode.validation.msg = Please enter Bank Master Code
rtgsCode.validation.msg = Please enter RTGS Code
participantId.validation.msg = Please enter Participant Id
savingsAccNumber.validation.msg = Please enter Savings Account Number
memberType.validation.msg = Please Select Bank Type
parentParticipantId.validation.msg = Please Select Sponsor Bank
##user common validation

##user.participantname.validation.msg = Please select Participant Name.
user.loginid.validation.msg= Login Id should be alphanumeric in range of 5 to 50 characters. 
user.empId.validation.msg= Emp Id must include only alphanumeric, $, %, ^, @, #, !, &, *,
user.accesslevel.validation.msg= Please select Access level.
user.salutation.validation.msg=Please select Salutation. 
user.firstname.validation.msg= First Name must include only alphanumeric, ', -,
user.middlename.validation.msg= Middle Name must include only alphanumeric, ', -,
user.lastname.validation.msg= Last Name must include only alphanumeric, ', -,
user.dob.validation.msg= Enter valid date
user.emailid.validation.msg=Enter valid email address
user.mobileno.validation.msg= Mobile should be between 10 to 15 digits & numeric only.
user.contactno.validation.msg= Contact should be between 8 to 12 digits & numeric only.
user.streetaddress.validation.msg= Please enter address.
user.stateId.validation.msg= Please select state
user.cityId.validation.msg=Please select city
user.pincode.validation.msg= Pincode should be numeric with 6 digits.
user.makerchecker.validation.msg= Please select Role Type.
user.status.validation.msg= Please select Status.

#capping amunt
cap.addCappingBtn= Add Capping
am.lbl.CappingAmountList=  Capping Amount List
am.lbl.capAmtflag= Cap Amount Flag
am.lbl.amountCapFlat= Amount Cap Flat
am.lbl.actionCode = Action Code
am.lbl.mccGroup= MCC Group
am.lbl.binCardBrandId= Bin Card Brand Id
am.lbl.binCardTypeId = Bin Card Type Id
am.lbl.capName= Cap Name
am.lbl.capType= Cap Type
am.lbl.fieldName= Field Name
am.lbl.relOperator=Rel Operator
am.lbl.fieldValue= Field Value
am.lbl.amountFlag= Amount Flag
am.lbl.flat= Flat
am.lbl.percentage= Amount Cap Percent
am.lbl.amountCapMax= Amount Cap Max
am.lbl.amountCapMin= Amount Cap Min
am.lbl.createdBy= Created By
am.lbl.createdOn=Created On
am.lbl.lastUpdatedBy=Last Updated By
am.lbl.lastUpdatedOn=Last Updated On
am.lbl.status=Status
am.lbl.requestState=Request State
am.lbl.checkerComments=Checker Comments
cap.MainTab.title= Capping Amount
msg.lbl.CappingAmountList= Capping Amount List
am.lbl.approvalstatus = Approval Status
Cap.addscreen.title= Add Capping Amount
Cap.editscreen.title= Edit Capping Amount
Cap.viewscreen.title= View Capping Amount
Cap.addSuccess.msg= Added Capping amount has been sent for approval 
cap.updateSuccess.msg= Updated Capping Amount is Sent for approval


cap.mccId= Mcc Id
cap.viewscreen.title= Capping Amount Information
cap.approvalPanel.title= Capping Amount Approval
cap.approvalSuccess.msg= Capping Amount Successfully Approved
cap.rejectSuccess.msg= Capping Amount Successfully Rejected


# Fee Rate
feeRate.Approve = Approve
feeRate.Reject = Reject
feeRate.feeTypeCode= FeeTypeCode
feeRate.feeType.validation.msg=Please Select Fee Type
feeRate.feeTypCode.validation.msg=Fee Type Code is mandatory and should be 4 digits.

feeRate.netMin.validation.msg=Please provide valid input with max of 6 numerics before decimal and 3 numerics after decimal
feeRate.netMax.validation.msg=Please provide valid input with max of 6 numerics before decimal and 3 numerics after decimal
#Capping amt Validation
capAmt.status.validation.msg=Please Select Status
capAmt.mccId.validation.msg=Please Select MCC Id
capAmt.capAmountFlag.validation.msg=Please Select Cap Amount Flag
capAmt.funcCode.validation.msg=Please Select Function Code
capAmt.priority.validation.msg=Please Enter priority

capAmt.actionCode.validation.msg= Please Select Action Code
capAmt.mccGroup.validation.msg=Please Select MCC Group
capAmt.capName.validation.msg=Please Select Cap Name
capAmt.fieldValue.validation.msg=Please Select Field Value
capAmt.fieldName.validation.msg=Please Select Field Name
capAmt.relOperator.validation.msg=Please Select Rel Operator


capAmt.amountCapFlat.validation.msg=Amount Cap Flat exceeding numeric precision (10,2)
capAmt.amountCapPercent.validation.msg=Amount Cap Percent exceeding numeric precision (4,2)
capAmt.amountCapMax.validation.msg=Amount Cap Max exceeding numeric precision (10,2)
capAmt.amountCapMin.validation.msg=Amount Cap Min exceeding numeric precision (10,2)
AM.lbl.delete=Delete File
AM.lbl.downloadAllFile=Download All Files
cappingAmount.discardSuccess.msg = Rejected Capping Amount Discarded Successfully

reasonCodeMaster.detail.lbl.reasonCode = Reason Code
reasonCodeMaster.detail.lbl.reasonCodeDesc = Reason Code Desc

AM_MSG_CappingAmountApproved =Capping Amount has been successfully approved.
AM_MSG_CappingAmountRejected =Capping Amount has been successfully rejected.
am.lbl.Approve= Approve
am.lbl.Reject= Reject
AM_MSG_EscalationApproved =Escalation Details has been successfully approved.
AM_MSG_EscalationRejected =Escalation Details has been successfully rejected.


#Action code
action.addSuccess.msg= Action Code Successfully added and sent for Approval
action.editSuccess.msg= Action Code Successfully updated
action.discardSuccess.msg= Action Code Discarded
action.editscreen.title= Action Code Edit Screen
action.addscreen.title= Action Code Add Screen
action.viewscreen.title= Action Code View Screen
action.approveSuccess.msg=Action Code approved successfully
action.rejectSuccess.msg=Action Code rejected successfully	

am.lbl.mti= MTI
am.lbl.actionCode= Action Code
am.lbl.actionCodeDesc= Action Code Description
am.lbl.raisedBy=Raised By
action.viewscreen.title= Action Code View Screen
action.lbl.update = Update
action.mti.validation.msg= Please Select mti
action.actionCode.validation.msg=Please Enter Action Code and should be AlphaNumeric with 2 digits
action.funcCode.validation.msg= Please Select Function Code
action.actionCodeDesc.validation.msg=Please Enter Action Code Desc
action.funcCodeDesc.validation.msg=Please Enter Function Code Desc
action.raisedBy.validation.msg=Please Select Raised By
action.tatPeriod.validation.msg=Please Enter Numeric Tat Period
action.tatPeriodDayType.validation.msg=Please Select Tat Period DayType
action.transitionActionCode.validation.msg=Please Select Transition Action Code
action.allowedActncdToRemove.validation.msg=Please Select Allowed Action Code To Remove
action.capAmtCalReq.validation.msg=Please Select CAP AMT Cal Req
action.defaultReasonRejCode.validation.msg=Please Select Default Reject Reason Code



action.mti.validation.msg= Please Select mti
action.actionCode.validation.msg=Please Enter Action Code and should be AlphaNumeric with 2 digits
action.funcCode.validation.msg= Please Select Function Code
action.actionCodeDesc.validation.msg=Please Enter Action Code Desc
action.funcCodeDesc.validation.msg=Please Enter Function Code Desc
action.raisedBy.validation.msg=Please Select Raised By

disputeFee.actionCode.validation.msg = Select the Action Code.
disputeFee.feeType.validation.msg = Select the Fee Type.
disputeFee.priority.validation.msg = Enter the priority.
disputeFee.feeCode.validation.msg = Select the Action Code.
disputeFee.relationalOperator.validation.msg = Select the Relational Operator.
disputeFee.fieldName1.validation.msg = Select the field Name.
disputeFee.fieldName2.validation.msg = Select the field Name.
disputeFee.fieldValue.validation.msg = Enter the field Value.
disputeFee.fieldOperator.validation.msg = Select the field Operator.
#Reason Code Rules
reasonCodeRules.actionCode.validation.msg=Please select Action Code
reasonCodeRules.reasonCode.validation.msg=Please select Reason Code
reasonCodeRules.relationOperator.validation.msg=Please select Relation Operator
reasonCodeRules.fieldOperator.validation.msg=Please select Field Operator
reasonCodeRules.relationOperator1.validation.msg=Please select Relation Operator
reasonCodeRules.fieldValue.validation.msg=Please Enter Field Value
reasonCodeRules.fieldValue1.validation.msg=Please Enter Field Value
reasonCodeRules.logicalReasonCode.validation.msg=Please select Field Operator
reasonCodeRules.addSuccess.msg=Reason Code Rule added successfully
reasonCodeRules.editSuccess.msg=Reason Code Rule edited successfully
reasonCodeRules.discardSuccess.msg=Reason Code Rule discarded successfully
reasonCodeRules.approveSuccess.msg=Reason Code Rule approved successfully
reasonCodeRules.rejectSuccess.msg=Reason Code Rule rejected successfully	
feeRate.daystobewaived.validation.msg = Days to be waived should be numeric with 4 digits



#Settlement Cycle
settlement.productId.validation.msg= Please Enter Product Id
settlement.endMin.validation.msg= Please Select End Minutes
settlement.startMin.validation.msg=Please Select Start Minutes
settlement.endHour.validation.msg=Please Select End Hour
settlement.startHour.validation.msg=Please Select Start Hour
settlement.cycleNumber.validation.msg=Please Enter Cycle Number

am.lbl.FCFD= Service Fee
am.lbl.AddFCFD= Add Service Fee
am.lbl.FCFDList= Service Fee List
am.lbl.ActionType= Action Type
am.lbl.TxnOrgnId= Transaction Originator ID
am.lbl.TxnDestId= Transaction Destination ID
am.lbl.BinType= Bin Type
am.lbl.CreatedOn= Created On
am.lbl.TxnId= Transaction ID
am.lbl.DefaultIssuingID =Default Issuing ID
am.lbl.DefaultAcquiringID= Default Acquiring ID
am.lbl.SettlementBin=Settlement Bin
am.lbl.FeeNames= Fee Names
am.lbl.NPCIMessageTxt= NPCI Message Text
am.lbl.FeeCurrency= Fee Currency
am.lbl.Amount= Amount
am.lbl.FeeCategoryCode = Fee Category Code
fcfd.viewscreen.title= Service Fee Information
FCFD.transactionDestID.validation.msg = Select Transaction Destination ID
FCFD.binType.validation.msg = Select Bin Type
FCFD.feeCcyCode.validation.msg =Select Currency Code
FCFD.amount.validation.msg = Enter Amount
FCFD.feeCategoryCode.validation.msg = Enter Fee Category Code
FCFD.memberMsgTxt.validation.msg = Enter Message
FCFD.fcfdAction.validation.msg = Select Action
FCFD.issuerId.validation.msg = Select Issuer ID
FCFD.acquirerId.validation.msg = Select Acquirer ID
FCFD.settlementBin.validation.msg = Select Settlement Bin
fc.success.msg = Fee Collection saved successfully
fd.success.msg = Fee Disbursement saved successfully
FCFD.feeNames.validation.msg = Select Fee Names
FCFD.npciMsgText.validation.msg = Enter Message

AM_MSG_FCFDApproved=Service Fee approved successfully
AM_MSG_FCFDRejected=Service Fee rejected successfully
Fcfd.discardSuccess.msg = Service fee discarded
fcfd.discardBtn= Discard
report.io.error.msg=Error while creating file
report.date.error=Invalid Date format

dispute.fee.success.msg =  Added Dispute fee submitted successfully and sent for Approval
dispute.transition.validation.entityMsg = Please select entity id
dispute.transition.validation.currStateMsg = Please select current state
dispute.transition.validation.toStateMsg = Please select to state
dispute.transition.validation.fieldNameMsg = Please select field name
dispute.transition.validation.operatorMsg = Please select logical operator
dispute.transition.validation.relOpMsg = Please select relational operator
dispute.transition.validation.fieldValueMsg = Value should contain Numbers,comma or star, but for compare to operator it should have alphabets or numbers, comma, hyphen, star and underscore
dispute.transition.validation.fieldOperatorMsg = Please select field operator
dispute.transition.success.msg = Request sent for approval
dispute.transition.request.msg=Request Information
dispute.transition.approve.reject=Approve/Reject :
dispute.transition.records.msg = No records present
dispute.transition.recordsSelect.msg = Please select a record to approve/reject
dispute.transition.approve.msg = Transition Rule has been successfully approved
dispute.transition.reject.msg = Transition Rule has been successfully rejected
dispute.transition.discard.msg = Transition Rule has been successfully discarded

#NPCI FUND FEE
npci.fund.success.msg = Request sent for approval
npci.fund.action.msg = Please select the action.
npci.fund.txnDestInstId.msg = Please select the transaction Destination Id.
npci.fund.feeType.msg = Please select the Fee Type.
npci.fund.binType.msg = Please select the Bin Type.
npci.fund.currency.msg = Please select the Fee Currency.
npci.fund.memMsg.msg = Please enter the member message.
npci.fund.amtTran.msg = Please enter the transaction amount.
npci.fund.defaultIssId = Please select the issuer Id.
npci.fund.defaultAcqId = Please select the acquirer Id.
npci.fund.defaultIssIdErr = Default Issuer Id Information not found.
npci.fund.defaultAcqIdErr = Default Acquirer Id Information not found.
npci.fund.settlementBin = Settlement Bin not found

networkbin.fileUpload.success.msg = File Upload success
networkbin.fileUpload.inProgress.msg = File Upload in Progress Please Wait....
#SettlementCycleConfig
st.lbl.cycleNo=Cycle Number
st.lbl.startHour=Start Hour
st.lbl.endHour=End Hour
st.lbl.isActive=Is Active
st.lbl.icnKey=Total ICN
st.lbl.dateInc=Date Increment
st.lbl.productId=Product Id
st.lbl.startMin= Start Min
st.lbl.endMin= End Min
settlementCycle.addscreen.title = Add Settlement Cycle
settlementCycle.editscreen.title = Edit Settlement Cycle
st.lbl.cycleNo=Cycle Number
st.lbl.start=Start Hour
st.lbl.end=End Hour
st.lbl.isActive=Is Active
st.lbl.icnKey=Total ICN
st.lbl.dateInc=Date Increment
st.lbl.createdOn=Created On
st.lbl.createdBy=Created By
st.lbl.requestState=Request State
st.lbl.lastUpdated=Last Updated By
settlement.srNo.msg=Sr.No
settlement.cycleNumber.msg=Cycle Number
settlement.startHour.msg=Start Hour
settlement.endHour.msg=End Hour
settlement.isActive.msg=IsActive
settlement.ICNkeys.msg=ICN Keys
settlement.DateIncrement.msg=Date Increment
settlement.viewscreen.title=Settlement Cycle View Screen
settlement.approvalPanel.title= Settlement Approval Panel


settlement.approvalPanel.title= Settlement Approval Panel
action.lbl.submit = Submit
report.date.error=Invalid Date format

##lookup

sm.lbl.lkpType=Type
sm.lbl.lkpValue=Code
sm.lbl.lkpDesc=Description
sm.lbl.lkpDescp =Description
sm.lbl.lkpStatus=Status
sm.lbl.lkpCreatedBy=Created By
sm.lbl.lkpCreatedOn=Created On
sm.lbl.lkpLastUpdatedBy=Last Updated By
sm.lbl.lkpLastUpdatedOn=Last Updated On
sm.lbl.requestState = Request State
sm.lbl.lkpCheckerComments = Checker Comments

lkpType.status.validation.msg= Lookup type must contain alphanumeric characters only and lenght must be between 3 to 10.
lkpValue.status.validation.msg= Lookup value must contain alphanumeric characters only and length must be between 1 to 10 character.

lookUp.addSuccess.msg= Lookup Detail added successfully and sent for Approval
lookUp.viewscreen.title= LookUp View Screen

AM_MSG_LookUpApproved =LookUp has been successfully approved.
AM_MSG_LookUpRejected =LookUp has been successfully rejected.
lookup.discardSuccess.msg= LookUp Discarded
lookup.approveSuccess.msg=LookUp approved successfully
lookup.rejectSuccess.msg=LookUp rejected successfully


lookup.lkpType.validation.msg = Please Select Look Up Type.
lookup.lkpType1.validation.msg = Please Enter  Look Up Type and should be AlphaNumeric.
lookup.lkpDesc.validation.msg = Please Enter Look Up Desc and should be AlphaNumeric.
lookup.lkpValue.validation.msg = Please Enter  Look Up Value and should be AlphaNumeric.

document.upload.name = Document Name
document.upload.type = Document Type
document.upload.creation.date = Creation Date
document.upload.createdBy = Created By
document.upload.action = Action
document.download.error = File not available in specified location

msg.lbl.inProgress = In Progress


reasonCode.Rule.success.msg = Reason Code Rules Added SuccessFully and Request sent for approval
reasonCode.Rule2.success.msg = Reason Code Rules Edited SuccessFully and  Request sent for approval




##Holiday Master
AM_MSG_HolidayMasterApproved =Holiday Master has been successfully approved.
AM_MSG_HolidayMasterRejected =Holiday Master has been successfully rejected
msg_err_future_holiday_date=Please enter future date.
msg_err_valid_holiday_date=Please enter valid date
am.lbl.holiday.status=Status
am.lbl.holiday.remarks=Remarks
AM_MSG_holidayApproved=Holiday Activation request approved successfully.
AM_MSG_holidayRejectByApprover=Holiday Activation request rejected successfully.
am.lbl.holiday.action=Action
am.lbl.Holiday.HolidayType=Holiday Type
msg_err_hldType= Please Enter Holiday Type
msg_err_Holiday= Please Enter Holiday
msg_holidaydesc= Please Enter Holiday Description
msg_err_valid_HolidayStr=Please Enter Valid Holiday Remarks
msg_err_valid_HolidayDesc=Please Enter Valid Holiday Description
AM_MSG_errorHolidayDeactivation=Error while Holiday Deactivation.
AM_MSG_holidayDeactivationPending=Holiday Deactivation Request sent for Approval.
am.lbl.Holiday.addEditHoliday=Add Holiday
AM_MSG_holidayPending=Already Pending for Approval.
am.lbl.holiday.holidayType=Holiday Type
am.lbl.holiday.holiday=Holiday
am.lbl.holiday=Holiday
am.lbl.holiday.view=View
am.lbl.remarks=Remarks
am.lbl.holiday.holiday=Remarks
am.lbl.holiday.crtdate=Create Date
am.lbl.holiday.rejectreason=Reject Reason
am.lbl.Holiday.holidayid=HolidayID
am.lbl.Holiday.holiday=Holiday
am.lbl.holiday.holiday=Holiday
AM_MSG_pendingUpdatedHoliday=Succesfully Updated Holiday Details
am.lbl.holiday.appr=Approval
AM_MSG_HolidayPending=Record Already Pending for Approval.
am.msg.successpendingHoliday=New Holiday Request Sent successfully for Approval.
am.lbl.holiday.holidayRemarks=Remarks
am.lbl.Holiday.holidaydesc=Holiday Descriptio
am.lbl.holiday.addEditholiday=Add/Edit Penalty
am.lbl.penalty.addHoliday=Add Holiday
am.lbl.holiday.addHoliday=Add Holiday
am.lbl.Holiday.status=Status
am.lbl.holiday.description=Description
am.lbl.Holiday.penAmt=Holiday Amount
am.lbl.holiday.effFrom=Holiday Date
am.lbl.Holiday.activeInactive=Active/Inactive
am.msg.errorHoliday=Error Creating Holiday
am.msg.pendingHoliday=Holiday Pending for Approval
am.msg.duplicateHoliday=Same Holiday entry already exists for given Holiday type and Holiday Date.
am.lbl.Holiday.Holiday=Holiday
am.lbl.Holiday.appr=Approval
am.lbl.Holiday.HolidayList=Holiday List
am.lbl.Holiday.view=Action
AM_MSG_errorUpdateHoliday=Error Updating Holiday Details



report.noData = No Data available for this Product Code


HolidayMaster.addSuccess.msg= Holiday Master Successfully added


#capping amount 

capAmt.binCardBrandId.validation.msg=Please Select Bin Card Brand Id
capAmt.binCardTypeId.validation.msg=Please Select Bin Card Type Id
capAmt.capType.validation.msg=Please Enter Cap Type AlphaNumeric No Space



##SysParam
sysParam.main.title=Sys Param
sysParam.viewscreen.title=Sys Param Information
sysParam.requestInformation=Request Information
sysParam.requestType=Request Type
sysParam.status=Status
sysParam.requestDate=Request Date

sysParam.requestState.approved.description = Approved 
sysParam.requestState.pendingApproval.description=Pending for Approval
sysParam.requestState.rejected.description=Rejected
sysParam.requestState.discared.description=Discarded
sysParam.requestBy=Request By
sysParam.approverComments= Approver Comments
sysParam.approvalPanel.title=Sys Param Approval
sysParam.resetBtn=Reset
sysParam.submitBtn=Save
sysParam.backBtn=Back



sys.param.validation.msg.sysTypeSelect=Please select sys param type
sys.param.validation.msg.mandatory=This field is mandatory
sys.param.add.label=Add Sys Param
sys.param.edit.label=Edit Sys Param
sys.param.view.label=View Sys Param
sys.param.type.label=Sys Param Type
sys.param.type.others.label=Other
sys.param.key.label=Key
sys.param.value.label=Value
sys.param.description.label=Description
sys.param.status.label=Status
sys.param.update.button=Update
sys.param.discard.button=Discard
sys.param.back.button=Back
sys.param.addProp.label=Add Property

sm.lbl.sysParamMain=Sys Param Config
sm.lbl.addSysParam = Add System Parameter
sm.lbl.sysParamsList = System Parameter Information
sysParams.viewscreen.title = System Parameter View Screen
sm.lbl.requestDate= Date
sm.lbl.requestType= Request Type
sm.lbl.requestBy= Request By
sm.lbl.remarks= Remarks
sm.lbl.approveReject= Approve/Reject
sm.lbl.approve= Approve
sm.lbl.select= Select

AM_MSG_SysParamApproved =Sys Param has been successfully approved.
AM_MSG_SysParamRejected =Sys Param has been successfully rejected
sys.param.validation.msg.key=Please enter Key and should be alphanumeric
sys.param.validation.msg.value=Please enter Value and should be alphanumeric
sys.param.validation.msg.sysType=Please enter Sys Param Type and should be alphanumeric
sys.param.validation.msg.desc=Please enter Description and should be alphanumeric
sys.param.validation.msg.addProp=Please enter Add Property and should be alphanumeric
##Activity Cycle Details
ac.lb.productCode=Product Code
ac.lb.status=Status
ac.lb.activityCode=Activity Code
ac.lb.cycleDate=Cycle Date
ac.lb.cycleNum=Cycle Number
activityModule.listscreen.title = Activity Module
actModule.viewBtn = View
am.lbl.Refresh = Refresh


# Member Onboarding
additionalParams.validation.msg=Please select additional params.
binLength.validation.msg = Please select Bin Length.
binNumber.validation.msg = Bin Number must be 6 or 8 numerics and Mandatory.

#ForexRate
forexRate.main.title=ForexRate
forexRate.viewscreen.title=ForexRate Information
forexRate.networkId= Network Id
forexRate.rateConversion= Rate Conversion
forexRate.dateSettle= Date Settle
forexRate.currencyFrom= Currency From
forexRate.currencyTo= Currency To
forexRate.approval.title=Approval
forexRate.Approve = Approve
forexRate.Reject = Reject
forexRate.clearBtn=Clear
forexRate.csvBtn=CSV
forexRate.exportBtn=Excel
forexRate.searchBtn=Search

forexRate.requestState.approved.description=Approved
forexRate.requestState.pendingApproval.description=Pending for Approval
forexRate.requestState.rejected.description=Rejected
forexRate.requestState.discared.description=Discarded
forexRate.requestType=Request Type
forexRate.status=Status
forexRate.checkerComments=Checker Comments
forexRate.requestInformation=Request Information
forexRate.requestDate=Request Date
forexRate.forexRateId=ForexRate ID
forexRate.backBtn=Back
forexRate.requestStatus=Request Status
forexRate.requestBy=Request By
forexRate.approverComments= Approver Comments
forexRate.approvalPanel.title=ForexRate Approval
forexRate.addBtn=Add ForexRate
forexRate.approval.title=Approval
forexRate.addscreen.title=Add New ForexRate
forexRate.editscreen.title=Edit ForexRate
forexRate.resetBtn=Reset
forexRate.submitBtn=Save
forexRate.listscreen.title=ForexRate List
forexRate.viewscreen.title=ForexRate Informations

forexRate.update.title=Currency Rate Update
forexRate.business.date.title=Current Business Date:
forexRate.conversion=Conversion
forexRate.currencyRate=Currency Rate


#ForexRate Validations
forexRate.networkId.validation.msg= Network Id is mandatory
forexRate.rateConversion.validation.msg=Rate Conversion is mandatory
forexRate.settleDate.validation.msg= Date Settle is Mandatory
forexRate.currencyFrom.validation.msg= Currency From is mandatory
forexRate.currencyTo.validation.msg= Currency To is Mandatory
forexRate.addSuccess.msg=ForexRate data has been sent for approval

#ForexRate Messages
forexRate.updateSuccess.msg=Edited ForexRate data has been sent for approval
forexRate.approvalSuccess.msg=ForexRate data has been approved successfully
forexRate.rejectionSuccess.msg=ForexRate data has been rejected successfully
forexRate.discardSuccess.msg=Rejected ForexRate entry has been discarded successfully
forexRate.updateSuccess.msg=Edited ForexRate data has been sent for approval
ERR_ForexRate_EXISTS1=Duplicate, data Already exits


binNumber.validation.msg = Bin Number must be 6 or 8 or 9 numerics and Mandatory.



txn.scheme.validation.msg=Please Select SchemeCode

#Incomming Transaction details
msg.lbl.incomingTransactionDetails = Incomming Transaction Details
msg.lbl.fromDate = From Date
msg.lbl.schemeCode = Scheme Code
msg.lbl.funcCodeDesc = Function Code Description
msg.lbl.toDate = To Date
msg.lbl.messageDirection = Message Direction
msg.lbl.Pan = Pan
msg.lbl.rrn = RRN
msg.lbl.search= Search
msg.lbl.reset = Reset
msg.lbl.maker = Maker
msg.lbl.checker = Checker
msg.lbl.rejectReason = Reject Reason
msg.lbl.makerAmount= Maker Amount
msg.lbl.funcCode = Function Code
msg.lbl.transactionAmount=Transaction Amount
msg.lbl.currentStatus=Current Status
msg.lbl.acknowledgementStatus=Acknowledgement Status
msg.lbl.markerStatus=Marker Status
msg.lbl.reasonCode= Reason Code
msg.lbl.transactionTime=Transaction Time
msg.lbl.acquirewrReferenceDataOrRRN=Acquirer Reference Data Or RRN
msg.lbl.approvalCode=Approval Code
msg.lbl.dciOrRRN=DCI RRN

#Currency rate file upload 
currencyRate.fileUpload.schemeName=Scheme Name
currencyRate.fileUpload.fileName=File Name
currencyRate.fileUpload.participantId=Participant Id
currencyRate.fileUpload.network=Network
currencyRate.fileUpload.forexId=Forex Id
currencyRate.fileUpload.totalNoRecords=Total No Records
currencyRate.fileUpload.failedRecords=Failed Records
currencyRate.fileUpload.status=Status
currencyRate.fileUpload.insertDate=Insert Date
currencyRate.fileUpload.recordId=Record ID
currencyRate.fileUpload.sellCurrency=Sell Currency
currencyRate.fileUpload.buyCurrency=Buy Currency
currencyRate.fileUpload.rate=Rate
currencyRate.fileUpload.settlementDate=Settlement Date
currencyRate.fileUpload.fileUploadedDate=File Uploaded Date
currencyRate.fileUpload.errorCode=Error Code
currencyRate.fileUpload.fileName=File Name
currencyRate.fileUpload.totalCount=Total Count
currencyRate.fileUpload.receiveDate=Receive Date
currencyRate.fileUpload.fileDate=File Date
currencyRate.fileUpload.action=Action
currencyRate.fileUpload.fromDate=From Date
currencyRate.fileUpload.toDate=To Date
currencyRate.fileUpload.fileStatus=File Status
currencyRate.fileUpload.title=Currency File Upload Information
currencyRate.fileUpload.caption=Currency Rate File Data
currencyRate.fileUpload.fileReject=File Reject Data


