<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="ISO-8859-1">
<title>Insert title here</title>
</head>
<body>
			<table class="table table-striped">
			<caption style="display:none;">View Bin List</caption> 
			<thead style="display:none;"><th scope="col"></th></thead>
				<thead>
					<tr>
						<th scope="col"><label for="squareInput">Participant ID</label></th>
						<th scope="col"><label for="squareInput">Acquirer ID </label></th>
						<th scope="col"><label for="squareInput">Product Type </label></th>
						<th scope="col"><label for="squareInput">Settlement BIN</label></th>
						<th scope="col"><label for="squareInput">Status</label></th>
						
						
						<th scope="col"><label for="squareInput">Action</label></th>
					</tr>
				</thead>
				
				<tbody id="acquirerBinsList">
					<c:forEach var="bin" items="${memberDTO.acqBinList}"
						varStatus="status">
						<tr id="${status.index}">
							<td>${bin.participantId}</td>
							<td>${bin.acquirerId}</td>
							
							<td>${issProductTypeMap[bin.acqProductType]}</td>
							<td>${bin.acqSettlementBin}</td>
								<c:if test="${(bin.status eq 'A')}">
						<td>Active</td>
					</c:if>
					<c:if test="${(bin.status eq 'B')}">
						<td>Blocked</td>
					</c:if>
					
							<td>
							<input type="button" id="viewAcqBin" value="VIEW" class="btn btn-success" onclick="populateAcqBinDtls('${bin.acqBankGroup}','${bin.acquirerId}','${domainUsageMap[bin.acqDomainUsage]}','${issProductTypeMap[bin.acqProductType]}','${bin.acqSettlementBin}','${bin.acqFrmDateStr}','${bin.acqToDateStr}')">
																																														
						</tr>
					</c:forEach>
				</tbody>
			</table>

</body>
</html>