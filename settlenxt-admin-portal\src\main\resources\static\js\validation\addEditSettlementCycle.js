$(document).ready(function () {

 setProp();
 
if($('#editFlow').val() == "Y"){


let text =$("#StartHour").val();

var myArray = text.split(":");

document.getElementById('startMin').value=myArray[1];

let text1 =$("#EndHour").val();
var myArray1 = text1.split(":");

$("#endMin").val(myArray1[1]);


	if($("#isActiv").val()=="t"){
	$("#isActiveValue").prop("checked", true);}
	if($("#dateInc").val()==1){
 	$("#dateIncrement").prop("checked", true);}
}
 $('#cycleNumber').on('keyup keypress blur change',function () {
         validateFromCommonVal('cycleNumber', true, "AlphaNumericNoSpace", 2, true);
        });

 $('#startHour').on('keyup keypress blur change',function () {
         validateFromCommonVal('startHour', true, "SelectionBox", 20, false);
         
         
         if($('#startHour').val()!="0" && $('#endHour').val()!="0" && $('#cycleNumber').val()!=""){
           validateSettlementTime();
         }
       
        });

 $('#endHour').on('keyup keypress blur change',function () {
         validateFromCommonVal('endHour', true, "SelectionBox", 20, false);
        
            
           
        });
        
        $('#endMin').on('keyup keypress blur change',function () {
         if($('#startHour').val()!="0" && $('#endHour').val()!="0" && $('#cycleNumber').val()!="") {
         validateSettlementTime();
         }
        
        
        });

 
 $('#startMin').on('keyup keypress blur change',function () {
         validateFromCommonVal('startMin', true, "SelectionBox", 20, false);
        });
        
        
         $('#endMin').on('keyup keypress blur change',function () {
         validateFromCommonVal('endMin', true, "SelectionBox", 20, false);
        });
 $('#productId').on('keyup keypress blur change',function () {
     validateFromCommonVal('productId', true, "AlphaNumericNoSpace", 30, false);
    });
 
 
 
 $('#clearSettlementCycle').click(function()
			{
		
	
	 	 $('#cycleNumber').val("")
	 	  $('#productId').val("")
	  	$('#startHour').val("0")
		$('#endHour').val("0")
		$('#startMin').val("0")
		$('#endMin').val("0")
		$("#isActiveValue").prop("checked", false);
	 	$("#dateIncrement").prop("checked", false);
	 	
	 	 $("#errcycleNumber").hide();
	    $("#errstartHour").hide();
	    $("#errendHour").hide();
	    $("#errendMin").hide();
	    $("#errstartMin").hide();
	    $("#errisActiveValue").hide();
	    $("#errdateIncrement").hide();
	   
	   
		    			});
 
 
 
 
 

 
 
 
 
});


var ajaxValidTime;
var ajaxValidTimes=false;

function setProp() {
	if (($('#showButton').val() == "Y")) {
		$("input").prop('disabled', true);
		$("select").prop('disabled', true);
		if ($("#isActiv").val() == "true" || $("#isActiv").val() == "t") {
			$("#isActiveValue").prop("checked", true);
		}
		if ($("#dateInc").val() == 1) {
			$("#dateIncrement").prop("checked", true);
		}
	}
}

function callBackDupSettlementTime(flag){
	ajaxValidTime=flag;
}
function validateSettlementTime() {
	var flow="";
	 
	 
	var startHour=$('#startHour').val()+":"+$('#startMin').val();
	
	var endHour=$('#endHour').val()+":"+$('#endMin').val();
	var cycleNum=$('#cycleNumber').val();
	var prodId=$('#productId').val();
	var tokenValue = document.getElementsByName("_TransactToken")[0].value;
	if($('#editFlow').val() == "Y"){
	 flow="editFlow"}
	else{
		flow="addFlow"
	}
	
	if (flow != "") {

		$.ajax({
			url: "checkDupSettlementTime",
			type: "POST",
			dataType: "json",
			data: {
				"flow": flow,
				"startHr":startHour,
				"endHr":endHour,
				"cycleNum":cycleNum,
				"prodId":prodId,
				"_TransactToken": tokenValue
			},
			async:false,
	        cache: false,
	        timeout: 30000,
			success: function(response) {

				if (response.status == "BSUC_0001") {
					callBackDupSettlementTime(true);
					 
				$('#errorStatus').html("Selected Time already configured");
				$('#jqueryError').show();
				$('#jquerySuccess').hide();
				ajaxValidTimes = true;
				


				} else {
					
				
					 callBackDupSettlementTime(false);
					 $('#errorStatus').html("");
					 $('#jquerySuccess').hide();
					 $('#jqueryError').hide();
					 ajaxValidTimes = false;
				}
			},
			error: function(_request, _status, _error) {
				 $('#errorStatus').html("");
				 $('#jqueryError').hide();
			}
		});

	} else {
		ajaxValidTimes = false;
	}
	return ajaxValidTimes;
}


function submitForm(url) {
	var data = "";
	postData(url, data);
}


function validateOverLapTime(){

var startHour=$('#startHour').val()+":"+$('#startMin').val();
var endHour=$('#endHour').val()+":"+$('#endMin').val();
var cycleNum=$('#cycleNumber').val().toUpperCase();
var prodId=$('#productId').val();
var tokenValue = document.getElementsByName("_TransactToken")[0].value;
let flag2="";
$.ajax({
			url: "checkOverLapSettlementTime",
			type: "POST",
			dataType: "json",
			data: {
				
				"startHr":startHour,
				"endHr":endHour,
				"cycleNum":cycleNum,
				"prodId":prodId,
				"_TransactToken": tokenValue
			},
			async:false,
	        cache: false,
	        timeout: 30000,
			success: function(response) {

				if (response.status == "BSUC_0001") {
				
				$('#errorStatus').html("Selected Time is overlapping with another configuration for same product Id");
				$('#jqueryError').show();
				$('#jquerySuccess').hide();
				flag2=true;
				
				}
				else{
				
					$('#errorStatus').html("");
					 $('#jquerySuccess').hide();
					 $('#jqueryError').hide();
					flag2=false;
				}
				},
			error: function(_request, _status, _error) {
				 $('#errorStatus').html("");
				 $('#jqueryError').hide();
			}
		});
					
return flag2;


}


function validateSettlementCycle(action){
	var check = false;
	
	if(!check){
		
		if(validateSettlementTime())
		
			if(ajaxValidTime){
				check=true;
			}
		
	}
	
	if(!check)
	{
		check = validateOverLapTime();
			
	
	}
	


	  if(!validateFromCommonVal('cycleNumber', true, "AlphaNumericNoSpace", 2, true)) {
		  check = true;
		  
	     }
	     
	    
	     
	       if(!validateFromCommonVal('startHour', true, "SelectionBox", 20, false)) {
		  check = true;
		  
	     }
	     
	     

	    
	     
	       if(!validateFromCommonVal('endHour', true, "SelectionBox", 20, false)) {
		  check = true;
		  
	     }
	       
	     
	       
	       if(!validateFromCommonVal('productId', true, "AlphaNumericWithSpace", 30, false)) {
		  check = true;
		  
	     }
	    

	            if(!validateFromCommonVal('startMin', true, "SelectionBox", 20, false)) {
		  check = true;
		  
	     }
	     
	      if(!validateFromCommonVal('endMin', true, "SelectionBox", 20, false)) {
		  check = true;
		  
	     }
	      
	  

	 if (!check) {
	      
	     
	      addOrUpdateSettlementCycle(action)
	      }else{
	      return false;
	      }
	 
	}


function userAction(action) {
let url =action;
var srNo =$("#srNo").val();
var data ="srNo,"+ srNo;
postData(url, data);
}


function addOrUpdateSettlementCycle(action){

	var dateInc=0;
	
	if($('#dateIncrement').prop("checked"))
		{
		dateInc=1;
		}
	
	var cyclnum= $('#cycleNumber').val().toUpperCase();

    var data = "cycleNumber," + cyclnum  + ",startHour," + $('#startHour').val() + ",endHour," + $('#endHour').val()  + ",isActiveValue," +$("#isActiveValue").prop("checked")  + ",dateIncrement," + dateInc+ ",productId," + $('#productId').val() +",reqState," + $('#reqState').val()+",srNo," + $('#srNo').val() + ",startMin," + $('#startMin').val() + ",endMin," + $('#endMin').val();                              
   
    postData(action, data);

}

