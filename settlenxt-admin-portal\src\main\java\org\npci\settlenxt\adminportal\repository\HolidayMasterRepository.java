package org.npci.settlenxt.adminportal.repository;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.npci.settlenxt.adminportal.dto.HolidayMasterDTO;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;

@Mapper
public interface HolidayMasterRepository {
	List<HolidayMasterDTO> getApprovedHolidayMasterList(@Param("requestState") String requestState);

	List<HolidayMasterDTO> getPendingHolidayMasterList(@Param("requestStateList") List<String> requestStateList);

	HolidayMasterDTO getHolidayById(int holidaySeqId);

	void updateHolidayinStg(HolidayMasterDTO holidayMasterDTO);

	HolidayMasterDTO getHolidayFromMainById(int holidaySeqId);

	List<CodeValueDTO> getProductList();

	void insertHolidayMasterWeeklyHoliday(@Param("holidayMasterDTO") HolidayMasterDTO holidayMasterDTO,
			@Param("weekNumber") int weekNumber);

	Integer fetchIdFromSeq();

	 List<HolidayMasterDTO> getHolidayMasterStgInfoList(
			@Param("holidayMasterlist") List<Integer> holidayMasterlist);

	 void updateHolidayMasterStgState(@Param("lastUpdatedBy") String lastUpdatedBy,
			@Param("lastUpdatedOn") Date lastUpdatedOn, @Param("requestState") String requestState,
			@Param("holidaySeqId") int holidaySeqId, @Param("remarks") String remarks,
			@Param("checkerComments") String checkerComments, @Param("lastOperation") String lastOperation);

	 HolidayMasterDTO getHolidayMasterInfoByHolidaySeqId(@Param("holidaySeqId") Integer holidaySeqId);

	 List<HolidayMasterDTO> getPendingForApprovalHolidayMasterList(
			@Param("requestStateList") List<String> requestStateList);

	 HolidayMasterDTO getHolidayMasterStgInfoBySeqId(@Param("holidaySeqId") int holidayMasterSeqId);

	 void saveHolidayMaster(HolidayMasterDTO holidayMasterDto);

	 void updateHolidayMaster(HolidayMasterDTO holidayMasterDto);

	void deleteDiscardedEntry(int holidaySeqId);

	void deleteReq(@Param("hid") String hid, @Param("state") String state, @Param("status") String status,
			@Param("lastOpDelete") String lastOpDelete);

	 HolidayMasterDTO existingHolidayMasterPublicHoliday(
			@Param("holidayMasterDTO") HolidayMasterDTO holidayMasterDTO);

	 HolidayMasterDTO existingHolidayMasterWeeklyHoliday(
			@Param("holidayMasterDTO") HolidayMasterDTO holidayMasterDTO, @Param("weekNumber") int weekNumber);

}
