	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	
	}
	function backAction(type, action) {
		var url = action;
		var data = "userType," + type;
		postData(url, data);
	}
	
	function postAction(_action) {
	var crtuser="";
	var remarks="";
	var data="";
	var url ="";
	var mccId;
		if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 mccId = $("#mccId").val();
				 crtuser = $("#crtuser").val();
				 remarks=$("#rejectReason").val();
		
				 url = '/approveMcc';
				 data = "mccId," + mccId + ",status," + "Approved" + ",crtuser,"
						+ crtuser + ",remarks,"
						+ remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				postRejectedData(mccId, crtuser, remarks, url, data);
	
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		} else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
function postRejectedData(mccId, crtuser, remarks, url, data) {
    if ('${requestInfo.requestName}' == 'ROLE FUNCTIONALITY MAP') {
        mccId = $("#mccId").val();

        crtuser = $("#crtuser").val();

        remarks = $("#rejectReason").val();

        url = '/rejectMcc';

        data = "mccId," + mccId + ",status," + "Rejected"
            + ",crtuser," + crtuser + ",rejectReason," + remarks;

        postData(url, data);
    }
    else {
        mccId = $("#mccId").val();
        crtuser = $("#crtuser").val();
        remarks = $("#rejectReason").val();

        url = '/approveMcc';

        data = "mccId," + mccId + ",status," + "Rejected"
            + ",crtuser," + crtuser + ",remarks," + remarks;
        postData(url, data);
    }
   
}

	function userAction(action, mccId) {
		
		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		var data = "mccId," + mccId + ",_vTransactToken,"
				+ tokenValue;
		postData(action, data);
	}	
		
	function edit(action, mccId,parentPage) {
		
		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		var data = "mccId," + mccId + ",_vTransactToken,"
				+ tokenValue + ",parentPage," + parentPage;
		postData(action, data);
	}	
	

