package org.npci.settlenxt.adminportal.service;

import java.util.List;

import org.npci.settlenxt.adminportal.dto.DisputeFeeRuleDTO;
import org.npci.settlenxt.adminportal.dto.DisputeTransitionDTO;
import org.npci.settlenxt.portal.common.dto.ActionCodeDTO;
import org.springframework.stereotype.Service;

@Service
public interface DisputeService {
	
	List<DisputeFeeRuleDTO> disputeFeeRuleList();
	
	DisputeFeeRuleDTO getDisputeFeeRuleInfo(int seqId);
	
	List<ActionCodeDTO> getActionCodeList();
	
	List<String> getRelationComplexOpList(String type);

	String addDisputeFeeRule(List<DisputeFeeRuleDTO> disputeFeeRuleDtoList);

	DisputeFeeRuleDTO updateDisputeFeeRuleStg(DisputeFeeRuleDTO disputeFeeRuleDto);

	List<DisputeFeeRuleDTO> getPendingDisputeFeeRuleList();

	DisputeFeeRuleDTO updateApproveDisputeFeeRule(int seqId, String status, String remarks);

	DisputeFeeRuleDTO discardDisputeFeeRule(int seqId);
	
	List<DisputeTransitionDTO> getTransitionRulesList(String type);
	
	String addTransitionRule(List<DisputeTransitionDTO> transitionDTO);
	
	DisputeTransitionDTO getTransitionObj(String id);
	
	String editTransitionRule(List<DisputeTransitionDTO> transitionDTO);
	
	String updateStgTransitionRule(String tranIDList, String status, String comments);
	
	String discardTransRule(String id);

	String updateBulkStgDisputeFee(String disputeFeeList, String status);
}
