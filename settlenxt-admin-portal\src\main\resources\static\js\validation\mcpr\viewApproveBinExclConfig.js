	$(document).ready(function () {
	    $('.appRejMust').hide();
	    $('.remarkMust').hide();
	    
	    $('#apprej').change(function(){
			if ($("#apprej").val() != "N") {
				$(".appRejMust").hide();
			} else {
				$(".appRejMust").show();
				$(".remarkMust").hide();
			}
		});

	});
	function display() {
		$(".appRejMust").hide();
	
	}
	function backAction(type, action) {
		var url = action;
		var data = "userType," + type ;
		postData(url, data);
	}
	
	function postAction(_action) {
	var excId;
	
	var remarks;
	var url;
	var data;
	if(maxLengthTextArea('rejectReason')){
		if ($('#apprej option:selected').val() == "A") {
			if ($("#rejectReason").val() != "") {
				 excId = $("#excId").val();
				 remarks=$("#rejectReason").val();
		
				 url = '/approveBinExclConfig';
				 data = "excId," + excId + ",status," + "A" 
				 + ",remarks," + remarks;
				postData(url, data);
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
		}else if ($('#apprej option:selected').val() == "R") {
			if ($("#rejectReason").val() != "") {
				excId = $("#excId").val();
				  remarks = $("#rejectReason").val();
				
				 url = '/approveBinExclConfig';

				 data = "excId," + excId + ",status," + "R"
						 + ",remarks," + remarks;
				postData(url, data);
			
			} else {
				$(".remarkMust").show();
				$('html, body').animate({ scrollTop: 0 }, 'slow');
				return false;
			}
	
			 
		}
		
		else {
			$(".appRejMust").show();
			$('html, body').animate({ scrollTop: 0 }, 'slow');
			return false;
		}
		}
	}
		
	
	function postDiscardBinAction(action) {
		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		var url = action;
		var excId = document.getElementById("excId").value;
		var data = "excId," + excId + ",_vTransactToken," + tokenValue ;
		postData(url, data);
		
	}	

	function viewBinExclConfig(excId, type,parentPage) {
	var url;
		if (type == 'V')
			url = '/editBinExclConfig';
		var tokenValue = "jmFJQ+PdiyhvSK98IREKBrFKmjE=";
		var data = "excId," + excId + ",viewType," + type + ",_vTransactToken,"
			+ tokenValue +",parentPage," + parentPage;
		postData(url, data);
	}