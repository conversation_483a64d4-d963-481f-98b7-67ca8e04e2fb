package org.npci.settlenxt.adminportal.controllers;

import java.text.ParseException;
import java.util.List;
import java.util.Locale;

import org.npci.settlenxt.adminportal.common.util.CommonConstants;
import org.npci.settlenxt.adminportal.dto.SessionDTO;
import org.npci.settlenxt.adminportal.service.SettlementCycleConfigService;
import org.npci.settlenxt.portal.common.controllers.BaseController;
import org.npci.settlenxt.portal.common.dto.CodeValueDTO;
import org.npci.settlenxt.portal.common.dto.SettlementCycleConfigDTO;
import org.npci.settlenxt.portal.common.service.BaseLookupServiceImpl;
import org.npci.settlenxt.portal.common.util.BaseCommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.google.gson.JsonObject;

@Controller
public class SettlementCycleContoller extends BaseController {

	@Autowired
	private SessionDTO sessionDTO;
	@Autowired
	SettlementCycleConfigService settlementCycleService;

	@Autowired
	BaseLookupServiceImpl lookUpService;

	@Autowired
	private MessageSource messageSource;
	private static final String SETTLEMENT_CYCLE_DTO = "settlementCycleConfigDTO";

	private static final String SETTLEMENT_CYCLE_CONFIG_DTO = "settlementCycleConfigDTO";
	private static final String VIEW_SETTLEMENT_DETAILS = "viewSettlementDetails";
	private static final String SETTLEMENT_CYCLE_MINUTES = "SettlementCycleMin";
	private static final String SETTLEMENT_CYCLE_HOURS = "SettlementCycleHour";

	private static final String SHOW_SETTLEMENT_CYCLE = "showSettlementCycle";
	private static final String ADD_EDIT_SETTLEMENT_CYCLE = "addEditSettlementCycle";
	private static final String MINUTE = "minute";
	private static final String TRUE="true";
	private static final String T_CONST="t";
	private static final String YES="Y";
	private static final String NO="N";
	private static final String HOUR="hour";
	private static final String APPROVE_MSG="Settlement Cycle Record Data approved successfully!!";
	private static final String REJ_MSG="Settlement Cycle Record Data Record rejected successfully!!";
	private static final String DISCARD_MSG="Settlement Cycle Record Data has been successfully discarded!!";
	private static final String START_HOUR="startHour";
	private static final String END_HOUR="endHour";
	private static final String SHOWBUTTON= "showButton";

	@PostMapping("/settlementCycle")
	@PreAuthorize("hasAuthority('View Settlement Cycle')")
	public String showSettlementCycle(Model model) {

		addParamsForMainTab(model);
		List<SettlementCycleConfigDTO> settlementCycleList = settlementCycleService.getApprovedSettlementFromMain();

		prepareSettlementCycleList(settlementCycleList);
		model.addAttribute("settlementCycleList", settlementCycleList);
		validateCheckBoxMakerCheckerSettlemntCycle(model);

		return getView(model, SHOW_SETTLEMENT_CYCLE);
	}

	private void addParamsForMainTab(Model model) {
		model.addAttribute(CommonConstants.SHOW_ADD_BUTTON, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SHOW_MAIN_TAB, CommonConstants.YES_FLAG);
	}

	private void validateCheckBoxMakerCheckerSettlemntCycle(Model model) {
		if (CommonConstants.ROLE_TYPE_MAKER.equalsIgnoreCase(sessionDTO.getMakChkFlag())) {
			model.addAttribute(CommonConstants.SHOW_CHECK_BOX, BaseCommonConstants.NO_FLAG);
		} else {
			model.addAttribute(CommonConstants.SHOW_CHECK_BOX, CommonConstants.YES_FLAG);
		}
	}

	private void prepareSettlementCycleList(List<SettlementCycleConfigDTO> settlementCycleList) {
		for (int i = 0; i < settlementCycleList.size(); i++) {
			SettlementCycleConfigDTO s = settlementCycleList.get(i);
			if (T_CONST.equals(s.getIsActiveValue()) || TRUE.equals(s.getIsActiveValue()))
				{
				s.setIsActiveValue(YES);
				}
			else
				{
				s.setIsActiveValue(NO);
				}
			settlementCycleList.set(i, s);
		}
	}

	@PostMapping("/settlementCyclePendingForApproval")
	@PreAuthorize("hasAuthority('View Settlement Cycle')")
	public String getPendingActionCode(Model model) {

		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);

		List<SettlementCycleConfigDTO> settlementCycleList = settlementCycleService.getPendingSettlementFromStg();
		prepareSettlementCycleList(settlementCycleList);

		model.addAttribute("pendingsettlementCycleList", settlementCycleList);
		validateCheckBoxMakerCheckerSettlemntCycle(model);

		return getView(model, SHOW_SETTLEMENT_CYCLE);
	}

	private void checkSettlementApproveStatus(SettlementCycleConfigDTO settlementCycleConfigDTO, Model model) {
		Locale locale=new Locale(null);
		if (CommonConstants.TRANSACT_SUCCESS.equalsIgnoreCase(settlementCycleConfigDTO.getStatusCode())) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("settlementCycle.approvalSuccess.msg", null, locale));
		} else {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					messageSource.getMessage("settlementCycle.rejectionSuccess.msg", null, locale));
		}
	}

	@PostMapping("/addSettlementCycle")
	@PreAuthorize("hasAuthority('Add Settlement Cycle')")
	public String addSettlementCycle(Model model) {

		SettlementCycleConfigDTO settlementCycleConfigDTO = new SettlementCycleConfigDTO();
		List<CodeValueDTO> hour = settlementCycleService.getLookupDataSorted(HOUR);
		List<CodeValueDTO> min = settlementCycleService.getLookupDataSorted(MINUTE);
		model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
		model.addAttribute(SETTLEMENT_CYCLE_DTO, settlementCycleConfigDTO);
		model.addAttribute(SETTLEMENT_CYCLE_HOURS, hour);
		model.addAttribute(SETTLEMENT_CYCLE_MINUTES, min);
		model.addAttribute(CommonConstants.MAIN_TAB, CommonConstants.YES_FLAG);
		return getView(model, ADD_EDIT_SETTLEMENT_CYCLE);
	}

	@PostMapping("/saveSettlementCycle")
	@PreAuthorize("hasAuthority('Add Settlement Cycle')")
	public String saveSettlementCycle(@ModelAttribute SettlementCycleConfigDTO settlementCycleConfigDTO, Model model) {
		try {

			settlementCycleConfigDTO = settlementCycleService.addSettlementCycle(settlementCycleConfigDTO);
			model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
			model.addAttribute(CommonConstants.MAIN_TAB, CommonConstants.YES_FLAG);

			settlementCycleConfigDTO.setStartHour(settlementCycleConfigDTO.getStartHour().split(":")[0]);
			settlementCycleConfigDTO.setEndHour(settlementCycleConfigDTO.getEndHour().split(":")[0]);

		} catch (Exception ex) {
			model.addAttribute(SETTLEMENT_CYCLE_DTO, settlementCycleConfigDTO);
			model.addAttribute(CommonConstants.ADD_FLOW, CommonConstants.YES_FLAG);
			model.addAttribute(CommonConstants.MAIN_TAB, CommonConstants.YES_FLAG);
			return handleErrorCodeAndForward(model, ADD_EDIT_SETTLEMENT_CYCLE, ex);

		}
		model.addAttribute(SETTLEMENT_CYCLE_DTO, settlementCycleConfigDTO);

		List<CodeValueDTO> hour = settlementCycleService.getLookupDataSorted(HOUR);
		List<CodeValueDTO> min = settlementCycleService.getLookupDataSorted(MINUTE);
		model.addAttribute(SETTLEMENT_CYCLE_HOURS, hour);
		model.addAttribute(SETTLEMENT_CYCLE_MINUTES, min);
		model.addAttribute(SHOWBUTTON, CommonConstants.YES_FLAG);
		model.addAttribute(CommonConstants.SUCCESS_STATUS, getMessageFromBundle("settlementCycle.addSuccess.msg"));
		return getView(model, ADD_EDIT_SETTLEMENT_CYCLE);

	}

	@PostMapping("/approveOrRejectBulkSettlementCycle")
	@PreAuthorize("hasAuthority('Approve Settlement Cycle')")
	public String approveOrRejectBulkSettlementCycle(
			@RequestParam("settlementCycleNoList") String settlementCycleNoList, @RequestParam("status") String status,
			Model model) {

		String remarks = "";
		if (status.equals(CommonConstants.STATUS_APPROVE)) {
			remarks = CommonConstants.BULK_APPROVE;
		} else if (status.equals(CommonConstants.STATUS_REJECT)) {
			remarks = CommonConstants.BULK_REJECT;
		}

		SettlementCycleConfigDTO settlementCycleConfigDTO = settlementCycleService
				.updateApproveOrRejectBulkSettlementCycle(settlementCycleNoList, status, remarks);
		checkSettlementApproveStatus(settlementCycleConfigDTO, model);

		model.addAttribute(CommonConstants.SHOW_APPROVAL_TAB, CommonConstants.YES_FLAG);

		List<SettlementCycleConfigDTO> settlementCycleList = settlementCycleService.getPendingSettlementFromStg();
		prepareSettlementCycleList(settlementCycleList);

		model.addAttribute("pendingsettlementCycleList", settlementCycleList);
		validateCheckBoxMakerCheckerSettlemntCycle(model);

		return getView(model, SHOW_SETTLEMENT_CYCLE);
	}

	@PreAuthorize("hasAuthority('View Settlement Cycle')")
	@PostMapping("/viewApproveSettlement")
	public String viewApproveSettlementCycle(@RequestParam("srNo") String srNo, Model model) {

		SettlementCycleConfigDTO settlementCycleConfigDTO = settlementCycleService.getSettlementDetails(srNo);
		model.addAttribute(SETTLEMENT_CYCLE_CONFIG_DTO, settlementCycleConfigDTO);
		return getView(model, VIEW_SETTLEMENT_DETAILS);
	}

	@PreAuthorize("hasAuthority('Approve Settlement Cycle')")
	@PostMapping("/approveOrRejectSettlement")
	public String approveOrRejectSettlement(@RequestParam("srNo") String srNo, @RequestParam("status") String status,
			@RequestParam("remarks") String remarks, Model model) {
		SettlementCycleConfigDTO settlementCycleConfigDTO;
		try {
			settlementCycleConfigDTO = settlementCycleService.approveOrRejectSettlement(srNo, status, remarks);

		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, VIEW_SETTLEMENT_DETAILS, ex);
		}
		model.addAttribute(SETTLEMENT_CYCLE_CONFIG_DTO, settlementCycleConfigDTO);

		if (CommonConstants.REQUEST_STATE_APPROVED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS, APPROVE_MSG);
		} else if (CommonConstants.REQUEST_STATE_REJECTED.equalsIgnoreCase(status)) {
			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					REJ_MSG);
		}
		return getView(model, VIEW_SETTLEMENT_DETAILS);

	}

	@PreAuthorize("hasAuthority('Edit Settlement Cycle')")
	@PostMapping("/discardSettlement")
	public String discardSettlement(@RequestParam("srNo") String srNo, Model model) {
		SettlementCycleConfigDTO settlementCycleConfigDTO = settlementCycleService.getSettlementDetails(srNo);
		settlementCycleService.deleteSettlement(srNo);
		model.addAttribute(CommonConstants.SUCCESS_STATUS,
				DISCARD_MSG);
		model.addAttribute(SETTLEMENT_CYCLE_CONFIG_DTO, settlementCycleConfigDTO);
		return getView(model, VIEW_SETTLEMENT_DETAILS);

	}

	@PostMapping("/viewSettlementCycle")
	@PreAuthorize("hasAuthority('View Settlement Cycle')")
	public String viewSettlementCycle(@RequestParam("srNo") String srNo, Model model) {
		SettlementCycleConfigDTO settlementCycleConfigDTO2 = settlementCycleService
				.getSrNoInfoFromMain(Integer.valueOf(srNo));

		model.addAttribute(SETTLEMENT_CYCLE_DTO, settlementCycleConfigDTO2);
		return getView(model, VIEW_SETTLEMENT_DETAILS);

	}

	@PostMapping("/editSettlementCycle")
	@PreAuthorize("hasAuthority('Edit Settlement Cycle')")
	public String editSettlementCycleConfig(@RequestParam("srNo") String srNo,
			@RequestParam("reqState") String reqState, Model model) {

		SettlementCycleConfigDTO settlementCycleConfigDTO;
		if (reqState.equals(CommonConstants.REQUEST_STATE_APPROVED))
		{
			settlementCycleConfigDTO = settlementCycleService.getSrNoInfoFromMain(Integer.valueOf(srNo));
		}
		else
			{
			settlementCycleConfigDTO = settlementCycleService.getSrNoInfoFromStg(Integer.valueOf(srNo));
			}

		settlementCycleConfigDTO.setAddEditFlag(CommonConstants.EDIT_SETTLEMENT_CYCLE);
		fetchHourMinuteDataFromLookupAndSet(model, settlementCycleConfigDTO);

		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
		validateMainTabOrApproved(reqState, model);
		return getView(model, ADD_EDIT_SETTLEMENT_CYCLE);
	}

	private void fetchHourMinuteDataFromLookupAndSet(Model model, SettlementCycleConfigDTO settlementCycleConfigDTO) {
		model.addAttribute(CommonConstants.EDIT_FLOW, CommonConstants.YES_FLAG);
		List<CodeValueDTO> hour = settlementCycleService.getLookupDataSorted(HOUR);
		List<CodeValueDTO> min = settlementCycleService.getLookupDataSorted(MINUTE);
		model.addAttribute(SETTLEMENT_CYCLE_HOURS, hour);
		model.addAttribute(SETTLEMENT_CYCLE_MINUTES, min);

		model.addAttribute(START_HOUR, settlementCycleConfigDTO.getStartHour());
		model.addAttribute(END_HOUR, settlementCycleConfigDTO.getStartHour());

		settlementCycleConfigDTO.setStartHour(settlementCycleConfigDTO.getStartHour().split(":")[0]);
		settlementCycleConfigDTO.setEndHour(settlementCycleConfigDTO.getEndHour().split(":")[0]);

		model.addAttribute(SETTLEMENT_CYCLE_DTO, settlementCycleConfigDTO);
	}

	private void validateMainTabOrApproved(String reqState, Model model) {
		if (reqState.equals(CommonConstants.REQUEST_STATE_APPROVED)) {
			model.addAttribute(CommonConstants.MAIN_TAB, CommonConstants.YES_FLAG);
		} else {
			model.addAttribute(CommonConstants.APPROVAL_TAB, CommonConstants.YES_FLAG);
		}
	}

	@PostMapping("/updateSettlementCycle")
	@PreAuthorize("hasAuthority('Edit Settlement Cycle')")
	public String updateSettlementCycle(@ModelAttribute SettlementCycleConfigDTO settlementCycleConfigDTO,
			@RequestParam("reqState") String reqState, Model model) {

		try {

			settlementCycleConfigDTO = settlementCycleService.updateSettlementCycle(settlementCycleConfigDTO);
			fetchHourMinuteDataFromLookupAndSet(model, settlementCycleConfigDTO);

			model.addAttribute(CommonConstants.SUCCESS_STATUS,
					getMessageFromBundle("settlementCycle.updateSuccess.msg"));
			model.addAttribute(SHOWBUTTON, CommonConstants.YES_FLAG);
			
			validateMainTabOrApproved(reqState, model);
		} catch (Exception ex) {
			return handleErrorCodeAndForward(model, ADD_EDIT_SETTLEMENT_CYCLE, ex);
		}

		return getView(model, ADD_EDIT_SETTLEMENT_CYCLE);
	}

	@PostMapping("/checkDupSettlementTime")
	public ResponseEntity<Object> checkDupSettlementTime(Model model, @RequestParam("flow") String flow,
			@RequestParam("startHr") String startHr, @RequestParam("endHr") String endHr,
			@RequestParam("cycleNum") String cycleNum, @RequestParam("prodId") String prodId) {

		boolean result = settlementCycleService.validateSettlementTime(flow, startHr, endHr, cycleNum, prodId);

		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty(BaseCommonConstants.RESPONSE_STATUS, CommonConstants.TRANSACT_SUCCESS);

		} else {
			jsonResponse.addProperty(BaseCommonConstants.RESPONSE_STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

	@PostMapping("/checkOverLapSettlementTime")
	public ResponseEntity<Object> checkOverLapSettlementTime(Model model, @RequestParam("startHr") String startHr,
			@RequestParam("endHr") String endHr, @RequestParam("cycleNum") String cycleNum,
			@RequestParam("prodId") String prodId) throws ParseException {

		boolean result = settlementCycleService.validateOverLapSettlementTime(prodId, startHr, endHr, cycleNum);

		JsonObject jsonResponse = new JsonObject();

		if (result) {
			jsonResponse.addProperty(BaseCommonConstants.RESPONSE_STATUS, CommonConstants.TRANSACT_SUCCESS);

		} else {
			jsonResponse.addProperty(BaseCommonConstants.RESPONSE_STATUS, CommonConstants.TRANSACT_FAIL);
		}

		return new ResponseEntity<>(jsonResponse.toString(), HttpStatus.OK);

	}

}
